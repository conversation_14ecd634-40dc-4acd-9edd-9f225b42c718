"""
Testes de Integração Ponta a Ponta - UX/UI Designer Agent

Testes abrangentes cobrindo:
- Fluxo completo de análise UX/UI
- Integração com VisualAnalysisTools
- Integração com sistema de storage
- Cenários reais de uso
- Performance e robustez
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from typing import Dict, Any

from ..agents.ux_ui_designer_agent import UXUIDesignerAgent
from ..storage import AgentStorage, save_analysis_async
from ..schemas import AnalysisResult, ArchitecturalRecommendation, TechnicalRecommendation, RecommendationCategory, PriorityLevel, EffortLevel


class TestUXUIIntegration:
    """Testes de integração ponta a ponta para UX/UI Designer"""

    @pytest.fixture
    def sample_complete_data(self):
        """Dados completos para teste de integração"""
        return {
            "visual_analysis": {
                "analise_ui_ux": {
                    "layout_quality": "regular",
                    "user_experience": "ruim",
                    "responsive_design": "péssimo",
                    "visual_hierarchy": "baixo"
                },
                "screenshots_urls": [
                    "https://example.com/desktop.png",
                    "https://example.com/mobile.png",
                    "https://example.com/tablet.png"
                ],
                "recomendacoes_design": [
                    "Melhorar contraste de cores",
                    "Implementar design responsivo",
                    "Simplificar navegação"
                ],
                "issues": [
                    "Layout quebrado em mobile",
                    "Navegação confusa",
                    "Cores com baixo contraste",
                    "Elementos sobrepostos"
                ]
            },
            "lighthouse_data": {
                "performance": {"score": 0.45},
                "accessibility": {"score": 0.60},
                "best_practices": {"score": 0.70},
                "seo": {"score": 0.80}
            },
            "company_context": {
                "name": "TechCorp Solutions",
                "sector": "Tecnologia",
                "website": "https://techcorp.example.com",
                "size": "Média empresa"
            }
        }

    @patch('backend.tools.agents.agents.ux_ui_designer_agent.Agent')
    @patch('backend.tools.agents.agents.ux_ui_designer_agent.VisualAnalysisTools')
    def test_complete_analysis_flow(self, mock_visual_tools, mock_agent, sample_complete_data):
        """Testa fluxo completo de análise UX/UI"""
        # Setup do agente
        agent = UXUIDesignerAgent()
        agent.visual_tools = mock_visual_tools.return_value

        # Mock das ferramentas visuais
        mock_visual_insights = Mock()
        mock_visual_insights.layout_quality = "regular"
        mock_visual_insights.user_experience = "ruim"
        mock_visual_insights.responsive_design = "péssimo"
        mock_visual_insights.visual_hierarchy = "baixo"
        mock_visual_insights.ui_issues = [
            "Layout quebrado em mobile",
            "Cores com baixo contraste",
            "Elementos sobrepostos"
        ]
        mock_visual_insights.ux_issues = [
            "Navegação confusa",
            "Experiência do usuário prejudicada"
        ]
        mock_visual_insights.overall_visual_score = 35.0
        mock_visual_insights.accessibility_visual_score = 40.0
        mock_visual_insights.model_dump.return_value = {
            "layout_quality": "regular",
            "user_experience": "ruim",
            "responsive_design": "péssimo",
            "overall_visual_score": 35.0,
            "ui_issues": mock_visual_insights.ui_issues,
            "ux_issues": mock_visual_insights.ux_issues
        }

        agent.visual_tools.analyze_visual_data.return_value = mock_visual_insights
        agent.visual_tools.get_visual_priorities.return_value = [
            "Implementar design responsivo urgente",
            "Melhorar contraste para acessibilidade",
            "Simplificar navegação"
        ]
        agent.visual_tools.assess_mobile_readiness.return_value = {
            "assessment": "Crítico - Site não funciona em mobile",
            "issues": ["Layout quebrado", "Elementos inacessíveis"],
            "score": 15.0
        }

        # Mock da resposta do agente
        mock_response = Mock()
        mock_response.content = """
# Análise UX/UI - TechCorp Solutions

## Problemas Críticos Identificados

### 1. Design Responsivo Inadequado
- **Impacto**: Alto - Site inutilizável em dispositivos móveis
- **Prioridade**: Crítica
- **Solução**: Implementar CSS Grid/Flexbox com breakpoints apropriados

### 2. Problemas de Acessibilidade Visual
- **Impacto**: Alto - Usuários com deficiências visuais não conseguem usar o site
- **Prioridade**: Alta
- **Solução**: Ajustar contraste para WCAG AA (4.5:1 mínimo)

### 3. Navegação Confusa
- **Impacto**: Médio - Usuários se perdem no site
- **Prioridade**: Média
- **Solução**: Redesenhar arquitetura de informação

## Recomendações Específicas

1. **Mobile-First Redesign** (1-2 meses)
2. **Auditoria de Acessibilidade** (2-3 semanas)
3. **Testes de Usabilidade** (1 semana)

## Métricas de Sucesso
- Score visual: 35 → 80+
- Mobile usability: 15 → 90+
- Accessibility score: 40 → 85+
"""
        agent.agent.run.return_value = mock_response

        # Executar análise completa
        result = agent.analyze_ux_ui_design(
            sample_complete_data["visual_analysis"],
            sample_complete_data["lighthouse_data"],
            sample_complete_data["company_context"]
        )

        # Verificações do resultado
        assert result["agent_type"] == "UXUIDesignerAgent"
        assert result["overall_visual_score"] == 35.0
        assert result["design_category"] == "Crítico - Redesign necessário"
        assert result["ui_issues_count"] == 3
        assert result["ux_issues_count"] == 2

        # Verificar análise detalhada
        assert "TechCorp Solutions" in result["agent_analysis"]
        assert "Mobile-First Redesign" in result["agent_analysis"]
        assert "Acessibilidade" in result["agent_analysis"]

        # Verificar resumo de análise
        summary = result["analysis_summary"]
        assert summary["layout_quality"] == "regular"
        assert summary["user_experience"] == "ruim"
        assert summary["responsive_design"] == "péssimo"
        assert summary["total_issues"] == 5

    @patch('backend.tools.agents.agents.ux_ui_designer_agent.Agent')
    @patch('backend.tools.agents.agents.ux_ui_designer_agent.VisualAnalysisTools')
    def test_recommendations_extraction_integration(self, mock_visual_tools, mock_agent, sample_complete_data):
        """Testa extração e estruturação de recomendações"""
        agent = UXUIDesignerAgent()
        agent.visual_tools = mock_visual_tools.return_value

        # Setup mocks para cenário com muitos problemas
        mock_visual_insights = Mock()
        mock_visual_insights.ui_issues = [
            "Layout de baixa qualidade detectado",
            "Cores inadequadas para acessibilidade",
            "Elementos mal posicionados"
        ]
        mock_visual_insights.ux_issues = [
            "Experiência do usuário prejudicada",
            "Navegação não intuitiva",
            "Fluxo de tarefas interrompido"
        ]
        mock_visual_insights.overall_visual_score = 25.0
        mock_visual_insights.model_dump.return_value = {
            "ui_issues": mock_visual_insights.ui_issues,
            "ux_issues": mock_visual_insights.ux_issues,
            "overall_visual_score": 25.0
        }

        agent.visual_tools.analyze_visual_data.return_value = mock_visual_insights
        agent.visual_tools.get_visual_priorities.return_value = []
        agent.visual_tools.assess_mobile_readiness.return_value = {
            "assessment": "Crítico"}

        mock_response = Mock()
        mock_response.content = "Análise crítica de UX/UI"
        agent.agent.run.return_value = mock_response

        # Executar análise
        result = agent.analyze_ux_ui_design(
            sample_complete_data["visual_analysis"])

        # Extrair recomendações
        recommendations = agent.get_design_recommendations_summary(result)

        # Verificações
        assert len(recommendations) >= 6  # 3 UI + 3 UX + 1 Strategic

        # Verificar tipos de recomendações
        types = [rec["type"] for rec in recommendations]
        assert "UI_Issue" in types
        assert "UX_Issue" in types
        assert "Strategic" in types

        # Verificar categorias
        categories = [rec["category"] for rec in recommendations]
        assert "Interface Design" in categories
        assert "User Experience" in categories
        assert "Design Strategy" in categories

        # Verificar prioridades
        priorities = [rec["priority"] for rec in recommendations]
        assert "High" in priorities  # Deve ter pelo menos uma alta prioridade

    @patch('backend.tools.agents.storage.AgentStorage')
    async def test_storage_integration(self, mock_storage_class):
        """Testa integração com sistema de storage"""
        # Mock do storage
        mock_storage = AsyncMock()
        mock_storage_class.return_value = mock_storage
        mock_storage.save_analysis_result.return_value = "analysis_123"

        # Criar resultado de análise simulado
        analysis_result = {
            "analysis_id": "ux_ui_test_123",
            "company_info": {
                "name": "Test Company",
                "sector": "E-commerce"
            },
            "recommendations": {
                "identified_problems": ["Layout responsivo inadequado"],
                "current_overall_score": 45.0,
                "business_impact": "Perda de conversões em mobile",
                "technical_recommendations": [],
                "implementation_timeline": "2-3 meses"
            },
            "analysis_metadata": {
                "agent_type": "UXUIDesignerAgent",
                "timestamp": "2025-05-23T17:00:00Z"
            },
            "confidence_score": 0.85
        }

        # Simular salvamento
        with patch('backend.tools.agents.storage.AgentStorageManager') as mock_manager:
            mock_context = AsyncMock()
            mock_context.__aenter__.return_value = mock_storage
            mock_manager.return_value = mock_context

            # Executar salvamento
            result_id = await save_analysis_async(analysis_result)

            # Verificações
            assert result_id == "analysis_123"
            mock_storage.save_analysis_result.assert_called_once()

    @patch('backend.tools.agents.agents.ux_ui_designer_agent.Agent')
    @patch('backend.tools.agents.agents.ux_ui_designer_agent.VisualAnalysisTools')
    def test_performance_with_large_dataset(self, mock_visual_tools, mock_agent):
        """Testa performance com dataset grande"""
        agent = UXUIDesignerAgent()
        agent.visual_tools = mock_visual_tools.return_value

        # Simular dataset grande
        large_visual_data = {
            "analise_ui_ux": {
                "layout_quality": "bom",
                "user_experience": "regular",
                "responsive_design": "bom"
            },
            "screenshots_urls": [f"https://example.com/screenshot_{i}.png" for i in range(50)],
            "recomendacoes_design": [f"Recomendação {i}" for i in range(20)],
            "issues": [f"Issue {i}" for i in range(30)]
        }

        # Mock otimizado
        mock_visual_insights = Mock()
        mock_visual_insights.layout_quality = "bom"
        mock_visual_insights.user_experience = "regular"
        mock_visual_insights.responsive_design = "bom"
        mock_visual_insights.ui_issues = [f"UI Issue {i}" for i in range(10)]
        mock_visual_insights.ux_issues = [f"UX Issue {i}" for i in range(8)]
        mock_visual_insights.overall_visual_score = 75.0
        mock_visual_insights.accessibility_visual_score = 80.0
        mock_visual_insights.model_dump.return_value = {
            "overall_visual_score": 75.0}

        agent.visual_tools.analyze_visual_data.return_value = mock_visual_insights
        agent.visual_tools.get_visual_priorities.return_value = [
            "Priority 1", "Priority 2"]
        agent.visual_tools.assess_mobile_readiness.return_value = {
            "assessment": "Bom"}

        mock_response = Mock()
        mock_response.content = "Análise de dataset grande concluída"
        agent.agent.run.return_value = mock_response

        # Executar análise
        import time
        start_time = time.time()

        result = agent.analyze_ux_ui_design(large_visual_data)

        end_time = time.time()
        execution_time = end_time - start_time

        # Verificações de performance
        assert execution_time < 2.0  # Deve executar em menos de 2 segundos
        assert result["overall_visual_score"] == 75.0
        assert result["ui_issues_count"] == 10
        assert result["ux_issues_count"] == 8

    @patch('backend.tools.agents.agents.ux_ui_designer_agent.Agent')
    @patch('backend.tools.agents.agents.ux_ui_designer_agent.VisualAnalysisTools')
    def test_error_recovery_integration(self, mock_visual_tools, mock_agent):
        """Testa recuperação de erros em cenários reais"""
        agent = UXUIDesignerAgent()
        agent.visual_tools = mock_visual_tools.return_value

        # Simular erro na análise visual
        agent.visual_tools.analyze_visual_data.side_effect = Exception(
            "Erro de conexão")

        # Verificar que o erro é propagado corretamente
        with pytest.raises(Exception, match="Erro de conexão"):
            agent.analyze_ux_ui_design({"test": "data"})

        # Simular recuperação após erro
        mock_visual_insights = Mock()
        mock_visual_insights.layout_quality = "regular"
        mock_visual_insights.user_experience = "regular"
        mock_visual_insights.responsive_design = "regular"
        mock_visual_insights.ui_issues = []
        mock_visual_insights.ux_issues = []
        mock_visual_insights.overall_visual_score = 60.0
        mock_visual_insights.accessibility_visual_score = 65.0
        mock_visual_insights.model_dump.return_value = {
            "overall_visual_score": 60.0}

        # Remover o side_effect para simular recuperação
        agent.visual_tools.analyze_visual_data.side_effect = None
        agent.visual_tools.analyze_visual_data.return_value = mock_visual_insights
        agent.visual_tools.get_visual_priorities.return_value = []
        agent.visual_tools.assess_mobile_readiness.return_value = {
            "assessment": "Regular"}

        mock_response = Mock()
        mock_response.content = "Análise recuperada com sucesso"
        agent.agent.run.return_value = mock_response

        # Verificar recuperação
        result = agent.analyze_ux_ui_design({"test": "data"})
        assert result["overall_visual_score"] == 60.0
        assert "Análise recuperada com sucesso" in result["agent_analysis"]

    def test_concurrent_analysis_simulation(self):
        """Testa simulação de análises concorrentes"""
        # Este teste simula múltiplas análises simultâneas
        # para verificar thread safety e performance

        def create_agent_analysis():
            with patch('backend.tools.agents.agents.ux_ui_designer_agent.Agent'):
                with patch('backend.tools.agents.agents.ux_ui_designer_agent.VisualAnalysisTools') as mock_tools:
                    agent = UXUIDesignerAgent()
                    agent.visual_tools = mock_tools.return_value

                    mock_visual_insights = Mock()
                    mock_visual_insights.overall_visual_score = 70.0
                    mock_visual_insights.ui_issues = []
                    mock_visual_insights.ux_issues = []
                    mock_visual_insights.model_dump.return_value = {
                        "overall_visual_score": 70.0}

                    agent.visual_tools.analyze_visual_data.return_value = mock_visual_insights
                    agent.visual_tools.get_visual_priorities.return_value = []
                    agent.visual_tools.assess_mobile_readiness.return_value = {
                        "assessment": "Bom"}

                    mock_response = Mock()
                    mock_response.content = f"Análise concorrente"
                    agent.agent.run.return_value = mock_response

                    return agent.analyze_ux_ui_design({"test": "concurrent"})

        # Simular múltiplas análises
        results = []
        for i in range(5):
            result = create_agent_analysis()
            results.append(result)

        # Verificar que todas as análises foram bem-sucedidas
        assert len(results) == 5
        for result in results:
            assert result["overall_visual_score"] == 70.0
            assert "Análise concorrente" in result["agent_analysis"]
