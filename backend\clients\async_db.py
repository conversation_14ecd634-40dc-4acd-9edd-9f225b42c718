"""
Async MongoDB connection using Motor
"""
from motor.motor_asyncio import AsyncIOMotorClient
from config.settings import env
import logging

logger = logging.getLogger(__name__)

# Motor async client
motor_client = AsyncIOMotorClient(env.MONGODB_ATLAS_CONNECTION_URI)
motor_db = motor_client[env.MONGODB_DATABASE_NAME]

# Async collections
motor_clients_collection = motor_db["clients"]
motor_projetos_collection = motor_db["projetos"]


async def initialize_async_database():
    """
    Inicializa configuração do banco de dados async (schemas e índices)
    """
    try:
        # Test connection
        await motor_client.admin.command('ping')
        logger.info("Async MongoDB connection successful")
        
        # You can add index creation here if needed
        # Example:
        # await motor_clients_collection.create_index("name")
        # await motor_clients_collection.create_index([("created_at", -1)])
        
        return True
    except Exception as e:
        logger.error(f"Erro ao inicializar async database: {e}")
        return False


async def close_async_database():
    """
    Fecha conexão async com MongoDB
    """
    try:
        motor_client.close()
        logger.info("Async MongoDB connection closed")
    except Exception as e:
        logger.error(f"Erro ao fechar conexão async: {e}")