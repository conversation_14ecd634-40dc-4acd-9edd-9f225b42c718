"""
Testes de Performance Assíncrona - ScopeAI MT-5
"""

import pytest
import asyncio
import time
import statistics


class TestAsyncPerformance:
    """Testes de performance da conversão assíncrona"""

    @pytest.mark.asyncio
    async def test_concurrent_vs_sequential_performance(self):
        """Compara performance concorrente vs sequencial"""
        
        async def async_operation(operation_id):
            await asyncio.sleep(0.1)
            return f"async_result_{operation_id}"
        
        def sync_operation(operation_id):
            time.sleep(0.1)
            return f"sync_result_{operation_id}"
        
        operation_count = 5
        
        # Teste sequencial (síncrono)
        start_time = time.time()
        sync_results = []
        for i in range(operation_count):
            result = sync_operation(i)
            sync_results.append(result)
        sync_time = time.time() - start_time
        
        # Teste concorrente (assíncrono)
        start_time = time.time()
        async_tasks = [async_operation(i) for i in range(operation_count)]
        async_results = await asyncio.gather(*async_tasks)
        async_time = time.time() - start_time
        
        # Validações
        assert len(sync_results) == len(async_results) == operation_count
        assert async_time < sync_time
        
        speedup = sync_time / async_time
        print(f"📊 Performance: Sync: {sync_time:.3f}s, Async: {async_time:.3f}s, Speedup: {speedup:.2f}x")
        
        assert speedup >= 2.0, f"Speedup insuficiente: {speedup:.2f}x"

    @pytest.mark.asyncio 
    async def test_response_time_improvement(self):
        """Testa melhoria no tempo de resposta"""
        
        async def async_endpoint():
            tasks = [
                asyncio.sleep(0.05),  # DB query
                asyncio.sleep(0.08),  # Processing  
                asyncio.sleep(0.06),  # Validation
                asyncio.sleep(0.04)   # Response prep
            ]
            await asyncio.gather(*tasks)
            return {"status": "async_complete"}
        
        def sync_endpoint():
            time.sleep(0.05)  # DB query
            time.sleep(0.08)  # Processing
            time.sleep(0.06)  # Validation  
            time.sleep(0.04)  # Response prep
            return {"status": "sync_complete"}
        
        # Medir tempos
        start_time = time.time()
        sync_result = sync_endpoint()
        sync_time = time.time() - start_time
        
        start_time = time.time()
        async_result = await async_endpoint()
        async_time = time.time() - start_time
        
        improvement = (sync_time - async_time) / sync_time * 100
        
        print(f"📊 Response Time: Sync: {sync_time:.3f}s, Async: {async_time:.3f}s, Improvement: {improvement:.1f}%")
        
        assert async_time < sync_time
        assert improvement >= 50, f"Melhoria insuficiente: {improvement:.1f}%"

    @pytest.mark.asyncio
    async def test_concurrent_users(self):
        """Simula múltiplos usuários simultâneos"""
        
        async def user_request(user_id):
            start_time = time.time()
            
            # Simular operações de um usuário
            await asyncio.sleep(0.05)  # Auth
            await asyncio.sleep(0.1)   # Data fetch
            await asyncio.sleep(0.08)  # Processing
            
            response_time = time.time() - start_time
            return {"user_id": user_id, "response_time": response_time}
        
        # Simular 8 usuários simultâneos
        user_count = 8
        start_time = time.time()
        
        user_tasks = [user_request(i) for i in range(user_count)]
        results = await asyncio.gather(*user_tasks)
        
        total_time = time.time() - start_time
        response_times = [r["response_time"] for r in results]
        avg_response = statistics.mean(response_times)
        
        assert len(results) == user_count
        assert total_time < 0.4, f"Tempo total muito alto: {total_time:.3f}s"
        assert avg_response < 0.3, f"Resposta média muito lenta: {avg_response:.3f}s"
        
        print(f"📊 {user_count} usuários simultâneos: {total_time:.3f}s total, {avg_response:.3f}s média")


if __name__ == "__main__":
    import sys
    sys.exit(pytest.main([__file__, "-v"])) 