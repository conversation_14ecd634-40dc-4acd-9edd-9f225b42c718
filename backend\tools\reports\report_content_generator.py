"""
ReportContentGenerator - Responsável pela Geração de Conteúdo de Relatórios

Módulo especializado em gerar conteúdo estruturado para diferentes tipos 
de relatórios, seguindo o Single Responsibility Principle.
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone

logger = logging.getLogger(__name__)


class ReportContentGenerator:
    """
    Responsável por gerar conteúdo estruturado para relatórios.

    Única responsabilidade: Transformar dados raw em conteúdo organizado e insights.
    """

    def __init__(self):
        """Inicializa o gerador de conteúdo."""
        self.sector_templates = {
            "tecnologia": "tech_template",
            "financeiro": "fintech_template",
            "saude": "healthtech_template",
            "educacao": "edtech_template",
            "varejo": "retail_template",
            "servicos": "services_template",
            "industria": "industrial_template",
            "default": "generic_template"
        }

    async def generate_report_content(
        self,
        raw_data: Dict[str, Any],
        report_type: str,
        template_name: str,
        customizations: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Gera conteúdo estruturado baseado nos dados raw e tipo de relatório.

        Args:
            raw_data: Dados coletados pelo ReportDataCollector
            report_type: Tipo do relatório
            template_name: Nome do template a ser usado
            customizations: Personalizações específicas

        Returns:
            Conteúdo estruturado do relatório
        """
        try:
            logger.info(f"Gerando conteúdo para relatório {report_type}")

            # Estrutura base do relatório
            content = {
                "report_type": report_type,
                "template_used": template_name,
                "generated_at": datetime.now(timezone.utc).isoformat(),
                "data_quality_score": self._calculate_data_quality_score(raw_data),
                "processing_time": 0,  # Será calculado pelo orquestrador
                "client_info": raw_data.get("client_basic_info", {}),
                "executive_summary": "",
                "key_insights": [],
                "recommendations": [],
                "detailed_analysis": {},
                "appendices": {}
            }

            # Gerar conteúdo específico baseado no tipo
            if report_type == "dossie_executivo":
                content.update(await self._generate_executive_dossier(raw_data))
            elif report_type == "analise_tecnica":
                content.update(await self._generate_technical_analysis(raw_data))
            elif report_type == "competitividade":
                content.update(await self._generate_competitive_analysis(raw_data))
            elif report_type == "mercado_oportunidades":
                content.update(await self._generate_market_opportunities(raw_data))
            elif report_type == "crescimento_digital":
                content.update(await self._generate_digital_growth(raw_data))
            elif report_type == "funding_investimentos":
                content.update(await self._generate_funding_analysis(raw_data))
            elif report_type == "modelo_negocio":
                content.update(await self._generate_business_model(raw_data))
            elif report_type == "dashboard_executivo":
                content.update(await self._generate_executive_dashboard(raw_data))

            # Aplicar customizações se fornecidas
            if customizations:
                content = self._apply_template_customizations(
                    content, customizations)

            # Gerar componentes comuns
            content["executive_summary"] = self._create_executive_summary(
                content)
            content["key_insights"] = self._extract_key_insights(content)
            content["recommendations"] = self._generate_recommendations(
                content)

            logger.info(f"Conteúdo gerado com sucesso para {report_type}")
            return content

        except Exception as e:
            logger.error(
                f"Erro na geração de conteúdo {report_type}: {str(e)}")
            raise Exception(f"Falha na geração de conteúdo: {str(e)}")

    def _calculate_data_quality_score(self, data: Dict[str, Any]) -> float:
        """Calcula score de qualidade dos dados (0-100)."""
        try:
            score = 0.0
            total_checks = 0

            # Verificar informações básicas do cliente (peso: 40%)
            client_info = data.get("client_basic_info", {})
            basic_fields = ["name", "company", "sector", "website"]
            basic_score = sum(
                1 for field in basic_fields if client_info.get(field))
            score += (basic_score / len(basic_fields)) * 40
            total_checks += 40

            # Verificar dados de diagnóstico (peso: 60%)
            diagnostic_data = data.get("diagnostic_data")
            if diagnostic_data and not diagnostic_data.get("error"):
                score += 60
            total_checks += 60

            return min(100.0, score)

        except Exception as e:
            logger.error(f"Erro no cálculo de qualidade: {str(e)}")
            return 0.0

    async def _generate_executive_dossier(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Gera dossiê executivo consolidado."""
        client_info = data.get("client_basic_info", {})
        diagnostic_data = data.get("diagnostic_data", {})
        technical_data = data.get("technical_data", {})

        return {
            "detailed_analysis": {
                "company_overview": {
                    "name": client_info.get("company", ""),
                    "sector": client_info.get("sector", ""),
                    "description": client_info.get("description", ""),
                    "website": client_info.get("website", "")
                },
                "digital_presence": {
                    "performance_score": diagnostic_data.get("performance_metrics", {}).get("score", 0),
                    "accessibility_score": diagnostic_data.get("accessibility_score", {}).get("score", 0),
                    "seo_score": diagnostic_data.get("seo_analysis", {}).get("score", 0)
                },
                "technical_infrastructure": {
                    "security_level": technical_data.get("security", {}).get("security_score", 0),
                    "technology_stack": technical_data.get("technology_stack", []),
                    "scalability_assessment": technical_data.get("scalability", {})
                },
                "risk_assessment": self._assess_risks(data),
                "opportunities": self._identify_opportunities(data)
            }
        }

    async def _generate_technical_analysis(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Gera análise técnica detalhada."""
        technical_data = data.get("technical_data", {})
        diagnostic_data = data.get("diagnostic_data", {})

        return {
            "detailed_analysis": {
                "infrastructure_analysis": technical_data.get("infrastructure", {}),
                "security_assessment": technical_data.get("security", {}),
                "performance_metrics": diagnostic_data.get("performance_metrics", {}),
                "scalability_analysis": technical_data.get("scalability", {}),
                "technical_debt": technical_data.get("technical_debt", {}),
                "technology_recommendations": self._generate_tech_recommendations(data),
                "architecture_assessment": self._analyze_architecture(data)
            }
        }

    def _create_executive_summary(self, data: Dict[str, Any]) -> str:
        """Cria resumo executivo baseado nos dados do relatório."""
        try:
            client_info = data.get("client_info", {})
            company_name = client_info.get("company", "A empresa")
            sector = client_info.get("sector", "diversos setores")

            quality_score = data.get("data_quality_score", 0)

            summary_parts = [
                f"{company_name} é uma empresa atuante no setor de {sector}.",
                f"Nossa análise abrangente revelou insights importantes sobre sua posição no mercado.",
                f"O score de qualidade dos dados analisados foi de {quality_score:.1f}%."
            ]

            # Adicionar insights específicos baseado no tipo de relatório
            report_type = data.get("report_type", "")
            if report_type == "dossie_executivo":
                summary_parts.append(
                    "Este dossiê executivo apresenta uma visão consolidada da empresa, incluindo análise técnica, competitiva e de mercado.")
            elif report_type == "analise_tecnica":
                summary_parts.append(
                    "A análise técnica revela o estado atual da infraestrutura digital e oportunidades de melhoria.")

            return " ".join(summary_parts)

        except Exception as e:
            logger.error(f"Erro na criação do resumo executivo: {str(e)}")
            return "Resumo executivo não disponível devido a erro na geração."

    def _extract_key_insights(self, data: Dict[str, Any]) -> List[str]:
        """Extrai insights principais dos dados analisados."""
        try:
            insights = []
            detailed_analysis = data.get("detailed_analysis", {})

            # Insights de performance digital
            digital_presence = detailed_analysis.get("digital_presence", {})
            if digital_presence:
                perf_score = digital_presence.get("performance_score", 0)
                if perf_score > 80:
                    insights.append(
                        "Excelente performance digital com pontuação superior a 80%")
                elif perf_score > 60:
                    insights.append(
                        "Performance digital moderada com oportunidades de otimização")
                else:
                    insights.append(
                        "Performance digital necessita melhorias significativas")

            # Insights de segurança
            tech_infra = detailed_analysis.get("technical_infrastructure", {})
            if tech_infra:
                security_level = tech_infra.get("security_level", 0)
                if security_level > 80:
                    insights.append("Alto nível de segurança implementado")
                elif security_level > 60:
                    insights.append(
                        "Segurança adequada com algumas vulnerabilidades")
                else:
                    insights.append(
                        "Necessária revisão urgente das práticas de segurança")

            # Insights de infraestrutura
            infra_analysis = detailed_analysis.get(
                "infrastructure_analysis", {})
            if infra_analysis:
                insights.append(
                    "Infraestrutura analisada apresenta características modernas")

            # Garantir pelo menos 3 insights
            while len(insights) < 3:
                insights.append(
                    "Análise detalhada revelou oportunidades estratégicas importantes")

            return insights[:5]  # Máximo 5 insights

        except Exception as e:
            logger.error(f"Erro na extração de insights: {str(e)}")
            return ["Análise dos dados revelou informações valiosas para tomada de decisão"]

    def _generate_recommendations(self, data: Dict[str, Any]) -> List[str]:
        """Gera recomendações baseadas na análise."""
        try:
            recommendations = []
            detailed_analysis = data.get("detailed_analysis", {})

            # Recomendações de performance
            digital_presence = detailed_analysis.get("digital_presence", {})
            if digital_presence:
                perf_score = digital_presence.get("performance_score", 0)
                if perf_score < 80:
                    recommendations.append(
                        "Implementar otimizações de performance para melhorar velocidade de carregamento")

                seo_score = digital_presence.get("seo_score", 0)
                if seo_score < 80:
                    recommendations.append(
                        "Melhorar práticas de SEO para aumentar visibilidade nos motores de busca")

            # Recomendações de segurança
            tech_infra = detailed_analysis.get("technical_infrastructure", {})
            if tech_infra:
                security_level = tech_infra.get("security_level", 0)
                if security_level < 80:
                    recommendations.append(
                        "Reforçar medidas de segurança e implementar melhores práticas")

            # Recomendações gerais
            recommendations.append(
                "Monitorar continuamente métricas de performance e segurança")
            recommendations.append(
                "Considerar implementação de analytics avançados para insights de negócio")

            return recommendations[:5]  # Máximo 5 recomendações

        except Exception as e:
            logger.error(f"Erro na geração de recomendações: {str(e)}")
            return ["Implementar monitoramento contínuo para acompanhar evolução das métricas"]

    def _assess_risks(self, data: Dict[str, Any]) -> List[str]:
        """Avalia riscos baseados nos dados analisados."""
        risks = []

        technical_data = data.get("technical_data", {})
        if technical_data:
            security = technical_data.get("security", {})
            if security.get("security_score", 0) < 60:
                risks.append(
                    "Risco de segurança elevado devido a vulnerabilidades identificadas")

        diagnostic_data = data.get("diagnostic_data", {})
        if diagnostic_data:
            performance = diagnostic_data.get("performance_metrics", {})
            if performance.get("score", 0) < 60:
                risks.append(
                    "Risco de perda de usuários devido à baixa performance")

        return risks

    def _identify_opportunities(self, data: Dict[str, Any]) -> List[str]:
        """Identifica oportunidades baseadas nos dados."""
        opportunities = []

        market_data = data.get("market_data", {})
        if market_data and not market_data.get("error"):
            opportunities.append(
                "Oportunidades de expansão no mercado digital")

        competitive_data = data.get("competitive_data", {})
        if competitive_data and not competitive_data.get("error"):
            opportunities.append("Potencial de diferenciação competitiva")

        return opportunities

    async def _generate_competitive_analysis(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Gera análise competitiva."""
        return {"detailed_analysis": {"competitive_landscape": "Análise competitiva em desenvolvimento"}}

    async def _generate_market_opportunities(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Gera análise de oportunidades de mercado."""
        return {"detailed_analysis": {"market_analysis": "Análise de mercado em desenvolvimento"}}

    async def _generate_digital_growth(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Gera análise de crescimento digital."""
        return {"detailed_analysis": {"digital_strategy": "Estratégia digital em desenvolvimento"}}

    async def _generate_funding_analysis(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Gera análise de funding e investimentos."""
        return {"detailed_analysis": {"funding_landscape": "Análise de funding em desenvolvimento"}}

    async def _generate_business_model(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Gera análise de modelo de negócio."""
        return {"detailed_analysis": {"business_model": "Análise de modelo de negócio em desenvolvimento"}}

    async def _generate_executive_dashboard(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Gera dashboard executivo."""
        return {"detailed_analysis": {"dashboard_metrics": "Dashboard executivo em desenvolvimento"}}

    def _generate_tech_recommendations(self, data: Dict[str, Any]) -> List[str]:
        """Gera recomendações técnicas."""
        return ["Implementar práticas DevOps modernas", "Considerar migração para cloud"]

    def _analyze_architecture(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Analisa arquitetura técnica."""
        return {"architecture_score": 75, "recommendations": ["Microserviços", "API Gateway"]}

    def _apply_template_customizations(self, content: Dict[str, Any], customizations: Dict[str, Any]) -> Dict[str, Any]:
        """Aplica customizações específicas do template."""
        # Implementar lógica de customização
        return content
