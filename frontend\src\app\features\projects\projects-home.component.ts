/* eslint-disable @typescript-eslint/no-redundant-type-constituents */
/* eslint-disable @typescript-eslint/member-ordering */
/* eslint-disable @angular-eslint/prefer-signals */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { DecimalPipe, NgC<PERSON>, NgFor, NgIf } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { Component, OnDestroy, OnInit, signal, ViewEncapsulation } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';

import { MenuItem, MessageService } from 'primeng/api';
import { AvatarModule } from 'primeng/avatar';
import { AvatarGroupModule } from 'primeng/avatargroup';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { ChipModule } from 'primeng/chip';
import { DataViewModule } from 'primeng/dataview';
import { DialogModule } from 'primeng/dialog';
import { DrawerModule } from 'primeng/drawer';
import { InputTextModule } from 'primeng/inputtext';
import { Menu, MenuModule } from 'primeng/menu';
import { PaginatorModule } from 'primeng/paginator';
import { ProgressBarModule } from 'primeng/progressbar';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { SelectModule } from 'primeng/select';
import { TabViewModule } from 'primeng/tabview';
import { TagModule } from 'primeng/tag';
import { TooltipModule } from 'primeng/tooltip';

import { MarkdownViewerComponent } from '../../shared/components/markdown-viewer/markdown-viewer.component';
import { MermaidDiagramUnifiedComponent } from '../../shared/components/mermaid-diagram';

import { IClient } from '../clients/client.interface';
import { ClientsService } from '../clients/clients.service';
import { PdfModalComponent } from './components/pdf-modal/pdf-modal.component';
import { PodcastModalComponent } from './components/podcast-modal/podcast-modal.component';
import { ProjectCardComponent } from './components/project-card/project-card.component';
import {
	IAgenteJustificativa,
	IEstimativaDetalhada,
	IGeneratedProject,
	IGeneratedProjectFilter,
	IProjectCardClickEvent,
} from './interfaces/generated-project.interface';
import { IPageChangeEvent, IPageSizeOption, ISortOption, IStatusOption } from './project.interface';
import { GeneratedProjectsService } from './services/generated-projects.service';

@Component({
	selector: 'app-projects-home',
	standalone: true,
	imports: [
		DecimalPipe,
		NgClass,
		NgFor,

		NgIf,
		FormsModule,
		ButtonModule,
		CardModule,
		DataViewModule,
		SelectModule,
		InputTextModule,
		TabViewModule,
		TagModule,
		TooltipModule,
		ChipModule,
		ProgressBarModule,
		ProgressSpinnerModule,
		AvatarModule,
		AvatarGroupModule,
		MenuModule,
		PaginatorModule,
		ReactiveFormsModule,
		DialogModule,
		ProjectCardComponent,
		DrawerModule,
		MermaidDiagramUnifiedComponent,
		MarkdownViewerComponent,
		PodcastModalComponent,
		PdfModalComponent,
	],
	templateUrl: './projects-home.component.html',
	styleUrls: ['./projects-home.component.scss'],
	encapsulation: ViewEncapsulation.None,
	styles: [
		`
			:host ::ng-deep .p-dataview .p-dataview-header {
				background-color: transparent;
				border: none;
				padding: 0;
			}

			:host ::ng-deep .p-card .p-card-content {
				padding-top: 1rem;
				padding-bottom: 1rem;
			}

			:host ::ng-deep .p-select-panel .p-select-items .p-select-item {
				padding: 0.75rem 1.25rem;
				white-space: nowrap;
			}

			:host ::ng-deep .p-select-panel .p-select-items {
				padding: 0.5rem 0;
			}

			:host ::ng-deep .p-dataview .p-dataview-content {
				background: transparent;
				border: none;
				padding: 0;
			}

			:host ::ng-deep .p-paginator {
				background: white;
				border-radius: 0.5rem;
				margin-top: 1rem;
				padding: 0.5rem;
			}

			:host ::ng-deep .p-select-empty-message {
				display: none;
			}

			.text-shadow {
				text-shadow: 0px 1px 2px rgba(0, 0, 0, 0.3);
			}

			:host ::ng-deep .p-card {
				border-radius: 0.5rem;
				overflow: hidden;
			}

			:host ::ng-deep .p-card .p-card-body {
				padding: 1.25rem;
			}

			:host ::ng-deep .p-card .p-card-header {
				padding: 0;
			}

			:host ::ng-deep .p-select {
				width: 100%;
			}

			:host ::ng-deep .p-menu.p-menu-overlay {
				z-index: 1000;
			}

			:host ::ng-deep .p-menu-overlay {
				box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
			}

			@media (min-width: 768px) {
				:host ::ng-deep .p-select {
					min-width: 180px;
				}
			}
		`,
	],
})
export class ProjectsHomeComponent implements OnInit, OnDestroy {
	private destroy$ = new Subject<void>();

	public pageTitle = 'Sugestão de projetos';
	public pageDescription = 'Receba sugestões e acompanhe todos os seus projetos em um só lugar';
	public newProjectButton = 'Nova Sugestão';
	public importButton = 'Importar';
	public emptyTitle = 'Nenhuma sugestão encontrada';
	public emptyDescription = 'Tente ajustar seus filtros de busca ou crie uma nova sugestão';
	public createButton = 'Criar Sugestão';
	public modalTitle = 'Nova Sugestão de Projeto';
	public modalNameLabel = 'Nome do projeto *';
	public modalNameAria = 'Nome do projeto';
	public modalClientLabel = 'Cliente *';
	public modalClientPlaceholder = 'Selecione o cliente';
	public modalDescriptionLabel = 'Descrição';
	public modalCancel = 'Cancelar';
	public modalSave = 'Salvar';
	public detalhesProjeto: boolean = false;

	// Variáveis de estado para o novo fluxo de IA
	public aiChatHistory: { sender: string; text: string }[] = [];
	public aiChatInput = '';
	public isDescriptionEnhanced = false;
	public isProjectScopeAgreed = false;
	public aiConfidenceScore = 0;
	public isEnhancingDescription = false;

	// Signals do serviço (Angular 19)
	public projects = signal<Array<any>>([]);
	public loading = signal<boolean>(false);
	public podcastGenerating = signal<boolean>(false);

	// Signals para o modal de podcast
	public podcastModalVisible = signal<boolean>(false);
	public currentPodcastUrl = signal<string>('');
	public currentPodcastTitle = signal<string>('');

	// Signals para o modal de relatório PDF
	public pdfModalVisible = signal<boolean>(false);
	public currentPdfUrl = signal<string>('');
	public currentPdfTitle = signal<string>('');
	public pdfGenerating = signal<boolean>(false);

	public layout: 'grid' | 'list' = 'grid';
	public sortOptions: Array<ISortOption> = [
		{ label: 'Mais recentes', value: 'createdAt|desc' },
		{ label: 'Mais antigos', value: 'createdAt|asc' },
		{ label: 'Nome (A-Z)', value: 'title|asc' },
		{ label: 'Nome (Z-A)', value: 'title|desc' },
		{ label: 'Cliente (A-Z)', value: 'client|asc' },
		{ label: 'Cliente (Z-A)', value: 'client|desc' },
		{ label: 'Setor (A-Z)', value: 'sector|asc' },
		{ label: 'Setor (Z-A)', value: 'sector|desc' },
	];
	public selectedSort = this.sortOptions[0]; // Padrão: mais recentes
	public rows = 10;
	public searchQuery = '';
	public selectedStatus: IStatusOption = { label: 'Todos', value: null };
	public itemsMenu: Array<MenuItem> = [];
	public pageSizeOptions: Array<IPageSizeOption> = [
		{ label: '5 por página', value: 5 },
		{ label: '10 por página', value: 10 },
		{ label: '20 por página', value: 20 },
		{ label: '50 por página', value: 50 },
	];
	public selectedPageSize: IPageSizeOption = { label: '10 por página', value: 10 };
	public first = 0;
	public showNewProjectDialog = false;
	public newProjectForm: FormGroup;
	public clients: Array<IClient> = [];
	public clientOptions: Array<{ label: string; value: string | null }>;

	// Filtros específicos para projetos gerados
	public sectorOptions: Array<{ label: string; value: string }> = [];
	public selectedSector = '';

	public statusOptions: Array<IStatusOption> = [
		{ label: 'Todos', value: null },
		{ label: 'Novo', value: 'Novo' },
		{ label: 'Em andamento', value: 'Em andamento' },
		{ label: 'Concluído', value: 'Concluído' },
		{ label: 'Pausado', value: 'Pausado' },
	];

	constructor(
		private messageService: MessageService,
		private fb: FormBuilder,
		private generatedProjectsService: GeneratedProjectsService,
		private clientsService: ClientsService,
		private http: HttpClient,
		private router: Router,
	) {
		this.newProjectForm = this.fb.group({
			name: ['', Validators.required],
			client: [null], // Cliente agora é opcional
			description: [''],
		});

		this.clientOptions = [];
	}

	public ngOnInit(): void {
		// Inicializa signals do serviço (Angular 19)
		this.generatedProjectsService.filteredProjects$
			.pipe(takeUntil(this.destroy$))
			.subscribe(projects => this.projects.set(projects));

		this.generatedProjectsService.loading$
			.pipe(takeUntil(this.destroy$))
			.subscribe(loading => this.loading.set(loading));

		this.setupMenuItems();
		this.loadSectors();
		this.applyInitialSort();
		this.loadClients(); // Carregar clientes disponíveis
	}

	public ngOnDestroy(): void {
		this.destroy$.next();
		this.destroy$.complete();
	}

	/**
	 * Carrega setores únicos para o filtro
	 */
	private loadSectors(): void {
		this.generatedProjectsService
			.getUniqueSectors()
			.pipe(takeUntil(this.destroy$))
			.subscribe(sectors => {
				this.sectorOptions = [
					{ label: 'Todos os setores', value: '' },
					...sectors.map(sector => ({ label: sector, value: sector })),
				];
			});
	}

	/**
	 * Aplica ordenação inicial
	 */
	private applyInitialSort(): void {
		this.onSortChange({ value: this.selectedSort.value });
	}

	private setupMenuItems(): void {
		this.itemsMenu = [
			{
				label: 'Opções',
				items: [
					{
						label: 'Visualizar detalhes',
						icon: 'pi pi-info-circle',
						command: () => this.showDetails(),
					},
					{
						label: 'Editar projeto',
						icon: 'pi pi-pencil',
						command: () => this.editProject(),
					},
					{
						label: this.getReportMenuLabel(),
						icon: this.getReportMenuIcon(),
						command: () => this.generateReport(),
					},
					{
						label: 'Arquivar',
						icon: 'pi pi-inbox',
						command: () => this.archiveProject(),
					},
				],
			},
		];
	}

	public showMenuAtPosition(event: MouseEvent, menu: Menu, project?: IGeneratedProject): void {
		event.stopPropagation();

		// Armazenar o projeto selecionado quando o menu for aberto
		if (project) {
			this.projectSelected = project;
			// Atualizar menu com base no projeto selecionado
			this.setupMenuItems();
		}

		menu.toggle(event);
	}

	/**
	 * Retorna o label do menu de relatório baseado no status
	 */
	public getReportMenuLabel(): string {
		if (!this.projectSelected?._id) return 'Relatório';

		const hasReport = this.projectHasEstimate(this.projectSelected);
		const isProcessing = this.projectIsProcessingEstimate(this.projectSelected);

		if (isProcessing) {
			return 'Processando...';
		}

		return hasReport ? 'Visualizar Relatório' : 'Gerar Relatório';
	}

	/**
	 * Retorna o ícone do menu de relatório baseado no status
	 */
	public getReportMenuIcon(): string {
		if (!this.projectSelected?._id) return 'pi pi-file-pdf';

		const hasReport = this.projectHasEstimate(this.projectSelected);
		const isProcessing = this.projectIsProcessingEstimate(this.projectSelected);

		if (isProcessing) {
			return 'pi pi-spin pi-spinner';
		}

		return hasReport ? 'pi pi-eye' : 'pi pi-file-pdf';
	}

	public showDetails(): void {
		if (!this.projectSelected?._id) {
			this.messageService.add({
				severity: 'warn',
				summary: 'Nenhum projeto selecionado',
				detail: 'Selecione um projeto primeiro',
			});
			return;
		}

		// Abrir drawer com detalhes do projeto
		this.getProjetoDetalhes(this.projectSelected);
	}

	public editProject(): void {
		this.messageService.add({
			severity: 'info',
			summary: 'Editar',
			detail: 'Edição de projeto será implementada em breve',
		});
	}

	public generateReport(): void {
		// Se não há projeto selecionado, não fazer nada
		if (!this.projectSelected?._id) {
			return;
		}

		const projectId = this.projectSelected._id;
		const hasReport = this.projectHasEstimate(this.projectSelected);

		if (hasReport) {
			// Se já tem relatório, visualizar
			this.visualizarEstimativa();
		} else {
			// Se não tem relatório, gerar
			this.solicitarNovaEstimativa();
		}
	}

	public archiveProject(): void {
		this.messageService.add({
			severity: 'info',
			summary: 'Arquivar',
			detail: 'Arquivamento de projeto será implementado em breve',
		});
	}

	public onProjectCardClick(event: IProjectCardClickEvent): void {
		const { project, action } = event;

		switch (action) {
			case 'view':
				this.messageService.add({
					severity: 'info',
					summary: 'Visualizar Detalhes',
					detail: `Detalhes do projeto ${project.nome_projeto} serão exibidos em breve`,
				});
				break;
			case 'nextPhase':
				this.getProjetoDetalhes(project);
				// this.messageService.add({
				// 	severity: 'info',
				// 	summary: 'Próxima Fase',
				// 	detail: `Avançar para próxima fase do projeto ${project.nome_projeto}`,
				// });
				break;
			default:
				break;
		}
	}

	public tituloDrawer = signal<string>('');
	public justificativaDrawer = signal<string>('');
	public importanciaDrawer = signal<string>('');
	public beneficiosDrawer = signal<string | any>(null);
	public agentesJustificativasDrawer = signal<Array<IAgenteJustificativa>>([]);
	public projectSelected: any;

	// 🎯 NOVOS SIGNALS PARA ESTIMATIVA
	public estimativaVisible = signal<boolean>(false);
	public estimativaProcessando = signal<boolean>(false);
	public estimativaDetalhada = signal<IEstimativaDetalhada | null>(null);

	private getProjetoDetalhes(project: IGeneratedProject) {
		this.tituloDrawer.set(`${project.cliente_nome} - ${project.nome_projeto} (⭐ ${project.pontuacao}/100)`);
		this.justificativaDrawer.set(project.justificativa);
		this.importanciaDrawer.set(project.importancia);
		this.beneficiosDrawer.set(project.beneficios);
		this.agentesJustificativasDrawer.set(project.agentes_justificativa);
		this.projectSelected = project;
		// Atualizar menu com base no projeto selecionado
		this.setupMenuItems();
		this.detalhesProjeto = true;
	}

	public gerarEstimativa() {
		if (!this.projectSelected?._id) return;

		const projectId = this.projectSelected._id;
		const hasEstimate = this.generatedProjectsService.projectHasEstimate(projectId);

		if (hasEstimate) {
			// Se já tem estimativa, apenas visualizar
			this.visualizarEstimativa();
		} else {
			// Se não tem estimativa, gerar nova
			this.solicitarNovaEstimativa();
		}

		this.detalhesProjeto = false;
	}

	/**
	 * 🎯 Solicita nova estimativa usando Team Agno
	 */
	private async solicitarNovaEstimativa(): Promise<void> {
		if (!this.projectSelected?._id) return;

		const projectId = this.projectSelected._id;

		try {
			this.estimativaProcessando.set(true);

			// Exibir mensagem de início
			this.messageService.add({
				severity: 'info',
				summary: '🤖 Team Agno Trabalhando',
				detail: `Gerando estimativa detalhada para ${this.projectSelected.nome_projeto}. Aguarde 2-3 minutos...`,
				life: 8000,
			});

			// Solicitar estimativa via serviço
			const response = await this.generatedProjectsService.requestProjectEstimate(projectId);

			// Iniciar polling para verificar status
			this.iniciarMonitoramentoEstimativa(projectId);
		} catch (error) {
			console.error('❌ Erro ao solicitar estimativa:', error);
			this.estimativaProcessando.set(false);

			this.messageService.add({
				severity: 'error',
				summary: 'Erro ao Gerar Estimativa',
				detail: 'Não foi possível solicitar a estimativa. Tente novamente.',
				life: 5000,
			});
		}
	}

	/**
	 * 📊 Monitora status da estimativa até conclusão
	 */
	private iniciarMonitoramentoEstimativa(projectId: string): void {
		const checkStatus = async (): Promise<void> => {
			try {
				const status = await this.generatedProjectsService.getProjectEstimateStatus(projectId).toPromise();

				// Verificar ambos os nomes de status possíveis
				const isCompleted =
					status?.estimativa_status === 'completed' || status?.estimativa_status === 'concluida';
				const isError = status?.estimativa_status === 'error' || status?.estimativa_status === 'erro';
				const isProcessing =
					status?.estimativa_status === 'processing' || status?.estimativa_status === 'processando';

				if (isCompleted && status?.has_estimativa) {
					// Estimativa concluída

					this.estimativaProcessando.set(false);

					// Recarregar dados específicos do projeto
					const updatedProject = await this.generatedProjectsService.reloadSpecificProject(projectId);

					if (updatedProject && updatedProject._id === this.projectSelected?._id) {
						this.projectSelected = updatedProject;
						// Atualizar menu para mostrar o ícone do olho
						this.setupMenuItems();
					}

					this.messageService.add({
						severity: 'success',
						summary: '✅ Estimativa Concluída',
						detail: `Team Agno finalizou a análise de ${this.projectSelected?.nome_projeto}!`,
						life: 5000,
					});

					// Também recarregar lista geral
					this.generatedProjectsService.refreshProjects();
				} else if (isError) {
					// Erro na estimativa

					this.estimativaProcessando.set(false);

					this.messageService.add({
						severity: 'error',
						summary: 'Erro na Estimativa',
						detail: 'Ocorreu um erro durante a geração da estimativa.',
						life: 5000,
					});
				} else if (isProcessing) {
					// Ainda processando, verificar novamente em 10 segundos

					this.scheduleStatusCheck(checkStatus, 10000);
				} else {
					// Status desconhecido, tentar novamente

					this.scheduleStatusCheck(checkStatus, 10000);
				}
			} catch (error) {
				console.error('❌ Erro ao verificar status:', error);
				this.estimativaProcessando.set(false);

				this.messageService.add({
					severity: 'error',
					summary: 'Erro de Comunicação',
					detail: 'Erro ao verificar status da estimativa.',
					life: 3000,
				});
			}
		};

		// Iniciar verificação após 5 segundos
		this.scheduleStatusCheck(checkStatus, 5000);
	}

	/**
	 * 🕐 Helper para agendar verificação de status
	 */
	private scheduleStatusCheck(checkFunction: () => Promise<void>, delay: number): void {
		setTimeout(() => {
			checkFunction().catch(console.error);
		}, delay);
	}

	/**
	 * 👁️ Visualiza estimativa existente
	 */
	private visualizarEstimativa(): void {
		if (!this.projectSelected?._id) return;

		const projectId = this.projectSelected._id;

		// 🎯 NOVO: Primeiro, tentar usar dados do campo "projeto" estruturado
		const projetoData = this.getProjetoData();

		if (projetoData) {
			// 🎯 CALCULAR total de profissionais a partir dos stacks
			const totalProfissionais = this.calculateTotalFromStacks(projetoData.stacks);

			// 🎯 CONVERTER stacks para formato stacks_tecnologicas esperado pelo template
			const stacksTecnologicas = this.convertStacksToTechStacks(projetoData.stacks);

			// 🎯 CONVERTER para formato esperado pelo template antigo usando nomes corretos
			const estimativaCompativel: any = {
				estimativa_escopo: {
					projeto_nome: projetoData.nome || this.projectSelected.nome_projeto,
					descricao_detalhada: projetoData.descricao || this.projectSelected.resumo,
					complexidade_geral: 7, // Valor padrão
					risco_tecnico: 'Médio' as 'Alto' | 'Médio' | 'Baixo',
					cronograma: {
						// 🎯 CORRIGIDO: usar campos corretos do JSON
						duracao_meses: this.extractNumberFromString(projetoData.prazo_de_entrega) || 0,
						total_sprints: projetoData.total_de_sprints || 0,
						sprints_por_mes: projetoData.sprint_por_mes || 2,
						marcos_principais: [], // Não há no JSON atual
					},
					equipe_necessaria: {
						// 🎯 CORRIGIDO: calcular a partir dos stacks
						total_profissionais: totalProfissionais,
						formacao: this.convertStacksToFormacao(projetoData.stacks),
						custo_mensal_total: 0, // Calcular depois se necessário
						custo_projeto_total: projetoData.custos_estimados_r$?.total_estimada || 0,
					},
					// 🎯 CORRIGIDO: usar formato convertido dos stacks
					stacks_tecnologicas: stacksTecnologicas,
					investimento_total: {
						// 🎯 CORRIGIDO: usar campo correto do JSON
						total_projeto: projetoData.custos_estimados_r$?.total_estimada || 0,
						desenvolvimento: 0, // Não há breakdown no JSON atual
						infraestrutura_setup: 0,
						infraestrutura_mes: 0,
						contingencia_20pct: 0,
						valor_mensal_operacao: 0,
					},
					arquitetura: {
						diagrama_mermaid: projetoData.diagrama || '',
						tipo_arquitetura: 'Microserviços',
						tecnologias_principais: projetoData.tecnologias?.slice(0, 5) || [],
					},
					analise_riscos: {
						fator_risco_total: 5, // Valor padrão
						riscos_identificados: [],
						plano_mitigacao: '',
					},
					recomendacoes: [],
				},
			};

			this.estimativaDetalhada.set(estimativaCompativel);
			this.estimativaVisible.set(true);

			return;
		}

		// 🔄 FALLBACK: Tentar formato antigo
		const estimativa = this.generatedProjectsService.getProjectEstimate(projectId);

		if (estimativa) {
			this.estimativaDetalhada.set(estimativa);
			this.estimativaVisible.set(true);
		} else {
			this.messageService.add({
				severity: 'warn',
				summary: 'Estimativa não encontrada',
				detail: 'Não foi possível carregar a estimativa deste projeto.',
				life: 3000,
			});
		}
	}

	/**
	 * ✅ Verifica se projeto tem estimativa concluída
	 */
	public projectHasEstimate(project: IGeneratedProject | any): boolean {
		if (!project?._id) return false;
		return this.generatedProjectsService.projectHasEstimate(project._id);
	}

	/**
	 * 🔄 Verifica se projeto está processando estimativa
	 */
	public projectIsProcessingEstimate(project: IGeneratedProject | any): boolean {
		if (!project?._id) return false;
		const status = this.generatedProjectsService.getProjectEstimateCurrentStatus(project._id);
		return status === 'processando';
	}

	/**
	 * 🎯 Retorna texto do botão baseado no status da estimativa
	 */
	public getEstimateButtonText(): string {
		if (!this.projectSelected?._id) return 'Gerar Estimativa';

		if (this.estimativaProcessando()) {
			return 'Processando...';
		}

		const hasEstimate = this.projectHasEstimate(this.projectSelected);
		const isProcessing = this.projectIsProcessingEstimate(this.projectSelected);

		if (isProcessing) {
			return 'Processando...';
		}

		return hasEstimate ? 'Visualizar Estimativa' : 'Gerar Estimativa';
	}

	/**
	 * 🎯 Retorna ícone do botão baseado no status da estimativa
	 */
	public getEstimateButtonIcon(): string {
		if (!this.projectSelected?._id) return 'pi pi-calculator';

		if (this.estimativaProcessando()) {
			return 'pi pi-spin pi-spinner';
		}

		const hasEstimate = this.projectHasEstimate(this.projectSelected);
		const isProcessing = this.projectIsProcessingEstimate(this.projectSelected);

		if (isProcessing) {
			return 'pi pi-spin pi-spinner';
		}

		return hasEstimate ? 'pi pi-eye' : 'pi pi-calculator';
	}

	/**
	 * 📋 Fecha modal de estimativa
	 */
	public fecharEstimativa(): void {
		this.estimativaVisible.set(false);
		this.estimativaDetalhada.set(null);
	}

	/**
	 * 📄 Gera e exibe relatório PDF no modal
	 */
	public async exportarEstimativaPDF(): Promise<void> {
		if (!this.projectSelected?._id || !this.estimativaDetalhada()) {
			this.messageService.add({
				severity: 'warn',
				summary: 'Dados Insuficientes',
				detail: 'Não é possível gerar o PDF sem dados da estimativa.',
				life: 3000,
			});
			return;
		}

		try {
			this.pdfGenerating.set(true);

			this.messageService.add({
				severity: 'info',
				summary: 'Gerando Relatório',
				detail: 'Preparando relatório PDF profissional... Aguarde alguns segundos.',
				life: 5000,
			});

			// Gerar PDF via backend
			const response = await this.http
				.post(
					`http://localhost:8040/projects/${this.projectSelected._id}/generate-pdf`,
					{
						project_data: this.getProjetoData(),
						estimate_data: this.estimativaDetalhada(),
					},
					{ responseType: 'blob', observe: 'response' },
				)
				.toPromise();

			if (response?.body) {
				// Criar URL do blob para exibir no modal
				const pdfBlob = new Blob([response.body], { type: 'application/pdf' });
				const pdfUrl = URL.createObjectURL(pdfBlob);

				// Configurar modal PDF
				this.currentPdfUrl.set(pdfUrl);
				this.currentPdfTitle.set(`Relatório - ${this.getNomeProjetoReal()}`);
				this.pdfModalVisible.set(true);

				this.messageService.add({
					severity: 'success',
					summary: 'Relatório Gerado',
					detail: 'PDF pronto para visualização!',
					life: 3000,
				});
			}
		} catch (error) {
			console.error('❌ Erro ao gerar PDF:', error);
			this.messageService.add({
				severity: 'error',
				summary: 'Erro na Geração',
				detail: 'Não foi possível gerar o relatório PDF. Tente novamente.',
				life: 5000,
			});
		} finally {
			this.pdfGenerating.set(false);
		}
	}

	public onNewProject(): void {
		this.newProjectForm.reset();
		this.showNewProjectDialog = true;
		// Limpar estado da IA
		this.aiChatHistory = [];
		this.aiChatInput = '';
		this.isDescriptionEnhanced = false;
		this.isProjectScopeAgreed = false;
		this.isEnhancingDescription = false;
	}

	public onImport(): void {
		this.messageService.add({
			severity: 'info',
			summary: 'Importar',
			detail: 'Funcionalidade de importação será implementada em breve',
		});
	}

	public onPageChange(event: IPageChangeEvent): void {
		this.first = event.first !== undefined ? event.first : 0;
		this.rows = event.rows !== undefined ? event.rows : this.rows;
	}

	public onPageSizeChange(event: { value: IPageSizeOption }): void {
		this.rows = event.value.value;
		this.first = 0;
	}

	/**
	 * Manipula mudança na busca
	 */
	public onSearchChange(): void {
		this.updateFilter({
			search: this.searchQuery,
		});
	}

	/**
	 * Manipula mudança no filtro de setor
	 */
	public onSectorChange(): void {
		this.updateFilter({
			cliente_sector: this.selectedSector || undefined,
		});
	}

	/**
	 * Manipula mudança na ordenação
	 */
	public onSortChange(event: { value: string }): void {
		const [sortBy, sortOrder] = event.value.split('|') as [IGeneratedProjectFilter['sortBy'], 'asc' | 'desc'];

		this.updateFilter({
			sortBy,
			sortOrder,
		});
	}

	/**
	 * Atualiza filtros no serviço
	 */
	private updateFilter(filter: Partial<IGeneratedProjectFilter>): void {
		this.generatedProjectsService.updateFilter(filter);
	}

	/**
	 * Limpa todos os filtros
	 */
	public clearFilters(): void {
		this.searchQuery = '';
		this.selectedSector = '';
		this.selectedSort = this.sortOptions[0];
		this.generatedProjectsService.clearFilters();
		this.applyInitialSort();
	}

	/**
	 * Recarrega projetos
	 */
	public refreshProjects(): void {
		this.generatedProjectsService.refreshProjects();
	}

	// Métodos auxiliares para o template
	public getSeverity(status: string): string {
		switch (status) {
			case 'Novo':
				return 'success';
			case 'Em andamento':
				return 'info';
			case 'Concluído':
				return 'success';
			case 'Pausado':
				return 'warning';
			default:
				return 'info';
		}
	}

	public getScoreSeverity(score: number): string {
		if (score >= 80) {
			return 'success';
		} else if (score >= 60) {
			return 'warning';
		} else {
			return 'danger';
		}
	}

	public formatDate(dateString: string): string {
		const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'short', day: 'numeric' };
		return new Date(dateString).toLocaleDateString('pt-BR', options);
	}

	public saveNewProject(): void {
		if (this.newProjectForm.valid && this.isProjectScopeAgreed) {
			const formData = this.newProjectForm.value;

			// Buscar cliente selecionado (se houver)
			const selectedClient = formData.client ? this.clients.find(c => c.id === formData.client) : null;

			// Criar novo projeto
			const newProject = {
				nome_projeto: formData.name,
				cliente_id: formData.client || null,
				cliente_nome: selectedClient ? selectedClient.company : 'Projeto Genérico',
				cliente_sector: selectedClient ? selectedClient.sector || 'Não definido' : 'Genérico',
				resumo: formData.description.substring(0, 200) + '...',
				descricao_completa: formData.description,
				tags: ['IA Assistida', 'Nova Sugestão'],
				status: 'Novo',
				progresso: 0,
				equipe: 'A definir',
				justificativa: 'Projeto criado com assistência de IA',
				importancia: 'Alta',
				pontuacao: 85, // Pontuação inicial alta por ter sido validado pela IA
			};

			// Enviar para backend
			this.http.post<{ project_id: string }>('http://localhost:8040/projects', newProject).subscribe({
				next: response => {
					this.messageService.add({
						severity: 'success',
						summary: 'Projeto Criado!',
						detail: `O projeto "${formData.name}" foi criado com sucesso!`,
						life: 5000,
					});

					// Gerar card do projeto automaticamente
					if (response.project_id) {
						this.generateProjectCard(response.project_id, formData.name);
					}

					// Recarregar lista de projetos
					this.generatedProjectsService.refreshProjects();

					// Fechar modal e limpar
					this.showNewProjectDialog = false;
					this.cancelNewProject();
				},
				error: error => {
					console.error('Erro ao salvar projeto:', error);
					this.messageService.add({
						severity: 'error',
						summary: 'Erro ao Salvar',
						detail: 'Não foi possível salvar o projeto. Tente novamente.',
						life: 5000,
					});
				},
			});
		} else if (!this.isProjectScopeAgreed) {
			this.messageService.add({
				severity: 'warn',
				summary: 'Escopo não validado',
				detail: 'Converse com a IA até ela compreender o escopo do projeto.',
				life: 5000,
			});
		} else {
			this.newProjectForm.markAllAsTouched();
			this.messageService.add({
				severity: 'error',
				summary: 'Erro de Validação',
				detail: 'Por favor, preencha todos os campos obrigatórios.',
			});
		}
	}

	public cancelNewProject(): void {
		this.showNewProjectDialog = false;
		this.newProjectForm.reset();
		this.aiChatHistory = [];
		this.aiChatInput = '';
		this.isDescriptionEnhanced = false;
		this.isProjectScopeAgreed = false;
		this.isEnhancingDescription = false;
	}

	/**
	 * Carrega lista de clientes disponíveis
	 */
	private loadClients(): void {
		this.clientsService.getClients().subscribe({
			next: (clients: Array<IClient>) => {
				this.clients = clients;
				this.clientOptions = [
					{ label: 'Sem cliente (projeto genérico)', value: null },
					...clients.map(c => ({
						label: c.company,
						value: c.id, // Usar ID ao invés do nome
					})),
				];
			},
			error: error => {
				console.error('Erro ao carregar clientes:', error);
				this.clients = [];
				// Mesmo sem clientes, permitir criar projeto genérico
				this.clientOptions = [{ label: 'Sem cliente (projeto genérico)', value: null }];
			},
		});
	}

	/**
	 * Redireciona para tela de cadastro de cliente
	 */
	public redirectToNewClient(): void {
		this.showNewProjectDialog = false;
		this.router.navigate(['/clients']);
		this.messageService.add({
			severity: 'info',
			summary: 'Redirecionando',
			detail: 'Você será redirecionado para cadastrar um novo cliente.',
			life: 3000,
		});
	}

	/**
	 * Melhora a descrição usando IA
	 */
	public enhanceDescription(): void {
		const currentDescription = this.newProjectForm.get('description')?.value;
		if (!currentDescription || currentDescription.trim().length < 10) {
			this.messageService.add({
				severity: 'warn',
				summary: 'Descrição muito curta',
				detail: 'Por favor, escreva pelo menos 10 caracteres na descrição.',
				life: 4000,
			});
			return;
		}

		// Ativar loading
		this.isEnhancingDescription = true;

		// Mostrar loading
		this.messageService.add({
			severity: 'info',
			summary: 'Melhorando descrição',
			detail: 'A IA está analisando e melhorando sua descrição...',
			life: 3000,
		});

		// Chamar API de IA para melhorar descrição
		this.http
			.post<{ enhanced_description: string }>('http://localhost:8040/ai/enhance-description', {
				description: currentDescription,
			})
			.subscribe({
				next: response => {
					this.newProjectForm.patchValue({
						description: response.enhanced_description,
					});
					this.isDescriptionEnhanced = true;
					this.isEnhancingDescription = false;

					this.messageService.add({
						severity: 'success',
						summary: 'Descrição melhorada!',
						detail: 'A IA melhorou e enriqueceu sua descrição.',
						life: 4000,
					});

					// Iniciar conversa com IA automaticamente
					this.startAiConversation();
				},
				error: error => {
					console.error('Erro ao melhorar descrição:', error);
					this.isEnhancingDescription = false;
					this.messageService.add({
						severity: 'error',
						summary: 'Erro',
						detail: 'Não foi possível melhorar a descrição. Tente novamente.',
						life: 5000,
					});
				},
			});
	}

	/**
	 * Inicia conversa com IA sobre o projeto
	 */
	private startAiConversation(): void {
		const projectName = this.newProjectForm.get('name')?.value;
		const description = this.newProjectForm.get('description')?.value;

		if (!description) return;

		// Fazer primeira chamada para a IA analisar o contexto
		this.sendAiMessage('Analisar projeto inicial', true);
	}

	/**
	 * Envia mensagem para IA
	 */
	public sendAiMessage(messageOverride?: string, isAutomatic = false): void {
		if (!isAutomatic && (!this.aiChatInput.trim() || this.isProjectScopeAgreed)) return;

		const userMessage = messageOverride || this.aiChatInput.trim();

		// Adicionar mensagem do usuário ao histórico apenas se não for automática
		if (!isAutomatic) {
			this.aiChatHistory.push({
				sender: 'user',
				text: userMessage,
			});
		}

		// Limpar input
		this.aiChatInput = '';

		// Preparar contexto para IA
		const context = {
			project_name: this.newProjectForm.get('name')?.value,
			description: this.newProjectForm.get('description')?.value,
			chat_history: this.aiChatHistory.map(msg => ({
				sender: msg.sender === 'user' ? 'user' : 'assistant',
				text: msg.text,
			})),
			user_message: userMessage,
		};

		// Chamar API de IA
		this.http
			.post<{
				ai_response: string;
				scope_understood: boolean;
				refined_scope?: string;
				confidence_score?: number;
			}>('http://localhost:8040/ai/chat-project-scope', context)
			.subscribe({
				next: response => {
					// Atualizar score de confiança
					if (response.confidence_score !== undefined) {
						this.aiConfidenceScore = response.confidence_score;
					}

					// Adicionar resposta da IA ao histórico
					this.aiChatHistory.push({
						sender: 'ai',
						text: response.ai_response,
					});

					// Se confiança >= 90% e usuário confirmou satisfação
					if (response.scope_understood) {
						this.isProjectScopeAgreed = true;

						// Atualizar descrição com escopo refinado se disponível
						if (response.refined_scope) {
							this.newProjectForm.patchValue({
								description: response.refined_scope,
							});
						}

						this.messageService.add({
							severity: 'success',
							summary: 'Escopo compreendido!',
							detail: 'A IA compreendeu o projeto. Você pode salvar agora.',
							life: 5000,
						});
					} else if (this.aiConfidenceScore >= 80) {
						// Se a IA tem 80% de confiança mas usuário não confirmou, continuar conversando
						if (userMessage.toLowerCase().includes('não') || userMessage.toLowerCase().includes('nao')) {
							this.messageService.add({
								severity: 'info',
								summary: 'Continuando análise',
								detail: 'Vamos continuar esclarecendo o escopo do projeto.',
								life: 3000,
							});
						}
					}
				},
				error: error => {
					console.error('Erro ao conversar com IA:', error);
					this.aiChatHistory.push({
						sender: 'ai',
						text: 'Desculpe, tive um problema ao processar sua mensagem. Pode tentar novamente?',
					});
				},
			});
	}

	// 🎯 ATUALIZADO: Métodos auxiliares usando novo sistema unificado
	public getEstimativaEscopo() {
		// Tenta primeiro o formato novo, depois o antigo
		const projetoData = this.getProjetoData();
		if (projetoData) return projetoData;

		// Fallback para formato antigo
		const estimativa = this.estimativaDetalhada();
		return estimativa?.estimativa_escopo || null;
	}

	public hasEstimativaEscopo(): boolean {
		return !!this.getEstimativaEscopo();
	}

	// Métodos para evitar erros de undefined no template
	public getArquitetura() {
		return this.getEstimativaEscopo()?.arquitetura || null;
	}

	public getStacksTecnologicas() {
		return this.getEstimativaEscopo()?.stacks_tecnologicas || [];
	}

	public getCronograma() {
		return this.getEstimativaEscopo()?.cronograma || null;
	}

	public getEquipeNecessaria() {
		return this.getEstimativaEscopo()?.equipe_necessaria || null;
	}

	public getInvestimentoTotal() {
		return this.getEstimativaEscopo()?.investimento_total || null;
	}

	// Removido - duplicado com linha 1234

	public getRecomendacoes() {
		return this.getEstimativaEscopo()?.recomendacoes || [];
	}

	// 📊 Métodos para interface rica de estimativas

	/**
	 * Conta total de tecnologias
	 */
	public getTechCount(): number {
		const projetoData = this.getProjetoData();
		if (projetoData?.tecnologias && Array.isArray(projetoData.tecnologias)) {
			// 🎯 CORRIGIDO: usar array direto de tecnologias
			return projetoData.tecnologias.length;
		}

		// Fallback para formato antigo
		const stacks = this.estimativaDetalhada()?.estimativa_escopo?.stacks_tecnologicas || [];
		return stacks.reduce((total, stack) => total + (stack.tecnologias?.length || 0), 0);
	}

	/**
	 * Calcula percentagem de custo para cada categoria
	 */
	public getCostPercentage(category: string): number {
		// 🎯 PRIORIDADE: Usar dados reais do campo "projeto"
		const projeto = this.getProjetoData();
		if (projeto?.custos_estimados_r$?.total_estimada) {
			return this.getCostPercentageNew(category);
		}

		// Fallback para formato antigo
		const investimento = this.estimativaDetalhada()?.estimativa_escopo?.investimento_total;
		if (!investimento || !investimento.total_projeto) return 0;

		const total = investimento.total_projeto;
		let value = 0;

		switch (category) {
			case 'desenvolvimento':
				value = investimento.desenvolvimento || 0;
				break;
			case 'infraestrutura':
				value = investimento.infraestrutura_setup || 0;
				break;
			case 'contingencia':
				value = investimento.contingencia_20pct || 0;
				break;
		}

		return Math.round((value / total) * 100);
	}

	/**
	 * Gera cor para membro da equipe baseado no índice
	 */
	public getTeamMemberColor(index: number): string {
		const colors = [
			'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
			'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
			'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
			'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
			'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
			'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
			'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
			'linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%)',
		];
		return colors[index % colors.length];
	}

	/**
	 * Gera cor para sprint baseado no número
	 */
	public getSprintColor(sprintNumber: number): string {
		const hue = ((sprintNumber - 1) * 45) % 360;
		return `hsl(${hue}, 70%, 55%)`;
	}

	/**
	 * Calcula percentagem de progresso baseado na sprint
	 */
	public getProgressPercentage(sprintNumber: number): number {
		const cronograma = this.estimativaDetalhada()?.estimativa_escopo?.cronograma;
		if (!cronograma?.total_sprints) return 0;

		return Math.round((sprintNumber / cronograma.total_sprints) * 100);
	}

	/**
	 * Gera diagrama de arquitetura em texto
	 */
	public getArchitectureDiagram(): string {
		// 🎯 PRIORIDADE: Primeiro tenta acessar diagrama_sistema (novo formato)
		const projetoData = this.getProjetoData();
		if (projetoData?.diagrama_sistema) {
			let diagrama = projetoData.diagrama_sistema;
			// Remove prefixo "mermaid" se presente
			if (diagrama.startsWith('mermaid\n')) {
				diagrama = diagrama.substring(8); // Remove "mermaid\n"
			}
			return diagrama;
		}

		// Fallback para diagrama legado
		if (projetoData?.diagrama) {
			let diagrama = projetoData.diagrama;
			// Remove prefixo "mermaid" se presente
			if (diagrama.startsWith('mermaid\n')) {
				diagrama = diagrama.substring(8); // Remove "mermaid\n"
			}
			return diagrama;
		}

		// Fallback para diagrama da arquitetura
		const arquitetura = this.estimativaDetalhada()?.estimativa_escopo?.arquitetura;
		if (arquitetura?.diagrama_mermaid) {
			return arquitetura.diagrama_mermaid;
		}

		// Último fallback: gerar diagrama básico
		if (arquitetura) {
			return `
graph TD
    A[Frontend] --> B[Backend API]
    B --> C[Banco de Dados]
    B --> D[Cache]
    A --> E[CDN]

    subgraph "Tecnologias"
        F[${arquitetura.tecnologias_principais?.join(', ') || 'N/A'}]
    end

    subgraph "Arquitetura ${(arquitetura as any).tipo_arquitetura || arquitetura.tipo || 'Microserviços'}"
        B
        C
        D
    end
			`.trim();
		}

		return 'Diagrama não disponível';
	}

	/**
	 * Classe CSS para categorias de tecnologia
	 */
	public getTechCategoryClass(categoria: string): string {
		const categoryClasses: { [key: string]: string } = {
			Frontend: 'bg-blue-100 text-blue-800',
			Backend: 'bg-green-100 text-green-800',
			Database: 'bg-purple-100 text-purple-800',
			DevOps: 'bg-orange-100 text-orange-800',
			Mobile: 'bg-pink-100 text-pink-800',
			API: 'bg-yellow-100 text-yellow-800',
			Cache: 'bg-red-100 text-red-800',
			Monitoramento: 'bg-indigo-100 text-indigo-800',
		};

		return categoryClasses[categoria] || 'bg-gray-100 text-gray-800';
	}

	/**
	 * Conta palavras na descrição
	 */
	public getWordCount(): number {
		const descricao = this.getEstimativaEscopo()?.descricao_detalhada || '';
		return descricao.split(/\s+/).filter((word: string) => word.length > 0).length;
	}

	/**
	 * Estima número de funcionalidades
	 */
	public getFunctionalityCount(): number {
		const stacks = this.estimativaDetalhada()?.estimativa_escopo?.stacks_tecnologicas || [];
		return (
			stacks.length + (this.estimativaDetalhada()?.estimativa_escopo?.cronograma?.marcos_principais?.length || 0)
		);
	}

	/**
	 * Conta integrações estimadas
	 */
	public getIntegrationCount(): number {
		const stacks = this.estimativaDetalhada()?.estimativa_escopo?.stacks_tecnologicas || [];
		return stacks.filter(
			stack =>
				stack.categoria?.includes('API') ||
				stack.categoria?.includes('Integração') ||
				stack.tecnologias?.some(tech => tech.includes('API')),
		).length;
	}

	/**
	 * Calcula complexidade geral do projeto
	 */
	public getOverallComplexity(): number {
		const stacks = this.estimativaDetalhada()?.estimativa_escopo?.stacks_tecnologicas || [];
		if (stacks.length === 0) return 0;

		const totalComplexity = stacks.reduce((sum, stack) => sum + (stack.complexidade || 0), 0);
		return Math.round(totalComplexity / stacks.length);
	}

	/**
	 * Texto do nível de risco
	 */
	public getRiskLevelText(): string {
		const risco = this.estimativaDetalhada()?.estimativa_escopo?.analise_riscos?.fator_risco_total || 0;

		if (risco <= 3) return 'Baixo';
		if (risco <= 6) return 'Médio';
		if (risco <= 8) return 'Alto';
		return 'Crítico';
	}

	/**
	 * Severidade de um risco específico
	 */
	public getRiskSeverity(index: number): string {
		const severities = ['Alto', 'Médio', 'Baixo', 'Médio', 'Alto'];
		return severities[index % severities.length];
	}

	/**
	 * Classe CSS para severidade de risco
	 */
	public getRiskSeverityClass(index: number): string {
		const severity = this.getRiskSeverity(index);
		const classes: { [key: string]: string } = {
			Alto: 'bg-red-100 text-red-800',
			Médio: 'bg-yellow-100 text-yellow-800',
			Baixo: 'bg-green-100 text-green-800',
		};
		return classes[severity] || 'bg-gray-100 text-gray-800';
	}

	/**
	 * Copia texto para área de transferência
	 */
	public async copyToClipboard(text: string): Promise<void> {
		try {
			await navigator.clipboard.writeText(text);
			this.messageService.add({
				severity: 'success',
				summary: 'Copiado!',
				detail: 'Conteúdo copiado para a área de transferência',
			});
		} catch {
			this.messageService.add({
				severity: 'error',
				summary: 'Erro',
				detail: 'Falha ao copiar conteúdo',
			});
		}
	}

	/**
	 * Copia resumo do projeto
	 */
	public async copyProjectSummary(): Promise<void> {
		const estimativa = this.estimativaDetalhada();
		if (!estimativa?.estimativa_escopo) return;

		const escopo = estimativa.estimativa_escopo;
		const projeto = this.projectSelected;

		const summary = `
📊 RESUMO DO PROJETO: ${projeto?.nome_projeto || 'N/A'}

💰 INVESTIMENTO TOTAL: R$ ${escopo.investimento_total?.total_projeto?.toLocaleString('pt-BR') || 'N/A'}

⏱️ CRONOGRAMA:
• Duração: ${escopo.cronograma?.duracao_meses || 0} meses
• Sprints: ${escopo.cronograma?.total_sprints || 0}

👥 EQUIPE:
• Total: ${escopo.equipe_necessaria?.total_profissionais || 0} profissionais
• Custo mensal: R$ ${escopo.equipe_necessaria?.custo_mensal_total?.toLocaleString('pt-BR') || 'N/A'}

🏗️ ARQUITETURA: ${escopo.arquitetura?.tipo || 'N/A'}

⚠️ RISCO: ${escopo.analise_riscos?.fator_risco_total || 0}/10 (${this.getRiskLevelText()})

🛠️ TECNOLOGIAS: ${this.getTechCount()} tecnologias identificadas

📋 DESCRIÇÃO:
${escopo.descricao_detalhada || 'Não disponível'}
		`.trim();

		await this.copyToClipboard(summary);
	}

	// 🎯 NOVO: Método limpo para acessar dados do projeto estruturado
	public getProjetoData(): any {
		if (!this.projectSelected) {
			return null;
		}

		// 1. Primeiro: Tenta acessar o campo 'projeto' estruturado (novo formato)
		if (this.projectSelected.projeto) {
			return this.projectSelected.projeto;
		}

		// 2. Fallback: Se existe estimativa_detalhada, usa essa estrutura
		if (this.projectSelected.estimativa_detalhada?.estimativa_escopo) {
			return this.projectSelected.estimativa_detalhada.estimativa_escopo;
		}

		// 3. Último fallback: Parse do JSON dentro de estimate_resultado (formato antigo)
		if (this.projectSelected.estimate_resultado?.resultado_completo) {
			try {
				const resultContent = this.projectSelected.estimate_resultado.resultado_completo;
				const startMarker = '<ESCOPO_DO_PROJETO>';
				const endMarker = '</ESCOPO_DO_PROJETO>';

				const startIndex = resultContent.indexOf(startMarker);
				const endIndex = resultContent.indexOf(endMarker);

				if (startIndex !== -1 && endIndex !== -1) {
					const jsonStr = resultContent.substring(startIndex + startMarker.length, endIndex).trim();
					const parsed = JSON.parse(jsonStr);

					return parsed.projeto || parsed;
				}
			} catch (error) {
				console.warn('⚠️ Erro no fallback de parse:', error);
			}
		}

		return null;
	}

	// Método para compatibilidade (alias)
	public getEstimateData(): any {
		return this.getProjetoData();
	}

	// 🆕 NOVOS MÉTODOS PARA O FORMATO ATUAL

	/**
	 * Converte string de requisitos em array
	 */
	public getRequisitosArray(): string[] {
		const projeto = this.getProjetoData();
		if (!projeto?.requisitos) return [];

		// Se já for um array, retorna direto
		if (Array.isArray(projeto.requisitos)) {
			return projeto.requisitos;
		}

		// Se for string, divide por vírgula
		if (typeof projeto.requisitos === 'string') {
			return projeto.requisitos.split(',').map((req: string) => req.trim());
		}

		// Se for objeto com funcionais/não funcionais
		if (projeto.requisitos.funcionais || projeto.requisitos['não_funcionais']) {
			return [];
		}

		return [];
	}

	/**
	 * Retorna custos estimados do novo formato
	 */
	public getCustosEstimados(): any {
		const projeto = this.getProjetoData();
		return projeto?.custos_estimados_r$ || null;
	}

	/**
	 * Retorna roadmap do projeto
	 */
	public getRoadmap(): any {
		const projeto = this.getProjetoData();
		return projeto?.roadmap || null;
	}

	/**
	 * Retorna análise de riscos
	 */
	public getAnaliseRiscos(): any {
		const projeto = this.getProjetoData();
		return projeto?.analise_riscos || null;
	}

	/**
	 * Retorna fases do roadmap para timeline
	 */
	public getTimelineFases(): any[] {
		const roadmap = this.getRoadmap();
		return roadmap?.fases || [];
	}

	/**
	 * Calcula percentual de custo baseado nos novos dados
	 */
	public getCostPercentageNew(category: string): number {
		const custos = this.getCustosEstimados();
		if (!custos?.total_estimada) return 0;

		const total = custos.total_estimada;
		let value = 0;

		switch (category) {
			case 'desenvolvimento':
				// Soma todos os custos de desenvolvimento (Frontend, Backend, etc)
				const devRoles = ['Frontend', 'Backend', 'Data Engineer', 'Arquiteto de Software'];
				value = Object.entries(custos.detalhamento || {})
					.filter(([key]) => devRoles.some(role => key.includes(role)))
					.reduce((sum, [, val]) => sum + (val as number), 0);
				break;
			case 'infraestrutura':
				// DevOps e relacionados
				value = custos.detalhamento?.DevOps || 0;
				break;
			case 'contingencia':
				// 20% do total
				value = total * 0.2;
				break;
		}

		return Math.round((value / total) * 100);
	}

	/**
	 * Retorna nome real do projeto
	 */
	public getNomeProjetoReal(): string {
		const projeto = this.getProjetoData();
		return projeto?.nome || this.projectSelected?.nome_projeto || 'Projeto sem nome';
	}

	/**
	 * Formata valor monetário
	 */
	public formatCurrency(value: number): string {
		return new Intl.NumberFormat('pt-BR', {
			style: 'currency',
			currency: 'BRL',
		}).format(value);
	}

	/**
	 * Retorna total de tecnologias
	 */
	public getTotalTecnologias(): number {
		const projeto = this.getProjetoData();
		return projeto?.tecnologias?.length || 0;
	}

	/**
	 * Retorna riscos como array
	 */
	public getRiscosArray(): string[] {
		const analiseRiscos = this.getAnaliseRiscos();
		return analiseRiscos?.riscos || [];
	}

	/**
	 * Retorna mitigações como array
	 */
	public getMitigacoesArray(): string[] {
		const analiseRiscos = this.getAnaliseRiscos();
		return analiseRiscos?.mitigacoes || [];
	}

	/**
	 * Calcula fator de risco baseado nos dados reais
	 */
	public calculateRiskFactor(): number {
		const projeto = this.getProjetoData();

		// Se tem análise de riscos específica, usa ela
		if (projeto?.analise_riscos?.fator_risco_total) {
			return projeto.analise_riscos.fator_risco_total;
		}

		// Senão, calcula baseado na complexidade do projeto
		const totalSprints = projeto?.total_de_sprints || 0;
		const totalTechs = projeto?.tecnologias?.length || 0;
		const totalDevs = this.getTotalTeamSizeDynamic();

		// Fórmula simples: mais sprints, tecnologias e desenvolvedores = maior risco
		let riskFactor = 3; // Base baixa

		if (totalSprints > 15) riskFactor += 2;
		if (totalSprints > 20) riskFactor += 1;

		if (totalTechs > 8) riskFactor += 1;
		if (totalTechs > 12) riskFactor += 1;

		if (totalDevs > 8) riskFactor += 1;
		if (totalDevs > 12) riskFactor += 1;

		return Math.min(riskFactor, 10); // Máximo 10
	}

	/**
	 * Calcula progresso da fase baseado no índice
	 */
	public getFaseProgress(index: number, totalFases: number): number {
		if (totalFases === 0) return 0;
		return Math.round(((index + 1) / totalFases) * 100);
	}

	/**
	 * Retorna cor para fase do roadmap
	 */
	public getFaseColor(index: number): string {
		const colors = [
			'#3B82F6', // blue
			'#10B981', // green
			'#F59E0B', // amber
			'#EF4444', // red
			'#8B5CF6', // purple
			'#EC4899', // pink
		];
		return colors[index % colors.length];
	}

	// Métodos auxiliares dinâmicos para interface rica (sem duplicação)
	public getTechCountDynamic(): number {
		const estimateData = this.getEstimateData();
		return estimateData?.tecnologias?.length || 0;
	}

	public getTotalDurationDynamic(): string {
		const estimateData = this.getEstimateData();
		return estimateData?.prazo_de_entrega || 'Não definido';
	}

	public getTotalTeamSizeDynamic(): number {
		const estimateData = this.getEstimateData();

		if (!estimateData?.stacks) return 0;

		const totalDevelopers = estimateData.stacks.reduce((total: number, stack: any) => {
			const stackCount = this.calculateStackDeveloperCount(stack);
			return total + stackCount;
		}, 0);
		return totalDevelopers;
	}

	public getTechStackListDynamic(): Array<string> {
		const estimateData = this.getEstimateData();
		return estimateData?.tecnologias || [];
	}

	public getInvestmentRangeDynamic(): string {
		const estimateData = this.getEstimateData();
		const total = estimateData?.custos_estimados_r$?.total_estimada || 0;
		if (total === 0) return 'A definir';

		const millions = total / 1000000;
		if (millions >= 1) {
			return `R$ ${millions.toFixed(1)}M`;
		}

		const thousands = total / 1000;
		return `R$ ${thousands.toFixed(0)}k`;
	}

	public formatCurrencyDynamic(value: number): string {
		return this.formatCurrency(value);
	}

	/**
	 * Calcula valor específico de custo para uma categoria
	 */
	public getCostValue(category: string): number {
		const projeto = this.getProjetoData();
		if (projeto?.custos_estimados_r$?.total_estimada) {
			const total = projeto.custos_estimados_r$.total_estimada;
			const percentage = this.getCostPercentageNew(category);
			return (total * percentage) / 100;
		}

		// Fallback para formato antigo
		const investimento = this.estimativaDetalhada()?.estimativa_escopo?.investimento_total;
		if (!investimento || !investimento.total_projeto) return 0;

		switch (category) {
			case 'desenvolvimento':
				return investimento.desenvolvimento || 0;
			case 'infraestrutura':
				return investimento.infraestrutura_setup || 0;
			case 'contingencia':
				return investimento.contingencia_20pct || 0;
			default:
				return 0;
		}
	}

	/**
	 * Retorna stacks que possuem diagramas específicos
	 */
	public getStacksWithDiagrams(): any[] {
		const projeto = this.getProjetoData();
		if (!projeto?.stacks) return [];

		return projeto.stacks
			.filter((stack: any) => {
				// Suporte para ambos os formatos: novo (diagrama_sequencia) e legado (diagrama)
				const diagrama = stack.diagrama_sequencia || stack.diagrama;
				return diagrama && diagrama.trim() !== '';
			})
			.map((stack: any) => {
				let diagrama = stack.diagrama_sequencia || stack.diagrama;
				// Remove prefixo "mermaid" se presente
				if (diagrama && diagrama.startsWith('mermaid\n')) {
					diagrama = diagrama.substring(8); // Remove "mermaid\n"
				}
				return {
					...stack,
					// Normaliza o campo diagrama para compatibilidade
					diagrama: diagrama,
				};
			});
	}

	/**
	 * Retorna classe CSS para ícone da stack
	 */
	public getStackIconClass(stackName: string): string {
		const stackClasses: { [key: string]: string } = {
			Frontend: 'text-blue-500',
			Backend: 'text-green-500',
			DevOps: 'text-orange-500',
			Infraestrutura: 'text-orange-500',
			Database: 'text-purple-500',
			Mobile: 'text-pink-500',
			API: 'text-yellow-500',
			Cache: 'text-red-500',
			Monitoramento: 'text-indigo-500',
		};

		// Busca por palavras-chave no nome da stack
		for (const [key, className] of Object.entries(stackClasses)) {
			if (stackName.toLowerCase().includes(key.toLowerCase())) {
				return className;
			}
		}

		return 'text-gray-500'; // Cor padrão
	}

	/**
	 * Retorna cor para membro da equipe (versão dinâmica)
	 */
	public getTeamMemberColorDynamic(index: number): string {
		return this.getTeamMemberColor(index);
	}

	/**
	 * Classe CSS para categorias de tecnologia (versão dinâmica)
	 */
	public getTechCategoryClassDynamic(tech: string): string {
		// Mapear tecnologias para categorias
		const techCategories: { [key: string]: string } = {
			'React': 'Frontend',
			'Vue': 'Frontend',
			'Angular': 'Frontend',
			'Node.js': 'Backend',
			'Express': 'Backend',
			'Python': 'Backend',
			'Django': 'Backend',
			'PostgreSQL': 'Database',
			'MySQL': 'Database',
			'MongoDB': 'Database',
			'Docker': 'DevOps',
			'Kubernetes': 'DevOps',
			'AWS': 'DevOps',
			'Redis': 'Cache',
		};

		const category = techCategories[tech] || 'API';
		return this.getTechCategoryClass(category);
	}

	public getWordCountDynamic(): number {
		const estimateData = this.getEstimateData();
		const text = estimateData?.descricao || '';
		return text.split(/\s+/).filter((word: string) => word.length > 0).length;
	}

	public getFunctionalityCountDynamic(): number {
		const estimateData = this.getEstimateData();
		// Estimar baseado no número de stacks
		if (estimateData?.stacks) {
			return estimateData.stacks.length * 3; // Estimativa de 3 funcionalidades por stack
		}
		return 0;
	}

	public getIntegrationCountDynamic(): number {
		const estimateData = this.getEstimateData();
		// Contar tecnologias que são tipicamente de integração
		const integrationTechs = ['API', 'REST', 'GraphQL', 'Kafka', 'RabbitMQ', 'AWS', 'Google Cloud'];
		return (
			estimateData?.tecnologias?.filter((tech: string) =>
				integrationTechs.some(intTech => tech.includes(intTech)),
			).length || 0
		);
	}

	public getOverallComplexityDynamic(): string {
		const estimateData = this.getEstimateData();
		const totalSprints = estimateData?.total_de_sprints || 0;

		if (totalSprints >= 20) return 'Alta';
		if (totalSprints >= 12) return 'Média';
		return 'Baixa';
	}

	public getArchitectureDiagramDynamic(): string {
		const estimateData = this.getEstimateData();
		let diagrama = estimateData?.diagrama_sistema || estimateData?.diagrama || this.getArchitectureDiagram();

		// Converter C4Context para flowchart se necessário
		if (diagrama && diagrama.includes('C4Context')) {
			diagrama = this.convertC4ToFlowchart(diagrama);
		}

		// Remove prefixo "mermaid" se presente
		if (diagrama && diagrama.startsWith('mermaid\n')) {
			diagrama = diagrama.substring(8); // Remove "mermaid\n"
		}

		return diagrama;
	}

	/**
	 * Obtém diagrama de dados (ERD) quando disponível
	 */
	public getDataDiagram(): string {
		const projetoData = this.getProjetoData();
		if (projetoData?.diagrama_dados) {
			let diagrama = projetoData.diagrama_dados;
			// Remove prefixo "mermaid" se presente
			if (diagrama.startsWith('mermaid\n')) {
				diagrama = diagrama.substring(8); // Remove "mermaid\n"
			}
			return diagrama;
		}
		return '';
	}

	/**
	 * Verifica se existe diagrama de dados disponível
	 */
	public hasDataDiagram(): boolean {
		return this.getDataDiagram().trim() !== '';
	}

	public getCostPercentageDynamic(stackName: string): number {
		const estimateData = this.getEstimateData();
		if (!estimateData?.stacks) return 0;

		const stack = estimateData.stacks.find((s: any) => s.stack === stackName);
		if (!stack) return 0;

		const stackDevCount = this.calculateStackDeveloperCount(stack);
		const totalDevs = this.getTotalTeamSizeDynamic();

		if (totalDevs === 0) return 0;
		return Math.round((stackDevCount / totalDevs) * 100);
	}

	public getProgressPercentageDynamic(): number {
		return this.projectSelected?.progresso || 0;
	}

	public getSprintColorDynamic(sprintIndex: number): string {
		return this.getSprintColor(sprintIndex + 1);
	}

	public getRiskSeverityClassDynamic(risk: string): string {
		if (risk.toLowerCase().includes('vazamento') || risk.toLowerCase().includes('segurança')) {
			return 'bg-red-100 text-red-800';
		}
		if (risk.toLowerCase().includes('conformidade') || risk.toLowerCase().includes('lgpd')) {
			return 'bg-yellow-100 text-yellow-800';
		}
		return 'bg-green-100 text-green-800';
	}

	public getRiskLevelTextDynamic(risk: string): string {
		if (risk.toLowerCase().includes('crítico')) return 'Crítico';
		if (risk.toLowerCase().includes('alto')) return 'Alto';
		if (risk.toLowerCase().includes('médio')) return 'Médio';
		return 'Baixo';
	}

	public getRiskSeverityDynamic(risk: string): number {
		const level = this.getRiskLevelTextDynamic(risk);
		switch (level) {
			case 'Crítico':
				return 4;
			case 'Alto':
				return 3;
			case 'Médio':
				return 2;
			default:
				return 1;
		}
	}

	public getSprintArray(): Array<number> {
		const estimateData = this.getEstimateData();
		const totalSprints = estimateData?.total_de_sprints || 0;
		return Array.from({ length: totalSprints }, (_, i) => i);
	}

	public getSprintProgressWidth(sprintIndex: number): number {
		const totalSprints = this.getSprintArray().length;
		if (totalSprints === 0) return 0;

		// Simular progresso baseado no progresso geral do projeto
		const projectProgress = this.projectSelected?.progresso || 0;
		const sprintProgress = (projectProgress / 100) * totalSprints;

		return sprintIndex < sprintProgress ? 100 : 0;
	}

	public getComplexityRiskClass(): string {
		const complexity = this.getOverallComplexityDynamic();
		if (complexity === 'Alta') return 'risk-high';
		if (complexity === 'Média') return 'risk-medium';
		return 'risk-low';
	}

	public closeEstimateDrawer(): void {
		// Implementação do fechamento do drawer
		this.estimativaVisible.set(false);
	}

	public generateEstimate(project: any): void {
		// Implementação da geração de estimativa
		this.gerarEstimativa();
	}

	public extractNumberFromString(text: string): number {
		const match = text.match(/\d+/);
		return match ? parseInt(match[0], 10) : 0;
	}

	private calculateTotalTeamMembers(equipe: any): number {
		if (!equipe?.formacao) return 0;
		return equipe.formacao.reduce((total: number, membro: any) => total + (membro.quantidade || 0), 0);
	}

	private calculateTotalFromStacks(stacks: any[]): number {
		if (!stacks) return 0;
		return stacks.reduce((total, stack) => total + (stack.quantidade_de_desenvolvedores || 0), 0);
	}

	/**
	 * Calcula o número de desenvolvedores de uma stack específica
	 */
	private calculateStackDeveloperCount(stack: any): number {
		if (!stack?.desenvolvedores || !Array.isArray(stack.desenvolvedores)) return 0;

		return stack.desenvolvedores.reduce((total: number, dev: any) => {
			return total + (parseInt(dev.quantidade) || 0);
		}, 0);
	}

	/**
	 * Retorna informações detalhadas sobre desenvolvedores por stack
	 */
	public getStackDeveloperDetails(): Array<{ stack: string; total: number; details: any[] }> {
		const estimateData = this.getEstimateData();
		if (!estimateData?.stacks) return [];

		return estimateData.stacks.map((stack: any) => ({
			stack: stack.stack,
			total: this.calculateStackDeveloperCount(stack),
			details: stack.desenvolvedores || [],
		}));
	}

	/**
	 * Extrai o tipo de desenvolvedor do campo motivo
	 */
	public extractDeveloperType(motivo: string): string {
		if (!motivo) return 'Desenvolvedor';

		const motivoLower = motivo.toLowerCase();

		if (motivoLower.includes('backend') || motivoLower.includes('api') || motivoLower.includes('servidor')) {
			return 'Backend';
		}
		if (motivoLower.includes('frontend') || motivoLower.includes('ui') || motivoLower.includes('interface')) {
			return 'Frontend';
		}
		if (
			motivoLower.includes('devops') ||
			motivoLower.includes('infraestrutura') ||
			motivoLower.includes('deploy')
		) {
			return 'DevOps';
		}
		if (motivoLower.includes('mobile') || motivoLower.includes('app')) {
			return 'Mobile';
		}
		if (motivoLower.includes('qa') || motivoLower.includes('teste') || motivoLower.includes('qualidade')) {
			return 'QA';
		}
		if (motivoLower.includes('tech lead') || motivoLower.includes('líder') || motivoLower.includes('arquitet')) {
			return 'Tech Lead';
		}

		return 'Desenvolvedor';
	}

	/**
	 * Retorna ícone para tipo de desenvolvedor
	 */
	public getDeveloperTypeIcon(type: string): string {
		const icons: { [key: string]: string } = {
			'Backend': '⚙️',
			'Frontend': '🖥️',
			'DevOps': '☁️',
			'Mobile': '📱',
			'QA': '🧪',
			'Tech Lead': '👨‍💼',
			'Desenvolvedor': '👨‍💻',
		};

		return icons[type] || '👨‍💻';
	}

	/**
	 * Retorna cor para tipo de desenvolvedor
	 */
	public getDeveloperTypeColor(type: string): string {
		const colors: { [key: string]: string } = {
			'Backend': 'bg-green-100 text-green-800',
			'Frontend': 'bg-blue-100 text-blue-800',
			'DevOps': 'bg-orange-100 text-orange-800',
			'Mobile': 'bg-purple-100 text-purple-800',
			'QA': 'bg-yellow-100 text-yellow-800',
			'Tech Lead': 'bg-red-100 text-red-800',
			'Desenvolvedor': 'bg-gray-100 text-gray-800',
		};

		return colors[type] || 'bg-gray-100 text-gray-800';
	}

	/**
	 * Retorna estrutura da equipe formatada para exibição
	 */
	public getFormattedTeamStructure(): Array<{
		type: string;
		icon: string;
		color: string;
		quantidade: number;
		senioridade: string[];
		modalidade: string;
		duracao: string;
		descricao: string;
	}> {
		const estimateData = this.getEstimateData();
		if (!estimateData?.stacks) return [];

		const teamMembers: any[] = [];

		estimateData.stacks.forEach((stack: any) => {
			if (stack.desenvolvedores && Array.isArray(stack.desenvolvedores)) {
				stack.desenvolvedores.forEach((dev: any) => {
					const type = this.extractDeveloperType(dev.motivo);
					teamMembers.push({
						type: stack.stack,
						icon: this.getDeveloperTypeIcon(type),
						color: this.getDeveloperTypeColor(type),
						quantidade: parseInt(dev.quantidade) || 0,
						senioridade: dev.senioridade || [],
						modalidade: dev.modalidade || 'full-time',
						duracao: dev.duracao || '6 meses',
						descricao: dev.motivo || '',
					});
				});
			}
		});

		return teamMembers;
	}

	/**
	 * Converte stacks para formato esperado pelo template
	 */
	private convertStacksToTechStacks(stacks: any[]): any[] {
		if (!stacks) return [];

		return stacks.map(stack => ({
			categoria: stack.stack,
			tecnologias: stack.tecnologias || [],
			complexidade: 7, // Valor padrão
			horas_estimadas: 200, // Valor padrão
			justificativa: stack.motivo || 'Tecnologias escolhidas para otimizar desenvolvimento',
		}));
	}

	/**
	 * Converte stacks para formação de equipe
	 */
	private convertStacksToFormacao(stacks: any[]): any[] {
		if (!stacks) return [];

		const formacao: any[] = [];

		stacks.forEach(stack => {
			if (stack.desenvolvedores) {
				stack.desenvolvedores.forEach((dev: any) => {
					formacao.push({
						funcao: stack.stack,
						quantidade: parseInt(dev.quantidade) || 1,
						senioridade: dev.senioridade?.[0] || 'Pleno',
						custo_mensal: this.estimateMonthlyCost(dev.senioridade?.[0] || 'Pleno', stack.stack),
					});
				});
			}
		});

		return formacao;
	}

	/**
	 * Estima custo mensal baseado em senioridade e área
	 */
	private estimateMonthlyCost(senioridade: string, area: string): number {
		const baseCosts: { [key: string]: number } = {
			Júnior: 8000,
			Pleno: 12000,
			Sênior: 18000,
			Especialista: 22000,
		};

		const areaMult: { [key: string]: number } = {
			'Frontend': 1.0,
			'Backend': 1.1,
			'DevOps': 1.2,
			'Data Engineering': 1.3,
			'Arquitetura': 1.4,
		};

		const base = baseCosts[senioridade] || baseCosts['Pleno'];
		const mult = areaMult[area] || 1.0;

		return Math.round(base * mult);
	}

	public getProjetoPRD(): string {
		const projeto = this.getProjetoData();
		return projeto?.prd || '';
	}

	public getProjetoDiagrama(): string {
		const projeto = this.getProjetoData();
		return projeto?.diagrama || '';
	}

	public getStackDiagramas(): Array<{ stack: string; diagrama: string }> {
		const projeto = this.getProjetoData();
		if (!projeto?.stacks) return [];

		return projeto.stacks
			.filter((stack: any) => {
				// Suporte para ambos os formatos: novo (diagrama_sequencia) e legado (diagrama)
				const diagrama = stack.diagrama_sequencia || stack.diagrama;
				return diagrama && diagrama.trim() !== '';
			})
			.map((stack: any) => {
				let diagrama = stack.diagrama_sequencia || stack.diagrama;
				// Remove prefixo "mermaid" se presente
				if (diagrama && diagrama.startsWith('mermaid\n')) {
					diagrama = diagrama.substring(8); // Remove "mermaid\n"
				}
				return {
					stack: stack.stack,
					diagrama: diagrama,
				};
			});
	}

	public getProjetoNome(): string {
		return this.getNomeProjetoReal();
	}

	/**
	 * Retorna texto do nível de risco baseado no fator
	 */
	public getRiskLevelFromFactor(factor: number): string {
		if (factor <= 3) return 'Baixo';
		if (factor <= 6) return 'Médio';
		if (factor <= 8) return 'Alto';
		return 'Crítico';
	}

	/**
	 * Gera card do projeto após criar
	 */
	private generateProjectCard(projectId: string, projectName: string): void {
		this.messageService.add({
			severity: 'info',
			summary: 'Gerando Card',
			detail: `Criando card visual para ${projectName}...`,
			life: 3000,
		});

		// Simular geração de card (aqui você pode chamar uma API se necessário)
		setTimeout(() => {
			this.messageService.add({
				severity: 'success',
				summary: 'Card Gerado!',
				detail: `Card do projeto ${projectName} foi criado e está visível na lista.`,
				life: 5000,
			});
		}, 2000);
	}

	/**
	 * Converte diagrama C4Context para flowchart Mermaid válido
	 * NOTA: Removidos emojis para compatibilidade com Mermaid Live API
	 */
	private convertC4ToFlowchart(_c4Diagram: string): string {
		// Diagrama flowchart equivalente ao C4 (sem emojis para compatibilidade)
		return `flowchart TD
    Usuario[Usuario Final]

    subgraph Sistema["Sistema Web"]
        Frontend[Interface Web]
        Backend[API Backend]
        Database[Banco de Dados]
        Auth[Autenticacao]
    end

    Usuario --> Frontend
    Frontend --> Backend
    Backend --> Database
    Backend --> Auth`;
	}

	/**
	 * Reproduz podcast do projeto (delegado para PodcastModalComponent)
	 */
	public async playProjectPodcast(): Promise<void> {
		if (!this.projectSelected?._id) return;

		const projectTitle = this.projectSelected?.nome_projeto || this.projectSelected?.titulo || 'Projeto';
		const sanitizedTitle = projectTitle.replace(/[<>&"']/g, '');

		// Configurar dados e abrir modal
		this.currentPodcastTitle.set(sanitizedTitle);
		this.podcastModalVisible.set(true);
	}

	/**
	 * Fecha o modal do podcast
	 */
	public closePodcastModal(): void {
		this.podcastModalVisible.set(false);
		// Limpar dados após um pequeno delay para evitar flicker
		setTimeout(() => {
			this.currentPodcastUrl.set('');
			this.currentPodcastTitle.set('');
		}, 300);
	}

	/**
	 * 📄 Fecha modal do PDF e limpa recursos
	 */
	public closePdfModal(): void {
		// Limpar URL do blob para liberar memória
		if (this.currentPdfUrl()) {
			URL.revokeObjectURL(this.currentPdfUrl());
		}

		this.pdfModalVisible.set(false);
		this.currentPdfUrl.set('');
		this.currentPdfTitle.set('');
	}

	/**
	 * 📥 Callback para download de PDF
	 */
	public onPdfDownload(): void {
		this.messageService.add({
			severity: 'success',
			summary: 'Download Iniciado',
			detail: 'O relatório PDF está sendo baixado.',
			life: 3000,
		});
	}

	/**
	 * 📥 Download do PDF atual
	 */
	public downloadCurrentPdf(): void {
		if (!this.currentPdfUrl()) return;

		const link = document.createElement('a');
		link.href = this.currentPdfUrl();
		link.download = `${this.currentPdfTitle()}.pdf`;
		link.click();

		this.onPdfDownload();
	}
}
