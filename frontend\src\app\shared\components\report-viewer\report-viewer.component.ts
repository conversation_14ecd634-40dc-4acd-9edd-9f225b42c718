import { CommonModule } from '@angular/common';
import { Component, computed, EventEmitter, Input, OnInit, Output, signal } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { DividerModule } from 'primeng/divider';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { TagModule } from 'primeng/tag';
import { ToastModule } from 'primeng/toast';
import { ClientsService } from '../../../features/clients/clients.service';
import { MarkdownViewerComponent } from '../markdown-viewer/markdown-viewer.component';

export interface ReportData {
	client_id: string;
	client_name: string;
	markdown_content: string;
	metadata: {
		generated_at: string;
		file_info: {
			filename: string;
			upload_date: string;
			content_type: string;
			length: number;
		};
		report_type: string;
	};
}

@Component({
	selector: 'app-report-viewer',
	standalone: true,
	imports: [
		CommonModule,
		ButtonModule,
		DialogModule,
		ProgressSpinnerModule,
		ToastModule,
		DividerModule,
		TagModule,
		MarkdownViewerComponent,
	],
	template: `
		<p-dialog
			[visible]="visible()"
			[modal]="true"
			[closable]="true"
			[draggable]="false"
			[resizable]="true"
			[closeOnEscape]="true"
			[dismissableMask]="true"
			styleClass="report-viewer-dialog"
			[style]="{ width: '95vw', maxWidth: '1400px', height: '90vh' }"
			(onHide)="onClose()"
		>
			<ng-template pTemplate="header">
				<div class="flex items-center justify-between w-full">
					<div class="flex items-center gap-3">
						<i class="pi pi-file-text text-blue-600 text-2xl"></i>
						<div>
							<h2 class="text-xl font-bold text-gray-900 m-0">Relatório Executivo</h2>
							@if (reportData()) {
								<p class="text-sm text-gray-600 m-0 mt-1">{{ reportData()?.client_name }}</p>
							}
						</div>
					</div>
					<div class="flex items-center gap-2">
						@if (reportData()) {
							<p-tag value="Markdown" severity="info" [rounded]="true" styleClass="text-xs"></p-tag>
						}
					</div>
				</div>
			</ng-template>

			<ng-template pTemplate="content">
				<div class="report-viewer-container h-full">
					@if (loading()) {
						<!-- Estado de Loading -->
						<div class="flex flex-col items-center justify-center h-full p-8">
							<p-progressSpinner
								styleClass="w-12 h-12"
								strokeWidth="3"
								animationDuration="1s"
							></p-progressSpinner>
							<p class="text-gray-600 mt-4">Carregando relatório...</p>
						</div>
					} @else if (error()) {
						<!-- Estado de Erro -->
						<div class="flex flex-col items-center justify-center h-full p-8 text-center">
							<i class="pi pi-exclamation-triangle text-red-500 text-6xl mb-4"></i>
							<h3 class="text-lg font-semibold text-gray-900 mb-2">Erro ao Carregar Relatório</h3>
							<p class="text-gray-600 mb-4">{{ errorMessage() }}</p>
							<button
								pButton
								label="Tentar Novamente"
								icon="pi pi-refresh"
								class="p-button-outlined p-button-primary"
								(click)="onRetry()"
							></button>
						</div>
					} @else if (reportData()) {
						<!-- Conteúdo do Relatório -->
						<div class="report-content-wrapper">
							<!-- Header do Relatório -->
							<div class="report-header bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-lg mb-6">
								<div class="flex justify-between items-start">
									<div>
										<h1 class="text-2xl font-bold text-gray-900 mb-2">
											{{ reportData()?.client_name }}
										</h1>
										<p class="text-gray-600">Relatório Executivo Detalhado</p>
									</div>
									<div class="text-right">
										@if (reportData()?.metadata?.generated_at) {
											<p class="text-sm text-gray-500">
												Gerado em: {{ formatDate(reportData()!.metadata.generated_at) }}
											</p>
										}
										@if (reportData()?.metadata?.file_info?.length) {
											<p class="text-xs text-gray-400 mt-1">
												{{ formatFileSize(reportData()!.metadata.file_info.length) }}
											</p>
										}
									</div>
								</div>
							</div>

							<!-- Barra de Ações -->
							<div class="report-actions bg-white border border-gray-200 rounded-lg p-4 mb-6">
								<div class="flex justify-between items-center">
									<div class="flex items-center gap-2">
										<span class="text-sm font-medium text-gray-700">Ações:</span>
									</div>
									<div class="flex items-center gap-2">
										<button
											pButton
											[icon]="downloadingPdf() ? 'pi pi-spin pi-spinner' : 'pi pi-download'"
											[label]="downloadingPdf() ? 'Gerando PDF...' : 'Baixar PDF'"
											class="p-button-outlined p-button-success p-button-sm"
											(click)="onDownloadPdf()"
											[disabled]="downloadingPdf()"
											pTooltip="Baixar como PDF"
											tooltipPosition="top"
										></button>
										<button
											pButton
											icon="pi pi-print"
											label="Imprimir"
											class="p-button-outlined p-button-secondary p-button-sm"
											(click)="onPrint()"
											pTooltip="Imprimir relatório"
											tooltipPosition="top"
										></button>
										<button
											pButton
											icon="pi pi-copy"
											label="Copiar"
											class="p-button-outlined p-button-info p-button-sm"
											(click)="onCopyContent()"
											pTooltip="Copiar conteúdo"
											tooltipPosition="top"
										></button>
									</div>
								</div>
							</div>

							<!-- Conteúdo Markdown -->
							<div class="report-markdown-content bg-white border border-gray-200 rounded-lg">
								<app-markdown-viewer
									[content]="reportData()!.markdown_content"
									[enableKatex]="false"
									[enableMermaid]="false"
								></app-markdown-viewer>
							</div>
						</div>
					} @else {
						<!-- Estado Vazio -->
						<div class="flex flex-col items-center justify-center h-full p-8 text-center">
							<i class="pi pi-file-o text-gray-400 text-6xl mb-4"></i>
							<h3 class="text-lg font-semibold text-gray-900 mb-2">Nenhum Relatório Disponível</h3>
							<p class="text-gray-600">Não há relatório para exibir no momento.</p>
						</div>
					}
				</div>
			</ng-template>

			<ng-template pTemplate="footer">
				<div class="flex justify-end gap-2 p-4">
					<button
						pButton
						icon="pi pi-times"
						label="Fechar"
						class="p-button-outlined p-button-secondary"
						(click)="onClose()"
						pTooltip="Fechar relatório"
						tooltipPosition="top"
					></button>
				</div>
			</ng-template>
		</p-dialog>
	`,
	styles: [
		`
			:host ::ng-deep .report-viewer-dialog .p-dialog-content {
				padding: 1.5rem;
				overflow-y: auto;
			}

			:host ::ng-deep .report-viewer-dialog .p-dialog-header {
				padding: 1.5rem 1.5rem 1rem 1.5rem;
				border-bottom: 1px solid #e5e7eb;
			}

			:host ::ng-deep .report-viewer-dialog .p-dialog-footer {
				padding: 0;
				border-top: 1px solid #e5e7eb;
				background-color: #f9fafb;
			}

			.report-content-wrapper {
				max-height: calc(90vh - 200px);
				overflow-y: auto;
			}

			.report-markdown-content {
				min-height: 400px;
			}

			/* Print styles */
			@media print {
				.report-actions {
					display: none !important;
				}

				.report-content-wrapper {
					max-height: none !important;
					overflow: visible !important;
				}
			}
		`,
	],
})
export class ReportViewerComponent implements OnInit {
	@Input() visible = signal(false);
	@Input() clientId = signal<string>('');
	@Input() reportData = signal<ReportData | null>(null);

	@Output() onHide = new EventEmitter<void>();

	// Estados internos
	loading = signal(false);
	error = signal(false);
	errorMessage = signal('');
	downloadingPdf = signal(false);

	// Computed properties
	hasReport = computed(() => !!this.reportData());

	constructor(
		private sanitizer: DomSanitizer,
		private messageService: MessageService,
		private clientsService: ClientsService,
	) {}

	ngOnInit(): void {
		// Inicialização se necessário
	}

	onClose(): void {
		// Limpar estados internos
		this.loading.set(false);
		this.error.set(false);
		this.errorMessage.set('');
		this.downloadingPdf.set(false);

		// Fechar modal
		this.visible.set(false);

		// Notificar componente pai
		this.onHide.emit();
	}

	onRetry(): void {
		this.error.set(false);
		this.errorMessage.set('');
		// Emitir evento para componente pai recarregar
		// TODO: Implementar EventEmitter se necessário
	}

	onDownloadPdf(): void {
		const reportData = this.reportData();

		if (!reportData) {
			this.messageService.add({
				severity: 'warn',
				summary: 'Erro',
				detail: 'Nenhum relatório disponível para download.',
				life: 3000,
			});
			return;
		}

		this.downloadingPdf.set(true);

		this.messageService.add({
			severity: 'info',
			summary: 'Gerando PDF',
			detail: 'Convertendo relatório para PDF. Aguarde...',
			life: 3000,
		});

		// Usar o ClientsService para converter markdown para PDF
		this.clientsService.convertMarkdownToPdf(reportData.client_id, reportData.markdown_content).subscribe({
			next: (pdfBlob: Blob) => {
				this.downloadingPdf.set(false);
				this.downloadPdfFile(pdfBlob, reportData.client_name);

				this.messageService.add({
					severity: 'success',
					summary: 'PDF Gerado',
					detail: 'Download do PDF iniciado com sucesso!',
					life: 3000,
				});
			},
			error: (error: any) => {
				console.error('Erro ao gerar PDF:', error);
				this.downloadingPdf.set(false);

				this.messageService.add({
					severity: 'error',
					summary: 'Erro ao Gerar PDF',
					detail: 'Não foi possível gerar o PDF. Tente novamente.',
					life: 5000,
				});
			},
		});
	}

	private downloadPdfFile(pdfBlob: Blob, clientName: string): void {
		// Criar URL temporária para o blob
		const url = window.URL.createObjectURL(pdfBlob);

		// Criar elemento de link temporário para download
		const link = document.createElement('a');
		link.href = url;

		// Gerar nome do arquivo com data atual
		const now = new Date();
		const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD
		const fileName = `Relatorio-${clientName.replace(/[^a-zA-Z0-9]/g, '_')}-${dateStr}.pdf`;

		link.download = fileName;

		// Adicionar ao DOM, clicar e remover
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);

		// Limpar URL temporária
		window.URL.revokeObjectURL(url);
	}

	onPrint(): void {
		window.print();
	}

	onCopyContent(): void {
		if (this.reportData()?.markdown_content) {
			navigator.clipboard
				.writeText(this.reportData()!.markdown_content)
				.then(() => {
					this.messageService.add({
						severity: 'success',
						summary: 'Copiado',
						detail: 'Conteúdo copiado para a área de transferência.',
						life: 3000,
					});
				})
				.catch(() => {
					this.messageService.add({
						severity: 'error',
						summary: 'Erro',
						detail: 'Não foi possível copiar o conteúdo.',
						life: 3000,
					});
				});
		}
	}

	formatDate(dateString: string): string {
		try {
			const date = new Date(dateString);
			return date.toLocaleDateString('pt-BR', {
				year: 'numeric',
				month: 'long',
				day: 'numeric',
				hour: '2-digit',
				minute: '2-digit',
			});
		} catch {
			return 'Data inválida';
		}
	}

	formatFileSize(bytes: number): string {
		if (bytes === 0) return '0 Bytes';
		const k = 1024;
		const sizes = ['Bytes', 'KB', 'MB', 'GB'];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
	}

	// Métodos públicos para controle externo
	public setLoading(loading: boolean): void {
		this.loading.set(loading);
	}

	public setError(error: boolean, message: string = ''): void {
		this.error.set(error);
		this.errorMessage.set(message);
	}

	public setReportData(data: ReportData | null): void {
		this.reportData.set(data);
	}

	public show(): void {
		this.visible.set(true);
	}

	public hide(): void {
		this.visible.set(false);
	}
}
