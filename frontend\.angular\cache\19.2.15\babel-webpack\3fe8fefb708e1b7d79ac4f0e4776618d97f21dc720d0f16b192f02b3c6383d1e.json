{"ast": null, "code": "import _asyncToGenerator from \"C:/projetos/scope-ai/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { inject as inject$1, <PERSON><PERSON><PERSON>, <PERSON><PERSON>r<PERSON><PERSON><PERSON>, Injectable, ɵDeferBlockState as _DeferBlockState, ɵtriggerResourceLoading as _triggerResourceLoading, ɵrenderDeferBlockState as _renderDeferBlockState, ɵCONTAINER_HEADER_OFFSET as _CONTAINER_HEADER_OFFSET, ɵgetDeferBlocks as _getDeferBlocks, InjectionToken, ɵDeferBlockBehavior as _DeferBlockBehavior, ɵNoopNgZone as _NoopNgZone, ApplicationRef, ɵPendingTasksInternal as _PendingTasksInternal, ɵZONELESS_ENABLED as _ZONELESS_ENABLED, ɵChangeDetectionScheduler as _ChangeDetectionScheduler, ɵEffectScheduler as _EffectScheduler, ɵMicrotaskEffectScheduler as _MicrotaskEffectScheduler, getDebugNode, RendererFactory2, ɵstringify as _stringify, Pipe, Directive, Component, NgModule, ɵReflectionCapabilities as _ReflectionCapabilities, ɵUSE_RUNTIME_DEPS_TRACKER_FOR_JIT as _USE_RUNTIME_DEPS_TRACKER_FOR_JIT, ɵdepsTracker as _depsTracker, ɵgetInjectableDef as _getInjectableDef, resolveForwardRef, ɵisComponentDefPendingResolution as _isComponentDefPendingResolution, ɵgetAsyncClassMetadataFn as _getAsyncClassMetadataFn, ɵresolveComponentResources as _resolveComponentResources, ɵRender3NgModuleRef as _Render3NgModuleRef, ApplicationInitStatus, LOCALE_ID, ɵDEFAULT_LOCALE_ID as _DEFAULT_LOCALE_ID, ɵsetLocaleId as _setLocaleId, ɵRender3ComponentFactory as _Render3ComponentFactory, ɵNG_COMP_DEF as _NG_COMP_DEF, ɵcompileComponent as _compileComponent, ɵNG_DIR_DEF as _NG_DIR_DEF, ɵcompileDirective as _compileDirective, ɵNG_PIPE_DEF as _NG_PIPE_DEF, ɵcompilePipe as _compilePipe, ɵNG_MOD_DEF as _NG_MOD_DEF, ɵpatchComponentDefWithScope as _patchComponentDefWithScope, ɵNG_INJ_DEF as _NG_INJ_DEF, ɵcompileNgModuleDefs as _compileNgModuleDefs, ɵclearResolutionOfComponentResourcesQueue as _clearResolutionOfComponentResourcesQueue, ɵrestoreComponentResolutionQueue as _restoreComponentResolutionQueue, ɵinternalProvideZoneChangeDetection as _internalProvideZoneChangeDetection, ɵChangeDetectionSchedulerImpl as _ChangeDetectionSchedulerImpl, Compiler, ɵDEFER_BLOCK_CONFIG as _DEFER_BLOCK_CONFIG, ɵINTERNAL_APPLICATION_ERROR_HANDLER as _INTERNAL_APPLICATION_ERROR_HANDLER, COMPILER_OPTIONS, Injector, ɵtransitiveScopesFor as _transitiveScopesFor, ɵgenerateStandaloneInDeclarationsError as _generateStandaloneInDeclarationsError, ɵNgModuleFactory as _NgModuleFactory, ModuleWithComponentFactories, ɵisEnvironmentProviders as _isEnvironmentProviders, ɵconvertToBitFlags as _convertToBitFlags, InjectFlags, ɵsetAllowDuplicateNgModuleIdsForTest as _setAllowDuplicateNgModuleIdsForTest, ɵresetCompiledComponents as _resetCompiledComponents, ɵsetUnknownElementStrictMode as _setUnknownElementStrictMode, ɵsetUnknownPropertyStrictMode as _setUnknownPropertyStrictMode, ɵgetUnknownElementStrictMode as _getUnknownElementStrictMode, ɵgetUnknownPropertyStrictMode as _getUnknownPropertyStrictMode, runInInjectionContext, EnvironmentInjector, ɵflushModuleScopingQueueAsMuchAsPossible as _flushModuleScopingQueueAsMuchAsPossible } from '@angular/core';\nexport { ɵDeferBlockBehavior as DeferBlockBehavior, ɵDeferBlockState as DeferBlockState } from '@angular/core';\nimport { Subscription } from 'rxjs';\nimport { ResourceLoader } from '@angular/compiler';\n\n/**\n * Wraps a test function in an asynchronous test zone. The test will automatically\n * complete when all asynchronous calls within this zone are done. Can be used\n * to wrap an {@link inject} call.\n *\n * Example:\n *\n * ```ts\n * it('...', waitForAsync(inject([AClass], (object) => {\n *   object.doSomething.then(() => {\n *     expect(...);\n *   })\n * })));\n * ```\n *\n * @publicApi\n */\nfunction waitForAsync(fn) {\n  const _Zone = typeof Zone !== 'undefined' ? Zone : null;\n  if (!_Zone) {\n    return function () {\n      return Promise.reject('Zone is needed for the waitForAsync() test helper but could not be found. ' + 'Please make sure that your environment includes zone.js');\n    };\n  }\n  const asyncTest = _Zone && _Zone[_Zone.__symbol__('asyncTest')];\n  if (typeof asyncTest === 'function') {\n    return asyncTest(fn);\n  }\n  return function () {\n    return Promise.reject('zone-testing.js is needed for the async() test helper but could not be found. ' + 'Please make sure that your environment includes zone.js/testing');\n  };\n}\nconst RETHROW_APPLICATION_ERRORS_DEFAULT = true;\nclass TestBedApplicationErrorHandler {\n  zone = inject$1(NgZone);\n  userErrorHandler = inject$1(ErrorHandler);\n  whenStableRejectFunctions = new Set();\n  handleError(e) {\n    try {\n      this.zone.runOutsideAngular(() => this.userErrorHandler.handleError(e));\n    } catch (userError) {\n      e = userError;\n    }\n    // Instead of throwing the error when there are outstanding `fixture.whenStable` promises,\n    // reject those promises with the error. This allows developers to write\n    // expectAsync(fix.whenStable()).toBeRejected();\n    if (this.whenStableRejectFunctions.size > 0) {\n      for (const fn of this.whenStableRejectFunctions.values()) {\n        fn(e);\n      }\n      this.whenStableRejectFunctions.clear();\n    } else {\n      throw e;\n    }\n  }\n  static ɵfac = function TestBedApplicationErrorHandler_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || TestBedApplicationErrorHandler)();\n  };\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: TestBedApplicationErrorHandler,\n    factory: TestBedApplicationErrorHandler.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TestBedApplicationErrorHandler, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * Represents an individual defer block for testing purposes.\n *\n * @publicApi\n */\nclass DeferBlockFixture {\n  block;\n  componentFixture;\n  /** @docs-private */\n  constructor(block, componentFixture) {\n    this.block = block;\n    this.componentFixture = componentFixture;\n  }\n  /**\n   * Renders the specified state of the defer fixture.\n   * @param state the defer state to render\n   */\n  render(state) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!hasStateTemplate(state, _this.block)) {\n        const stateAsString = getDeferBlockStateNameFromEnum(state);\n        throw new Error(`Tried to render this defer block in the \\`${stateAsString}\\` state, ` + `but there was no @${stateAsString.toLowerCase()} block defined in a template.`);\n      }\n      if (state === _DeferBlockState.Complete) {\n        yield _triggerResourceLoading(_this.block.tDetails, _this.block.lView, _this.block.tNode);\n      }\n      // If the `render` method is used explicitly - skip timer-based scheduling for\n      // `@placeholder` and `@loading` blocks and render them immediately.\n      const skipTimerScheduling = true;\n      _renderDeferBlockState(state, _this.block.tNode, _this.block.lContainer, skipTimerScheduling);\n      _this.componentFixture.detectChanges();\n    })();\n  }\n  /**\n   * Retrieves all nested child defer block fixtures\n   * in a given defer block.\n   */\n  getDeferBlocks() {\n    const deferBlocks = [];\n    // An LContainer that represents a defer block has at most 1 view, which is\n    // located right after an LContainer header. Get a hold of that view and inspect\n    // it for nested defer blocks.\n    const deferBlockFixtures = [];\n    if (this.block.lContainer.length >= _CONTAINER_HEADER_OFFSET) {\n      const lView = this.block.lContainer[_CONTAINER_HEADER_OFFSET];\n      _getDeferBlocks(lView, deferBlocks);\n      for (const block of deferBlocks) {\n        deferBlockFixtures.push(new DeferBlockFixture(block, this.componentFixture));\n      }\n    }\n    return Promise.resolve(deferBlockFixtures);\n  }\n}\nfunction hasStateTemplate(state, block) {\n  switch (state) {\n    case _DeferBlockState.Placeholder:\n      return block.tDetails.placeholderTmplIndex !== null;\n    case _DeferBlockState.Loading:\n      return block.tDetails.loadingTmplIndex !== null;\n    case _DeferBlockState.Error:\n      return block.tDetails.errorTmplIndex !== null;\n    case _DeferBlockState.Complete:\n      return true;\n    default:\n      return false;\n  }\n}\nfunction getDeferBlockStateNameFromEnum(state) {\n  switch (state) {\n    case _DeferBlockState.Placeholder:\n      return 'Placeholder';\n    case _DeferBlockState.Loading:\n      return 'Loading';\n    case _DeferBlockState.Error:\n      return 'Error';\n    default:\n      return 'Main';\n  }\n}\n\n/** Whether test modules should be torn down by default. */\nconst TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT = true;\n/** Whether unknown elements in templates should throw by default. */\nconst THROW_ON_UNKNOWN_ELEMENTS_DEFAULT = false;\n/** Whether unknown properties in templates should throw by default. */\nconst THROW_ON_UNKNOWN_PROPERTIES_DEFAULT = false;\n/** Whether defer blocks should use manual triggering or play through normally. */\nconst DEFER_BLOCK_DEFAULT_BEHAVIOR = _DeferBlockBehavior.Playthrough;\n/**\n * An abstract class for inserting the root test component element in a platform independent way.\n *\n * @publicApi\n */\nclass TestComponentRenderer {\n  insertRootElement(rootElementId) {}\n  removeAllRootElements() {}\n}\n/**\n * @publicApi\n */\nconst ComponentFixtureAutoDetect = new InjectionToken('ComponentFixtureAutoDetect');\n/**\n * @publicApi\n */\nconst ComponentFixtureNoNgZone = new InjectionToken('ComponentFixtureNoNgZone');\n\n/**\n * Fixture for debugging and testing a component.\n *\n * @publicApi\n */\nclass ComponentFixture {\n  componentRef;\n  /**\n   * The DebugElement associated with the root element of this component.\n   */\n  debugElement;\n  /**\n   * The instance of the root component class.\n   */\n  componentInstance;\n  /**\n   * The native element at the root of the component.\n   */\n  nativeElement;\n  /**\n   * The ElementRef for the element at the root of the component.\n   */\n  elementRef;\n  /**\n   * The ChangeDetectorRef for the component\n   */\n  changeDetectorRef;\n  _renderer;\n  _isDestroyed = false;\n  /** @internal */\n  _noZoneOptionIsSet = inject$1(ComponentFixtureNoNgZone, {\n    optional: true\n  });\n  /** @internal */\n  _ngZone = this._noZoneOptionIsSet ? new _NoopNgZone() : inject$1(NgZone);\n  // Inject ApplicationRef to ensure NgZone stableness causes after render hooks to run\n  // This will likely happen as a result of fixture.detectChanges because it calls ngZone.run\n  // This is a crazy way of doing things but hey, it's the world we live in.\n  // The zoneless scheduler should instead do this more imperatively by attaching\n  // the `ComponentRef` to `ApplicationRef` and calling `appRef.tick` as the `detectChanges`\n  // behavior.\n  /** @internal */\n  _appRef = inject$1(ApplicationRef);\n  _testAppRef = this._appRef;\n  pendingTasks = inject$1(_PendingTasksInternal);\n  appErrorHandler = inject$1(TestBedApplicationErrorHandler);\n  zonelessEnabled = inject$1(_ZONELESS_ENABLED);\n  scheduler = inject$1(_ChangeDetectionScheduler);\n  rootEffectScheduler = inject$1(_EffectScheduler);\n  microtaskEffectScheduler = inject$1(_MicrotaskEffectScheduler);\n  autoDetectDefault = this.zonelessEnabled ? true : false;\n  autoDetect = inject$1(ComponentFixtureAutoDetect, {\n    optional: true\n  }) ?? this.autoDetectDefault;\n  subscriptions = new Subscription();\n  // TODO(atscott): Remove this from public API\n  ngZone = this._noZoneOptionIsSet ? null : this._ngZone;\n  /** @docs-private */\n  constructor(componentRef) {\n    this.componentRef = componentRef;\n    this.changeDetectorRef = componentRef.changeDetectorRef;\n    this.elementRef = componentRef.location;\n    this.debugElement = getDebugNode(this.elementRef.nativeElement);\n    this.componentInstance = componentRef.instance;\n    this.nativeElement = this.elementRef.nativeElement;\n    this.componentRef = componentRef;\n    if (this.autoDetect) {\n      this._testAppRef.externalTestViews.add(this.componentRef.hostView);\n      this.scheduler?.notify(8 /* ɵNotificationSource.ViewAttached */);\n      this.scheduler?.notify(0 /* ɵNotificationSource.MarkAncestorsForTraversal */);\n    }\n    this.componentRef.hostView.onDestroy(() => {\n      this._testAppRef.externalTestViews.delete(this.componentRef.hostView);\n    });\n    // Create subscriptions outside the NgZone so that the callbacks run outside\n    // of NgZone.\n    this._ngZone.runOutsideAngular(() => {\n      this.subscriptions.add(this._ngZone.onError.subscribe({\n        next: error => {\n          throw error;\n        }\n      }));\n    });\n  }\n  /**\n   * Trigger a change detection cycle for the component.\n   */\n  detectChanges(checkNoChanges = true) {\n    this.microtaskEffectScheduler.flush();\n    const originalCheckNoChanges = this.componentRef.changeDetectorRef.checkNoChanges;\n    try {\n      if (!checkNoChanges) {\n        this.componentRef.changeDetectorRef.checkNoChanges = () => {};\n      }\n      if (this.zonelessEnabled) {\n        try {\n          this._testAppRef.externalTestViews.add(this.componentRef.hostView);\n          this._appRef.tick();\n        } finally {\n          if (!this.autoDetect) {\n            this._testAppRef.externalTestViews.delete(this.componentRef.hostView);\n          }\n        }\n      } else {\n        // Run the change detection inside the NgZone so that any async tasks as part of the change\n        // detection are captured by the zone and can be waited for in isStable.\n        this._ngZone.run(() => {\n          // Flush root effects before `detectChanges()`, to emulate the sequencing of `tick()`.\n          this.rootEffectScheduler.flush();\n          this.changeDetectorRef.detectChanges();\n          this.checkNoChanges();\n        });\n      }\n    } finally {\n      this.componentRef.changeDetectorRef.checkNoChanges = originalCheckNoChanges;\n    }\n    this.microtaskEffectScheduler.flush();\n  }\n  /**\n   * Do a change detection run to make sure there were no changes.\n   */\n  checkNoChanges() {\n    this.changeDetectorRef.checkNoChanges();\n  }\n  /**\n   * Set whether the fixture should autodetect changes.\n   *\n   * Also runs detectChanges once so that any existing change is detected.\n   *\n   * @param autoDetect Whether to autodetect changes. By default, `true`.\n   */\n  autoDetectChanges(autoDetect = true) {\n    if (this._noZoneOptionIsSet && !this.zonelessEnabled) {\n      throw new Error('Cannot call autoDetectChanges when ComponentFixtureNoNgZone is set.');\n    }\n    if (autoDetect !== this.autoDetect) {\n      if (autoDetect) {\n        this._testAppRef.externalTestViews.add(this.componentRef.hostView);\n      } else {\n        this._testAppRef.externalTestViews.delete(this.componentRef.hostView);\n      }\n    }\n    this.autoDetect = autoDetect;\n    this.detectChanges();\n  }\n  /**\n   * Return whether the fixture is currently stable or has async tasks that have not been completed\n   * yet.\n   */\n  isStable() {\n    return !this.pendingTasks.hasPendingTasks.value;\n  }\n  /**\n   * Get a promise that resolves when the fixture is stable.\n   *\n   * This can be used to resume testing after events have triggered asynchronous activity or\n   * asynchronous change detection.\n   */\n  whenStable() {\n    if (this.isStable()) {\n      return Promise.resolve(false);\n    }\n    return new Promise((resolve, reject) => {\n      this.appErrorHandler.whenStableRejectFunctions.add(reject);\n      this._appRef.whenStable().then(() => {\n        this.appErrorHandler.whenStableRejectFunctions.delete(reject);\n        resolve(true);\n      });\n    });\n  }\n  /**\n   * Retrieves all defer block fixtures in the component fixture.\n   */\n  getDeferBlocks() {\n    const deferBlocks = [];\n    const lView = this.componentRef.hostView['_lView'];\n    _getDeferBlocks(lView, deferBlocks);\n    const deferBlockFixtures = [];\n    for (const block of deferBlocks) {\n      deferBlockFixtures.push(new DeferBlockFixture(block, this));\n    }\n    return Promise.resolve(deferBlockFixtures);\n  }\n  _getRenderer() {\n    if (this._renderer === undefined) {\n      this._renderer = this.componentRef.injector.get(RendererFactory2, null);\n    }\n    return this._renderer;\n  }\n  /**\n   * Get a promise that resolves when the ui state is stable following animations.\n   */\n  whenRenderingDone() {\n    const renderer = this._getRenderer();\n    if (renderer && renderer.whenRenderingDone) {\n      return renderer.whenRenderingDone();\n    }\n    return this.whenStable();\n  }\n  /**\n   * Trigger component destruction.\n   */\n  destroy() {\n    this.subscriptions.unsubscribe();\n    this._testAppRef.externalTestViews.delete(this.componentRef.hostView);\n    if (!this._isDestroyed) {\n      this.componentRef.destroy();\n      this._isDestroyed = true;\n    }\n  }\n}\nconst _Zone = typeof Zone !== 'undefined' ? Zone : null;\nconst fakeAsyncTestModule = _Zone && _Zone[_Zone.__symbol__('fakeAsyncTest')];\nconst fakeAsyncTestModuleNotLoadedErrorMessage = `zone-testing.js is needed for the fakeAsync() test helper but could not be found.\n        Please make sure that your environment includes zone.js/testing`;\n/**\n * Clears out the shared fake async zone for a test.\n * To be called in a global `beforeEach`.\n *\n * @publicApi\n */\nfunction resetFakeAsyncZone() {\n  if (fakeAsyncTestModule) {\n    return fakeAsyncTestModule.resetFakeAsyncZone();\n  }\n  throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\nfunction resetFakeAsyncZoneIfExists() {\n  if (fakeAsyncTestModule && Zone['ProxyZoneSpec']?.isLoaded()) {\n    fakeAsyncTestModule.resetFakeAsyncZone();\n  }\n}\n/**\n * Wraps a function to be executed in the `fakeAsync` zone:\n * - Microtasks are manually executed by calling `flushMicrotasks()`.\n * - Timers are synchronous; `tick()` simulates the asynchronous passage of time.\n *\n * Can be used to wrap `inject()` calls.\n *\n * @param fn The function that you want to wrap in the `fakeAsync` zone.\n * @param options\n *   - flush: When true, will drain the macrotask queue after the test function completes.\n *     When false, will throw an exception at the end of the function if there are pending timers.\n *\n * @usageNotes\n * ### Example\n *\n * {@example core/testing/ts/fake_async.ts region='basic'}\n *\n *\n * @returns The function wrapped to be executed in the `fakeAsync` zone.\n * Any arguments passed when calling this returned function will be passed through to the `fn`\n * function in the parameters when it is called.\n *\n * @publicApi\n */\nfunction fakeAsync(fn, options) {\n  if (fakeAsyncTestModule) {\n    return fakeAsyncTestModule.fakeAsync(fn, options);\n  }\n  throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n/**\n * Simulates the asynchronous passage of time for the timers in the `fakeAsync` zone.\n *\n * The microtasks queue is drained at the very start of this function and after any timer callback\n * has been executed.\n *\n * @param millis The number of milliseconds to advance the virtual timer.\n * @param tickOptions The options to pass to the `tick()` function.\n *\n * @usageNotes\n *\n * The `tick()` option is a flag called `processNewMacroTasksSynchronously`,\n * which determines whether or not to invoke new macroTasks.\n *\n * If you provide a `tickOptions` object, but do not specify a\n * `processNewMacroTasksSynchronously` property (`tick(100, {})`),\n * then `processNewMacroTasksSynchronously` defaults to true.\n *\n * If you omit the `tickOptions` parameter (`tick(100))`), then\n * `tickOptions` defaults to `{processNewMacroTasksSynchronously: true}`.\n *\n * ### Example\n *\n * {@example core/testing/ts/fake_async.ts region='basic'}\n *\n * The following example includes a nested timeout (new macroTask), and\n * the `tickOptions` parameter is allowed to default. In this case,\n * `processNewMacroTasksSynchronously` defaults to true, and the nested\n * function is executed on each tick.\n *\n * ```ts\n * it ('test with nested setTimeout', fakeAsync(() => {\n *   let nestedTimeoutInvoked = false;\n *   function funcWithNestedTimeout() {\n *     setTimeout(() => {\n *       nestedTimeoutInvoked = true;\n *     });\n *   };\n *   setTimeout(funcWithNestedTimeout);\n *   tick();\n *   expect(nestedTimeoutInvoked).toBe(true);\n * }));\n * ```\n *\n * In the following case, `processNewMacroTasksSynchronously` is explicitly\n * set to false, so the nested timeout function is not invoked.\n *\n * ```ts\n * it ('test with nested setTimeout', fakeAsync(() => {\n *   let nestedTimeoutInvoked = false;\n *   function funcWithNestedTimeout() {\n *     setTimeout(() => {\n *       nestedTimeoutInvoked = true;\n *     });\n *   };\n *   setTimeout(funcWithNestedTimeout);\n *   tick(0, {processNewMacroTasksSynchronously: false});\n *   expect(nestedTimeoutInvoked).toBe(false);\n * }));\n * ```\n *\n *\n * @publicApi\n */\nfunction tick(millis = 0, tickOptions = {\n  processNewMacroTasksSynchronously: true\n}) {\n  if (fakeAsyncTestModule) {\n    return fakeAsyncTestModule.tick(millis, tickOptions);\n  }\n  throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n/**\n * Flushes any pending microtasks and simulates the asynchronous passage of time for the timers in\n * the `fakeAsync` zone by\n * draining the macrotask queue until it is empty.\n *\n * @param maxTurns The maximum number of times the scheduler attempts to clear its queue before\n *     throwing an error.\n * @returns The simulated time elapsed, in milliseconds.\n *\n * @publicApi\n */\nfunction flush(maxTurns) {\n  if (fakeAsyncTestModule) {\n    return fakeAsyncTestModule.flush(maxTurns);\n  }\n  throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n/**\n * Discard all remaining periodic tasks.\n *\n * @publicApi\n */\nfunction discardPeriodicTasks() {\n  if (fakeAsyncTestModule) {\n    return fakeAsyncTestModule.discardPeriodicTasks();\n  }\n  throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n/**\n * Flush any pending microtasks.\n *\n * @publicApi\n */\nfunction flushMicrotasks() {\n  if (fakeAsyncTestModule) {\n    return fakeAsyncTestModule.flushMicrotasks();\n  }\n  throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\nlet _nextReferenceId = 0;\nclass MetadataOverrider {\n  _references = new Map();\n  /**\n   * Creates a new instance for the given metadata class\n   * based on an old instance and overrides.\n   */\n  overrideMetadata(metadataClass, oldMetadata, override) {\n    const props = {};\n    if (oldMetadata) {\n      _valueProps(oldMetadata).forEach(prop => props[prop] = oldMetadata[prop]);\n    }\n    if (override.set) {\n      if (override.remove || override.add) {\n        throw new Error(`Cannot set and add/remove ${_stringify(metadataClass)} at the same time!`);\n      }\n      setMetadata(props, override.set);\n    }\n    if (override.remove) {\n      removeMetadata(props, override.remove, this._references);\n    }\n    if (override.add) {\n      addMetadata(props, override.add);\n    }\n    return new metadataClass(props);\n  }\n}\nfunction removeMetadata(metadata, remove, references) {\n  const removeObjects = new Set();\n  for (const prop in remove) {\n    const removeValue = remove[prop];\n    if (Array.isArray(removeValue)) {\n      removeValue.forEach(value => {\n        removeObjects.add(_propHashKey(prop, value, references));\n      });\n    } else {\n      removeObjects.add(_propHashKey(prop, removeValue, references));\n    }\n  }\n  for (const prop in metadata) {\n    const propValue = metadata[prop];\n    if (Array.isArray(propValue)) {\n      metadata[prop] = propValue.filter(value => !removeObjects.has(_propHashKey(prop, value, references)));\n    } else {\n      if (removeObjects.has(_propHashKey(prop, propValue, references))) {\n        metadata[prop] = undefined;\n      }\n    }\n  }\n}\nfunction addMetadata(metadata, add) {\n  for (const prop in add) {\n    const addValue = add[prop];\n    const propValue = metadata[prop];\n    if (propValue != null && Array.isArray(propValue)) {\n      metadata[prop] = propValue.concat(addValue);\n    } else {\n      metadata[prop] = addValue;\n    }\n  }\n}\nfunction setMetadata(metadata, set) {\n  for (const prop in set) {\n    metadata[prop] = set[prop];\n  }\n}\nfunction _propHashKey(propName, propValue, references) {\n  let nextObjectId = 0;\n  const objectIds = new Map();\n  const replacer = (key, value) => {\n    if (value !== null && typeof value === 'object') {\n      if (objectIds.has(value)) {\n        return objectIds.get(value);\n      }\n      // Record an id for this object such that any later references use the object's id instead\n      // of the object itself, in order to break cyclic pointers in objects.\n      objectIds.set(value, `ɵobj#${nextObjectId++}`);\n      // The first time an object is seen the object itself is serialized.\n      return value;\n    } else if (typeof value === 'function') {\n      value = _serializeReference(value, references);\n    }\n    return value;\n  };\n  return `${propName}:${JSON.stringify(propValue, replacer)}`;\n}\nfunction _serializeReference(ref, references) {\n  let id = references.get(ref);\n  if (!id) {\n    id = `${_stringify(ref)}${_nextReferenceId++}`;\n    references.set(ref, id);\n  }\n  return id;\n}\nfunction _valueProps(obj) {\n  const props = [];\n  // regular public props\n  Object.keys(obj).forEach(prop => {\n    if (!prop.startsWith('_')) {\n      props.push(prop);\n    }\n  });\n  // getters\n  let proto = obj;\n  while (proto = Object.getPrototypeOf(proto)) {\n    Object.keys(proto).forEach(protoProp => {\n      const desc = Object.getOwnPropertyDescriptor(proto, protoProp);\n      if (!protoProp.startsWith('_') && desc && 'get' in desc) {\n        props.push(protoProp);\n      }\n    });\n  }\n  return props;\n}\nconst reflection = new _ReflectionCapabilities();\n/**\n * Allows to override ivy metadata for tests (via the `TestBed`).\n */\nclass OverrideResolver {\n  overrides = new Map();\n  resolved = new Map();\n  addOverride(type, override) {\n    const overrides = this.overrides.get(type) || [];\n    overrides.push(override);\n    this.overrides.set(type, overrides);\n    this.resolved.delete(type);\n  }\n  setOverrides(overrides) {\n    this.overrides.clear();\n    overrides.forEach(([type, override]) => {\n      this.addOverride(type, override);\n    });\n  }\n  getAnnotation(type) {\n    const annotations = reflection.annotations(type);\n    // Try to find the nearest known Type annotation and make sure that this annotation is an\n    // instance of the type we are looking for, so we can use it for resolution. Note: there might\n    // be multiple known annotations found due to the fact that Components can extend Directives (so\n    // both Directive and Component annotations would be present), so we always check if the known\n    // annotation has the right type.\n    for (let i = annotations.length - 1; i >= 0; i--) {\n      const annotation = annotations[i];\n      const isKnownType = annotation instanceof Directive || annotation instanceof Component || annotation instanceof Pipe || annotation instanceof NgModule;\n      if (isKnownType) {\n        return annotation instanceof this.type ? annotation : null;\n      }\n    }\n    return null;\n  }\n  resolve(type) {\n    let resolved = this.resolved.get(type) || null;\n    if (!resolved) {\n      resolved = this.getAnnotation(type);\n      if (resolved) {\n        const overrides = this.overrides.get(type);\n        if (overrides) {\n          const overrider = new MetadataOverrider();\n          overrides.forEach(override => {\n            resolved = overrider.overrideMetadata(this.type, resolved, override);\n          });\n        }\n      }\n      this.resolved.set(type, resolved);\n    }\n    return resolved;\n  }\n}\nclass DirectiveResolver extends OverrideResolver {\n  get type() {\n    return Directive;\n  }\n}\nclass ComponentResolver extends OverrideResolver {\n  get type() {\n    return Component;\n  }\n}\nclass PipeResolver extends OverrideResolver {\n  get type() {\n    return Pipe;\n  }\n}\nclass NgModuleResolver extends OverrideResolver {\n  get type() {\n    return NgModule;\n  }\n}\nvar TestingModuleOverride;\n(function (TestingModuleOverride) {\n  TestingModuleOverride[TestingModuleOverride[\"DECLARATION\"] = 0] = \"DECLARATION\";\n  TestingModuleOverride[TestingModuleOverride[\"OVERRIDE_TEMPLATE\"] = 1] = \"OVERRIDE_TEMPLATE\";\n})(TestingModuleOverride || (TestingModuleOverride = {}));\nfunction isTestingModuleOverride(value) {\n  return value === TestingModuleOverride.DECLARATION || value === TestingModuleOverride.OVERRIDE_TEMPLATE;\n}\nfunction assertNoStandaloneComponents(types, resolver, location) {\n  types.forEach(type => {\n    if (!_getAsyncClassMetadataFn(type)) {\n      const component = resolver.resolve(type);\n      if (component && (component.standalone == null || component.standalone)) {\n        throw new Error(_generateStandaloneInDeclarationsError(type, location));\n      }\n    }\n  });\n}\nclass TestBedCompiler {\n  platform;\n  additionalModuleTypes;\n  originalComponentResolutionQueue = null;\n  // Testing module configuration\n  declarations = [];\n  imports = [];\n  providers = [];\n  schemas = [];\n  // Queues of components/directives/pipes that should be recompiled.\n  pendingComponents = new Set();\n  pendingDirectives = new Set();\n  pendingPipes = new Set();\n  // Set of components with async metadata, i.e. components with `@defer` blocks\n  // in their templates.\n  componentsWithAsyncMetadata = new Set();\n  // Keep track of all components and directives, so we can patch Providers onto defs later.\n  seenComponents = new Set();\n  seenDirectives = new Set();\n  // Keep track of overridden modules, so that we can collect all affected ones in the module tree.\n  overriddenModules = new Set();\n  // Store resolved styles for Components that have template overrides present and `styleUrls`\n  // defined at the same time.\n  existingComponentStyles = new Map();\n  resolvers = initResolvers();\n  // Map of component type to an NgModule that declares it.\n  //\n  // There are a couple special cases:\n  // - for standalone components, the module scope value is `null`\n  // - when a component is declared in `TestBed.configureTestingModule()` call or\n  //   a component's template is overridden via `TestBed.overrideTemplateUsingTestingModule()`.\n  //   we use a special value from the `TestingModuleOverride` enum.\n  componentToModuleScope = new Map();\n  // Map that keeps initial version of component/directive/pipe defs in case\n  // we compile a Type again, thus overriding respective static fields. This is\n  // required to make sure we restore defs to their initial states between test runs.\n  // Note: one class may have multiple defs (for example: ɵmod and ɵinj in case of an\n  // NgModule), store all of them in a map.\n  initialNgDefs = new Map();\n  // Array that keeps cleanup operations for initial versions of component/directive/pipe/module\n  // defs in case TestBed makes changes to the originals.\n  defCleanupOps = [];\n  _injector = null;\n  compilerProviders = null;\n  providerOverrides = [];\n  rootProviderOverrides = [];\n  // Overrides for injectables with `{providedIn: SomeModule}` need to be tracked and added to that\n  // module's provider list.\n  providerOverridesByModule = new Map();\n  providerOverridesByToken = new Map();\n  scopesWithOverriddenProviders = new Set();\n  testModuleType;\n  testModuleRef = null;\n  deferBlockBehavior = DEFER_BLOCK_DEFAULT_BEHAVIOR;\n  rethrowApplicationTickErrors = RETHROW_APPLICATION_ERRORS_DEFAULT;\n  constructor(platform, additionalModuleTypes) {\n    this.platform = platform;\n    this.additionalModuleTypes = additionalModuleTypes;\n    class DynamicTestModule {}\n    this.testModuleType = DynamicTestModule;\n  }\n  setCompilerProviders(providers) {\n    this.compilerProviders = providers;\n    this._injector = null;\n  }\n  configureTestingModule(moduleDef) {\n    // Enqueue any compilation tasks for the directly declared component.\n    if (moduleDef.declarations !== undefined) {\n      // Verify that there are no standalone components\n      assertNoStandaloneComponents(moduleDef.declarations, this.resolvers.component, '\"TestBed.configureTestingModule\" call');\n      this.queueTypeArray(moduleDef.declarations, TestingModuleOverride.DECLARATION);\n      this.declarations.push(...moduleDef.declarations);\n    }\n    // Enqueue any compilation tasks for imported modules.\n    if (moduleDef.imports !== undefined) {\n      this.queueTypesFromModulesArray(moduleDef.imports);\n      this.imports.push(...moduleDef.imports);\n    }\n    if (moduleDef.providers !== undefined) {\n      this.providers.push(...moduleDef.providers);\n    }\n    if (moduleDef.schemas !== undefined) {\n      this.schemas.push(...moduleDef.schemas);\n    }\n    this.deferBlockBehavior = moduleDef.deferBlockBehavior ?? DEFER_BLOCK_DEFAULT_BEHAVIOR;\n    this.rethrowApplicationTickErrors = moduleDef.rethrowApplicationErrors ?? RETHROW_APPLICATION_ERRORS_DEFAULT;\n  }\n  overrideModule(ngModule, override) {\n    if (_USE_RUNTIME_DEPS_TRACKER_FOR_JIT) {\n      _depsTracker.clearScopeCacheFor(ngModule);\n    }\n    this.overriddenModules.add(ngModule);\n    // Compile the module right away.\n    this.resolvers.module.addOverride(ngModule, override);\n    const metadata = this.resolvers.module.resolve(ngModule);\n    if (metadata === null) {\n      throw invalidTypeError(ngModule.name, 'NgModule');\n    }\n    this.recompileNgModule(ngModule, metadata);\n    // At this point, the module has a valid module def (ɵmod), but the override may have introduced\n    // new declarations or imported modules. Ingest any possible new types and add them to the\n    // current queue.\n    this.queueTypesFromModulesArray([ngModule]);\n  }\n  overrideComponent(component, override) {\n    this.verifyNoStandaloneFlagOverrides(component, override);\n    this.resolvers.component.addOverride(component, override);\n    this.pendingComponents.add(component);\n    // If this is a component with async metadata (i.e. a component with a `@defer` block\n    // in a template) - store it for future processing.\n    this.maybeRegisterComponentWithAsyncMetadata(component);\n  }\n  overrideDirective(directive, override) {\n    this.verifyNoStandaloneFlagOverrides(directive, override);\n    this.resolvers.directive.addOverride(directive, override);\n    this.pendingDirectives.add(directive);\n  }\n  overridePipe(pipe, override) {\n    this.verifyNoStandaloneFlagOverrides(pipe, override);\n    this.resolvers.pipe.addOverride(pipe, override);\n    this.pendingPipes.add(pipe);\n  }\n  verifyNoStandaloneFlagOverrides(type, override) {\n    if (override.add?.hasOwnProperty('standalone') || override.set?.hasOwnProperty('standalone') || override.remove?.hasOwnProperty('standalone')) {\n      throw new Error(`An override for the ${type.name} class has the \\`standalone\\` flag. ` + `Changing the \\`standalone\\` flag via TestBed overrides is not supported.`);\n    }\n  }\n  overrideProvider(token, provider) {\n    let providerDef;\n    if (provider.useFactory !== undefined) {\n      providerDef = {\n        provide: token,\n        useFactory: provider.useFactory,\n        deps: provider.deps || [],\n        multi: provider.multi\n      };\n    } else if (provider.useValue !== undefined) {\n      providerDef = {\n        provide: token,\n        useValue: provider.useValue,\n        multi: provider.multi\n      };\n    } else {\n      providerDef = {\n        provide: token\n      };\n    }\n    const injectableDef = typeof token !== 'string' ? _getInjectableDef(token) : null;\n    const providedIn = injectableDef === null ? null : resolveForwardRef(injectableDef.providedIn);\n    const overridesBucket = providedIn === 'root' ? this.rootProviderOverrides : this.providerOverrides;\n    overridesBucket.push(providerDef);\n    // Keep overrides grouped by token as well for fast lookups using token\n    this.providerOverridesByToken.set(token, providerDef);\n    if (injectableDef !== null && providedIn !== null && typeof providedIn !== 'string') {\n      const existingOverrides = this.providerOverridesByModule.get(providedIn);\n      if (existingOverrides !== undefined) {\n        existingOverrides.push(providerDef);\n      } else {\n        this.providerOverridesByModule.set(providedIn, [providerDef]);\n      }\n    }\n  }\n  overrideTemplateUsingTestingModule(type, template) {\n    const def = type[_NG_COMP_DEF];\n    const hasStyleUrls = () => {\n      const metadata = this.resolvers.component.resolve(type);\n      return !!metadata.styleUrl || !!metadata.styleUrls?.length;\n    };\n    const overrideStyleUrls = !!def && !_isComponentDefPendingResolution(type) && hasStyleUrls();\n    // In Ivy, compiling a component does not require knowing the module providing the\n    // component's scope, so overrideTemplateUsingTestingModule can be implemented purely via\n    // overrideComponent. Important: overriding template requires full Component re-compilation,\n    // which may fail in case styleUrls are also present (thus Component is considered as required\n    // resolution). In order to avoid this, we preemptively set styleUrls to an empty array,\n    // preserve current styles available on Component def and restore styles back once compilation\n    // is complete.\n    const override = overrideStyleUrls ? {\n      template,\n      styles: [],\n      styleUrls: [],\n      styleUrl: undefined\n    } : {\n      template\n    };\n    this.overrideComponent(type, {\n      set: override\n    });\n    if (overrideStyleUrls && def.styles && def.styles.length > 0) {\n      this.existingComponentStyles.set(type, def.styles);\n    }\n    // Set the component's scope to be the testing module.\n    this.componentToModuleScope.set(type, TestingModuleOverride.OVERRIDE_TEMPLATE);\n  }\n  resolvePendingComponentsWithAsyncMetadata() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (_this2.componentsWithAsyncMetadata.size === 0) return;\n      const promises = [];\n      for (const component of _this2.componentsWithAsyncMetadata) {\n        const asyncMetadataFn = _getAsyncClassMetadataFn(component);\n        if (asyncMetadataFn) {\n          promises.push(asyncMetadataFn());\n        }\n      }\n      _this2.componentsWithAsyncMetadata.clear();\n      const resolvedDeps = yield Promise.all(promises);\n      const flatResolvedDeps = resolvedDeps.flat(2);\n      _this2.queueTypesFromModulesArray(flatResolvedDeps);\n      // Loaded standalone components might contain imports of NgModules\n      // with providers, make sure we override providers there too.\n      for (const component of flatResolvedDeps) {\n        _this2.applyProviderOverridesInScope(component);\n      }\n    })();\n  }\n  compileComponents() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      _this3.clearComponentResolutionQueue();\n      // Wait for all async metadata for components that were\n      // overridden, we need resolved metadata to perform an override\n      // and re-compile a component.\n      yield _this3.resolvePendingComponentsWithAsyncMetadata();\n      // Verify that there were no standalone components present in the `declarations` field\n      // during the `TestBed.configureTestingModule` call. We perform this check here in addition\n      // to the logic in the `configureTestingModule` function, since at this point we have\n      // all async metadata resolved.\n      assertNoStandaloneComponents(_this3.declarations, _this3.resolvers.component, '\"TestBed.configureTestingModule\" call');\n      // Run compilers for all queued types.\n      let needsAsyncResources = _this3.compileTypesSync();\n      // compileComponents() should not be async unless it needs to be.\n      if (needsAsyncResources) {\n        let resourceLoader;\n        let resolver = url => {\n          if (!resourceLoader) {\n            resourceLoader = _this3.injector.get(ResourceLoader);\n          }\n          return Promise.resolve(resourceLoader.get(url));\n        };\n        yield _resolveComponentResources(resolver);\n      }\n    })();\n  }\n  finalize() {\n    // One last compile\n    this.compileTypesSync();\n    // Create the testing module itself.\n    this.compileTestModule();\n    this.applyTransitiveScopes();\n    this.applyProviderOverrides();\n    // Patch previously stored `styles` Component values (taken from ɵcmp), in case these\n    // Components have `styleUrls` fields defined and template override was requested.\n    this.patchComponentsWithExistingStyles();\n    // Clear the componentToModuleScope map, so that future compilations don't reset the scope of\n    // every component.\n    this.componentToModuleScope.clear();\n    const parentInjector = this.platform.injector;\n    this.testModuleRef = new _Render3NgModuleRef(this.testModuleType, parentInjector, []);\n    // ApplicationInitStatus.runInitializers() is marked @internal to core.\n    // Cast it to any before accessing it.\n    this.testModuleRef.injector.get(ApplicationInitStatus).runInitializers();\n    // Set locale ID after running app initializers, since locale information might be updated while\n    // running initializers. This is also consistent with the execution order while bootstrapping an\n    // app (see `packages/core/src/application_ref.ts` file).\n    const localeId = this.testModuleRef.injector.get(LOCALE_ID, _DEFAULT_LOCALE_ID);\n    _setLocaleId(localeId);\n    return this.testModuleRef;\n  }\n  /**\n   * @internal\n   */\n  _compileNgModuleSync(moduleType) {\n    this.queueTypesFromModulesArray([moduleType]);\n    this.compileTypesSync();\n    this.applyProviderOverrides();\n    this.applyProviderOverridesInScope(moduleType);\n    this.applyTransitiveScopes();\n  }\n  /**\n   * @internal\n   */\n  _compileNgModuleAsync(moduleType) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      _this4.queueTypesFromModulesArray([moduleType]);\n      yield _this4.compileComponents();\n      _this4.applyProviderOverrides();\n      _this4.applyProviderOverridesInScope(moduleType);\n      _this4.applyTransitiveScopes();\n    })();\n  }\n  /**\n   * @internal\n   */\n  _getModuleResolver() {\n    return this.resolvers.module;\n  }\n  /**\n   * @internal\n   */\n  _getComponentFactories(moduleType) {\n    return maybeUnwrapFn(moduleType.ɵmod.declarations).reduce((factories, declaration) => {\n      const componentDef = declaration.ɵcmp;\n      componentDef && factories.push(new _Render3ComponentFactory(componentDef, this.testModuleRef));\n      return factories;\n    }, []);\n  }\n  compileTypesSync() {\n    // Compile all queued components, directives, pipes.\n    let needsAsyncResources = false;\n    this.pendingComponents.forEach(declaration => {\n      if (_getAsyncClassMetadataFn(declaration)) {\n        throw new Error(`Component '${declaration.name}' has unresolved metadata. ` + `Please call \\`await TestBed.compileComponents()\\` before running this test.`);\n      }\n      needsAsyncResources = needsAsyncResources || _isComponentDefPendingResolution(declaration);\n      const metadata = this.resolvers.component.resolve(declaration);\n      if (metadata === null) {\n        throw invalidTypeError(declaration.name, 'Component');\n      }\n      this.maybeStoreNgDef(_NG_COMP_DEF, declaration);\n      if (_USE_RUNTIME_DEPS_TRACKER_FOR_JIT) {\n        _depsTracker.clearScopeCacheFor(declaration);\n      }\n      _compileComponent(declaration, metadata);\n    });\n    this.pendingComponents.clear();\n    this.pendingDirectives.forEach(declaration => {\n      const metadata = this.resolvers.directive.resolve(declaration);\n      if (metadata === null) {\n        throw invalidTypeError(declaration.name, 'Directive');\n      }\n      this.maybeStoreNgDef(_NG_DIR_DEF, declaration);\n      _compileDirective(declaration, metadata);\n    });\n    this.pendingDirectives.clear();\n    this.pendingPipes.forEach(declaration => {\n      const metadata = this.resolvers.pipe.resolve(declaration);\n      if (metadata === null) {\n        throw invalidTypeError(declaration.name, 'Pipe');\n      }\n      this.maybeStoreNgDef(_NG_PIPE_DEF, declaration);\n      _compilePipe(declaration, metadata);\n    });\n    this.pendingPipes.clear();\n    return needsAsyncResources;\n  }\n  applyTransitiveScopes() {\n    if (this.overriddenModules.size > 0) {\n      // Module overrides (via `TestBed.overrideModule`) might affect scopes that were previously\n      // calculated and stored in `transitiveCompileScopes`. If module overrides are present,\n      // collect all affected modules and reset scopes to force their re-calculation.\n      const testingModuleDef = this.testModuleType[_NG_MOD_DEF];\n      const affectedModules = this.collectModulesAffectedByOverrides(testingModuleDef.imports);\n      if (affectedModules.size > 0) {\n        affectedModules.forEach(moduleType => {\n          if (!_USE_RUNTIME_DEPS_TRACKER_FOR_JIT) {\n            this.storeFieldOfDefOnType(moduleType, _NG_MOD_DEF, 'transitiveCompileScopes');\n            moduleType[_NG_MOD_DEF].transitiveCompileScopes = null;\n          } else {\n            _depsTracker.clearScopeCacheFor(moduleType);\n          }\n        });\n      }\n    }\n    const moduleToScope = new Map();\n    const getScopeOfModule = moduleType => {\n      if (!moduleToScope.has(moduleType)) {\n        const isTestingModule = isTestingModuleOverride(moduleType);\n        const realType = isTestingModule ? this.testModuleType : moduleType;\n        moduleToScope.set(moduleType, _transitiveScopesFor(realType));\n      }\n      return moduleToScope.get(moduleType);\n    };\n    this.componentToModuleScope.forEach((moduleType, componentType) => {\n      if (moduleType !== null) {\n        const moduleScope = getScopeOfModule(moduleType);\n        this.storeFieldOfDefOnType(componentType, _NG_COMP_DEF, 'directiveDefs');\n        this.storeFieldOfDefOnType(componentType, _NG_COMP_DEF, 'pipeDefs');\n        _patchComponentDefWithScope(getComponentDef(componentType), moduleScope);\n      }\n      // `tView` that is stored on component def contains information about directives and pipes\n      // that are in the scope of this component. Patching component scope will cause `tView` to be\n      // changed. Store original `tView` before patching scope, so the `tView` (including scope\n      // information) is restored back to its previous/original state before running next test.\n      // Resetting `tView` is also needed for cases when we apply provider overrides and those\n      // providers are defined on component's level, in which case they may end up included into\n      // `tView.blueprint`.\n      this.storeFieldOfDefOnType(componentType, _NG_COMP_DEF, 'tView');\n    });\n    this.componentToModuleScope.clear();\n  }\n  applyProviderOverrides() {\n    const maybeApplyOverrides = field => type => {\n      const resolver = field === _NG_COMP_DEF ? this.resolvers.component : this.resolvers.directive;\n      const metadata = resolver.resolve(type);\n      if (this.hasProviderOverrides(metadata.providers)) {\n        this.patchDefWithProviderOverrides(type, field);\n      }\n    };\n    this.seenComponents.forEach(maybeApplyOverrides(_NG_COMP_DEF));\n    this.seenDirectives.forEach(maybeApplyOverrides(_NG_DIR_DEF));\n    this.seenComponents.clear();\n    this.seenDirectives.clear();\n  }\n  /**\n   * Applies provider overrides to a given type (either an NgModule or a standalone component)\n   * and all imported NgModules and standalone components recursively.\n   */\n  applyProviderOverridesInScope(type) {\n    const hasScope = isStandaloneComponent(type) || isNgModule(type);\n    // The function can be re-entered recursively while inspecting dependencies\n    // of an NgModule or a standalone component. Exit early if we come across a\n    // type that can not have a scope (directive or pipe) or the type is already\n    // processed earlier.\n    if (!hasScope || this.scopesWithOverriddenProviders.has(type)) {\n      return;\n    }\n    this.scopesWithOverriddenProviders.add(type);\n    // NOTE: the line below triggers JIT compilation of the module injector,\n    // which also invokes verification of the NgModule semantics, which produces\n    // detailed error messages. The fact that the code relies on this line being\n    // present here is suspicious and should be refactored in a way that the line\n    // below can be moved (for ex. after an early exit check below).\n    const injectorDef = type[_NG_INJ_DEF];\n    // No provider overrides, exit early.\n    if (this.providerOverridesByToken.size === 0) return;\n    if (isStandaloneComponent(type)) {\n      // Visit all component dependencies and override providers there.\n      const def = getComponentDef(type);\n      const dependencies = maybeUnwrapFn(def.dependencies ?? []);\n      for (const dependency of dependencies) {\n        this.applyProviderOverridesInScope(dependency);\n      }\n    } else {\n      const providers = [...injectorDef.providers, ...(this.providerOverridesByModule.get(type) || [])];\n      if (this.hasProviderOverrides(providers)) {\n        this.maybeStoreNgDef(_NG_INJ_DEF, type);\n        this.storeFieldOfDefOnType(type, _NG_INJ_DEF, 'providers');\n        injectorDef.providers = this.getOverriddenProviders(providers);\n      }\n      // Apply provider overrides to imported modules recursively\n      const moduleDef = type[_NG_MOD_DEF];\n      const imports = maybeUnwrapFn(moduleDef.imports);\n      for (const importedModule of imports) {\n        this.applyProviderOverridesInScope(importedModule);\n      }\n      // Also override the providers on any ModuleWithProviders imports since those don't appear in\n      // the moduleDef.\n      for (const importedModule of flatten(injectorDef.imports)) {\n        if (isModuleWithProviders(importedModule)) {\n          this.defCleanupOps.push({\n            object: importedModule,\n            fieldName: 'providers',\n            originalValue: importedModule.providers\n          });\n          importedModule.providers = this.getOverriddenProviders(importedModule.providers);\n        }\n      }\n    }\n  }\n  patchComponentsWithExistingStyles() {\n    this.existingComponentStyles.forEach((styles, type) => type[_NG_COMP_DEF].styles = styles);\n    this.existingComponentStyles.clear();\n  }\n  queueTypeArray(arr, moduleType) {\n    for (const value of arr) {\n      if (Array.isArray(value)) {\n        this.queueTypeArray(value, moduleType);\n      } else {\n        this.queueType(value, moduleType);\n      }\n    }\n  }\n  recompileNgModule(ngModule, metadata) {\n    // Cache the initial ngModuleDef as it will be overwritten.\n    this.maybeStoreNgDef(_NG_MOD_DEF, ngModule);\n    this.maybeStoreNgDef(_NG_INJ_DEF, ngModule);\n    _compileNgModuleDefs(ngModule, metadata);\n  }\n  maybeRegisterComponentWithAsyncMetadata(type) {\n    const asyncMetadataFn = _getAsyncClassMetadataFn(type);\n    if (asyncMetadataFn) {\n      this.componentsWithAsyncMetadata.add(type);\n    }\n  }\n  queueType(type, moduleType) {\n    // If this is a component with async metadata (i.e. a component with a `@defer` block\n    // in a template) - store it for future processing.\n    this.maybeRegisterComponentWithAsyncMetadata(type);\n    const component = this.resolvers.component.resolve(type);\n    if (component) {\n      // Check whether a give Type has respective NG def (ɵcmp) and compile if def is\n      // missing. That might happen in case a class without any Angular decorators extends another\n      // class where Component/Directive/Pipe decorator is defined.\n      if (_isComponentDefPendingResolution(type) || !type.hasOwnProperty(_NG_COMP_DEF)) {\n        this.pendingComponents.add(type);\n      }\n      this.seenComponents.add(type);\n      // Keep track of the module which declares this component, so later the component's scope\n      // can be set correctly. If the component has already been recorded here, then one of several\n      // cases is true:\n      // * the module containing the component was imported multiple times (common).\n      // * the component is declared in multiple modules (which is an error).\n      // * the component was in 'declarations' of the testing module, and also in an imported module\n      //   in which case the module scope will be TestingModuleOverride.DECLARATION.\n      // * overrideTemplateUsingTestingModule was called for the component in which case the module\n      //   scope will be TestingModuleOverride.OVERRIDE_TEMPLATE.\n      //\n      // If the component was previously in the testing module's 'declarations' (meaning the\n      // current value is TestingModuleOverride.DECLARATION), then `moduleType` is the component's\n      // real module, which was imported. This pattern is understood to mean that the component\n      // should use its original scope, but that the testing module should also contain the\n      // component in its scope.\n      if (!this.componentToModuleScope.has(type) || this.componentToModuleScope.get(type) === TestingModuleOverride.DECLARATION) {\n        this.componentToModuleScope.set(type, moduleType);\n      }\n      return;\n    }\n    const directive = this.resolvers.directive.resolve(type);\n    if (directive) {\n      if (!type.hasOwnProperty(_NG_DIR_DEF)) {\n        this.pendingDirectives.add(type);\n      }\n      this.seenDirectives.add(type);\n      return;\n    }\n    const pipe = this.resolvers.pipe.resolve(type);\n    if (pipe && !type.hasOwnProperty(_NG_PIPE_DEF)) {\n      this.pendingPipes.add(type);\n      return;\n    }\n  }\n  queueTypesFromModulesArray(arr) {\n    // Because we may encounter the same NgModule or a standalone Component while processing\n    // the dependencies of an NgModule or a standalone Component, we cache them in this set so we\n    // can skip ones that have already been seen encountered. In some test setups, this caching\n    // resulted in 10X runtime improvement.\n    const processedDefs = new Set();\n    const queueTypesFromModulesArrayRecur = arr => {\n      for (const value of arr) {\n        if (Array.isArray(value)) {\n          queueTypesFromModulesArrayRecur(value);\n        } else if (hasNgModuleDef(value)) {\n          const def = value.ɵmod;\n          if (processedDefs.has(def)) {\n            continue;\n          }\n          processedDefs.add(def);\n          // Look through declarations, imports, and exports, and queue\n          // everything found there.\n          this.queueTypeArray(maybeUnwrapFn(def.declarations), value);\n          queueTypesFromModulesArrayRecur(maybeUnwrapFn(def.imports));\n          queueTypesFromModulesArrayRecur(maybeUnwrapFn(def.exports));\n        } else if (isModuleWithProviders(value)) {\n          queueTypesFromModulesArrayRecur([value.ngModule]);\n        } else if (isStandaloneComponent(value)) {\n          this.queueType(value, null);\n          const def = getComponentDef(value);\n          if (processedDefs.has(def)) {\n            continue;\n          }\n          processedDefs.add(def);\n          const dependencies = maybeUnwrapFn(def.dependencies ?? []);\n          dependencies.forEach(dependency => {\n            // Note: in AOT, the `dependencies` might also contain regular\n            // (NgModule-based) Component, Directive and Pipes, so we handle\n            // them separately and proceed with recursive process for standalone\n            // Components and NgModules only.\n            if (isStandaloneComponent(dependency) || hasNgModuleDef(dependency)) {\n              queueTypesFromModulesArrayRecur([dependency]);\n            } else {\n              this.queueType(dependency, null);\n            }\n          });\n        }\n      }\n    };\n    queueTypesFromModulesArrayRecur(arr);\n  }\n  // When module overrides (via `TestBed.overrideModule`) are present, it might affect all modules\n  // that import (even transitively) an overridden one. For all affected modules we need to\n  // recalculate their scopes for a given test run and restore original scopes at the end. The goal\n  // of this function is to collect all affected modules in a set for further processing. Example:\n  // if we have the following module hierarchy: A -> B -> C (where `->` means `imports`) and module\n  // `C` is overridden, we consider `A` and `B` as affected, since their scopes might become\n  // invalidated with the override.\n  collectModulesAffectedByOverrides(arr) {\n    const seenModules = new Set();\n    const affectedModules = new Set();\n    const calcAffectedModulesRecur = (arr, path) => {\n      for (const value of arr) {\n        if (Array.isArray(value)) {\n          // If the value is an array, just flatten it (by invoking this function recursively),\n          // keeping \"path\" the same.\n          calcAffectedModulesRecur(value, path);\n        } else if (hasNgModuleDef(value)) {\n          if (seenModules.has(value)) {\n            // If we've seen this module before and it's included into \"affected modules\" list, mark\n            // the whole path that leads to that module as affected, but do not descend into its\n            // imports, since we already examined them before.\n            if (affectedModules.has(value)) {\n              path.forEach(item => affectedModules.add(item));\n            }\n            continue;\n          }\n          seenModules.add(value);\n          if (this.overriddenModules.has(value)) {\n            path.forEach(item => affectedModules.add(item));\n          }\n          // Examine module imports recursively to look for overridden modules.\n          const moduleDef = value[_NG_MOD_DEF];\n          calcAffectedModulesRecur(maybeUnwrapFn(moduleDef.imports), path.concat(value));\n        }\n      }\n    };\n    calcAffectedModulesRecur(arr, []);\n    return affectedModules;\n  }\n  /**\n   * Preserve an original def (such as ɵmod, ɵinj, etc) before applying an override.\n   * Note: one class may have multiple defs (for example: ɵmod and ɵinj in case of\n   * an NgModule). If there is a def in a set already, don't override it, since\n   * an original one should be restored at the end of a test.\n   */\n  maybeStoreNgDef(prop, type) {\n    if (!this.initialNgDefs.has(type)) {\n      this.initialNgDefs.set(type, new Map());\n    }\n    const currentDefs = this.initialNgDefs.get(type);\n    if (!currentDefs.has(prop)) {\n      const currentDef = Object.getOwnPropertyDescriptor(type, prop);\n      currentDefs.set(prop, currentDef);\n    }\n  }\n  storeFieldOfDefOnType(type, defField, fieldName) {\n    const def = type[defField];\n    const originalValue = def[fieldName];\n    this.defCleanupOps.push({\n      object: def,\n      fieldName,\n      originalValue\n    });\n  }\n  /**\n   * Clears current components resolution queue, but stores the state of the queue, so we can\n   * restore it later. Clearing the queue is required before we try to compile components (via\n   * `TestBed.compileComponents`), so that component defs are in sync with the resolution queue.\n   */\n  clearComponentResolutionQueue() {\n    if (this.originalComponentResolutionQueue === null) {\n      this.originalComponentResolutionQueue = new Map();\n    }\n    _clearResolutionOfComponentResourcesQueue().forEach((value, key) => this.originalComponentResolutionQueue.set(key, value));\n  }\n  /*\n   * Restores component resolution queue to the previously saved state. This operation is performed\n   * as a part of restoring the state after completion of the current set of tests (that might\n   * potentially mutate the state).\n   */\n  restoreComponentResolutionQueue() {\n    if (this.originalComponentResolutionQueue !== null) {\n      _restoreComponentResolutionQueue(this.originalComponentResolutionQueue);\n      this.originalComponentResolutionQueue = null;\n    }\n  }\n  restoreOriginalState() {\n    // Process cleanup ops in reverse order so the field's original value is restored correctly (in\n    // case there were multiple overrides for the same field).\n    forEachRight(this.defCleanupOps, op => {\n      op.object[op.fieldName] = op.originalValue;\n    });\n    // Restore initial component/directive/pipe defs\n    this.initialNgDefs.forEach((defs, type) => {\n      if (_USE_RUNTIME_DEPS_TRACKER_FOR_JIT) {\n        _depsTracker.clearScopeCacheFor(type);\n      }\n      defs.forEach((descriptor, prop) => {\n        if (!descriptor) {\n          // Delete operations are generally undesirable since they have performance\n          // implications on objects they were applied to. In this particular case, situations\n          // where this code is invoked should be quite rare to cause any noticeable impact,\n          // since it's applied only to some test cases (for example when class with no\n          // annotations extends some @Component) when we need to clear 'ɵcmp' field on a given\n          // class to restore its original state (before applying overrides and running tests).\n          delete type[prop];\n        } else {\n          Object.defineProperty(type, prop, descriptor);\n        }\n      });\n    });\n    this.initialNgDefs.clear();\n    this.scopesWithOverriddenProviders.clear();\n    this.restoreComponentResolutionQueue();\n    // Restore the locale ID to the default value, this shouldn't be necessary but we never know\n    _setLocaleId(_DEFAULT_LOCALE_ID);\n  }\n  compileTestModule() {\n    class RootScopeModule {}\n    _compileNgModuleDefs(RootScopeModule, {\n      providers: [...this.rootProviderOverrides, _internalProvideZoneChangeDetection({}), TestBedApplicationErrorHandler, {\n        provide: _ChangeDetectionScheduler,\n        useExisting: _ChangeDetectionSchedulerImpl\n      }]\n    });\n    const providers = [{\n      provide: Compiler,\n      useFactory: () => new R3TestCompiler(this)\n    }, {\n      provide: _DEFER_BLOCK_CONFIG,\n      useValue: {\n        behavior: this.deferBlockBehavior\n      }\n    }, {\n      provide: _INTERNAL_APPLICATION_ERROR_HANDLER,\n      useFactory: () => {\n        if (this.rethrowApplicationTickErrors) {\n          const handler = inject$1(TestBedApplicationErrorHandler);\n          return e => {\n            handler.handleError(e);\n          };\n        } else {\n          const userErrorHandler = inject$1(ErrorHandler);\n          const ngZone = inject$1(NgZone);\n          return e => ngZone.runOutsideAngular(() => userErrorHandler.handleError(e));\n        }\n      }\n    }, ...this.providers, ...this.providerOverrides];\n    const imports = [RootScopeModule, this.additionalModuleTypes, this.imports || []];\n    _compileNgModuleDefs(this.testModuleType, {\n      declarations: this.declarations,\n      imports,\n      schemas: this.schemas,\n      providers\n    }, /* allowDuplicateDeclarationsInRoot */true);\n    this.applyProviderOverridesInScope(this.testModuleType);\n  }\n  get injector() {\n    if (this._injector !== null) {\n      return this._injector;\n    }\n    const providers = [];\n    const compilerOptions = this.platform.injector.get(COMPILER_OPTIONS, []);\n    compilerOptions.forEach(opts => {\n      if (opts.providers) {\n        providers.push(opts.providers);\n      }\n    });\n    if (this.compilerProviders !== null) {\n      providers.push(...this.compilerProviders);\n    }\n    this._injector = Injector.create({\n      providers,\n      parent: this.platform.injector\n    });\n    return this._injector;\n  }\n  // get overrides for a specific provider (if any)\n  getSingleProviderOverrides(provider) {\n    const token = getProviderToken(provider);\n    return this.providerOverridesByToken.get(token) || null;\n  }\n  getProviderOverrides(providers) {\n    if (!providers || !providers.length || this.providerOverridesByToken.size === 0) return [];\n    // There are two flattening operations here. The inner flattenProviders() operates on the\n    // metadata's providers and applies a mapping function which retrieves overrides for each\n    // incoming provider. The outer flatten() then flattens the produced overrides array. If this is\n    // not done, the array can contain other empty arrays (e.g. `[[], []]`) which leak into the\n    // providers array and contaminate any error messages that might be generated.\n    return flatten(flattenProviders(providers, provider => this.getSingleProviderOverrides(provider) || []));\n  }\n  getOverriddenProviders(providers) {\n    if (!providers || !providers.length || this.providerOverridesByToken.size === 0) return [];\n    const flattenedProviders = flattenProviders(providers);\n    const overrides = this.getProviderOverrides(flattenedProviders);\n    const overriddenProviders = [...flattenedProviders, ...overrides];\n    const final = [];\n    const seenOverriddenProviders = new Set();\n    // We iterate through the list of providers in reverse order to make sure provider overrides\n    // take precedence over the values defined in provider list. We also filter out all providers\n    // that have overrides, keeping overridden values only. This is needed, since presence of a\n    // provider with `ngOnDestroy` hook will cause this hook to be registered and invoked later.\n    forEachRight(overriddenProviders, provider => {\n      const token = getProviderToken(provider);\n      if (this.providerOverridesByToken.has(token)) {\n        if (!seenOverriddenProviders.has(token)) {\n          seenOverriddenProviders.add(token);\n          // Treat all overridden providers as `{multi: false}` (even if it's a multi-provider) to\n          // make sure that provided override takes highest precedence and is not combined with\n          // other instances of the same multi provider.\n          final.unshift({\n            ...provider,\n            multi: false\n          });\n        }\n      } else {\n        final.unshift(provider);\n      }\n    });\n    return final;\n  }\n  hasProviderOverrides(providers) {\n    return this.getProviderOverrides(providers).length > 0;\n  }\n  patchDefWithProviderOverrides(declaration, field) {\n    const def = declaration[field];\n    if (def && def.providersResolver) {\n      this.maybeStoreNgDef(field, declaration);\n      const resolver = def.providersResolver;\n      const processProvidersFn = providers => this.getOverriddenProviders(providers);\n      this.storeFieldOfDefOnType(declaration, field, 'providersResolver');\n      def.providersResolver = ngDef => resolver(ngDef, processProvidersFn);\n    }\n  }\n}\nfunction initResolvers() {\n  return {\n    module: new NgModuleResolver(),\n    component: new ComponentResolver(),\n    directive: new DirectiveResolver(),\n    pipe: new PipeResolver()\n  };\n}\nfunction isStandaloneComponent(value) {\n  const def = getComponentDef(value);\n  return !!def?.standalone;\n}\nfunction getComponentDef(value) {\n  return value.ɵcmp ?? null;\n}\nfunction hasNgModuleDef(value) {\n  return value.hasOwnProperty('ɵmod');\n}\nfunction isNgModule(value) {\n  return hasNgModuleDef(value);\n}\nfunction maybeUnwrapFn(maybeFn) {\n  return maybeFn instanceof Function ? maybeFn() : maybeFn;\n}\nfunction flatten(values) {\n  const out = [];\n  values.forEach(value => {\n    if (Array.isArray(value)) {\n      out.push(...flatten(value));\n    } else {\n      out.push(value);\n    }\n  });\n  return out;\n}\nfunction identityFn(value) {\n  return value;\n}\nfunction flattenProviders(providers, mapFn = identityFn) {\n  const out = [];\n  for (let provider of providers) {\n    if (_isEnvironmentProviders(provider)) {\n      provider = provider.ɵproviders;\n    }\n    if (Array.isArray(provider)) {\n      out.push(...flattenProviders(provider, mapFn));\n    } else {\n      out.push(mapFn(provider));\n    }\n  }\n  return out;\n}\nfunction getProviderField(provider, field) {\n  return provider && typeof provider === 'object' && provider[field];\n}\nfunction getProviderToken(provider) {\n  return getProviderField(provider, 'provide') || provider;\n}\nfunction isModuleWithProviders(value) {\n  return value.hasOwnProperty('ngModule');\n}\nfunction forEachRight(values, fn) {\n  for (let idx = values.length - 1; idx >= 0; idx--) {\n    fn(values[idx], idx);\n  }\n}\nfunction invalidTypeError(name, expectedType) {\n  return new Error(`${name} class doesn't have @${expectedType} decorator or is missing metadata.`);\n}\nclass R3TestCompiler {\n  testBed;\n  constructor(testBed) {\n    this.testBed = testBed;\n  }\n  compileModuleSync(moduleType) {\n    this.testBed._compileNgModuleSync(moduleType);\n    return new _NgModuleFactory(moduleType);\n  }\n  compileModuleAsync(moduleType) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      yield _this5.testBed._compileNgModuleAsync(moduleType);\n      return new _NgModuleFactory(moduleType);\n    })();\n  }\n  compileModuleAndAllComponentsSync(moduleType) {\n    const ngModuleFactory = this.compileModuleSync(moduleType);\n    const componentFactories = this.testBed._getComponentFactories(moduleType);\n    return new ModuleWithComponentFactories(ngModuleFactory, componentFactories);\n  }\n  compileModuleAndAllComponentsAsync(moduleType) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      const ngModuleFactory = yield _this6.compileModuleAsync(moduleType);\n      const componentFactories = _this6.testBed._getComponentFactories(moduleType);\n      return new ModuleWithComponentFactories(ngModuleFactory, componentFactories);\n    })();\n  }\n  clearCache() {}\n  clearCacheFor(type) {}\n  getModuleId(moduleType) {\n    const meta = this.testBed._getModuleResolver().resolve(moduleType);\n    return meta && meta.id || undefined;\n  }\n}\n\n// The formatter and CI disagree on how this import statement should be formatted. Both try to keep\n// it on one line, too, which has gotten very hard to read & manage. So disable the formatter for\n// this statement only.\nlet _nextRootElementId = 0;\n/**\n * Returns a singleton of the `TestBed` class.\n *\n * @publicApi\n */\nfunction getTestBed() {\n  return TestBedImpl.INSTANCE;\n}\n/**\n * @description\n * Configures and initializes environment for unit testing and provides methods for\n * creating components and services in unit tests.\n *\n * TestBed is the primary api for writing unit tests for Angular applications and libraries.\n */\nclass TestBedImpl {\n  static _INSTANCE = null;\n  static get INSTANCE() {\n    return TestBedImpl._INSTANCE = TestBedImpl._INSTANCE || new TestBedImpl();\n  }\n  /**\n   * Teardown options that have been configured at the environment level.\n   * Used as a fallback if no instance-level options have been provided.\n   */\n  static _environmentTeardownOptions;\n  /**\n   * \"Error on unknown elements\" option that has been configured at the environment level.\n   * Used as a fallback if no instance-level option has been provided.\n   */\n  static _environmentErrorOnUnknownElementsOption;\n  /**\n   * \"Error on unknown properties\" option that has been configured at the environment level.\n   * Used as a fallback if no instance-level option has been provided.\n   */\n  static _environmentErrorOnUnknownPropertiesOption;\n  /**\n   * Teardown options that have been configured at the `TestBed` instance level.\n   * These options take precedence over the environment-level ones.\n   */\n  _instanceTeardownOptions;\n  /**\n   * Defer block behavior option that specifies whether defer blocks will be triggered manually\n   * or set to play through.\n   */\n  _instanceDeferBlockBehavior = DEFER_BLOCK_DEFAULT_BEHAVIOR;\n  /**\n   * \"Error on unknown elements\" option that has been configured at the `TestBed` instance level.\n   * This option takes precedence over the environment-level one.\n   */\n  _instanceErrorOnUnknownElementsOption;\n  /**\n   * \"Error on unknown properties\" option that has been configured at the `TestBed` instance level.\n   * This option takes precedence over the environment-level one.\n   */\n  _instanceErrorOnUnknownPropertiesOption;\n  /**\n   * Stores the previous \"Error on unknown elements\" option value,\n   * allowing to restore it in the reset testing module logic.\n   */\n  _previousErrorOnUnknownElementsOption;\n  /**\n   * Stores the previous \"Error on unknown properties\" option value,\n   * allowing to restore it in the reset testing module logic.\n   */\n  _previousErrorOnUnknownPropertiesOption;\n  /**\n   * Initialize the environment for testing with a compiler factory, a PlatformRef, and an\n   * angular module. These are common to every test in the suite.\n   *\n   * This may only be called once, to set up the common providers for the current test\n   * suite on the current platform. If you absolutely need to change the providers,\n   * first use `resetTestEnvironment`.\n   *\n   * Test modules and platforms for individual platforms are available from\n   * '@angular/<platform_name>/testing'.\n   *\n   * @publicApi\n   */\n  static initTestEnvironment(ngModule, platform, options) {\n    const testBed = TestBedImpl.INSTANCE;\n    testBed.initTestEnvironment(ngModule, platform, options);\n    return testBed;\n  }\n  /**\n   * Reset the providers for the test injector.\n   *\n   * @publicApi\n   */\n  static resetTestEnvironment() {\n    TestBedImpl.INSTANCE.resetTestEnvironment();\n  }\n  static configureCompiler(config) {\n    return TestBedImpl.INSTANCE.configureCompiler(config);\n  }\n  /**\n   * Allows overriding default providers, directives, pipes, modules of the test injector,\n   * which are defined in test_injector.js\n   */\n  static configureTestingModule(moduleDef) {\n    return TestBedImpl.INSTANCE.configureTestingModule(moduleDef);\n  }\n  /**\n   * Compile components with a `templateUrl` for the test's NgModule.\n   * It is necessary to call this function\n   * as fetching urls is asynchronous.\n   */\n  static compileComponents() {\n    return TestBedImpl.INSTANCE.compileComponents();\n  }\n  static overrideModule(ngModule, override) {\n    return TestBedImpl.INSTANCE.overrideModule(ngModule, override);\n  }\n  static overrideComponent(component, override) {\n    return TestBedImpl.INSTANCE.overrideComponent(component, override);\n  }\n  static overrideDirective(directive, override) {\n    return TestBedImpl.INSTANCE.overrideDirective(directive, override);\n  }\n  static overridePipe(pipe, override) {\n    return TestBedImpl.INSTANCE.overridePipe(pipe, override);\n  }\n  static overrideTemplate(component, template) {\n    return TestBedImpl.INSTANCE.overrideTemplate(component, template);\n  }\n  /**\n   * Overrides the template of the given component, compiling the template\n   * in the context of the TestingModule.\n   *\n   * Note: This works for JIT and AOTed components as well.\n   */\n  static overrideTemplateUsingTestingModule(component, template) {\n    return TestBedImpl.INSTANCE.overrideTemplateUsingTestingModule(component, template);\n  }\n  static overrideProvider(token, provider) {\n    return TestBedImpl.INSTANCE.overrideProvider(token, provider);\n  }\n  static inject(token, notFoundValue, flags) {\n    return TestBedImpl.INSTANCE.inject(token, notFoundValue, _convertToBitFlags(flags));\n  }\n  /** @deprecated from v9.0.0 use TestBed.inject */\n  static get(token, notFoundValue = Injector.THROW_IF_NOT_FOUND, flags = InjectFlags.Default) {\n    return TestBedImpl.INSTANCE.inject(token, notFoundValue, flags);\n  }\n  /**\n   * Runs the given function in the `EnvironmentInjector` context of `TestBed`.\n   *\n   * @see {@link EnvironmentInjector#runInContext}\n   */\n  static runInInjectionContext(fn) {\n    return TestBedImpl.INSTANCE.runInInjectionContext(fn);\n  }\n  static createComponent(component) {\n    return TestBedImpl.INSTANCE.createComponent(component);\n  }\n  static resetTestingModule() {\n    return TestBedImpl.INSTANCE.resetTestingModule();\n  }\n  static execute(tokens, fn, context) {\n    return TestBedImpl.INSTANCE.execute(tokens, fn, context);\n  }\n  static get platform() {\n    return TestBedImpl.INSTANCE.platform;\n  }\n  static get ngModule() {\n    return TestBedImpl.INSTANCE.ngModule;\n  }\n  static flushEffects() {\n    return TestBedImpl.INSTANCE.flushEffects();\n  }\n  // Properties\n  platform = null;\n  ngModule = null;\n  _compiler = null;\n  _testModuleRef = null;\n  _activeFixtures = [];\n  /**\n   * Internal-only flag to indicate whether a module\n   * scoping queue has been checked and flushed already.\n   * @docs-private\n   */\n  globalCompilationChecked = false;\n  /**\n   * Initialize the environment for testing with a compiler factory, a PlatformRef, and an\n   * angular module. These are common to every test in the suite.\n   *\n   * This may only be called once, to set up the common providers for the current test\n   * suite on the current platform. If you absolutely need to change the providers,\n   * first use `resetTestEnvironment`.\n   *\n   * Test modules and platforms for individual platforms are available from\n   * '@angular/<platform_name>/testing'.\n   *\n   * @publicApi\n   */\n  initTestEnvironment(ngModule, platform, options) {\n    if (this.platform || this.ngModule) {\n      throw new Error('Cannot set base providers because it has already been called');\n    }\n    TestBedImpl._environmentTeardownOptions = options?.teardown;\n    TestBedImpl._environmentErrorOnUnknownElementsOption = options?.errorOnUnknownElements;\n    TestBedImpl._environmentErrorOnUnknownPropertiesOption = options?.errorOnUnknownProperties;\n    this.platform = platform;\n    this.ngModule = ngModule;\n    this._compiler = new TestBedCompiler(this.platform, this.ngModule);\n    // TestBed does not have an API which can reliably detect the start of a test, and thus could be\n    // used to track the state of the NgModule registry and reset it correctly. Instead, when we\n    // know we're in a testing scenario, we disable the check for duplicate NgModule registration\n    // completely.\n    _setAllowDuplicateNgModuleIdsForTest(true);\n  }\n  /**\n   * Reset the providers for the test injector.\n   *\n   * @publicApi\n   */\n  resetTestEnvironment() {\n    this.resetTestingModule();\n    this._compiler = null;\n    this.platform = null;\n    this.ngModule = null;\n    TestBedImpl._environmentTeardownOptions = undefined;\n    _setAllowDuplicateNgModuleIdsForTest(false);\n  }\n  resetTestingModule() {\n    this.checkGlobalCompilationFinished();\n    _resetCompiledComponents();\n    if (this._compiler !== null) {\n      this.compiler.restoreOriginalState();\n    }\n    this._compiler = new TestBedCompiler(this.platform, this.ngModule);\n    // Restore the previous value of the \"error on unknown elements\" option\n    _setUnknownElementStrictMode(this._previousErrorOnUnknownElementsOption ?? THROW_ON_UNKNOWN_ELEMENTS_DEFAULT);\n    // Restore the previous value of the \"error on unknown properties\" option\n    _setUnknownPropertyStrictMode(this._previousErrorOnUnknownPropertiesOption ?? THROW_ON_UNKNOWN_PROPERTIES_DEFAULT);\n    // We have to chain a couple of try/finally blocks, because each step can\n    // throw errors and we don't want it to interrupt the next step and we also\n    // want an error to be thrown at the end.\n    try {\n      this.destroyActiveFixtures();\n    } finally {\n      try {\n        if (this.shouldTearDownTestingModule()) {\n          this.tearDownTestingModule();\n        }\n      } finally {\n        this._testModuleRef = null;\n        this._instanceTeardownOptions = undefined;\n        this._instanceErrorOnUnknownElementsOption = undefined;\n        this._instanceErrorOnUnknownPropertiesOption = undefined;\n        this._instanceDeferBlockBehavior = DEFER_BLOCK_DEFAULT_BEHAVIOR;\n      }\n    }\n    return this;\n  }\n  configureCompiler(config) {\n    if (config.useJit != null) {\n      throw new Error('JIT compiler is not configurable via TestBed APIs.');\n    }\n    if (config.providers !== undefined) {\n      this.compiler.setCompilerProviders(config.providers);\n    }\n    return this;\n  }\n  configureTestingModule(moduleDef) {\n    this.assertNotInstantiated('TestBed.configureTestingModule', 'configure the test module');\n    // Trigger module scoping queue flush before executing other TestBed operations in a test.\n    // This is needed for the first test invocation to ensure that globally declared modules have\n    // their components scoped properly. See the `checkGlobalCompilationFinished` function\n    // description for additional info.\n    this.checkGlobalCompilationFinished();\n    // Always re-assign the options, even if they're undefined.\n    // This ensures that we don't carry them between tests.\n    this._instanceTeardownOptions = moduleDef.teardown;\n    this._instanceErrorOnUnknownElementsOption = moduleDef.errorOnUnknownElements;\n    this._instanceErrorOnUnknownPropertiesOption = moduleDef.errorOnUnknownProperties;\n    this._instanceDeferBlockBehavior = moduleDef.deferBlockBehavior ?? DEFER_BLOCK_DEFAULT_BEHAVIOR;\n    // Store the current value of the strict mode option,\n    // so we can restore it later\n    this._previousErrorOnUnknownElementsOption = _getUnknownElementStrictMode();\n    _setUnknownElementStrictMode(this.shouldThrowErrorOnUnknownElements());\n    this._previousErrorOnUnknownPropertiesOption = _getUnknownPropertyStrictMode();\n    _setUnknownPropertyStrictMode(this.shouldThrowErrorOnUnknownProperties());\n    this.compiler.configureTestingModule(moduleDef);\n    return this;\n  }\n  compileComponents() {\n    return this.compiler.compileComponents();\n  }\n  inject(token, notFoundValue, flags) {\n    if (token === TestBed) {\n      return this;\n    }\n    const UNDEFINED = {};\n    const result = this.testModuleRef.injector.get(token, UNDEFINED, _convertToBitFlags(flags));\n    return result === UNDEFINED ? this.compiler.injector.get(token, notFoundValue, flags) : result;\n  }\n  /** @deprecated from v9.0.0 use TestBed.inject */\n  get(token, notFoundValue = Injector.THROW_IF_NOT_FOUND, flags = InjectFlags.Default) {\n    return this.inject(token, notFoundValue, flags);\n  }\n  runInInjectionContext(fn) {\n    return runInInjectionContext(this.inject(EnvironmentInjector), fn);\n  }\n  execute(tokens, fn, context) {\n    const params = tokens.map(t => this.inject(t));\n    return fn.apply(context, params);\n  }\n  overrideModule(ngModule, override) {\n    this.assertNotInstantiated('overrideModule', 'override module metadata');\n    this.compiler.overrideModule(ngModule, override);\n    return this;\n  }\n  overrideComponent(component, override) {\n    this.assertNotInstantiated('overrideComponent', 'override component metadata');\n    this.compiler.overrideComponent(component, override);\n    return this;\n  }\n  overrideTemplateUsingTestingModule(component, template) {\n    this.assertNotInstantiated('TestBed.overrideTemplateUsingTestingModule', 'Cannot override template when the test module has already been instantiated');\n    this.compiler.overrideTemplateUsingTestingModule(component, template);\n    return this;\n  }\n  overrideDirective(directive, override) {\n    this.assertNotInstantiated('overrideDirective', 'override directive metadata');\n    this.compiler.overrideDirective(directive, override);\n    return this;\n  }\n  overridePipe(pipe, override) {\n    this.assertNotInstantiated('overridePipe', 'override pipe metadata');\n    this.compiler.overridePipe(pipe, override);\n    return this;\n  }\n  /**\n   * Overwrites all providers for the given token with the given provider definition.\n   */\n  overrideProvider(token, provider) {\n    this.assertNotInstantiated('overrideProvider', 'override provider');\n    this.compiler.overrideProvider(token, provider);\n    return this;\n  }\n  overrideTemplate(component, template) {\n    return this.overrideComponent(component, {\n      set: {\n        template,\n        templateUrl: null\n      }\n    });\n  }\n  createComponent(type) {\n    const testComponentRenderer = this.inject(TestComponentRenderer);\n    const rootElId = `root${_nextRootElementId++}`;\n    testComponentRenderer.insertRootElement(rootElId);\n    if (_getAsyncClassMetadataFn(type)) {\n      throw new Error(`Component '${type.name}' has unresolved metadata. ` + `Please call \\`await TestBed.compileComponents()\\` before running this test.`);\n    }\n    const componentDef = type.ɵcmp;\n    if (!componentDef) {\n      throw new Error(`It looks like '${_stringify(type)}' has not been compiled.`);\n    }\n    const componentFactory = new _Render3ComponentFactory(componentDef);\n    const initComponent = () => {\n      const componentRef = componentFactory.create(Injector.NULL, [], `#${rootElId}`, this.testModuleRef);\n      return this.runInInjectionContext(() => new ComponentFixture(componentRef));\n    };\n    const noNgZone = this.inject(ComponentFixtureNoNgZone, false);\n    const ngZone = noNgZone ? null : this.inject(NgZone, null);\n    const fixture = ngZone ? ngZone.run(initComponent) : initComponent();\n    this._activeFixtures.push(fixture);\n    return fixture;\n  }\n  /**\n   * @internal strip this from published d.ts files due to\n   * https://github.com/microsoft/TypeScript/issues/36216\n   */\n  get compiler() {\n    if (this._compiler === null) {\n      throw new Error(`Need to call TestBed.initTestEnvironment() first`);\n    }\n    return this._compiler;\n  }\n  /**\n   * @internal strip this from published d.ts files due to\n   * https://github.com/microsoft/TypeScript/issues/36216\n   */\n  get testModuleRef() {\n    if (this._testModuleRef === null) {\n      this._testModuleRef = this.compiler.finalize();\n    }\n    return this._testModuleRef;\n  }\n  assertNotInstantiated(methodName, methodDescription) {\n    if (this._testModuleRef !== null) {\n      throw new Error(`Cannot ${methodDescription} when the test module has already been instantiated. ` + `Make sure you are not using \\`inject\\` before \\`${methodName}\\`.`);\n    }\n  }\n  /**\n   * Check whether the module scoping queue should be flushed, and flush it if needed.\n   *\n   * When the TestBed is reset, it clears the JIT module compilation queue, cancelling any\n   * in-progress module compilation. This creates a potential hazard - the very first time the\n   * TestBed is initialized (or if it's reset without being initialized), there may be pending\n   * compilations of modules declared in global scope. These compilations should be finished.\n   *\n   * To ensure that globally declared modules have their components scoped properly, this function\n   * is called whenever TestBed is initialized or reset. The _first_ time that this happens, prior\n   * to any other operations, the scoping queue is flushed.\n   */\n  checkGlobalCompilationFinished() {\n    // Checking _testNgModuleRef is null should not be necessary, but is left in as an additional\n    // guard that compilations queued in tests (after instantiation) are never flushed accidentally.\n    if (!this.globalCompilationChecked && this._testModuleRef === null) {\n      _flushModuleScopingQueueAsMuchAsPossible();\n    }\n    this.globalCompilationChecked = true;\n  }\n  destroyActiveFixtures() {\n    let errorCount = 0;\n    this._activeFixtures.forEach(fixture => {\n      try {\n        fixture.destroy();\n      } catch (e) {\n        errorCount++;\n        console.error('Error during cleanup of component', {\n          component: fixture.componentInstance,\n          stacktrace: e\n        });\n      }\n    });\n    this._activeFixtures = [];\n    if (errorCount > 0 && this.shouldRethrowTeardownErrors()) {\n      throw Error(`${errorCount} ${errorCount === 1 ? 'component' : 'components'} ` + `threw errors during cleanup`);\n    }\n  }\n  shouldRethrowTeardownErrors() {\n    const instanceOptions = this._instanceTeardownOptions;\n    const environmentOptions = TestBedImpl._environmentTeardownOptions;\n    // If the new teardown behavior hasn't been configured, preserve the old behavior.\n    if (!instanceOptions && !environmentOptions) {\n      return TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT;\n    }\n    // Otherwise use the configured behavior or default to rethrowing.\n    return instanceOptions?.rethrowErrors ?? environmentOptions?.rethrowErrors ?? this.shouldTearDownTestingModule();\n  }\n  shouldThrowErrorOnUnknownElements() {\n    // Check if a configuration has been provided to throw when an unknown element is found\n    return this._instanceErrorOnUnknownElementsOption ?? TestBedImpl._environmentErrorOnUnknownElementsOption ?? THROW_ON_UNKNOWN_ELEMENTS_DEFAULT;\n  }\n  shouldThrowErrorOnUnknownProperties() {\n    // Check if a configuration has been provided to throw when an unknown property is found\n    return this._instanceErrorOnUnknownPropertiesOption ?? TestBedImpl._environmentErrorOnUnknownPropertiesOption ?? THROW_ON_UNKNOWN_PROPERTIES_DEFAULT;\n  }\n  shouldTearDownTestingModule() {\n    return this._instanceTeardownOptions?.destroyAfterEach ?? TestBedImpl._environmentTeardownOptions?.destroyAfterEach ?? TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT;\n  }\n  getDeferBlockBehavior() {\n    return this._instanceDeferBlockBehavior;\n  }\n  tearDownTestingModule() {\n    // If the module ref has already been destroyed, we won't be able to get a test renderer.\n    if (this._testModuleRef === null) {\n      return;\n    }\n    // Resolve the renderer ahead of time, because we want to remove the root elements as the very\n    // last step, but the injector will be destroyed as a part of the module ref destruction.\n    const testRenderer = this.inject(TestComponentRenderer);\n    try {\n      this._testModuleRef.destroy();\n    } catch (e) {\n      if (this.shouldRethrowTeardownErrors()) {\n        throw e;\n      } else {\n        console.error('Error during cleanup of a testing module', {\n          component: this._testModuleRef.instance,\n          stacktrace: e\n        });\n      }\n    } finally {\n      testRenderer.removeAllRootElements?.();\n    }\n  }\n  /**\n   * Execute any pending effects.\n   *\n   * @developerPreview\n   */\n  flushEffects() {\n    this.inject(_MicrotaskEffectScheduler).flush();\n    this.inject(_EffectScheduler).flush();\n  }\n}\n/**\n * @description\n * Configures and initializes environment for unit testing and provides methods for\n * creating components and services in unit tests.\n *\n * `TestBed` is the primary api for writing unit tests for Angular applications and libraries.\n *\n * @publicApi\n */\nconst TestBed = TestBedImpl;\n/**\n * Allows injecting dependencies in `beforeEach()` and `it()`. Note: this function\n * (imported from the `@angular/core/testing` package) can **only** be used to inject dependencies\n * in tests. To inject dependencies in your application code, use the [`inject`](api/core/inject)\n * function from the `@angular/core` package instead.\n *\n * Example:\n *\n * ```ts\n * beforeEach(inject([Dependency, AClass], (dep, object) => {\n *   // some code that uses `dep` and `object`\n *   // ...\n * }));\n *\n * it('...', inject([AClass], (object) => {\n *   object.doSomething();\n *   expect(...);\n * })\n * ```\n *\n * @publicApi\n */\nfunction inject(tokens, fn) {\n  const testBed = TestBedImpl.INSTANCE;\n  // Not using an arrow function to preserve context passed from call site\n  return function () {\n    return testBed.execute(tokens, fn, this);\n  };\n}\n/**\n * @publicApi\n */\nclass InjectSetupWrapper {\n  _moduleDef;\n  constructor(_moduleDef) {\n    this._moduleDef = _moduleDef;\n  }\n  _addModule() {\n    const moduleDef = this._moduleDef();\n    if (moduleDef) {\n      TestBedImpl.configureTestingModule(moduleDef);\n    }\n  }\n  inject(tokens, fn) {\n    const self = this;\n    // Not using an arrow function to preserve context passed from call site\n    return function () {\n      self._addModule();\n      return inject(tokens, fn).call(this);\n    };\n  }\n}\nfunction withModule(moduleDef, fn) {\n  if (fn) {\n    // Not using an arrow function to preserve context passed from call site\n    return function () {\n      const testBed = TestBedImpl.INSTANCE;\n      if (moduleDef) {\n        testBed.configureTestingModule(moduleDef);\n      }\n      return fn.apply(this);\n    };\n  }\n  return new InjectSetupWrapper(() => moduleDef);\n}\n\n/**\n * Public Test Library for unit testing Angular applications. Assumes that you are running\n * with Jasmine, Mocha, or a similar framework which exports a beforeEach function and\n * allows tests to be asynchronous by either returning a promise or using a 'done' parameter.\n */\n// Reset the test providers and the fake async zone before each test.\n// We keep a guard because somehow this file can make it into a bundle and be executed\n// beforeEach is only defined when executing the tests\nglobalThis.beforeEach?.(getCleanupHook(false));\n// We provide both a `beforeEach` and `afterEach`, because the updated behavior for\n// tearing down the module is supposed to run after the test so that we can associate\n// teardown errors with the correct test.\n// We keep a guard because somehow this file can make it into a bundle and be executed\n// afterEach is only defined when executing the tests\nglobalThis.afterEach?.(getCleanupHook(true));\nfunction getCleanupHook(expectedTeardownValue) {\n  return () => {\n    const testBed = TestBedImpl.INSTANCE;\n    if (testBed.shouldTearDownTestingModule() === expectedTeardownValue) {\n      testBed.resetTestingModule();\n      resetFakeAsyncZoneIfExists();\n    }\n  };\n}\n/**\n * This API should be removed. But doing so seems to break `google3` and so it requires a bit of\n * investigation.\n *\n * A work around is to mark it as `@codeGenApi` for now and investigate later.\n *\n * @codeGenApi\n */\n// TODO(iminar): Remove this code in a safe way.\nconst __core_private_testing_placeholder__ = '';\n\n/**\n * Fake implementation of user agent history and navigation behavior. This is a\n * high-fidelity implementation of browser behavior that attempts to emulate\n * things like traversal delay.\n */\nclass FakeNavigation {\n  /**\n   * The fake implementation of an entries array. Only same-document entries\n   * allowed.\n   */\n  entriesArr = [];\n  /**\n   * The current active entry index into `entriesArr`.\n   */\n  currentEntryIndex = 0;\n  /**\n   * The current navigate event.\n   * @internal\n   */\n  navigateEvent = null;\n  /**\n   * A Map of pending traversals, so that traversals to the same entry can be\n   * re-used.\n   */\n  traversalQueue = new Map();\n  /**\n   * A Promise that resolves when the previous traversals have finished. Used to\n   * simulate the cross-process communication necessary for traversals.\n   */\n  nextTraversal = Promise.resolve();\n  /**\n   * A prospective current active entry index, which includes unresolved\n   * traversals. Used by `go` to determine where navigations are intended to go.\n   */\n  prospectiveEntryIndex = 0;\n  /**\n   * A test-only option to make traversals synchronous, rather than emulate\n   * cross-process communication.\n   */\n  synchronousTraversals = false;\n  /** Whether to allow a call to setInitialEntryForTesting. */\n  canSetInitialEntry = true;\n  /**\n   * `EventTarget` to dispatch events.\n   * @internal\n   */\n  eventTarget;\n  /** The next unique id for created entries. Replace recreates this id. */\n  nextId = 0;\n  /** The next unique key for created entries. Replace inherits this id. */\n  nextKey = 0;\n  /** Whether this fake is disposed. */\n  disposed = false;\n  /** Equivalent to `navigation.currentEntry`. */\n  get currentEntry() {\n    return this.entriesArr[this.currentEntryIndex];\n  }\n  get canGoBack() {\n    return this.currentEntryIndex > 0;\n  }\n  get canGoForward() {\n    return this.currentEntryIndex < this.entriesArr.length - 1;\n  }\n  createEventTarget;\n  _window;\n  get window() {\n    return this._window;\n  }\n  constructor(doc, startURL) {\n    this.createEventTarget = () => {\n      try {\n        // `document.createElement` because NodeJS `EventTarget` is\n        // incompatible with Domino's `Event`. That is, attempting to\n        // dispatch an event created by Domino's patched `Event` will\n        // throw an error since it is not an instance of a real Node\n        // `Event`.\n        return doc.createElement('div');\n      } catch {\n        // Fallback to a basic EventTarget if `document.createElement`\n        // fails. This can happen with tests that pass in a value for document\n        // that is stubbed.\n        return new EventTarget();\n      }\n    };\n    this._window = document.defaultView ?? this.createEventTarget();\n    this.eventTarget = this.createEventTarget();\n    // First entry.\n    this.setInitialEntryForTesting(startURL);\n  }\n  /**\n   * Sets the initial entry.\n   */\n  setInitialEntryForTesting(url, options = {\n    historyState: null\n  }) {\n    if (!this.canSetInitialEntry) {\n      throw new Error('setInitialEntryForTesting can only be called before any ' + 'navigation has occurred');\n    }\n    const currentInitialEntry = this.entriesArr[0];\n    this.entriesArr[0] = new FakeNavigationHistoryEntry(this.eventTarget, new URL(url).toString(), {\n      index: 0,\n      key: currentInitialEntry?.key ?? String(this.nextKey++),\n      id: currentInitialEntry?.id ?? String(this.nextId++),\n      sameDocument: true,\n      historyState: options?.historyState,\n      state: options.state\n    });\n  }\n  /** Returns whether the initial entry is still eligible to be set. */\n  canSetInitialEntryForTesting() {\n    return this.canSetInitialEntry;\n  }\n  /**\n   * Sets whether to emulate traversals as synchronous rather than\n   * asynchronous.\n   */\n  setSynchronousTraversalsForTesting(synchronousTraversals) {\n    this.synchronousTraversals = synchronousTraversals;\n  }\n  /** Equivalent to `navigation.entries()`. */\n  entries() {\n    return this.entriesArr.slice();\n  }\n  /** Equivalent to `navigation.navigate()`. */\n  navigate(url, options) {\n    const fromUrl = new URL(this.currentEntry.url);\n    const toUrl = new URL(url, this.currentEntry.url);\n    let navigationType;\n    if (!options?.history || options.history === 'auto') {\n      // Auto defaults to push, but if the URLs are the same, is a replace.\n      if (fromUrl.toString() === toUrl.toString()) {\n        navigationType = 'replace';\n      } else {\n        navigationType = 'push';\n      }\n    } else {\n      navigationType = options.history;\n    }\n    const hashChange = isHashChange(fromUrl, toUrl);\n    const destination = new FakeNavigationDestination({\n      url: toUrl.toString(),\n      state: options?.state,\n      sameDocument: hashChange,\n      historyState: null\n    });\n    const result = new InternalNavigationResult(this);\n    const intercepted = this.userAgentNavigate(destination, result, {\n      navigationType,\n      cancelable: true,\n      canIntercept: true,\n      // Always false for navigate().\n      userInitiated: false,\n      hashChange,\n      info: options?.info\n    });\n    if (!intercepted) {\n      this.updateNavigationEntriesForSameDocumentNavigation(this.navigateEvent);\n    }\n    return {\n      committed: result.committed,\n      finished: result.finished\n    };\n  }\n  /** Equivalent to `history.pushState()`. */\n  pushState(data, title, url) {\n    this.pushOrReplaceState('push', data, title, url);\n  }\n  /** Equivalent to `history.replaceState()`. */\n  replaceState(data, title, url) {\n    this.pushOrReplaceState('replace', data, title, url);\n  }\n  pushOrReplaceState(navigationType, data, _title, url) {\n    const fromUrl = new URL(this.currentEntry.url);\n    const toUrl = url ? new URL(url, this.currentEntry.url) : fromUrl;\n    const hashChange = isHashChange(fromUrl, toUrl);\n    const destination = new FakeNavigationDestination({\n      url: toUrl.toString(),\n      sameDocument: true,\n      historyState: data\n    });\n    const result = new InternalNavigationResult(this);\n    const intercepted = this.userAgentNavigate(destination, result, {\n      navigationType,\n      cancelable: true,\n      canIntercept: true,\n      // Always false for pushState() or replaceState().\n      userInitiated: false,\n      hashChange\n    });\n    if (intercepted) {\n      return;\n    }\n    this.updateNavigationEntriesForSameDocumentNavigation(this.navigateEvent);\n  }\n  /** Equivalent to `navigation.traverseTo()`. */\n  traverseTo(key, options) {\n    const fromUrl = new URL(this.currentEntry.url);\n    const entry = this.findEntry(key);\n    if (!entry) {\n      const domException = new DOMException('Invalid key', 'InvalidStateError');\n      const committed = Promise.reject(domException);\n      const finished = Promise.reject(domException);\n      committed.catch(() => {});\n      finished.catch(() => {});\n      return {\n        committed,\n        finished\n      };\n    }\n    if (entry === this.currentEntry) {\n      return {\n        committed: Promise.resolve(this.currentEntry),\n        finished: Promise.resolve(this.currentEntry)\n      };\n    }\n    if (this.traversalQueue.has(entry.key)) {\n      const existingResult = this.traversalQueue.get(entry.key);\n      return {\n        committed: existingResult.committed,\n        finished: existingResult.finished\n      };\n    }\n    const hashChange = isHashChange(fromUrl, new URL(entry.url, this.currentEntry.url));\n    const destination = new FakeNavigationDestination({\n      url: entry.url,\n      state: entry.getState(),\n      historyState: entry.getHistoryState(),\n      key: entry.key,\n      id: entry.id,\n      index: entry.index,\n      sameDocument: entry.sameDocument\n    });\n    this.prospectiveEntryIndex = entry.index;\n    const result = new InternalNavigationResult(this);\n    this.traversalQueue.set(entry.key, result);\n    this.runTraversal(() => {\n      this.traversalQueue.delete(entry.key);\n      const intercepted = this.userAgentNavigate(destination, result, {\n        navigationType: 'traverse',\n        cancelable: true,\n        canIntercept: true,\n        // Always false for traverseTo().\n        userInitiated: false,\n        hashChange,\n        info: options?.info\n      });\n      if (!intercepted) {\n        this.userAgentTraverse(this.navigateEvent);\n      }\n    });\n    return {\n      committed: result.committed,\n      finished: result.finished\n    };\n  }\n  /** Equivalent to `navigation.back()`. */\n  back(options) {\n    if (this.currentEntryIndex === 0) {\n      const domException = new DOMException('Cannot go back', 'InvalidStateError');\n      const committed = Promise.reject(domException);\n      const finished = Promise.reject(domException);\n      committed.catch(() => {});\n      finished.catch(() => {});\n      return {\n        committed,\n        finished\n      };\n    }\n    const entry = this.entriesArr[this.currentEntryIndex - 1];\n    return this.traverseTo(entry.key, options);\n  }\n  /** Equivalent to `navigation.forward()`. */\n  forward(options) {\n    if (this.currentEntryIndex === this.entriesArr.length - 1) {\n      const domException = new DOMException('Cannot go forward', 'InvalidStateError');\n      const committed = Promise.reject(domException);\n      const finished = Promise.reject(domException);\n      committed.catch(() => {});\n      finished.catch(() => {});\n      return {\n        committed,\n        finished\n      };\n    }\n    const entry = this.entriesArr[this.currentEntryIndex + 1];\n    return this.traverseTo(entry.key, options);\n  }\n  /**\n   * Equivalent to `history.go()`.\n   * Note that this method does not actually work precisely to how Chrome\n   * does, instead choosing a simpler model with less unexpected behavior.\n   * Chrome has a few edge case optimizations, for instance with repeated\n   * `back(); forward()` chains it collapses certain traversals.\n   */\n  go(direction) {\n    const targetIndex = this.prospectiveEntryIndex + direction;\n    if (targetIndex >= this.entriesArr.length || targetIndex < 0) {\n      return;\n    }\n    this.prospectiveEntryIndex = targetIndex;\n    this.runTraversal(() => {\n      // Check again that destination is in the entries array.\n      if (targetIndex >= this.entriesArr.length || targetIndex < 0) {\n        return;\n      }\n      const fromUrl = new URL(this.currentEntry.url);\n      const entry = this.entriesArr[targetIndex];\n      const hashChange = isHashChange(fromUrl, new URL(entry.url, this.currentEntry.url));\n      const destination = new FakeNavigationDestination({\n        url: entry.url,\n        state: entry.getState(),\n        historyState: entry.getHistoryState(),\n        key: entry.key,\n        id: entry.id,\n        index: entry.index,\n        sameDocument: entry.sameDocument\n      });\n      const result = new InternalNavigationResult(this);\n      const intercepted = this.userAgentNavigate(destination, result, {\n        navigationType: 'traverse',\n        cancelable: true,\n        canIntercept: true,\n        // Always false for go().\n        userInitiated: false,\n        hashChange\n      });\n      if (!intercepted) {\n        this.userAgentTraverse(this.navigateEvent);\n      }\n    });\n  }\n  /** Runs a traversal synchronously or asynchronously */\n  runTraversal(traversal) {\n    if (this.synchronousTraversals) {\n      traversal();\n      return;\n    }\n    // Each traversal occupies a single timeout resolution.\n    // This means that Promises added to commit and finish should resolve\n    // before the next traversal.\n    this.nextTraversal = this.nextTraversal.then(() => {\n      return new Promise(resolve => {\n        setTimeout(() => {\n          resolve();\n          traversal();\n        });\n      });\n    });\n  }\n  /** Equivalent to `navigation.addEventListener()`. */\n  addEventListener(type, callback, options) {\n    this.eventTarget.addEventListener(type, callback, options);\n  }\n  /** Equivalent to `navigation.removeEventListener()`. */\n  removeEventListener(type, callback, options) {\n    this.eventTarget.removeEventListener(type, callback, options);\n  }\n  /** Equivalent to `navigation.dispatchEvent()` */\n  dispatchEvent(event) {\n    return this.eventTarget.dispatchEvent(event);\n  }\n  /** Cleans up resources. */\n  dispose() {\n    // Recreate eventTarget to release current listeners.\n    this.eventTarget = this.createEventTarget();\n    this.disposed = true;\n  }\n  /** Returns whether this fake is disposed. */\n  isDisposed() {\n    return this.disposed;\n  }\n  /**\n   * Implementation for all navigations and traversals.\n   * @returns true if the event was intercepted, otherwise false\n   */\n  userAgentNavigate(destination, result, options) {\n    // The first navigation should disallow any future calls to set the initial\n    // entry.\n    this.canSetInitialEntry = false;\n    if (this.navigateEvent) {\n      this.navigateEvent.cancel(new DOMException('Navigation was aborted', 'AbortError'));\n      this.navigateEvent = null;\n    }\n    return dispatchNavigateEvent({\n      navigationType: options.navigationType,\n      cancelable: options.cancelable,\n      canIntercept: options.canIntercept,\n      userInitiated: options.userInitiated,\n      hashChange: options.hashChange,\n      signal: result.signal,\n      destination,\n      info: options.info,\n      sameDocument: destination.sameDocument,\n      result\n    });\n  }\n  /**\n   * Implementation for a push or replace navigation.\n   * https://whatpr.org/html/10919/browsing-the-web.html#url-and-history-update-steps\n   * https://whatpr.org/html/10919/nav-history-apis.html#update-the-navigation-api-entries-for-a-same-document-navigation\n   * @internal\n   */\n  urlAndHistoryUpdateSteps(navigateEvent) {\n    this.updateNavigationEntriesForSameDocumentNavigation(navigateEvent);\n  }\n  /**\n   * Implementation for a traverse navigation.\n   *\n   * https://whatpr.org/html/10919/browsing-the-web.html#apply-the-traverse-history-step\n   * ...\n   * > Let updateDocument be an algorithm step which performs update document for history step application given targetEntry's document, targetEntry, changingNavigableContinuation's update-only, scriptHistoryLength, scriptHistoryIndex, navigationType, entriesForNavigationAPI, and previousEntry.\n   * > If targetEntry's document is equal to displayedDocument, then perform updateDocument.\n   * https://whatpr.org/html/10919/browsing-the-web.html#update-document-for-history-step-application\n   * which then goes to https://whatpr.org/html/10919/nav-history-apis.html#update-the-navigation-api-entries-for-a-same-document-navigation\n   * @internal\n   */\n  userAgentTraverse(navigateEvent) {\n    const oldUrl = this.currentEntry.url;\n    this.updateNavigationEntriesForSameDocumentNavigation(navigateEvent);\n    // Happens as part of \"updating the document\" steps https://whatpr.org/html/10919/browsing-the-web.html#updating-the-document\n    const popStateEvent = createPopStateEvent({\n      state: navigateEvent.destination.getHistoryState()\n    });\n    this._window.dispatchEvent(popStateEvent);\n    if (navigateEvent.hashChange) {\n      const hashchangeEvent = createHashChangeEvent(oldUrl, this.currentEntry.url);\n      this._window.dispatchEvent(hashchangeEvent);\n    }\n  }\n  /**\n   * https://whatpr.org/html/10919/nav-history-apis.html#update-the-navigation-api-entries-for-a-same-document-navigation\n   * @internal\n   */\n  updateNavigationEntriesForSameDocumentNavigation({\n    destination,\n    navigationType,\n    result\n  }) {\n    const oldCurrentNHE = this.currentEntry;\n    const disposedNHEs = [];\n    if (navigationType === 'traverse') {\n      this.currentEntryIndex = destination.index;\n      if (this.currentEntryIndex === -1) {\n        throw new Error('unexpected current entry index');\n      }\n    } else if (navigationType === 'push') {\n      this.currentEntryIndex++;\n      this.prospectiveEntryIndex = this.currentEntryIndex; // prospectiveEntryIndex isn't in the spec but is an implementation detail\n      disposedNHEs.push(...this.entriesArr.splice(this.currentEntryIndex));\n    } else if (navigationType === 'replace') {\n      disposedNHEs.push(oldCurrentNHE);\n    }\n    if (navigationType === 'push' || navigationType === 'replace') {\n      const index = this.currentEntryIndex;\n      const key = navigationType === 'push' ? String(this.nextKey++) : this.currentEntry.key;\n      const newNHE = new FakeNavigationHistoryEntry(this.eventTarget, destination.url, {\n        id: String(this.nextId++),\n        key,\n        index,\n        sameDocument: true,\n        state: destination.getState(),\n        historyState: destination.getHistoryState()\n      });\n      this.entriesArr[this.currentEntryIndex] = newNHE;\n    }\n    result.committedResolve(this.currentEntry);\n    const currentEntryChangeEvent = createFakeNavigationCurrentEntryChangeEvent({\n      from: oldCurrentNHE,\n      navigationType: navigationType\n    });\n    this.eventTarget.dispatchEvent(currentEntryChangeEvent);\n    for (const disposedNHE of disposedNHEs) {\n      disposedNHE.dispose();\n    }\n  }\n  /** Utility method for finding entries with the given `key`. */\n  findEntry(key) {\n    for (const entry of this.entriesArr) {\n      if (entry.key === key) return entry;\n    }\n    return undefined;\n  }\n  set onnavigate(\n  // tslint:disable-next-line:no-any\n  _handler) {\n    throw new Error('unimplemented');\n  }\n  // tslint:disable-next-line:no-any\n  get onnavigate() {\n    throw new Error('unimplemented');\n  }\n  set oncurrententrychange(_handler) {\n    throw new Error('unimplemented');\n  }\n  get oncurrententrychange() {\n    throw new Error('unimplemented');\n  }\n  set onnavigatesuccess(\n  // tslint:disable-next-line:no-any\n  _handler) {\n    throw new Error('unimplemented');\n  }\n  // tslint:disable-next-line:no-any\n  get onnavigatesuccess() {\n    throw new Error('unimplemented');\n  }\n  set onnavigateerror(\n  // tslint:disable-next-line:no-any\n  _handler) {\n    throw new Error('unimplemented');\n  }\n  // tslint:disable-next-line:no-any\n  get onnavigateerror() {\n    throw new Error('unimplemented');\n  }\n  _transition = null;\n  /** @internal */\n  set transition(t) {\n    this._transition = t;\n  }\n  get transition() {\n    return this._transition;\n  }\n  updateCurrentEntry(_options) {\n    throw new Error('unimplemented');\n  }\n  reload(_options) {\n    throw new Error('unimplemented');\n  }\n}\n/**\n * Fake equivalent of `NavigationHistoryEntry`.\n */\nclass FakeNavigationHistoryEntry {\n  eventTarget;\n  url;\n  sameDocument;\n  id;\n  key;\n  index;\n  state;\n  historyState;\n  // tslint:disable-next-line:no-any\n  ondispose = null;\n  constructor(eventTarget, url, {\n    id,\n    key,\n    index,\n    sameDocument,\n    state,\n    historyState\n  }) {\n    this.eventTarget = eventTarget;\n    this.url = url;\n    this.id = id;\n    this.key = key;\n    this.index = index;\n    this.sameDocument = sameDocument;\n    this.state = state;\n    this.historyState = historyState;\n  }\n  getState() {\n    // Budget copy.\n    return this.state ? JSON.parse(JSON.stringify(this.state)) : this.state;\n  }\n  getHistoryState() {\n    // Budget copy.\n    return this.historyState ? JSON.parse(JSON.stringify(this.historyState)) : this.historyState;\n  }\n  addEventListener(type, callback, options) {\n    this.eventTarget.addEventListener(type, callback, options);\n  }\n  removeEventListener(type, callback, options) {\n    this.eventTarget.removeEventListener(type, callback, options);\n  }\n  dispatchEvent(event) {\n    return this.eventTarget.dispatchEvent(event);\n  }\n  /** internal */\n  dispose() {\n    const disposeEvent = new Event('disposed');\n    this.dispatchEvent(disposeEvent);\n    // release current listeners\n    this.eventTarget = null;\n  }\n}\n/**\n * Create a fake equivalent of `NavigateEvent`. This is not a class because ES5\n * transpiled JavaScript cannot extend native Event.\n *\n * https://html.spec.whatwg.org/multipage/nav-history-apis.html#navigate-event-firing\n */\nfunction dispatchNavigateEvent({\n  cancelable,\n  canIntercept,\n  userInitiated,\n  hashChange,\n  navigationType,\n  signal,\n  destination,\n  info,\n  sameDocument,\n  result\n}) {\n  const {\n    navigation\n  } = result;\n  const event = new Event('navigate', {\n    bubbles: false,\n    cancelable\n  });\n  event.focusResetBehavior = null;\n  event.scrollBehavior = null;\n  event.interceptionState = 'none';\n  event.canIntercept = canIntercept;\n  event.userInitiated = userInitiated;\n  event.hashChange = hashChange;\n  event.navigationType = navigationType;\n  event.signal = signal;\n  event.destination = destination;\n  event.info = info;\n  event.downloadRequest = null;\n  event.formData = null;\n  event.result = result;\n  event.sameDocument = sameDocument;\n  let precommitHandlers = [];\n  let handlers = [];\n  // https://whatpr.org/html/10919/nav-history-apis.html#dom-navigateevent-intercept\n  event.intercept = function (options) {\n    if (!this.canIntercept) {\n      throw new DOMException(`Cannot intercept when canIntercept is 'false'`, 'SecurityError');\n    }\n    this.interceptionState = 'intercepted';\n    event.sameDocument = true;\n    const precommitHandler = options?.precommitHandler;\n    if (precommitHandler) {\n      if (!this.cancelable) {\n        throw new DOMException(`Cannot use precommitHandler when cancelable is 'false'`, 'InvalidStateError');\n      }\n      precommitHandlers.push(precommitHandler);\n    }\n    if (event.interceptionState !== 'none' && event.interceptionState !== 'intercepted') {\n      throw new Error('Event interceptionState should be \"none\" or \"intercepted\"');\n    }\n    event.interceptionState = 'intercepted';\n    const handler = options?.handler;\n    if (handler) {\n      handlers.push(handler);\n    }\n    // override old options with new ones. UA _may_ report a console warning if new options differ from previous\n    event.focusResetBehavior = options?.focusReset ?? event.focusResetBehavior;\n    event.scrollBehavior = options?.scroll ?? event.scrollBehavior;\n  };\n  // https://whatpr.org/html/10919/nav-history-apis.html#dom-navigateevent-scroll\n  event.scroll = function () {\n    if (event.interceptionState !== 'committed') {\n      throw new DOMException(`Failed to execute 'scroll' on 'NavigateEvent': scroll() must be ` + `called after commit() and interception options must specify manual scroll.`, 'InvalidStateError');\n    }\n    processScrollBehavior(event);\n  };\n  // https://whatpr.org/html/10919/nav-history-apis.html#dom-navigationprecommitcontroller-redirect\n  function redirect(url) {\n    if (event.interceptionState === 'none') {\n      throw new Error('cannot redirect when event is not intercepted');\n    }\n    if (event.interceptionState !== 'intercepted') {\n      throw new DOMException(`cannot redirect when event is not in 'intercepted' state`, 'InvalidStateError');\n    }\n    if (event.navigationType !== 'push' && event.navigationType !== 'replace') {\n      throw new DOMException(`cannot redirect when navigationType is not 'push' or 'replace`, 'InvalidStateError');\n    }\n    const toUrl = new URL(url, navigation.currentEntry.url);\n    event.destination.url = toUrl.href;\n  }\n  // https://whatpr.org/html/10919/nav-history-apis.html#inner-navigate-event-firing-algorithm\n  // \"Let commit be the following steps:\"\n  function commit() {\n    if (result.signal.aborted) {\n      return;\n    }\n    if (event.interceptionState !== 'none') {\n      event.interceptionState = 'committed';\n      if (!navigation.currentEntry) {\n        throw new Error('from history entry should not be null');\n      }\n      navigation.transition = new InternalNavigationTransition(navigation.currentEntry, navigationType);\n      switch (event.navigationType) {\n        case 'push':\n        case 'replace':\n          {\n            navigation.urlAndHistoryUpdateSteps(event);\n            break;\n          }\n        case 'reload':\n          {\n            navigation.updateNavigationEntriesForSameDocumentNavigation(event);\n            break;\n          }\n        case 'traverse':\n          {\n            navigation.userAgentTraverse(event);\n            break;\n          }\n      }\n    }\n    const promisesList = handlers.map(handler => handler());\n    if (promisesList.length === 0) {\n      promisesList.push(Promise.resolve());\n    }\n    Promise.all(promisesList).then(() => {\n      // Follows steps outlined under \"Wait for all of promisesList, with the following success steps:\"\n      // in the spec https://html.spec.whatwg.org/multipage/nav-history-apis.html#navigate-event-firing.\n      if (result.signal.aborted) {\n        return;\n      }\n      if (event !== navigation.navigateEvent) {\n        throw new Error(\"Navigation's ongoing event not equal to resolved event\");\n      }\n      navigation.navigateEvent = null;\n      finishNavigationEvent(event, true);\n      const navigatesuccessEvent = new Event('navigatesuccess', {\n        bubbles: false,\n        cancelable\n      });\n      navigation.eventTarget.dispatchEvent(navigatesuccessEvent);\n      result.finishedResolve();\n      if (navigation.transition !== null) {\n        navigation.transition.finishedResolve();\n      }\n      navigation.transition = null;\n    }).catch(reason => event.cancel(reason));\n  }\n  // Internal only.\n  // https://whatpr.org/html/10919/nav-history-apis.html#inner-navigate-event-firing-algorithm\n  // \"Let cancel be the following steps given reason\"\n  event.cancel = function (reason) {\n    if (result.signal.aborted) {\n      return;\n    }\n    if (event !== navigation.navigateEvent) {\n      throw new Error(\"Navigation's ongoing event not equal to resolved event\");\n    }\n    navigation.navigateEvent = null;\n    if (event.interceptionState !== 'intercepted') {\n      finishNavigationEvent(event, false);\n    }\n    const navigateerrorEvent = new Event('navigateerror', {\n      bubbles: false,\n      cancelable\n    });\n    navigation.eventTarget.dispatchEvent(navigateerrorEvent);\n    result.finishedReject(reason);\n    if (navigation.transition !== null) {\n      navigation.transition.finishedReject(reason);\n    }\n    navigation.transition = null;\n  };\n  function dispatch() {\n    navigation.navigateEvent = event;\n    navigation.eventTarget.dispatchEvent(event);\n    if (precommitHandlers.length === 0) {\n      commit();\n    } else {\n      const precommitController = {\n        redirect\n      };\n      const precommitPromisesList = precommitHandlers.map(handler => handler(precommitController));\n      Promise.all(precommitPromisesList).then(() => commit()).catch(reason => event.cancel(reason));\n    }\n  }\n  dispatch();\n  return event.interceptionState !== 'none';\n}\n/** https://whatpr.org/html/10919/nav-history-apis.html#navigateevent-finish */\nfunction finishNavigationEvent(event, didFulfill) {\n  if (event.interceptionState === 'finished') {\n    throw new Error('Attempting to finish navigation event that was already finished');\n  }\n  if (event.interceptionState === 'intercepted') {\n    if (didFulfill === true) {\n      throw new Error('didFulfill should be false');\n    }\n    // assert precommit handlers is not empty\n    event.interceptionState = 'finished';\n    return;\n  }\n  if (event.interceptionState === 'none') {\n    return;\n  }\n  potentiallyResetFocus(event);\n  if (didFulfill) {\n    potentiallyResetScroll(event);\n  }\n  event.interceptionState = 'finished';\n}\n/** https://whatpr.org/html/10919/nav-history-apis.html#potentially-reset-the-focus */\nfunction potentiallyResetFocus(event) {\n  if (event.interceptionState !== 'committed' && event.interceptionState !== 'scrolled') {\n    throw new Error('cannot reset focus if navigation event is not committed or scrolled');\n  }\n  // TODO(atscott): The rest of the steps\n}\nfunction potentiallyResetScroll(event) {\n  if (event.interceptionState !== 'committed' && event.interceptionState !== 'scrolled') {\n    throw new Error('cannot reset scroll if navigation event is not committed or scrolled');\n  }\n  if (event.interceptionState === 'scrolled' || event.scrollBehavior === 'manual') {\n    return;\n  }\n  processScrollBehavior(event);\n}\n/* https://whatpr.org/html/10919/nav-history-apis.html#process-scroll-behavior */\nfunction processScrollBehavior(event) {\n  if (event.interceptionState !== 'committed') {\n    throw new Error('invalid event interception state when processing scroll behavior');\n  }\n  event.interceptionState = 'scrolled';\n  // TODO(atscott): the rest of the steps\n}\n/**\n * Create a fake equivalent of `NavigationCurrentEntryChange`. This does not use\n * a class because ES5 transpiled JavaScript cannot extend native Event.\n */\nfunction createFakeNavigationCurrentEntryChangeEvent({\n  from,\n  navigationType\n}) {\n  const event = new Event('currententrychange', {\n    bubbles: false,\n    cancelable: false\n  });\n  event.from = from;\n  event.navigationType = navigationType;\n  return event;\n}\n/**\n * Create a fake equivalent of `PopStateEvent`. This does not use a class\n * because ES5 transpiled JavaScript cannot extend native Event.\n */\nfunction createPopStateEvent({\n  state\n}) {\n  const event = new Event('popstate', {\n    bubbles: false,\n    cancelable: false\n  });\n  event.state = state;\n  return event;\n}\nfunction createHashChangeEvent(newURL, oldURL) {\n  const event = new Event('hashchange', {\n    bubbles: false,\n    cancelable: false\n  });\n  event.newURL = newURL;\n  event.oldURL = oldURL;\n  return event;\n}\n/**\n * Fake equivalent of `NavigationDestination`.\n */\nclass FakeNavigationDestination {\n  url;\n  sameDocument;\n  key;\n  id;\n  index;\n  state;\n  historyState;\n  constructor({\n    url,\n    sameDocument,\n    historyState,\n    state,\n    key = null,\n    id = null,\n    index = -1\n  }) {\n    this.url = url;\n    this.sameDocument = sameDocument;\n    this.state = state;\n    this.historyState = historyState;\n    this.key = key;\n    this.id = id;\n    this.index = index;\n  }\n  getState() {\n    return this.state;\n  }\n  getHistoryState() {\n    return this.historyState;\n  }\n}\n/** Utility function to determine whether two UrlLike have the same hash. */\nfunction isHashChange(from, to) {\n  return to.hash !== from.hash && to.hostname === from.hostname && to.pathname === from.pathname && to.search === from.search;\n}\nclass InternalNavigationTransition {\n  from;\n  navigationType;\n  finished;\n  finishedResolve;\n  finishedReject;\n  constructor(from, navigationType) {\n    this.from = from;\n    this.navigationType = navigationType;\n    this.finished = new Promise((resolve, reject) => {\n      this.finishedReject = reject;\n      this.finishedResolve = resolve;\n    });\n    // All rejections are handled.\n    this.finished.catch(() => {});\n  }\n}\n/**\n * Internal utility class for representing the result of a navigation.\n * Generally equivalent to the \"apiMethodTracker\" in the spec.\n */\nclass InternalNavigationResult {\n  navigation;\n  committedTo = null;\n  committedResolve;\n  committedReject;\n  finishedResolve;\n  finishedReject;\n  committed;\n  finished;\n  get signal() {\n    return this.abortController.signal;\n  }\n  abortController = new AbortController();\n  constructor(navigation) {\n    var _this7 = this;\n    this.navigation = navigation;\n    this.committed = new Promise((resolve, reject) => {\n      this.committedResolve = entry => {\n        this.committedTo = entry;\n        resolve(entry);\n      };\n      this.committedReject = reject;\n    });\n    this.finished = new Promise(/*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (resolve, reject) {\n        _this7.finishedResolve = () => {\n          if (_this7.committedTo === null) {\n            throw new Error('NavigateEvent should have been committed before resolving finished promise.');\n          }\n          resolve(_this7.committedTo);\n        };\n        _this7.finishedReject = reason => {\n          reject(reason);\n          _this7.abortController.abort(reason);\n        };\n      });\n      return function (_x, _x2) {\n        return _ref.apply(this, arguments);\n      };\n    }());\n    // All rejections are handled.\n    this.committed.catch(() => {});\n    this.finished.catch(() => {});\n  }\n}\nclass Log {\n  logItems;\n  constructor() {\n    this.logItems = [];\n  }\n  add(value) {\n    this.logItems.push(value);\n  }\n  fn(value) {\n    return () => {\n      this.logItems.push(value);\n    };\n  }\n  clear() {\n    this.logItems = [];\n  }\n  result() {\n    return this.logItems.join('; ');\n  }\n  static ɵfac = function Log_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Log)();\n  };\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: Log,\n    factory: Log.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Log, [{\n    type: Injectable\n  }], () => [], null);\n})();\nexport { ComponentFixture, ComponentFixtureAutoDetect, ComponentFixtureNoNgZone, DeferBlockFixture, InjectSetupWrapper, TestBed, TestComponentRenderer, __core_private_testing_placeholder__, discardPeriodicTasks, fakeAsync, flush, flushMicrotasks, getTestBed, inject, resetFakeAsyncZone, tick, waitForAsync, withModule, FakeNavigation as ɵFakeNavigation, Log as ɵLog, MetadataOverrider as ɵMetadataOverrider };", "map": {"version": 3, "names": ["i0", "inject", "inject$1", "NgZone", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Injectable", "ɵDeferBlockState", "_DeferBlockState", "ɵtriggerResourceLoading", "_triggerResourceLoading", "ɵrenderDeferBlockState", "_renderDeferBlockState", "ɵCONTAINER_HEADER_OFFSET", "_CONTAINER_HEADER_OFFSET", "ɵgetDeferBlocks", "_getDeferBlocks", "InjectionToken", "ɵDeferBlockBehavior", "_DeferBlockBehavior", "ɵNoopNgZone", "_NoopNgZone", "ApplicationRef", "ɵPendingTasksInternal", "_PendingTasksInternal", "ɵZONELESS_ENABLED", "_ZONELESS_ENABLED", "ɵChangeDetectionScheduler", "_ChangeDetectionScheduler", "ɵEffectScheduler", "_EffectScheduler", "ɵMicrotaskEffectScheduler", "_MicrotaskEffectScheduler", "getDebugNode", "RendererFactory2", "ɵstringify", "_stringify", "<PERSON><PERSON>", "Directive", "Component", "NgModule", "ɵReflectionCapabilities", "_ReflectionCapabilities", "ɵUSE_RUNTIME_DEPS_TRACKER_FOR_JIT", "_USE_RUNTIME_DEPS_TRACKER_FOR_JIT", "ɵdepsTracker", "_depsTracker", "ɵgetInjectableDef", "_getInjectableDef", "resolveForwardRef", "ɵisComponentDefPendingResolution", "_isComponentDefPendingResolution", "ɵgetAsyncClassMetadataFn", "_getAsyncClassMetadataFn", "ɵresolveComponentResources", "_resolveComponentResources", "ɵRender3NgModuleRef", "_Render3NgModuleRef", "ApplicationInitStatus", "LOCALE_ID", "ɵDEFAULT_LOCALE_ID", "_DEFAULT_LOCALE_ID", "ɵsetLocaleId", "_setLocaleId", "ɵRender3ComponentFactory", "_Render3ComponentFactory", "ɵNG_COMP_DEF", "_NG_COMP_DEF", "ɵcompileComponent", "_compileComponent", "ɵNG_DIR_DEF", "_NG_DIR_DEF", "ɵcompileDirective", "_compileDirective", "ɵNG_PIPE_DEF", "_NG_PIPE_DEF", "ɵcompilePipe", "_compilePipe", "ɵNG_MOD_DEF", "_NG_MOD_DEF", "ɵpatchComponentDefWithScope", "_patchComponentDefWithScope", "ɵNG_INJ_DEF", "_NG_INJ_DEF", "ɵcompileNgModuleDefs", "_compileNgModuleDefs", "ɵclearResolutionOfComponentResourcesQueue", "_clearResolutionOfComponentResourcesQueue", "ɵrestoreComponentResolutionQueue", "_restoreComponentResolutionQueue", "ɵinternalProvideZoneChangeDetection", "_internalProvideZoneChangeDetection", "ɵChangeDetectionSchedulerImpl", "_ChangeDetectionSchedulerImpl", "Compiler", "ɵDEFER_BLOCK_CONFIG", "_DEFER_BLOCK_CONFIG", "ɵINTERNAL_APPLICATION_ERROR_HANDLER", "_INTERNAL_APPLICATION_ERROR_HANDLER", "COMPILER_OPTIONS", "Injector", "ɵtransitiveScopesFor", "_transitiveScopesFor", "ɵgenerateStandaloneInDeclarationsError", "_generateStandaloneInDeclarationsError", "ɵNgModuleFactory", "_NgModuleFactory", "ModuleWithComponentFactories", "ɵisEnvironmentProviders", "_isEnvironmentProviders", "ɵconvertToBitFlags", "_convertToBitFlags", "InjectFlags", "ɵsetAllowDuplicateNgModuleIdsForTest", "_setAllowDuplicateNgModuleIdsForTest", "ɵresetCompiledComponents", "_resetCompiledComponents", "ɵsetUnknownElementStrictMode", "_setUnknownElementStrictMode", "ɵsetUnknownPropertyStrictMode", "_setUnknownPropertyStrictMode", "ɵgetUnknownElementStrictMode", "_getUnknownElementStrictMode", "ɵgetUnknownPropertyStrictMode", "_getUnknownPropertyStrictMode", "runInInjectionContext", "EnvironmentInjector", "ɵflushModuleScopingQueueAsMuchAsPossible", "_flushModuleScopingQueueAsMuchAsPossible", "DeferBlockBehavior", "DeferBlockState", "Subscription", "Resource<PERSON><PERSON>der", "waitForAsync", "fn", "_Zone", "Zone", "Promise", "reject", "asyncTest", "__symbol__", "RETHROW_APPLICATION_ERRORS_DEFAULT", "TestBedApplicationErrorHandler", "zone", "userErrorHandler", "whenStableRejectFunctions", "Set", "handleError", "e", "runOutsideAngular", "userError", "size", "values", "clear", "ɵfac", "TestBedApplicationErrorHandler_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "DeferBlockFixture", "block", "componentFixture", "constructor", "render", "state", "_this", "_asyncToGenerator", "hasStateTemplate", "stateAsString", "getDeferBlockStateNameFromEnum", "Error", "toLowerCase", "Complete", "tDetails", "lView", "tNode", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>uling", "lContainer", "detectChanges", "getDeferBlocks", "deferBlocks", "deferBlockFixtures", "length", "push", "resolve", "Placeholder", "placeholderTmplIndex", "Loading", "loadingTmplIndex", "errorTmplIndex", "TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT", "THROW_ON_UNKNOWN_ELEMENTS_DEFAULT", "THROW_ON_UNKNOWN_PROPERTIES_DEFAULT", "DEFER_BLOCK_DEFAULT_BEHAVIOR", "Playthrough", "TestComponent<PERSON><PERSON><PERSON>", "insertRootElement", "rootElementId", "removeAllRootElements", "ComponentFixtureAutoDetect", "ComponentFixtureNoNgZone", "ComponentFixture", "componentRef", "debugElement", "componentInstance", "nativeElement", "elementRef", "changeDetectorRef", "_renderer", "_isDestroyed", "_noZoneOptionIsSet", "optional", "_ngZone", "_appRef", "_testAppRef", "pendingTasks", "appError<PERSON><PERSON><PERSON>", "zonelessEnabled", "scheduler", "rootEffectScheduler", "microtaskEffectScheduler", "autoDetectDefault", "autoDetect", "subscriptions", "ngZone", "location", "instance", "externalTestViews", "add", "<PERSON><PERSON><PERSON><PERSON>", "notify", "onDestroy", "delete", "onError", "subscribe", "next", "error", "checkNoChanges", "flush", "originalCheckNoChanges", "tick", "run", "autoDetectChanges", "isStable", "hasPendingTasks", "value", "whenStable", "then", "_get<PERSON><PERSON><PERSON>", "undefined", "injector", "get", "whenRenderingDone", "renderer", "destroy", "unsubscribe", "fakeAsyncTestModule", "fakeAsyncTestModuleNotLoadedErrorMessage", "resetFakeAsyncZone", "resetFakeAsyncZoneIfExists", "isLoaded", "fakeAsync", "options", "millis", "tickOptions", "processNewMacroTasksSynchronously", "maxTurns", "discardPeriodicTasks", "flushMicrotasks", "_nextReferenceId", "MetadataOverrider", "_references", "Map", "overrideMetadata", "metadataClass", "oldMetadata", "override", "props", "_valueProps", "for<PERSON>ach", "prop", "set", "remove", "setMetadata", "removeMetadata", "addMetadata", "metadata", "references", "removeObjects", "removeValue", "Array", "isArray", "_propH<PERSON><PERSON><PERSON>", "propValue", "filter", "has", "addValue", "concat", "propName", "nextObjectId", "objectIds", "replacer", "key", "_serializeReference", "JSON", "stringify", "ref", "id", "obj", "Object", "keys", "startsWith", "proto", "getPrototypeOf", "protoProp", "desc", "getOwnPropertyDescriptor", "reflection", "OverrideResolver", "overrides", "resolved", "addOverride", "setOverrides", "getAnnotation", "annotations", "i", "annotation", "isKnownType", "overrider", "DirectiveResolver", "ComponentResolver", "PipeResolver", "NgModuleResolver", "TestingModuleOverride", "isTestingModuleOverride", "DECLARATION", "OVERRIDE_TEMPLATE", "assertNoStandaloneComponents", "types", "resolver", "component", "standalone", "TestBedCompiler", "platform", "additionalModuleTypes", "originalComponentResolutionQueue", "declarations", "imports", "providers", "schemas", "pendingComponents", "pendingDirectives", "pending<PERSON><PERSON>es", "componentsWithAsyncMetadata", "seenComponents", "seenDirectives", "overriddenModules", "existingComponentStyles", "resolvers", "initResolvers", "componentToModuleScope", "initialNgDefs", "defCleanupOps", "_injector", "compilerProviders", "providerOverrides", "rootProviderOverrides", "providerOverridesByModule", "providerOverridesByToken", "scopesWithOverriddenProviders", "testModuleType", "testModuleRef", "deferBlock<PERSON><PERSON><PERSON>or", "rethrowApplicationTickErrors", "DynamicTestModule", "setCompilerProviders", "configureTestingModule", "moduleDef", "queueTypeArray", "queueTypesFromModulesArray", "rethrowApplicationErrors", "overrideModule", "ngModule", "clearScopeCacheFor", "module", "invalidTypeError", "name", "recompileNgModule", "overrideComponent", "verifyNoStandaloneFlagOverrides", "maybeRegisterComponentWithAsyncMetadata", "overrideDirective", "directive", "overridePipe", "pipe", "hasOwnProperty", "override<PERSON><PERSON><PERSON>", "provider", "providerDef", "useFactory", "provide", "deps", "multi", "useValue", "injectableDef", "providedIn", "overridesBucket", "existingOverrides", "overrideTemplateUsingTestingModule", "template", "def", "hasStyleUrls", "styleUrl", "styleUrls", "overrideStyleUrls", "styles", "resolvePendingComponentsWithAsyncMetadata", "_this2", "promises", "asyncMetadataFn", "resolvedDeps", "all", "flatResolvedDeps", "flat", "applyProviderOverridesInScope", "compileComponents", "_this3", "clearComponentResolutionQueue", "needsAsyncResources", "compileTypesSync", "resourceLoader", "url", "finalize", "compileTestModule", "applyTransitiveScopes", "applyProviderOverrides", "patchComponentsWithExistingStyles", "parentInjector", "runInitializers", "localeId", "_compileNgModuleSync", "moduleType", "_compileNgModuleAsync", "_this4", "_getModuleResolver", "_getComponentFactories", "maybeUnwrapFn", "ɵmod", "reduce", "factories", "declaration", "componentDef", "ɵcmp", "maybeStoreNgDef", "testingModuleDef", "affectedModules", "collectModulesAffectedByOverrides", "storeFieldOfDefOnType", "transitiveCompileScopes", "moduleToScope", "getScopeOfModule", "isTestingModule", "realType", "componentType", "moduleScope", "getComponentDef", "maybeApplyOverrides", "field", "hasProviderOverrides", "patchDefWithProviderOverrides", "hasScope", "isStandaloneComponent", "isNgModule", "injectorDef", "dependencies", "dependency", "getOverriddenProviders", "importedModule", "flatten", "isModuleWithProviders", "object", "fieldName", "originalValue", "arr", "queueType", "processedDefs", "queueTypesFromModulesArrayRecur", "hasNgModuleDef", "exports", "seenModules", "calcAffectedModulesRecur", "path", "item", "currentDefs", "currentDef", "defField", "restoreComponentResolutionQueue", "restoreOriginalState", "forEachRight", "op", "defs", "descriptor", "defineProperty", "RootScopeModule", "useExisting", "R3TestCompiler", "behavior", "handler", "compilerOptions", "opts", "create", "parent", "getSingleProviderOverrides", "getProviderToken", "getProviderOverrides", "flattenProviders", "flattenedProviders", "overriddenProviders", "final", "seenOverriddenProviders", "unshift", "providersResolver", "processProvidersFn", "ngDef", "maybeFn", "Function", "out", "identityFn", "mapFn", "ɵproviders", "getProviderField", "idx", "expectedType", "testBed", "compileModuleSync", "compileModuleAsync", "_this5", "compileModuleAndAllComponentsSync", "ngModuleFactory", "componentFactories", "compileModuleAndAllComponentsAsync", "_this6", "clearCache", "clearCacheFor", "getModuleId", "meta", "_nextRootElementId", "getTestBed", "TestBedImpl", "INSTANCE", "_INSTANCE", "_environmentTeardownOptions", "_environmentErrorOnUnknownElementsOption", "_environmentErrorOnUnknownPropertiesOption", "_instanceTeardownOptions", "_instanceDeferBlockBehavior", "_instanceErrorOnUnknownElementsOption", "_instanceErrorOnUnknownPropertiesOption", "_previousErrorOnUnknownElementsOption", "_previousErrorOnUnknownPropertiesOption", "initTestEnvironment", "resetTestEnvironment", "configureCompiler", "config", "overrideTemplate", "notFoundValue", "flags", "THROW_IF_NOT_FOUND", "<PERSON><PERSON><PERSON>", "createComponent", "resetTestingModule", "execute", "tokens", "context", "flushEffects", "_compiler", "_testModuleRef", "_activeFixtures", "globalCompilationChecked", "teardown", "errorOnUnknownElements", "errorOnUnknownProperties", "checkGlobalCompilationFinished", "compiler", "destroyActiveFixtures", "shouldTearDownTestingModule", "tearDownTestingModule", "useJit", "assertNotInstantiated", "shouldThrowErrorOnUnknownElements", "shouldThrowErrorOnUnknownProperties", "TestBed", "UNDEFINED", "result", "params", "map", "t", "apply", "templateUrl", "testComponent<PERSON><PERSON><PERSON>", "rootElId", "componentFactory", "initComponent", "NULL", "noNgZone", "fixture", "methodName", "methodDescription", "errorCount", "console", "stacktrace", "shouldRethrowTeardownErrors", "instanceOptions", "environmentOptions", "rethrowErrors", "destroyAfterEach", "getDeferBlockBehavior", "<PERSON><PERSON><PERSON><PERSON>", "InjectSetupWrapper", "_moduleDef", "_addModule", "self", "call", "withModule", "globalThis", "beforeEach", "getCleanupHook", "after<PERSON>ach", "expectedTeardownValue", "__core_private_testing_placeholder__", "FakeNavigation", "entriesArr", "currentEntryIndex", "navigateEvent", "traversalQueue", "nextTraversal", "prospectiveEntryIndex", "synchronousTraversals", "canSetInitialEntry", "eventTarget", "nextId", "<PERSON><PERSON><PERSON>", "disposed", "currentEntry", "canGoBack", "canGoForward", "createEventTarget", "_window", "window", "doc", "startURL", "createElement", "EventTarget", "document", "defaultView", "setInitialEntryForTesting", "historyState", "currentInitialEntry", "FakeNavigationHistoryEntry", "URL", "toString", "index", "String", "sameDocument", "canSetInitialEntryForTesting", "setSynchronousTraversalsForTesting", "entries", "slice", "navigate", "fromUrl", "toUrl", "navigationType", "history", "hashChange", "isHashChange", "destination", "FakeNavigationDestination", "InternalNavigationResult", "intercepted", "userAgentNavigate", "cancelable", "canIntercept", "userInitiated", "info", "updateNavigationEntriesForSameDocumentNavigation", "committed", "finished", "pushState", "data", "title", "pushOrReplaceState", "replaceState", "_title", "traverseTo", "entry", "findEntry", "domException", "DOMException", "catch", "existingResult", "getState", "getHistoryState", "runTraversal", "userAgentTraverse", "back", "forward", "go", "direction", "targetIndex", "traversal", "setTimeout", "addEventListener", "callback", "removeEventListener", "dispatchEvent", "event", "dispose", "isDisposed", "cancel", "dispatchNavigateEvent", "signal", "urlAndHistoryUpdateSteps", "oldUrl", "popStateEvent", "createPopStateEvent", "hashchangeEvent", "createHashChangeEvent", "oldCurrentNHE", "disposedNHEs", "splice", "newNHE", "committedResolve", "currentEntryChangeEvent", "createFakeNavigationCurrentEntryChangeEvent", "from", "disposedNHE", "onnavigate", "_handler", "oncurrententrychange", "onnavigatesuccess", "onnavigateerror", "_transition", "transition", "updateCurrentEntry", "_options", "reload", "ondispose", "parse", "disposeEvent", "Event", "navigation", "bubbles", "focusResetBehavior", "scroll<PERSON>eh<PERSON>or", "interceptionState", "downloadRequest", "formData", "precommitHandlers", "handlers", "intercept", "precommit<PERSON><PERSON><PERSON>", "focusReset", "scroll", "processScrollBehavior", "redirect", "href", "commit", "aborted", "InternalNavigationTransition", "promisesList", "finishNavigationEvent", "navigatesuccessEvent", "finishedResolve", "reason", "navigateerrorEvent", "finishedReject", "dispatch", "precommitController", "precommitPromisesList", "did<PERSON><PERSON><PERSON>", "potentiallyResetFocus", "potentiallyResetScroll", "newURL", "oldURL", "to", "hash", "hostname", "pathname", "search", "committedTo", "committedReject", "abortController", "AbortController", "_this7", "_ref", "abort", "_x", "_x2", "arguments", "Log", "logItems", "join", "Log_Factory", "ɵFakeNavigation", "ɵLog", "ɵMetadataOverrider"], "sources": ["C:/projetos/scope-ai/frontend/node_modules/@angular/core/fesm2022/testing.mjs"], "sourcesContent": ["/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { inject as inject$1, <PERSON><PERSON><PERSON>, <PERSON><PERSON>r<PERSON><PERSON><PERSON>, Injectable, ɵDeferBlockState as _DeferBlockState, ɵtriggerResourceLoading as _triggerResourceLoading, ɵrenderDeferBlockState as _renderDeferBlockState, ɵCONTAINER_HEADER_OFFSET as _CONTAINER_HEADER_OFFSET, ɵgetDeferBlocks as _getDeferBlocks, InjectionToken, ɵDeferBlockBehavior as _DeferBlockBehavior, ɵNoopNgZone as _NoopNgZone, ApplicationRef, ɵPendingTasksInternal as _PendingTasksInternal, ɵZONELESS_ENABLED as _ZONELESS_ENABLED, ɵChangeDetectionScheduler as _ChangeDetectionScheduler, ɵEffectScheduler as _EffectScheduler, ɵMicrotaskEffectScheduler as _MicrotaskEffectScheduler, getDebugNode, RendererFactory2, ɵstringify as _stringify, Pipe, Directive, Component, NgModule, ɵReflectionCapabilities as _ReflectionCapabilities, ɵUSE_RUNTIME_DEPS_TRACKER_FOR_JIT as _USE_RUNTIME_DEPS_TRACKER_FOR_JIT, ɵdepsTracker as _depsTracker, ɵgetInjectableDef as _getInjectableDef, resolveForwardRef, ɵisComponentDefPendingResolution as _isComponentDefPendingResolution, ɵgetAsyncClassMetadataFn as _getAsyncClassMetadataFn, ɵresolveComponentResources as _resolveComponentResources, ɵRender3NgModuleRef as _Render3NgModuleRef, ApplicationInitStatus, LOCALE_ID, ɵDEFAULT_LOCALE_ID as _DEFAULT_LOCALE_ID, ɵsetLocaleId as _setLocaleId, ɵRender3ComponentFactory as _Render3ComponentFactory, ɵNG_COMP_DEF as _NG_COMP_DEF, ɵcompileComponent as _compileComponent, ɵNG_DIR_DEF as _NG_DIR_DEF, ɵcompileDirective as _compileDirective, ɵNG_PIPE_DEF as _NG_PIPE_DEF, ɵcompilePipe as _compilePipe, ɵNG_MOD_DEF as _NG_MOD_DEF, ɵpatchComponentDefWithScope as _patchComponentDefWithScope, ɵNG_INJ_DEF as _NG_INJ_DEF, ɵcompileNgModuleDefs as _compileNgModuleDefs, ɵclearResolutionOfComponentResourcesQueue as _clearResolutionOfComponentResourcesQueue, ɵrestoreComponentResolutionQueue as _restoreComponentResolutionQueue, ɵinternalProvideZoneChangeDetection as _internalProvideZoneChangeDetection, ɵChangeDetectionSchedulerImpl as _ChangeDetectionSchedulerImpl, Compiler, ɵDEFER_BLOCK_CONFIG as _DEFER_BLOCK_CONFIG, ɵINTERNAL_APPLICATION_ERROR_HANDLER as _INTERNAL_APPLICATION_ERROR_HANDLER, COMPILER_OPTIONS, Injector, ɵtransitiveScopesFor as _transitiveScopesFor, ɵgenerateStandaloneInDeclarationsError as _generateStandaloneInDeclarationsError, ɵNgModuleFactory as _NgModuleFactory, ModuleWithComponentFactories, ɵisEnvironmentProviders as _isEnvironmentProviders, ɵconvertToBitFlags as _convertToBitFlags, InjectFlags, ɵsetAllowDuplicateNgModuleIdsForTest as _setAllowDuplicateNgModuleIdsForTest, ɵresetCompiledComponents as _resetCompiledComponents, ɵsetUnknownElementStrictMode as _setUnknownElementStrictMode, ɵsetUnknownPropertyStrictMode as _setUnknownPropertyStrictMode, ɵgetUnknownElementStrictMode as _getUnknownElementStrictMode, ɵgetUnknownPropertyStrictMode as _getUnknownPropertyStrictMode, runInInjectionContext, EnvironmentInjector, ɵflushModuleScopingQueueAsMuchAsPossible as _flushModuleScopingQueueAsMuchAsPossible } from '@angular/core';\nexport { ɵDeferBlockBehavior as DeferBlockBehavior, ɵDeferBlockState as DeferBlockState } from '@angular/core';\nimport { Subscription } from 'rxjs';\nimport { ResourceLoader } from '@angular/compiler';\n\n/**\n * Wraps a test function in an asynchronous test zone. The test will automatically\n * complete when all asynchronous calls within this zone are done. Can be used\n * to wrap an {@link inject} call.\n *\n * Example:\n *\n * ```ts\n * it('...', waitForAsync(inject([AClass], (object) => {\n *   object.doSomething.then(() => {\n *     expect(...);\n *   })\n * })));\n * ```\n *\n * @publicApi\n */\nfunction waitForAsync(fn) {\n    const _Zone = typeof Zone !== 'undefined' ? Zone : null;\n    if (!_Zone) {\n        return function () {\n            return Promise.reject('Zone is needed for the waitForAsync() test helper but could not be found. ' +\n                'Please make sure that your environment includes zone.js');\n        };\n    }\n    const asyncTest = _Zone && _Zone[_Zone.__symbol__('asyncTest')];\n    if (typeof asyncTest === 'function') {\n        return asyncTest(fn);\n    }\n    return function () {\n        return Promise.reject('zone-testing.js is needed for the async() test helper but could not be found. ' +\n            'Please make sure that your environment includes zone.js/testing');\n    };\n}\n\nconst RETHROW_APPLICATION_ERRORS_DEFAULT = true;\nclass TestBedApplicationErrorHandler {\n    zone = inject$1(NgZone);\n    userErrorHandler = inject$1(ErrorHandler);\n    whenStableRejectFunctions = new Set();\n    handleError(e) {\n        try {\n            this.zone.runOutsideAngular(() => this.userErrorHandler.handleError(e));\n        }\n        catch (userError) {\n            e = userError;\n        }\n        // Instead of throwing the error when there are outstanding `fixture.whenStable` promises,\n        // reject those promises with the error. This allows developers to write\n        // expectAsync(fix.whenStable()).toBeRejected();\n        if (this.whenStableRejectFunctions.size > 0) {\n            for (const fn of this.whenStableRejectFunctions.values()) {\n                fn(e);\n            }\n            this.whenStableRejectFunctions.clear();\n        }\n        else {\n            throw e;\n        }\n    }\n    static ɵfac = function TestBedApplicationErrorHandler_Factory(__ngFactoryType__) { return new (__ngFactoryType__ || TestBedApplicationErrorHandler)(); };\n    static ɵprov = /*@__PURE__*/ i0.ɵɵdefineInjectable({ token: TestBedApplicationErrorHandler, factory: TestBedApplicationErrorHandler.ɵfac });\n}\n(() => { (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TestBedApplicationErrorHandler, [{\n        type: Injectable\n    }], null, null); })();\n\n/**\n * Represents an individual defer block for testing purposes.\n *\n * @publicApi\n */\nclass DeferBlockFixture {\n    block;\n    componentFixture;\n    /** @docs-private */\n    constructor(block, componentFixture) {\n        this.block = block;\n        this.componentFixture = componentFixture;\n    }\n    /**\n     * Renders the specified state of the defer fixture.\n     * @param state the defer state to render\n     */\n    async render(state) {\n        if (!hasStateTemplate(state, this.block)) {\n            const stateAsString = getDeferBlockStateNameFromEnum(state);\n            throw new Error(`Tried to render this defer block in the \\`${stateAsString}\\` state, ` +\n                `but there was no @${stateAsString.toLowerCase()} block defined in a template.`);\n        }\n        if (state === _DeferBlockState.Complete) {\n            await _triggerResourceLoading(this.block.tDetails, this.block.lView, this.block.tNode);\n        }\n        // If the `render` method is used explicitly - skip timer-based scheduling for\n        // `@placeholder` and `@loading` blocks and render them immediately.\n        const skipTimerScheduling = true;\n        _renderDeferBlockState(state, this.block.tNode, this.block.lContainer, skipTimerScheduling);\n        this.componentFixture.detectChanges();\n    }\n    /**\n     * Retrieves all nested child defer block fixtures\n     * in a given defer block.\n     */\n    getDeferBlocks() {\n        const deferBlocks = [];\n        // An LContainer that represents a defer block has at most 1 view, which is\n        // located right after an LContainer header. Get a hold of that view and inspect\n        // it for nested defer blocks.\n        const deferBlockFixtures = [];\n        if (this.block.lContainer.length >= _CONTAINER_HEADER_OFFSET) {\n            const lView = this.block.lContainer[_CONTAINER_HEADER_OFFSET];\n            _getDeferBlocks(lView, deferBlocks);\n            for (const block of deferBlocks) {\n                deferBlockFixtures.push(new DeferBlockFixture(block, this.componentFixture));\n            }\n        }\n        return Promise.resolve(deferBlockFixtures);\n    }\n}\nfunction hasStateTemplate(state, block) {\n    switch (state) {\n        case _DeferBlockState.Placeholder:\n            return block.tDetails.placeholderTmplIndex !== null;\n        case _DeferBlockState.Loading:\n            return block.tDetails.loadingTmplIndex !== null;\n        case _DeferBlockState.Error:\n            return block.tDetails.errorTmplIndex !== null;\n        case _DeferBlockState.Complete:\n            return true;\n        default:\n            return false;\n    }\n}\nfunction getDeferBlockStateNameFromEnum(state) {\n    switch (state) {\n        case _DeferBlockState.Placeholder:\n            return 'Placeholder';\n        case _DeferBlockState.Loading:\n            return 'Loading';\n        case _DeferBlockState.Error:\n            return 'Error';\n        default:\n            return 'Main';\n    }\n}\n\n/** Whether test modules should be torn down by default. */\nconst TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT = true;\n/** Whether unknown elements in templates should throw by default. */\nconst THROW_ON_UNKNOWN_ELEMENTS_DEFAULT = false;\n/** Whether unknown properties in templates should throw by default. */\nconst THROW_ON_UNKNOWN_PROPERTIES_DEFAULT = false;\n/** Whether defer blocks should use manual triggering or play through normally. */\nconst DEFER_BLOCK_DEFAULT_BEHAVIOR = _DeferBlockBehavior.Playthrough;\n/**\n * An abstract class for inserting the root test component element in a platform independent way.\n *\n * @publicApi\n */\nclass TestComponentRenderer {\n    insertRootElement(rootElementId) { }\n    removeAllRootElements() { }\n}\n/**\n * @publicApi\n */\nconst ComponentFixtureAutoDetect = new InjectionToken('ComponentFixtureAutoDetect');\n/**\n * @publicApi\n */\nconst ComponentFixtureNoNgZone = new InjectionToken('ComponentFixtureNoNgZone');\n\n/**\n * Fixture for debugging and testing a component.\n *\n * @publicApi\n */\nclass ComponentFixture {\n    componentRef;\n    /**\n     * The DebugElement associated with the root element of this component.\n     */\n    debugElement;\n    /**\n     * The instance of the root component class.\n     */\n    componentInstance;\n    /**\n     * The native element at the root of the component.\n     */\n    nativeElement;\n    /**\n     * The ElementRef for the element at the root of the component.\n     */\n    elementRef;\n    /**\n     * The ChangeDetectorRef for the component\n     */\n    changeDetectorRef;\n    _renderer;\n    _isDestroyed = false;\n    /** @internal */\n    _noZoneOptionIsSet = inject$1(ComponentFixtureNoNgZone, { optional: true });\n    /** @internal */\n    _ngZone = this._noZoneOptionIsSet ? new _NoopNgZone() : inject$1(NgZone);\n    // Inject ApplicationRef to ensure NgZone stableness causes after render hooks to run\n    // This will likely happen as a result of fixture.detectChanges because it calls ngZone.run\n    // This is a crazy way of doing things but hey, it's the world we live in.\n    // The zoneless scheduler should instead do this more imperatively by attaching\n    // the `ComponentRef` to `ApplicationRef` and calling `appRef.tick` as the `detectChanges`\n    // behavior.\n    /** @internal */\n    _appRef = inject$1(ApplicationRef);\n    _testAppRef = this._appRef;\n    pendingTasks = inject$1(_PendingTasksInternal);\n    appErrorHandler = inject$1(TestBedApplicationErrorHandler);\n    zonelessEnabled = inject$1(_ZONELESS_ENABLED);\n    scheduler = inject$1(_ChangeDetectionScheduler);\n    rootEffectScheduler = inject$1(_EffectScheduler);\n    microtaskEffectScheduler = inject$1(_MicrotaskEffectScheduler);\n    autoDetectDefault = this.zonelessEnabled ? true : false;\n    autoDetect = inject$1(ComponentFixtureAutoDetect, { optional: true }) ?? this.autoDetectDefault;\n    subscriptions = new Subscription();\n    // TODO(atscott): Remove this from public API\n    ngZone = this._noZoneOptionIsSet ? null : this._ngZone;\n    /** @docs-private */\n    constructor(componentRef) {\n        this.componentRef = componentRef;\n        this.changeDetectorRef = componentRef.changeDetectorRef;\n        this.elementRef = componentRef.location;\n        this.debugElement = getDebugNode(this.elementRef.nativeElement);\n        this.componentInstance = componentRef.instance;\n        this.nativeElement = this.elementRef.nativeElement;\n        this.componentRef = componentRef;\n        if (this.autoDetect) {\n            this._testAppRef.externalTestViews.add(this.componentRef.hostView);\n            this.scheduler?.notify(8 /* ɵNotificationSource.ViewAttached */);\n            this.scheduler?.notify(0 /* ɵNotificationSource.MarkAncestorsForTraversal */);\n        }\n        this.componentRef.hostView.onDestroy(() => {\n            this._testAppRef.externalTestViews.delete(this.componentRef.hostView);\n        });\n        // Create subscriptions outside the NgZone so that the callbacks run outside\n        // of NgZone.\n        this._ngZone.runOutsideAngular(() => {\n            this.subscriptions.add(this._ngZone.onError.subscribe({\n                next: (error) => {\n                    throw error;\n                },\n            }));\n        });\n    }\n    /**\n     * Trigger a change detection cycle for the component.\n     */\n    detectChanges(checkNoChanges = true) {\n        this.microtaskEffectScheduler.flush();\n        const originalCheckNoChanges = this.componentRef.changeDetectorRef.checkNoChanges;\n        try {\n            if (!checkNoChanges) {\n                this.componentRef.changeDetectorRef.checkNoChanges = () => { };\n            }\n            if (this.zonelessEnabled) {\n                try {\n                    this._testAppRef.externalTestViews.add(this.componentRef.hostView);\n                    this._appRef.tick();\n                }\n                finally {\n                    if (!this.autoDetect) {\n                        this._testAppRef.externalTestViews.delete(this.componentRef.hostView);\n                    }\n                }\n            }\n            else {\n                // Run the change detection inside the NgZone so that any async tasks as part of the change\n                // detection are captured by the zone and can be waited for in isStable.\n                this._ngZone.run(() => {\n                    // Flush root effects before `detectChanges()`, to emulate the sequencing of `tick()`.\n                    this.rootEffectScheduler.flush();\n                    this.changeDetectorRef.detectChanges();\n                    this.checkNoChanges();\n                });\n            }\n        }\n        finally {\n            this.componentRef.changeDetectorRef.checkNoChanges = originalCheckNoChanges;\n        }\n        this.microtaskEffectScheduler.flush();\n    }\n    /**\n     * Do a change detection run to make sure there were no changes.\n     */\n    checkNoChanges() {\n        this.changeDetectorRef.checkNoChanges();\n    }\n    /**\n     * Set whether the fixture should autodetect changes.\n     *\n     * Also runs detectChanges once so that any existing change is detected.\n     *\n     * @param autoDetect Whether to autodetect changes. By default, `true`.\n     */\n    autoDetectChanges(autoDetect = true) {\n        if (this._noZoneOptionIsSet && !this.zonelessEnabled) {\n            throw new Error('Cannot call autoDetectChanges when ComponentFixtureNoNgZone is set.');\n        }\n        if (autoDetect !== this.autoDetect) {\n            if (autoDetect) {\n                this._testAppRef.externalTestViews.add(this.componentRef.hostView);\n            }\n            else {\n                this._testAppRef.externalTestViews.delete(this.componentRef.hostView);\n            }\n        }\n        this.autoDetect = autoDetect;\n        this.detectChanges();\n    }\n    /**\n     * Return whether the fixture is currently stable or has async tasks that have not been completed\n     * yet.\n     */\n    isStable() {\n        return !this.pendingTasks.hasPendingTasks.value;\n    }\n    /**\n     * Get a promise that resolves when the fixture is stable.\n     *\n     * This can be used to resume testing after events have triggered asynchronous activity or\n     * asynchronous change detection.\n     */\n    whenStable() {\n        if (this.isStable()) {\n            return Promise.resolve(false);\n        }\n        return new Promise((resolve, reject) => {\n            this.appErrorHandler.whenStableRejectFunctions.add(reject);\n            this._appRef.whenStable().then(() => {\n                this.appErrorHandler.whenStableRejectFunctions.delete(reject);\n                resolve(true);\n            });\n        });\n    }\n    /**\n     * Retrieves all defer block fixtures in the component fixture.\n     */\n    getDeferBlocks() {\n        const deferBlocks = [];\n        const lView = this.componentRef.hostView['_lView'];\n        _getDeferBlocks(lView, deferBlocks);\n        const deferBlockFixtures = [];\n        for (const block of deferBlocks) {\n            deferBlockFixtures.push(new DeferBlockFixture(block, this));\n        }\n        return Promise.resolve(deferBlockFixtures);\n    }\n    _getRenderer() {\n        if (this._renderer === undefined) {\n            this._renderer = this.componentRef.injector.get(RendererFactory2, null);\n        }\n        return this._renderer;\n    }\n    /**\n     * Get a promise that resolves when the ui state is stable following animations.\n     */\n    whenRenderingDone() {\n        const renderer = this._getRenderer();\n        if (renderer && renderer.whenRenderingDone) {\n            return renderer.whenRenderingDone();\n        }\n        return this.whenStable();\n    }\n    /**\n     * Trigger component destruction.\n     */\n    destroy() {\n        this.subscriptions.unsubscribe();\n        this._testAppRef.externalTestViews.delete(this.componentRef.hostView);\n        if (!this._isDestroyed) {\n            this.componentRef.destroy();\n            this._isDestroyed = true;\n        }\n    }\n}\n\nconst _Zone = typeof Zone !== 'undefined' ? Zone : null;\nconst fakeAsyncTestModule = _Zone && _Zone[_Zone.__symbol__('fakeAsyncTest')];\nconst fakeAsyncTestModuleNotLoadedErrorMessage = `zone-testing.js is needed for the fakeAsync() test helper but could not be found.\n        Please make sure that your environment includes zone.js/testing`;\n/**\n * Clears out the shared fake async zone for a test.\n * To be called in a global `beforeEach`.\n *\n * @publicApi\n */\nfunction resetFakeAsyncZone() {\n    if (fakeAsyncTestModule) {\n        return fakeAsyncTestModule.resetFakeAsyncZone();\n    }\n    throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\nfunction resetFakeAsyncZoneIfExists() {\n    if (fakeAsyncTestModule && Zone['ProxyZoneSpec']?.isLoaded()) {\n        fakeAsyncTestModule.resetFakeAsyncZone();\n    }\n}\n/**\n * Wraps a function to be executed in the `fakeAsync` zone:\n * - Microtasks are manually executed by calling `flushMicrotasks()`.\n * - Timers are synchronous; `tick()` simulates the asynchronous passage of time.\n *\n * Can be used to wrap `inject()` calls.\n *\n * @param fn The function that you want to wrap in the `fakeAsync` zone.\n * @param options\n *   - flush: When true, will drain the macrotask queue after the test function completes.\n *     When false, will throw an exception at the end of the function if there are pending timers.\n *\n * @usageNotes\n * ### Example\n *\n * {@example core/testing/ts/fake_async.ts region='basic'}\n *\n *\n * @returns The function wrapped to be executed in the `fakeAsync` zone.\n * Any arguments passed when calling this returned function will be passed through to the `fn`\n * function in the parameters when it is called.\n *\n * @publicApi\n */\nfunction fakeAsync(fn, options) {\n    if (fakeAsyncTestModule) {\n        return fakeAsyncTestModule.fakeAsync(fn, options);\n    }\n    throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n/**\n * Simulates the asynchronous passage of time for the timers in the `fakeAsync` zone.\n *\n * The microtasks queue is drained at the very start of this function and after any timer callback\n * has been executed.\n *\n * @param millis The number of milliseconds to advance the virtual timer.\n * @param tickOptions The options to pass to the `tick()` function.\n *\n * @usageNotes\n *\n * The `tick()` option is a flag called `processNewMacroTasksSynchronously`,\n * which determines whether or not to invoke new macroTasks.\n *\n * If you provide a `tickOptions` object, but do not specify a\n * `processNewMacroTasksSynchronously` property (`tick(100, {})`),\n * then `processNewMacroTasksSynchronously` defaults to true.\n *\n * If you omit the `tickOptions` parameter (`tick(100))`), then\n * `tickOptions` defaults to `{processNewMacroTasksSynchronously: true}`.\n *\n * ### Example\n *\n * {@example core/testing/ts/fake_async.ts region='basic'}\n *\n * The following example includes a nested timeout (new macroTask), and\n * the `tickOptions` parameter is allowed to default. In this case,\n * `processNewMacroTasksSynchronously` defaults to true, and the nested\n * function is executed on each tick.\n *\n * ```ts\n * it ('test with nested setTimeout', fakeAsync(() => {\n *   let nestedTimeoutInvoked = false;\n *   function funcWithNestedTimeout() {\n *     setTimeout(() => {\n *       nestedTimeoutInvoked = true;\n *     });\n *   };\n *   setTimeout(funcWithNestedTimeout);\n *   tick();\n *   expect(nestedTimeoutInvoked).toBe(true);\n * }));\n * ```\n *\n * In the following case, `processNewMacroTasksSynchronously` is explicitly\n * set to false, so the nested timeout function is not invoked.\n *\n * ```ts\n * it ('test with nested setTimeout', fakeAsync(() => {\n *   let nestedTimeoutInvoked = false;\n *   function funcWithNestedTimeout() {\n *     setTimeout(() => {\n *       nestedTimeoutInvoked = true;\n *     });\n *   };\n *   setTimeout(funcWithNestedTimeout);\n *   tick(0, {processNewMacroTasksSynchronously: false});\n *   expect(nestedTimeoutInvoked).toBe(false);\n * }));\n * ```\n *\n *\n * @publicApi\n */\nfunction tick(millis = 0, tickOptions = {\n    processNewMacroTasksSynchronously: true,\n}) {\n    if (fakeAsyncTestModule) {\n        return fakeAsyncTestModule.tick(millis, tickOptions);\n    }\n    throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n/**\n * Flushes any pending microtasks and simulates the asynchronous passage of time for the timers in\n * the `fakeAsync` zone by\n * draining the macrotask queue until it is empty.\n *\n * @param maxTurns The maximum number of times the scheduler attempts to clear its queue before\n *     throwing an error.\n * @returns The simulated time elapsed, in milliseconds.\n *\n * @publicApi\n */\nfunction flush(maxTurns) {\n    if (fakeAsyncTestModule) {\n        return fakeAsyncTestModule.flush(maxTurns);\n    }\n    throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n/**\n * Discard all remaining periodic tasks.\n *\n * @publicApi\n */\nfunction discardPeriodicTasks() {\n    if (fakeAsyncTestModule) {\n        return fakeAsyncTestModule.discardPeriodicTasks();\n    }\n    throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n/**\n * Flush any pending microtasks.\n *\n * @publicApi\n */\nfunction flushMicrotasks() {\n    if (fakeAsyncTestModule) {\n        return fakeAsyncTestModule.flushMicrotasks();\n    }\n    throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n\nlet _nextReferenceId = 0;\nclass MetadataOverrider {\n    _references = new Map();\n    /**\n     * Creates a new instance for the given metadata class\n     * based on an old instance and overrides.\n     */\n    overrideMetadata(metadataClass, oldMetadata, override) {\n        const props = {};\n        if (oldMetadata) {\n            _valueProps(oldMetadata).forEach((prop) => (props[prop] = oldMetadata[prop]));\n        }\n        if (override.set) {\n            if (override.remove || override.add) {\n                throw new Error(`Cannot set and add/remove ${_stringify(metadataClass)} at the same time!`);\n            }\n            setMetadata(props, override.set);\n        }\n        if (override.remove) {\n            removeMetadata(props, override.remove, this._references);\n        }\n        if (override.add) {\n            addMetadata(props, override.add);\n        }\n        return new metadataClass(props);\n    }\n}\nfunction removeMetadata(metadata, remove, references) {\n    const removeObjects = new Set();\n    for (const prop in remove) {\n        const removeValue = remove[prop];\n        if (Array.isArray(removeValue)) {\n            removeValue.forEach((value) => {\n                removeObjects.add(_propHashKey(prop, value, references));\n            });\n        }\n        else {\n            removeObjects.add(_propHashKey(prop, removeValue, references));\n        }\n    }\n    for (const prop in metadata) {\n        const propValue = metadata[prop];\n        if (Array.isArray(propValue)) {\n            metadata[prop] = propValue.filter((value) => !removeObjects.has(_propHashKey(prop, value, references)));\n        }\n        else {\n            if (removeObjects.has(_propHashKey(prop, propValue, references))) {\n                metadata[prop] = undefined;\n            }\n        }\n    }\n}\nfunction addMetadata(metadata, add) {\n    for (const prop in add) {\n        const addValue = add[prop];\n        const propValue = metadata[prop];\n        if (propValue != null && Array.isArray(propValue)) {\n            metadata[prop] = propValue.concat(addValue);\n        }\n        else {\n            metadata[prop] = addValue;\n        }\n    }\n}\nfunction setMetadata(metadata, set) {\n    for (const prop in set) {\n        metadata[prop] = set[prop];\n    }\n}\nfunction _propHashKey(propName, propValue, references) {\n    let nextObjectId = 0;\n    const objectIds = new Map();\n    const replacer = (key, value) => {\n        if (value !== null && typeof value === 'object') {\n            if (objectIds.has(value)) {\n                return objectIds.get(value);\n            }\n            // Record an id for this object such that any later references use the object's id instead\n            // of the object itself, in order to break cyclic pointers in objects.\n            objectIds.set(value, `ɵobj#${nextObjectId++}`);\n            // The first time an object is seen the object itself is serialized.\n            return value;\n        }\n        else if (typeof value === 'function') {\n            value = _serializeReference(value, references);\n        }\n        return value;\n    };\n    return `${propName}:${JSON.stringify(propValue, replacer)}`;\n}\nfunction _serializeReference(ref, references) {\n    let id = references.get(ref);\n    if (!id) {\n        id = `${_stringify(ref)}${_nextReferenceId++}`;\n        references.set(ref, id);\n    }\n    return id;\n}\nfunction _valueProps(obj) {\n    const props = [];\n    // regular public props\n    Object.keys(obj).forEach((prop) => {\n        if (!prop.startsWith('_')) {\n            props.push(prop);\n        }\n    });\n    // getters\n    let proto = obj;\n    while ((proto = Object.getPrototypeOf(proto))) {\n        Object.keys(proto).forEach((protoProp) => {\n            const desc = Object.getOwnPropertyDescriptor(proto, protoProp);\n            if (!protoProp.startsWith('_') && desc && 'get' in desc) {\n                props.push(protoProp);\n            }\n        });\n    }\n    return props;\n}\n\nconst reflection = new _ReflectionCapabilities();\n/**\n * Allows to override ivy metadata for tests (via the `TestBed`).\n */\nclass OverrideResolver {\n    overrides = new Map();\n    resolved = new Map();\n    addOverride(type, override) {\n        const overrides = this.overrides.get(type) || [];\n        overrides.push(override);\n        this.overrides.set(type, overrides);\n        this.resolved.delete(type);\n    }\n    setOverrides(overrides) {\n        this.overrides.clear();\n        overrides.forEach(([type, override]) => {\n            this.addOverride(type, override);\n        });\n    }\n    getAnnotation(type) {\n        const annotations = reflection.annotations(type);\n        // Try to find the nearest known Type annotation and make sure that this annotation is an\n        // instance of the type we are looking for, so we can use it for resolution. Note: there might\n        // be multiple known annotations found due to the fact that Components can extend Directives (so\n        // both Directive and Component annotations would be present), so we always check if the known\n        // annotation has the right type.\n        for (let i = annotations.length - 1; i >= 0; i--) {\n            const annotation = annotations[i];\n            const isKnownType = annotation instanceof Directive ||\n                annotation instanceof Component ||\n                annotation instanceof Pipe ||\n                annotation instanceof NgModule;\n            if (isKnownType) {\n                return annotation instanceof this.type ? annotation : null;\n            }\n        }\n        return null;\n    }\n    resolve(type) {\n        let resolved = this.resolved.get(type) || null;\n        if (!resolved) {\n            resolved = this.getAnnotation(type);\n            if (resolved) {\n                const overrides = this.overrides.get(type);\n                if (overrides) {\n                    const overrider = new MetadataOverrider();\n                    overrides.forEach((override) => {\n                        resolved = overrider.overrideMetadata(this.type, resolved, override);\n                    });\n                }\n            }\n            this.resolved.set(type, resolved);\n        }\n        return resolved;\n    }\n}\nclass DirectiveResolver extends OverrideResolver {\n    get type() {\n        return Directive;\n    }\n}\nclass ComponentResolver extends OverrideResolver {\n    get type() {\n        return Component;\n    }\n}\nclass PipeResolver extends OverrideResolver {\n    get type() {\n        return Pipe;\n    }\n}\nclass NgModuleResolver extends OverrideResolver {\n    get type() {\n        return NgModule;\n    }\n}\n\nvar TestingModuleOverride;\n(function (TestingModuleOverride) {\n    TestingModuleOverride[TestingModuleOverride[\"DECLARATION\"] = 0] = \"DECLARATION\";\n    TestingModuleOverride[TestingModuleOverride[\"OVERRIDE_TEMPLATE\"] = 1] = \"OVERRIDE_TEMPLATE\";\n})(TestingModuleOverride || (TestingModuleOverride = {}));\nfunction isTestingModuleOverride(value) {\n    return (value === TestingModuleOverride.DECLARATION || value === TestingModuleOverride.OVERRIDE_TEMPLATE);\n}\nfunction assertNoStandaloneComponents(types, resolver, location) {\n    types.forEach((type) => {\n        if (!_getAsyncClassMetadataFn(type)) {\n            const component = resolver.resolve(type);\n            if (component && (component.standalone == null || component.standalone)) {\n                throw new Error(_generateStandaloneInDeclarationsError(type, location));\n            }\n        }\n    });\n}\nclass TestBedCompiler {\n    platform;\n    additionalModuleTypes;\n    originalComponentResolutionQueue = null;\n    // Testing module configuration\n    declarations = [];\n    imports = [];\n    providers = [];\n    schemas = [];\n    // Queues of components/directives/pipes that should be recompiled.\n    pendingComponents = new Set();\n    pendingDirectives = new Set();\n    pendingPipes = new Set();\n    // Set of components with async metadata, i.e. components with `@defer` blocks\n    // in their templates.\n    componentsWithAsyncMetadata = new Set();\n    // Keep track of all components and directives, so we can patch Providers onto defs later.\n    seenComponents = new Set();\n    seenDirectives = new Set();\n    // Keep track of overridden modules, so that we can collect all affected ones in the module tree.\n    overriddenModules = new Set();\n    // Store resolved styles for Components that have template overrides present and `styleUrls`\n    // defined at the same time.\n    existingComponentStyles = new Map();\n    resolvers = initResolvers();\n    // Map of component type to an NgModule that declares it.\n    //\n    // There are a couple special cases:\n    // - for standalone components, the module scope value is `null`\n    // - when a component is declared in `TestBed.configureTestingModule()` call or\n    //   a component's template is overridden via `TestBed.overrideTemplateUsingTestingModule()`.\n    //   we use a special value from the `TestingModuleOverride` enum.\n    componentToModuleScope = new Map();\n    // Map that keeps initial version of component/directive/pipe defs in case\n    // we compile a Type again, thus overriding respective static fields. This is\n    // required to make sure we restore defs to their initial states between test runs.\n    // Note: one class may have multiple defs (for example: ɵmod and ɵinj in case of an\n    // NgModule), store all of them in a map.\n    initialNgDefs = new Map();\n    // Array that keeps cleanup operations for initial versions of component/directive/pipe/module\n    // defs in case TestBed makes changes to the originals.\n    defCleanupOps = [];\n    _injector = null;\n    compilerProviders = null;\n    providerOverrides = [];\n    rootProviderOverrides = [];\n    // Overrides for injectables with `{providedIn: SomeModule}` need to be tracked and added to that\n    // module's provider list.\n    providerOverridesByModule = new Map();\n    providerOverridesByToken = new Map();\n    scopesWithOverriddenProviders = new Set();\n    testModuleType;\n    testModuleRef = null;\n    deferBlockBehavior = DEFER_BLOCK_DEFAULT_BEHAVIOR;\n    rethrowApplicationTickErrors = RETHROW_APPLICATION_ERRORS_DEFAULT;\n    constructor(platform, additionalModuleTypes) {\n        this.platform = platform;\n        this.additionalModuleTypes = additionalModuleTypes;\n        class DynamicTestModule {\n        }\n        this.testModuleType = DynamicTestModule;\n    }\n    setCompilerProviders(providers) {\n        this.compilerProviders = providers;\n        this._injector = null;\n    }\n    configureTestingModule(moduleDef) {\n        // Enqueue any compilation tasks for the directly declared component.\n        if (moduleDef.declarations !== undefined) {\n            // Verify that there are no standalone components\n            assertNoStandaloneComponents(moduleDef.declarations, this.resolvers.component, '\"TestBed.configureTestingModule\" call');\n            this.queueTypeArray(moduleDef.declarations, TestingModuleOverride.DECLARATION);\n            this.declarations.push(...moduleDef.declarations);\n        }\n        // Enqueue any compilation tasks for imported modules.\n        if (moduleDef.imports !== undefined) {\n            this.queueTypesFromModulesArray(moduleDef.imports);\n            this.imports.push(...moduleDef.imports);\n        }\n        if (moduleDef.providers !== undefined) {\n            this.providers.push(...moduleDef.providers);\n        }\n        if (moduleDef.schemas !== undefined) {\n            this.schemas.push(...moduleDef.schemas);\n        }\n        this.deferBlockBehavior = moduleDef.deferBlockBehavior ?? DEFER_BLOCK_DEFAULT_BEHAVIOR;\n        this.rethrowApplicationTickErrors =\n            moduleDef.rethrowApplicationErrors ?? RETHROW_APPLICATION_ERRORS_DEFAULT;\n    }\n    overrideModule(ngModule, override) {\n        if (_USE_RUNTIME_DEPS_TRACKER_FOR_JIT) {\n            _depsTracker.clearScopeCacheFor(ngModule);\n        }\n        this.overriddenModules.add(ngModule);\n        // Compile the module right away.\n        this.resolvers.module.addOverride(ngModule, override);\n        const metadata = this.resolvers.module.resolve(ngModule);\n        if (metadata === null) {\n            throw invalidTypeError(ngModule.name, 'NgModule');\n        }\n        this.recompileNgModule(ngModule, metadata);\n        // At this point, the module has a valid module def (ɵmod), but the override may have introduced\n        // new declarations or imported modules. Ingest any possible new types and add them to the\n        // current queue.\n        this.queueTypesFromModulesArray([ngModule]);\n    }\n    overrideComponent(component, override) {\n        this.verifyNoStandaloneFlagOverrides(component, override);\n        this.resolvers.component.addOverride(component, override);\n        this.pendingComponents.add(component);\n        // If this is a component with async metadata (i.e. a component with a `@defer` block\n        // in a template) - store it for future processing.\n        this.maybeRegisterComponentWithAsyncMetadata(component);\n    }\n    overrideDirective(directive, override) {\n        this.verifyNoStandaloneFlagOverrides(directive, override);\n        this.resolvers.directive.addOverride(directive, override);\n        this.pendingDirectives.add(directive);\n    }\n    overridePipe(pipe, override) {\n        this.verifyNoStandaloneFlagOverrides(pipe, override);\n        this.resolvers.pipe.addOverride(pipe, override);\n        this.pendingPipes.add(pipe);\n    }\n    verifyNoStandaloneFlagOverrides(type, override) {\n        if (override.add?.hasOwnProperty('standalone') ||\n            override.set?.hasOwnProperty('standalone') ||\n            override.remove?.hasOwnProperty('standalone')) {\n            throw new Error(`An override for the ${type.name} class has the \\`standalone\\` flag. ` +\n                `Changing the \\`standalone\\` flag via TestBed overrides is not supported.`);\n        }\n    }\n    overrideProvider(token, provider) {\n        let providerDef;\n        if (provider.useFactory !== undefined) {\n            providerDef = {\n                provide: token,\n                useFactory: provider.useFactory,\n                deps: provider.deps || [],\n                multi: provider.multi,\n            };\n        }\n        else if (provider.useValue !== undefined) {\n            providerDef = { provide: token, useValue: provider.useValue, multi: provider.multi };\n        }\n        else {\n            providerDef = { provide: token };\n        }\n        const injectableDef = typeof token !== 'string' ? _getInjectableDef(token) : null;\n        const providedIn = injectableDef === null ? null : resolveForwardRef(injectableDef.providedIn);\n        const overridesBucket = providedIn === 'root' ? this.rootProviderOverrides : this.providerOverrides;\n        overridesBucket.push(providerDef);\n        // Keep overrides grouped by token as well for fast lookups using token\n        this.providerOverridesByToken.set(token, providerDef);\n        if (injectableDef !== null && providedIn !== null && typeof providedIn !== 'string') {\n            const existingOverrides = this.providerOverridesByModule.get(providedIn);\n            if (existingOverrides !== undefined) {\n                existingOverrides.push(providerDef);\n            }\n            else {\n                this.providerOverridesByModule.set(providedIn, [providerDef]);\n            }\n        }\n    }\n    overrideTemplateUsingTestingModule(type, template) {\n        const def = type[_NG_COMP_DEF];\n        const hasStyleUrls = () => {\n            const metadata = this.resolvers.component.resolve(type);\n            return !!metadata.styleUrl || !!metadata.styleUrls?.length;\n        };\n        const overrideStyleUrls = !!def && !_isComponentDefPendingResolution(type) && hasStyleUrls();\n        // In Ivy, compiling a component does not require knowing the module providing the\n        // component's scope, so overrideTemplateUsingTestingModule can be implemented purely via\n        // overrideComponent. Important: overriding template requires full Component re-compilation,\n        // which may fail in case styleUrls are also present (thus Component is considered as required\n        // resolution). In order to avoid this, we preemptively set styleUrls to an empty array,\n        // preserve current styles available on Component def and restore styles back once compilation\n        // is complete.\n        const override = overrideStyleUrls\n            ? { template, styles: [], styleUrls: [], styleUrl: undefined }\n            : { template };\n        this.overrideComponent(type, { set: override });\n        if (overrideStyleUrls && def.styles && def.styles.length > 0) {\n            this.existingComponentStyles.set(type, def.styles);\n        }\n        // Set the component's scope to be the testing module.\n        this.componentToModuleScope.set(type, TestingModuleOverride.OVERRIDE_TEMPLATE);\n    }\n    async resolvePendingComponentsWithAsyncMetadata() {\n        if (this.componentsWithAsyncMetadata.size === 0)\n            return;\n        const promises = [];\n        for (const component of this.componentsWithAsyncMetadata) {\n            const asyncMetadataFn = _getAsyncClassMetadataFn(component);\n            if (asyncMetadataFn) {\n                promises.push(asyncMetadataFn());\n            }\n        }\n        this.componentsWithAsyncMetadata.clear();\n        const resolvedDeps = await Promise.all(promises);\n        const flatResolvedDeps = resolvedDeps.flat(2);\n        this.queueTypesFromModulesArray(flatResolvedDeps);\n        // Loaded standalone components might contain imports of NgModules\n        // with providers, make sure we override providers there too.\n        for (const component of flatResolvedDeps) {\n            this.applyProviderOverridesInScope(component);\n        }\n    }\n    async compileComponents() {\n        this.clearComponentResolutionQueue();\n        // Wait for all async metadata for components that were\n        // overridden, we need resolved metadata to perform an override\n        // and re-compile a component.\n        await this.resolvePendingComponentsWithAsyncMetadata();\n        // Verify that there were no standalone components present in the `declarations` field\n        // during the `TestBed.configureTestingModule` call. We perform this check here in addition\n        // to the logic in the `configureTestingModule` function, since at this point we have\n        // all async metadata resolved.\n        assertNoStandaloneComponents(this.declarations, this.resolvers.component, '\"TestBed.configureTestingModule\" call');\n        // Run compilers for all queued types.\n        let needsAsyncResources = this.compileTypesSync();\n        // compileComponents() should not be async unless it needs to be.\n        if (needsAsyncResources) {\n            let resourceLoader;\n            let resolver = (url) => {\n                if (!resourceLoader) {\n                    resourceLoader = this.injector.get(ResourceLoader);\n                }\n                return Promise.resolve(resourceLoader.get(url));\n            };\n            await _resolveComponentResources(resolver);\n        }\n    }\n    finalize() {\n        // One last compile\n        this.compileTypesSync();\n        // Create the testing module itself.\n        this.compileTestModule();\n        this.applyTransitiveScopes();\n        this.applyProviderOverrides();\n        // Patch previously stored `styles` Component values (taken from ɵcmp), in case these\n        // Components have `styleUrls` fields defined and template override was requested.\n        this.patchComponentsWithExistingStyles();\n        // Clear the componentToModuleScope map, so that future compilations don't reset the scope of\n        // every component.\n        this.componentToModuleScope.clear();\n        const parentInjector = this.platform.injector;\n        this.testModuleRef = new _Render3NgModuleRef(this.testModuleType, parentInjector, []);\n        // ApplicationInitStatus.runInitializers() is marked @internal to core.\n        // Cast it to any before accessing it.\n        this.testModuleRef.injector.get(ApplicationInitStatus).runInitializers();\n        // Set locale ID after running app initializers, since locale information might be updated while\n        // running initializers. This is also consistent with the execution order while bootstrapping an\n        // app (see `packages/core/src/application_ref.ts` file).\n        const localeId = this.testModuleRef.injector.get(LOCALE_ID, _DEFAULT_LOCALE_ID);\n        _setLocaleId(localeId);\n        return this.testModuleRef;\n    }\n    /**\n     * @internal\n     */\n    _compileNgModuleSync(moduleType) {\n        this.queueTypesFromModulesArray([moduleType]);\n        this.compileTypesSync();\n        this.applyProviderOverrides();\n        this.applyProviderOverridesInScope(moduleType);\n        this.applyTransitiveScopes();\n    }\n    /**\n     * @internal\n     */\n    async _compileNgModuleAsync(moduleType) {\n        this.queueTypesFromModulesArray([moduleType]);\n        await this.compileComponents();\n        this.applyProviderOverrides();\n        this.applyProviderOverridesInScope(moduleType);\n        this.applyTransitiveScopes();\n    }\n    /**\n     * @internal\n     */\n    _getModuleResolver() {\n        return this.resolvers.module;\n    }\n    /**\n     * @internal\n     */\n    _getComponentFactories(moduleType) {\n        return maybeUnwrapFn(moduleType.ɵmod.declarations).reduce((factories, declaration) => {\n            const componentDef = declaration.ɵcmp;\n            componentDef && factories.push(new _Render3ComponentFactory(componentDef, this.testModuleRef));\n            return factories;\n        }, []);\n    }\n    compileTypesSync() {\n        // Compile all queued components, directives, pipes.\n        let needsAsyncResources = false;\n        this.pendingComponents.forEach((declaration) => {\n            if (_getAsyncClassMetadataFn(declaration)) {\n                throw new Error(`Component '${declaration.name}' has unresolved metadata. ` +\n                    `Please call \\`await TestBed.compileComponents()\\` before running this test.`);\n            }\n            needsAsyncResources = needsAsyncResources || _isComponentDefPendingResolution(declaration);\n            const metadata = this.resolvers.component.resolve(declaration);\n            if (metadata === null) {\n                throw invalidTypeError(declaration.name, 'Component');\n            }\n            this.maybeStoreNgDef(_NG_COMP_DEF, declaration);\n            if (_USE_RUNTIME_DEPS_TRACKER_FOR_JIT) {\n                _depsTracker.clearScopeCacheFor(declaration);\n            }\n            _compileComponent(declaration, metadata);\n        });\n        this.pendingComponents.clear();\n        this.pendingDirectives.forEach((declaration) => {\n            const metadata = this.resolvers.directive.resolve(declaration);\n            if (metadata === null) {\n                throw invalidTypeError(declaration.name, 'Directive');\n            }\n            this.maybeStoreNgDef(_NG_DIR_DEF, declaration);\n            _compileDirective(declaration, metadata);\n        });\n        this.pendingDirectives.clear();\n        this.pendingPipes.forEach((declaration) => {\n            const metadata = this.resolvers.pipe.resolve(declaration);\n            if (metadata === null) {\n                throw invalidTypeError(declaration.name, 'Pipe');\n            }\n            this.maybeStoreNgDef(_NG_PIPE_DEF, declaration);\n            _compilePipe(declaration, metadata);\n        });\n        this.pendingPipes.clear();\n        return needsAsyncResources;\n    }\n    applyTransitiveScopes() {\n        if (this.overriddenModules.size > 0) {\n            // Module overrides (via `TestBed.overrideModule`) might affect scopes that were previously\n            // calculated and stored in `transitiveCompileScopes`. If module overrides are present,\n            // collect all affected modules and reset scopes to force their re-calculation.\n            const testingModuleDef = this.testModuleType[_NG_MOD_DEF];\n            const affectedModules = this.collectModulesAffectedByOverrides(testingModuleDef.imports);\n            if (affectedModules.size > 0) {\n                affectedModules.forEach((moduleType) => {\n                    if (!_USE_RUNTIME_DEPS_TRACKER_FOR_JIT) {\n                        this.storeFieldOfDefOnType(moduleType, _NG_MOD_DEF, 'transitiveCompileScopes');\n                        moduleType[_NG_MOD_DEF].transitiveCompileScopes = null;\n                    }\n                    else {\n                        _depsTracker.clearScopeCacheFor(moduleType);\n                    }\n                });\n            }\n        }\n        const moduleToScope = new Map();\n        const getScopeOfModule = (moduleType) => {\n            if (!moduleToScope.has(moduleType)) {\n                const isTestingModule = isTestingModuleOverride(moduleType);\n                const realType = isTestingModule ? this.testModuleType : moduleType;\n                moduleToScope.set(moduleType, _transitiveScopesFor(realType));\n            }\n            return moduleToScope.get(moduleType);\n        };\n        this.componentToModuleScope.forEach((moduleType, componentType) => {\n            if (moduleType !== null) {\n                const moduleScope = getScopeOfModule(moduleType);\n                this.storeFieldOfDefOnType(componentType, _NG_COMP_DEF, 'directiveDefs');\n                this.storeFieldOfDefOnType(componentType, _NG_COMP_DEF, 'pipeDefs');\n                _patchComponentDefWithScope(getComponentDef(componentType), moduleScope);\n            }\n            // `tView` that is stored on component def contains information about directives and pipes\n            // that are in the scope of this component. Patching component scope will cause `tView` to be\n            // changed. Store original `tView` before patching scope, so the `tView` (including scope\n            // information) is restored back to its previous/original state before running next test.\n            // Resetting `tView` is also needed for cases when we apply provider overrides and those\n            // providers are defined on component's level, in which case they may end up included into\n            // `tView.blueprint`.\n            this.storeFieldOfDefOnType(componentType, _NG_COMP_DEF, 'tView');\n        });\n        this.componentToModuleScope.clear();\n    }\n    applyProviderOverrides() {\n        const maybeApplyOverrides = (field) => (type) => {\n            const resolver = field === _NG_COMP_DEF ? this.resolvers.component : this.resolvers.directive;\n            const metadata = resolver.resolve(type);\n            if (this.hasProviderOverrides(metadata.providers)) {\n                this.patchDefWithProviderOverrides(type, field);\n            }\n        };\n        this.seenComponents.forEach(maybeApplyOverrides(_NG_COMP_DEF));\n        this.seenDirectives.forEach(maybeApplyOverrides(_NG_DIR_DEF));\n        this.seenComponents.clear();\n        this.seenDirectives.clear();\n    }\n    /**\n     * Applies provider overrides to a given type (either an NgModule or a standalone component)\n     * and all imported NgModules and standalone components recursively.\n     */\n    applyProviderOverridesInScope(type) {\n        const hasScope = isStandaloneComponent(type) || isNgModule(type);\n        // The function can be re-entered recursively while inspecting dependencies\n        // of an NgModule or a standalone component. Exit early if we come across a\n        // type that can not have a scope (directive or pipe) or the type is already\n        // processed earlier.\n        if (!hasScope || this.scopesWithOverriddenProviders.has(type)) {\n            return;\n        }\n        this.scopesWithOverriddenProviders.add(type);\n        // NOTE: the line below triggers JIT compilation of the module injector,\n        // which also invokes verification of the NgModule semantics, which produces\n        // detailed error messages. The fact that the code relies on this line being\n        // present here is suspicious and should be refactored in a way that the line\n        // below can be moved (for ex. after an early exit check below).\n        const injectorDef = type[_NG_INJ_DEF];\n        // No provider overrides, exit early.\n        if (this.providerOverridesByToken.size === 0)\n            return;\n        if (isStandaloneComponent(type)) {\n            // Visit all component dependencies and override providers there.\n            const def = getComponentDef(type);\n            const dependencies = maybeUnwrapFn(def.dependencies ?? []);\n            for (const dependency of dependencies) {\n                this.applyProviderOverridesInScope(dependency);\n            }\n        }\n        else {\n            const providers = [\n                ...injectorDef.providers,\n                ...(this.providerOverridesByModule.get(type) || []),\n            ];\n            if (this.hasProviderOverrides(providers)) {\n                this.maybeStoreNgDef(_NG_INJ_DEF, type);\n                this.storeFieldOfDefOnType(type, _NG_INJ_DEF, 'providers');\n                injectorDef.providers = this.getOverriddenProviders(providers);\n            }\n            // Apply provider overrides to imported modules recursively\n            const moduleDef = type[_NG_MOD_DEF];\n            const imports = maybeUnwrapFn(moduleDef.imports);\n            for (const importedModule of imports) {\n                this.applyProviderOverridesInScope(importedModule);\n            }\n            // Also override the providers on any ModuleWithProviders imports since those don't appear in\n            // the moduleDef.\n            for (const importedModule of flatten(injectorDef.imports)) {\n                if (isModuleWithProviders(importedModule)) {\n                    this.defCleanupOps.push({\n                        object: importedModule,\n                        fieldName: 'providers',\n                        originalValue: importedModule.providers,\n                    });\n                    importedModule.providers = this.getOverriddenProviders(importedModule.providers);\n                }\n            }\n        }\n    }\n    patchComponentsWithExistingStyles() {\n        this.existingComponentStyles.forEach((styles, type) => (type[_NG_COMP_DEF].styles = styles));\n        this.existingComponentStyles.clear();\n    }\n    queueTypeArray(arr, moduleType) {\n        for (const value of arr) {\n            if (Array.isArray(value)) {\n                this.queueTypeArray(value, moduleType);\n            }\n            else {\n                this.queueType(value, moduleType);\n            }\n        }\n    }\n    recompileNgModule(ngModule, metadata) {\n        // Cache the initial ngModuleDef as it will be overwritten.\n        this.maybeStoreNgDef(_NG_MOD_DEF, ngModule);\n        this.maybeStoreNgDef(_NG_INJ_DEF, ngModule);\n        _compileNgModuleDefs(ngModule, metadata);\n    }\n    maybeRegisterComponentWithAsyncMetadata(type) {\n        const asyncMetadataFn = _getAsyncClassMetadataFn(type);\n        if (asyncMetadataFn) {\n            this.componentsWithAsyncMetadata.add(type);\n        }\n    }\n    queueType(type, moduleType) {\n        // If this is a component with async metadata (i.e. a component with a `@defer` block\n        // in a template) - store it for future processing.\n        this.maybeRegisterComponentWithAsyncMetadata(type);\n        const component = this.resolvers.component.resolve(type);\n        if (component) {\n            // Check whether a give Type has respective NG def (ɵcmp) and compile if def is\n            // missing. That might happen in case a class without any Angular decorators extends another\n            // class where Component/Directive/Pipe decorator is defined.\n            if (_isComponentDefPendingResolution(type) || !type.hasOwnProperty(_NG_COMP_DEF)) {\n                this.pendingComponents.add(type);\n            }\n            this.seenComponents.add(type);\n            // Keep track of the module which declares this component, so later the component's scope\n            // can be set correctly. If the component has already been recorded here, then one of several\n            // cases is true:\n            // * the module containing the component was imported multiple times (common).\n            // * the component is declared in multiple modules (which is an error).\n            // * the component was in 'declarations' of the testing module, and also in an imported module\n            //   in which case the module scope will be TestingModuleOverride.DECLARATION.\n            // * overrideTemplateUsingTestingModule was called for the component in which case the module\n            //   scope will be TestingModuleOverride.OVERRIDE_TEMPLATE.\n            //\n            // If the component was previously in the testing module's 'declarations' (meaning the\n            // current value is TestingModuleOverride.DECLARATION), then `moduleType` is the component's\n            // real module, which was imported. This pattern is understood to mean that the component\n            // should use its original scope, but that the testing module should also contain the\n            // component in its scope.\n            if (!this.componentToModuleScope.has(type) ||\n                this.componentToModuleScope.get(type) === TestingModuleOverride.DECLARATION) {\n                this.componentToModuleScope.set(type, moduleType);\n            }\n            return;\n        }\n        const directive = this.resolvers.directive.resolve(type);\n        if (directive) {\n            if (!type.hasOwnProperty(_NG_DIR_DEF)) {\n                this.pendingDirectives.add(type);\n            }\n            this.seenDirectives.add(type);\n            return;\n        }\n        const pipe = this.resolvers.pipe.resolve(type);\n        if (pipe && !type.hasOwnProperty(_NG_PIPE_DEF)) {\n            this.pendingPipes.add(type);\n            return;\n        }\n    }\n    queueTypesFromModulesArray(arr) {\n        // Because we may encounter the same NgModule or a standalone Component while processing\n        // the dependencies of an NgModule or a standalone Component, we cache them in this set so we\n        // can skip ones that have already been seen encountered. In some test setups, this caching\n        // resulted in 10X runtime improvement.\n        const processedDefs = new Set();\n        const queueTypesFromModulesArrayRecur = (arr) => {\n            for (const value of arr) {\n                if (Array.isArray(value)) {\n                    queueTypesFromModulesArrayRecur(value);\n                }\n                else if (hasNgModuleDef(value)) {\n                    const def = value.ɵmod;\n                    if (processedDefs.has(def)) {\n                        continue;\n                    }\n                    processedDefs.add(def);\n                    // Look through declarations, imports, and exports, and queue\n                    // everything found there.\n                    this.queueTypeArray(maybeUnwrapFn(def.declarations), value);\n                    queueTypesFromModulesArrayRecur(maybeUnwrapFn(def.imports));\n                    queueTypesFromModulesArrayRecur(maybeUnwrapFn(def.exports));\n                }\n                else if (isModuleWithProviders(value)) {\n                    queueTypesFromModulesArrayRecur([value.ngModule]);\n                }\n                else if (isStandaloneComponent(value)) {\n                    this.queueType(value, null);\n                    const def = getComponentDef(value);\n                    if (processedDefs.has(def)) {\n                        continue;\n                    }\n                    processedDefs.add(def);\n                    const dependencies = maybeUnwrapFn(def.dependencies ?? []);\n                    dependencies.forEach((dependency) => {\n                        // Note: in AOT, the `dependencies` might also contain regular\n                        // (NgModule-based) Component, Directive and Pipes, so we handle\n                        // them separately and proceed with recursive process for standalone\n                        // Components and NgModules only.\n                        if (isStandaloneComponent(dependency) || hasNgModuleDef(dependency)) {\n                            queueTypesFromModulesArrayRecur([dependency]);\n                        }\n                        else {\n                            this.queueType(dependency, null);\n                        }\n                    });\n                }\n            }\n        };\n        queueTypesFromModulesArrayRecur(arr);\n    }\n    // When module overrides (via `TestBed.overrideModule`) are present, it might affect all modules\n    // that import (even transitively) an overridden one. For all affected modules we need to\n    // recalculate their scopes for a given test run and restore original scopes at the end. The goal\n    // of this function is to collect all affected modules in a set for further processing. Example:\n    // if we have the following module hierarchy: A -> B -> C (where `->` means `imports`) and module\n    // `C` is overridden, we consider `A` and `B` as affected, since their scopes might become\n    // invalidated with the override.\n    collectModulesAffectedByOverrides(arr) {\n        const seenModules = new Set();\n        const affectedModules = new Set();\n        const calcAffectedModulesRecur = (arr, path) => {\n            for (const value of arr) {\n                if (Array.isArray(value)) {\n                    // If the value is an array, just flatten it (by invoking this function recursively),\n                    // keeping \"path\" the same.\n                    calcAffectedModulesRecur(value, path);\n                }\n                else if (hasNgModuleDef(value)) {\n                    if (seenModules.has(value)) {\n                        // If we've seen this module before and it's included into \"affected modules\" list, mark\n                        // the whole path that leads to that module as affected, but do not descend into its\n                        // imports, since we already examined them before.\n                        if (affectedModules.has(value)) {\n                            path.forEach((item) => affectedModules.add(item));\n                        }\n                        continue;\n                    }\n                    seenModules.add(value);\n                    if (this.overriddenModules.has(value)) {\n                        path.forEach((item) => affectedModules.add(item));\n                    }\n                    // Examine module imports recursively to look for overridden modules.\n                    const moduleDef = value[_NG_MOD_DEF];\n                    calcAffectedModulesRecur(maybeUnwrapFn(moduleDef.imports), path.concat(value));\n                }\n            }\n        };\n        calcAffectedModulesRecur(arr, []);\n        return affectedModules;\n    }\n    /**\n     * Preserve an original def (such as ɵmod, ɵinj, etc) before applying an override.\n     * Note: one class may have multiple defs (for example: ɵmod and ɵinj in case of\n     * an NgModule). If there is a def in a set already, don't override it, since\n     * an original one should be restored at the end of a test.\n     */\n    maybeStoreNgDef(prop, type) {\n        if (!this.initialNgDefs.has(type)) {\n            this.initialNgDefs.set(type, new Map());\n        }\n        const currentDefs = this.initialNgDefs.get(type);\n        if (!currentDefs.has(prop)) {\n            const currentDef = Object.getOwnPropertyDescriptor(type, prop);\n            currentDefs.set(prop, currentDef);\n        }\n    }\n    storeFieldOfDefOnType(type, defField, fieldName) {\n        const def = type[defField];\n        const originalValue = def[fieldName];\n        this.defCleanupOps.push({ object: def, fieldName, originalValue });\n    }\n    /**\n     * Clears current components resolution queue, but stores the state of the queue, so we can\n     * restore it later. Clearing the queue is required before we try to compile components (via\n     * `TestBed.compileComponents`), so that component defs are in sync with the resolution queue.\n     */\n    clearComponentResolutionQueue() {\n        if (this.originalComponentResolutionQueue === null) {\n            this.originalComponentResolutionQueue = new Map();\n        }\n        _clearResolutionOfComponentResourcesQueue().forEach((value, key) => this.originalComponentResolutionQueue.set(key, value));\n    }\n    /*\n     * Restores component resolution queue to the previously saved state. This operation is performed\n     * as a part of restoring the state after completion of the current set of tests (that might\n     * potentially mutate the state).\n     */\n    restoreComponentResolutionQueue() {\n        if (this.originalComponentResolutionQueue !== null) {\n            _restoreComponentResolutionQueue(this.originalComponentResolutionQueue);\n            this.originalComponentResolutionQueue = null;\n        }\n    }\n    restoreOriginalState() {\n        // Process cleanup ops in reverse order so the field's original value is restored correctly (in\n        // case there were multiple overrides for the same field).\n        forEachRight(this.defCleanupOps, (op) => {\n            op.object[op.fieldName] = op.originalValue;\n        });\n        // Restore initial component/directive/pipe defs\n        this.initialNgDefs.forEach((defs, type) => {\n            if (_USE_RUNTIME_DEPS_TRACKER_FOR_JIT) {\n                _depsTracker.clearScopeCacheFor(type);\n            }\n            defs.forEach((descriptor, prop) => {\n                if (!descriptor) {\n                    // Delete operations are generally undesirable since they have performance\n                    // implications on objects they were applied to. In this particular case, situations\n                    // where this code is invoked should be quite rare to cause any noticeable impact,\n                    // since it's applied only to some test cases (for example when class with no\n                    // annotations extends some @Component) when we need to clear 'ɵcmp' field on a given\n                    // class to restore its original state (before applying overrides and running tests).\n                    delete type[prop];\n                }\n                else {\n                    Object.defineProperty(type, prop, descriptor);\n                }\n            });\n        });\n        this.initialNgDefs.clear();\n        this.scopesWithOverriddenProviders.clear();\n        this.restoreComponentResolutionQueue();\n        // Restore the locale ID to the default value, this shouldn't be necessary but we never know\n        _setLocaleId(_DEFAULT_LOCALE_ID);\n    }\n    compileTestModule() {\n        class RootScopeModule {\n        }\n        _compileNgModuleDefs(RootScopeModule, {\n            providers: [\n                ...this.rootProviderOverrides,\n                _internalProvideZoneChangeDetection({}),\n                TestBedApplicationErrorHandler,\n                { provide: _ChangeDetectionScheduler, useExisting: _ChangeDetectionSchedulerImpl },\n            ],\n        });\n        const providers = [\n            { provide: Compiler, useFactory: () => new R3TestCompiler(this) },\n            { provide: _DEFER_BLOCK_CONFIG, useValue: { behavior: this.deferBlockBehavior } },\n            {\n                provide: _INTERNAL_APPLICATION_ERROR_HANDLER,\n                useFactory: () => {\n                    if (this.rethrowApplicationTickErrors) {\n                        const handler = inject$1(TestBedApplicationErrorHandler);\n                        return (e) => {\n                            handler.handleError(e);\n                        };\n                    }\n                    else {\n                        const userErrorHandler = inject$1(ErrorHandler);\n                        const ngZone = inject$1(NgZone);\n                        return (e) => ngZone.runOutsideAngular(() => userErrorHandler.handleError(e));\n                    }\n                },\n            },\n            ...this.providers,\n            ...this.providerOverrides,\n        ];\n        const imports = [RootScopeModule, this.additionalModuleTypes, this.imports || []];\n        _compileNgModuleDefs(this.testModuleType, {\n            declarations: this.declarations,\n            imports,\n            schemas: this.schemas,\n            providers,\n        }, \n        /* allowDuplicateDeclarationsInRoot */ true);\n        this.applyProviderOverridesInScope(this.testModuleType);\n    }\n    get injector() {\n        if (this._injector !== null) {\n            return this._injector;\n        }\n        const providers = [];\n        const compilerOptions = this.platform.injector.get(COMPILER_OPTIONS, []);\n        compilerOptions.forEach((opts) => {\n            if (opts.providers) {\n                providers.push(opts.providers);\n            }\n        });\n        if (this.compilerProviders !== null) {\n            providers.push(...this.compilerProviders);\n        }\n        this._injector = Injector.create({ providers, parent: this.platform.injector });\n        return this._injector;\n    }\n    // get overrides for a specific provider (if any)\n    getSingleProviderOverrides(provider) {\n        const token = getProviderToken(provider);\n        return this.providerOverridesByToken.get(token) || null;\n    }\n    getProviderOverrides(providers) {\n        if (!providers || !providers.length || this.providerOverridesByToken.size === 0)\n            return [];\n        // There are two flattening operations here. The inner flattenProviders() operates on the\n        // metadata's providers and applies a mapping function which retrieves overrides for each\n        // incoming provider. The outer flatten() then flattens the produced overrides array. If this is\n        // not done, the array can contain other empty arrays (e.g. `[[], []]`) which leak into the\n        // providers array and contaminate any error messages that might be generated.\n        return flatten(flattenProviders(providers, (provider) => this.getSingleProviderOverrides(provider) || []));\n    }\n    getOverriddenProviders(providers) {\n        if (!providers || !providers.length || this.providerOverridesByToken.size === 0)\n            return [];\n        const flattenedProviders = flattenProviders(providers);\n        const overrides = this.getProviderOverrides(flattenedProviders);\n        const overriddenProviders = [...flattenedProviders, ...overrides];\n        const final = [];\n        const seenOverriddenProviders = new Set();\n        // We iterate through the list of providers in reverse order to make sure provider overrides\n        // take precedence over the values defined in provider list. We also filter out all providers\n        // that have overrides, keeping overridden values only. This is needed, since presence of a\n        // provider with `ngOnDestroy` hook will cause this hook to be registered and invoked later.\n        forEachRight(overriddenProviders, (provider) => {\n            const token = getProviderToken(provider);\n            if (this.providerOverridesByToken.has(token)) {\n                if (!seenOverriddenProviders.has(token)) {\n                    seenOverriddenProviders.add(token);\n                    // Treat all overridden providers as `{multi: false}` (even if it's a multi-provider) to\n                    // make sure that provided override takes highest precedence and is not combined with\n                    // other instances of the same multi provider.\n                    final.unshift({ ...provider, multi: false });\n                }\n            }\n            else {\n                final.unshift(provider);\n            }\n        });\n        return final;\n    }\n    hasProviderOverrides(providers) {\n        return this.getProviderOverrides(providers).length > 0;\n    }\n    patchDefWithProviderOverrides(declaration, field) {\n        const def = declaration[field];\n        if (def && def.providersResolver) {\n            this.maybeStoreNgDef(field, declaration);\n            const resolver = def.providersResolver;\n            const processProvidersFn = (providers) => this.getOverriddenProviders(providers);\n            this.storeFieldOfDefOnType(declaration, field, 'providersResolver');\n            def.providersResolver = (ngDef) => resolver(ngDef, processProvidersFn);\n        }\n    }\n}\nfunction initResolvers() {\n    return {\n        module: new NgModuleResolver(),\n        component: new ComponentResolver(),\n        directive: new DirectiveResolver(),\n        pipe: new PipeResolver(),\n    };\n}\nfunction isStandaloneComponent(value) {\n    const def = getComponentDef(value);\n    return !!def?.standalone;\n}\nfunction getComponentDef(value) {\n    return value.ɵcmp ?? null;\n}\nfunction hasNgModuleDef(value) {\n    return value.hasOwnProperty('ɵmod');\n}\nfunction isNgModule(value) {\n    return hasNgModuleDef(value);\n}\nfunction maybeUnwrapFn(maybeFn) {\n    return maybeFn instanceof Function ? maybeFn() : maybeFn;\n}\nfunction flatten(values) {\n    const out = [];\n    values.forEach((value) => {\n        if (Array.isArray(value)) {\n            out.push(...flatten(value));\n        }\n        else {\n            out.push(value);\n        }\n    });\n    return out;\n}\nfunction identityFn(value) {\n    return value;\n}\nfunction flattenProviders(providers, mapFn = identityFn) {\n    const out = [];\n    for (let provider of providers) {\n        if (_isEnvironmentProviders(provider)) {\n            provider = provider.ɵproviders;\n        }\n        if (Array.isArray(provider)) {\n            out.push(...flattenProviders(provider, mapFn));\n        }\n        else {\n            out.push(mapFn(provider));\n        }\n    }\n    return out;\n}\nfunction getProviderField(provider, field) {\n    return provider && typeof provider === 'object' && provider[field];\n}\nfunction getProviderToken(provider) {\n    return getProviderField(provider, 'provide') || provider;\n}\nfunction isModuleWithProviders(value) {\n    return value.hasOwnProperty('ngModule');\n}\nfunction forEachRight(values, fn) {\n    for (let idx = values.length - 1; idx >= 0; idx--) {\n        fn(values[idx], idx);\n    }\n}\nfunction invalidTypeError(name, expectedType) {\n    return new Error(`${name} class doesn't have @${expectedType} decorator or is missing metadata.`);\n}\nclass R3TestCompiler {\n    testBed;\n    constructor(testBed) {\n        this.testBed = testBed;\n    }\n    compileModuleSync(moduleType) {\n        this.testBed._compileNgModuleSync(moduleType);\n        return new _NgModuleFactory(moduleType);\n    }\n    async compileModuleAsync(moduleType) {\n        await this.testBed._compileNgModuleAsync(moduleType);\n        return new _NgModuleFactory(moduleType);\n    }\n    compileModuleAndAllComponentsSync(moduleType) {\n        const ngModuleFactory = this.compileModuleSync(moduleType);\n        const componentFactories = this.testBed._getComponentFactories(moduleType);\n        return new ModuleWithComponentFactories(ngModuleFactory, componentFactories);\n    }\n    async compileModuleAndAllComponentsAsync(moduleType) {\n        const ngModuleFactory = await this.compileModuleAsync(moduleType);\n        const componentFactories = this.testBed._getComponentFactories(moduleType);\n        return new ModuleWithComponentFactories(ngModuleFactory, componentFactories);\n    }\n    clearCache() { }\n    clearCacheFor(type) { }\n    getModuleId(moduleType) {\n        const meta = this.testBed._getModuleResolver().resolve(moduleType);\n        return (meta && meta.id) || undefined;\n    }\n}\n\n// The formatter and CI disagree on how this import statement should be formatted. Both try to keep\n// it on one line, too, which has gotten very hard to read & manage. So disable the formatter for\n// this statement only.\nlet _nextRootElementId = 0;\n/**\n * Returns a singleton of the `TestBed` class.\n *\n * @publicApi\n */\nfunction getTestBed() {\n    return TestBedImpl.INSTANCE;\n}\n/**\n * @description\n * Configures and initializes environment for unit testing and provides methods for\n * creating components and services in unit tests.\n *\n * TestBed is the primary api for writing unit tests for Angular applications and libraries.\n */\nclass TestBedImpl {\n    static _INSTANCE = null;\n    static get INSTANCE() {\n        return (TestBedImpl._INSTANCE = TestBedImpl._INSTANCE || new TestBedImpl());\n    }\n    /**\n     * Teardown options that have been configured at the environment level.\n     * Used as a fallback if no instance-level options have been provided.\n     */\n    static _environmentTeardownOptions;\n    /**\n     * \"Error on unknown elements\" option that has been configured at the environment level.\n     * Used as a fallback if no instance-level option has been provided.\n     */\n    static _environmentErrorOnUnknownElementsOption;\n    /**\n     * \"Error on unknown properties\" option that has been configured at the environment level.\n     * Used as a fallback if no instance-level option has been provided.\n     */\n    static _environmentErrorOnUnknownPropertiesOption;\n    /**\n     * Teardown options that have been configured at the `TestBed` instance level.\n     * These options take precedence over the environment-level ones.\n     */\n    _instanceTeardownOptions;\n    /**\n     * Defer block behavior option that specifies whether defer blocks will be triggered manually\n     * or set to play through.\n     */\n    _instanceDeferBlockBehavior = DEFER_BLOCK_DEFAULT_BEHAVIOR;\n    /**\n     * \"Error on unknown elements\" option that has been configured at the `TestBed` instance level.\n     * This option takes precedence over the environment-level one.\n     */\n    _instanceErrorOnUnknownElementsOption;\n    /**\n     * \"Error on unknown properties\" option that has been configured at the `TestBed` instance level.\n     * This option takes precedence over the environment-level one.\n     */\n    _instanceErrorOnUnknownPropertiesOption;\n    /**\n     * Stores the previous \"Error on unknown elements\" option value,\n     * allowing to restore it in the reset testing module logic.\n     */\n    _previousErrorOnUnknownElementsOption;\n    /**\n     * Stores the previous \"Error on unknown properties\" option value,\n     * allowing to restore it in the reset testing module logic.\n     */\n    _previousErrorOnUnknownPropertiesOption;\n    /**\n     * Initialize the environment for testing with a compiler factory, a PlatformRef, and an\n     * angular module. These are common to every test in the suite.\n     *\n     * This may only be called once, to set up the common providers for the current test\n     * suite on the current platform. If you absolutely need to change the providers,\n     * first use `resetTestEnvironment`.\n     *\n     * Test modules and platforms for individual platforms are available from\n     * '@angular/<platform_name>/testing'.\n     *\n     * @publicApi\n     */\n    static initTestEnvironment(ngModule, platform, options) {\n        const testBed = TestBedImpl.INSTANCE;\n        testBed.initTestEnvironment(ngModule, platform, options);\n        return testBed;\n    }\n    /**\n     * Reset the providers for the test injector.\n     *\n     * @publicApi\n     */\n    static resetTestEnvironment() {\n        TestBedImpl.INSTANCE.resetTestEnvironment();\n    }\n    static configureCompiler(config) {\n        return TestBedImpl.INSTANCE.configureCompiler(config);\n    }\n    /**\n     * Allows overriding default providers, directives, pipes, modules of the test injector,\n     * which are defined in test_injector.js\n     */\n    static configureTestingModule(moduleDef) {\n        return TestBedImpl.INSTANCE.configureTestingModule(moduleDef);\n    }\n    /**\n     * Compile components with a `templateUrl` for the test's NgModule.\n     * It is necessary to call this function\n     * as fetching urls is asynchronous.\n     */\n    static compileComponents() {\n        return TestBedImpl.INSTANCE.compileComponents();\n    }\n    static overrideModule(ngModule, override) {\n        return TestBedImpl.INSTANCE.overrideModule(ngModule, override);\n    }\n    static overrideComponent(component, override) {\n        return TestBedImpl.INSTANCE.overrideComponent(component, override);\n    }\n    static overrideDirective(directive, override) {\n        return TestBedImpl.INSTANCE.overrideDirective(directive, override);\n    }\n    static overridePipe(pipe, override) {\n        return TestBedImpl.INSTANCE.overridePipe(pipe, override);\n    }\n    static overrideTemplate(component, template) {\n        return TestBedImpl.INSTANCE.overrideTemplate(component, template);\n    }\n    /**\n     * Overrides the template of the given component, compiling the template\n     * in the context of the TestingModule.\n     *\n     * Note: This works for JIT and AOTed components as well.\n     */\n    static overrideTemplateUsingTestingModule(component, template) {\n        return TestBedImpl.INSTANCE.overrideTemplateUsingTestingModule(component, template);\n    }\n    static overrideProvider(token, provider) {\n        return TestBedImpl.INSTANCE.overrideProvider(token, provider);\n    }\n    static inject(token, notFoundValue, flags) {\n        return TestBedImpl.INSTANCE.inject(token, notFoundValue, _convertToBitFlags(flags));\n    }\n    /** @deprecated from v9.0.0 use TestBed.inject */\n    static get(token, notFoundValue = Injector.THROW_IF_NOT_FOUND, flags = InjectFlags.Default) {\n        return TestBedImpl.INSTANCE.inject(token, notFoundValue, flags);\n    }\n    /**\n     * Runs the given function in the `EnvironmentInjector` context of `TestBed`.\n     *\n     * @see {@link EnvironmentInjector#runInContext}\n     */\n    static runInInjectionContext(fn) {\n        return TestBedImpl.INSTANCE.runInInjectionContext(fn);\n    }\n    static createComponent(component) {\n        return TestBedImpl.INSTANCE.createComponent(component);\n    }\n    static resetTestingModule() {\n        return TestBedImpl.INSTANCE.resetTestingModule();\n    }\n    static execute(tokens, fn, context) {\n        return TestBedImpl.INSTANCE.execute(tokens, fn, context);\n    }\n    static get platform() {\n        return TestBedImpl.INSTANCE.platform;\n    }\n    static get ngModule() {\n        return TestBedImpl.INSTANCE.ngModule;\n    }\n    static flushEffects() {\n        return TestBedImpl.INSTANCE.flushEffects();\n    }\n    // Properties\n    platform = null;\n    ngModule = null;\n    _compiler = null;\n    _testModuleRef = null;\n    _activeFixtures = [];\n    /**\n     * Internal-only flag to indicate whether a module\n     * scoping queue has been checked and flushed already.\n     * @docs-private\n     */\n    globalCompilationChecked = false;\n    /**\n     * Initialize the environment for testing with a compiler factory, a PlatformRef, and an\n     * angular module. These are common to every test in the suite.\n     *\n     * This may only be called once, to set up the common providers for the current test\n     * suite on the current platform. If you absolutely need to change the providers,\n     * first use `resetTestEnvironment`.\n     *\n     * Test modules and platforms for individual platforms are available from\n     * '@angular/<platform_name>/testing'.\n     *\n     * @publicApi\n     */\n    initTestEnvironment(ngModule, platform, options) {\n        if (this.platform || this.ngModule) {\n            throw new Error('Cannot set base providers because it has already been called');\n        }\n        TestBedImpl._environmentTeardownOptions = options?.teardown;\n        TestBedImpl._environmentErrorOnUnknownElementsOption = options?.errorOnUnknownElements;\n        TestBedImpl._environmentErrorOnUnknownPropertiesOption = options?.errorOnUnknownProperties;\n        this.platform = platform;\n        this.ngModule = ngModule;\n        this._compiler = new TestBedCompiler(this.platform, this.ngModule);\n        // TestBed does not have an API which can reliably detect the start of a test, and thus could be\n        // used to track the state of the NgModule registry and reset it correctly. Instead, when we\n        // know we're in a testing scenario, we disable the check for duplicate NgModule registration\n        // completely.\n        _setAllowDuplicateNgModuleIdsForTest(true);\n    }\n    /**\n     * Reset the providers for the test injector.\n     *\n     * @publicApi\n     */\n    resetTestEnvironment() {\n        this.resetTestingModule();\n        this._compiler = null;\n        this.platform = null;\n        this.ngModule = null;\n        TestBedImpl._environmentTeardownOptions = undefined;\n        _setAllowDuplicateNgModuleIdsForTest(false);\n    }\n    resetTestingModule() {\n        this.checkGlobalCompilationFinished();\n        _resetCompiledComponents();\n        if (this._compiler !== null) {\n            this.compiler.restoreOriginalState();\n        }\n        this._compiler = new TestBedCompiler(this.platform, this.ngModule);\n        // Restore the previous value of the \"error on unknown elements\" option\n        _setUnknownElementStrictMode(this._previousErrorOnUnknownElementsOption ?? THROW_ON_UNKNOWN_ELEMENTS_DEFAULT);\n        // Restore the previous value of the \"error on unknown properties\" option\n        _setUnknownPropertyStrictMode(this._previousErrorOnUnknownPropertiesOption ?? THROW_ON_UNKNOWN_PROPERTIES_DEFAULT);\n        // We have to chain a couple of try/finally blocks, because each step can\n        // throw errors and we don't want it to interrupt the next step and we also\n        // want an error to be thrown at the end.\n        try {\n            this.destroyActiveFixtures();\n        }\n        finally {\n            try {\n                if (this.shouldTearDownTestingModule()) {\n                    this.tearDownTestingModule();\n                }\n            }\n            finally {\n                this._testModuleRef = null;\n                this._instanceTeardownOptions = undefined;\n                this._instanceErrorOnUnknownElementsOption = undefined;\n                this._instanceErrorOnUnknownPropertiesOption = undefined;\n                this._instanceDeferBlockBehavior = DEFER_BLOCK_DEFAULT_BEHAVIOR;\n            }\n        }\n        return this;\n    }\n    configureCompiler(config) {\n        if (config.useJit != null) {\n            throw new Error('JIT compiler is not configurable via TestBed APIs.');\n        }\n        if (config.providers !== undefined) {\n            this.compiler.setCompilerProviders(config.providers);\n        }\n        return this;\n    }\n    configureTestingModule(moduleDef) {\n        this.assertNotInstantiated('TestBed.configureTestingModule', 'configure the test module');\n        // Trigger module scoping queue flush before executing other TestBed operations in a test.\n        // This is needed for the first test invocation to ensure that globally declared modules have\n        // their components scoped properly. See the `checkGlobalCompilationFinished` function\n        // description for additional info.\n        this.checkGlobalCompilationFinished();\n        // Always re-assign the options, even if they're undefined.\n        // This ensures that we don't carry them between tests.\n        this._instanceTeardownOptions = moduleDef.teardown;\n        this._instanceErrorOnUnknownElementsOption = moduleDef.errorOnUnknownElements;\n        this._instanceErrorOnUnknownPropertiesOption = moduleDef.errorOnUnknownProperties;\n        this._instanceDeferBlockBehavior = moduleDef.deferBlockBehavior ?? DEFER_BLOCK_DEFAULT_BEHAVIOR;\n        // Store the current value of the strict mode option,\n        // so we can restore it later\n        this._previousErrorOnUnknownElementsOption = _getUnknownElementStrictMode();\n        _setUnknownElementStrictMode(this.shouldThrowErrorOnUnknownElements());\n        this._previousErrorOnUnknownPropertiesOption = _getUnknownPropertyStrictMode();\n        _setUnknownPropertyStrictMode(this.shouldThrowErrorOnUnknownProperties());\n        this.compiler.configureTestingModule(moduleDef);\n        return this;\n    }\n    compileComponents() {\n        return this.compiler.compileComponents();\n    }\n    inject(token, notFoundValue, flags) {\n        if (token === TestBed) {\n            return this;\n        }\n        const UNDEFINED = {};\n        const result = this.testModuleRef.injector.get(token, UNDEFINED, _convertToBitFlags(flags));\n        return result === UNDEFINED\n            ? this.compiler.injector.get(token, notFoundValue, flags)\n            : result;\n    }\n    /** @deprecated from v9.0.0 use TestBed.inject */\n    get(token, notFoundValue = Injector.THROW_IF_NOT_FOUND, flags = InjectFlags.Default) {\n        return this.inject(token, notFoundValue, flags);\n    }\n    runInInjectionContext(fn) {\n        return runInInjectionContext(this.inject(EnvironmentInjector), fn);\n    }\n    execute(tokens, fn, context) {\n        const params = tokens.map((t) => this.inject(t));\n        return fn.apply(context, params);\n    }\n    overrideModule(ngModule, override) {\n        this.assertNotInstantiated('overrideModule', 'override module metadata');\n        this.compiler.overrideModule(ngModule, override);\n        return this;\n    }\n    overrideComponent(component, override) {\n        this.assertNotInstantiated('overrideComponent', 'override component metadata');\n        this.compiler.overrideComponent(component, override);\n        return this;\n    }\n    overrideTemplateUsingTestingModule(component, template) {\n        this.assertNotInstantiated('TestBed.overrideTemplateUsingTestingModule', 'Cannot override template when the test module has already been instantiated');\n        this.compiler.overrideTemplateUsingTestingModule(component, template);\n        return this;\n    }\n    overrideDirective(directive, override) {\n        this.assertNotInstantiated('overrideDirective', 'override directive metadata');\n        this.compiler.overrideDirective(directive, override);\n        return this;\n    }\n    overridePipe(pipe, override) {\n        this.assertNotInstantiated('overridePipe', 'override pipe metadata');\n        this.compiler.overridePipe(pipe, override);\n        return this;\n    }\n    /**\n     * Overwrites all providers for the given token with the given provider definition.\n     */\n    overrideProvider(token, provider) {\n        this.assertNotInstantiated('overrideProvider', 'override provider');\n        this.compiler.overrideProvider(token, provider);\n        return this;\n    }\n    overrideTemplate(component, template) {\n        return this.overrideComponent(component, { set: { template, templateUrl: null } });\n    }\n    createComponent(type) {\n        const testComponentRenderer = this.inject(TestComponentRenderer);\n        const rootElId = `root${_nextRootElementId++}`;\n        testComponentRenderer.insertRootElement(rootElId);\n        if (_getAsyncClassMetadataFn(type)) {\n            throw new Error(`Component '${type.name}' has unresolved metadata. ` +\n                `Please call \\`await TestBed.compileComponents()\\` before running this test.`);\n        }\n        const componentDef = type.ɵcmp;\n        if (!componentDef) {\n            throw new Error(`It looks like '${_stringify(type)}' has not been compiled.`);\n        }\n        const componentFactory = new _Render3ComponentFactory(componentDef);\n        const initComponent = () => {\n            const componentRef = componentFactory.create(Injector.NULL, [], `#${rootElId}`, this.testModuleRef);\n            return this.runInInjectionContext(() => new ComponentFixture(componentRef));\n        };\n        const noNgZone = this.inject(ComponentFixtureNoNgZone, false);\n        const ngZone = noNgZone ? null : this.inject(NgZone, null);\n        const fixture = ngZone ? ngZone.run(initComponent) : initComponent();\n        this._activeFixtures.push(fixture);\n        return fixture;\n    }\n    /**\n     * @internal strip this from published d.ts files due to\n     * https://github.com/microsoft/TypeScript/issues/36216\n     */\n    get compiler() {\n        if (this._compiler === null) {\n            throw new Error(`Need to call TestBed.initTestEnvironment() first`);\n        }\n        return this._compiler;\n    }\n    /**\n     * @internal strip this from published d.ts files due to\n     * https://github.com/microsoft/TypeScript/issues/36216\n     */\n    get testModuleRef() {\n        if (this._testModuleRef === null) {\n            this._testModuleRef = this.compiler.finalize();\n        }\n        return this._testModuleRef;\n    }\n    assertNotInstantiated(methodName, methodDescription) {\n        if (this._testModuleRef !== null) {\n            throw new Error(`Cannot ${methodDescription} when the test module has already been instantiated. ` +\n                `Make sure you are not using \\`inject\\` before \\`${methodName}\\`.`);\n        }\n    }\n    /**\n     * Check whether the module scoping queue should be flushed, and flush it if needed.\n     *\n     * When the TestBed is reset, it clears the JIT module compilation queue, cancelling any\n     * in-progress module compilation. This creates a potential hazard - the very first time the\n     * TestBed is initialized (or if it's reset without being initialized), there may be pending\n     * compilations of modules declared in global scope. These compilations should be finished.\n     *\n     * To ensure that globally declared modules have their components scoped properly, this function\n     * is called whenever TestBed is initialized or reset. The _first_ time that this happens, prior\n     * to any other operations, the scoping queue is flushed.\n     */\n    checkGlobalCompilationFinished() {\n        // Checking _testNgModuleRef is null should not be necessary, but is left in as an additional\n        // guard that compilations queued in tests (after instantiation) are never flushed accidentally.\n        if (!this.globalCompilationChecked && this._testModuleRef === null) {\n            _flushModuleScopingQueueAsMuchAsPossible();\n        }\n        this.globalCompilationChecked = true;\n    }\n    destroyActiveFixtures() {\n        let errorCount = 0;\n        this._activeFixtures.forEach((fixture) => {\n            try {\n                fixture.destroy();\n            }\n            catch (e) {\n                errorCount++;\n                console.error('Error during cleanup of component', {\n                    component: fixture.componentInstance,\n                    stacktrace: e,\n                });\n            }\n        });\n        this._activeFixtures = [];\n        if (errorCount > 0 && this.shouldRethrowTeardownErrors()) {\n            throw Error(`${errorCount} ${errorCount === 1 ? 'component' : 'components'} ` +\n                `threw errors during cleanup`);\n        }\n    }\n    shouldRethrowTeardownErrors() {\n        const instanceOptions = this._instanceTeardownOptions;\n        const environmentOptions = TestBedImpl._environmentTeardownOptions;\n        // If the new teardown behavior hasn't been configured, preserve the old behavior.\n        if (!instanceOptions && !environmentOptions) {\n            return TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT;\n        }\n        // Otherwise use the configured behavior or default to rethrowing.\n        return (instanceOptions?.rethrowErrors ??\n            environmentOptions?.rethrowErrors ??\n            this.shouldTearDownTestingModule());\n    }\n    shouldThrowErrorOnUnknownElements() {\n        // Check if a configuration has been provided to throw when an unknown element is found\n        return (this._instanceErrorOnUnknownElementsOption ??\n            TestBedImpl._environmentErrorOnUnknownElementsOption ??\n            THROW_ON_UNKNOWN_ELEMENTS_DEFAULT);\n    }\n    shouldThrowErrorOnUnknownProperties() {\n        // Check if a configuration has been provided to throw when an unknown property is found\n        return (this._instanceErrorOnUnknownPropertiesOption ??\n            TestBedImpl._environmentErrorOnUnknownPropertiesOption ??\n            THROW_ON_UNKNOWN_PROPERTIES_DEFAULT);\n    }\n    shouldTearDownTestingModule() {\n        return (this._instanceTeardownOptions?.destroyAfterEach ??\n            TestBedImpl._environmentTeardownOptions?.destroyAfterEach ??\n            TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT);\n    }\n    getDeferBlockBehavior() {\n        return this._instanceDeferBlockBehavior;\n    }\n    tearDownTestingModule() {\n        // If the module ref has already been destroyed, we won't be able to get a test renderer.\n        if (this._testModuleRef === null) {\n            return;\n        }\n        // Resolve the renderer ahead of time, because we want to remove the root elements as the very\n        // last step, but the injector will be destroyed as a part of the module ref destruction.\n        const testRenderer = this.inject(TestComponentRenderer);\n        try {\n            this._testModuleRef.destroy();\n        }\n        catch (e) {\n            if (this.shouldRethrowTeardownErrors()) {\n                throw e;\n            }\n            else {\n                console.error('Error during cleanup of a testing module', {\n                    component: this._testModuleRef.instance,\n                    stacktrace: e,\n                });\n            }\n        }\n        finally {\n            testRenderer.removeAllRootElements?.();\n        }\n    }\n    /**\n     * Execute any pending effects.\n     *\n     * @developerPreview\n     */\n    flushEffects() {\n        this.inject(_MicrotaskEffectScheduler).flush();\n        this.inject(_EffectScheduler).flush();\n    }\n}\n/**\n * @description\n * Configures and initializes environment for unit testing and provides methods for\n * creating components and services in unit tests.\n *\n * `TestBed` is the primary api for writing unit tests for Angular applications and libraries.\n *\n * @publicApi\n */\nconst TestBed = TestBedImpl;\n/**\n * Allows injecting dependencies in `beforeEach()` and `it()`. Note: this function\n * (imported from the `@angular/core/testing` package) can **only** be used to inject dependencies\n * in tests. To inject dependencies in your application code, use the [`inject`](api/core/inject)\n * function from the `@angular/core` package instead.\n *\n * Example:\n *\n * ```ts\n * beforeEach(inject([Dependency, AClass], (dep, object) => {\n *   // some code that uses `dep` and `object`\n *   // ...\n * }));\n *\n * it('...', inject([AClass], (object) => {\n *   object.doSomething();\n *   expect(...);\n * })\n * ```\n *\n * @publicApi\n */\nfunction inject(tokens, fn) {\n    const testBed = TestBedImpl.INSTANCE;\n    // Not using an arrow function to preserve context passed from call site\n    return function () {\n        return testBed.execute(tokens, fn, this);\n    };\n}\n/**\n * @publicApi\n */\nclass InjectSetupWrapper {\n    _moduleDef;\n    constructor(_moduleDef) {\n        this._moduleDef = _moduleDef;\n    }\n    _addModule() {\n        const moduleDef = this._moduleDef();\n        if (moduleDef) {\n            TestBedImpl.configureTestingModule(moduleDef);\n        }\n    }\n    inject(tokens, fn) {\n        const self = this;\n        // Not using an arrow function to preserve context passed from call site\n        return function () {\n            self._addModule();\n            return inject(tokens, fn).call(this);\n        };\n    }\n}\nfunction withModule(moduleDef, fn) {\n    if (fn) {\n        // Not using an arrow function to preserve context passed from call site\n        return function () {\n            const testBed = TestBedImpl.INSTANCE;\n            if (moduleDef) {\n                testBed.configureTestingModule(moduleDef);\n            }\n            return fn.apply(this);\n        };\n    }\n    return new InjectSetupWrapper(() => moduleDef);\n}\n\n/**\n * Public Test Library for unit testing Angular applications. Assumes that you are running\n * with Jasmine, Mocha, or a similar framework which exports a beforeEach function and\n * allows tests to be asynchronous by either returning a promise or using a 'done' parameter.\n */\n// Reset the test providers and the fake async zone before each test.\n// We keep a guard because somehow this file can make it into a bundle and be executed\n// beforeEach is only defined when executing the tests\nglobalThis.beforeEach?.(getCleanupHook(false));\n// We provide both a `beforeEach` and `afterEach`, because the updated behavior for\n// tearing down the module is supposed to run after the test so that we can associate\n// teardown errors with the correct test.\n// We keep a guard because somehow this file can make it into a bundle and be executed\n// afterEach is only defined when executing the tests\nglobalThis.afterEach?.(getCleanupHook(true));\nfunction getCleanupHook(expectedTeardownValue) {\n    return () => {\n        const testBed = TestBedImpl.INSTANCE;\n        if (testBed.shouldTearDownTestingModule() === expectedTeardownValue) {\n            testBed.resetTestingModule();\n            resetFakeAsyncZoneIfExists();\n        }\n    };\n}\n/**\n * This API should be removed. But doing so seems to break `google3` and so it requires a bit of\n * investigation.\n *\n * A work around is to mark it as `@codeGenApi` for now and investigate later.\n *\n * @codeGenApi\n */\n// TODO(iminar): Remove this code in a safe way.\nconst __core_private_testing_placeholder__ = '';\n\n/**\n * Fake implementation of user agent history and navigation behavior. This is a\n * high-fidelity implementation of browser behavior that attempts to emulate\n * things like traversal delay.\n */\nclass FakeNavigation {\n    /**\n     * The fake implementation of an entries array. Only same-document entries\n     * allowed.\n     */\n    entriesArr = [];\n    /**\n     * The current active entry index into `entriesArr`.\n     */\n    currentEntryIndex = 0;\n    /**\n     * The current navigate event.\n     * @internal\n     */\n    navigateEvent = null;\n    /**\n     * A Map of pending traversals, so that traversals to the same entry can be\n     * re-used.\n     */\n    traversalQueue = new Map();\n    /**\n     * A Promise that resolves when the previous traversals have finished. Used to\n     * simulate the cross-process communication necessary for traversals.\n     */\n    nextTraversal = Promise.resolve();\n    /**\n     * A prospective current active entry index, which includes unresolved\n     * traversals. Used by `go` to determine where navigations are intended to go.\n     */\n    prospectiveEntryIndex = 0;\n    /**\n     * A test-only option to make traversals synchronous, rather than emulate\n     * cross-process communication.\n     */\n    synchronousTraversals = false;\n    /** Whether to allow a call to setInitialEntryForTesting. */\n    canSetInitialEntry = true;\n    /**\n     * `EventTarget` to dispatch events.\n     * @internal\n     */\n    eventTarget;\n    /** The next unique id for created entries. Replace recreates this id. */\n    nextId = 0;\n    /** The next unique key for created entries. Replace inherits this id. */\n    nextKey = 0;\n    /** Whether this fake is disposed. */\n    disposed = false;\n    /** Equivalent to `navigation.currentEntry`. */\n    get currentEntry() {\n        return this.entriesArr[this.currentEntryIndex];\n    }\n    get canGoBack() {\n        return this.currentEntryIndex > 0;\n    }\n    get canGoForward() {\n        return this.currentEntryIndex < this.entriesArr.length - 1;\n    }\n    createEventTarget;\n    _window;\n    get window() {\n        return this._window;\n    }\n    constructor(doc, startURL) {\n        this.createEventTarget = () => {\n            try {\n                // `document.createElement` because NodeJS `EventTarget` is\n                // incompatible with Domino's `Event`. That is, attempting to\n                // dispatch an event created by Domino's patched `Event` will\n                // throw an error since it is not an instance of a real Node\n                // `Event`.\n                return doc.createElement('div');\n            }\n            catch {\n                // Fallback to a basic EventTarget if `document.createElement`\n                // fails. This can happen with tests that pass in a value for document\n                // that is stubbed.\n                return new EventTarget();\n            }\n        };\n        this._window = document.defaultView ?? this.createEventTarget();\n        this.eventTarget = this.createEventTarget();\n        // First entry.\n        this.setInitialEntryForTesting(startURL);\n    }\n    /**\n     * Sets the initial entry.\n     */\n    setInitialEntryForTesting(url, options = { historyState: null }) {\n        if (!this.canSetInitialEntry) {\n            throw new Error('setInitialEntryForTesting can only be called before any ' + 'navigation has occurred');\n        }\n        const currentInitialEntry = this.entriesArr[0];\n        this.entriesArr[0] = new FakeNavigationHistoryEntry(this.eventTarget, new URL(url).toString(), {\n            index: 0,\n            key: currentInitialEntry?.key ?? String(this.nextKey++),\n            id: currentInitialEntry?.id ?? String(this.nextId++),\n            sameDocument: true,\n            historyState: options?.historyState,\n            state: options.state,\n        });\n    }\n    /** Returns whether the initial entry is still eligible to be set. */\n    canSetInitialEntryForTesting() {\n        return this.canSetInitialEntry;\n    }\n    /**\n     * Sets whether to emulate traversals as synchronous rather than\n     * asynchronous.\n     */\n    setSynchronousTraversalsForTesting(synchronousTraversals) {\n        this.synchronousTraversals = synchronousTraversals;\n    }\n    /** Equivalent to `navigation.entries()`. */\n    entries() {\n        return this.entriesArr.slice();\n    }\n    /** Equivalent to `navigation.navigate()`. */\n    navigate(url, options) {\n        const fromUrl = new URL(this.currentEntry.url);\n        const toUrl = new URL(url, this.currentEntry.url);\n        let navigationType;\n        if (!options?.history || options.history === 'auto') {\n            // Auto defaults to push, but if the URLs are the same, is a replace.\n            if (fromUrl.toString() === toUrl.toString()) {\n                navigationType = 'replace';\n            }\n            else {\n                navigationType = 'push';\n            }\n        }\n        else {\n            navigationType = options.history;\n        }\n        const hashChange = isHashChange(fromUrl, toUrl);\n        const destination = new FakeNavigationDestination({\n            url: toUrl.toString(),\n            state: options?.state,\n            sameDocument: hashChange,\n            historyState: null,\n        });\n        const result = new InternalNavigationResult(this);\n        const intercepted = this.userAgentNavigate(destination, result, {\n            navigationType,\n            cancelable: true,\n            canIntercept: true,\n            // Always false for navigate().\n            userInitiated: false,\n            hashChange,\n            info: options?.info,\n        });\n        if (!intercepted) {\n            this.updateNavigationEntriesForSameDocumentNavigation(this.navigateEvent);\n        }\n        return {\n            committed: result.committed,\n            finished: result.finished,\n        };\n    }\n    /** Equivalent to `history.pushState()`. */\n    pushState(data, title, url) {\n        this.pushOrReplaceState('push', data, title, url);\n    }\n    /** Equivalent to `history.replaceState()`. */\n    replaceState(data, title, url) {\n        this.pushOrReplaceState('replace', data, title, url);\n    }\n    pushOrReplaceState(navigationType, data, _title, url) {\n        const fromUrl = new URL(this.currentEntry.url);\n        const toUrl = url ? new URL(url, this.currentEntry.url) : fromUrl;\n        const hashChange = isHashChange(fromUrl, toUrl);\n        const destination = new FakeNavigationDestination({\n            url: toUrl.toString(),\n            sameDocument: true,\n            historyState: data,\n        });\n        const result = new InternalNavigationResult(this);\n        const intercepted = this.userAgentNavigate(destination, result, {\n            navigationType,\n            cancelable: true,\n            canIntercept: true,\n            // Always false for pushState() or replaceState().\n            userInitiated: false,\n            hashChange,\n        });\n        if (intercepted) {\n            return;\n        }\n        this.updateNavigationEntriesForSameDocumentNavigation(this.navigateEvent);\n    }\n    /** Equivalent to `navigation.traverseTo()`. */\n    traverseTo(key, options) {\n        const fromUrl = new URL(this.currentEntry.url);\n        const entry = this.findEntry(key);\n        if (!entry) {\n            const domException = new DOMException('Invalid key', 'InvalidStateError');\n            const committed = Promise.reject(domException);\n            const finished = Promise.reject(domException);\n            committed.catch(() => { });\n            finished.catch(() => { });\n            return {\n                committed,\n                finished,\n            };\n        }\n        if (entry === this.currentEntry) {\n            return {\n                committed: Promise.resolve(this.currentEntry),\n                finished: Promise.resolve(this.currentEntry),\n            };\n        }\n        if (this.traversalQueue.has(entry.key)) {\n            const existingResult = this.traversalQueue.get(entry.key);\n            return {\n                committed: existingResult.committed,\n                finished: existingResult.finished,\n            };\n        }\n        const hashChange = isHashChange(fromUrl, new URL(entry.url, this.currentEntry.url));\n        const destination = new FakeNavigationDestination({\n            url: entry.url,\n            state: entry.getState(),\n            historyState: entry.getHistoryState(),\n            key: entry.key,\n            id: entry.id,\n            index: entry.index,\n            sameDocument: entry.sameDocument,\n        });\n        this.prospectiveEntryIndex = entry.index;\n        const result = new InternalNavigationResult(this);\n        this.traversalQueue.set(entry.key, result);\n        this.runTraversal(() => {\n            this.traversalQueue.delete(entry.key);\n            const intercepted = this.userAgentNavigate(destination, result, {\n                navigationType: 'traverse',\n                cancelable: true,\n                canIntercept: true,\n                // Always false for traverseTo().\n                userInitiated: false,\n                hashChange,\n                info: options?.info,\n            });\n            if (!intercepted) {\n                this.userAgentTraverse(this.navigateEvent);\n            }\n        });\n        return {\n            committed: result.committed,\n            finished: result.finished,\n        };\n    }\n    /** Equivalent to `navigation.back()`. */\n    back(options) {\n        if (this.currentEntryIndex === 0) {\n            const domException = new DOMException('Cannot go back', 'InvalidStateError');\n            const committed = Promise.reject(domException);\n            const finished = Promise.reject(domException);\n            committed.catch(() => { });\n            finished.catch(() => { });\n            return {\n                committed,\n                finished,\n            };\n        }\n        const entry = this.entriesArr[this.currentEntryIndex - 1];\n        return this.traverseTo(entry.key, options);\n    }\n    /** Equivalent to `navigation.forward()`. */\n    forward(options) {\n        if (this.currentEntryIndex === this.entriesArr.length - 1) {\n            const domException = new DOMException('Cannot go forward', 'InvalidStateError');\n            const committed = Promise.reject(domException);\n            const finished = Promise.reject(domException);\n            committed.catch(() => { });\n            finished.catch(() => { });\n            return {\n                committed,\n                finished,\n            };\n        }\n        const entry = this.entriesArr[this.currentEntryIndex + 1];\n        return this.traverseTo(entry.key, options);\n    }\n    /**\n     * Equivalent to `history.go()`.\n     * Note that this method does not actually work precisely to how Chrome\n     * does, instead choosing a simpler model with less unexpected behavior.\n     * Chrome has a few edge case optimizations, for instance with repeated\n     * `back(); forward()` chains it collapses certain traversals.\n     */\n    go(direction) {\n        const targetIndex = this.prospectiveEntryIndex + direction;\n        if (targetIndex >= this.entriesArr.length || targetIndex < 0) {\n            return;\n        }\n        this.prospectiveEntryIndex = targetIndex;\n        this.runTraversal(() => {\n            // Check again that destination is in the entries array.\n            if (targetIndex >= this.entriesArr.length || targetIndex < 0) {\n                return;\n            }\n            const fromUrl = new URL(this.currentEntry.url);\n            const entry = this.entriesArr[targetIndex];\n            const hashChange = isHashChange(fromUrl, new URL(entry.url, this.currentEntry.url));\n            const destination = new FakeNavigationDestination({\n                url: entry.url,\n                state: entry.getState(),\n                historyState: entry.getHistoryState(),\n                key: entry.key,\n                id: entry.id,\n                index: entry.index,\n                sameDocument: entry.sameDocument,\n            });\n            const result = new InternalNavigationResult(this);\n            const intercepted = this.userAgentNavigate(destination, result, {\n                navigationType: 'traverse',\n                cancelable: true,\n                canIntercept: true,\n                // Always false for go().\n                userInitiated: false,\n                hashChange,\n            });\n            if (!intercepted) {\n                this.userAgentTraverse(this.navigateEvent);\n            }\n        });\n    }\n    /** Runs a traversal synchronously or asynchronously */\n    runTraversal(traversal) {\n        if (this.synchronousTraversals) {\n            traversal();\n            return;\n        }\n        // Each traversal occupies a single timeout resolution.\n        // This means that Promises added to commit and finish should resolve\n        // before the next traversal.\n        this.nextTraversal = this.nextTraversal.then(() => {\n            return new Promise((resolve) => {\n                setTimeout(() => {\n                    resolve();\n                    traversal();\n                });\n            });\n        });\n    }\n    /** Equivalent to `navigation.addEventListener()`. */\n    addEventListener(type, callback, options) {\n        this.eventTarget.addEventListener(type, callback, options);\n    }\n    /** Equivalent to `navigation.removeEventListener()`. */\n    removeEventListener(type, callback, options) {\n        this.eventTarget.removeEventListener(type, callback, options);\n    }\n    /** Equivalent to `navigation.dispatchEvent()` */\n    dispatchEvent(event) {\n        return this.eventTarget.dispatchEvent(event);\n    }\n    /** Cleans up resources. */\n    dispose() {\n        // Recreate eventTarget to release current listeners.\n        this.eventTarget = this.createEventTarget();\n        this.disposed = true;\n    }\n    /** Returns whether this fake is disposed. */\n    isDisposed() {\n        return this.disposed;\n    }\n    /**\n     * Implementation for all navigations and traversals.\n     * @returns true if the event was intercepted, otherwise false\n     */\n    userAgentNavigate(destination, result, options) {\n        // The first navigation should disallow any future calls to set the initial\n        // entry.\n        this.canSetInitialEntry = false;\n        if (this.navigateEvent) {\n            this.navigateEvent.cancel(new DOMException('Navigation was aborted', 'AbortError'));\n            this.navigateEvent = null;\n        }\n        return dispatchNavigateEvent({\n            navigationType: options.navigationType,\n            cancelable: options.cancelable,\n            canIntercept: options.canIntercept,\n            userInitiated: options.userInitiated,\n            hashChange: options.hashChange,\n            signal: result.signal,\n            destination,\n            info: options.info,\n            sameDocument: destination.sameDocument,\n            result,\n        });\n    }\n    /**\n     * Implementation for a push or replace navigation.\n     * https://whatpr.org/html/10919/browsing-the-web.html#url-and-history-update-steps\n     * https://whatpr.org/html/10919/nav-history-apis.html#update-the-navigation-api-entries-for-a-same-document-navigation\n     * @internal\n     */\n    urlAndHistoryUpdateSteps(navigateEvent) {\n        this.updateNavigationEntriesForSameDocumentNavigation(navigateEvent);\n    }\n    /**\n     * Implementation for a traverse navigation.\n     *\n     * https://whatpr.org/html/10919/browsing-the-web.html#apply-the-traverse-history-step\n     * ...\n     * > Let updateDocument be an algorithm step which performs update document for history step application given targetEntry's document, targetEntry, changingNavigableContinuation's update-only, scriptHistoryLength, scriptHistoryIndex, navigationType, entriesForNavigationAPI, and previousEntry.\n     * > If targetEntry's document is equal to displayedDocument, then perform updateDocument.\n     * https://whatpr.org/html/10919/browsing-the-web.html#update-document-for-history-step-application\n     * which then goes to https://whatpr.org/html/10919/nav-history-apis.html#update-the-navigation-api-entries-for-a-same-document-navigation\n     * @internal\n     */\n    userAgentTraverse(navigateEvent) {\n        const oldUrl = this.currentEntry.url;\n        this.updateNavigationEntriesForSameDocumentNavigation(navigateEvent);\n        // Happens as part of \"updating the document\" steps https://whatpr.org/html/10919/browsing-the-web.html#updating-the-document\n        const popStateEvent = createPopStateEvent({\n            state: navigateEvent.destination.getHistoryState(),\n        });\n        this._window.dispatchEvent(popStateEvent);\n        if (navigateEvent.hashChange) {\n            const hashchangeEvent = createHashChangeEvent(oldUrl, this.currentEntry.url);\n            this._window.dispatchEvent(hashchangeEvent);\n        }\n    }\n    /**\n     * https://whatpr.org/html/10919/nav-history-apis.html#update-the-navigation-api-entries-for-a-same-document-navigation\n     * @internal\n     */\n    updateNavigationEntriesForSameDocumentNavigation({ destination, navigationType, result, }) {\n        const oldCurrentNHE = this.currentEntry;\n        const disposedNHEs = [];\n        if (navigationType === 'traverse') {\n            this.currentEntryIndex = destination.index;\n            if (this.currentEntryIndex === -1) {\n                throw new Error('unexpected current entry index');\n            }\n        }\n        else if (navigationType === 'push') {\n            this.currentEntryIndex++;\n            this.prospectiveEntryIndex = this.currentEntryIndex; // prospectiveEntryIndex isn't in the spec but is an implementation detail\n            disposedNHEs.push(...this.entriesArr.splice(this.currentEntryIndex));\n        }\n        else if (navigationType === 'replace') {\n            disposedNHEs.push(oldCurrentNHE);\n        }\n        if (navigationType === 'push' || navigationType === 'replace') {\n            const index = this.currentEntryIndex;\n            const key = navigationType === 'push' ? String(this.nextKey++) : this.currentEntry.key;\n            const newNHE = new FakeNavigationHistoryEntry(this.eventTarget, destination.url, {\n                id: String(this.nextId++),\n                key,\n                index,\n                sameDocument: true,\n                state: destination.getState(),\n                historyState: destination.getHistoryState(),\n            });\n            this.entriesArr[this.currentEntryIndex] = newNHE;\n        }\n        result.committedResolve(this.currentEntry);\n        const currentEntryChangeEvent = createFakeNavigationCurrentEntryChangeEvent({\n            from: oldCurrentNHE,\n            navigationType: navigationType,\n        });\n        this.eventTarget.dispatchEvent(currentEntryChangeEvent);\n        for (const disposedNHE of disposedNHEs) {\n            disposedNHE.dispose();\n        }\n    }\n    /** Utility method for finding entries with the given `key`. */\n    findEntry(key) {\n        for (const entry of this.entriesArr) {\n            if (entry.key === key)\n                return entry;\n        }\n        return undefined;\n    }\n    set onnavigate(\n    // tslint:disable-next-line:no-any\n    _handler) {\n        throw new Error('unimplemented');\n    }\n    // tslint:disable-next-line:no-any\n    get onnavigate() {\n        throw new Error('unimplemented');\n    }\n    set oncurrententrychange(_handler) {\n        throw new Error('unimplemented');\n    }\n    get oncurrententrychange() {\n        throw new Error('unimplemented');\n    }\n    set onnavigatesuccess(\n    // tslint:disable-next-line:no-any\n    _handler) {\n        throw new Error('unimplemented');\n    }\n    // tslint:disable-next-line:no-any\n    get onnavigatesuccess() {\n        throw new Error('unimplemented');\n    }\n    set onnavigateerror(\n    // tslint:disable-next-line:no-any\n    _handler) {\n        throw new Error('unimplemented');\n    }\n    // tslint:disable-next-line:no-any\n    get onnavigateerror() {\n        throw new Error('unimplemented');\n    }\n    _transition = null;\n    /** @internal */\n    set transition(t) {\n        this._transition = t;\n    }\n    get transition() {\n        return this._transition;\n    }\n    updateCurrentEntry(_options) {\n        throw new Error('unimplemented');\n    }\n    reload(_options) {\n        throw new Error('unimplemented');\n    }\n}\n/**\n * Fake equivalent of `NavigationHistoryEntry`.\n */\nclass FakeNavigationHistoryEntry {\n    eventTarget;\n    url;\n    sameDocument;\n    id;\n    key;\n    index;\n    state;\n    historyState;\n    // tslint:disable-next-line:no-any\n    ondispose = null;\n    constructor(eventTarget, url, { id, key, index, sameDocument, state, historyState, }) {\n        this.eventTarget = eventTarget;\n        this.url = url;\n        this.id = id;\n        this.key = key;\n        this.index = index;\n        this.sameDocument = sameDocument;\n        this.state = state;\n        this.historyState = historyState;\n    }\n    getState() {\n        // Budget copy.\n        return this.state ? JSON.parse(JSON.stringify(this.state)) : this.state;\n    }\n    getHistoryState() {\n        // Budget copy.\n        return this.historyState\n            ? JSON.parse(JSON.stringify(this.historyState))\n            : this.historyState;\n    }\n    addEventListener(type, callback, options) {\n        this.eventTarget.addEventListener(type, callback, options);\n    }\n    removeEventListener(type, callback, options) {\n        this.eventTarget.removeEventListener(type, callback, options);\n    }\n    dispatchEvent(event) {\n        return this.eventTarget.dispatchEvent(event);\n    }\n    /** internal */\n    dispose() {\n        const disposeEvent = new Event('disposed');\n        this.dispatchEvent(disposeEvent);\n        // release current listeners\n        this.eventTarget = null;\n    }\n}\n/**\n * Create a fake equivalent of `NavigateEvent`. This is not a class because ES5\n * transpiled JavaScript cannot extend native Event.\n *\n * https://html.spec.whatwg.org/multipage/nav-history-apis.html#navigate-event-firing\n */\nfunction dispatchNavigateEvent({ cancelable, canIntercept, userInitiated, hashChange, navigationType, signal, destination, info, sameDocument, result, }) {\n    const { navigation } = result;\n    const event = new Event('navigate', { bubbles: false, cancelable });\n    event.focusResetBehavior = null;\n    event.scrollBehavior = null;\n    event.interceptionState = 'none';\n    event.canIntercept = canIntercept;\n    event.userInitiated = userInitiated;\n    event.hashChange = hashChange;\n    event.navigationType = navigationType;\n    event.signal = signal;\n    event.destination = destination;\n    event.info = info;\n    event.downloadRequest = null;\n    event.formData = null;\n    event.result = result;\n    event.sameDocument = sameDocument;\n    let precommitHandlers = [];\n    let handlers = [];\n    // https://whatpr.org/html/10919/nav-history-apis.html#dom-navigateevent-intercept\n    event.intercept = function (options) {\n        if (!this.canIntercept) {\n            throw new DOMException(`Cannot intercept when canIntercept is 'false'`, 'SecurityError');\n        }\n        this.interceptionState = 'intercepted';\n        event.sameDocument = true;\n        const precommitHandler = options?.precommitHandler;\n        if (precommitHandler) {\n            if (!this.cancelable) {\n                throw new DOMException(`Cannot use precommitHandler when cancelable is 'false'`, 'InvalidStateError');\n            }\n            precommitHandlers.push(precommitHandler);\n        }\n        if (event.interceptionState !== 'none' && event.interceptionState !== 'intercepted') {\n            throw new Error('Event interceptionState should be \"none\" or \"intercepted\"');\n        }\n        event.interceptionState = 'intercepted';\n        const handler = options?.handler;\n        if (handler) {\n            handlers.push(handler);\n        }\n        // override old options with new ones. UA _may_ report a console warning if new options differ from previous\n        event.focusResetBehavior = options?.focusReset ?? event.focusResetBehavior;\n        event.scrollBehavior = options?.scroll ?? event.scrollBehavior;\n    };\n    // https://whatpr.org/html/10919/nav-history-apis.html#dom-navigateevent-scroll\n    event.scroll = function () {\n        if (event.interceptionState !== 'committed') {\n            throw new DOMException(`Failed to execute 'scroll' on 'NavigateEvent': scroll() must be ` +\n                `called after commit() and interception options must specify manual scroll.`, 'InvalidStateError');\n        }\n        processScrollBehavior(event);\n    };\n    // https://whatpr.org/html/10919/nav-history-apis.html#dom-navigationprecommitcontroller-redirect\n    function redirect(url) {\n        if (event.interceptionState === 'none') {\n            throw new Error('cannot redirect when event is not intercepted');\n        }\n        if (event.interceptionState !== 'intercepted') {\n            throw new DOMException(`cannot redirect when event is not in 'intercepted' state`, 'InvalidStateError');\n        }\n        if (event.navigationType !== 'push' && event.navigationType !== 'replace') {\n            throw new DOMException(`cannot redirect when navigationType is not 'push' or 'replace`, 'InvalidStateError');\n        }\n        const toUrl = new URL(url, navigation.currentEntry.url);\n        event.destination.url = toUrl.href;\n    }\n    // https://whatpr.org/html/10919/nav-history-apis.html#inner-navigate-event-firing-algorithm\n    // \"Let commit be the following steps:\"\n    function commit() {\n        if (result.signal.aborted) {\n            return;\n        }\n        if (event.interceptionState !== 'none') {\n            event.interceptionState = 'committed';\n            if (!navigation.currentEntry) {\n                throw new Error('from history entry should not be null');\n            }\n            navigation.transition = new InternalNavigationTransition(navigation.currentEntry, navigationType);\n            switch (event.navigationType) {\n                case 'push':\n                case 'replace': {\n                    navigation.urlAndHistoryUpdateSteps(event);\n                    break;\n                }\n                case 'reload': {\n                    navigation.updateNavigationEntriesForSameDocumentNavigation(event);\n                    break;\n                }\n                case 'traverse': {\n                    navigation.userAgentTraverse(event);\n                    break;\n                }\n            }\n        }\n        const promisesList = handlers.map((handler) => handler());\n        if (promisesList.length === 0) {\n            promisesList.push(Promise.resolve());\n        }\n        Promise.all(promisesList)\n            .then(() => {\n            // Follows steps outlined under \"Wait for all of promisesList, with the following success steps:\"\n            // in the spec https://html.spec.whatwg.org/multipage/nav-history-apis.html#navigate-event-firing.\n            if (result.signal.aborted) {\n                return;\n            }\n            if (event !== navigation.navigateEvent) {\n                throw new Error(\"Navigation's ongoing event not equal to resolved event\");\n            }\n            navigation.navigateEvent = null;\n            finishNavigationEvent(event, true);\n            const navigatesuccessEvent = new Event('navigatesuccess', { bubbles: false, cancelable });\n            navigation.eventTarget.dispatchEvent(navigatesuccessEvent);\n            result.finishedResolve();\n            if (navigation.transition !== null) {\n                navigation.transition.finishedResolve();\n            }\n            navigation.transition = null;\n        })\n            .catch((reason) => event.cancel(reason));\n    }\n    // Internal only.\n    // https://whatpr.org/html/10919/nav-history-apis.html#inner-navigate-event-firing-algorithm\n    // \"Let cancel be the following steps given reason\"\n    event.cancel = function (reason) {\n        if (result.signal.aborted) {\n            return;\n        }\n        if (event !== navigation.navigateEvent) {\n            throw new Error(\"Navigation's ongoing event not equal to resolved event\");\n        }\n        navigation.navigateEvent = null;\n        if (event.interceptionState !== 'intercepted') {\n            finishNavigationEvent(event, false);\n        }\n        const navigateerrorEvent = new Event('navigateerror', { bubbles: false, cancelable });\n        navigation.eventTarget.dispatchEvent(navigateerrorEvent);\n        result.finishedReject(reason);\n        if (navigation.transition !== null) {\n            navigation.transition.finishedReject(reason);\n        }\n        navigation.transition = null;\n    };\n    function dispatch() {\n        navigation.navigateEvent = event;\n        navigation.eventTarget.dispatchEvent(event);\n        if (precommitHandlers.length === 0) {\n            commit();\n        }\n        else {\n            const precommitController = { redirect };\n            const precommitPromisesList = precommitHandlers.map((handler) => handler(precommitController));\n            Promise.all(precommitPromisesList)\n                .then(() => commit())\n                .catch((reason) => event.cancel(reason));\n        }\n    }\n    dispatch();\n    return event.interceptionState !== 'none';\n}\n/** https://whatpr.org/html/10919/nav-history-apis.html#navigateevent-finish */\nfunction finishNavigationEvent(event, didFulfill) {\n    if (event.interceptionState === 'finished') {\n        throw new Error('Attempting to finish navigation event that was already finished');\n    }\n    if (event.interceptionState === 'intercepted') {\n        if (didFulfill === true) {\n            throw new Error('didFulfill should be false');\n        }\n        // assert precommit handlers is not empty\n        event.interceptionState = 'finished';\n        return;\n    }\n    if (event.interceptionState === 'none') {\n        return;\n    }\n    potentiallyResetFocus(event);\n    if (didFulfill) {\n        potentiallyResetScroll(event);\n    }\n    event.interceptionState = 'finished';\n}\n/** https://whatpr.org/html/10919/nav-history-apis.html#potentially-reset-the-focus */\nfunction potentiallyResetFocus(event) {\n    if (event.interceptionState !== 'committed' && event.interceptionState !== 'scrolled') {\n        throw new Error('cannot reset focus if navigation event is not committed or scrolled');\n    }\n    // TODO(atscott): The rest of the steps\n}\nfunction potentiallyResetScroll(event) {\n    if (event.interceptionState !== 'committed' && event.interceptionState !== 'scrolled') {\n        throw new Error('cannot reset scroll if navigation event is not committed or scrolled');\n    }\n    if (event.interceptionState === 'scrolled' || event.scrollBehavior === 'manual') {\n        return;\n    }\n    processScrollBehavior(event);\n}\n/* https://whatpr.org/html/10919/nav-history-apis.html#process-scroll-behavior */\nfunction processScrollBehavior(event) {\n    if (event.interceptionState !== 'committed') {\n        throw new Error('invalid event interception state when processing scroll behavior');\n    }\n    event.interceptionState = 'scrolled';\n    // TODO(atscott): the rest of the steps\n}\n/**\n * Create a fake equivalent of `NavigationCurrentEntryChange`. This does not use\n * a class because ES5 transpiled JavaScript cannot extend native Event.\n */\nfunction createFakeNavigationCurrentEntryChangeEvent({ from, navigationType, }) {\n    const event = new Event('currententrychange', {\n        bubbles: false,\n        cancelable: false,\n    });\n    event.from = from;\n    event.navigationType = navigationType;\n    return event;\n}\n/**\n * Create a fake equivalent of `PopStateEvent`. This does not use a class\n * because ES5 transpiled JavaScript cannot extend native Event.\n */\nfunction createPopStateEvent({ state }) {\n    const event = new Event('popstate', {\n        bubbles: false,\n        cancelable: false,\n    });\n    event.state = state;\n    return event;\n}\nfunction createHashChangeEvent(newURL, oldURL) {\n    const event = new Event('hashchange', {\n        bubbles: false,\n        cancelable: false,\n    });\n    event.newURL = newURL;\n    event.oldURL = oldURL;\n    return event;\n}\n/**\n * Fake equivalent of `NavigationDestination`.\n */\nclass FakeNavigationDestination {\n    url;\n    sameDocument;\n    key;\n    id;\n    index;\n    state;\n    historyState;\n    constructor({ url, sameDocument, historyState, state, key = null, id = null, index = -1, }) {\n        this.url = url;\n        this.sameDocument = sameDocument;\n        this.state = state;\n        this.historyState = historyState;\n        this.key = key;\n        this.id = id;\n        this.index = index;\n    }\n    getState() {\n        return this.state;\n    }\n    getHistoryState() {\n        return this.historyState;\n    }\n}\n/** Utility function to determine whether two UrlLike have the same hash. */\nfunction isHashChange(from, to) {\n    return (to.hash !== from.hash &&\n        to.hostname === from.hostname &&\n        to.pathname === from.pathname &&\n        to.search === from.search);\n}\nclass InternalNavigationTransition {\n    from;\n    navigationType;\n    finished;\n    finishedResolve;\n    finishedReject;\n    constructor(from, navigationType) {\n        this.from = from;\n        this.navigationType = navigationType;\n        this.finished = new Promise((resolve, reject) => {\n            this.finishedReject = reject;\n            this.finishedResolve = resolve;\n        });\n        // All rejections are handled.\n        this.finished.catch(() => { });\n    }\n}\n/**\n * Internal utility class for representing the result of a navigation.\n * Generally equivalent to the \"apiMethodTracker\" in the spec.\n */\nclass InternalNavigationResult {\n    navigation;\n    committedTo = null;\n    committedResolve;\n    committedReject;\n    finishedResolve;\n    finishedReject;\n    committed;\n    finished;\n    get signal() {\n        return this.abortController.signal;\n    }\n    abortController = new AbortController();\n    constructor(navigation) {\n        this.navigation = navigation;\n        this.committed = new Promise((resolve, reject) => {\n            this.committedResolve = (entry) => {\n                this.committedTo = entry;\n                resolve(entry);\n            };\n            this.committedReject = reject;\n        });\n        this.finished = new Promise(async (resolve, reject) => {\n            this.finishedResolve = () => {\n                if (this.committedTo === null) {\n                    throw new Error('NavigateEvent should have been committed before resolving finished promise.');\n                }\n                resolve(this.committedTo);\n            };\n            this.finishedReject = (reason) => {\n                reject(reason);\n                this.abortController.abort(reason);\n            };\n        });\n        // All rejections are handled.\n        this.committed.catch(() => { });\n        this.finished.catch(() => { });\n    }\n}\n\nclass Log {\n    logItems;\n    constructor() {\n        this.logItems = [];\n    }\n    add(value) {\n        this.logItems.push(value);\n    }\n    fn(value) {\n        return () => {\n            this.logItems.push(value);\n        };\n    }\n    clear() {\n        this.logItems = [];\n    }\n    result() {\n        return this.logItems.join('; ');\n    }\n    static ɵfac = function Log_Factory(__ngFactoryType__) { return new (__ngFactoryType__ || Log)(); };\n    static ɵprov = /*@__PURE__*/ i0.ɵɵdefineInjectable({ token: Log, factory: Log.ɵfac });\n}\n(() => { (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Log, [{\n        type: Injectable\n    }], () => [], null); })();\n\nexport { ComponentFixture, ComponentFixtureAutoDetect, ComponentFixtureNoNgZone, DeferBlockFixture, InjectSetupWrapper, TestBed, TestComponentRenderer, __core_private_testing_placeholder__, discardPeriodicTasks, fakeAsync, flush, flushMicrotasks, getTestBed, inject, resetFakeAsyncZone, tick, waitForAsync, withModule, FakeNavigation as ɵFakeNavigation, Log as ɵLog, MetadataOverrider as ɵMetadataOverrider };\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,YAAY,EAAEC,UAAU,EAAEC,gBAAgB,IAAIC,gBAAgB,EAAEC,uBAAuB,IAAIC,uBAAuB,EAAEC,sBAAsB,IAAIC,sBAAsB,EAAEC,wBAAwB,IAAIC,wBAAwB,EAAEC,eAAe,IAAIC,eAAe,EAAEC,cAAc,EAAEC,mBAAmB,IAAIC,mBAAmB,EAAEC,WAAW,IAAIC,WAAW,EAAEC,cAAc,EAAEC,qBAAqB,IAAIC,qBAAqB,EAAEC,iBAAiB,IAAIC,iBAAiB,EAAEC,yBAAyB,IAAIC,yBAAyB,EAAEC,gBAAgB,IAAIC,gBAAgB,EAAEC,yBAAyB,IAAIC,yBAAyB,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,UAAU,IAAIC,UAAU,EAAEC,IAAI,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,uBAAuB,IAAIC,uBAAuB,EAAEC,iCAAiC,IAAIC,iCAAiC,EAAEC,YAAY,IAAIC,YAAY,EAAEC,iBAAiB,IAAIC,iBAAiB,EAAEC,iBAAiB,EAAEC,gCAAgC,IAAIC,gCAAgC,EAAEC,wBAAwB,IAAIC,wBAAwB,EAAEC,0BAA0B,IAAIC,0BAA0B,EAAEC,mBAAmB,IAAIC,mBAAmB,EAAEC,qBAAqB,EAAEC,SAAS,EAAEC,kBAAkB,IAAIC,kBAAkB,EAAEC,YAAY,IAAIC,YAAY,EAAEC,wBAAwB,IAAIC,wBAAwB,EAAEC,YAAY,IAAIC,YAAY,EAAEC,iBAAiB,IAAIC,iBAAiB,EAAEC,WAAW,IAAIC,WAAW,EAAEC,iBAAiB,IAAIC,iBAAiB,EAAEC,YAAY,IAAIC,YAAY,EAAEC,YAAY,IAAIC,YAAY,EAAEC,WAAW,IAAIC,WAAW,EAAEC,2BAA2B,IAAIC,2BAA2B,EAAEC,WAAW,IAAIC,WAAW,EAAEC,oBAAoB,IAAIC,oBAAoB,EAAEC,yCAAyC,IAAIC,yCAAyC,EAAEC,gCAAgC,IAAIC,gCAAgC,EAAEC,mCAAmC,IAAIC,mCAAmC,EAAEC,6BAA6B,IAAIC,6BAA6B,EAAEC,QAAQ,EAAEC,mBAAmB,IAAIC,mBAAmB,EAAEC,mCAAmC,IAAIC,mCAAmC,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEC,oBAAoB,IAAIC,oBAAoB,EAAEC,sCAAsC,IAAIC,sCAAsC,EAAEC,gBAAgB,IAAIC,gBAAgB,EAAEC,4BAA4B,EAAEC,uBAAuB,IAAIC,uBAAuB,EAAEC,kBAAkB,IAAIC,kBAAkB,EAAEC,WAAW,EAAEC,oCAAoC,IAAIC,oCAAoC,EAAEC,wBAAwB,IAAIC,wBAAwB,EAAEC,4BAA4B,IAAIC,4BAA4B,EAAEC,6BAA6B,IAAIC,6BAA6B,EAAEC,4BAA4B,IAAIC,4BAA4B,EAAEC,6BAA6B,IAAIC,6BAA6B,EAAEC,qBAAqB,EAAEC,mBAAmB,EAAEC,wCAAwC,IAAIC,wCAAwC,QAAQ,eAAe;AACx6F,SAAS9G,mBAAmB,IAAI+G,kBAAkB,EAAE1H,gBAAgB,IAAI2H,eAAe,QAAQ,eAAe;AAC9G,SAASC,YAAY,QAAQ,MAAM;AACnC,SAASC,cAAc,QAAQ,mBAAmB;;AAElD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,EAAE,EAAE;EACtB,MAAMC,KAAK,GAAG,OAAOC,IAAI,KAAK,WAAW,GAAGA,IAAI,GAAG,IAAI;EACvD,IAAI,CAACD,KAAK,EAAE;IACR,OAAO,YAAY;MACf,OAAOE,OAAO,CAACC,MAAM,CAAC,4EAA4E,GAC9F,yDAAyD,CAAC;IAClE,CAAC;EACL;EACA,MAAMC,SAAS,GAAGJ,KAAK,IAAIA,KAAK,CAACA,KAAK,CAACK,UAAU,CAAC,WAAW,CAAC,CAAC;EAC/D,IAAI,OAAOD,SAAS,KAAK,UAAU,EAAE;IACjC,OAAOA,SAAS,CAACL,EAAE,CAAC;EACxB;EACA,OAAO,YAAY;IACf,OAAOG,OAAO,CAACC,MAAM,CAAC,gFAAgF,GAClG,iEAAiE,CAAC;EAC1E,CAAC;AACL;AAEA,MAAMG,kCAAkC,GAAG,IAAI;AAC/C,MAAMC,8BAA8B,CAAC;EACjCC,IAAI,GAAG5I,QAAQ,CAACC,MAAM,CAAC;EACvB4I,gBAAgB,GAAG7I,QAAQ,CAACE,YAAY,CAAC;EACzC4I,yBAAyB,GAAG,IAAIC,GAAG,CAAC,CAAC;EACrCC,WAAWA,CAACC,CAAC,EAAE;IACX,IAAI;MACA,IAAI,CAACL,IAAI,CAACM,iBAAiB,CAAC,MAAM,IAAI,CAACL,gBAAgB,CAACG,WAAW,CAACC,CAAC,CAAC,CAAC;IAC3E,CAAC,CACD,OAAOE,SAAS,EAAE;MACdF,CAAC,GAAGE,SAAS;IACjB;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACL,yBAAyB,CAACM,IAAI,GAAG,CAAC,EAAE;MACzC,KAAK,MAAMjB,EAAE,IAAI,IAAI,CAACW,yBAAyB,CAACO,MAAM,CAAC,CAAC,EAAE;QACtDlB,EAAE,CAACc,CAAC,CAAC;MACT;MACA,IAAI,CAACH,yBAAyB,CAACQ,KAAK,CAAC,CAAC;IAC1C,CAAC,MACI;MACD,MAAML,CAAC;IACX;EACJ;EACA,OAAOM,IAAI,GAAG,SAASC,sCAAsCA,CAACC,iBAAiB,EAAE;IAAE,OAAO,KAAKA,iBAAiB,IAAId,8BAA8B,EAAE,CAAC;EAAE,CAAC;EACxJ,OAAOe,KAAK,GAAG,aAAc5J,EAAE,CAAC6J,kBAAkB,CAAC;IAAEC,KAAK,EAAEjB,8BAA8B;IAAEkB,OAAO,EAAElB,8BAA8B,CAACY;EAAK,CAAC,CAAC;AAC/I;AACA,CAAC,MAAM;EAAE,CAAC,OAAOO,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKhK,EAAE,CAACiK,iBAAiB,CAACpB,8BAA8B,EAAE,CAAC;IAC1GqB,IAAI,EAAE7J;EACV,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;AAAE,CAAC,EAAE,CAAC;;AAEzB;AACA;AACA;AACA;AACA;AACA,MAAM8J,iBAAiB,CAAC;EACpBC,KAAK;EACLC,gBAAgB;EAChB;EACAC,WAAWA,CAACF,KAAK,EAAEC,gBAAgB,EAAE;IACjC,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;EAC5C;EACA;AACJ;AACA;AACA;EACUE,MAAMA,CAACC,KAAK,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAChB,IAAI,CAACC,gBAAgB,CAACH,KAAK,EAAEC,KAAI,CAACL,KAAK,CAAC,EAAE;QACtC,MAAMQ,aAAa,GAAGC,8BAA8B,CAACL,KAAK,CAAC;QAC3D,MAAM,IAAIM,KAAK,CAAC,6CAA6CF,aAAa,YAAY,GAClF,qBAAqBA,aAAa,CAACG,WAAW,CAAC,CAAC,+BAA+B,CAAC;MACxF;MACA,IAAIP,KAAK,KAAKjK,gBAAgB,CAACyK,QAAQ,EAAE;QACrC,MAAMvK,uBAAuB,CAACgK,KAAI,CAACL,KAAK,CAACa,QAAQ,EAAER,KAAI,CAACL,KAAK,CAACc,KAAK,EAAET,KAAI,CAACL,KAAK,CAACe,KAAK,CAAC;MAC1F;MACA;MACA;MACA,MAAMC,mBAAmB,GAAG,IAAI;MAChCzK,sBAAsB,CAAC6J,KAAK,EAAEC,KAAI,CAACL,KAAK,CAACe,KAAK,EAAEV,KAAI,CAACL,KAAK,CAACiB,UAAU,EAAED,mBAAmB,CAAC;MAC3FX,KAAI,CAACJ,gBAAgB,CAACiB,aAAa,CAAC,CAAC;IAAC;EAC1C;EACA;AACJ;AACA;AACA;EACIC,cAAcA,CAAA,EAAG;IACb,MAAMC,WAAW,GAAG,EAAE;IACtB;IACA;IACA;IACA,MAAMC,kBAAkB,GAAG,EAAE;IAC7B,IAAI,IAAI,CAACrB,KAAK,CAACiB,UAAU,CAACK,MAAM,IAAI7K,wBAAwB,EAAE;MAC1D,MAAMqK,KAAK,GAAG,IAAI,CAACd,KAAK,CAACiB,UAAU,CAACxK,wBAAwB,CAAC;MAC7DE,eAAe,CAACmK,KAAK,EAAEM,WAAW,CAAC;MACnC,KAAK,MAAMpB,KAAK,IAAIoB,WAAW,EAAE;QAC7BC,kBAAkB,CAACE,IAAI,CAAC,IAAIxB,iBAAiB,CAACC,KAAK,EAAE,IAAI,CAACC,gBAAgB,CAAC,CAAC;MAChF;IACJ;IACA,OAAO7B,OAAO,CAACoD,OAAO,CAACH,kBAAkB,CAAC;EAC9C;AACJ;AACA,SAASd,gBAAgBA,CAACH,KAAK,EAAEJ,KAAK,EAAE;EACpC,QAAQI,KAAK;IACT,KAAKjK,gBAAgB,CAACsL,WAAW;MAC7B,OAAOzB,KAAK,CAACa,QAAQ,CAACa,oBAAoB,KAAK,IAAI;IACvD,KAAKvL,gBAAgB,CAACwL,OAAO;MACzB,OAAO3B,KAAK,CAACa,QAAQ,CAACe,gBAAgB,KAAK,IAAI;IACnD,KAAKzL,gBAAgB,CAACuK,KAAK;MACvB,OAAOV,KAAK,CAACa,QAAQ,CAACgB,cAAc,KAAK,IAAI;IACjD,KAAK1L,gBAAgB,CAACyK,QAAQ;MAC1B,OAAO,IAAI;IACf;MACI,OAAO,KAAK;EACpB;AACJ;AACA,SAASH,8BAA8BA,CAACL,KAAK,EAAE;EAC3C,QAAQA,KAAK;IACT,KAAKjK,gBAAgB,CAACsL,WAAW;MAC7B,OAAO,aAAa;IACxB,KAAKtL,gBAAgB,CAACwL,OAAO;MACzB,OAAO,SAAS;IACpB,KAAKxL,gBAAgB,CAACuK,KAAK;MACvB,OAAO,OAAO;IAClB;MACI,OAAO,MAAM;EACrB;AACJ;;AAEA;AACA,MAAMoB,0CAA0C,GAAG,IAAI;AACvD;AACA,MAAMC,iCAAiC,GAAG,KAAK;AAC/C;AACA,MAAMC,mCAAmC,GAAG,KAAK;AACjD;AACA,MAAMC,4BAA4B,GAAGnL,mBAAmB,CAACoL,WAAW;AACpE;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,CAAC;EACxBC,iBAAiBA,CAACC,aAAa,EAAE,CAAE;EACnCC,qBAAqBA,CAAA,EAAG,CAAE;AAC9B;AACA;AACA;AACA;AACA,MAAMC,0BAA0B,GAAG,IAAI3L,cAAc,CAAC,4BAA4B,CAAC;AACnF;AACA;AACA;AACA,MAAM4L,wBAAwB,GAAG,IAAI5L,cAAc,CAAC,0BAA0B,CAAC;;AAE/E;AACA;AACA;AACA;AACA;AACA,MAAM6L,gBAAgB,CAAC;EACnBC,YAAY;EACZ;AACJ;AACA;EACIC,YAAY;EACZ;AACJ;AACA;EACIC,iBAAiB;EACjB;AACJ;AACA;EACIC,aAAa;EACb;AACJ;AACA;EACIC,UAAU;EACV;AACJ;AACA;EACIC,iBAAiB;EACjBC,SAAS;EACTC,YAAY,GAAG,KAAK;EACpB;EACAC,kBAAkB,GAAGpN,QAAQ,CAAC0M,wBAAwB,EAAE;IAAEW,QAAQ,EAAE;EAAK,CAAC,CAAC;EAC3E;EACAC,OAAO,GAAG,IAAI,CAACF,kBAAkB,GAAG,IAAIlM,WAAW,CAAC,CAAC,GAAGlB,QAAQ,CAACC,MAAM,CAAC;EACxE;EACA;EACA;EACA;EACA;EACA;EACA;EACAsN,OAAO,GAAGvN,QAAQ,CAACmB,cAAc,CAAC;EAClCqM,WAAW,GAAG,IAAI,CAACD,OAAO;EAC1BE,YAAY,GAAGzN,QAAQ,CAACqB,qBAAqB,CAAC;EAC9CqM,eAAe,GAAG1N,QAAQ,CAAC2I,8BAA8B,CAAC;EAC1DgF,eAAe,GAAG3N,QAAQ,CAACuB,iBAAiB,CAAC;EAC7CqM,SAAS,GAAG5N,QAAQ,CAACyB,yBAAyB,CAAC;EAC/CoM,mBAAmB,GAAG7N,QAAQ,CAAC2B,gBAAgB,CAAC;EAChDmM,wBAAwB,GAAG9N,QAAQ,CAAC6B,yBAAyB,CAAC;EAC9DkM,iBAAiB,GAAG,IAAI,CAACJ,eAAe,GAAG,IAAI,GAAG,KAAK;EACvDK,UAAU,GAAGhO,QAAQ,CAACyM,0BAA0B,EAAE;IAAEY,QAAQ,EAAE;EAAK,CAAC,CAAC,IAAI,IAAI,CAACU,iBAAiB;EAC/FE,aAAa,GAAG,IAAIjG,YAAY,CAAC,CAAC;EAClC;EACAkG,MAAM,GAAG,IAAI,CAACd,kBAAkB,GAAG,IAAI,GAAG,IAAI,CAACE,OAAO;EACtD;EACAlD,WAAWA,CAACwC,YAAY,EAAE;IACtB,IAAI,CAACA,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACK,iBAAiB,GAAGL,YAAY,CAACK,iBAAiB;IACvD,IAAI,CAACD,UAAU,GAAGJ,YAAY,CAACuB,QAAQ;IACvC,IAAI,CAACtB,YAAY,GAAG/K,YAAY,CAAC,IAAI,CAACkL,UAAU,CAACD,aAAa,CAAC;IAC/D,IAAI,CAACD,iBAAiB,GAAGF,YAAY,CAACwB,QAAQ;IAC9C,IAAI,CAACrB,aAAa,GAAG,IAAI,CAACC,UAAU,CAACD,aAAa;IAClD,IAAI,CAACH,YAAY,GAAGA,YAAY;IAChC,IAAI,IAAI,CAACoB,UAAU,EAAE;MACjB,IAAI,CAACR,WAAW,CAACa,iBAAiB,CAACC,GAAG,CAAC,IAAI,CAAC1B,YAAY,CAAC2B,QAAQ,CAAC;MAClE,IAAI,CAACX,SAAS,EAAEY,MAAM,CAAC,CAAC,CAAC,sCAAsC,CAAC;MAChE,IAAI,CAACZ,SAAS,EAAEY,MAAM,CAAC,CAAC,CAAC,mDAAmD,CAAC;IACjF;IACA,IAAI,CAAC5B,YAAY,CAAC2B,QAAQ,CAACE,SAAS,CAAC,MAAM;MACvC,IAAI,CAACjB,WAAW,CAACa,iBAAiB,CAACK,MAAM,CAAC,IAAI,CAAC9B,YAAY,CAAC2B,QAAQ,CAAC;IACzE,CAAC,CAAC;IACF;IACA;IACA,IAAI,CAACjB,OAAO,CAACpE,iBAAiB,CAAC,MAAM;MACjC,IAAI,CAAC+E,aAAa,CAACK,GAAG,CAAC,IAAI,CAAChB,OAAO,CAACqB,OAAO,CAACC,SAAS,CAAC;QAClDC,IAAI,EAAGC,KAAK,IAAK;UACb,MAAMA,KAAK;QACf;MACJ,CAAC,CAAC,CAAC;IACP,CAAC,CAAC;EACN;EACA;AACJ;AACA;EACI1D,aAAaA,CAAC2D,cAAc,GAAG,IAAI,EAAE;IACjC,IAAI,CAACjB,wBAAwB,CAACkB,KAAK,CAAC,CAAC;IACrC,MAAMC,sBAAsB,GAAG,IAAI,CAACrC,YAAY,CAACK,iBAAiB,CAAC8B,cAAc;IACjF,IAAI;MACA,IAAI,CAACA,cAAc,EAAE;QACjB,IAAI,CAACnC,YAAY,CAACK,iBAAiB,CAAC8B,cAAc,GAAG,MAAM,CAAE,CAAC;MAClE;MACA,IAAI,IAAI,CAACpB,eAAe,EAAE;QACtB,IAAI;UACA,IAAI,CAACH,WAAW,CAACa,iBAAiB,CAACC,GAAG,CAAC,IAAI,CAAC1B,YAAY,CAAC2B,QAAQ,CAAC;UAClE,IAAI,CAAChB,OAAO,CAAC2B,IAAI,CAAC,CAAC;QACvB,CAAC,SACO;UACJ,IAAI,CAAC,IAAI,CAAClB,UAAU,EAAE;YAClB,IAAI,CAACR,WAAW,CAACa,iBAAiB,CAACK,MAAM,CAAC,IAAI,CAAC9B,YAAY,CAAC2B,QAAQ,CAAC;UACzE;QACJ;MACJ,CAAC,MACI;QACD;QACA;QACA,IAAI,CAACjB,OAAO,CAAC6B,GAAG,CAAC,MAAM;UACnB;UACA,IAAI,CAACtB,mBAAmB,CAACmB,KAAK,CAAC,CAAC;UAChC,IAAI,CAAC/B,iBAAiB,CAAC7B,aAAa,CAAC,CAAC;UACtC,IAAI,CAAC2D,cAAc,CAAC,CAAC;QACzB,CAAC,CAAC;MACN;IACJ,CAAC,SACO;MACJ,IAAI,CAACnC,YAAY,CAACK,iBAAiB,CAAC8B,cAAc,GAAGE,sBAAsB;IAC/E;IACA,IAAI,CAACnB,wBAAwB,CAACkB,KAAK,CAAC,CAAC;EACzC;EACA;AACJ;AACA;EACID,cAAcA,CAAA,EAAG;IACb,IAAI,CAAC9B,iBAAiB,CAAC8B,cAAc,CAAC,CAAC;EAC3C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIK,iBAAiBA,CAACpB,UAAU,GAAG,IAAI,EAAE;IACjC,IAAI,IAAI,CAACZ,kBAAkB,IAAI,CAAC,IAAI,CAACO,eAAe,EAAE;MAClD,MAAM,IAAI/C,KAAK,CAAC,qEAAqE,CAAC;IAC1F;IACA,IAAIoD,UAAU,KAAK,IAAI,CAACA,UAAU,EAAE;MAChC,IAAIA,UAAU,EAAE;QACZ,IAAI,CAACR,WAAW,CAACa,iBAAiB,CAACC,GAAG,CAAC,IAAI,CAAC1B,YAAY,CAAC2B,QAAQ,CAAC;MACtE,CAAC,MACI;QACD,IAAI,CAACf,WAAW,CAACa,iBAAiB,CAACK,MAAM,CAAC,IAAI,CAAC9B,YAAY,CAAC2B,QAAQ,CAAC;MACzE;IACJ;IACA,IAAI,CAACP,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAAC5C,aAAa,CAAC,CAAC;EACxB;EACA;AACJ;AACA;AACA;EACIiE,QAAQA,CAAA,EAAG;IACP,OAAO,CAAC,IAAI,CAAC5B,YAAY,CAAC6B,eAAe,CAACC,KAAK;EACnD;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAACH,QAAQ,CAAC,CAAC,EAAE;MACjB,OAAO/G,OAAO,CAACoD,OAAO,CAAC,KAAK,CAAC;IACjC;IACA,OAAO,IAAIpD,OAAO,CAAC,CAACoD,OAAO,EAAEnD,MAAM,KAAK;MACpC,IAAI,CAACmF,eAAe,CAAC5E,yBAAyB,CAACwF,GAAG,CAAC/F,MAAM,CAAC;MAC1D,IAAI,CAACgF,OAAO,CAACiC,UAAU,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QACjC,IAAI,CAAC/B,eAAe,CAAC5E,yBAAyB,CAAC4F,MAAM,CAACnG,MAAM,CAAC;QAC7DmD,OAAO,CAAC,IAAI,CAAC;MACjB,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;AACJ;AACA;EACIL,cAAcA,CAAA,EAAG;IACb,MAAMC,WAAW,GAAG,EAAE;IACtB,MAAMN,KAAK,GAAG,IAAI,CAAC4B,YAAY,CAAC2B,QAAQ,CAAC,QAAQ,CAAC;IAClD1N,eAAe,CAACmK,KAAK,EAAEM,WAAW,CAAC;IACnC,MAAMC,kBAAkB,GAAG,EAAE;IAC7B,KAAK,MAAMrB,KAAK,IAAIoB,WAAW,EAAE;MAC7BC,kBAAkB,CAACE,IAAI,CAAC,IAAIxB,iBAAiB,CAACC,KAAK,EAAE,IAAI,CAAC,CAAC;IAC/D;IACA,OAAO5B,OAAO,CAACoD,OAAO,CAACH,kBAAkB,CAAC;EAC9C;EACAmE,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACxC,SAAS,KAAKyC,SAAS,EAAE;MAC9B,IAAI,CAACzC,SAAS,GAAG,IAAI,CAACN,YAAY,CAACgD,QAAQ,CAACC,GAAG,CAAC9N,gBAAgB,EAAE,IAAI,CAAC;IAC3E;IACA,OAAO,IAAI,CAACmL,SAAS;EACzB;EACA;AACJ;AACA;EACI4C,iBAAiBA,CAAA,EAAG;IAChB,MAAMC,QAAQ,GAAG,IAAI,CAACL,YAAY,CAAC,CAAC;IACpC,IAAIK,QAAQ,IAAIA,QAAQ,CAACD,iBAAiB,EAAE;MACxC,OAAOC,QAAQ,CAACD,iBAAiB,CAAC,CAAC;IACvC;IACA,OAAO,IAAI,CAACN,UAAU,CAAC,CAAC;EAC5B;EACA;AACJ;AACA;EACIQ,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC/B,aAAa,CAACgC,WAAW,CAAC,CAAC;IAChC,IAAI,CAACzC,WAAW,CAACa,iBAAiB,CAACK,MAAM,CAAC,IAAI,CAAC9B,YAAY,CAAC2B,QAAQ,CAAC;IACrE,IAAI,CAAC,IAAI,CAACpB,YAAY,EAAE;MACpB,IAAI,CAACP,YAAY,CAACoD,OAAO,CAAC,CAAC;MAC3B,IAAI,CAAC7C,YAAY,GAAG,IAAI;IAC5B;EACJ;AACJ;AAEA,MAAM/E,KAAK,GAAG,OAAOC,IAAI,KAAK,WAAW,GAAGA,IAAI,GAAG,IAAI;AACvD,MAAM6H,mBAAmB,GAAG9H,KAAK,IAAIA,KAAK,CAACA,KAAK,CAACK,UAAU,CAAC,eAAe,CAAC,CAAC;AAC7E,MAAM0H,wCAAwC,GAAG;AACjD,wEAAwE;AACxE;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAAA,EAAG;EAC1B,IAAIF,mBAAmB,EAAE;IACrB,OAAOA,mBAAmB,CAACE,kBAAkB,CAAC,CAAC;EACnD;EACA,MAAM,IAAIxF,KAAK,CAACuF,wCAAwC,CAAC;AAC7D;AACA,SAASE,0BAA0BA,CAAA,EAAG;EAClC,IAAIH,mBAAmB,IAAI7H,IAAI,CAAC,eAAe,CAAC,EAAEiI,QAAQ,CAAC,CAAC,EAAE;IAC1DJ,mBAAmB,CAACE,kBAAkB,CAAC,CAAC;EAC5C;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,SAASA,CAACpI,EAAE,EAAEqI,OAAO,EAAE;EAC5B,IAAIN,mBAAmB,EAAE;IACrB,OAAOA,mBAAmB,CAACK,SAAS,CAACpI,EAAE,EAAEqI,OAAO,CAAC;EACrD;EACA,MAAM,IAAI5F,KAAK,CAACuF,wCAAwC,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASjB,IAAIA,CAACuB,MAAM,GAAG,CAAC,EAAEC,WAAW,GAAG;EACpCC,iCAAiC,EAAE;AACvC,CAAC,EAAE;EACC,IAAIT,mBAAmB,EAAE;IACrB,OAAOA,mBAAmB,CAAChB,IAAI,CAACuB,MAAM,EAAEC,WAAW,CAAC;EACxD;EACA,MAAM,IAAI9F,KAAK,CAACuF,wCAAwC,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASnB,KAAKA,CAAC4B,QAAQ,EAAE;EACrB,IAAIV,mBAAmB,EAAE;IACrB,OAAOA,mBAAmB,CAAClB,KAAK,CAAC4B,QAAQ,CAAC;EAC9C;EACA,MAAM,IAAIhG,KAAK,CAACuF,wCAAwC,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA,SAASU,oBAAoBA,CAAA,EAAG;EAC5B,IAAIX,mBAAmB,EAAE;IACrB,OAAOA,mBAAmB,CAACW,oBAAoB,CAAC,CAAC;EACrD;EACA,MAAM,IAAIjG,KAAK,CAACuF,wCAAwC,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA,SAASW,eAAeA,CAAA,EAAG;EACvB,IAAIZ,mBAAmB,EAAE;IACrB,OAAOA,mBAAmB,CAACY,eAAe,CAAC,CAAC;EAChD;EACA,MAAM,IAAIlG,KAAK,CAACuF,wCAAwC,CAAC;AAC7D;AAEA,IAAIY,gBAAgB,GAAG,CAAC;AACxB,MAAMC,iBAAiB,CAAC;EACpBC,WAAW,GAAG,IAAIC,GAAG,CAAC,CAAC;EACvB;AACJ;AACA;AACA;EACIC,gBAAgBA,CAACC,aAAa,EAAEC,WAAW,EAAEC,QAAQ,EAAE;IACnD,MAAMC,KAAK,GAAG,CAAC,CAAC;IAChB,IAAIF,WAAW,EAAE;MACbG,WAAW,CAACH,WAAW,CAAC,CAACI,OAAO,CAAEC,IAAI,IAAMH,KAAK,CAACG,IAAI,CAAC,GAAGL,WAAW,CAACK,IAAI,CAAE,CAAC;IACjF;IACA,IAAIJ,QAAQ,CAACK,GAAG,EAAE;MACd,IAAIL,QAAQ,CAACM,MAAM,IAAIN,QAAQ,CAAChD,GAAG,EAAE;QACjC,MAAM,IAAI1D,KAAK,CAAC,6BAA6B3I,UAAU,CAACmP,aAAa,CAAC,oBAAoB,CAAC;MAC/F;MACAS,WAAW,CAACN,KAAK,EAAED,QAAQ,CAACK,GAAG,CAAC;IACpC;IACA,IAAIL,QAAQ,CAACM,MAAM,EAAE;MACjBE,cAAc,CAACP,KAAK,EAAED,QAAQ,CAACM,MAAM,EAAE,IAAI,CAACX,WAAW,CAAC;IAC5D;IACA,IAAIK,QAAQ,CAAChD,GAAG,EAAE;MACdyD,WAAW,CAACR,KAAK,EAAED,QAAQ,CAAChD,GAAG,CAAC;IACpC;IACA,OAAO,IAAI8C,aAAa,CAACG,KAAK,CAAC;EACnC;AACJ;AACA,SAASO,cAAcA,CAACE,QAAQ,EAAEJ,MAAM,EAAEK,UAAU,EAAE;EAClD,MAAMC,aAAa,GAAG,IAAInJ,GAAG,CAAC,CAAC;EAC/B,KAAK,MAAM2I,IAAI,IAAIE,MAAM,EAAE;IACvB,MAAMO,WAAW,GAAGP,MAAM,CAACF,IAAI,CAAC;IAChC,IAAIU,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,EAAE;MAC5BA,WAAW,CAACV,OAAO,CAAElC,KAAK,IAAK;QAC3B2C,aAAa,CAAC5D,GAAG,CAACgE,YAAY,CAACZ,IAAI,EAAEnC,KAAK,EAAE0C,UAAU,CAAC,CAAC;MAC5D,CAAC,CAAC;IACN,CAAC,MACI;MACDC,aAAa,CAAC5D,GAAG,CAACgE,YAAY,CAACZ,IAAI,EAAES,WAAW,EAAEF,UAAU,CAAC,CAAC;IAClE;EACJ;EACA,KAAK,MAAMP,IAAI,IAAIM,QAAQ,EAAE;IACzB,MAAMO,SAAS,GAAGP,QAAQ,CAACN,IAAI,CAAC;IAChC,IAAIU,KAAK,CAACC,OAAO,CAACE,SAAS,CAAC,EAAE;MAC1BP,QAAQ,CAACN,IAAI,CAAC,GAAGa,SAAS,CAACC,MAAM,CAAEjD,KAAK,IAAK,CAAC2C,aAAa,CAACO,GAAG,CAACH,YAAY,CAACZ,IAAI,EAAEnC,KAAK,EAAE0C,UAAU,CAAC,CAAC,CAAC;IAC3G,CAAC,MACI;MACD,IAAIC,aAAa,CAACO,GAAG,CAACH,YAAY,CAACZ,IAAI,EAAEa,SAAS,EAAEN,UAAU,CAAC,CAAC,EAAE;QAC9DD,QAAQ,CAACN,IAAI,CAAC,GAAG/B,SAAS;MAC9B;IACJ;EACJ;AACJ;AACA,SAASoC,WAAWA,CAACC,QAAQ,EAAE1D,GAAG,EAAE;EAChC,KAAK,MAAMoD,IAAI,IAAIpD,GAAG,EAAE;IACpB,MAAMoE,QAAQ,GAAGpE,GAAG,CAACoD,IAAI,CAAC;IAC1B,MAAMa,SAAS,GAAGP,QAAQ,CAACN,IAAI,CAAC;IAChC,IAAIa,SAAS,IAAI,IAAI,IAAIH,KAAK,CAACC,OAAO,CAACE,SAAS,CAAC,EAAE;MAC/CP,QAAQ,CAACN,IAAI,CAAC,GAAGa,SAAS,CAACI,MAAM,CAACD,QAAQ,CAAC;IAC/C,CAAC,MACI;MACDV,QAAQ,CAACN,IAAI,CAAC,GAAGgB,QAAQ;IAC7B;EACJ;AACJ;AACA,SAASb,WAAWA,CAACG,QAAQ,EAAEL,GAAG,EAAE;EAChC,KAAK,MAAMD,IAAI,IAAIC,GAAG,EAAE;IACpBK,QAAQ,CAACN,IAAI,CAAC,GAAGC,GAAG,CAACD,IAAI,CAAC;EAC9B;AACJ;AACA,SAASY,YAAYA,CAACM,QAAQ,EAAEL,SAAS,EAAEN,UAAU,EAAE;EACnD,IAAIY,YAAY,GAAG,CAAC;EACpB,MAAMC,SAAS,GAAG,IAAI5B,GAAG,CAAC,CAAC;EAC3B,MAAM6B,QAAQ,GAAGA,CAACC,GAAG,EAAEzD,KAAK,KAAK;IAC7B,IAAIA,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7C,IAAIuD,SAAS,CAACL,GAAG,CAAClD,KAAK,CAAC,EAAE;QACtB,OAAOuD,SAAS,CAACjD,GAAG,CAACN,KAAK,CAAC;MAC/B;MACA;MACA;MACAuD,SAAS,CAACnB,GAAG,CAACpC,KAAK,EAAE,QAAQsD,YAAY,EAAE,EAAE,CAAC;MAC9C;MACA,OAAOtD,KAAK;IAChB,CAAC,MACI,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE;MAClCA,KAAK,GAAG0D,mBAAmB,CAAC1D,KAAK,EAAE0C,UAAU,CAAC;IAClD;IACA,OAAO1C,KAAK;EAChB,CAAC;EACD,OAAO,GAAGqD,QAAQ,IAAIM,IAAI,CAACC,SAAS,CAACZ,SAAS,EAAEQ,QAAQ,CAAC,EAAE;AAC/D;AACA,SAASE,mBAAmBA,CAACG,GAAG,EAAEnB,UAAU,EAAE;EAC1C,IAAIoB,EAAE,GAAGpB,UAAU,CAACpC,GAAG,CAACuD,GAAG,CAAC;EAC5B,IAAI,CAACC,EAAE,EAAE;IACLA,EAAE,GAAG,GAAGpR,UAAU,CAACmR,GAAG,CAAC,GAAGrC,gBAAgB,EAAE,EAAE;IAC9CkB,UAAU,CAACN,GAAG,CAACyB,GAAG,EAAEC,EAAE,CAAC;EAC3B;EACA,OAAOA,EAAE;AACb;AACA,SAAS7B,WAAWA,CAAC8B,GAAG,EAAE;EACtB,MAAM/B,KAAK,GAAG,EAAE;EAChB;EACAgC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CAAC7B,OAAO,CAAEC,IAAI,IAAK;IAC/B,IAAI,CAACA,IAAI,CAAC+B,UAAU,CAAC,GAAG,CAAC,EAAE;MACvBlC,KAAK,CAAC9F,IAAI,CAACiG,IAAI,CAAC;IACpB;EACJ,CAAC,CAAC;EACF;EACA,IAAIgC,KAAK,GAAGJ,GAAG;EACf,OAAQI,KAAK,GAAGH,MAAM,CAACI,cAAc,CAACD,KAAK,CAAC,EAAG;IAC3CH,MAAM,CAACC,IAAI,CAACE,KAAK,CAAC,CAACjC,OAAO,CAAEmC,SAAS,IAAK;MACtC,MAAMC,IAAI,GAAGN,MAAM,CAACO,wBAAwB,CAACJ,KAAK,EAAEE,SAAS,CAAC;MAC9D,IAAI,CAACA,SAAS,CAACH,UAAU,CAAC,GAAG,CAAC,IAAII,IAAI,IAAI,KAAK,IAAIA,IAAI,EAAE;QACrDtC,KAAK,CAAC9F,IAAI,CAACmI,SAAS,CAAC;MACzB;IACJ,CAAC,CAAC;EACN;EACA,OAAOrC,KAAK;AAChB;AAEA,MAAMwC,UAAU,GAAG,IAAIxR,uBAAuB,CAAC,CAAC;AAChD;AACA;AACA;AACA,MAAMyR,gBAAgB,CAAC;EACnBC,SAAS,GAAG,IAAI/C,GAAG,CAAC,CAAC;EACrBgD,QAAQ,GAAG,IAAIhD,GAAG,CAAC,CAAC;EACpBiD,WAAWA,CAACnK,IAAI,EAAEsH,QAAQ,EAAE;IACxB,MAAM2C,SAAS,GAAG,IAAI,CAACA,SAAS,CAACpE,GAAG,CAAC7F,IAAI,CAAC,IAAI,EAAE;IAChDiK,SAAS,CAACxI,IAAI,CAAC6F,QAAQ,CAAC;IACxB,IAAI,CAAC2C,SAAS,CAACtC,GAAG,CAAC3H,IAAI,EAAEiK,SAAS,CAAC;IACnC,IAAI,CAACC,QAAQ,CAACxF,MAAM,CAAC1E,IAAI,CAAC;EAC9B;EACAoK,YAAYA,CAACH,SAAS,EAAE;IACpB,IAAI,CAACA,SAAS,CAAC3K,KAAK,CAAC,CAAC;IACtB2K,SAAS,CAACxC,OAAO,CAAC,CAAC,CAACzH,IAAI,EAAEsH,QAAQ,CAAC,KAAK;MACpC,IAAI,CAAC6C,WAAW,CAACnK,IAAI,EAAEsH,QAAQ,CAAC;IACpC,CAAC,CAAC;EACN;EACA+C,aAAaA,CAACrK,IAAI,EAAE;IAChB,MAAMsK,WAAW,GAAGP,UAAU,CAACO,WAAW,CAACtK,IAAI,CAAC;IAChD;IACA;IACA;IACA;IACA;IACA,KAAK,IAAIuK,CAAC,GAAGD,WAAW,CAAC9I,MAAM,GAAG,CAAC,EAAE+I,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC9C,MAAMC,UAAU,GAAGF,WAAW,CAACC,CAAC,CAAC;MACjC,MAAME,WAAW,GAAGD,UAAU,YAAYrS,SAAS,IAC/CqS,UAAU,YAAYpS,SAAS,IAC/BoS,UAAU,YAAYtS,IAAI,IAC1BsS,UAAU,YAAYnS,QAAQ;MAClC,IAAIoS,WAAW,EAAE;QACb,OAAOD,UAAU,YAAY,IAAI,CAACxK,IAAI,GAAGwK,UAAU,GAAG,IAAI;MAC9D;IACJ;IACA,OAAO,IAAI;EACf;EACA9I,OAAOA,CAAC1B,IAAI,EAAE;IACV,IAAIkK,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACrE,GAAG,CAAC7F,IAAI,CAAC,IAAI,IAAI;IAC9C,IAAI,CAACkK,QAAQ,EAAE;MACXA,QAAQ,GAAG,IAAI,CAACG,aAAa,CAACrK,IAAI,CAAC;MACnC,IAAIkK,QAAQ,EAAE;QACV,MAAMD,SAAS,GAAG,IAAI,CAACA,SAAS,CAACpE,GAAG,CAAC7F,IAAI,CAAC;QAC1C,IAAIiK,SAAS,EAAE;UACX,MAAMS,SAAS,GAAG,IAAI1D,iBAAiB,CAAC,CAAC;UACzCiD,SAAS,CAACxC,OAAO,CAAEH,QAAQ,IAAK;YAC5B4C,QAAQ,GAAGQ,SAAS,CAACvD,gBAAgB,CAAC,IAAI,CAACnH,IAAI,EAAEkK,QAAQ,EAAE5C,QAAQ,CAAC;UACxE,CAAC,CAAC;QACN;MACJ;MACA,IAAI,CAAC4C,QAAQ,CAACvC,GAAG,CAAC3H,IAAI,EAAEkK,QAAQ,CAAC;IACrC;IACA,OAAOA,QAAQ;EACnB;AACJ;AACA,MAAMS,iBAAiB,SAASX,gBAAgB,CAAC;EAC7C,IAAIhK,IAAIA,CAAA,EAAG;IACP,OAAO7H,SAAS;EACpB;AACJ;AACA,MAAMyS,iBAAiB,SAASZ,gBAAgB,CAAC;EAC7C,IAAIhK,IAAIA,CAAA,EAAG;IACP,OAAO5H,SAAS;EACpB;AACJ;AACA,MAAMyS,YAAY,SAASb,gBAAgB,CAAC;EACxC,IAAIhK,IAAIA,CAAA,EAAG;IACP,OAAO9H,IAAI;EACf;AACJ;AACA,MAAM4S,gBAAgB,SAASd,gBAAgB,CAAC;EAC5C,IAAIhK,IAAIA,CAAA,EAAG;IACP,OAAO3H,QAAQ;EACnB;AACJ;AAEA,IAAI0S,qBAAqB;AACzB,CAAC,UAAUA,qBAAqB,EAAE;EAC9BA,qBAAqB,CAACA,qBAAqB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa;EAC/EA,qBAAqB,CAACA,qBAAqB,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,GAAG,mBAAmB;AAC/F,CAAC,EAAEA,qBAAqB,KAAKA,qBAAqB,GAAG,CAAC,CAAC,CAAC,CAAC;AACzD,SAASC,uBAAuBA,CAACzF,KAAK,EAAE;EACpC,OAAQA,KAAK,KAAKwF,qBAAqB,CAACE,WAAW,IAAI1F,KAAK,KAAKwF,qBAAqB,CAACG,iBAAiB;AAC5G;AACA,SAASC,4BAA4BA,CAACC,KAAK,EAAEC,QAAQ,EAAElH,QAAQ,EAAE;EAC7DiH,KAAK,CAAC3D,OAAO,CAAEzH,IAAI,IAAK;IACpB,IAAI,CAAC9G,wBAAwB,CAAC8G,IAAI,CAAC,EAAE;MACjC,MAAMsL,SAAS,GAAGD,QAAQ,CAAC3J,OAAO,CAAC1B,IAAI,CAAC;MACxC,IAAIsL,SAAS,KAAKA,SAAS,CAACC,UAAU,IAAI,IAAI,IAAID,SAAS,CAACC,UAAU,CAAC,EAAE;QACrE,MAAM,IAAI3K,KAAK,CAACvE,sCAAsC,CAAC2D,IAAI,EAAEmE,QAAQ,CAAC,CAAC;MAC3E;IACJ;EACJ,CAAC,CAAC;AACN;AACA,MAAMqH,eAAe,CAAC;EAClBC,QAAQ;EACRC,qBAAqB;EACrBC,gCAAgC,GAAG,IAAI;EACvC;EACAC,YAAY,GAAG,EAAE;EACjBC,OAAO,GAAG,EAAE;EACZC,SAAS,GAAG,EAAE;EACdC,OAAO,GAAG,EAAE;EACZ;EACAC,iBAAiB,GAAG,IAAIjN,GAAG,CAAC,CAAC;EAC7BkN,iBAAiB,GAAG,IAAIlN,GAAG,CAAC,CAAC;EAC7BmN,YAAY,GAAG,IAAInN,GAAG,CAAC,CAAC;EACxB;EACA;EACAoN,2BAA2B,GAAG,IAAIpN,GAAG,CAAC,CAAC;EACvC;EACAqN,cAAc,GAAG,IAAIrN,GAAG,CAAC,CAAC;EAC1BsN,cAAc,GAAG,IAAItN,GAAG,CAAC,CAAC;EAC1B;EACAuN,iBAAiB,GAAG,IAAIvN,GAAG,CAAC,CAAC;EAC7B;EACA;EACAwN,uBAAuB,GAAG,IAAIrF,GAAG,CAAC,CAAC;EACnCsF,SAAS,GAAGC,aAAa,CAAC,CAAC;EAC3B;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,sBAAsB,GAAG,IAAIxF,GAAG,CAAC,CAAC;EAClC;EACA;EACA;EACA;EACA;EACAyF,aAAa,GAAG,IAAIzF,GAAG,CAAC,CAAC;EACzB;EACA;EACA0F,aAAa,GAAG,EAAE;EAClBC,SAAS,GAAG,IAAI;EAChBC,iBAAiB,GAAG,IAAI;EACxBC,iBAAiB,GAAG,EAAE;EACtBC,qBAAqB,GAAG,EAAE;EAC1B;EACA;EACAC,yBAAyB,GAAG,IAAI/F,GAAG,CAAC,CAAC;EACrCgG,wBAAwB,GAAG,IAAIhG,GAAG,CAAC,CAAC;EACpCiG,6BAA6B,GAAG,IAAIpO,GAAG,CAAC,CAAC;EACzCqO,cAAc;EACdC,aAAa,GAAG,IAAI;EACpBC,kBAAkB,GAAGnL,4BAA4B;EACjDoL,4BAA4B,GAAG7O,kCAAkC;EACjE0B,WAAWA,CAACqL,QAAQ,EAAEC,qBAAqB,EAAE;IACzC,IAAI,CAACD,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,qBAAqB,GAAGA,qBAAqB;IAClD,MAAM8B,iBAAiB,CAAC;IAExB,IAAI,CAACJ,cAAc,GAAGI,iBAAiB;EAC3C;EACAC,oBAAoBA,CAAC3B,SAAS,EAAE;IAC5B,IAAI,CAACgB,iBAAiB,GAAGhB,SAAS;IAClC,IAAI,CAACe,SAAS,GAAG,IAAI;EACzB;EACAa,sBAAsBA,CAACC,SAAS,EAAE;IAC9B;IACA,IAAIA,SAAS,CAAC/B,YAAY,KAAKjG,SAAS,EAAE;MACtC;MACAwF,4BAA4B,CAACwC,SAAS,CAAC/B,YAAY,EAAE,IAAI,CAACY,SAAS,CAAClB,SAAS,EAAE,uCAAuC,CAAC;MACvH,IAAI,CAACsC,cAAc,CAACD,SAAS,CAAC/B,YAAY,EAAEb,qBAAqB,CAACE,WAAW,CAAC;MAC9E,IAAI,CAACW,YAAY,CAACnK,IAAI,CAAC,GAAGkM,SAAS,CAAC/B,YAAY,CAAC;IACrD;IACA;IACA,IAAI+B,SAAS,CAAC9B,OAAO,KAAKlG,SAAS,EAAE;MACjC,IAAI,CAACkI,0BAA0B,CAACF,SAAS,CAAC9B,OAAO,CAAC;MAClD,IAAI,CAACA,OAAO,CAACpK,IAAI,CAAC,GAAGkM,SAAS,CAAC9B,OAAO,CAAC;IAC3C;IACA,IAAI8B,SAAS,CAAC7B,SAAS,KAAKnG,SAAS,EAAE;MACnC,IAAI,CAACmG,SAAS,CAACrK,IAAI,CAAC,GAAGkM,SAAS,CAAC7B,SAAS,CAAC;IAC/C;IACA,IAAI6B,SAAS,CAAC5B,OAAO,KAAKpG,SAAS,EAAE;MACjC,IAAI,CAACoG,OAAO,CAACtK,IAAI,CAAC,GAAGkM,SAAS,CAAC5B,OAAO,CAAC;IAC3C;IACA,IAAI,CAACuB,kBAAkB,GAAGK,SAAS,CAACL,kBAAkB,IAAInL,4BAA4B;IACtF,IAAI,CAACoL,4BAA4B,GAC7BI,SAAS,CAACG,wBAAwB,IAAIpP,kCAAkC;EAChF;EACAqP,cAAcA,CAACC,QAAQ,EAAE1G,QAAQ,EAAE;IAC/B,IAAI7O,iCAAiC,EAAE;MACnCE,YAAY,CAACsV,kBAAkB,CAACD,QAAQ,CAAC;IAC7C;IACA,IAAI,CAAC1B,iBAAiB,CAAChI,GAAG,CAAC0J,QAAQ,CAAC;IACpC;IACA,IAAI,CAACxB,SAAS,CAAC0B,MAAM,CAAC/D,WAAW,CAAC6D,QAAQ,EAAE1G,QAAQ,CAAC;IACrD,MAAMU,QAAQ,GAAG,IAAI,CAACwE,SAAS,CAAC0B,MAAM,CAACxM,OAAO,CAACsM,QAAQ,CAAC;IACxD,IAAIhG,QAAQ,KAAK,IAAI,EAAE;MACnB,MAAMmG,gBAAgB,CAACH,QAAQ,CAACI,IAAI,EAAE,UAAU,CAAC;IACrD;IACA,IAAI,CAACC,iBAAiB,CAACL,QAAQ,EAAEhG,QAAQ,CAAC;IAC1C;IACA;IACA;IACA,IAAI,CAAC6F,0BAA0B,CAAC,CAACG,QAAQ,CAAC,CAAC;EAC/C;EACAM,iBAAiBA,CAAChD,SAAS,EAAEhE,QAAQ,EAAE;IACnC,IAAI,CAACiH,+BAA+B,CAACjD,SAAS,EAAEhE,QAAQ,CAAC;IACzD,IAAI,CAACkF,SAAS,CAAClB,SAAS,CAACnB,WAAW,CAACmB,SAAS,EAAEhE,QAAQ,CAAC;IACzD,IAAI,CAAC0E,iBAAiB,CAAC1H,GAAG,CAACgH,SAAS,CAAC;IACrC;IACA;IACA,IAAI,CAACkD,uCAAuC,CAAClD,SAAS,CAAC;EAC3D;EACAmD,iBAAiBA,CAACC,SAAS,EAAEpH,QAAQ,EAAE;IACnC,IAAI,CAACiH,+BAA+B,CAACG,SAAS,EAAEpH,QAAQ,CAAC;IACzD,IAAI,CAACkF,SAAS,CAACkC,SAAS,CAACvE,WAAW,CAACuE,SAAS,EAAEpH,QAAQ,CAAC;IACzD,IAAI,CAAC2E,iBAAiB,CAAC3H,GAAG,CAACoK,SAAS,CAAC;EACzC;EACAC,YAAYA,CAACC,IAAI,EAAEtH,QAAQ,EAAE;IACzB,IAAI,CAACiH,+BAA+B,CAACK,IAAI,EAAEtH,QAAQ,CAAC;IACpD,IAAI,CAACkF,SAAS,CAACoC,IAAI,CAACzE,WAAW,CAACyE,IAAI,EAAEtH,QAAQ,CAAC;IAC/C,IAAI,CAAC4E,YAAY,CAAC5H,GAAG,CAACsK,IAAI,CAAC;EAC/B;EACAL,+BAA+BA,CAACvO,IAAI,EAAEsH,QAAQ,EAAE;IAC5C,IAAIA,QAAQ,CAAChD,GAAG,EAAEuK,cAAc,CAAC,YAAY,CAAC,IAC1CvH,QAAQ,CAACK,GAAG,EAAEkH,cAAc,CAAC,YAAY,CAAC,IAC1CvH,QAAQ,CAACM,MAAM,EAAEiH,cAAc,CAAC,YAAY,CAAC,EAAE;MAC/C,MAAM,IAAIjO,KAAK,CAAC,uBAAuBZ,IAAI,CAACoO,IAAI,sCAAsC,GAClF,0EAA0E,CAAC;IACnF;EACJ;EACAU,gBAAgBA,CAAClP,KAAK,EAAEmP,QAAQ,EAAE;IAC9B,IAAIC,WAAW;IACf,IAAID,QAAQ,CAACE,UAAU,KAAKtJ,SAAS,EAAE;MACnCqJ,WAAW,GAAG;QACVE,OAAO,EAAEtP,KAAK;QACdqP,UAAU,EAAEF,QAAQ,CAACE,UAAU;QAC/BE,IAAI,EAAEJ,QAAQ,CAACI,IAAI,IAAI,EAAE;QACzBC,KAAK,EAAEL,QAAQ,CAACK;MACpB,CAAC;IACL,CAAC,MACI,IAAIL,QAAQ,CAACM,QAAQ,KAAK1J,SAAS,EAAE;MACtCqJ,WAAW,GAAG;QAAEE,OAAO,EAAEtP,KAAK;QAAEyP,QAAQ,EAAEN,QAAQ,CAACM,QAAQ;QAAED,KAAK,EAAEL,QAAQ,CAACK;MAAM,CAAC;IACxF,CAAC,MACI;MACDJ,WAAW,GAAG;QAAEE,OAAO,EAAEtP;MAAM,CAAC;IACpC;IACA,MAAM0P,aAAa,GAAG,OAAO1P,KAAK,KAAK,QAAQ,GAAG/G,iBAAiB,CAAC+G,KAAK,CAAC,GAAG,IAAI;IACjF,MAAM2P,UAAU,GAAGD,aAAa,KAAK,IAAI,GAAG,IAAI,GAAGxW,iBAAiB,CAACwW,aAAa,CAACC,UAAU,CAAC;IAC9F,MAAMC,eAAe,GAAGD,UAAU,KAAK,MAAM,GAAG,IAAI,CAACvC,qBAAqB,GAAG,IAAI,CAACD,iBAAiB;IACnGyC,eAAe,CAAC/N,IAAI,CAACuN,WAAW,CAAC;IACjC;IACA,IAAI,CAAC9B,wBAAwB,CAACvF,GAAG,CAAC/H,KAAK,EAAEoP,WAAW,CAAC;IACrD,IAAIM,aAAa,KAAK,IAAI,IAAIC,UAAU,KAAK,IAAI,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;MACjF,MAAME,iBAAiB,GAAG,IAAI,CAACxC,yBAAyB,CAACpH,GAAG,CAAC0J,UAAU,CAAC;MACxE,IAAIE,iBAAiB,KAAK9J,SAAS,EAAE;QACjC8J,iBAAiB,CAAChO,IAAI,CAACuN,WAAW,CAAC;MACvC,CAAC,MACI;QACD,IAAI,CAAC/B,yBAAyB,CAACtF,GAAG,CAAC4H,UAAU,EAAE,CAACP,WAAW,CAAC,CAAC;MACjE;IACJ;EACJ;EACAU,kCAAkCA,CAAC1P,IAAI,EAAE2P,QAAQ,EAAE;IAC/C,MAAMC,GAAG,GAAG5P,IAAI,CAAChG,YAAY,CAAC;IAC9B,MAAM6V,YAAY,GAAGA,CAAA,KAAM;MACvB,MAAM7H,QAAQ,GAAG,IAAI,CAACwE,SAAS,CAAClB,SAAS,CAAC5J,OAAO,CAAC1B,IAAI,CAAC;MACvD,OAAO,CAAC,CAACgI,QAAQ,CAAC8H,QAAQ,IAAI,CAAC,CAAC9H,QAAQ,CAAC+H,SAAS,EAAEvO,MAAM;IAC9D,CAAC;IACD,MAAMwO,iBAAiB,GAAG,CAAC,CAACJ,GAAG,IAAI,CAAC5W,gCAAgC,CAACgH,IAAI,CAAC,IAAI6P,YAAY,CAAC,CAAC;IAC5F;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMvI,QAAQ,GAAG0I,iBAAiB,GAC5B;MAAEL,QAAQ;MAAEM,MAAM,EAAE,EAAE;MAAEF,SAAS,EAAE,EAAE;MAAED,QAAQ,EAAEnK;IAAU,CAAC,GAC5D;MAAEgK;IAAS,CAAC;IAClB,IAAI,CAACrB,iBAAiB,CAACtO,IAAI,EAAE;MAAE2H,GAAG,EAAEL;IAAS,CAAC,CAAC;IAC/C,IAAI0I,iBAAiB,IAAIJ,GAAG,CAACK,MAAM,IAAIL,GAAG,CAACK,MAAM,CAACzO,MAAM,GAAG,CAAC,EAAE;MAC1D,IAAI,CAAC+K,uBAAuB,CAAC5E,GAAG,CAAC3H,IAAI,EAAE4P,GAAG,CAACK,MAAM,CAAC;IACtD;IACA;IACA,IAAI,CAACvD,sBAAsB,CAAC/E,GAAG,CAAC3H,IAAI,EAAE+K,qBAAqB,CAACG,iBAAiB,CAAC;EAClF;EACMgF,yCAAyCA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAA3P,iBAAA;MAC9C,IAAI2P,MAAI,CAAChE,2BAA2B,CAAC/M,IAAI,KAAK,CAAC,EAC3C;MACJ,MAAMgR,QAAQ,GAAG,EAAE;MACnB,KAAK,MAAM9E,SAAS,IAAI6E,MAAI,CAAChE,2BAA2B,EAAE;QACtD,MAAMkE,eAAe,GAAGnX,wBAAwB,CAACoS,SAAS,CAAC;QAC3D,IAAI+E,eAAe,EAAE;UACjBD,QAAQ,CAAC3O,IAAI,CAAC4O,eAAe,CAAC,CAAC,CAAC;QACpC;MACJ;MACAF,MAAI,CAAChE,2BAA2B,CAAC7M,KAAK,CAAC,CAAC;MACxC,MAAMgR,YAAY,SAAShS,OAAO,CAACiS,GAAG,CAACH,QAAQ,CAAC;MAChD,MAAMI,gBAAgB,GAAGF,YAAY,CAACG,IAAI,CAAC,CAAC,CAAC;MAC7CN,MAAI,CAACtC,0BAA0B,CAAC2C,gBAAgB,CAAC;MACjD;MACA;MACA,KAAK,MAAMlF,SAAS,IAAIkF,gBAAgB,EAAE;QACtCL,MAAI,CAACO,6BAA6B,CAACpF,SAAS,CAAC;MACjD;IAAC;EACL;EACMqF,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAApQ,iBAAA;MACtBoQ,MAAI,CAACC,6BAA6B,CAAC,CAAC;MACpC;MACA;MACA;MACA,MAAMD,MAAI,CAACV,yCAAyC,CAAC,CAAC;MACtD;MACA;MACA;MACA;MACA/E,4BAA4B,CAACyF,MAAI,CAAChF,YAAY,EAAEgF,MAAI,CAACpE,SAAS,CAAClB,SAAS,EAAE,uCAAuC,CAAC;MAClH;MACA,IAAIwF,mBAAmB,GAAGF,MAAI,CAACG,gBAAgB,CAAC,CAAC;MACjD;MACA,IAAID,mBAAmB,EAAE;QACrB,IAAIE,cAAc;QAClB,IAAI3F,QAAQ,GAAI4F,GAAG,IAAK;UACpB,IAAI,CAACD,cAAc,EAAE;YACjBA,cAAc,GAAGJ,MAAI,CAAChL,QAAQ,CAACC,GAAG,CAAC5H,cAAc,CAAC;UACtD;UACA,OAAOK,OAAO,CAACoD,OAAO,CAACsP,cAAc,CAACnL,GAAG,CAACoL,GAAG,CAAC,CAAC;QACnD,CAAC;QACD,MAAM7X,0BAA0B,CAACiS,QAAQ,CAAC;MAC9C;IAAC;EACL;EACA6F,QAAQA,CAAA,EAAG;IACP;IACA,IAAI,CAACH,gBAAgB,CAAC,CAAC;IACvB;IACA,IAAI,CAACI,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAACC,sBAAsB,CAAC,CAAC;IAC7B;IACA;IACA,IAAI,CAACC,iCAAiC,CAAC,CAAC;IACxC;IACA;IACA,IAAI,CAAC5E,sBAAsB,CAACpN,KAAK,CAAC,CAAC;IACnC,MAAMiS,cAAc,GAAG,IAAI,CAAC9F,QAAQ,CAAC7F,QAAQ;IAC7C,IAAI,CAACyH,aAAa,GAAG,IAAI/T,mBAAmB,CAAC,IAAI,CAAC8T,cAAc,EAAEmE,cAAc,EAAE,EAAE,CAAC;IACrF;IACA;IACA,IAAI,CAAClE,aAAa,CAACzH,QAAQ,CAACC,GAAG,CAACtM,qBAAqB,CAAC,CAACiY,eAAe,CAAC,CAAC;IACxE;IACA;IACA;IACA,MAAMC,QAAQ,GAAG,IAAI,CAACpE,aAAa,CAACzH,QAAQ,CAACC,GAAG,CAACrM,SAAS,EAAEE,kBAAkB,CAAC;IAC/EE,YAAY,CAAC6X,QAAQ,CAAC;IACtB,OAAO,IAAI,CAACpE,aAAa;EAC7B;EACA;AACJ;AACA;EACIqE,oBAAoBA,CAACC,UAAU,EAAE;IAC7B,IAAI,CAAC9D,0BAA0B,CAAC,CAAC8D,UAAU,CAAC,CAAC;IAC7C,IAAI,CAACZ,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACM,sBAAsB,CAAC,CAAC;IAC7B,IAAI,CAACX,6BAA6B,CAACiB,UAAU,CAAC;IAC9C,IAAI,CAACP,qBAAqB,CAAC,CAAC;EAChC;EACA;AACJ;AACA;EACUQ,qBAAqBA,CAACD,UAAU,EAAE;IAAA,IAAAE,MAAA;IAAA,OAAArR,iBAAA;MACpCqR,MAAI,CAAChE,0BAA0B,CAAC,CAAC8D,UAAU,CAAC,CAAC;MAC7C,MAAME,MAAI,CAAClB,iBAAiB,CAAC,CAAC;MAC9BkB,MAAI,CAACR,sBAAsB,CAAC,CAAC;MAC7BQ,MAAI,CAACnB,6BAA6B,CAACiB,UAAU,CAAC;MAC9CE,MAAI,CAACT,qBAAqB,CAAC,CAAC;IAAC;EACjC;EACA;AACJ;AACA;EACIU,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACtF,SAAS,CAAC0B,MAAM;EAChC;EACA;AACJ;AACA;EACI6D,sBAAsBA,CAACJ,UAAU,EAAE;IAC/B,OAAOK,aAAa,CAACL,UAAU,CAACM,IAAI,CAACrG,YAAY,CAAC,CAACsG,MAAM,CAAC,CAACC,SAAS,EAAEC,WAAW,KAAK;MAClF,MAAMC,YAAY,GAAGD,WAAW,CAACE,IAAI;MACrCD,YAAY,IAAIF,SAAS,CAAC1Q,IAAI,CAAC,IAAI3H,wBAAwB,CAACuY,YAAY,EAAE,IAAI,CAAChF,aAAa,CAAC,CAAC;MAC9F,OAAO8E,SAAS;IACpB,CAAC,EAAE,EAAE,CAAC;EACV;EACApB,gBAAgBA,CAAA,EAAG;IACf;IACA,IAAID,mBAAmB,GAAG,KAAK;IAC/B,IAAI,CAAC9E,iBAAiB,CAACvE,OAAO,CAAE2K,WAAW,IAAK;MAC5C,IAAIlZ,wBAAwB,CAACkZ,WAAW,CAAC,EAAE;QACvC,MAAM,IAAIxR,KAAK,CAAC,cAAcwR,WAAW,CAAChE,IAAI,6BAA6B,GACvE,6EAA6E,CAAC;MACtF;MACA0C,mBAAmB,GAAGA,mBAAmB,IAAI9X,gCAAgC,CAACoZ,WAAW,CAAC;MAC1F,MAAMpK,QAAQ,GAAG,IAAI,CAACwE,SAAS,CAAClB,SAAS,CAAC5J,OAAO,CAAC0Q,WAAW,CAAC;MAC9D,IAAIpK,QAAQ,KAAK,IAAI,EAAE;QACnB,MAAMmG,gBAAgB,CAACiE,WAAW,CAAChE,IAAI,EAAE,WAAW,CAAC;MACzD;MACA,IAAI,CAACmE,eAAe,CAACvY,YAAY,EAAEoY,WAAW,CAAC;MAC/C,IAAI3Z,iCAAiC,EAAE;QACnCE,YAAY,CAACsV,kBAAkB,CAACmE,WAAW,CAAC;MAChD;MACAlY,iBAAiB,CAACkY,WAAW,EAAEpK,QAAQ,CAAC;IAC5C,CAAC,CAAC;IACF,IAAI,CAACgE,iBAAiB,CAAC1M,KAAK,CAAC,CAAC;IAC9B,IAAI,CAAC2M,iBAAiB,CAACxE,OAAO,CAAE2K,WAAW,IAAK;MAC5C,MAAMpK,QAAQ,GAAG,IAAI,CAACwE,SAAS,CAACkC,SAAS,CAAChN,OAAO,CAAC0Q,WAAW,CAAC;MAC9D,IAAIpK,QAAQ,KAAK,IAAI,EAAE;QACnB,MAAMmG,gBAAgB,CAACiE,WAAW,CAAChE,IAAI,EAAE,WAAW,CAAC;MACzD;MACA,IAAI,CAACmE,eAAe,CAACnY,WAAW,EAAEgY,WAAW,CAAC;MAC9C9X,iBAAiB,CAAC8X,WAAW,EAAEpK,QAAQ,CAAC;IAC5C,CAAC,CAAC;IACF,IAAI,CAACiE,iBAAiB,CAAC3M,KAAK,CAAC,CAAC;IAC9B,IAAI,CAAC4M,YAAY,CAACzE,OAAO,CAAE2K,WAAW,IAAK;MACvC,MAAMpK,QAAQ,GAAG,IAAI,CAACwE,SAAS,CAACoC,IAAI,CAAClN,OAAO,CAAC0Q,WAAW,CAAC;MACzD,IAAIpK,QAAQ,KAAK,IAAI,EAAE;QACnB,MAAMmG,gBAAgB,CAACiE,WAAW,CAAChE,IAAI,EAAE,MAAM,CAAC;MACpD;MACA,IAAI,CAACmE,eAAe,CAAC/X,YAAY,EAAE4X,WAAW,CAAC;MAC/C1X,YAAY,CAAC0X,WAAW,EAAEpK,QAAQ,CAAC;IACvC,CAAC,CAAC;IACF,IAAI,CAACkE,YAAY,CAAC5M,KAAK,CAAC,CAAC;IACzB,OAAOwR,mBAAmB;EAC9B;EACAM,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAAC9E,iBAAiB,CAAClN,IAAI,GAAG,CAAC,EAAE;MACjC;MACA;MACA;MACA,MAAMoT,gBAAgB,GAAG,IAAI,CAACpF,cAAc,CAACxS,WAAW,CAAC;MACzD,MAAM6X,eAAe,GAAG,IAAI,CAACC,iCAAiC,CAACF,gBAAgB,CAAC3G,OAAO,CAAC;MACxF,IAAI4G,eAAe,CAACrT,IAAI,GAAG,CAAC,EAAE;QAC1BqT,eAAe,CAAChL,OAAO,CAAEkK,UAAU,IAAK;UACpC,IAAI,CAAClZ,iCAAiC,EAAE;YACpC,IAAI,CAACka,qBAAqB,CAAChB,UAAU,EAAE/W,WAAW,EAAE,yBAAyB,CAAC;YAC9E+W,UAAU,CAAC/W,WAAW,CAAC,CAACgY,uBAAuB,GAAG,IAAI;UAC1D,CAAC,MACI;YACDja,YAAY,CAACsV,kBAAkB,CAAC0D,UAAU,CAAC;UAC/C;QACJ,CAAC,CAAC;MACN;IACJ;IACA,MAAMkB,aAAa,GAAG,IAAI3L,GAAG,CAAC,CAAC;IAC/B,MAAM4L,gBAAgB,GAAInB,UAAU,IAAK;MACrC,IAAI,CAACkB,aAAa,CAACpK,GAAG,CAACkJ,UAAU,CAAC,EAAE;QAChC,MAAMoB,eAAe,GAAG/H,uBAAuB,CAAC2G,UAAU,CAAC;QAC3D,MAAMqB,QAAQ,GAAGD,eAAe,GAAG,IAAI,CAAC3F,cAAc,GAAGuE,UAAU;QACnEkB,aAAa,CAAClL,GAAG,CAACgK,UAAU,EAAExV,oBAAoB,CAAC6W,QAAQ,CAAC,CAAC;MACjE;MACA,OAAOH,aAAa,CAAChN,GAAG,CAAC8L,UAAU,CAAC;IACxC,CAAC;IACD,IAAI,CAACjF,sBAAsB,CAACjF,OAAO,CAAC,CAACkK,UAAU,EAAEsB,aAAa,KAAK;MAC/D,IAAItB,UAAU,KAAK,IAAI,EAAE;QACrB,MAAMuB,WAAW,GAAGJ,gBAAgB,CAACnB,UAAU,CAAC;QAChD,IAAI,CAACgB,qBAAqB,CAACM,aAAa,EAAEjZ,YAAY,EAAE,eAAe,CAAC;QACxE,IAAI,CAAC2Y,qBAAqB,CAACM,aAAa,EAAEjZ,YAAY,EAAE,UAAU,CAAC;QACnEc,2BAA2B,CAACqY,eAAe,CAACF,aAAa,CAAC,EAAEC,WAAW,CAAC;MAC5E;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,CAACP,qBAAqB,CAACM,aAAa,EAAEjZ,YAAY,EAAE,OAAO,CAAC;IACpE,CAAC,CAAC;IACF,IAAI,CAAC0S,sBAAsB,CAACpN,KAAK,CAAC,CAAC;EACvC;EACA+R,sBAAsBA,CAAA,EAAG;IACrB,MAAM+B,mBAAmB,GAAIC,KAAK,IAAMrT,IAAI,IAAK;MAC7C,MAAMqL,QAAQ,GAAGgI,KAAK,KAAKrZ,YAAY,GAAG,IAAI,CAACwS,SAAS,CAAClB,SAAS,GAAG,IAAI,CAACkB,SAAS,CAACkC,SAAS;MAC7F,MAAM1G,QAAQ,GAAGqD,QAAQ,CAAC3J,OAAO,CAAC1B,IAAI,CAAC;MACvC,IAAI,IAAI,CAACsT,oBAAoB,CAACtL,QAAQ,CAAC8D,SAAS,CAAC,EAAE;QAC/C,IAAI,CAACyH,6BAA6B,CAACvT,IAAI,EAAEqT,KAAK,CAAC;MACnD;IACJ,CAAC;IACD,IAAI,CAACjH,cAAc,CAAC3E,OAAO,CAAC2L,mBAAmB,CAACpZ,YAAY,CAAC,CAAC;IAC9D,IAAI,CAACqS,cAAc,CAAC5E,OAAO,CAAC2L,mBAAmB,CAAChZ,WAAW,CAAC,CAAC;IAC7D,IAAI,CAACgS,cAAc,CAAC9M,KAAK,CAAC,CAAC;IAC3B,IAAI,CAAC+M,cAAc,CAAC/M,KAAK,CAAC,CAAC;EAC/B;EACA;AACJ;AACA;AACA;EACIoR,6BAA6BA,CAAC1Q,IAAI,EAAE;IAChC,MAAMwT,QAAQ,GAAGC,qBAAqB,CAACzT,IAAI,CAAC,IAAI0T,UAAU,CAAC1T,IAAI,CAAC;IAChE;IACA;IACA;IACA;IACA,IAAI,CAACwT,QAAQ,IAAI,IAAI,CAACrG,6BAA6B,CAAC1E,GAAG,CAACzI,IAAI,CAAC,EAAE;MAC3D;IACJ;IACA,IAAI,CAACmN,6BAA6B,CAAC7I,GAAG,CAACtE,IAAI,CAAC;IAC5C;IACA;IACA;IACA;IACA;IACA,MAAM2T,WAAW,GAAG3T,IAAI,CAAChF,WAAW,CAAC;IACrC;IACA,IAAI,IAAI,CAACkS,wBAAwB,CAAC9N,IAAI,KAAK,CAAC,EACxC;IACJ,IAAIqU,qBAAqB,CAACzT,IAAI,CAAC,EAAE;MAC7B;MACA,MAAM4P,GAAG,GAAGuD,eAAe,CAACnT,IAAI,CAAC;MACjC,MAAM4T,YAAY,GAAG5B,aAAa,CAACpC,GAAG,CAACgE,YAAY,IAAI,EAAE,CAAC;MAC1D,KAAK,MAAMC,UAAU,IAAID,YAAY,EAAE;QACnC,IAAI,CAAClD,6BAA6B,CAACmD,UAAU,CAAC;MAClD;IACJ,CAAC,MACI;MACD,MAAM/H,SAAS,GAAG,CACd,GAAG6H,WAAW,CAAC7H,SAAS,EACxB,IAAI,IAAI,CAACmB,yBAAyB,CAACpH,GAAG,CAAC7F,IAAI,CAAC,IAAI,EAAE,CAAC,CACtD;MACD,IAAI,IAAI,CAACsT,oBAAoB,CAACxH,SAAS,CAAC,EAAE;QACtC,IAAI,CAACyG,eAAe,CAACvX,WAAW,EAAEgF,IAAI,CAAC;QACvC,IAAI,CAAC2S,qBAAqB,CAAC3S,IAAI,EAAEhF,WAAW,EAAE,WAAW,CAAC;QAC1D2Y,WAAW,CAAC7H,SAAS,GAAG,IAAI,CAACgI,sBAAsB,CAAChI,SAAS,CAAC;MAClE;MACA;MACA,MAAM6B,SAAS,GAAG3N,IAAI,CAACpF,WAAW,CAAC;MACnC,MAAMiR,OAAO,GAAGmG,aAAa,CAACrE,SAAS,CAAC9B,OAAO,CAAC;MAChD,KAAK,MAAMkI,cAAc,IAAIlI,OAAO,EAAE;QAClC,IAAI,CAAC6E,6BAA6B,CAACqD,cAAc,CAAC;MACtD;MACA;MACA;MACA,KAAK,MAAMA,cAAc,IAAIC,OAAO,CAACL,WAAW,CAAC9H,OAAO,CAAC,EAAE;QACvD,IAAIoI,qBAAqB,CAACF,cAAc,CAAC,EAAE;UACvC,IAAI,CAACnH,aAAa,CAACnL,IAAI,CAAC;YACpByS,MAAM,EAAEH,cAAc;YACtBI,SAAS,EAAE,WAAW;YACtBC,aAAa,EAAEL,cAAc,CAACjI;UAClC,CAAC,CAAC;UACFiI,cAAc,CAACjI,SAAS,GAAG,IAAI,CAACgI,sBAAsB,CAACC,cAAc,CAACjI,SAAS,CAAC;QACpF;MACJ;IACJ;EACJ;EACAwF,iCAAiCA,CAAA,EAAG;IAChC,IAAI,CAAC/E,uBAAuB,CAAC9E,OAAO,CAAC,CAACwI,MAAM,EAAEjQ,IAAI,KAAMA,IAAI,CAAChG,YAAY,CAAC,CAACiW,MAAM,GAAGA,MAAO,CAAC;IAC5F,IAAI,CAAC1D,uBAAuB,CAACjN,KAAK,CAAC,CAAC;EACxC;EACAsO,cAAcA,CAACyG,GAAG,EAAE1C,UAAU,EAAE;IAC5B,KAAK,MAAMpM,KAAK,IAAI8O,GAAG,EAAE;MACrB,IAAIjM,KAAK,CAACC,OAAO,CAAC9C,KAAK,CAAC,EAAE;QACtB,IAAI,CAACqI,cAAc,CAACrI,KAAK,EAAEoM,UAAU,CAAC;MAC1C,CAAC,MACI;QACD,IAAI,CAAC2C,SAAS,CAAC/O,KAAK,EAAEoM,UAAU,CAAC;MACrC;IACJ;EACJ;EACAtD,iBAAiBA,CAACL,QAAQ,EAAEhG,QAAQ,EAAE;IAClC;IACA,IAAI,CAACuK,eAAe,CAAC3X,WAAW,EAAEoT,QAAQ,CAAC;IAC3C,IAAI,CAACuE,eAAe,CAACvX,WAAW,EAAEgT,QAAQ,CAAC;IAC3C9S,oBAAoB,CAAC8S,QAAQ,EAAEhG,QAAQ,CAAC;EAC5C;EACAwG,uCAAuCA,CAACxO,IAAI,EAAE;IAC1C,MAAMqQ,eAAe,GAAGnX,wBAAwB,CAAC8G,IAAI,CAAC;IACtD,IAAIqQ,eAAe,EAAE;MACjB,IAAI,CAAClE,2BAA2B,CAAC7H,GAAG,CAACtE,IAAI,CAAC;IAC9C;EACJ;EACAsU,SAASA,CAACtU,IAAI,EAAE2R,UAAU,EAAE;IACxB;IACA;IACA,IAAI,CAACnD,uCAAuC,CAACxO,IAAI,CAAC;IAClD,MAAMsL,SAAS,GAAG,IAAI,CAACkB,SAAS,CAAClB,SAAS,CAAC5J,OAAO,CAAC1B,IAAI,CAAC;IACxD,IAAIsL,SAAS,EAAE;MACX;MACA;MACA;MACA,IAAItS,gCAAgC,CAACgH,IAAI,CAAC,IAAI,CAACA,IAAI,CAAC6O,cAAc,CAAC7U,YAAY,CAAC,EAAE;QAC9E,IAAI,CAACgS,iBAAiB,CAAC1H,GAAG,CAACtE,IAAI,CAAC;MACpC;MACA,IAAI,CAACoM,cAAc,CAAC9H,GAAG,CAACtE,IAAI,CAAC;MAC7B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,CAAC,IAAI,CAAC0M,sBAAsB,CAACjE,GAAG,CAACzI,IAAI,CAAC,IACtC,IAAI,CAAC0M,sBAAsB,CAAC7G,GAAG,CAAC7F,IAAI,CAAC,KAAK+K,qBAAqB,CAACE,WAAW,EAAE;QAC7E,IAAI,CAACyB,sBAAsB,CAAC/E,GAAG,CAAC3H,IAAI,EAAE2R,UAAU,CAAC;MACrD;MACA;IACJ;IACA,MAAMjD,SAAS,GAAG,IAAI,CAAClC,SAAS,CAACkC,SAAS,CAAChN,OAAO,CAAC1B,IAAI,CAAC;IACxD,IAAI0O,SAAS,EAAE;MACX,IAAI,CAAC1O,IAAI,CAAC6O,cAAc,CAACzU,WAAW,CAAC,EAAE;QACnC,IAAI,CAAC6R,iBAAiB,CAAC3H,GAAG,CAACtE,IAAI,CAAC;MACpC;MACA,IAAI,CAACqM,cAAc,CAAC/H,GAAG,CAACtE,IAAI,CAAC;MAC7B;IACJ;IACA,MAAM4O,IAAI,GAAG,IAAI,CAACpC,SAAS,CAACoC,IAAI,CAAClN,OAAO,CAAC1B,IAAI,CAAC;IAC9C,IAAI4O,IAAI,IAAI,CAAC5O,IAAI,CAAC6O,cAAc,CAACrU,YAAY,CAAC,EAAE;MAC5C,IAAI,CAAC0R,YAAY,CAAC5H,GAAG,CAACtE,IAAI,CAAC;MAC3B;IACJ;EACJ;EACA6N,0BAA0BA,CAACwG,GAAG,EAAE;IAC5B;IACA;IACA;IACA;IACA,MAAME,aAAa,GAAG,IAAIxV,GAAG,CAAC,CAAC;IAC/B,MAAMyV,+BAA+B,GAAIH,GAAG,IAAK;MAC7C,KAAK,MAAM9O,KAAK,IAAI8O,GAAG,EAAE;QACrB,IAAIjM,KAAK,CAACC,OAAO,CAAC9C,KAAK,CAAC,EAAE;UACtBiP,+BAA+B,CAACjP,KAAK,CAAC;QAC1C,CAAC,MACI,IAAIkP,cAAc,CAAClP,KAAK,CAAC,EAAE;UAC5B,MAAMqK,GAAG,GAAGrK,KAAK,CAAC0M,IAAI;UACtB,IAAIsC,aAAa,CAAC9L,GAAG,CAACmH,GAAG,CAAC,EAAE;YACxB;UACJ;UACA2E,aAAa,CAACjQ,GAAG,CAACsL,GAAG,CAAC;UACtB;UACA;UACA,IAAI,CAAChC,cAAc,CAACoE,aAAa,CAACpC,GAAG,CAAChE,YAAY,CAAC,EAAErG,KAAK,CAAC;UAC3DiP,+BAA+B,CAACxC,aAAa,CAACpC,GAAG,CAAC/D,OAAO,CAAC,CAAC;UAC3D2I,+BAA+B,CAACxC,aAAa,CAACpC,GAAG,CAAC8E,OAAO,CAAC,CAAC;QAC/D,CAAC,MACI,IAAIT,qBAAqB,CAAC1O,KAAK,CAAC,EAAE;UACnCiP,+BAA+B,CAAC,CAACjP,KAAK,CAACyI,QAAQ,CAAC,CAAC;QACrD,CAAC,MACI,IAAIyF,qBAAqB,CAAClO,KAAK,CAAC,EAAE;UACnC,IAAI,CAAC+O,SAAS,CAAC/O,KAAK,EAAE,IAAI,CAAC;UAC3B,MAAMqK,GAAG,GAAGuD,eAAe,CAAC5N,KAAK,CAAC;UAClC,IAAIgP,aAAa,CAAC9L,GAAG,CAACmH,GAAG,CAAC,EAAE;YACxB;UACJ;UACA2E,aAAa,CAACjQ,GAAG,CAACsL,GAAG,CAAC;UACtB,MAAMgE,YAAY,GAAG5B,aAAa,CAACpC,GAAG,CAACgE,YAAY,IAAI,EAAE,CAAC;UAC1DA,YAAY,CAACnM,OAAO,CAAEoM,UAAU,IAAK;YACjC;YACA;YACA;YACA;YACA,IAAIJ,qBAAqB,CAACI,UAAU,CAAC,IAAIY,cAAc,CAACZ,UAAU,CAAC,EAAE;cACjEW,+BAA+B,CAAC,CAACX,UAAU,CAAC,CAAC;YACjD,CAAC,MACI;cACD,IAAI,CAACS,SAAS,CAACT,UAAU,EAAE,IAAI,CAAC;YACpC;UACJ,CAAC,CAAC;QACN;MACJ;IACJ,CAAC;IACDW,+BAA+B,CAACH,GAAG,CAAC;EACxC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA3B,iCAAiCA,CAAC2B,GAAG,EAAE;IACnC,MAAMM,WAAW,GAAG,IAAI5V,GAAG,CAAC,CAAC;IAC7B,MAAM0T,eAAe,GAAG,IAAI1T,GAAG,CAAC,CAAC;IACjC,MAAM6V,wBAAwB,GAAGA,CAACP,GAAG,EAAEQ,IAAI,KAAK;MAC5C,KAAK,MAAMtP,KAAK,IAAI8O,GAAG,EAAE;QACrB,IAAIjM,KAAK,CAACC,OAAO,CAAC9C,KAAK,CAAC,EAAE;UACtB;UACA;UACAqP,wBAAwB,CAACrP,KAAK,EAAEsP,IAAI,CAAC;QACzC,CAAC,MACI,IAAIJ,cAAc,CAAClP,KAAK,CAAC,EAAE;UAC5B,IAAIoP,WAAW,CAAClM,GAAG,CAAClD,KAAK,CAAC,EAAE;YACxB;YACA;YACA;YACA,IAAIkN,eAAe,CAAChK,GAAG,CAAClD,KAAK,CAAC,EAAE;cAC5BsP,IAAI,CAACpN,OAAO,CAAEqN,IAAI,IAAKrC,eAAe,CAACnO,GAAG,CAACwQ,IAAI,CAAC,CAAC;YACrD;YACA;UACJ;UACAH,WAAW,CAACrQ,GAAG,CAACiB,KAAK,CAAC;UACtB,IAAI,IAAI,CAAC+G,iBAAiB,CAAC7D,GAAG,CAAClD,KAAK,CAAC,EAAE;YACnCsP,IAAI,CAACpN,OAAO,CAAEqN,IAAI,IAAKrC,eAAe,CAACnO,GAAG,CAACwQ,IAAI,CAAC,CAAC;UACrD;UACA;UACA,MAAMnH,SAAS,GAAGpI,KAAK,CAAC3K,WAAW,CAAC;UACpCga,wBAAwB,CAAC5C,aAAa,CAACrE,SAAS,CAAC9B,OAAO,CAAC,EAAEgJ,IAAI,CAAClM,MAAM,CAACpD,KAAK,CAAC,CAAC;QAClF;MACJ;IACJ,CAAC;IACDqP,wBAAwB,CAACP,GAAG,EAAE,EAAE,CAAC;IACjC,OAAO5B,eAAe;EAC1B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIF,eAAeA,CAAC7K,IAAI,EAAE1H,IAAI,EAAE;IACxB,IAAI,CAAC,IAAI,CAAC2M,aAAa,CAAClE,GAAG,CAACzI,IAAI,CAAC,EAAE;MAC/B,IAAI,CAAC2M,aAAa,CAAChF,GAAG,CAAC3H,IAAI,EAAE,IAAIkH,GAAG,CAAC,CAAC,CAAC;IAC3C;IACA,MAAM6N,WAAW,GAAG,IAAI,CAACpI,aAAa,CAAC9G,GAAG,CAAC7F,IAAI,CAAC;IAChD,IAAI,CAAC+U,WAAW,CAACtM,GAAG,CAACf,IAAI,CAAC,EAAE;MACxB,MAAMsN,UAAU,GAAGzL,MAAM,CAACO,wBAAwB,CAAC9J,IAAI,EAAE0H,IAAI,CAAC;MAC9DqN,WAAW,CAACpN,GAAG,CAACD,IAAI,EAAEsN,UAAU,CAAC;IACrC;EACJ;EACArC,qBAAqBA,CAAC3S,IAAI,EAAEiV,QAAQ,EAAEd,SAAS,EAAE;IAC7C,MAAMvE,GAAG,GAAG5P,IAAI,CAACiV,QAAQ,CAAC;IAC1B,MAAMb,aAAa,GAAGxE,GAAG,CAACuE,SAAS,CAAC;IACpC,IAAI,CAACvH,aAAa,CAACnL,IAAI,CAAC;MAAEyS,MAAM,EAAEtE,GAAG;MAAEuE,SAAS;MAAEC;IAAc,CAAC,CAAC;EACtE;EACA;AACJ;AACA;AACA;AACA;EACIvD,6BAA6BA,CAAA,EAAG;IAC5B,IAAI,IAAI,CAAClF,gCAAgC,KAAK,IAAI,EAAE;MAChD,IAAI,CAACA,gCAAgC,GAAG,IAAIzE,GAAG,CAAC,CAAC;IACrD;IACA9L,yCAAyC,CAAC,CAAC,CAACqM,OAAO,CAAC,CAAClC,KAAK,EAAEyD,GAAG,KAAK,IAAI,CAAC2C,gCAAgC,CAAChE,GAAG,CAACqB,GAAG,EAAEzD,KAAK,CAAC,CAAC;EAC9H;EACA;AACJ;AACA;AACA;AACA;EACI2P,+BAA+BA,CAAA,EAAG;IAC9B,IAAI,IAAI,CAACvJ,gCAAgC,KAAK,IAAI,EAAE;MAChDrQ,gCAAgC,CAAC,IAAI,CAACqQ,gCAAgC,CAAC;MACvE,IAAI,CAACA,gCAAgC,GAAG,IAAI;IAChD;EACJ;EACAwJ,oBAAoBA,CAAA,EAAG;IACnB;IACA;IACAC,YAAY,CAAC,IAAI,CAACxI,aAAa,EAAGyI,EAAE,IAAK;MACrCA,EAAE,CAACnB,MAAM,CAACmB,EAAE,CAAClB,SAAS,CAAC,GAAGkB,EAAE,CAACjB,aAAa;IAC9C,CAAC,CAAC;IACF;IACA,IAAI,CAACzH,aAAa,CAAClF,OAAO,CAAC,CAAC6N,IAAI,EAAEtV,IAAI,KAAK;MACvC,IAAIvH,iCAAiC,EAAE;QACnCE,YAAY,CAACsV,kBAAkB,CAACjO,IAAI,CAAC;MACzC;MACAsV,IAAI,CAAC7N,OAAO,CAAC,CAAC8N,UAAU,EAAE7N,IAAI,KAAK;QAC/B,IAAI,CAAC6N,UAAU,EAAE;UACb;UACA;UACA;UACA;UACA;UACA;UACA,OAAOvV,IAAI,CAAC0H,IAAI,CAAC;QACrB,CAAC,MACI;UACD6B,MAAM,CAACiM,cAAc,CAACxV,IAAI,EAAE0H,IAAI,EAAE6N,UAAU,CAAC;QACjD;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;IACF,IAAI,CAAC5I,aAAa,CAACrN,KAAK,CAAC,CAAC;IAC1B,IAAI,CAAC6N,6BAA6B,CAAC7N,KAAK,CAAC,CAAC;IAC1C,IAAI,CAAC4V,+BAA+B,CAAC,CAAC;IACtC;IACAtb,YAAY,CAACF,kBAAkB,CAAC;EACpC;EACAyX,iBAAiBA,CAAA,EAAG;IAChB,MAAMsE,eAAe,CAAC;IAEtBva,oBAAoB,CAACua,eAAe,EAAE;MAClC3J,SAAS,EAAE,CACP,GAAG,IAAI,CAACkB,qBAAqB,EAC7BxR,mCAAmC,CAAC,CAAC,CAAC,CAAC,EACvCmD,8BAA8B,EAC9B;QAAEuQ,OAAO,EAAEzX,yBAAyB;QAAEie,WAAW,EAAEha;MAA8B,CAAC;IAE1F,CAAC,CAAC;IACF,MAAMoQ,SAAS,GAAG,CACd;MAAEoD,OAAO,EAAEvT,QAAQ;MAAEsT,UAAU,EAAEA,CAAA,KAAM,IAAI0G,cAAc,CAAC,IAAI;IAAE,CAAC,EACjE;MAAEzG,OAAO,EAAErT,mBAAmB;MAAEwT,QAAQ,EAAE;QAAEuG,QAAQ,EAAE,IAAI,CAACtI;MAAmB;IAAE,CAAC,EACjF;MACI4B,OAAO,EAAEnT,mCAAmC;MAC5CkT,UAAU,EAAEA,CAAA,KAAM;QACd,IAAI,IAAI,CAAC1B,4BAA4B,EAAE;UACnC,MAAMsI,OAAO,GAAG7f,QAAQ,CAAC2I,8BAA8B,CAAC;UACxD,OAAQM,CAAC,IAAK;YACV4W,OAAO,CAAC7W,WAAW,CAACC,CAAC,CAAC;UAC1B,CAAC;QACL,CAAC,MACI;UACD,MAAMJ,gBAAgB,GAAG7I,QAAQ,CAACE,YAAY,CAAC;UAC/C,MAAMgO,MAAM,GAAGlO,QAAQ,CAACC,MAAM,CAAC;UAC/B,OAAQgJ,CAAC,IAAKiF,MAAM,CAAChF,iBAAiB,CAAC,MAAML,gBAAgB,CAACG,WAAW,CAACC,CAAC,CAAC,CAAC;QACjF;MACJ;IACJ,CAAC,EACD,GAAG,IAAI,CAAC6M,SAAS,EACjB,GAAG,IAAI,CAACiB,iBAAiB,CAC5B;IACD,MAAMlB,OAAO,GAAG,CAAC4J,eAAe,EAAE,IAAI,CAAC/J,qBAAqB,EAAE,IAAI,CAACG,OAAO,IAAI,EAAE,CAAC;IACjF3Q,oBAAoB,CAAC,IAAI,CAACkS,cAAc,EAAE;MACtCxB,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BC,OAAO;MACPE,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBD;IACJ,CAAC,EACD,sCAAuC,IAAI,CAAC;IAC5C,IAAI,CAAC4E,6BAA6B,CAAC,IAAI,CAACtD,cAAc,CAAC;EAC3D;EACA,IAAIxH,QAAQA,CAAA,EAAG;IACX,IAAI,IAAI,CAACiH,SAAS,KAAK,IAAI,EAAE;MACzB,OAAO,IAAI,CAACA,SAAS;IACzB;IACA,MAAMf,SAAS,GAAG,EAAE;IACpB,MAAMgK,eAAe,GAAG,IAAI,CAACrK,QAAQ,CAAC7F,QAAQ,CAACC,GAAG,CAAC7J,gBAAgB,EAAE,EAAE,CAAC;IACxE8Z,eAAe,CAACrO,OAAO,CAAEsO,IAAI,IAAK;MAC9B,IAAIA,IAAI,CAACjK,SAAS,EAAE;QAChBA,SAAS,CAACrK,IAAI,CAACsU,IAAI,CAACjK,SAAS,CAAC;MAClC;IACJ,CAAC,CAAC;IACF,IAAI,IAAI,CAACgB,iBAAiB,KAAK,IAAI,EAAE;MACjChB,SAAS,CAACrK,IAAI,CAAC,GAAG,IAAI,CAACqL,iBAAiB,CAAC;IAC7C;IACA,IAAI,CAACD,SAAS,GAAG5Q,QAAQ,CAAC+Z,MAAM,CAAC;MAAElK,SAAS;MAAEmK,MAAM,EAAE,IAAI,CAACxK,QAAQ,CAAC7F;IAAS,CAAC,CAAC;IAC/E,OAAO,IAAI,CAACiH,SAAS;EACzB;EACA;EACAqJ,0BAA0BA,CAACnH,QAAQ,EAAE;IACjC,MAAMnP,KAAK,GAAGuW,gBAAgB,CAACpH,QAAQ,CAAC;IACxC,OAAO,IAAI,CAAC7B,wBAAwB,CAACrH,GAAG,CAACjG,KAAK,CAAC,IAAI,IAAI;EAC3D;EACAwW,oBAAoBA,CAACtK,SAAS,EAAE;IAC5B,IAAI,CAACA,SAAS,IAAI,CAACA,SAAS,CAACtK,MAAM,IAAI,IAAI,CAAC0L,wBAAwB,CAAC9N,IAAI,KAAK,CAAC,EAC3E,OAAO,EAAE;IACb;IACA;IACA;IACA;IACA;IACA,OAAO4U,OAAO,CAACqC,gBAAgB,CAACvK,SAAS,EAAGiD,QAAQ,IAAK,IAAI,CAACmH,0BAA0B,CAACnH,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;EAC9G;EACA+E,sBAAsBA,CAAChI,SAAS,EAAE;IAC9B,IAAI,CAACA,SAAS,IAAI,CAACA,SAAS,CAACtK,MAAM,IAAI,IAAI,CAAC0L,wBAAwB,CAAC9N,IAAI,KAAK,CAAC,EAC3E,OAAO,EAAE;IACb,MAAMkX,kBAAkB,GAAGD,gBAAgB,CAACvK,SAAS,CAAC;IACtD,MAAM7B,SAAS,GAAG,IAAI,CAACmM,oBAAoB,CAACE,kBAAkB,CAAC;IAC/D,MAAMC,mBAAmB,GAAG,CAAC,GAAGD,kBAAkB,EAAE,GAAGrM,SAAS,CAAC;IACjE,MAAMuM,KAAK,GAAG,EAAE;IAChB,MAAMC,uBAAuB,GAAG,IAAI1X,GAAG,CAAC,CAAC;IACzC;IACA;IACA;IACA;IACAqW,YAAY,CAACmB,mBAAmB,EAAGxH,QAAQ,IAAK;MAC5C,MAAMnP,KAAK,GAAGuW,gBAAgB,CAACpH,QAAQ,CAAC;MACxC,IAAI,IAAI,CAAC7B,wBAAwB,CAACzE,GAAG,CAAC7I,KAAK,CAAC,EAAE;QAC1C,IAAI,CAAC6W,uBAAuB,CAAChO,GAAG,CAAC7I,KAAK,CAAC,EAAE;UACrC6W,uBAAuB,CAACnS,GAAG,CAAC1E,KAAK,CAAC;UAClC;UACA;UACA;UACA4W,KAAK,CAACE,OAAO,CAAC;YAAE,GAAG3H,QAAQ;YAAEK,KAAK,EAAE;UAAM,CAAC,CAAC;QAChD;MACJ,CAAC,MACI;QACDoH,KAAK,CAACE,OAAO,CAAC3H,QAAQ,CAAC;MAC3B;IACJ,CAAC,CAAC;IACF,OAAOyH,KAAK;EAChB;EACAlD,oBAAoBA,CAACxH,SAAS,EAAE;IAC5B,OAAO,IAAI,CAACsK,oBAAoB,CAACtK,SAAS,CAAC,CAACtK,MAAM,GAAG,CAAC;EAC1D;EACA+R,6BAA6BA,CAACnB,WAAW,EAAEiB,KAAK,EAAE;IAC9C,MAAMzD,GAAG,GAAGwC,WAAW,CAACiB,KAAK,CAAC;IAC9B,IAAIzD,GAAG,IAAIA,GAAG,CAAC+G,iBAAiB,EAAE;MAC9B,IAAI,CAACpE,eAAe,CAACc,KAAK,EAAEjB,WAAW,CAAC;MACxC,MAAM/G,QAAQ,GAAGuE,GAAG,CAAC+G,iBAAiB;MACtC,MAAMC,kBAAkB,GAAI9K,SAAS,IAAK,IAAI,CAACgI,sBAAsB,CAAChI,SAAS,CAAC;MAChF,IAAI,CAAC6G,qBAAqB,CAACP,WAAW,EAAEiB,KAAK,EAAE,mBAAmB,CAAC;MACnEzD,GAAG,CAAC+G,iBAAiB,GAAIE,KAAK,IAAKxL,QAAQ,CAACwL,KAAK,EAAED,kBAAkB,CAAC;IAC1E;EACJ;AACJ;AACA,SAASnK,aAAaA,CAAA,EAAG;EACrB,OAAO;IACHyB,MAAM,EAAE,IAAIpD,gBAAgB,CAAC,CAAC;IAC9BQ,SAAS,EAAE,IAAIV,iBAAiB,CAAC,CAAC;IAClC8D,SAAS,EAAE,IAAI/D,iBAAiB,CAAC,CAAC;IAClCiE,IAAI,EAAE,IAAI/D,YAAY,CAAC;EAC3B,CAAC;AACL;AACA,SAAS4I,qBAAqBA,CAAClO,KAAK,EAAE;EAClC,MAAMqK,GAAG,GAAGuD,eAAe,CAAC5N,KAAK,CAAC;EAClC,OAAO,CAAC,CAACqK,GAAG,EAAErE,UAAU;AAC5B;AACA,SAAS4H,eAAeA,CAAC5N,KAAK,EAAE;EAC5B,OAAOA,KAAK,CAAC+M,IAAI,IAAI,IAAI;AAC7B;AACA,SAASmC,cAAcA,CAAClP,KAAK,EAAE;EAC3B,OAAOA,KAAK,CAACsJ,cAAc,CAAC,MAAM,CAAC;AACvC;AACA,SAAS6E,UAAUA,CAACnO,KAAK,EAAE;EACvB,OAAOkP,cAAc,CAAClP,KAAK,CAAC;AAChC;AACA,SAASyM,aAAaA,CAAC8E,OAAO,EAAE;EAC5B,OAAOA,OAAO,YAAYC,QAAQ,GAAGD,OAAO,CAAC,CAAC,GAAGA,OAAO;AAC5D;AACA,SAAS9C,OAAOA,CAAC3U,MAAM,EAAE;EACrB,MAAM2X,GAAG,GAAG,EAAE;EACd3X,MAAM,CAACoI,OAAO,CAAElC,KAAK,IAAK;IACtB,IAAI6C,KAAK,CAACC,OAAO,CAAC9C,KAAK,CAAC,EAAE;MACtByR,GAAG,CAACvV,IAAI,CAAC,GAAGuS,OAAO,CAACzO,KAAK,CAAC,CAAC;IAC/B,CAAC,MACI;MACDyR,GAAG,CAACvV,IAAI,CAAC8D,KAAK,CAAC;IACnB;EACJ,CAAC,CAAC;EACF,OAAOyR,GAAG;AACd;AACA,SAASC,UAAUA,CAAC1R,KAAK,EAAE;EACvB,OAAOA,KAAK;AAChB;AACA,SAAS8Q,gBAAgBA,CAACvK,SAAS,EAAEoL,KAAK,GAAGD,UAAU,EAAE;EACrD,MAAMD,GAAG,GAAG,EAAE;EACd,KAAK,IAAIjI,QAAQ,IAAIjD,SAAS,EAAE;IAC5B,IAAIpP,uBAAuB,CAACqS,QAAQ,CAAC,EAAE;MACnCA,QAAQ,GAAGA,QAAQ,CAACoI,UAAU;IAClC;IACA,IAAI/O,KAAK,CAACC,OAAO,CAAC0G,QAAQ,CAAC,EAAE;MACzBiI,GAAG,CAACvV,IAAI,CAAC,GAAG4U,gBAAgB,CAACtH,QAAQ,EAAEmI,KAAK,CAAC,CAAC;IAClD,CAAC,MACI;MACDF,GAAG,CAACvV,IAAI,CAACyV,KAAK,CAACnI,QAAQ,CAAC,CAAC;IAC7B;EACJ;EACA,OAAOiI,GAAG;AACd;AACA,SAASI,gBAAgBA,CAACrI,QAAQ,EAAEsE,KAAK,EAAE;EACvC,OAAOtE,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,CAACsE,KAAK,CAAC;AACtE;AACA,SAAS8C,gBAAgBA,CAACpH,QAAQ,EAAE;EAChC,OAAOqI,gBAAgB,CAACrI,QAAQ,EAAE,SAAS,CAAC,IAAIA,QAAQ;AAC5D;AACA,SAASkF,qBAAqBA,CAAC1O,KAAK,EAAE;EAClC,OAAOA,KAAK,CAACsJ,cAAc,CAAC,UAAU,CAAC;AAC3C;AACA,SAASuG,YAAYA,CAAC/V,MAAM,EAAElB,EAAE,EAAE;EAC9B,KAAK,IAAIkZ,GAAG,GAAGhY,MAAM,CAACmC,MAAM,GAAG,CAAC,EAAE6V,GAAG,IAAI,CAAC,EAAEA,GAAG,EAAE,EAAE;IAC/ClZ,EAAE,CAACkB,MAAM,CAACgY,GAAG,CAAC,EAAEA,GAAG,CAAC;EACxB;AACJ;AACA,SAASlJ,gBAAgBA,CAACC,IAAI,EAAEkJ,YAAY,EAAE;EAC1C,OAAO,IAAI1W,KAAK,CAAC,GAAGwN,IAAI,wBAAwBkJ,YAAY,oCAAoC,CAAC;AACrG;AACA,MAAM3B,cAAc,CAAC;EACjB4B,OAAO;EACPnX,WAAWA,CAACmX,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;EAC1B;EACAC,iBAAiBA,CAAC7F,UAAU,EAAE;IAC1B,IAAI,CAAC4F,OAAO,CAAC7F,oBAAoB,CAACC,UAAU,CAAC;IAC7C,OAAO,IAAIpV,gBAAgB,CAACoV,UAAU,CAAC;EAC3C;EACM8F,kBAAkBA,CAAC9F,UAAU,EAAE;IAAA,IAAA+F,MAAA;IAAA,OAAAlX,iBAAA;MACjC,MAAMkX,MAAI,CAACH,OAAO,CAAC3F,qBAAqB,CAACD,UAAU,CAAC;MACpD,OAAO,IAAIpV,gBAAgB,CAACoV,UAAU,CAAC;IAAC;EAC5C;EACAgG,iCAAiCA,CAAChG,UAAU,EAAE;IAC1C,MAAMiG,eAAe,GAAG,IAAI,CAACJ,iBAAiB,CAAC7F,UAAU,CAAC;IAC1D,MAAMkG,kBAAkB,GAAG,IAAI,CAACN,OAAO,CAACxF,sBAAsB,CAACJ,UAAU,CAAC;IAC1E,OAAO,IAAInV,4BAA4B,CAACob,eAAe,EAAEC,kBAAkB,CAAC;EAChF;EACMC,kCAAkCA,CAACnG,UAAU,EAAE;IAAA,IAAAoG,MAAA;IAAA,OAAAvX,iBAAA;MACjD,MAAMoX,eAAe,SAASG,MAAI,CAACN,kBAAkB,CAAC9F,UAAU,CAAC;MACjE,MAAMkG,kBAAkB,GAAGE,MAAI,CAACR,OAAO,CAACxF,sBAAsB,CAACJ,UAAU,CAAC;MAC1E,OAAO,IAAInV,4BAA4B,CAACob,eAAe,EAAEC,kBAAkB,CAAC;IAAC;EACjF;EACAG,UAAUA,CAAA,EAAG,CAAE;EACfC,aAAaA,CAACjY,IAAI,EAAE,CAAE;EACtBkY,WAAWA,CAACvG,UAAU,EAAE;IACpB,MAAMwG,IAAI,GAAG,IAAI,CAACZ,OAAO,CAACzF,kBAAkB,CAAC,CAAC,CAACpQ,OAAO,CAACiQ,UAAU,CAAC;IAClE,OAAQwG,IAAI,IAAIA,IAAI,CAAC9O,EAAE,IAAK1D,SAAS;EACzC;AACJ;;AAEA;AACA;AACA;AACA,IAAIyS,kBAAkB,GAAG,CAAC;AAC1B;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAAA,EAAG;EAClB,OAAOC,WAAW,CAACC,QAAQ;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMD,WAAW,CAAC;EACd,OAAOE,SAAS,GAAG,IAAI;EACvB,WAAWD,QAAQA,CAAA,EAAG;IAClB,OAAQD,WAAW,CAACE,SAAS,GAAGF,WAAW,CAACE,SAAS,IAAI,IAAIF,WAAW,CAAC,CAAC;EAC9E;EACA;AACJ;AACA;AACA;EACI,OAAOG,2BAA2B;EAClC;AACJ;AACA;AACA;EACI,OAAOC,wCAAwC;EAC/C;AACJ;AACA;AACA;EACI,OAAOC,0CAA0C;EACjD;AACJ;AACA;AACA;EACIC,wBAAwB;EACxB;AACJ;AACA;AACA;EACIC,2BAA2B,GAAG1W,4BAA4B;EAC1D;AACJ;AACA;AACA;EACI2W,qCAAqC;EACrC;AACJ;AACA;AACA;EACIC,uCAAuC;EACvC;AACJ;AACA;AACA;EACIC,qCAAqC;EACrC;AACJ;AACA;AACA;EACIC,uCAAuC;EACvC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOC,mBAAmBA,CAAClL,QAAQ,EAAEvC,QAAQ,EAAEjF,OAAO,EAAE;IACpD,MAAM+Q,OAAO,GAAGe,WAAW,CAACC,QAAQ;IACpChB,OAAO,CAAC2B,mBAAmB,CAAClL,QAAQ,EAAEvC,QAAQ,EAAEjF,OAAO,CAAC;IACxD,OAAO+Q,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;EACI,OAAO4B,oBAAoBA,CAAA,EAAG;IAC1Bb,WAAW,CAACC,QAAQ,CAACY,oBAAoB,CAAC,CAAC;EAC/C;EACA,OAAOC,iBAAiBA,CAACC,MAAM,EAAE;IAC7B,OAAOf,WAAW,CAACC,QAAQ,CAACa,iBAAiB,CAACC,MAAM,CAAC;EACzD;EACA;AACJ;AACA;AACA;EACI,OAAO3L,sBAAsBA,CAACC,SAAS,EAAE;IACrC,OAAO2K,WAAW,CAACC,QAAQ,CAAC7K,sBAAsB,CAACC,SAAS,CAAC;EACjE;EACA;AACJ;AACA;AACA;AACA;EACI,OAAOgD,iBAAiBA,CAAA,EAAG;IACvB,OAAO2H,WAAW,CAACC,QAAQ,CAAC5H,iBAAiB,CAAC,CAAC;EACnD;EACA,OAAO5C,cAAcA,CAACC,QAAQ,EAAE1G,QAAQ,EAAE;IACtC,OAAOgR,WAAW,CAACC,QAAQ,CAACxK,cAAc,CAACC,QAAQ,EAAE1G,QAAQ,CAAC;EAClE;EACA,OAAOgH,iBAAiBA,CAAChD,SAAS,EAAEhE,QAAQ,EAAE;IAC1C,OAAOgR,WAAW,CAACC,QAAQ,CAACjK,iBAAiB,CAAChD,SAAS,EAAEhE,QAAQ,CAAC;EACtE;EACA,OAAOmH,iBAAiBA,CAACC,SAAS,EAAEpH,QAAQ,EAAE;IAC1C,OAAOgR,WAAW,CAACC,QAAQ,CAAC9J,iBAAiB,CAACC,SAAS,EAAEpH,QAAQ,CAAC;EACtE;EACA,OAAOqH,YAAYA,CAACC,IAAI,EAAEtH,QAAQ,EAAE;IAChC,OAAOgR,WAAW,CAACC,QAAQ,CAAC5J,YAAY,CAACC,IAAI,EAAEtH,QAAQ,CAAC;EAC5D;EACA,OAAOgS,gBAAgBA,CAAChO,SAAS,EAAEqE,QAAQ,EAAE;IACzC,OAAO2I,WAAW,CAACC,QAAQ,CAACe,gBAAgB,CAAChO,SAAS,EAAEqE,QAAQ,CAAC;EACrE;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,OAAOD,kCAAkCA,CAACpE,SAAS,EAAEqE,QAAQ,EAAE;IAC3D,OAAO2I,WAAW,CAACC,QAAQ,CAAC7I,kCAAkC,CAACpE,SAAS,EAAEqE,QAAQ,CAAC;EACvF;EACA,OAAOb,gBAAgBA,CAAClP,KAAK,EAAEmP,QAAQ,EAAE;IACrC,OAAOuJ,WAAW,CAACC,QAAQ,CAACzJ,gBAAgB,CAAClP,KAAK,EAAEmP,QAAQ,CAAC;EACjE;EACA,OAAOhZ,MAAMA,CAAC6J,KAAK,EAAE2Z,aAAa,EAAEC,KAAK,EAAE;IACvC,OAAOlB,WAAW,CAACC,QAAQ,CAACxiB,MAAM,CAAC6J,KAAK,EAAE2Z,aAAa,EAAE3c,kBAAkB,CAAC4c,KAAK,CAAC,CAAC;EACvF;EACA;EACA,OAAO3T,GAAGA,CAACjG,KAAK,EAAE2Z,aAAa,GAAGtd,QAAQ,CAACwd,kBAAkB,EAAED,KAAK,GAAG3c,WAAW,CAAC6c,OAAO,EAAE;IACxF,OAAOpB,WAAW,CAACC,QAAQ,CAACxiB,MAAM,CAAC6J,KAAK,EAAE2Z,aAAa,EAAEC,KAAK,CAAC;EACnE;EACA;AACJ;AACA;AACA;AACA;EACI,OAAO9b,qBAAqBA,CAACS,EAAE,EAAE;IAC7B,OAAOma,WAAW,CAACC,QAAQ,CAAC7a,qBAAqB,CAACS,EAAE,CAAC;EACzD;EACA,OAAOwb,eAAeA,CAACrO,SAAS,EAAE;IAC9B,OAAOgN,WAAW,CAACC,QAAQ,CAACoB,eAAe,CAACrO,SAAS,CAAC;EAC1D;EACA,OAAOsO,kBAAkBA,CAAA,EAAG;IACxB,OAAOtB,WAAW,CAACC,QAAQ,CAACqB,kBAAkB,CAAC,CAAC;EACpD;EACA,OAAOC,OAAOA,CAACC,MAAM,EAAE3b,EAAE,EAAE4b,OAAO,EAAE;IAChC,OAAOzB,WAAW,CAACC,QAAQ,CAACsB,OAAO,CAACC,MAAM,EAAE3b,EAAE,EAAE4b,OAAO,CAAC;EAC5D;EACA,WAAWtO,QAAQA,CAAA,EAAG;IAClB,OAAO6M,WAAW,CAACC,QAAQ,CAAC9M,QAAQ;EACxC;EACA,WAAWuC,QAAQA,CAAA,EAAG;IAClB,OAAOsK,WAAW,CAACC,QAAQ,CAACvK,QAAQ;EACxC;EACA,OAAOgM,YAAYA,CAAA,EAAG;IAClB,OAAO1B,WAAW,CAACC,QAAQ,CAACyB,YAAY,CAAC,CAAC;EAC9C;EACA;EACAvO,QAAQ,GAAG,IAAI;EACfuC,QAAQ,GAAG,IAAI;EACfiM,SAAS,GAAG,IAAI;EAChBC,cAAc,GAAG,IAAI;EACrBC,eAAe,GAAG,EAAE;EACpB;AACJ;AACA;AACA;AACA;EACIC,wBAAwB,GAAG,KAAK;EAChC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIlB,mBAAmBA,CAAClL,QAAQ,EAAEvC,QAAQ,EAAEjF,OAAO,EAAE;IAC7C,IAAI,IAAI,CAACiF,QAAQ,IAAI,IAAI,CAACuC,QAAQ,EAAE;MAChC,MAAM,IAAIpN,KAAK,CAAC,8DAA8D,CAAC;IACnF;IACA0X,WAAW,CAACG,2BAA2B,GAAGjS,OAAO,EAAE6T,QAAQ;IAC3D/B,WAAW,CAACI,wCAAwC,GAAGlS,OAAO,EAAE8T,sBAAsB;IACtFhC,WAAW,CAACK,0CAA0C,GAAGnS,OAAO,EAAE+T,wBAAwB;IAC1F,IAAI,CAAC9O,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACuC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACiM,SAAS,GAAG,IAAIzO,eAAe,CAAC,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACuC,QAAQ,CAAC;IAClE;IACA;IACA;IACA;IACAjR,oCAAoC,CAAC,IAAI,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;EACIoc,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACS,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACK,SAAS,GAAG,IAAI;IACrB,IAAI,CAACxO,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACuC,QAAQ,GAAG,IAAI;IACpBsK,WAAW,CAACG,2BAA2B,GAAG9S,SAAS;IACnD5I,oCAAoC,CAAC,KAAK,CAAC;EAC/C;EACA6c,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACY,8BAA8B,CAAC,CAAC;IACrCvd,wBAAwB,CAAC,CAAC;IAC1B,IAAI,IAAI,CAACgd,SAAS,KAAK,IAAI,EAAE;MACzB,IAAI,CAACQ,QAAQ,CAACtF,oBAAoB,CAAC,CAAC;IACxC;IACA,IAAI,CAAC8E,SAAS,GAAG,IAAIzO,eAAe,CAAC,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACuC,QAAQ,CAAC;IAClE;IACA7Q,4BAA4B,CAAC,IAAI,CAAC6b,qCAAqC,IAAI/W,iCAAiC,CAAC;IAC7G;IACA5E,6BAA6B,CAAC,IAAI,CAAC4b,uCAAuC,IAAI/W,mCAAmC,CAAC;IAClH;IACA;IACA;IACA,IAAI;MACA,IAAI,CAACwY,qBAAqB,CAAC,CAAC;IAChC,CAAC,SACO;MACJ,IAAI;QACA,IAAI,IAAI,CAACC,2BAA2B,CAAC,CAAC,EAAE;UACpC,IAAI,CAACC,qBAAqB,CAAC,CAAC;QAChC;MACJ,CAAC,SACO;QACJ,IAAI,CAACV,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACtB,wBAAwB,GAAGjT,SAAS;QACzC,IAAI,CAACmT,qCAAqC,GAAGnT,SAAS;QACtD,IAAI,CAACoT,uCAAuC,GAAGpT,SAAS;QACxD,IAAI,CAACkT,2BAA2B,GAAG1W,4BAA4B;MACnE;IACJ;IACA,OAAO,IAAI;EACf;EACAiX,iBAAiBA,CAACC,MAAM,EAAE;IACtB,IAAIA,MAAM,CAACwB,MAAM,IAAI,IAAI,EAAE;MACvB,MAAM,IAAIja,KAAK,CAAC,oDAAoD,CAAC;IACzE;IACA,IAAIyY,MAAM,CAACvN,SAAS,KAAKnG,SAAS,EAAE;MAChC,IAAI,CAAC8U,QAAQ,CAAChN,oBAAoB,CAAC4L,MAAM,CAACvN,SAAS,CAAC;IACxD;IACA,OAAO,IAAI;EACf;EACA4B,sBAAsBA,CAACC,SAAS,EAAE;IAC9B,IAAI,CAACmN,qBAAqB,CAAC,gCAAgC,EAAE,2BAA2B,CAAC;IACzF;IACA;IACA;IACA;IACA,IAAI,CAACN,8BAA8B,CAAC,CAAC;IACrC;IACA;IACA,IAAI,CAAC5B,wBAAwB,GAAGjL,SAAS,CAAC0M,QAAQ;IAClD,IAAI,CAACvB,qCAAqC,GAAGnL,SAAS,CAAC2M,sBAAsB;IAC7E,IAAI,CAACvB,uCAAuC,GAAGpL,SAAS,CAAC4M,wBAAwB;IACjF,IAAI,CAAC1B,2BAA2B,GAAGlL,SAAS,CAACL,kBAAkB,IAAInL,4BAA4B;IAC/F;IACA;IACA,IAAI,CAAC6W,qCAAqC,GAAGzb,4BAA4B,CAAC,CAAC;IAC3EJ,4BAA4B,CAAC,IAAI,CAAC4d,iCAAiC,CAAC,CAAC,CAAC;IACtE,IAAI,CAAC9B,uCAAuC,GAAGxb,6BAA6B,CAAC,CAAC;IAC9EJ,6BAA6B,CAAC,IAAI,CAAC2d,mCAAmC,CAAC,CAAC,CAAC;IACzE,IAAI,CAACP,QAAQ,CAAC/M,sBAAsB,CAACC,SAAS,CAAC;IAC/C,OAAO,IAAI;EACf;EACAgD,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC8J,QAAQ,CAAC9J,iBAAiB,CAAC,CAAC;EAC5C;EACA5a,MAAMA,CAAC6J,KAAK,EAAE2Z,aAAa,EAAEC,KAAK,EAAE;IAChC,IAAI5Z,KAAK,KAAKqb,OAAO,EAAE;MACnB,OAAO,IAAI;IACf;IACA,MAAMC,SAAS,GAAG,CAAC,CAAC;IACpB,MAAMC,MAAM,GAAG,IAAI,CAAC9N,aAAa,CAACzH,QAAQ,CAACC,GAAG,CAACjG,KAAK,EAAEsb,SAAS,EAAEte,kBAAkB,CAAC4c,KAAK,CAAC,CAAC;IAC3F,OAAO2B,MAAM,KAAKD,SAAS,GACrB,IAAI,CAACT,QAAQ,CAAC7U,QAAQ,CAACC,GAAG,CAACjG,KAAK,EAAE2Z,aAAa,EAAEC,KAAK,CAAC,GACvD2B,MAAM;EAChB;EACA;EACAtV,GAAGA,CAACjG,KAAK,EAAE2Z,aAAa,GAAGtd,QAAQ,CAACwd,kBAAkB,EAAED,KAAK,GAAG3c,WAAW,CAAC6c,OAAO,EAAE;IACjF,OAAO,IAAI,CAAC3jB,MAAM,CAAC6J,KAAK,EAAE2Z,aAAa,EAAEC,KAAK,CAAC;EACnD;EACA9b,qBAAqBA,CAACS,EAAE,EAAE;IACtB,OAAOT,qBAAqB,CAAC,IAAI,CAAC3H,MAAM,CAAC4H,mBAAmB,CAAC,EAAEQ,EAAE,CAAC;EACtE;EACA0b,OAAOA,CAACC,MAAM,EAAE3b,EAAE,EAAE4b,OAAO,EAAE;IACzB,MAAMqB,MAAM,GAAGtB,MAAM,CAACuB,GAAG,CAAEC,CAAC,IAAK,IAAI,CAACvlB,MAAM,CAACulB,CAAC,CAAC,CAAC;IAChD,OAAOnd,EAAE,CAACod,KAAK,CAACxB,OAAO,EAAEqB,MAAM,CAAC;EACpC;EACArN,cAAcA,CAACC,QAAQ,EAAE1G,QAAQ,EAAE;IAC/B,IAAI,CAACwT,qBAAqB,CAAC,gBAAgB,EAAE,0BAA0B,CAAC;IACxE,IAAI,CAACL,QAAQ,CAAC1M,cAAc,CAACC,QAAQ,EAAE1G,QAAQ,CAAC;IAChD,OAAO,IAAI;EACf;EACAgH,iBAAiBA,CAAChD,SAAS,EAAEhE,QAAQ,EAAE;IACnC,IAAI,CAACwT,qBAAqB,CAAC,mBAAmB,EAAE,6BAA6B,CAAC;IAC9E,IAAI,CAACL,QAAQ,CAACnM,iBAAiB,CAAChD,SAAS,EAAEhE,QAAQ,CAAC;IACpD,OAAO,IAAI;EACf;EACAoI,kCAAkCA,CAACpE,SAAS,EAAEqE,QAAQ,EAAE;IACpD,IAAI,CAACmL,qBAAqB,CAAC,4CAA4C,EAAE,6EAA6E,CAAC;IACvJ,IAAI,CAACL,QAAQ,CAAC/K,kCAAkC,CAACpE,SAAS,EAAEqE,QAAQ,CAAC;IACrE,OAAO,IAAI;EACf;EACAlB,iBAAiBA,CAACC,SAAS,EAAEpH,QAAQ,EAAE;IACnC,IAAI,CAACwT,qBAAqB,CAAC,mBAAmB,EAAE,6BAA6B,CAAC;IAC9E,IAAI,CAACL,QAAQ,CAAChM,iBAAiB,CAACC,SAAS,EAAEpH,QAAQ,CAAC;IACpD,OAAO,IAAI;EACf;EACAqH,YAAYA,CAACC,IAAI,EAAEtH,QAAQ,EAAE;IACzB,IAAI,CAACwT,qBAAqB,CAAC,cAAc,EAAE,wBAAwB,CAAC;IACpE,IAAI,CAACL,QAAQ,CAAC9L,YAAY,CAACC,IAAI,EAAEtH,QAAQ,CAAC;IAC1C,OAAO,IAAI;EACf;EACA;AACJ;AACA;EACIwH,gBAAgBA,CAAClP,KAAK,EAAEmP,QAAQ,EAAE;IAC9B,IAAI,CAAC+L,qBAAqB,CAAC,kBAAkB,EAAE,mBAAmB,CAAC;IACnE,IAAI,CAACL,QAAQ,CAAC3L,gBAAgB,CAAClP,KAAK,EAAEmP,QAAQ,CAAC;IAC/C,OAAO,IAAI;EACf;EACAuK,gBAAgBA,CAAChO,SAAS,EAAEqE,QAAQ,EAAE;IAClC,OAAO,IAAI,CAACrB,iBAAiB,CAAChD,SAAS,EAAE;MAAE3D,GAAG,EAAE;QAAEgI,QAAQ;QAAE6L,WAAW,EAAE;MAAK;IAAE,CAAC,CAAC;EACtF;EACA7B,eAAeA,CAAC3Z,IAAI,EAAE;IAClB,MAAMyb,qBAAqB,GAAG,IAAI,CAAC1lB,MAAM,CAACsM,qBAAqB,CAAC;IAChE,MAAMqZ,QAAQ,GAAG,OAAOtD,kBAAkB,EAAE,EAAE;IAC9CqD,qBAAqB,CAACnZ,iBAAiB,CAACoZ,QAAQ,CAAC;IACjD,IAAIxiB,wBAAwB,CAAC8G,IAAI,CAAC,EAAE;MAChC,MAAM,IAAIY,KAAK,CAAC,cAAcZ,IAAI,CAACoO,IAAI,6BAA6B,GAChE,6EAA6E,CAAC;IACtF;IACA,MAAMiE,YAAY,GAAGrS,IAAI,CAACsS,IAAI;IAC9B,IAAI,CAACD,YAAY,EAAE;MACf,MAAM,IAAIzR,KAAK,CAAC,kBAAkB3I,UAAU,CAAC+H,IAAI,CAAC,0BAA0B,CAAC;IACjF;IACA,MAAM2b,gBAAgB,GAAG,IAAI7hB,wBAAwB,CAACuY,YAAY,CAAC;IACnE,MAAMuJ,aAAa,GAAGA,CAAA,KAAM;MACxB,MAAMhZ,YAAY,GAAG+Y,gBAAgB,CAAC3F,MAAM,CAAC/Z,QAAQ,CAAC4f,IAAI,EAAE,EAAE,EAAE,IAAIH,QAAQ,EAAE,EAAE,IAAI,CAACrO,aAAa,CAAC;MACnG,OAAO,IAAI,CAAC3P,qBAAqB,CAAC,MAAM,IAAIiF,gBAAgB,CAACC,YAAY,CAAC,CAAC;IAC/E,CAAC;IACD,MAAMkZ,QAAQ,GAAG,IAAI,CAAC/lB,MAAM,CAAC2M,wBAAwB,EAAE,KAAK,CAAC;IAC7D,MAAMwB,MAAM,GAAG4X,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC/lB,MAAM,CAACE,MAAM,EAAE,IAAI,CAAC;IAC1D,MAAM8lB,OAAO,GAAG7X,MAAM,GAAGA,MAAM,CAACiB,GAAG,CAACyW,aAAa,CAAC,GAAGA,aAAa,CAAC,CAAC;IACpE,IAAI,CAACzB,eAAe,CAAC1Y,IAAI,CAACsa,OAAO,CAAC;IAClC,OAAOA,OAAO;EAClB;EACA;AACJ;AACA;AACA;EACI,IAAItB,QAAQA,CAAA,EAAG;IACX,IAAI,IAAI,CAACR,SAAS,KAAK,IAAI,EAAE;MACzB,MAAM,IAAIrZ,KAAK,CAAC,kDAAkD,CAAC;IACvE;IACA,OAAO,IAAI,CAACqZ,SAAS;EACzB;EACA;AACJ;AACA;AACA;EACI,IAAI5M,aAAaA,CAAA,EAAG;IAChB,IAAI,IAAI,CAAC6M,cAAc,KAAK,IAAI,EAAE;MAC9B,IAAI,CAACA,cAAc,GAAG,IAAI,CAACO,QAAQ,CAACvJ,QAAQ,CAAC,CAAC;IAClD;IACA,OAAO,IAAI,CAACgJ,cAAc;EAC9B;EACAY,qBAAqBA,CAACkB,UAAU,EAAEC,iBAAiB,EAAE;IACjD,IAAI,IAAI,CAAC/B,cAAc,KAAK,IAAI,EAAE;MAC9B,MAAM,IAAItZ,KAAK,CAAC,UAAUqb,iBAAiB,uDAAuD,GAC9F,mDAAmDD,UAAU,KAAK,CAAC;IAC3E;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIxB,8BAA8BA,CAAA,EAAG;IAC7B;IACA;IACA,IAAI,CAAC,IAAI,CAACJ,wBAAwB,IAAI,IAAI,CAACF,cAAc,KAAK,IAAI,EAAE;MAChErc,wCAAwC,CAAC,CAAC;IAC9C;IACA,IAAI,CAACuc,wBAAwB,GAAG,IAAI;EACxC;EACAM,qBAAqBA,CAAA,EAAG;IACpB,IAAIwB,UAAU,GAAG,CAAC;IAClB,IAAI,CAAC/B,eAAe,CAAC1S,OAAO,CAAEsU,OAAO,IAAK;MACtC,IAAI;QACAA,OAAO,CAAC/V,OAAO,CAAC,CAAC;MACrB,CAAC,CACD,OAAO/G,CAAC,EAAE;QACNid,UAAU,EAAE;QACZC,OAAO,CAACrX,KAAK,CAAC,mCAAmC,EAAE;UAC/CwG,SAAS,EAAEyQ,OAAO,CAACjZ,iBAAiB;UACpCsZ,UAAU,EAAEnd;QAChB,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;IACF,IAAI,CAACkb,eAAe,GAAG,EAAE;IACzB,IAAI+B,UAAU,GAAG,CAAC,IAAI,IAAI,CAACG,2BAA2B,CAAC,CAAC,EAAE;MACtD,MAAMzb,KAAK,CAAC,GAAGsb,UAAU,IAAIA,UAAU,KAAK,CAAC,GAAG,WAAW,GAAG,YAAY,GAAG,GACzE,6BAA6B,CAAC;IACtC;EACJ;EACAG,2BAA2BA,CAAA,EAAG;IAC1B,MAAMC,eAAe,GAAG,IAAI,CAAC1D,wBAAwB;IACrD,MAAM2D,kBAAkB,GAAGjE,WAAW,CAACG,2BAA2B;IAClE;IACA,IAAI,CAAC6D,eAAe,IAAI,CAACC,kBAAkB,EAAE;MACzC,OAAOva,0CAA0C;IACrD;IACA;IACA,OAAQsa,eAAe,EAAEE,aAAa,IAClCD,kBAAkB,EAAEC,aAAa,IACjC,IAAI,CAAC7B,2BAA2B,CAAC,CAAC;EAC1C;EACAI,iCAAiCA,CAAA,EAAG;IAChC;IACA,OAAQ,IAAI,CAACjC,qCAAqC,IAC9CR,WAAW,CAACI,wCAAwC,IACpDzW,iCAAiC;EACzC;EACA+Y,mCAAmCA,CAAA,EAAG;IAClC;IACA,OAAQ,IAAI,CAACjC,uCAAuC,IAChDT,WAAW,CAACK,0CAA0C,IACtDzW,mCAAmC;EAC3C;EACAyY,2BAA2BA,CAAA,EAAG;IAC1B,OAAQ,IAAI,CAAC/B,wBAAwB,EAAE6D,gBAAgB,IACnDnE,WAAW,CAACG,2BAA2B,EAAEgE,gBAAgB,IACzDza,0CAA0C;EAClD;EACA0a,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAAC7D,2BAA2B;EAC3C;EACA+B,qBAAqBA,CAAA,EAAG;IACpB;IACA,IAAI,IAAI,CAACV,cAAc,KAAK,IAAI,EAAE;MAC9B;IACJ;IACA;IACA;IACA,MAAMyC,YAAY,GAAG,IAAI,CAAC5mB,MAAM,CAACsM,qBAAqB,CAAC;IACvD,IAAI;MACA,IAAI,CAAC6X,cAAc,CAAClU,OAAO,CAAC,CAAC;IACjC,CAAC,CACD,OAAO/G,CAAC,EAAE;MACN,IAAI,IAAI,CAACod,2BAA2B,CAAC,CAAC,EAAE;QACpC,MAAMpd,CAAC;MACX,CAAC,MACI;QACDkd,OAAO,CAACrX,KAAK,CAAC,0CAA0C,EAAE;UACtDwG,SAAS,EAAE,IAAI,CAAC4O,cAAc,CAAC9V,QAAQ;UACvCgY,UAAU,EAAEnd;QAChB,CAAC,CAAC;MACN;IACJ,CAAC,SACO;MACJ0d,YAAY,CAACna,qBAAqB,GAAG,CAAC;IAC1C;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIwX,YAAYA,CAAA,EAAG;IACX,IAAI,CAACjkB,MAAM,CAAC8B,yBAAyB,CAAC,CAACmN,KAAK,CAAC,CAAC;IAC9C,IAAI,CAACjP,MAAM,CAAC4B,gBAAgB,CAAC,CAACqN,KAAK,CAAC,CAAC;EACzC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiW,OAAO,GAAG3C,WAAW;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASviB,MAAMA,CAAC+jB,MAAM,EAAE3b,EAAE,EAAE;EACxB,MAAMoZ,OAAO,GAAGe,WAAW,CAACC,QAAQ;EACpC;EACA,OAAO,YAAY;IACf,OAAOhB,OAAO,CAACsC,OAAO,CAACC,MAAM,EAAE3b,EAAE,EAAE,IAAI,CAAC;EAC5C,CAAC;AACL;AACA;AACA;AACA;AACA,MAAMye,kBAAkB,CAAC;EACrBC,UAAU;EACVzc,WAAWA,CAACyc,UAAU,EAAE;IACpB,IAAI,CAACA,UAAU,GAAGA,UAAU;EAChC;EACAC,UAAUA,CAAA,EAAG;IACT,MAAMnP,SAAS,GAAG,IAAI,CAACkP,UAAU,CAAC,CAAC;IACnC,IAAIlP,SAAS,EAAE;MACX2K,WAAW,CAAC5K,sBAAsB,CAACC,SAAS,CAAC;IACjD;EACJ;EACA5X,MAAMA,CAAC+jB,MAAM,EAAE3b,EAAE,EAAE;IACf,MAAM4e,IAAI,GAAG,IAAI;IACjB;IACA,OAAO,YAAY;MACfA,IAAI,CAACD,UAAU,CAAC,CAAC;MACjB,OAAO/mB,MAAM,CAAC+jB,MAAM,EAAE3b,EAAE,CAAC,CAAC6e,IAAI,CAAC,IAAI,CAAC;IACxC,CAAC;EACL;AACJ;AACA,SAASC,UAAUA,CAACtP,SAAS,EAAExP,EAAE,EAAE;EAC/B,IAAIA,EAAE,EAAE;IACJ;IACA,OAAO,YAAY;MACf,MAAMoZ,OAAO,GAAGe,WAAW,CAACC,QAAQ;MACpC,IAAI5K,SAAS,EAAE;QACX4J,OAAO,CAAC7J,sBAAsB,CAACC,SAAS,CAAC;MAC7C;MACA,OAAOxP,EAAE,CAACod,KAAK,CAAC,IAAI,CAAC;IACzB,CAAC;EACL;EACA,OAAO,IAAIqB,kBAAkB,CAAC,MAAMjP,SAAS,CAAC;AAClD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAuP,UAAU,CAACC,UAAU,GAAGC,cAAc,CAAC,KAAK,CAAC,CAAC;AAC9C;AACA;AACA;AACA;AACA;AACAF,UAAU,CAACG,SAAS,GAAGD,cAAc,CAAC,IAAI,CAAC,CAAC;AAC5C,SAASA,cAAcA,CAACE,qBAAqB,EAAE;EAC3C,OAAO,MAAM;IACT,MAAM/F,OAAO,GAAGe,WAAW,CAACC,QAAQ;IACpC,IAAIhB,OAAO,CAACoD,2BAA2B,CAAC,CAAC,KAAK2C,qBAAqB,EAAE;MACjE/F,OAAO,CAACqC,kBAAkB,CAAC,CAAC;MAC5BvT,0BAA0B,CAAC,CAAC;IAChC;EACJ,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkX,oCAAoC,GAAG,EAAE;;AAE/C;AACA;AACA;AACA;AACA;AACA,MAAMC,cAAc,CAAC;EACjB;AACJ;AACA;AACA;EACIC,UAAU,GAAG,EAAE;EACf;AACJ;AACA;EACIC,iBAAiB,GAAG,CAAC;EACrB;AACJ;AACA;AACA;EACIC,aAAa,GAAG,IAAI;EACpB;AACJ;AACA;AACA;EACIC,cAAc,GAAG,IAAI1W,GAAG,CAAC,CAAC;EAC1B;AACJ;AACA;AACA;EACI2W,aAAa,GAAGvf,OAAO,CAACoD,OAAO,CAAC,CAAC;EACjC;AACJ;AACA;AACA;EACIoc,qBAAqB,GAAG,CAAC;EACzB;AACJ;AACA;AACA;EACIC,qBAAqB,GAAG,KAAK;EAC7B;EACAC,kBAAkB,GAAG,IAAI;EACzB;AACJ;AACA;AACA;EACIC,WAAW;EACX;EACAC,MAAM,GAAG,CAAC;EACV;EACAC,OAAO,GAAG,CAAC;EACX;EACAC,QAAQ,GAAG,KAAK;EAChB;EACA,IAAIC,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACZ,UAAU,CAAC,IAAI,CAACC,iBAAiB,CAAC;EAClD;EACA,IAAIY,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACZ,iBAAiB,GAAG,CAAC;EACrC;EACA,IAAIa,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACb,iBAAiB,GAAG,IAAI,CAACD,UAAU,CAACjc,MAAM,GAAG,CAAC;EAC9D;EACAgd,iBAAiB;EACjBC,OAAO;EACP,IAAIC,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACD,OAAO;EACvB;EACAre,WAAWA,CAACue,GAAG,EAAEC,QAAQ,EAAE;IACvB,IAAI,CAACJ,iBAAiB,GAAG,MAAM;MAC3B,IAAI;QACA;QACA;QACA;QACA;QACA;QACA,OAAOG,GAAG,CAACE,aAAa,CAAC,KAAK,CAAC;MACnC,CAAC,CACD,MAAM;QACF;QACA;QACA;QACA,OAAO,IAAIC,WAAW,CAAC,CAAC;MAC5B;IACJ,CAAC;IACD,IAAI,CAACL,OAAO,GAAGM,QAAQ,CAACC,WAAW,IAAI,IAAI,CAACR,iBAAiB,CAAC,CAAC;IAC/D,IAAI,CAACP,WAAW,GAAG,IAAI,CAACO,iBAAiB,CAAC,CAAC;IAC3C;IACA,IAAI,CAACS,yBAAyB,CAACL,QAAQ,CAAC;EAC5C;EACA;AACJ;AACA;EACIK,yBAAyBA,CAAChO,GAAG,EAAEzK,OAAO,GAAG;IAAE0Y,YAAY,EAAE;EAAK,CAAC,EAAE;IAC7D,IAAI,CAAC,IAAI,CAAClB,kBAAkB,EAAE;MAC1B,MAAM,IAAIpd,KAAK,CAAC,0DAA0D,GAAG,yBAAyB,CAAC;IAC3G;IACA,MAAMue,mBAAmB,GAAG,IAAI,CAAC1B,UAAU,CAAC,CAAC,CAAC;IAC9C,IAAI,CAACA,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI2B,0BAA0B,CAAC,IAAI,CAACnB,WAAW,EAAE,IAAIoB,GAAG,CAACpO,GAAG,CAAC,CAACqO,QAAQ,CAAC,CAAC,EAAE;MAC3FC,KAAK,EAAE,CAAC;MACRvW,GAAG,EAAEmW,mBAAmB,EAAEnW,GAAG,IAAIwW,MAAM,CAAC,IAAI,CAACrB,OAAO,EAAE,CAAC;MACvD9U,EAAE,EAAE8V,mBAAmB,EAAE9V,EAAE,IAAImW,MAAM,CAAC,IAAI,CAACtB,MAAM,EAAE,CAAC;MACpDuB,YAAY,EAAE,IAAI;MAClBP,YAAY,EAAE1Y,OAAO,EAAE0Y,YAAY;MACnC5e,KAAK,EAAEkG,OAAO,CAAClG;IACnB,CAAC,CAAC;EACN;EACA;EACAof,4BAA4BA,CAAA,EAAG;IAC3B,OAAO,IAAI,CAAC1B,kBAAkB;EAClC;EACA;AACJ;AACA;AACA;EACI2B,kCAAkCA,CAAC5B,qBAAqB,EAAE;IACtD,IAAI,CAACA,qBAAqB,GAAGA,qBAAqB;EACtD;EACA;EACA6B,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACnC,UAAU,CAACoC,KAAK,CAAC,CAAC;EAClC;EACA;EACAC,QAAQA,CAAC7O,GAAG,EAAEzK,OAAO,EAAE;IACnB,MAAMuZ,OAAO,GAAG,IAAIV,GAAG,CAAC,IAAI,CAAChB,YAAY,CAACpN,GAAG,CAAC;IAC9C,MAAM+O,KAAK,GAAG,IAAIX,GAAG,CAACpO,GAAG,EAAE,IAAI,CAACoN,YAAY,CAACpN,GAAG,CAAC;IACjD,IAAIgP,cAAc;IAClB,IAAI,CAACzZ,OAAO,EAAE0Z,OAAO,IAAI1Z,OAAO,CAAC0Z,OAAO,KAAK,MAAM,EAAE;MACjD;MACA,IAAIH,OAAO,CAACT,QAAQ,CAAC,CAAC,KAAKU,KAAK,CAACV,QAAQ,CAAC,CAAC,EAAE;QACzCW,cAAc,GAAG,SAAS;MAC9B,CAAC,MACI;QACDA,cAAc,GAAG,MAAM;MAC3B;IACJ,CAAC,MACI;MACDA,cAAc,GAAGzZ,OAAO,CAAC0Z,OAAO;IACpC;IACA,MAAMC,UAAU,GAAGC,YAAY,CAACL,OAAO,EAAEC,KAAK,CAAC;IAC/C,MAAMK,WAAW,GAAG,IAAIC,yBAAyB,CAAC;MAC9CrP,GAAG,EAAE+O,KAAK,CAACV,QAAQ,CAAC,CAAC;MACrBhf,KAAK,EAAEkG,OAAO,EAAElG,KAAK;MACrBmf,YAAY,EAAEU,UAAU;MACxBjB,YAAY,EAAE;IAClB,CAAC,CAAC;IACF,MAAM/D,MAAM,GAAG,IAAIoF,wBAAwB,CAAC,IAAI,CAAC;IACjD,MAAMC,WAAW,GAAG,IAAI,CAACC,iBAAiB,CAACJ,WAAW,EAAElF,MAAM,EAAE;MAC5D8E,cAAc;MACdS,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,IAAI;MAClB;MACAC,aAAa,EAAE,KAAK;MACpBT,UAAU;MACVU,IAAI,EAAEra,OAAO,EAAEqa;IACnB,CAAC,CAAC;IACF,IAAI,CAACL,WAAW,EAAE;MACd,IAAI,CAACM,gDAAgD,CAAC,IAAI,CAACnD,aAAa,CAAC;IAC7E;IACA,OAAO;MACHoD,SAAS,EAAE5F,MAAM,CAAC4F,SAAS;MAC3BC,QAAQ,EAAE7F,MAAM,CAAC6F;IACrB,CAAC;EACL;EACA;EACAC,SAASA,CAACC,IAAI,EAAEC,KAAK,EAAElQ,GAAG,EAAE;IACxB,IAAI,CAACmQ,kBAAkB,CAAC,MAAM,EAAEF,IAAI,EAAEC,KAAK,EAAElQ,GAAG,CAAC;EACrD;EACA;EACAoQ,YAAYA,CAACH,IAAI,EAAEC,KAAK,EAAElQ,GAAG,EAAE;IAC3B,IAAI,CAACmQ,kBAAkB,CAAC,SAAS,EAAEF,IAAI,EAAEC,KAAK,EAAElQ,GAAG,CAAC;EACxD;EACAmQ,kBAAkBA,CAACnB,cAAc,EAAEiB,IAAI,EAAEI,MAAM,EAAErQ,GAAG,EAAE;IAClD,MAAM8O,OAAO,GAAG,IAAIV,GAAG,CAAC,IAAI,CAAChB,YAAY,CAACpN,GAAG,CAAC;IAC9C,MAAM+O,KAAK,GAAG/O,GAAG,GAAG,IAAIoO,GAAG,CAACpO,GAAG,EAAE,IAAI,CAACoN,YAAY,CAACpN,GAAG,CAAC,GAAG8O,OAAO;IACjE,MAAMI,UAAU,GAAGC,YAAY,CAACL,OAAO,EAAEC,KAAK,CAAC;IAC/C,MAAMK,WAAW,GAAG,IAAIC,yBAAyB,CAAC;MAC9CrP,GAAG,EAAE+O,KAAK,CAACV,QAAQ,CAAC,CAAC;MACrBG,YAAY,EAAE,IAAI;MAClBP,YAAY,EAAEgC;IAClB,CAAC,CAAC;IACF,MAAM/F,MAAM,GAAG,IAAIoF,wBAAwB,CAAC,IAAI,CAAC;IACjD,MAAMC,WAAW,GAAG,IAAI,CAACC,iBAAiB,CAACJ,WAAW,EAAElF,MAAM,EAAE;MAC5D8E,cAAc;MACdS,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,IAAI;MAClB;MACAC,aAAa,EAAE,KAAK;MACpBT;IACJ,CAAC,CAAC;IACF,IAAIK,WAAW,EAAE;MACb;IACJ;IACA,IAAI,CAACM,gDAAgD,CAAC,IAAI,CAACnD,aAAa,CAAC;EAC7E;EACA;EACA4D,UAAUA,CAACvY,GAAG,EAAExC,OAAO,EAAE;IACrB,MAAMuZ,OAAO,GAAG,IAAIV,GAAG,CAAC,IAAI,CAAChB,YAAY,CAACpN,GAAG,CAAC;IAC9C,MAAMuQ,KAAK,GAAG,IAAI,CAACC,SAAS,CAACzY,GAAG,CAAC;IACjC,IAAI,CAACwY,KAAK,EAAE;MACR,MAAME,YAAY,GAAG,IAAIC,YAAY,CAAC,aAAa,EAAE,mBAAmB,CAAC;MACzE,MAAMZ,SAAS,GAAGziB,OAAO,CAACC,MAAM,CAACmjB,YAAY,CAAC;MAC9C,MAAMV,QAAQ,GAAG1iB,OAAO,CAACC,MAAM,CAACmjB,YAAY,CAAC;MAC7CX,SAAS,CAACa,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;MAC1BZ,QAAQ,CAACY,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;MACzB,OAAO;QACHb,SAAS;QACTC;MACJ,CAAC;IACL;IACA,IAAIQ,KAAK,KAAK,IAAI,CAACnD,YAAY,EAAE;MAC7B,OAAO;QACH0C,SAAS,EAAEziB,OAAO,CAACoD,OAAO,CAAC,IAAI,CAAC2c,YAAY,CAAC;QAC7C2C,QAAQ,EAAE1iB,OAAO,CAACoD,OAAO,CAAC,IAAI,CAAC2c,YAAY;MAC/C,CAAC;IACL;IACA,IAAI,IAAI,CAACT,cAAc,CAACnV,GAAG,CAAC+Y,KAAK,CAACxY,GAAG,CAAC,EAAE;MACpC,MAAM6Y,cAAc,GAAG,IAAI,CAACjE,cAAc,CAAC/X,GAAG,CAAC2b,KAAK,CAACxY,GAAG,CAAC;MACzD,OAAO;QACH+X,SAAS,EAAEc,cAAc,CAACd,SAAS;QACnCC,QAAQ,EAAEa,cAAc,CAACb;MAC7B,CAAC;IACL;IACA,MAAMb,UAAU,GAAGC,YAAY,CAACL,OAAO,EAAE,IAAIV,GAAG,CAACmC,KAAK,CAACvQ,GAAG,EAAE,IAAI,CAACoN,YAAY,CAACpN,GAAG,CAAC,CAAC;IACnF,MAAMoP,WAAW,GAAG,IAAIC,yBAAyB,CAAC;MAC9CrP,GAAG,EAAEuQ,KAAK,CAACvQ,GAAG;MACd3Q,KAAK,EAAEkhB,KAAK,CAACM,QAAQ,CAAC,CAAC;MACvB5C,YAAY,EAAEsC,KAAK,CAACO,eAAe,CAAC,CAAC;MACrC/Y,GAAG,EAAEwY,KAAK,CAACxY,GAAG;MACdK,EAAE,EAAEmY,KAAK,CAACnY,EAAE;MACZkW,KAAK,EAAEiC,KAAK,CAACjC,KAAK;MAClBE,YAAY,EAAE+B,KAAK,CAAC/B;IACxB,CAAC,CAAC;IACF,IAAI,CAAC3B,qBAAqB,GAAG0D,KAAK,CAACjC,KAAK;IACxC,MAAMpE,MAAM,GAAG,IAAIoF,wBAAwB,CAAC,IAAI,CAAC;IACjD,IAAI,CAAC3C,cAAc,CAACjW,GAAG,CAAC6Z,KAAK,CAACxY,GAAG,EAAEmS,MAAM,CAAC;IAC1C,IAAI,CAAC6G,YAAY,CAAC,MAAM;MACpB,IAAI,CAACpE,cAAc,CAAClZ,MAAM,CAAC8c,KAAK,CAACxY,GAAG,CAAC;MACrC,MAAMwX,WAAW,GAAG,IAAI,CAACC,iBAAiB,CAACJ,WAAW,EAAElF,MAAM,EAAE;QAC5D8E,cAAc,EAAE,UAAU;QAC1BS,UAAU,EAAE,IAAI;QAChBC,YAAY,EAAE,IAAI;QAClB;QACAC,aAAa,EAAE,KAAK;QACpBT,UAAU;QACVU,IAAI,EAAEra,OAAO,EAAEqa;MACnB,CAAC,CAAC;MACF,IAAI,CAACL,WAAW,EAAE;QACd,IAAI,CAACyB,iBAAiB,CAAC,IAAI,CAACtE,aAAa,CAAC;MAC9C;IACJ,CAAC,CAAC;IACF,OAAO;MACHoD,SAAS,EAAE5F,MAAM,CAAC4F,SAAS;MAC3BC,QAAQ,EAAE7F,MAAM,CAAC6F;IACrB,CAAC;EACL;EACA;EACAkB,IAAIA,CAAC1b,OAAO,EAAE;IACV,IAAI,IAAI,CAACkX,iBAAiB,KAAK,CAAC,EAAE;MAC9B,MAAMgE,YAAY,GAAG,IAAIC,YAAY,CAAC,gBAAgB,EAAE,mBAAmB,CAAC;MAC5E,MAAMZ,SAAS,GAAGziB,OAAO,CAACC,MAAM,CAACmjB,YAAY,CAAC;MAC9C,MAAMV,QAAQ,GAAG1iB,OAAO,CAACC,MAAM,CAACmjB,YAAY,CAAC;MAC7CX,SAAS,CAACa,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;MAC1BZ,QAAQ,CAACY,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;MACzB,OAAO;QACHb,SAAS;QACTC;MACJ,CAAC;IACL;IACA,MAAMQ,KAAK,GAAG,IAAI,CAAC/D,UAAU,CAAC,IAAI,CAACC,iBAAiB,GAAG,CAAC,CAAC;IACzD,OAAO,IAAI,CAAC6D,UAAU,CAACC,KAAK,CAACxY,GAAG,EAAExC,OAAO,CAAC;EAC9C;EACA;EACA2b,OAAOA,CAAC3b,OAAO,EAAE;IACb,IAAI,IAAI,CAACkX,iBAAiB,KAAK,IAAI,CAACD,UAAU,CAACjc,MAAM,GAAG,CAAC,EAAE;MACvD,MAAMkgB,YAAY,GAAG,IAAIC,YAAY,CAAC,mBAAmB,EAAE,mBAAmB,CAAC;MAC/E,MAAMZ,SAAS,GAAGziB,OAAO,CAACC,MAAM,CAACmjB,YAAY,CAAC;MAC9C,MAAMV,QAAQ,GAAG1iB,OAAO,CAACC,MAAM,CAACmjB,YAAY,CAAC;MAC7CX,SAAS,CAACa,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;MAC1BZ,QAAQ,CAACY,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;MACzB,OAAO;QACHb,SAAS;QACTC;MACJ,CAAC;IACL;IACA,MAAMQ,KAAK,GAAG,IAAI,CAAC/D,UAAU,CAAC,IAAI,CAACC,iBAAiB,GAAG,CAAC,CAAC;IACzD,OAAO,IAAI,CAAC6D,UAAU,CAACC,KAAK,CAACxY,GAAG,EAAExC,OAAO,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI4b,EAAEA,CAACC,SAAS,EAAE;IACV,MAAMC,WAAW,GAAG,IAAI,CAACxE,qBAAqB,GAAGuE,SAAS;IAC1D,IAAIC,WAAW,IAAI,IAAI,CAAC7E,UAAU,CAACjc,MAAM,IAAI8gB,WAAW,GAAG,CAAC,EAAE;MAC1D;IACJ;IACA,IAAI,CAACxE,qBAAqB,GAAGwE,WAAW;IACxC,IAAI,CAACN,YAAY,CAAC,MAAM;MACpB;MACA,IAAIM,WAAW,IAAI,IAAI,CAAC7E,UAAU,CAACjc,MAAM,IAAI8gB,WAAW,GAAG,CAAC,EAAE;QAC1D;MACJ;MACA,MAAMvC,OAAO,GAAG,IAAIV,GAAG,CAAC,IAAI,CAAChB,YAAY,CAACpN,GAAG,CAAC;MAC9C,MAAMuQ,KAAK,GAAG,IAAI,CAAC/D,UAAU,CAAC6E,WAAW,CAAC;MAC1C,MAAMnC,UAAU,GAAGC,YAAY,CAACL,OAAO,EAAE,IAAIV,GAAG,CAACmC,KAAK,CAACvQ,GAAG,EAAE,IAAI,CAACoN,YAAY,CAACpN,GAAG,CAAC,CAAC;MACnF,MAAMoP,WAAW,GAAG,IAAIC,yBAAyB,CAAC;QAC9CrP,GAAG,EAAEuQ,KAAK,CAACvQ,GAAG;QACd3Q,KAAK,EAAEkhB,KAAK,CAACM,QAAQ,CAAC,CAAC;QACvB5C,YAAY,EAAEsC,KAAK,CAACO,eAAe,CAAC,CAAC;QACrC/Y,GAAG,EAAEwY,KAAK,CAACxY,GAAG;QACdK,EAAE,EAAEmY,KAAK,CAACnY,EAAE;QACZkW,KAAK,EAAEiC,KAAK,CAACjC,KAAK;QAClBE,YAAY,EAAE+B,KAAK,CAAC/B;MACxB,CAAC,CAAC;MACF,MAAMtE,MAAM,GAAG,IAAIoF,wBAAwB,CAAC,IAAI,CAAC;MACjD,MAAMC,WAAW,GAAG,IAAI,CAACC,iBAAiB,CAACJ,WAAW,EAAElF,MAAM,EAAE;QAC5D8E,cAAc,EAAE,UAAU;QAC1BS,UAAU,EAAE,IAAI;QAChBC,YAAY,EAAE,IAAI;QAClB;QACAC,aAAa,EAAE,KAAK;QACpBT;MACJ,CAAC,CAAC;MACF,IAAI,CAACK,WAAW,EAAE;QACd,IAAI,CAACyB,iBAAiB,CAAC,IAAI,CAACtE,aAAa,CAAC;MAC9C;IACJ,CAAC,CAAC;EACN;EACA;EACAqE,YAAYA,CAACO,SAAS,EAAE;IACpB,IAAI,IAAI,CAACxE,qBAAqB,EAAE;MAC5BwE,SAAS,CAAC,CAAC;MACX;IACJ;IACA;IACA;IACA;IACA,IAAI,CAAC1E,aAAa,GAAG,IAAI,CAACA,aAAa,CAACpY,IAAI,CAAC,MAAM;MAC/C,OAAO,IAAInH,OAAO,CAAEoD,OAAO,IAAK;QAC5B8gB,UAAU,CAAC,MAAM;UACb9gB,OAAO,CAAC,CAAC;UACT6gB,SAAS,CAAC,CAAC;QACf,CAAC,CAAC;MACN,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;EACAE,gBAAgBA,CAACziB,IAAI,EAAE0iB,QAAQ,EAAElc,OAAO,EAAE;IACtC,IAAI,CAACyX,WAAW,CAACwE,gBAAgB,CAACziB,IAAI,EAAE0iB,QAAQ,EAAElc,OAAO,CAAC;EAC9D;EACA;EACAmc,mBAAmBA,CAAC3iB,IAAI,EAAE0iB,QAAQ,EAAElc,OAAO,EAAE;IACzC,IAAI,CAACyX,WAAW,CAAC0E,mBAAmB,CAAC3iB,IAAI,EAAE0iB,QAAQ,EAAElc,OAAO,CAAC;EACjE;EACA;EACAoc,aAAaA,CAACC,KAAK,EAAE;IACjB,OAAO,IAAI,CAAC5E,WAAW,CAAC2E,aAAa,CAACC,KAAK,CAAC;EAChD;EACA;EACAC,OAAOA,CAAA,EAAG;IACN;IACA,IAAI,CAAC7E,WAAW,GAAG,IAAI,CAACO,iBAAiB,CAAC,CAAC;IAC3C,IAAI,CAACJ,QAAQ,GAAG,IAAI;EACxB;EACA;EACA2E,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC3E,QAAQ;EACxB;EACA;AACJ;AACA;AACA;EACIqC,iBAAiBA,CAACJ,WAAW,EAAElF,MAAM,EAAE3U,OAAO,EAAE;IAC5C;IACA;IACA,IAAI,CAACwX,kBAAkB,GAAG,KAAK;IAC/B,IAAI,IAAI,CAACL,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAACqF,MAAM,CAAC,IAAIrB,YAAY,CAAC,wBAAwB,EAAE,YAAY,CAAC,CAAC;MACnF,IAAI,CAAChE,aAAa,GAAG,IAAI;IAC7B;IACA,OAAOsF,qBAAqB,CAAC;MACzBhD,cAAc,EAAEzZ,OAAO,CAACyZ,cAAc;MACtCS,UAAU,EAAEla,OAAO,CAACka,UAAU;MAC9BC,YAAY,EAAEna,OAAO,CAACma,YAAY;MAClCC,aAAa,EAAEpa,OAAO,CAACoa,aAAa;MACpCT,UAAU,EAAE3Z,OAAO,CAAC2Z,UAAU;MAC9B+C,MAAM,EAAE/H,MAAM,CAAC+H,MAAM;MACrB7C,WAAW;MACXQ,IAAI,EAAEra,OAAO,CAACqa,IAAI;MAClBpB,YAAY,EAAEY,WAAW,CAACZ,YAAY;MACtCtE;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;EACIgI,wBAAwBA,CAACxF,aAAa,EAAE;IACpC,IAAI,CAACmD,gDAAgD,CAACnD,aAAa,CAAC;EACxE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIsE,iBAAiBA,CAACtE,aAAa,EAAE;IAC7B,MAAMyF,MAAM,GAAG,IAAI,CAAC/E,YAAY,CAACpN,GAAG;IACpC,IAAI,CAAC6P,gDAAgD,CAACnD,aAAa,CAAC;IACpE;IACA,MAAM0F,aAAa,GAAGC,mBAAmB,CAAC;MACtChjB,KAAK,EAAEqd,aAAa,CAAC0C,WAAW,CAAC0B,eAAe,CAAC;IACrD,CAAC,CAAC;IACF,IAAI,CAACtD,OAAO,CAACmE,aAAa,CAACS,aAAa,CAAC;IACzC,IAAI1F,aAAa,CAACwC,UAAU,EAAE;MAC1B,MAAMoD,eAAe,GAAGC,qBAAqB,CAACJ,MAAM,EAAE,IAAI,CAAC/E,YAAY,CAACpN,GAAG,CAAC;MAC5E,IAAI,CAACwN,OAAO,CAACmE,aAAa,CAACW,eAAe,CAAC;IAC/C;EACJ;EACA;AACJ;AACA;AACA;EACIzC,gDAAgDA,CAAC;IAAET,WAAW;IAAEJ,cAAc;IAAE9E;EAAQ,CAAC,EAAE;IACvF,MAAMsI,aAAa,GAAG,IAAI,CAACpF,YAAY;IACvC,MAAMqF,YAAY,GAAG,EAAE;IACvB,IAAIzD,cAAc,KAAK,UAAU,EAAE;MAC/B,IAAI,CAACvC,iBAAiB,GAAG2C,WAAW,CAACd,KAAK;MAC1C,IAAI,IAAI,CAAC7B,iBAAiB,KAAK,CAAC,CAAC,EAAE;QAC/B,MAAM,IAAI9c,KAAK,CAAC,gCAAgC,CAAC;MACrD;IACJ,CAAC,MACI,IAAIqf,cAAc,KAAK,MAAM,EAAE;MAChC,IAAI,CAACvC,iBAAiB,EAAE;MACxB,IAAI,CAACI,qBAAqB,GAAG,IAAI,CAACJ,iBAAiB,CAAC,CAAC;MACrDgG,YAAY,CAACjiB,IAAI,CAAC,GAAG,IAAI,CAACgc,UAAU,CAACkG,MAAM,CAAC,IAAI,CAACjG,iBAAiB,CAAC,CAAC;IACxE,CAAC,MACI,IAAIuC,cAAc,KAAK,SAAS,EAAE;MACnCyD,YAAY,CAACjiB,IAAI,CAACgiB,aAAa,CAAC;IACpC;IACA,IAAIxD,cAAc,KAAK,MAAM,IAAIA,cAAc,KAAK,SAAS,EAAE;MAC3D,MAAMV,KAAK,GAAG,IAAI,CAAC7B,iBAAiB;MACpC,MAAM1U,GAAG,GAAGiX,cAAc,KAAK,MAAM,GAAGT,MAAM,CAAC,IAAI,CAACrB,OAAO,EAAE,CAAC,GAAG,IAAI,CAACE,YAAY,CAACrV,GAAG;MACtF,MAAM4a,MAAM,GAAG,IAAIxE,0BAA0B,CAAC,IAAI,CAACnB,WAAW,EAAEoC,WAAW,CAACpP,GAAG,EAAE;QAC7E5H,EAAE,EAAEmW,MAAM,CAAC,IAAI,CAACtB,MAAM,EAAE,CAAC;QACzBlV,GAAG;QACHuW,KAAK;QACLE,YAAY,EAAE,IAAI;QAClBnf,KAAK,EAAE+f,WAAW,CAACyB,QAAQ,CAAC,CAAC;QAC7B5C,YAAY,EAAEmB,WAAW,CAAC0B,eAAe,CAAC;MAC9C,CAAC,CAAC;MACF,IAAI,CAACtE,UAAU,CAAC,IAAI,CAACC,iBAAiB,CAAC,GAAGkG,MAAM;IACpD;IACAzI,MAAM,CAAC0I,gBAAgB,CAAC,IAAI,CAACxF,YAAY,CAAC;IAC1C,MAAMyF,uBAAuB,GAAGC,2CAA2C,CAAC;MACxEC,IAAI,EAAEP,aAAa;MACnBxD,cAAc,EAAEA;IACpB,CAAC,CAAC;IACF,IAAI,CAAChC,WAAW,CAAC2E,aAAa,CAACkB,uBAAuB,CAAC;IACvD,KAAK,MAAMG,WAAW,IAAIP,YAAY,EAAE;MACpCO,WAAW,CAACnB,OAAO,CAAC,CAAC;IACzB;EACJ;EACA;EACArB,SAASA,CAACzY,GAAG,EAAE;IACX,KAAK,MAAMwY,KAAK,IAAI,IAAI,CAAC/D,UAAU,EAAE;MACjC,IAAI+D,KAAK,CAACxY,GAAG,KAAKA,GAAG,EACjB,OAAOwY,KAAK;IACpB;IACA,OAAO7b,SAAS;EACpB;EACA,IAAIue,UAAUA;EACd;EACAC,QAAQ,EAAE;IACN,MAAM,IAAIvjB,KAAK,CAAC,eAAe,CAAC;EACpC;EACA;EACA,IAAIsjB,UAAUA,CAAA,EAAG;IACb,MAAM,IAAItjB,KAAK,CAAC,eAAe,CAAC;EACpC;EACA,IAAIwjB,oBAAoBA,CAACD,QAAQ,EAAE;IAC/B,MAAM,IAAIvjB,KAAK,CAAC,eAAe,CAAC;EACpC;EACA,IAAIwjB,oBAAoBA,CAAA,EAAG;IACvB,MAAM,IAAIxjB,KAAK,CAAC,eAAe,CAAC;EACpC;EACA,IAAIyjB,iBAAiBA;EACrB;EACAF,QAAQ,EAAE;IACN,MAAM,IAAIvjB,KAAK,CAAC,eAAe,CAAC;EACpC;EACA;EACA,IAAIyjB,iBAAiBA,CAAA,EAAG;IACpB,MAAM,IAAIzjB,KAAK,CAAC,eAAe,CAAC;EACpC;EACA,IAAI0jB,eAAeA;EACnB;EACAH,QAAQ,EAAE;IACN,MAAM,IAAIvjB,KAAK,CAAC,eAAe,CAAC;EACpC;EACA;EACA,IAAI0jB,eAAeA,CAAA,EAAG;IAClB,MAAM,IAAI1jB,KAAK,CAAC,eAAe,CAAC;EACpC;EACA2jB,WAAW,GAAG,IAAI;EAClB;EACA,IAAIC,UAAUA,CAAClJ,CAAC,EAAE;IACd,IAAI,CAACiJ,WAAW,GAAGjJ,CAAC;EACxB;EACA,IAAIkJ,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACD,WAAW;EAC3B;EACAE,kBAAkBA,CAACC,QAAQ,EAAE;IACzB,MAAM,IAAI9jB,KAAK,CAAC,eAAe,CAAC;EACpC;EACA+jB,MAAMA,CAACD,QAAQ,EAAE;IACb,MAAM,IAAI9jB,KAAK,CAAC,eAAe,CAAC;EACpC;AACJ;AACA;AACA;AACA;AACA,MAAMwe,0BAA0B,CAAC;EAC7BnB,WAAW;EACXhN,GAAG;EACHwO,YAAY;EACZpW,EAAE;EACFL,GAAG;EACHuW,KAAK;EACLjf,KAAK;EACL4e,YAAY;EACZ;EACA0F,SAAS,GAAG,IAAI;EAChBxkB,WAAWA,CAAC6d,WAAW,EAAEhN,GAAG,EAAE;IAAE5H,EAAE;IAAEL,GAAG;IAAEuW,KAAK;IAAEE,YAAY;IAAEnf,KAAK;IAAE4e;EAAc,CAAC,EAAE;IAClF,IAAI,CAACjB,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAChN,GAAG,GAAGA,GAAG;IACd,IAAI,CAAC5H,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACL,GAAG,GAAGA,GAAG;IACd,IAAI,CAACuW,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACE,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACnf,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC4e,YAAY,GAAGA,YAAY;EACpC;EACA4C,QAAQA,CAAA,EAAG;IACP;IACA,OAAO,IAAI,CAACxhB,KAAK,GAAG4I,IAAI,CAAC2b,KAAK,CAAC3b,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC7I,KAAK,CAAC,CAAC,GAAG,IAAI,CAACA,KAAK;EAC3E;EACAyhB,eAAeA,CAAA,EAAG;IACd;IACA,OAAO,IAAI,CAAC7C,YAAY,GAClBhW,IAAI,CAAC2b,KAAK,CAAC3b,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC+V,YAAY,CAAC,CAAC,GAC7C,IAAI,CAACA,YAAY;EAC3B;EACAuD,gBAAgBA,CAACziB,IAAI,EAAE0iB,QAAQ,EAAElc,OAAO,EAAE;IACtC,IAAI,CAACyX,WAAW,CAACwE,gBAAgB,CAACziB,IAAI,EAAE0iB,QAAQ,EAAElc,OAAO,CAAC;EAC9D;EACAmc,mBAAmBA,CAAC3iB,IAAI,EAAE0iB,QAAQ,EAAElc,OAAO,EAAE;IACzC,IAAI,CAACyX,WAAW,CAAC0E,mBAAmB,CAAC3iB,IAAI,EAAE0iB,QAAQ,EAAElc,OAAO,CAAC;EACjE;EACAoc,aAAaA,CAACC,KAAK,EAAE;IACjB,OAAO,IAAI,CAAC5E,WAAW,CAAC2E,aAAa,CAACC,KAAK,CAAC;EAChD;EACA;EACAC,OAAOA,CAAA,EAAG;IACN,MAAMgC,YAAY,GAAG,IAAIC,KAAK,CAAC,UAAU,CAAC;IAC1C,IAAI,CAACnC,aAAa,CAACkC,YAAY,CAAC;IAChC;IACA,IAAI,CAAC7G,WAAW,GAAG,IAAI;EAC3B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgF,qBAAqBA,CAAC;EAAEvC,UAAU;EAAEC,YAAY;EAAEC,aAAa;EAAET,UAAU;EAAEF,cAAc;EAAEiD,MAAM;EAAE7C,WAAW;EAAEQ,IAAI;EAAEpB,YAAY;EAAEtE;AAAQ,CAAC,EAAE;EACtJ,MAAM;IAAE6J;EAAW,CAAC,GAAG7J,MAAM;EAC7B,MAAM0H,KAAK,GAAG,IAAIkC,KAAK,CAAC,UAAU,EAAE;IAAEE,OAAO,EAAE,KAAK;IAAEvE;EAAW,CAAC,CAAC;EACnEmC,KAAK,CAACqC,kBAAkB,GAAG,IAAI;EAC/BrC,KAAK,CAACsC,cAAc,GAAG,IAAI;EAC3BtC,KAAK,CAACuC,iBAAiB,GAAG,MAAM;EAChCvC,KAAK,CAAClC,YAAY,GAAGA,YAAY;EACjCkC,KAAK,CAACjC,aAAa,GAAGA,aAAa;EACnCiC,KAAK,CAAC1C,UAAU,GAAGA,UAAU;EAC7B0C,KAAK,CAAC5C,cAAc,GAAGA,cAAc;EACrC4C,KAAK,CAACK,MAAM,GAAGA,MAAM;EACrBL,KAAK,CAACxC,WAAW,GAAGA,WAAW;EAC/BwC,KAAK,CAAChC,IAAI,GAAGA,IAAI;EACjBgC,KAAK,CAACwC,eAAe,GAAG,IAAI;EAC5BxC,KAAK,CAACyC,QAAQ,GAAG,IAAI;EACrBzC,KAAK,CAAC1H,MAAM,GAAGA,MAAM;EACrB0H,KAAK,CAACpD,YAAY,GAAGA,YAAY;EACjC,IAAI8F,iBAAiB,GAAG,EAAE;EAC1B,IAAIC,QAAQ,GAAG,EAAE;EACjB;EACA3C,KAAK,CAAC4C,SAAS,GAAG,UAAUjf,OAAO,EAAE;IACjC,IAAI,CAAC,IAAI,CAACma,YAAY,EAAE;MACpB,MAAM,IAAIgB,YAAY,CAAC,+CAA+C,EAAE,eAAe,CAAC;IAC5F;IACA,IAAI,CAACyD,iBAAiB,GAAG,aAAa;IACtCvC,KAAK,CAACpD,YAAY,GAAG,IAAI;IACzB,MAAMiG,gBAAgB,GAAGlf,OAAO,EAAEkf,gBAAgB;IAClD,IAAIA,gBAAgB,EAAE;MAClB,IAAI,CAAC,IAAI,CAAChF,UAAU,EAAE;QAClB,MAAM,IAAIiB,YAAY,CAAC,wDAAwD,EAAE,mBAAmB,CAAC;MACzG;MACA4D,iBAAiB,CAAC9jB,IAAI,CAACikB,gBAAgB,CAAC;IAC5C;IACA,IAAI7C,KAAK,CAACuC,iBAAiB,KAAK,MAAM,IAAIvC,KAAK,CAACuC,iBAAiB,KAAK,aAAa,EAAE;MACjF,MAAM,IAAIxkB,KAAK,CAAC,2DAA2D,CAAC;IAChF;IACAiiB,KAAK,CAACuC,iBAAiB,GAAG,aAAa;IACvC,MAAMvP,OAAO,GAAGrP,OAAO,EAAEqP,OAAO;IAChC,IAAIA,OAAO,EAAE;MACT2P,QAAQ,CAAC/jB,IAAI,CAACoU,OAAO,CAAC;IAC1B;IACA;IACAgN,KAAK,CAACqC,kBAAkB,GAAG1e,OAAO,EAAEmf,UAAU,IAAI9C,KAAK,CAACqC,kBAAkB;IAC1ErC,KAAK,CAACsC,cAAc,GAAG3e,OAAO,EAAEof,MAAM,IAAI/C,KAAK,CAACsC,cAAc;EAClE,CAAC;EACD;EACAtC,KAAK,CAAC+C,MAAM,GAAG,YAAY;IACvB,IAAI/C,KAAK,CAACuC,iBAAiB,KAAK,WAAW,EAAE;MACzC,MAAM,IAAIzD,YAAY,CAAC,kEAAkE,GACrF,4EAA4E,EAAE,mBAAmB,CAAC;IAC1G;IACAkE,qBAAqB,CAAChD,KAAK,CAAC;EAChC,CAAC;EACD;EACA,SAASiD,QAAQA,CAAC7U,GAAG,EAAE;IACnB,IAAI4R,KAAK,CAACuC,iBAAiB,KAAK,MAAM,EAAE;MACpC,MAAM,IAAIxkB,KAAK,CAAC,+CAA+C,CAAC;IACpE;IACA,IAAIiiB,KAAK,CAACuC,iBAAiB,KAAK,aAAa,EAAE;MAC3C,MAAM,IAAIzD,YAAY,CAAC,0DAA0D,EAAE,mBAAmB,CAAC;IAC3G;IACA,IAAIkB,KAAK,CAAC5C,cAAc,KAAK,MAAM,IAAI4C,KAAK,CAAC5C,cAAc,KAAK,SAAS,EAAE;MACvE,MAAM,IAAI0B,YAAY,CAAC,+DAA+D,EAAE,mBAAmB,CAAC;IAChH;IACA,MAAM3B,KAAK,GAAG,IAAIX,GAAG,CAACpO,GAAG,EAAE+T,UAAU,CAAC3G,YAAY,CAACpN,GAAG,CAAC;IACvD4R,KAAK,CAACxC,WAAW,CAACpP,GAAG,GAAG+O,KAAK,CAAC+F,IAAI;EACtC;EACA;EACA;EACA,SAASC,MAAMA,CAAA,EAAG;IACd,IAAI7K,MAAM,CAAC+H,MAAM,CAAC+C,OAAO,EAAE;MACvB;IACJ;IACA,IAAIpD,KAAK,CAACuC,iBAAiB,KAAK,MAAM,EAAE;MACpCvC,KAAK,CAACuC,iBAAiB,GAAG,WAAW;MACrC,IAAI,CAACJ,UAAU,CAAC3G,YAAY,EAAE;QAC1B,MAAM,IAAIzd,KAAK,CAAC,uCAAuC,CAAC;MAC5D;MACAokB,UAAU,CAACR,UAAU,GAAG,IAAI0B,4BAA4B,CAAClB,UAAU,CAAC3G,YAAY,EAAE4B,cAAc,CAAC;MACjG,QAAQ4C,KAAK,CAAC5C,cAAc;QACxB,KAAK,MAAM;QACX,KAAK,SAAS;UAAE;YACZ+E,UAAU,CAAC7B,wBAAwB,CAACN,KAAK,CAAC;YAC1C;UACJ;QACA,KAAK,QAAQ;UAAE;YACXmC,UAAU,CAAClE,gDAAgD,CAAC+B,KAAK,CAAC;YAClE;UACJ;QACA,KAAK,UAAU;UAAE;YACbmC,UAAU,CAAC/C,iBAAiB,CAACY,KAAK,CAAC;YACnC;UACJ;MACJ;IACJ;IACA,MAAMsD,YAAY,GAAGX,QAAQ,CAACnK,GAAG,CAAExF,OAAO,IAAKA,OAAO,CAAC,CAAC,CAAC;IACzD,IAAIsQ,YAAY,CAAC3kB,MAAM,KAAK,CAAC,EAAE;MAC3B2kB,YAAY,CAAC1kB,IAAI,CAACnD,OAAO,CAACoD,OAAO,CAAC,CAAC,CAAC;IACxC;IACApD,OAAO,CAACiS,GAAG,CAAC4V,YAAY,CAAC,CACpB1gB,IAAI,CAAC,MAAM;MACZ;MACA;MACA,IAAI0V,MAAM,CAAC+H,MAAM,CAAC+C,OAAO,EAAE;QACvB;MACJ;MACA,IAAIpD,KAAK,KAAKmC,UAAU,CAACrH,aAAa,EAAE;QACpC,MAAM,IAAI/c,KAAK,CAAC,wDAAwD,CAAC;MAC7E;MACAokB,UAAU,CAACrH,aAAa,GAAG,IAAI;MAC/ByI,qBAAqB,CAACvD,KAAK,EAAE,IAAI,CAAC;MAClC,MAAMwD,oBAAoB,GAAG,IAAItB,KAAK,CAAC,iBAAiB,EAAE;QAAEE,OAAO,EAAE,KAAK;QAAEvE;MAAW,CAAC,CAAC;MACzFsE,UAAU,CAAC/G,WAAW,CAAC2E,aAAa,CAACyD,oBAAoB,CAAC;MAC1DlL,MAAM,CAACmL,eAAe,CAAC,CAAC;MACxB,IAAItB,UAAU,CAACR,UAAU,KAAK,IAAI,EAAE;QAChCQ,UAAU,CAACR,UAAU,CAAC8B,eAAe,CAAC,CAAC;MAC3C;MACAtB,UAAU,CAACR,UAAU,GAAG,IAAI;IAChC,CAAC,CAAC,CACG5C,KAAK,CAAE2E,MAAM,IAAK1D,KAAK,CAACG,MAAM,CAACuD,MAAM,CAAC,CAAC;EAChD;EACA;EACA;EACA;EACA1D,KAAK,CAACG,MAAM,GAAG,UAAUuD,MAAM,EAAE;IAC7B,IAAIpL,MAAM,CAAC+H,MAAM,CAAC+C,OAAO,EAAE;MACvB;IACJ;IACA,IAAIpD,KAAK,KAAKmC,UAAU,CAACrH,aAAa,EAAE;MACpC,MAAM,IAAI/c,KAAK,CAAC,wDAAwD,CAAC;IAC7E;IACAokB,UAAU,CAACrH,aAAa,GAAG,IAAI;IAC/B,IAAIkF,KAAK,CAACuC,iBAAiB,KAAK,aAAa,EAAE;MAC3CgB,qBAAqB,CAACvD,KAAK,EAAE,KAAK,CAAC;IACvC;IACA,MAAM2D,kBAAkB,GAAG,IAAIzB,KAAK,CAAC,eAAe,EAAE;MAAEE,OAAO,EAAE,KAAK;MAAEvE;IAAW,CAAC,CAAC;IACrFsE,UAAU,CAAC/G,WAAW,CAAC2E,aAAa,CAAC4D,kBAAkB,CAAC;IACxDrL,MAAM,CAACsL,cAAc,CAACF,MAAM,CAAC;IAC7B,IAAIvB,UAAU,CAACR,UAAU,KAAK,IAAI,EAAE;MAChCQ,UAAU,CAACR,UAAU,CAACiC,cAAc,CAACF,MAAM,CAAC;IAChD;IACAvB,UAAU,CAACR,UAAU,GAAG,IAAI;EAChC,CAAC;EACD,SAASkC,QAAQA,CAAA,EAAG;IAChB1B,UAAU,CAACrH,aAAa,GAAGkF,KAAK;IAChCmC,UAAU,CAAC/G,WAAW,CAAC2E,aAAa,CAACC,KAAK,CAAC;IAC3C,IAAI0C,iBAAiB,CAAC/jB,MAAM,KAAK,CAAC,EAAE;MAChCwkB,MAAM,CAAC,CAAC;IACZ,CAAC,MACI;MACD,MAAMW,mBAAmB,GAAG;QAAEb;MAAS,CAAC;MACxC,MAAMc,qBAAqB,GAAGrB,iBAAiB,CAAClK,GAAG,CAAExF,OAAO,IAAKA,OAAO,CAAC8Q,mBAAmB,CAAC,CAAC;MAC9FroB,OAAO,CAACiS,GAAG,CAACqW,qBAAqB,CAAC,CAC7BnhB,IAAI,CAAC,MAAMugB,MAAM,CAAC,CAAC,CAAC,CACpBpE,KAAK,CAAE2E,MAAM,IAAK1D,KAAK,CAACG,MAAM,CAACuD,MAAM,CAAC,CAAC;IAChD;EACJ;EACAG,QAAQ,CAAC,CAAC;EACV,OAAO7D,KAAK,CAACuC,iBAAiB,KAAK,MAAM;AAC7C;AACA;AACA,SAASgB,qBAAqBA,CAACvD,KAAK,EAAEgE,UAAU,EAAE;EAC9C,IAAIhE,KAAK,CAACuC,iBAAiB,KAAK,UAAU,EAAE;IACxC,MAAM,IAAIxkB,KAAK,CAAC,iEAAiE,CAAC;EACtF;EACA,IAAIiiB,KAAK,CAACuC,iBAAiB,KAAK,aAAa,EAAE;IAC3C,IAAIyB,UAAU,KAAK,IAAI,EAAE;MACrB,MAAM,IAAIjmB,KAAK,CAAC,4BAA4B,CAAC;IACjD;IACA;IACAiiB,KAAK,CAACuC,iBAAiB,GAAG,UAAU;IACpC;EACJ;EACA,IAAIvC,KAAK,CAACuC,iBAAiB,KAAK,MAAM,EAAE;IACpC;EACJ;EACA0B,qBAAqB,CAACjE,KAAK,CAAC;EAC5B,IAAIgE,UAAU,EAAE;IACZE,sBAAsB,CAAClE,KAAK,CAAC;EACjC;EACAA,KAAK,CAACuC,iBAAiB,GAAG,UAAU;AACxC;AACA;AACA,SAAS0B,qBAAqBA,CAACjE,KAAK,EAAE;EAClC,IAAIA,KAAK,CAACuC,iBAAiB,KAAK,WAAW,IAAIvC,KAAK,CAACuC,iBAAiB,KAAK,UAAU,EAAE;IACnF,MAAM,IAAIxkB,KAAK,CAAC,qEAAqE,CAAC;EAC1F;EACA;AACJ;AACA,SAASmmB,sBAAsBA,CAAClE,KAAK,EAAE;EACnC,IAAIA,KAAK,CAACuC,iBAAiB,KAAK,WAAW,IAAIvC,KAAK,CAACuC,iBAAiB,KAAK,UAAU,EAAE;IACnF,MAAM,IAAIxkB,KAAK,CAAC,sEAAsE,CAAC;EAC3F;EACA,IAAIiiB,KAAK,CAACuC,iBAAiB,KAAK,UAAU,IAAIvC,KAAK,CAACsC,cAAc,KAAK,QAAQ,EAAE;IAC7E;EACJ;EACAU,qBAAqB,CAAChD,KAAK,CAAC;AAChC;AACA;AACA,SAASgD,qBAAqBA,CAAChD,KAAK,EAAE;EAClC,IAAIA,KAAK,CAACuC,iBAAiB,KAAK,WAAW,EAAE;IACzC,MAAM,IAAIxkB,KAAK,CAAC,kEAAkE,CAAC;EACvF;EACAiiB,KAAK,CAACuC,iBAAiB,GAAG,UAAU;EACpC;AACJ;AACA;AACA;AACA;AACA;AACA,SAASrB,2CAA2CA,CAAC;EAAEC,IAAI;EAAE/D;AAAgB,CAAC,EAAE;EAC5E,MAAM4C,KAAK,GAAG,IAAIkC,KAAK,CAAC,oBAAoB,EAAE;IAC1CE,OAAO,EAAE,KAAK;IACdvE,UAAU,EAAE;EAChB,CAAC,CAAC;EACFmC,KAAK,CAACmB,IAAI,GAAGA,IAAI;EACjBnB,KAAK,CAAC5C,cAAc,GAAGA,cAAc;EACrC,OAAO4C,KAAK;AAChB;AACA;AACA;AACA;AACA;AACA,SAASS,mBAAmBA,CAAC;EAAEhjB;AAAM,CAAC,EAAE;EACpC,MAAMuiB,KAAK,GAAG,IAAIkC,KAAK,CAAC,UAAU,EAAE;IAChCE,OAAO,EAAE,KAAK;IACdvE,UAAU,EAAE;EAChB,CAAC,CAAC;EACFmC,KAAK,CAACviB,KAAK,GAAGA,KAAK;EACnB,OAAOuiB,KAAK;AAChB;AACA,SAASW,qBAAqBA,CAACwD,MAAM,EAAEC,MAAM,EAAE;EAC3C,MAAMpE,KAAK,GAAG,IAAIkC,KAAK,CAAC,YAAY,EAAE;IAClCE,OAAO,EAAE,KAAK;IACdvE,UAAU,EAAE;EAChB,CAAC,CAAC;EACFmC,KAAK,CAACmE,MAAM,GAAGA,MAAM;EACrBnE,KAAK,CAACoE,MAAM,GAAGA,MAAM;EACrB,OAAOpE,KAAK;AAChB;AACA;AACA;AACA;AACA,MAAMvC,yBAAyB,CAAC;EAC5BrP,GAAG;EACHwO,YAAY;EACZzW,GAAG;EACHK,EAAE;EACFkW,KAAK;EACLjf,KAAK;EACL4e,YAAY;EACZ9e,WAAWA,CAAC;IAAE6Q,GAAG;IAAEwO,YAAY;IAAEP,YAAY;IAAE5e,KAAK;IAAE0I,GAAG,GAAG,IAAI;IAAEK,EAAE,GAAG,IAAI;IAAEkW,KAAK,GAAG,CAAC;EAAG,CAAC,EAAE;IACxF,IAAI,CAACtO,GAAG,GAAGA,GAAG;IACd,IAAI,CAACwO,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACnf,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC4e,YAAY,GAAGA,YAAY;IAChC,IAAI,CAAClW,GAAG,GAAGA,GAAG;IACd,IAAI,CAACK,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACkW,KAAK,GAAGA,KAAK;EACtB;EACAuC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACxhB,KAAK;EACrB;EACAyhB,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC7C,YAAY;EAC5B;AACJ;AACA;AACA,SAASkB,YAAYA,CAAC4D,IAAI,EAAEkD,EAAE,EAAE;EAC5B,OAAQA,EAAE,CAACC,IAAI,KAAKnD,IAAI,CAACmD,IAAI,IACzBD,EAAE,CAACE,QAAQ,KAAKpD,IAAI,CAACoD,QAAQ,IAC7BF,EAAE,CAACG,QAAQ,KAAKrD,IAAI,CAACqD,QAAQ,IAC7BH,EAAE,CAACI,MAAM,KAAKtD,IAAI,CAACsD,MAAM;AACjC;AACA,MAAMpB,4BAA4B,CAAC;EAC/BlC,IAAI;EACJ/D,cAAc;EACde,QAAQ;EACRsF,eAAe;EACfG,cAAc;EACdrmB,WAAWA,CAAC4jB,IAAI,EAAE/D,cAAc,EAAE;IAC9B,IAAI,CAAC+D,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC/D,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACe,QAAQ,GAAG,IAAI1iB,OAAO,CAAC,CAACoD,OAAO,EAAEnD,MAAM,KAAK;MAC7C,IAAI,CAACkoB,cAAc,GAAGloB,MAAM;MAC5B,IAAI,CAAC+nB,eAAe,GAAG5kB,OAAO;IAClC,CAAC,CAAC;IACF;IACA,IAAI,CAACsf,QAAQ,CAACY,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;EAClC;AACJ;AACA;AACA;AACA;AACA;AACA,MAAMrB,wBAAwB,CAAC;EAC3ByE,UAAU;EACVuC,WAAW,GAAG,IAAI;EAClB1D,gBAAgB;EAChB2D,eAAe;EACflB,eAAe;EACfG,cAAc;EACd1F,SAAS;EACTC,QAAQ;EACR,IAAIkC,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACuE,eAAe,CAACvE,MAAM;EACtC;EACAuE,eAAe,GAAG,IAAIC,eAAe,CAAC,CAAC;EACvCtnB,WAAWA,CAAC4kB,UAAU,EAAE;IAAA,IAAA2C,MAAA;IACpB,IAAI,CAAC3C,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACjE,SAAS,GAAG,IAAIziB,OAAO,CAAC,CAACoD,OAAO,EAAEnD,MAAM,KAAK;MAC9C,IAAI,CAACslB,gBAAgB,GAAIrC,KAAK,IAAK;QAC/B,IAAI,CAAC+F,WAAW,GAAG/F,KAAK;QACxB9f,OAAO,CAAC8f,KAAK,CAAC;MAClB,CAAC;MACD,IAAI,CAACgG,eAAe,GAAGjpB,MAAM;IACjC,CAAC,CAAC;IACF,IAAI,CAACyiB,QAAQ,GAAG,IAAI1iB,OAAO;MAAA,IAAAspB,IAAA,GAAApnB,iBAAA,CAAC,WAAOkB,OAAO,EAAEnD,MAAM,EAAK;QACnDopB,MAAI,CAACrB,eAAe,GAAG,MAAM;UACzB,IAAIqB,MAAI,CAACJ,WAAW,KAAK,IAAI,EAAE;YAC3B,MAAM,IAAI3mB,KAAK,CAAC,6EAA6E,CAAC;UAClG;UACAc,OAAO,CAACimB,MAAI,CAACJ,WAAW,CAAC;QAC7B,CAAC;QACDI,MAAI,CAAClB,cAAc,GAAIF,MAAM,IAAK;UAC9BhoB,MAAM,CAACgoB,MAAM,CAAC;UACdoB,MAAI,CAACF,eAAe,CAACI,KAAK,CAACtB,MAAM,CAAC;QACtC,CAAC;MACL,CAAC;MAAA,iBAAAuB,EAAA,EAAAC,GAAA;QAAA,OAAAH,IAAA,CAAArM,KAAA,OAAAyM,SAAA;MAAA;IAAA,IAAC;IACF;IACA,IAAI,CAACjH,SAAS,CAACa,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;IAC/B,IAAI,CAACZ,QAAQ,CAACY,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;EAClC;AACJ;AAEA,MAAMqG,GAAG,CAAC;EACNC,QAAQ;EACR9nB,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC8nB,QAAQ,GAAG,EAAE;EACtB;EACA5jB,GAAGA,CAACiB,KAAK,EAAE;IACP,IAAI,CAAC2iB,QAAQ,CAACzmB,IAAI,CAAC8D,KAAK,CAAC;EAC7B;EACApH,EAAEA,CAACoH,KAAK,EAAE;IACN,OAAO,MAAM;MACT,IAAI,CAAC2iB,QAAQ,CAACzmB,IAAI,CAAC8D,KAAK,CAAC;IAC7B,CAAC;EACL;EACAjG,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC4oB,QAAQ,GAAG,EAAE;EACtB;EACA/M,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAAC+M,QAAQ,CAACC,IAAI,CAAC,IAAI,CAAC;EACnC;EACA,OAAO5oB,IAAI,GAAG,SAAS6oB,WAAWA,CAAC3oB,iBAAiB,EAAE;IAAE,OAAO,KAAKA,iBAAiB,IAAIwoB,GAAG,EAAE,CAAC;EAAE,CAAC;EAClG,OAAOvoB,KAAK,GAAG,aAAc5J,EAAE,CAAC6J,kBAAkB,CAAC;IAAEC,KAAK,EAAEqoB,GAAG;IAAEpoB,OAAO,EAAEooB,GAAG,CAAC1oB;EAAK,CAAC,CAAC;AACzF;AACA,CAAC,MAAM;EAAE,CAAC,OAAOO,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKhK,EAAE,CAACiK,iBAAiB,CAACkoB,GAAG,EAAE,CAAC;IAC/EjoB,IAAI,EAAE7J;EACV,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC;AAAE,CAAC,EAAE,CAAC;AAE7B,SAASwM,gBAAgB,EAAEF,0BAA0B,EAAEC,wBAAwB,EAAEzC,iBAAiB,EAAE2c,kBAAkB,EAAE3B,OAAO,EAAE5Y,qBAAqB,EAAEkb,oCAAoC,EAAE1W,oBAAoB,EAAEN,SAAS,EAAEvB,KAAK,EAAE8B,eAAe,EAAEuR,UAAU,EAAEtiB,MAAM,EAAEqQ,kBAAkB,EAAElB,IAAI,EAAEhH,YAAY,EAAE+e,UAAU,EAAEO,cAAc,IAAI6K,eAAe,EAAEJ,GAAG,IAAIK,IAAI,EAAEthB,iBAAiB,IAAIuhB,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}