/* Project Card Component Styles */
.project-card {
	height: 100%;
	transition: all 0.3s ease;

	&:hover {
		transform: translateY(-4px);
		box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
	}

	&.card-clickable {
		cursor: pointer;
	}

	&.card-new {
		border-left: 4px solid var(--primary-500);
	}
}

/* Progress Bar Customization */
:host ::ng-deep .progress-bar-custom {
	.p-progressbar-value {
		background: linear-gradient(90deg, var(--primary-400), var(--primary-600));
		border-radius: 3px;
	}

	.p-progressbar {
		background-color: var(--gray-200);
		border-radius: 3px;
	}
}

/* Tag Customization */
:host ::ng-deep .p-chip {
	font-size: 0.75rem;
	font-weight: 500;
	border-radius: 1rem;

	.p-chip-text {
		line-height: 1.2;
	}
}

/* Status Tag Customization */
:host ::ng-deep .p-tag {
	font-weight: 600;
	letter-spacing: 0.025em;
	text-transform: uppercase;

	&.p-tag-success {
		background-color: var(--green-100);
		color: var(--green-800);
		border: 1px solid var(--green-200);
	}
}

/* Responsive Design */
@media (max-width: 640px) {
	.project-card {
		:host ::ng-deep .p-card-body {
			padding: 1rem;
		}

		.client-sector-container {
			.flex-row {
				flex-direction: column;
			}
		}
	}
}

/* Text Utilities */
.line-clamp-2 {
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

/* Animation for card entrance */
@keyframes cardEntrance {
	from {
		opacity: 0;
		transform: translateY(20px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.project-card {
	animation: cardEntrance 0.3s ease-out;
}

/* Hover effect for tags */
:host ::ng-deep .p-chip {
	transition: all 0.2s ease;

	&:hover {
		transform: scale(1.05);
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	}
}

/* Enhanced gradient for header */
.project-card :host ::ng-deep .p-card-header {
	overflow: hidden;
	border-radius: 0.5rem 0.5rem 0 0;

	.bg-gradient-to-br {
		position: relative;

		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 100%);
			opacity: 0.8;
		}
	}
}

:host {
	display: block;
}

/* Animação suave para os badges */
::ng-deep .p-tag {
	transition: all 0.2s ease;
}

/* Hover effect nos badges */
.project-card:hover ::ng-deep .p-tag {
	transform: scale(1.05);
}
