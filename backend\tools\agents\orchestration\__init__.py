"""
Sistema de Orquestração de Agentes - ScopeAI

Sistema responsável por coordenar a execução dos 6 agentes especializados
do Team Agno, gerenciando dependências, fluxo de dados e robustez.

Componentes principais:
- AgentOrchestrator: Coordenador principal
- ExecutionPlan: Planejamento de execução
- OrchestrationContext: Contexto compartilhado
- AgentRunner: Executor individual
- OrchestrationStorage: Persistência de estado
"""

from .orchestrator import AgentOrchestrator
from .execution_plan import ExecutionPlan, ExecutionPhase
from .context import OrchestrationContext
from .runner import AgentRunner
from .schemas import (
    OrchestrationConfig,
    OrchestrationResult,
    OrchestrationStatus,
    AgentExecutionResult,
    AgentExecutionStatus
)

__all__ = [
    "AgentOrchestrator",
    "ExecutionPlan",
    "ExecutionPhase",
    "OrchestrationContext",
    "AgentRunner",
    "OrchestrationConfig",
    "OrchestrationResult",
    "OrchestrationStatus",
    "AgentExecutionResult",
    "AgentExecutionStatus"
]
