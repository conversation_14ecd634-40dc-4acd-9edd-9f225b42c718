{"version": 3, "sources": ["../../../../../../node_modules/@angular/common/locales/extra/pt.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nexport default [[[\"meia-noite\", \"meio-dia\", \"da manhã\", \"da tarde\", \"da noite\", \"da madrugada\"], u, u], [[\"meia-noite\", \"meio-dia\", \"manhã\", \"tarde\", \"noite\", \"madrugada\"], u, u], [\"00:00\", \"12:00\", [\"06:00\", \"12:00\"], [\"12:00\", \"19:00\"], [\"19:00\", \"24:00\"], [\"00:00\", \"06:00\"]]];\n"], "mappings": ";;;AAQA,IAAM,IAAI;AACV,IAAO,aAAQ,CAAC,CAAC,CAAC,cAAc,YAAY,YAAY,YAAY,YAAY,cAAc,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,cAAc,YAAY,SAAS,SAAS,SAAS,WAAW,GAAG,GAAG,CAAC,GAAG,CAAC,SAAS,SAAS,CAAC,SAAS,OAAO,GAAG,CAAC,SAAS,OAAO,GAAG,CAAC,SAAS,OAAO,GAAG,CAAC,SAAS,OAAO,CAAC,CAAC;", "names": []}