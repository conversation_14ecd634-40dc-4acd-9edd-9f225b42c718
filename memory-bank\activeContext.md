# Active Context - ScopeAI

**Última Atualização**: 2025-01-29

## Estado Atual

### Branch Ativa
- **feat-microservices**: Migração em progresso (TASK-001)

### Progresso da Migração

**[TASK-001] Client Service - Fase 1** (7.5% concluído)
- ✅ MT-1: Micro-task prompt definido
- ✅ MT-2: Tree of Thought aplicado - decisões arquiteturais tomadas
- ✅ MT-3: Mapeamento de dependências completo
- 🔄 MT-4: Próximo - criar estrutura do microserviço

### Últimas Decisões

1. **Arquitetura do Microserviço** (2025-01-29)
   - Clean Architecture com layers horizontais
   - Domain, Application, Infrastructure, API
   - Dependency Rule rigorosamente aplicada
   - Status: Aprovado após análise ToT

2. **Estratégia de Comunicação** (2025-01-29)
   - Híbrida: REST para queries, eventos para comandos
   - RabbitMQ para mensageria assíncrona
   - WebSocket mantido para notificações real-time
   - Status: Definido

3. **Database Migration** (2025-01-29)
   - Fase 1: Shared MongoDB, collection clients_v2
   - Sincronização via eventos durante transição
   - Migração gradual sem downtime
   - Status: Estratégia aprovada

4. **API Versioning** (2025-01-29)
   - URL path: /api/v1 (monolito), /api/v2 (microserviço)
   - API Gateway para roteamento inteligente
   - Feature flags para controle de rollout
   - Status: Padrão definido

### Descobertas do Mapeamento

1. **Complexidade Identificada**
   - 16 endpoints no domínio de clientes
   - 2.417 linhas em routes.py
   - 25+ eventos WebSocket
   - Acoplamento circular detectado

2. **Dependências Críticas**
   - Perplexity API (análise de empresas)
   - PDF Generator (reports)
   - WebSocket Manager (notificações)
   - GridFS (armazenamento)

3. **Riscos Mapeados**
   - Alta complexidade ciclomática
   - Lógica de negócio espalhada
   - Estado compartilhado global
   - Multiple external APIs

## Problemas Conhecidos

1. **Arquitetura Monolítica**
   - routes.py com 2.417 linhas
   - parsers.py com 2.878 linhas
   - Violações SOLID identificadas

2. **Performance**
   - Gargalo em análises paralelas
   - Cache não otimizado
   - WebSockets instáveis em alta carga

3. **Frontend**
   - Migração Angular 19 completa
   - Alguns componentes precisam refatoração
   - Build warnings a resolver

## Próximos Passos

### Imediato (Hoje)
1. [x] MT-1: Draft micro-task prompt ✅
2. [x] MT-2: Tree of Thought analysis ✅
3. [x] MT-3: Dependency mapping ✅
4. [ ] MT-4: Criar estrutura do microserviço Client
5. [ ] MT-5: Implementar domain layer

### Curto Prazo (Esta Semana)
1. [ ] Migrar Project Service
2. [ ] Implementar API Gateway
3. [ ] Setup Docker Compose para microserviços
4. [ ] Criar CI/CD pipeline

### Médio Prazo (Próximo Mês)
1. [ ] Migrar Web Diagnostics Service
2. [ ] Implementar Market Research Service
3. [ ] Setup Kubernetes local
4. [ ] Implementar circuit breakers

## Decisões Pendentes

1. **Escolha de Message Broker**
   - Opções: RabbitMQ vs Kafka
   - Critérios: Simplicidade vs Escalabilidade
   - Prazo: Próxima semana

2. **Estratégia de Cache**
   - Redis vs Memcached
   - Cache distribuído necessário
   - Decisão impacta arquitetura

3. **Monitoramento**
   - Prometheus + Grafana vs DataDog
   - Orçamento a definir
   - Setup antes da produção

## Notas de Desenvolvimento

- Manter compatibilidade durante migração
- Zero downtime é prioridade
- Documentar todas as mudanças
- Testes antes de cada merge

## Recursos Úteis

- [Plano de Refatoração](./archive/refactoring-microservices-plan.md)
- [Documentação Team Agno](./systemPatterns.md)
- [Roadmap Completo](./roadmap/tasks.md)

## Tarefa Atual: Migração para Microserviços - Phase 1

### Progresso Atual
- **Status**: MT-4 concluída, preparando MT-5
- **Tempo Gasto**: ~3 horas de 40h estimadas (7.5%)
- **Branch**: feat-microservices

### Decisões Técnicas Tomadas (2025-01-29)

1. **Arquitetura Base**:
   - Clean Architecture com Hexagonal (Ports & Adapters)
   - Domain-Driven Design (DDD) para modelagem
   - Dependency Rule rigorosamente aplicada

2. **Estrutura de Camadas**:
   - **Domain**: Entidades puras sem dependências externas
   - **Application**: Use cases e orquestração
   - **Infrastructure**: Implementações concretas (DB, APIs, etc)
   - **API**: Controllers e rotas FastAPI

3. **Comunicação entre Serviços**:
   - REST para queries síncronas (GET)
   - RabbitMQ para comandos e eventos assíncronos
   - Padrão híbrido para máxima flexibilidade

4. **Estratégia de Migração**:
   - Strangler Fig Pattern
   - Database compartilhado temporário
   - Versionamento de API (/api/v1 monolito, /api/v2 microserviço)
   - Período de coexistência com sincronização via eventos

5. **Configuração e Infraestrutura** (ATUALIZADO):
   - **DECISÃO IMPORTANTE**: Reutilizar toda infraestrutura existente do backend
   - Usar `/backend/pyproject.toml` para dependências (sem duplicação)
   - Usar `/backend/.env` para variáveis de ambiente
   - Usar `/backend/Dockerfile` para container
   - Microserviço é parte do backend, não projeto separado
   - Evitar duplicação de configurações = manutenção simplificada

### Próximos Passos

**MT-5: Implementar Domain Layer**
- [ ] Criar entidade Client com value objects
- [ ] Implementar eventos de domínio
- [ ] Definir interfaces de repositório
- [ ] Adicionar regras de negócio
- [ ] Testes unitários do domínio

### Insights Importantes

1. **Tree of Thought revelou**: Clean Architecture é melhor que Vertical Slice para este caso devido à complexidade do domínio e necessidade de reutilização entre use cases.

2. **Mapeamento identificou**: 16 endpoints críticos, 25+ eventos WebSocket, e forte acoplamento com Perplexity API que precisará ser abstraído.

3. **Configuração unificada**: Decisão de reutilizar configurações existentes simplifica drasticamente a manutenção e evita drift de configuração entre monolito e microserviço.

### Arquivos Modificados Nesta Sessão
- Criada estrutura completa em `/backend/microservices/client-service/`
- Removidos arquivos de configuração duplicados
- Atualizado README.md do microserviço para documentar abordagem
- Configurado src/config.py para mapear variáveis do backend principal
- Adaptado src/api/main.py para usar loguru (já disponível) 