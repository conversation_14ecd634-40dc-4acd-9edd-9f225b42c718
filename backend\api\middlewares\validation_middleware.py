"""
ValidationMiddleware - Middleware de Validação

Este módulo implementa validação automática de dados de entrada
e sanitização de parâmetros da API.
"""

import logging
import json
from typing import Dict, Any, Optional, List
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response
from ..error_handler import ValidationAPIError
from ..response_formatter import response_formatter


logger = logging.getLogger(__name__)


class ValidationMiddleware(BaseHTTPMiddleware):
    """
    Middleware de Validação da API

    Responsável por:
    - Validar dados de entrada automaticamente
    - Sanitizar parâmetros de query e body
    - Verificar tipos de conteúdo aceitos
    - Aplicar regras de validação globais
    - Limitar tamanho de payload
    """

    def __init__(
        self,
        app,
        max_payload_size: int = 10 * 1024 * 1024,  # 10MB
        allowed_content_types: Optional[List[str]] = None
    ):
        """
        Inicializa o middleware de validação

        Args:
            app: Aplicação FastAPI
            max_payload_size: Tamanho máximo do payload em bytes
            allowed_content_types: Tipos de conteúdo permitidos
        """
        super().__init__(app)
        self.max_payload_size = max_payload_size
        self.allowed_content_types = allowed_content_types or [
            "application/json",
            "application/x-www-form-urlencoded",
            "multipart/form-data",
            "text/plain"
        ]
        self.exclude_validation_paths = [
            "/docs", "/redoc", "/openapi.json", "/ping", "/health", "/ws"
        ]

    async def dispatch(self, request: Request, call_next) -> Response:
        """
        Processa validação da requisição

        Args:
            request: Requisição FastAPI
            call_next: Próximo middleware/handler

        Returns:
            Response processada
        """
        try:
            # Verificar se deve aplicar validação
            if self._should_skip_validation(request):
                return await call_next(request)

            # Validar tamanho do payload
            await self._validate_payload_size(request)

            # Validar content-type
            self._validate_content_type(request)

            # Validar query parameters
            self._validate_query_parameters(request)

            # Sanitizar dados se necessário
            await self._sanitize_request_data(request)

            # Processar requisição
            response = await call_next(request)

            return response

        except ValidationAPIError:
            # Re-raise validation errors
            raise
        except Exception as e:
            logger.error(f"Validation middleware error: {str(e)}")
            # Permitir continuar em caso de erro no middleware
            return await call_next(request)

    def _should_skip_validation(self, request: Request) -> bool:
        """
        Verifica se deve pular validação para este path

        Args:
            request: Requisição FastAPI

        Returns:
            True se deve pular validação
        """
        path = request.url.path
        for excluded in self.exclude_validation_paths:
            if path.startswith(excluded):
                return True
        return False

    async def _validate_payload_size(self, request: Request):
        """
        Valida tamanho do payload

        Args:
            request: Requisição FastAPI

        Raises:
            ValidationAPIError: Se payload muito grande
        """
        content_length = request.headers.get("content-length")
        if content_length:
            try:
                size = int(content_length)
                if size > self.max_payload_size:
                    raise ValidationAPIError(
                        f"Payload muito grande. Máximo permitido: {self.max_payload_size // (1024*1024)}MB",
                        [f"Tamanho: {size // (1024*1024)}MB, Máximo: {self.max_payload_size // (1024*1024)}MB"]
                    )
            except ValueError:
                logger.warning(
                    f"Invalid content-length header: {content_length}")

    def _validate_content_type(self, request: Request):
        """
        Valida content-type da requisição

        Args:
            request: Requisição FastAPI

        Raises:
            ValidationAPIError: Se content-type inválido
        """
        # Só validar para métodos que podem ter body
        if request.method in ["POST", "PUT", "PATCH"]:
            content_type = request.headers.get(
                "content-type", "").split(";")[0].strip()

            if content_type and content_type not in self.allowed_content_types:
                raise ValidationAPIError(
                    "Tipo de conteúdo não suportado",
                    [f"Content-Type '{content_type}' não é suportado",
                     f"Tipos aceitos: {', '.join(self.allowed_content_types)}"]
                )

    def _validate_query_parameters(self, request: Request):
        """
        Valida parâmetros de query

        Args:
            request: Requisição FastAPI

        Raises:
            ValidationAPIError: Se parâmetros inválidos
        """
        try:
            errors = []

            # Verificar parâmetros comuns
            query_params = dict(request.query_params)

            # Validar parâmetros de paginação
            if "page" in query_params:
                try:
                    page = int(query_params["page"])
                    if page < 1:
                        errors.append("Parâmetro 'page' deve ser maior que 0")
                except ValueError:
                    errors.append(
                        "Parâmetro 'page' deve ser um número inteiro")

            if "per_page" in query_params:
                try:
                    per_page = int(query_params["per_page"])
                    if per_page < 1:
                        errors.append(
                            "Parâmetro 'per_page' deve ser maior que 0")
                    elif per_page > 100:
                        errors.append(
                            "Parâmetro 'per_page' não pode ser maior que 100")
                except ValueError:
                    errors.append(
                        "Parâmetro 'per_page' deve ser um número inteiro")

            # Validar parâmetros de ordenação
            if "sort" in query_params:
                sort_value = query_params["sort"]
                if not sort_value.replace("-", "").replace("_", "").isalnum():
                    errors.append(
                        "Parâmetro 'sort' contém caracteres inválidos")

            # Verificar caracteres perigosos em parâmetros gerais
            for key, value in query_params.items():
                if self._contains_dangerous_chars(str(value)):
                    errors.append(
                        f"Parâmetro '{key}' contém caracteres potencialmente perigosos")

            if errors:
                raise ValidationAPIError(
                    "Parâmetros de query inválidos",
                    errors
                )

        except ValidationAPIError:
            raise
        except Exception as e:
            logger.warning(f"Error validating query parameters: {str(e)}")

    async def _sanitize_request_data(self, request: Request):
        """
        Sanitiza dados da requisição se necessário

        Args:
            request: Requisição FastAPI
        """
        try:
            # Sanitização básica será implementada conforme necessário
            # Por enquanto, apenas log de dados suspeitos

            if request.method in ["POST", "PUT", "PATCH"]:
                # Verificar se o body contém dados suspeitos
                # Nota: Não podemos modificar o body facilmente no middleware,
                # então apenas logamos alertas

                content_type = request.headers.get("content-type", "")
                if "application/json" in content_type:
                    # Em implementação futura, poderia analisar JSON
                    pass

        except Exception as e:
            logger.warning(f"Error sanitizing request data: {str(e)}")

    def _contains_dangerous_chars(self, value: str) -> bool:
        """
        Verifica se string contém caracteres potencialmente perigosos

        Args:
            value: Valor a ser verificado

        Returns:
            True se contém caracteres perigosos
        """
        dangerous_patterns = [
            "<script", "</script>", "javascript:", "onload=", "onerror=",
            "SELECT ", "INSERT ", "DELETE ", "UPDATE ", "DROP ",
            "UNION ", "exec(", "eval(", "__import__"
        ]

        value_lower = value.lower()
        for pattern in dangerous_patterns:
            if pattern.lower() in value_lower:
                return True
        return False

    def validate_json_body(self, body: Dict[str, Any], required_fields: Optional[List[str]] = None) -> List[str]:
        """
        Valida body JSON específico

        Args:
            body: Body da requisição
            required_fields: Campos obrigatórios

        Returns:
            Lista de erros de validação
        """
        errors = []

        try:
            # Verificar campos obrigatórios
            if required_fields:
                for field in required_fields:
                    if field not in body:
                        errors.append(f"Campo obrigatório '{field}' ausente")
                    elif body[field] is None or body[field] == "":
                        errors.append(f"Campo '{field}' não pode estar vazio")

            # Validações específicas por tipo de campo
            for key, value in body.items():
                if isinstance(value, str):
                    # Verificar tamanho máximo de strings
                    if len(value) > 10000:  # 10KB max por string
                        errors.append(
                            f"Campo '{key}' muito longo (máximo 10KB)")

                    # Verificar caracteres perigosos
                    if self._contains_dangerous_chars(value):
                        errors.append(
                            f"Campo '{key}' contém caracteres potencialmente perigosos")

                elif isinstance(value, (int, float)):
                    # Verificar ranges razoáveis
                    if isinstance(value, int) and abs(value) > 2**31:
                        errors.append(f"Campo '{key}' fora do range válido")

        except Exception as e:
            errors.append(f"Erro na validação do JSON: {str(e)}")

        return errors

    def validate_id_parameter(self, id_value: str, field_name: str = "id") -> List[str]:
        """
        Valida parâmetro de ID

        Args:
            id_value: Valor do ID
            field_name: Nome do campo

        Returns:
            Lista de erros
        """
        errors = []

        if not id_value:
            errors.append(f"Campo '{field_name}' é obrigatório")
            return errors

        # Validar formato ObjectId (MongoDB)
        if len(id_value) == 24 and all(c in "0123456789abcdef" for c in id_value.lower()):
            return errors  # ID válido

        # Validar ID numérico
        if id_value.isdigit():
            return errors  # ID numérico válido

        errors.append(
            f"Campo '{field_name}' deve ser um ID válido (ObjectId ou numérico)")
        return errors
