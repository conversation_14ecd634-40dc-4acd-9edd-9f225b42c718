"""
Architectural Recommendation Tools para Agno

Tools para geração de recomendações arquiteturais:
- Consolidação de análises técnicas e visuais
- Geração de recomendações priorizadas
- Cálculo de impacto e esforço
- Timeline de implementação
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from pydantic import BaseModel, Field
from ..schemas import (
    TechnicalRecommendation,
    RecommendationCategory,
    PriorityLevel,
    EffortLevel
)

logger = logging.getLogger(__name__)


class RecommendationInput(BaseModel):
    """Input consolidado para geração de recomendações"""
    lighthouse_metrics: Dict[str, Any] = Field(
        ..., description="Métricas do Lighthouse processadas")
    visual_insights: Dict[str,
                          Any] = Field(..., description="Insights visuais processados")
    company_context: Dict[str, Any] = Field(
        default_factory=dict, description="Contexto da empresa")
    business_priorities: List[str] = Field(
        default_factory=list, description="Prioridades de negócio")


class ArchitecturalRecommendationTools:
    """
    Tools para geração de recomendações arquiteturais

    Consolida análises técnicas e visuais para gerar
    recomendações priorizadas e acionáveis.
    """

    def __init__(self):
        # Mapeamento de categorias por tipo de problema
        self.category_mapping = {
            "performance": RecommendationCategory.PERFORMANCE,
            "acessibilidade": RecommendationCategory.ACCESSIBILITY,
            "seo": RecommendationCategory.SEO,
            "seguranca": RecommendationCategory.SECURITY,
            "ui": RecommendationCategory.USER_EXPERIENCE,
            "ux": RecommendationCategory.USER_EXPERIENCE,
            "responsivo": RecommendationCategory.USER_EXPERIENCE,
            "infraestrutura": RecommendationCategory.INFRASTRUCTURE,
            "escalabilidade": RecommendationCategory.SCALABILITY,
            "manutencao": RecommendationCategory.MAINTAINABILITY
        }

    def generate_recommendations(self, input_data: RecommendationInput) -> List[TechnicalRecommendation]:
        """
        Gera recomendações técnicas baseadas nas análises consolidadas

        Args:
            input_data: Dados consolidados das análises

        Returns:
            Lista de recomendações técnicas priorizadas
        """
        try:
            logger.info("Iniciando geração de recomendações arquiteturais")

            recommendations = []

            # Gerar recomendações de performance
            perf_recs = self._generate_performance_recommendations(
                input_data.lighthouse_metrics)
            recommendations.extend(perf_recs)

            # Gerar recomendações de acessibilidade
            acc_recs = self._generate_accessibility_recommendations(
                input_data.lighthouse_metrics)
            recommendations.extend(acc_recs)

            # Gerar recomendações de SEO
            seo_recs = self._generate_seo_recommendations(
                input_data.lighthouse_metrics)
            recommendations.extend(seo_recs)

            # Gerar recomendações visuais/UX
            visual_recs = self._generate_visual_recommendations(
                input_data.visual_insights)
            recommendations.extend(visual_recs)

            # Gerar recomendações de infraestrutura
            infra_recs = self._generate_infrastructure_recommendations(
                input_data)
            recommendations.extend(infra_recs)

            # Priorizar e limitar recomendações
            prioritized_recs = self._prioritize_recommendations(
                recommendations, input_data.business_priorities)

            logger.info(
                f"Geradas {len(prioritized_recs)} recomendações priorizadas")
            return prioritized_recs[:8]  # Máximo 8 recomendações

        except Exception as e:
            logger.error(f"Erro na geração de recomendações: {str(e)}")
            raise

    def _generate_performance_recommendations(self, lighthouse_metrics: Dict[str, Any]) -> List[TechnicalRecommendation]:
        """Gera recomendações de performance"""
        recommendations = []

        try:
            perf_score = lighthouse_metrics.get("performance_score", 100)
            lcp = lighthouse_metrics.get("lcp")
            fcp = lighthouse_metrics.get("fcp")
            cls = lighthouse_metrics.get("cls")

            # LCP alto
            if lcp and lcp > 3000:
                priority = PriorityLevel.CRITICAL if lcp > 4000 else PriorityLevel.HIGH
                effort = EffortLevel.HIGH if lcp > 5000 else EffortLevel.MEDIUM

                rec = TechnicalRecommendation(
                    category=RecommendationCategory.PERFORMANCE,
                    title="Otimizar Largest Contentful Paint (LCP)",
                    description=f"LCP atual de {lcp}ms está acima do recomendado (< 2.5s). Implementar otimizações de carregamento.",
                    technologies=["WebP", "CDN",
                                  "Critical CSS", "Resource Hints"],
                    implementation_steps=[
                        "Otimizar imagens principais (WebP, compressão)",
                        "Implementar Critical CSS inline",
                        "Configurar resource hints (preload, prefetch)",
                        "Configurar CDN para assets estáticos"
                    ],
                    effort=effort,
                    priority=priority,
                    expected_impact="Melhoria significativa na percepção de velocidade pelos usuários",
                    estimated_timeframe="2-3 semanas",
                    metrics_improvement={
                        "LCP": f"-{min(1500, lcp-2000)}ms", "Performance": "+15-25 pontos"}
                )
                recommendations.append(rec)

            # CLS alto
            if cls and cls > 0.1:
                priority = PriorityLevel.HIGH if cls > 0.25 else PriorityLevel.MEDIUM

                rec = TechnicalRecommendation(
                    category=RecommendationCategory.PERFORMANCE,
                    title="Corrigir Cumulative Layout Shift (CLS)",
                    description=f"CLS de {cls} causa mudanças visuais inesperadas. Implementar dimensões explícitas.",
                    technologies=["CSS Grid", "Flexbox",
                                  "Aspect Ratio", "Size Attributes"],
                    implementation_steps=[
                        "Definir dimensões explícitas para imagens",
                        "Reservar espaço para anúncios e embeds",
                        "Usar CSS aspect-ratio",
                        "Evitar inserção dinâmica acima do fold"
                    ],
                    effort=EffortLevel.MEDIUM,
                    priority=priority,
                    expected_impact="Melhor estabilidade visual e experiência do usuário",
                    estimated_timeframe="1-2 semanas",
                    metrics_improvement={
                        "CLS": f"-{min(0.15, cls-0.05):.2f}", "Performance": "+10-15 pontos"}
                )
                recommendations.append(rec)

            # Performance geral baixa
            if perf_score < 70:
                rec = TechnicalRecommendation(
                    category=RecommendationCategory.PERFORMANCE,
                    title="Auditoria completa de performance",
                    description=f"Score de performance baixo ({perf_score}/100). Necessária análise abrangente.",
                    technologies=["Webpack Bundle Analyzer",
                                  "Lighthouse CI", "WebPageTest"],
                    implementation_steps=[
                        "Análise de bundle JavaScript",
                        "Implementar lazy loading",
                        "Otimizar critical rendering path",
                        "Configurar caching estratégico"
                    ],
                    effort=EffortLevel.HIGH,
                    priority=PriorityLevel.HIGH,
                    expected_impact="Melhoria geral na velocidade e métricas Core Web Vitals",
                    estimated_timeframe="3-4 semanas",
                    metrics_improvement={"Performance": "+20-30 pontos"}
                )
                recommendations.append(rec)

        except Exception as e:
            logger.error(f"Erro nas recomendações de performance: {str(e)}")

        return recommendations

    def _generate_accessibility_recommendations(self, lighthouse_metrics: Dict[str, Any]) -> List[TechnicalRecommendation]:
        """Gera recomendações de acessibilidade"""
        recommendations = []

        try:
            acc_score = lighthouse_metrics.get("accessibility_score", 100)
            acc_issues = lighthouse_metrics.get("accessibility_issues", [])

            if acc_score < 80 or acc_issues:
                priority = PriorityLevel.CRITICAL if acc_score < 60 else PriorityLevel.HIGH
                effort = EffortLevel.MEDIUM

                rec = TechnicalRecommendation(
                    category=RecommendationCategory.ACCESSIBILITY,
                    title="Melhorar acessibilidade web",
                    description=f"Score de acessibilidade de {acc_score}/100. Implementar padrões WCAG 2.1.",
                    technologies=["ARIA", "Semantic HTML",
                                  "Screen Readers", "axe-core"],
                    implementation_steps=[
                        "Adicionar alt text em imagens",
                        "Implementar navegação por teclado",
                        "Melhorar contraste de cores",
                        "Adicionar labels em formulários",
                        "Estruturar headings corretamente"
                    ],
                    effort=effort,
                    priority=priority,
                    expected_impact="Inclusão de usuários com deficiências e conformidade legal",
                    estimated_timeframe="2-3 semanas",
                    metrics_improvement={"Acessibilidade": "+15-25 pontos"}
                )
                recommendations.append(rec)

        except Exception as e:
            logger.error(f"Erro nas recomendações de acessibilidade: {str(e)}")

        return recommendations

    def _generate_seo_recommendations(self, lighthouse_metrics: Dict[str, Any]) -> List[TechnicalRecommendation]:
        """Gera recomendações de SEO"""
        recommendations = []

        try:
            seo_score = lighthouse_metrics.get("seo_score", 100)
            seo_issues = lighthouse_metrics.get("seo_issues", [])

            if seo_score < 85 or seo_issues:
                rec = TechnicalRecommendation(
                    category=RecommendationCategory.SEO,
                    title="Otimizar SEO técnico",
                    description=f"Score SEO de {seo_score}/100. Implementar otimizações técnicas.",
                    technologies=["Schema.org", "Meta Tags",
                                  "Sitemap XML", "Open Graph"],
                    implementation_steps=[
                        "Otimizar meta descriptions e titles",
                        "Implementar structured data",
                        "Configurar sitemap XML",
                        "Melhorar URLs semânticas",
                        "Otimizar para mobile-first"
                    ],
                    effort=EffortLevel.MEDIUM,
                    priority=PriorityLevel.MEDIUM,
                    expected_impact="Melhor visibilidade em motores de busca",
                    estimated_timeframe="1-2 semanas",
                    metrics_improvement={"SEO": "+10-20 pontos"}
                )
                recommendations.append(rec)

        except Exception as e:
            logger.error(f"Erro nas recomendações de SEO: {str(e)}")

        return recommendations

    def _generate_visual_recommendations(self, visual_insights: Dict[str, Any]) -> List[TechnicalRecommendation]:
        """Gera recomendações baseadas na análise visual"""
        recommendations = []

        try:
            visual_score = visual_insights.get("overall_visual_score", 100)
            responsive_design = visual_insights.get(
                "responsive_design", "").lower()
            ui_issues = visual_insights.get("ui_issues", [])
            ux_issues = visual_insights.get("ux_issues", [])

            # Design responsivo inadequado
            if responsive_design in ["ruim", "péssimo", "não", "baixo"]:
                rec = TechnicalRecommendation(
                    category=RecommendationCategory.USER_EXPERIENCE,
                    title="Implementar design responsivo",
                    description="Design não responsivo prejudica experiência mobile. Implementar abordagem mobile-first.",
                    technologies=["CSS Grid", "Flexbox",
                                  "Media Queries", "Viewport Meta Tag"],
                    implementation_steps=[
                        "Implementar meta viewport",
                        "Criar breakpoints responsivos",
                        "Otimizar imagens para diferentes telas",
                        "Testar em dispositivos reais"
                    ],
                    effort=EffortLevel.HIGH,
                    priority=PriorityLevel.HIGH,
                    expected_impact="Melhor experiência em dispositivos móveis",
                    estimated_timeframe="3-4 semanas",
                    metrics_improvement={"Mobile Usability": "+30-40 pontos"}
                )
                recommendations.append(rec)

            # Issues de UX críticos
            if ux_issues:
                rec = TechnicalRecommendation(
                    category=RecommendationCategory.USER_EXPERIENCE,
                    title="Melhorar experiência do usuário",
                    description="Problemas de usabilidade identificados. Otimizar jornada do usuário.",
                    technologies=["User Testing", "Analytics", "Heatmaps"],
                    implementation_steps=[
                        "Simplificar navegação principal",
                        "Otimizar formulários",
                        "Melhorar call-to-actions",
                        "Implementar feedback visual"
                    ],
                    effort=EffortLevel.MEDIUM,
                    priority=PriorityLevel.MEDIUM,
                    expected_impact="Maior conversão e satisfação do usuário",
                    estimated_timeframe="2-3 semanas",
                    metrics_improvement={"User Experience": "+20-30 pontos"}
                )
                recommendations.append(rec)

        except Exception as e:
            logger.error(f"Erro nas recomendações visuais: {str(e)}")

        return recommendations

    def _generate_infrastructure_recommendations(self, input_data: RecommendationInput) -> List[TechnicalRecommendation]:
        """Gera recomendações de infraestrutura"""
        recommendations = []

        try:
            lighthouse_metrics = input_data.lighthouse_metrics
            perf_score = lighthouse_metrics.get("performance_score", 100)
            opportunities = lighthouse_metrics.get("opportunities", [])

            # Infraestrutura geral se performance muito baixa
            if perf_score < 50:
                rec = TechnicalRecommendation(
                    category=RecommendationCategory.INFRASTRUCTURE,
                    title="Modernizar infraestrutura web",
                    description="Performance crítica indica necessidade de modernização de infraestrutura.",
                    technologies=["CDN", "HTTP/2", "Compression", "Caching"],
                    implementation_steps=[
                        "Implementar CDN global",
                        "Configurar HTTP/2",
                        "Ativar compressão gzip/brotli",
                        "Configurar cache headers",
                        "Otimizar servidor web"
                    ],
                    effort=EffortLevel.HIGH,
                    priority=PriorityLevel.HIGH,
                    expected_impact="Melhoria fundamental na velocidade global",
                    estimated_timeframe="4-6 semanas",
                    metrics_improvement={
                        "Performance": "+25-35 pontos", "Global Speed": "+40%"}
                )
                recommendations.append(rec)

        except Exception as e:
            logger.error(f"Erro nas recomendações de infraestrutura: {str(e)}")

        return recommendations

    def _prioritize_recommendations(self, recommendations: List[TechnicalRecommendation], business_priorities: List[str]) -> List[TechnicalRecommendation]:
        """Prioriza recomendações baseado em critérios múltiplos"""
        try:
            def priority_score(rec: TechnicalRecommendation) -> int:
                score = 0

                # Score base por prioridade
                priority_scores = {
                    PriorityLevel.CRITICAL: 100,
                    PriorityLevel.HIGH: 75,
                    PriorityLevel.MEDIUM: 50,
                    PriorityLevel.LOW: 25
                }
                score += priority_scores.get(rec.priority, 25)

                # Boost por prioridades de negócio
                for business_priority in business_priorities:
                    if business_priority.lower() in rec.title.lower() or business_priority.lower() in rec.description.lower():
                        score += 25

                # Penalização por esforço alto
                effort_penalties = {
                    EffortLevel.LOW: 0,
                    EffortLevel.MEDIUM: -5,
                    EffortLevel.HIGH: -15,
                    EffortLevel.VERY_HIGH: -25
                }
                score += effort_penalties.get(rec.effort, 0)

                return score

            # Ordenar por score de prioridade
            sorted_recs = sorted(
                recommendations, key=priority_score, reverse=True)
            return sorted_recs

        except Exception as e:
            logger.error(f"Erro na priorização: {str(e)}")
            return recommendations

    def calculate_implementation_timeline(self, recommendations: List[TechnicalRecommendation]) -> str:
        """Calcula timeline geral de implementação"""
        try:
            total_weeks = 0

            # Mapear esforços para semanas
            effort_weeks = {
                EffortLevel.LOW: 1,
                EffortLevel.MEDIUM: 2,
                EffortLevel.HIGH: 4,
                EffortLevel.VERY_HIGH: 6
            }

            # Calcular considerando paralelização
            critical_high_weeks = sum(effort_weeks.get(rec.effort, 2) for rec in recommendations
                                      if rec.priority in [PriorityLevel.CRITICAL, PriorityLevel.HIGH])

            medium_low_weeks = sum(effort_weeks.get(rec.effort, 2) for rec in recommendations
                                   # Paralelização
                                   if rec.priority in [PriorityLevel.MEDIUM, PriorityLevel.LOW]) // 2

            total_weeks = critical_high_weeks + medium_low_weeks

            if total_weeks <= 4:
                return "1 mês"
            elif total_weeks <= 8:
                return "2 meses"
            elif total_weeks <= 12:
                return "3 meses"
            else:
                return f"{total_weeks//4} meses"

        except Exception:
            return "2-3 meses"
