"""
Benchmark Analysis Tools para Agno

Tools customizadas para análise competitiva e benchmarking:
- Análise de dados competitivos
- Identificação de gaps competitivos
- Cálculo de scores de comparação
- Análise de diferenciação estratégica
"""

import logging
from typing import Dict, Any, List, Optional, Union
from pydantic import BaseModel, Field
from datetime import datetime

logger = logging.getLogger(__name__)


class CompetitorInsights(BaseModel):
    """Schema para insights competitivos processados"""
    competitor_name: str = Field(..., description="Nome do concorrente")
    market_position: str = Field(..., description="Posição no mercado")
    strength_areas: List[str] = Field(
        default_factory=list, description="Áreas de força")
    weakness_areas: List[str] = Field(
        default_factory=list, description="Áreas de fraqueza")

    # Scores de comparação
    overall_score: float = Field(
        default=0.0, description="Score geral (0-100)")
    feature_score: float = Field(
        default=0.0, description="Score de funcionalidades (0-100)")
    performance_score: float = Field(
        default=0.0, description="Score de performance (0-100)")
    market_share: float = Field(
        default=0.0, description="Participação de mercado (%)")

    # Análise temporal
    growth_trend: str = Field(
        default="Estável", description="Tendência de crescimento")
    recent_moves: List[str] = Field(
        default_factory=list, description="Movimentos recentes")


class CompetitiveGap(BaseModel):
    """Schema para gaps competitivos identificados"""
    gap_type: str = Field(
        ..., description="Tipo do gap (funcionalidade, performance, mercado, etc.)")
    description: str = Field(..., description="Descrição detalhada do gap")
    impact_level: str = Field(...,
                              description="Nível de impacto (Alto, Médio, Baixo)")
    urgency: str = Field(...,
                         description="Urgência de ação (Crítico, Importante, Desejável)")

    # Detalhes do gap
    affected_segments: List[str] = Field(
        default_factory=list, description="Segmentos afetados")
    competitor_advantage: str = Field(
        default="", description="Vantagem do concorrente")
    estimated_revenue_impact: float = Field(
        default=0.0, description="Impacto estimado na receita")

    # Recomendações
    recommended_action: str = Field(default="", description="Ação recomendada")
    implementation_effort: str = Field(
        default="Médio", description="Esforço de implementação")
    timeline_estimate: str = Field(default="", description="Timeline estimado")

    # Score de prioridade (calculado dinamicamente)
    priority_score: float = Field(
        default=0.0, description="Score de prioridade para ordenação")


class DifferentiationStrategy(BaseModel):
    """Schema para estratégias de diferenciação"""
    strategy_name: str = Field(..., description="Nome da estratégia")
    focus_area: str = Field(..., description="Área de foco")
    description: str = Field(..., description="Descrição da estratégia")

    # Análise estratégica
    competitive_advantage: str = Field(...,
                                       description="Vantagem competitiva a ser criada")
    target_segments: List[str] = Field(
        default_factory=list, description="Segmentos alvo")
    success_metrics: List[str] = Field(
        default_factory=list, description="Métricas de sucesso")

    # Implementação
    required_resources: List[str] = Field(
        default_factory=list, description="Recursos necessários")
    implementation_complexity: str = Field(
        default="Médio", description="Complexidade de implementação")
    expected_roi: float = Field(default=0.0, description="ROI esperado")
    risk_level: str = Field(default="Médio", description="Nível de risco")

    # Score de prioridade (calculado dinamicamente)
    priority_score: float = Field(
        default=0.0, description="Score de prioridade para ordenação")


class BenchmarkAnalysisTools:
    """
    Tools para análise competitiva e benchmarking

    Processa dados de concorrentes, identifica gaps competitivos
    e gera estratégias de diferenciação baseadas em dados.
    """

    def __init__(self):
        """Inicializa as ferramentas de análise competitive"""
        self.competitive_weights = {
            "features": 0.3,
            "performance": 0.25,
            "market_presence": 0.2,
            "innovation": 0.15,
            "customer_satisfaction": 0.1
        }
        logger.info("BenchmarkAnalysisTools inicializado")

    def analyze_competitor_data(self,
                                company_data: Dict[str, Any],
                                competitor_data: Dict[str, Any],
                                market_context: Optional[Dict[str, Any]] = None) -> CompetitorInsights:
        """
        Analisa dados de um concorrente específico

        Args:
            company_data: Dados da empresa analisando
            competitor_data: Dados do concorrente
            market_context: Contexto de mercado (opcional)

        Returns:
            Insights do concorrente processados
        """
        try:
            logger.info(
                f"Analisando concorrente: {competitor_data.get('name', 'Unnamed')}")

            competitor_name = competitor_data.get(
                "name", "Concorrente desconhecido")

            # Analisar posição no mercado
            market_position = self._determine_market_position(
                competitor_data, market_context)

            # Identificar áreas de força e fraqueza
            strength_areas = self._identify_strengths(
                competitor_data, company_data)
            weakness_areas = self._identify_weaknesses(
                competitor_data, company_data)

            # Calcular scores comparativos
            overall_score = self._calculate_overall_score(competitor_data)
            feature_score = self._calculate_feature_score(
                competitor_data, company_data)
            performance_score = self._calculate_performance_score(
                competitor_data)

            # Analisar tendências
            growth_trend = self._analyze_growth_trend(competitor_data)
            recent_moves = self._extract_recent_moves(competitor_data)

            # Determinar market share
            market_share = competitor_data.get("market_share", 0.0)

            insights = CompetitorInsights(
                competitor_name=competitor_name,
                market_position=market_position,
                strength_areas=strength_areas,
                weakness_areas=weakness_areas,
                overall_score=overall_score,
                feature_score=feature_score,
                performance_score=performance_score,
                market_share=market_share,
                growth_trend=growth_trend,
                recent_moves=recent_moves
            )

            logger.info(
                f"Análise concluída para {competitor_name} - Score: {overall_score}")
            return insights

        except Exception as e:
            logger.error(f"Erro na análise do concorrente: {str(e)}")
            raise

    def identify_competitive_gaps(self,
                                  company_data: Dict[str, Any],
                                  competitors_data: List[Dict[str, Any]],
                                  focus_areas: Optional[List[str]] = None) -> List[CompetitiveGap]:
        """
        Identifica gaps competitivos em relação aos concorrentes

        Args:
            company_data: Dados da empresa
            competitors_data: Lista de dados dos concorrentes
            focus_areas: Áreas específicas para focar (opcional)

        Returns:
            Lista de gaps competitivos identificados
        """
        try:
            logger.info(
                f"Identificando gaps competitivos contra {len(competitors_data)} concorrentes")

            gaps = []

            # Analisar cada área de comparação
            analysis_areas = focus_areas or [
                "features", "performance", "market_presence", "innovation", "pricing"]

            for area in analysis_areas:
                area_gaps = self._analyze_gaps_in_area(
                    company_data, competitors_data, area)
                gaps.extend(area_gaps)

            # Priorizar gaps por impacto e urgência
            gaps = self._prioritize_gaps(gaps)

            logger.info(f"Identificados {len(gaps)} gaps competitivos")
            return gaps

        except Exception as e:
            logger.error(f"Erro na identificação de gaps: {str(e)}")
            return []

    def calculate_competitive_scores(self,
                                     company_data: Dict[str, Any],
                                     competitors_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Calcula scores competitivos comparativos

        Args:
            company_data: Dados da empresa
            competitors_data: Lista de concorrentes

        Returns:
            Dicionário com scores e rankings
        """
        try:
            logger.info("Calculando scores competitivos")

            # Calcular score da empresa
            company_score = self._calculate_overall_score(company_data)

            # Calcular scores dos concorrentes
            competitor_scores = []
            for competitor in competitors_data:
                score = self._calculate_overall_score(competitor)
                competitor_scores.append({
                    "name": competitor.get("name", "Unnamed"),
                    "score": score,
                    "market_share": competitor.get("market_share", 0.0)
                })

            # Ordenar por score
            competitor_scores.sort(key=lambda x: x["score"], reverse=True)

            # Determinar posição da empresa
            company_rank = 1
            for i, comp in enumerate(competitor_scores):
                if comp["score"] < company_score:
                    company_rank = i + 1
                    break
                elif i == len(competitor_scores) - 1:
                    company_rank = len(competitor_scores) + 1

            # Calcular gaps de score
            if competitor_scores:
                leader_score = max(comp["score"] for comp in competitor_scores)
                score_gap = leader_score - company_score
            else:
                leader_score = company_score
                score_gap = 0.0

            results = {
                "company_score": company_score,
                "company_rank": company_rank,
                "total_analyzed": len(competitors_data) + 1,
                "score_gap_to_leader": score_gap,
                "leader_score": leader_score,
                "competitor_scores": competitor_scores,
                "performance_percentile": max(0, (len(competitors_data) + 1 - company_rank) / (len(competitors_data) + 1) * 100)
            }

            logger.info(
                f"Scores calculados - Empresa: {company_score}, Ranking: {company_rank}")
            return results

        except Exception as e:
            logger.error(f"Erro no cálculo de scores: {str(e)}")
            return {}

    def generate_differentiation_opportunities(self,
                                               company_data: Dict[str, Any],
                                               competitors_data: List[Dict[str, Any]],
                                               market_trends: Optional[List[str]] = None) -> List[DifferentiationStrategy]:
        """
        Gera oportunidades de diferenciação estratégica

        Args:
            company_data: Dados da empresa
            competitors_data: Dados dos concorrentes
            market_trends: Tendências de mercado (opcional)

        Returns:
            Lista de estratégias de diferenciação
        """
        try:
            logger.info("Gerando oportunidades de diferenciação")

            strategies = []

            # Analisar white spaces no mercado
            white_spaces = self._identify_market_white_spaces(
                company_data, competitors_data)

            # Analisar forças únicas da empresa
            unique_strengths = self._identify_unique_strengths(
                company_data, competitors_data)

            # Gerar estratégias baseadas em white spaces
            for space in white_spaces:
                strategy = self._create_white_space_strategy(
                    space, company_data)
                if strategy:
                    strategies.append(strategy)

            # Gerar estratégias baseadas em forças únicas
            for strength in unique_strengths:
                strategy = self._create_strength_based_strategy(
                    strength, company_data)
                if strategy:
                    strategies.append(strategy)

            # Gerar estratégias baseadas em trends de mercado
            if market_trends:
                for trend in market_trends:
                    strategy = self._create_trend_based_strategy(
                        trend, company_data, competitors_data)
                    if strategy:
                        strategies.append(strategy)

            # Priorizar estratégias
            strategies = self._prioritize_strategies(strategies)

            logger.info(
                f"Geradas {len(strategies)} estratégias de diferenciação")
            return strategies

        except Exception as e:
            logger.error(f"Erro na geração de estratégias: {str(e)}")
            return []

    # Métodos privados de apoio

    def _determine_market_position(self, competitor_data: Dict[str, Any], market_context: Optional[Dict[str, Any]]) -> str:
        """Determina a posição no mercado do concorrente"""
        try:
            market_share = competitor_data.get("market_share", 0.0)
            revenue = competitor_data.get("revenue", 0)

            if market_share > 25 or revenue > *********0:  # > $1B
                return "Líder de mercado"
            elif market_share > 10 or revenue > *********:  # > $100M
                return "Player forte"
            elif market_share > 5 or revenue > 10000000:  # > $10M
                return "Player estabelecido"
            else:
                return "Nicho/Emergente"

        except Exception:
            return "Posição indeterminada"

    def _identify_strengths(self, competitor_data: Dict[str, Any], company_data: Dict[str, Any]) -> List[str]:
        """Identifica áreas de força do concorrente"""
        strengths = []

        try:
            # Analisar funcionalidades
            comp_features = set(competitor_data.get("features", []))
            company_features = set(company_data.get("features", []))
            unique_features = comp_features - company_features

            if len(unique_features) > 5:
                strengths.append("Funcionalidades avançadas")

            # Analisar métricas de performance
            comp_performance = competitor_data.get("performance_metrics", {})
            if comp_performance.get("response_time", 1000) < 200:
                strengths.append("Performance superior")

            # Analisar presença de mercado
            if competitor_data.get("market_share", 0) > 15:
                strengths.append("Forte presença de mercado")

            # Analisar satisfação do cliente
            if competitor_data.get("customer_satisfaction", 0) > 4.5:
                strengths.append("Alta satisfação do cliente")

            # Analisar inovação
            patents = competitor_data.get("patents", 0)
            if patents > 10:
                strengths.append("Forte capacidade de inovação")

        except Exception:
            pass

        return strengths

    def _identify_weaknesses(self, competitor_data: Dict[str, Any], company_data: Dict[str, Any]) -> List[str]:
        """Identifica áreas de fraqueza do concorrente"""
        weaknesses = []

        try:
            # Analisar lacunas de funcionalidades
            comp_features = set(competitor_data.get("features", []))
            company_features = set(company_data.get("features", []))
            missing_features = company_features - comp_features

            if len(missing_features) > 3:
                weaknesses.append("Gaps funcionais significativos")

            # Analisar problemas de performance
            comp_performance = competitor_data.get("performance_metrics", {})
            if comp_performance.get("response_time", 0) > 1000:
                weaknesses.append("Performance lenta")

            # Analisar satisfação do cliente
            if competitor_data.get("customer_satisfaction", 5) < 3.5:
                weaknesses.append("Baixa satisfação do cliente")

            # Analisar preços
            comp_pricing = competitor_data.get("pricing", {})
            company_pricing = company_data.get("pricing", {})

            if comp_pricing.get("base_price", 0) > company_pricing.get("base_price", 0) * 1.5:
                weaknesses.append("Preços elevados")

        except Exception:
            pass

        return weaknesses

    def _calculate_overall_score(self, entity_data: Dict[str, Any]) -> float:
        """Calcula score geral baseado em múltiplas dimensões"""
        try:
            score = 50.0  # Score base

            # Score de funcionalidades
            features_count = len(entity_data.get("features", []))
            score += min(features_count * 2, 25)

            # Score de performance
            perf_metrics = entity_data.get("performance_metrics", {})
            response_time = perf_metrics.get("response_time", 1000)
            if response_time < 200:
                score += 15
            elif response_time < 500:
                score += 10
            elif response_time < 1000:
                score += 5

            # Score de satisfação do cliente
            satisfaction = entity_data.get("customer_satisfaction", 3.5)
            if satisfaction > 4.5:
                score += 15
            elif satisfaction > 4.0:
                score += 10
            elif satisfaction > 3.5:
                score += 5

            # Score de inovação
            patents = entity_data.get("patents", 0)
            score += min(patents * 0.5, 10)

            return min(score, 100.0)

        except Exception:
            return 50.0

    def _calculate_feature_score(self, competitor_data: Dict[str, Any], company_data: Dict[str, Any]) -> float:
        """Calcula score específico de funcionalidades"""
        try:
            comp_features = set(competitor_data.get("features", []))
            company_features = set(company_data.get("features", []))

            if not comp_features:
                return 50.0

            # Funcionalidades únicas do concorrente
            unique_to_competitor = comp_features - company_features

            # Funcionalidades compartilhadas
            shared_features = comp_features & company_features

            # Calcular score baseado na cobertura
            total_market_features = len(comp_features | company_features)
            competitor_coverage = len(
                comp_features) / max(total_market_features, 1)

            score = competitor_coverage * 100

            # Bonus por funcionalidades únicas
            if unique_to_competitor:
                score += len(unique_to_competitor) * 2

            return min(score, 100.0)

        except Exception:
            return 50.0

    def _calculate_performance_score(self, entity_data: Dict[str, Any]) -> float:
        """Calcula score de performance"""
        try:
            score = 50.0
            perf_metrics = entity_data.get("performance_metrics", {})

            # Response time
            response_time = perf_metrics.get("response_time", 1000)
            if response_time < 100:
                score += 25
            elif response_time < 300:
                score += 20
            elif response_time < 500:
                score += 15
            elif response_time < 1000:
                score += 10

            # Uptime
            uptime = perf_metrics.get("uptime", 95)
            if uptime > 99.9:
                score += 15
            elif uptime > 99.5:
                score += 10
            elif uptime > 99:
                score += 5

            # Throughput
            throughput = perf_metrics.get("throughput", 100)
            if throughput > 1000:
                score += 10
            elif throughput > 500:
                score += 5

            return min(score, 100.0)

        except Exception:
            return 50.0

    def _analyze_growth_trend(self, competitor_data: Dict[str, Any]) -> str:
        """Analisa tendência de crescimento"""
        try:
            growth_metrics = competitor_data.get("growth_metrics", {})
            revenue_growth = growth_metrics.get("revenue_growth_rate", 0)
            user_growth = growth_metrics.get("user_growth_rate", 0)

            avg_growth = (revenue_growth + user_growth) / 2

            if avg_growth > 30:
                return "Crescimento rápido"
            elif avg_growth > 15:
                return "Crescimento moderado"
            elif avg_growth > 5:
                return "Crescimento lento"
            elif avg_growth > -5:
                return "Estável"
            else:
                return "Declínio"

        except Exception:
            return "Indeterminado"

    def _extract_recent_moves(self, competitor_data: Dict[str, Any]) -> List[str]:
        """Extrai movimentos recentes do concorrente"""
        try:
            recent_moves = competitor_data.get("recent_moves", [])
            return recent_moves[:5]  # Limitar a 5 movimentos mais recentes
        except Exception:
            return []

    def _analyze_gaps_in_area(self, company_data: Dict[str, Any], competitors_data: List[Dict[str, Any]], area: str) -> List[CompetitiveGap]:
        """Analisa gaps em uma área específica"""
        gaps = []

        try:
            if area == "features":
                gaps.extend(self._analyze_feature_gaps(
                    company_data, competitors_data))
            elif area == "performance":
                gaps.extend(self._analyze_performance_gaps(
                    company_data, competitors_data))
            elif area == "market_presence":
                gaps.extend(self._analyze_market_gaps(
                    company_data, competitors_data))
            elif area == "innovation":
                gaps.extend(self._analyze_innovation_gaps(
                    company_data, competitors_data))
            elif area == "pricing":
                gaps.extend(self._analyze_pricing_gaps(
                    company_data, competitors_data))
        except Exception:
            pass

        return gaps

    def _analyze_feature_gaps(self, company_data: Dict[str, Any], competitors_data: List[Dict[str, Any]]) -> List[CompetitiveGap]:
        """Analisa gaps de funcionalidades"""
        gaps = []

        try:
            company_features = set(company_data.get("features", []))

            # Analisar funcionalidades que concorrentes têm mas a empresa não
            for competitor in competitors_data:
                comp_features = set(competitor.get("features", []))
                missing_features = comp_features - company_features

                for feature in missing_features:
                    gap = CompetitiveGap(
                        gap_type="Funcionalidade",
                        description=f"Funcionalidade '{feature}' presente em {competitor.get('name', 'concorrente')} mas ausente em nosso produto",
                        impact_level="Médio",
                        urgency="Importante",
                        competitor_advantage=f"{competitor.get('name', 'Concorrente')} oferece {feature}",
                        recommended_action=f"Considerar implementação de {feature}",
                        implementation_effort="Médio",
                        timeline_estimate="2-3 meses"
                    )
                    gaps.append(gap)
        except Exception:
            pass

        return gaps

    def _analyze_performance_gaps(self, company_data: Dict[str, Any], competitors_data: List[Dict[str, Any]]) -> List[CompetitiveGap]:
        """Analisa gaps de performance"""
        gaps = []

        try:
            company_perf = company_data.get("performance_metrics", {})
            company_response_time = company_perf.get("response_time", 1000)

            for competitor in competitors_data:
                comp_perf = competitor.get("performance_metrics", {})
                comp_response_time = comp_perf.get("response_time", 1000)

                if comp_response_time < company_response_time * 0.8:  # 20% melhor
                    gap = CompetitiveGap(
                        gap_type="Performance",
                        description=f"Response time significativamente inferior ao {competitor.get('name', 'concorrente')}",
                        impact_level="Alto",
                        urgency="Crítico",
                        competitor_advantage=f"Response time de {comp_response_time}ms vs nossos {company_response_time}ms",
                        recommended_action="Otimização de performance prioritária",
                        implementation_effort="Alto",
                        timeline_estimate="3-6 meses"
                    )
                    gaps.append(gap)
        except Exception:
            pass

        return gaps

    def _analyze_market_gaps(self, company_data: Dict[str, Any], competitors_data: List[Dict[str, Any]]) -> List[CompetitiveGap]:
        """Analisa gaps de presença de mercado"""
        gaps = []

        try:
            company_market_share = company_data.get("market_share", 0)

            for competitor in competitors_data:
                comp_market_share = competitor.get("market_share", 0)

                if comp_market_share > company_market_share * 2:  # Dobro da participação
                    gap = CompetitiveGap(
                        gap_type="Presença de Mercado",
                        description=f"Participação de mercado significativamente menor que {competitor.get('name', 'concorrente')}",
                        impact_level="Alto",
                        urgency="Importante",
                        competitor_advantage=f"Market share de {comp_market_share}% vs nossos {company_market_share}%",
                        recommended_action="Estratégia de expansão de mercado",
                        implementation_effort="Alto",
                        timeline_estimate="6-12 meses"
                    )
                    gaps.append(gap)
        except Exception:
            pass

        return gaps

    def _analyze_innovation_gaps(self, company_data: Dict[str, Any], competitors_data: List[Dict[str, Any]]) -> List[CompetitiveGap]:
        """Analisa gaps de inovação"""
        gaps = []

        try:
            company_patents = company_data.get("patents", 0)

            for competitor in competitors_data:
                comp_patents = competitor.get("patents", 0)

                if comp_patents > company_patents * 3:  # Triplo de patentes
                    gap = CompetitiveGap(
                        gap_type="Inovação",
                        description=f"Portfolio de inovação inferior ao {competitor.get('name', 'concorrente')}",
                        impact_level="Médio",
                        urgency="Importante",
                        competitor_advantage=f"{comp_patents} patentes vs nossas {company_patents}",
                        recommended_action="Investimento em P&D e inovação",
                        implementation_effort="Alto",
                        timeline_estimate="12+ meses"
                    )
                    gaps.append(gap)
        except Exception:
            pass

        return gaps

    def _analyze_pricing_gaps(self, company_data: Dict[str, Any], competitors_data: List[Dict[str, Any]]) -> List[CompetitiveGap]:
        """Analisa gaps de pricing"""
        gaps = []

        try:
            company_pricing = company_data.get("pricing", {})
            company_price = company_pricing.get("base_price", 0)

            for competitor in competitors_data:
                comp_pricing = competitor.get("pricing", {})
                comp_price = comp_pricing.get("base_price", 0)

                if comp_price < company_price * 0.7:  # 30% mais barato
                    gap = CompetitiveGap(
                        gap_type="Pricing",
                        description=f"Preço significativamente superior ao {competitor.get('name', 'concorrente')}",
                        impact_level="Alto",
                        urgency="Crítico",
                        competitor_advantage=f"Preço de ${comp_price} vs nossos ${company_price}",
                        recommended_action="Revisão da estratégia de pricing",
                        implementation_effort="Médio",
                        timeline_estimate="1-2 meses"
                    )
                    gaps.append(gap)
        except Exception:
            pass

        return gaps

    def _prioritize_gaps(self, gaps: List[CompetitiveGap]) -> List[CompetitiveGap]:
        """Prioriza gaps por impacto e urgência"""
        try:
            # Definir ordem de prioridade
            impact_priority = {"Alto": 3, "Médio": 2, "Baixo": 1}
            urgency_priority = {"Crítico": 3, "Importante": 2, "Desejável": 1}

            # Calcular score de prioridade
            for gap in gaps:
                impact_score = impact_priority.get(gap.impact_level, 1)
                urgency_score = urgency_priority.get(gap.urgency, 1)
                gap.priority_score = impact_score * urgency_score

            # Ordenar por prioridade
            gaps.sort(key=lambda x: getattr(
                x, 'priority_score', 0), reverse=True)

        except Exception:
            pass

        return gaps

    def _identify_market_white_spaces(self, company_data: Dict[str, Any], competitors_data: List[Dict[str, Any]]) -> List[str]:
        """Identifica espaços em branco no mercado"""
        white_spaces = []

        try:
            # Analisar segmentos não atendidos
            all_segments = set()
            for competitor in competitors_data:
                segments = competitor.get("target_segments", [])
                all_segments.update(segments)

            company_segments = set(company_data.get("target_segments", []))

            # Identificar segmentos potenciais
            potential_segments = [
                "Pequenas empresas",
                "Empresas médias",
                "Grandes corporações",
                "Startups",
                "ONGs",
                "Educação",
                "Governo",
                "Saúde"
            ]

            for segment in potential_segments:
                if segment not in all_segments and segment not in company_segments:
                    white_spaces.append(f"Segmento {segment} não atendido")

        except Exception:
            pass

        return white_spaces

    def _identify_unique_strengths(self, company_data: Dict[str, Any], competitors_data: List[Dict[str, Any]]) -> List[str]:
        """Identifica forças únicas da empresa"""
        unique_strengths = []

        try:
            company_features = set(company_data.get("features", []))

            # Encontrar funcionalidades que só a empresa tem
            all_competitor_features = set()
            for competitor in competitors_data:
                comp_features = set(competitor.get("features", []))
                all_competitor_features.update(comp_features)

            unique_features = company_features - all_competitor_features
            for feature in unique_features:
                unique_strengths.append(f"Funcionalidade única: {feature}")

            # Analisar vantagens de performance
            company_perf = company_data.get("performance_metrics", {})
            company_response_time = company_perf.get("response_time", 1000)

            best_competitor_time = min(
                (comp.get("performance_metrics", {}).get("response_time", 1000)
                 for comp in competitors_data),
                default=1000
            )

            if company_response_time < best_competitor_time * 0.9:
                unique_strengths.append("Performance superior")

        except Exception:
            pass

        return unique_strengths

    def _create_white_space_strategy(self, white_space: str, company_data: Dict[str, Any]) -> Optional[DifferentiationStrategy]:
        """Cria estratégia baseada em white space"""
        try:
            return DifferentiationStrategy(
                strategy_name=f"Exploração de {white_space}",
                focus_area="Expansão de Mercado",
                description=f"Estratégia para explorar {white_space} identificado no mercado",
                competitive_advantage="Pioneirismo em segmento não atendido",
                target_segments=[white_space.split(
                )[-2:][0]] if len(white_space.split()) > 1 else ["Novo segmento"],
                success_metrics=["Market share no segmento",
                                 "Revenue do segmento", "Customer acquisition"],
                required_resources=["Marketing",
                                    "Sales", "Product customization"],
                implementation_complexity="Médio",
                expected_roi=15.0,
                risk_level="Médio"
            )
        except Exception:
            return None

    def _create_strength_based_strategy(self, strength: str, company_data: Dict[str, Any]) -> Optional[DifferentiationStrategy]:
        """Cria estratégia baseada em força única"""
        try:
            return DifferentiationStrategy(
                strategy_name=f"Maximização de {strength}",
                focus_area="Diferenciação Competitiva",
                description=f"Estratégia para maximizar vantagem em {strength}",
                competitive_advantage=f"Liderança em {strength}",
                target_segments=company_data.get(
                    "target_segments", ["Mercado atual"]),
                success_metrics=["Market leadership",
                                 "Premium pricing", "Customer loyalty"],
                required_resources=["Marketing", "R&D", "Sales training"],
                implementation_complexity="Baixo",
                expected_roi=20.0,
                risk_level="Baixo"
            )
        except Exception:
            return None

    def _create_trend_based_strategy(self, trend: str, company_data: Dict[str, Any], competitors_data: List[Dict[str, Any]]) -> Optional[DifferentiationStrategy]:
        """Cria estratégia baseada em trend de mercado"""
        try:
            return DifferentiationStrategy(
                strategy_name=f"Capitalização em {trend}",
                focus_area="Inovação e Trends",
                description=f"Estratégia para capitalizar na tendência {trend}",
                competitive_advantage=f"Early adopter de {trend}",
                target_segments=["Early adopters", "Tech-forward companies"],
                success_metrics=["Innovation leadership",
                                 "Media coverage", "Customer adoption"],
                required_resources=["R&D", "Technology", "Marketing"],
                implementation_complexity="Alto",
                expected_roi=25.0,
                risk_level="Alto"
            )
        except Exception:
            return None

    def _prioritize_strategies(self, strategies: List[DifferentiationStrategy]) -> List[DifferentiationStrategy]:
        """Prioriza estratégias por ROI e risco"""
        try:
            # Calcular score de prioridade (ROI / Risco)
            risk_multiplier = {"Baixo": 1.0, "Médio": 0.7, "Alto": 0.5}

            for strategy in strategies:
                risk_mult = risk_multiplier.get(strategy.risk_level, 0.7)
                strategy.priority_score = strategy.expected_roi * risk_mult

            # Ordenar por score de prioridade
            strategies.sort(key=lambda x: getattr(
                x, 'priority_score', 0), reverse=True)

        except Exception:
            pass

        return strategies
