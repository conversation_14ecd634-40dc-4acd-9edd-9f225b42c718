"""Value Object para status de cliente usando Enum."""

from enum import Enum
from typing import List, Set


class ClientStatus(str, Enum):
    """Estados possíveis de um cliente no sistema.

    Herda de str para facilitar serialização JSON.
    """

    # Estados principais
    DRAFT = "draft"  # Cliente criado mas não analisado
    PENDING = "pending"  # Aguardando análise inicial
    PROCESSING = "processing"  # Análise em andamento
    COMPLETED = "completed"  # Análise concluída com sucesso
    FAILED = "failed"  # Análise falhou
    ARCHIVED = "archived"  # Cliente arquivado

    @classmethod
    def active_statuses(cls) -> Set['ClientStatus']:
        """Retorna os status considerados ativos."""
        return {cls.DRAFT, cls.PENDING, cls.PROCESSING, cls.COMPLETED}

    @classmethod
    def terminal_statuses(cls) -> Set['ClientStatus']:
        """Retorna os status terminais (não podem mudar)."""
        return {cls.ARCHIVED}

    @classmethod
    def analysis_statuses(cls) -> Set['ClientStatus']:
        """Retorna os status relacionados à análise."""
        return {cls.PENDING, cls.PROCESSING, cls.COMPLETED, cls.FAILED}

    def can_transition_to(self, new_status: 'ClientStatus') -> bool:
        """Verifica se pode transicionar para um novo status.

        Args:
            new_status: Status de destino

        Returns:
            True se a transição é permitida
        """
        # Define transições permitidas
        transitions = {
            ClientStatus.DRAFT: {
                ClientStatus.PENDING,
                ClientStatus.ARCHIVED
            },
            ClientStatus.PENDING: {
                ClientStatus.PROCESSING,
                ClientStatus.FAILED,
                ClientStatus.ARCHIVED
            },
            ClientStatus.PROCESSING: {
                ClientStatus.COMPLETED,
                ClientStatus.FAILED
            },
            ClientStatus.COMPLETED: {
                ClientStatus.PENDING,  # Permite re-análise
                ClientStatus.ARCHIVED
            },
            ClientStatus.FAILED: {
                ClientStatus.PENDING,  # Permite retry
                ClientStatus.ARCHIVED
            },
            ClientStatus.ARCHIVED: set()  # Não pode sair de archived
        }

        allowed = transitions.get(self, set())
        return new_status in allowed

    def is_active(self) -> bool:
        """Verifica se o status é considerado ativo."""
        return self in self.active_statuses()

    def is_terminal(self) -> bool:
        """Verifica se o status é terminal."""
        return self in self.terminal_statuses()

    def can_request_analysis(self) -> bool:
        """Verifica se pode solicitar análise neste status."""
        return self in {ClientStatus.DRAFT, ClientStatus.COMPLETED, ClientStatus.FAILED}

    def __str__(self) -> str:
        """Representação string do status."""
        return self.value
