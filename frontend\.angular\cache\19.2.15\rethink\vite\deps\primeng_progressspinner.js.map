{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-progressspinner.mjs"], "sourcesContent": ["import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, inject, Input, ViewEncapsulation, ChangeDetectionStrategy, Component, NgModule } from '@angular/core';\nimport { SharedModule } from 'primeng/api';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { BaseStyle } from 'primeng/base';\nconst theme = ({\n  dt\n}) => `\n.p-progressspinner {\n    position: relative;\n    margin: 0 auto;\n    width: 100px;\n    height: 100px;\n    display: inline-block;\n}\n\n.p-progressspinner::before {\n    content: \"\";\n    display: block;\n    padding-top: 100%;\n}\n\n.p-progressspinner-spin {\n    height: 100%;\n    transform-origin: center center;\n    width: 100%;\n    position: absolute;\n    top: 0;\n    bottom: 0;\n    left: 0;\n    right: 0;\n    margin: auto;\n    animation: p-progressspinner-rotate 2s linear infinite;\n}\n\n.p-progressspinner-circle {\n    stroke-dasharray: 89, 200;\n    stroke-dashoffset: 0;\n    stroke: ${dt('progressspinner.colorOne')};\n    animation: p-progressspinner-dash 1.5s ease-in-out infinite, p-progressspinner-color 6s ease-in-out infinite;\n    stroke-linecap: round;\n}\n\n@keyframes p-progressspinner-rotate {\n    100% {\n        transform: rotate(360deg);\n    }\n}\n@keyframes p-progressspinner-dash {\n    0% {\n        stroke-dasharray: 1, 200;\n        stroke-dashoffset: 0;\n    }\n    50% {\n        stroke-dasharray: 89, 200;\n        stroke-dashoffset: -35px;\n    }\n    100% {\n        stroke-dasharray: 89, 200;\n        stroke-dashoffset: -124px;\n    }\n}\n@keyframes p-progressspinner-color {\n    100%,\n    0% {\n        stroke: ${dt('progressspinner.colorOne')};\n    }\n    40% {\n        stroke: ${dt('progressspinner.colorTwo')};\n    }\n    66% {\n        stroke: ${dt('progressspinner.colorThree')};\n    }\n    80%,\n    90% {\n        stroke: ${dt('progressspinner.colorFour')};\n    }\n}\n`;\nconst classes = {\n  root: 'p-progressspinner',\n  spin: 'p-progressspinner-spin',\n  circle: 'p-progressspinner-circle'\n};\nclass ProgressSpinnerStyle extends BaseStyle {\n  name = 'progressspinner';\n  theme = theme;\n  classes = classes;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵProgressSpinnerStyle_BaseFactory;\n    return function ProgressSpinnerStyle_Factory(__ngFactoryType__) {\n      return (ɵProgressSpinnerStyle_BaseFactory || (ɵProgressSpinnerStyle_BaseFactory = i0.ɵɵgetInheritedFactory(ProgressSpinnerStyle)))(__ngFactoryType__ || ProgressSpinnerStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ProgressSpinnerStyle,\n    factory: ProgressSpinnerStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ProgressSpinnerStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n *\n * ProgressSpinner is a process status indicator.\n *\n * [Live Demo](https://www.primeng.org/progressspinner)\n *\n * @module progressspinnerstyle\n *\n */\nvar ProgressSpinnerClasses;\n(function (ProgressSpinnerClasses) {\n  /**\n   * Class name of the root element\n   */\n  ProgressSpinnerClasses[\"root\"] = \"p-progressspinner\";\n  /**\n   * Class name of the spin element\n   */\n  ProgressSpinnerClasses[\"spin\"] = \"p-progressspinner-spin\";\n  /**\n   * Class name of the circle element\n   */\n  ProgressSpinnerClasses[\"circle\"] = \"p-progressspinner-circle\";\n})(ProgressSpinnerClasses || (ProgressSpinnerClasses = {}));\n\n/**\n * ProgressSpinner is a process status indicator.\n * @group Components\n */\nclass ProgressSpinner extends BaseComponent {\n  /**\n   * Class of the element.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Inline style of the element.\n   * @group Props\n   */\n  style;\n  /**\n   * Width of the circle stroke.\n   * @group Props\n   */\n  strokeWidth = '2';\n  /**\n   * Color for the background of the circle.\n   * @group Props\n   */\n  fill = 'none';\n  /**\n   * Duration of the rotate animation.\n   * @group Props\n   */\n  animationDuration = '2s';\n  /**\n   * Used to define a aria label attribute the current element.\n   * @group Props\n   */\n  ariaLabel;\n  _componentStyle = inject(ProgressSpinnerStyle);\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵProgressSpinner_BaseFactory;\n    return function ProgressSpinner_Factory(__ngFactoryType__) {\n      return (ɵProgressSpinner_BaseFactory || (ɵProgressSpinner_BaseFactory = i0.ɵɵgetInheritedFactory(ProgressSpinner)))(__ngFactoryType__ || ProgressSpinner);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: ProgressSpinner,\n    selectors: [[\"p-progressSpinner\"], [\"p-progress-spinner\"], [\"p-progressspinner\"]],\n    inputs: {\n      styleClass: \"styleClass\",\n      style: \"style\",\n      strokeWidth: \"strokeWidth\",\n      fill: \"fill\",\n      animationDuration: \"animationDuration\",\n      ariaLabel: \"ariaLabel\"\n    },\n    features: [i0.ɵɵProvidersFeature([ProgressSpinnerStyle]), i0.ɵɵInheritDefinitionFeature],\n    decls: 3,\n    vars: 11,\n    consts: [[\"role\", \"progressbar\", 1, \"p-progressspinner\", 3, \"ngStyle\", \"ngClass\"], [\"viewBox\", \"25 25 50 50\", 1, \"p-progressspinner-spin\"], [\"cx\", \"50\", \"cy\", \"50\", \"r\", \"20\", \"stroke-miterlimit\", \"10\", 1, \"p-progressspinner-circle\"]],\n    template: function ProgressSpinner_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(1, \"svg\", 1);\n        i0.ɵɵelement(2, \"circle\", 2);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngStyle\", ctx.style)(\"ngClass\", ctx.styleClass);\n        i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel)(\"aria-busy\", true)(\"data-pc-name\", \"progressspinner\")(\"data-pc-section\", \"root\");\n        i0.ɵɵadvance();\n        i0.ɵɵstyleProp(\"animation-duration\", ctx.animationDuration);\n        i0.ɵɵattribute(\"data-pc-section\", \"root\");\n        i0.ɵɵadvance();\n        i0.ɵɵattribute(\"fill\", ctx.fill)(\"stroke-width\", ctx.strokeWidth);\n      }\n    },\n    dependencies: [CommonModule, i1.NgClass, i1.NgStyle, SharedModule],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ProgressSpinner, [{\n    type: Component,\n    args: [{\n      selector: 'p-progressSpinner, p-progress-spinner, p-progressspinner',\n      standalone: true,\n      imports: [CommonModule, SharedModule],\n      template: `\n        <div class=\"p-progressspinner\" [ngStyle]=\"style\" [ngClass]=\"styleClass\" role=\"progressbar\" [attr.aria-label]=\"ariaLabel\" [attr.aria-busy]=\"true\" [attr.data-pc-name]=\"'progressspinner'\" [attr.data-pc-section]=\"'root'\">\n            <svg class=\"p-progressspinner-spin\" viewBox=\"25 25 50 50\" [style.animation-duration]=\"animationDuration\" [attr.data-pc-section]=\"'root'\">\n                <circle class=\"p-progressspinner-circle\" cx=\"50\" cy=\"50\" r=\"20\" [attr.fill]=\"fill\" [attr.stroke-width]=\"strokeWidth\" stroke-miterlimit=\"10\" />\n            </svg>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [ProgressSpinnerStyle]\n    }]\n  }], null, {\n    styleClass: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    strokeWidth: [{\n      type: Input\n    }],\n    fill: [{\n      type: Input\n    }],\n    animationDuration: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }]\n  });\n})();\nclass ProgressSpinnerModule {\n  static ɵfac = function ProgressSpinnerModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ProgressSpinnerModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ProgressSpinnerModule,\n    imports: [ProgressSpinner, SharedModule],\n    exports: [ProgressSpinner, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [ProgressSpinner, SharedModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ProgressSpinnerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [ProgressSpinner, SharedModule],\n      exports: [ProgressSpinner, SharedModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ProgressSpinner, ProgressSpinnerClasses, ProgressSpinnerModule, ProgressSpinnerStyle };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAM,QAAQ,CAAC;AAAA,EACb;AACF,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cA+BQ,GAAG,0BAA0B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBA2B1B,GAAG,0BAA0B,CAAC;AAAA;AAAA;AAAA,kBAG9B,GAAG,0BAA0B,CAAC;AAAA;AAAA;AAAA,kBAG9B,GAAG,4BAA4B,CAAC;AAAA;AAAA;AAAA;AAAA,kBAIhC,GAAG,2BAA2B,CAAC;AAAA;AAAA;AAAA;AAIjD,IAAM,UAAU;AAAA,EACd,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AACV;AACA,IAAM,uBAAN,MAAM,8BAA6B,UAAU;AAAA,EAC3C,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,6BAA6B,mBAAmB;AAC9D,cAAQ,sCAAsC,oCAAuC,sBAAsB,qBAAoB,IAAI,qBAAqB,qBAAoB;AAAA,IAC9K;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,sBAAqB;AAAA,EAChC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAI;AAAA,CACH,SAAUA,yBAAwB;AAIjC,EAAAA,wBAAuB,MAAM,IAAI;AAIjC,EAAAA,wBAAuB,MAAM,IAAI;AAIjC,EAAAA,wBAAuB,QAAQ,IAAI;AACrC,GAAG,2BAA2B,yBAAyB,CAAC,EAAE;AAM1D,IAAM,kBAAN,MAAM,yBAAwB,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKd,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAKP,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB;AAAA,EACA,kBAAkB,OAAO,oBAAoB;AAAA,EAC7C,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,wBAAwB,mBAAmB;AACzD,cAAQ,iCAAiC,+BAAkC,sBAAsB,gBAAe,IAAI,qBAAqB,gBAAe;AAAA,IAC1J;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,mBAAmB,GAAG,CAAC,oBAAoB,GAAG,CAAC,mBAAmB,CAAC;AAAA,IAChF,QAAQ;AAAA,MACN,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,aAAa;AAAA,MACb,MAAM;AAAA,MACN,mBAAmB;AAAA,MACnB,WAAW;AAAA,IACb;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC,oBAAoB,CAAC,GAAM,0BAA0B;AAAA,IACvF,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,QAAQ,eAAe,GAAG,qBAAqB,GAAG,WAAW,SAAS,GAAG,CAAC,WAAW,eAAe,GAAG,wBAAwB,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,KAAK,MAAM,qBAAqB,MAAM,GAAG,0BAA0B,CAAC;AAAA,IACzO,UAAU,SAAS,yBAAyB,IAAI,KAAK;AACnD,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,eAAe;AAClB,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,UAAU,GAAG,UAAU,CAAC;AAC3B,QAAG,aAAa,EAAE;AAAA,MACpB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,WAAW,IAAI,KAAK,EAAE,WAAW,IAAI,UAAU;AAC7D,QAAG,YAAY,cAAc,IAAI,SAAS,EAAE,aAAa,IAAI,EAAE,gBAAgB,iBAAiB,EAAE,mBAAmB,MAAM;AAC3H,QAAG,UAAU;AACb,QAAG,YAAY,sBAAsB,IAAI,iBAAiB;AAC1D,QAAG,YAAY,mBAAmB,MAAM;AACxC,QAAG,UAAU;AACb,QAAG,YAAY,QAAQ,IAAI,IAAI,EAAE,gBAAgB,IAAI,WAAW;AAAA,MAClE;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAiB,SAAY,SAAS,YAAY;AAAA,IACjE,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc,YAAY;AAAA,MACpC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,WAAW,CAAC,oBAAoB;AAAA,IAClC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,OAAO,OAAO,SAAS,8BAA8B,mBAAmB;AACtE,WAAO,KAAK,qBAAqB,wBAAuB;AAAA,EAC1D;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,iBAAiB,YAAY;AAAA,IACvC,SAAS,CAAC,iBAAiB,YAAY;AAAA,EACzC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,iBAAiB,cAAc,YAAY;AAAA,EACvD,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,YAAY;AAAA,MACvC,SAAS,CAAC,iBAAiB,YAAY;AAAA,IACzC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["ProgressSpinnerClasses"]}