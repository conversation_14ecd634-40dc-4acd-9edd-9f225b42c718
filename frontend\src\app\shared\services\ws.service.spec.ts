import { TestBed } from '@angular/core/testing';
import { ProcessingNotification, WebSocketService } from './ws.service';

// Mock do WebSocket
class MockWebSocket {
	public readyState = 1; // WebSocket.OPEN
	public onmessage: ((event: MessageEvent) => void) | null = null;
	public onclose: (() => void) | null = null;
	public onerror: ((error: Event) => void) | null = null;

	constructor(public url: string) {}

	send(data: string): void {
		// Mock implementation
	}

	close(): void {
		this.readyState = 3; // WebSocket.CLOSED
		if (this.onclose) {
			this.onclose();
		}
	}

	// Helper para simular mensagens recebidas
	simulateMessage(data: any): void {
		if (this.onmessage) {
			this.onmessage({ data: JSON.stringify(data) } as MessageEvent);
		}
	}

	// Helper para simular erro
	simulateError(error: Event): void {
		if (this.onerror) {
			this.onerror(error);
		}
	}
}

describe('WebSocketService', () => {
	let service: WebSocketService;
	let mockWebSocket: MockWebSocket;
	let originalWebSocket: any;

	beforeEach(() => {
		TestBed.configureTestingModule({});
		service = TestBed.inject(WebSocketService);

		// Salvar WebSocket original
		originalWebSocket = (global as any).WebSocket;

		// Mock do WebSocket global
		(global as any).WebSocket = jasmine.createSpy('WebSocket').and.callFake((url: string) => {
			mockWebSocket = new MockWebSocket(url);
			return mockWebSocket;
		});
	});

	afterEach(() => {
		service.close();
		// Restaurar WebSocket original
		(global as any).WebSocket = originalWebSocket;
	});

	describe('Conexão WebSocket', () => {
		it('deve conectar ao WebSocket com URL correta', () => {
			service.connect();
			expect((global as any).WebSocket).toHaveBeenCalledWith('ws://localhost:8040/ws');
		});

		it('deve configurar handlers de eventos ao conectar', () => {
			service.connect();
			expect(mockWebSocket.onmessage).toBeDefined();
			expect(mockWebSocket.onclose).toBeDefined();
			expect(mockWebSocket.onerror).toBeDefined();
		});

		it('deve fechar conexão WebSocket', () => {
			service.connect();
			spyOn(mockWebSocket, 'close');
			service.close();
			expect(mockWebSocket.close).toHaveBeenCalled();
		});
	});

	describe('Mensagens Gerais', () => {
		beforeEach(() => {
			service.connect();
		});

		it('deve adicionar mensagens ao signal messages', () => {
			const testMessage = { type: 'test', data: 'test data' };

			mockWebSocket.simulateMessage(testMessage);

			const messages = service.messages();
			expect(messages).toContain(testMessage);
		});

		it('deve acumular múltiplas mensagens', () => {
			const message1 = { type: 'test1', data: 'data1' };
			const message2 = { type: 'test2', data: 'data2' };

			mockWebSocket.simulateMessage(message1);
			mockWebSocket.simulateMessage(message2);

			const messages = service.messages();
			expect(messages.length).toBe(2);
			expect(messages).toContain(message1);
			expect(messages).toContain(message2);
		});
	});

	describe('Notificações de Processamento', () => {
		beforeEach(() => {
			service.connect();
		});

		it('deve identificar notificação de processamento válida', () => {
			const validNotification: ProcessingNotification = {
				type: 'processing_started',
				clientId: 'client-123',
				data: {
					clientId: 'client-123',
					isProcessing: true,
					statusMessage: 'Iniciando análise',
					progressPercentage: 0,
					canViewReport: false,
					timestamp: new Date(),
				},
			};

			mockWebSocket.simulateMessage(validNotification);

			const states = service.processingStates();
			expect(states.has('client-123')).toBe(true);
		});

		it('deve ignorar mensagens que não são notificações de processamento', () => {
			const invalidMessage = { type: 'other_type', data: 'some data' };

			mockWebSocket.simulateMessage(invalidMessage);

			const states = service.processingStates();
			expect(states.size).toBe(0);
		});

		it('deve atualizar estado de processamento para cliente específico', () => {
			const notification: ProcessingNotification = {
				type: 'processing_progress',
				clientId: 'client-456',
				data: {
					clientId: 'client-456',
					isProcessing: true,
					statusMessage: 'Analisando dados técnicos',
					progressPercentage: 50,
					canViewReport: false,
					phase: 'technical_analysis',
					timestamp: new Date(),
				},
			};

			mockWebSocket.simulateMessage(notification);

			const state = service.getProcessingState('client-456');
			expect(state).toBeDefined();
			expect(state!.progressPercentage).toBe(50);
			expect(state!.phase).toBe('technical_analysis');
		});

		it('deve emitir notificação via Subject para subscribers', done => {
			const notification: ProcessingNotification = {
				type: 'processing_complete',
				clientId: 'client-789',
				data: {
					clientId: 'client-789',
					isProcessing: false,
					statusMessage: 'Análise concluída',
					progressPercentage: 100,
					canViewReport: true,
					timestamp: new Date(),
				},
			};

			service.processingNotifications$.subscribe(receivedNotification => {
				expect(receivedNotification).toEqual(notification);
				done();
			});

			mockWebSocket.simulateMessage(notification);
		});

		it('deve filtrar notificações por cliente específico', done => {
			const targetClientId = 'client-target';
			const otherClientId = 'client-other';

			let receivedCount = 0;

			service.subscribeToClientProcessing(targetClientId).subscribe(notification => {
				expect(notification.clientId).toBe(targetClientId);
				receivedCount++;
				if (receivedCount === 1) {
					done();
				}
			});

			// Enviar notificação para outro cliente (não deve ser recebida)
			mockWebSocket.simulateMessage({
				type: 'processing_started',
				clientId: otherClientId,
				data: {
					clientId: otherClientId,
					isProcessing: true,
					statusMessage: 'Iniciando',
					progressPercentage: 0,
					canViewReport: false,
					timestamp: new Date(),
				},
			});

			// Enviar notificação para cliente alvo (deve ser recebida)
			mockWebSocket.simulateMessage({
				type: 'processing_started',
				clientId: targetClientId,
				data: {
					clientId: targetClientId,
					isProcessing: true,
					statusMessage: 'Iniciando',
					progressPercentage: 0,
					canViewReport: false,
					timestamp: new Date(),
				},
			});
		});
	});

	describe('Envio de Mensagens', () => {
		beforeEach(() => {
			service.connect();
		});

		it('deve enviar mensagem quando WebSocket está aberto', () => {
			spyOn(mockWebSocket, 'send');
			const testMessage = { type: 'test', data: 'test' };

			service.sendMessage(testMessage);

			expect(mockWebSocket.send).toHaveBeenCalledWith(JSON.stringify(testMessage));
		});

		it('não deve enviar mensagem quando WebSocket está fechado', () => {
			mockWebSocket.readyState = 3; // WebSocket.CLOSED
			spyOn(mockWebSocket, 'send');

			service.sendMessage({ type: 'test' });

			expect(mockWebSocket.send).not.toHaveBeenCalled();
		});

		it('deve enviar solicitação de início de processamento', () => {
			spyOn(mockWebSocket, 'send');
			const clientId = 'client-123';

			service.requestProcessingStart(clientId);

			expect(mockWebSocket.send).toHaveBeenCalledWith(jasmine.stringMatching('"type":"start_processing"'));
		});
	});

	describe('Tratamento de Erros', () => {
		beforeEach(() => {
			service.connect();
		});

		it('deve logar erro quando WebSocket falha', () => {
			spyOn(console, 'error');
			const testError = new Event('error');

			mockWebSocket.simulateError(testError);

			expect(console.error).toHaveBeenCalledWith('WebSocket erro:', testError);
		});

		it('deve logar quando conexão é fechada', () => {
			spyOn(console, 'log');

			mockWebSocket.close();

			expect(console.log).toHaveBeenCalledWith('WebSocket conexão fechada');
		});
	});

	describe('Estados de Processamento', () => {
		beforeEach(() => {
			service.connect();
		});

		it('deve retornar undefined para cliente sem estado', () => {
			const state = service.getProcessingState('nonexistent-client');
			expect(state).toBeUndefined();
		});

		it('deve atualizar timestamp ao receber notificação', () => {
			const beforeTime = new Date();

			mockWebSocket.simulateMessage({
				type: 'processing_started',
				clientId: 'client-time',
				data: {
					clientId: 'client-time',
					isProcessing: true,
					statusMessage: 'Iniciando',
					progressPercentage: 0,
					canViewReport: false,
					timestamp: new Date('2023-01-01'),
				},
			});

			const state = service.getProcessingState('client-time');
			expect(state!.timestamp.getTime()).toBeGreaterThanOrEqual(beforeTime.getTime());
		});

		it('deve manter múltiplos estados de clientes simultaneamente', () => {
			const client1 = 'client-1';
			const client2 = 'client-2';

			mockWebSocket.simulateMessage({
				type: 'processing_started',
				clientId: client1,
				data: {
					clientId: client1,
					isProcessing: true,
					statusMessage: 'Cliente 1 processando',
					progressPercentage: 25,
					canViewReport: false,
					timestamp: new Date(),
				},
			});

			mockWebSocket.simulateMessage({
				type: 'processing_progress',
				clientId: client2,
				data: {
					clientId: client2,
					isProcessing: true,
					statusMessage: 'Cliente 2 processando',
					progressPercentage: 75,
					canViewReport: false,
					timestamp: new Date(),
				},
			});

			const state1 = service.getProcessingState(client1);
			const state2 = service.getProcessingState(client2);

			expect(state1!.progressPercentage).toBe(25);
			expect(state2!.progressPercentage).toBe(75);
			expect(state1!.statusMessage).toBe('Cliente 1 processando');
			expect(state2!.statusMessage).toBe('Cliente 2 processando');
		});
	});

	describe('Tipos de Notificação', () => {
		beforeEach(() => {
			service.connect();
		});

		const testCases = ['processing_started', 'processing_progress', 'processing_complete', 'processing_error'];

		testCases.forEach(notificationType => {
			it(`deve processar notificação do tipo ${notificationType}`, () => {
				const notification = {
					type: notificationType,
					clientId: 'test-client',
					data: {
						clientId: 'test-client',
						isProcessing: notificationType !== 'processing_complete',
						statusMessage: `Status para ${notificationType}`,
						progressPercentage: notificationType === 'processing_complete' ? 100 : 50,
						canViewReport: notificationType === 'processing_complete',
						timestamp: new Date(),
					},
				};

				mockWebSocket.simulateMessage(notification);

				const state = service.getProcessingState('test-client');
				expect(state).toBeDefined();
				expect(state!.statusMessage).toBe(`Status para ${notificationType}`);
			});
		});
	});
});
