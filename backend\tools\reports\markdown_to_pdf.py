"""
Conversor de Markdown para PDF com formatação profissional
"""
import logging
from datetime import datetime
from typing import Dict, Any, Optional
import markdown
from weasyprint import HTML, CSS
from weasyprint.text.fonts import FontConfiguration
from io import BytesIO
import base64

logger = logging.getLogger(__name__)


class MarkdownToPDFConverter:
    """Converte relatórios Markdown para PDF com formatação profissional"""
    
    def __init__(self):
        """Inicializa o conversor com configurações de fonte"""
        self.font_config = FontConfiguration()
        
    def convert_to_pdf(self, markdown_content: str, metadata: Dict[str, Any]) -> bytes:
        """
        Converte Markdown para PDF
        
        Args:
            markdown_content: Conteúdo em Markdown
            metadata: Metadados do relatório
            
        Returns:
            bytes: Conteúdo do PDF
        """
        try:
            logger.info(f"🔄 Convertendo Markdown para PDF - {metadata.get('client_name', 'Cliente')}")
            
            # Converter Markdown para HTML
            html_content = self._markdown_to_html(markdown_content, metadata)
            
            # Gerar CSS customizado
            css_content = self._generate_css()
            
            # Converter HTML para PDF
            pdf_bytes = self._html_to_pdf(html_content, css_content)
            
            logger.info(f"✅ PDF gerado com sucesso - {len(pdf_bytes)} bytes")
            
            return pdf_bytes
            
        except Exception as e:
            logger.error(f"❌ Erro ao converter Markdown para PDF: {str(e)}")
            raise
    
    def _markdown_to_html(self, markdown_content: str, metadata: Dict[str, Any]) -> str:
        """Converte Markdown para HTML com template"""
        
        # Pré-processar o Markdown para garantir formatação correta
        # Adicionar quebras de linha duplas onde necessário
        lines = markdown_content.split('\n')
        processed_lines = []
        
        for i, line in enumerate(lines):
            processed_lines.append(line)
            
            # Adicionar linha em branco após listas se próxima linha não for vazia
            if i < len(lines) - 1:
                current_is_list = line.strip().startswith(('-', '*', '+')) or (len(line) > 0 and line[0].isdigit() and '. ' in line[:4])
                next_is_list = lines[i+1].strip().startswith(('-', '*', '+')) or (len(lines[i+1]) > 0 and lines[i+1][0].isdigit() and '. ' in lines[i+1][:4])
                next_is_header = lines[i+1].strip().startswith('#')
                
                if current_is_list and not next_is_list and lines[i+1].strip() != '' and not next_is_header:
                    processed_lines.append('')
        
        processed_markdown = '\n'.join(processed_lines)
        
        # Extensões do Markdown para suporte avançado
        md = markdown.Markdown(extensions=[
            'markdown.extensions.tables',
            'markdown.extensions.fenced_code',
            'markdown.extensions.codehilite',
            'markdown.extensions.toc',
            'markdown.extensions.sane_lists',
            'markdown.extensions.attr_list',
            'markdown.extensions.extra',
            'markdown.extensions.meta'
        ])
        
        # Converter Markdown para HTML
        content_html = md.convert(processed_markdown)
        
        # Formatar data em português
        date_str = self._format_date_portuguese(datetime.now())
        
        # Criar template HTML completo
        html_template = f"""
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <title>Relatório Executivo - {metadata.get('client_name', 'Cliente')}</title>
</head>
<body>
    <!-- Cabeçalho -->
    <header>
        <div class="header-content">
            <div class="logo-section">
                <h1 class="company-name">SCOPE AI</h1>
                <p class="tagline">Inteligência Artificial para Análise Empresarial</p>
            </div>
            <div class="date-section">
                <p class="generation-date">{date_str}</p>
                <p class="report-type">Relatório Executivo</p>
            </div>
        </div>
    </header>
    
    <!-- Linha divisória -->
    <div class="divider"></div>
    
    <!-- Conteúdo principal -->
    <main>
        {content_html}
    </main>
    
</body>
</html>
"""
        
        return html_template
    
    def _generate_css(self) -> str:
        """Gera CSS profissional para o PDF"""
        
        css = """
/* Reset e configurações base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Configuração da página */
@page {
    size: A4;
    margin: 2cm 1.5cm 2.5cm 1.5cm;
    
    @bottom-center {
        content: "Página " counter(page);
        font-size: 9pt;
        color: #666;
    }
}

/* Fonte principal */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: 11pt;
    line-height: 1.6;
    color: #333;
    background: white;
    word-wrap: break-word;
    word-break: break-word;
}

/* Container principal */
.container {
    max-width: 100%;
    margin: 0 auto;
}

main {
    width: 100%;
}

/* Cabeçalho */
header {
    margin-bottom: 20px;
    padding-bottom: 15px;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo-section {
    flex: 1;
}

.company-name {
    font-size: 28pt;
    font-weight: bold;
    color: #1a73e8;
    margin-bottom: 5px;
}

.tagline {
    font-size: 10pt;
    color: #666;
    font-style: italic;
}

.date-section {
    text-align: right;
}

.generation-date {
    font-size: 10pt;
    color: #666;
}

.report-type {
    font-size: 12pt;
    font-weight: bold;
    color: #333;
    margin-top: 5px;
}

/* Linha divisória */
.divider {
    height: 2px;
    background: #1a73e8;
    margin-bottom: 20px;
}

/* Títulos */
h1 {
    font-size: 20pt;
    font-weight: bold;
    color: #1a73e8;
    margin: 30px 0 20px 0;
    page-break-after: avoid;
    page-break-inside: avoid;
    border-bottom: 2px solid #e0e0e0;
    padding-bottom: 10px;
    line-height: 1.3;
}

h2 {
    font-size: 16pt;
    font-weight: bold;
    color: #1a73e8;
    margin: 25px 0 15px 0;
    page-break-after: avoid;
    page-break-inside: avoid;
    line-height: 1.3;
}

h3 {
    font-size: 13pt;
    font-weight: bold;
    color: #333;
    margin: 20px 0 10px 0;
    page-break-after: avoid;
    page-break-inside: avoid;
    line-height: 1.3;
}

/* Parágrafos */
p {
    margin-bottom: 12px;
    line-height: 1.6;
    text-align: left;
    orphans: 2;
    widows: 2;
}

/* Listas */
ul, ol {
    margin: 12px 0 12px 25px;
    padding-left: 0;
}

li {
    margin-bottom: 6px;
    line-height: 1.5;
}

ul li {
    list-style-type: disc;
}

ol li {
    list-style-type: decimal;
}

/* Tabelas */
table {
    width: 100%;
    border-collapse: collapse;
    margin: 15px 0;
    font-size: 10pt;
    page-break-inside: avoid;
}

th {
    background-color: #1a73e8;
    color: white;
    padding: 12px;
    text-align: left;
    font-weight: bold;
}

td {
    padding: 10px;
    border-bottom: 1px solid #e0e0e0;
}

tr:nth-child(even) {
    background-color: #f8f9fa;
}

/* Blocos de código */
code {
    background-color: #f5f5f5;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 9pt;
    white-space: pre-wrap;
}

pre {
    background-color: #f5f5f5;
    padding: 15px;
    border-radius: 5px;
    overflow-x: auto;
    margin: 15px 0;
    white-space: pre-wrap;
    word-wrap: break-word;
}

pre code {
    background-color: transparent;
    padding: 0;
    white-space: pre-wrap;
}

/* Citações */
blockquote {
    border-left: 4px solid #1a73e8;
    padding-left: 20px;
    margin: 20px 0;
    font-style: italic;
    color: #666;
}

/* Destaques */
strong, b {
    font-weight: bold;
    color: #1a1a1a;
}

/* Bullets */
ul li::marker {
    color: #1a73e8;
}

em {
    font-style: italic;
}

/* Links */
a {
    color: #1a73e8;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

/* Rodapé - removido para evitar problemas de formatação */

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.confidential {
    font-style: italic;
}

/* Controle de quebras de página */
.page-break {
    page-break-after: always;
}

/* Evitar quebras desnecessárias */
h1, h2, h3, h4, h5, h6 {
    page-break-after: avoid;
    page-break-inside: avoid;
}

table, figure, blockquote, pre, ul, ol {
    page-break-inside: avoid;
}

/* Garantir que parágrafos não fiquem órfãos */
p + h1, p + h2, p + h3 {
    page-break-before: auto;
}

/* Seções especiais */
.highlight-box {
    background-color: #e8f0fe;
    border: 1px solid #1a73e8;
    border-radius: 5px;
    padding: 15px;
    margin: 20px 0;
}

.warning-box {
    background-color: #fef7e0;
    border: 1px solid #f9ab00;
    border-radius: 5px;
    padding: 15px;
    margin: 20px 0;
}

.success-box {
    background-color: #e6f4ea;
    border: 1px solid #34a853;
    border-radius: 5px;
    padding: 15px;
    margin: 20px 0;
}

/* Métricas e KPIs */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin: 20px 0;
}

.metric-card {
    background-color: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    padding: 15px;
    text-align: center;
}

.metric-value {
    font-size: 24pt;
    font-weight: bold;
    color: #1a73e8;
    margin: 10px 0;
}

.metric-label {
    font-size: 10pt;
    color: #666;
}

/* Formatação especial para listas dentro de parágrafos */
p + ul, p + ol {
    margin-top: 8px;
}

/* Garantir quebra de texto em todos elementos */
* {
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
}

/* Melhor espaçamento entre seções */
h1 + p, h2 + p, h3 + p {
    margin-top: 12px;
}

/* Espaçamento após listas */
ul + p, ol + p, ul + h1, ol + h1, ul + h2, ol + h2, ul + h3, ol + h3 {
    margin-top: 16px;
}

/* Impressão */
@media print {
    body {
        font-size: 10pt;
    }
    
    .page-break {
        page-break-after: always;
    }
    
    a {
        color: #333;
    }
}
"""
        
        return css
    
    def _html_to_pdf(self, html_content: str, css_content: str) -> bytes:
        """Converte HTML para PDF usando WeasyPrint"""
        
        # Criar CSS object
        css = CSS(string=css_content, font_config=self.font_config)
        
        # Converter HTML para PDF
        html = HTML(string=html_content)
        pdf_document = html.render(stylesheets=[css], font_config=self.font_config)
        
        # Escrever PDF em bytes
        pdf_bytes = BytesIO()
        pdf_document.write_pdf(pdf_bytes)
        
        return pdf_bytes.getvalue()
    
    def _format_date_portuguese(self, date: datetime) -> str:
        """Formata data em português"""
        months = {
            1: "Janeiro", 2: "Fevereiro", 3: "Março", 4: "Abril",
            5: "Maio", 6: "Junho", 7: "Julho", 8: "Agosto",
            9: "Setembro", 10: "Outubro", 11: "Novembro", 12: "Dezembro"
        }
        
        return f"{date.day} de {months[date.month]} de {date.year}"