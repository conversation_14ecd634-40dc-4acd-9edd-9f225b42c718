"""
ReportManager - Orquestrador do Sistema de Reports (Refatorado)

Módulo refatorado seguindo Single Responsibility Principle.
Responsabilidade única: Orquestrar os 5 serviços especializados para geração de relatórios.

Serviços especializados utilizados:
- ReportDataCollector: Coleta de dados
- ReportContentGenerator: Geração de conteúdo
- ReportStorageService: Persistência
- ReportVersionManager: Versionamento
- ReportNotificationService: Notificações
"""

import logging
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional, Literal
import time

# Importar serviços especializados
from .report_data_collector import ReportDataCollector
from .report_content_generator import ReportContentGenerator
from .report_storage_service import ReportStorageService
from .report_version_manager import ReportVersionManager
from .report_notification_service import ReportNotificationService, NotificationType

logger = logging.getLogger(__name__)


class ReportManager:
    """
    Orquestrador Principal do Sistema de Reports Estruturado.

    Responsabilidade única: Coordenar e orquestrar os 5 serviços especializados
    para gerar relatórios estruturados seguindo o padrão SRP.
    """

    def __init__(self):
        """Inicializa o orquestrador com os 5 serviços especializados."""
        # Inicializar serviços especializados
        self.data_collector = ReportDataCollector()
        self.content_generator = ReportContentGenerator()
        self.storage_service = ReportStorageService()
        self.version_manager = ReportVersionManager()
        self.notification_service = ReportNotificationService()

        # Configurações do orquestrador
        self.available_report_types = [
            "dossie_executivo",
            "analise_tecnica",
            "competitividade",
            "mercado_oportunidades",
            "crescimento_digital",
            "funding_investimentos",
            "modelo_negocio",
            "dashboard_executivo"
        ]

        self.sector_templates = {
            "tecnologia": "tech_template",
            "financeiro": "fintech_template",
            "saude": "healthtech_template",
            "educacao": "edtech_template",
            "varejo": "retail_template",
            "servicos": "services_template",
            "industria": "industrial_template",
            "default": "generic_template"
        }

    async def generate_structured_report(
        self,
        client_id: str,
        report_type: Literal[
            "dossie_executivo",
            "analise_tecnica",
            "competitividade",
            "mercado_oportunidades",
            "crescimento_digital",
            "funding_investimentos",
            "modelo_negocio",
            "dashboard_executivo"
        ],
        sector: Optional[str] = None,
        template_customizations: Optional[Dict[str, Any]] = None,
        generate_pdf: bool = True,
        send_notifications: bool = True
    ) -> Dict[str, Any]:
        """
        Orquestra a geração de um relatório estruturado usando os 5 serviços especializados.

        Args:
            client_id: ID do cliente no MongoDB
            report_type: Tipo de report a ser gerado
            sector: Setor da empresa (para personalização de template)
            template_customizations: Personalizações específicas do template
            generate_pdf: Se deve gerar PDF executivo
            send_notifications: Se deve enviar notificações

        Returns:
            Dict contendo o report estruturado e metadados

        Raises:
            Exception: Se houver erro na geração do report
        """
        start_time = time.time()

        try:
            logger.info(
                f"🎯 ORQUESTRADOR: Iniciando geração de report {report_type} para cliente {client_id}")

            # 📊 FASE 1: COLETA DE DADOS
            logger.info("📊 FASE 1: Coletando dados...")
            client_data = await self.data_collector.get_client_data(client_id)
            if not client_data:
                raise Exception(f"Cliente {client_id} não encontrado")

            # Detectar setor se não fornecido
            if not sector:
                sector = client_data.get(
                    "sector") or client_data.get("setor") or "default"

            # Coletar dados específicos do relatório
            raw_data = await self.data_collector.collect_report_data(client_data, report_type)

            # 🎨 FASE 2: GERAÇÃO DE CONTEÚDO
            logger.info("🎨 FASE 2: Gerando conteúdo estruturado...")
            template_name = self.sector_templates.get(
                (sector or "default").lower(), "generic_template")

            structured_content = await self.content_generator.generate_report_content(
                raw_data, report_type, template_name, template_customizations
            )

            # ⏱️ FASE 3: VERSIONAMENTO
            logger.info("⏱️ FASE 3: Aplicando versionamento...")
            # Buscar versão anterior para comparação
            previous_version = await self.storage_service.get_latest_report(client_id, report_type)

            version_info = await self.version_manager.apply_versioning(
                client_id, report_type, structured_content, previous_version
            )

            # 💾 FASE 4: PERSISTÊNCIA
            logger.info("💾 FASE 4: Salvando relatório...")
            # Adicionar tempo de processamento ao conteúdo
            processing_time = time.time() - start_time
            structured_content["processing_time"] = processing_time

            saved_report = await self.storage_service.save_structured_report(
                client_id, report_type, structured_content, version_info
            )

            # 🔔 FASE 5: NOTIFICAÇÕES
            if send_notifications:
                logger.info("🔔 FASE 5: Enviando notificações...")
                await self.notification_service.send_report_notifications(
                    client_id, report_type, saved_report, NotificationType.REPORT_GENERATED
                )

                # Enviar alertas se necessário
                quality_score = structured_content.get("data_quality_score", 0)
                if quality_score < 70:
                    await self.notification_service.send_quality_alert(
                        client_id, report_type, quality_score
                    )

                if version_info.get("is_major_change", False):
                    await self.notification_service.send_version_alert(
                        client_id, report_type, version_info
                    )

            # 📄 FASE 6: PDF (Opcional)
            pdf_info = None
            if generate_pdf:
                logger.info("📄 FASE 6: Gerando PDF...")
                pdf_info = await self._generate_pdf_report(saved_report)

            # 🎯 RESPOSTA FINAL
            total_processing_time = time.time() - start_time

            response = {
                "report_id": saved_report["_id"],
                "report_type": report_type,
                "client_id": client_id,
                "sector": sector,
                "template_used": template_name,
                "version": version_info["version"],
                "status": "completed",
                "generated_at": datetime.now(timezone.utc).isoformat(),
                "data_quality_score": structured_content.get("data_quality_score", 0),
                "processing_time": total_processing_time,
                "pdf_available": pdf_info is not None,
                "pdf_url": pdf_info.get("url") if pdf_info else None,
                "dashboard_url": f"/dashboard/reports/{saved_report['_id']}",
                "summary": structured_content.get("executive_summary", ""),
                "key_insights": structured_content.get("key_insights", []),
                "recommendations": structured_content.get("recommendations", []),
                "orchestration_metadata": {
                    "services_used": 5,
                    "phases_completed": 6 if generate_pdf else 5,
                    "notifications_sent": send_notifications,
                    "version_change_type": version_info.get("change_type", "unknown"),
                    "data_collection_time": raw_data.get("collection_timestamp"),
                    "content_size": len(str(structured_content)),
                    "srp_compliant": True
                }
            }

            logger.info(
                f"✅ ORQUESTRADOR: Report {report_type} gerado com sucesso em {total_processing_time:.2f}s")
            return response

        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(
                f"❌ ORQUESTRADOR: Erro na geração do report {report_type}: {str(e)} (tempo: {processing_time:.2f}s)")

            # Tentar enviar notificação de erro
            if send_notifications:
                try:
                    await self.notification_service.send_report_notifications(
                        client_id, report_type, {"error": str(
                            e)}, NotificationType.REPORT_ERROR
                    )
                except:
                    pass  # Não falhar por causa de notificação

            raise Exception(f"Falha na geração do report: {str(e)}")

    async def get_report_by_id(self, report_id: str) -> Optional[Dict[str, Any]]:
        """
        Recupera relatório pelo ID usando o serviço de armazenamento.

        Args:
            report_id: ID do relatório

        Returns:
            Dados do relatório ou None se não encontrado
        """
        return await self.storage_service.get_report_by_id(report_id)

    async def get_client_reports(
        self,
        client_id: str,
        report_type: Optional[str] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        Recupera relatórios de um cliente usando o serviço de armazenamento.

        Args:
            client_id: ID do cliente
            report_type: Tipo específico de relatório (opcional)
            limit: Número máximo de relatórios

        Returns:
            Lista de relatórios do cliente
        """
        return await self.storage_service.get_client_reports(client_id, report_type, limit)

    async def get_latest_report(self, client_id: str, report_type: str) -> Optional[Dict[str, Any]]:
        """
        Recupera a versão mais recente de um relatório usando o serviço de armazenamento.

        Args:
            client_id: ID do cliente
            report_type: Tipo do relatório

        Returns:
            Relatório mais recente ou None se não encontrado
        """
        return await self.storage_service.get_latest_report(client_id, report_type)

    async def get_report_statistics(self, client_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Recupera estatísticas dos relatórios usando o serviço de armazenamento.

        Args:
            client_id: ID do cliente (opcional)

        Returns:
            Estatísticas dos relatórios
        """
        return await self.storage_service.get_report_statistics(client_id)

    async def update_notification_preferences(
        self,
        client_id: str,
        preferences: Dict[str, Any]
    ) -> bool:
        """
        Atualiza preferências de notificação usando o serviço de notificações.

        Args:
            client_id: ID do cliente
            preferences: Novas preferências

        Returns:
            True se atualização foi bem-sucedida
        """
        return await self.notification_service.update_notification_preferences(client_id, preferences)

    async def _generate_pdf_report(self, report: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Gera PDF do relatório (placeholder para integração futura).

        Args:
            report: Dados do relatório

        Returns:
            Informações do PDF gerado ou None se falhar
        """
        try:
            # Placeholder para integração com sistema de PDF
            logger.info(f"PDF gerado para relatório {report.get('_id')}")
            return {
                "status": "success",
                "url": f"/pdfs/{report.get('_id')}.pdf",
                "generated_at": datetime.now(timezone.utc).isoformat()
            }
        except Exception as e:
            logger.error(f"Erro na geração de PDF: {str(e)}")
            return None

    def get_available_report_types(self) -> List[str]:
        """Retorna lista de tipos de relatório disponíveis."""
        return self.available_report_types.copy()

    def get_available_sectors(self) -> List[str]:
        """Retorna lista de setores com templates específicos."""
        return list(self.sector_templates.keys())

    async def health_check(self) -> Dict[str, Any]:
        """
        Verifica saúde do orquestrador e seus serviços.

        Returns:
            Status de saúde de todos os componentes
        """
        try:
            start_time = time.time()

            health_status = {
                "orchestrator": "healthy",
                "services": {},
                "checked_at": datetime.now(timezone.utc).isoformat(),
                "response_time": 0
            }

            # Verificar cada serviço (implementação básica)
            services = [
                ("data_collector", self.data_collector),
                ("content_generator", self.content_generator),
                ("storage_service", self.storage_service),
                ("version_manager", self.version_manager),
                ("notification_service", self.notification_service)
            ]

            for service_name, service_instance in services:
                try:
                    # Verificação básica - se instância existe e está configurada
                    if service_instance:
                        health_status["services"][service_name] = "healthy"
                    else:
                        health_status["services"][service_name] = "unhealthy"
                except Exception as e:
                    health_status["services"][service_name] = f"error: {str(e)}"

            health_status["response_time"] = time.time() - start_time

            # Determinar status geral
            unhealthy_services = [
                k for k, v in health_status["services"].items() if v != "healthy"]
            if unhealthy_services:
                health_status["orchestrator"] = f"degraded - unhealthy services: {unhealthy_services}"

            return health_status

        except Exception as e:
            return {
                "orchestrator": "error",
                "error": str(e),
                "checked_at": datetime.now(timezone.utc).isoformat()
            }

    # Métodos legados mantidos temporariamente para compatibilidade
    async def _get_client_data(self, client_id: str) -> Optional[Dict[str, Any]]:
        """
        Busca dados completos do cliente no MongoDB

        Args:
            client_id: ID do cliente

        Returns:
            Dados do cliente ou None se não encontrado
        """
        try:
            # Importação local para evitar dependência circular
            from clients.async_db import motor_clients_collection
            from bson import ObjectId
            
            client = await motor_clients_collection.find_one({"_id": ObjectId(client_id)})
            return client
        except Exception as e:
            logger.error(f"Erro ao buscar cliente {client_id}: {str(e)}")
            return None

    async def _collect_report_data(
        self,
        client_data: Dict[str, Any],
        report_type: str
    ) -> Dict[str, Any]:
        """
        Coleta dados específicos necessários para o tipo de report

        Args:
            client_data: Dados do cliente
            report_type: Tipo de report a ser gerado

        Returns:
            Dados coletados e organizados
        """
        try:
            # Extrair reports existentes do cliente
            reports = client_data.get("reports", [])

            # Mapear dados por tipo baseado no report solicitado
            data_mapping = {
                "dossie_executivo": [
                    "informacoes_gerais", "analise_swot_expandida", "stack_tecnico",
                    "dados_funding", "dados_presenca_digital", "dados_parcerias",
                    "dados_modelo_negocio", "dados_pricing", "dados_canais_reviews",
                    "dados_pesquisa_mercado", "dados_diagnostico_tecnico"
                ],
                "analise_tecnica": [
                    "stack_tecnico", "dados_diagnostico_tecnico"
                ],
                "competitividade": [
                    "concorrentes", "dados_presenca_digital", "dados_parcerias",
                    "dados_pesquisa_mercado"
                ],
                "mercado_oportunidades": [
                    "dados_pesquisa_mercado", "oportunidades_novos_produtos",
                    "melhorias_futuras"
                ],
                "crescimento_digital": [
                    "dados_presenca_digital", "dados_canais_reviews",
                    "dados_diagnostico_tecnico"
                ],
                "funding_investimentos": [
                    "dados_funding", "saude_financeira"
                ],
                "modelo_negocio": [
                    "dados_modelo_negocio", "dados_pricing", "dados_parcerias"
                ],
                "dashboard_executivo": [
                    # Todos os dados para dashboard consolidado
                    "informacoes_gerais", "analise_swot_expandida", "dados_funding",
                    "dados_presenca_digital", "dados_modelo_negocio", "dados_pricing",
                    "dados_diagnostico_tecnico"
                ]
            }

            # Obter campos necessários para o tipo de report
            required_fields = data_mapping.get(report_type, [])

            # Extrair dados dos reports
            collected_data = {}
            for report in reports:
                for field in required_fields:
                    if field in report:
                        collected_data[field] = report[field]

            # Adicionar metadados do cliente
            collected_data["client_metadata"] = {
                "name": client_data.get("name"),
                "sector": client_data.get("sector") or client_data.get("setor"),
                "site": client_data.get("site"),
                "city": client_data.get("city"),
                "state": client_data.get("state")
            }

            return collected_data

        except Exception as e:
            logger.error(
                f"Erro ao coletar dados para report {report_type}: {str(e)}")
            return {}

    async def _generate_report_content(
        self,
        raw_data: Dict[str, Any],
        report_type: str,
        template_name: str,
        customizations: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Gera conteúdo estruturado do report baseado nos dados e template

        Args:
            raw_data: Dados coletados
            report_type: Tipo do report
            template_name: Nome do template a ser usado
            customizations: Personalizações do template

        Returns:
            Report estruturado
        """
        try:
            # Estrutura base do report
            report_content = {
                "report_type": report_type,
                "template_used": template_name,
                "generated_at": datetime.now(timezone.utc).isoformat(),
                "data_quality_score": self._calculate_data_quality_score(raw_data),
                "processing_time": 0  # Será calculado ao final
            }

            # Gerar conteúdo específico baseado no tipo
            if report_type == "dossie_executivo":
                report_content.update(await self._generate_executive_dossier(raw_data))
            elif report_type == "analise_tecnica":
                report_content.update(await self._generate_technical_analysis(raw_data))
            elif report_type == "competitividade":
                report_content.update(await self._generate_competitive_analysis(raw_data))
            elif report_type == "mercado_oportunidades":
                report_content.update(await self._generate_market_opportunities(raw_data))
            elif report_type == "crescimento_digital":
                report_content.update(await self._generate_digital_growth(raw_data))
            elif report_type == "funding_investimentos":
                report_content.update(await self._generate_funding_analysis(raw_data))
            elif report_type == "modelo_negocio":
                report_content.update(await self._generate_business_model(raw_data))
            elif report_type == "dashboard_executivo":
                report_content.update(await self._generate_executive_dashboard(raw_data))
            else:
                raise Exception(f"Tipo de report não suportado: {report_type}")

            # Aplicar customizações se fornecidas
            if customizations:
                report_content = self._apply_template_customizations(
                    report_content, customizations)

            return report_content

        except Exception as e:
            logger.error(f"Erro ao gerar conteúdo do report: {str(e)}")
            raise

    def _calculate_data_quality_score(self, data: Dict[str, Any]) -> float:
        """
        Calcula score de qualidade dos dados (0-10)

        Args:
            data: Dados a serem avaliados

        Returns:
            Score de qualidade (0-10)
        """
        try:
            if not data:
                return 0.0

            # Critérios de qualidade
            completeness_score = min(
                len(data.keys()) / 10.0, 1.0) * 3  # Max 3 pontos

            # Verificar presença de dados críticos
            critical_fields = ["client_metadata", "informacoes_gerais"]
            critical_score = sum(
                2 for field in critical_fields if field in data)  # Max 4 pontos

            # Verificar qualidade individual dos campos
            quality_score = 0
            for key, value in data.items():
                if isinstance(value, dict) and value:
                    quality_score += 0.3
                elif isinstance(value, list) and value:
                    quality_score += 0.2
                elif value:
                    quality_score += 0.1

            quality_score = min(quality_score, 3.0)  # Max 3 pontos

            total_score = completeness_score + critical_score + quality_score
            return min(total_score, 10.0)

        except Exception:
            return 0.0

    async def _generate_executive_dossier(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Gera dossiê executivo consolidado

        Args:
            data: Dados coletados

        Returns:
            Dossiê executivo estruturado
        """
        try:
            dossier = {
                "executive_summary": self._create_executive_summary(data),
                "company_overview": data.get("informacoes_gerais", {}),
                "strategic_analysis": data.get("analise_swot_expandida", {}),
                "technical_stack": data.get("stack_tecnico", {}),
                "funding_overview": data.get("dados_funding", {}).get("resumo_funding", {}),
                "digital_presence": data.get("dados_presenca_digital", {}).get("resumo_presenca_digital", {}),
                "business_model": data.get("dados_modelo_negocio", {}).get("classificacao_modelo", {}),
                "pricing_strategy": data.get("dados_pricing", {}).get("resumo_pricing", {}),
                "market_position": data.get("dados_pesquisa_mercado", {}).get("insights_competitivos", {}),
                "technical_health": data.get("dados_diagnostico_tecnico", {}).get("relatorio_consolidado", {}),
                "key_insights": self._extract_key_insights(data),
                "recommendations": self._generate_recommendations(data),
                "risk_assessment": self._assess_risks(data),
                "growth_opportunities": self._identify_opportunities(data)
            }

            return dossier

        except Exception as e:
            logger.error(f"Erro ao gerar dossiê executivo: {str(e)}")
            return {}

    async def _generate_technical_analysis(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Gera análise técnica detalhada

        Args:
            data: Dados coletados

        Returns:
            Análise técnica estruturada
        """
        try:
            analysis = {
                "technical_overview": data.get("stack_tecnico", {}),
                "performance_metrics": data.get("dados_diagnostico_tecnico", {}).get("diagnostico_lighthouse", {}),
                "visual_analysis": data.get("dados_diagnostico_tecnico", {}).get("analise_visual", {}),
                "security_assessment": self._extract_security_data(data),
                "infrastructure_analysis": self._extract_infrastructure_data(data),
                "optimization_recommendations": self._generate_tech_recommendations(data),
                "technical_debt_assessment": self._assess_technical_debt(data),
                "scalability_analysis": self._analyze_scalability(data)
            }

            return analysis

        except Exception as e:
            logger.error(f"Erro ao gerar análise técnica: {str(e)}")
            return {}

    def _create_executive_summary(self, data: Dict[str, Any]) -> str:
        """
        Cria resumo executivo baseado nos dados disponíveis

        Args:
            data: Dados da empresa

        Returns:
            Resumo executivo em texto
        """
        try:
            client_name = data.get("client_metadata", {}
                                   ).get("name", "Empresa")
            sector = data.get("client_metadata", {}).get(
                "sector", "não identificado")

            # Extrair insights principais
            funding_status = "Não informado"
            if "dados_funding" in data:
                funding_data = data["dados_funding"]
                if "resumo_funding" in funding_data:
                    funding_status = funding_data["resumo_funding"].get(
                        "status_funding", "Não informado")

            digital_score = "Não avaliado"
            if "dados_presenca_digital" in data:
                digital_data = data["dados_presenca_digital"]
                if "resumo_presenca_digital" in digital_data:
                    digital_score = digital_data["resumo_presenca_digital"].get(
                        "score_presenca_digital", "Não avaliado")

            tech_score = "Não avaliado"
            if "dados_diagnostico_tecnico" in data:
                tech_data = data["dados_diagnostico_tecnico"]
                if "relatorio_consolidado" in tech_data:
                    tech_score = tech_data["relatorio_consolidado"].get(
                        "score_geral", "Não avaliado")

            summary = f"""
{client_name} é uma empresa do setor {sector} que apresenta o seguinte panorama:

SITUAÇÃO FINANCEIRA: {funding_status}
PRESENÇA DIGITAL: Score {digital_score}
SAÚDE TÉCNICA: Score {tech_score}

Esta análise consolidada oferece uma visão 360° da empresa, identificando
forças competitivas, oportunidades de crescimento e áreas de melhoria
estratégica para impulsionar o desenvolvimento do negócio.
            """.strip()

            return summary

        except Exception as e:
            logger.error(f"Erro ao criar resumo executivo: {str(e)}")
            return "Resumo executivo não disponível devido a dados insuficientes."

    def _extract_key_insights(self, data: Dict[str, Any]) -> List[str]:
        """
        Extrai insights-chave dos dados disponíveis

        Args:
            data: Dados da empresa

        Returns:
            Lista de insights principais
        """
        insights = []

        try:
            # Insights de SWOT
            if "analise_swot_expandida" in data:
                swot = data["analise_swot_expandida"]
                if "estrategias_recomendadas" in swot:
                    insights.append(
                        "Estratégias SWOT identificadas para maximizar forças e oportunidades")

            # Insights técnicos
            if "stack_tecnico" in data:
                stack = data["stack_tecnico"]
                if "analise_tecnica" in stack:
                    insights.append(
                        "Stack tecnológico mapeado com recomendações de otimização")

            # Insights de mercado
            if "dados_pesquisa_mercado" in data:
                mercado = data["dados_pesquisa_mercado"]
                if "insights_competitivos" in mercado:
                    insights.append(
                        "Posicionamento competitivo e oportunidades de mercado identificadas")

            # Insights de crescimento digital
            if "dados_presenca_digital" in data:
                digital = data["dados_presenca_digital"]
                if "recomendacoes_estrategicas" in digital:
                    insights.append(
                        "Estratégias de crescimento digital mapeadas")

            # Insights de modelo de negócio
            if "dados_modelo_negocio" in data:
                modelo = data["dados_modelo_negocio"]
                if "escalabilidade_modelo" in modelo:
                    insights.append(
                        "Análise de escalabilidade do modelo de negócio realizada")

            return insights if insights else ["Análise detalhada disponível nos dados coletados"]

        except Exception as e:
            logger.error(f"Erro ao extrair insights: {str(e)}")
            return ["Insights não disponíveis devido a erro no processamento"]

    def _generate_recommendations(self, data: Dict[str, Any]) -> List[str]:
        """
        Gera recomendações baseadas na análise dos dados

        Args:
            data: Dados da empresa

        Returns:
            Lista de recomendações
        """
        recommendations = []

        try:
            # Recomendações técnicas
            if "dados_diagnostico_tecnico" in data:
                tech_data = data["dados_diagnostico_tecnico"]
                if "relatorio_consolidado" in tech_data:
                    consolidado = tech_data["relatorio_consolidado"]
                    if "prioridades_melhorias" in consolidado:
                        tech_recommendations = consolidado["prioridades_melhorias"]
                        if isinstance(tech_recommendations, list):
                            recommendations.extend(
                                tech_recommendations[:3])  # Top 3

            # Recomendações de presença digital
            if "dados_presenca_digital" in data:
                digital_data = data["dados_presenca_digital"]
                if "recomendacoes_estrategicas" in digital_data:
                    digital_recs = digital_data["recomendacoes_estrategicas"]
                    if "prioridade_alta" in digital_recs:
                        alta_prioridade = digital_recs["prioridade_alta"]
                        if isinstance(alta_prioridade, list):
                            recommendations.extend(
                                alta_prioridade[:2])  # Top 2

            # Recomendações de SWOT
            if "analise_swot_expandida" in data:
                swot_data = data["analise_swot_expandida"]
                if "prioridades_acao" in swot_data:
                    acoes = swot_data["prioridades_acao"]
                    if isinstance(acoes, dict):
                        for priority, actions in acoes.items():
                            if isinstance(actions, list) and actions:
                                recommendations.append(
                                    f"{priority}: {actions[0]}")
                                break

            return recommendations if recommendations else [
                "Recomendações específicas serão geradas com mais dados disponíveis"
            ]

        except Exception as e:
            logger.error(f"Erro ao gerar recomendações: {str(e)}")
            return ["Recomendações não disponíveis devido a erro no processamento"]

    def _assess_risks(self, data: Dict[str, Any]) -> List[str]:
        """
        Avalia riscos baseado nos dados disponíveis

        Args:
            data: Dados da empresa

        Returns:
            Lista de riscos identificados
        """
        risks = []

        try:
            # Riscos técnicos
            if "dados_diagnostico_tecnico" in data:
                tech_data = data["dados_diagnostico_tecnico"]
                if "diagnostico_lighthouse" in tech_data:
                    lighthouse = tech_data["diagnostico_lighthouse"]

                    # Verificar scores baixos
                    performance_score = lighthouse.get(
                        "performance", {}).get("score", 100)
                    if isinstance(performance_score, (int, float)) and performance_score < 50:
                        risks.append(
                            "Performance técnica abaixo do ideal pode impactar experiência do usuário")

                    security_score = lighthouse.get(
                        "best_practices", {}).get("score", 100)
                    if isinstance(security_score, (int, float)) and security_score < 70:
                        risks.append(
                            "Práticas de segurança podem ser melhoradas")

            # Riscos de modelo de negócio
            if "dados_modelo_negocio" in data:
                modelo_data = data["dados_modelo_negocio"]
                if "riscos_modelo" in modelo_data:
                    riscos_modelo = modelo_data["riscos_modelo"]
                    if "vulnerabilidades" in riscos_modelo:
                        vulnerabilidades = riscos_modelo["vulnerabilidades"]
                        if isinstance(vulnerabilidades, list):
                            risks.extend(vulnerabilidades[:2])  # Top 2 riscos

            # Riscos competitivos
            if "dados_pesquisa_mercado" in data:
                mercado_data = data["dados_pesquisa_mercado"]
                if "pesquisa_mercado" in mercado_data:
                    pesquisa = mercado_data["pesquisa_mercado"]
                    if "barreiras_entrada" in pesquisa:
                        barreiras = pesquisa["barreiras_entrada"]
                        if isinstance(barreiras, list) and len(barreiras) > 3:
                            risks.append(
                                "Múltiplas barreiras de entrada podem dificultar expansão")

            return risks if risks else [
                "Análise de riscos será aprofundada com dados adicionais"
            ]

        except Exception as e:
            logger.error(f"Erro ao avaliar riscos: {str(e)}")
            return ["Avaliação de riscos não disponível devido a erro no processamento"]

    def _identify_opportunities(self, data: Dict[str, Any]) -> List[str]:
        """
        Identifica oportunidades de crescimento

        Args:
            data: Dados da empresa

        Returns:
            Lista de oportunidades identificadas
        """
        opportunities = []

        try:
            # Oportunidades de mercado
            if "dados_pesquisa_mercado" in data:
                mercado_data = data["dados_pesquisa_mercado"]
                if "analise_produtos_servicos" in mercado_data:
                    produtos = mercado_data["analise_produtos_servicos"]
                    if "gaps_mercado" in produtos:
                        gaps = produtos["gaps_mercado"]
                        if isinstance(gaps, list):
                            opportunities.extend(gaps[:2])  # Top 2 gaps

            # Oportunidades de parcerias
            if "dados_parcerias" in data:
                parcerias_data = data["dados_parcerias"]
                if "analise_competitiva_parcerias" in parcerias_data:
                    analise = parcerias_data["analise_competitiva_parcerias"]
                    if "oportunidades_parcerias" in analise:
                        oportunidades_parceria = analise["oportunidades_parcerias"]
                        if isinstance(oportunidades_parceria, list):
                            opportunities.extend(
                                oportunidades_parceria[:2])  # Top 2

            # Oportunidades digitais
            if "dados_presenca_digital" in data:
                digital_data = data["dados_presenca_digital"]
                if "competitividade_digital" in digital_data:
                    competitividade = digital_data["competitividade_digital"]
                    if "oportunidades_melhoria" in competitividade:
                        oportunidades_digital = competitividade["oportunidades_melhoria"]
                        if isinstance(oportunidades_digital, list):
                            opportunities.extend(
                                oportunidades_digital[:2])  # Top 2

            return opportunities if opportunities else [
                "Oportunidades específicas serão mapeadas com análise mais detalhada"
            ]

        except Exception as e:
            logger.error(f"Erro ao identificar oportunidades: {str(e)}")
            return ["Identificação de oportunidades não disponível devido a erro no processamento"]

    async def _apply_versioning(
        self,
        client_id: str,
        report_type: str,
        report_content: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Aplica versionamento ao report

        Args:
            client_id: ID do cliente
            report_type: Tipo do report
            report_content: Conteúdo do report

        Returns:
            Informações de versionamento
        """
        try:
            # Buscar versões existentes - ASYNC
            from clients.async_db import motor_clients_collection
            from bson import ObjectId
            
            existing_reports = await motor_clients_collection.find_one(
                {"_id": ObjectId(client_id)},
                {"reports": 1}
            )

            version_number = 1
            if existing_reports and "reports" in existing_reports:
                # Contar versões existentes deste tipo de report
                same_type_reports = [
                    r for r in existing_reports["reports"]
                    if r.get("report_type") == report_type
                ]
                version_number = len(same_type_reports) + 1

            version_info = {
                "version": f"v{version_number}.0",
                "version_number": version_number,
                "created_at": datetime.now(timezone.utc).isoformat(),
                "changes_from_previous": self._detect_changes(client_id, report_type, report_content),
                "checksum": self._calculate_checksum(report_content)
            }

            return version_info

        except Exception as e:
            logger.error(f"Erro ao aplicar versionamento: {str(e)}")
            return {
                "version": "v1.0",
                "version_number": 1,
                "created_at": datetime.now(timezone.utc).isoformat(),
                "changes_from_previous": [],
                "checksum": ""
            }

    async def _save_structured_report(
        self,
        client_id: str,
        report_type: str,
        report_content: Dict[str, Any],
        version_info: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Salva report estruturado no MongoDB

        Args:
            client_id: ID do cliente
            report_type: Tipo do report
            report_content: Conteúdo estruturado
            version_info: Informações de versionamento

        Returns:
            Report salvo com _id
        """
        try:
            report_document = {
                "report_type": report_type,
                "version_info": version_info,
                "content": report_content,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc),
                "status": "completed",
                "client_id": ObjectId(client_id)
            }

            # Salvar na collection dedicada de reports (será criada se não existir)
            from clients.db import clients_collection
            db = clients_collection.database
            reports_collection = db.structured_reports

            result = reports_collection.insert_one(report_document)
            report_document["_id"] = result.inserted_id

            # Também adicionar referência no cliente - ASYNC
            from clients.async_db import motor_clients_collection
            from bson import ObjectId
            
            await motor_clients_collection.update_one(
                {"_id": ObjectId(client_id)},
                {
                    "$push": {
                        "structured_reports": {
                            "report_id": result.inserted_id,
                            "report_type": report_type,
                            "version": version_info["version"],
                            "created_at": datetime.now(timezone.utc)
                        }
                    }
                }
            )

            logger.info(
                f"Report {report_type} salvo com ID {result.inserted_id}")
            return report_document

        except Exception as e:
            logger.error(f"Erro ao salvar report estruturado: {str(e)}")
            raise

    def _detect_changes(self, client_id: str, report_type: str, current_content: Dict[str, Any]) -> List[str]:
        """
        Detecta mudanças em relação à versão anterior

        Args:
            client_id: ID do cliente
            report_type: Tipo do report
            current_content: Conteúdo atual

        Returns:
            Lista de mudanças detectadas
        """
        try:
            # Por simplicidade, retornar lista vazia por enquanto
            # Em implementação futura, comparar com versão anterior
            return ["Primeira versão do report"]

        except Exception as e:
            logger.error(f"Erro ao detectar mudanças: {str(e)}")
            return []

    def _calculate_checksum(self, content: Dict[str, Any]) -> str:
        """
        Calcula checksum do conteúdo para controle de integridade

        Args:
            content: Conteúdo do report

        Returns:
            Checksum em string
        """
        try:
            import hashlib
            content_str = json.dumps(content, sort_keys=True, default=str)
            return hashlib.md5(content_str.encode()).hexdigest()
        except Exception:
            return ""

    # Métodos auxiliares para implementação futura
    async def _generate_competitive_analysis(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Gera análise competitiva (implementação futura)"""
        return {"message": "Análise competitiva em desenvolvimento"}

    async def _generate_market_opportunities(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Gera análise de oportunidades de mercado (implementação futura)"""
        return {"message": "Análise de mercado em desenvolvimento"}

    async def _generate_digital_growth(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Gera análise de crescimento digital (implementação futura)"""
        return {"message": "Análise de crescimento digital em desenvolvimento"}

    async def _generate_funding_analysis(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Gera análise de funding (implementação futura)"""
        return {"message": "Análise de funding em desenvolvimento"}

    async def _generate_business_model(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Gera análise de modelo de negócio (implementação futura)"""
        return {"message": "Análise de modelo de negócio em desenvolvimento"}

    async def _generate_executive_dashboard(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Gera dashboard executivo (implementação futura)"""
        return {"message": "Dashboard executivo em desenvolvimento"}

    def _extract_security_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Extrai dados de segurança (implementação futura)"""
        return {"message": "Análise de segurança em desenvolvimento"}

    def _extract_infrastructure_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Extrai dados de infraestrutura (implementação futura)"""
        return {"message": "Análise de infraestrutura em desenvolvimento"}

    def _generate_tech_recommendations(self, data: Dict[str, Any]) -> List[str]:
        """Gera recomendações técnicas (implementação futura)"""
        return ["Recomendações técnicas em desenvolvimento"]

    def _assess_technical_debt(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Avalia débito técnico (implementação futura)"""
        return {"message": "Análise de débito técnico em desenvolvimento"}

    def _analyze_scalability(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Analisa escalabilidade (implementação futura)"""
        return {"message": "Análise de escalabilidade em desenvolvimento"}

    def _apply_template_customizations(self, content: Dict[str, Any], customizations: Dict[str, Any]) -> Dict[str, Any]:
        """Aplica customizações do template (implementação futura)"""
        return content

    async def _generate_pdf_report(self, report: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Gera PDF do report (implementação futura)"""
        return None

    async def _send_report_notifications(self, client_id: str, report_type: str, report: Dict[str, Any]):
        """Envia notificações (implementação futura)"""
        pass
