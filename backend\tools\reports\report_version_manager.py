"""
ReportVersionManager - Responsável pelo Controle de Versões de Relatórios

Módulo especializado em gerenciar versionamento e controle de mudanças
em relatórios, seguindo o Single Responsibility Principle.
"""

import logging
import hashlib
import json
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)


class ReportVersionManager:
    """
    Responsável por gerenciar versões e detectar mudanças em relatórios.

    Única responsabilidade: Controle de versionamento e detecção de mudanças.
    """

    def __init__(self):
        """Inicializa o gerenciador de versões."""
        self.version_strategy = "semantic"  # semantic, incremental, timestamp

    async def apply_versioning(
        self,
        client_id: str,
        report_type: str,
        report_content: Dict[str, Any],
        previous_version: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Aplica versionamento a um relatório baseado em mudanças detectadas.

        Args:
            client_id: ID do cliente
            report_type: Tipo do relatório
            report_content: Conteúdo atual do relatório
            previous_version: Versão anterior para comparação (opcional)

        Returns:
            Informações de versionamento incluindo versão e mudanças
        """
        try:
            logger.info(
                f"Aplicando versionamento para {report_type} do cliente {client_id}")

            # Calcular checksum do conteúdo atual
            current_checksum = self._calculate_checksum(report_content)

            # Determinar versão
            if not previous_version:
                # Primeira versão
                version_number = "1.0.0"
                is_major_change = True
                changes_detected = ["Initial version"]
            else:
                # Detectar mudanças comparando com versão anterior
                changes_detected = self._detect_changes(
                    previous_version, report_content)

                # Determinar tipo de mudança e nova versão
                change_type = self._classify_changes(changes_detected)
                version_number = self._increment_version(
                    previous_version.get("version", "1.0.0"),
                    change_type
                )
                is_major_change = change_type == "major"

            # Construir informações de versionamento
            version_info = {
                "version": version_number,
                "previous_version": previous_version.get("version") if previous_version else None,
                "checksum": current_checksum,
                "previous_checksum": previous_version.get("checksum") if previous_version else None,
                "changes_detected": changes_detected,
                "change_type": self._classify_changes(changes_detected) if previous_version else "initial",
                "is_major_change": is_major_change,
                "created_at": datetime.now(timezone.utc).isoformat(),
                "metadata": {
                    "strategy_used": self.version_strategy,
                    "auto_generated": True,
                    "content_size": len(json.dumps(report_content, default=str)),
                    "sections_changed": len([c for c in changes_detected if "section" in c.lower()]),
                    "data_quality_change": self._calculate_quality_change(previous_version, report_content)
                }
            }

            logger.info(f"Versionamento aplicado: versão {version_number}")
            return version_info

        except Exception as e:
            logger.error(f"Erro no versionamento: {str(e)}")
            raise Exception(f"Falha no versionamento: {str(e)}")

    def _detect_changes(self, previous_version: Dict[str, Any], current_content: Dict[str, Any]) -> List[str]:
        """
        Detecta mudanças entre versões de relatório.

        Args:
            previous_version: Dados da versão anterior
            current_content: Conteúdo atual

        Returns:
            Lista de mudanças detectadas
        """
        try:
            changes = []

            if not previous_version:
                return ["Initial version"]

            previous_content = previous_version.get("content", {})

            # Comparar checksums
            previous_checksum = previous_version.get("checksum", "")
            current_checksum = self._calculate_checksum(current_content)

            if previous_checksum != current_checksum:
                changes.append("Content modified")

            # Comparar seções específicas
            self._compare_section(
                previous_content.get("executive_summary", ""),
                current_content.get("executive_summary", ""),
                "Executive summary",
                changes
            )

            self._compare_section(
                previous_content.get("key_insights", []),
                current_content.get("key_insights", []),
                "Key insights",
                changes
            )

            self._compare_section(
                previous_content.get("recommendations", []),
                current_content.get("recommendations", []),
                "Recommendations",
                changes
            )

            # Comparar score de qualidade
            prev_quality = previous_content.get("data_quality_score", 0)
            curr_quality = current_content.get("data_quality_score", 0)

            if abs(prev_quality - curr_quality) > 5:  # Mudança significativa: >5%
                changes.append(
                    f"Data quality score changed from {prev_quality:.1f}% to {curr_quality:.1f}%")

            # Comparar análise detalhada
            prev_analysis = previous_content.get("detailed_analysis", {})
            curr_analysis = current_content.get("detailed_analysis", {})

            if prev_analysis != curr_analysis:
                changes.append("Detailed analysis updated")

            return changes if changes else ["No significant changes detected"]

        except Exception as e:
            logger.error(f"Erro na detecção de mudanças: {str(e)}")
            return ["Error detecting changes"]

    def _compare_section(self, previous: Any, current: Any, section_name: str, changes: List[str]) -> None:
        """Compara uma seção específica e adiciona mudanças à lista."""
        try:
            if previous != current:
                if isinstance(previous, list) and isinstance(current, list):
                    if len(previous) != len(current):
                        changes.append(
                            f"{section_name} count changed from {len(previous)} to {len(current)}")
                    elif set(str(item) for item in previous) != set(str(item) for item in current):
                        changes.append(f"{section_name} content modified")
                else:
                    if str(previous) != str(current):
                        changes.append(f"{section_name} updated")
        except Exception:
            changes.append(f"{section_name} comparison failed")

    def _classify_changes(self, changes: List[str]) -> str:
        """
        Classifica o tipo de mudança baseado nas alterações detectadas.

        Args:
            changes: Lista de mudanças detectadas

        Returns:
            Tipo de mudança: major, minor, patch
        """
        try:
            # Mudanças que indicam versão major
            major_indicators = [
                "initial version", "content structure changed", "new report type",
                "major data source change", "template changed"
            ]

            # Mudanças que indicam versão minor
            minor_indicators = [
                "detailed analysis updated", "new insights added", "recommendations updated",
                "data quality score changed", "new section added"
            ]

            changes_text = " ".join(changes).lower()

            # Verificar indicadores major
            if any(indicator in changes_text for indicator in major_indicators):
                return "major"

            # Verificar indicadores minor
            if any(indicator in changes_text for indicator in minor_indicators):
                return "minor"

            # Por padrão, mudanças menores são patch
            return "patch"

        except Exception:
            return "patch"  # Padrão conservador

    def _increment_version(self, current_version: str, change_type: str) -> str:
        """
        Incrementa número da versão baseado no tipo de mudança.

        Args:
            current_version: Versão atual (formato semantic: x.y.z)
            change_type: Tipo de mudança (major, minor, patch)

        Returns:
            Nova versão incrementada
        """
        try:
            # Parsear versão atual
            parts = current_version.split(".")
            if len(parts) != 3:
                return "1.0.0"  # Fallback para formato inválido

            major, minor, patch = map(int, parts)

            # Incrementar baseado no tipo
            if change_type == "major":
                major += 1
                minor = 0
                patch = 0
            elif change_type == "minor":
                minor += 1
                patch = 0
            else:  # patch
                patch += 1

            return f"{major}.{minor}.{patch}"

        except Exception:
            # Fallback para incremento simples
            try:
                current_num = float(current_version)
                return str(current_num + 0.1)
            except:
                return "1.0.0"

    def _calculate_checksum(self, content: Dict[str, Any]) -> str:
        """
        Calcula checksum do conteúdo para detecção de mudanças.

        Args:
            content: Conteúdo do relatório

        Returns:
            Hash MD5 do conteúdo
        """
        try:
            # Serializar conteúdo de forma determinística
            content_str = json.dumps(content, sort_keys=True, default=str)

            # Calcular hash MD5
            return hashlib.md5(content_str.encode()).hexdigest()

        except Exception as e:
            logger.error(f"Erro no cálculo de checksum: {str(e)}")
            return ""

    def _calculate_quality_change(self, previous_version: Optional[Dict[str, Any]], current_content: Dict[str, Any]) -> float:
        """
        Calcula a mudança no score de qualidade entre versões.

        Args:
            previous_version: Versão anterior
            current_content: Conteúdo atual

        Returns:
            Diferença no score de qualidade (positivo = melhoria)
        """
        try:
            if not previous_version:
                return 0.0

            previous_content = previous_version.get("content", {})
            prev_quality = previous_content.get("data_quality_score", 0)
            curr_quality = current_content.get("data_quality_score", 0)

            return curr_quality - prev_quality

        except Exception:
            return 0.0

    def get_version_history(self, client_id: str, report_type: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Recupera histórico de versões de um relatório.

        Args:
            client_id: ID do cliente
            report_type: Tipo do relatório
            limit: Número máximo de versões

        Returns:
            Lista de versões históricas
        """
        # Esta funcionalidade seria implementada em conjunto com ReportStorageService
        return []

    def rollback_to_version(self, client_id: str, report_type: str, target_version: str) -> bool:
        """
        Reverte relatório para uma versão específica.

        Args:
            client_id: ID do cliente
            report_type: Tipo do relatório
            target_version: Versão alvo para rollback

        Returns:
            True se rollback foi bem-sucedido
        """
        # Esta funcionalidade seria implementada em conjunto com ReportStorageService
        logger.info(
            f"Rollback para versão {target_version} (funcionalidade a ser implementada)")
        return False
