{"version": 3, "sources": ["../../../../../../node_modules/ts-dedent/esm/index.js", "../../../../../../node_modules/marked/lib/marked.esm.js", "../../../../../../node_modules/mermaid/dist/chunks/mermaid.core/chunk-C3MQ5ANM.mjs"], "sourcesContent": ["export function dedent(templ) {\n  var values = [];\n  for (var _i = 1; _i < arguments.length; _i++) {\n    values[_i - 1] = arguments[_i];\n  }\n  var strings = Array.from(typeof templ === 'string' ? [templ] : templ);\n  strings[strings.length - 1] = strings[strings.length - 1].replace(/\\r?\\n([\\t ]*)$/, '');\n  var indentLengths = strings.reduce(function (arr, str) {\n    var matches = str.match(/\\n([\\t ]+|(?!\\s).)/g);\n    if (matches) {\n      return arr.concat(matches.map(function (match) {\n        var _a, _b;\n        return (_b = (_a = match.match(/[\\t ]/g)) === null || _a === void 0 ? void 0 : _a.length) !== null && _b !== void 0 ? _b : 0;\n      }));\n    }\n    return arr;\n  }, []);\n  if (indentLengths.length) {\n    var pattern_1 = new RegExp(\"\\n[\\t ]{\" + Math.min.apply(Math, indentLengths) + \"}\", 'g');\n    strings = strings.map(function (str) {\n      return str.replace(pattern_1, '\\n');\n    });\n  }\n  strings[0] = strings[0].replace(/^\\r?\\n/, '');\n  var string = strings[0];\n  values.forEach(function (value, i) {\n    var endentations = string.match(/(?:^|\\n)( *)$/);\n    var endentation = endentations ? endentations[1] : '';\n    var indentedValue = value;\n    if (typeof value === 'string' && value.includes('\\n')) {\n      indentedValue = String(value).split('\\n').map(function (str, i) {\n        return i === 0 ? str : \"\" + endentation + str;\n      }).join('\\n');\n    }\n    string += indentedValue + strings[i + 1];\n  });\n  return string;\n}\nexport default dedent;\n", "/**\n * marked v15.0.12 - a markdown parser\n * Copyright (c) 2011-2025, <PERSON>. (MIT Licensed)\n * https://github.com/markedjs/marked\n */\n\n/**\n * DO NOT EDIT THIS FILE\n * The code in this file is generated from files in ./src/\n */\n\n// src/defaults.ts\nfunction _getDefaults() {\n  return {\n    async: false,\n    breaks: false,\n    extensions: null,\n    gfm: true,\n    hooks: null,\n    pedantic: false,\n    renderer: null,\n    silent: false,\n    tokenizer: null,\n    walkTokens: null\n  };\n}\nvar _defaults = _getDefaults();\nfunction changeDefaults(newDefaults) {\n  _defaults = newDefaults;\n}\n\n// src/rules.ts\nvar noopTest = {\n  exec: () => null\n};\nfunction edit(regex, opt = \"\") {\n  let source = typeof regex === \"string\" ? regex : regex.source;\n  const obj = {\n    replace: (name, val) => {\n      let valSource = typeof val === \"string\" ? val : val.source;\n      valSource = valSource.replace(other.caret, \"$1\");\n      source = source.replace(name, valSource);\n      return obj;\n    },\n    getRegex: () => {\n      return new RegExp(source, opt);\n    }\n  };\n  return obj;\n}\nvar other = {\n  codeRemoveIndent: /^(?: {1,4}| {0,3}\\t)/gm,\n  outputLinkReplace: /\\\\([\\[\\]])/g,\n  indentCodeCompensation: /^(\\s+)(?:```)/,\n  beginningSpace: /^\\s+/,\n  endingHash: /#$/,\n  startingSpaceChar: /^ /,\n  endingSpaceChar: / $/,\n  nonSpaceChar: /[^ ]/,\n  newLineCharGlobal: /\\n/g,\n  tabCharGlobal: /\\t/g,\n  multipleSpaceGlobal: /\\s+/g,\n  blankLine: /^[ \\t]*$/,\n  doubleBlankLine: /\\n[ \\t]*\\n[ \\t]*$/,\n  blockquoteStart: /^ {0,3}>/,\n  blockquoteSetextReplace: /\\n {0,3}((?:=+|-+) *)(?=\\n|$)/g,\n  blockquoteSetextReplace2: /^ {0,3}>[ \\t]?/gm,\n  listReplaceTabs: /^\\t+/,\n  listReplaceNesting: /^ {1,4}(?=( {4})*[^ ])/g,\n  listIsTask: /^\\[[ xX]\\] /,\n  listReplaceTask: /^\\[[ xX]\\] +/,\n  anyLine: /\\n.*\\n/,\n  hrefBrackets: /^<(.*)>$/,\n  tableDelimiter: /[:|]/,\n  tableAlignChars: /^\\||\\| *$/g,\n  tableRowBlankLine: /\\n[ \\t]*$/,\n  tableAlignRight: /^ *-+: *$/,\n  tableAlignCenter: /^ *:-+: *$/,\n  tableAlignLeft: /^ *:-+ *$/,\n  startATag: /^<a /i,\n  endATag: /^<\\/a>/i,\n  startPreScriptTag: /^<(pre|code|kbd|script)(\\s|>)/i,\n  endPreScriptTag: /^<\\/(pre|code|kbd|script)(\\s|>)/i,\n  startAngleBracket: /^</,\n  endAngleBracket: />$/,\n  pedanticHrefTitle: /^([^'\"]*[^\\s])\\s+(['\"])(.*)\\2/,\n  unicodeAlphaNumeric: /[\\p{L}\\p{N}]/u,\n  escapeTest: /[&<>\"']/,\n  escapeReplace: /[&<>\"']/g,\n  escapeTestNoEncode: /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/,\n  escapeReplaceNoEncode: /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/g,\n  unescapeTest: /&(#(?:\\d+)|(?:#x[0-9A-Fa-f]+)|(?:\\w+));?/ig,\n  caret: /(^|[^\\[])\\^/g,\n  percentDecode: /%25/g,\n  findPipe: /\\|/g,\n  splitPipe: / \\|/,\n  slashPipe: /\\\\\\|/g,\n  carriageReturn: /\\r\\n|\\r/g,\n  spaceLine: /^ +$/gm,\n  notSpaceStart: /^\\S*/,\n  endingNewline: /\\n$/,\n  listItemRegex: bull => new RegExp(`^( {0,3}${bull})((?:[\t ][^\\\\n]*)?(?:\\\\n|$))`),\n  nextBulletRegex: indent => new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:[*+-]|\\\\d{1,9}[.)])((?:[ \t][^\\\\n]*)?(?:\\\\n|$))`),\n  hrRegex: indent => new RegExp(`^ {0,${Math.min(3, indent - 1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\\\* *){3,})(?:\\\\n+|$)`),\n  fencesBeginRegex: indent => new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:\\`\\`\\`|~~~)`),\n  headingBeginRegex: indent => new RegExp(`^ {0,${Math.min(3, indent - 1)}}#`),\n  htmlBeginRegex: indent => new RegExp(`^ {0,${Math.min(3, indent - 1)}}<(?:[a-z].*>|!--)`, \"i\")\n};\nvar newline = /^(?:[ \\t]*(?:\\n|$))+/;\nvar blockCode = /^((?: {4}| {0,3}\\t)[^\\n]+(?:\\n(?:[ \\t]*(?:\\n|$))*)?)+/;\nvar fences = /^ {0,3}(`{3,}(?=[^`\\n]*(?:\\n|$))|~{3,})([^\\n]*)(?:\\n|$)(?:|([\\s\\S]*?)(?:\\n|$))(?: {0,3}\\1[~`]* *(?=\\n|$)|$)/;\nvar hr = /^ {0,3}((?:-[\\t ]*){3,}|(?:_[ \\t]*){3,}|(?:\\*[ \\t]*){3,})(?:\\n+|$)/;\nvar heading = /^ {0,3}(#{1,6})(?=\\s|$)(.*)(?:\\n+|$)/;\nvar bullet = /(?:[*+-]|\\d{1,9}[.)])/;\nvar lheadingCore = /^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\\n(?!\\s*?\\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/;\nvar lheading = edit(lheadingCore).replace(/bull/g, bullet).replace(/blockCode/g, /(?: {4}| {0,3}\\t)/).replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g, / {0,3}>/).replace(/heading/g, / {0,3}#{1,6}/).replace(/html/g, / {0,3}<[^\\n>]+>\\n/).replace(/\\|table/g, \"\").getRegex();\nvar lheadingGfm = edit(lheadingCore).replace(/bull/g, bullet).replace(/blockCode/g, /(?: {4}| {0,3}\\t)/).replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g, / {0,3}>/).replace(/heading/g, / {0,3}#{1,6}/).replace(/html/g, / {0,3}<[^\\n>]+>\\n/).replace(/table/g, / {0,3}\\|?(?:[:\\- ]*\\|)+[\\:\\- ]*\\n/).getRegex();\nvar _paragraph = /^([^\\n]+(?:\\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\\n)[^\\n]+)*)/;\nvar blockText = /^[^\\n]+/;\nvar _blockLabel = /(?!\\s*\\])(?:\\\\.|[^\\[\\]\\\\])+/;\nvar def = edit(/^ {0,3}\\[(label)\\]: *(?:\\n[ \\t]*)?([^<\\s][^\\s]*|<.*?>)(?:(?: +(?:\\n[ \\t]*)?| *\\n[ \\t]*)(title))? *(?:\\n+|$)/).replace(\"label\", _blockLabel).replace(\"title\", /(?:\"(?:\\\\\"?|[^\"\\\\])*\"|'[^'\\n]*(?:\\n[^'\\n]+)*\\n?'|\\([^()]*\\))/).getRegex();\nvar list = edit(/^( {0,3}bull)([ \\t][^\\n]+?)?(?:\\n|$)/).replace(/bull/g, bullet).getRegex();\nvar _tag = \"address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul\";\nvar _comment = /<!--(?:-?>|[\\s\\S]*?(?:-->|$))/;\nvar html = edit(\"^ {0,3}(?:<(script|pre|style|textarea)[\\\\s>][\\\\s\\\\S]*?(?:</\\\\1>[^\\\\n]*\\\\n+|$)|comment[^\\\\n]*(\\\\n+|$)|<\\\\?[\\\\s\\\\S]*?(?:\\\\?>\\\\n*|$)|<![A-Z][\\\\s\\\\S]*?(?:>\\\\n*|$)|<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?(?:\\\\]\\\\]>\\\\n*|$)|</?(tag)(?: +|\\\\n|/?>)[\\\\s\\\\S]*?(?:(?:\\\\n[ \t]*)+\\\\n|$)|<(?!script|pre|style|textarea)([a-z][\\\\w-]*)(?:attribute)*? */?>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n[ \t]*)+\\\\n|$)|</(?!script|pre|style|textarea)[a-z][\\\\w-]*\\\\s*>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n[ \t]*)+\\\\n|$))\", \"i\").replace(\"comment\", _comment).replace(\"tag\", _tag).replace(\"attribute\", / +[a-zA-Z:_][\\w.:-]*(?: *= *\"[^\"\\n]*\"| *= *'[^'\\n]*'| *= *[^\\s\"'=<>`]+)?/).getRegex();\nvar paragraph = edit(_paragraph).replace(\"hr\", hr).replace(\"heading\", \" {0,3}#{1,6}(?:\\\\s|$)\").replace(\"|lheading\", \"\").replace(\"|table\", \"\").replace(\"blockquote\", \" {0,3}>\").replace(\"fences\", \" {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n\").replace(\"list\", \" {0,3}(?:[*+-]|1[.)]) \").replace(\"html\", \"</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)\").replace(\"tag\", _tag).getRegex();\nvar blockquote = edit(/^( {0,3}> ?(paragraph|[^\\n]*)(?:\\n|$))+/).replace(\"paragraph\", paragraph).getRegex();\nvar blockNormal = {\n  blockquote,\n  code: blockCode,\n  def,\n  fences,\n  heading,\n  hr,\n  html,\n  lheading,\n  list,\n  newline,\n  paragraph,\n  table: noopTest,\n  text: blockText\n};\nvar gfmTable = edit(\"^ *([^\\\\n ].*)\\\\n {0,3}((?:\\\\| *)?:?-+:? *(?:\\\\| *:?-+:? *)*(?:\\\\| *)?)(?:\\\\n((?:(?! *\\\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\\\n|$))*)\\\\n*|$)\").replace(\"hr\", hr).replace(\"heading\", \" {0,3}#{1,6}(?:\\\\s|$)\").replace(\"blockquote\", \" {0,3}>\").replace(\"code\", \"(?: {4}| {0,3}\t)[^\\\\n]\").replace(\"fences\", \" {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n\").replace(\"list\", \" {0,3}(?:[*+-]|1[.)]) \").replace(\"html\", \"</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)\").replace(\"tag\", _tag).getRegex();\nvar blockGfm = {\n  ...blockNormal,\n  lheading: lheadingGfm,\n  table: gfmTable,\n  paragraph: edit(_paragraph).replace(\"hr\", hr).replace(\"heading\", \" {0,3}#{1,6}(?:\\\\s|$)\").replace(\"|lheading\", \"\").replace(\"table\", gfmTable).replace(\"blockquote\", \" {0,3}>\").replace(\"fences\", \" {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n\").replace(\"list\", \" {0,3}(?:[*+-]|1[.)]) \").replace(\"html\", \"</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)\").replace(\"tag\", _tag).getRegex()\n};\nvar blockPedantic = {\n  ...blockNormal,\n  html: edit(`^ *(?:comment *(?:\\\\n|\\\\s*$)|<(tag)[\\\\s\\\\S]+?</\\\\1> *(?:\\\\n{2,}|\\\\s*$)|<tag(?:\"[^\"]*\"|'[^']*'|\\\\s[^'\"/>\\\\s]*)*?/?> *(?:\\\\n{2,}|\\\\s*$))`).replace(\"comment\", _comment).replace(/tag/g, \"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\\\b)\\\\w+(?!:|[^\\\\w\\\\s@]*@)\\\\b\").getRegex(),\n  def: /^ *\\[([^\\]]+)\\]: *<?([^\\s>]+)>?(?: +([\"(][^\\n]+[\")]))? *(?:\\n+|$)/,\n  heading: /^(#{1,6})(.*)(?:\\n+|$)/,\n  fences: noopTest,\n  // fences not supported\n  lheading: /^(.+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/,\n  paragraph: edit(_paragraph).replace(\"hr\", hr).replace(\"heading\", \" *#{1,6} *[^\\n]\").replace(\"lheading\", lheading).replace(\"|table\", \"\").replace(\"blockquote\", \" {0,3}>\").replace(\"|fences\", \"\").replace(\"|list\", \"\").replace(\"|html\", \"\").replace(\"|tag\", \"\").getRegex()\n};\nvar escape = /^\\\\([!\"#$%&'()*+,\\-./:;<=>?@\\[\\]\\\\^_`{|}~])/;\nvar inlineCode = /^(`+)([^`]|[^`][\\s\\S]*?[^`])\\1(?!`)/;\nvar br = /^( {2,}|\\\\)\\n(?!\\s*$)/;\nvar inlineText = /^(`+|[^`])(?:(?= {2,}\\n)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*_]|\\b_|$)|[^ ](?= {2,}\\n)))/;\nvar _punctuation = /[\\p{P}\\p{S}]/u;\nvar _punctuationOrSpace = /[\\s\\p{P}\\p{S}]/u;\nvar _notPunctuationOrSpace = /[^\\s\\p{P}\\p{S}]/u;\nvar punctuation = edit(/^((?![*_])punctSpace)/, \"u\").replace(/punctSpace/g, _punctuationOrSpace).getRegex();\nvar _punctuationGfmStrongEm = /(?!~)[\\p{P}\\p{S}]/u;\nvar _punctuationOrSpaceGfmStrongEm = /(?!~)[\\s\\p{P}\\p{S}]/u;\nvar _notPunctuationOrSpaceGfmStrongEm = /(?:[^\\s\\p{P}\\p{S}]|~)/u;\nvar blockSkip = /\\[[^[\\]]*?\\]\\((?:\\\\.|[^\\\\\\(\\)]|\\((?:\\\\.|[^\\\\\\(\\)])*\\))*\\)|`[^`]*?`|<[^<>]*?>/g;\nvar emStrongLDelimCore = /^(?:\\*+(?:((?!\\*)punct)|[^\\s*]))|^_+(?:((?!_)punct)|([^\\s_]))/;\nvar emStrongLDelim = edit(emStrongLDelimCore, \"u\").replace(/punct/g, _punctuation).getRegex();\nvar emStrongLDelimGfm = edit(emStrongLDelimCore, \"u\").replace(/punct/g, _punctuationGfmStrongEm).getRegex();\nvar emStrongRDelimAstCore = \"^[^_*]*?__[^_*]*?\\\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\\\*)punct(\\\\*+)(?=[\\\\s]|$)|notPunctSpace(\\\\*+)(?!\\\\*)(?=punctSpace|$)|(?!\\\\*)punctSpace(\\\\*+)(?=notPunctSpace)|[\\\\s](\\\\*+)(?!\\\\*)(?=punct)|(?!\\\\*)punct(\\\\*+)(?!\\\\*)(?=punct)|notPunctSpace(\\\\*+)(?=notPunctSpace)\";\nvar emStrongRDelimAst = edit(emStrongRDelimAstCore, \"gu\").replace(/notPunctSpace/g, _notPunctuationOrSpace).replace(/punctSpace/g, _punctuationOrSpace).replace(/punct/g, _punctuation).getRegex();\nvar emStrongRDelimAstGfm = edit(emStrongRDelimAstCore, \"gu\").replace(/notPunctSpace/g, _notPunctuationOrSpaceGfmStrongEm).replace(/punctSpace/g, _punctuationOrSpaceGfmStrongEm).replace(/punct/g, _punctuationGfmStrongEm).getRegex();\nvar emStrongRDelimUnd = edit(\"^[^_*]*?\\\\*\\\\*[^_*]*?_[^_*]*?(?=\\\\*\\\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)\", \"gu\").replace(/notPunctSpace/g, _notPunctuationOrSpace).replace(/punctSpace/g, _punctuationOrSpace).replace(/punct/g, _punctuation).getRegex();\nvar anyPunctuation = edit(/\\\\(punct)/, \"gu\").replace(/punct/g, _punctuation).getRegex();\nvar autolink = edit(/^<(scheme:[^\\s\\x00-\\x1f<>]*|email)>/).replace(\"scheme\", /[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace(\"email\", /[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex();\nvar _inlineComment = edit(_comment).replace(\"(?:-->|$)\", \"-->\").getRegex();\nvar tag = edit(\"^comment|^</[a-zA-Z][\\\\w:-]*\\\\s*>|^<[a-zA-Z][\\\\w-]*(?:attribute)*?\\\\s*/?>|^<\\\\?[\\\\s\\\\S]*?\\\\?>|^<![a-zA-Z]+\\\\s[\\\\s\\\\S]*?>|^<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?\\\\]\\\\]>\").replace(\"comment\", _inlineComment).replace(\"attribute\", /\\s+[a-zA-Z:_][\\w.:-]*(?:\\s*=\\s*\"[^\"]*\"|\\s*=\\s*'[^']*'|\\s*=\\s*[^\\s\"'=<>`]+)?/).getRegex();\nvar _inlineLabel = /(?:\\[(?:\\\\.|[^\\[\\]\\\\])*\\]|\\\\.|`[^`]*`|[^\\[\\]\\\\`])*?/;\nvar link = edit(/^!?\\[(label)\\]\\(\\s*(href)(?:(?:[ \\t]*(?:\\n[ \\t]*)?)(title))?\\s*\\)/).replace(\"label\", _inlineLabel).replace(\"href\", /<(?:\\\\.|[^\\n<>\\\\])+>|[^ \\t\\n\\x00-\\x1f]*/).replace(\"title\", /\"(?:\\\\\"?|[^\"\\\\])*\"|'(?:\\\\'?|[^'\\\\])*'|\\((?:\\\\\\)?|[^)\\\\])*\\)/).getRegex();\nvar reflink = edit(/^!?\\[(label)\\]\\[(ref)\\]/).replace(\"label\", _inlineLabel).replace(\"ref\", _blockLabel).getRegex();\nvar nolink = edit(/^!?\\[(ref)\\](?:\\[\\])?/).replace(\"ref\", _blockLabel).getRegex();\nvar reflinkSearch = edit(\"reflink|nolink(?!\\\\()\", \"g\").replace(\"reflink\", reflink).replace(\"nolink\", nolink).getRegex();\nvar inlineNormal = {\n  _backpedal: noopTest,\n  // only used for GFM url\n  anyPunctuation,\n  autolink,\n  blockSkip,\n  br,\n  code: inlineCode,\n  del: noopTest,\n  emStrongLDelim,\n  emStrongRDelimAst,\n  emStrongRDelimUnd,\n  escape,\n  link,\n  nolink,\n  punctuation,\n  reflink,\n  reflinkSearch,\n  tag,\n  text: inlineText,\n  url: noopTest\n};\nvar inlinePedantic = {\n  ...inlineNormal,\n  link: edit(/^!?\\[(label)\\]\\((.*?)\\)/).replace(\"label\", _inlineLabel).getRegex(),\n  reflink: edit(/^!?\\[(label)\\]\\s*\\[([^\\]]*)\\]/).replace(\"label\", _inlineLabel).getRegex()\n};\nvar inlineGfm = {\n  ...inlineNormal,\n  emStrongRDelimAst: emStrongRDelimAstGfm,\n  emStrongLDelim: emStrongLDelimGfm,\n  url: edit(/^((?:ftp|https?):\\/\\/|www\\.)(?:[a-zA-Z0-9\\-]+\\.?)+[^\\s<]*|^email/, \"i\").replace(\"email\", /[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),\n  _backpedal: /(?:[^?!.,:;*_'\"~()&]+|\\([^)]*\\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'\"~)]+(?!$))+/,\n  del: /^(~~?)(?=[^\\s~])((?:\\\\.|[^\\\\])*?(?:\\\\.|[^\\s~\\\\]))\\1(?=[^~]|$)/,\n  text: /^([`~]+|[^`~])(?:(?= {2,}\\n)|(?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*~_]|\\b_|https?:\\/\\/|ftp:\\/\\/|www\\.|$)|[^ ](?= {2,}\\n)|[^a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-](?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)))/\n};\nvar inlineBreaks = {\n  ...inlineGfm,\n  br: edit(br).replace(\"{2,}\", \"*\").getRegex(),\n  text: edit(inlineGfm.text).replace(\"\\\\b_\", \"\\\\b_| {2,}\\\\n\").replace(/\\{2,\\}/g, \"*\").getRegex()\n};\nvar block = {\n  normal: blockNormal,\n  gfm: blockGfm,\n  pedantic: blockPedantic\n};\nvar inline = {\n  normal: inlineNormal,\n  gfm: inlineGfm,\n  breaks: inlineBreaks,\n  pedantic: inlinePedantic\n};\n\n// src/helpers.ts\nvar escapeReplacements = {\n  \"&\": \"&amp;\",\n  \"<\": \"&lt;\",\n  \">\": \"&gt;\",\n  '\"': \"&quot;\",\n  \"'\": \"&#39;\"\n};\nvar getEscapeReplacement = ch => escapeReplacements[ch];\nfunction escape2(html2, encode) {\n  if (encode) {\n    if (other.escapeTest.test(html2)) {\n      return html2.replace(other.escapeReplace, getEscapeReplacement);\n    }\n  } else {\n    if (other.escapeTestNoEncode.test(html2)) {\n      return html2.replace(other.escapeReplaceNoEncode, getEscapeReplacement);\n    }\n  }\n  return html2;\n}\nfunction cleanUrl(href) {\n  try {\n    href = encodeURI(href).replace(other.percentDecode, \"%\");\n  } catch {\n    return null;\n  }\n  return href;\n}\nfunction splitCells(tableRow, count) {\n  const row = tableRow.replace(other.findPipe, (match, offset, str) => {\n      let escaped = false;\n      let curr = offset;\n      while (--curr >= 0 && str[curr] === \"\\\\\") escaped = !escaped;\n      if (escaped) {\n        return \"|\";\n      } else {\n        return \" |\";\n      }\n    }),\n    cells = row.split(other.splitPipe);\n  let i = 0;\n  if (!cells[0].trim()) {\n    cells.shift();\n  }\n  if (cells.length > 0 && !cells.at(-1)?.trim()) {\n    cells.pop();\n  }\n  if (count) {\n    if (cells.length > count) {\n      cells.splice(count);\n    } else {\n      while (cells.length < count) cells.push(\"\");\n    }\n  }\n  for (; i < cells.length; i++) {\n    cells[i] = cells[i].trim().replace(other.slashPipe, \"|\");\n  }\n  return cells;\n}\nfunction rtrim(str, c, invert) {\n  const l = str.length;\n  if (l === 0) {\n    return \"\";\n  }\n  let suffLen = 0;\n  while (suffLen < l) {\n    const currChar = str.charAt(l - suffLen - 1);\n    if (currChar === c && !invert) {\n      suffLen++;\n    } else if (currChar !== c && invert) {\n      suffLen++;\n    } else {\n      break;\n    }\n  }\n  return str.slice(0, l - suffLen);\n}\nfunction findClosingBracket(str, b) {\n  if (str.indexOf(b[1]) === -1) {\n    return -1;\n  }\n  let level = 0;\n  for (let i = 0; i < str.length; i++) {\n    if (str[i] === \"\\\\\") {\n      i++;\n    } else if (str[i] === b[0]) {\n      level++;\n    } else if (str[i] === b[1]) {\n      level--;\n      if (level < 0) {\n        return i;\n      }\n    }\n  }\n  if (level > 0) {\n    return -2;\n  }\n  return -1;\n}\n\n// src/Tokenizer.ts\nfunction outputLink(cap, link2, raw, lexer2, rules) {\n  const href = link2.href;\n  const title = link2.title || null;\n  const text = cap[1].replace(rules.other.outputLinkReplace, \"$1\");\n  lexer2.state.inLink = true;\n  const token = {\n    type: cap[0].charAt(0) === \"!\" ? \"image\" : \"link\",\n    raw,\n    href,\n    title,\n    text,\n    tokens: lexer2.inlineTokens(text)\n  };\n  lexer2.state.inLink = false;\n  return token;\n}\nfunction indentCodeCompensation(raw, text, rules) {\n  const matchIndentToCode = raw.match(rules.other.indentCodeCompensation);\n  if (matchIndentToCode === null) {\n    return text;\n  }\n  const indentToCode = matchIndentToCode[1];\n  return text.split(\"\\n\").map(node => {\n    const matchIndentInNode = node.match(rules.other.beginningSpace);\n    if (matchIndentInNode === null) {\n      return node;\n    }\n    const [indentInNode] = matchIndentInNode;\n    if (indentInNode.length >= indentToCode.length) {\n      return node.slice(indentToCode.length);\n    }\n    return node;\n  }).join(\"\\n\");\n}\nvar _Tokenizer = class {\n  options;\n  rules;\n  // set by the lexer\n  lexer;\n  // set by the lexer\n  constructor(options2) {\n    this.options = options2 || _defaults;\n  }\n  space(src) {\n    const cap = this.rules.block.newline.exec(src);\n    if (cap && cap[0].length > 0) {\n      return {\n        type: \"space\",\n        raw: cap[0]\n      };\n    }\n  }\n  code(src) {\n    const cap = this.rules.block.code.exec(src);\n    if (cap) {\n      const text = cap[0].replace(this.rules.other.codeRemoveIndent, \"\");\n      return {\n        type: \"code\",\n        raw: cap[0],\n        codeBlockStyle: \"indented\",\n        text: !this.options.pedantic ? rtrim(text, \"\\n\") : text\n      };\n    }\n  }\n  fences(src) {\n    const cap = this.rules.block.fences.exec(src);\n    if (cap) {\n      const raw = cap[0];\n      const text = indentCodeCompensation(raw, cap[3] || \"\", this.rules);\n      return {\n        type: \"code\",\n        raw,\n        lang: cap[2] ? cap[2].trim().replace(this.rules.inline.anyPunctuation, \"$1\") : cap[2],\n        text\n      };\n    }\n  }\n  heading(src) {\n    const cap = this.rules.block.heading.exec(src);\n    if (cap) {\n      let text = cap[2].trim();\n      if (this.rules.other.endingHash.test(text)) {\n        const trimmed = rtrim(text, \"#\");\n        if (this.options.pedantic) {\n          text = trimmed.trim();\n        } else if (!trimmed || this.rules.other.endingSpaceChar.test(trimmed)) {\n          text = trimmed.trim();\n        }\n      }\n      return {\n        type: \"heading\",\n        raw: cap[0],\n        depth: cap[1].length,\n        text,\n        tokens: this.lexer.inline(text)\n      };\n    }\n  }\n  hr(src) {\n    const cap = this.rules.block.hr.exec(src);\n    if (cap) {\n      return {\n        type: \"hr\",\n        raw: rtrim(cap[0], \"\\n\")\n      };\n    }\n  }\n  blockquote(src) {\n    const cap = this.rules.block.blockquote.exec(src);\n    if (cap) {\n      let lines = rtrim(cap[0], \"\\n\").split(\"\\n\");\n      let raw = \"\";\n      let text = \"\";\n      const tokens = [];\n      while (lines.length > 0) {\n        let inBlockquote = false;\n        const currentLines = [];\n        let i;\n        for (i = 0; i < lines.length; i++) {\n          if (this.rules.other.blockquoteStart.test(lines[i])) {\n            currentLines.push(lines[i]);\n            inBlockquote = true;\n          } else if (!inBlockquote) {\n            currentLines.push(lines[i]);\n          } else {\n            break;\n          }\n        }\n        lines = lines.slice(i);\n        const currentRaw = currentLines.join(\"\\n\");\n        const currentText = currentRaw.replace(this.rules.other.blockquoteSetextReplace, \"\\n    $1\").replace(this.rules.other.blockquoteSetextReplace2, \"\");\n        raw = raw ? `${raw}\n${currentRaw}` : currentRaw;\n        text = text ? `${text}\n${currentText}` : currentText;\n        const top = this.lexer.state.top;\n        this.lexer.state.top = true;\n        this.lexer.blockTokens(currentText, tokens, true);\n        this.lexer.state.top = top;\n        if (lines.length === 0) {\n          break;\n        }\n        const lastToken = tokens.at(-1);\n        if (lastToken?.type === \"code\") {\n          break;\n        } else if (lastToken?.type === \"blockquote\") {\n          const oldToken = lastToken;\n          const newText = oldToken.raw + \"\\n\" + lines.join(\"\\n\");\n          const newToken = this.blockquote(newText);\n          tokens[tokens.length - 1] = newToken;\n          raw = raw.substring(0, raw.length - oldToken.raw.length) + newToken.raw;\n          text = text.substring(0, text.length - oldToken.text.length) + newToken.text;\n          break;\n        } else if (lastToken?.type === \"list\") {\n          const oldToken = lastToken;\n          const newText = oldToken.raw + \"\\n\" + lines.join(\"\\n\");\n          const newToken = this.list(newText);\n          tokens[tokens.length - 1] = newToken;\n          raw = raw.substring(0, raw.length - lastToken.raw.length) + newToken.raw;\n          text = text.substring(0, text.length - oldToken.raw.length) + newToken.raw;\n          lines = newText.substring(tokens.at(-1).raw.length).split(\"\\n\");\n          continue;\n        }\n      }\n      return {\n        type: \"blockquote\",\n        raw,\n        tokens,\n        text\n      };\n    }\n  }\n  list(src) {\n    let cap = this.rules.block.list.exec(src);\n    if (cap) {\n      let bull = cap[1].trim();\n      const isordered = bull.length > 1;\n      const list2 = {\n        type: \"list\",\n        raw: \"\",\n        ordered: isordered,\n        start: isordered ? +bull.slice(0, -1) : \"\",\n        loose: false,\n        items: []\n      };\n      bull = isordered ? `\\\\d{1,9}\\\\${bull.slice(-1)}` : `\\\\${bull}`;\n      if (this.options.pedantic) {\n        bull = isordered ? bull : \"[*+-]\";\n      }\n      const itemRegex = this.rules.other.listItemRegex(bull);\n      let endsWithBlankLine = false;\n      while (src) {\n        let endEarly = false;\n        let raw = \"\";\n        let itemContents = \"\";\n        if (!(cap = itemRegex.exec(src))) {\n          break;\n        }\n        if (this.rules.block.hr.test(src)) {\n          break;\n        }\n        raw = cap[0];\n        src = src.substring(raw.length);\n        let line = cap[2].split(\"\\n\", 1)[0].replace(this.rules.other.listReplaceTabs, t => \" \".repeat(3 * t.length));\n        let nextLine = src.split(\"\\n\", 1)[0];\n        let blankLine = !line.trim();\n        let indent = 0;\n        if (this.options.pedantic) {\n          indent = 2;\n          itemContents = line.trimStart();\n        } else if (blankLine) {\n          indent = cap[1].length + 1;\n        } else {\n          indent = cap[2].search(this.rules.other.nonSpaceChar);\n          indent = indent > 4 ? 1 : indent;\n          itemContents = line.slice(indent);\n          indent += cap[1].length;\n        }\n        if (blankLine && this.rules.other.blankLine.test(nextLine)) {\n          raw += nextLine + \"\\n\";\n          src = src.substring(nextLine.length + 1);\n          endEarly = true;\n        }\n        if (!endEarly) {\n          const nextBulletRegex = this.rules.other.nextBulletRegex(indent);\n          const hrRegex = this.rules.other.hrRegex(indent);\n          const fencesBeginRegex = this.rules.other.fencesBeginRegex(indent);\n          const headingBeginRegex = this.rules.other.headingBeginRegex(indent);\n          const htmlBeginRegex = this.rules.other.htmlBeginRegex(indent);\n          while (src) {\n            const rawLine = src.split(\"\\n\", 1)[0];\n            let nextLineWithoutTabs;\n            nextLine = rawLine;\n            if (this.options.pedantic) {\n              nextLine = nextLine.replace(this.rules.other.listReplaceNesting, \"  \");\n              nextLineWithoutTabs = nextLine;\n            } else {\n              nextLineWithoutTabs = nextLine.replace(this.rules.other.tabCharGlobal, \"    \");\n            }\n            if (fencesBeginRegex.test(nextLine)) {\n              break;\n            }\n            if (headingBeginRegex.test(nextLine)) {\n              break;\n            }\n            if (htmlBeginRegex.test(nextLine)) {\n              break;\n            }\n            if (nextBulletRegex.test(nextLine)) {\n              break;\n            }\n            if (hrRegex.test(nextLine)) {\n              break;\n            }\n            if (nextLineWithoutTabs.search(this.rules.other.nonSpaceChar) >= indent || !nextLine.trim()) {\n              itemContents += \"\\n\" + nextLineWithoutTabs.slice(indent);\n            } else {\n              if (blankLine) {\n                break;\n              }\n              if (line.replace(this.rules.other.tabCharGlobal, \"    \").search(this.rules.other.nonSpaceChar) >= 4) {\n                break;\n              }\n              if (fencesBeginRegex.test(line)) {\n                break;\n              }\n              if (headingBeginRegex.test(line)) {\n                break;\n              }\n              if (hrRegex.test(line)) {\n                break;\n              }\n              itemContents += \"\\n\" + nextLine;\n            }\n            if (!blankLine && !nextLine.trim()) {\n              blankLine = true;\n            }\n            raw += rawLine + \"\\n\";\n            src = src.substring(rawLine.length + 1);\n            line = nextLineWithoutTabs.slice(indent);\n          }\n        }\n        if (!list2.loose) {\n          if (endsWithBlankLine) {\n            list2.loose = true;\n          } else if (this.rules.other.doubleBlankLine.test(raw)) {\n            endsWithBlankLine = true;\n          }\n        }\n        let istask = null;\n        let ischecked;\n        if (this.options.gfm) {\n          istask = this.rules.other.listIsTask.exec(itemContents);\n          if (istask) {\n            ischecked = istask[0] !== \"[ ] \";\n            itemContents = itemContents.replace(this.rules.other.listReplaceTask, \"\");\n          }\n        }\n        list2.items.push({\n          type: \"list_item\",\n          raw,\n          task: !!istask,\n          checked: ischecked,\n          loose: false,\n          text: itemContents,\n          tokens: []\n        });\n        list2.raw += raw;\n      }\n      const lastItem = list2.items.at(-1);\n      if (lastItem) {\n        lastItem.raw = lastItem.raw.trimEnd();\n        lastItem.text = lastItem.text.trimEnd();\n      } else {\n        return;\n      }\n      list2.raw = list2.raw.trimEnd();\n      for (let i = 0; i < list2.items.length; i++) {\n        this.lexer.state.top = false;\n        list2.items[i].tokens = this.lexer.blockTokens(list2.items[i].text, []);\n        if (!list2.loose) {\n          const spacers = list2.items[i].tokens.filter(t => t.type === \"space\");\n          const hasMultipleLineBreaks = spacers.length > 0 && spacers.some(t => this.rules.other.anyLine.test(t.raw));\n          list2.loose = hasMultipleLineBreaks;\n        }\n      }\n      if (list2.loose) {\n        for (let i = 0; i < list2.items.length; i++) {\n          list2.items[i].loose = true;\n        }\n      }\n      return list2;\n    }\n  }\n  html(src) {\n    const cap = this.rules.block.html.exec(src);\n    if (cap) {\n      const token = {\n        type: \"html\",\n        block: true,\n        raw: cap[0],\n        pre: cap[1] === \"pre\" || cap[1] === \"script\" || cap[1] === \"style\",\n        text: cap[0]\n      };\n      return token;\n    }\n  }\n  def(src) {\n    const cap = this.rules.block.def.exec(src);\n    if (cap) {\n      const tag2 = cap[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal, \" \");\n      const href = cap[2] ? cap[2].replace(this.rules.other.hrefBrackets, \"$1\").replace(this.rules.inline.anyPunctuation, \"$1\") : \"\";\n      const title = cap[3] ? cap[3].substring(1, cap[3].length - 1).replace(this.rules.inline.anyPunctuation, \"$1\") : cap[3];\n      return {\n        type: \"def\",\n        tag: tag2,\n        raw: cap[0],\n        href,\n        title\n      };\n    }\n  }\n  table(src) {\n    const cap = this.rules.block.table.exec(src);\n    if (!cap) {\n      return;\n    }\n    if (!this.rules.other.tableDelimiter.test(cap[2])) {\n      return;\n    }\n    const headers = splitCells(cap[1]);\n    const aligns = cap[2].replace(this.rules.other.tableAlignChars, \"\").split(\"|\");\n    const rows = cap[3]?.trim() ? cap[3].replace(this.rules.other.tableRowBlankLine, \"\").split(\"\\n\") : [];\n    const item = {\n      type: \"table\",\n      raw: cap[0],\n      header: [],\n      align: [],\n      rows: []\n    };\n    if (headers.length !== aligns.length) {\n      return;\n    }\n    for (const align of aligns) {\n      if (this.rules.other.tableAlignRight.test(align)) {\n        item.align.push(\"right\");\n      } else if (this.rules.other.tableAlignCenter.test(align)) {\n        item.align.push(\"center\");\n      } else if (this.rules.other.tableAlignLeft.test(align)) {\n        item.align.push(\"left\");\n      } else {\n        item.align.push(null);\n      }\n    }\n    for (let i = 0; i < headers.length; i++) {\n      item.header.push({\n        text: headers[i],\n        tokens: this.lexer.inline(headers[i]),\n        header: true,\n        align: item.align[i]\n      });\n    }\n    for (const row of rows) {\n      item.rows.push(splitCells(row, item.header.length).map((cell, i) => {\n        return {\n          text: cell,\n          tokens: this.lexer.inline(cell),\n          header: false,\n          align: item.align[i]\n        };\n      }));\n    }\n    return item;\n  }\n  lheading(src) {\n    const cap = this.rules.block.lheading.exec(src);\n    if (cap) {\n      return {\n        type: \"heading\",\n        raw: cap[0],\n        depth: cap[2].charAt(0) === \"=\" ? 1 : 2,\n        text: cap[1],\n        tokens: this.lexer.inline(cap[1])\n      };\n    }\n  }\n  paragraph(src) {\n    const cap = this.rules.block.paragraph.exec(src);\n    if (cap) {\n      const text = cap[1].charAt(cap[1].length - 1) === \"\\n\" ? cap[1].slice(0, -1) : cap[1];\n      return {\n        type: \"paragraph\",\n        raw: cap[0],\n        text,\n        tokens: this.lexer.inline(text)\n      };\n    }\n  }\n  text(src) {\n    const cap = this.rules.block.text.exec(src);\n    if (cap) {\n      return {\n        type: \"text\",\n        raw: cap[0],\n        text: cap[0],\n        tokens: this.lexer.inline(cap[0])\n      };\n    }\n  }\n  escape(src) {\n    const cap = this.rules.inline.escape.exec(src);\n    if (cap) {\n      return {\n        type: \"escape\",\n        raw: cap[0],\n        text: cap[1]\n      };\n    }\n  }\n  tag(src) {\n    const cap = this.rules.inline.tag.exec(src);\n    if (cap) {\n      if (!this.lexer.state.inLink && this.rules.other.startATag.test(cap[0])) {\n        this.lexer.state.inLink = true;\n      } else if (this.lexer.state.inLink && this.rules.other.endATag.test(cap[0])) {\n        this.lexer.state.inLink = false;\n      }\n      if (!this.lexer.state.inRawBlock && this.rules.other.startPreScriptTag.test(cap[0])) {\n        this.lexer.state.inRawBlock = true;\n      } else if (this.lexer.state.inRawBlock && this.rules.other.endPreScriptTag.test(cap[0])) {\n        this.lexer.state.inRawBlock = false;\n      }\n      return {\n        type: \"html\",\n        raw: cap[0],\n        inLink: this.lexer.state.inLink,\n        inRawBlock: this.lexer.state.inRawBlock,\n        block: false,\n        text: cap[0]\n      };\n    }\n  }\n  link(src) {\n    const cap = this.rules.inline.link.exec(src);\n    if (cap) {\n      const trimmedUrl = cap[2].trim();\n      if (!this.options.pedantic && this.rules.other.startAngleBracket.test(trimmedUrl)) {\n        if (!this.rules.other.endAngleBracket.test(trimmedUrl)) {\n          return;\n        }\n        const rtrimSlash = rtrim(trimmedUrl.slice(0, -1), \"\\\\\");\n        if ((trimmedUrl.length - rtrimSlash.length) % 2 === 0) {\n          return;\n        }\n      } else {\n        const lastParenIndex = findClosingBracket(cap[2], \"()\");\n        if (lastParenIndex === -2) {\n          return;\n        }\n        if (lastParenIndex > -1) {\n          const start = cap[0].indexOf(\"!\") === 0 ? 5 : 4;\n          const linkLen = start + cap[1].length + lastParenIndex;\n          cap[2] = cap[2].substring(0, lastParenIndex);\n          cap[0] = cap[0].substring(0, linkLen).trim();\n          cap[3] = \"\";\n        }\n      }\n      let href = cap[2];\n      let title = \"\";\n      if (this.options.pedantic) {\n        const link2 = this.rules.other.pedanticHrefTitle.exec(href);\n        if (link2) {\n          href = link2[1];\n          title = link2[3];\n        }\n      } else {\n        title = cap[3] ? cap[3].slice(1, -1) : \"\";\n      }\n      href = href.trim();\n      if (this.rules.other.startAngleBracket.test(href)) {\n        if (this.options.pedantic && !this.rules.other.endAngleBracket.test(trimmedUrl)) {\n          href = href.slice(1);\n        } else {\n          href = href.slice(1, -1);\n        }\n      }\n      return outputLink(cap, {\n        href: href ? href.replace(this.rules.inline.anyPunctuation, \"$1\") : href,\n        title: title ? title.replace(this.rules.inline.anyPunctuation, \"$1\") : title\n      }, cap[0], this.lexer, this.rules);\n    }\n  }\n  reflink(src, links) {\n    let cap;\n    if ((cap = this.rules.inline.reflink.exec(src)) || (cap = this.rules.inline.nolink.exec(src))) {\n      const linkString = (cap[2] || cap[1]).replace(this.rules.other.multipleSpaceGlobal, \" \");\n      const link2 = links[linkString.toLowerCase()];\n      if (!link2) {\n        const text = cap[0].charAt(0);\n        return {\n          type: \"text\",\n          raw: text,\n          text\n        };\n      }\n      return outputLink(cap, link2, cap[0], this.lexer, this.rules);\n    }\n  }\n  emStrong(src, maskedSrc, prevChar = \"\") {\n    let match = this.rules.inline.emStrongLDelim.exec(src);\n    if (!match) return;\n    if (match[3] && prevChar.match(this.rules.other.unicodeAlphaNumeric)) return;\n    const nextChar = match[1] || match[2] || \"\";\n    if (!nextChar || !prevChar || this.rules.inline.punctuation.exec(prevChar)) {\n      const lLength = [...match[0]].length - 1;\n      let rDelim,\n        rLength,\n        delimTotal = lLength,\n        midDelimTotal = 0;\n      const endReg = match[0][0] === \"*\" ? this.rules.inline.emStrongRDelimAst : this.rules.inline.emStrongRDelimUnd;\n      endReg.lastIndex = 0;\n      maskedSrc = maskedSrc.slice(-1 * src.length + lLength);\n      while ((match = endReg.exec(maskedSrc)) != null) {\n        rDelim = match[1] || match[2] || match[3] || match[4] || match[5] || match[6];\n        if (!rDelim) continue;\n        rLength = [...rDelim].length;\n        if (match[3] || match[4]) {\n          delimTotal += rLength;\n          continue;\n        } else if (match[5] || match[6]) {\n          if (lLength % 3 && !((lLength + rLength) % 3)) {\n            midDelimTotal += rLength;\n            continue;\n          }\n        }\n        delimTotal -= rLength;\n        if (delimTotal > 0) continue;\n        rLength = Math.min(rLength, rLength + delimTotal + midDelimTotal);\n        const lastCharLength = [...match[0]][0].length;\n        const raw = src.slice(0, lLength + match.index + lastCharLength + rLength);\n        if (Math.min(lLength, rLength) % 2) {\n          const text2 = raw.slice(1, -1);\n          return {\n            type: \"em\",\n            raw,\n            text: text2,\n            tokens: this.lexer.inlineTokens(text2)\n          };\n        }\n        const text = raw.slice(2, -2);\n        return {\n          type: \"strong\",\n          raw,\n          text,\n          tokens: this.lexer.inlineTokens(text)\n        };\n      }\n    }\n  }\n  codespan(src) {\n    const cap = this.rules.inline.code.exec(src);\n    if (cap) {\n      let text = cap[2].replace(this.rules.other.newLineCharGlobal, \" \");\n      const hasNonSpaceChars = this.rules.other.nonSpaceChar.test(text);\n      const hasSpaceCharsOnBothEnds = this.rules.other.startingSpaceChar.test(text) && this.rules.other.endingSpaceChar.test(text);\n      if (hasNonSpaceChars && hasSpaceCharsOnBothEnds) {\n        text = text.substring(1, text.length - 1);\n      }\n      return {\n        type: \"codespan\",\n        raw: cap[0],\n        text\n      };\n    }\n  }\n  br(src) {\n    const cap = this.rules.inline.br.exec(src);\n    if (cap) {\n      return {\n        type: \"br\",\n        raw: cap[0]\n      };\n    }\n  }\n  del(src) {\n    const cap = this.rules.inline.del.exec(src);\n    if (cap) {\n      return {\n        type: \"del\",\n        raw: cap[0],\n        text: cap[2],\n        tokens: this.lexer.inlineTokens(cap[2])\n      };\n    }\n  }\n  autolink(src) {\n    const cap = this.rules.inline.autolink.exec(src);\n    if (cap) {\n      let text, href;\n      if (cap[2] === \"@\") {\n        text = cap[1];\n        href = \"mailto:\" + text;\n      } else {\n        text = cap[1];\n        href = text;\n      }\n      return {\n        type: \"link\",\n        raw: cap[0],\n        text,\n        href,\n        tokens: [{\n          type: \"text\",\n          raw: text,\n          text\n        }]\n      };\n    }\n  }\n  url(src) {\n    let cap;\n    if (cap = this.rules.inline.url.exec(src)) {\n      let text, href;\n      if (cap[2] === \"@\") {\n        text = cap[0];\n        href = \"mailto:\" + text;\n      } else {\n        let prevCapZero;\n        do {\n          prevCapZero = cap[0];\n          cap[0] = this.rules.inline._backpedal.exec(cap[0])?.[0] ?? \"\";\n        } while (prevCapZero !== cap[0]);\n        text = cap[0];\n        if (cap[1] === \"www.\") {\n          href = \"http://\" + cap[0];\n        } else {\n          href = cap[0];\n        }\n      }\n      return {\n        type: \"link\",\n        raw: cap[0],\n        text,\n        href,\n        tokens: [{\n          type: \"text\",\n          raw: text,\n          text\n        }]\n      };\n    }\n  }\n  inlineText(src) {\n    const cap = this.rules.inline.text.exec(src);\n    if (cap) {\n      const escaped = this.lexer.state.inRawBlock;\n      return {\n        type: \"text\",\n        raw: cap[0],\n        text: cap[0],\n        escaped\n      };\n    }\n  }\n};\n\n// src/Lexer.ts\nvar _Lexer = class __Lexer {\n  tokens;\n  options;\n  state;\n  tokenizer;\n  inlineQueue;\n  constructor(options2) {\n    this.tokens = [];\n    this.tokens.links = /* @__PURE__ */Object.create(null);\n    this.options = options2 || _defaults;\n    this.options.tokenizer = this.options.tokenizer || new _Tokenizer();\n    this.tokenizer = this.options.tokenizer;\n    this.tokenizer.options = this.options;\n    this.tokenizer.lexer = this;\n    this.inlineQueue = [];\n    this.state = {\n      inLink: false,\n      inRawBlock: false,\n      top: true\n    };\n    const rules = {\n      other,\n      block: block.normal,\n      inline: inline.normal\n    };\n    if (this.options.pedantic) {\n      rules.block = block.pedantic;\n      rules.inline = inline.pedantic;\n    } else if (this.options.gfm) {\n      rules.block = block.gfm;\n      if (this.options.breaks) {\n        rules.inline = inline.breaks;\n      } else {\n        rules.inline = inline.gfm;\n      }\n    }\n    this.tokenizer.rules = rules;\n  }\n  /**\n   * Expose Rules\n   */\n  static get rules() {\n    return {\n      block,\n      inline\n    };\n  }\n  /**\n   * Static Lex Method\n   */\n  static lex(src, options2) {\n    const lexer2 = new __Lexer(options2);\n    return lexer2.lex(src);\n  }\n  /**\n   * Static Lex Inline Method\n   */\n  static lexInline(src, options2) {\n    const lexer2 = new __Lexer(options2);\n    return lexer2.inlineTokens(src);\n  }\n  /**\n   * Preprocessing\n   */\n  lex(src) {\n    src = src.replace(other.carriageReturn, \"\\n\");\n    this.blockTokens(src, this.tokens);\n    for (let i = 0; i < this.inlineQueue.length; i++) {\n      const next = this.inlineQueue[i];\n      this.inlineTokens(next.src, next.tokens);\n    }\n    this.inlineQueue = [];\n    return this.tokens;\n  }\n  blockTokens(src, tokens = [], lastParagraphClipped = false) {\n    if (this.options.pedantic) {\n      src = src.replace(other.tabCharGlobal, \"    \").replace(other.spaceLine, \"\");\n    }\n    while (src) {\n      let token;\n      if (this.options.extensions?.block?.some(extTokenizer => {\n        if (token = extTokenizer.call({\n          lexer: this\n        }, src, tokens)) {\n          src = src.substring(token.raw.length);\n          tokens.push(token);\n          return true;\n        }\n        return false;\n      })) {\n        continue;\n      }\n      if (token = this.tokenizer.space(src)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (token.raw.length === 1 && lastToken !== void 0) {\n          lastToken.raw += \"\\n\";\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n      if (token = this.tokenizer.code(src)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (lastToken?.type === \"paragraph\" || lastToken?.type === \"text\") {\n          lastToken.raw += \"\\n\" + token.raw;\n          lastToken.text += \"\\n\" + token.text;\n          this.inlineQueue.at(-1).src = lastToken.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n      if (token = this.tokenizer.fences(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.heading(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.hr(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.blockquote(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.list(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.html(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.def(src)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (lastToken?.type === \"paragraph\" || lastToken?.type === \"text\") {\n          lastToken.raw += \"\\n\" + token.raw;\n          lastToken.text += \"\\n\" + token.raw;\n          this.inlineQueue.at(-1).src = lastToken.text;\n        } else if (!this.tokens.links[token.tag]) {\n          this.tokens.links[token.tag] = {\n            href: token.href,\n            title: token.title\n          };\n        }\n        continue;\n      }\n      if (token = this.tokenizer.table(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.lheading(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      let cutSrc = src;\n      if (this.options.extensions?.startBlock) {\n        let startIndex = Infinity;\n        const tempSrc = src.slice(1);\n        let tempStart;\n        this.options.extensions.startBlock.forEach(getStartIndex => {\n          tempStart = getStartIndex.call({\n            lexer: this\n          }, tempSrc);\n          if (typeof tempStart === \"number\" && tempStart >= 0) {\n            startIndex = Math.min(startIndex, tempStart);\n          }\n        });\n        if (startIndex < Infinity && startIndex >= 0) {\n          cutSrc = src.substring(0, startIndex + 1);\n        }\n      }\n      if (this.state.top && (token = this.tokenizer.paragraph(cutSrc))) {\n        const lastToken = tokens.at(-1);\n        if (lastParagraphClipped && lastToken?.type === \"paragraph\") {\n          lastToken.raw += \"\\n\" + token.raw;\n          lastToken.text += \"\\n\" + token.text;\n          this.inlineQueue.pop();\n          this.inlineQueue.at(-1).src = lastToken.text;\n        } else {\n          tokens.push(token);\n        }\n        lastParagraphClipped = cutSrc.length !== src.length;\n        src = src.substring(token.raw.length);\n        continue;\n      }\n      if (token = this.tokenizer.text(src)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (lastToken?.type === \"text\") {\n          lastToken.raw += \"\\n\" + token.raw;\n          lastToken.text += \"\\n\" + token.text;\n          this.inlineQueue.pop();\n          this.inlineQueue.at(-1).src = lastToken.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n      if (src) {\n        const errMsg = \"Infinite loop on byte: \" + src.charCodeAt(0);\n        if (this.options.silent) {\n          console.error(errMsg);\n          break;\n        } else {\n          throw new Error(errMsg);\n        }\n      }\n    }\n    this.state.top = true;\n    return tokens;\n  }\n  inline(src, tokens = []) {\n    this.inlineQueue.push({\n      src,\n      tokens\n    });\n    return tokens;\n  }\n  /**\n   * Lexing/Compiling\n   */\n  inlineTokens(src, tokens = []) {\n    let maskedSrc = src;\n    let match = null;\n    if (this.tokens.links) {\n      const links = Object.keys(this.tokens.links);\n      if (links.length > 0) {\n        while ((match = this.tokenizer.rules.inline.reflinkSearch.exec(maskedSrc)) != null) {\n          if (links.includes(match[0].slice(match[0].lastIndexOf(\"[\") + 1, -1))) {\n            maskedSrc = maskedSrc.slice(0, match.index) + \"[\" + \"a\".repeat(match[0].length - 2) + \"]\" + maskedSrc.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex);\n          }\n        }\n      }\n    }\n    while ((match = this.tokenizer.rules.inline.anyPunctuation.exec(maskedSrc)) != null) {\n      maskedSrc = maskedSrc.slice(0, match.index) + \"++\" + maskedSrc.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);\n    }\n    while ((match = this.tokenizer.rules.inline.blockSkip.exec(maskedSrc)) != null) {\n      maskedSrc = maskedSrc.slice(0, match.index) + \"[\" + \"a\".repeat(match[0].length - 2) + \"]\" + maskedSrc.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);\n    }\n    let keepPrevChar = false;\n    let prevChar = \"\";\n    while (src) {\n      if (!keepPrevChar) {\n        prevChar = \"\";\n      }\n      keepPrevChar = false;\n      let token;\n      if (this.options.extensions?.inline?.some(extTokenizer => {\n        if (token = extTokenizer.call({\n          lexer: this\n        }, src, tokens)) {\n          src = src.substring(token.raw.length);\n          tokens.push(token);\n          return true;\n        }\n        return false;\n      })) {\n        continue;\n      }\n      if (token = this.tokenizer.escape(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.tag(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.link(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.reflink(src, this.tokens.links)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (token.type === \"text\" && lastToken?.type === \"text\") {\n          lastToken.raw += token.raw;\n          lastToken.text += token.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n      if (token = this.tokenizer.emStrong(src, maskedSrc, prevChar)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.codespan(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.br(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.del(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.autolink(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (!this.state.inLink && (token = this.tokenizer.url(src))) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      let cutSrc = src;\n      if (this.options.extensions?.startInline) {\n        let startIndex = Infinity;\n        const tempSrc = src.slice(1);\n        let tempStart;\n        this.options.extensions.startInline.forEach(getStartIndex => {\n          tempStart = getStartIndex.call({\n            lexer: this\n          }, tempSrc);\n          if (typeof tempStart === \"number\" && tempStart >= 0) {\n            startIndex = Math.min(startIndex, tempStart);\n          }\n        });\n        if (startIndex < Infinity && startIndex >= 0) {\n          cutSrc = src.substring(0, startIndex + 1);\n        }\n      }\n      if (token = this.tokenizer.inlineText(cutSrc)) {\n        src = src.substring(token.raw.length);\n        if (token.raw.slice(-1) !== \"_\") {\n          prevChar = token.raw.slice(-1);\n        }\n        keepPrevChar = true;\n        const lastToken = tokens.at(-1);\n        if (lastToken?.type === \"text\") {\n          lastToken.raw += token.raw;\n          lastToken.text += token.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n      if (src) {\n        const errMsg = \"Infinite loop on byte: \" + src.charCodeAt(0);\n        if (this.options.silent) {\n          console.error(errMsg);\n          break;\n        } else {\n          throw new Error(errMsg);\n        }\n      }\n    }\n    return tokens;\n  }\n};\n\n// src/Renderer.ts\nvar _Renderer = class {\n  options;\n  parser;\n  // set by the parser\n  constructor(options2) {\n    this.options = options2 || _defaults;\n  }\n  space(token) {\n    return \"\";\n  }\n  code({\n    text,\n    lang,\n    escaped\n  }) {\n    const langString = (lang || \"\").match(other.notSpaceStart)?.[0];\n    const code = text.replace(other.endingNewline, \"\") + \"\\n\";\n    if (!langString) {\n      return \"<pre><code>\" + (escaped ? code : escape2(code, true)) + \"</code></pre>\\n\";\n    }\n    return '<pre><code class=\"language-' + escape2(langString) + '\">' + (escaped ? code : escape2(code, true)) + \"</code></pre>\\n\";\n  }\n  blockquote({\n    tokens\n  }) {\n    const body = this.parser.parse(tokens);\n    return `<blockquote>\n${body}</blockquote>\n`;\n  }\n  html({\n    text\n  }) {\n    return text;\n  }\n  heading({\n    tokens,\n    depth\n  }) {\n    return `<h${depth}>${this.parser.parseInline(tokens)}</h${depth}>\n`;\n  }\n  hr(token) {\n    return \"<hr>\\n\";\n  }\n  list(token) {\n    const ordered = token.ordered;\n    const start = token.start;\n    let body = \"\";\n    for (let j = 0; j < token.items.length; j++) {\n      const item = token.items[j];\n      body += this.listitem(item);\n    }\n    const type = ordered ? \"ol\" : \"ul\";\n    const startAttr = ordered && start !== 1 ? ' start=\"' + start + '\"' : \"\";\n    return \"<\" + type + startAttr + \">\\n\" + body + \"</\" + type + \">\\n\";\n  }\n  listitem(item) {\n    let itemBody = \"\";\n    if (item.task) {\n      const checkbox = this.checkbox({\n        checked: !!item.checked\n      });\n      if (item.loose) {\n        if (item.tokens[0]?.type === \"paragraph\") {\n          item.tokens[0].text = checkbox + \" \" + item.tokens[0].text;\n          if (item.tokens[0].tokens && item.tokens[0].tokens.length > 0 && item.tokens[0].tokens[0].type === \"text\") {\n            item.tokens[0].tokens[0].text = checkbox + \" \" + escape2(item.tokens[0].tokens[0].text);\n            item.tokens[0].tokens[0].escaped = true;\n          }\n        } else {\n          item.tokens.unshift({\n            type: \"text\",\n            raw: checkbox + \" \",\n            text: checkbox + \" \",\n            escaped: true\n          });\n        }\n      } else {\n        itemBody += checkbox + \" \";\n      }\n    }\n    itemBody += this.parser.parse(item.tokens, !!item.loose);\n    return `<li>${itemBody}</li>\n`;\n  }\n  checkbox({\n    checked\n  }) {\n    return \"<input \" + (checked ? 'checked=\"\" ' : \"\") + 'disabled=\"\" type=\"checkbox\">';\n  }\n  paragraph({\n    tokens\n  }) {\n    return `<p>${this.parser.parseInline(tokens)}</p>\n`;\n  }\n  table(token) {\n    let header = \"\";\n    let cell = \"\";\n    for (let j = 0; j < token.header.length; j++) {\n      cell += this.tablecell(token.header[j]);\n    }\n    header += this.tablerow({\n      text: cell\n    });\n    let body = \"\";\n    for (let j = 0; j < token.rows.length; j++) {\n      const row = token.rows[j];\n      cell = \"\";\n      for (let k = 0; k < row.length; k++) {\n        cell += this.tablecell(row[k]);\n      }\n      body += this.tablerow({\n        text: cell\n      });\n    }\n    if (body) body = `<tbody>${body}</tbody>`;\n    return \"<table>\\n<thead>\\n\" + header + \"</thead>\\n\" + body + \"</table>\\n\";\n  }\n  tablerow({\n    text\n  }) {\n    return `<tr>\n${text}</tr>\n`;\n  }\n  tablecell(token) {\n    const content = this.parser.parseInline(token.tokens);\n    const type = token.header ? \"th\" : \"td\";\n    const tag2 = token.align ? `<${type} align=\"${token.align}\">` : `<${type}>`;\n    return tag2 + content + `</${type}>\n`;\n  }\n  /**\n   * span level renderer\n   */\n  strong({\n    tokens\n  }) {\n    return `<strong>${this.parser.parseInline(tokens)}</strong>`;\n  }\n  em({\n    tokens\n  }) {\n    return `<em>${this.parser.parseInline(tokens)}</em>`;\n  }\n  codespan({\n    text\n  }) {\n    return `<code>${escape2(text, true)}</code>`;\n  }\n  br(token) {\n    return \"<br>\";\n  }\n  del({\n    tokens\n  }) {\n    return `<del>${this.parser.parseInline(tokens)}</del>`;\n  }\n  link({\n    href,\n    title,\n    tokens\n  }) {\n    const text = this.parser.parseInline(tokens);\n    const cleanHref = cleanUrl(href);\n    if (cleanHref === null) {\n      return text;\n    }\n    href = cleanHref;\n    let out = '<a href=\"' + href + '\"';\n    if (title) {\n      out += ' title=\"' + escape2(title) + '\"';\n    }\n    out += \">\" + text + \"</a>\";\n    return out;\n  }\n  image({\n    href,\n    title,\n    text,\n    tokens\n  }) {\n    if (tokens) {\n      text = this.parser.parseInline(tokens, this.parser.textRenderer);\n    }\n    const cleanHref = cleanUrl(href);\n    if (cleanHref === null) {\n      return escape2(text);\n    }\n    href = cleanHref;\n    let out = `<img src=\"${href}\" alt=\"${text}\"`;\n    if (title) {\n      out += ` title=\"${escape2(title)}\"`;\n    }\n    out += \">\";\n    return out;\n  }\n  text(token) {\n    return \"tokens\" in token && token.tokens ? this.parser.parseInline(token.tokens) : \"escaped\" in token && token.escaped ? token.text : escape2(token.text);\n  }\n};\n\n// src/TextRenderer.ts\nvar _TextRenderer = class {\n  // no need for block level renderers\n  strong({\n    text\n  }) {\n    return text;\n  }\n  em({\n    text\n  }) {\n    return text;\n  }\n  codespan({\n    text\n  }) {\n    return text;\n  }\n  del({\n    text\n  }) {\n    return text;\n  }\n  html({\n    text\n  }) {\n    return text;\n  }\n  text({\n    text\n  }) {\n    return text;\n  }\n  link({\n    text\n  }) {\n    return \"\" + text;\n  }\n  image({\n    text\n  }) {\n    return \"\" + text;\n  }\n  br() {\n    return \"\";\n  }\n};\n\n// src/Parser.ts\nvar _Parser = class __Parser {\n  options;\n  renderer;\n  textRenderer;\n  constructor(options2) {\n    this.options = options2 || _defaults;\n    this.options.renderer = this.options.renderer || new _Renderer();\n    this.renderer = this.options.renderer;\n    this.renderer.options = this.options;\n    this.renderer.parser = this;\n    this.textRenderer = new _TextRenderer();\n  }\n  /**\n   * Static Parse Method\n   */\n  static parse(tokens, options2) {\n    const parser2 = new __Parser(options2);\n    return parser2.parse(tokens);\n  }\n  /**\n   * Static Parse Inline Method\n   */\n  static parseInline(tokens, options2) {\n    const parser2 = new __Parser(options2);\n    return parser2.parseInline(tokens);\n  }\n  /**\n   * Parse Loop\n   */\n  parse(tokens, top = true) {\n    let out = \"\";\n    for (let i = 0; i < tokens.length; i++) {\n      const anyToken = tokens[i];\n      if (this.options.extensions?.renderers?.[anyToken.type]) {\n        const genericToken = anyToken;\n        const ret = this.options.extensions.renderers[genericToken.type].call({\n          parser: this\n        }, genericToken);\n        if (ret !== false || ![\"space\", \"hr\", \"heading\", \"code\", \"table\", \"blockquote\", \"list\", \"html\", \"paragraph\", \"text\"].includes(genericToken.type)) {\n          out += ret || \"\";\n          continue;\n        }\n      }\n      const token = anyToken;\n      switch (token.type) {\n        case \"space\":\n          {\n            out += this.renderer.space(token);\n            continue;\n          }\n        case \"hr\":\n          {\n            out += this.renderer.hr(token);\n            continue;\n          }\n        case \"heading\":\n          {\n            out += this.renderer.heading(token);\n            continue;\n          }\n        case \"code\":\n          {\n            out += this.renderer.code(token);\n            continue;\n          }\n        case \"table\":\n          {\n            out += this.renderer.table(token);\n            continue;\n          }\n        case \"blockquote\":\n          {\n            out += this.renderer.blockquote(token);\n            continue;\n          }\n        case \"list\":\n          {\n            out += this.renderer.list(token);\n            continue;\n          }\n        case \"html\":\n          {\n            out += this.renderer.html(token);\n            continue;\n          }\n        case \"paragraph\":\n          {\n            out += this.renderer.paragraph(token);\n            continue;\n          }\n        case \"text\":\n          {\n            let textToken = token;\n            let body = this.renderer.text(textToken);\n            while (i + 1 < tokens.length && tokens[i + 1].type === \"text\") {\n              textToken = tokens[++i];\n              body += \"\\n\" + this.renderer.text(textToken);\n            }\n            if (top) {\n              out += this.renderer.paragraph({\n                type: \"paragraph\",\n                raw: body,\n                text: body,\n                tokens: [{\n                  type: \"text\",\n                  raw: body,\n                  text: body,\n                  escaped: true\n                }]\n              });\n            } else {\n              out += body;\n            }\n            continue;\n          }\n        default:\n          {\n            const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n            if (this.options.silent) {\n              console.error(errMsg);\n              return \"\";\n            } else {\n              throw new Error(errMsg);\n            }\n          }\n      }\n    }\n    return out;\n  }\n  /**\n   * Parse Inline Tokens\n   */\n  parseInline(tokens, renderer = this.renderer) {\n    let out = \"\";\n    for (let i = 0; i < tokens.length; i++) {\n      const anyToken = tokens[i];\n      if (this.options.extensions?.renderers?.[anyToken.type]) {\n        const ret = this.options.extensions.renderers[anyToken.type].call({\n          parser: this\n        }, anyToken);\n        if (ret !== false || ![\"escape\", \"html\", \"link\", \"image\", \"strong\", \"em\", \"codespan\", \"br\", \"del\", \"text\"].includes(anyToken.type)) {\n          out += ret || \"\";\n          continue;\n        }\n      }\n      const token = anyToken;\n      switch (token.type) {\n        case \"escape\":\n          {\n            out += renderer.text(token);\n            break;\n          }\n        case \"html\":\n          {\n            out += renderer.html(token);\n            break;\n          }\n        case \"link\":\n          {\n            out += renderer.link(token);\n            break;\n          }\n        case \"image\":\n          {\n            out += renderer.image(token);\n            break;\n          }\n        case \"strong\":\n          {\n            out += renderer.strong(token);\n            break;\n          }\n        case \"em\":\n          {\n            out += renderer.em(token);\n            break;\n          }\n        case \"codespan\":\n          {\n            out += renderer.codespan(token);\n            break;\n          }\n        case \"br\":\n          {\n            out += renderer.br(token);\n            break;\n          }\n        case \"del\":\n          {\n            out += renderer.del(token);\n            break;\n          }\n        case \"text\":\n          {\n            out += renderer.text(token);\n            break;\n          }\n        default:\n          {\n            const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n            if (this.options.silent) {\n              console.error(errMsg);\n              return \"\";\n            } else {\n              throw new Error(errMsg);\n            }\n          }\n      }\n    }\n    return out;\n  }\n};\n\n// src/Hooks.ts\nvar _Hooks = class {\n  options;\n  block;\n  constructor(options2) {\n    this.options = options2 || _defaults;\n  }\n  static passThroughHooks = /* @__PURE__ */new Set([\"preprocess\", \"postprocess\", \"processAllTokens\"]);\n  /**\n   * Process markdown before marked\n   */\n  preprocess(markdown) {\n    return markdown;\n  }\n  /**\n   * Process HTML after marked is finished\n   */\n  postprocess(html2) {\n    return html2;\n  }\n  /**\n   * Process all tokens before walk tokens\n   */\n  processAllTokens(tokens) {\n    return tokens;\n  }\n  /**\n   * Provide function to tokenize markdown\n   */\n  provideLexer() {\n    return this.block ? _Lexer.lex : _Lexer.lexInline;\n  }\n  /**\n   * Provide function to parse tokens\n   */\n  provideParser() {\n    return this.block ? _Parser.parse : _Parser.parseInline;\n  }\n};\n\n// src/Instance.ts\nvar Marked = class {\n  defaults = _getDefaults();\n  options = this.setOptions;\n  parse = this.parseMarkdown(true);\n  parseInline = this.parseMarkdown(false);\n  Parser = _Parser;\n  Renderer = _Renderer;\n  TextRenderer = _TextRenderer;\n  Lexer = _Lexer;\n  Tokenizer = _Tokenizer;\n  Hooks = _Hooks;\n  constructor(...args) {\n    this.use(...args);\n  }\n  /**\n   * Run callback for every token\n   */\n  walkTokens(tokens, callback) {\n    let values = [];\n    for (const token of tokens) {\n      values = values.concat(callback.call(this, token));\n      switch (token.type) {\n        case \"table\":\n          {\n            const tableToken = token;\n            for (const cell of tableToken.header) {\n              values = values.concat(this.walkTokens(cell.tokens, callback));\n            }\n            for (const row of tableToken.rows) {\n              for (const cell of row) {\n                values = values.concat(this.walkTokens(cell.tokens, callback));\n              }\n            }\n            break;\n          }\n        case \"list\":\n          {\n            const listToken = token;\n            values = values.concat(this.walkTokens(listToken.items, callback));\n            break;\n          }\n        default:\n          {\n            const genericToken = token;\n            if (this.defaults.extensions?.childTokens?.[genericToken.type]) {\n              this.defaults.extensions.childTokens[genericToken.type].forEach(childTokens => {\n                const tokens2 = genericToken[childTokens].flat(Infinity);\n                values = values.concat(this.walkTokens(tokens2, callback));\n              });\n            } else if (genericToken.tokens) {\n              values = values.concat(this.walkTokens(genericToken.tokens, callback));\n            }\n          }\n      }\n    }\n    return values;\n  }\n  use(...args) {\n    const extensions = this.defaults.extensions || {\n      renderers: {},\n      childTokens: {}\n    };\n    args.forEach(pack => {\n      const opts = {\n        ...pack\n      };\n      opts.async = this.defaults.async || opts.async || false;\n      if (pack.extensions) {\n        pack.extensions.forEach(ext => {\n          if (!ext.name) {\n            throw new Error(\"extension name required\");\n          }\n          if (\"renderer\" in ext) {\n            const prevRenderer = extensions.renderers[ext.name];\n            if (prevRenderer) {\n              extensions.renderers[ext.name] = function (...args2) {\n                let ret = ext.renderer.apply(this, args2);\n                if (ret === false) {\n                  ret = prevRenderer.apply(this, args2);\n                }\n                return ret;\n              };\n            } else {\n              extensions.renderers[ext.name] = ext.renderer;\n            }\n          }\n          if (\"tokenizer\" in ext) {\n            if (!ext.level || ext.level !== \"block\" && ext.level !== \"inline\") {\n              throw new Error(\"extension level must be 'block' or 'inline'\");\n            }\n            const extLevel = extensions[ext.level];\n            if (extLevel) {\n              extLevel.unshift(ext.tokenizer);\n            } else {\n              extensions[ext.level] = [ext.tokenizer];\n            }\n            if (ext.start) {\n              if (ext.level === \"block\") {\n                if (extensions.startBlock) {\n                  extensions.startBlock.push(ext.start);\n                } else {\n                  extensions.startBlock = [ext.start];\n                }\n              } else if (ext.level === \"inline\") {\n                if (extensions.startInline) {\n                  extensions.startInline.push(ext.start);\n                } else {\n                  extensions.startInline = [ext.start];\n                }\n              }\n            }\n          }\n          if (\"childTokens\" in ext && ext.childTokens) {\n            extensions.childTokens[ext.name] = ext.childTokens;\n          }\n        });\n        opts.extensions = extensions;\n      }\n      if (pack.renderer) {\n        const renderer = this.defaults.renderer || new _Renderer(this.defaults);\n        for (const prop in pack.renderer) {\n          if (!(prop in renderer)) {\n            throw new Error(`renderer '${prop}' does not exist`);\n          }\n          if ([\"options\", \"parser\"].includes(prop)) {\n            continue;\n          }\n          const rendererProp = prop;\n          const rendererFunc = pack.renderer[rendererProp];\n          const prevRenderer = renderer[rendererProp];\n          renderer[rendererProp] = (...args2) => {\n            let ret = rendererFunc.apply(renderer, args2);\n            if (ret === false) {\n              ret = prevRenderer.apply(renderer, args2);\n            }\n            return ret || \"\";\n          };\n        }\n        opts.renderer = renderer;\n      }\n      if (pack.tokenizer) {\n        const tokenizer = this.defaults.tokenizer || new _Tokenizer(this.defaults);\n        for (const prop in pack.tokenizer) {\n          if (!(prop in tokenizer)) {\n            throw new Error(`tokenizer '${prop}' does not exist`);\n          }\n          if ([\"options\", \"rules\", \"lexer\"].includes(prop)) {\n            continue;\n          }\n          const tokenizerProp = prop;\n          const tokenizerFunc = pack.tokenizer[tokenizerProp];\n          const prevTokenizer = tokenizer[tokenizerProp];\n          tokenizer[tokenizerProp] = (...args2) => {\n            let ret = tokenizerFunc.apply(tokenizer, args2);\n            if (ret === false) {\n              ret = prevTokenizer.apply(tokenizer, args2);\n            }\n            return ret;\n          };\n        }\n        opts.tokenizer = tokenizer;\n      }\n      if (pack.hooks) {\n        const hooks = this.defaults.hooks || new _Hooks();\n        for (const prop in pack.hooks) {\n          if (!(prop in hooks)) {\n            throw new Error(`hook '${prop}' does not exist`);\n          }\n          if ([\"options\", \"block\"].includes(prop)) {\n            continue;\n          }\n          const hooksProp = prop;\n          const hooksFunc = pack.hooks[hooksProp];\n          const prevHook = hooks[hooksProp];\n          if (_Hooks.passThroughHooks.has(prop)) {\n            hooks[hooksProp] = arg => {\n              if (this.defaults.async) {\n                return Promise.resolve(hooksFunc.call(hooks, arg)).then(ret2 => {\n                  return prevHook.call(hooks, ret2);\n                });\n              }\n              const ret = hooksFunc.call(hooks, arg);\n              return prevHook.call(hooks, ret);\n            };\n          } else {\n            hooks[hooksProp] = (...args2) => {\n              let ret = hooksFunc.apply(hooks, args2);\n              if (ret === false) {\n                ret = prevHook.apply(hooks, args2);\n              }\n              return ret;\n            };\n          }\n        }\n        opts.hooks = hooks;\n      }\n      if (pack.walkTokens) {\n        const walkTokens2 = this.defaults.walkTokens;\n        const packWalktokens = pack.walkTokens;\n        opts.walkTokens = function (token) {\n          let values = [];\n          values.push(packWalktokens.call(this, token));\n          if (walkTokens2) {\n            values = values.concat(walkTokens2.call(this, token));\n          }\n          return values;\n        };\n      }\n      this.defaults = {\n        ...this.defaults,\n        ...opts\n      };\n    });\n    return this;\n  }\n  setOptions(opt) {\n    this.defaults = {\n      ...this.defaults,\n      ...opt\n    };\n    return this;\n  }\n  lexer(src, options2) {\n    return _Lexer.lex(src, options2 ?? this.defaults);\n  }\n  parser(tokens, options2) {\n    return _Parser.parse(tokens, options2 ?? this.defaults);\n  }\n  parseMarkdown(blockType) {\n    const parse2 = (src, options2) => {\n      const origOpt = {\n        ...options2\n      };\n      const opt = {\n        ...this.defaults,\n        ...origOpt\n      };\n      const throwError = this.onError(!!opt.silent, !!opt.async);\n      if (this.defaults.async === true && origOpt.async === false) {\n        return throwError(new Error(\"marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise.\"));\n      }\n      if (typeof src === \"undefined\" || src === null) {\n        return throwError(new Error(\"marked(): input parameter is undefined or null\"));\n      }\n      if (typeof src !== \"string\") {\n        return throwError(new Error(\"marked(): input parameter is of type \" + Object.prototype.toString.call(src) + \", string expected\"));\n      }\n      if (opt.hooks) {\n        opt.hooks.options = opt;\n        opt.hooks.block = blockType;\n      }\n      const lexer2 = opt.hooks ? opt.hooks.provideLexer() : blockType ? _Lexer.lex : _Lexer.lexInline;\n      const parser2 = opt.hooks ? opt.hooks.provideParser() : blockType ? _Parser.parse : _Parser.parseInline;\n      if (opt.async) {\n        return Promise.resolve(opt.hooks ? opt.hooks.preprocess(src) : src).then(src2 => lexer2(src2, opt)).then(tokens => opt.hooks ? opt.hooks.processAllTokens(tokens) : tokens).then(tokens => opt.walkTokens ? Promise.all(this.walkTokens(tokens, opt.walkTokens)).then(() => tokens) : tokens).then(tokens => parser2(tokens, opt)).then(html2 => opt.hooks ? opt.hooks.postprocess(html2) : html2).catch(throwError);\n      }\n      try {\n        if (opt.hooks) {\n          src = opt.hooks.preprocess(src);\n        }\n        let tokens = lexer2(src, opt);\n        if (opt.hooks) {\n          tokens = opt.hooks.processAllTokens(tokens);\n        }\n        if (opt.walkTokens) {\n          this.walkTokens(tokens, opt.walkTokens);\n        }\n        let html2 = parser2(tokens, opt);\n        if (opt.hooks) {\n          html2 = opt.hooks.postprocess(html2);\n        }\n        return html2;\n      } catch (e) {\n        return throwError(e);\n      }\n    };\n    return parse2;\n  }\n  onError(silent, async) {\n    return e => {\n      e.message += \"\\nPlease report this to https://github.com/markedjs/marked.\";\n      if (silent) {\n        const msg = \"<p>An error occurred:</p><pre>\" + escape2(e.message + \"\", true) + \"</pre>\";\n        if (async) {\n          return Promise.resolve(msg);\n        }\n        return msg;\n      }\n      if (async) {\n        return Promise.reject(e);\n      }\n      throw e;\n    };\n  }\n};\n\n// src/marked.ts\nvar markedInstance = new Marked();\nfunction marked(src, opt) {\n  return markedInstance.parse(src, opt);\n}\nmarked.options = marked.setOptions = function (options2) {\n  markedInstance.setOptions(options2);\n  marked.defaults = markedInstance.defaults;\n  changeDefaults(marked.defaults);\n  return marked;\n};\nmarked.getDefaults = _getDefaults;\nmarked.defaults = _defaults;\nmarked.use = function (...args) {\n  markedInstance.use(...args);\n  marked.defaults = markedInstance.defaults;\n  changeDefaults(marked.defaults);\n  return marked;\n};\nmarked.walkTokens = function (tokens, callback) {\n  return markedInstance.walkTokens(tokens, callback);\n};\nmarked.parseInline = markedInstance.parseInline;\nmarked.Parser = _Parser;\nmarked.parser = _Parser.parse;\nmarked.Renderer = _Renderer;\nmarked.TextRenderer = _TextRenderer;\nmarked.Lexer = _Lexer;\nmarked.lexer = _Lexer.lex;\nmarked.Tokenizer = _Tokenizer;\nmarked.Hooks = _Hooks;\nmarked.parse = marked;\nvar options = marked.options;\nvar setOptions = marked.setOptions;\nvar use = marked.use;\nvar walkTokens = marked.walkTokens;\nvar parseInline = marked.parseInline;\nvar parse = marked;\nvar parser = _Parser.parse;\nvar lexer = _Lexer.lex;\nexport { _Hooks as Hooks, _Lexer as Lexer, Marked, _Parser as Parser, _Renderer as Renderer, _TextRenderer as TextRenderer, _Tokenizer as Tokenizer, _defaults as defaults, _getDefaults as getDefaults, lexer, marked, options, parse, parseInline, parser, setOptions, use, walkTokens };\n", "import { decodeEntities } from \"./chunk-O4NI6UNU.mjs\";\nimport { __name, common_default, getConfig2 as getConfig, hasKatex, log, renderKatex } from \"./chunk-YTJNT7DU.mjs\";\n\n// src/rendering-util/createText.ts\nimport { select } from \"d3\";\n\n// src/rendering-util/handle-markdown-text.ts\nimport { marked } from \"marked\";\nimport { dedent } from \"ts-dedent\";\nfunction preprocessMarkdown(markdown, {\n  markdownAutoWrap\n}) {\n  const withoutBR = markdown.replace(/<br\\/>/g, \"\\n\");\n  const withoutMultipleNewlines = withoutBR.replace(/\\n{2,}/g, \"\\n\");\n  const withoutExtraSpaces = dedent(withoutMultipleNewlines);\n  if (markdownAutoWrap === false) {\n    return withoutExtraSpaces.replace(/ /g, \"&nbsp;\");\n  }\n  return withoutExtraSpaces;\n}\n__name(preprocessMarkdown, \"preprocessMarkdown\");\nfunction markdownToLines(markdown, config = {}) {\n  const preprocessedMarkdown = preprocessMarkdown(markdown, config);\n  const nodes = marked.lexer(preprocessedMarkdown);\n  const lines = [[]];\n  let currentLine = 0;\n  function processNode(node, parentType = \"normal\") {\n    if (node.type === \"text\") {\n      const textLines = node.text.split(\"\\n\");\n      textLines.forEach((textLine, index) => {\n        if (index !== 0) {\n          currentLine++;\n          lines.push([]);\n        }\n        textLine.split(\" \").forEach(word => {\n          word = word.replace(/&#39;/g, `'`);\n          if (word) {\n            lines[currentLine].push({\n              content: word,\n              type: parentType\n            });\n          }\n        });\n      });\n    } else if (node.type === \"strong\" || node.type === \"em\") {\n      node.tokens.forEach(contentNode => {\n        processNode(contentNode, node.type);\n      });\n    } else if (node.type === \"html\") {\n      lines[currentLine].push({\n        content: node.text,\n        type: \"normal\"\n      });\n    }\n  }\n  __name(processNode, \"processNode\");\n  nodes.forEach(treeNode => {\n    if (treeNode.type === \"paragraph\") {\n      treeNode.tokens?.forEach(contentNode => {\n        processNode(contentNode);\n      });\n    } else if (treeNode.type === \"html\") {\n      lines[currentLine].push({\n        content: treeNode.text,\n        type: \"normal\"\n      });\n    }\n  });\n  return lines;\n}\n__name(markdownToLines, \"markdownToLines\");\nfunction markdownToHTML(markdown, {\n  markdownAutoWrap\n} = {}) {\n  const nodes = marked.lexer(markdown);\n  function output(node) {\n    if (node.type === \"text\") {\n      if (markdownAutoWrap === false) {\n        return node.text.replace(/\\n */g, \"<br/>\").replace(/ /g, \"&nbsp;\");\n      }\n      return node.text.replace(/\\n */g, \"<br/>\");\n    } else if (node.type === \"strong\") {\n      return `<strong>${node.tokens?.map(output).join(\"\")}</strong>`;\n    } else if (node.type === \"em\") {\n      return `<em>${node.tokens?.map(output).join(\"\")}</em>`;\n    } else if (node.type === \"paragraph\") {\n      return `<p>${node.tokens?.map(output).join(\"\")}</p>`;\n    } else if (node.type === \"space\") {\n      return \"\";\n    } else if (node.type === \"html\") {\n      return `${node.text}`;\n    } else if (node.type === \"escape\") {\n      return node.text;\n    }\n    return `Unsupported markdown: ${node.type}`;\n  }\n  __name(output, \"output\");\n  return nodes.map(output).join(\"\");\n}\n__name(markdownToHTML, \"markdownToHTML\");\n\n// src/rendering-util/splitText.ts\nfunction splitTextToChars(text) {\n  if (Intl.Segmenter) {\n    return [...new Intl.Segmenter().segment(text)].map(s => s.segment);\n  }\n  return [...text];\n}\n__name(splitTextToChars, \"splitTextToChars\");\nfunction splitWordToFitWidth(checkFit, word) {\n  const characters = splitTextToChars(word.content);\n  return splitWordToFitWidthRecursion(checkFit, [], characters, word.type);\n}\n__name(splitWordToFitWidth, \"splitWordToFitWidth\");\nfunction splitWordToFitWidthRecursion(checkFit, usedChars, remainingChars, type) {\n  if (remainingChars.length === 0) {\n    return [{\n      content: usedChars.join(\"\"),\n      type\n    }, {\n      content: \"\",\n      type\n    }];\n  }\n  const [nextChar, ...rest] = remainingChars;\n  const newWord = [...usedChars, nextChar];\n  if (checkFit([{\n    content: newWord.join(\"\"),\n    type\n  }])) {\n    return splitWordToFitWidthRecursion(checkFit, newWord, rest, type);\n  }\n  if (usedChars.length === 0 && nextChar) {\n    usedChars.push(nextChar);\n    remainingChars.shift();\n  }\n  return [{\n    content: usedChars.join(\"\"),\n    type\n  }, {\n    content: remainingChars.join(\"\"),\n    type\n  }];\n}\n__name(splitWordToFitWidthRecursion, \"splitWordToFitWidthRecursion\");\nfunction splitLineToFitWidth(line, checkFit) {\n  if (line.some(({\n    content\n  }) => content.includes(\"\\n\"))) {\n    throw new Error(\"splitLineToFitWidth does not support newlines in the line\");\n  }\n  return splitLineToFitWidthRecursion(line, checkFit);\n}\n__name(splitLineToFitWidth, \"splitLineToFitWidth\");\nfunction splitLineToFitWidthRecursion(words, checkFit, lines = [], newLine = []) {\n  if (words.length === 0) {\n    if (newLine.length > 0) {\n      lines.push(newLine);\n    }\n    return lines.length > 0 ? lines : [];\n  }\n  let joiner = \"\";\n  if (words[0].content === \" \") {\n    joiner = \" \";\n    words.shift();\n  }\n  const nextWord = words.shift() ?? {\n    content: \" \",\n    type: \"normal\"\n  };\n  const lineWithNextWord = [...newLine];\n  if (joiner !== \"\") {\n    lineWithNextWord.push({\n      content: joiner,\n      type: \"normal\"\n    });\n  }\n  lineWithNextWord.push(nextWord);\n  if (checkFit(lineWithNextWord)) {\n    return splitLineToFitWidthRecursion(words, checkFit, lines, lineWithNextWord);\n  }\n  if (newLine.length > 0) {\n    lines.push(newLine);\n    words.unshift(nextWord);\n  } else if (nextWord.content) {\n    const [line, rest] = splitWordToFitWidth(checkFit, nextWord);\n    lines.push([line]);\n    if (rest.content) {\n      words.unshift(rest);\n    }\n  }\n  return splitLineToFitWidthRecursion(words, checkFit, lines);\n}\n__name(splitLineToFitWidthRecursion, \"splitLineToFitWidthRecursion\");\n\n// src/rendering-util/createText.ts\nfunction applyStyle(dom, styleFn) {\n  if (styleFn) {\n    dom.attr(\"style\", styleFn);\n  }\n}\n__name(applyStyle, \"applyStyle\");\nasync function addHtmlSpan(element, node, width, classes, addBackground = false) {\n  const fo = element.append(\"foreignObject\");\n  fo.attr(\"width\", `${10 * width}px`);\n  fo.attr(\"height\", `${10 * width}px`);\n  const div = fo.append(\"xhtml:div\");\n  let label = node.label;\n  if (node.label && hasKatex(node.label)) {\n    label = await renderKatex(node.label.replace(common_default.lineBreakRegex, \"\\n\"), getConfig());\n  }\n  const labelClass = node.isNode ? \"nodeLabel\" : \"edgeLabel\";\n  const span = div.append(\"span\");\n  span.html(label);\n  applyStyle(span, node.labelStyle);\n  span.attr(\"class\", `${labelClass} ${classes}`);\n  applyStyle(div, node.labelStyle);\n  div.style(\"display\", \"table-cell\");\n  div.style(\"white-space\", \"nowrap\");\n  div.style(\"line-height\", \"1.5\");\n  div.style(\"max-width\", width + \"px\");\n  div.style(\"text-align\", \"center\");\n  div.attr(\"xmlns\", \"http://www.w3.org/1999/xhtml\");\n  if (addBackground) {\n    div.attr(\"class\", \"labelBkg\");\n  }\n  let bbox = div.node().getBoundingClientRect();\n  if (bbox.width === width) {\n    div.style(\"display\", \"table\");\n    div.style(\"white-space\", \"break-spaces\");\n    div.style(\"width\", width + \"px\");\n    bbox = div.node().getBoundingClientRect();\n  }\n  return fo.node();\n}\n__name(addHtmlSpan, \"addHtmlSpan\");\nfunction createTspan(textElement, lineIndex, lineHeight) {\n  return textElement.append(\"tspan\").attr(\"class\", \"text-outer-tspan\").attr(\"x\", 0).attr(\"y\", lineIndex * lineHeight - 0.1 + \"em\").attr(\"dy\", lineHeight + \"em\");\n}\n__name(createTspan, \"createTspan\");\nfunction computeWidthOfText(parentNode, lineHeight, line) {\n  const testElement = parentNode.append(\"text\");\n  const testSpan = createTspan(testElement, 1, lineHeight);\n  updateTextContentAndStyles(testSpan, line);\n  const textLength = testSpan.node().getComputedTextLength();\n  testElement.remove();\n  return textLength;\n}\n__name(computeWidthOfText, \"computeWidthOfText\");\nfunction computeDimensionOfText(parentNode, lineHeight, text) {\n  const testElement = parentNode.append(\"text\");\n  const testSpan = createTspan(testElement, 1, lineHeight);\n  updateTextContentAndStyles(testSpan, [{\n    content: text,\n    type: \"normal\"\n  }]);\n  const textDimension = testSpan.node()?.getBoundingClientRect();\n  if (textDimension) {\n    testElement.remove();\n  }\n  return textDimension;\n}\n__name(computeDimensionOfText, \"computeDimensionOfText\");\nfunction createFormattedText(width, g, structuredText, addBackground = false) {\n  const lineHeight = 1.1;\n  const labelGroup = g.append(\"g\");\n  const bkg = labelGroup.insert(\"rect\").attr(\"class\", \"background\").attr(\"style\", \"stroke: none\");\n  const textElement = labelGroup.append(\"text\").attr(\"y\", \"-10.1\");\n  let lineIndex = 0;\n  for (const line of structuredText) {\n    const checkWidth = /* @__PURE__ */__name(line2 => computeWidthOfText(labelGroup, lineHeight, line2) <= width, \"checkWidth\");\n    const linesUnderWidth = checkWidth(line) ? [line] : splitLineToFitWidth(line, checkWidth);\n    for (const preparedLine of linesUnderWidth) {\n      const tspan = createTspan(textElement, lineIndex, lineHeight);\n      updateTextContentAndStyles(tspan, preparedLine);\n      lineIndex++;\n    }\n  }\n  if (addBackground) {\n    const bbox = textElement.node().getBBox();\n    const padding = 2;\n    bkg.attr(\"x\", bbox.x - padding).attr(\"y\", bbox.y - padding).attr(\"width\", bbox.width + 2 * padding).attr(\"height\", bbox.height + 2 * padding);\n    return labelGroup.node();\n  } else {\n    return textElement.node();\n  }\n}\n__name(createFormattedText, \"createFormattedText\");\nfunction updateTextContentAndStyles(tspan, wrappedLine) {\n  tspan.text(\"\");\n  wrappedLine.forEach((word, index) => {\n    const innerTspan = tspan.append(\"tspan\").attr(\"font-style\", word.type === \"em\" ? \"italic\" : \"normal\").attr(\"class\", \"text-inner-tspan\").attr(\"font-weight\", word.type === \"strong\" ? \"bold\" : \"normal\");\n    if (index === 0) {\n      innerTspan.text(word.content);\n    } else {\n      innerTspan.text(\" \" + word.content);\n    }\n  });\n}\n__name(updateTextContentAndStyles, \"updateTextContentAndStyles\");\nfunction replaceIconSubstring(text) {\n  return text.replace(/fa[bklrs]?:fa-[\\w-]+/g,\n  // cspell: disable-line\n  s => `<i class='${s.replace(\":\", \" \")}'></i>`);\n}\n__name(replaceIconSubstring, \"replaceIconSubstring\");\nvar createText = /* @__PURE__ */__name(async (el, text = \"\", {\n  style = \"\",\n  isTitle = false,\n  classes = \"\",\n  useHtmlLabels = true,\n  isNode = true,\n  width = 200,\n  addSvgBackground = false\n} = {}, config) => {\n  log.debug(\"XYZ createText\", text, style, isTitle, classes, useHtmlLabels, isNode, \"addSvgBackground: \", addSvgBackground);\n  if (useHtmlLabels) {\n    const htmlText = markdownToHTML(text, config);\n    const decodedReplacedText = replaceIconSubstring(decodeEntities(htmlText));\n    const inputForKatex = text.replace(/\\\\\\\\/g, \"\\\\\");\n    const node = {\n      isNode,\n      label: hasKatex(text) ? inputForKatex : decodedReplacedText,\n      labelStyle: style.replace(\"fill:\", \"color:\")\n    };\n    const vertexNode = await addHtmlSpan(el, node, width, classes, addSvgBackground);\n    return vertexNode;\n  } else {\n    const sanitizeBR = text.replace(/<br\\s*\\/?>/g, \"<br/>\");\n    const structuredText = markdownToLines(sanitizeBR.replace(\"<br>\", \"<br/>\"), config);\n    const svgLabel = createFormattedText(width, el, structuredText, text ? addSvgBackground : false);\n    if (isNode) {\n      if (/stroke:/.exec(style)) {\n        style = style.replace(\"stroke:\", \"lineColor:\");\n      }\n      const nodeLabelTextStyle = style.replace(/stroke:[^;]+;?/g, \"\").replace(/stroke-width:[^;]+;?/g, \"\").replace(/fill:[^;]+;?/g, \"\").replace(/color:/g, \"fill:\");\n      select(svgLabel).attr(\"style\", nodeLabelTextStyle);\n    } else {\n      const edgeLabelRectStyle = style.replace(/stroke:[^;]+;?/g, \"\").replace(/stroke-width:[^;]+;?/g, \"\").replace(/fill:[^;]+;?/g, \"\").replace(/background:/g, \"fill:\");\n      select(svgLabel).select(\"rect\").attr(\"style\", edgeLabelRectStyle.replace(/background:/g, \"fill:\"));\n      const edgeLabelTextStyle = style.replace(/stroke:[^;]+;?/g, \"\").replace(/stroke-width:[^;]+;?/g, \"\").replace(/fill:[^;]+;?/g, \"\").replace(/color:/g, \"fill:\");\n      select(svgLabel).select(\"text\").attr(\"style\", edgeLabelTextStyle);\n    }\n    return svgLabel;\n  }\n}, \"createText\");\nexport { computeDimensionOfText, replaceIconSubstring, createText };"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAO,SAAS,OAAO,OAAO;AAC5B,MAAI,SAAS,CAAC;AACd,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,WAAO,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,EAC/B;AACA,MAAI,UAAU,MAAM,KAAK,OAAO,UAAU,WAAW,CAAC,KAAK,IAAI,KAAK;AACpE,UAAQ,QAAQ,SAAS,CAAC,IAAI,QAAQ,QAAQ,SAAS,CAAC,EAAE,QAAQ,kBAAkB,EAAE;AACtF,MAAI,gBAAgB,QAAQ,OAAO,SAAU,KAAK,KAAK;AACrD,QAAI,UAAU,IAAI,MAAM,qBAAqB;AAC7C,QAAI,SAAS;AACX,aAAO,IAAI,OAAO,QAAQ,IAAI,SAAU,OAAO;AAC7C,YAAI,IAAI;AACR,gBAAQ,MAAM,KAAK,MAAM,MAAM,QAAQ,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,QAAQ,OAAO,SAAS,KAAK;AAAA,MAC7H,CAAC,CAAC;AAAA,IACJ;AACA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACL,MAAI,cAAc,QAAQ;AACxB,QAAI,YAAY,IAAI,OAAO,YAAa,KAAK,IAAI,MAAM,MAAM,aAAa,IAAI,KAAK,GAAG;AACtF,cAAU,QAAQ,IAAI,SAAU,KAAK;AACnC,aAAO,IAAI,QAAQ,WAAW,IAAI;AAAA,IACpC,CAAC;AAAA,EACH;AACA,UAAQ,CAAC,IAAI,QAAQ,CAAC,EAAE,QAAQ,UAAU,EAAE;AAC5C,MAAI,SAAS,QAAQ,CAAC;AACtB,SAAO,QAAQ,SAAU,OAAO,GAAG;AACjC,QAAI,eAAe,OAAO,MAAM,eAAe;AAC/C,QAAI,cAAc,eAAe,aAAa,CAAC,IAAI;AACnD,QAAI,gBAAgB;AACpB,QAAI,OAAO,UAAU,YAAY,MAAM,SAAS,IAAI,GAAG;AACrD,sBAAgB,OAAO,KAAK,EAAE,MAAM,IAAI,EAAE,IAAI,SAAU,KAAKA,IAAG;AAC9D,eAAOA,OAAM,IAAI,MAAM,KAAK,cAAc;AAAA,MAC5C,CAAC,EAAE,KAAK,IAAI;AAAA,IACd;AACA,cAAU,gBAAgB,QAAQ,IAAI,CAAC;AAAA,EACzC,CAAC;AACD,SAAO;AACT;;;ACzBA,SAAS,eAAe;AACtB,SAAO;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,KAAK;AAAA,IACL,OAAO;AAAA,IACP,UAAU;AAAA,IACV,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,YAAY;AAAA,EACd;AACF;AACA,IAAI,YAAY,aAAa;AAC7B,SAAS,eAAe,aAAa;AACnC,cAAY;AACd;AAGA,IAAI,WAAW;AAAA,EACb,MAAM,MAAM;AACd;AACA,SAAS,KAAK,OAAO,MAAM,IAAI;AAC7B,MAAI,SAAS,OAAO,UAAU,WAAW,QAAQ,MAAM;AACvD,QAAM,MAAM;AAAA,IACV,SAAS,CAAC,MAAM,QAAQ;AACtB,UAAI,YAAY,OAAO,QAAQ,WAAW,MAAM,IAAI;AACpD,kBAAY,UAAU,QAAQ,MAAM,OAAO,IAAI;AAC/C,eAAS,OAAO,QAAQ,MAAM,SAAS;AACvC,aAAO;AAAA,IACT;AAAA,IACA,UAAU,MAAM;AACd,aAAO,IAAI,OAAO,QAAQ,GAAG;AAAA,IAC/B;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAI,QAAQ;AAAA,EACV,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,qBAAqB;AAAA,EACrB,WAAW;AAAA,EACX,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,iBAAiB;AAAA,EACjB,oBAAoB;AAAA,EACpB,YAAY;AAAA,EACZ,iBAAiB;AAAA,EACjB,SAAS;AAAA,EACT,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,gBAAgB;AAAA,EAChB,WAAW;AAAA,EACX,SAAS;AAAA,EACT,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,cAAc;AAAA,EACd,OAAO;AAAA,EACP,eAAe;AAAA,EACf,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,WAAW;AAAA,EACX,eAAe;AAAA,EACf,eAAe;AAAA,EACf,eAAe,UAAQ,IAAI,OAAO,WAAW,IAAI,8BAA8B;AAAA,EAC/E,iBAAiB,YAAU,IAAI,OAAO,QAAQ,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC,oDAAoD;AAAA,EACzH,SAAS,YAAU,IAAI,OAAO,QAAQ,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC,oDAAoD;AAAA,EACjH,kBAAkB,YAAU,IAAI,OAAO,QAAQ,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC,iBAAiB;AAAA,EACvF,mBAAmB,YAAU,IAAI,OAAO,QAAQ,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC,IAAI;AAAA,EAC3E,gBAAgB,YAAU,IAAI,OAAO,QAAQ,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC,sBAAsB,GAAG;AAC/F;AACA,IAAI,UAAU;AACd,IAAI,YAAY;AAChB,IAAI,SAAS;AACb,IAAI,KAAK;AACT,IAAI,UAAU;AACd,IAAI,SAAS;AACb,IAAI,eAAe;AACnB,IAAI,WAAW,KAAK,YAAY,EAAE,QAAQ,SAAS,MAAM,EAAE,QAAQ,cAAc,mBAAmB,EAAE,QAAQ,WAAW,uBAAuB,EAAE,QAAQ,eAAe,SAAS,EAAE,QAAQ,YAAY,cAAc,EAAE,QAAQ,SAAS,mBAAmB,EAAE,QAAQ,YAAY,EAAE,EAAE,SAAS;AAC/R,IAAI,cAAc,KAAK,YAAY,EAAE,QAAQ,SAAS,MAAM,EAAE,QAAQ,cAAc,mBAAmB,EAAE,QAAQ,WAAW,uBAAuB,EAAE,QAAQ,eAAe,SAAS,EAAE,QAAQ,YAAY,cAAc,EAAE,QAAQ,SAAS,mBAAmB,EAAE,QAAQ,UAAU,mCAAmC,EAAE,SAAS;AACjU,IAAI,aAAa;AACjB,IAAI,YAAY;AAChB,IAAI,cAAc;AAClB,IAAI,MAAM,KAAK,6GAA6G,EAAE,QAAQ,SAAS,WAAW,EAAE,QAAQ,SAAS,8DAA8D,EAAE,SAAS;AACtP,IAAI,OAAO,KAAK,sCAAsC,EAAE,QAAQ,SAAS,MAAM,EAAE,SAAS;AAC1F,IAAI,OAAO;AACX,IAAI,WAAW;AACf,IAAI,OAAO,KAAK,6dAA6d,GAAG,EAAE,QAAQ,WAAW,QAAQ,EAAE,QAAQ,OAAO,IAAI,EAAE,QAAQ,aAAa,0EAA0E,EAAE,SAAS;AAC9oB,IAAI,YAAY,KAAK,UAAU,EAAE,QAAQ,MAAM,EAAE,EAAE,QAAQ,WAAW,uBAAuB,EAAE,QAAQ,aAAa,EAAE,EAAE,QAAQ,UAAU,EAAE,EAAE,QAAQ,cAAc,SAAS,EAAE,QAAQ,UAAU,gDAAgD,EAAE,QAAQ,QAAQ,wBAAwB,EAAE,QAAQ,QAAQ,6DAA6D,EAAE,QAAQ,OAAO,IAAI,EAAE,SAAS;AAC1Y,IAAI,aAAa,KAAK,yCAAyC,EAAE,QAAQ,aAAa,SAAS,EAAE,SAAS;AAC1G,IAAI,cAAc;AAAA,EAChB;AAAA,EACA,MAAM;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,OAAO;AAAA,EACP,MAAM;AACR;AACA,IAAI,WAAW,KAAK,6JAA6J,EAAE,QAAQ,MAAM,EAAE,EAAE,QAAQ,WAAW,uBAAuB,EAAE,QAAQ,cAAc,SAAS,EAAE,QAAQ,QAAQ,wBAAwB,EAAE,QAAQ,UAAU,gDAAgD,EAAE,QAAQ,QAAQ,wBAAwB,EAAE,QAAQ,QAAQ,6DAA6D,EAAE,QAAQ,OAAO,IAAI,EAAE,SAAS;AACvhB,IAAI,WAAW,iCACV,cADU;AAAA,EAEb,UAAU;AAAA,EACV,OAAO;AAAA,EACP,WAAW,KAAK,UAAU,EAAE,QAAQ,MAAM,EAAE,EAAE,QAAQ,WAAW,uBAAuB,EAAE,QAAQ,aAAa,EAAE,EAAE,QAAQ,SAAS,QAAQ,EAAE,QAAQ,cAAc,SAAS,EAAE,QAAQ,UAAU,gDAAgD,EAAE,QAAQ,QAAQ,wBAAwB,EAAE,QAAQ,QAAQ,6DAA6D,EAAE,QAAQ,OAAO,IAAI,EAAE,SAAS;AAC5Y;AACA,IAAI,gBAAgB,iCACf,cADe;AAAA,EAElB,MAAM,KAAK,wIAAwI,EAAE,QAAQ,WAAW,QAAQ,EAAE,QAAQ,QAAQ,mKAAmK,EAAE,SAAS;AAAA,EAChX,KAAK;AAAA,EACL,SAAS;AAAA,EACT,QAAQ;AAAA;AAAA,EAER,UAAU;AAAA,EACV,WAAW,KAAK,UAAU,EAAE,QAAQ,MAAM,EAAE,EAAE,QAAQ,WAAW,iBAAiB,EAAE,QAAQ,YAAY,QAAQ,EAAE,QAAQ,UAAU,EAAE,EAAE,QAAQ,cAAc,SAAS,EAAE,QAAQ,WAAW,EAAE,EAAE,QAAQ,SAAS,EAAE,EAAE,QAAQ,SAAS,EAAE,EAAE,QAAQ,QAAQ,EAAE,EAAE,SAAS;AACzQ;AACA,IAAI,SAAS;AACb,IAAI,aAAa;AACjB,IAAI,KAAK;AACT,IAAI,aAAa;AACjB,IAAI,eAAe;AACnB,IAAI,sBAAsB;AAC1B,IAAI,yBAAyB;AAC7B,IAAI,cAAc,KAAK,yBAAyB,GAAG,EAAE,QAAQ,eAAe,mBAAmB,EAAE,SAAS;AAC1G,IAAI,0BAA0B;AAC9B,IAAI,iCAAiC;AACrC,IAAI,oCAAoC;AACxC,IAAI,YAAY;AAChB,IAAI,qBAAqB;AACzB,IAAI,iBAAiB,KAAK,oBAAoB,GAAG,EAAE,QAAQ,UAAU,YAAY,EAAE,SAAS;AAC5F,IAAI,oBAAoB,KAAK,oBAAoB,GAAG,EAAE,QAAQ,UAAU,uBAAuB,EAAE,SAAS;AAC1G,IAAI,wBAAwB;AAC5B,IAAI,oBAAoB,KAAK,uBAAuB,IAAI,EAAE,QAAQ,kBAAkB,sBAAsB,EAAE,QAAQ,eAAe,mBAAmB,EAAE,QAAQ,UAAU,YAAY,EAAE,SAAS;AACjM,IAAI,uBAAuB,KAAK,uBAAuB,IAAI,EAAE,QAAQ,kBAAkB,iCAAiC,EAAE,QAAQ,eAAe,8BAA8B,EAAE,QAAQ,UAAU,uBAAuB,EAAE,SAAS;AACrO,IAAI,oBAAoB,KAAK,oNAAoN,IAAI,EAAE,QAAQ,kBAAkB,sBAAsB,EAAE,QAAQ,eAAe,mBAAmB,EAAE,QAAQ,UAAU,YAAY,EAAE,SAAS;AAC9X,IAAI,iBAAiB,KAAK,aAAa,IAAI,EAAE,QAAQ,UAAU,YAAY,EAAE,SAAS;AACtF,IAAI,WAAW,KAAK,qCAAqC,EAAE,QAAQ,UAAU,8BAA8B,EAAE,QAAQ,SAAS,8IAA8I,EAAE,SAAS;AACvR,IAAI,iBAAiB,KAAK,QAAQ,EAAE,QAAQ,aAAa,KAAK,EAAE,SAAS;AACzE,IAAI,MAAM,KAAK,0JAA0J,EAAE,QAAQ,WAAW,cAAc,EAAE,QAAQ,aAAa,6EAA6E,EAAE,SAAS;AAC3T,IAAI,eAAe;AACnB,IAAI,OAAO,KAAK,mEAAmE,EAAE,QAAQ,SAAS,YAAY,EAAE,QAAQ,QAAQ,yCAAyC,EAAE,QAAQ,SAAS,6DAA6D,EAAE,SAAS;AACxQ,IAAI,UAAU,KAAK,yBAAyB,EAAE,QAAQ,SAAS,YAAY,EAAE,QAAQ,OAAO,WAAW,EAAE,SAAS;AAClH,IAAI,SAAS,KAAK,uBAAuB,EAAE,QAAQ,OAAO,WAAW,EAAE,SAAS;AAChF,IAAI,gBAAgB,KAAK,yBAAyB,GAAG,EAAE,QAAQ,WAAW,OAAO,EAAE,QAAQ,UAAU,MAAM,EAAE,SAAS;AACtH,IAAI,eAAe;AAAA,EACjB,YAAY;AAAA;AAAA,EAEZ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,MAAM;AAAA,EACN,KAAK;AAAA,EACL;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,MAAM;AAAA,EACN,KAAK;AACP;AACA,IAAI,iBAAiB,iCAChB,eADgB;AAAA,EAEnB,MAAM,KAAK,yBAAyB,EAAE,QAAQ,SAAS,YAAY,EAAE,SAAS;AAAA,EAC9E,SAAS,KAAK,+BAA+B,EAAE,QAAQ,SAAS,YAAY,EAAE,SAAS;AACzF;AACA,IAAI,YAAY,iCACX,eADW;AAAA,EAEd,mBAAmB;AAAA,EACnB,gBAAgB;AAAA,EAChB,KAAK,KAAK,oEAAoE,GAAG,EAAE,QAAQ,SAAS,2EAA2E,EAAE,SAAS;AAAA,EAC1L,YAAY;AAAA,EACZ,KAAK;AAAA,EACL,MAAM;AACR;AACA,IAAI,eAAe,iCACd,YADc;AAAA,EAEjB,IAAI,KAAK,EAAE,EAAE,QAAQ,QAAQ,GAAG,EAAE,SAAS;AAAA,EAC3C,MAAM,KAAK,UAAU,IAAI,EAAE,QAAQ,QAAQ,eAAe,EAAE,QAAQ,WAAW,GAAG,EAAE,SAAS;AAC/F;AACA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,UAAU;AACZ;AACA,IAAI,SAAS;AAAA,EACX,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,UAAU;AACZ;AAGA,IAAI,qBAAqB;AAAA,EACvB,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AACP;AACA,IAAI,uBAAuB,QAAM,mBAAmB,EAAE;AACtD,SAAS,QAAQ,OAAO,QAAQ;AAC9B,MAAI,QAAQ;AACV,QAAI,MAAM,WAAW,KAAK,KAAK,GAAG;AAChC,aAAO,MAAM,QAAQ,MAAM,eAAe,oBAAoB;AAAA,IAChE;AAAA,EACF,OAAO;AACL,QAAI,MAAM,mBAAmB,KAAK,KAAK,GAAG;AACxC,aAAO,MAAM,QAAQ,MAAM,uBAAuB,oBAAoB;AAAA,IACxE;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,SAAS,MAAM;AACtB,MAAI;AACF,WAAO,UAAU,IAAI,EAAE,QAAQ,MAAM,eAAe,GAAG;AAAA,EACzD,QAAQ;AACN,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,WAAW,UAAU,OAAO;AACnC,QAAM,MAAM,SAAS,QAAQ,MAAM,UAAU,CAAC,OAAO,QAAQ,QAAQ;AACjE,QAAI,UAAU;AACd,QAAI,OAAO;AACX,WAAO,EAAE,QAAQ,KAAK,IAAI,IAAI,MAAM,KAAM,WAAU,CAAC;AACrD,QAAI,SAAS;AACX,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF,CAAC,GACD,QAAQ,IAAI,MAAM,MAAM,SAAS;AACnC,MAAI,IAAI;AACR,MAAI,CAAC,MAAM,CAAC,EAAE,KAAK,GAAG;AACpB,UAAM,MAAM;AAAA,EACd;AACA,MAAI,MAAM,SAAS,KAAK,CAAC,MAAM,GAAG,EAAE,GAAG,KAAK,GAAG;AAC7C,UAAM,IAAI;AAAA,EACZ;AACA,MAAI,OAAO;AACT,QAAI,MAAM,SAAS,OAAO;AACxB,YAAM,OAAO,KAAK;AAAA,IACpB,OAAO;AACL,aAAO,MAAM,SAAS,MAAO,OAAM,KAAK,EAAE;AAAA,IAC5C;AAAA,EACF;AACA,SAAO,IAAI,MAAM,QAAQ,KAAK;AAC5B,UAAM,CAAC,IAAI,MAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,MAAM,WAAW,GAAG;AAAA,EACzD;AACA,SAAO;AACT;AACA,SAAS,MAAM,KAAK,GAAG,QAAQ;AAC7B,QAAM,IAAI,IAAI;AACd,MAAI,MAAM,GAAG;AACX,WAAO;AAAA,EACT;AACA,MAAI,UAAU;AACd,SAAO,UAAU,GAAG;AAClB,UAAM,WAAW,IAAI,OAAO,IAAI,UAAU,CAAC;AAC3C,QAAI,aAAa,KAAK,CAAC,QAAQ;AAC7B;AAAA,IACF,WAAW,aAAa,KAAK,QAAQ;AACnC;AAAA,IACF,OAAO;AACL;AAAA,IACF;AAAA,EACF;AACA,SAAO,IAAI,MAAM,GAAG,IAAI,OAAO;AACjC;AACA,SAAS,mBAAmB,KAAK,GAAG;AAClC,MAAI,IAAI,QAAQ,EAAE,CAAC,CAAC,MAAM,IAAI;AAC5B,WAAO;AAAA,EACT;AACA,MAAI,QAAQ;AACZ,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,QAAI,IAAI,CAAC,MAAM,MAAM;AACnB;AAAA,IACF,WAAW,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG;AAC1B;AAAA,IACF,WAAW,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG;AAC1B;AACA,UAAI,QAAQ,GAAG;AACb,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,MAAI,QAAQ,GAAG;AACb,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAGA,SAAS,WAAW,KAAK,OAAO,KAAK,QAAQ,OAAO;AAClD,QAAM,OAAO,MAAM;AACnB,QAAM,QAAQ,MAAM,SAAS;AAC7B,QAAM,OAAO,IAAI,CAAC,EAAE,QAAQ,MAAM,MAAM,mBAAmB,IAAI;AAC/D,SAAO,MAAM,SAAS;AACtB,QAAM,QAAQ;AAAA,IACZ,MAAM,IAAI,CAAC,EAAE,OAAO,CAAC,MAAM,MAAM,UAAU;AAAA,IAC3C;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ,OAAO,aAAa,IAAI;AAAA,EAClC;AACA,SAAO,MAAM,SAAS;AACtB,SAAO;AACT;AACA,SAAS,uBAAuB,KAAK,MAAM,OAAO;AAChD,QAAM,oBAAoB,IAAI,MAAM,MAAM,MAAM,sBAAsB;AACtE,MAAI,sBAAsB,MAAM;AAC9B,WAAO;AAAA,EACT;AACA,QAAM,eAAe,kBAAkB,CAAC;AACxC,SAAO,KAAK,MAAM,IAAI,EAAE,IAAI,UAAQ;AAClC,UAAM,oBAAoB,KAAK,MAAM,MAAM,MAAM,cAAc;AAC/D,QAAI,sBAAsB,MAAM;AAC9B,aAAO;AAAA,IACT;AACA,UAAM,CAAC,YAAY,IAAI;AACvB,QAAI,aAAa,UAAU,aAAa,QAAQ;AAC9C,aAAO,KAAK,MAAM,aAAa,MAAM;AAAA,IACvC;AACA,WAAO;AAAA,EACT,CAAC,EAAE,KAAK,IAAI;AACd;AACA,IAAI,aAAa,MAAM;AAAA,EACrB;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,YAAY,UAAU;AACpB,SAAK,UAAU,YAAY;AAAA,EAC7B;AAAA,EACA,MAAM,KAAK;AACT,UAAM,MAAM,KAAK,MAAM,MAAM,QAAQ,KAAK,GAAG;AAC7C,QAAI,OAAO,IAAI,CAAC,EAAE,SAAS,GAAG;AAC5B,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAAA,EACA,KAAK,KAAK;AACR,UAAM,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,GAAG;AAC1C,QAAI,KAAK;AACP,YAAM,OAAO,IAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,kBAAkB,EAAE;AACjE,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,gBAAgB;AAAA,QAChB,MAAM,CAAC,KAAK,QAAQ,WAAW,MAAM,MAAM,IAAI,IAAI;AAAA,MACrD;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,KAAK;AACV,UAAM,MAAM,KAAK,MAAM,MAAM,OAAO,KAAK,GAAG;AAC5C,QAAI,KAAK;AACP,YAAM,MAAM,IAAI,CAAC;AACjB,YAAM,OAAO,uBAAuB,KAAK,IAAI,CAAC,KAAK,IAAI,KAAK,KAAK;AACjE,aAAO;AAAA,QACL,MAAM;AAAA,QACN;AAAA,QACA,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,KAAK,EAAE,QAAQ,KAAK,MAAM,OAAO,gBAAgB,IAAI,IAAI,IAAI,CAAC;AAAA,QACpF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ,KAAK;AACX,UAAM,MAAM,KAAK,MAAM,MAAM,QAAQ,KAAK,GAAG;AAC7C,QAAI,KAAK;AACP,UAAI,OAAO,IAAI,CAAC,EAAE,KAAK;AACvB,UAAI,KAAK,MAAM,MAAM,WAAW,KAAK,IAAI,GAAG;AAC1C,cAAM,UAAU,MAAM,MAAM,GAAG;AAC/B,YAAI,KAAK,QAAQ,UAAU;AACzB,iBAAO,QAAQ,KAAK;AAAA,QACtB,WAAW,CAAC,WAAW,KAAK,MAAM,MAAM,gBAAgB,KAAK,OAAO,GAAG;AACrE,iBAAO,QAAQ,KAAK;AAAA,QACtB;AAAA,MACF;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,OAAO,IAAI,CAAC,EAAE;AAAA,QACd;AAAA,QACA,QAAQ,KAAK,MAAM,OAAO,IAAI;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AAAA,EACA,GAAG,KAAK;AACN,UAAM,MAAM,KAAK,MAAM,MAAM,GAAG,KAAK,GAAG;AACxC,QAAI,KAAK;AACP,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,MAAM,IAAI,CAAC,GAAG,IAAI;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW,KAAK;AACd,UAAM,MAAM,KAAK,MAAM,MAAM,WAAW,KAAK,GAAG;AAChD,QAAI,KAAK;AACP,UAAI,QAAQ,MAAM,IAAI,CAAC,GAAG,IAAI,EAAE,MAAM,IAAI;AAC1C,UAAI,MAAM;AACV,UAAI,OAAO;AACX,YAAM,SAAS,CAAC;AAChB,aAAO,MAAM,SAAS,GAAG;AACvB,YAAI,eAAe;AACnB,cAAM,eAAe,CAAC;AACtB,YAAI;AACJ,aAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACjC,cAAI,KAAK,MAAM,MAAM,gBAAgB,KAAK,MAAM,CAAC,CAAC,GAAG;AACnD,yBAAa,KAAK,MAAM,CAAC,CAAC;AAC1B,2BAAe;AAAA,UACjB,WAAW,CAAC,cAAc;AACxB,yBAAa,KAAK,MAAM,CAAC,CAAC;AAAA,UAC5B,OAAO;AACL;AAAA,UACF;AAAA,QACF;AACA,gBAAQ,MAAM,MAAM,CAAC;AACrB,cAAM,aAAa,aAAa,KAAK,IAAI;AACzC,cAAM,cAAc,WAAW,QAAQ,KAAK,MAAM,MAAM,yBAAyB,UAAU,EAAE,QAAQ,KAAK,MAAM,MAAM,0BAA0B,EAAE;AAClJ,cAAM,MAAM,GAAG,GAAG;AAAA,EACxB,UAAU,KAAK;AACT,eAAO,OAAO,GAAG,IAAI;AAAA,EAC3B,WAAW,KAAK;AACV,cAAM,MAAM,KAAK,MAAM,MAAM;AAC7B,aAAK,MAAM,MAAM,MAAM;AACvB,aAAK,MAAM,YAAY,aAAa,QAAQ,IAAI;AAChD,aAAK,MAAM,MAAM,MAAM;AACvB,YAAI,MAAM,WAAW,GAAG;AACtB;AAAA,QACF;AACA,cAAM,YAAY,OAAO,GAAG,EAAE;AAC9B,YAAI,WAAW,SAAS,QAAQ;AAC9B;AAAA,QACF,WAAW,WAAW,SAAS,cAAc;AAC3C,gBAAM,WAAW;AACjB,gBAAM,UAAU,SAAS,MAAM,OAAO,MAAM,KAAK,IAAI;AACrD,gBAAM,WAAW,KAAK,WAAW,OAAO;AACxC,iBAAO,OAAO,SAAS,CAAC,IAAI;AAC5B,gBAAM,IAAI,UAAU,GAAG,IAAI,SAAS,SAAS,IAAI,MAAM,IAAI,SAAS;AACpE,iBAAO,KAAK,UAAU,GAAG,KAAK,SAAS,SAAS,KAAK,MAAM,IAAI,SAAS;AACxE;AAAA,QACF,WAAW,WAAW,SAAS,QAAQ;AACrC,gBAAM,WAAW;AACjB,gBAAM,UAAU,SAAS,MAAM,OAAO,MAAM,KAAK,IAAI;AACrD,gBAAM,WAAW,KAAK,KAAK,OAAO;AAClC,iBAAO,OAAO,SAAS,CAAC,IAAI;AAC5B,gBAAM,IAAI,UAAU,GAAG,IAAI,SAAS,UAAU,IAAI,MAAM,IAAI,SAAS;AACrE,iBAAO,KAAK,UAAU,GAAG,KAAK,SAAS,SAAS,IAAI,MAAM,IAAI,SAAS;AACvE,kBAAQ,QAAQ,UAAU,OAAO,GAAG,EAAE,EAAE,IAAI,MAAM,EAAE,MAAM,IAAI;AAC9D;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,KAAK,KAAK;AACR,QAAI,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,GAAG;AACxC,QAAI,KAAK;AACP,UAAI,OAAO,IAAI,CAAC,EAAE,KAAK;AACvB,YAAM,YAAY,KAAK,SAAS;AAChC,YAAM,QAAQ;AAAA,QACZ,MAAM;AAAA,QACN,KAAK;AAAA,QACL,SAAS;AAAA,QACT,OAAO,YAAY,CAAC,KAAK,MAAM,GAAG,EAAE,IAAI;AAAA,QACxC,OAAO;AAAA,QACP,OAAO,CAAC;AAAA,MACV;AACA,aAAO,YAAY,aAAa,KAAK,MAAM,EAAE,CAAC,KAAK,KAAK,IAAI;AAC5D,UAAI,KAAK,QAAQ,UAAU;AACzB,eAAO,YAAY,OAAO;AAAA,MAC5B;AACA,YAAM,YAAY,KAAK,MAAM,MAAM,cAAc,IAAI;AACrD,UAAI,oBAAoB;AACxB,aAAO,KAAK;AACV,YAAI,WAAW;AACf,YAAI,MAAM;AACV,YAAI,eAAe;AACnB,YAAI,EAAE,MAAM,UAAU,KAAK,GAAG,IAAI;AAChC;AAAA,QACF;AACA,YAAI,KAAK,MAAM,MAAM,GAAG,KAAK,GAAG,GAAG;AACjC;AAAA,QACF;AACA,cAAM,IAAI,CAAC;AACX,cAAM,IAAI,UAAU,IAAI,MAAM;AAC9B,YAAI,OAAO,IAAI,CAAC,EAAE,MAAM,MAAM,CAAC,EAAE,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,iBAAiB,OAAK,IAAI,OAAO,IAAI,EAAE,MAAM,CAAC;AAC3G,YAAI,WAAW,IAAI,MAAM,MAAM,CAAC,EAAE,CAAC;AACnC,YAAI,YAAY,CAAC,KAAK,KAAK;AAC3B,YAAI,SAAS;AACb,YAAI,KAAK,QAAQ,UAAU;AACzB,mBAAS;AACT,yBAAe,KAAK,UAAU;AAAA,QAChC,WAAW,WAAW;AACpB,mBAAS,IAAI,CAAC,EAAE,SAAS;AAAA,QAC3B,OAAO;AACL,mBAAS,IAAI,CAAC,EAAE,OAAO,KAAK,MAAM,MAAM,YAAY;AACpD,mBAAS,SAAS,IAAI,IAAI;AAC1B,yBAAe,KAAK,MAAM,MAAM;AAChC,oBAAU,IAAI,CAAC,EAAE;AAAA,QACnB;AACA,YAAI,aAAa,KAAK,MAAM,MAAM,UAAU,KAAK,QAAQ,GAAG;AAC1D,iBAAO,WAAW;AAClB,gBAAM,IAAI,UAAU,SAAS,SAAS,CAAC;AACvC,qBAAW;AAAA,QACb;AACA,YAAI,CAAC,UAAU;AACb,gBAAM,kBAAkB,KAAK,MAAM,MAAM,gBAAgB,MAAM;AAC/D,gBAAM,UAAU,KAAK,MAAM,MAAM,QAAQ,MAAM;AAC/C,gBAAM,mBAAmB,KAAK,MAAM,MAAM,iBAAiB,MAAM;AACjE,gBAAM,oBAAoB,KAAK,MAAM,MAAM,kBAAkB,MAAM;AACnE,gBAAM,iBAAiB,KAAK,MAAM,MAAM,eAAe,MAAM;AAC7D,iBAAO,KAAK;AACV,kBAAM,UAAU,IAAI,MAAM,MAAM,CAAC,EAAE,CAAC;AACpC,gBAAI;AACJ,uBAAW;AACX,gBAAI,KAAK,QAAQ,UAAU;AACzB,yBAAW,SAAS,QAAQ,KAAK,MAAM,MAAM,oBAAoB,IAAI;AACrE,oCAAsB;AAAA,YACxB,OAAO;AACL,oCAAsB,SAAS,QAAQ,KAAK,MAAM,MAAM,eAAe,MAAM;AAAA,YAC/E;AACA,gBAAI,iBAAiB,KAAK,QAAQ,GAAG;AACnC;AAAA,YACF;AACA,gBAAI,kBAAkB,KAAK,QAAQ,GAAG;AACpC;AAAA,YACF;AACA,gBAAI,eAAe,KAAK,QAAQ,GAAG;AACjC;AAAA,YACF;AACA,gBAAI,gBAAgB,KAAK,QAAQ,GAAG;AAClC;AAAA,YACF;AACA,gBAAI,QAAQ,KAAK,QAAQ,GAAG;AAC1B;AAAA,YACF;AACA,gBAAI,oBAAoB,OAAO,KAAK,MAAM,MAAM,YAAY,KAAK,UAAU,CAAC,SAAS,KAAK,GAAG;AAC3F,8BAAgB,OAAO,oBAAoB,MAAM,MAAM;AAAA,YACzD,OAAO;AACL,kBAAI,WAAW;AACb;AAAA,cACF;AACA,kBAAI,KAAK,QAAQ,KAAK,MAAM,MAAM,eAAe,MAAM,EAAE,OAAO,KAAK,MAAM,MAAM,YAAY,KAAK,GAAG;AACnG;AAAA,cACF;AACA,kBAAI,iBAAiB,KAAK,IAAI,GAAG;AAC/B;AAAA,cACF;AACA,kBAAI,kBAAkB,KAAK,IAAI,GAAG;AAChC;AAAA,cACF;AACA,kBAAI,QAAQ,KAAK,IAAI,GAAG;AACtB;AAAA,cACF;AACA,8BAAgB,OAAO;AAAA,YACzB;AACA,gBAAI,CAAC,aAAa,CAAC,SAAS,KAAK,GAAG;AAClC,0BAAY;AAAA,YACd;AACA,mBAAO,UAAU;AACjB,kBAAM,IAAI,UAAU,QAAQ,SAAS,CAAC;AACtC,mBAAO,oBAAoB,MAAM,MAAM;AAAA,UACzC;AAAA,QACF;AACA,YAAI,CAAC,MAAM,OAAO;AAChB,cAAI,mBAAmB;AACrB,kBAAM,QAAQ;AAAA,UAChB,WAAW,KAAK,MAAM,MAAM,gBAAgB,KAAK,GAAG,GAAG;AACrD,gCAAoB;AAAA,UACtB;AAAA,QACF;AACA,YAAI,SAAS;AACb,YAAI;AACJ,YAAI,KAAK,QAAQ,KAAK;AACpB,mBAAS,KAAK,MAAM,MAAM,WAAW,KAAK,YAAY;AACtD,cAAI,QAAQ;AACV,wBAAY,OAAO,CAAC,MAAM;AAC1B,2BAAe,aAAa,QAAQ,KAAK,MAAM,MAAM,iBAAiB,EAAE;AAAA,UAC1E;AAAA,QACF;AACA,cAAM,MAAM,KAAK;AAAA,UACf,MAAM;AAAA,UACN;AAAA,UACA,MAAM,CAAC,CAAC;AAAA,UACR,SAAS;AAAA,UACT,OAAO;AAAA,UACP,MAAM;AAAA,UACN,QAAQ,CAAC;AAAA,QACX,CAAC;AACD,cAAM,OAAO;AAAA,MACf;AACA,YAAM,WAAW,MAAM,MAAM,GAAG,EAAE;AAClC,UAAI,UAAU;AACZ,iBAAS,MAAM,SAAS,IAAI,QAAQ;AACpC,iBAAS,OAAO,SAAS,KAAK,QAAQ;AAAA,MACxC,OAAO;AACL;AAAA,MACF;AACA,YAAM,MAAM,MAAM,IAAI,QAAQ;AAC9B,eAAS,IAAI,GAAG,IAAI,MAAM,MAAM,QAAQ,KAAK;AAC3C,aAAK,MAAM,MAAM,MAAM;AACvB,cAAM,MAAM,CAAC,EAAE,SAAS,KAAK,MAAM,YAAY,MAAM,MAAM,CAAC,EAAE,MAAM,CAAC,CAAC;AACtE,YAAI,CAAC,MAAM,OAAO;AAChB,gBAAM,UAAU,MAAM,MAAM,CAAC,EAAE,OAAO,OAAO,OAAK,EAAE,SAAS,OAAO;AACpE,gBAAM,wBAAwB,QAAQ,SAAS,KAAK,QAAQ,KAAK,OAAK,KAAK,MAAM,MAAM,QAAQ,KAAK,EAAE,GAAG,CAAC;AAC1G,gBAAM,QAAQ;AAAA,QAChB;AAAA,MACF;AACA,UAAI,MAAM,OAAO;AACf,iBAAS,IAAI,GAAG,IAAI,MAAM,MAAM,QAAQ,KAAK;AAC3C,gBAAM,MAAM,CAAC,EAAE,QAAQ;AAAA,QACzB;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,KAAK,KAAK;AACR,UAAM,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,GAAG;AAC1C,QAAI,KAAK;AACP,YAAM,QAAQ;AAAA,QACZ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,KAAK,IAAI,CAAC;AAAA,QACV,KAAK,IAAI,CAAC,MAAM,SAAS,IAAI,CAAC,MAAM,YAAY,IAAI,CAAC,MAAM;AAAA,QAC3D,MAAM,IAAI,CAAC;AAAA,MACb;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,IAAI,KAAK;AACP,UAAM,MAAM,KAAK,MAAM,MAAM,IAAI,KAAK,GAAG;AACzC,QAAI,KAAK;AACP,YAAM,OAAO,IAAI,CAAC,EAAE,YAAY,EAAE,QAAQ,KAAK,MAAM,MAAM,qBAAqB,GAAG;AACnF,YAAM,OAAO,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,cAAc,IAAI,EAAE,QAAQ,KAAK,MAAM,OAAO,gBAAgB,IAAI,IAAI;AAC5H,YAAM,QAAQ,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,UAAU,GAAG,IAAI,CAAC,EAAE,SAAS,CAAC,EAAE,QAAQ,KAAK,MAAM,OAAO,gBAAgB,IAAI,IAAI,IAAI,CAAC;AACrH,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK;AAAA,QACL,KAAK,IAAI,CAAC;AAAA,QACV;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,MAAM,KAAK;AACT,UAAM,MAAM,KAAK,MAAM,MAAM,MAAM,KAAK,GAAG;AAC3C,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,QAAI,CAAC,KAAK,MAAM,MAAM,eAAe,KAAK,IAAI,CAAC,CAAC,GAAG;AACjD;AAAA,IACF;AACA,UAAM,UAAU,WAAW,IAAI,CAAC,CAAC;AACjC,UAAM,SAAS,IAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,iBAAiB,EAAE,EAAE,MAAM,GAAG;AAC7E,UAAM,OAAO,IAAI,CAAC,GAAG,KAAK,IAAI,IAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,mBAAmB,EAAE,EAAE,MAAM,IAAI,IAAI,CAAC;AACpG,UAAM,OAAO;AAAA,MACX,MAAM;AAAA,MACN,KAAK,IAAI,CAAC;AAAA,MACV,QAAQ,CAAC;AAAA,MACT,OAAO,CAAC;AAAA,MACR,MAAM,CAAC;AAAA,IACT;AACA,QAAI,QAAQ,WAAW,OAAO,QAAQ;AACpC;AAAA,IACF;AACA,eAAW,SAAS,QAAQ;AAC1B,UAAI,KAAK,MAAM,MAAM,gBAAgB,KAAK,KAAK,GAAG;AAChD,aAAK,MAAM,KAAK,OAAO;AAAA,MACzB,WAAW,KAAK,MAAM,MAAM,iBAAiB,KAAK,KAAK,GAAG;AACxD,aAAK,MAAM,KAAK,QAAQ;AAAA,MAC1B,WAAW,KAAK,MAAM,MAAM,eAAe,KAAK,KAAK,GAAG;AACtD,aAAK,MAAM,KAAK,MAAM;AAAA,MACxB,OAAO;AACL,aAAK,MAAM,KAAK,IAAI;AAAA,MACtB;AAAA,IACF;AACA,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,WAAK,OAAO,KAAK;AAAA,QACf,MAAM,QAAQ,CAAC;AAAA,QACf,QAAQ,KAAK,MAAM,OAAO,QAAQ,CAAC,CAAC;AAAA,QACpC,QAAQ;AAAA,QACR,OAAO,KAAK,MAAM,CAAC;AAAA,MACrB,CAAC;AAAA,IACH;AACA,eAAW,OAAO,MAAM;AACtB,WAAK,KAAK,KAAK,WAAW,KAAK,KAAK,OAAO,MAAM,EAAE,IAAI,CAAC,MAAM,MAAM;AAClE,eAAO;AAAA,UACL,MAAM;AAAA,UACN,QAAQ,KAAK,MAAM,OAAO,IAAI;AAAA,UAC9B,QAAQ;AAAA,UACR,OAAO,KAAK,MAAM,CAAC;AAAA,QACrB;AAAA,MACF,CAAC,CAAC;AAAA,IACJ;AACA,WAAO;AAAA,EACT;AAAA,EACA,SAAS,KAAK;AACZ,UAAM,MAAM,KAAK,MAAM,MAAM,SAAS,KAAK,GAAG;AAC9C,QAAI,KAAK;AACP,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,OAAO,IAAI,CAAC,EAAE,OAAO,CAAC,MAAM,MAAM,IAAI;AAAA,QACtC,MAAM,IAAI,CAAC;AAAA,QACX,QAAQ,KAAK,MAAM,OAAO,IAAI,CAAC,CAAC;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,KAAK;AACb,UAAM,MAAM,KAAK,MAAM,MAAM,UAAU,KAAK,GAAG;AAC/C,QAAI,KAAK;AACP,YAAM,OAAO,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC,EAAE,SAAS,CAAC,MAAM,OAAO,IAAI,CAAC,EAAE,MAAM,GAAG,EAAE,IAAI,IAAI,CAAC;AACpF,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV;AAAA,QACA,QAAQ,KAAK,MAAM,OAAO,IAAI;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AAAA,EACA,KAAK,KAAK;AACR,UAAM,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,GAAG;AAC1C,QAAI,KAAK;AACP,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,MAAM,IAAI,CAAC;AAAA,QACX,QAAQ,KAAK,MAAM,OAAO,IAAI,CAAC,CAAC;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,KAAK;AACV,UAAM,MAAM,KAAK,MAAM,OAAO,OAAO,KAAK,GAAG;AAC7C,QAAI,KAAK;AACP,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,MAAM,IAAI,CAAC;AAAA,MACb;AAAA,IACF;AAAA,EACF;AAAA,EACA,IAAI,KAAK;AACP,UAAM,MAAM,KAAK,MAAM,OAAO,IAAI,KAAK,GAAG;AAC1C,QAAI,KAAK;AACP,UAAI,CAAC,KAAK,MAAM,MAAM,UAAU,KAAK,MAAM,MAAM,UAAU,KAAK,IAAI,CAAC,CAAC,GAAG;AACvE,aAAK,MAAM,MAAM,SAAS;AAAA,MAC5B,WAAW,KAAK,MAAM,MAAM,UAAU,KAAK,MAAM,MAAM,QAAQ,KAAK,IAAI,CAAC,CAAC,GAAG;AAC3E,aAAK,MAAM,MAAM,SAAS;AAAA,MAC5B;AACA,UAAI,CAAC,KAAK,MAAM,MAAM,cAAc,KAAK,MAAM,MAAM,kBAAkB,KAAK,IAAI,CAAC,CAAC,GAAG;AACnF,aAAK,MAAM,MAAM,aAAa;AAAA,MAChC,WAAW,KAAK,MAAM,MAAM,cAAc,KAAK,MAAM,MAAM,gBAAgB,KAAK,IAAI,CAAC,CAAC,GAAG;AACvF,aAAK,MAAM,MAAM,aAAa;AAAA,MAChC;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,QAAQ,KAAK,MAAM,MAAM;AAAA,QACzB,YAAY,KAAK,MAAM,MAAM;AAAA,QAC7B,OAAO;AAAA,QACP,MAAM,IAAI,CAAC;AAAA,MACb;AAAA,IACF;AAAA,EACF;AAAA,EACA,KAAK,KAAK;AACR,UAAM,MAAM,KAAK,MAAM,OAAO,KAAK,KAAK,GAAG;AAC3C,QAAI,KAAK;AACP,YAAM,aAAa,IAAI,CAAC,EAAE,KAAK;AAC/B,UAAI,CAAC,KAAK,QAAQ,YAAY,KAAK,MAAM,MAAM,kBAAkB,KAAK,UAAU,GAAG;AACjF,YAAI,CAAC,KAAK,MAAM,MAAM,gBAAgB,KAAK,UAAU,GAAG;AACtD;AAAA,QACF;AACA,cAAM,aAAa,MAAM,WAAW,MAAM,GAAG,EAAE,GAAG,IAAI;AACtD,aAAK,WAAW,SAAS,WAAW,UAAU,MAAM,GAAG;AACrD;AAAA,QACF;AAAA,MACF,OAAO;AACL,cAAM,iBAAiB,mBAAmB,IAAI,CAAC,GAAG,IAAI;AACtD,YAAI,mBAAmB,IAAI;AACzB;AAAA,QACF;AACA,YAAI,iBAAiB,IAAI;AACvB,gBAAM,QAAQ,IAAI,CAAC,EAAE,QAAQ,GAAG,MAAM,IAAI,IAAI;AAC9C,gBAAM,UAAU,QAAQ,IAAI,CAAC,EAAE,SAAS;AACxC,cAAI,CAAC,IAAI,IAAI,CAAC,EAAE,UAAU,GAAG,cAAc;AAC3C,cAAI,CAAC,IAAI,IAAI,CAAC,EAAE,UAAU,GAAG,OAAO,EAAE,KAAK;AAC3C,cAAI,CAAC,IAAI;AAAA,QACX;AAAA,MACF;AACA,UAAI,OAAO,IAAI,CAAC;AAChB,UAAI,QAAQ;AACZ,UAAI,KAAK,QAAQ,UAAU;AACzB,cAAM,QAAQ,KAAK,MAAM,MAAM,kBAAkB,KAAK,IAAI;AAC1D,YAAI,OAAO;AACT,iBAAO,MAAM,CAAC;AACd,kBAAQ,MAAM,CAAC;AAAA,QACjB;AAAA,MACF,OAAO;AACL,gBAAQ,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,MAAM,GAAG,EAAE,IAAI;AAAA,MACzC;AACA,aAAO,KAAK,KAAK;AACjB,UAAI,KAAK,MAAM,MAAM,kBAAkB,KAAK,IAAI,GAAG;AACjD,YAAI,KAAK,QAAQ,YAAY,CAAC,KAAK,MAAM,MAAM,gBAAgB,KAAK,UAAU,GAAG;AAC/E,iBAAO,KAAK,MAAM,CAAC;AAAA,QACrB,OAAO;AACL,iBAAO,KAAK,MAAM,GAAG,EAAE;AAAA,QACzB;AAAA,MACF;AACA,aAAO,WAAW,KAAK;AAAA,QACrB,MAAM,OAAO,KAAK,QAAQ,KAAK,MAAM,OAAO,gBAAgB,IAAI,IAAI;AAAA,QACpE,OAAO,QAAQ,MAAM,QAAQ,KAAK,MAAM,OAAO,gBAAgB,IAAI,IAAI;AAAA,MACzE,GAAG,IAAI,CAAC,GAAG,KAAK,OAAO,KAAK,KAAK;AAAA,IACnC;AAAA,EACF;AAAA,EACA,QAAQ,KAAK,OAAO;AAClB,QAAI;AACJ,SAAK,MAAM,KAAK,MAAM,OAAO,QAAQ,KAAK,GAAG,OAAO,MAAM,KAAK,MAAM,OAAO,OAAO,KAAK,GAAG,IAAI;AAC7F,YAAM,cAAc,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG,QAAQ,KAAK,MAAM,MAAM,qBAAqB,GAAG;AACvF,YAAM,QAAQ,MAAM,WAAW,YAAY,CAAC;AAC5C,UAAI,CAAC,OAAO;AACV,cAAM,OAAO,IAAI,CAAC,EAAE,OAAO,CAAC;AAC5B,eAAO;AAAA,UACL,MAAM;AAAA,UACN,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AACA,aAAO,WAAW,KAAK,OAAO,IAAI,CAAC,GAAG,KAAK,OAAO,KAAK,KAAK;AAAA,IAC9D;AAAA,EACF;AAAA,EACA,SAAS,KAAK,WAAW,WAAW,IAAI;AACtC,QAAI,QAAQ,KAAK,MAAM,OAAO,eAAe,KAAK,GAAG;AACrD,QAAI,CAAC,MAAO;AACZ,QAAI,MAAM,CAAC,KAAK,SAAS,MAAM,KAAK,MAAM,MAAM,mBAAmB,EAAG;AACtE,UAAM,WAAW,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK;AACzC,QAAI,CAAC,YAAY,CAAC,YAAY,KAAK,MAAM,OAAO,YAAY,KAAK,QAAQ,GAAG;AAC1E,YAAM,UAAU,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,SAAS;AACvC,UAAI,QACF,SACA,aAAa,SACb,gBAAgB;AAClB,YAAM,SAAS,MAAM,CAAC,EAAE,CAAC,MAAM,MAAM,KAAK,MAAM,OAAO,oBAAoB,KAAK,MAAM,OAAO;AAC7F,aAAO,YAAY;AACnB,kBAAY,UAAU,MAAM,KAAK,IAAI,SAAS,OAAO;AACrD,cAAQ,QAAQ,OAAO,KAAK,SAAS,MAAM,MAAM;AAC/C,iBAAS,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC;AAC5E,YAAI,CAAC,OAAQ;AACb,kBAAU,CAAC,GAAG,MAAM,EAAE;AACtB,YAAI,MAAM,CAAC,KAAK,MAAM,CAAC,GAAG;AACxB,wBAAc;AACd;AAAA,QACF,WAAW,MAAM,CAAC,KAAK,MAAM,CAAC,GAAG;AAC/B,cAAI,UAAU,KAAK,GAAG,UAAU,WAAW,IAAI;AAC7C,6BAAiB;AACjB;AAAA,UACF;AAAA,QACF;AACA,sBAAc;AACd,YAAI,aAAa,EAAG;AACpB,kBAAU,KAAK,IAAI,SAAS,UAAU,aAAa,aAAa;AAChE,cAAM,iBAAiB,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;AACxC,cAAM,MAAM,IAAI,MAAM,GAAG,UAAU,MAAM,QAAQ,iBAAiB,OAAO;AACzE,YAAI,KAAK,IAAI,SAAS,OAAO,IAAI,GAAG;AAClC,gBAAM,QAAQ,IAAI,MAAM,GAAG,EAAE;AAC7B,iBAAO;AAAA,YACL,MAAM;AAAA,YACN;AAAA,YACA,MAAM;AAAA,YACN,QAAQ,KAAK,MAAM,aAAa,KAAK;AAAA,UACvC;AAAA,QACF;AACA,cAAM,OAAO,IAAI,MAAM,GAAG,EAAE;AAC5B,eAAO;AAAA,UACL,MAAM;AAAA,UACN;AAAA,UACA;AAAA,UACA,QAAQ,KAAK,MAAM,aAAa,IAAI;AAAA,QACtC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAAS,KAAK;AACZ,UAAM,MAAM,KAAK,MAAM,OAAO,KAAK,KAAK,GAAG;AAC3C,QAAI,KAAK;AACP,UAAI,OAAO,IAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,mBAAmB,GAAG;AACjE,YAAM,mBAAmB,KAAK,MAAM,MAAM,aAAa,KAAK,IAAI;AAChE,YAAM,0BAA0B,KAAK,MAAM,MAAM,kBAAkB,KAAK,IAAI,KAAK,KAAK,MAAM,MAAM,gBAAgB,KAAK,IAAI;AAC3H,UAAI,oBAAoB,yBAAyB;AAC/C,eAAO,KAAK,UAAU,GAAG,KAAK,SAAS,CAAC;AAAA,MAC1C;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,GAAG,KAAK;AACN,UAAM,MAAM,KAAK,MAAM,OAAO,GAAG,KAAK,GAAG;AACzC,QAAI,KAAK;AACP,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAAA,EACA,IAAI,KAAK;AACP,UAAM,MAAM,KAAK,MAAM,OAAO,IAAI,KAAK,GAAG;AAC1C,QAAI,KAAK;AACP,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,MAAM,IAAI,CAAC;AAAA,QACX,QAAQ,KAAK,MAAM,aAAa,IAAI,CAAC,CAAC;AAAA,MACxC;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAAS,KAAK;AACZ,UAAM,MAAM,KAAK,MAAM,OAAO,SAAS,KAAK,GAAG;AAC/C,QAAI,KAAK;AACP,UAAI,MAAM;AACV,UAAI,IAAI,CAAC,MAAM,KAAK;AAClB,eAAO,IAAI,CAAC;AACZ,eAAO,YAAY;AAAA,MACrB,OAAO;AACL,eAAO,IAAI,CAAC;AACZ,eAAO;AAAA,MACT;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV;AAAA,QACA;AAAA,QACA,QAAQ,CAAC;AAAA,UACP,MAAM;AAAA,UACN,KAAK;AAAA,UACL;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EACA,IAAI,KAAK;AACP,QAAI;AACJ,QAAI,MAAM,KAAK,MAAM,OAAO,IAAI,KAAK,GAAG,GAAG;AACzC,UAAI,MAAM;AACV,UAAI,IAAI,CAAC,MAAM,KAAK;AAClB,eAAO,IAAI,CAAC;AACZ,eAAO,YAAY;AAAA,MACrB,OAAO;AACL,YAAI;AACJ,WAAG;AACD,wBAAc,IAAI,CAAC;AACnB,cAAI,CAAC,IAAI,KAAK,MAAM,OAAO,WAAW,KAAK,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK;AAAA,QAC7D,SAAS,gBAAgB,IAAI,CAAC;AAC9B,eAAO,IAAI,CAAC;AACZ,YAAI,IAAI,CAAC,MAAM,QAAQ;AACrB,iBAAO,YAAY,IAAI,CAAC;AAAA,QAC1B,OAAO;AACL,iBAAO,IAAI,CAAC;AAAA,QACd;AAAA,MACF;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV;AAAA,QACA;AAAA,QACA,QAAQ,CAAC;AAAA,UACP,MAAM;AAAA,UACN,KAAK;AAAA,UACL;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW,KAAK;AACd,UAAM,MAAM,KAAK,MAAM,OAAO,KAAK,KAAK,GAAG;AAC3C,QAAI,KAAK;AACP,YAAM,UAAU,KAAK,MAAM,MAAM;AACjC,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,MAAM,IAAI,CAAC;AAAA,QACX;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAGA,IAAI,SAAS,MAAM,QAAQ;AAAA,EACzB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,UAAU;AACpB,SAAK,SAAS,CAAC;AACf,SAAK,OAAO,QAAuB,uBAAO,OAAO,IAAI;AACrD,SAAK,UAAU,YAAY;AAC3B,SAAK,QAAQ,YAAY,KAAK,QAAQ,aAAa,IAAI,WAAW;AAClE,SAAK,YAAY,KAAK,QAAQ;AAC9B,SAAK,UAAU,UAAU,KAAK;AAC9B,SAAK,UAAU,QAAQ;AACvB,SAAK,cAAc,CAAC;AACpB,SAAK,QAAQ;AAAA,MACX,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,KAAK;AAAA,IACP;AACA,UAAM,QAAQ;AAAA,MACZ;AAAA,MACA,OAAO,MAAM;AAAA,MACb,QAAQ,OAAO;AAAA,IACjB;AACA,QAAI,KAAK,QAAQ,UAAU;AACzB,YAAM,QAAQ,MAAM;AACpB,YAAM,SAAS,OAAO;AAAA,IACxB,WAAW,KAAK,QAAQ,KAAK;AAC3B,YAAM,QAAQ,MAAM;AACpB,UAAI,KAAK,QAAQ,QAAQ;AACvB,cAAM,SAAS,OAAO;AAAA,MACxB,OAAO;AACL,cAAM,SAAS,OAAO;AAAA,MACxB;AAAA,IACF;AACA,SAAK,UAAU,QAAQ;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,QAAQ;AACjB,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,IAAI,KAAK,UAAU;AACxB,UAAM,SAAS,IAAI,QAAQ,QAAQ;AACnC,WAAO,OAAO,IAAI,GAAG;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,UAAU,KAAK,UAAU;AAC9B,UAAM,SAAS,IAAI,QAAQ,QAAQ;AACnC,WAAO,OAAO,aAAa,GAAG;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,KAAK;AACP,UAAM,IAAI,QAAQ,MAAM,gBAAgB,IAAI;AAC5C,SAAK,YAAY,KAAK,KAAK,MAAM;AACjC,aAAS,IAAI,GAAG,IAAI,KAAK,YAAY,QAAQ,KAAK;AAChD,YAAM,OAAO,KAAK,YAAY,CAAC;AAC/B,WAAK,aAAa,KAAK,KAAK,KAAK,MAAM;AAAA,IACzC;AACA,SAAK,cAAc,CAAC;AACpB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,YAAY,KAAK,SAAS,CAAC,GAAG,uBAAuB,OAAO;AAC1D,QAAI,KAAK,QAAQ,UAAU;AACzB,YAAM,IAAI,QAAQ,MAAM,eAAe,MAAM,EAAE,QAAQ,MAAM,WAAW,EAAE;AAAA,IAC5E;AACA,WAAO,KAAK;AACV,UAAI;AACJ,UAAI,KAAK,QAAQ,YAAY,OAAO,KAAK,kBAAgB;AACvD,YAAI,QAAQ,aAAa,KAAK;AAAA,UAC5B,OAAO;AAAA,QACT,GAAG,KAAK,MAAM,GAAG;AACf,gBAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,iBAAO,KAAK,KAAK;AACjB,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT,CAAC,GAAG;AACF;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,MAAM,GAAG,GAAG;AACrC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,cAAM,YAAY,OAAO,GAAG,EAAE;AAC9B,YAAI,MAAM,IAAI,WAAW,KAAK,cAAc,QAAQ;AAClD,oBAAU,OAAO;AAAA,QACnB,OAAO;AACL,iBAAO,KAAK,KAAK;AAAA,QACnB;AACA;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,GAAG;AACpC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,cAAM,YAAY,OAAO,GAAG,EAAE;AAC9B,YAAI,WAAW,SAAS,eAAe,WAAW,SAAS,QAAQ;AACjE,oBAAU,OAAO,OAAO,MAAM;AAC9B,oBAAU,QAAQ,OAAO,MAAM;AAC/B,eAAK,YAAY,GAAG,EAAE,EAAE,MAAM,UAAU;AAAA,QAC1C,OAAO;AACL,iBAAO,KAAK,KAAK;AAAA,QACnB;AACA;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,OAAO,GAAG,GAAG;AACtC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,QAAQ,GAAG,GAAG;AACvC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,GAAG,GAAG,GAAG;AAClC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,WAAW,GAAG,GAAG;AAC1C,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,GAAG;AACpC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,GAAG;AACpC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,IAAI,GAAG,GAAG;AACnC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,cAAM,YAAY,OAAO,GAAG,EAAE;AAC9B,YAAI,WAAW,SAAS,eAAe,WAAW,SAAS,QAAQ;AACjE,oBAAU,OAAO,OAAO,MAAM;AAC9B,oBAAU,QAAQ,OAAO,MAAM;AAC/B,eAAK,YAAY,GAAG,EAAE,EAAE,MAAM,UAAU;AAAA,QAC1C,WAAW,CAAC,KAAK,OAAO,MAAM,MAAM,GAAG,GAAG;AACxC,eAAK,OAAO,MAAM,MAAM,GAAG,IAAI;AAAA,YAC7B,MAAM,MAAM;AAAA,YACZ,OAAO,MAAM;AAAA,UACf;AAAA,QACF;AACA;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,MAAM,GAAG,GAAG;AACrC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,SAAS,GAAG,GAAG;AACxC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AACA,UAAI,SAAS;AACb,UAAI,KAAK,QAAQ,YAAY,YAAY;AACvC,YAAI,aAAa;AACjB,cAAM,UAAU,IAAI,MAAM,CAAC;AAC3B,YAAI;AACJ,aAAK,QAAQ,WAAW,WAAW,QAAQ,mBAAiB;AAC1D,sBAAY,cAAc,KAAK;AAAA,YAC7B,OAAO;AAAA,UACT,GAAG,OAAO;AACV,cAAI,OAAO,cAAc,YAAY,aAAa,GAAG;AACnD,yBAAa,KAAK,IAAI,YAAY,SAAS;AAAA,UAC7C;AAAA,QACF,CAAC;AACD,YAAI,aAAa,YAAY,cAAc,GAAG;AAC5C,mBAAS,IAAI,UAAU,GAAG,aAAa,CAAC;AAAA,QAC1C;AAAA,MACF;AACA,UAAI,KAAK,MAAM,QAAQ,QAAQ,KAAK,UAAU,UAAU,MAAM,IAAI;AAChE,cAAM,YAAY,OAAO,GAAG,EAAE;AAC9B,YAAI,wBAAwB,WAAW,SAAS,aAAa;AAC3D,oBAAU,OAAO,OAAO,MAAM;AAC9B,oBAAU,QAAQ,OAAO,MAAM;AAC/B,eAAK,YAAY,IAAI;AACrB,eAAK,YAAY,GAAG,EAAE,EAAE,MAAM,UAAU;AAAA,QAC1C,OAAO;AACL,iBAAO,KAAK,KAAK;AAAA,QACnB;AACA,+BAAuB,OAAO,WAAW,IAAI;AAC7C,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,GAAG;AACpC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,cAAM,YAAY,OAAO,GAAG,EAAE;AAC9B,YAAI,WAAW,SAAS,QAAQ;AAC9B,oBAAU,OAAO,OAAO,MAAM;AAC9B,oBAAU,QAAQ,OAAO,MAAM;AAC/B,eAAK,YAAY,IAAI;AACrB,eAAK,YAAY,GAAG,EAAE,EAAE,MAAM,UAAU;AAAA,QAC1C,OAAO;AACL,iBAAO,KAAK,KAAK;AAAA,QACnB;AACA;AAAA,MACF;AACA,UAAI,KAAK;AACP,cAAM,SAAS,4BAA4B,IAAI,WAAW,CAAC;AAC3D,YAAI,KAAK,QAAQ,QAAQ;AACvB,kBAAQ,MAAM,MAAM;AACpB;AAAA,QACF,OAAO;AACL,gBAAM,IAAI,MAAM,MAAM;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AACA,SAAK,MAAM,MAAM;AACjB,WAAO;AAAA,EACT;AAAA,EACA,OAAO,KAAK,SAAS,CAAC,GAAG;AACvB,SAAK,YAAY,KAAK;AAAA,MACpB;AAAA,MACA;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,KAAK,SAAS,CAAC,GAAG;AAC7B,QAAI,YAAY;AAChB,QAAI,QAAQ;AACZ,QAAI,KAAK,OAAO,OAAO;AACrB,YAAM,QAAQ,OAAO,KAAK,KAAK,OAAO,KAAK;AAC3C,UAAI,MAAM,SAAS,GAAG;AACpB,gBAAQ,QAAQ,KAAK,UAAU,MAAM,OAAO,cAAc,KAAK,SAAS,MAAM,MAAM;AAClF,cAAI,MAAM,SAAS,MAAM,CAAC,EAAE,MAAM,MAAM,CAAC,EAAE,YAAY,GAAG,IAAI,GAAG,EAAE,CAAC,GAAG;AACrE,wBAAY,UAAU,MAAM,GAAG,MAAM,KAAK,IAAI,MAAM,IAAI,OAAO,MAAM,CAAC,EAAE,SAAS,CAAC,IAAI,MAAM,UAAU,MAAM,KAAK,UAAU,MAAM,OAAO,cAAc,SAAS;AAAA,UACjK;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,YAAQ,QAAQ,KAAK,UAAU,MAAM,OAAO,eAAe,KAAK,SAAS,MAAM,MAAM;AACnF,kBAAY,UAAU,MAAM,GAAG,MAAM,KAAK,IAAI,OAAO,UAAU,MAAM,KAAK,UAAU,MAAM,OAAO,eAAe,SAAS;AAAA,IAC3H;AACA,YAAQ,QAAQ,KAAK,UAAU,MAAM,OAAO,UAAU,KAAK,SAAS,MAAM,MAAM;AAC9E,kBAAY,UAAU,MAAM,GAAG,MAAM,KAAK,IAAI,MAAM,IAAI,OAAO,MAAM,CAAC,EAAE,SAAS,CAAC,IAAI,MAAM,UAAU,MAAM,KAAK,UAAU,MAAM,OAAO,UAAU,SAAS;AAAA,IAC7J;AACA,QAAI,eAAe;AACnB,QAAI,WAAW;AACf,WAAO,KAAK;AACV,UAAI,CAAC,cAAc;AACjB,mBAAW;AAAA,MACb;AACA,qBAAe;AACf,UAAI;AACJ,UAAI,KAAK,QAAQ,YAAY,QAAQ,KAAK,kBAAgB;AACxD,YAAI,QAAQ,aAAa,KAAK;AAAA,UAC5B,OAAO;AAAA,QACT,GAAG,KAAK,MAAM,GAAG;AACf,gBAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,iBAAO,KAAK,KAAK;AACjB,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT,CAAC,GAAG;AACF;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,OAAO,GAAG,GAAG;AACtC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,IAAI,GAAG,GAAG;AACnC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,GAAG;AACpC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,QAAQ,KAAK,KAAK,OAAO,KAAK,GAAG;AAC1D,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,cAAM,YAAY,OAAO,GAAG,EAAE;AAC9B,YAAI,MAAM,SAAS,UAAU,WAAW,SAAS,QAAQ;AACvD,oBAAU,OAAO,MAAM;AACvB,oBAAU,QAAQ,MAAM;AAAA,QAC1B,OAAO;AACL,iBAAO,KAAK,KAAK;AAAA,QACnB;AACA;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,SAAS,KAAK,WAAW,QAAQ,GAAG;AAC7D,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,SAAS,GAAG,GAAG;AACxC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,GAAG,GAAG,GAAG;AAClC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,IAAI,GAAG,GAAG;AACnC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,SAAS,GAAG,GAAG;AACxC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AACA,UAAI,CAAC,KAAK,MAAM,WAAW,QAAQ,KAAK,UAAU,IAAI,GAAG,IAAI;AAC3D,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AACA,UAAI,SAAS;AACb,UAAI,KAAK,QAAQ,YAAY,aAAa;AACxC,YAAI,aAAa;AACjB,cAAM,UAAU,IAAI,MAAM,CAAC;AAC3B,YAAI;AACJ,aAAK,QAAQ,WAAW,YAAY,QAAQ,mBAAiB;AAC3D,sBAAY,cAAc,KAAK;AAAA,YAC7B,OAAO;AAAA,UACT,GAAG,OAAO;AACV,cAAI,OAAO,cAAc,YAAY,aAAa,GAAG;AACnD,yBAAa,KAAK,IAAI,YAAY,SAAS;AAAA,UAC7C;AAAA,QACF,CAAC;AACD,YAAI,aAAa,YAAY,cAAc,GAAG;AAC5C,mBAAS,IAAI,UAAU,GAAG,aAAa,CAAC;AAAA,QAC1C;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,WAAW,MAAM,GAAG;AAC7C,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,YAAI,MAAM,IAAI,MAAM,EAAE,MAAM,KAAK;AAC/B,qBAAW,MAAM,IAAI,MAAM,EAAE;AAAA,QAC/B;AACA,uBAAe;AACf,cAAM,YAAY,OAAO,GAAG,EAAE;AAC9B,YAAI,WAAW,SAAS,QAAQ;AAC9B,oBAAU,OAAO,MAAM;AACvB,oBAAU,QAAQ,MAAM;AAAA,QAC1B,OAAO;AACL,iBAAO,KAAK,KAAK;AAAA,QACnB;AACA;AAAA,MACF;AACA,UAAI,KAAK;AACP,cAAM,SAAS,4BAA4B,IAAI,WAAW,CAAC;AAC3D,YAAI,KAAK,QAAQ,QAAQ;AACvB,kBAAQ,MAAM,MAAM;AACpB;AAAA,QACF,OAAO;AACL,gBAAM,IAAI,MAAM,MAAM;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAGA,IAAI,YAAY,MAAM;AAAA,EACpB;AAAA,EACA;AAAA;AAAA,EAEA,YAAY,UAAU;AACpB,SAAK,UAAU,YAAY;AAAA,EAC7B;AAAA,EACA,MAAM,OAAO;AACX,WAAO;AAAA,EACT;AAAA,EACA,KAAK;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG;AACD,UAAM,cAAc,QAAQ,IAAI,MAAM,MAAM,aAAa,IAAI,CAAC;AAC9D,UAAM,OAAO,KAAK,QAAQ,MAAM,eAAe,EAAE,IAAI;AACrD,QAAI,CAAC,YAAY;AACf,aAAO,iBAAiB,UAAU,OAAO,QAAQ,MAAM,IAAI,KAAK;AAAA,IAClE;AACA,WAAO,gCAAgC,QAAQ,UAAU,IAAI,QAAQ,UAAU,OAAO,QAAQ,MAAM,IAAI,KAAK;AAAA,EAC/G;AAAA,EACA,WAAW;AAAA,IACT;AAAA,EACF,GAAG;AACD,UAAM,OAAO,KAAK,OAAO,MAAM,MAAM;AACrC,WAAO;AAAA,EACT,IAAI;AAAA;AAAA,EAEJ;AAAA,EACA,KAAK;AAAA,IACH;AAAA,EACF,GAAG;AACD,WAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN;AAAA,IACA;AAAA,EACF,GAAG;AACD,WAAO,KAAK,KAAK,IAAI,KAAK,OAAO,YAAY,MAAM,CAAC,MAAM,KAAK;AAAA;AAAA,EAEjE;AAAA,EACA,GAAG,OAAO;AACR,WAAO;AAAA,EACT;AAAA,EACA,KAAK,OAAO;AACV,UAAM,UAAU,MAAM;AACtB,UAAM,QAAQ,MAAM;AACpB,QAAI,OAAO;AACX,aAAS,IAAI,GAAG,IAAI,MAAM,MAAM,QAAQ,KAAK;AAC3C,YAAM,OAAO,MAAM,MAAM,CAAC;AAC1B,cAAQ,KAAK,SAAS,IAAI;AAAA,IAC5B;AACA,UAAM,OAAO,UAAU,OAAO;AAC9B,UAAM,YAAY,WAAW,UAAU,IAAI,aAAa,QAAQ,MAAM;AACtE,WAAO,MAAM,OAAO,YAAY,QAAQ,OAAO,OAAO,OAAO;AAAA,EAC/D;AAAA,EACA,SAAS,MAAM;AACb,QAAI,WAAW;AACf,QAAI,KAAK,MAAM;AACb,YAAM,WAAW,KAAK,SAAS;AAAA,QAC7B,SAAS,CAAC,CAAC,KAAK;AAAA,MAClB,CAAC;AACD,UAAI,KAAK,OAAO;AACd,YAAI,KAAK,OAAO,CAAC,GAAG,SAAS,aAAa;AACxC,eAAK,OAAO,CAAC,EAAE,OAAO,WAAW,MAAM,KAAK,OAAO,CAAC,EAAE;AACtD,cAAI,KAAK,OAAO,CAAC,EAAE,UAAU,KAAK,OAAO,CAAC,EAAE,OAAO,SAAS,KAAK,KAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,SAAS,QAAQ;AACzG,iBAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,OAAO,WAAW,MAAM,QAAQ,KAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,IAAI;AACtF,iBAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,UAAU;AAAA,UACrC;AAAA,QACF,OAAO;AACL,eAAK,OAAO,QAAQ;AAAA,YAClB,MAAM;AAAA,YACN,KAAK,WAAW;AAAA,YAChB,MAAM,WAAW;AAAA,YACjB,SAAS;AAAA,UACX,CAAC;AAAA,QACH;AAAA,MACF,OAAO;AACL,oBAAY,WAAW;AAAA,MACzB;AAAA,IACF;AACA,gBAAY,KAAK,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,KAAK,KAAK;AACvD,WAAO,OAAO,QAAQ;AAAA;AAAA,EAExB;AAAA,EACA,SAAS;AAAA,IACP;AAAA,EACF,GAAG;AACD,WAAO,aAAa,UAAU,gBAAgB,MAAM;AAAA,EACtD;AAAA,EACA,UAAU;AAAA,IACR;AAAA,EACF,GAAG;AACD,WAAO,MAAM,KAAK,OAAO,YAAY,MAAM,CAAC;AAAA;AAAA,EAE9C;AAAA,EACA,MAAM,OAAO;AACX,QAAI,SAAS;AACb,QAAI,OAAO;AACX,aAAS,IAAI,GAAG,IAAI,MAAM,OAAO,QAAQ,KAAK;AAC5C,cAAQ,KAAK,UAAU,MAAM,OAAO,CAAC,CAAC;AAAA,IACxC;AACA,cAAU,KAAK,SAAS;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AACD,QAAI,OAAO;AACX,aAAS,IAAI,GAAG,IAAI,MAAM,KAAK,QAAQ,KAAK;AAC1C,YAAM,MAAM,MAAM,KAAK,CAAC;AACxB,aAAO;AACP,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,gBAAQ,KAAK,UAAU,IAAI,CAAC,CAAC;AAAA,MAC/B;AACA,cAAQ,KAAK,SAAS;AAAA,QACpB,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AACA,QAAI,KAAM,QAAO,UAAU,IAAI;AAC/B,WAAO,uBAAuB,SAAS,eAAe,OAAO;AAAA,EAC/D;AAAA,EACA,SAAS;AAAA,IACP;AAAA,EACF,GAAG;AACD,WAAO;AAAA,EACT,IAAI;AAAA;AAAA,EAEJ;AAAA,EACA,UAAU,OAAO;AACf,UAAM,UAAU,KAAK,OAAO,YAAY,MAAM,MAAM;AACpD,UAAM,OAAO,MAAM,SAAS,OAAO;AACnC,UAAM,OAAO,MAAM,QAAQ,IAAI,IAAI,WAAW,MAAM,KAAK,OAAO,IAAI,IAAI;AACxE,WAAO,OAAO,UAAU,KAAK,IAAI;AAAA;AAAA,EAEnC;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO;AAAA,IACL;AAAA,EACF,GAAG;AACD,WAAO,WAAW,KAAK,OAAO,YAAY,MAAM,CAAC;AAAA,EACnD;AAAA,EACA,GAAG;AAAA,IACD;AAAA,EACF,GAAG;AACD,WAAO,OAAO,KAAK,OAAO,YAAY,MAAM,CAAC;AAAA,EAC/C;AAAA,EACA,SAAS;AAAA,IACP;AAAA,EACF,GAAG;AACD,WAAO,SAAS,QAAQ,MAAM,IAAI,CAAC;AAAA,EACrC;AAAA,EACA,GAAG,OAAO;AACR,WAAO;AAAA,EACT;AAAA,EACA,IAAI;AAAA,IACF;AAAA,EACF,GAAG;AACD,WAAO,QAAQ,KAAK,OAAO,YAAY,MAAM,CAAC;AAAA,EAChD;AAAA,EACA,KAAK;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG;AACD,UAAM,OAAO,KAAK,OAAO,YAAY,MAAM;AAC3C,UAAM,YAAY,SAAS,IAAI;AAC/B,QAAI,cAAc,MAAM;AACtB,aAAO;AAAA,IACT;AACA,WAAO;AACP,QAAI,MAAM,cAAc,OAAO;AAC/B,QAAI,OAAO;AACT,aAAO,aAAa,QAAQ,KAAK,IAAI;AAAA,IACvC;AACA,WAAO,MAAM,OAAO;AACpB,WAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG;AACD,QAAI,QAAQ;AACV,aAAO,KAAK,OAAO,YAAY,QAAQ,KAAK,OAAO,YAAY;AAAA,IACjE;AACA,UAAM,YAAY,SAAS,IAAI;AAC/B,QAAI,cAAc,MAAM;AACtB,aAAO,QAAQ,IAAI;AAAA,IACrB;AACA,WAAO;AACP,QAAI,MAAM,aAAa,IAAI,UAAU,IAAI;AACzC,QAAI,OAAO;AACT,aAAO,WAAW,QAAQ,KAAK,CAAC;AAAA,IAClC;AACA,WAAO;AACP,WAAO;AAAA,EACT;AAAA,EACA,KAAK,OAAO;AACV,WAAO,YAAY,SAAS,MAAM,SAAS,KAAK,OAAO,YAAY,MAAM,MAAM,IAAI,aAAa,SAAS,MAAM,UAAU,MAAM,OAAO,QAAQ,MAAM,IAAI;AAAA,EAC1J;AACF;AAGA,IAAI,gBAAgB,MAAM;AAAA;AAAA,EAExB,OAAO;AAAA,IACL;AAAA,EACF,GAAG;AACD,WAAO;AAAA,EACT;AAAA,EACA,GAAG;AAAA,IACD;AAAA,EACF,GAAG;AACD,WAAO;AAAA,EACT;AAAA,EACA,SAAS;AAAA,IACP;AAAA,EACF,GAAG;AACD,WAAO;AAAA,EACT;AAAA,EACA,IAAI;AAAA,IACF;AAAA,EACF,GAAG;AACD,WAAO;AAAA,EACT;AAAA,EACA,KAAK;AAAA,IACH;AAAA,EACF,GAAG;AACD,WAAO;AAAA,EACT;AAAA,EACA,KAAK;AAAA,IACH;AAAA,EACF,GAAG;AACD,WAAO;AAAA,EACT;AAAA,EACA,KAAK;AAAA,IACH;AAAA,EACF,GAAG;AACD,WAAO,KAAK;AAAA,EACd;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,EACF,GAAG;AACD,WAAO,KAAK;AAAA,EACd;AAAA,EACA,KAAK;AACH,WAAO;AAAA,EACT;AACF;AAGA,IAAI,UAAU,MAAM,SAAS;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,UAAU;AACpB,SAAK,UAAU,YAAY;AAC3B,SAAK,QAAQ,WAAW,KAAK,QAAQ,YAAY,IAAI,UAAU;AAC/D,SAAK,WAAW,KAAK,QAAQ;AAC7B,SAAK,SAAS,UAAU,KAAK;AAC7B,SAAK,SAAS,SAAS;AACvB,SAAK,eAAe,IAAI,cAAc;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,MAAM,QAAQ,UAAU;AAC7B,UAAM,UAAU,IAAI,SAAS,QAAQ;AACrC,WAAO,QAAQ,MAAM,MAAM;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,YAAY,QAAQ,UAAU;AACnC,UAAM,UAAU,IAAI,SAAS,QAAQ;AACrC,WAAO,QAAQ,YAAY,MAAM;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,QAAQ,MAAM,MAAM;AACxB,QAAI,MAAM;AACV,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAM,WAAW,OAAO,CAAC;AACzB,UAAI,KAAK,QAAQ,YAAY,YAAY,SAAS,IAAI,GAAG;AACvD,cAAM,eAAe;AACrB,cAAM,MAAM,KAAK,QAAQ,WAAW,UAAU,aAAa,IAAI,EAAE,KAAK;AAAA,UACpE,QAAQ;AAAA,QACV,GAAG,YAAY;AACf,YAAI,QAAQ,SAAS,CAAC,CAAC,SAAS,MAAM,WAAW,QAAQ,SAAS,cAAc,QAAQ,QAAQ,aAAa,MAAM,EAAE,SAAS,aAAa,IAAI,GAAG;AAChJ,iBAAO,OAAO;AACd;AAAA,QACF;AAAA,MACF;AACA,YAAM,QAAQ;AACd,cAAQ,MAAM,MAAM;AAAA,QAClB,KAAK,SACH;AACE,iBAAO,KAAK,SAAS,MAAM,KAAK;AAChC;AAAA,QACF;AAAA,QACF,KAAK,MACH;AACE,iBAAO,KAAK,SAAS,GAAG,KAAK;AAC7B;AAAA,QACF;AAAA,QACF,KAAK,WACH;AACE,iBAAO,KAAK,SAAS,QAAQ,KAAK;AAClC;AAAA,QACF;AAAA,QACF,KAAK,QACH;AACE,iBAAO,KAAK,SAAS,KAAK,KAAK;AAC/B;AAAA,QACF;AAAA,QACF,KAAK,SACH;AACE,iBAAO,KAAK,SAAS,MAAM,KAAK;AAChC;AAAA,QACF;AAAA,QACF,KAAK,cACH;AACE,iBAAO,KAAK,SAAS,WAAW,KAAK;AACrC;AAAA,QACF;AAAA,QACF,KAAK,QACH;AACE,iBAAO,KAAK,SAAS,KAAK,KAAK;AAC/B;AAAA,QACF;AAAA,QACF,KAAK,QACH;AACE,iBAAO,KAAK,SAAS,KAAK,KAAK;AAC/B;AAAA,QACF;AAAA,QACF,KAAK,aACH;AACE,iBAAO,KAAK,SAAS,UAAU,KAAK;AACpC;AAAA,QACF;AAAA,QACF,KAAK,QACH;AACE,cAAI,YAAY;AAChB,cAAI,OAAO,KAAK,SAAS,KAAK,SAAS;AACvC,iBAAO,IAAI,IAAI,OAAO,UAAU,OAAO,IAAI,CAAC,EAAE,SAAS,QAAQ;AAC7D,wBAAY,OAAO,EAAE,CAAC;AACtB,oBAAQ,OAAO,KAAK,SAAS,KAAK,SAAS;AAAA,UAC7C;AACA,cAAI,KAAK;AACP,mBAAO,KAAK,SAAS,UAAU;AAAA,cAC7B,MAAM;AAAA,cACN,KAAK;AAAA,cACL,MAAM;AAAA,cACN,QAAQ,CAAC;AAAA,gBACP,MAAM;AAAA,gBACN,KAAK;AAAA,gBACL,MAAM;AAAA,gBACN,SAAS;AAAA,cACX,CAAC;AAAA,YACH,CAAC;AAAA,UACH,OAAO;AACL,mBAAO;AAAA,UACT;AACA;AAAA,QACF;AAAA,QACF,SACE;AACE,gBAAM,SAAS,iBAAiB,MAAM,OAAO;AAC7C,cAAI,KAAK,QAAQ,QAAQ;AACvB,oBAAQ,MAAM,MAAM;AACpB,mBAAO;AAAA,UACT,OAAO;AACL,kBAAM,IAAI,MAAM,MAAM;AAAA,UACxB;AAAA,QACF;AAAA,MACJ;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY,QAAQ,WAAW,KAAK,UAAU;AAC5C,QAAI,MAAM;AACV,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAM,WAAW,OAAO,CAAC;AACzB,UAAI,KAAK,QAAQ,YAAY,YAAY,SAAS,IAAI,GAAG;AACvD,cAAM,MAAM,KAAK,QAAQ,WAAW,UAAU,SAAS,IAAI,EAAE,KAAK;AAAA,UAChE,QAAQ;AAAA,QACV,GAAG,QAAQ;AACX,YAAI,QAAQ,SAAS,CAAC,CAAC,UAAU,QAAQ,QAAQ,SAAS,UAAU,MAAM,YAAY,MAAM,OAAO,MAAM,EAAE,SAAS,SAAS,IAAI,GAAG;AAClI,iBAAO,OAAO;AACd;AAAA,QACF;AAAA,MACF;AACA,YAAM,QAAQ;AACd,cAAQ,MAAM,MAAM;AAAA,QAClB,KAAK,UACH;AACE,iBAAO,SAAS,KAAK,KAAK;AAC1B;AAAA,QACF;AAAA,QACF,KAAK,QACH;AACE,iBAAO,SAAS,KAAK,KAAK;AAC1B;AAAA,QACF;AAAA,QACF,KAAK,QACH;AACE,iBAAO,SAAS,KAAK,KAAK;AAC1B;AAAA,QACF;AAAA,QACF,KAAK,SACH;AACE,iBAAO,SAAS,MAAM,KAAK;AAC3B;AAAA,QACF;AAAA,QACF,KAAK,UACH;AACE,iBAAO,SAAS,OAAO,KAAK;AAC5B;AAAA,QACF;AAAA,QACF,KAAK,MACH;AACE,iBAAO,SAAS,GAAG,KAAK;AACxB;AAAA,QACF;AAAA,QACF,KAAK,YACH;AACE,iBAAO,SAAS,SAAS,KAAK;AAC9B;AAAA,QACF;AAAA,QACF,KAAK,MACH;AACE,iBAAO,SAAS,GAAG,KAAK;AACxB;AAAA,QACF;AAAA,QACF,KAAK,OACH;AACE,iBAAO,SAAS,IAAI,KAAK;AACzB;AAAA,QACF;AAAA,QACF,KAAK,QACH;AACE,iBAAO,SAAS,KAAK,KAAK;AAC1B;AAAA,QACF;AAAA,QACF,SACE;AACE,gBAAM,SAAS,iBAAiB,MAAM,OAAO;AAC7C,cAAI,KAAK,QAAQ,QAAQ;AACvB,oBAAQ,MAAM,MAAM;AACpB,mBAAO;AAAA,UACT,OAAO;AACL,kBAAM,IAAI,MAAM,MAAM;AAAA,UACxB;AAAA,QACF;AAAA,MACJ;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAGA,IAAI,SAAS,MAAM;AAAA,EACjB;AAAA,EACA;AAAA,EACA,YAAY,UAAU;AACpB,SAAK,UAAU,YAAY;AAAA,EAC7B;AAAA,EACA,OAAO,mBAAkC,oBAAI,IAAI,CAAC,cAAc,eAAe,kBAAkB,CAAC;AAAA;AAAA;AAAA;AAAA,EAIlG,WAAW,UAAU;AACnB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY,OAAO;AACjB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB,QAAQ;AACvB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AACb,WAAO,KAAK,QAAQ,OAAO,MAAM,OAAO;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB;AACd,WAAO,KAAK,QAAQ,QAAQ,QAAQ,QAAQ;AAAA,EAC9C;AACF;AAGA,IAAI,SAAS,MAAM;AAAA,EACjB,WAAW,aAAa;AAAA,EACxB,UAAU,KAAK;AAAA,EACf,QAAQ,KAAK,cAAc,IAAI;AAAA,EAC/B,cAAc,KAAK,cAAc,KAAK;AAAA,EACtC,SAAS;AAAA,EACT,WAAW;AAAA,EACX,eAAe;AAAA,EACf,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,eAAe,MAAM;AACnB,SAAK,IAAI,GAAG,IAAI;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,QAAQ,UAAU;AAC3B,QAAI,SAAS,CAAC;AACd,eAAW,SAAS,QAAQ;AAC1B,eAAS,OAAO,OAAO,SAAS,KAAK,MAAM,KAAK,CAAC;AACjD,cAAQ,MAAM,MAAM;AAAA,QAClB,KAAK,SACH;AACE,gBAAM,aAAa;AACnB,qBAAW,QAAQ,WAAW,QAAQ;AACpC,qBAAS,OAAO,OAAO,KAAK,WAAW,KAAK,QAAQ,QAAQ,CAAC;AAAA,UAC/D;AACA,qBAAW,OAAO,WAAW,MAAM;AACjC,uBAAW,QAAQ,KAAK;AACtB,uBAAS,OAAO,OAAO,KAAK,WAAW,KAAK,QAAQ,QAAQ,CAAC;AAAA,YAC/D;AAAA,UACF;AACA;AAAA,QACF;AAAA,QACF,KAAK,QACH;AACE,gBAAM,YAAY;AAClB,mBAAS,OAAO,OAAO,KAAK,WAAW,UAAU,OAAO,QAAQ,CAAC;AACjE;AAAA,QACF;AAAA,QACF,SACE;AACE,gBAAM,eAAe;AACrB,cAAI,KAAK,SAAS,YAAY,cAAc,aAAa,IAAI,GAAG;AAC9D,iBAAK,SAAS,WAAW,YAAY,aAAa,IAAI,EAAE,QAAQ,iBAAe;AAC7E,oBAAM,UAAU,aAAa,WAAW,EAAE,KAAK,QAAQ;AACvD,uBAAS,OAAO,OAAO,KAAK,WAAW,SAAS,QAAQ,CAAC;AAAA,YAC3D,CAAC;AAAA,UACH,WAAW,aAAa,QAAQ;AAC9B,qBAAS,OAAO,OAAO,KAAK,WAAW,aAAa,QAAQ,QAAQ,CAAC;AAAA,UACvE;AAAA,QACF;AAAA,MACJ;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,MAAM;AACX,UAAM,aAAa,KAAK,SAAS,cAAc;AAAA,MAC7C,WAAW,CAAC;AAAA,MACZ,aAAa,CAAC;AAAA,IAChB;AACA,SAAK,QAAQ,UAAQ;AACnB,YAAM,OAAO,mBACR;AAEL,WAAK,QAAQ,KAAK,SAAS,SAAS,KAAK,SAAS;AAClD,UAAI,KAAK,YAAY;AACnB,aAAK,WAAW,QAAQ,SAAO;AAC7B,cAAI,CAAC,IAAI,MAAM;AACb,kBAAM,IAAI,MAAM,yBAAyB;AAAA,UAC3C;AACA,cAAI,cAAc,KAAK;AACrB,kBAAM,eAAe,WAAW,UAAU,IAAI,IAAI;AAClD,gBAAI,cAAc;AAChB,yBAAW,UAAU,IAAI,IAAI,IAAI,YAAa,OAAO;AACnD,oBAAI,MAAM,IAAI,SAAS,MAAM,MAAM,KAAK;AACxC,oBAAI,QAAQ,OAAO;AACjB,wBAAM,aAAa,MAAM,MAAM,KAAK;AAAA,gBACtC;AACA,uBAAO;AAAA,cACT;AAAA,YACF,OAAO;AACL,yBAAW,UAAU,IAAI,IAAI,IAAI,IAAI;AAAA,YACvC;AAAA,UACF;AACA,cAAI,eAAe,KAAK;AACtB,gBAAI,CAAC,IAAI,SAAS,IAAI,UAAU,WAAW,IAAI,UAAU,UAAU;AACjE,oBAAM,IAAI,MAAM,6CAA6C;AAAA,YAC/D;AACA,kBAAM,WAAW,WAAW,IAAI,KAAK;AACrC,gBAAI,UAAU;AACZ,uBAAS,QAAQ,IAAI,SAAS;AAAA,YAChC,OAAO;AACL,yBAAW,IAAI,KAAK,IAAI,CAAC,IAAI,SAAS;AAAA,YACxC;AACA,gBAAI,IAAI,OAAO;AACb,kBAAI,IAAI,UAAU,SAAS;AACzB,oBAAI,WAAW,YAAY;AACzB,6BAAW,WAAW,KAAK,IAAI,KAAK;AAAA,gBACtC,OAAO;AACL,6BAAW,aAAa,CAAC,IAAI,KAAK;AAAA,gBACpC;AAAA,cACF,WAAW,IAAI,UAAU,UAAU;AACjC,oBAAI,WAAW,aAAa;AAC1B,6BAAW,YAAY,KAAK,IAAI,KAAK;AAAA,gBACvC,OAAO;AACL,6BAAW,cAAc,CAAC,IAAI,KAAK;AAAA,gBACrC;AAAA,cACF;AAAA,YACF;AAAA,UACF;AACA,cAAI,iBAAiB,OAAO,IAAI,aAAa;AAC3C,uBAAW,YAAY,IAAI,IAAI,IAAI,IAAI;AAAA,UACzC;AAAA,QACF,CAAC;AACD,aAAK,aAAa;AAAA,MACpB;AACA,UAAI,KAAK,UAAU;AACjB,cAAM,WAAW,KAAK,SAAS,YAAY,IAAI,UAAU,KAAK,QAAQ;AACtE,mBAAW,QAAQ,KAAK,UAAU;AAChC,cAAI,EAAE,QAAQ,WAAW;AACvB,kBAAM,IAAI,MAAM,aAAa,IAAI,kBAAkB;AAAA,UACrD;AACA,cAAI,CAAC,WAAW,QAAQ,EAAE,SAAS,IAAI,GAAG;AACxC;AAAA,UACF;AACA,gBAAM,eAAe;AACrB,gBAAM,eAAe,KAAK,SAAS,YAAY;AAC/C,gBAAM,eAAe,SAAS,YAAY;AAC1C,mBAAS,YAAY,IAAI,IAAI,UAAU;AACrC,gBAAI,MAAM,aAAa,MAAM,UAAU,KAAK;AAC5C,gBAAI,QAAQ,OAAO;AACjB,oBAAM,aAAa,MAAM,UAAU,KAAK;AAAA,YAC1C;AACA,mBAAO,OAAO;AAAA,UAChB;AAAA,QACF;AACA,aAAK,WAAW;AAAA,MAClB;AACA,UAAI,KAAK,WAAW;AAClB,cAAM,YAAY,KAAK,SAAS,aAAa,IAAI,WAAW,KAAK,QAAQ;AACzE,mBAAW,QAAQ,KAAK,WAAW;AACjC,cAAI,EAAE,QAAQ,YAAY;AACxB,kBAAM,IAAI,MAAM,cAAc,IAAI,kBAAkB;AAAA,UACtD;AACA,cAAI,CAAC,WAAW,SAAS,OAAO,EAAE,SAAS,IAAI,GAAG;AAChD;AAAA,UACF;AACA,gBAAM,gBAAgB;AACtB,gBAAM,gBAAgB,KAAK,UAAU,aAAa;AAClD,gBAAM,gBAAgB,UAAU,aAAa;AAC7C,oBAAU,aAAa,IAAI,IAAI,UAAU;AACvC,gBAAI,MAAM,cAAc,MAAM,WAAW,KAAK;AAC9C,gBAAI,QAAQ,OAAO;AACjB,oBAAM,cAAc,MAAM,WAAW,KAAK;AAAA,YAC5C;AACA,mBAAO;AAAA,UACT;AAAA,QACF;AACA,aAAK,YAAY;AAAA,MACnB;AACA,UAAI,KAAK,OAAO;AACd,cAAM,QAAQ,KAAK,SAAS,SAAS,IAAI,OAAO;AAChD,mBAAW,QAAQ,KAAK,OAAO;AAC7B,cAAI,EAAE,QAAQ,QAAQ;AACpB,kBAAM,IAAI,MAAM,SAAS,IAAI,kBAAkB;AAAA,UACjD;AACA,cAAI,CAAC,WAAW,OAAO,EAAE,SAAS,IAAI,GAAG;AACvC;AAAA,UACF;AACA,gBAAM,YAAY;AAClB,gBAAM,YAAY,KAAK,MAAM,SAAS;AACtC,gBAAM,WAAW,MAAM,SAAS;AAChC,cAAI,OAAO,iBAAiB,IAAI,IAAI,GAAG;AACrC,kBAAM,SAAS,IAAI,SAAO;AACxB,kBAAI,KAAK,SAAS,OAAO;AACvB,uBAAO,QAAQ,QAAQ,UAAU,KAAK,OAAO,GAAG,CAAC,EAAE,KAAK,UAAQ;AAC9D,yBAAO,SAAS,KAAK,OAAO,IAAI;AAAA,gBAClC,CAAC;AAAA,cACH;AACA,oBAAM,MAAM,UAAU,KAAK,OAAO,GAAG;AACrC,qBAAO,SAAS,KAAK,OAAO,GAAG;AAAA,YACjC;AAAA,UACF,OAAO;AACL,kBAAM,SAAS,IAAI,IAAI,UAAU;AAC/B,kBAAI,MAAM,UAAU,MAAM,OAAO,KAAK;AACtC,kBAAI,QAAQ,OAAO;AACjB,sBAAM,SAAS,MAAM,OAAO,KAAK;AAAA,cACnC;AACA,qBAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AACA,aAAK,QAAQ;AAAA,MACf;AACA,UAAI,KAAK,YAAY;AACnB,cAAM,cAAc,KAAK,SAAS;AAClC,cAAM,iBAAiB,KAAK;AAC5B,aAAK,aAAa,SAAU,OAAO;AACjC,cAAI,SAAS,CAAC;AACd,iBAAO,KAAK,eAAe,KAAK,MAAM,KAAK,CAAC;AAC5C,cAAI,aAAa;AACf,qBAAS,OAAO,OAAO,YAAY,KAAK,MAAM,KAAK,CAAC;AAAA,UACtD;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AACA,WAAK,WAAW,kCACX,KAAK,WACL;AAAA,IAEP,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,WAAW,KAAK;AACd,SAAK,WAAW,kCACX,KAAK,WACL;AAEL,WAAO;AAAA,EACT;AAAA,EACA,MAAM,KAAK,UAAU;AACnB,WAAO,OAAO,IAAI,KAAK,YAAY,KAAK,QAAQ;AAAA,EAClD;AAAA,EACA,OAAO,QAAQ,UAAU;AACvB,WAAO,QAAQ,MAAM,QAAQ,YAAY,KAAK,QAAQ;AAAA,EACxD;AAAA,EACA,cAAc,WAAW;AACvB,UAAM,SAAS,CAAC,KAAK,aAAa;AAChC,YAAM,UAAU,mBACX;AAEL,YAAM,MAAM,kCACP,KAAK,WACL;AAEL,YAAM,aAAa,KAAK,QAAQ,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,IAAI,KAAK;AACzD,UAAI,KAAK,SAAS,UAAU,QAAQ,QAAQ,UAAU,OAAO;AAC3D,eAAO,WAAW,IAAI,MAAM,oIAAoI,CAAC;AAAA,MACnK;AACA,UAAI,OAAO,QAAQ,eAAe,QAAQ,MAAM;AAC9C,eAAO,WAAW,IAAI,MAAM,gDAAgD,CAAC;AAAA,MAC/E;AACA,UAAI,OAAO,QAAQ,UAAU;AAC3B,eAAO,WAAW,IAAI,MAAM,0CAA0C,OAAO,UAAU,SAAS,KAAK,GAAG,IAAI,mBAAmB,CAAC;AAAA,MAClI;AACA,UAAI,IAAI,OAAO;AACb,YAAI,MAAM,UAAU;AACpB,YAAI,MAAM,QAAQ;AAAA,MACpB;AACA,YAAM,SAAS,IAAI,QAAQ,IAAI,MAAM,aAAa,IAAI,YAAY,OAAO,MAAM,OAAO;AACtF,YAAM,UAAU,IAAI,QAAQ,IAAI,MAAM,cAAc,IAAI,YAAY,QAAQ,QAAQ,QAAQ;AAC5F,UAAI,IAAI,OAAO;AACb,eAAO,QAAQ,QAAQ,IAAI,QAAQ,IAAI,MAAM,WAAW,GAAG,IAAI,GAAG,EAAE,KAAK,UAAQ,OAAO,MAAM,GAAG,CAAC,EAAE,KAAK,YAAU,IAAI,QAAQ,IAAI,MAAM,iBAAiB,MAAM,IAAI,MAAM,EAAE,KAAK,YAAU,IAAI,aAAa,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,UAAU,CAAC,EAAE,KAAK,MAAM,MAAM,IAAI,MAAM,EAAE,KAAK,YAAU,QAAQ,QAAQ,GAAG,CAAC,EAAE,KAAK,WAAS,IAAI,QAAQ,IAAI,MAAM,YAAY,KAAK,IAAI,KAAK,EAAE,MAAM,UAAU;AAAA,MACrZ;AACA,UAAI;AACF,YAAI,IAAI,OAAO;AACb,gBAAM,IAAI,MAAM,WAAW,GAAG;AAAA,QAChC;AACA,YAAI,SAAS,OAAO,KAAK,GAAG;AAC5B,YAAI,IAAI,OAAO;AACb,mBAAS,IAAI,MAAM,iBAAiB,MAAM;AAAA,QAC5C;AACA,YAAI,IAAI,YAAY;AAClB,eAAK,WAAW,QAAQ,IAAI,UAAU;AAAA,QACxC;AACA,YAAI,QAAQ,QAAQ,QAAQ,GAAG;AAC/B,YAAI,IAAI,OAAO;AACb,kBAAQ,IAAI,MAAM,YAAY,KAAK;AAAA,QACrC;AACA,eAAO;AAAA,MACT,SAAS,GAAG;AACV,eAAO,WAAW,CAAC;AAAA,MACrB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,QAAQ,OAAO;AACrB,WAAO,OAAK;AACV,QAAE,WAAW;AACb,UAAI,QAAQ;AACV,cAAM,MAAM,mCAAmC,QAAQ,EAAE,UAAU,IAAI,IAAI,IAAI;AAC/E,YAAI,OAAO;AACT,iBAAO,QAAQ,QAAQ,GAAG;AAAA,QAC5B;AACA,eAAO;AAAA,MACT;AACA,UAAI,OAAO;AACT,eAAO,QAAQ,OAAO,CAAC;AAAA,MACzB;AACA,YAAM;AAAA,IACR;AAAA,EACF;AACF;AAGA,IAAI,iBAAiB,IAAI,OAAO;AAChC,SAAS,OAAO,KAAK,KAAK;AACxB,SAAO,eAAe,MAAM,KAAK,GAAG;AACtC;AACA,OAAO,UAAU,OAAO,aAAa,SAAU,UAAU;AACvD,iBAAe,WAAW,QAAQ;AAClC,SAAO,WAAW,eAAe;AACjC,iBAAe,OAAO,QAAQ;AAC9B,SAAO;AACT;AACA,OAAO,cAAc;AACrB,OAAO,WAAW;AAClB,OAAO,MAAM,YAAa,MAAM;AAC9B,iBAAe,IAAI,GAAG,IAAI;AAC1B,SAAO,WAAW,eAAe;AACjC,iBAAe,OAAO,QAAQ;AAC9B,SAAO;AACT;AACA,OAAO,aAAa,SAAU,QAAQ,UAAU;AAC9C,SAAO,eAAe,WAAW,QAAQ,QAAQ;AACnD;AACA,OAAO,cAAc,eAAe;AACpC,OAAO,SAAS;AAChB,OAAO,SAAS,QAAQ;AACxB,OAAO,WAAW;AAClB,OAAO,eAAe;AACtB,OAAO,QAAQ;AACf,OAAO,QAAQ,OAAO;AACtB,OAAO,YAAY;AACnB,OAAO,QAAQ;AACf,OAAO,QAAQ;AACf,IAAI,UAAU,OAAO;AACrB,IAAI,aAAa,OAAO;AACxB,IAAI,MAAM,OAAO;AACjB,IAAI,aAAa,OAAO;AACxB,IAAI,cAAc,OAAO;AAEzB,IAAI,SAAS,QAAQ;AACrB,IAAI,QAAQ,OAAO;;;ACptEnB,SAAS,mBAAmB,UAAU;AAAA,EACpC;AACF,GAAG;AACD,QAAM,YAAY,SAAS,QAAQ,WAAW,IAAI;AAClD,QAAM,0BAA0B,UAAU,QAAQ,WAAW,IAAI;AACjE,QAAM,qBAAqB,OAAO,uBAAuB;AACzD,MAAI,qBAAqB,OAAO;AAC9B,WAAO,mBAAmB,QAAQ,MAAM,QAAQ;AAAA,EAClD;AACA,SAAO;AACT;AACA,OAAO,oBAAoB,oBAAoB;AAC/C,SAAS,gBAAgB,UAAU,SAAS,CAAC,GAAG;AAC9C,QAAM,uBAAuB,mBAAmB,UAAU,MAAM;AAChE,QAAM,QAAQ,OAAO,MAAM,oBAAoB;AAC/C,QAAM,QAAQ,CAAC,CAAC,CAAC;AACjB,MAAI,cAAc;AAClB,WAAS,YAAY,MAAM,aAAa,UAAU;AAChD,QAAI,KAAK,SAAS,QAAQ;AACxB,YAAM,YAAY,KAAK,KAAK,MAAM,IAAI;AACtC,gBAAU,QAAQ,CAAC,UAAU,UAAU;AACrC,YAAI,UAAU,GAAG;AACf;AACA,gBAAM,KAAK,CAAC,CAAC;AAAA,QACf;AACA,iBAAS,MAAM,GAAG,EAAE,QAAQ,UAAQ;AAClC,iBAAO,KAAK,QAAQ,UAAU,GAAG;AACjC,cAAI,MAAM;AACR,kBAAM,WAAW,EAAE,KAAK;AAAA,cACtB,SAAS;AAAA,cACT,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH,WAAW,KAAK,SAAS,YAAY,KAAK,SAAS,MAAM;AACvD,WAAK,OAAO,QAAQ,iBAAe;AACjC,oBAAY,aAAa,KAAK,IAAI;AAAA,MACpC,CAAC;AAAA,IACH,WAAW,KAAK,SAAS,QAAQ;AAC/B,YAAM,WAAW,EAAE,KAAK;AAAA,QACtB,SAAS,KAAK;AAAA,QACd,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAAA,EACF;AACA,SAAO,aAAa,aAAa;AACjC,QAAM,QAAQ,cAAY;AACxB,QAAI,SAAS,SAAS,aAAa;AACjC,eAAS,QAAQ,QAAQ,iBAAe;AACtC,oBAAY,WAAW;AAAA,MACzB,CAAC;AAAA,IACH,WAAW,SAAS,SAAS,QAAQ;AACnC,YAAM,WAAW,EAAE,KAAK;AAAA,QACtB,SAAS,SAAS;AAAA,QAClB,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,OAAO,iBAAiB,iBAAiB;AACzC,SAAS,eAAe,UAAU;AAAA,EAChC;AACF,IAAI,CAAC,GAAG;AACN,QAAM,QAAQ,OAAO,MAAM,QAAQ;AACnC,WAAS,OAAO,MAAM;AACpB,QAAI,KAAK,SAAS,QAAQ;AACxB,UAAI,qBAAqB,OAAO;AAC9B,eAAO,KAAK,KAAK,QAAQ,SAAS,OAAO,EAAE,QAAQ,MAAM,QAAQ;AAAA,MACnE;AACA,aAAO,KAAK,KAAK,QAAQ,SAAS,OAAO;AAAA,IAC3C,WAAW,KAAK,SAAS,UAAU;AACjC,aAAO,WAAW,KAAK,QAAQ,IAAI,MAAM,EAAE,KAAK,EAAE,CAAC;AAAA,IACrD,WAAW,KAAK,SAAS,MAAM;AAC7B,aAAO,OAAO,KAAK,QAAQ,IAAI,MAAM,EAAE,KAAK,EAAE,CAAC;AAAA,IACjD,WAAW,KAAK,SAAS,aAAa;AACpC,aAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,EAAE,KAAK,EAAE,CAAC;AAAA,IAChD,WAAW,KAAK,SAAS,SAAS;AAChC,aAAO;AAAA,IACT,WAAW,KAAK,SAAS,QAAQ;AAC/B,aAAO,GAAG,KAAK,IAAI;AAAA,IACrB,WAAW,KAAK,SAAS,UAAU;AACjC,aAAO,KAAK;AAAA,IACd;AACA,WAAO,yBAAyB,KAAK,IAAI;AAAA,EAC3C;AACA,SAAO,QAAQ,QAAQ;AACvB,SAAO,MAAM,IAAI,MAAM,EAAE,KAAK,EAAE;AAClC;AACA,OAAO,gBAAgB,gBAAgB;AAGvC,SAAS,iBAAiB,MAAM;AAC9B,MAAI,KAAK,WAAW;AAClB,WAAO,CAAC,GAAG,IAAI,KAAK,UAAU,EAAE,QAAQ,IAAI,CAAC,EAAE,IAAI,OAAK,EAAE,OAAO;AAAA,EACnE;AACA,SAAO,CAAC,GAAG,IAAI;AACjB;AACA,OAAO,kBAAkB,kBAAkB;AAC3C,SAAS,oBAAoB,UAAU,MAAM;AAC3C,QAAM,aAAa,iBAAiB,KAAK,OAAO;AAChD,SAAO,6BAA6B,UAAU,CAAC,GAAG,YAAY,KAAK,IAAI;AACzE;AACA,OAAO,qBAAqB,qBAAqB;AACjD,SAAS,6BAA6B,UAAU,WAAW,gBAAgB,MAAM;AAC/E,MAAI,eAAe,WAAW,GAAG;AAC/B,WAAO,CAAC;AAAA,MACN,SAAS,UAAU,KAAK,EAAE;AAAA,MAC1B;AAAA,IACF,GAAG;AAAA,MACD,SAAS;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,CAAC,UAAU,GAAG,IAAI,IAAI;AAC5B,QAAM,UAAU,CAAC,GAAG,WAAW,QAAQ;AACvC,MAAI,SAAS,CAAC;AAAA,IACZ,SAAS,QAAQ,KAAK,EAAE;AAAA,IACxB;AAAA,EACF,CAAC,CAAC,GAAG;AACH,WAAO,6BAA6B,UAAU,SAAS,MAAM,IAAI;AAAA,EACnE;AACA,MAAI,UAAU,WAAW,KAAK,UAAU;AACtC,cAAU,KAAK,QAAQ;AACvB,mBAAe,MAAM;AAAA,EACvB;AACA,SAAO,CAAC;AAAA,IACN,SAAS,UAAU,KAAK,EAAE;AAAA,IAC1B;AAAA,EACF,GAAG;AAAA,IACD,SAAS,eAAe,KAAK,EAAE;AAAA,IAC/B;AAAA,EACF,CAAC;AACH;AACA,OAAO,8BAA8B,8BAA8B;AACnE,SAAS,oBAAoB,MAAM,UAAU;AAC3C,MAAI,KAAK,KAAK,CAAC;AAAA,IACb;AAAA,EACF,MAAM,QAAQ,SAAS,IAAI,CAAC,GAAG;AAC7B,UAAM,IAAI,MAAM,2DAA2D;AAAA,EAC7E;AACA,SAAO,6BAA6B,MAAM,QAAQ;AACpD;AACA,OAAO,qBAAqB,qBAAqB;AACjD,SAAS,6BAA6B,OAAO,UAAU,QAAQ,CAAC,GAAG,UAAU,CAAC,GAAG;AAC/E,MAAI,MAAM,WAAW,GAAG;AACtB,QAAI,QAAQ,SAAS,GAAG;AACtB,YAAM,KAAK,OAAO;AAAA,IACpB;AACA,WAAO,MAAM,SAAS,IAAI,QAAQ,CAAC;AAAA,EACrC;AACA,MAAI,SAAS;AACb,MAAI,MAAM,CAAC,EAAE,YAAY,KAAK;AAC5B,aAAS;AACT,UAAM,MAAM;AAAA,EACd;AACA,QAAM,WAAW,MAAM,MAAM,KAAK;AAAA,IAChC,SAAS;AAAA,IACT,MAAM;AAAA,EACR;AACA,QAAM,mBAAmB,CAAC,GAAG,OAAO;AACpC,MAAI,WAAW,IAAI;AACjB,qBAAiB,KAAK;AAAA,MACpB,SAAS;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AACA,mBAAiB,KAAK,QAAQ;AAC9B,MAAI,SAAS,gBAAgB,GAAG;AAC9B,WAAO,6BAA6B,OAAO,UAAU,OAAO,gBAAgB;AAAA,EAC9E;AACA,MAAI,QAAQ,SAAS,GAAG;AACtB,UAAM,KAAK,OAAO;AAClB,UAAM,QAAQ,QAAQ;AAAA,EACxB,WAAW,SAAS,SAAS;AAC3B,UAAM,CAAC,MAAM,IAAI,IAAI,oBAAoB,UAAU,QAAQ;AAC3D,UAAM,KAAK,CAAC,IAAI,CAAC;AACjB,QAAI,KAAK,SAAS;AAChB,YAAM,QAAQ,IAAI;AAAA,IACpB;AAAA,EACF;AACA,SAAO,6BAA6B,OAAO,UAAU,KAAK;AAC5D;AACA,OAAO,8BAA8B,8BAA8B;AAGnE,SAAS,WAAW,KAAK,SAAS;AAChC,MAAI,SAAS;AACX,QAAI,KAAK,SAAS,OAAO;AAAA,EAC3B;AACF;AACA,OAAO,YAAY,YAAY;AAC/B,SAAe,YAAY,SAAS,MAAM,OAAO,SAAS,gBAAgB,OAAO;AAAA;AAC/E,UAAM,KAAK,QAAQ,OAAO,eAAe;AACzC,OAAG,KAAK,SAAS,GAAG,KAAK,KAAK,IAAI;AAClC,OAAG,KAAK,UAAU,GAAG,KAAK,KAAK,IAAI;AACnC,UAAM,MAAM,GAAG,OAAO,WAAW;AACjC,QAAI,QAAQ,KAAK;AACjB,QAAI,KAAK,SAAS,SAAS,KAAK,KAAK,GAAG;AACtC,cAAQ,MAAM,YAAY,KAAK,MAAM,QAAQ,eAAe,gBAAgB,IAAI,GAAG,WAAU,CAAC;AAAA,IAChG;AACA,UAAM,aAAa,KAAK,SAAS,cAAc;AAC/C,UAAM,OAAO,IAAI,OAAO,MAAM;AAC9B,SAAK,KAAK,KAAK;AACf,eAAW,MAAM,KAAK,UAAU;AAChC,SAAK,KAAK,SAAS,GAAG,UAAU,IAAI,OAAO,EAAE;AAC7C,eAAW,KAAK,KAAK,UAAU;AAC/B,QAAI,MAAM,WAAW,YAAY;AACjC,QAAI,MAAM,eAAe,QAAQ;AACjC,QAAI,MAAM,eAAe,KAAK;AAC9B,QAAI,MAAM,aAAa,QAAQ,IAAI;AACnC,QAAI,MAAM,cAAc,QAAQ;AAChC,QAAI,KAAK,SAAS,8BAA8B;AAChD,QAAI,eAAe;AACjB,UAAI,KAAK,SAAS,UAAU;AAAA,IAC9B;AACA,QAAI,OAAO,IAAI,KAAK,EAAE,sBAAsB;AAC5C,QAAI,KAAK,UAAU,OAAO;AACxB,UAAI,MAAM,WAAW,OAAO;AAC5B,UAAI,MAAM,eAAe,cAAc;AACvC,UAAI,MAAM,SAAS,QAAQ,IAAI;AAC/B,aAAO,IAAI,KAAK,EAAE,sBAAsB;AAAA,IAC1C;AACA,WAAO,GAAG,KAAK;AAAA,EACjB;AAAA;AACA,OAAO,aAAa,aAAa;AACjC,SAAS,YAAY,aAAa,WAAW,YAAY;AACvD,SAAO,YAAY,OAAO,OAAO,EAAE,KAAK,SAAS,kBAAkB,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,KAAK,YAAY,aAAa,MAAM,IAAI,EAAE,KAAK,MAAM,aAAa,IAAI;AAC/J;AACA,OAAO,aAAa,aAAa;AACjC,SAAS,mBAAmB,YAAY,YAAY,MAAM;AACxD,QAAM,cAAc,WAAW,OAAO,MAAM;AAC5C,QAAM,WAAW,YAAY,aAAa,GAAG,UAAU;AACvD,6BAA2B,UAAU,IAAI;AACzC,QAAM,aAAa,SAAS,KAAK,EAAE,sBAAsB;AACzD,cAAY,OAAO;AACnB,SAAO;AACT;AACA,OAAO,oBAAoB,oBAAoB;AAC/C,SAAS,uBAAuB,YAAY,YAAY,MAAM;AAC5D,QAAM,cAAc,WAAW,OAAO,MAAM;AAC5C,QAAM,WAAW,YAAY,aAAa,GAAG,UAAU;AACvD,6BAA2B,UAAU,CAAC;AAAA,IACpC,SAAS;AAAA,IACT,MAAM;AAAA,EACR,CAAC,CAAC;AACF,QAAM,gBAAgB,SAAS,KAAK,GAAG,sBAAsB;AAC7D,MAAI,eAAe;AACjB,gBAAY,OAAO;AAAA,EACrB;AACA,SAAO;AACT;AACA,OAAO,wBAAwB,wBAAwB;AACvD,SAAS,oBAAoB,OAAO,GAAG,gBAAgB,gBAAgB,OAAO;AAC5E,QAAM,aAAa;AACnB,QAAM,aAAa,EAAE,OAAO,GAAG;AAC/B,QAAM,MAAM,WAAW,OAAO,MAAM,EAAE,KAAK,SAAS,YAAY,EAAE,KAAK,SAAS,cAAc;AAC9F,QAAM,cAAc,WAAW,OAAO,MAAM,EAAE,KAAK,KAAK,OAAO;AAC/D,MAAI,YAAY;AAChB,aAAW,QAAQ,gBAAgB;AACjC,UAAM,aAA4B,OAAO,WAAS,mBAAmB,YAAY,YAAY,KAAK,KAAK,OAAO,YAAY;AAC1H,UAAM,kBAAkB,WAAW,IAAI,IAAI,CAAC,IAAI,IAAI,oBAAoB,MAAM,UAAU;AACxF,eAAW,gBAAgB,iBAAiB;AAC1C,YAAM,QAAQ,YAAY,aAAa,WAAW,UAAU;AAC5D,iCAA2B,OAAO,YAAY;AAC9C;AAAA,IACF;AAAA,EACF;AACA,MAAI,eAAe;AACjB,UAAM,OAAO,YAAY,KAAK,EAAE,QAAQ;AACxC,UAAM,UAAU;AAChB,QAAI,KAAK,KAAK,KAAK,IAAI,OAAO,EAAE,KAAK,KAAK,KAAK,IAAI,OAAO,EAAE,KAAK,SAAS,KAAK,QAAQ,IAAI,OAAO,EAAE,KAAK,UAAU,KAAK,SAAS,IAAI,OAAO;AAC5I,WAAO,WAAW,KAAK;AAAA,EACzB,OAAO;AACL,WAAO,YAAY,KAAK;AAAA,EAC1B;AACF;AACA,OAAO,qBAAqB,qBAAqB;AACjD,SAAS,2BAA2B,OAAO,aAAa;AACtD,QAAM,KAAK,EAAE;AACb,cAAY,QAAQ,CAAC,MAAM,UAAU;AACnC,UAAM,aAAa,MAAM,OAAO,OAAO,EAAE,KAAK,cAAc,KAAK,SAAS,OAAO,WAAW,QAAQ,EAAE,KAAK,SAAS,kBAAkB,EAAE,KAAK,eAAe,KAAK,SAAS,WAAW,SAAS,QAAQ;AACtM,QAAI,UAAU,GAAG;AACf,iBAAW,KAAK,KAAK,OAAO;AAAA,IAC9B,OAAO;AACL,iBAAW,KAAK,MAAM,KAAK,OAAO;AAAA,IACpC;AAAA,EACF,CAAC;AACH;AACA,OAAO,4BAA4B,4BAA4B;AAC/D,SAAS,qBAAqB,MAAM;AAClC,SAAO,KAAK;AAAA,IAAQ;AAAA;AAAA,IAEpB,OAAK,aAAa,EAAE,QAAQ,KAAK,GAAG,CAAC;AAAA,EAAQ;AAC/C;AACA,OAAO,sBAAsB,sBAAsB;AACnD,IAAI,aAA4B,OAAO,CAAO,OAQ3B,sBAR2B,IAQ3B,mBAR2B,IAAI,OAAO,IAAI;AAAA,EAC3D,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,UAAU;AAAA,EACV,gBAAgB;AAAA,EAChB,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,mBAAmB;AACrB,IAAI,CAAC,GAAG,QAAW;AACjB,MAAI,MAAM,kBAAkB,MAAM,OAAO,SAAS,SAAS,eAAe,QAAQ,sBAAsB,gBAAgB;AACxH,MAAI,eAAe;AACjB,UAAM,WAAW,eAAe,MAAM,MAAM;AAC5C,UAAM,sBAAsB,qBAAqB,eAAe,QAAQ,CAAC;AACzE,UAAM,gBAAgB,KAAK,QAAQ,SAAS,IAAI;AAChD,UAAM,OAAO;AAAA,MACX;AAAA,MACA,OAAO,SAAS,IAAI,IAAI,gBAAgB;AAAA,MACxC,YAAY,MAAM,QAAQ,SAAS,QAAQ;AAAA,IAC7C;AACA,UAAM,aAAa,MAAM,YAAY,IAAI,MAAM,OAAO,SAAS,gBAAgB;AAC/E,WAAO;AAAA,EACT,OAAO;AACL,UAAM,aAAa,KAAK,QAAQ,eAAe,OAAO;AACtD,UAAM,iBAAiB,gBAAgB,WAAW,QAAQ,QAAQ,OAAO,GAAG,MAAM;AAClF,UAAM,WAAW,oBAAoB,OAAO,IAAI,gBAAgB,OAAO,mBAAmB,KAAK;AAC/F,QAAI,QAAQ;AACV,UAAI,UAAU,KAAK,KAAK,GAAG;AACzB,gBAAQ,MAAM,QAAQ,WAAW,YAAY;AAAA,MAC/C;AACA,YAAM,qBAAqB,MAAM,QAAQ,mBAAmB,EAAE,EAAE,QAAQ,yBAAyB,EAAE,EAAE,QAAQ,iBAAiB,EAAE,EAAE,QAAQ,WAAW,OAAO;AAC5J,qBAAO,QAAQ,EAAE,KAAK,SAAS,kBAAkB;AAAA,IACnD,OAAO;AACL,YAAM,qBAAqB,MAAM,QAAQ,mBAAmB,EAAE,EAAE,QAAQ,yBAAyB,EAAE,EAAE,QAAQ,iBAAiB,EAAE,EAAE,QAAQ,gBAAgB,OAAO;AACjK,qBAAO,QAAQ,EAAE,OAAO,MAAM,EAAE,KAAK,SAAS,mBAAmB,QAAQ,gBAAgB,OAAO,CAAC;AACjG,YAAM,qBAAqB,MAAM,QAAQ,mBAAmB,EAAE,EAAE,QAAQ,yBAAyB,EAAE,EAAE,QAAQ,iBAAiB,EAAE,EAAE,QAAQ,WAAW,OAAO;AAC5J,qBAAO,QAAQ,EAAE,OAAO,MAAM,EAAE,KAAK,SAAS,kBAAkB;AAAA,IAClE;AACA,WAAO;AAAA,EACT;AACF,IAAG,YAAY;", "names": ["i"]}