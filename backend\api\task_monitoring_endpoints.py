"""
Endpoints para Monitoramento de Background Tasks
Fornece visibilidade completa sobre tasks em execução, filas e performance.
"""

import logging
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, status
from fastapi.responses import JSONResponse

from shared.task_manager import task_manager, TaskInfo, TaskStatus, TaskPriority

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/tasks", tags=["task-monitoring"])


@router.get("/stats")
async def get_system_stats():
    """
    Retorna estatísticas gerais do sistema de tasks
    
    Returns:
        Estatísticas completas incluindo tasks ativas, pools de recursos e performance
    """
    try:
        stats = task_manager.get_stats()
        
        return {
            "status": "success",
            "data": {
                "overview": {
                    "total_tasks": stats.get('total_tasks', 0),
                    "completed_tasks": stats.get('completed_tasks', 0),
                    "failed_tasks": stats.get('failed_tasks', 0),
                    "cancelled_tasks": stats.get('cancelled_tasks', 0),
                    "running_tasks": stats.get('running_tasks', 0),
                    "success_rate": round(
                        (stats.get('completed_tasks', 0) / max(stats.get('total_tasks', 1), 1)) * 100, 2
                    )
                },
                "resources": {
                    "max_concurrent_tasks": stats.get('max_concurrent', 10),
                    "current_running": stats.get('running_tasks', 0),
                    "available_slots": stats.get('max_concurrent', 10) - stats.get('running_tasks', 0),
                    "resource_usage": stats.get('resource_usage', {})
                },
                "performance": {
                    "total_processed": stats.get('completed_tasks', 0) + stats.get('failed_tasks', 0),
                    "efficiency": round(
                        (stats.get('completed_tasks', 0) / max(
                            stats.get('completed_tasks', 0) + stats.get('failed_tasks', 0), 1
                        )) * 100, 2
                    )
                }
            }
        }
        
    except Exception as e:
        logger.error(f"Erro ao obter estatísticas: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        )


@router.get("/running")
async def get_running_tasks():
    """
    Lista todas as tasks em execução no momento
    
    Returns:
        Lista de tasks ativas com detalhes de progresso
    """
    try:
        running_tasks = await task_manager.get_running_tasks()
        
        tasks_data = []
        for task in running_tasks:
            tasks_data.append({
                "task_id": task.task_id,
                "task_type": task.task_type,
                "priority": task.priority.name,
                "client_id": task.client_id,
                "client_name": task.client_name,
                "project_id": task.project_id,
                "status": task.status.value,
                "progress": task.progress,
                "elapsed_time": task.elapsed_time,
                "estimated_duration": task.estimated_duration,
                "created_at": task.created_at.isoformat(),
                "started_at": task.started_at.isoformat() if task.started_at else None,
                "metadata": task.metadata
            })
        
        return {
            "status": "success",
            "count": len(tasks_data),
            "data": tasks_data
        }
        
    except Exception as e:
        logger.error(f"Erro ao obter tasks em execução: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        )


@router.get("/client/{client_id}")
async def get_client_tasks(client_id: str):
    """
    Lista todas as tasks de um cliente específico
    
    Args:
        client_id: ID do cliente
        
    Returns:
        Lista de tasks do cliente com histórico completo
    """
    try:
        client_tasks = await task_manager.get_client_tasks(client_id)
        
        tasks_data = []
        for task in client_tasks:
            tasks_data.append({
                "task_id": task.task_id,
                "task_type": task.task_type,
                "priority": task.priority.name,
                "status": task.status.value,
                "progress": task.progress,
                "elapsed_time": task.elapsed_time,
                "estimated_duration": task.estimated_duration,
                "created_at": task.created_at.isoformat(),
                "started_at": task.started_at.isoformat() if task.started_at else None,
                "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                "error_message": task.error_message,
                "metadata": task.metadata
            })
        
        # Ordenar por data de criação (mais recente primeiro)
        tasks_data.sort(key=lambda x: x['created_at'], reverse=True)
        
        return {
            "status": "success",
            "client_id": client_id,
            "count": len(tasks_data),
            "data": tasks_data
        }
        
    except Exception as e:
        logger.error(f"Erro ao obter tasks do cliente {client_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        )


@router.get("/{task_id}")
async def get_task_details(task_id: str):
    """
    Obtém detalhes completos de uma task específica
    
    Args:
        task_id: ID único da task
        
    Returns:
        Informações detalhadas da task
    """
    try:
        task_info = await task_manager.get_task_info(task_id)
        
        if not task_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Task {task_id} não encontrada"
            )
        
        return {
            "status": "success",
            "data": {
                "task_id": task_info.task_id,
                "task_type": task_info.task_type,
                "priority": task_info.priority.name,
                "client_id": task_info.client_id,
                "client_name": task_info.client_name,
                "project_id": task_info.project_id,
                "status": task_info.status.value,
                "progress": task_info.progress,
                "elapsed_time": task_info.elapsed_time,
                "estimated_duration": task_info.estimated_duration,
                "created_at": task_info.created_at.isoformat(),
                "started_at": task_info.started_at.isoformat() if task_info.started_at else None,
                "completed_at": task_info.completed_at.isoformat() if task_info.completed_at else None,
                "error_message": task_info.error_message,
                "metadata": task_info.metadata,
                "performance": {
                    "is_running": task_info.is_running,
                    "is_completed": task_info.is_completed,
                    "estimated_remaining": (
                        task_info.estimated_duration - task_info.elapsed_time
                        if task_info.estimated_duration and task_info.elapsed_time
                        and task_info.elapsed_time < task_info.estimated_duration
                        else None
                    )
                }
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao obter detalhes da task {task_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        )


@router.post("/{task_id}/cancel")
async def cancel_task(task_id: str):
    """
    Cancela uma task pendente
    
    Args:
        task_id: ID único da task
        
    Returns:
        Confirmação do cancelamento
    """
    try:
        cancelled = await task_manager.cancel_task(task_id)
        
        if not cancelled:
            # Verificar se task existe
            task_info = await task_manager.get_task_info(task_id)
            if not task_info:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Task {task_id} não encontrada"
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Task {task_id} não pode ser cancelada (status: {task_info.status.value})"
                )
        
        return {
            "status": "success",
            "message": f"Task {task_id} foi cancelada com sucesso",
            "task_id": task_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao cancelar task {task_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        )


@router.get("/health/check")
async def health_check():
    """
    Verifica saúde do sistema de tasks
    
    Returns:
        Status de saúde e métricas críticas
    """
    try:
        stats = task_manager.get_stats()
        running_tasks = stats.get('running_tasks', 0)
        max_concurrent = stats.get('max_concurrent', 10)
        
        # Determinar saúde baseado na carga
        health_status = "healthy"
        if running_tasks >= max_concurrent * 0.8:
            health_status = "warning"
        elif running_tasks >= max_concurrent:
            health_status = "critical"
        
        return {
            "status": "success",
            "health": {
                "status": health_status,
                "load_percentage": round((running_tasks / max_concurrent) * 100, 1),
                "available_capacity": max_concurrent - running_tasks,
                "total_capacity": max_concurrent
            },
            "metrics": {
                "tasks_running": running_tasks,
                "tasks_completed": stats.get('completed_tasks', 0),
                "tasks_failed": stats.get('failed_tasks', 0),
                "uptime_tasks": stats.get('total_tasks', 0)
            },
            "timestamp": task_manager._tasks.get(list(task_manager._tasks.keys())[0]).created_at.isoformat() if task_manager._tasks else None
        }
        
    except Exception as e:
        logger.error(f"Erro no health check: {str(e)}")
        return {
            "status": "error",
            "health": {
                "status": "critical",
                "error": str(e)
            },
            "timestamp": None
        }


@router.get("/summary/dashboard")
async def get_dashboard_summary():
    """
    Retorna resumo executivo para dashboard de administração
    
    Returns:
        Dados consolidados para exibição em dashboard
    """
    try:
        stats = task_manager.get_stats()
        running_tasks = await task_manager.get_running_tasks()
        
        # Agrupar tasks por tipo
        tasks_by_type = {}
        for task in running_tasks:
            task_type = task.task_type
            if task_type not in tasks_by_type:
                tasks_by_type[task_type] = {
                    'count': 0,
                    'avg_progress': 0,
                    'tasks': []
                }
            
            tasks_by_type[task_type]['count'] += 1
            tasks_by_type[task_type]['tasks'].append({
                'client_name': task.client_name,
                'progress': task.progress,
                'elapsed_time': task.elapsed_time
            })
        
        # Calcular progresso médio por tipo
        for task_type_data in tasks_by_type.values():
            if task_type_data['tasks']:
                task_type_data['avg_progress'] = round(
                    sum(t['progress'] for t in task_type_data['tasks']) / len(task_type_data['tasks']), 1
                )
        
        return {
            "status": "success",
            "dashboard": {
                "summary": {
                    "total_tasks": stats.get('total_tasks', 0),
                    "active_tasks": len(running_tasks),
                    "completed_today": stats.get('completed_tasks', 0),
                    "success_rate": round(
                        (stats.get('completed_tasks', 0) / max(stats.get('total_tasks', 1), 1)) * 100, 1
                    )
                },
                "active_by_type": tasks_by_type,
                "system_load": {
                    "current": stats.get('running_tasks', 0),
                    "maximum": stats.get('max_concurrent', 10),
                    "percentage": round(
                        (stats.get('running_tasks', 0) / stats.get('max_concurrent', 10)) * 100, 1
                    )
                },
                "resource_usage": stats.get('resource_usage', {})
            }
        }
        
    except Exception as e:
        logger.error(f"Erro ao gerar resumo do dashboard: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        ) 