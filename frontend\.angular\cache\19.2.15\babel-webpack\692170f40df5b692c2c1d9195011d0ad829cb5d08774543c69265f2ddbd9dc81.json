{"ast": null, "code": "import { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, Injectable } from '@angular/core';\nimport { setAttributes, setAttribute } from '@primeuix/utils';\nlet _id = 0;\nclass UseStyle {\n  document = inject(DOCUMENT);\n  use(css, options = {}) {\n    let isLoaded = false;\n    let cssRef = css;\n    let styleRef = null;\n    const {\n      immediate = true,\n      manual = false,\n      name = `style_${++_id}`,\n      id = undefined,\n      media = undefined,\n      nonce = undefined,\n      first = false,\n      props = {}\n    } = options;\n    if (!this.document) return;\n    styleRef = this.document.querySelector(`style[data-primeng-style-id=\"${name}\"]`) || id && this.document.getElementById(id) || this.document.createElement('style');\n    if (!styleRef.isConnected) {\n      cssRef = css;\n      setAttributes(styleRef, {\n        type: 'text/css',\n        media,\n        nonce\n      });\n      const HEAD = this.document.head;\n      first && HEAD.firstChild ? HEAD.insertBefore(styleRef, HEAD.firstChild) : HEAD.appendChild(styleRef);\n      setAttribute(styleRef, 'data-primeng-style-id', name);\n    }\n    if (styleRef.textContent !== cssRef) {\n      styleRef.textContent = cssRef;\n    }\n    return {\n      id,\n      name,\n      el: styleRef,\n      css: cssRef\n    };\n  }\n  static ɵfac = function UseStyle_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || UseStyle)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: UseStyle,\n    factory: UseStyle.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(UseStyle, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { UseStyle };", "map": {"version": 3, "names": ["DOCUMENT", "i0", "inject", "Injectable", "setAttributes", "setAttribute", "_id", "UseStyle", "document", "use", "css", "options", "isLoaded", "cssRef", "styleRef", "immediate", "manual", "name", "id", "undefined", "media", "nonce", "first", "props", "querySelector", "getElementById", "createElement", "isConnected", "type", "HEAD", "head", "<PERSON><PERSON><PERSON><PERSON>", "insertBefore", "append<PERSON><PERSON><PERSON>", "textContent", "el", "ɵfac", "UseStyle_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "args"], "sources": ["C:/projetos/scope-ai/frontend/node_modules/primeng/fesm2022/primeng-usestyle.mjs"], "sourcesContent": ["import { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, Injectable } from '@angular/core';\nimport { setAttributes, setAttribute } from '@primeuix/utils';\n\nlet _id = 0;\nclass UseStyle {\n    document = inject(DOCUMENT);\n    use(css, options = {}) {\n        let isLoaded = false;\n        let cssRef = css;\n        let styleRef = null;\n        const { immediate = true, manual = false, name = `style_${++_id}`, id = undefined, media = undefined, nonce = undefined, first = false, props = {} } = options;\n        if (!this.document)\n            return;\n        styleRef = this.document.querySelector(`style[data-primeng-style-id=\"${name}\"]`) || (id && this.document.getElementById(id)) || this.document.createElement('style');\n        if (!styleRef.isConnected) {\n            cssRef = css;\n            setAttributes(styleRef, {\n                type: 'text/css',\n                media,\n                nonce\n            });\n            const HEAD = this.document.head;\n            first && HEAD.firstChild ? HEAD.insertBefore(styleRef, HEAD.firstChild) : HEAD.appendChild(styleRef);\n            setAttribute(styleRef, 'data-primeng-style-id', name);\n        }\n        if (styleRef.textContent !== cssRef) {\n            styleRef.textContent = cssRef;\n        }\n        return {\n            id,\n            name,\n            el: styleRef,\n            css: cssRef\n        };\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: UseStyle, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: UseStyle, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: UseStyle, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { UseStyle };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,UAAU,QAAQ,eAAe;AAClD,SAASC,aAAa,EAAEC,YAAY,QAAQ,iBAAiB;AAE7D,IAAIC,GAAG,GAAG,CAAC;AACX,MAAMC,QAAQ,CAAC;EACXC,QAAQ,GAAGN,MAAM,CAACF,QAAQ,CAAC;EAC3BS,GAAGA,CAACC,GAAG,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACnB,IAAIC,QAAQ,GAAG,KAAK;IACpB,IAAIC,MAAM,GAAGH,GAAG;IAChB,IAAII,QAAQ,GAAG,IAAI;IACnB,MAAM;MAAEC,SAAS,GAAG,IAAI;MAAEC,MAAM,GAAG,KAAK;MAAEC,IAAI,GAAG,SAAS,EAAEX,GAAG,EAAE;MAAEY,EAAE,GAAGC,SAAS;MAAEC,KAAK,GAAGD,SAAS;MAAEE,KAAK,GAAGF,SAAS;MAAEG,KAAK,GAAG,KAAK;MAAEC,KAAK,GAAG,CAAC;IAAE,CAAC,GAAGZ,OAAO;IAC9J,IAAI,CAAC,IAAI,CAACH,QAAQ,EACd;IACJM,QAAQ,GAAG,IAAI,CAACN,QAAQ,CAACgB,aAAa,CAAC,gCAAgCP,IAAI,IAAI,CAAC,IAAKC,EAAE,IAAI,IAAI,CAACV,QAAQ,CAACiB,cAAc,CAACP,EAAE,CAAE,IAAI,IAAI,CAACV,QAAQ,CAACkB,aAAa,CAAC,OAAO,CAAC;IACpK,IAAI,CAACZ,QAAQ,CAACa,WAAW,EAAE;MACvBd,MAAM,GAAGH,GAAG;MACZN,aAAa,CAACU,QAAQ,EAAE;QACpBc,IAAI,EAAE,UAAU;QAChBR,KAAK;QACLC;MACJ,CAAC,CAAC;MACF,MAAMQ,IAAI,GAAG,IAAI,CAACrB,QAAQ,CAACsB,IAAI;MAC/BR,KAAK,IAAIO,IAAI,CAACE,UAAU,GAAGF,IAAI,CAACG,YAAY,CAAClB,QAAQ,EAAEe,IAAI,CAACE,UAAU,CAAC,GAAGF,IAAI,CAACI,WAAW,CAACnB,QAAQ,CAAC;MACpGT,YAAY,CAACS,QAAQ,EAAE,uBAAuB,EAAEG,IAAI,CAAC;IACzD;IACA,IAAIH,QAAQ,CAACoB,WAAW,KAAKrB,MAAM,EAAE;MACjCC,QAAQ,CAACoB,WAAW,GAAGrB,MAAM;IACjC;IACA,OAAO;MACHK,EAAE;MACFD,IAAI;MACJkB,EAAE,EAAErB,QAAQ;MACZJ,GAAG,EAAEG;IACT,CAAC;EACL;EACA,OAAOuB,IAAI,YAAAC,iBAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAyF/B,QAAQ;EAAA;EAC5G,OAAOgC,KAAK,kBAD8EtC,EAAE,CAAAuC,kBAAA;IAAAC,KAAA,EACYlC,QAAQ;IAAAmC,OAAA,EAARnC,QAAQ,CAAA6B,IAAA;IAAAO,UAAA,EAAc;EAAM;AACxI;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH8F3C,EAAE,CAAA4C,iBAAA,CAGJtC,QAAQ,EAAc,CAAC;IACvGqB,IAAI,EAAEzB,UAAU;IAChB2C,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASpC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}