"""
AuthMiddleware - Middleware de Autenticação

Este módulo implementa autenticação básica e controle de acesso
para os endpoints da API.
"""

import logging
import uuid
from typing import Optional, List
from fastapi import Request, HTTPException
from fastapi.security import HTTPBearer
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response
from ..error_handler import UnauthorizedAPIError, ForbiddenAPIError


logger = logging.getLogger(__name__)


class AuthMiddleware(BaseHTTPMiddleware):
    """
    Middleware de Autenticação da API

    Responsável por:
    - Verificar autenticação de requisições
    - Controlar acesso a endpoints protegidos
    - Adicionar informações de usuário ao contexto
    - Gerar IDs únicos para requisições
    """

    def __init__(self, app, exclude_paths: Optional[List[str]] = None):
        """
        Inicializa o middleware de autenticação

        Args:
            app: Aplicação FastAPI
            exclude_paths: Caminhos excluídos da autenticação
        """
        super().__init__(app)
        self.exclude_paths = exclude_paths or [
            "/docs", "/redoc", "/openapi.json", "/ping", "/health",
            "/", "/ws"  # WebSocket também excluído por enquanto
        ]
        self.security = HTTPBearer(auto_error=False)

    async def dispatch(self, request: Request, call_next) -> Response:
        """
        Processa requisição através do middleware

        Args:
            request: Requisição FastAPI
            call_next: Próximo middleware/handler

        Returns:
            Response processada
        """
        try:
            # Gerar ID único para a requisição
            request_id = str(uuid.uuid4())
            request.state.request_id = request_id

            # Log da requisição
            logger.info(
                f"Request: {request.method} {request.url.path}",
                extra={
                    "request_id": request_id,
                    "method": request.method,
                    "path": request.url.path,
                    "query_params": str(request.query_params),
                    "client_ip": self._get_client_ip(request)
                }
            )

            # Verificar se o path deve ser autenticado
            if self._is_excluded_path(request.url.path):
                # Processar sem autenticação
                response = await call_next(request)
                self._add_response_headers(response, request_id)
                return response

            # Verificar autenticação
            user_info = await self._authenticate_request(request)
            if user_info:
                request.state.user = user_info
                request.state.authenticated = True
            else:
                # Por enquanto, permitir acesso sem autenticação
                # Em implementação futura, isso será mais restritivo
                request.state.user = {"id": "anonymous", "role": "guest"}
                request.state.authenticated = False

            # Processar requisição
            response = await call_next(request)
            self._add_response_headers(response, request_id)

            return response

        except UnauthorizedAPIError as e:
            logger.warning(f"Unauthorized access attempt: {str(e)}")
            raise HTTPException(status_code=401, detail=str(e))
        except ForbiddenAPIError as e:
            logger.warning(f"Forbidden access attempt: {str(e)}")
            raise HTTPException(status_code=403, detail=str(e))
        except Exception as e:
            logger.error(f"Auth middleware error: {str(e)}")
            # Permitir continuar em caso de erro no middleware
            response = await call_next(request)
            return response

    def _is_excluded_path(self, path: str) -> bool:
        """
        Verifica se o path deve ser excluído da autenticação

        Args:
            path: Caminho da requisição

        Returns:
            True se deve ser excluído
        """
        for excluded in self.exclude_paths:
            if path.startswith(excluded):
                return True
        return False

    async def _authenticate_request(self, request: Request) -> Optional[dict]:
        """
        Autentica a requisição

        Args:
            request: Requisição FastAPI

        Returns:
            Informações do usuário se autenticado
        """
        try:
            # Tentar extrair token do cabeçalho Authorization
            auth_header = request.headers.get("Authorization")
            if not auth_header:
                return None

            # Verificar formato Bearer token
            if not auth_header.startswith("Bearer "):
                return None

            token = auth_header.replace("Bearer ", "")

            # Validar token (implementação futura)
            user_info = await self._validate_token(token)
            return user_info

        except Exception as e:
            logger.warning(f"Authentication error: {str(e)}")
            return None

    async def _validate_token(self, token: str) -> Optional[dict]:
        """
        Valida token de autenticação

        Args:
            token: Token a ser validado

        Returns:
            Informações do usuário se válido
        """
        try:
            # Implementação simplificada para desenvolvimento
            # Em produção, isso validaria JWT ou consultaria banco de dados

            if token == "dev-token-admin":
                return {
                    "id": "admin",
                    "email": "<EMAIL>",
                    "role": "admin",
                    "permissions": ["*"]
                }
            elif token == "dev-token-user":
                return {
                    "id": "user1",
                    "email": "<EMAIL>",
                    "role": "user",
                    "permissions": ["read", "write"]
                }
            else:
                # Token inválido
                return None

        except Exception as e:
            logger.error(f"Token validation error: {str(e)}")
            return None

    def _get_client_ip(self, request: Request) -> str:
        """
        Extrai IP do cliente da requisição

        Args:
            request: Requisição FastAPI

        Returns:
            IP do cliente
        """
        # Verificar headers de proxy primeiro
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()

        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip

        # Fallback para IP direto
        client_host = request.client.host if request.client else "unknown"
        return client_host

    def _add_response_headers(self, response: Response, request_id: str):
        """
        Adiciona headers de resposta

        Args:
            response: Resposta HTTP
            request_id: ID da requisição
        """
        response.headers["X-Request-ID"] = request_id
        response.headers["X-API-Version"] = "1.0"

    def require_authentication(self, request: Request) -> dict:
        """
        Força autenticação para uma requisição

        Args:
            request: Requisição FastAPI

        Returns:
            Informações do usuário

        Raises:
            UnauthorizedAPIError: Se não autenticado
        """
        if not getattr(request.state, 'authenticated', False):
            raise UnauthorizedAPIError("Autenticação obrigatória")

        return getattr(request.state, 'user', {})

    def require_permission(self, request: Request, permission: str):
        """
        Verifica permissão específica

        Args:
            request: Requisição FastAPI
            permission: Permissão requerida

        Raises:
            ForbiddenAPIError: Se sem permissão
        """
        user = self.require_authentication(request)
        user_permissions = user.get("permissions", [])

        if "*" not in user_permissions and permission not in user_permissions:
            raise ForbiddenAPIError(f"Permissão '{permission}' obrigatória")

    def require_role(self, request: Request, role: str):
        """
        Verifica role específica

        Args:
            request: Requisição FastAPI
            role: Role requerida

        Raises:
            ForbiddenAPIError: Se sem role
        """
        user = self.require_authentication(request)
        user_role = user.get("role", "guest")

        if user_role != role and user_role != "admin":
            raise ForbiddenAPIError(f"Role '{role}' obrigatória")
