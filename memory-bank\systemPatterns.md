# System Patterns - ScopeAI

**Versão**: 1.0.0
**Última Atualização**: 2025-01-29

## 🏗️ Arquitetura Atual

### Visão Geral
```
┌─────────────────┐     ┌──────────────────┐     ┌─────────────────┐
│  Frontend       │────▶│  Backend API     │────▶│  Databases      │
│  Angular 19     │     │  FastAPI         │     │  MongoDB        │
│  TypeScript     │◀────│  Python 3.12     │     │  Redis          │
└─────────────────┘     └──────────────────┘     └─────────────────┘
        │                        │                         │
        └────── WebSocket ───────┴─────── GridFS ─────────┘
```

### Stack Tecnológico

#### Frontend
- **Framework**: Angular 19 (standalone components)
- **Linguagem**: TypeScript 5.x strict mode
- **Estilo**: Tailwind CSS + SCSS modules
- **Estado**: RxJS + Services
- **Build**: Vite + esbuild
- **Comunicação**: HTTP/WebSocket

#### Backend
- **Framework**: FastAPI 0.104+
- **Linguagem**: Python 3.12
- **Async**: asyncio + aiohttp
- **ORM**: Motor (MongoDB async)
- **Cache**: Redis com TTL
- **Tasks**: BackgroundTasks + Celery

#### Infraestrutura
- **Containers**: Docker + Docker Compose
- **Database**: MongoDB Atlas (cloud)
- **Storage**: GridFS para PDFs
- **Queue**: Redis pub/sub
- **Monitoring**: Logs estruturados

## 🎯 Padrões de Design

### Domain-Driven Design (DDD)
```python
# Entidades ricas com comportamento
class Client(Entity):
    def __init__(self, id: ClientId, name: str, url: URL):
        self.id = id
        self.name = name
        self.url = url
        self.reports = []
    
    def can_generate_report(self) -> bool:
        return len(self.reports) < 10
```

### Hexagonal Architecture
```
┌─────────────────────────────────────────────┐
│             API Layer (Controllers)          │
├─────────────────────────────────────────────┤
│          Application Layer (Services)        │
├─────────────────────────────────────────────┤
│            Domain Layer (Entities)           │
├─────────────────────────────────────────────┤
│        Infrastructure Layer (Adapters)       │
└─────────────────────────────────────────────┘
```

### Repository Pattern
```python
# Interface no domínio
class ClientRepository(ABC):
    async def save(self, client: Client) -> None: ...
    async def find_by_id(self, id: ClientId) -> Optional[Client]: ...

# Implementação na infraestrutura
class MongoClientRepository(ClientRepository):
    async def save(self, client: Client) -> None:
        await self.collection.insert_one(client.to_dict())
```

### Event-Driven Architecture
```python
# Eventos de domínio
@dataclass
class ClientCreated(DomainEvent):
    client_id: str
    timestamp: datetime

# Publisher
async def create_client(data: dict):
    client = Client.create(data)
    await repository.save(client)
    await event_bus.publish(ClientCreated(client.id))
```

## 🔄 Fluxos Principais

### 1. Análise de Cliente
```mermaid
sequenceDiagram
    User->>Frontend: Insere URL
    Frontend->>API: POST /clients
    API->>MongoDB: Salva cliente
    API->>Queue: Envia tarefa
    Queue->>Perplexity: Análise IA
    Perplexity->>API: Retorna dossiê
    API->>MongoDB: Salva relatório
    API->>WebSocket: Notifica frontend
    WebSocket->>Frontend: Atualiza UI
```

### 2. Geração de Projetos
```mermaid
graph LR
    A[Cliente Analisado] --> B{Reports Completos?}
    B -->|Sim| C[Gerar Projetos]
    B -->|Não| D[Aguardar]
    C --> E[Team Agno]
    E --> F[10 Agentes]
    F --> G[Projetos Sugeridos]
    G --> H[Notificar User]
```

## 🛡️ Padrões de Segurança

### Authentication & Authorization
- JWT tokens com refresh
- Role-based access control (RBAC)
- API key para serviços externos

### Data Protection
- Encriptação em trânsito (HTTPS)
- Encriptação em repouso (MongoDB)
- Sanitização de inputs
- Rate limiting por IP

### Error Handling
```python
# Padrão consistente de erros
class DomainError(Exception):
    def __init__(self, message: str, code: str):
        self.message = message
        self.code = code

# Tratamento centralizado
@app.exception_handler(DomainError)
async def domain_error_handler(request, exc):
    return JSONResponse(
        status_code=400,
        content={"error": exc.code, "message": exc.message}
    )
```

## 📊 Padrões de Performance

### Caching Strategy
```python
# Cache multi-nível
L1_CACHE = {}  # Memória local (5min)
L2_CACHE = Redis()  # Cache distribuído (1h)
L3_CACHE = MongoDB()  # Persistência (∞)

async def get_report(id: str):
    # L1: Memória
    if id in L1_CACHE:
        return L1_CACHE[id]
    
    # L2: Redis
    cached = await L2_CACHE.get(id)
    if cached:
        L1_CACHE[id] = cached
        return cached
    
    # L3: Database
    report = await db.find_report(id)
    await L2_CACHE.set(id, report, ttl=3600)
    L1_CACHE[id] = report
    return report
```

### Async Patterns
```python
# Processamento paralelo
async def analyze_client(client_id: str):
    tasks = [
        analyze_website(client_id),
        research_market(client_id),
        generate_insights(client_id),
        create_screenshots(client_id)
    ]
    results = await asyncio.gather(*tasks)
    return consolidate_results(results)
```

## 🔮 Arquitetura Futura (Microserviços)

### Decomposição Planejada
```
┌──────────────┐  ┌──────────────┐  ┌──────────────┐
│   Gateway    │  │   Client     │  │   Project    │
│   Service    │  │   Service    │  │   Service    │
└──────┬───────┘  └──────┬───────┘  └──────┬───────┘
       │                 │                  │
       └─────────┬───────┴──────────────────┘
                 │
         ┌───────▼────────┐
         │  Message Bus   │
         │ (RabbitMQ)     │
         └────────────────┘
```

### Tecnologias Planejadas
- **Service Mesh**: Istio para comunicação
- **API Gateway**: Kong ou custom
- **Message Queue**: RabbitMQ/Kafka
- **Service Discovery**: Consul
- **Monitoring**: Prometheus + Grafana
- **Tracing**: Jaeger
- **Container Orchestration**: Kubernetes

## 📐 Princípios Arquiteturais

1. **SOLID**: Aplicado em todos os módulos
2. **DRY**: Zero duplicação de código
3. **KISS**: Simplicidade sobre complexidade
4. **YAGNI**: Implementar apenas o necessário
5. **Clean Code**: Código autodocumentado
6. **12-Factor**: Apps stateless e configuráveis
7. **Domain-First**: Lógica de negócio isolada
8. **Test-Driven**: TDD quando possível 