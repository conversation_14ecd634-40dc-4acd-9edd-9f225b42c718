import { animate, state, style, transition, trigger } from '@angular/animations';
import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output, signal } from '@angular/core';

interface CardAction {
	label: string;
	icon?: string;
	action: () => void;
}

@Component({
	selector: 'app-modern-card',
	standalone: true,
	imports: [CommonModule],
	template: `
		<div
			class="modern-card"
			[class.glass-effect]="glassmorphism()"
			[class.gradient-border]="gradientBorder()"
			[@cardHover]="hoverState"
			(mouseenter)="onMouseEnter()"
			(mouseleave)="onMouseLeave()"
			(click)="onClick()"
		>
			<!-- Gradient Background (optional) -->
			<div
				*ngIf="gradientBackground()"
				class="absolute inset-0 bg-gradient-to-br opacity-10 rounded-2xl"
				[ngClass]="gradientClass()"
			></div>

			<!-- Icon Section -->
			<div *ngIf="icon()" class="relative mb-4">
				<div class="icon-container" [class.icon-gradient]="iconGradient()">
					<i [class]="'pi pi-' + icon() + ' text-2xl'"></i>
				</div>
			</div>

			<!-- Title -->
			<h3 *ngIf="title()" class="text-xl font-semibold mb-2 text-gray-800 dark:text-white">
				{{ title() }}
			</h3>

			<!-- Subtitle -->
			<p *ngIf="subtitle()" class="text-sm text-gray-600 dark:text-gray-300 mb-4">
				{{ subtitle() }}
			</p>

			<!-- Main Value (for metric cards) -->
			<div *ngIf="value()" class="text-3xl font-bold mb-2" [ngClass]="valueColorClass()">
				{{ value() }}
				<span *ngIf="valueUnit()" class="text-lg font-normal text-gray-600 dark:text-gray-400">
					{{ valueUnit() }}
				</span>
			</div>

			<!-- Progress Bar -->
			<div
				*ngIf="showProgress()"
				class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-4 overflow-hidden"
			>
				<div
					class="progress-bar h-full rounded-full transition-all duration-500"
					[style.width.%]="progress()"
					[ngClass]="progressColorClass()"
				></div>
			</div>

			<!-- Content Slot -->
			<div class="card-content relative">
				<ng-content></ng-content>
			</div>

			<!-- Actions -->
			<div
				*ngIf="actions().length > 0"
				class="flex gap-2 mt-4 pt-4 border-t border-gray-200 dark:border-gray-700"
			>
				<button
					*ngFor="let action of actions()"
					(click)="onAction(action, $event)"
					class="flex-1 px-3 py-2 rounded-lg text-sm font-medium transition-all
                 hover:scale-105 active:scale-95"
					[ngClass]="actionButtonClass()"
				>
					<i *ngIf="action.icon" [class]="'pi pi-' + action.icon + ' mr-2'"> </i>
					{{ action.label }}
				</button>
			</div>

			<!-- Hover Effect Overlay -->
			<div
				class="absolute inset-0 rounded-2xl pointer-events-none transition-opacity duration-300"
				[class.opacity-0]="!isHovered()"
				[class.opacity-100]="isHovered()"
			>
				<div class="absolute inset-0 bg-gradient-to-t from-blue-500/10 to-transparent rounded-2xl"></div>
			</div>
		</div>
	`,
	styles: [
		`
			:host {
				display: block;
			}

			.modern-card {
				@apply relative p-6 bg-white dark:bg-gray-800 rounded-2xl;
				@apply shadow-lg hover:shadow-2xl transition-all duration-300;
				@apply cursor-pointer overflow-hidden;

				&.glass-effect {
					@apply bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg;
					@apply border border-white/20 dark:border-gray-700/30;
				}

				&.gradient-border {
					@apply p-[1px] bg-gradient-to-br from-purple-500 to-blue-500;

					&::before {
						content: '';
						@apply absolute inset-[1px] bg-white dark:bg-gray-800 rounded-2xl;
						z-index: -1;
					}
				}
			}

			.icon-container {
				@apply w-14 h-14 rounded-xl flex items-center justify-center;
				@apply bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300;
				@apply transition-all duration-300 group-hover:scale-110;

				&.icon-gradient {
					@apply bg-gradient-to-br from-purple-500 to-blue-500 text-white;
				}
			}

			.progress-bar {
				@apply bg-gradient-to-r;

				&.progress-primary {
					@apply from-blue-500 to-indigo-500;
				}

				&.progress-success {
					@apply from-green-500 to-emerald-500;
				}

				&.progress-warning {
					@apply from-yellow-500 to-orange-500;
				}

				&.progress-danger {
					@apply from-red-500 to-pink-500;
				}
			}

			/* Dark mode adjustments */
			@media (prefers-color-scheme: dark) {
				.modern-card {
					box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);

					&:hover {
						box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
					}
				}
			}
		`,
	],
	animations: [
		trigger('cardHover', [
			state('idle', style({ transform: 'translateY(0) scale(1)' })),
			state('hover', style({ transform: 'translateY(-4px) scale(1.02)' })),
			transition('idle <=> hover', animate('200ms ease-out')),
		]),
	],
})
export class ModernCardComponent {
	// Inputs
	@Input() title = signal<string>('');
	@Input() subtitle = signal<string>('');
	@Input() icon = signal<string>('');
	@Input() value = signal<string>('');
	@Input() valueUnit = signal<string>('');
	@Input() progress = signal<number>(0);
	@Input() showProgress = signal<boolean>(false);
	@Input() actions = signal<CardAction[]>([]);

	// Style options
	@Input() glassmorphism = signal<boolean>(false);
	@Input() gradientBorder = signal<boolean>(false);
	@Input() gradientBackground = signal<boolean>(false);
	@Input() gradientClass = signal<string>('from-purple-600 to-blue-600');
	@Input() iconGradient = signal<boolean>(true);
	@Input() progressColorClass = signal<string>('progress-primary');
	@Input() valueColorClass = signal<string>('text-gray-900 dark:text-white');
	@Input() actionButtonClass = signal<string>('bg-blue-500 hover:bg-blue-600 text-white');

	// Events
	@Output() cardClick = new EventEmitter<void>();
	@Output() actionClick = new EventEmitter<CardAction>();

	// State
	isHovered = signal<boolean>(false);
	hoverState = 'idle';

	onMouseEnter() {
		this.isHovered.set(true);
		this.hoverState = 'hover';
	}

	onMouseLeave() {
		this.isHovered.set(false);
		this.hoverState = 'idle';
	}

	onClick() {
		this.cardClick.emit();
	}

	onAction(action: CardAction, event: Event) {
		event.stopPropagation();
		action.action();
		this.actionClick.emit(action);
	}
}

// Example usage:
/*
<app-modern-card
  [title]="signal('Total Revenue')"
  [subtitle]="signal('Last 30 days')"
  [icon]="signal('chart-line')"
  [value]="signal('$45,231')"
  [valueUnit]="signal('USD')"
  [showProgress]="signal(true)"
  [progress]="signal(75)"
  [glassmorphism]="signal(true)"
  [gradientBackground]="signal(true)"
  [actions]="signal([
    { label: 'View Details', icon: 'eye', action: () => viewDetails() },
    { label: 'Export', icon: 'download', action: () => exportData() }
  ])"
  (cardClick)="onCardClick()"
  (actionClick)="onActionClick($event)">
  
  <!-- Additional custom content can go here -->
  <p class="text-sm text-gray-600 mt-2">
    <span class="text-green-500">↑ 12%</span> from last month
  </p>
</app-modern-card>
*/
