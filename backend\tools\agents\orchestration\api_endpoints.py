"""
API Endpoints para Sistema de Orquestração de Agentes

Implementa endpoints REST para:
- Iniciar processo de orquestração
- Monitorar progresso
- Recuperar resultados
- Consultar logs e estatísticas
"""

from fastapi import APIRouter, HTTPException, status, BackgroundTasks
from fastapi.responses import JSONResponse
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging
from pydantic import BaseModel, Field

# Imports relativos condicionais (mesmo padrão do runner.py)
try:
    from .orchestrator import AgentOrchestrator
    from .storage import get_storage, initialize_storage
    from .schemas import (
        OrchestrationConfig,
        OrchestrationResult,
        OrchestrationStatus,
        AgentExecutionResult,
        AgentExecutionStatus
    )
except ImportError:
    # Para desenvolvimento/testes isolados
    pass

logger = logging.getLogger(__name__)

# Router para endpoints de orquestração
router = APIRouter(prefix="/orchestration", tags=["orchestration"])


# === MODELOS DE REQUEST/RESPONSE ===

class StartOrchestrationRequest(BaseModel):
    """Request para iniciar orquestração"""
    client_id: str = Field(..., description="ID único do cliente")
    client_data: Dict[str, Any] = Field(...,
                                        description="Dados do cliente para análise")
    config: Optional[Dict[str, Any]] = Field(
        None, description="Configuração personalizada")

    model_config = {
        "json_schema_extra": {
            "example": {
                "client_id": "client_123",
                "client_data": {
                    "nome": "Empresa ABC",
                    "site": "https://empresa-abc.com",
                    "setor": "tecnologia"
                },
                "config": {
                    "max_retries_per_agent": 3,
                    "timeout_per_agent_seconds": 120,
                    "enable_parallel_execution": True
                }
            }
        }
    }


class OrchestrationResponse(BaseModel):
    """Response com informações da orquestração"""
    orchestration_id: str
    client_id: str
    status: str
    start_time: datetime
    end_time: Optional[datetime] = None
    total_execution_time_ms: Optional[int] = None
    successful_agents: int = 0
    failed_agents: int = 0
    total_agents: int = 0
    current_phase: Optional[int] = None
    agent_results: Dict[str, Dict[str, Any]] = {}
    error_summary: Optional[str] = None

    model_config = {
        "json_schema_extra": {
            "example": {
                "orchestration_id": "orch_456",
                "client_id": "client_123",
                "status": "running",
                "start_time": "2024-01-15T10:30:00Z",
                "successful_agents": 2,
                "failed_agents": 0,
                "total_agents": 6,
                "current_phase": 1
            }
        }
    }


class AgentResultResponse(BaseModel):
    """Response com resultado de um agente específico"""
    agent_name: str
    status: str
    start_time: datetime
    end_time: Optional[datetime] = None
    execution_time_ms: Optional[int] = None
    phase_id: int
    result_data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    retry_count: int = 0


class OrchestrationLogsResponse(BaseModel):
    """Response com logs de execução"""
    orchestration_id: str
    total_logs: int
    logs: List[Dict[str, Any]]


class OrchestrationStatsResponse(BaseModel):
    """Response com estatísticas do sistema"""
    total_orchestrations: int
    active_orchestrations: int
    completed_orchestrations: int
    failed_orchestrations: int
    average_execution_time_ms: Optional[float] = None
    most_active_client: Optional[str] = None


# === ENDPOINTS PRINCIPAIS ===

@router.post("/start", response_model=OrchestrationResponse, status_code=status.HTTP_201_CREATED)
async def start_orchestration(
    request: StartOrchestrationRequest,
    background_tasks: BackgroundTasks
) -> OrchestrationResponse:
    """
    Inicia novo processo de orquestração de agentes

    Inicia a execução dos 6 agentes especializados do ScopeAI:
    1. Technical Architect
    2. UX/UI Designer  
    3. Product Owner
    4. SEO Specialist
    5. Benchmark Agent
    6. Technical Writer
    """
    try:
        # Obter storage
        storage = get_storage()
        if not storage:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Storage não inicializado"
            )

        # Criar configuração
        if request.config:
            config = OrchestrationConfig(**request.config)
        else:
            config = OrchestrationConfig.get_default_config()

        # Criar orquestrador
        orchestrator = AgentOrchestrator(config, storage)

        # Iniciar orquestração em background
        orchestration_result = await orchestrator.start_orchestration(
            request.client_id,
            request.client_data
        )

        # Log da operação
        await storage.log_execution(
            orchestration_result.orchestration_id,
            "INFO",
            f"Orquestração iniciada para cliente {request.client_id}",
            metadata={"client_data_keys": list(request.client_data.keys())}
        )

        logger.info(
            f"Orquestração {orchestration_result.orchestration_id} iniciada para cliente {request.client_id}")

        return OrchestrationResponse(
            orchestration_id=orchestration_result.orchestration_id,
            client_id=orchestration_result.client_id,
            status=orchestration_result.status.value,
            start_time=orchestration_result.start_time,
            end_time=orchestration_result.end_time,
            total_execution_time_ms=orchestration_result.total_execution_time_ms,
            successful_agents=orchestration_result.successful_agents,
            failed_agents=orchestration_result.failed_agents,
            total_agents=len(orchestration_result.agent_results),
            current_phase=None,  # TODO: Implementar current_phase no schema se necessário
            agent_results={
                name: result.result_data or {}
                for name, result in orchestration_result.agent_results.items()
                if result.result_data
            },
            error_summary=orchestration_result.error_summary
        )

    except Exception as e:
        logger.error(f"Erro ao iniciar orquestração: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        )


@router.get("/status/{orchestration_id}", response_model=OrchestrationResponse)
async def get_orchestration_status(orchestration_id: str) -> OrchestrationResponse:
    """
    Recupera status atual de uma orquestração

    Retorna informações detalhadas sobre o progresso da execução,
    incluindo status dos agentes individuais e resultados parciais.
    """
    try:
        storage = get_storage()
        if not storage:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Storage não inicializado"
            )

        # Buscar orquestração
        result = await storage.get_orchestration(orchestration_id)
        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Orquestração {orchestration_id} não encontrada"
            )

        return OrchestrationResponse(
            orchestration_id=result.orchestration_id,
            client_id=result.client_id,
            status=result.status.value,
            start_time=result.start_time,
            end_time=result.end_time,
            total_execution_time_ms=result.total_execution_time_ms,
            successful_agents=result.successful_agents,
            failed_agents=result.failed_agents,
            total_agents=len(result.agent_results),
            current_phase=None,  # TODO: Implementar current_phase no schema se necessário
            agent_results={
                name: result_obj.result_data or {}
                for name, result_obj in result.agent_results.items()
                if result_obj.result_data
            },
            error_summary=result.error_summary
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Erro ao buscar status da orquestração {orchestration_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        )


@router.get("/results/{orchestration_id}", response_model=Dict[str, AgentResultResponse])
async def get_orchestration_results(orchestration_id: str) -> Dict[str, AgentResultResponse]:
    """
    Recupera resultados detalhados de todos os agentes

    Retorna os resultados completos de cada agente que foi executado,
    incluindo dados específicos e metadados de execução.
    """
    try:
        storage = get_storage()
        if not storage:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Storage não inicializado"
            )

        # Buscar resultados dos agentes
        agent_results = await storage.get_agent_results(orchestration_id)

        if not agent_results:
            # Verificar se a orquestração existe
            orchestration = await storage.get_orchestration(orchestration_id)
            if not orchestration:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Orquestração {orchestration_id} não encontrada"
                )

            # Orquestração existe mas sem resultados ainda
            return {}

        # Converter para response format
        results = {}
        for result in agent_results:
            results[result.agent_name] = AgentResultResponse(
                agent_name=result.agent_name,
                status=result.status.value,
                start_time=result.start_time,
                end_time=result.end_time,
                execution_time_ms=result.execution_time_ms,
                phase_id=result.phase_id,
                result_data=result.result_data,
                error_message=result.error_message,
                retry_count=result.retry_count
            )

        return results

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Erro ao buscar resultados da orquestração {orchestration_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        )


@router.get("/agent/{orchestration_id}/{agent_name}", response_model=AgentResultResponse)
async def get_agent_result(orchestration_id: str, agent_name: str) -> AgentResultResponse:
    """
    Recupera resultado específico de um agente

    Retorna os dados detalhados da execução de um agente específico,
    incluindo resultado da análise e metadados.
    """
    try:
        storage = get_storage()
        if not storage:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Storage não inicializado"
            )

        # Buscar resultado específico
        agent_results = await storage.get_agent_results(orchestration_id)
        target_result = None

        for result in agent_results:
            if result.agent_name == agent_name:
                target_result = result
                break

        if not target_result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Resultado do agente {agent_name} não encontrado para orquestração {orchestration_id}"
            )

        return AgentResultResponse(
            agent_name=target_result.agent_name,
            status=target_result.status.value,
            start_time=target_result.start_time,
            end_time=target_result.end_time,
            execution_time_ms=target_result.execution_time_ms,
            phase_id=target_result.phase_id,
            result_data=target_result.result_data,
            error_message=target_result.error_message,
            retry_count=target_result.retry_count
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao buscar resultado do agente {agent_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        )


@router.get("/logs/{orchestration_id}", response_model=OrchestrationLogsResponse)
async def get_orchestration_logs(
    orchestration_id: str,
    level: Optional[str] = None,
    agent_name: Optional[str] = None,
    limit: int = 100
) -> OrchestrationLogsResponse:
    """
    Recupera logs de execução de uma orquestração

    Permite filtrar logs por nível (INFO, ERROR, DEBUG) e por agente específico.
    Útil para debugging e monitoramento detalhado.
    """
    try:
        storage = get_storage()
        if not storage:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Storage não inicializado"
            )

        # Buscar logs
        logs = await storage.get_execution_logs(
            orchestration_id=orchestration_id,
            level=level,
            agent_name=agent_name,
            limit=limit
        )

        return OrchestrationLogsResponse(
            orchestration_id=orchestration_id,
            total_logs=len(logs),
            logs=logs
        )

    except Exception as e:
        logger.error(
            f"Erro ao buscar logs da orquestração {orchestration_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        )


@router.get("/client/{client_id}", response_model=List[OrchestrationResponse])
async def get_client_orchestrations(
    client_id: str,
    status_filter: Optional[str] = None,
    limit: int = 50
) -> List[OrchestrationResponse]:
    """
    Recupera todas as orquestrações de um cliente

    Permite filtrar por status e limitar quantidade de resultados.
    Útil para dashboard do cliente.
    """
    try:
        storage = get_storage()
        if not storage:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Storage não inicializado"
            )

        # Converter status string para enum se fornecido
        status_enum = None
        if status_filter:
            try:
                status_enum = OrchestrationStatus(status_filter)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Status inválido: {status_filter}"
                )

        # Buscar orquestrações
        orchestrations = await storage.get_orchestrations_by_client(
            client_id=client_id,
            status=status_enum,
            limit=limit
        )

        # Converter para response format
        results = []
        for orch in orchestrations:
            results.append(OrchestrationResponse(
                orchestration_id=orch.orchestration_id,
                client_id=orch.client_id,
                status=orch.status.value,
                start_time=orch.start_time,
                end_time=orch.end_time,
                total_execution_time_ms=orch.total_execution_time_ms,
                successful_agents=orch.successful_agents,
                failed_agents=orch.failed_agents,
                total_agents=len(orch.agent_results),
                current_phase=orch.current_phase,
                agent_results={
                    name: result.result_data or {}
                    for name, result in orch.agent_results.items()
                    if result.result_data
                },
                error_summary=orch.error_summary
            ))

        return results

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Erro ao buscar orquestrações do cliente {client_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        )


@router.get("/stats", response_model=OrchestrationStatsResponse)
async def get_orchestration_statistics() -> OrchestrationStatsResponse:
    """
    Recupera estatísticas gerais do sistema de orquestração

    Fornece métricas úteis para monitoramento e análise de performance.
    """
    try:
        storage = get_storage()
        if not storage:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Storage não inicializado"
            )

        # Buscar estatísticas
        stats = await storage.get_orchestration_statistics()

        return OrchestrationStatsResponse(
            total_orchestrations=stats.get("total_orchestrations", 0),
            active_orchestrations=stats.get("active_orchestrations", 0),
            completed_orchestrations=stats.get("completed_orchestrations", 0),
            failed_orchestrations=stats.get("failed_orchestrations", 0),
            average_execution_time_ms=stats.get("average_execution_time_ms"),
            most_active_client=stats.get("most_active_client")
        )

    except Exception as e:
        logger.error(f"Erro ao buscar estatísticas: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        )


@router.delete("/cleanup")
async def cleanup_old_orchestrations(days_to_keep: int = 30) -> JSONResponse:
    """
    Remove orquestrações antigas do sistema

    Útil para manutenção e gestão de espaço de armazenamento.
    Remove dados mais antigos que o número de dias especificado.
    """
    try:
        storage = get_storage()
        if not storage:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Storage não inicializado"
            )

        # Executar limpeza
        cleanup_result = await storage.cleanup_old_data(days_to_keep)

        logger.info(f"Limpeza executada: {cleanup_result}")

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "message": f"Limpeza concluída. Dados mais antigos que {days_to_keep} dias foram removidos.",
                "details": cleanup_result
            }
        )

    except Exception as e:
        logger.error(f"Erro durante limpeza: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        )


# === ENDPOINTS DE HEALTH CHECK ===

@router.get("/health")
async def health_check() -> JSONResponse:
    """
    Verifica saúde do sistema de orquestração

    Valida conectividade com storage e disponibilidade dos componentes.
    """
    try:
        storage = get_storage()
        health_status = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "components": {
                "storage": "unknown",
                "orchestrator": "available"
            }
        }

        # Testar storage se disponível
        if storage:
            try:
                # Tentar operação simples no storage
                await storage.get_orchestration_statistics()
                health_status["components"]["storage"] = "healthy"
            except Exception as e:
                health_status["components"]["storage"] = f"error: {str(e)}"
                health_status["status"] = "degraded"
        else:
            health_status["components"]["storage"] = "not_initialized"
            health_status["status"] = "degraded"

        status_code = status.HTTP_200_OK if health_status[
            "status"] == "healthy" else status.HTTP_503_SERVICE_UNAVAILABLE

        return JSONResponse(
            status_code=status_code,
            content=health_status
        )

    except Exception as e:
        logger.error(f"Erro no health check: {e}")
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "status": "unhealthy",
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            }
        )


# === FUNÇÕES DE INICIALIZAÇÃO ===

def setup_orchestration_routes(app, mongo_url: str, database_name: str = "scope_ai"):
    """
    Configura rotas de orquestração na aplicação FastAPI

    Args:
        app: Instância FastAPI
        mongo_url: URL de conexão MongoDB
        database_name: Nome do banco de dados
    """

    @app.on_event("startup")
    async def startup_orchestration():
        """Inicializar storage na startup da aplicação"""
        try:
            await initialize_storage(mongo_url, database_name)
            logger.info("Sistema de orquestração inicializado com sucesso")
        except Exception as e:
            logger.error(f"Erro ao inicializar sistema de orquestração: {e}")
            raise

    # Adicionar router
    app.include_router(router)

    logger.info("Rotas de orquestração configuradas")


# === DOCUMENTAÇÃO ADICIONAL ===

# Exemplo de uso:
"""
# Em main.py da aplicação FastAPI:

from fastapi import FastAPI
from backend.tools.agents.orchestration.api_endpoints import setup_orchestration_routes

app = FastAPI(title="ScopeAI", version="1.0.0")

# Configurar orquestração
setup_orchestration_routes(
    app=app,
    mongo_url="mongodb://localhost:27017",
    database_name="scope_ai"
)

# Agora os endpoints estarão disponíveis:
# POST /orchestration/start - Iniciar orquestração
# GET /orchestration/status/{id} - Status da orquestração
# GET /orchestration/results/{id} - Resultados completos
# GET /orchestration/agent/{id}/{agent} - Resultado de agente específico
# GET /orchestration/logs/{id} - Logs de execução
# GET /orchestration/client/{client_id} - Orquestrações do cliente
# GET /orchestration/stats - Estatísticas do sistema
# DELETE /orchestration/cleanup - Limpeza de dados antigos
# GET /orchestration/health - Health check
"""
