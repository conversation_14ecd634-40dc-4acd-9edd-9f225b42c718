export interface IProject {
	id: number;
	name: string;
	client: string;
	cliente_sector: string;
	description: string;
	status: string;
	progress: number;
	startDate: string;
	endDate: string;
	budget: string;
	tags: Array<string>;
	team: Array<ITeamMember>;
}

export interface ITeamMember {
	name: string;
	color: string;
}

export interface ISortOption {
	label: string;
	value: string;
}

export interface IStatusOption {
	label: string;
	value: string | null;
}

export interface IPageSizeOption {
	label: string;
	value: number;
}

/**
 * Interface para o evento de mudança de página do PrimeNG Paginator
 * Equivalente ao PaginatorState do PrimeNG
 */
export interface IPageChangeEvent {
	first?: number;
	rows?: number;
	page?: number;
	pageCount?: number;
}
