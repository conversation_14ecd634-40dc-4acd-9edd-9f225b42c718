{"ast": null, "code": "import { DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, PLATFORM_ID, booleanAttribute, Input, Directive, NgModule } from '@angular/core';\nimport { createElement, getFirstFocusableElement, focus, getLastFocusableElement } from '@primeuix/utils';\nimport { BaseComponent } from 'primeng/basecomponent';\n\n/**\n * Focus Trap keeps focus within a certain DOM element while tabbing.\n * @group Components\n */\nclass FocusTrap extends BaseComponent {\n  /**\n   * When set as true, focus wouldn't be managed.\n   * @group Props\n   */\n  pFocusTrapDisabled = false;\n  platformId = inject(PLATFORM_ID);\n  document = inject(DOCUMENT);\n  firstHiddenFocusableElement;\n  lastHiddenFocusableElement;\n  ngOnInit() {\n    super.ngOnInit();\n    if (isPlatformBrowser(this.platformId) && !this.pFocusTrapDisabled) {\n      !this.firstHiddenFocusableElement && !this.lastHiddenFocusableElement && this.createHiddenFocusableElements();\n    }\n  }\n  ngOnChanges(changes) {\n    super.ngOnChanges(changes);\n    if (changes.pFocusTrapDisabled && isPlatformBrowser(this.platformId)) {\n      if (changes.pFocusTrapDisabled.currentValue) {\n        this.removeHiddenFocusableElements();\n      } else {\n        this.createHiddenFocusableElements();\n      }\n    }\n  }\n  removeHiddenFocusableElements() {\n    if (this.firstHiddenFocusableElement && this.firstHiddenFocusableElement.parentNode) {\n      this.firstHiddenFocusableElement.parentNode.removeChild(this.firstHiddenFocusableElement);\n    }\n    if (this.lastHiddenFocusableElement && this.lastHiddenFocusableElement.parentNode) {\n      this.lastHiddenFocusableElement.parentNode.removeChild(this.lastHiddenFocusableElement);\n    }\n  }\n  getComputedSelector(selector) {\n    return `:not(.p-hidden-focusable):not([data-p-hidden-focusable=\"true\"])${selector ?? ''}`;\n  }\n  createHiddenFocusableElements() {\n    const tabindex = '0';\n    const createFocusableElement = onFocus => {\n      return createElement('span', {\n        class: 'p-hidden-accessible p-hidden-focusable',\n        tabindex,\n        role: 'presentation',\n        'aria-hidden': true,\n        'data-p-hidden-accessible': true,\n        'data-p-hidden-focusable': true,\n        onFocus: onFocus?.bind(this)\n      });\n    };\n    this.firstHiddenFocusableElement = createFocusableElement(this.onFirstHiddenElementFocus);\n    this.lastHiddenFocusableElement = createFocusableElement(this.onLastHiddenElementFocus);\n    this.firstHiddenFocusableElement.setAttribute('data-pc-section', 'firstfocusableelement');\n    this.lastHiddenFocusableElement.setAttribute('data-pc-section', 'lastfocusableelement');\n    this.el.nativeElement.prepend(this.firstHiddenFocusableElement);\n    this.el.nativeElement.append(this.lastHiddenFocusableElement);\n  }\n  onFirstHiddenElementFocus(event) {\n    const {\n      currentTarget,\n      relatedTarget\n    } = event;\n    const focusableElement = relatedTarget === this.lastHiddenFocusableElement || !this.el.nativeElement?.contains(relatedTarget) ? getFirstFocusableElement(currentTarget.parentElement, ':not(.p-hidden-focusable)') : this.lastHiddenFocusableElement;\n    focus(focusableElement);\n  }\n  onLastHiddenElementFocus(event) {\n    const {\n      currentTarget,\n      relatedTarget\n    } = event;\n    const focusableElement = relatedTarget === this.firstHiddenFocusableElement || !this.el.nativeElement?.contains(relatedTarget) ? getLastFocusableElement(currentTarget.parentElement, ':not(.p-hidden-focusable)') : this.firstHiddenFocusableElement;\n    focus(focusableElement);\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵFocusTrap_BaseFactory;\n    return function FocusTrap_Factory(__ngFactoryType__) {\n      return (ɵFocusTrap_BaseFactory || (ɵFocusTrap_BaseFactory = i0.ɵɵgetInheritedFactory(FocusTrap)))(__ngFactoryType__ || FocusTrap);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: FocusTrap,\n    selectors: [[\"\", \"pFocusTrap\", \"\"]],\n    inputs: {\n      pFocusTrapDisabled: [2, \"pFocusTrapDisabled\", \"pFocusTrapDisabled\", booleanAttribute]\n    },\n    features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusTrap, [{\n    type: Directive,\n    args: [{\n      selector: '[pFocusTrap]',\n      standalone: true\n    }]\n  }], null, {\n    pFocusTrapDisabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nclass FocusTrapModule {\n  static ɵfac = function FocusTrapModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FocusTrapModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: FocusTrapModule,\n    imports: [FocusTrap],\n    exports: [FocusTrap]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusTrapModule, [{\n    type: NgModule,\n    args: [{\n      imports: [FocusTrap],\n      exports: [FocusTrap]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { FocusTrap, FocusTrapModule };", "map": {"version": 3, "names": ["DOCUMENT", "isPlatformBrowser", "i0", "inject", "PLATFORM_ID", "booleanAttribute", "Input", "Directive", "NgModule", "createElement", "getFirstFocusableElement", "focus", "getLastFocusableElement", "BaseComponent", "FocusTrap", "pFocusTrapDisabled", "platformId", "document", "firstHiddenFocusableElement", "lastHiddenFocusableElement", "ngOnInit", "createHiddenFocusableElements", "ngOnChanges", "changes", "currentValue", "removeHiddenFocusableElements", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "getComputedSelector", "selector", "tabindex", "createFocusableElement", "onFocus", "class", "role", "bind", "onFirstHiddenElementFocus", "onLastHiddenElementFocus", "setAttribute", "el", "nativeElement", "prepend", "append", "event", "currentTarget", "relatedTarget", "focusableElement", "contains", "parentElement", "ɵfac", "ɵFocusTrap_BaseFactory", "FocusTrap_Factory", "__ngFactoryType__", "ɵɵgetInheritedFactory", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "inputs", "features", "ɵɵInheritDefinitionFeature", "ɵɵNgOnChangesFeature", "ngDevMode", "ɵsetClassMetadata", "args", "standalone", "transform", "FocusTrapModule", "FocusTrapModule_Factory", "ɵmod", "ɵɵdefineNgModule", "imports", "exports", "ɵinj", "ɵɵdefineInjector"], "sources": ["C:/projetos/scope-ai/frontend/node_modules/primeng/fesm2022/primeng-focustrap.mjs"], "sourcesContent": ["import { DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, PLATFORM_ID, booleanAttribute, Input, Directive, NgModule } from '@angular/core';\nimport { createElement, getFirstFocusableElement, focus, getLastFocusableElement } from '@primeuix/utils';\nimport { BaseComponent } from 'primeng/basecomponent';\n\n/**\n * Focus Trap keeps focus within a certain DOM element while tabbing.\n * @group Components\n */\nclass FocusTrap extends BaseComponent {\n    /**\n     * When set as true, focus wouldn't be managed.\n     * @group Props\n     */\n    pFocusTrapDisabled = false;\n    platformId = inject(PLATFORM_ID);\n    document = inject(DOCUMENT);\n    firstHiddenFocusableElement;\n    lastHiddenFocusableElement;\n    ngOnInit() {\n        super.ngOnInit();\n        if (isPlatformBrowser(this.platformId) && !this.pFocusTrapDisabled) {\n            !this.firstHiddenFocusableElement && !this.lastHiddenFocusableElement && this.createHiddenFocusableElements();\n        }\n    }\n    ngOnChanges(changes) {\n        super.ngOnChanges(changes);\n        if (changes.pFocusTrapDisabled && isPlatformBrowser(this.platformId)) {\n            if (changes.pFocusTrapDisabled.currentValue) {\n                this.removeHiddenFocusableElements();\n            }\n            else {\n                this.createHiddenFocusableElements();\n            }\n        }\n    }\n    removeHiddenFocusableElements() {\n        if (this.firstHiddenFocusableElement && this.firstHiddenFocusableElement.parentNode) {\n            this.firstHiddenFocusableElement.parentNode.removeChild(this.firstHiddenFocusableElement);\n        }\n        if (this.lastHiddenFocusableElement && this.lastHiddenFocusableElement.parentNode) {\n            this.lastHiddenFocusableElement.parentNode.removeChild(this.lastHiddenFocusableElement);\n        }\n    }\n    getComputedSelector(selector) {\n        return `:not(.p-hidden-focusable):not([data-p-hidden-focusable=\"true\"])${selector ?? ''}`;\n    }\n    createHiddenFocusableElements() {\n        const tabindex = '0';\n        const createFocusableElement = (onFocus) => {\n            return createElement('span', {\n                class: 'p-hidden-accessible p-hidden-focusable',\n                tabindex,\n                role: 'presentation',\n                'aria-hidden': true,\n                'data-p-hidden-accessible': true,\n                'data-p-hidden-focusable': true,\n                onFocus: onFocus?.bind(this)\n            });\n        };\n        this.firstHiddenFocusableElement = createFocusableElement(this.onFirstHiddenElementFocus);\n        this.lastHiddenFocusableElement = createFocusableElement(this.onLastHiddenElementFocus);\n        this.firstHiddenFocusableElement.setAttribute('data-pc-section', 'firstfocusableelement');\n        this.lastHiddenFocusableElement.setAttribute('data-pc-section', 'lastfocusableelement');\n        this.el.nativeElement.prepend(this.firstHiddenFocusableElement);\n        this.el.nativeElement.append(this.lastHiddenFocusableElement);\n    }\n    onFirstHiddenElementFocus(event) {\n        const { currentTarget, relatedTarget } = event;\n        const focusableElement = relatedTarget === this.lastHiddenFocusableElement || !this.el.nativeElement?.contains(relatedTarget) ? getFirstFocusableElement(currentTarget.parentElement, ':not(.p-hidden-focusable)') : this.lastHiddenFocusableElement;\n        focus(focusableElement);\n    }\n    onLastHiddenElementFocus(event) {\n        const { currentTarget, relatedTarget } = event;\n        const focusableElement = relatedTarget === this.firstHiddenFocusableElement || !this.el.nativeElement?.contains(relatedTarget) ? getLastFocusableElement(currentTarget.parentElement, ':not(.p-hidden-focusable)') : this.firstHiddenFocusableElement;\n        focus(focusableElement);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: FocusTrap, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"19.2.10\", type: FocusTrap, isStandalone: true, selector: \"[pFocusTrap]\", inputs: { pFocusTrapDisabled: [\"pFocusTrapDisabled\", \"pFocusTrapDisabled\", booleanAttribute] }, usesInheritance: true, usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: FocusTrap, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[pFocusTrap]',\n                    standalone: true\n                }]\n        }], propDecorators: { pFocusTrapDisabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\nclass FocusTrapModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: FocusTrapModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.10\", ngImport: i0, type: FocusTrapModule, imports: [FocusTrap], exports: [FocusTrap] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: FocusTrapModule });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: FocusTrapModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [FocusTrap],\n                    exports: [FocusTrap]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { FocusTrap, FocusTrapModule };\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,iBAAiB,QAAQ,iBAAiB;AAC7D,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AACjG,SAASC,aAAa,EAAEC,wBAAwB,EAAEC,KAAK,EAAEC,uBAAuB,QAAQ,iBAAiB;AACzG,SAASC,aAAa,QAAQ,uBAAuB;;AAErD;AACA;AACA;AACA;AACA,MAAMC,SAAS,SAASD,aAAa,CAAC;EAClC;AACJ;AACA;AACA;EACIE,kBAAkB,GAAG,KAAK;EAC1BC,UAAU,GAAGb,MAAM,CAACC,WAAW,CAAC;EAChCa,QAAQ,GAAGd,MAAM,CAACH,QAAQ,CAAC;EAC3BkB,2BAA2B;EAC3BC,0BAA0B;EAC1BC,QAAQA,CAAA,EAAG;IACP,KAAK,CAACA,QAAQ,CAAC,CAAC;IAChB,IAAInB,iBAAiB,CAAC,IAAI,CAACe,UAAU,CAAC,IAAI,CAAC,IAAI,CAACD,kBAAkB,EAAE;MAChE,CAAC,IAAI,CAACG,2BAA2B,IAAI,CAAC,IAAI,CAACC,0BAA0B,IAAI,IAAI,CAACE,6BAA6B,CAAC,CAAC;IACjH;EACJ;EACAC,WAAWA,CAACC,OAAO,EAAE;IACjB,KAAK,CAACD,WAAW,CAACC,OAAO,CAAC;IAC1B,IAAIA,OAAO,CAACR,kBAAkB,IAAId,iBAAiB,CAAC,IAAI,CAACe,UAAU,CAAC,EAAE;MAClE,IAAIO,OAAO,CAACR,kBAAkB,CAACS,YAAY,EAAE;QACzC,IAAI,CAACC,6BAA6B,CAAC,CAAC;MACxC,CAAC,MACI;QACD,IAAI,CAACJ,6BAA6B,CAAC,CAAC;MACxC;IACJ;EACJ;EACAI,6BAA6BA,CAAA,EAAG;IAC5B,IAAI,IAAI,CAACP,2BAA2B,IAAI,IAAI,CAACA,2BAA2B,CAACQ,UAAU,EAAE;MACjF,IAAI,CAACR,2BAA2B,CAACQ,UAAU,CAACC,WAAW,CAAC,IAAI,CAACT,2BAA2B,CAAC;IAC7F;IACA,IAAI,IAAI,CAACC,0BAA0B,IAAI,IAAI,CAACA,0BAA0B,CAACO,UAAU,EAAE;MAC/E,IAAI,CAACP,0BAA0B,CAACO,UAAU,CAACC,WAAW,CAAC,IAAI,CAACR,0BAA0B,CAAC;IAC3F;EACJ;EACAS,mBAAmBA,CAACC,QAAQ,EAAE;IAC1B,OAAO,kEAAkEA,QAAQ,IAAI,EAAE,EAAE;EAC7F;EACAR,6BAA6BA,CAAA,EAAG;IAC5B,MAAMS,QAAQ,GAAG,GAAG;IACpB,MAAMC,sBAAsB,GAAIC,OAAO,IAAK;MACxC,OAAOvB,aAAa,CAAC,MAAM,EAAE;QACzBwB,KAAK,EAAE,wCAAwC;QAC/CH,QAAQ;QACRI,IAAI,EAAE,cAAc;QACpB,aAAa,EAAE,IAAI;QACnB,0BAA0B,EAAE,IAAI;QAChC,yBAAyB,EAAE,IAAI;QAC/BF,OAAO,EAAEA,OAAO,EAAEG,IAAI,CAAC,IAAI;MAC/B,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACjB,2BAA2B,GAAGa,sBAAsB,CAAC,IAAI,CAACK,yBAAyB,CAAC;IACzF,IAAI,CAACjB,0BAA0B,GAAGY,sBAAsB,CAAC,IAAI,CAACM,wBAAwB,CAAC;IACvF,IAAI,CAACnB,2BAA2B,CAACoB,YAAY,CAAC,iBAAiB,EAAE,uBAAuB,CAAC;IACzF,IAAI,CAACnB,0BAA0B,CAACmB,YAAY,CAAC,iBAAiB,EAAE,sBAAsB,CAAC;IACvF,IAAI,CAACC,EAAE,CAACC,aAAa,CAACC,OAAO,CAAC,IAAI,CAACvB,2BAA2B,CAAC;IAC/D,IAAI,CAACqB,EAAE,CAACC,aAAa,CAACE,MAAM,CAAC,IAAI,CAACvB,0BAA0B,CAAC;EACjE;EACAiB,yBAAyBA,CAACO,KAAK,EAAE;IAC7B,MAAM;MAAEC,aAAa;MAAEC;IAAc,CAAC,GAAGF,KAAK;IAC9C,MAAMG,gBAAgB,GAAGD,aAAa,KAAK,IAAI,CAAC1B,0BAA0B,IAAI,CAAC,IAAI,CAACoB,EAAE,CAACC,aAAa,EAAEO,QAAQ,CAACF,aAAa,CAAC,GAAGnC,wBAAwB,CAACkC,aAAa,CAACI,aAAa,EAAE,2BAA2B,CAAC,GAAG,IAAI,CAAC7B,0BAA0B;IACpPR,KAAK,CAACmC,gBAAgB,CAAC;EAC3B;EACAT,wBAAwBA,CAACM,KAAK,EAAE;IAC5B,MAAM;MAAEC,aAAa;MAAEC;IAAc,CAAC,GAAGF,KAAK;IAC9C,MAAMG,gBAAgB,GAAGD,aAAa,KAAK,IAAI,CAAC3B,2BAA2B,IAAI,CAAC,IAAI,CAACqB,EAAE,CAACC,aAAa,EAAEO,QAAQ,CAACF,aAAa,CAAC,GAAGjC,uBAAuB,CAACgC,aAAa,CAACI,aAAa,EAAE,2BAA2B,CAAC,GAAG,IAAI,CAAC9B,2BAA2B;IACrPP,KAAK,CAACmC,gBAAgB,CAAC;EAC3B;EACA,OAAOG,IAAI;IAAA,IAAAC,sBAAA;IAAA,gBAAAC,kBAAAC,iBAAA;MAAA,QAAAF,sBAAA,KAAAA,sBAAA,GAA+EhD,EAAE,CAAAmD,qBAAA,CAAQvC,SAAS,IAAAsC,iBAAA,IAATtC,SAAS;IAAA;EAAA;EAC7G,OAAOwC,IAAI,kBAD+EpD,EAAE,CAAAqD,iBAAA;IAAAC,IAAA,EACJ1C,SAAS;IAAA2C,SAAA;IAAAC,MAAA;MAAA3C,kBAAA,kDAA2HV,gBAAgB;IAAA;IAAAsD,QAAA,GADlJzD,EAAE,CAAA0D,0BAAA,EAAF1D,EAAE,CAAA2D,oBAAA;EAAA;AAEhG;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH8F5D,EAAE,CAAA6D,iBAAA,CAGJjD,SAAS,EAAc,CAAC;IACxG0C,IAAI,EAAEjD,SAAS;IACfyD,IAAI,EAAE,CAAC;MACCnC,QAAQ,EAAE,cAAc;MACxBoC,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,QAAkB;IAAElD,kBAAkB,EAAE,CAAC;MACnCyC,IAAI,EAAElD,KAAK;MACX0D,IAAI,EAAE,CAAC;QAAEE,SAAS,EAAE7D;MAAiB,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;AAChB,MAAM8D,eAAe,CAAC;EAClB,OAAOlB,IAAI,YAAAmB,wBAAAhB,iBAAA;IAAA,YAAAA,iBAAA,IAAyFe,eAAe;EAAA;EACnH,OAAOE,IAAI,kBAf+EnE,EAAE,CAAAoE,gBAAA;IAAAd,IAAA,EAeSW,eAAe;IAAAI,OAAA,GAAYzD,SAAS;IAAA0D,OAAA,GAAa1D,SAAS;EAAA;EAC/J,OAAO2D,IAAI,kBAhB+EvE,EAAE,CAAAwE,gBAAA;AAiBhG;AACA;EAAA,QAAAZ,SAAA,oBAAAA,SAAA,KAlB8F5D,EAAE,CAAA6D,iBAAA,CAkBJI,eAAe,EAAc,CAAC;IAC9GX,IAAI,EAAEhD,QAAQ;IACdwD,IAAI,EAAE,CAAC;MACCO,OAAO,EAAE,CAACzD,SAAS,CAAC;MACpB0D,OAAO,EAAE,CAAC1D,SAAS;IACvB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASA,SAAS,EAAEqD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}