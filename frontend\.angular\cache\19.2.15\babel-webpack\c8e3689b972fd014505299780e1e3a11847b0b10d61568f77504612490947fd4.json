{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component } from '@angular/core';\nimport { BaseIcon } from 'primeng/icons/baseicon';\nclass BlankIcon extends BaseIcon {\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵBlankIcon_BaseFactory;\n    return function BlankIcon_Factory(__ngFactoryType__) {\n      return (ɵBlankIcon_BaseFactory || (ɵBlankIcon_BaseFactory = i0.ɵɵgetInheritedFactory(BlankIcon)))(__ngFactoryType__ || BlankIcon);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: BlankIcon,\n    selectors: [[\"BlankIcon\"]],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    decls: 2,\n    vars: 0,\n    consts: [[\"width\", \"14\", \"height\", \"14\", \"viewBox\", \"0 0 14 14\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"width\", \"1\", \"height\", \"1\", \"fill\", \"currentColor\", \"fill-opacity\", \"0\"]],\n    template: function BlankIcon_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(0, \"svg\", 0);\n        i0.ɵɵelement(1, \"rect\", 1);\n        i0.ɵɵelementEnd();\n      }\n    },\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BlankIcon, [{\n    type: Component,\n    args: [{\n      selector: 'BlankIcon',\n      standalone: true,\n      template: `\n        <svg width=\"14\" height=\"14\" viewBox=\"0 0 14 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <rect width=\"1\" height=\"1\" fill=\"currentColor\" fill-opacity=\"0\" />\n        </svg>\n    `\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BlankIcon };", "map": {"version": 3, "names": ["i0", "Component", "BaseIcon", "BlankIcon", "ɵfac", "ɵBlankIcon_BaseFactory", "BlankIcon_Factory", "__ngFactoryType__", "ɵɵgetInheritedFactory", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "BlankIcon_Template", "rf", "ctx", "ɵɵnamespaceSVG", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "encapsulation", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "standalone"], "sources": ["C:/projetos/scope-ai/frontend/node_modules/primeng/fesm2022/primeng-icons-blank.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component } from '@angular/core';\nimport { BaseIcon } from 'primeng/icons/baseicon';\n\nclass BlankIcon extends BaseIcon {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: BlankIcon, deps: null, target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.10\", type: BlankIcon, isStandalone: true, selector: \"BlankIcon\", usesInheritance: true, ngImport: i0, template: `\n        <svg width=\"14\" height=\"14\" viewBox=\"0 0 14 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <rect width=\"1\" height=\"1\" fill=\"currentColor\" fill-opacity=\"0\" />\n        </svg>\n    `, isInline: true });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: BlankIcon, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'BlankIcon',\n                    standalone: true,\n                    template: `\n        <svg width=\"14\" height=\"14\" viewBox=\"0 0 14 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <rect width=\"1\" height=\"1\" fill=\"currentColor\" fill-opacity=\"0\" />\n        </svg>\n    `\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BlankIcon };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,QAAQ,eAAe;AACzC,SAASC,QAAQ,QAAQ,wBAAwB;AAEjD,MAAMC,SAAS,SAASD,QAAQ,CAAC;EAC7B,OAAOE,IAAI;IAAA,IAAAC,sBAAA;IAAA,gBAAAC,kBAAAC,iBAAA;MAAA,QAAAF,sBAAA,KAAAA,sBAAA,GAA+EL,EAAE,CAAAQ,qBAAA,CAAQL,SAAS,IAAAI,iBAAA,IAATJ,SAAS;IAAA;EAAA;EAC7G,OAAOM,IAAI,kBAD+ET,EAAE,CAAAU,iBAAA;IAAAC,IAAA,EACJR,SAAS;IAAAS,SAAA;IAAAC,QAAA,GADPb,EAAE,CAAAc,0BAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,mBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFpB,EAAE,CAAAsB,cAAA;QAAFtB,EAAE,CAAAuB,cAAA,YAEM,CAAC;QAFTvB,EAAE,CAAAwB,SAAA,aAGnB,CAAC;QAHgBxB,EAAE,CAAAyB,YAAA,CAInF,CAAC;MAAA;IAAA;IAAAC,aAAA;EAAA;AAEd;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAP8F3B,EAAE,CAAA4B,iBAAA,CAOJzB,SAAS,EAAc,CAAC;IACxGQ,IAAI,EAAEV,SAAS;IACf4B,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,WAAW;MACrBC,UAAU,EAAE,IAAI;MAChBb,QAAQ,EAAE;AAC9B;AACA;AACA;AACA;IACgB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASf,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}