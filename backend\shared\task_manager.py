"""
Sistema Centralizado de Gerenciamento de Background Tasks
Fornece tracking avançado, prioridades e controle de recursos para tarefas longas.
"""

import asyncio
import logging
import time
import uuid
from datetime import datetime, UTC
from enum import Enum
from typing import Dict, Any, Optional, Callable, Awaitable, List
from dataclasses import dataclass, field
from concurrent.futures import ThreadPoolExecutor
import threading
from collections import defaultdict

from .websocket_manager import WebSocketManager

logger = logging.getLogger(__name__)


class TaskPriority(Enum):
    """Prioridades para diferentes tipos de task"""
    CRITICAL = 1    # Requests críticos do usuário
    HIGH = 2        # Análises principais (dossiê, relatórios)
    MEDIUM = 3      # Processamento secundário (PDFs, otimizações)
    LOW = 4         # Background maintenance


class TaskStatus(Enum):
    """Status das tasks"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class TaskInfo:
    """Informações de uma task"""
    task_id: str
    task_type: str
    priority: TaskPriority
    client_id: Optional[str] = None
    client_name: Optional[str] = None
    project_id: Optional[str] = None
    status: TaskStatus = TaskStatus.PENDING
    created_at: datetime = field(default_factory=lambda: datetime.now(UTC))
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    progress: float = 0.0
    estimated_duration: Optional[int] = None  # segundos
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def elapsed_time(self) -> Optional[float]:
        """Tempo decorrido em segundos"""
        if self.started_at:
            end_time = self.completed_at or datetime.now(UTC)
            return (end_time - self.started_at).total_seconds()
        return None
    
    @property
    def is_running(self) -> bool:
        """Verifica se a task está rodando"""
        return self.status == TaskStatus.RUNNING
    
    @property
    def is_completed(self) -> bool:
        """Verifica se a task foi concluída"""
        return self.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]


class OptimizedResourcePool:
    """Pool otimizado de recursos para diferentes tipos de task"""
    
    def __init__(self):
        # Pools específicos para diferentes tipos de operação
        self.analysis_pool = ThreadPoolExecutor(
            max_workers=3, 
            thread_name_prefix="analysis"
        )
        self.report_pool = ThreadPoolExecutor(
            max_workers=2, 
            thread_name_prefix="report"
        )
        self.pdf_pool = ThreadPoolExecutor(
            max_workers=2, 
            thread_name_prefix="pdf"
        )
        self.general_pool = ThreadPoolExecutor(
            max_workers=4, 
            thread_name_prefix="general"
        )
        
        # Contadores de uso
        self._pool_usage = defaultdict(int)
        self._lock = threading.Lock()
        
    def get_pool(self, task_type: str) -> ThreadPoolExecutor:
        """Retorna o pool apropriado para o tipo de task"""
        pool_mapping = {
            "complete_report": self.analysis_pool,
            "team_agno_analysis": self.analysis_pool,
            "optimized_dossie": self.analysis_pool,
            "markdown_report": self.report_pool,
            "html_report": self.report_pool,
            "pdf_generation": self.pdf_pool,
            "enhanced_pdf": self.pdf_pool,
        }
        
        pool = pool_mapping.get(task_type, self.general_pool)
        
        with self._lock:
            self._pool_usage[task_type] += 1
            
        return pool
    
    def get_usage_stats(self) -> Dict[str, int]:
        """Retorna estatísticas de uso dos pools"""
        with self._lock:
            return dict(self._pool_usage)
    
    async def shutdown(self):
        """Finaliza todos os pools de forma segura"""
        pools = [self.analysis_pool, self.report_pool, self.pdf_pool, self.general_pool]
        
        for pool in pools:
            pool.shutdown(wait=False)
            
        # Aguardar finalização com timeout
        for pool in pools:
            pool.shutdown(wait=True)
            
        logger.info("✅ Todos os resource pools foram finalizados")


class PriorityTaskQueue:
    """Sistema de filas com prioridades"""
    
    def __init__(self):
        # Filas separadas por prioridade
        self._queues = {
            TaskPriority.CRITICAL: asyncio.Queue(maxsize=10),
            TaskPriority.HIGH: asyncio.Queue(maxsize=20),
            TaskPriority.MEDIUM: asyncio.Queue(maxsize=50),
            TaskPriority.LOW: asyncio.Queue(maxsize=100)
        }
        self._queue_sizes = defaultdict(int)
        self._lock = asyncio.Lock()
        
    async def enqueue(self, task_info: TaskInfo, task_func: Callable, *args, **kwargs):
        """Adiciona task na fila apropriada"""
        queue = self._queues[task_info.priority]
        
        task_item = {
            'task_info': task_info,
            'task_func': task_func,
            'args': args,
            'kwargs': kwargs
        }
        
        try:
            await queue.put(task_item)
            async with self._lock:
                self._queue_sizes[task_info.priority] += 1
            logger.info(f"📋 Task {task_info.task_id} adicionada na fila {task_info.priority.name}")
        except asyncio.QueueFull:
            logger.warning(f"⚠️ Fila {task_info.priority.name} está cheia. Task rejeitada.")
            raise Exception(f"Queue {task_info.priority.name} is full")
    
    async def dequeue_by_priority(self) -> Optional[Dict]:
        """Remove task da fila de maior prioridade disponível"""
        for priority in TaskPriority:
            queue = self._queues[priority]
            if not queue.empty():
                try:
                    task_item = await queue.get()
                    async with self._lock:
                        self._queue_sizes[priority] -= 1
                    return task_item
                except asyncio.QueueEmpty:
                    continue
        return None
    
    def get_queue_stats(self) -> Dict[str, int]:
        """Retorna estatísticas das filas"""
        return {
            priority.name: queue.qsize() 
            for priority, queue in self._queues.items()
        }


class TaskManager:
    """Gerenciador centralizado de background tasks"""
    
    def __init__(self):
        self._tasks: Dict[str, TaskInfo] = {}
        self._lock = asyncio.Lock()
        self._websocket_manager = WebSocketManager()
        self._resource_pool = OptimizedResourcePool()
        self._task_queue = PriorityTaskQueue()
        self._running_tasks = set()
        self._max_concurrent_tasks = 10
        
        # Estatísticas
        self._stats = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'cancelled_tasks': 0
        }
    
    async def create_task(
        self,
        task_type: str,
        task_func: Callable,
        priority: TaskPriority = TaskPriority.MEDIUM,
        client_id: Optional[str] = None,
        client_name: Optional[str] = None,
        project_id: Optional[str] = None,
        estimated_duration: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None,
        *args,
        **kwargs
    ) -> str:
        """
        Cria uma nova background task com tracking completo
        
        Args:
            task_type: Tipo da task (ex: 'complete_report', 'team_agno_analysis')
            task_func: Função a ser executada
            priority: Prioridade da task
            client_id: ID do cliente relacionado
            client_name: Nome do cliente
            project_id: ID do projeto relacionado (se aplicável)
            estimated_duration: Duração estimada em segundos
            metadata: Metadados adicionais
            
        Returns:
            ID único da task criada
        """
        task_id = str(uuid.uuid4())
        
        task_info = TaskInfo(
            task_id=task_id,
            task_type=task_type,
            priority=priority,
            client_id=client_id,
            client_name=client_name,
            project_id=project_id,
            estimated_duration=estimated_duration,
            metadata=metadata or {}
        )
        
        async with self._lock:
            self._tasks[task_id] = task_info
            self._stats['total_tasks'] += 1
        
        # Adicionar na fila de prioridade
        await self._task_queue.enqueue(task_info, task_func, *args, **kwargs)
        
        # Notificar via WebSocket
        await self._notify_task_created(task_info)
        
        # Iniciar processamento se há slots disponíveis
        asyncio.create_task(self._process_queue())
        
        logger.info(f"✅ Task {task_id} ({task_type}) criada com prioridade {priority.name}")
        return task_id
    
    async def _process_queue(self):
        """Processa filas de tasks por prioridade"""
        if len(self._running_tasks) >= self._max_concurrent_tasks:
            return
        
        task_item = await self._task_queue.dequeue_by_priority()
        if not task_item:
            return
        
        task_info = task_item['task_info']
        task_func = task_item['task_func']
        args = task_item['args']
        kwargs = task_item['kwargs']
        
        # Marcar como rodando
        self._running_tasks.add(task_info.task_id)
        await self._update_task_status(task_info.task_id, TaskStatus.RUNNING)
        
        # Executar task
        asyncio.create_task(self._execute_task(task_info, task_func, *args, **kwargs))
    
    async def _execute_task(self, task_info: TaskInfo, task_func: Callable, *args, **kwargs):
        """Executa uma task com tracking completo"""
        task_id = task_info.task_id
        
        try:
            task_info.started_at = datetime.now(UTC)
            await self._notify_task_started(task_info)
            
            # Escolher pool apropriado
            pool = self._resource_pool.get_pool(task_info.task_type)
            
            # Executar task
            if asyncio.iscoroutinefunction(task_func):
                result = await task_func(*args, **kwargs)
            else:
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(pool, task_func, *args, **kwargs)
            
            # Task concluída com sucesso
            await self._complete_task(task_id, TaskStatus.COMPLETED, result)
            
        except Exception as e:
            logger.error(f"❌ Erro na task {task_id}: {str(e)}")
            await self._complete_task(task_id, TaskStatus.FAILED, None, str(e))
        
        finally:
            self._running_tasks.discard(task_id)
            # Continuar processando fila
            asyncio.create_task(self._process_queue())
    
    async def _complete_task(
        self, 
        task_id: str, 
        status: TaskStatus, 
        result: Any = None, 
        error_message: Optional[str] = None
    ):
        """Finaliza uma task"""
        async with self._lock:
            if task_id in self._tasks:
                task_info = self._tasks[task_id]
                task_info.status = status
                task_info.completed_at = datetime.now(UTC)
                task_info.progress = 100.0
                task_info.error_message = error_message
                
                # Atualizar estatísticas
                if status == TaskStatus.COMPLETED:
                    self._stats['completed_tasks'] += 1
                elif status == TaskStatus.FAILED:
                    self._stats['failed_tasks'] += 1
                elif status == TaskStatus.CANCELLED:
                    self._stats['cancelled_tasks'] += 1
        
        await self._notify_task_completed(task_info, result)
    
    async def _update_task_status(self, task_id: str, status: TaskStatus, progress: float = None):
        """Atualiza status de uma task"""
        async with self._lock:
            if task_id in self._tasks:
                self._tasks[task_id].status = status
                if progress is not None:
                    self._tasks[task_id].progress = progress
    
    async def update_task_progress(self, task_id: str, progress: float, message: Optional[str] = None):
        """Atualiza progresso de uma task"""
        async with self._lock:
            if task_id in self._tasks:
                task_info = self._tasks[task_id]
                task_info.progress = min(100.0, max(0.0, progress))
                if message:
                    task_info.metadata['last_message'] = message
                
                # Notificar progresso via WebSocket
                await self._notify_task_progress(task_info, message)
    
    async def get_task_info(self, task_id: str) -> Optional[TaskInfo]:
        """Retorna informações de uma task"""
        async with self._lock:
            return self._tasks.get(task_id)
    
    async def get_client_tasks(self, client_id: str) -> List[TaskInfo]:
        """Retorna todas as tasks de um cliente"""
        async with self._lock:
            return [
                task for task in self._tasks.values() 
                if task.client_id == client_id
            ]
    
    async def get_running_tasks(self) -> List[TaskInfo]:
        """Retorna tasks em execução"""
        async with self._lock:
            return [
                task for task in self._tasks.values() 
                if task.status == TaskStatus.RUNNING
            ]
    
    async def cancel_task(self, task_id: str) -> bool:
        """Cancela uma task"""
        async with self._lock:
            if task_id in self._tasks:
                task_info = self._tasks[task_id]
                if task_info.status == TaskStatus.PENDING:
                    task_info.status = TaskStatus.CANCELLED
                    task_info.completed_at = datetime.now(UTC)
                    self._stats['cancelled_tasks'] += 1
                    await self._notify_task_cancelled(task_info)
                    return True
        return False
    
    def get_stats(self) -> Dict[str, Any]:
        """Retorna estatísticas do gerenciador"""
        running_tasks = len(self._running_tasks)
        queue_stats = self._task_queue.get_queue_stats()
        resource_stats = self._resource_pool.get_usage_stats()
        
        return {
            **self._stats,
            'running_tasks': running_tasks,
            'max_concurrent': self._max_concurrent_tasks,
            'queue_sizes': queue_stats,
            'resource_usage': resource_stats,
            'total_active': running_tasks + sum(queue_stats.values())
        }
    
    # Métodos de notificação WebSocket
    async def _notify_task_created(self, task_info: TaskInfo):
        """Notifica criação de task"""
        await self._websocket_manager.broadcast({
            "type": "task_created",
            "taskId": task_info.task_id,
            "taskType": task_info.task_type,
            "clientId": task_info.client_id,
            "clientName": task_info.client_name,
            "priority": task_info.priority.name,
            "estimatedDuration": task_info.estimated_duration,
            "timestamp": task_info.created_at.isoformat(),
            "message": f"📋 Task {task_info.task_type} criada para {task_info.client_name or 'cliente'}"
        })
    
    async def _notify_task_started(self, task_info: TaskInfo):
        """Notifica início de execução"""
        await self._websocket_manager.broadcast({
            "type": "task_started",
            "taskId": task_info.task_id,
            "taskType": task_info.task_type,
            "clientId": task_info.client_id,
            "clientName": task_info.client_name,
            "timestamp": task_info.started_at.isoformat(),
            "message": f"🚀 Iniciando {task_info.task_type} para {task_info.client_name or 'cliente'}"
        })
    
    async def _notify_task_progress(self, task_info: TaskInfo, message: Optional[str]):
        """Notifica progresso da task"""
        await self._websocket_manager.broadcast({
            "type": "task_progress",
            "taskId": task_info.task_id,
            "taskType": task_info.task_type,
            "clientId": task_info.client_id,
            "progress": task_info.progress,
            "message": message or f"Progresso: {task_info.progress:.1f}%",
            "timestamp": datetime.now(UTC).isoformat()
        })
    
    async def _notify_task_completed(self, task_info: TaskInfo, result: Any):
        """Notifica conclusão da task"""
        success = task_info.status == TaskStatus.COMPLETED
        elapsed = task_info.elapsed_time
        
        await self._websocket_manager.broadcast({
            "type": "task_completed",
            "taskId": task_info.task_id,
            "taskType": task_info.task_type,
            "clientId": task_info.client_id,
            "clientName": task_info.client_name,
            "status": task_info.status.value,
            "success": success,
            "elapsedTime": elapsed,
            "errorMessage": task_info.error_message,
            "timestamp": task_info.completed_at.isoformat(),
            "message": f"✅ {task_info.task_type} concluído" if success else f"❌ {task_info.task_type} falhou"
        })
    
    async def _notify_task_cancelled(self, task_info: TaskInfo):
        """Notifica cancelamento da task"""
        await self._websocket_manager.broadcast({
            "type": "task_cancelled",
            "taskId": task_info.task_id,
            "taskType": task_info.task_type,
            "clientId": task_info.client_id,
            "timestamp": task_info.completed_at.isoformat(),
            "message": f"🚫 {task_info.task_type} cancelado"
        })
    
    async def shutdown(self):
        """Finaliza o gerenciador de forma segura"""
        # Cancelar tasks pendentes
        pending_tasks = [
            task for task in self._tasks.values() 
            if task.status == TaskStatus.PENDING
        ]
        
        for task in pending_tasks:
            await self.cancel_task(task.task_id)
        
        # Aguardar tasks em execução (com timeout)
        timeout = 30  # 30 segundos
        start_time = time.time()
        
        while self._running_tasks and (time.time() - start_time) < timeout:
            await asyncio.sleep(1)
        
        # Finalizar resource pool
        await self._resource_pool.shutdown()
        
        logger.info("✅ TaskManager finalizado com segurança")


# Instância global do gerenciador
task_manager = TaskManager()


async def create_background_task(
    task_type: str,
    task_func: Callable,
    priority: TaskPriority = TaskPriority.MEDIUM,
    client_id: Optional[str] = None,
    client_name: Optional[str] = None,
    project_id: Optional[str] = None,
    estimated_duration: Optional[int] = None,
    *args,
    **kwargs
) -> str:
    """
    Função utilitária para criar background tasks facilmente
    
    Args:
        task_type: Tipo da task
        task_func: Função a ser executada
        priority: Prioridade da task
        client_id: ID do cliente
        client_name: Nome do cliente
        project_id: ID do projeto
        estimated_duration: Duração estimada em segundos
        
    Returns:
        ID da task criada
    """
    return await task_manager.create_task(
        task_type=task_type,
        task_func=task_func,
        priority=priority,
        client_id=client_id,
        client_name=client_name,
        project_id=project_id,
        estimated_duration=estimated_duration,
        *args,
        **kwargs
    )


async def get_task_status(task_id: str) -> Optional[TaskInfo]:
    """Retorna status de uma task"""
    return await task_manager.get_task_info(task_id)


async def update_progress(task_id: str, progress: float, message: Optional[str] = None):
    """Atualiza progresso de uma task"""
    await task_manager.update_task_progress(task_id, progress, message)


def get_system_stats() -> Dict[str, Any]:
    """Retorna estatísticas do sistema de tasks"""
    return task_manager.get_stats() 