# System Patterns - ScopeAI

**Última Atualização**: 2025-01-29

## Arquitetura Atual (Monolítica)

### Visão Geral
```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│   Angular 19    │────▶│   FastAPI       │────▶│   MongoDB       │
│   Frontend      │     │   Backend       │     │   Atlas         │
└─────────────────┘     └─────────────────┘     └─────────────────┘
         │                       │                        │
         └───── WebSocket ───────┘                       │
                                                         │
                                                    GridFS Storage
```

### Componentes Principais

1. **Frontend (Angular 19)**
   - Standalone components
   - Tailwind CSS
   - WebSocket para real-time
   - Lazy loading modules

2. **Backend (FastAPI)**
   - Async/await nativo
   - Background tasks
   - WebSocket support
   - Pydantic validation

3. **Database (MongoDB)**
   - Atlas cloud
   - GridFS para PDFs
   - Índices otimizados
   - Aggregation pipelines

## Arquitetura Futura (Microserviços)

### Visão Geral
```
┌─────────────┐     ┌─────────────┐     ┌─────────────────┐
│   Angular   │────▶│ API Gateway │────▶│ Microservices   │
│  Frontend   │     │  (Kong)     │     │   Cluster       │
└─────────────┘     └─────────────┘     └─────────────────┘
                            │
                    ┌───────┴────────┐
                    │  Load Balancer │
                    └───────┬────────┘
                            │
        ┌───────────────────┼───────────────────┐
        │                   │                   │
┌───────▼─────┐    ┌────────▼──────┐   ┌───────▼─────┐
│   Client    │    │   Project     │   │    Web      │
│   Service   │    │   Service     │   │ Diagnostics │
└─────────────┘    └───────────────┘   └─────────────┘
        │                   │                   │
        └───────────────────┼───────────────────┘
                            │
                    ┌───────▼────────┐
                    │   RabbitMQ     │
                    │ Message Broker │
                    └────────────────┘
```

## Estratégia de Migração de Microserviços (2025)

### Configuração Unificada
**Decisão Arquitetural**: Microserviços compartilham infraestrutura de configuração

```
backend/
├── microservices/         # Código dos microserviços
│   └── client-service/
│       ├── src/          # Código específico
│       ├── tests/        # Testes específicos
│       └── docs/         # Documentação
├── pyproject.toml        # ✅ Dependências compartilhadas
├── .env                  # ✅ Variáveis compartilhadas
├── Dockerfile            # ✅ Container base compartilhado
└── docker-compose.yml    # ✅ Orquestração unificada
```

**Benefícios**:
- Zero drift de configuração
- Manutenção simplificada
- Versionamento único de dependências
- Build unificado

### Strangler Fig Pattern
1. **Fase 1**: Parallel Run
   - Microserviço roda em paralelo ao monolito
   - API Gateway roteia requisições (feature flag)
   - Database compartilhado temporário
   
2. **Fase 2**: Migração Gradual
   - Novos clientes usam microserviço
   - Migração batch dos existentes
   - Sincronização via eventos
   
3. **Fase 3**: Cleanup
   - Remover código do monolito
   - Otimizar performance
   - Database dedicado

### API Versioning Strategy
```
/api/v1/clients  → Monolito (legacy)
/api/v2/clients  → Microserviço (novo)
```

## Padrões de Design

### 1. Domain-Driven Design (DDD)
```python
# Estrutura de módulos
src/
├── domain/              # Core do negócio (sem dependências)
│   ├── entities/       # Entidades com identidade
│   ├── value_objects/  # Objetos imutáveis
│   ├── events/        # Eventos de domínio
│   └── repositories/  # Interfaces apenas
├── application/        # Casos de uso
│   ├── use_cases/     # Lógica de aplicação
│   ├── dtos/          # Data Transfer Objects
│   └── services/      # Serviços de aplicação
├── infrastructure/     # Implementações concretas
│   ├── persistence/   # Repositórios MongoDB
│   ├── external/      # APIs externas
│   └── messaging/     # RabbitMQ, WebSocket
└── api/               # Interface HTTP
    ├── controllers/   # Endpoints REST
    ├── routes/        # Roteamento
    └── middleware/    # Auth, logging, etc
```

### 2. Clean Architecture
- **Dependency Rule**: Dependências apontam para dentro
- **Use Cases**: Lógica de negócio isolada
- **Adapters**: Interfaces com mundo externo
- **Entities**: Regras de negócio core

### 3. Event-Driven Architecture
```python
# Exemplo de evento
class ClientAnalysisCompleted(DomainEvent):
    client_id: str
    dossier_id: str
    insights: List[str]
    timestamp: datetime
```

### 4. CQRS Pattern
- **Commands**: Modificam estado
- **Queries**: Leem estado
- **Segregação**: Modelos otimizados

## Decisões Técnicas

### 1. Async First
- Todo I/O é assíncrono
- Background tasks para operações pesadas
- Concorrência com asyncio
- Rate limiting implementado

### 2. Team Agno Framework
```python
# Arquitetura de agentes
agents = [
    ArchitectAgent(),      # Arquitetura e design
    EngineerAgent(),       # Implementação técnica
    DataAgent(),          # Análise de dados
    ProductAgent(),       # Visão de produto
    QAAgent(),           # Qualidade e testes
    DevOpsAgent(),       # Infraestrutura
    SecurityAgent(),     # Segurança
    AIMLAgent(),        # IA e ML
    TechLeaderAgent(),  # Liderança técnica
    AgileCoachAgent()   # Metodologia ágil
]
```

### 3. Caching Strategy
- Redis para cache distribuído
- TTL baseado em tipo de dado
- Cache invalidation events
- Fallback para database

### 4. Error Handling
```python
# Padrão de retry com backoff
@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=4, max=10)
)
async def external_api_call():
    # implementação
```

## Princípios SOLID

### Single Responsibility
- Cada classe/módulo tem uma única razão para mudar
- Services focados em domínio específico

### Open/Closed
- Extensão via interfaces
- Plugins para novos agentes

### Liskov Substitution
- Interfaces bem definidas
- Contratos respeitados

### Interface Segregation
- Interfaces específicas por cliente
- Não forçar dependências desnecessárias

### Dependency Inversion
- Depender de abstrações
- Injeção de dependência

## Padrões de Integração

### 1. API Gateway Pattern
- Roteamento inteligente
- Rate limiting
- Authentication/Authorization
- Request/Response transformation

### 2. Circuit Breaker
- Proteção contra falhas em cascata
- Fallback mechanisms
- Health checks automáticos

### 3. Saga Pattern
- Transações distribuídas
- Compensating transactions
- Estado eventual consistente

### 4. Strangler Fig Pattern
- Migração gradual
- Feature toggles
- Dual running
- Rollback capability

## Segurança

### 1. Authentication
- JWT tokens
- OAuth2 flow
- Refresh tokens
- Session management

### 2. Authorization
- RBAC (Role-Based Access Control)
- Resource-based permissions
- API key management

### 3. Data Protection
- Encryption at rest
- TLS for transport
- PII anonymization
- GDPR compliance

## Observabilidade

### 1. Logging
- Structured logging (JSON)
- Correlation IDs
- Log aggregation (ELK)

### 2. Monitoring
- Prometheus metrics
- Custom dashboards
- Alert rules

### 3. Tracing
- OpenTelemetry
- Distributed tracing
- Performance insights

## Performance

### 1. Database
- Índices otimizados
- Aggregation pipelines
- Connection pooling
- Query optimization

### 2. API
- Response compression
- Pagination
- Field filtering
- Batch operations

### 3. Frontend
- Lazy loading
- Code splitting
- Asset optimization
- CDN usage 