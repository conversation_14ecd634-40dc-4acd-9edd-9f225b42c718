import { Component } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { FooterComponent } from '../footer/footer.component';
import { HeaderComponent } from '../header/header.component';
import { SidebarComponent } from '../sidebar/sidebar.component';

@Component({
	selector: 'app-main-layout',
	standalone: true,
	imports: [RouterOutlet, HeaderComponent, SidebarComponent, FooterComponent],
	templateUrl: './main-layout.component.html',
	host: {
		display: 'block',
		height: '100%',
	},
})
export class MainLayoutComponent {
	public isSidebarVisible = true;

	public toggleSidebar(): void {
		this.isSidebarVisible = !this.isSidebarVisible;
	}
}
