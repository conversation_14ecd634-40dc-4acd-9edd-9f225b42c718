{"version": 3, "sources": ["../../../../../../node_modules/lodash-es/_nativeKeys.js", "../../../../../../node_modules/lodash-es/_baseKeys.js", "../../../../../../node_modules/lodash-es/_DataView.js", "../../../../../../node_modules/lodash-es/_Promise.js", "../../../../../../node_modules/lodash-es/_Set.js", "../../../../../../node_modules/lodash-es/_WeakMap.js", "../../../../../../node_modules/lodash-es/_getTag.js", "../../../../../../node_modules/lodash-es/isEmpty.js"], "sourcesContent": ["import overArg from './_overArg.js';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeKeys = overArg(Object.keys, Object);\nexport default nativeKeys;", "import isPrototype from './_isPrototype.js';\nimport nativeKeys from './_nativeKeys.js';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * The base implementation of `_.keys` which doesn't treat sparse arrays as dense.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n */\nfunction baseKeys(object) {\n  if (!isPrototype(object)) {\n    return nativeKeys(object);\n  }\n  var result = [];\n  for (var key in Object(object)) {\n    if (hasOwnProperty.call(object, key) && key != 'constructor') {\n      result.push(key);\n    }\n  }\n  return result;\n}\nexport default baseKeys;", "import getNative from './_getNative.js';\nimport root from './_root.js';\n\n/* Built-in method references that are verified to be native. */\nvar DataView = getNative(root, 'DataView');\nexport default DataView;", "import getNative from './_getNative.js';\nimport root from './_root.js';\n\n/* Built-in method references that are verified to be native. */\nvar Promise = getNative(root, 'Promise');\nexport default Promise;", "import getNative from './_getNative.js';\nimport root from './_root.js';\n\n/* Built-in method references that are verified to be native. */\nvar Set = getNative(root, 'Set');\nexport default Set;", "import getNative from './_getNative.js';\nimport root from './_root.js';\n\n/* Built-in method references that are verified to be native. */\nvar WeakMap = getNative(root, 'WeakMap');\nexport default WeakMap;", "import DataView from './_DataView.js';\nimport Map from './_Map.js';\nimport Promise from './_Promise.js';\nimport Set from './_Set.js';\nimport WeakMap from './_WeakMap.js';\nimport baseGetTag from './_baseGetTag.js';\nimport toSource from './_toSource.js';\n\n/** `Object#toString` result references. */\nvar mapTag = '[object Map]',\n  objectTag = '[object Object]',\n  promiseTag = '[object Promise]',\n  setTag = '[object Set]',\n  weakMapTag = '[object WeakMap]';\nvar dataViewTag = '[object DataView]';\n\n/** Used to detect maps, sets, and weakmaps. */\nvar dataViewCtorString = toSource(DataView),\n  mapCtorString = toSource(Map),\n  promiseCtorString = toSource(Promise),\n  setCtorString = toSource(Set),\n  weakMapCtorString = toSource(WeakMap);\n\n/**\n * Gets the `toStringTag` of `value`.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the `toStringTag`.\n */\nvar getTag = baseGetTag;\n\n// Fallback for data views, maps, sets, and weak maps in IE 11 and promises in Node.js < 6.\nif (DataView && getTag(new DataView(new ArrayBuffer(1))) != dataViewTag || Map && getTag(new Map()) != mapTag || Promise && getTag(Promise.resolve()) != promiseTag || Set && getTag(new Set()) != setTag || WeakMap && getTag(new WeakMap()) != weakMapTag) {\n  getTag = function (value) {\n    var result = baseGetTag(value),\n      Ctor = result == objectTag ? value.constructor : undefined,\n      ctorString = Ctor ? toSource(Ctor) : '';\n    if (ctorString) {\n      switch (ctorString) {\n        case dataViewCtorString:\n          return dataViewTag;\n        case mapCtorString:\n          return mapTag;\n        case promiseCtorString:\n          return promiseTag;\n        case setCtorString:\n          return setTag;\n        case weakMapCtorString:\n          return weakMapTag;\n      }\n    }\n    return result;\n  };\n}\nexport default getTag;", "import baseKeys from './_baseKeys.js';\nimport getTag from './_getTag.js';\nimport isArguments from './isArguments.js';\nimport isArray from './isArray.js';\nimport isArrayLike from './isArrayLike.js';\nimport isBuffer from './isBuffer.js';\nimport isPrototype from './_isPrototype.js';\nimport isTypedArray from './isTypedArray.js';\n\n/** `Object#toString` result references. */\nvar mapTag = '[object Map]',\n  setTag = '[object Set]';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Checks if `value` is an empty object, collection, map, or set.\n *\n * Objects are considered empty if they have no own enumerable string keyed\n * properties.\n *\n * Array-like values such as `arguments` objects, arrays, buffers, strings, or\n * jQuery-like collections are considered empty if they have a `length` of `0`.\n * Similarly, maps and sets are considered empty if they have a `size` of `0`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is empty, else `false`.\n * @example\n *\n * _.isEmpty(null);\n * // => true\n *\n * _.isEmpty(true);\n * // => true\n *\n * _.isEmpty(1);\n * // => true\n *\n * _.isEmpty([1, 2, 3]);\n * // => false\n *\n * _.isEmpty({ 'a': 1 });\n * // => false\n */\nfunction isEmpty(value) {\n  if (value == null) {\n    return true;\n  }\n  if (isArrayLike(value) && (isArray(value) || typeof value == 'string' || typeof value.splice == 'function' || isBuffer(value) || isTypedArray(value) || isArguments(value))) {\n    return !value.length;\n  }\n  var tag = getTag(value);\n  if (tag == mapTag || tag == setTag) {\n    return !value.size;\n  }\n  if (isPrototype(value)) {\n    return !baseKeys(value).length;\n  }\n  for (var key in value) {\n    if (hasOwnProperty.call(value, key)) {\n      return false;\n    }\n  }\n  return true;\n}\nexport default isEmpty;"], "mappings": ";;;;;;;;;;;;;;;;AAGA,IAAI,aAAa,gBAAQ,OAAO,MAAM,MAAM;AAC5C,IAAO,qBAAQ;;;ACAf,IAAI,cAAc,OAAO;AAGzB,IAAI,iBAAiB,YAAY;AASjC,SAAS,SAAS,QAAQ;AACxB,MAAI,CAAC,oBAAY,MAAM,GAAG;AACxB,WAAO,mBAAW,MAAM;AAAA,EAC1B;AACA,MAAI,SAAS,CAAC;AACd,WAAS,OAAO,OAAO,MAAM,GAAG;AAC9B,QAAI,eAAe,KAAK,QAAQ,GAAG,KAAK,OAAO,eAAe;AAC5D,aAAO,KAAK,GAAG;AAAA,IACjB;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAO,mBAAQ;;;ACxBf,IAAI,WAAW,kBAAU,cAAM,UAAU;AACzC,IAAO,mBAAQ;;;ACDf,IAAIA,WAAU,kBAAU,cAAM,SAAS;AACvC,IAAO,kBAAQA;;;ACDf,IAAI,MAAM,kBAAU,cAAM,KAAK;AAC/B,IAAO,cAAQ;;;ACDf,IAAI,UAAU,kBAAU,cAAM,SAAS;AACvC,IAAO,kBAAQ;;;ACIf,IAAI,SAAS;AAAb,IACE,YAAY;AADd,IAEE,aAAa;AAFf,IAGE,SAAS;AAHX,IAIE,aAAa;AACf,IAAI,cAAc;AAGlB,IAAI,qBAAqB,iBAAS,gBAAQ;AAA1C,IACE,gBAAgB,iBAAS,WAAG;AAD9B,IAEE,oBAAoB,iBAAS,eAAO;AAFtC,IAGE,gBAAgB,iBAAS,WAAG;AAH9B,IAIE,oBAAoB,iBAAS,eAAO;AAStC,IAAI,SAAS;AAGb,IAAI,oBAAY,OAAO,IAAI,iBAAS,IAAI,YAAY,CAAC,CAAC,CAAC,KAAK,eAAe,eAAO,OAAO,IAAI,YAAI,CAAC,KAAK,UAAU,mBAAW,OAAO,gBAAQ,QAAQ,CAAC,KAAK,cAAc,eAAO,OAAO,IAAI,YAAI,CAAC,KAAK,UAAU,mBAAW,OAAO,IAAI,gBAAQ,CAAC,KAAK,YAAY;AAC3P,WAAS,SAAU,OAAO;AACxB,QAAI,SAAS,mBAAW,KAAK,GAC3B,OAAO,UAAU,YAAY,MAAM,cAAc,QACjD,aAAa,OAAO,iBAAS,IAAI,IAAI;AACvC,QAAI,YAAY;AACd,cAAQ,YAAY;AAAA,QAClB,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,MACX;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AACA,IAAO,iBAAQ;;;AC7Cf,IAAIC,UAAS;AAAb,IACEC,UAAS;AAGX,IAAIC,eAAc,OAAO;AAGzB,IAAIC,kBAAiBD,aAAY;AAmCjC,SAAS,QAAQ,OAAO;AACtB,MAAI,SAAS,MAAM;AACjB,WAAO;AAAA,EACT;AACA,MAAI,oBAAY,KAAK,MAAM,gBAAQ,KAAK,KAAK,OAAO,SAAS,YAAY,OAAO,MAAM,UAAU,cAAc,iBAAS,KAAK,KAAK,qBAAa,KAAK,KAAK,oBAAY,KAAK,IAAI;AAC3K,WAAO,CAAC,MAAM;AAAA,EAChB;AACA,MAAI,MAAM,eAAO,KAAK;AACtB,MAAI,OAAOF,WAAU,OAAOC,SAAQ;AAClC,WAAO,CAAC,MAAM;AAAA,EAChB;AACA,MAAI,oBAAY,KAAK,GAAG;AACtB,WAAO,CAAC,iBAAS,KAAK,EAAE;AAAA,EAC1B;AACA,WAAS,OAAO,OAAO;AACrB,QAAIE,gBAAe,KAAK,OAAO,GAAG,GAAG;AACnC,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAO,kBAAQ;", "names": ["Promise", "mapTag", "setTag", "objectProto", "hasOwnProperty"]}