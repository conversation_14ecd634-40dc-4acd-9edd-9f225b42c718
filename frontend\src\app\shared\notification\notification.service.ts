import { Injectable, inject } from '@angular/core';
import { MessageService } from 'primeng/api';
import { ProcessingNotification, WebSocketService } from '../services/ws.service';

export interface NotificationConfig {
	showStarted: boolean;
	showProgress: boolean;
	showComplete: boolean;
	showError: boolean;
	autoClose: boolean;
	life?: number;
}

@Injectable({ providedIn: 'root' })
export class ProcessingNotificationService {
	private messageService = inject(MessageService);
	private ws = inject(WebSocketService);

	private defaultConfig: NotificationConfig = {
		showStarted: true,
		showProgress: false, // Evita spam de notificações de progresso
		showComplete: true,
		showError: true,
		autoClose: true,
		life: 5000,
	};

	private config = { ...this.defaultConfig };

	constructor() {
		this.setupGlobalNotifications();
	}

	public updateConfig(newConfig: Partial<NotificationConfig>): void {
		this.config = { ...this.config, ...newConfig };
	}

	private setupGlobalNotifications(): void {
		this.ws.processingNotifications$.subscribe({
			next: (notification: ProcessingNotification) => {
				this.handleGlobalNotification(notification);
			},
			error: error => {
				console.error('Erro nas notificações globais de processamento:', error);
			},
		});
	}

	private handleGlobalNotification(notification: ProcessingNotification): void {
		const { type, data } = notification;

		switch (type) {
			case 'processing_started':
				if (this.config.showStarted) {
					this.showNotification({
						severity: 'info',
						summary: 'Análise Iniciada',
						detail: `Iniciando análise para o cliente...`,
						icon: 'pi pi-play',
					});
				}
				break;

			case 'processing_progress':
				if (this.config.showProgress) {
					// Só mostrar notificações de progresso em marcos específicos
					if (data.progressPercentage % 25 === 0 && data.progressPercentage > 0) {
						this.showNotification({
							severity: 'info',
							summary: 'Progresso da Análise',
							detail: `${data.progressPercentage}% concluído - ${data.statusMessage}`,
							icon: 'pi pi-clock',
						});
					}
				}
				break;

			case 'processing_complete':
				if (this.config.showComplete) {
					this.showNotification({
						severity: 'success',
						summary: 'Análise Concluída',
						detail: 'A análise foi finalizada com sucesso! O relatório está disponível.',
						icon: 'pi pi-check-circle',
					});
				}
				break;

			case 'processing_error':
				if (this.config.showError) {
					this.showNotification({
						severity: 'error',
						summary: 'Erro na Análise',
						detail: data.statusMessage || 'Ocorreu um erro durante o processamento.',
						icon: 'pi pi-exclamation-triangle',
					});
				}
				break;
		}
	}

	private showNotification(options: {
		severity: 'success' | 'info' | 'warn' | 'error';
		summary: string;
		detail: string;
		icon?: string;
	}): void {
		this.messageService.add({
			severity: options.severity,
			summary: options.summary,
			detail: options.detail,
			life: this.config.autoClose ? this.config.life : undefined,
			icon: options.icon,
		});
	}

	// Métodos para notificações específicas
	public notifyProcessingStarted(clientName?: string): void {
		this.showNotification({
			severity: 'info',
			summary: 'Análise Iniciada',
			detail: clientName ? `Iniciando análise para ${clientName}...` : 'Análise iniciada...',
			icon: 'pi pi-play',
		});
	}

	public notifyProcessingComplete(clientName?: string): void {
		this.showNotification({
			severity: 'success',
			summary: 'Análise Concluída',
			detail: clientName ? `Análise de ${clientName} finalizada com sucesso!` : 'Análise finalizada com sucesso!',
			icon: 'pi pi-check-circle',
		});
	}

	public notifyProcessingError(error?: string, clientName?: string): void {
		this.showNotification({
			severity: 'error',
			summary: 'Erro na Análise',
			detail:
				error || (clientName ? `Erro na análise de ${clientName}` : 'Ocorreu um erro durante o processamento'),
			icon: 'pi pi-exclamation-triangle',
		});
	}

	public clearAll(): void {
		this.messageService.clear();
	}
}
