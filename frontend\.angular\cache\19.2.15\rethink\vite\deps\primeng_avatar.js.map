{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-avatar.mjs"], "sourcesContent": ["import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, inject, HostBinding, Output, Input, ViewEncapsulation, ChangeDetectionStrategy, Component, NgModule } from '@angular/core';\nimport { SharedModule } from 'primeng/api';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { BaseStyle } from 'primeng/base';\nconst _c0 = [\"*\"];\nfunction Avatar_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 3);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.label);\n  }\n}\nfunction Avatar_ng_template_2_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 5);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r0.icon);\n    i0.ɵɵproperty(\"ngClass\", \"p-avatar-icon\");\n  }\n}\nfunction Avatar_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Avatar_ng_template_2_span_0_Template, 1, 3, \"span\", 4);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const imageTemplate_r2 = i0.ɵɵreference(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.icon)(\"ngIfElse\", imageTemplate_r2);\n  }\n}\nfunction Avatar_ng_template_4_img_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 7);\n    i0.ɵɵlistener(\"error\", function Avatar_ng_template_4_img_0_Template_img_error_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.imageError($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r0.image, i0.ɵɵsanitizeUrl);\n    i0.ɵɵattribute(\"aria-label\", ctx_r0.ariaLabel);\n  }\n}\nfunction Avatar_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Avatar_ng_template_4_img_0_Template, 1, 2, \"img\", 6);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.image);\n  }\n}\nconst theme = ({\n  dt\n}) => `\n.p-avatar {\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n    width: ${dt('avatar.width')};\n    height: ${dt('avatar.height')};\n    font-size: ${dt('avatar.font.size')};\n    color: ${dt('avatar.color')};\n    background: ${dt('avatar.background')};\n    border-radius: ${dt('avatar.border.radius')};\n}\n\n.p-avatar-image {\n    background: transparent;\n}\n\n.p-avatar-circle {\n    border-radius: 50%;\n}\n\n.p-avatar-circle img {\n    border-radius: 50%;\n}\n\n.p-avatar-icon {\n    font-size: ${dt('avatar.icon.size')};\n    width: ${dt('avatar.icon.size')};\n    height: ${dt('avatar.icon.size')};\n}\n\n.p-avatar img {\n    width: 100%;\n    height: 100%;\n}\n\n.p-avatar-lg {\n    width: ${dt('avatar.lg.width')};\n    height: ${dt('avatar.lg.width')};\n    font-size: ${dt('avatar.lg.font.size')};\n}\n\n.p-avatar-lg .p-avatar-icon {\n    font-size: ${dt('avatar.lg.icon.size')};\n    width: ${dt('avatar.lg.icon.size')};\n    height: ${dt('avatar.lg.icon.size')};\n}\n\n.p-avatar-xl {\n    width: ${dt('avatar.xl.width')};\n    height: ${dt('avatar.xl.width')};\n    font-size: ${dt('avatar.xl.font.size')};\n}\n\n.p-avatar-xl .p-avatar-icon {\n    font-size: ${dt('avatar.xl.font.size')};\n    width: ${dt('avatar.xl.icon.size')};\n    height: ${dt('avatar.xl.icon.size')};\n}\n\n.p-avatar-group {\n    display: flex;\n    align-items: center;\n}\n\n.p-avatar-group .p-avatar + .p-avatar {\n    margin-inline-start: ${dt('avatar.group.offset')};\n}\n\n.p-avatar-group .p-avatar {\n    border: 2px solid ${dt('avatar.group.border.color')};\n}\n\n.p-avatar-group .p-avatar-lg + .p-avatar-lg {\n    margin-inline-start: ${dt('avatar.lg.group.offset')};\n}\n\n.p-avatar-group .p-avatar-xl + .p-avatar-xl {\n    margin-inline-start: ${dt('avatar.xl.group.offset')};\n}\n`;\nconst classes = {\n  root: ({\n    props\n  }) => ['p-avatar p-component', {\n    'p-avatar-image': props.image != null,\n    'p-avatar-circle': props.shape === 'circle',\n    'p-avatar-lg': props.size === 'large',\n    'p-avatar-xl': props.size === 'xlarge'\n  }],\n  label: 'p-avatar-label',\n  icon: 'p-avatar-icon'\n};\nclass AvatarStyle extends BaseStyle {\n  name = 'avatar';\n  theme = theme;\n  classes = classes;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵAvatarStyle_BaseFactory;\n    return function AvatarStyle_Factory(__ngFactoryType__) {\n      return (ɵAvatarStyle_BaseFactory || (ɵAvatarStyle_BaseFactory = i0.ɵɵgetInheritedFactory(AvatarStyle)))(__ngFactoryType__ || AvatarStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: AvatarStyle,\n    factory: AvatarStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AvatarStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n *\n * Avatar represents people using icons, labels and images.\n *\n * - [Live Demo](https://primeng.org/avatar)\n *\n * @module avatarstyle\n *\n */\nvar AvatarClasses;\n(function (AvatarClasses) {\n  /**\n   * Class name of the root element\n   */\n  AvatarClasses[\"root\"] = \"p-avatar\";\n  /**\n   * Class name of the label element\n   */\n  AvatarClasses[\"label\"] = \"p-avatar-label\";\n  /**\n   * Class name of the icon element\n   */\n  AvatarClasses[\"icon\"] = \"p-avatar-icon\";\n})(AvatarClasses || (AvatarClasses = {}));\n\n/**\n * Avatar represents people using icons, labels and images.\n * @group Components\n */\nclass Avatar extends BaseComponent {\n  /**\n   * Defines the text to display.\n   * @group Props\n   */\n  label;\n  /**\n   * Defines the icon to display.\n   * @group Props\n   */\n  icon;\n  /**\n   * Defines the image to display.\n   * @group Props\n   */\n  image;\n  /**\n   * Size of the element.\n   * @group Props\n   */\n  size = 'normal';\n  /**\n   * Shape of the element.\n   * @group Props\n   */\n  shape = 'square';\n  /**\n   * Inline style of the element.\n   * @group Props\n   */\n  style;\n  /**\n   * Class of the element.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Establishes a string value that labels the component.\n   * @group Props\n   */\n  ariaLabel;\n  /**\n   * Establishes relationships between the component and label(s) where its value should be one or more element IDs.\n   * @group Props\n   */\n  ariaLabelledBy;\n  /**\n   * This event is triggered if an error occurs while loading an image file.\n   * @param {Event} event - Browser event.\n   * @group Emits\n   */\n  onImageError = new EventEmitter();\n  _componentStyle = inject(AvatarStyle);\n  imageError(event) {\n    this.onImageError.emit(event);\n  }\n  get hostClass() {\n    return this.styleClass;\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵAvatar_BaseFactory;\n    return function Avatar_Factory(__ngFactoryType__) {\n      return (ɵAvatar_BaseFactory || (ɵAvatar_BaseFactory = i0.ɵɵgetInheritedFactory(Avatar)))(__ngFactoryType__ || Avatar);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Avatar,\n    selectors: [[\"p-avatar\"]],\n    hostVars: 19,\n    hostBindings: function Avatar_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"data-pc-name\", \"avatar\")(\"aria-label\", ctx.ariaLabel)(\"aria-labelledby\", ctx.ariaLabelledBy);\n        i0.ɵɵstyleMap(ctx.style);\n        i0.ɵɵclassMap(ctx.hostClass);\n        i0.ɵɵclassProp(\"p-avatar\", true)(\"p-component\", true)(\"p-avatar-circle\", ctx.shape === \"circle\")(\"p-avatar-lg\", ctx.size === \"large\")(\"p-avatar-xl\", ctx.size === \"xlarge\")(\"p-avatar-image\", ctx.image != null);\n      }\n    },\n    inputs: {\n      label: \"label\",\n      icon: \"icon\",\n      image: \"image\",\n      size: \"size\",\n      shape: \"shape\",\n      style: \"style\",\n      styleClass: \"styleClass\",\n      ariaLabel: \"ariaLabel\",\n      ariaLabelledBy: \"ariaLabelledBy\"\n    },\n    outputs: {\n      onImageError: \"onImageError\"\n    },\n    features: [i0.ɵɵProvidersFeature([AvatarStyle]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c0,\n    decls: 6,\n    vars: 2,\n    consts: [[\"iconTemplate\", \"\"], [\"imageTemplate\", \"\"], [\"class\", \"p-avatar-text\", 4, \"ngIf\", \"ngIfElse\"], [1, \"p-avatar-text\"], [3, \"class\", \"ngClass\", 4, \"ngIf\", \"ngIfElse\"], [3, \"ngClass\"], [3, \"src\", \"error\", 4, \"ngIf\"], [3, \"error\", \"src\"]],\n    template: function Avatar_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n        i0.ɵɵtemplate(1, Avatar_span_1_Template, 2, 1, \"span\", 2)(2, Avatar_ng_template_2_Template, 1, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(4, Avatar_ng_template_4_Template, 1, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        const iconTemplate_r4 = i0.ɵɵreference(3);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.label)(\"ngIfElse\", iconTemplate_r4);\n      }\n    },\n    dependencies: [CommonModule, i1.NgClass, i1.NgIf, SharedModule],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Avatar, [{\n    type: Component,\n    args: [{\n      selector: 'p-avatar',\n      standalone: true,\n      imports: [CommonModule, SharedModule],\n      template: `\n        <ng-content></ng-content>\n        <span class=\"p-avatar-text\" *ngIf=\"label; else iconTemplate\">{{ label }}</span>\n        <ng-template #iconTemplate><span [class]=\"icon\" [ngClass]=\"'p-avatar-icon'\" *ngIf=\"icon; else imageTemplate\"></span></ng-template>\n        <ng-template #imageTemplate> <img [src]=\"image\" *ngIf=\"image\" (error)=\"imageError($event)\" [attr.aria-label]=\"ariaLabel\" /></ng-template>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        '[class.p-avatar]': 'true',\n        '[class.p-component]': 'true',\n        '[class.p-avatar-circle]': 'shape === \"circle\"',\n        '[class.p-avatar-lg]': 'size === \"large\"',\n        '[class.p-avatar-xl]': 'size === \"xlarge\"',\n        '[class.p-avatar-image]': 'image != null',\n        '[attr.data-pc-name]': '\"avatar\"',\n        '[attr.aria-label]': 'ariaLabel',\n        '[attr.aria-labelledby]': 'ariaLabelledBy',\n        '[style]': 'style'\n      },\n      providers: [AvatarStyle]\n    }]\n  }], null, {\n    label: [{\n      type: Input\n    }],\n    icon: [{\n      type: Input\n    }],\n    image: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }],\n    shape: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }],\n    ariaLabelledBy: [{\n      type: Input\n    }],\n    onImageError: [{\n      type: Output\n    }],\n    hostClass: [{\n      type: HostBinding,\n      args: ['class']\n    }]\n  });\n})();\nclass AvatarModule {\n  static ɵfac = function AvatarModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AvatarModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: AvatarModule,\n    imports: [Avatar, SharedModule],\n    exports: [Avatar, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [Avatar, SharedModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AvatarModule, [{\n    type: NgModule,\n    args: [{\n      imports: [Avatar, SharedModule],\n      exports: [Avatar, SharedModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Avatar, AvatarClasses, AvatarModule, AvatarStyle };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAM,MAAM,CAAC,GAAG;AAChB,SAAS,uBAAuB,IAAI,KAAK;AACvC,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,KAAK;AAAA,EACnC;AACF;AACA,SAAS,qCAAqC,IAAI,KAAK;AACrD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,OAAO,IAAI;AACzB,IAAG,WAAW,WAAW,eAAe;AAAA,EAC1C;AACF;AACA,SAAS,8BAA8B,IAAI,KAAK;AAC9C,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,sCAAsC,GAAG,GAAG,QAAQ,CAAC;AAAA,EACxE;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,mBAAsB,YAAY,CAAC;AACzC,IAAG,WAAW,QAAQ,OAAO,IAAI,EAAE,YAAY,gBAAgB;AAAA,EACjE;AACF;AACA,SAAS,oCAAoC,IAAI,KAAK;AACpD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,SAAS,SAAS,yDAAyD,QAAQ;AAC/F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,WAAW,MAAM,CAAC;AAAA,IACjD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,OAAO,OAAO,OAAU,aAAa;AACnD,IAAG,YAAY,cAAc,OAAO,SAAS;AAAA,EAC/C;AACF;AACA,SAAS,8BAA8B,IAAI,KAAK;AAC9C,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,qCAAqC,GAAG,GAAG,OAAO,CAAC;AAAA,EACtE;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,QAAQ,OAAO,KAAK;AAAA,EACpC;AACF;AACA,IAAM,QAAQ,CAAC;AAAA,EACb;AACF,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,aAKO,GAAG,cAAc,CAAC;AAAA,cACjB,GAAG,eAAe,CAAC;AAAA,iBAChB,GAAG,kBAAkB,CAAC;AAAA,aAC1B,GAAG,cAAc,CAAC;AAAA,kBACb,GAAG,mBAAmB,CAAC;AAAA,qBACpB,GAAG,sBAAsB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAgB9B,GAAG,kBAAkB,CAAC;AAAA,aAC1B,GAAG,kBAAkB,CAAC;AAAA,cACrB,GAAG,kBAAkB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aASvB,GAAG,iBAAiB,CAAC;AAAA,cACpB,GAAG,iBAAiB,CAAC;AAAA,iBAClB,GAAG,qBAAqB,CAAC;AAAA;AAAA;AAAA;AAAA,iBAIzB,GAAG,qBAAqB,CAAC;AAAA,aAC7B,GAAG,qBAAqB,CAAC;AAAA,cACxB,GAAG,qBAAqB,CAAC;AAAA;AAAA;AAAA;AAAA,aAI1B,GAAG,iBAAiB,CAAC;AAAA,cACpB,GAAG,iBAAiB,CAAC;AAAA,iBAClB,GAAG,qBAAqB,CAAC;AAAA;AAAA;AAAA;AAAA,iBAIzB,GAAG,qBAAqB,CAAC;AAAA,aAC7B,GAAG,qBAAqB,CAAC;AAAA,cACxB,GAAG,qBAAqB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,2BASZ,GAAG,qBAAqB,CAAC;AAAA;AAAA;AAAA;AAAA,wBAI5B,GAAG,2BAA2B,CAAC;AAAA;AAAA;AAAA;AAAA,2BAI5B,GAAG,wBAAwB,CAAC;AAAA;AAAA;AAAA;AAAA,2BAI5B,GAAG,wBAAwB,CAAC;AAAA;AAAA;AAGvD,IAAM,UAAU;AAAA,EACd,MAAM,CAAC;AAAA,IACL;AAAA,EACF,MAAM,CAAC,wBAAwB;AAAA,IAC7B,kBAAkB,MAAM,SAAS;AAAA,IACjC,mBAAmB,MAAM,UAAU;AAAA,IACnC,eAAe,MAAM,SAAS;AAAA,IAC9B,eAAe,MAAM,SAAS;AAAA,EAChC,CAAC;AAAA,EACD,OAAO;AAAA,EACP,MAAM;AACR;AACA,IAAM,cAAN,MAAM,qBAAoB,UAAU;AAAA,EAClC,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,oBAAoB,mBAAmB;AACrD,cAAQ,6BAA6B,2BAA8B,sBAAsB,YAAW,IAAI,qBAAqB,YAAW;AAAA,IAC1I;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,aAAY;AAAA,EACvB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAI;AAAA,CACH,SAAUA,gBAAe;AAIxB,EAAAA,eAAc,MAAM,IAAI;AAIxB,EAAAA,eAAc,OAAO,IAAI;AAIzB,EAAAA,eAAc,MAAM,IAAI;AAC1B,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AAMxC,IAAM,SAAN,MAAM,gBAAe,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAKP,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKR;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,IAAI,aAAa;AAAA,EAChC,kBAAkB,OAAO,WAAW;AAAA,EACpC,WAAW,OAAO;AAChB,SAAK,aAAa,KAAK,KAAK;AAAA,EAC9B;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,eAAe,mBAAmB;AAChD,cAAQ,wBAAwB,sBAAyB,sBAAsB,OAAM,IAAI,qBAAqB,OAAM;AAAA,IACtH;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,UAAU,CAAC;AAAA,IACxB,UAAU;AAAA,IACV,cAAc,SAAS,oBAAoB,IAAI,KAAK;AAClD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,gBAAgB,QAAQ,EAAE,cAAc,IAAI,SAAS,EAAE,mBAAmB,IAAI,cAAc;AAC3G,QAAG,WAAW,IAAI,KAAK;AACvB,QAAG,WAAW,IAAI,SAAS;AAC3B,QAAG,YAAY,YAAY,IAAI,EAAE,eAAe,IAAI,EAAE,mBAAmB,IAAI,UAAU,QAAQ,EAAE,eAAe,IAAI,SAAS,OAAO,EAAE,eAAe,IAAI,SAAS,QAAQ,EAAE,kBAAkB,IAAI,SAAS,IAAI;AAAA,MACjN;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,IAClB;AAAA,IACA,SAAS;AAAA,MACP,cAAc;AAAA,IAChB;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC,WAAW,CAAC,GAAM,0BAA0B;AAAA,IAC9E,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,gBAAgB,EAAE,GAAG,CAAC,iBAAiB,EAAE,GAAG,CAAC,SAAS,iBAAiB,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC,GAAG,SAAS,WAAW,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC,GAAG,OAAO,SAAS,GAAG,MAAM,GAAG,CAAC,GAAG,SAAS,KAAK,CAAC;AAAA,IAClP,UAAU,SAAS,gBAAgB,IAAI,KAAK;AAC1C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,aAAa,CAAC;AACjB,QAAG,WAAW,GAAG,wBAAwB,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,+BAA+B,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,+BAA+B,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAAA,MAChP;AACA,UAAI,KAAK,GAAG;AACV,cAAM,kBAAqB,YAAY,CAAC;AACxC,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,KAAK,EAAE,YAAY,eAAe;AAAA,MAC9D;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAiB,SAAY,MAAM,YAAY;AAAA,IAC9D,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,QAAQ,CAAC;AAAA,IAC/E,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc,YAAY;AAAA,MACpC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,oBAAoB;AAAA,QACpB,uBAAuB;AAAA,QACvB,2BAA2B;AAAA,QAC3B,uBAAuB;AAAA,QACvB,uBAAuB;AAAA,QACvB,0BAA0B;AAAA,QAC1B,uBAAuB;AAAA,QACvB,qBAAqB;AAAA,QACrB,0BAA0B;AAAA,QAC1B,WAAW;AAAA,MACb;AAAA,MACA,WAAW,CAAC,WAAW;AAAA,IACzB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,QAAQ,YAAY;AAAA,IAC9B,SAAS,CAAC,QAAQ,YAAY;AAAA,EAChC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,QAAQ,cAAc,YAAY;AAAA,EAC9C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,QAAQ,YAAY;AAAA,MAC9B,SAAS,CAAC,QAAQ,YAAY;AAAA,IAChC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["AvatarClasses"]}