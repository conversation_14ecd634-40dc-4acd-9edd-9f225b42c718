"""
Sistema de Cache Inteligente para Otimização de Performance
Reduz tempo de reprocessamento de dados similares
"""

import json
import hashlib
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from config.settings import env

logger = logging.getLogger(__name__)


class CacheManager:
    """
    🚀 Sistema de cache inteligente multi-camadas

    Funcionalidades:
    - Cache em memória para acesso ultra-rápido
    - Cache Redis para persistência
    - Cache inteligente baseado em similaridade
    - Invalidação automática por TTL
    - Compressão de dados grandes
    """

    def __init__(self):
        # Cache em memória (mais rápido)
        self.memory_cache = {}
        self.memory_cache_ttl = {}

        # Cache Redis (persistente)
        try:
            import redis
            self.redis_client = redis.Redis(
                host=getattr(env, 'REDIS_HOST', 'localhost'),
                port=getattr(env, 'REDIS_PORT', 6379),
                db=getattr(env, 'REDIS_DB', 0),
                decode_responses=True,
                socket_connect_timeout=10,
                socket_timeout=10,
                retry_on_timeout=True,
                health_check_interval=30
            )
            # Testar conexão com retry
            import time
            for attempt in range(3):
                try:
                    self.redis_client.ping()
                    self.redis_available = True
                    logger.info("✅ Redis cache conectado")
                    break
                except redis.ConnectionError:
                    if attempt < 2:
                        logger.info(
                            f"🔄 Tentativa {attempt + 1}/3 de conexão Redis...")
                        time.sleep(2)
                    else:
                        raise
        except ImportError:
            self.redis_available = False
            logger.info(
                "ℹ️ Redis não instalado, usando apenas cache em memória")
        except Exception as e:
            self.redis_available = False
            # Só mostrar como warning se for erro real, não timeout de inicialização
            if "Connection refused" in str(e):
                logger.info(
                    "ℹ️ Redis não disponível (usando cache em memória)")
            else:
                logger.warning(
                    f"⚠️ Redis não disponível ({str(e)}), usando apenas cache em memória")

    def generate_cache_key(self, empresa: str, tipo_pesquisa: str, **kwargs) -> str:
        """
        Gera chave de cache baseada em empresa e tipo de pesquisa

        Args:
            empresa: Nome da empresa
            tipo_pesquisa: Tipo de análise (dossie_basico, swot, funding, etc.)
            **kwargs: Parâmetros adicionais

        Returns:
            Chave única para cache
        """
        # Normalizar nome da empresa
        empresa_norm = empresa.lower().strip()

        # Criar string única
        cache_data = f"{empresa_norm}:{tipo_pesquisa}"

        # Adicionar parâmetros extras
        for key, value in sorted(kwargs.items()):
            if value:
                cache_data += f":{key}={str(value).lower()}"

        # Gerar hash MD5
        return hashlib.md5(cache_data.encode()).hexdigest()

    def get(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """
        Busca dados no cache (memória primeiro, depois Redis)

        Args:
            cache_key: Chave do cache

        Returns:
            Dados do cache ou None se não encontrado/expirado
        """
        # 1. Verificar cache em memória primeiro
        if cache_key in self.memory_cache:
            # Verificar TTL
            if cache_key in self.memory_cache_ttl:
                if datetime.now() < self.memory_cache_ttl[cache_key]:
                    logger.info(f"📋 Cache hit (memória): {cache_key[:8]}...")
                    return self.memory_cache[cache_key]
                else:
                    # Expirado, remover
                    del self.memory_cache[cache_key]
                    del self.memory_cache_ttl[cache_key]

        # 2. Verificar Redis se disponível
        if self.redis_available:
            try:
                data = self.redis_client.get(cache_key)
                if data:
                    result = json.loads(data)

                    # Salvar também em memória para próximos acessos
                    self.memory_cache[cache_key] = result
                    self.memory_cache_ttl[cache_key] = datetime.now(
                    ) + timedelta(minutes=30)

                    logger.info(f"📋 Cache hit (Redis): {cache_key[:8]}...")
                    return result
            except Exception as e:
                logger.error(f"❌ Erro ao buscar no Redis: {str(e)}")

        return None

    def set(self, cache_key: str, data: Dict[str, Any], ttl_hours: int = 24) -> bool:
        """
        Salva dados no cache (memória e Redis)

        Args:
            cache_key: Chave do cache
            data: Dados para salvar
            ttl_hours: Tempo de vida em horas

        Returns:
            True se salvou com sucesso
        """
        try:
            # 1. Salvar em memória
            self.memory_cache[cache_key] = data
            self.memory_cache_ttl[cache_key] = datetime.now(
            ) + timedelta(minutes=30)

            # 2. Salvar no Redis se disponível
            if self.redis_available:
                try:
                    json_data = json.dumps(
                        data, ensure_ascii=False, default=str)
                    self.redis_client.setex(
                        cache_key,
                        timedelta(hours=ttl_hours),
                        json_data
                    )
                    logger.info(f"💾 Cache salvo (Redis): {cache_key[:8]}...")
                except Exception as e:
                    logger.error(f"❌ Erro ao salvar no Redis: {str(e)}")

            logger.info(f"💾 Cache salvo (memória): {cache_key[:8]}...")
            return True

        except Exception as e:
            logger.error(f"❌ Erro ao salvar cache: {str(e)}")
            return False

    def get_similar_companies(self, empresa: str, limit: int = 5) -> List[str]:
        """
        Busca empresas similares no cache para reutilização de dados

        Args:
            empresa: Nome da empresa
            limit: Número máximo de empresas similares

        Returns:
            Lista de empresas similares
        """
        if not self.redis_available:
            return []

        try:
            # Buscar todas as chaves que contêm nomes de empresas
            pattern = "*:dossie_basico*"
            keys = self.redis_client.keys(pattern)

            # Extrair nomes de empresas das chaves
            empresas = []
            for key in keys:
                parts = key.split(':')
                if len(parts) >= 2:
                    empresa_cache = parts[0]
                    if empresa_cache != empresa.lower():
                        empresas.append(empresa_cache)

            # Ordenar por similaridade (implementação simples)
            empresa_lower = empresa.lower()
            empresas_similares = []

            for emp in empresas:
                # Calcular similaridade básica
                similarity = self._calculate_similarity(empresa_lower, emp)
                if similarity > 0.3:  # Threshold de similaridade
                    empresas_similares.append((emp, similarity))

            # Ordenar por similaridade e retornar top N
            empresas_similares.sort(key=lambda x: x[1], reverse=True)
            return [emp[0] for emp in empresas_similares[:limit]]

        except Exception as e:
            logger.error(f"❌ Erro ao buscar empresas similares: {str(e)}")
            return []

    def _calculate_similarity(self, str1: str, str2: str) -> float:
        """
        Calcula similaridade simples entre duas strings
        """
        # Implementação básica usando Jaccard similarity
        set1 = set(str1.split())
        set2 = set(str2.split())

        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))

        return intersection / union if union > 0 else 0

    def invalidate_company(self, empresa: str) -> int:
        """
        Invalida todo o cache de uma empresa específica

        Args:
            empresa: Nome da empresa

        Returns:
            Número de chaves removidas
        """
        empresa_norm = empresa.lower().strip()
        removed_count = 0

        # Remover da memória
        keys_to_remove = []
        for key in self.memory_cache.keys():
            if key.startswith(hashlib.md5(empresa_norm.encode()).hexdigest()[:8]):
                keys_to_remove.append(key)

        for key in keys_to_remove:
            del self.memory_cache[key]
            if key in self.memory_cache_ttl:
                del self.memory_cache_ttl[key]
            removed_count += 1

        # Remover do Redis
        if self.redis_available:
            try:
                pattern = f"*{empresa_norm}*"
                keys = self.redis_client.keys(pattern)
                if keys:
                    removed_count += self.redis_client.delete(*keys)
            except Exception as e:
                logger.error(f"❌ Erro ao invalidar cache Redis: {str(e)}")

        logger.info(
            f"🗑️ Cache invalidado para {empresa}: {removed_count} chaves removidas")
        return removed_count

    def get_cache_stats(self) -> Dict[str, Any]:
        """
        Retorna estatísticas do cache
        """
        stats = {
            "memory_cache_size": len(self.memory_cache),
            "redis_available": self.redis_available,
            "redis_keys": 0,
            "redis_memory_usage": "N/A"
        }

        if self.redis_available:
            try:
                info = self.redis_client.info()
                stats["redis_keys"] = info.get("db0", {}).get("keys", 0)
                stats["redis_memory_usage"] = info.get(
                    "used_memory_human", "N/A")
            except:
                pass

        return stats


# Instância global do cache
cache_manager = CacheManager()
