import {
  __name,
  configureSvgSize,
  log,
  select_default
} from "./chunk-ULV4NQHW.js";

// node_modules/mermaid/dist/chunks/mermaid.core/chunk-RZ5BOZE2.mjs
var getDiagramElement = __name((id, securityLevel) => {
  let sandboxElement;
  if (securityLevel === "sandbox") {
    sandboxElement = select_default("#i" + id);
  }
  const root = securityLevel === "sandbox" ? select_default(sandboxElement.nodes()[0].contentDocument.body) : select_default("body");
  const svg = root.select(`[id="${id}"]`);
  return svg;
}, "getDiagramElement");
var setupViewPortForSVG = __name((svg, padding, cssDiagram, useMaxWidth) => {
  svg.attr("class", cssDiagram);
  const {
    width,
    height,
    x,
    y
  } = calculateDimensionsWithPadding(svg, padding);
  configureSvgSize(svg, height, width, useMaxWidth);
  const viewBox = createViewBox(x, y, width, height, padding);
  svg.attr("viewBox", viewBox);
  log.debug(`viewBox configured: ${viewBox} with padding: ${padding}`);
}, "setupViewPortForSVG");
var calculateDimensionsWithPadding = __name((svg, padding) => {
  const bounds = svg.node()?.getBBox() || {
    width: 0,
    height: 0,
    x: 0,
    y: 0
  };
  return {
    width: bounds.width + padding * 2,
    height: bounds.height + padding * 2,
    x: bounds.x,
    y: bounds.y
  };
}, "calculateDimensionsWithPadding");
var createViewBox = __name((x, y, width, height, padding) => {
  return `${x - padding} ${y - padding} ${width} ${height}`;
}, "createViewBox");

export {
  getDiagramElement,
  setupViewPortForSVG
};
//# sourceMappingURL=chunk-F75XBXFM.js.map
