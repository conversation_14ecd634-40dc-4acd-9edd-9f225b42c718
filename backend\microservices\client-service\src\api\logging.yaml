version: 1
disable_existing_loggers: false

formatters:
  default:
    format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  
  json:
    (): pythonjsonlogger.jsonlogger.JsonFormatter
    format: '%(asctime)s %(name)s %(levelname)s %(message)s'

handlers:
  console:
    class: logging.StreamHandler
    formatter: json
    stream: ext://sys.stdout

  file:
    class: logging.handlers.RotatingFileHandler
    formatter: json
    filename: /app/logs/client-service.log
    maxBytes: 10485760  # 10MB
    backupCount: 5
    encoding: utf8

root:
  level: INFO
  handlers: [console]

loggers:
  uvicorn:
    level: INFO
  
  uvicorn.error:
    level: INFO
  
  uvicorn.access:
    level: INFO
    handlers: [console]
    propagate: no
  
  src:
    level: DEBUG
    handlers: [console]
    propagate: no 