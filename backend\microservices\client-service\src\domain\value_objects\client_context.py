"""Value Object para contexto de cliente com validação."""

from typing import Any


class ClientContext:
    """Value Object imutável para contexto de cliente.

    Garante que o contexto tem tamanho mínimo e está formatado corretamente.
    """

    MIN_LENGTH = 50
    MAX_LENGTH = 5000

    def __init__(self, context: str) -> None:
        """Inicializa ClientContext com validação.

        Args:
            context: Contexto/descrição do cliente

        Raises:
            ValueError: Se o contexto for inválido
        """
        if not context:
            raise ValueError("Contexto não pode ser vazio")

        # Remove espaços extras e normaliza quebras de linha
        context = context.strip()
        context = ' '.join(context.split())

        if len(context) < self.MIN_LENGTH:
            raise ValueError(
                f"Contexto deve ter no mínimo {self.MIN_LENGTH} caracteres. "
                f"Atual: {len(context)}"
            )

        if len(context) > self.MAX_LENGTH:
            raise ValueError(
                f"Contexto não pode exceder {self.MAX_LENGTH} caracteres. "
                f"Atual: {len(context)}"
            )

        self._value = context

    @property
    def value(self) -> str:
        """Retorna o valor do contexto."""
        return self._value

    @property
    def length(self) -> int:
        """Retorna o tamanho do contexto."""
        return len(self._value)

    @property
    def summary(self) -> str:
        """Retorna um resumo do contexto (primeiros 100 caracteres)."""
        if len(self._value) <= 100:
            return self._value
        return f"{self._value[:97]}..."

    def contains_keyword(self, keyword: str) -> bool:
        """Verifica se o contexto contém uma palavra-chave (case-insensitive)."""
        return keyword.lower() in self._value.lower()

    def __str__(self) -> str:
        """Representação string do contexto."""
        return self._value

    def __repr__(self) -> str:
        """Representação para debug."""
        return f"ClientContext('{self.summary}')"

    def __eq__(self, other: Any) -> bool:
        """Compara dois contextos."""
        if not isinstance(other, ClientContext):
            return False
        return self._value == other._value

    def __hash__(self) -> int:
        """Hash do contexto para uso em sets/dicts."""
        return hash(self._value)
