{"ast": null, "code": "import { merge } from './merge';\nexport function mergeWith(...otherSources) {\n  return merge(...otherSources);\n}", "map": {"version": 3, "names": ["merge", "mergeWith", "otherSources"], "sources": ["C:/projetos/scope-ai/frontend/node_modules/rxjs/dist/esm/internal/operators/mergeWith.js"], "sourcesContent": ["import { merge } from './merge';\nexport function mergeWith(...otherSources) {\n    return merge(...otherSources);\n}\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,SAAS;AAC/B,OAAO,SAASC,SAASA,CAAC,GAAGC,YAAY,EAAE;EACvC,OAAOF,KAAK,CAAC,GAAGE,YAAY,CAAC;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}