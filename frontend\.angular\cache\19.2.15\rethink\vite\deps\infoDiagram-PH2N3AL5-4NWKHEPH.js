import {
  parse
} from "./chunk-Q5P4DLGB.js";
import "./chunk-XFR7GOLJ.js";
import "./chunk-3OW24C4G.js";
import "./chunk-UYJZXBVA.js";
import "./chunk-A4QYEFQ7.js";
import "./chunk-NP6LAYGK.js";
import "./chunk-76TRM2IH.js";
import "./chunk-PZQ3YB7N.js";
import "./chunk-YBC7CDLT.js";
import {
  package_default
} from "./chunk-ZM2LTMNF.js";
import {
  selectSvgElement
} from "./chunk-25UUFKYO.js";
import {
  __name,
  configureSvgSize,
  log
} from "./chunk-ULV4NQHW.js";
import "./chunk-UKEJCSU4.js";
import "./chunk-S3PAIZX7.js";
import {
  __async
} from "./chunk-SERTD5K6.js";

// node_modules/mermaid/dist/chunks/mermaid.core/infoDiagram-PH2N3AL5.mjs
var parser = {
  parse: __name((input) => __async(null, null, function* () {
    const ast = yield parse("info", input);
    log.debug(ast);
  }), "parse")
};
var DEFAULT_INFO_DB = {
  version: package_default.version
};
var getVersion = __name(() => DEFAULT_INFO_DB.version, "getVersion");
var db = {
  getVersion
};
var draw = __name((text, id, version) => {
  log.debug("rendering info diagram\n" + text);
  const svg = selectSvgElement(id);
  configureSvgSize(svg, 100, 400, true);
  const group = svg.append("g");
  group.append("text").attr("x", 100).attr("y", 40).attr("class", "version").attr("font-size", 32).style("text-anchor", "middle").text(`v${version}`);
}, "draw");
var renderer = {
  draw
};
var diagram = {
  parser,
  db,
  renderer
};
export {
  diagram
};
//# sourceMappingURL=infoDiagram-PH2N3AL5-4NWKHEPH.js.map
