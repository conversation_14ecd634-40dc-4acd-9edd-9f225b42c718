"""
Endpoints Básicos - Extraído de main.py
Responsabilidade única: endpoints de sistema (ping, websocket)
"""

import json
from fastapi import APIRouter, WebSocket, WebSocketDisconnect
from shared.websocket_manager import websocket_manager

router = APIRouter()


@router.get("/ping")
async def ping():
    """Endpoint básico de ping (mantido para compatibilidade)"""
    return {"ping": "pong"}


@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """Endpoint WebSocket para comunicação em tempo real"""
    await websocket_manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_json()
            # Se for um ping, responde pong
            if isinstance(data, dict) and data.get("type") == "ping":
                await websocket_manager.send_personal_message(
                    json.dumps({"type": "pong", "timestamp": data.get(
                        "timestamp", ""), "msg": "pong"}),
                    websocket
                )
            else:
                await websocket_manager.send_personal_message(
                    json.dumps({"type": "echo", "data": data}),
                    websocket
                )
    except WebSocketDisconnect:
        websocket_manager.disconnect(websocket)
