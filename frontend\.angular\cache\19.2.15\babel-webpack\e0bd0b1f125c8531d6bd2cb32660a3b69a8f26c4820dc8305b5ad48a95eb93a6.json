{"ast": null, "code": "import { concat } from '../observable/concat';\nimport { take } from './take';\nimport { ignoreElements } from './ignoreElements';\nimport { mapTo } from './mapTo';\nimport { mergeMap } from './mergeMap';\nimport { innerFrom } from '../observable/innerFrom';\nexport function delayWhen(delayDurationSelector, subscriptionDelay) {\n  if (subscriptionDelay) {\n    return source => concat(subscriptionDelay.pipe(take(1), ignoreElements()), source.pipe(delayWhen(delayDurationSelector)));\n  }\n  return mergeMap((value, index) => innerFrom(delayDurationSelector(value, index)).pipe(take(1), mapTo(value)));\n}", "map": {"version": 3, "names": ["concat", "take", "ignoreElements", "mapTo", "mergeMap", "innerFrom", "<PERSON><PERSON>hen", "delayDurationSelector", "subscriptionDelay", "source", "pipe", "value", "index"], "sources": ["C:/projetos/scope-ai/frontend/node_modules/rxjs/dist/esm/internal/operators/delayWhen.js"], "sourcesContent": ["import { concat } from '../observable/concat';\nimport { take } from './take';\nimport { ignoreElements } from './ignoreElements';\nimport { mapTo } from './mapTo';\nimport { mergeMap } from './mergeMap';\nimport { innerFrom } from '../observable/innerFrom';\nexport function delayWhen(delayDurationSelector, subscriptionDelay) {\n    if (subscriptionDelay) {\n        return (source) => concat(subscriptionDelay.pipe(take(1), ignoreElements()), source.pipe(delayWhen(delayDurationSelector)));\n    }\n    return mergeMap((value, index) => innerFrom(delayDurationSelector(value, index)).pipe(take(1), mapTo(value)));\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,IAAI,QAAQ,QAAQ;AAC7B,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,SAAS,QAAQ,yBAAyB;AACnD,OAAO,SAASC,SAASA,CAACC,qBAAqB,EAAEC,iBAAiB,EAAE;EAChE,IAAIA,iBAAiB,EAAE;IACnB,OAAQC,MAAM,IAAKT,MAAM,CAACQ,iBAAiB,CAACE,IAAI,CAACT,IAAI,CAAC,CAAC,CAAC,EAAEC,cAAc,CAAC,CAAC,CAAC,EAAEO,MAAM,CAACC,IAAI,CAACJ,SAAS,CAACC,qBAAqB,CAAC,CAAC,CAAC;EAC/H;EACA,OAAOH,QAAQ,CAAC,CAACO,KAAK,EAAEC,KAAK,KAAKP,SAAS,CAACE,qBAAqB,CAACI,KAAK,EAAEC,KAAK,CAAC,CAAC,CAACF,IAAI,CAACT,IAAI,CAAC,CAAC,CAAC,EAAEE,KAAK,CAACQ,KAAK,CAAC,CAAC,CAAC;AACjH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}