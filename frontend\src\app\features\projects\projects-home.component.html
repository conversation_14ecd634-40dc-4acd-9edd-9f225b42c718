<!-- <PERSON><PERSON><PERSON><PERSON><PERSON> da Página de Sugestão de Projetos -->
<div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8">
	<div>
		<h1 class="text-3xl font-bold text-gray-900">{{ pageTitle }}</h1>
		<p class="text-gray-500">{{ pageDescription }}</p>
	</div>
	<div class="flex flex-wrap gap-3 w-full md:w-auto">
		<button
			pButton
			[label]="newProjectButton"
			icon="pi pi-plus"
			class="p-button-primary w-full md:w-auto"
			(click)="onNewProject()"
		></button>
		<button
			pButton
			[label]="importButton"
			icon="pi pi-file-import"
			class="p-button-outlined w-full md:w-auto"
			(click)="onImport()"
		></button>
		<button
			pButton
			label="Atualizar"
			icon="pi pi-refresh"
			class="p-button-outlined w-full md:w-auto"
			(click)="refreshProjects()"
		></button>
	</div>
</div>

<!-- <PERSON>lt<PERSON> e Controles -->
<div class="bg-white p-5 rounded-xl shadow-sm mb-8">
	<div class="flex flex-col md:flex-row gap-4 items-center justify-between">
		<!-- Barra de pesquisa -->
		<div class="w-full md:w-1/3">
			<input
				type="text"
				pInputText
				class="w-full"
				placeholder="Buscar sugestões..."
				[(ngModel)]="searchQuery"
				(input)="onSearchChange()"
			/>
		</div>

		<!-- Filtros -->
		<div class="flex flex-col sm:flex-row gap-3 w-full md:w-auto items-center">
			<p-select
				[options]="statusOptions"
				[(ngModel)]="selectedStatus"
				placeholder="Status"
				optionLabel="label"
				class="w-full md:w-44"
				[appendTo]="'body'"
				(onChange)="onSearchChange()"
			></p-select>

			<p-select
				[options]="sectorOptions"
				[(ngModel)]="selectedSector"
				placeholder="Setor"
				optionLabel="label"
				class="w-full md:w-44"
				[appendTo]="'body'"
				(onChange)="onSectorChange()"
			></p-select>

			<p-select
				[options]="sortOptions"
				[(ngModel)]="selectedSort"
				placeholder="Ordenar por"
				optionLabel="label"
				class="w-full md:w-48"
				[appendTo]="'body'"
				(onChange)="onSortChange($event)"
			></p-select>

			<!-- Toggle de visualização -->
			<div class="flex border border-gray-200 rounded-lg overflow-hidden bg-gray-50">
				<button
					pButton
					icon="pi pi-th-large"
					[ngClass]="{
						'bg-primary-500 text-white': layout === 'grid',
						'text-gray-600 hover:bg-gray-100': layout !== 'grid',
					}"
					(click)="layout = 'grid'"
					class="p-button-text"
					pTooltip="Visualização em grade"
					tooltipPosition="top"
				></button>
				<button
					pButton
					icon="pi pi-list"
					[ngClass]="{
						'bg-primary-500 text-white': layout === 'list',
						'text-gray-600 hover:bg-gray-100': layout !== 'list',
					}"
					(click)="layout = 'list'"
					class="p-button-text"
					pTooltip="Visualização em lista"
					tooltipPosition="top"
				></button>
			</div>

			<button
				pButton
				label="Limpar"
				icon="pi pi-filter-slash"
				class="p-button-outlined p-button-sm"
				(click)="clearFilters()"
			></button>
		</div>
	</div>
</div>

<!-- Contador de resultados -->
@if (projects().length > 0) {
	<div class="flex justify-between items-center mb-5">
		<p class="text-sm text-gray-600">
			Mostrando <span class="font-medium">{{ projects().length }}</span> sugestões
		</p>
		<p-select
			[options]="pageSizeOptions"
			[(ngModel)]="selectedPageSize"
			optionLabel="label"
			[showClear]="false"
			class="w-40"
			[appendTo]="'body'"
			(onChange)="onPageSizeChange($event)"
		></p-select>
	</div>
}

<!-- Visualização dos Projetos -->
@if (projects().length === 0 && !loading()) {
	<div class="bg-white rounded-xl shadow-sm p-8 text-center">
		<i class="pi pi-inbox text-5xl text-gray-300 mb-4"></i>
		<h3 class="text-xl font-medium text-gray-700 mb-2">{{ emptyTitle }}</h3>
		<p class="text-gray-500 mb-6">{{ emptyDescription }}</p>
		<button
			pButton
			[label]="createButton"
			icon="pi pi-plus"
			class="p-button-primary"
			(click)="onNewProject()"
		></button>
	</div>
} @else {
	@if (layout === 'grid') {
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
			@for (project of projects().slice(first, first + rows); track $index) {
				<app-project-card
					[project]="project"
					[showActions]="true"
					[clickable]="true"
					(cardClick)="onProjectCardClick($event)"
				></app-project-card>
			}
		</div>
	} @else {
		<div class="flex flex-col gap-5">
			@for (project of projects().slice(first, first + rows); track $index) {
				<div
					class="flex flex-col sm:flex-row w-full bg-white p-5 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200"
				>
					<div class="sm:w-72 flex-shrink-0 relative mb-4 sm:mb-0 sm:mr-5">
						<p-tag
							[value]="project.status"
							[severity]="getSeverity(project.status)"
							[rounded]="true"
							styleClass="absolute top-3 left-3 z-10"
						></p-tag>
						<div class="h-full min-h-[120px] bg-gray-100 rounded flex items-end p-4">
							<h3 class="text-primary-700 text-lg font-semibold">{{ project.nome_projeto }}</h3>
						</div>
					</div>

					<div class="flex-1 flex flex-col">
						<div class="flex-1">
							<div class="flex justify-between mb-3">
								<div>
									<span class="text-gray-700 font-medium">{{ project.cliente_nome }}</span>
									<span class="text-gray-500 text-sm ml-2">• {{ project.cliente_sector }}</span>
								</div>
								<div>
									<button
										pButton
										icon="pi pi-ellipsis-v"
										class="p-button-rounded p-button-text p-button-sm"
										[pTooltip]="'Opções'"
										tooltipPosition="left"
										(click)="showMenuAtPosition($event, menu2, project)"
									></button>
									<p-menu #menu2 [model]="itemsMenu" [popup]="true" appendTo="body"></p-menu>
								</div>
							</div>

							<p class="text-gray-600 text-sm mb-4">{{ project.resumo }}</p>

							<div class="flex flex-wrap gap-1.5 mb-4">
								@for (tag of project.tags; track $index) {
									<p-chip [label]="tag" styleClass="bg-gray-100 text-gray-700 text-xs"></p-chip>
								}
							</div>
						</div>

						<div class="flex flex-col sm:flex-row gap-5">
							<div class="flex-1">
								<div class="flex justify-between mb-2">
									<span class="text-sm">Progresso</span>
									<span class="text-sm font-medium">{{ project.progresso }}%</span>
								</div>
								<p-progressBar
									[value]="project.progresso"
									[showValue]="false"
									[style]="{ height: '8px' }"
								></p-progressBar>
							</div>

							<div class="flex items-center gap-5">
								<div class="flex items-center">
									<span class="text-sm text-gray-600 mr-2">Equipe:</span>
									<span class="text-sm text-gray-500">{{ project.team ?? '-' }}</span>
								</div>
								<div class="text-xs text-gray-500">
									{{ formatDate(project.updated_at) }}
								</div>
							</div>
						</div>
					</div>
				</div>
			}
		</div>
	}

	<!-- Paginação -->
	@if (projects().length > 0) {
		<div class="mt-6">
			<p-paginator
				[rows]="rows"
				[totalRecords]="projects().length"
				[rowsPerPageOptions]="[5, 9, 10, 20, 50]"
				styleClass="bg-white rounded-lg shadow-sm"
				(onPageChange)="onPageChange($event)"
				[first]="first"
			></p-paginator>
		</div>
	}
}

<!-- Modal de Nova Sugestão -->
<p-dialog
	header="{{ modalTitle }}"
	[(visible)]="showNewProjectDialog"
	[modal]="true"
	[closable]="false"
	[style]="{ 'width': '400px', 'max-width': '95vw', 'max-height': '95vh', 'height': 'auto' }"
	role="dialog"
>
	<form [formGroup]="newProjectForm" (ngSubmit)="saveNewProject()" autocomplete="off">
		<div class="mb-3">
			<label for="projectName" class="block mb-1 font-medium">{{ modalNameLabel }}</label>
			<input
				id="projectName"
				pInputText
				type="text"
				formControlName="name"
				[attr.aria-label]="modalNameAria"
				class="w-full"
				[ngClass]="{
					'p-invalid':
						newProjectForm.controls['name'].invalid &&
						(newProjectForm.controls['name'].dirty || newProjectForm.controls['name'].touched),
				}"
			/>
			@if (
				newProjectForm.controls['name'].invalid &&
				(newProjectForm.controls['name'].dirty || newProjectForm.controls['name'].touched)
			) {
				<div class="text-red-600 text-xs mt-1">
					@if (newProjectForm.controls['name'].errors?.['required']) {
						<div>Nome da sugestão é obrigatório.</div>
					}
				</div>
			}
		</div>
		<div class="mb-3">
			<label for="projectClient" class="block mb-1 font-medium">{{ modalClientLabel }}</label>
			<p-select
				id="projectClient"
				[options]="clientOptions"
				formControlName="client"
				[placeholder]="modalClientPlaceholder"
				optionLabel="label"
				optionValue="value"
				class="w-full"
				[appendTo]="'body'"
				[ngClass]="{
					'p-invalid':
						newProjectForm.controls['client'].invalid &&
						(newProjectForm.controls['client'].dirty || newProjectForm.controls['client'].touched),
				}"
				[attr.aria-label]="modalClientLabel"
			></p-select>
			<!-- Cliente agora é opcional, então não mostramos erro -->
		</div>
		<div class="mb-3">
			<label for="projectDescription" class="block mb-1">{{ modalDescriptionLabel }}</label>
			<div class="relative">
				<textarea
					id="projectDescription"
					pInputTextarea
					formControlName="description"
					class="w-full pr-10 border border-gray-300 rounded-md p-2"
					rows="4"
					[attr.aria-label]="'Descrição da sugestão'"
					[readonly]="isEnhancingDescription"
				></textarea>

				<!-- Loading overlay -->
				@if (isEnhancingDescription) {
					<div class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-md">
						<div class="flex items-center gap-2">
							<i class="pi pi-spin pi-spinner text-purple-600"></i>
							<span class="text-purple-600 font-medium">Melhorando com IA...</span>
						</div>
					</div>
				}

				<button
					pButton
					type="button"
					[icon]="isEnhancingDescription ? 'pi pi-spin pi-spinner' : 'pi pi-sparkles'"
					class="p-button-rounded p-button-text p-button-sm absolute top-2 right-2"
					pTooltip="Melhorar descrição com IA"
					tooltipPosition="left"
					[disabled]="!newProjectForm.get('description')?.value || isEnhancingDescription"
					(click)="enhanceDescription()"
				></button>
			</div>
		</div>

		<!-- Seção de Chat com IA -->
		<div class="mb-3 border-t pt-3">
			<div class="flex items-center justify-between mb-3">
				<div class="flex items-center gap-2">
					<i class="pi pi-comments text-purple-600"></i>
					<label class="font-medium">Conversar com IA sobre o projeto</label>
				</div>
				@if (aiConfidenceScore > 0 && !isProjectScopeAgreed) {
					<div class="flex items-center gap-2">
						<span class="text-sm text-gray-600">Confiança:</span>
						<div class="w-24 bg-gray-200 rounded-full h-2">
							<div
								class="h-2 rounded-full transition-all duration-300"
								[ngClass]="{
									'bg-red-500': aiConfidenceScore < 50,
									'bg-yellow-500': aiConfidenceScore >= 50 && aiConfidenceScore < 80,
									'bg-green-500': aiConfidenceScore >= 80,
								}"
								[style.width.%]="aiConfidenceScore"
							></div>
						</div>
						<span
							class="text-sm font-medium"
							[ngClass]="{
								'text-red-600': aiConfidenceScore < 50,
								'text-yellow-600': aiConfidenceScore >= 50 && aiConfidenceScore < 80,
								'text-green-600': aiConfidenceScore >= 80,
							}"
							>{{ aiConfidenceScore }}%</span
						>
					</div>
				}
			</div>

			<!-- Histórico do Chat -->
			<div class="bg-gray-50 rounded-lg p-3 mb-3 max-h-[300px] overflow-y-auto" *ngIf="aiChatHistory.length > 0">
				<div *ngFor="let message of aiChatHistory" class="mb-2">
					<div [ngClass]="message.sender === 'user' ? 'text-right' : 'text-left'">
						<span
							[ngClass]="
								message.sender === 'user'
									? 'bg-blue-100 text-blue-800 rounded-lg px-3 py-1 inline-block'
									: 'bg-purple-100 text-purple-800 rounded-lg px-3 py-1 inline-block'
							"
						>
							{{ message.text }}
						</span>
					</div>
				</div>
			</div>

			<!-- Input do Chat -->
			<div class="flex gap-2">
				<input
					type="text"
					pInputText
					[(ngModel)]="aiChatInput"
					[ngModelOptions]="{ standalone: true }"
					placeholder="Digite sua mensagem..."
					class="flex-1"
					(keyup.enter)="sendAiMessage()"
					[disabled]="!newProjectForm.get('description')?.value || isProjectScopeAgreed"
				/>
				<button
					pButton
					type="button"
					icon="pi pi-send"
					class="p-button-rounded p-button-primary"
					[disabled]="
						!aiChatInput.trim() || !newProjectForm.get('description')?.value || isProjectScopeAgreed
					"
					(click)="sendAiMessage()"
				></button>
			</div>

			<!-- Status de Acordo -->
			<div *ngIf="isProjectScopeAgreed" class="mt-3 p-3 bg-green-50 rounded-lg border border-green-200">
				<div class="flex items-center gap-2">
					<i class="pi pi-check-circle text-green-600"></i>
					<span class="text-green-700 font-medium">IA compreendeu o escopo do projeto!</span>
				</div>
			</div>
		</div>

		<div class="flex justify-end gap-2 mt-4">
			<button
				pButton
				type="button"
				[label]="modalCancel"
				class="p-button-text"
				(click)="cancelNewProject()"
			></button>
			<button
				pButton
				type="submit"
				[label]="modalSave"
				class="p-button-primary"
				[disabled]="!isProjectScopeAgreed"
			></button>
		</div>
	</form>
</p-dialog>

<p-drawer [(visible)]="detalhesProjeto" position="right" styleClass="project-details-drawer w-3/4 max-md:w-full">
	<!-- Header Section com Gradiente -->
	<div
		class="drawer-header bg-gradient-to-br from-blue-500 to-indigo-600 text-white p-6 rounded-lg mb-6 relative overflow-hidden"
	>
		<!-- Decorative elements -->
		<div class="absolute top-0 right-0 w-32 h-32 bg-white opacity-10 rounded-full -mr-16 -mt-16"></div>
		<div class="absolute bottom-0 left-0 w-24 h-24 bg-white opacity-10 rounded-full -ml-12 -mb-12"></div>

		<div class="relative z-10">
			<div class="flex items-center justify-between mb-4">
				<h2 class="text-2xl font-bold text-white drop-shadow-lg">{{ tituloDrawer() }}</h2>
				<div class="flex items-center gap-2">
					<p-tag
						[value]="'⭐ ' + (projectSelected?.pontuacao || 0) + '/100'"
						class="bg-white/20 border border-white/30 text-white font-bold"
						[rounded]="true"
					></p-tag>
				</div>
			</div>

			<div class="flex items-center gap-2 text-blue-100">
				<i class="pi pi-building text-lg"></i>
				<span class="text-lg">{{ projectSelected?.cliente_nome }}</span>
				<span class="text-blue-200">•</span>
				<span class="text-lg">{{ projectSelected?.cliente_sector }}</span>
			</div>
		</div>
	</div>

	<!-- Justificativa Section -->
	<div class="bg-white rounded-lg shadow-sm border border-gray-100 p-6 mb-6">
		<div class="flex items-center gap-3 mb-4">
			<div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
				<i class="pi pi-lightbulb text-blue-600 text-lg"></i>
			</div>
			<h3 class="text-xl font-semibold text-gray-900">Justificativa do Projeto</h3>
		</div>
		<p class="text-gray-700 leading-relaxed text-base">{{ justificativaDrawer() }}</p>
	</div>

	<!-- Importância e Benefícios Grid -->
	<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
		<!-- Importância Card -->
		<div class="bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg border border-orange-200 p-6">
			<div class="flex items-center gap-3 mb-4">
				<div class="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center">
					<i class="pi pi-exclamation-triangle text-white text-lg"></i>
				</div>
				<h3 class="text-xl font-semibold text-orange-900">Importância</h3>
			</div>
			<p class="text-orange-800 leading-relaxed">{{ importanciaDrawer() }}</p>
		</div>

		<!-- Benefícios Card -->
		<div class="bg-gradient-to-br from-green-50 to-green-100 rounded-lg border border-green-200 p-6">
			<div class="flex items-center gap-3 mb-4">
				<div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
					<i class="pi pi-check-circle text-white text-lg"></i>
				</div>
				<h3 class="text-xl font-semibold text-green-900">Benefícios</h3>
			</div>
			<p class="text-green-800 leading-relaxed">{{ beneficiosDrawer() }}</p>
		</div>
	</div>

	<!-- Agentes Análises Section -->
	<div class="bg-white rounded-lg shadow-sm border border-gray-100 p-6 mb-6">
		<div class="flex items-center gap-3 mb-6">
			<div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
				<i class="pi pi-users text-purple-600 text-lg"></i>
			</div>
			<h3 class="text-xl font-semibold text-gray-900">Análises dos Especialistas</h3>
		</div>

		<div class="space-y-4">
			@for (agente of agentesJustificativasDrawer(); track $index) {
				<div
					class="bg-gray-50 rounded-lg p-5 border border-gray-200 hover:bg-gray-100 transition-colors duration-200"
				>
					<div class="flex items-start justify-between mb-3">
						<div class="flex items-center gap-3">
							<div
								class="w-8 h-8 bg-indigo-500 rounded-full flex items-center justify-center flex-shrink-0"
							>
								<i class="pi pi-user text-white text-sm"></i>
							</div>
							<div>
								<h4 class="font-semibold text-gray-900 text-lg">{{ agente.nome_agente }}</h4>
								<p class="text-sm text-gray-600">Especialista</p>
							</div>
						</div>
						<div class="flex items-center gap-2">
							<p-tag
								[value]="(agente.pontuacao || 0) + '/100'"
								[severity]="getScoreSeverity(agente.pontuacao || 0)"
								[rounded]="true"
								class="font-medium"
							></p-tag>
						</div>
					</div>
					<p class="text-gray-700 leading-relaxed">{{ agente.justificativa }}</p>
				</div>
			}
		</div>
	</div>

	<!-- Footer Actions -->
	<ng-template #footer>
		<div class="flex flex-col sm:flex-row gap-3 justify-end">
			<button
				pButton
				label="Fechar"
				icon="pi pi-times"
				class="p-button-outlined p-button-secondary"
				(click)="detalhesProjeto = false"
			></button>
			<button
				pButton
				[label]="getEstimateButtonText()"
				[icon]="getEstimateButtonIcon()"
				class="p-button-primary"
				[disabled]="
					estimativaProcessando() || (projectSelected && projectIsProcessingEstimate(projectSelected))
				"
				(click)="gerarEstimativa()"
			></button>
		</div>
	</ng-template>
</p-drawer>

<!-- 📊 Modal/Drawer de Visualização da Estimativa Detalhada -->
<p-drawer [(visible)]="estimativaVisible" position="right" styleClass="estimativa-details-drawer w-5/6 max-md:w-full">
	@if (estimativaDetalhada(); as estimativa) {
		<!-- Header da Estimativa -->
		<div
			class="bg-gradient-to-br from-purple-500 to-blue-600 text-white p-6 rounded-lg mb-6 relative overflow-hidden"
		>
			<div class="absolute top-0 right-0 w-32 h-32 bg-white opacity-10 rounded-full -mr-16 -mt-16"></div>
			<div class="absolute bottom-0 left-0 w-24 h-24 bg-white opacity-10 rounded-full -ml-12 -mb-12"></div>

			<div class="relative z-10">
				<div class="flex items-center justify-between mb-4">
					<h2 class="text-2xl font-bold text-white drop-shadow-lg">📊 {{ getNomeProjetoReal() }}</h2>
					<p-tag
						value="✅ Concluída"
						class="bg-green-500 border-green-400 text-white font-bold"
						[rounded]="true"
					></p-tag>
				</div>

				<div class="flex items-center gap-2 text-purple-100">
					<i class="pi pi-chart-line text-lg"></i>
					<span class="text-lg">Estimativa Detalhada do Projeto</span>
				</div>

				<!-- Quick Stats - USANDO DADOS DINÂMICOS -->
				<div class="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
					<div class="bg-white bg-opacity-20 rounded-lg p-3 backdrop-blur-sm">
						<div class="text-xs text-purple-100 uppercase tracking-wide">Duração</div>
						<div class="text-xl font-bold">{{ getTotalDurationDynamic() }}</div>
					</div>
					<div class="bg-white bg-opacity-20 rounded-lg p-3 backdrop-blur-sm">
						<div class="text-xs text-purple-100 uppercase tracking-wide">Equipe</div>
						<div class="text-xl font-bold">{{ getTotalTeamSizeDynamic() }} pessoas</div>
					</div>
					<div class="bg-white bg-opacity-20 rounded-lg p-3 backdrop-blur-sm">
						<div class="text-xs text-purple-100 uppercase tracking-wide">Tecnologias</div>
						<div class="text-xl font-bold">{{ getTechCountDynamic() }}</div>
					</div>
					<div class="bg-white bg-opacity-20 rounded-lg p-3 backdrop-blur-sm">
						<div class="text-xs text-purple-100 uppercase tracking-wide">Investimento</div>
						<div class="text-xl font-bold">{{ getInvestmentRangeDynamic() }}</div>
					</div>
				</div>
			</div>
		</div>

		<!-- Tabs Interface -->
		<p-tabView styleClass="estimativa-tabs">
			<!-- 1. Dashboard Overview -->
			<p-tabPanel header="Dashboard" leftIcon="pi pi-chart-bar">
				<div class="space-y-6">
					<!-- Métricas Principais -->
					<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
						<div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200">
							<div class="flex items-center justify-between">
								<div>
									<p class="text-sm font-medium text-blue-600">Valor Total</p>
									<p class="text-2xl font-bold text-blue-700">
										{{ formatCurrencyDynamic(getCustosEstimados()?.total_estimada || 0) }}
									</p>
								</div>
								<div class="bg-blue-500 rounded-lg p-3">
									<i class="pi pi-dollar text-white text-xl"></i>
								</div>
							</div>
						</div>

						<div
							class="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-4 border border-green-200"
						>
							<div class="flex items-center justify-between">
								<div>
									<p class="text-sm font-medium text-green-600">Duração</p>
									<p class="text-2xl font-bold text-green-700">{{ getTotalDurationDynamic() }}</p>
								</div>
								<div class="bg-green-500 rounded-lg p-3">
									<i class="pi pi-calendar text-white text-xl"></i>
								</div>
							</div>
						</div>

						<div
							class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-4 border border-purple-200"
						>
							<div class="flex items-center justify-between">
								<div>
									<p class="text-sm font-medium text-purple-600">Sprints</p>
									<p class="text-2xl font-bold text-purple-700">
										{{ getEstimateData()?.total_de_sprints || 'N/A' }}
									</p>
								</div>
								<div class="bg-purple-500 rounded-lg p-3">
									<i class="pi pi-sitemap text-white text-xl"></i>
								</div>
							</div>
						</div>

						<div
							class="bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl p-4 border border-orange-200"
						>
							<div class="flex items-center justify-between">
								<div>
									<p class="text-sm font-medium text-orange-600">Risco</p>
									<p class="text-2xl font-bold text-orange-700">{{ calculateRiskFactor() }}/10</p>
								</div>
								<div class="bg-orange-500 rounded-lg p-3">
									<i class="pi pi-exclamation-triangle text-white text-xl"></i>
								</div>
							</div>
						</div>
					</div>

					<!-- Distribuição de Custos -->
					<div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
						<h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
							<i class="pi pi-chart-pie text-blue-500"></i>
							Distribuição de Custos
						</h3>
						<div class="space-y-4">
							<div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
								<span class="font-medium text-gray-900">Desenvolvimento</span>
								<div class="flex items-center gap-3">
									<div class="w-32 bg-gray-200 rounded-full h-2">
										<div
											class="bg-blue-500 h-2 rounded-full"
											[style.width.%]="getCostPercentage('desenvolvimento')"
										></div>
									</div>
									<span class="font-bold text-blue-600">{{
										formatCurrency(getCostValue('desenvolvimento'))
									}}</span>
								</div>
							</div>
							<div class="flex items-center justify-between p-3 bg-green-50 rounded-lg">
								<span class="font-medium text-gray-900">Infraestrutura</span>
								<div class="flex items-center gap-3">
									<div class="w-32 bg-gray-200 rounded-full h-2">
										<div
											class="bg-green-500 h-2 rounded-full"
											[style.width.%]="getCostPercentageNew('infraestrutura')"
										></div>
									</div>
									<span class="font-bold text-green-600">{{
										formatCurrency(getCostValue('infraestrutura'))
									}}</span>
								</div>
							</div>
							<div class="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
								<span class="font-medium text-gray-900">Contingência</span>
								<div class="flex items-center gap-3">
									<div class="w-32 bg-gray-200 rounded-full h-2">
										<div
											class="bg-yellow-500 h-2 rounded-full"
											[style.width.%]="getCostPercentageNew('contingencia')"
										></div>
									</div>
									<span class="font-bold text-yellow-600">{{
										formatCurrency(getCostValue('contingencia'))
									}}</span>
								</div>
							</div>
						</div>
					</div>

					<!-- Estrutura da Equipe - USANDO DADOS DINÂMICOS -->
					<div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
						<h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
							<i class="pi pi-users text-indigo-500"></i>
							Estrutura da Equipe
						</h3>
						@if (getFormattedTeamStructure().length > 0) {
							<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
								@for (member of getFormattedTeamStructure(); track member.type; let i = $index) {
									<div
										class="p-4 rounded-lg border border-gray-200 hover:shadow-md transition-shadow"
									>
										<div class="flex items-start gap-3">
											<div class="flex-shrink-0">
												<div
													class="w-12 h-12 rounded-full flex items-center justify-center text-2xl"
												>
													{{ member.icon }}
												</div>
												<div class="text-center mt-1">
													<span class="text-lg font-bold text-gray-900">{{
														member.quantidade
													}}</span>
												</div>
											</div>
											<div class="flex-1 min-w-0">
												<div class="flex items-center gap-2 mb-2">
													<h4 class="font-semibold text-gray-900 truncate">
														{{ member.type }}
													</h4>
													<span
														class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
														[ngClass]="member.color"
													>
														{{ member.modalidade }}
													</span>
												</div>
												<div class="space-y-1">
													<p class="text-sm text-gray-600">
														<strong>Senioridade:</strong>
														{{ member.senioridade.join(', ') }}
													</p>
													<p class="text-sm text-gray-600">
														<strong>Duração:</strong> {{ member.duracao }}
													</p>
												</div>
												@if (member.descricao) {
													<p class="text-xs text-gray-500 mt-2 line-clamp-2">
														{{ member.descricao }}
													</p>
												}
											</div>
										</div>
									</div>
								}
							</div>

							<!-- Resumo da Equipe -->
							<div class="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
								<div class="flex items-center justify-between">
									<div class="flex items-center gap-2">
										<i class="pi pi-users text-indigo-500"></i>
										<span class="font-semibold text-gray-900">Total da Equipe:</span>
									</div>
									<span class="text-2xl font-bold text-indigo-600">
										{{ getTotalTeamSizeDynamic() }} desenvolvedores
									</span>
								</div>
							</div>
						} @else {
							<div class="text-center py-8 text-gray-500">
								<i class="pi pi-users text-4xl mb-2"></i>
								<p>Estrutura da equipe não disponível</p>
							</div>
						}
					</div>

					<!-- Stack Tecnológico Categorizado - USANDO DADOS DINÂMICOS -->
					<div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
						<h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
							<i class="pi pi-cog text-purple-500"></i>
							Stack Tecnológico
						</h3>
						@if (getEstimateData()?.stacks) {
							<div class="space-y-4">
								@for (stack of getEstimateData()!.stacks; track stack.stack) {
									<div class="border border-gray-200 rounded-lg p-4">
										<div class="flex items-center justify-between mb-3">
											<h4 class="font-semibold text-gray-900">{{ stack.stack }}</h4>
											<div class="flex items-center gap-2">
												<span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded"
													>{{ stack.quantidade_de_desenvolvedores }} devs</span
												>
												<span class="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded"
													>{{ stack.tecnologias?.length || 0 }} techs</span
												>
											</div>
										</div>
										<p class="text-sm text-gray-600 mb-3">
											{{
												stack.motivo ||
													'Stack tecnológica escolhida para otimizar o desenvolvimento'
											}}
										</p>
										<div class="flex flex-wrap gap-2">
											@for (tech of stack.tecnologias; track tech) {
												<span
													class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
													[ngClass]="getTechCategoryClassDynamic(tech)"
												>
													{{ tech }}
												</span>
											}
										</div>
									</div>
								}
							</div>
						}
					</div>
				</div>
			</p-tabPanel>

			<!-- 2. Cronograma -->
			<p-tabPanel header="Cronograma" leftIcon="pi pi-calendar">
				<div class="space-y-6">
					<!-- Overview do Cronograma - USANDO DADOS DINÂMICOS -->
					<div class="grid grid-cols-1 md:grid-cols-4 gap-4">
						<div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200">
							<div class="text-center">
								<p class="text-sm font-medium text-blue-600">Duração Total</p>
								<p class="text-3xl font-bold text-blue-700">
									{{ extractNumberFromString(getTotalDurationDynamic()) || 'N/A' }}
								</p>
								<p class="text-xs text-blue-500">meses</p>
							</div>
						</div>
						<div
							class="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-4 border border-green-200"
						>
							<div class="text-center">
								<p class="text-sm font-medium text-green-600">Total Sprints</p>
								<p class="text-3xl font-bold text-green-700">
									{{ getEstimateData()?.total_de_sprints || 'N/A' }}
								</p>
								<p class="text-xs text-green-500">sprints</p>
							</div>
						</div>
						<div
							class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-4 border border-purple-200"
						>
							<div class="text-center">
								<p class="text-sm font-medium text-purple-600">Sprints/Mês</p>
								<p class="text-3xl font-bold text-purple-700">
									{{ getEstimateData()?.sprint_por_mes || '2' }}
								</p>
								<p class="text-xs text-purple-500">por mês</p>
							</div>
						</div>
						<div
							class="bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl p-4 border border-orange-200"
						>
							<div class="text-center">
								<p class="text-sm font-medium text-orange-600">Marcos</p>
								<p class="text-3xl font-bold text-orange-700">{{ getTimelineFases().length || 0 }}</p>
								<p class="text-xs text-orange-500">entregas</p>
							</div>
						</div>
					</div>

					<!-- Timeline dos Marcos -->
					<div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
						<h3 class="text-lg font-semibold text-gray-900 mb-6 flex items-center gap-2">
							<i class="pi pi-clock text-blue-500"></i>
							Timeline de Entregas
						</h3>
						<div class="space-y-6">
							@for (fase of getTimelineFases(); track fase.nome; let i = $index) {
								<div class="relative">
									@if (i < getTimelineFases().length - 1) {
										<div class="absolute left-6 top-14 w-0.5 h-full bg-gray-200"></div>
									}

									<div class="flex items-start gap-4">
										<div
											class="flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center text-white font-bold text-sm z-10"
											[style.background]="getFaseColor(i)"
										>
											{{ i + 1 }}
										</div>
										<div class="flex-1">
											<div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
												<div class="flex items-center justify-between mb-3">
													<h4 class="font-semibold text-gray-900 text-lg">{{ fase.nome }}</h4>
													@if (fase.duracao_semanas) {
														<span
															class="text-sm bg-blue-100 text-blue-700 px-2 py-1 rounded"
														>
															{{ fase.duracao_semanas }} semanas
														</span>
													}
												</div>

												@if (fase.atividades && fase.atividades.length > 0) {
													<ul class="space-y-2">
														@for (atividade of fase.atividades; track atividade) {
															<li class="flex items-start gap-2 text-sm text-gray-700">
																<i class="pi pi-check-circle text-green-500 mt-0.5"></i>
																<span>{{ atividade }}</span>
															</li>
														}
													</ul>
												}

												<div class="mt-3">
													<div class="w-full bg-gray-200 rounded-full h-2">
														<div
															class="bg-blue-500 h-2 rounded-full transition-all duration-300"
															[style.width.%]="
																getFaseProgress(i, getTimelineFases().length)
															"
														></div>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							} @empty {
								<div class="text-center py-8 text-gray-500">
									<i class="pi pi-calendar text-4xl mb-2"></i>
									<p>Timeline não disponível</p>
								</div>
							}
						</div>
					</div>
				</div>
			</p-tabPanel>

			<!-- 3. Arquitetura -->
			<p-tabPanel header="Arquitetura" leftIcon="pi pi-sitemap">
				<div class="space-y-6">
					<!-- Arquitetura Overview -->
					<div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
						<h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
							<i class="pi pi-sitemap text-blue-500"></i>
							Visão Geral da Arquitetura
						</h3>
						@if (estimativa.estimativa_escopo?.arquitetura) {
							<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
								<div>
									<h4 class="font-semibold text-gray-900 mb-2">Tipo de Arquitetura</h4>
									<p class="text-2xl font-bold text-blue-600 mb-2">
										{{ estimativa.estimativa_escopo?.arquitetura?.tipo }}
									</p>
									<p class="text-gray-700">
										{{ estimativa.estimativa_escopo?.arquitetura?.justificativa }}
									</p>
								</div>
								<div>
									<h4 class="font-semibold text-gray-900 mb-3">Tecnologias Principais</h4>
									<div class="flex flex-wrap gap-2">
										@for (
											tech of estimativa.estimativa_escopo?.arquitetura?.tecnologias_principais;
											track tech
										) {
											<span
												class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
											>
												{{ tech }}
											</span>
										}
									</div>
								</div>
							</div>
						}
					</div>

					<!-- Diagrama da Arquitetura Geral -->
					<div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
						<div class="flex items-center justify-between mb-4">
							<h3 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
								<i class="pi pi-diagram-tree text-purple-500"></i>
								Diagrama da Arquitetura Geral (C4)
							</h3>
							<button
								pButton
								label="Copiar Código"
								icon="pi pi-copy"
								class="p-button-outlined p-button-sm"
								(click)="copyToClipboard(getArchitectureDiagramDynamic())"
							></button>
						</div>
						<div class="bg-gray-50 rounded-lg p-4">
							<app-mermaid-diagram [diagram]="getArchitectureDiagramDynamic()" [theme]="'default'">
							</app-mermaid-diagram>
						</div>
					</div>

					<!-- Diagrama de Dados (ERD) -->
					@if (hasDataDiagram()) {
						<div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
							<div class="flex items-center justify-between mb-4">
								<h3 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
									<i class="pi pi-database text-blue-500"></i>
									Diagrama de Entidades e Relacionamentos (ERD)
								</h3>
								<button
									pButton
									label="Copiar Código"
									icon="pi pi-copy"
									class="p-button-outlined p-button-sm"
									(click)="copyToClipboard(getDataDiagram())"
								></button>
							</div>
							<div class="bg-gray-50 rounded-lg p-4">
								<app-mermaid-diagram [diagram]="getDataDiagram()" [theme]="'default'">
								</app-mermaid-diagram>
							</div>
						</div>
					}

					<!-- Diagramas por Stack -->
					@if (getStacksWithDiagrams().length > 0) {
						<div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
							<h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
								<i class="pi pi-sitemap text-indigo-500"></i>
								Diagramas de Sequência por Stack
							</h3>
							<div class="space-y-6">
								@for (stack of getStacksWithDiagrams(); track stack.stack) {
									<div class="border border-gray-200 rounded-lg p-4">
										<div class="flex items-center justify-between mb-3">
											<h4 class="font-semibold text-gray-900 flex items-center gap-2">
												<i class="pi pi-cog" [ngClass]="getStackIconClass(stack.stack)"></i>
												{{ stack.stack }}
											</h4>
											<button
												pButton
												label="Copiar"
												icon="pi pi-copy"
												class="p-button-outlined p-button-sm"
												(click)="copyToClipboard(stack.diagrama)"
											></button>
										</div>
										<div class="bg-gray-50 rounded-lg p-3 mb-3">
											<app-mermaid-diagram [diagram]="stack.diagrama" [theme]="'default'">
											</app-mermaid-diagram>
										</div>
										<div class="text-sm text-gray-600">
											<p>
												<strong>Tecnologias:</strong>
												{{ stack.tecnologias?.join(', ') || 'N/A' }}
											</p>
											<p>
												<strong>Desenvolvedores:</strong>
												{{ stack.quantidade_de_desenvolvedores || 0 }}
											</p>
											@if (stack.motivo) {
												<p class="mt-2"><strong>Justificativa:</strong> {{ stack.motivo }}</p>
											}
										</div>
									</div>
								}
							</div>
						</div>
					}

					<!-- Stack Detalhado -->
					<div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
						<h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
							<i class="pi pi-cog text-green-500"></i>
							Stack Tecnológico Detalhado
						</h3>
						@if (estimativa.estimativa_escopo?.stacks_tecnologicas) {
							<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
								@for (
									stack of estimativa.estimativa_escopo?.stacks_tecnologicas;
									track stack.categoria
								) {
									<div
										class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
									>
										<div class="flex items-center justify-between mb-3">
											<h4 class="font-semibold text-gray-900">{{ stack.categoria }}</h4>
											<div class="flex items-center gap-2">
												<span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded"
													>{{ stack.complexidade }}/10</span
												>
												<span class="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded"
													>{{ stack.horas_estimadas }}h</span
												>
											</div>
										</div>
										<p class="text-sm text-gray-600 mb-3">{{ stack.justificativa }}</p>
										<div class="space-y-2">
											<div class="flex flex-wrap gap-1">
												@for (tech of stack.tecnologias; track tech) {
													<span
														class="inline-flex items-center px-2 py-1 rounded text-xs font-medium"
														[ngClass]="getTechCategoryClass(stack.categoria)"
													>
														{{ tech }}
													</span>
												}
											</div>
										</div>
									</div>
								}
							</div>
						}
					</div>
				</div>
			</p-tabPanel>

			<!-- 4. PRD & Especificações -->
			<p-tabPanel header="Especificações" leftIcon="pi pi-file-text">
				<div class="space-y-6">
					<!-- PRD do Projeto -->
					@if (getProjetoPRD()) {
						<div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
							<h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
								<i class="pi pi-file-text text-purple-500"></i>
								Product Requirements Document (PRD)
							</h3>
							<div class="prose max-w-none">
								<app-markdown-viewer [content]="getProjetoPRD()"></app-markdown-viewer>
							</div>
						</div>
					}

					<!-- Descrição do Projeto -->
					@if (getProjetoData()?.descricao) {
						<div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
							<h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
								<i class="pi pi-star text-purple-500"></i>
								Descrição do Projeto
							</h3>
							<div class="prose max-w-none">
								<p class="text-gray-700 leading-relaxed text-justify">
									{{ getProjetoData()?.descricao }}
								</p>
							</div>
						</div>
					}

					<!-- Estatísticas do Documento -->
					<div class="grid grid-cols-1 md:grid-cols-4 gap-4">
						<div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200">
							<div class="text-center">
								<p class="text-sm font-medium text-blue-600">Palavras</p>
								<p class="text-2xl font-bold text-blue-700">{{ getWordCount() }}</p>
							</div>
						</div>
						<div
							class="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-4 border border-green-200"
						>
							<div class="text-center">
								<p class="text-sm font-medium text-green-600">Funcionalidades</p>
								<p class="text-2xl font-bold text-green-700">{{ getFunctionalityCount() }}</p>
							</div>
						</div>
						<div
							class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-4 border border-purple-200"
						>
							<div class="text-center">
								<p class="text-sm font-medium text-purple-600">Integrações</p>
								<p class="text-2xl font-bold text-purple-700">{{ getIntegrationCount() }}</p>
							</div>
						</div>
						<div
							class="bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl p-4 border border-orange-200"
						>
							<div class="text-center">
								<p class="text-sm font-medium text-orange-600">Complexidade</p>
								<p class="text-2xl font-bold text-orange-700">{{ getOverallComplexity() }}/10</p>
							</div>
						</div>
					</div>

					<!-- Recomendações -->
					@if (estimativa.estimativa_escopo?.recomendacoes?.length) {
						<div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
							<h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
								<i class="pi pi-lightbulb text-yellow-500"></i>
								Recomendações Estratégicas
							</h3>
							<div class="space-y-3">
								@for (rec of estimativa.estimativa_escopo?.recomendacoes; track rec; let i = $index) {
									<div
										class="flex items-start gap-3 p-3 bg-yellow-50 rounded-lg border border-yellow-200"
									>
										<div
											class="w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center text-white text-xs font-bold flex-shrink-0"
										>
											{{ i + 1 }}
										</div>
										<span class="text-gray-700">{{ rec }}</span>
									</div>
								}
							</div>
						</div>
					}
				</div>
			</p-tabPanel>

			<!-- 5. Análise de Riscos -->
			<p-tabPanel header="Riscos" leftIcon="pi pi-exclamation-triangle">
				<div class="space-y-6">
					<!-- Risk Overview -->
					<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
						<div class="bg-gradient-to-br from-red-50 to-red-100 rounded-xl p-4 border border-red-200">
							<div class="text-center">
								<p class="text-sm font-medium text-red-600">Fator de Risco</p>
								<p class="text-3xl font-bold text-red-700">{{ calculateRiskFactor() }}/10</p>
								<p class="text-xs text-red-500">{{ getRiskLevelFromFactor(calculateRiskFactor()) }}</p>
							</div>
						</div>
						<div
							class="bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl p-4 border border-orange-200"
						>
							<div class="text-center">
								<p class="text-sm font-medium text-orange-600">Riscos Identificados</p>
								<p class="text-3xl font-bold text-orange-700">{{ getRiscosArray().length }}</p>
								<p class="text-xs text-orange-500">itens</p>
							</div>
						</div>
						<div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200">
							<div class="text-center">
								<p class="text-sm font-medium text-blue-600">Contingência</p>
								<p class="text-3xl font-bold text-blue-700">20%</p>
								<p class="text-xs text-blue-500">do orçamento</p>
							</div>
						</div>
					</div>

					<!-- Riscos e Mitigações -->
					<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
						<!-- Riscos Identificados -->
						@if (getRiscosArray().length > 0) {
							<div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
								<h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
									<i class="pi pi-exclamation-triangle text-red-500"></i>
									Riscos Identificados
								</h3>
								<div class="space-y-3">
									@for (risco of getRiscosArray(); track risco; let i = $index) {
										<div
											class="flex items-start gap-3 p-4 bg-red-50 border border-red-200 rounded-lg"
										>
											<div
												class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center text-white text-sm font-bold flex-shrink-0"
											>
												{{ i + 1 }}
											</div>
											<div class="flex-1">
												<p class="text-gray-700">{{ risco }}</p>
												<div class="mt-2">
													<span
														class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
														[ngClass]="getRiskSeverityClassDynamic(risco)"
													>
														{{ getRiskLevelTextDynamic(risco) }}
													</span>
												</div>
											</div>
										</div>
									}
								</div>
							</div>
						}

						<!-- Plano de Mitigação -->
						@if (getMitigacoesArray().length > 0) {
							<div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
								<h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
									<i class="pi pi-shield text-green-500"></i>
									Plano de Mitigação
								</h3>
								<div class="space-y-3">
									@for (mitigacao of getMitigacoesArray(); track mitigacao; let i = $index) {
										<div
											class="flex items-start gap-3 p-4 bg-green-50 border border-green-200 rounded-lg"
										>
											<div
												class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-bold flex-shrink-0"
											>
												<i class="pi pi-check"></i>
											</div>
											<div class="flex-1">
												<p class="text-gray-700">{{ mitigacao }}</p>
											</div>
										</div>
									}
								</div>
							</div>
						}
					</div>

					<!-- Investimento e Contingência -->
					<div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
						<h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
							<i class="pi pi-dollar text-yellow-500"></i>
							Análise Financeira de Riscos
						</h3>
						<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
							<div class="space-y-4">
								<div class="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
									<span class="font-medium text-gray-900">Desenvolvimento Base</span>
									<span class="font-bold text-blue-600"
										>R$
										{{
											estimativa.estimativa_escopo?.investimento_total?.desenvolvimento
												| number: '1.0-0'
										}}</span
									>
								</div>
								<div class="flex justify-between items-center p-3 bg-green-50 rounded-lg">
									<span class="font-medium text-gray-900">Infraestrutura</span>
									<span class="font-bold text-green-600"
										>R$
										{{
											estimativa.estimativa_escopo?.investimento_total?.infraestrutura_setup
												| number: '1.0-0'
										}}</span
									>
								</div>
								<div class="flex justify-between items-center p-3 bg-yellow-50 rounded-lg">
									<span class="font-medium text-gray-900">Contingência (20%)</span>
									<span class="font-bold text-yellow-600"
										>R$
										{{
											estimativa.estimativa_escopo?.investimento_total?.contingencia_20pct
												| number: '1.0-0'
										}}</span
									>
								</div>
							</div>
							<div class="bg-gray-50 rounded-lg p-4">
								<h4 class="font-semibold text-gray-900 mb-3">Distribuição do Orçamento</h4>
								<div class="space-y-2">
									<div class="flex items-center justify-between text-sm">
										<span>Desenvolvimento</span>
										<span>{{ getCostPercentage('desenvolvimento') }}%</span>
									</div>
									<div class="w-full bg-gray-200 rounded-full h-2">
										<div
											class="bg-blue-500 h-2 rounded-full"
											[style.width.%]="getCostPercentage('desenvolvimento')"
										></div>
									</div>
									<div class="flex items-center justify-between text-sm">
										<span>Contingência</span>
										<span>{{ getCostPercentage('contingencia') }}%</span>
									</div>
									<div class="w-full bg-gray-200 rounded-full h-2">
										<div
											class="bg-yellow-500 h-2 rounded-full"
											[style.width.%]="getCostPercentage('contingencia')"
										></div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</p-tabPanel>
		</p-tabView>

		<!-- Footer Actions -->
		<div class="flex flex-col sm:flex-row gap-3 justify-end mt-6 pt-6 border-t border-gray-200">
			<button
				pButton
				label="Fechar"
				icon="pi pi-times"
				class="p-button-outlined p-button-secondary"
				(click)="fecharEstimativa()"
			></button>
			<button
				pButton
				label="Ouvir Podcast"
				icon="pi pi-microphone"
				class="p-button-outlined p-button-warning"
				(click)="playProjectPodcast()"
				[disabled]="podcastGenerating()"
			></button>
			<button
				pButton
				[label]="pdfGenerating() ? 'Gerando PDF...' : 'Exportar PDF'"
				[icon]="pdfGenerating() ? 'pi pi-spin pi-spinner' : 'pi pi-file-pdf'"
				class="p-button-outlined p-button-primary"
				(click)="exportarEstimativaPDF()"
				[disabled]="pdfGenerating()"
			></button>
			<button
				pButton
				label="Copiar Resumo"
				icon="pi pi-copy"
				class="p-button-outlined p-button-success"
				(click)="copyProjectSummary()"
			></button>
		</div>
	}
</p-drawer>

<!-- Modal do Podcast -->
<app-podcast-modal
	[data]="{
		visible: podcastModalVisible(),
		generating: podcastGenerating(),
		url: currentPodcastUrl(),
		title: currentPodcastTitle(),
		projectId: projectSelected?._id,
	}"
	(close)="closePodcastModal()"
	(generate)="playProjectPodcast()"
></app-podcast-modal>

<!-- Modal do Relatório PDF -->
<app-pdf-modal
	[data]="{
		visible: pdfModalVisible(),
		generating: pdfGenerating(),
		url: currentPdfUrl(),
		title: currentPdfTitle(),
		projectId: projectSelected?._id,
	}"
	(close)="closePdfModal()"
	(generate)="exportarEstimativaPDF()"
	(download)="onPdfDownload()"
></app-pdf-modal>
