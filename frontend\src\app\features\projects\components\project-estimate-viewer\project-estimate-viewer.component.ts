import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';

import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { ChipModule } from 'primeng/chip';
import { DialogModule } from 'primeng/dialog';
import { DividerModule } from 'primeng/divider';
import { ProgressBarModule } from 'primeng/progressbar';
import { TabViewModule } from 'primeng/tabview';
import { TagModule } from 'primeng/tag';
import { TimelineModule } from 'primeng/timeline';
import { TooltipModule } from 'primeng/tooltip';

import { MermaidDiagramUnifiedComponent } from '../../../../shared/components/mermaid-diagram';
import { IEstimativaProcessada } from '../../interfaces/backend-project.interface';

@Component({
	selector: 'app-project-estimate-viewer',
	standalone: true,
	imports: [
		CommonModule,
		CardModule,
		ChipModule,
		DialogModule,
		DividerModule,
		TabViewModule,
		TagModule,
		TimelineModule,
		ProgressBarModule,
		ButtonModule,
		TooltipModule,
		MermaidDiagramUnifiedComponent,
	],
	templateUrl: './project-estimate-viewer.component.html',
	styleUrls: ['./project-estimate-viewer.component.scss'],
})
export class ProjectEstimateViewerComponent implements OnInit {
	@Input() public estimativa!: IEstimativaProcessada;
	@Input() public projectTitle: string = '';
	@Input() public visible: boolean = false;

	// Expor Math para o template
	public Math = Math;

	public ngOnInit(): void {
		console.log('🎯 Estimativa carregada:', this.estimativa);
	}

	/**
	 * Formatar valor monetário
	 */
	public formatCurrency(value: string | number): string {
		if (!value) return 'N/A';

		// Se é número, formatar diretamente
		if (typeof value === 'number') {
			return new Intl.NumberFormat('pt-BR', {
				style: 'currency',
				currency: 'BRL',
			}).format(value);
		}

		// Se já tem formatação R$, retorna como está
		if (value.includes('R$')) return value;

		// Tenta converter string para número e formatar
		const numValue = parseFloat(value.replace(/[^\d,.-]/g, '').replace(',', '.'));
		if (isNaN(numValue)) return value;

		return new Intl.NumberFormat('pt-BR', {
			style: 'currency',
			currency: 'BRL',
		}).format(numValue);
	}

	/**
	 * Obter cor para chip de tecnologia
	 */
	public getTechChipSeverity(tech: string): string {
		const techLower = tech.toLowerCase();

		if (techLower.includes('react') || techLower.includes('angular') || techLower.includes('vue')) {
			return 'success';
		}
		if (techLower.includes('node') || techLower.includes('python') || techLower.includes('java')) {
			return 'info';
		}
		if (techLower.includes('aws') || techLower.includes('azure') || techLower.includes('docker')) {
			return 'warning';
		}
		if (techLower.includes('mongodb') || techLower.includes('postgresql') || techLower.includes('mysql')) {
			return 'help';
		}

		return 'secondary';
	}

	/**
	 * Obter ícone para papel da equipe
	 */
	public getRoleIcon(papel: string): string {
		const papelLower = papel.toLowerCase();

		if (papelLower.includes('desenvolvedor') || papelLower.includes('developer')) {
			return 'pi-code';
		}
		if (papelLower.includes('designer')) {
			return 'pi-palette';
		}
		if (papelLower.includes('arquiteto') || papelLower.includes('architect')) {
			return 'pi-sitemap';
		}
		if (papelLower.includes('analista') || papelLower.includes('analyst')) {
			return 'pi-chart-line';
		}
		if (papelLower.includes('tech') && papelLower.includes('lead')) {
			return 'pi-star';
		}
		if (papelLower.includes('devops')) {
			return 'pi-cog';
		}
		if (papelLower.includes('qa') || papelLower.includes('test')) {
			return 'pi-check-circle';
		}

		return 'pi-user';
	}

	/**
	 * Calcular largura da barra de progresso baseada no número do sprint
	 */
	public getSprintProgress(sprintNumber: number): number {
		if (!this.estimativa?.metricas_principais?.total_sprints) return 0;

		return (sprintNumber / this.estimativa.metricas_principais.total_sprints) * 100;
	}

	/**
	 * Filtrar tecnologias por categoria
	 */
	public getFrontendTechs(): Array<string> {
		if (!this.estimativa?.tecnologias) return [];
		const frontendTechs = ['React', 'Angular', 'Vue', 'Next.js', 'Material-UI', 'TypeScript', 'JavaScript'];
		return this.estimativa.tecnologias.filter(tech => frontendTechs.includes(tech));
	}

	public getBackendTechs(): Array<string> {
		if (!this.estimativa?.tecnologias) return [];
		const backendTechs = ['Node.js', 'Python', 'Django', 'Flask', 'FastAPI', 'Express', 'Java', 'Spring'];
		return this.estimativa.tecnologias.filter(tech => backendTechs.includes(tech));
	}

	public getDatabaseTechs(): Array<string> {
		if (!this.estimativa?.tecnologias) return [];
		const dbTechs = ['MongoDB', 'PostgreSQL', 'MySQL', 'Redis'];
		return this.estimativa.tecnologias.filter(tech => dbTechs.includes(tech));
	}

	public getDevOpsTechs(): Array<string> {
		if (!this.estimativa?.tecnologias) return [];
		const devOpsTechs = ['AWS', 'Azure', 'GCP', 'Docker', 'Kubernetes', 'Terraform', 'Jenkins', 'GitLab', 'GitHub'];
		return this.estimativa.tecnologias.filter(tech => devOpsTechs.includes(tech));
	}

	public getOtherTechs(): Array<string> {
		if (!this.estimativa?.tecnologias) return [];
		const categorizedTechs = [
			'React',
			'Angular',
			'Vue',
			'Next.js',
			'Material-UI',
			'TypeScript',
			'JavaScript',
			'Node.js',
			'Python',
			'Django',
			'Flask',
			'FastAPI',
			'Express',
			'Java',
			'Spring',
			'MongoDB',
			'PostgreSQL',
			'MySQL',
			'Redis',
			'AWS',
			'Azure',
			'GCP',
			'Docker',
			'Kubernetes',
			'Terraform',
			'Jenkins',
			'GitLab',
			'GitHub',
		];
		return this.estimativa.tecnologias.filter(tech => !categorizedTechs.includes(tech));
	}

	/**
	 * Copiar texto para clipboard
	 */
	public copyToClipboard(text: string): void {
		navigator.clipboard
			.writeText(text)
			.then(() => {
				console.log('✅ Código copiado para clipboard');
			})
			.catch(err => {
				console.error('❌ Erro ao copiar:', err);
			});
	}

	/**
	 * Obter dados das stacks do projeto
	 */
	public getStacksData(): any[] {
		// Tentar acessar dados das stacks do campo projeto estruturado
		if (this.estimativa && (this.estimativa as any).projeto?.stacks) {
			return (this.estimativa as any).projeto.stacks;
		}

		// Fallback: tentar acessar de outros campos possíveis
		if (this.estimativa && (this.estimativa as any).stacks) {
			return (this.estimativa as any).stacks;
		}

		// Se não encontrar dados de stacks, retornar array vazio
		return [];
	}

	/**
	 * Obter duração estimada da stack
	 */
	public getStackDuration(): string {
		if (this.estimativa?.metricas_principais?.duracao_estimada) {
			return this.estimativa.metricas_principais.duracao_estimada;
		}
		return '12 meses';
	}

	/**
	 * Obter modalidade de trabalho
	 */
	public getWorkMode(): string {
		// Pode ser configurável no futuro, por enquanto retorna um valor padrão
		return 'Híbrido';
	}

	/**
	 * Obter complexidade da stack
	 */
	public getStackComplexity(): number {
		// Pode ser calculado baseado nas tecnologias ou configurado
		return 7;
	}

	/**
	 * Estimar custo mensal baseado na senioridade e stack
	 */
	public estimateMonthlyCost(senioridade: string, stack: string): number {
		const baseCosts: { [key: string]: number } = {
			Júnior: 8000,
			Junior: 8000,
			Pleno: 12000,
			Sênior: 18000,
			Senior: 18000,
			Especialista: 25000,
		};

		// Multiplicador baseado na complexidade da stack
		let multiplier = 1.0;
		const stackLower = stack.toLowerCase();

		if (stackLower.includes('ia') || stackLower.includes('machine learning') || stackLower.includes('ai')) {
			multiplier = 1.3;
		} else if (stackLower.includes('devops') || stackLower.includes('cloud')) {
			multiplier = 1.2;
		} else if (stackLower.includes('mobile') || stackLower.includes('react native')) {
			multiplier = 1.1;
		}

		const baseCost = baseCosts[senioridade] || baseCosts['Pleno'];
		return Math.round(baseCost * multiplier);
	}

	/**
	 * Fechar o modal de estimativa
	 */
	public fecharEstimativa(): void {
		this.visible = false;
	}
}
