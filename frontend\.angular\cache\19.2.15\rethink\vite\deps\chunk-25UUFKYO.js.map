{"version": 3, "sources": ["../../../../../../node_modules/mermaid/dist/chunks/mermaid.core/chunk-7B677QYD.mjs"], "sourcesContent": ["import { __name, getConfig2 as getConfig } from \"./chunk-YTJNT7DU.mjs\";\n\n// src/rendering-util/selectSvgElement.ts\nimport { select } from \"d3\";\nvar selectSvgElement = /* @__PURE__ */__name(id => {\n  const {\n    securityLevel\n  } = getConfig();\n  let root = select(\"body\");\n  if (securityLevel === \"sandbox\") {\n    const sandboxElement = select(`#i${id}`);\n    const doc = sandboxElement.node()?.contentDocument ?? document;\n    root = select(doc.body);\n  }\n  const svg = root.select(`#${id}`);\n  return svg;\n}, \"selectSvgElement\");\nexport { selectSvgElement };"], "mappings": ";;;;;;;AAIA,IAAI,mBAAkC,OAAO,QAAM;AACjD,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,WAAU;AACd,MAAI,OAAO,eAAO,MAAM;AACxB,MAAI,kBAAkB,WAAW;AAC/B,UAAM,iBAAiB,eAAO,KAAK,EAAE,EAAE;AACvC,UAAM,MAAM,eAAe,KAAK,GAAG,mBAAmB;AACtD,WAAO,eAAO,IAAI,IAAI;AAAA,EACxB;AACA,QAAM,MAAM,KAAK,OAAO,IAAI,EAAE,EAAE;AAChC,SAAO;AACT,GAAG,kBAAkB;", "names": []}