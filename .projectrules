# Project Rules - ScopeAI

## Arquitetura

### Microserviços (Decisão 2025-01-29)
- **Padrão**: Clean Architecture com layers horizontais
- **Estrutura**: Domain → Application → Infrastructure → API
- **Dependency Rule**: Dependências sempre apontam para dentro
- **Comunicação**: Híbrida (REST para queries, eventos para comandos)
- **Database**: Estratégia transitória durante migração
- **API Versioning**: URL path (/api/v1, /api/v2)

### Migração
- **Estratégia**: Strangler Fig Pattern
- **Compatibilidade**: 100% backward compatibility obrigatório
- **Database**: Shared temporário com collections separadas
- **Rollout**: Feature flags para controle gradual

## Padrões de Código

### Backend (Python)
- **Async First**: Todo I/O deve ser assíncrono
- **Type Hints**: Obrigatório em todas as funções
- **Pydantic**: Para validação de dados
- **Error Handling**: Try/except com logging apropriado

### Estrutura de Arquivos
- **Limite**: Nenhum arquivo deve ter mais de 500 linhas
- **SRP**: Single Responsibility Principle rigorosamente aplicado
- **DRY**: Don't Repeat Yourself - extrair código comum

### Testes
- **Coverage**: Mínimo 80% para novos códigos
- **Unit Tests**: Para toda lógica de negócio
- **Integration Tests**: Para APIs e integrações externas

## WebSocket Events

### Convenções de Nomenclatura
- **Lifecycle**: `{entity}_{action}` (ex: client_created)
- **Status**: `{entity}_status_update`
- **Errors**: `{process}_error`
- **Completion**: `{process}_completed`

### Payload Padrão
```json
{
  "type": "event_type",
  "entityId": "id",
  "timestamp": "ISO8601",
  "data": {},
  "action": "optional_frontend_action"
}
```

## Background Tasks

### Padrões
- **Wrapper Híbrido**: Para compatibilidade com BackgroundTasks
```python
def sync_wrapper(param):
    asyncio.run(async_function(param))
```
- **Timeout**: Máximo 5 minutos por task
- **Retry**: 3 tentativas com backoff exponencial

## MongoDB

### Collections
- **Naming**: snake_case plural (clients, projects)
- **Indexes**: Criar para queries frequentes
- **GridFS**: Para arquivos > 16MB

### Schemas
- **Validation**: Usar JSON Schema do MongoDB
- **Required Fields**: Definir explicitamente
- **Timestamps**: created_at, updated_at obrigatórios

## Git

### Branches
- **Feature**: feat-{description}
- **Fix**: fix-{description}
- **Refactor**: refactor-{description}

### Commits
- **Format**: type(scope): description
- **Types**: feat, fix, refactor, docs, test, chore
- **Scope**: module ou domain afetado

## Documentação

### Código
- **Docstrings**: Para todas as funções públicas
- **Type Hints**: Completos e precisos
- **Comments**: Apenas para lógica complexa

### APIs
- **OpenAPI**: Obrigatório para todos os endpoints
- **Examples**: Incluir request/response examples
- **Errors**: Documentar todos os códigos de erro

## Performance

### Targets
- **API Response**: < 200ms (p95)
- **Background Tasks**: < 5 minutos
- **WebSocket Latency**: < 100ms

### Otimizações
- **Cache**: Redis para dados frequentes
- **Pagination**: Obrigatório para listas
- **Lazy Loading**: Para dados pesados

## Segurança

### APIs
- **Authentication**: JWT tokens
- **Authorization**: RBAC
- **Rate Limiting**: 100 req/min por IP

### Dados
- **PII**: Nunca logar dados sensíveis
- **Encryption**: TLS obrigatório
- **Passwords**: Bcrypt com salt

## Memory Bank

### Atualização
- **activeContext.md**: Durante cada sessão
- **progress.md**: Ao completar features
- **logs/**: Resumo ao final de cada sessão

### Arquivamento
- **Tarefas Completas**: Mover para archive/
- **Naming**: YYYY_MM_DD__HH-MM__description
- **Index**: Sempre atualizar archive/index.md

## 📋 Regras de Desenvolvimento

### 🔧 Microserviços e Modularização

1. **Reutilização de Configurações**
   - Microserviços devem reutilizar a infraestrutura existente do backend
   - NÃO criar arquivos de configuração duplicados (pyproject.toml, .env, Dockerfile)
   - Usar imports relativos para acessar configurações do backend principal
   - Manter microserviços como parte do backend, não projetos separados
   - Benefícios: evita drift de configuração, simplifica manutenção

2. **Estrutura de Microserviços**
   ```
   backend/
   ├── microservices/
   │   └── [service-name]/
   │       ├── src/           # Código do microserviço
   │       ├── tests/         # Testes específicos
   │       ├── docs/          # Documentação específica
   │       └── README.md      # Documentação do serviço
   ├── pyproject.toml         # Dependências compartilhadas
   ├── .env                   # Variáveis compartilhadas
   └── Dockerfile             # Container compartilhado
   ```

### 🌐 Team Agno e AI Agents

// ... existing code ...

---

**Última Atualização**: 2025-01-29
**Versão**: 1.0.0 