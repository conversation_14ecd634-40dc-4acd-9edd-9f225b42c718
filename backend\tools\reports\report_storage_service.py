"""
ReportStorageService - Responsável pela Persistência de Relatórios

Módulo especializado em salvar, recuperar e gerenciar relatórios no MongoDB,
seguindo o Single Responsibility Principle.
"""

import logging
import hashlib
import json
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, List
from bson import ObjectId

from clients.db import clients_collection

logger = logging.getLogger(__name__)


class ReportStorageService:
    """
    Responsável por todas as operações de persistência de relatórios.

    Única responsabilidade: Gerenciar armazenamento e recuperação de relatórios no MongoDB.
    """

    def __init__(self):
        """Inicializa o serviço de armazenamento."""
        # Usar a mesma conexão do clients_collection
        self.reports_collection = clients_collection.database["structured_reports"]
        # Criar índice se não existir
        try:
            self.reports_collection.create_index(
                [("client_id", 1), ("report_type", 1), ("version", -1)])
        except Exception:
            pass  # Índice já existe

    async def save_structured_report(
        self,
        client_id: str,
        report_type: str,
        report_content: Dict[str, Any],
        version_info: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Salva um relatório estruturado no MongoDB.

        Args:
            client_id: ID do cliente
            report_type: Tipo do relatório
            report_content: Conteúdo estruturado do relatório
            version_info: Informações de versionamento

        Returns:
            Dados do relatório salvo incluindo ID gerado
        """
        try:
            logger.info(
                f"Salvando relatório {report_type} para cliente {client_id}")

            # Preparar documento para inserção
            report_document = {
                "client_id": ObjectId(client_id),
                "report_type": report_type,
                "content": report_content,
                "version": version_info["version"],
                "version_info": version_info,
                "checksum": self._calculate_checksum(report_content),
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc),
                "status": "completed",
                "file_size": len(json.dumps(report_content, default=str)),
                "metadata": {
                    "data_quality_score": report_content.get("data_quality_score", 0),
                    "processing_time": report_content.get("processing_time", 0),
                    "template_used": report_content.get("template_used", ""),
                    "insights_count": len(report_content.get("key_insights", [])),
                    "recommendations_count": len(report_content.get("recommendations", []))
                }
            }

            # Inserir no MongoDB
            result = self.reports_collection.insert_one(report_document)

            # Recuperar documento inserido
            saved_report = self.reports_collection.find_one(
                {"_id": result.inserted_id})

            # Converter ObjectIds para strings para JSON serialization
            saved_report["_id"] = str(saved_report["_id"])
            saved_report["client_id"] = str(saved_report["client_id"])

            logger.info(
                f"Relatório salvo com sucesso: ID {saved_report['_id']}")
            return saved_report

        except Exception as e:
            logger.error(f"Erro ao salvar relatório {report_type}: {str(e)}")
            raise Exception(f"Falha no armazenamento: {str(e)}")

    async def get_report_by_id(self, report_id: str) -> Optional[Dict[str, Any]]:
        """
        Recupera um relatório específico pelo ID.

        Args:
            report_id: ID do relatório

        Returns:
            Dados do relatório ou None se não encontrado
        """
        try:
            report = self.reports_collection.find_one(
                {"_id": ObjectId(report_id)})
            if report:
                report["_id"] = str(report["_id"])
                report["client_id"] = str(report["client_id"])
            return report

        except Exception as e:
            logger.error(f"Erro ao recuperar relatório {report_id}: {str(e)}")
            return None

    async def get_client_reports(
        self,
        client_id: str,
        report_type: Optional[str] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        Recupera relatórios de um cliente específico.

        Args:
            client_id: ID do cliente
            report_type: Tipo específico de relatório (opcional)
            limit: Número máximo de relatórios

        Returns:
            Lista de relatórios do cliente
        """
        try:
            filter_query = {"client_id": ObjectId(client_id)}
            if report_type:
                filter_query["report_type"] = report_type

            cursor = self.reports_collection.find(
                filter_query).sort("created_at", -1).limit(limit)
            reports = []

            for report in cursor:
                report["_id"] = str(report["_id"])
                report["client_id"] = str(report["client_id"])
                reports.append(report)

            return reports

        except Exception as e:
            logger.error(
                f"Erro ao recuperar relatórios do cliente {client_id}: {str(e)}")
            return []

    async def get_latest_report(self, client_id: str, report_type: str) -> Optional[Dict[str, Any]]:
        """
        Recupera a versão mais recente de um tipo de relatório para um cliente.

        Args:
            client_id: ID do cliente
            report_type: Tipo do relatório

        Returns:
            Relatório mais recente ou None se não encontrado
        """
        try:
            filter_query = {
                "client_id": ObjectId(client_id),
                "report_type": report_type
            }

            report = self.reports_collection.find_one(
                filter_query,
                sort=[("version", -1)]
            )

            if report:
                report["_id"] = str(report["_id"])
                report["client_id"] = str(report["client_id"])

            return report

        except Exception as e:
            logger.error(
                f"Erro ao recuperar último relatório {report_type} do cliente {client_id}: {str(e)}")
            return None

    async def update_report_status(self, report_id: str, status: str, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        Atualiza o status de um relatório.

        Args:
            report_id: ID do relatório
            status: Novo status
            metadata: Metadados adicionais (opcional)

        Returns:
            True se atualização foi bem-sucedida
        """
        try:
            update_data = {
                "status": status,
                "updated_at": datetime.now(timezone.utc)
            }

            if metadata:
                update_data["metadata"] = {
                    **update_data.get("metadata", {}), **metadata}

            result = self.reports_collection.update_one(
                {"_id": ObjectId(report_id)},
                {"$set": update_data}
            )

            return result.modified_count > 0

        except Exception as e:
            logger.error(
                f"Erro ao atualizar status do relatório {report_id}: {str(e)}")
            return False

    async def delete_report(self, report_id: str) -> bool:
        """
        Remove um relatório do banco de dados.

        Args:
            report_id: ID do relatório

        Returns:
            True se remoção foi bem-sucedida
        """
        try:
            result = self.reports_collection.delete_one(
                {"_id": ObjectId(report_id)})
            return result.deleted_count > 0

        except Exception as e:
            logger.error(f"Erro ao deletar relatório {report_id}: {str(e)}")
            return False

    async def get_report_statistics(self, client_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Recupera estatísticas dos relatórios.

        Args:
            client_id: ID do cliente (opcional, para estatísticas específicas)

        Returns:
            Estatísticas dos relatórios
        """
        try:
            pipeline = []

            # Filtrar por cliente se especificado
            if client_id:
                pipeline.append({"$match": {"client_id": ObjectId(client_id)}})

            # Agrupar por tipo de relatório
            pipeline.extend([
                {
                    "$group": {
                        "_id": "$report_type",
                        "count": {"$sum": 1},
                        "avg_quality_score": {"$avg": "$metadata.data_quality_score"},
                        "avg_processing_time": {"$avg": "$metadata.processing_time"},
                        "latest_created": {"$max": "$created_at"}
                    }
                },
                {
                    "$sort": {"count": -1}
                }
            ])

            stats = list(self.reports_collection.aggregate(pipeline))

            # Calcular estatísticas gerais
            total_reports = sum(stat["count"] for stat in stats)

            return {
                "total_reports": total_reports,
                "report_types": stats,
                "generated_at": datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            logger.error(f"Erro ao calcular estatísticas: {str(e)}")
            return {"total_reports": 0, "report_types": [], "error": str(e)}

    def _calculate_checksum(self, content: Dict[str, Any]) -> str:
        """
        Calcula checksum do conteúdo para detecção de mudanças.

        Args:
            content: Conteúdo do relatório

        Returns:
            Hash MD5 do conteúdo
        """
        try:
            # Serializar conteúdo de forma determinística
            content_str = json.dumps(content, sort_keys=True, default=str)

            # Calcular hash MD5
            return hashlib.md5(content_str.encode()).hexdigest()

        except Exception as e:
            logger.error(f"Erro no cálculo de checksum: {str(e)}")
            return ""

    async def cleanup_old_reports(self, days_old: int = 90) -> int:
        """
        Remove relatórios antigos baseado na data.

        Args:
            days_old: Idade em dias para considerar relatório antigo

        Returns:
            Número de relatórios removidos
        """
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_old)

            result = self.reports_collection.delete_many({
                "created_at": {"$lt": cutoff_date},
                # Não remover relatórios arquivados
                "status": {"$ne": "archived"}
            })

            logger.info(f"Removidos {result.deleted_count} relatórios antigos")
            return result.deleted_count

        except Exception as e:
            logger.error(f"Erro na limpeza de relatórios antigos: {str(e)}")
            return 0
