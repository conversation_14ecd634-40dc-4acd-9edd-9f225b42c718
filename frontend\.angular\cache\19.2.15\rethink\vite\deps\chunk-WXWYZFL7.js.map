{"version": 3, "sources": ["../../../../../../node_modules/@braintree/sanitize-url/dist/constants.js", "../../../../../../node_modules/@braintree/sanitize-url/dist/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.BLANK_URL = exports.relativeFirstCharacters = exports.whitespaceEscapeCharsRegex = exports.urlSchemeRegex = exports.ctrlCharactersRegex = exports.htmlCtrlEntityRegex = exports.htmlEntitiesRegex = exports.invalidProtocolRegex = void 0;\nexports.invalidProtocolRegex = /^([^\\w]*)(javascript|data|vbscript)/im;\nexports.htmlEntitiesRegex = /&#(\\w+)(^\\w|;)?/g;\nexports.htmlCtrlEntityRegex = /&(newline|tab);/gi;\nexports.ctrlCharactersRegex = /[\\u0000-\\u001F\\u007F-\\u009F\\u2000-\\u200D\\uFEFF]/gim;\nexports.urlSchemeRegex = /^.+(:|&colon;)/gim;\nexports.whitespaceEscapeCharsRegex = /(\\\\|%5[cC])((%(6[eE]|72|74))|[nrt])/g;\nexports.relativeFirstCharacters = [\".\", \"/\"];\nexports.BLANK_URL = \"about:blank\";", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.sanitizeUrl = void 0;\nvar constants_1 = require(\"./constants\");\nfunction isRelativeUrlWithoutProtocol(url) {\n  return constants_1.relativeFirstCharacters.indexOf(url[0]) > -1;\n}\nfunction decodeHtmlCharacters(str) {\n  var removedNullByte = str.replace(constants_1.ctrlCharactersRegex, \"\");\n  return removedNullByte.replace(constants_1.htmlEntitiesRegex, function (match, dec) {\n    return String.fromCharCode(dec);\n  });\n}\nfunction isValidUrl(url) {\n  return URL.canParse(url);\n}\nfunction decodeURI(uri) {\n  try {\n    return decodeURIComponent(uri);\n  } catch (e) {\n    // Ignoring error\n    // It is possible that the URI contains a `%` not associated\n    // with URI/URL-encoding.\n    return uri;\n  }\n}\nfunction sanitizeUrl(url) {\n  if (!url) {\n    return constants_1.BLANK_URL;\n  }\n  var charsToDecode;\n  var decodedUrl = decodeURI(url.trim());\n  do {\n    decodedUrl = decodeHtmlCharacters(decodedUrl).replace(constants_1.htmlCtrlEntityRegex, \"\").replace(constants_1.ctrlCharactersRegex, \"\").replace(constants_1.whitespaceEscapeCharsRegex, \"\").trim();\n    decodedUrl = decodeURI(decodedUrl);\n    charsToDecode = decodedUrl.match(constants_1.ctrlCharactersRegex) || decodedUrl.match(constants_1.htmlEntitiesRegex) || decodedUrl.match(constants_1.htmlCtrlEntityRegex) || decodedUrl.match(constants_1.whitespaceEscapeCharsRegex);\n  } while (charsToDecode && charsToDecode.length > 0);\n  var sanitizedUrl = decodedUrl;\n  if (!sanitizedUrl) {\n    return constants_1.BLANK_URL;\n  }\n  if (isRelativeUrlWithoutProtocol(sanitizedUrl)) {\n    return sanitizedUrl;\n  }\n  // Remove any leading whitespace before checking the URL scheme\n  var trimmedUrl = sanitizedUrl.trimStart();\n  var urlSchemeParseResults = trimmedUrl.match(constants_1.urlSchemeRegex);\n  if (!urlSchemeParseResults) {\n    return sanitizedUrl;\n  }\n  var urlScheme = urlSchemeParseResults[0].toLowerCase().trim();\n  if (constants_1.invalidProtocolRegex.test(urlScheme)) {\n    return constants_1.BLANK_URL;\n  }\n  var backSanitized = trimmedUrl.replace(/\\\\/g, \"/\");\n  // Handle special cases for mailto: and custom deep-link protocols\n  if (urlScheme === \"mailto:\" || urlScheme.includes(\"://\")) {\n    return backSanitized;\n  }\n  // For http and https URLs, perform additional validation\n  if (urlScheme === \"http:\" || urlScheme === \"https:\") {\n    if (!isValidUrl(backSanitized)) {\n      return constants_1.BLANK_URL;\n    }\n    var url_1 = new URL(backSanitized);\n    url_1.protocol = url_1.protocol.toLowerCase();\n    url_1.hostname = url_1.hostname.toLowerCase();\n    return url_1.toString();\n  }\n  return backSanitized;\n}\nexports.sanitizeUrl = sanitizeUrl;"], "mappings": ";;;;;AAAA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,YAAY,QAAQ,0BAA0B,QAAQ,6BAA6B,QAAQ,iBAAiB,QAAQ,sBAAsB,QAAQ,sBAAsB,QAAQ,oBAAoB,QAAQ,uBAAuB;AAC3O,YAAQ,uBAAuB;AAC/B,YAAQ,oBAAoB;AAC5B,YAAQ,sBAAsB;AAC9B,YAAQ,sBAAsB;AAC9B,YAAQ,iBAAiB;AACzB,YAAQ,6BAA6B;AACrC,YAAQ,0BAA0B,CAAC,KAAK,GAAG;AAC3C,YAAQ,YAAY;AAAA;AAAA;;;ACbpB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,cAAc;AACtB,QAAI,cAAc;AAClB,aAAS,6BAA6B,KAAK;AACzC,aAAO,YAAY,wBAAwB,QAAQ,IAAI,CAAC,CAAC,IAAI;AAAA,IAC/D;AACA,aAAS,qBAAqB,KAAK;AACjC,UAAI,kBAAkB,IAAI,QAAQ,YAAY,qBAAqB,EAAE;AACrE,aAAO,gBAAgB,QAAQ,YAAY,mBAAmB,SAAU,OAAO,KAAK;AAClF,eAAO,OAAO,aAAa,GAAG;AAAA,MAChC,CAAC;AAAA,IACH;AACA,aAAS,WAAW,KAAK;AACvB,aAAO,IAAI,SAAS,GAAG;AAAA,IACzB;AACA,aAAS,UAAU,KAAK;AACtB,UAAI;AACF,eAAO,mBAAmB,GAAG;AAAA,MAC/B,SAAS,GAAG;AAIV,eAAO;AAAA,MACT;AAAA,IACF;AACA,aAAS,YAAY,KAAK;AACxB,UAAI,CAAC,KAAK;AACR,eAAO,YAAY;AAAA,MACrB;AACA,UAAI;AACJ,UAAI,aAAa,UAAU,IAAI,KAAK,CAAC;AACrC,SAAG;AACD,qBAAa,qBAAqB,UAAU,EAAE,QAAQ,YAAY,qBAAqB,EAAE,EAAE,QAAQ,YAAY,qBAAqB,EAAE,EAAE,QAAQ,YAAY,4BAA4B,EAAE,EAAE,KAAK;AACjM,qBAAa,UAAU,UAAU;AACjC,wBAAgB,WAAW,MAAM,YAAY,mBAAmB,KAAK,WAAW,MAAM,YAAY,iBAAiB,KAAK,WAAW,MAAM,YAAY,mBAAmB,KAAK,WAAW,MAAM,YAAY,0BAA0B;AAAA,MACtO,SAAS,iBAAiB,cAAc,SAAS;AACjD,UAAI,eAAe;AACnB,UAAI,CAAC,cAAc;AACjB,eAAO,YAAY;AAAA,MACrB;AACA,UAAI,6BAA6B,YAAY,GAAG;AAC9C,eAAO;AAAA,MACT;AAEA,UAAI,aAAa,aAAa,UAAU;AACxC,UAAI,wBAAwB,WAAW,MAAM,YAAY,cAAc;AACvE,UAAI,CAAC,uBAAuB;AAC1B,eAAO;AAAA,MACT;AACA,UAAI,YAAY,sBAAsB,CAAC,EAAE,YAAY,EAAE,KAAK;AAC5D,UAAI,YAAY,qBAAqB,KAAK,SAAS,GAAG;AACpD,eAAO,YAAY;AAAA,MACrB;AACA,UAAI,gBAAgB,WAAW,QAAQ,OAAO,GAAG;AAEjD,UAAI,cAAc,aAAa,UAAU,SAAS,KAAK,GAAG;AACxD,eAAO;AAAA,MACT;AAEA,UAAI,cAAc,WAAW,cAAc,UAAU;AACnD,YAAI,CAAC,WAAW,aAAa,GAAG;AAC9B,iBAAO,YAAY;AAAA,QACrB;AACA,YAAI,QAAQ,IAAI,IAAI,aAAa;AACjC,cAAM,WAAW,MAAM,SAAS,YAAY;AAC5C,cAAM,WAAW,MAAM,SAAS,YAAY;AAC5C,eAAO,MAAM,SAAS;AAAA,MACxB;AACA,aAAO;AAAA,IACT;AACA,YAAQ,cAAc;AAAA;AAAA;", "names": []}