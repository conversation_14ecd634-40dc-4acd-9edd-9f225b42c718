from .project_generation_routes import router as project_generation_router
from fastapi import APIRouter, HTTPException, status, BackgroundTasks, WebSocket
from fastapi.responses import Response
from .model import ClientCreate, ClientDB, QuickClientCreate, ClientResponse
from .db import clients_collection, projetos_collection, db
from .async_db import motor_clients_collection, motor_projetos_collection
from .perplexity import gerar_dossie_perplexity, gerar_relatorio_basico, classificar_setor_simples
from .optimized_dossie_generator import optimized_generator
from datetime import datetime, UTC
from bson import ObjectId
import asyncio
import logging
from typing import List, Dict, Any
from shared.websocket_manager import websocket_manager
import sys
from pathlib import Path
from pymongo import MongoClient
from gridfs import GridFS
import os

router = APIRouter()
logger = logging.getLogger(__name__)

# Configurar sys.path para importações corretas
current_dir = Path(__file__).parent
backend_dir = current_dir.parent
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

# Importar geradores de PDF no topo do arquivo
ENHANCED_GENERATOR_AVAILABLE = False
ENHANCED_GENERATOR = None
BASIC_GENERATOR = None

try:
    from tools.reports.pdf_generator_enhanced import EnhancedPDFGenerator, AIReportGeneratorEnhanced
    ENHANCED_GENERATOR = AIReportGeneratorEnhanced
    ENHANCED_GENERATOR_AVAILABLE = True
    logger.info("✅ Gerador de PDF aprimorado carregado com sucesso")
except Exception as e:
    logger.warning(f"⚠️ Gerador de PDF aprimorado não disponível: {str(e)}")
    try:
        # Fallback para compatibilidade
        from tools.reports.pdf_generator_enhanced import AIReportGeneratorEnhanced
        ENHANCED_GENERATOR = AIReportGeneratorEnhanced
        ENHANCED_GENERATOR_AVAILABLE = True
        logger.info("✅ Gerador de PDF aprimorado carregado (fallback)")
    except Exception as e2:
        logger.error(f"❌ Falha total no gerador aprimorado: {str(e2)}")

# Versão básica removida - usando apenas enhanced generator
BASIC_GENERATOR = None
logger.info("ℹ️ Gerador PDF consolidado: usando apenas versão enhanced")


@router.websocket("/clients/notifications")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket para notificações em tempo real"""
    await websocket_manager.connect(websocket)
    try:
        while True:
            # Manter conexão ativa
            await websocket.receive_text()
    except:
        websocket_manager.disconnect(websocket)


@router.post("/clients/quick", status_code=status.HTTP_201_CREATED)
async def create_client_quick(client: QuickClientCreate, background_tasks: BackgroundTasks):
    """
    FASE 1: Cadastro rápido de cliente (10-30 segundos)

    - Salva dados básicos do cliente
    - Classifica setor de forma simples (sem Perplexity)
    - Gera informações básicas da empresa
    - Retorna imediatamente
    - Habilita menu do usuário
    """
    try:
        # Montar documento básico para MongoDB
        now = datetime.now(UTC)
        client_data = client.model_dump()

        # Classificar setor de forma rápida (sem IA pesada)
        setor_basico = classificar_setor_simples(
            nome=client_data.get("name") or "",
            site=client_data.get("site") or "",
            cidade=client_data.get("city") or ""
        )

        client_data.update({
            "projects": [],
            "projectsCount": 0,
            "setor": setor_basico,
            "sector": setor_basico,  # Garantir que ambos os campos tenham o mesmo valor
            "tags": [],
            "created_at": now,
            "updated_at": now,
            "reports": [],
            "status": "Processando",  # Status especial para indicar processamento
            "totalValue": "R$ 0,00",
            "lastContact": "-",
            "report_status": "basic_only",  # Indica que só tem dados básicos
            "complete_report_requested": False
        })

        # Preencher contatos automaticamente
        if not client_data.get("contacts") and client_data.get("responsible"):
            client_data["contacts"] = [{
                "name": client_data["responsible"],
                "position": client_data.get("responsibleRole", ""),
                "primary": True
            }]

        # Gerar relatório básico simples (sem Perplexity)
        relatorio_basico = gerar_relatorio_basico(client_data)
        if relatorio_basico:
            client_data["reports"].append(relatorio_basico)

        # Salvar no MongoDB - ASYNC
        result = await motor_clients_collection.insert_one(client_data)
        client_id = result.inserted_id
        client_data["_id"] = str(client_id)

        # ✅ CORREÇÃO: Atualizar status para "Novo" após salvar (não "Ativo") - ASYNC
        await motor_clients_collection.update_one(
            {"_id": client_id},
            {"$set": {"status": "Novo", "updated_at": datetime.now(UTC)}}
        )

        logger.info(
            f"Cliente criado rapidamente: {client_data.get('name')} (ID: {client_id})")

        # Montar resposta para o frontend
        response = {
            "id": str(client_id),
            "name": client_data.get("name"),
            "company": client_data.get("company", client_data.get("name", "")),
            "email": client_data.get("email", ""),
            "phone": client_data.get("phone", ""),
            "cpfCnpj": client_data.get("cpfCnpj", ""),
            "responsible": client_data.get("responsible", ""),
            "responsibleRole": client_data.get("responsibleRole", ""),
            "city": client_data.get("city", ""),
            "state": client_data.get("state", ""),
            "site": client_data.get("site", ""),
            "address": client_data.get("address", ""),
            "sector": setor_basico,
            "active": client_data.get("active", True),
            "since": client_data.get("since", now.strftime("%Y-%m-%d")),
            "projectsCount": 0,
            "totalValue": "R$ 0,00",
            "lastContact": "-",
            "status": "Novo",  # ✅ CORREÇÃO: Status deve ser "Novo", não "Ativo"
            "tags": [],
            "contacts": client_data.get("contacts", []),
            "reports": client_data.get("reports", []),
            "report_status": "basic_only",
            "complete_report_available": False,
            "processing_complete_report": False,
            "created_at": client_data["created_at"].isoformat(),
            "updated_at": now.isoformat(),
        }

        return response

    except Exception as e:
        logger.error(f"Erro no cadastro rápido: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno no cadastro rápido: {str(e)}"
        )


# Endpoint moved to api/report_routes.py following SRP principles


# Endpoint moved to api/report_routes.py following SRP principles


async def processar_relatorio_completo(client_id: ObjectId, nome: str, site: str, cidade: str, estado: str):
    """
    Processa relatório completo em background e notifica quando pronto
    NOVO: Inclui pesquisa automática do Tavily após gerar o dossiê
    """
    try:
        logger.info(
            f"Iniciando processamento do relatório completo para: {nome}")

        # ✅ NOVO: Atualizar status para "Coletando informações"
        clients_collection.update_one(
            {"_id": client_id},
            {
                "$set": {
                    "status": "Coletando informações",
                    "report_status": "processing_complete",
                    "updated_at": datetime.now(UTC)
                }
            }
        )

        # ✅ NOVO: Notificar frontend sobre início da coleta
        await websocket_manager.broadcast({
            "type": "collection_status_update",
            "clientId": str(client_id),
            "status": "Coletando informações",
            "message": f"Iniciando coleta de dados para {nome} . Tempo estimado: 10 minutos",
            "timestamp": datetime.now(UTC).isoformat(),
            "phase": "data_collection_started"
        })

        # Gerar dossie completo (função existente)
        await asyncio.to_thread(
            gerar_dossie_perplexity,
            client_id, nome, site, cidade, estado
        )

        logger.info(
            f"✅ Dossiê completo gerado para {nome}. Iniciando pesquisa de mercado...")

        # Buscar dados do cliente para notificações
        client = clients_collection.find_one({"_id": client_id})
        sector = client.get("sector") or client.get(
            "setor") if client else None

        # ✅ ATUALIZAR: Status no banco para "Novo"
        clients_collection.update_one(
            {"_id": client_id},
            {
                "$set": {
                    "status": "Novo",  # ✅ Status deve ser "Novo", não "Ativo"
                    "report_status": "complete",
                    "complete_report_finished_at": datetime.now(UTC),
                    "complete_report_requested": False,  # ✅ Resetar flag de processamento
                    "updated_at": datetime.now(UTC)
                }
            }
        )

        # Mensagem de conclusão do dossiê
        ready_message = f"Dossiê concluído para {nome}. Deseja gerar projetos?"

        # ✅ NOVO: Notificar que TUDO está pronto para gerar projetos
        await websocket_manager.broadcast({
            "type": "ready_for_projects",
            "clientId": str(client_id),
            "clientName": nome,
            "status": "Novo",
            "message": ready_message,
            "timestamp": datetime.now(UTC).isoformat(),
            "action": "show_projects_alert",
            "phase": "ready_for_projects",
            "sector": sector
        })

        # 🚀 NOVO: Triggerar geração automática de PDF inteligente
        try:
            from .pdf_background_service import trigger_pdf_check
            pdf_generated = await trigger_pdf_check(str(client_id))
            if pdf_generated:
                logger.info(f"✅ PDF automático iniciado para {nome}")
            else:
                logger.info(
                    f"ℹ️ PDF automático não necessário para {nome} (requisitos não atendidos ou já existe)")
        except Exception as pdf_error:
            logger.warning(
                f"⚠️ Erro ao verificar PDF automático para {nome}: {pdf_error}")
            pdf_generated = False
            # Não falhar o processo principal por causa do PDF

        # Atualizar no banco se PDF foi gerado
        update_data = {
            "pdf_report_available": pdf_generated,
            "updated_at": datetime.now(UTC)
        }
        if pdf_generated:
            clients_collection.update_one(
                {"_id": client_id},
                {"$set": update_data}
            )

        # Notificar via WebSocket sobre conclusão do relatório
        await websocket_manager.broadcast({
            "type": "client_status_update",
            "clientId": str(client_id),
            "status": "Novo",
            "message": f"Relatório completo de {nome} foi finalizado",
            "timestamp": datetime.now(UTC).isoformat(),
            "action": "refresh_client",
            "pdf_available": pdf_generated
        })

        logger.info(
            f"✅ FLUXO COMPLETO CONCLUÍDO - Cliente {nome}: Dossiê ✅ | PDF {'✅' if pdf_generated else '⚠️'} | Frontend Notificado ✅")

    except Exception as e:
        logger.error(f"Erro no processamento do relatório completo: {str(e)}")

        # Marcar como erro no banco
        clients_collection.update_one(
            {"_id": client_id},
            {
                "$set": {
                    "status": "Erro",  # Atualizar status principal para erro
                    "report_status": "error",
                    "report_error": str(e),
                    "complete_report_requested": False,  # ✅ Resetar flag mesmo em erro
                    "updated_at": datetime.now(UTC)
                }
            }
        )


# Endpoint moved to api/report_routes.py following SRP principles


# Endpoint moved to api/report_routes.py following SRP principles


# Rota de debug do Ollama removida - sistema migrado para OpenAI


# Manter endpoint original para compatibilidade


@router.post("/clients", status_code=status.HTTP_201_CREATED)
async def create_client(client: ClientCreate, background_tasks: BackgroundTasks):
    """
    ENDPOINT ORIGINAL - Mantido para compatibilidade

    Comportamento legado: gera o dossie completo em background
    Recomendado usar /clients/quick + /clients/{id}/complete-report
    """
    # Usar a mesma lógica do quick mas marcar para processamento completo automático
    now = datetime.now(UTC)
    client_data = client.model_dump()

    # Classificar setor de forma rápida
    setor_basico = classificar_setor_simples(
        nome=client_data.get("name") or "",
        site=client_data.get("site") or "",
        cidade=client_data.get("city") or ""
    )

    client_data.update({
        "projects": [],
        "projectsCount": 0,
        "setor": setor_basico,
        "sector": setor_basico,
        "tags": [],
        "created_at": now,
        "updated_at": now,
        "reports": [],
        "status": "Processando",
        "totalValue": "R$ 0,00",
        "lastContact": "-",
        # Marcar como processando automaticamente
        "report_status": "processing_complete",
        "complete_report_requested": True
    })

    # Preencher contatos automaticamente
    if not client_data.get("contacts") and client_data.get("responsible"):
        client_data["contacts"] = [{
            "name": client_data["responsible"],
            "position": client_data.get("responsibleRole", ""),
            "primary": True
        }]

    result = await motor_clients_collection.insert_one(client_data)
    client_id = result.inserted_id

    # Notificar imediatamente via WebSocket que o cliente foi criado
    try:
        import asyncio
        loop = None
        try:
            loop = asyncio.get_running_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        async def notify_client_created():
            await websocket_manager.broadcast({
                "type": "client_created",
                "clientId": str(client_id),
                "clientName": client_data.get("name", ""),
                "sector": setor_basico
            })

        if loop.is_running():
            asyncio.run_coroutine_threadsafe(notify_client_created(), loop)
        else:
            loop.run_until_complete(notify_client_created())
    except Exception as e:
        print(f"Erro ao notificar criação de cliente via WebSocket: {e}")

    # Disparar pesquisa Perplexity completa em background (comportamento original)
    try:
        from api.report_routes import processar_relatorio_completo
        background_tasks.add_task(
            processar_relatorio_completo,
            client_id,
            client_data.get("name") or "",
            client_data.get("site") or "",
            client_data.get("city") or "",
            client_data.get("state") or ""
        )
    except ImportError:
        logger.warning(
            "Report module not available - skipping complete report processing")

    client_data["_id"] = str(client_id)

    # Montar resposta compatível com o frontend
    response = {
        "id": str(client_id),
        "name": client_data.get("name"),
        "company": client_data.get("company", client_data.get("name", "")),
        "email": client_data.get("email", ""),
        "phone": client_data.get("phone", ""),
        "cpfCnpj": client_data.get("cpfCnpj", ""),
        "responsible": client_data.get("responsible", ""),
        "responsibleRole": client_data.get("responsibleRole", ""),
        "city": client_data.get("city", ""),
        "state": client_data.get("state", ""),
        "site": client_data.get("site", ""),
        "address": client_data.get("address", ""),
        "sector": setor_basico,
        "active": client_data.get("active", True),
        "since": client_data.get("since", now.strftime("%Y-%m-%d")),
        "projectsCount": client_data.get("projectsCount", 0),
        "totalValue": client_data.get("totalValue", "R$ 0,00"),
        "lastContact": client_data.get("lastContact", "-"),
        "status": "Processando",
        "tags": client_data.get("tags", []),
        "contacts": client_data.get("contacts", []),
        "reports": client_data.get("reports", []),
        "created_at": client_data["created_at"].isoformat(),
        "updated_at": client_data["updated_at"].isoformat(),
    }
    return response


@router.get("/clients")
async def get_clients():
    """Lista todos os clientes de forma assíncrona"""
    # Usar operação assíncrona se disponível
    try:
        from clients.async_db import get_async_database
        async_db = get_async_database()
        clients_async_collection = async_db.clients

        clients = []
        async for client_data in clients_async_collection.find():
            # Para campos 'since' caso não existam
            now_for_default = datetime.now(UTC)
            # Assegurar que created_at e updated_at sejam datetime para formatar
            created_at = client_data.get("created_at")
            if not isinstance(created_at, datetime):
                created_at = now_for_default  # Ou alguma outra lógica de fallback

            updated_at = client_data.get("updated_at")
            if not isinstance(updated_at, datetime):
                updated_at = now_for_default  # Ou alguma outra lógica de fallback

            response_item = {
                "id": str(client_data["_id"]),
                "name": client_data.get("name"),
                "company": client_data.get("company", client_data.get("name", "")),
                "email": client_data.get("email", ""),
                "phone": client_data.get("phone", ""),
                "cpfCnpj": client_data.get("cpfCnpj", ""),
                "responsible": client_data.get("responsible", ""),
                "responsibleRole": client_data.get("responsibleRole", ""),
                "city": client_data.get("city", ""),
                "state": client_data.get("state", ""),
                "site": client_data.get("site", ""),
                "address": client_data.get("address", ""),
                "sector": client_data.get("sector") or client_data.get("setor", "Processando..."),
                "active": client_data.get("active", True),
                "since": client_data.get("since", created_at.strftime("%Y-%m-%d")),
                "projectsCount": client_data.get("projectsCount", 0),
                "totalValue": client_data.get("totalValue", "R$ 0,00"),
                "lastContact": client_data.get("lastContact", "-"),
                "status": client_data.get("status", "Novo"),
                "tags": client_data.get("tags", []),
                "contacts": client_data.get("contacts", []),
                "reports": client_data.get("reports", []),
                # ✅ CORREÇÃO: Incluir campos PDF
                "pdf_report_available": client_data.get("pdf_report_available", False),
                "pdf_report_id": client_data.get("pdf_report_id"),
                "pdf_generated_at": client_data.get("pdf_generated_at").isoformat() if client_data.get("pdf_generated_at") else None,
                "created_at": created_at.isoformat(),
                "updated_at": updated_at.isoformat(),
            }
            clients.append(response_item)
        return clients
    except ImportError:
        # Fallback para operação síncrona se Motor não estiver disponível
        # Mas executar em thread separada para não bloquear
        import asyncio
        from concurrent.futures import ThreadPoolExecutor

        def sync_get_clients():
            clients = []
            for client_data in clients_collection.find():
                # Para campos 'since' caso não existam
                now_for_default = datetime.now(UTC)
                # Assegurar que created_at e updated_at sejam datetime para formatar
                created_at = client_data.get("created_at")
                if not isinstance(created_at, datetime):
                    created_at = now_for_default  # Ou alguma outra lógica de fallback

                updated_at = client_data.get("updated_at")
                if not isinstance(updated_at, datetime):
                    updated_at = now_for_default  # Ou alguma outra lógica de fallback

                response_item = {
                    "id": str(client_data["_id"]),
                    "name": client_data.get("name"),
                    "company": client_data.get("company", client_data.get("name", "")),
                    "email": client_data.get("email", ""),
                    "phone": client_data.get("phone", ""),
                    "cpfCnpj": client_data.get("cpfCnpj", ""),
                    "responsible": client_data.get("responsible", ""),
                    "responsibleRole": client_data.get("responsibleRole", ""),
                    "city": client_data.get("city", ""),
                    "state": client_data.get("state", ""),
                    "site": client_data.get("site", ""),
                    "address": client_data.get("address", ""),
                    "sector": client_data.get("sector") or client_data.get("setor", "Processando..."),
                    "active": client_data.get("active", True),
                    "since": client_data.get("since", created_at.strftime("%Y-%m-%d")),
                    "projectsCount": client_data.get("projectsCount", 0),
                    "totalValue": client_data.get("totalValue", "R$ 0,00"),
                    "lastContact": client_data.get("lastContact", "-"),
                    "status": client_data.get("status", "Novo"),
                    "tags": client_data.get("tags", []),
                    "contacts": client_data.get("contacts", []),
                    "reports": client_data.get("reports", []),
                    # ✅ CORREÇÃO: Incluir campos PDF
                    "pdf_report_available": client_data.get("pdf_report_available", False),
                    "pdf_report_id": client_data.get("pdf_report_id"),
                    "pdf_generated_at": client_data.get("pdf_generated_at").isoformat() if client_data.get("pdf_generated_at") else None,
                    "created_at": created_at.isoformat(),
                    "updated_at": updated_at.isoformat(),
                }
                clients.append(response_item)
            return clients

        # Executar em thread separada para não bloquear
        loop = asyncio.get_event_loop()
        with ThreadPoolExecutor() as pool:
            return await loop.run_in_executor(pool, sync_get_clients)


@router.post("/clients/{client_id}/regenerate-funding-data")
async def regenerate_funding_data(client_id: str, background_tasks: BackgroundTasks):
    """
    Regenera dados de funding e presença digital para um cliente existente
    """
    try:
        # Verificar se o cliente existe - ASYNC
        client = await motor_clients_collection.find_one({"_id": ObjectId(client_id)})
        if not client:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Cliente não encontrado"
            )

        # Disparar regeneração em background
        background_tasks.add_task(
            gerar_dossie_perplexity,
            ObjectId(client_id),
            client.get("name"),
            client.get("site"),
            client.get("city"),
            client.get("state")
        )

        return {
            "message": "Regeneração de dados de funding e presença digital iniciada",
            "client_id": client_id,
            "client_name": client.get("name"),
            "status": "processing"
        }

    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="ID do cliente inválido"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        )


@router.get("/clients/{client_id}/funding-data")
async def get_client_funding_data(client_id: str):
    """
    Retorna apenas os dados de funding e presença digital de um cliente
    """
    try:
        # Buscar cliente - ASYNC
        client = await motor_clients_collection.find_one({"_id": ObjectId(client_id)})
        if not client:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Cliente não encontrado"
            )

        # Extrair dados de funding e presença digital dos reports
        funding_data = None
        presenca_digital_data = None

        for report in client.get("reports", []):
            if report.get("report_type") == "dossie_expandido":
                funding_data = report.get("dados_funding")
                presenca_digital_data = report.get("dados_presenca_digital")
                break

        return {
            "client_id": client_id,
            "client_name": client.get("name"),
            "dados_funding": funding_data,
            "dados_presenca_digital": presenca_digital_data,
            "has_funding_data": funding_data is not None,
            "has_presenca_digital_data": presenca_digital_data is not None
        }

    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="ID do cliente inválido"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        )


@router.get("/clients/{client_id}/enhanced-report")
async def download_enhanced_report(client_id: str):
    """
    Download do relatório executivo aprimorado em PDF
    """
    try:
        from fastapi.responses import Response

        # Verificar se o cliente existe - ASYNC
        client = await motor_clients_collection.find_one({"_id": ObjectId(client_id)})
        if not client:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Cliente não encontrado"
            )

        # Verificar se tem relatório aprimorado
        enhanced_pdf_id = client.get("enhanced_pdf_id")
        if not enhanced_pdf_id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Relatório aprimorado não encontrado. Execute a geração primeiro."
            )

        # Buscar PDF no GridFS
        fs = GridFS(db)
        try:
            pdf_file = fs.get(enhanced_pdf_id)
            pdf_data = pdf_file.read()

            client_name = client.get("nome", "Cliente")

            logger.info(f"✅ Servindo relatório aprimorado para {client_name}")

            return Response(
                content=pdf_data,
                media_type="application/pdf",
                headers={
                    "Content-Disposition": f"attachment; filename=relatorio_executivo_enhanced_{client_name.replace(' ', '_')}.pdf"
                }
            )

        except Exception as e:
            logger.error(f"Erro ao buscar PDF aprimorado no GridFS: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Arquivo PDF aprimorado não encontrado no sistema"
            )

    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="ID do cliente inválido"
        )
    except Exception as e:
        logger.error(f"Erro ao baixar relatório aprimorado: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        )


@router.get("/clients/{client_id}/report")
async def download_client_report(client_id: str, regenerate: bool = False, enhanced: bool = True):
    """
    Download do relatório executivo AI em PDF do cliente
    🚀 NOVA VERSÃO: Usa sistema AI-powered com GridFS
    ✨ PADRÃO: Sempre gera PDF expandido (15+ seções)

    Parâmetros:
    - regenerate: Força regeneração do PDF (default: False)
    - enhanced: Usa o gerador aprimorado (default: True) - SEMPRE ATIVO

    Exemplos:
    - GET /clients/{id}/report - Baixa PDF expandido
    - GET /clients/{id}/report?regenerate=true - Regenera PDF expandido
    - GET /clients/{id}/report?enhanced=false - Força PDF básico (não recomendado)
    """
    try:
        from fastapi.responses import Response
        import os

        # Verificar se o cliente existe - ASYNC
        client = await motor_clients_collection.find_one({"_id": ObjectId(client_id)})
        if not client:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Cliente não encontrado"
            )

        client_name = client.get("name", "Cliente")

        # 🔄 NOVA LÓGICA: Verificar se deve regenerar PDF
        should_regenerate = regenerate

        # Determinar qual gerador usar baseado no parâmetro enhanced
        if enhanced:
            pdf_file_id = client.get("enhanced_pdf_id")
            pdf_generated_at_field = "enhanced_pdf_generated_at"
            logger.info(f"📈 Usando gerador aprimorado para {client_name}")
        else:
            pdf_file_id = client.get("pdf_report_id")
            pdf_generated_at_field = "pdf_generated_at"

        # ✅ CORREÇÃO: Função helper para comparar datetimes de forma segura
        def _safe_datetime_age_days(pdf_generated_at):
            """Calcula idade em dias de forma segura lidando com timezone"""
            if not pdf_generated_at:
                return 999  # Força regeneração se não há data

            now_utc = datetime.now(UTC)

            # Se pdf_generated_at é naive (sem timezone), assumir UTC
            if isinstance(pdf_generated_at, datetime):
                if pdf_generated_at.tzinfo is None:
                    pdf_generated_at = pdf_generated_at.replace(tzinfo=UTC)
                elif pdf_generated_at.tzinfo != UTC:
                    pdf_generated_at = pdf_generated_at.astimezone(UTC)

            return (now_utc - pdf_generated_at).days

        # Verificar se PDF existe e se é muito antigo (7 dias)
        if not should_regenerate and pdf_file_id:
            pdf_generated_at = client.get(pdf_generated_at_field)
            if pdf_generated_at:
                age_days = _safe_datetime_age_days(pdf_generated_at)
                if age_days > 7:
                    should_regenerate = True
                    report_type = "aprimorado" if enhanced else "básico"
                    logger.info(
                        f"🔄 PDF {report_type} de {client_name} tem {age_days} dias. Regenerando...")

        # ✅ OPÇÃO 1: Gerar novo PDF se solicitado ou necessário
        if should_regenerate or not pdf_file_id:
            try:
                if enhanced and ENHANCED_GENERATOR_AVAILABLE and ENHANCED_GENERATOR:
                    # Usar gerador aprimorado
                    logger.info(
                        f"🔄 Gerando novo PDF aprimorado para {client_name}")

                    # Buscar dossiê expandido nos reports
                    expanded_dossier = None
                    for report in client.get("reports", []):
                        if report.get("reportType") == "dossie_expandido":
                            expanded_dossier = report.get("data", {})
                            break

                    if not expanded_dossier:
                        logger.warning(
                            f"⚠️ Cliente não possui dossiê expandido - criando baseado nos dados disponíveis")
                        expanded_dossier = _create_minimal_expanded_dossier(
                            client)

                    generator = ENHANCED_GENERATOR()
                    pdf_content = generator.generate_report(
                        expanded_dossier, client_name)

                    # Salvar no GridFS
                    fs = GridFS(db)
                    pdf_file_id_new = fs.put(
                        pdf_content,
                        filename=f"relatorio_executivo_enhanced_{client_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                        content_type="application/pdf",
                        client_id=str(client["_id"]),
                        generated_at=datetime.now(UTC),
                        report_type="enhanced_executive"
                    )

                    # Atualizar banco - ASYNC
                    await motor_clients_collection.update_one(
                        {"_id": client["_id"]},
                        {
                            "$set": {
                                "enhanced_pdf_id": pdf_file_id_new,
                                "enhanced_pdf_generated_at": datetime.now(UTC),
                                "updated_at": datetime.now(UTC)
                            }
                        }
                    )

                elif not enhanced or not ENHANCED_GENERATOR_AVAILABLE:
                    # Usar gerador básico como fallback
                    logger.info(
                        f"🔄 Gerando PDF básico para {client_name} (fallback)")

                    if BASIC_GENERATOR:
                        pdf_file_id_new = BASIC_GENERATOR.generate_ai_report(
                            client)
                    else:
                        raise Exception("Nenhum gerador de PDF disponível")

                    if pdf_file_id_new and not enhanced:
                        # Atualizar banco com novo PDF básico - ASYNC
                        await motor_clients_collection.update_one(
                            {"_id": ObjectId(client_id)},
                            {
                                "$set": {
                                    "pdf_report_id": pdf_file_id_new,
                                    "pdf_generated_at": datetime.now(UTC),
                                    "pdf_report_available": True,
                                    "updated_at": datetime.now(UTC)
                                }
                            }
                        )

                        # Baixar o novo PDF básico
                        if BASIC_GENERATOR:
                            pdf_data = BASIC_GENERATOR.get_pdf_from_gridfs(
                                pdf_file_id_new)

                            if pdf_data:
                                logger.info(
                                    f"✅ Novo PDF AI básico gerado e servido para {client_name}")
                                return Response(
                                    content=pdf_data,
                                    media_type="application/pdf",
                                    headers={
                                        "Content-Disposition": f"attachment; filename=relatorio_executivo_novo_{client_name.replace(' ', '_')}.pdf"
                                    }
                                )

                # Para o gerador aprimorado, já foi salvo acima
                if enhanced and pdf_file_id_new:
                    # Baixar o PDF aprimorado
                    fs = GridFS(db)
                    pdf_file = fs.get(pdf_file_id_new)
                    pdf_content = pdf_file.read()

                    logger.info(
                        f"✅ Novo PDF aprimorado gerado e servido para {client_name}")
                    return Response(
                        content=pdf_content,
                        media_type="application/pdf",
                        headers={
                            "Content-Disposition": f"attachment; filename=relatorio_executivo_enhanced_{client_name.replace(' ', '_')}.pdf"
                        }
                    )

                logger.warning(
                    f"⚠️ Falha na geração de novo PDF para {client_name}. Tentando usar PDF existente...")

            except Exception as e:
                logger.error(
                    f"❌ Erro ao gerar novo PDF para {client_name}: {str(e)}")

        # ✅ OPÇÃO 2: Usar PDF existente do cache
        if pdf_file_id:
            if enhanced:
                # Usar PDF aprimorado do cache
                fs = GridFS(db)
                try:
                    pdf_file = fs.get(pdf_file_id)
                    pdf_data = pdf_file.read()

                    pdf_age = ""
                    if client.get(pdf_generated_at_field):
                        age_days = _safe_datetime_age_days(
                            client.get(pdf_generated_at_field))
                        pdf_age = f" ({age_days} dias)" if age_days > 0 else " (hoje)"

                    logger.info(
                        f"✅ Servindo PDF aprimorado em cache para {client_name}{pdf_age}")
                    return Response(
                        content=pdf_data,
                        media_type="application/pdf",
                        headers={
                            "Content-Disposition": f"attachment; filename=relatorio_executivo_enhanced_{client_name.replace(' ', '_')}.pdf"
                        }
                    )
                except Exception as e:
                    logger.warning(
                        f"PDF aprimorado com ID {pdf_file_id} não encontrado no GridFS: {e}")
            else:
                # Usar PDF básico do cache
                if not BASIC_GENERATOR:
                    logger.error(
                        f"❌ AI Report Generator não disponível para baixar PDF de {client_name}")
                    raise HTTPException(
                        status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                        detail="Serviço de geração de PDF não está disponível"
                    )

                pdf_data = BASIC_GENERATOR.get_pdf_from_gridfs(pdf_file_id)

                if pdf_data:
                    pdf_age = ""
                    if client.get(pdf_generated_at_field):
                        # ✅ CORREÇÃO: Usar função segura para idade
                        age_days = _safe_datetime_age_days(
                            client.get(pdf_generated_at_field))
                        pdf_age = f" ({age_days} dias)" if age_days > 0 else " (hoje)"

                    logger.info(
                        f"✅ Servindo PDF AI básico em cache para {client_name}{pdf_age}")
                    return Response(
                        content=pdf_data,
                        media_type="application/pdf",
                        headers={
                            "Content-Disposition": f"attachment; filename=relatorio_executivo_{client_name.replace(' ', '_')}.pdf"
                        }
                    )
                else:
                    logger.warning(
                        f"PDF AI com ID {pdf_file_id} não encontrado no GridFS para cliente {client_id}")

        # ✅ PRIORIDADE 2: Verificar PDF do sistema antigo
        pdf_path = client.get("full_processing_pdf_path")
        if pdf_path and os.path.exists(pdf_path):
            from fastapi.responses import FileResponse
            return FileResponse(
                pdf_path,
                media_type="application/pdf",
                filename=f"relatorio_{client_name.replace(' ', '_')}.pdf"
            )

        # ✅ PRIORIDADE 3: Gerar PDF básico em tempo real se houver dados
        reports = client.get("reports", [])
        dossie_expandido = None

        for report in reports:
            if report.get("reportType") == "dossie_expandido":
                dossie_expandido = report.get("data", {})
                break

        if dossie_expandido:
            # Tentar gerar um relatório básico se não houver PDF AI
            from io import BytesIO
            from reportlab.pdfgen import canvas
            from fastapi.responses import StreamingResponse

            buffer = BytesIO()
            p = canvas.Canvas(buffer)

            # Título
            p.setFont("Helvetica-Bold", 16)
            p.drawString(100, 750, f"Relatório Básico - {client_name}")

            # Informações básicas
            p.setFont("Helvetica", 12)
            y_position = 700

            info_data = [
                f"Empresa: {client_name}",
                f"Setor: {client.get('sector', 'N/A')}",
                f"Cidade: {client.get('city', 'N/A')}",
                f"Estado: {client.get('state', 'N/A')}",
                f"Site: {client.get('site', 'N/A')}",
                "",
                "NOTA: Este é um relatório básico.",
                "Para o relatório executivo completo com IA,",
                "execute a análise completa do cliente."
            ]

            for info in info_data:
                p.drawString(100, y_position, info)
                y_position -= 25

            p.showPage()
            p.save()
            buffer.seek(0)

            return StreamingResponse(
                BytesIO(buffer.read()),
                media_type="application/pdf",
                headers={
                    "Content-Disposition": f"attachment; filename=relatorio_basico_{client_name.replace(' ', '_')}.pdf"
                }
            )

        # ✅ ERRO: Nenhum relatório disponível
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Nenhum relatório disponível para este cliente. Execute a análise completa primeiro."
        )

    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="ID do cliente inválido"
        )
    except Exception as e:
        logger.error(
            f"Erro ao baixar relatório para cliente {client_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        )


def _determinar_equipe_projeto(projeto: dict, client: dict) -> str:
    """
    🔍 Determina a equipe apropriada baseada nos dados do projeto e cliente

    Args:
        projeto: Dados do projeto gerado pelos agentes
        client: Dados do cliente

    Returns:
        Nome da equipe responsável
    """
    # Mapear área de especialidade para equipes com tratamento seguro de dados
    area_especialidade = str(projeto.get(
        "area_especialidade", "")).lower().strip()
    agente_responsavel = str(projeto.get(
        "agente_responsavel", "")).lower().strip()
    tags = [str(tag).lower().strip() for tag in projeto.get("tags", []) if tag]
    setor_cliente = str(client.get("sector") or client.get(
        "setor", "")).lower().strip()
    nome_projeto = str(projeto.get("nome_projeto", "")).lower().strip()
    justificativa = str(projeto.get("justificativa", "")).lower().strip()

    # Mapeamento baseado em área de especialidade (prioridade alta)
    if any(keyword in area_especialidade for keyword in ["dados", "analytics", "análise", "bi", "data", "cientista"]):
        return "Equipe de Dados"
    elif any(keyword in area_especialidade for keyword in ["marketing", "vendas", "comercial", "comunicação", "branding"]):
        return "Equipe de Marketing"
    elif any(keyword in area_especialidade for keyword in ["estratégia", "negócio", "consultoria", "gestão", "estratégico", "diretor", "arquitetura corporativa"]):
        return "Equipe de Estratégia"
    elif any(keyword in area_especialidade for keyword in ["tecnologia", "desenvolvimento", "digital", "software", "sistema", "desenvolvedor", "tech"]):
        return "Equipe de Desenvolvimento"
    elif any(keyword in area_especialidade for keyword in ["design", "ux", "ui", "experiência", "designer"]):
        return "Equipe de Design"

    # Mapeamento baseado em agente responsável
    if "ricardo" in agente_responsavel:
        return "Equipe de Estratégia"
    elif "joão" in agente_responsavel:
        return "Equipe de Marketing"
    elif any(keyword in agente_responsavel for keyword in ["desenvolvedor", "tech", "sistema"]):
        return "Equipe de Desenvolvimento"

    # Mapeamento baseado em tags (prioridade média)
    if any(tag in ["dados", "analytics", "análise", "bi", "insights", "metrics", "ia", "automação"] for tag in tags):
        return "Equipe de Dados"
    elif any(tag in ["marketing", "vendas", "comercial", "branding", "comunicação", "social"] for tag in tags):
        return "Equipe de Marketing"
    elif any(tag in ["estratégia", "consultoria", "gestão", "negócio", "planejamento", "governança", "portfolio management", "governance", "roadmap"] for tag in tags):
        return "Equipe de Estratégia"
    elif any(tag in ["tecnologia", "desenvolvimento", "digital", "software", "sistema", "plataforma", "app", "web"] for tag in tags):
        return "Equipe de Desenvolvimento"

    # Mapeamento baseado no nome do projeto
    if any(keyword in nome_projeto for keyword in ["site", "app", "sistema", "plataforma", "desenvolvimento", "tecnologia", "digital"]):
        return "Equipe de Desenvolvimento"
    elif any(keyword in nome_projeto for keyword in ["marketing", "campanha", "branding", "comunicação", "vendas"]):
        return "Equipe de Marketing"
    elif any(keyword in nome_projeto for keyword in ["estratégia", "consultoria", "planejamento", "gestão", "negócio"]):
        return "Equipe de Estratégia"
    elif any(keyword in nome_projeto for keyword in ["dados", "analytics", "análise", "relatório", "dashboard"]):
        return "Equipe de Dados"

    # Fallback baseado no setor do cliente
    if any(keyword in setor_cliente for keyword in ["tecnologia", "software", "ti"]):
        return "Equipe de Desenvolvimento"
    elif any(keyword in setor_cliente for keyword in ["marketing", "comunicação", "mídia"]):
        return "Equipe de Marketing"
    elif any(keyword in setor_cliente for keyword in ["consultoria", "serviços", "negócios"]):
        return "Equipe de Estratégia"

    # Fallback final
    return "Equipe Multidisciplinar"


def _calcular_progresso_inicial(projeto: dict) -> int:
    """
    📊 Calcula progresso inicial baseado no status e ciclo de vida do projeto

    LÓGICA CORRETA:
    - Projetos com status "Sugestão" = 0% (ainda não aceitos pelo cliente)
    - Projetos "Aceito" = 5% (projeto aceito, pode iniciar desenvolvimento) 
    - Projetos "Em Andamento" = progresso baseado no trabalho real feito

    IMPORTANTE: Progresso representa % de desenvolvimento executado,
    NÃO a qualidade da análise dos agentes!

    Args:
        projeto: Dados do projeto gerado pelos agentes

    Returns:
        int: Progresso inicial (0% para sugestões, >0% apenas após aceite)
    """
    # 🎯 Para projetos em status "Sugestão" (não aceitos), progresso sempre é 0
    # A qualidade da análise dos agentes NÃO é progresso de desenvolvimento!
    return 0


def processar_geracao_projetos(client_id: ObjectId, client_name: str):
    """
    🚀 Processa geração de projetos usando time da diretoria
    HÍBRIDO: Função síncrona que executa código assíncrono internamente
    """
    # Executar a lógica assíncrona dentro de uma função síncrona
    asyncio.run(_processar_geracao_projetos_async(client_id, client_name))


async def _processar_geracao_projetos_async(client_id: ObjectId, client_name: str):
    """
    🚀 Versão assíncrona interna da geração de projetos
    """
    try:
        logger.info(f"🚀 Iniciando geração de projetos para {client_name}")

        # Buscar dados do cliente
        client = clients_collection.find_one({"_id": client_id})
        if not client:
            logger.error(f"Cliente {client_id} não encontrado")
            return

        # Notificar início da geração
        await websocket_manager.broadcast({
            "type": "projects_generation_progress",
            "clientId": str(client_id),
            "clientName": client_name,
            "message": f"🤝 Consultando time da diretoria para {client_name}...",
            "phase": "boardroom_analysis",
            "timestamp": datetime.now(UTC).isoformat()
        })

        # Extrair reports relevantes para análise
        reports = client.get("reports", [])
        documents = []

        # Buscar dossiê completo
        dossie_report = None
        for report in reports:
            if report.get("report_type") == "dossie_expandido":
                dossie_report = report
                break

        # Buscar pesquisa de mercado
        market_report = None
        for report in reports:
            if report.get("report_type") == "pesquisa_mercado":
                market_report = report
                break

        if dossie_report:
            documents.append({
                "tipo": "dossie_completo",
                "dados": dossie_report
            })

        if market_report:
            documents.append({
                "tipo": "pesquisa_mercado",
                "dados": market_report
            })

        if not documents:
            logger.warning(f"Nenhum report encontrado para {client_name}")
            await websocket_manager.broadcast({
                "type": "projects_generation_error",
                "clientId": str(client_id),
                "clientName": client_name,
                "message": f"❌ Dados insuficientes para gerar projetos para {client_name}",
                "timestamp": datetime.now(UTC).isoformat()
            })
            return

        # Chamar agentes da diretoria
        from teams.boardroom_advisor_agents import boardroom_advisor_agents_suggestions

        logger.info(
            f"📊 Analisando {len(documents)} reports com time da diretoria")
        projetos_sugeridos = boardroom_advisor_agents_suggestions(documents)

        logger.info(
            f"💡 {len(projetos_sugeridos)} projetos sugeridos pelo time da diretoria")

        # Salvar projetos no MongoDB
        projetos_salvos = 0
        logger.info(
            f"🔍 Processando {len(projetos_sugeridos)} projetos para {client_name}")

        for i, projeto in enumerate(projetos_sugeridos, 1):
            try:
                logger.debug(
                    f"📋 Processando projeto {i}/{len(projetos_sugeridos)}: {projeto.get('nome_projeto', 'Sem nome')}")

                # 🆕 Determinar equipe baseada nos dados do projeto
                equipe_projeto = _determinar_equipe_projeto(projeto, client)
                logger.debug(
                    f"👥 Equipe determinada: {equipe_projeto} (baseada em: {projeto.get('area_especialidade', 'N/A')})")

                # 🆕 Calcular progresso inicial baseado em dados do projeto
                progresso_inicial = _calcular_progresso_inicial(projeto)
                logger.debug(
                    f"📊 Progresso inicial: {progresso_inicial}% (pontuação: {projeto.get('media_pontuacao_geral', projeto.get('pontuacao', 'N/A'))})")

                projeto_doc = {
                    "cliente_id": client_id,
                    "nome_projeto": projeto.get("nome_projeto", "Projeto sem nome"),
                    # Máximo 3 tags
                    "tags": projeto.get("tags", ["Estratégia"])[:3],
                    "status": "Sugestão",
                    # Máximo 100 caracteres
                    "resumo": projeto.get("resumo", "")[:100],
                    "progresso": progresso_inicial,
                    "equipe": equipe_projeto,
                    "justificativa": projeto.get("justificativa", ""),
                    "importancia": projeto.get("importancia", ""),
                    # ✅ CORRIGIDO: Novo formato usa 'media_pontuacao_geral' com fallback para compatibilidade
                    "pontuacao": projeto.get("media_pontuacao_geral", projeto.get("pontuacao", 70)),
                    # ✅ NOVOS CAMPOS: Adicionados campos do novo formato
                    "detalhamento": projeto.get("detalhamento", ""),
                    "beneficios": projeto.get("beneficios", ""),
                    "agentes_justificativa": projeto.get("agentes_justificativa", []),
                    "agente_responsavel": projeto.get("agente_responsavel", ""),
                    "area_especialidade": projeto.get("area_especialidade", ""),
                    "created_at": datetime.now(UTC),
                    "updated_at": datetime.now(UTC)
                }

                projetos_collection.insert_one(projeto_doc)
                projetos_salvos += 1

                logger.info(
                    f"✅ Projeto '{projeto_doc['nome_projeto']}' salvo - Equipe: {equipe_projeto} | Progresso: {progresso_inicial}% | Agente: {projeto_doc['agente_responsavel']}")

            except Exception as e:
                logger.error(f"Erro ao salvar projeto: {e}")
                continue

        # Atualizar status do cliente
        clients_collection.update_one(
            {"_id": client_id},
            {
                "$set": {
                    "project_generation_status": "completed",
                    "projetos_count": projetos_salvos,
                    "updated_at": datetime.now(UTC)
                }
            }
        )

        # Notificar conclusão
        await websocket_manager.broadcast({
            "type": "projects_generation_completed",
            "clientId": str(client_id),
            "clientName": client_name,
            "message": f"✅ {projetos_salvos} projetos gerados para {client_name}!",
            "projects_count": projetos_salvos,
            "timestamp": datetime.now(UTC).isoformat()
        })

        logger.info(
            f"🎉 Geração de projetos concluída para {client_name}: {projetos_salvos} projetos salvos")

    except Exception as e:
        logger.error(f"Erro na geração de projetos para {client_name}: {e}")

        # Notificar erro
        await websocket_manager.broadcast({
            "type": "projects_generation_error",
            "clientId": str(client_id),
            "clientName": client_name,
            "message": f"❌ Erro ao gerar projetos para {client_name}: {str(e)}",
            "timestamp": datetime.now(UTC).isoformat()
        })

        # Atualizar status com erro
        clients_collection.update_one(
            {"_id": client_id},
            {
                "$set": {
                    "project_generation_status": "error",
                    "updated_at": datetime.now(UTC)
                }
            }
        )


@router.post("/clients/{client_id}/generate-projects")
async def generate_projects(client_id: str, background_tasks: BackgroundTasks):
    """
    🚀 ATUALIZADO: Gera projetos para o cliente.

    Se pesquisa de mercado já foi feita automaticamente, usa os dados existentes.
    Caso contrário, faz a pesquisa primeiro (para compatibilidade).
    """
    client = None
    sector = None
    try:
        # Validar se cliente existe
        client = clients_collection.find_one({"_id": ObjectId(client_id)})
        if not client:
            raise HTTPException(
                status_code=404, detail="Cliente não encontrado")

        client_name = client.get("name", "")
        sector = client.get("sector") or client.get("setor")

        # Atualizar status para indicar processamento
        clients_collection.update_one(
            {"_id": ObjectId(client_id)},
            {
                "$set": {
                    "project_generation_status": "generating",
                    "updated_at": datetime.now(UTC)
                }
            }
        )

        # Log para debug
        logger.info(f"🔍 DEBUG - Cliente: {client_name}, Setor: {sector}")

        # Gerar projetos usando dados do dossiê expandido (que já inclui pesquisa de mercado)
        await websocket_manager.broadcast({
            "type": "projects_generation_started",
            "clientId": client_id,
            "clientName": client_name,
            "message": f"Gerando projetos para {client_name} usando dados do dossiê expandido...",
            "timestamp": datetime.now(UTC).isoformat(),
            "has_market_data": True
        })

        # Gerar projetos usando time da diretoria (boardroom_advisor_agents)
        background_tasks.add_task(
            processar_geracao_projetos,
            ObjectId(client_id),
            client_name
        )

        return {
            "message": "Geração de projetos iniciada usando dados do dossiê expandido.",
            "client_id": client_id,
            "status": "processing",
            "used_existing_market_data": True
        }

    except HTTPException:
        raise
    except Exception as e:
        # Garantir que client, client_name e sector existam para o log/erro
        client_safe = client if client is not None else {}
        client_name_safe = client_safe.get("name", "")
        sector_safe = sector if sector is not None else ""
        logger.error(f"Erro ao gerar projetos: {str(e)}")
        clients_collection.update_one(
            {"_id": ObjectId(client_id)},
            {"$set": {"project_generation_status": "error",
                      "project_generation_error": str(e), "updated_at": datetime.now(UTC)}}
        )
        await websocket_manager.broadcast({
            "type": "projects_generation_error",
            "clientId": client_id,
            "clientName": client_name_safe,
            "error": str(e),
            "message": f"Erro ao gerar projetos para {client_name_safe}",
            "timestamp": datetime.now(UTC).isoformat()
        })
        raise HTTPException(
            status_code=500, detail="Erro interno do servidor ao gerar projetos")


@router.post("/projects")
async def create_project(project_data: dict):
    """
    📝 Cria um novo projeto individual para um cliente
    """
    try:
        # Validar dados obrigatórios
        required_fields = ["nome_projeto", "cliente_id",
                           "cliente_nome", "descricao_completa"]
        for field in required_fields:
            if field not in project_data:
                raise HTTPException(
                    status_code=400,
                    detail=f"Campo obrigatório ausente: {field}"
                )

        # Converter cliente_id para ObjectId
        try:
            cliente_obj_id = ObjectId(project_data["cliente_id"])
        except:
            raise HTTPException(
                status_code=400,
                detail="ID de cliente inválido"
            )

        # Verificar se cliente existe
        client = clients_collection.find_one({"_id": cliente_obj_id})
        if not client:
            raise HTTPException(
                status_code=404,
                detail="Cliente não encontrado"
            )

        # Preparar dados do projeto
        novo_projeto = {
            "cliente_id": cliente_obj_id,
            "nome_projeto": project_data["nome_projeto"],
            "cliente_nome": project_data["cliente_nome"],
            "cliente_sector": project_data.get("cliente_sector", "Não definido"),
            "resumo": project_data.get("resumo", ""),
            "descricao_completa": project_data["descricao_completa"],
            "tags": project_data.get("tags", []),
            "status": project_data.get("status", "Novo"),
            "progresso": project_data.get("progresso", 0),
            "equipe": project_data.get("equipe", "A definir"),
            "justificativa": project_data.get("justificativa", ""),
            "importancia": project_data.get("importancia", "Média"),
            "pontuacao": project_data.get("pontuacao", 0),
            "agente_responsavel": project_data.get("agente_responsavel", "Manual"),
            "area_especialidade": project_data.get("area_especialidade", "Geral"),
            "created_at": datetime.now(UTC),
            "updated_at": datetime.now(UTC),
            "criado_por": "Usuario",
            "origem": "Manual"
        }

        # Inserir no banco
        result = projetos_collection.insert_one(novo_projeto)

        # Notificar via WebSocket
        await websocket_manager.broadcast({
            "type": "project_created",
            "clientId": str(cliente_obj_id),
            "projectId": str(result.inserted_id),
            "projectName": novo_projeto["nome_projeto"],
            "message": f"Novo projeto criado: {novo_projeto['nome_projeto']}",
            "timestamp": datetime.now(UTC).isoformat()
        })

        logger.info(
            f"✅ Projeto criado: {novo_projeto['nome_projeto']} para cliente {project_data['cliente_nome']}")

        return {
            "message": "Projeto criado com sucesso",
            "project_id": str(result.inserted_id),
            "status": "success"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao criar projeto: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao criar projeto: {str(e)}"
        )


@router.get("/clients/{client_id}/projects")
async def get_client_projects(client_id: str):
    """
    📋 Busca todos os projetos de um cliente específico
    """
    try:
        # Validar ObjectId
        client_obj_id = ObjectId(client_id)

        # Buscar projetos do cliente - ASYNC
        projetos_cursor = motor_projetos_collection.find(
            {"cliente_id": client_obj_id},
            {"_id": 1, "nome_projeto": 1, "tags": 1, "status": 1, "resumo": 1,
             "progresso": 1, "equipe": 1, "justificativa": 1, "importancia": 1,
             "pontuacao": 1, "agente_responsavel": 1, "area_especialidade": 1,
             "created_at": 1, "updated_at": 1}
        ).sort("created_at", -1)
        
        projetos = await projetos_cursor.to_list(length=None)

        # Converter ObjectIds para strings
        for projeto in projetos:
            projeto["_id"] = str(projeto["_id"])
            projeto["cliente_id"] = str(projeto["cliente_id"])

            # Converter datas para ISO string se necessário
            if isinstance(projeto.get("created_at"), datetime):
                projeto["created_at"] = projeto["created_at"].isoformat()
            if isinstance(projeto.get("updated_at"), datetime):
                projeto["updated_at"] = projeto["updated_at"].isoformat()

        # Buscar informações do cliente para contexto - ASYNC
        client = await motor_clients_collection.find_one(
            {"_id": client_obj_id},
            {"name": 1, "company": 1, "sector": 1, "status": 1}
        )

        client_info = None
        if client:
            client_info = {
                "id": str(client["_id"]),
                "name": client.get("name", ""),
                "company": client.get("company", ""),
                "sector": client.get("sector", ""),
                "status": client.get("status", "")
            }

        return {
            "client": client_info,
            "projects": projetos,
            "total_projects": len(projetos),
            "projects_by_status": {
                "sugestao": len([p for p in projetos if p.get("status") == "Sugestão"]),
                "em_analise": len([p for p in projetos if p.get("status") == "Em Análise"]),
                "aprovado": len([p for p in projetos if p.get("status") == "Aprovado"]),
                "rejeitado": len([p for p in projetos if p.get("status") == "Rejeitado"]),
                "em_execucao": len([p for p in projetos if p.get("status") == "Em Execução"]),
                "concluido": len([p for p in projetos if p.get("status") == "Concluído"])
            }
        }

    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="ID do cliente inválido"
        )
    except Exception as e:
        logger.error(f"Erro ao buscar projetos do cliente {client_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        )


@router.get("/projects")
async def get_all_projects():
    """
    📋 Busca todos os projetos com informações dos clientes
    """
    try:
        # Pipeline de agregação para juntar projetos com dados dos clientes
        pipeline = [
            {
                "$lookup": {
                    "from": "clients",
                    "localField": "cliente_id",
                    "foreignField": "_id",
                    "as": "cliente"
                }
            },
            {"$unwind": "$cliente"},
            {
                "$project": {
                    "_id": 1,
                    "nome_projeto": 1,
                    "tags": 1,
                    "status": 1,
                    "resumo": 1,
                    "progresso": 1,
                    "equipe": 1,
                    "justificativa": 1,
                    "importancia": 1,
                    "pontuacao": 1,
                    "detalhamento": 1,
                    "beneficios": 1,
                    "agentes_justificativa": 1,
                    "agente_responsavel": 1,
                    "area_especialidade": 1,
                    "created_at": 1,
                    "updated_at": 1,
                    "cliente_id": 1,
                    "cliente_nome": "$cliente.name",
                    "cliente_company": "$cliente.company",
                    "cliente_sector": "$cliente.sector",
                    # 🎯 NOVO: Campo "projeto" estruturado do Team Agno
                    "projeto": 1,
                    # 🎯 Campos de estimativa para detecção no frontend
                    "estimate_status": 1,
                    "estimate_resultado": 1,
                    "estimate_requested": 1,
                    "estimate_solicitada_em": 1,
                    "estimate_atualizada_em": 1,
                    "estimate_error": 1,
                    # Compatibilidade com campos legados
                    "estimativa_status": 1,
                    "estimativa_detalhada": 1,
                    "estimativa_solicitada": 1
                }
            },
            {"$sort": {"created_at": -1}}
        ]

        projetos = await motor_projetos_collection.aggregate(pipeline).to_list(length=None)

        # Converter ObjectIds para strings e adicionar campos de estimativa
        for projeto in projetos:
            projeto["_id"] = str(projeto["_id"])
            projeto["cliente_id"] = str(projeto["cliente_id"])

            # Converter datas para ISO string se necessário
            if isinstance(projeto.get("created_at"), datetime):
                projeto["created_at"] = projeto["created_at"].isoformat()
            if isinstance(projeto.get("updated_at"), datetime):
                projeto["updated_at"] = projeto["updated_at"].isoformat()

            # 🎯 Adicionar campos de estimativa para detecção no frontend
            estimate_status = projeto.get(
                "estimate_status") or projeto.get("estimativa_status")
            estimate_result = projeto.get(
                "estimate_resultado") or projeto.get("estimativa_detalhada")

            # Determinar se tem estimativa detalhada
            has_detailed_estimate = bool(
                estimate_status == "completed" and estimate_result
            )

            projeto["has_detailed_estimate"] = has_detailed_estimate

            # Garantir campos padrão se não existirem
            if not projeto.get("estimate_status"):
                projeto["estimate_status"] = "not_requested"
            if not projeto.get("estimate_requested"):
                projeto["estimate_requested"] = False

        return {
            "projects": projetos,
            "total_projects": len(projetos),
            "projects_by_status": {
                "sugestao": len([p for p in projetos if p.get("status") == "Sugestão"]),
                "em_analise": len([p for p in projetos if p.get("status") == "Em Análise"]),
                "aprovado": len([p for p in projetos if p.get("status") == "Aprovado"]),
                "rejeitado": len([p for p in projetos if p.get("status") == "Rejeitado"]),
                "em_execucao": len([p for p in projetos if p.get("status") == "Em Execução"]),
                "concluido": len([p for p in projetos if p.get("status") == "Concluído"])
            }
        }

    except Exception as e:
        logger.error(f"Erro ao buscar todos os projetos: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        )


# ===================================================================
# 🚀 INCLUSÃO DAS ROTAS AVANÇADAS DE GERAÇÃO DE PROJETOS
# ===================================================================

# Incluir as rotas avançadas no router principal
router.include_router(project_generation_router, tags=[
                      "Geração Avançada de Projetos"])


# ===================================================================
# 🧪 ENDPOINT MANUAL PARA TESTES DO GERADOR APRIMORADO
# ===================================================================

@router.post("/clients/{client_id}/force-enhanced-report")
async def force_enhanced_report(client_id: str):
    """
    🚫 DEPRECATED: Este endpoint está obsoleto desde a implementação do sistema automático de PDFs.

    O sistema agora gera PDFs automaticamente quando:
    1. Dossiê expandido está completo
    2. Pesquisa de mercado está completa

    Use o fluxo normal: complete-report → aguarde conclusão → PDF é gerado automaticamente.

    Para regenerar um PDF, use: POST /clients/{client_id}/generate-enhanced-report
    """
    raise HTTPException(
        status_code=status.HTTP_410_GONE,
        detail={
            "error": "Endpoint obsoleto",
            "message": "PDFs são agora gerados automaticamente após conclusão do dossiê e pesquisa de mercado",
            "deprecated_since": "2024-01-10",
            "replacement": "Fluxo automático via /clients/{client_id}/complete-report",
            "manual_regeneration": "/clients/{client_id}/generate-enhanced-report",
            "new_workflow": {
                "step_1": "POST /clients/{client_id}/complete-report",
                "step_2": "Aguarde WebSocket notification: 'pdf_generation_completed'",
                "step_3": "GET /clients/{client_id}/report para download"
            }
        }
    )


def _create_minimal_expanded_dossier(client_doc: dict) -> dict:
    """Cria um dossiê expandido mínimo baseado nos dados do cliente"""

    nome = client_doc.get("name", "Empresa")
    site = client_doc.get("site", "")
    cidade = client_doc.get("city", "")
    estado = client_doc.get("state", "")
    setor = client_doc.get("sector", client_doc.get("setor", "Indefinido"))

    # Buscar dados dos reports
    dossie_basico = {}

    for report in client_doc.get("reports", []):
        if report.get("reportType") == "dossie_basico":
            dossie_basico = report.get("data", {})

    return {
        "dossie_basico": {
            "nome": nome,
            "site": site,
            "setor": setor,
            "localizacao": f"{cidade}, {estado}" if cidade and estado else "Brasil",
            "descricao": dossie_basico.get("descricao", f"Empresa do setor {setor}"),
            "fundacao": dossie_basico.get("fundacao", "N/A"),
            "tamanho": dossie_basico.get("tamanho", "N/A"),
            **dossie_basico
        },
        "swot_expandida": {
            "forcas": dossie_basico.get("forcas", ["Empresa estabelecida"]),
            "fraquezas": dossie_basico.get("fraquezas", ["A identificar"]),
            "oportunidades": dossie_basico.get("oportunidades", ["Expansão digital"]),
            "ameacas": dossie_basico.get("ameacas", ["Concorrência"])
        },
        "stack_tecnologica": {"resumo": "A analisar", "detalhes": {}},
        "funding_investimentos": {"resumo": "Dados não disponíveis"},
        "presenca_digital": {"website": site, "status": "Análise pendente"},
        "parcerias_estrategicas": {"resumo": "A identificar"},
        "modelo_negocio": {"tipo": "A identificar", "setor": setor},
        "estrategia_pricing": {"resumo": "A analisar"},
        "canais_reviews": {"resumo": "A coletar"},
        "diagnostico_tecnico": {"resumo": "Pendente"},
        "pesquisa_mercado": {"resumo": "Dados incluídos no dossiê expandido", "setor": setor}
    }


@router.post("/clients/{client_id}/generate-enhanced-report")
async def generate_enhanced_report_manual(client_id: str, background_tasks: BackgroundTasks):
    """
    🧪 Endpoint manual para testar o novo gerador de PDF aprimorado
    """
    try:
        client_id_obj = ObjectId(client_id)

        # Buscar cliente
        client = clients_collection.find_one({"_id": client_id_obj})
        if not client:
            raise HTTPException(
                status_code=404, detail="Cliente não encontrado")

        # Verificar se tem expanded_dossier
        if not client.get("expanded_dossier"):
            raise HTTPException(
                status_code=400,
                detail="Cliente não possui dossiê expandido. Execute primeiro o relatório completo."
            )

        # Executar em background (criar wrapper síncrono)
        background_tasks.add_task(
            _processar_enhanced_report_sync,
            client_id_obj,
            client["nome"],
            client.get("site", ""),
            client.get("cidade", ""),
            client.get("estado", "")
        )

        return {
            "message": f"Geração de relatório aprimorado iniciada para {client['nome']}",
            "client_id": client_id,
            "status": "processing"
        }

    except Exception as e:
        logger.error(f"Erro ao iniciar geração de relatório aprimorado: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Erro interno: {str(e)}"
        )


def _processar_enhanced_report_sync(client_id: ObjectId, nome: str, site: str, cidade: str, estado: str):
    """
    Wrapper síncrono para processar_enhanced_report
    """
    asyncio.run(processar_enhanced_report(client_id, nome, site, cidade, estado))


async def processar_enhanced_report(client_id: ObjectId, nome: str, site: str, cidade: str, estado: str):
    """
    Processar geração do relatório executivo aprimorado
    """
    try:
        logger.info(
            f"🚀 Iniciando geração de relatório aprimorado para: {nome}")

        # Notificar início
        await websocket_manager.broadcast({
            "type": "enhanced_report_status",
            "clientId": str(client_id),
            "status": "processing",
            "message": f"🚀 Iniciando geração de relatório executivo aprimorado para {nome}",
            "timestamp": datetime.now(UTC).isoformat(),
            "phase": "enhanced_started"
        })

        # Atualizar status no banco
        clients_collection.update_one(
            {"_id": client_id},
            {
                "$set": {
                    "enhanced_report_status": "processing",
                    "enhanced_report_started_at": datetime.now(UTC),
                    "updated_at": datetime.now(UTC)
                }
            }
        )

        # Buscar dados do cliente
        client = clients_collection.find_one({"_id": client_id})
        if not client or not client.get("expanded_dossier"):
            raise Exception("Cliente ou dossiê expandido não encontrado")

        # Importar e usar o gerador aprimorado
        from tools.reports.pdf_generator_enhanced import AIReportGeneratorEnhanced

        # Notificar progresso
        await websocket_manager.broadcast({
            "type": "enhanced_report_status",
            "clientId": str(client_id),
            "status": "processing",
            "message": "🤖 IA gerando conteúdo executivo detalhado...",
            "timestamp": datetime.now(UTC).isoformat(),
            "phase": "ai_processing"
        })

        # Gerar relatório aprimorado
        generator = AIReportGeneratorEnhanced()
        pdf_content = await asyncio.to_thread(
            generator.generate_report,
            client["expanded_dossier"],
            nome
        )

        # Salvar PDF no GridFS
        fs = GridFS(db)
        pdf_id = fs.put(
            pdf_content,
            filename=f"relatorio_executivo_enhanced_{nome}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
            content_type="application/pdf",
            client_id=str(client_id),
            generated_at=datetime.now(UTC),
            report_type="enhanced_executive"
        )

        # Atualizar no banco
        clients_collection.update_one(
            {"_id": client_id},
            {
                "$set": {
                    "enhanced_report_status": "completed",
                    "enhanced_pdf_id": pdf_id,
                    "enhanced_pdf_generated_at": datetime.now(UTC),
                    "updated_at": datetime.now(UTC)
                }
            }
        )

        # Notificar sucesso
        await websocket_manager.broadcast({
            "type": "enhanced_report_completed",
            "clientId": str(client_id),
            "status": "completed",
            "message": f"✅ Relatório executivo aprimorado concluído para {nome}",
            "timestamp": datetime.now(UTC).isoformat(),
            "phase": "enhanced_completed",
            "pdf_id": str(pdf_id)
        })

        logger.info(f"✅ Relatório aprimorado concluído para {nome}")

    except Exception as e:
        logger.error(
            f"❌ Erro na geração do relatório aprimorado para {nome}: {str(e)}")

        # Atualizar erro no banco
        clients_collection.update_one(
            {"_id": client_id},
            {
                "$set": {
                    "enhanced_report_status": "error",
                    "enhanced_report_error": str(e),
                    "updated_at": datetime.now(UTC)
                }
            }
        )

        # Notificar erro
        await websocket_manager.broadcast({
            "type": "enhanced_report_error",
            "clientId": str(client_id),
            "status": "error",
            "message": f"❌ Erro na geração do relatório aprimorado: {str(e)}",
            "timestamp": datetime.now(UTC).isoformat(),
            "phase": "enhanced_error"
        })


# ⚠️ FUNÇÃO DEPRECATED: processar_analise_team_agno()
# Esta função está obsoleta e não deveria ser usada.
# Use processar_estimativa_projeto() em project_generation_routes.py
async def processar_analise_team_agno(client_id: ObjectId, nome: str, site: str, cidade: str, estado: str, projeto_descricao: str = ""):
    """
    ⚠️ DEPRECATED: Esta função está obsoleta.

    USE INSTEAD: processar_estimativa_projeto() no project_generation_routes.py

    Esta função analisa clientes genéricos em vez de projetos específicos,
    o que não gera estimativas precisas.
    """
    try:
        logger.info(f"🚀 Iniciando análise Team Agno para: {nome}")

        # Atualizar status para Team Agno Analysis
        clients_collection.update_one(
            {"_id": client_id},
            {
                "$set": {
                    "status": "Análise Team Agno",
                    "report_status": "processing_team_agno",
                    "team_agno_started_at": datetime.now(UTC),
                    "updated_at": datetime.now(UTC)
                }
            }
        )

        # Notificar início da análise Team Agno
        await websocket_manager.broadcast({
            "type": "team_agno_status_update",
            "clientId": str(client_id),
            "status": "Análise Team Agno",
            "message": f"🚀 Iniciando análise multi-especializada para {nome}",
            "timestamp": datetime.now(UTC).isoformat(),
            "phase": "team_agno_started",
            "estimated_time": "2-3 minutos"
        })

        # Preparar input para Team Agno
        if not projeto_descricao:
            # Se não tem descrição específica, usar dados do cliente
            projeto_descricao = f"Projeto para {nome}"
            if site:
                projeto_descricao += f" - website: {site}"
            if cidade and estado:
                projeto_descricao += f" - localizada em {cidade}, {estado}"

        # 🤖 EXECUTAR TEAM AGNO
        import sys
        from pathlib import Path

        # Adicionar path se necessário
        current_dir = Path(__file__).parent
        backend_dir = current_dir.parent
        if str(backend_dir) not in sys.path:
            sys.path.insert(0, str(backend_dir))

        # Importar o team configurado
        from teams.project_team import team

        # Notificar que os agentes estão trabalhando
        await websocket_manager.broadcast({
            "type": "team_agno_status_update",
            "clientId": str(client_id),
            "status": "Agentes trabalhando",
            "message": "10 especialistas analisando o projeto...",
            "timestamp": datetime.now(UTC).isoformat(),
            "phase": "agents_working"
        })

        # Executar análise Team Agno
        resultado_agno = await asyncio.to_thread(
            team.run,
            f"""
Cliente: {nome}
    Site: {site}
    Cidade: {cidade} - {estado}
    
    Necessidade do Projeto:
    {projeto_descricao}
    
    Objetivo: Obter estimativa detalhada com arquitetura, tecnologias, 
    cronograma, equipe necessária e custos estimados.
"""
        )

        # Processar resultado e salvar no banco
        team_agno_report = {
            "report_type": "team_agno_analysis",
            "timestamp": datetime.now(UTC).isoformat(),
            "projeto_analisado": projeto_descricao,
            "resultado_coordenador": resultado_agno.content if hasattr(resultado_agno, 'content') else str(resultado_agno),
            "agentes_executados": resultado_agno.formatted_tool_calls if hasattr(resultado_agno, 'formatted_tool_calls') else [],
            "status": "concluido"
        }

        # Salvar resultado no banco
        clients_collection.update_one(
            {"_id": client_id},
            {"$push": {"reports": team_agno_report}}
        )

        # Atualizar status final
        clients_collection.update_one(
            {"_id": client_id},
            {
                "$set": {
                    "status": "Novo",
                    "report_status": "team_agno_complete",
                    "team_agno_finished_at": datetime.now(UTC),
                    "team_agno_requested": False,
                    "updated_at": datetime.now(UTC)
                }
            }
        )

        # Notificar conclusão
        await websocket_manager.broadcast({
            "type": "team_agno_completed",
            "clientId": str(client_id),
            "clientName": nome,
            "status": "Novo",
            "message": f"✅ Análise Team Agno concluída para {nome}",
            "timestamp": datetime.now(UTC).isoformat(),
            "action": "refresh_client",
            "phase": "team_agno_completed"
        })

        logger.info(f"✅ Team Agno Analysis CONCLUÍDA para {nome}")

    except Exception as e:
        logger.error(f"❌ Erro na análise Team Agno para {nome}: {str(e)}")

        # Marcar como erro
        clients_collection.update_one(
            {"_id": client_id},
            {
                "$set": {
                    "status": "Erro Team Agno",
                    "report_status": "team_agno_error",
                    "team_agno_error": str(e),
                    "team_agno_requested": False,
                    "updated_at": datetime.now(UTC)
                }
            }
        )

        # Notificar erro
        await websocket_manager.broadcast({
            "type": "team_agno_error",
            "clientId": str(client_id),
            "message": f"❌ Erro na análise Team Agno: {str(e)}",
            "timestamp": datetime.now(UTC).isoformat()
        })


# ⚠️ FUNÇÃO REMOVIDA: processar_estimativa_projeto()
# Esta função foi movida para project_generation_routes.py
# para manter a separação de responsabilidades


# ⚠️ ROTAS DE PROJETOS REMOVIDAS
# Essas rotas foram movidas para project_generation_routes.py
# para manter a separação de responsabilidades adequada


# 🚀 NOVA ROTA OTIMIZADA PARA GERAÇÃO DE DOSSIÊ
@router.post("/clients/{client_id}/generate-optimized-dossie")
async def generate_optimized_dossie(client_id: str, background_tasks: BackgroundTasks):
    """
    🚀 Gera dossiê ultra-otimizado com paralelização e cache inteligente

    Melhorias de performance:
    - Redução de 18-25 minutos para 6-8 minutos (60-70% mais rápido)
    - Execução paralela de todas as pesquisas
    - Cache inteligente multi-camadas
    - Diagnósticos técnicos paralelos
    - Fallback automático para versão original

    Args:
        client_id: ID do cliente

    Returns:
        JSON com status da operação
    """
    try:
        # Verificar se cliente existe
        try:
            client_obj_id = ObjectId(client_id)
        except:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="ID de cliente inválido"
            )

        client = clients_collection.find_one({"_id": client_obj_id})
        if not client:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Cliente não encontrado"
            )

        nome = client.get("name", "")
        site = client.get("site", "")
        cidade = client.get("city", "")
        estado = client.get("state", "")

        # Verificar se já está processando
        if client.get("report_status") == "processing_optimized":
            return {
                "message": "Dossiê otimizado já está sendo processado",
                "status": "already_processing",
                "client_id": client_id
            }

        # Marcar como processando
        clients_collection.update_one(
            {"_id": client_obj_id},
            {
                "$set": {
                    "report_status": "processing_optimized",
                    "optimized_dossie_started_at": datetime.now(UTC),
                    "updated_at": datetime.now(UTC)
                }
            }
        )

        # Notificar início do processamento otimizado
        await websocket_manager.broadcast({
            "type": "optimized_dossie_started",
            "clientId": client_id,
            "status": "Processando (Otimizado)",
            "message": f"🚀 Iniciando geração ultra-otimizada do dossiê para {nome}",
            "timestamp": datetime.now(UTC).isoformat(),
            "phase": "optimized_started",
            "estimated_time": "6-8 minutos",
            "optimizations": [
                "Pesquisas paralelas",
                "Cache inteligente",
                "Diagnósticos paralelos",
                "Processamento assíncrono"
            ]
        })

        # Executar geração otimizada em background (criar wrapper síncrono)
        background_tasks.add_task(
            _processar_dossie_otimizado_sync,
            client_obj_id,
            nome,
            site,
            cidade,
            estado
        )

        return {
            "message": f"Geração otimizada do dossiê iniciada para {nome}",
            "status": "processing",
            "client_id": client_id,
            "estimated_time": "6-8 minutos",
            "performance_improvement": "60-70% mais rápido que versão original"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Erro ao iniciar geração otimizada: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        )


def _processar_dossie_otimizado_sync(
    client_id: ObjectId,
    nome: str,
    site: str,
    cidade: str,
    estado: str
):
    """
    Wrapper síncrono para _processar_dossie_otimizado
    """
    asyncio.run(_processar_dossie_otimizado(client_id, nome, site, cidade, estado))


async def _processar_dossie_otimizado(
    client_id: ObjectId,
    nome: str,
    site: str,
    cidade: str,
    estado: str
):
    """
    Processa dossiê otimizado em background
    """
    try:
        logger.info(f"🚀 Iniciando processamento otimizado para: {nome}")

        # Notificar progresso - Cache Check
        await websocket_manager.broadcast({
            "type": "optimized_dossie_progress",
            "clientId": str(client_id),
            "status": "Verificando cache",
            "message": "📋 Verificando cache inteligente...",
            "timestamp": datetime.now(UTC).isoformat(),
            "phase": "cache_check",
            "progress": 10
        })

        # Executar geração ultra-otimizada
        dossie_resultado = await optimized_generator.gerar_dossie_ultra_otimizado(
            str(client_id),
            nome,
            site if site else None,
            cidade if cidade else None,
            estado if estado else None
        )

        # Notificar progresso - Consolidação
        await websocket_manager.broadcast({
            "type": "optimized_dossie_progress",
            "clientId": str(client_id),
            "status": "Consolidando resultados",
            "message": "📊 Consolidando dados coletados...",
            "timestamp": datetime.now(UTC).isoformat(),
            "phase": "consolidation",
            "progress": 90
        })

        # Atualizar status final
        clients_collection.update_one(
            {"_id": client_id},
            {
                "$set": {
                    "report_status": "optimized_complete",
                    "optimized_dossie_finished_at": datetime.now(UTC),
                    "updated_at": datetime.now(UTC),
                    "optimized_dossie_data": dossie_resultado
                }
            }
        )

        # Calcular métricas de performance
        processing_time = dossie_resultado.get(
            "performance_metrics", {}).get("total_time", 0)
        efficiency_gain = dossie_resultado.get(
            "performance_metrics", {}).get("efficiency_gain", "N/A")

        # Notificar conclusão
        await websocket_manager.broadcast({
            "type": "optimized_dossie_completed",
            "clientId": str(client_id),
            "status": "Concluído (Otimizado)",
            "message": f"✅ Dossiê otimizado concluído para {nome}",
            "timestamp": datetime.now(UTC).isoformat(),
            "phase": "optimized_completed",
            "progress": 100,
            "performance_metrics": {
                "processing_time_seconds": processing_time,
                "efficiency_gain": efficiency_gain,
                "optimizations_used": dossie_resultado.get("metadata", {}).get("optimizations", [])
            }
        })

        logger.info(
            f"✅ Dossiê otimizado concluído para {nome} em {processing_time:.2f}s")

    except Exception as e:
        logger.error(
            f"❌ Erro no processamento otimizado para {nome}: {str(e)}")

        # Atualizar erro no banco
        clients_collection.update_one(
            {"_id": client_id},
            {
                "$set": {
                    "report_status": "optimized_error",
                    "optimized_dossie_error": str(e),
                    "updated_at": datetime.now(UTC)
                }
            }
        )

        # Notificar erro
        await websocket_manager.broadcast({
            "type": "optimized_dossie_error",
            "clientId": str(client_id),
            "status": "Erro (Otimizado)",
            "message": f"❌ Erro na geração otimizada: {str(e)}",
            "timestamp": datetime.now(UTC).isoformat(),
            "phase": "optimized_error"
        })


@router.get("/performance/stats")
async def get_performance_stats():
    """
    📊 Retorna estatísticas de performance do sistema otimizado

    Returns:
        JSON com métricas de cache, performance e status do sistema
    """
    try:
        # Obter estatísticas do sistema otimizado
        stats = await optimized_generator.get_performance_stats()

        # Adicionar estatísticas do banco de dados
        total_clients = clients_collection.count_documents({})
        optimized_reports = clients_collection.count_documents(
            {"report_status": "optimized_complete"})
        processing_reports = clients_collection.count_documents(
            {"report_status": "processing_optimized"})

        # Calcular tempo médio de processamento otimizado
        pipeline = [
            {"$match": {"optimized_dossie_data.performance_metrics.total_time": {
                "$exists": True}}},
            {"$group": {
                "_id": None,
                "avg_time": {"$avg": "$optimized_dossie_data.performance_metrics.total_time"},
                "min_time": {"$min": "$optimized_dossie_data.performance_metrics.total_time"},
                "max_time": {"$max": "$optimized_dossie_data.performance_metrics.total_time"},
                "count": {"$sum": 1}
            }}
        ]

        performance_stats = list(clients_collection.aggregate(pipeline))
        avg_processing_time = performance_stats[0] if performance_stats else {}

        return {
            "system_performance": stats,
            "database_stats": {
                "total_clients": total_clients,
                "optimized_reports_completed": optimized_reports,
                "currently_processing": processing_reports,
                "optimization_adoption_rate": f"{(optimized_reports / max(total_clients, 1) * 100):.1f}%"
            },
            "processing_metrics": {
                "average_time_seconds": avg_processing_time.get("avg_time", 0),
                "fastest_time_seconds": avg_processing_time.get("min_time", 0),
                "slowest_time_seconds": avg_processing_time.get("max_time", 0),
                "total_optimized_reports": avg_processing_time.get("count", 0)
            },
            "timestamp": datetime.now(UTC).isoformat()
        }

    except Exception as e:
        logger.error(f"❌ Erro ao obter estatísticas de performance: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro ao obter estatísticas: {str(e)}"
        )


@router.post("/cache/invalidate/{empresa}")
async def invalidate_company_cache(empresa: str):
    """
    🗑️ Invalida cache de uma empresa específica

    Args:
        empresa: Nome da empresa para invalidar cache

    Returns:
        JSON com número de chaves removidas
    """
    try:
        from .cache_manager import cache_manager

        removed_count = cache_manager.invalidate_company(empresa)

        return {
            "message": f"Cache invalidado para empresa: {empresa}",
            "keys_removed": removed_count,
            "timestamp": datetime.now(UTC).isoformat()
        }

    except Exception as e:
        logger.error(f"❌ Erro ao invalidar cache: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro ao invalidar cache: {str(e)}"
        )
