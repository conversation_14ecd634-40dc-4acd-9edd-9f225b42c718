# Tasks - Roadmap ScopeAI

**Última Atualização**: 2025-01-29

## 📊 Resumo Executivo

- **<PERSON>refas Ativas**: 1
- **Tarefas Pendentes**: 5
- **<PERSON><PERSON><PERSON><PERSON>**: 0
- **Progresso Total**: 10% (4/40h concluídas)

## 🎯 Foco Atual

### [TASK-001] Migração para Microserviços - Fase 1: Client Service

**Status**: 🔄 EM ANDAMENTO  
**Prioridade**: P1  
**Estimativa**: 40h  
**Progresso**: 10% (4h/40h)

**Microtarefas**:
1. [x] Draft & review micro-task prompt ✅ (15min - Score: 10/10)
2. [x] Reasoning Prelude (ToT) ✅ (45min - Score: 9/10)
3. [x] Repo scan - mapear dependências ✅ (2h - Score: 9/10)
4. [x] Criar estrutura do microserviço ✅ (1h - Score: 10/10)
5. [ ] Implementar domain layer (DDD)
6. [ ] Implementar application layer
7. [ ] Implementar infrastructure layer
8. [ ] Criar API REST completa
9. [ ] Implementar testes (≥80% coverage)
10. [ ] Documentar APIs (OpenAPI)
11. [ ] Self-score & log

**Decisões Arquiteturais**:
- ✅ Clean Architecture com layers horizontais
- ✅ Comunicação híbrida (REST + eventos)
- ✅ Database transitório com migração gradual
- ✅ API versioning via URL path
- ✅ **Configuração unificada**: Reutilizar infra do backend (sem duplicação)

**Documentos Criados**:
- ✅ [Dependency Mapping](../microservices/client-service/docs/dependency-mapping.md)
- ✅ Estrutura base do microserviço criada
- ✅ README.md atualizado com instruções de uso

**Arquivos Modificados**:
- `/backend/microservices/client-service/src/config.py` - Mapeamento de variáveis
- `/backend/microservices/client-service/src/api/main.py` - FastAPI base
- `.projectrules` - Nova regra sobre reutilização de configs

**Arquivos Detalhados**: 
- ➜ [2025_01_29__15-00__migracao_microservicos_fase1.md](./2025_01_29__15-00__migracao_microservicos_fase1.md)

---

## 📋 Backlog Priorizado

### [TASK-002] API Gateway Inteligente
**Status**: ⏳ PENDENTE  
**Prioridade**: P1  
**Estimativa**: 24h  
**Dependências**: TASK-001

**Objetivo**: Transformar routes.py em API Gateway com roteamento inteligente, circuit breaker e rate limiting.

---

### [TASK-003] Message Broker Setup
**Status**: ⏳ PENDENTE  
**Prioridade**: P1  
**Estimativa**: 16h  
**Dependências**: TASK-001

**Objetivo**: Implementar RabbitMQ para comunicação assíncrona entre microserviços.

---

### [TASK-004] Project Service Migration
**Status**: ⏳ PENDENTE  
**Prioridade**: P1  
**Estimativa**: 32h  
**Dependências**: TASK-001, TASK-002

**Objetivo**: Extrair toda lógica de projetos para microserviço independente.

---

### [TASK-005] Docker Compose Microservices
**Status**: ⏳ PENDENTE  
**Prioridade**: P2  
**Estimativa**: 8h  
**Dependências**: TASK-001

**Objetivo**: Configurar ambiente de desenvolvimento com todos os microserviços.

---

### [TASK-006] CI/CD Pipeline
**Status**: ⏳ PENDENTE  
**Prioridade**: P2  
**Estimativa**: 16h  
**Dependências**: TASK-005

**Objetivo**: Implementar GitHub Actions para build, test e deploy automatizados.

---

## 📈 Métricas

### Velocidade
- **Sprint Atual**: 0 story points
- **Média**: N/A
- **Capacidade**: 40h/semana

### Qualidade
- **Bugs Encontrados**: 0
- **Coverage Médio**: 85%
- **Score Médio**: 9.5/10 (MT-1 a MT-4)

### Riscos
1. 🔴 **Complexidade da Migração**: Manter sistema funcionando durante transição
2. 🟡 **Dependências Externas**: APIs podem mudar
3. 🟡 **Performance**: Latência entre microserviços

---

## 🔗 Links Úteis

- [Memory Bank Principal](../README.md)
- [Arquivos Arquivados](../archive/index.md)
- [Padrões do Sistema](../systemPatterns.md)
- [Contexto Técnico](../techContext.md) 