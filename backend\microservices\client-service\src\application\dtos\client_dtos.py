"""DTOs relacionados a clientes."""

from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field, HttpUrl, validator


class CreateClientDTO(BaseModel):
    """DTO para criação de cliente."""

    name: str = Field(..., min_length=1, max_length=255,
                      description="Nome do cliente")
    url: HttpUrl = Field(..., description="URL do site do cliente")
    context: str = Field(..., min_length=50, max_length=5000,
                         description="Contexto/descrição do cliente")

    @validator('name')
    def validate_name(cls, v: str) -> str:
        """Remove espaços extras do nome."""
        return ' '.join(v.split())

    @validator('context')
    def validate_context(cls, v: str) -> str:
        """Remove espaços extras do contexto."""
        return ' '.join(v.split())

    class Config:
        """Configuração do Pydantic."""
        schema_extra = {
            "example": {
                "name": "Empresa Exemplo",
                "url": "https://empresa-exemplo.com.br",
                "context": "Empresa de tecnologia especializada em desenvolvimento de software para o setor financeiro brasileiro"
            }
        }


class UpdateClientDTO(BaseModel):
    """DTO para atualização de cliente."""

    name: Optional[str] = Field(
        None, min_length=1, max_length=255, description="Novo nome do cliente")
    context: Optional[str] = Field(
        None, min_length=50, max_length=5000, description="Novo contexto do cliente")

    @validator('name')
    def validate_name(cls, v: Optional[str]) -> Optional[str]:
        """Remove espaços extras do nome se fornecido."""
        if v:
            return ' '.join(v.split())
        return v

    @validator('context')
    def validate_context(cls, v: Optional[str]) -> Optional[str]:
        """Remove espaços extras do contexto se fornecido."""
        if v:
            return ' '.join(v.split())
        return v

    class Config:
        """Configuração do Pydantic."""
        schema_extra = {
            "example": {
                "name": "Empresa Exemplo Atualizada",
                "context": "Novo contexto com informações atualizadas sobre a empresa"
            }
        }


class ClientResponseDTO(BaseModel):
    """DTO de resposta com dados completos do cliente."""

    id: str = Field(..., description="ID único do cliente")
    name: str = Field(..., description="Nome do cliente")
    url: str = Field(..., description="URL do site do cliente")
    context: str = Field(..., description="Contexto/descrição do cliente")
    status: str = Field(..., description="Status atual do cliente")
    created_at: datetime = Field(..., description="Data de criação")
    updated_at: datetime = Field(..., description="Data da última atualização")
    version: int = Field(...,
                         description="Versão para controle de concorrência")
    is_active: bool = Field(..., description="Se o cliente está ativo")
    is_archived: bool = Field(..., description="Se o cliente está arquivado")
    has_analysis_in_progress: bool = Field(...,
                                           description="Se há análise em progresso")
    days_since_last_activity: int = Field(...,
                                          description="Dias desde última atividade")
    successful_analyses_count: int = Field(...,
                                           description="Número de análises bem-sucedidas")

    class Config:
        """Configuração do Pydantic."""
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
        schema_extra = {
            "example": {
                "id": "123e4567-e89b-12d3-a456-426614174000",
                "name": "Empresa Exemplo",
                "url": "https://empresa-exemplo.com.br",
                "context": "Empresa de tecnologia...",
                "status": "completed",
                "created_at": "2024-01-01T10:00:00",
                "updated_at": "2024-01-01T12:00:00",
                "version": 1,
                "is_active": True,
                "is_archived": False,
                "has_analysis_in_progress": False,
                "days_since_last_activity": 0,
                "successful_analyses_count": 2
            }
        }


class ClientListItemDTO(BaseModel):
    """DTO simplificado para listagem de clientes."""

    id: str = Field(..., description="ID único do cliente")
    name: str = Field(..., description="Nome do cliente")
    url: str = Field(..., description="URL do site do cliente")
    status: str = Field(..., description="Status atual do cliente")
    created_at: datetime = Field(..., description="Data de criação")
    updated_at: datetime = Field(..., description="Data da última atualização")
    is_active: bool = Field(..., description="Se o cliente está ativo")

    class Config:
        """Configuração do Pydantic."""
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ClientListResponseDTO(BaseModel):
    """DTO de resposta para listagem paginada de clientes."""

    items: List[ClientListItemDTO] = Field(...,
                                           description="Lista de clientes")
    total: int = Field(..., description="Total de clientes")
    page: int = Field(..., ge=1, description="Página atual")
    page_size: int = Field(..., ge=1, le=100, description="Tamanho da página")
    total_pages: int = Field(..., ge=0, description="Total de páginas")
    has_next: bool = Field(..., description="Se há próxima página")
    has_previous: bool = Field(..., description="Se há página anterior")

    class Config:
        """Configuração do Pydantic."""
        schema_extra = {
            "example": {
                "items": [],
                "total": 50,
                "page": 1,
                "page_size": 20,
                "total_pages": 3,
                "has_next": True,
                "has_previous": False
            }
        }
