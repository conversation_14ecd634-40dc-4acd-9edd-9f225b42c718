{"ast": null, "code": "// src/zindex/index.ts\nfunction handler() {\n  let zIndexes = [];\n  const generateZIndex = (key, autoZIndex, baseZIndex = 999) => {\n    const lastZIndex = getLastZIndex(key, autoZIndex, baseZIndex);\n    const newZIndex = lastZIndex.value + (lastZIndex.key === key ? 0 : baseZIndex) + 1;\n    zIndexes.push({\n      key,\n      value: newZIndex\n    });\n    return newZIndex;\n  };\n  const revertZIndex = zIndex => {\n    zIndexes = zIndexes.filter(obj => obj.value !== zIndex);\n  };\n  const getCurrentZIndex = (key, autoZIndex) => {\n    return getLastZIndex(key, autoZIndex).value;\n  };\n  const getLastZIndex = (key, autoZIndex, baseZIndex = 0) => {\n    return [...zIndexes].reverse().find(obj => autoZIndex ? true : obj.key === key) || {\n      key,\n      value: baseZIndex\n    };\n  };\n  const getZIndex = element => {\n    return element ? parseInt(element.style.zIndex, 10) || 0 : 0;\n  };\n  return {\n    get: getZIndex,\n    set: (key, element, baseZIndex) => {\n      if (element) {\n        element.style.zIndex = String(generateZIndex(key, true, baseZIndex));\n      }\n    },\n    clear: element => {\n      if (element) {\n        revertZIndex(getZIndex(element));\n        element.style.zIndex = \"\";\n      }\n    },\n    getCurrent: key => getCurrentZIndex(key, true)\n  };\n}\nvar ZIndex = handler();\nexport { ZIndex };", "map": {"version": 3, "names": ["handler", "zIndexes", "generateZIndex", "key", "autoZIndex", "baseZIndex", "lastZIndex", "getLastZIndex", "newZIndex", "value", "push", "revertZIndex", "zIndex", "filter", "obj", "getCurrentZIndex", "reverse", "find", "getZIndex", "element", "parseInt", "style", "get", "set", "String", "clear", "get<PERSON>urrent", "ZIndex"], "sources": ["C:/projetos/scope-ai/frontend/node_modules/@primeuix/utils/zindex/index.mjs"], "sourcesContent": ["// src/zindex/index.ts\nfunction handler() {\n  let zIndexes = [];\n  const generateZIndex = (key, autoZIndex, baseZIndex = 999) => {\n    const lastZIndex = getLastZIndex(key, autoZIndex, baseZIndex);\n    const newZIndex = lastZIndex.value + (lastZIndex.key === key ? 0 : baseZIndex) + 1;\n    zIndexes.push({ key, value: newZIndex });\n    return newZIndex;\n  };\n  const revertZIndex = (zIndex) => {\n    zIndexes = zIndexes.filter((obj) => obj.value !== zIndex);\n  };\n  const getCurrentZIndex = (key, autoZIndex) => {\n    return getLastZIndex(key, autoZIndex).value;\n  };\n  const getLastZIndex = (key, autoZIndex, baseZIndex = 0) => {\n    return [...zIndexes].reverse().find((obj) => autoZIndex ? true : obj.key === key) || { key, value: baseZIndex };\n  };\n  const getZIndex = (element) => {\n    return element ? parseInt(element.style.zIndex, 10) || 0 : 0;\n  };\n  return {\n    get: getZIndex,\n    set: (key, element, baseZIndex) => {\n      if (element) {\n        element.style.zIndex = String(generateZIndex(key, true, baseZIndex));\n      }\n    },\n    clear: (element) => {\n      if (element) {\n        revertZIndex(getZIndex(element));\n        element.style.zIndex = \"\";\n      }\n    },\n    getCurrent: (key) => getCurrentZIndex(key, true)\n  };\n}\nvar ZIndex = handler();\nexport {\n  ZIndex\n};\n"], "mappings": "AAAA;AACA,SAASA,OAAOA,CAAA,EAAG;EACjB,IAAIC,QAAQ,GAAG,EAAE;EACjB,MAAMC,cAAc,GAAGA,CAACC,GAAG,EAAEC,UAAU,EAAEC,UAAU,GAAG,GAAG,KAAK;IAC5D,MAAMC,UAAU,GAAGC,aAAa,CAACJ,GAAG,EAAEC,UAAU,EAAEC,UAAU,CAAC;IAC7D,MAAMG,SAAS,GAAGF,UAAU,CAACG,KAAK,IAAIH,UAAU,CAACH,GAAG,KAAKA,GAAG,GAAG,CAAC,GAAGE,UAAU,CAAC,GAAG,CAAC;IAClFJ,QAAQ,CAACS,IAAI,CAAC;MAAEP,GAAG;MAAEM,KAAK,EAAED;IAAU,CAAC,CAAC;IACxC,OAAOA,SAAS;EAClB,CAAC;EACD,MAAMG,YAAY,GAAIC,MAAM,IAAK;IAC/BX,QAAQ,GAAGA,QAAQ,CAACY,MAAM,CAAEC,GAAG,IAAKA,GAAG,CAACL,KAAK,KAAKG,MAAM,CAAC;EAC3D,CAAC;EACD,MAAMG,gBAAgB,GAAGA,CAACZ,GAAG,EAAEC,UAAU,KAAK;IAC5C,OAAOG,aAAa,CAACJ,GAAG,EAAEC,UAAU,CAAC,CAACK,KAAK;EAC7C,CAAC;EACD,MAAMF,aAAa,GAAGA,CAACJ,GAAG,EAAEC,UAAU,EAAEC,UAAU,GAAG,CAAC,KAAK;IACzD,OAAO,CAAC,GAAGJ,QAAQ,CAAC,CAACe,OAAO,CAAC,CAAC,CAACC,IAAI,CAAEH,GAAG,IAAKV,UAAU,GAAG,IAAI,GAAGU,GAAG,CAACX,GAAG,KAAKA,GAAG,CAAC,IAAI;MAAEA,GAAG;MAAEM,KAAK,EAAEJ;IAAW,CAAC;EACjH,CAAC;EACD,MAAMa,SAAS,GAAIC,OAAO,IAAK;IAC7B,OAAOA,OAAO,GAAGC,QAAQ,CAACD,OAAO,CAACE,KAAK,CAACT,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC;EAC9D,CAAC;EACD,OAAO;IACLU,GAAG,EAAEJ,SAAS;IACdK,GAAG,EAAEA,CAACpB,GAAG,EAAEgB,OAAO,EAAEd,UAAU,KAAK;MACjC,IAAIc,OAAO,EAAE;QACXA,OAAO,CAACE,KAAK,CAACT,MAAM,GAAGY,MAAM,CAACtB,cAAc,CAACC,GAAG,EAAE,IAAI,EAAEE,UAAU,CAAC,CAAC;MACtE;IACF,CAAC;IACDoB,KAAK,EAAGN,OAAO,IAAK;MAClB,IAAIA,OAAO,EAAE;QACXR,YAAY,CAACO,SAAS,CAACC,OAAO,CAAC,CAAC;QAChCA,OAAO,CAACE,KAAK,CAACT,MAAM,GAAG,EAAE;MAC3B;IACF,CAAC;IACDc,UAAU,EAAGvB,GAAG,IAAKY,gBAAgB,CAACZ,GAAG,EAAE,IAAI;EACjD,CAAC;AACH;AACA,IAAIwB,MAAM,GAAG3B,OAAO,CAAC,CAAC;AACtB,SACE2B,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}