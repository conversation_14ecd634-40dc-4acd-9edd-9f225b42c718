"""
Architect Team - Time Agno para análise arquitetural completa

Time coordenado que integra todos os agentes especializados:
- Coordena Performance, Accessibility, SEO e Security agents
- Consolida análises em recomendações arquiteturais
- Prioriza implementação baseada em impacto
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone
from agno.team import Team

from ...shared.model_config import model_config
from ..agents import PerformanceAgent, AccessibilityAgent, SEOSpecialistAgent, SecurityAgent
from ..tools import VisualAnalysisTools, ArchitecturalRecommendationTools
from ..tools.recommendation_tools import RecommendationInput
from ..schemas import TechnicalDiagnosisInput, AnalysisResult, ArchitecturalRecommendation

logger = logging.getLogger(__name__)


class ArchitectTeam:
    """
    Time Agno para análise arquitetural completa

    Coordena agentes especializados para análise abrangente
    e geração de recomendações arquiteturais priorizadas.
    
    USA APENAS MODELOS DO SETTINGS.PY - SEM CLAUDE/ANTHROPIC
    """

    def __init__(self, model_name: Optional[str] = None):
        """
        Inicializa o Time Arquiteto usando modelos do settings.py

        Args:
            model_name: Nome específico do modelo (opcional, usa automático se None)
        """
        # Obter configuração de modelo baseada no settings.py
        agno_config = model_config.create_agno_config("analysis")
        
        if model_name:
            # Se modelo específico fornecido, tentar usar (mas validar se está no settings)
            if model_name in [model_config.get_openai_model(), model_config.get_cohere_model(), model_config.get_mistral_model()]:
                self.model_name = model_name
            else:
                logger.warning(f"Modelo {model_name} não está no settings.py, usando automático")
                self.model_name = agno_config["model_id"]
        else:
            self.model_name = agno_config["model_id"]
        
        logger.info(f"Architect Team usando modelo: {agno_config['provider']}:{self.model_name}")
        
        # Inicializar agentes especializados com mesmo modelo
        self.performance_agent = PerformanceAgent(self.model_name)
        self.accessibility_agent = AccessibilityAgent(self.model_name)
        self.seo_agent = SEOSpecialistAgent(self.model_name)
        self.security_agent = SecurityAgent(self.model_name)

        # Tools complementares
        self.visual_tools = VisualAnalysisTools()
        self.recommendation_tools = ArchitecturalRecommendationTools()

        # Criar modelo Agno baseado no provider
        if agno_config["provider"] == "openai":
            from agno.models.openai import OpenAI
            model_instance = OpenAI(id=self.model_name, api_key=agno_config["api_key"])
        elif agno_config["provider"] == "cohere":
            from agno.models.cohere import Cohere
            model_instance = Cohere(id=self.model_name, api_key=agno_config["api_key"])
        elif agno_config["provider"] == "mistral":
            from agno.models.mistral import Mistral
            model_instance = Mistral(id=self.model_name, api_key=agno_config["api_key"])
        else:
            # Fallback para OpenAI
            from agno.models.openai import OpenAI
            model_instance = OpenAI(id="gpt-4o-mini", api_key=agno_config["api_key"])
            logger.warning(f"Provider {agno_config['provider']} não suportado, usando OpenAI fallback")

        # Criar Team Agno em modo coordinate (sem members iniciais)
        self.team = Team(
            name="Technical Architecture Team",
            mode="coordinate",
            members=[],  # Empty members list - team leader coordinates
            model=model_instance,
            description="Coordinated team of technical specialists providing comprehensive architectural analysis",
            instructions=[
                "You are the lead architect coordinating specialized technical analyses",
                "Synthesize insights from performance, accessibility, SEO, and security specialists",
                "Provide executive summary with prioritized recommendations",
                "Focus on business impact and implementation feasibility",
                "Create actionable roadmap for technical improvements"
            ],
            markdown=True,
            show_tool_calls=True
        )

        logger.info(f"Architect Team inicializado com {agno_config['provider']}:{self.model_name}")

    def analyze_complete(self, technical_diagnosis: TechnicalDiagnosisInput) -> AnalysisResult:
        """
        Análise arquitetural completa usando todo o time

        Args:
            technical_diagnosis: Dados técnicos consolidados

        Returns:
            Resultado completo da análise arquitetural
        """
        try:
            logger.info(
                "Iniciando análise arquitetural completa com Architect Team")

            # 1. Análises especializadas pelos agentes
            analyses = self._run_specialized_analyses(technical_diagnosis)

            # 2. Análise visual complementar
            visual_insights = self._analyze_visual_data(
                technical_diagnosis.visual_analysis)

            # 3. Geração de recomendações consolidadas
            recommendations = self._generate_consolidated_recommendations(
                analyses, visual_insights, technical_diagnosis)

            # 4. Síntese final pelo Team Leader
            executive_summary = self._create_executive_summary(
                analyses, recommendations, technical_diagnosis)

            # 5. Construir resultado final
            analysis_result = self._build_final_result(
                technical_diagnosis,
                recommendations,
                executive_summary,
                analyses
            )

            logger.info(
                f"Análise completa concluída: {analysis_result.analysis_id}")
            return analysis_result

        except Exception as e:
            logger.error(f"Erro na análise completa: {str(e)}")
            raise

    def _run_specialized_analyses(self, technical_diagnosis: TechnicalDiagnosisInput) -> Dict[str, Any]:
        """Executa análises especializadas com cada agente"""
        try:
            company_context = technical_diagnosis.company_metadata
            lighthouse_data = technical_diagnosis.lighthouse_data

            analyses = {}

            # Performance Analysis
            logger.info("Executando análise de performance...")
            analyses["performance"] = self.performance_agent.analyze_performance(
                lighthouse_data, company_context
            )

            # Accessibility Analysis
            logger.info("Executando análise de acessibilidade...")
            analyses["accessibility"] = self.accessibility_agent.analyze_accessibility(
                lighthouse_data, company_context
            )

            # SEO Analysis
            logger.info("Executando análise de SEO...")
            analyses["seo"] = self.seo_agent.analyze_seo_strategy(
                lighthouse_data, company_context
            )

            # Security Analysis
            logger.info("Executando análise de segurança...")
            analyses["security"] = self.security_agent.analyze_security(
                lighthouse_data, company_context
            )

            return analyses

        except Exception as e:
            logger.error(f"Erro nas análises especializadas: {str(e)}")
            raise

    def _analyze_visual_data(self, visual_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Processa dados de análise visual"""
        try:
            visual_insights = self.visual_tools.analyze_visual_data(
                visual_analysis)
            visual_priorities = self.visual_tools.get_visual_priorities(
                visual_analysis)
            mobile_readiness = self.visual_tools.assess_mobile_readiness(
                visual_analysis)

            return {
                "insights": visual_insights.dict(),
                "priorities": visual_priorities,
                "mobile_readiness": mobile_readiness
            }

        except Exception as e:
            logger.error(f"Erro na análise visual: {str(e)}")
            return {"insights": {}, "priorities": [], "mobile_readiness": {}}

    def _generate_consolidated_recommendations(self, analyses: Dict[str, Any], visual_insights: Dict[str, Any], technical_diagnosis: TechnicalDiagnosisInput) -> Dict[str, Any]:
        """Gera recomendações consolidadas usando as tools"""
        try:
            # Extrair métricas das análises especializadas
            lighthouse_metrics = analyses["performance"]["lighthouse_metrics"]

            # Preparar input para recommendation tools
            recommendation_input = RecommendationInput(
                lighthouse_metrics=lighthouse_metrics,
                visual_insights=visual_insights["insights"],
                company_context=technical_diagnosis.company_metadata,
                business_priorities=[]  # Pode ser extraído do contexto
            )

            # Gerar recomendações
            recommendations = self.recommendation_tools.generate_recommendations(
                recommendation_input)
            timeline = self.recommendation_tools.calculate_implementation_timeline(
                recommendations)

            return {
                "technical_recommendations": [rec.dict() for rec in recommendations],
                "implementation_timeline": timeline,
                "total_recommendations": len(recommendations)
            }

        except Exception as e:
            logger.error(f"Erro na geração de recomendações: {str(e)}")
            return {"technical_recommendations": [], "implementation_timeline": "Indeterminado", "total_recommendations": 0}

    def _create_executive_summary(self, analyses: Dict[str, Any], recommendations: Dict[str, Any], technical_diagnosis: TechnicalDiagnosisInput) -> str:
        """Cria síntese executiva usando Team Leader"""
        try:
            company_name = technical_diagnosis.company_metadata.get(
                "name", "a empresa")
            overall_score = technical_diagnosis.consolidated_report.get(
                "score_geral", 0)

            # Preparar contexto para síntese
            synthesis_prompt = f"""
Como líder do time de arquitetura técnica, sintetize as análises especializadas para {company_name}:

**SCORE GERAL ATUAL:** {overall_score}/100

**ANÁLISES ESPECIALIZADAS:**

**Performance (Score: {analyses['performance']['lighthouse_metrics']['performance_score']}/100):**
- Issues críticos: {len(analyses['performance']['critical_issues'])}
- Prioridade: {analyses['performance']['priority_score']}/100

**Acessibilidade (Score: {analyses['accessibility']['accessibility_score']}/100):**
- Nível WCAG: {analyses['accessibility']['wcag_level']}
- Issues: {len(analyses['accessibility']['accessibility_issues'])}

**SEO (Score: {analyses['seo']['seo_score']}/100):**
- Status: {analyses['seo']['seo_health']}
- Impacto performance: {analyses['seo']['performance_impact']}/100

**Segurança (Score: {analyses['security']['best_practices_score']}/100):**
- Nível: {analyses['security']['security_level']}
- HTTPS: {'Ativo' if analyses['security']['https_enabled'] else 'Inativo'}

**RECOMENDAÇÕES GERADAS:** {recommendations['total_recommendations']} recomendações
**TIMELINE ESTIMADO:** {recommendations['implementation_timeline']}

Forneça um **EXECUTIVE SUMMARY** incluindo:
1. **Status arquitetural atual** (pontos fortes e fracos)
2. **Prioridades críticas** (top 3 mais urgentes)  
3. **Impacto no negócio** das melhorias propostas
4. **Roadmap de implementação** (fases recomendadas)
5. **ROI esperado** das melhorias

Seja conciso, executivo e focado em decisões de negócio.
"""

            # Gerar síntese com Team Leader
            synthesis_response = self.team.run(synthesis_prompt)

            # Garantir retorno de string sempre
            if synthesis_response and hasattr(synthesis_response, 'content') and synthesis_response.content:
                return str(synthesis_response.content)
            else:
                return "Síntese executiva não pôde ser gerada pelo team leader"

        except Exception as e:
            logger.error(f"Erro na síntese executiva: {str(e)}")
            return "Erro na geração da síntese executiva"

    def _build_final_result(self, technical_diagnosis: TechnicalDiagnosisInput, recommendations: Dict[str, Any], executive_summary: str, analyses: Dict[str, Any]) -> AnalysisResult:
        """Constrói resultado final da análise"""
        try:
            # Gerar ID único
            analysis_id = f"arch_team_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}"

            # Extrair problemas identificados
            identified_problems = []
            for analysis in analyses.values():
                if "critical_issues" in analysis:
                    identified_problems.extend(analysis["critical_issues"])
                if "accessibility_issues" in analysis:
                    identified_problems.extend(
                        analysis["accessibility_issues"])
                if "security_issues" in analysis:
                    identified_problems.extend(analysis["security_issues"])

            # Construir recomendação arquitetural
            architectural_recommendation = ArchitecturalRecommendation(
                identified_problems=identified_problems,
                current_overall_score=float(
                    technical_diagnosis.consolidated_report.get("score_geral", 0)),
                business_impact=f"Análise completa identificou {len(identified_problems)} problemas críticos",
                technical_recommendations=recommendations["technical_recommendations"],
                implementation_timeline=recommendations["implementation_timeline"],
                expected_improvements={
                    "performance": f"+{max(10, 100-analyses['performance']['lighthouse_metrics']['performance_score'])} pontos",
                    "accessibility": f"+{max(5, 100-analyses['accessibility']['accessibility_score'])} pontos",
                    "seo": f"+{max(5, 100-analyses['seo']['seo_score'])} pontos"
                },
                architectural_principles=[
                    "Mobile-first design",
                    "Progressive enhancement",
                    "Performance budgets",
                    "Accessibility-first development",
                    "SEO-driven architecture"
                ]
            )

            # Construir resultado final
            analysis_result = AnalysisResult(
                analysis_id=analysis_id,
                company_info=technical_diagnosis.company_metadata,
                recommendations=architectural_recommendation,
                analysis_metadata={
                    "team_composition": ["PerformanceAgent", "AccessibilityAgent", "SEOSpecialistAgent", "SecurityAgent"],
                    "coordination_method": "Agno Team coordinate mode",
                    "executive_summary": executive_summary,
                    "specialized_analyses": analyses,
                    "total_analysis_time": "Completed",
                    "model_used": self.model_name
                },
                confidence_score=0.95,  # High confidence with multiple specialized analyses
                created_at=datetime.now(timezone.utc),
                agent_version="1.0-agno"
            )

            return analysis_result

        except Exception as e:
            logger.error(f"Erro na construção do resultado final: {str(e)}")
            raise

    def get_team_status(self) -> Dict[str, Any]:
        """Retorna status do time e agentes"""
        return {
            "team_name": "Technical Architecture Team",
            "coordination_mode": "coordinate",
            "agents": {
                "performance": "PerformanceAgent - Ativo",
                "accessibility": "AccessibilityAgent - Ativo",
                "seo": "SEOSpecialistAgent - Ativo",
                "security": "SecurityAgent - Ativo"
            },
            "tools": {
                "visual_analysis": "VisualAnalysisTools - Ativo",
                "recommendations": "ArchitecturalRecommendationTools - Ativo"
            },
            "team_leader": f"{self.model_name} (Agno Team)",
            "status": "Operational"
        }
