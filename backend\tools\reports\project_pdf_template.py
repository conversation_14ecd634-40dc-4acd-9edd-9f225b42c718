"""
Template HTML profissional para relatórios de projetos em PDF
Utiliza design moderno, responsivo e focado na experiência profissional
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional, List

logger = logging.getLogger(__name__)


class ProjectPDFTemplate:
    """Gerador de templates HTML para relatórios de projetos"""
    
    def __init__(self):
        """Inicializa o gerador de templates"""
        self.logger = logger
        
    def generate_project_html(
        self, 
        project_data: Dict[str, Any], 
        estimate_data: Dict[str, Any], 
        metadata: Dict[str, Any]
    ) -> str:
        """
        Gera HTML completo para o relatório do projeto
        
        Args:
            project_data: Dados do projeto
            estimate_data: Dados da estimativa
            metadata: Metadados do relatório
            
        Returns:
            String contendo HTML completo
        """
        try:
            # Extrair informações principais
            project_name = project_data.get("nome", "Projeto")
            project_description = project_data.get("descricao", "")
            
            # Extrair dados da estimativa
            estimate_scope = estimate_data.get("estimativa_escopo", {}) if estimate_data else {}
            
            # Montar HTML
            html_content = f"""
            <!DOCTYPE html>
            <html lang="pt-BR">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Relatório - {project_name}</title>
                {self._get_pdf_styles()}
            </head>
            <body>
                {self._generate_header(project_name, metadata)}
                {self._generate_executive_summary(project_data, estimate_scope)}
                {self._generate_project_overview(project_data)}
                {self._generate_technical_details(project_data, estimate_scope)}
                {self._generate_timeline_budget(estimate_scope)}
                {self._generate_team_structure(estimate_scope)}
                {self._generate_risk_analysis(estimate_scope)}
                {self._generate_recommendations(estimate_scope)}
                {self._generate_footer(metadata)}
            </body>
            </html>
            """
            
            self.logger.info(f"✅ Template HTML gerado com sucesso para {project_name}")
            return html_content
            
        except Exception as e:
            self.logger.error(f"❌ Erro ao gerar template HTML: {str(e)}")
            raise
    
    def _get_pdf_styles(self) -> str:
        """Retorna CSS profissional para o PDF"""
        return """
        <style>
            @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
            
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }
            
            body {
                font-family: 'Inter', Arial, sans-serif;
                line-height: 1.6;
                color: #1f2937;
                font-size: 11pt;
                background: #ffffff;
            }
            
            .page {
                max-width: 210mm;
                margin: 0 auto;
                padding: 20mm;
                background: white;
            }
            
            /* Header */
            .header {
                text-align: center;
                margin-bottom: 40px;
                padding-bottom: 20px;
                border-bottom: 3px solid #3b82f6;
            }
            
            .header h1 {
                font-size: 28pt;
                font-weight: 700;
                color: #1e40af;
                margin-bottom: 10px;
            }
            
            .header .subtitle {
                font-size: 16pt;
                color: #6b7280;
                font-weight: 300;
            }
            
            /* Sections */
            .section {
                margin-bottom: 30px;
                page-break-inside: avoid;
            }
            
            .section-title {
                font-size: 18pt;
                font-weight: 600;
                color: #1e40af;
                margin-bottom: 15px;
                padding-bottom: 8px;
                border-bottom: 2px solid #e5e7eb;
            }
            
            .subsection {
                margin-bottom: 20px;
            }
            
            .subsection-title {
                font-size: 14pt;
                font-weight: 600;
                color: #374151;
                margin-bottom: 10px;
            }
            
            /* Cards e Containers */
            .card {
                background: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 8px;
                padding: 16px;
                margin-bottom: 15px;
            }
            
            .highlight-card {
                background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
                border-left: 4px solid #3b82f6;
                padding: 20px;
                margin: 20px 0;
            }
            
            /* Grid Layout */
            .grid {
                display: grid;
                gap: 15px;
                margin-bottom: 20px;
            }
            
            .grid-2 {
                grid-template-columns: 1fr 1fr;
            }
            
            .grid-3 {
                grid-template-columns: 1fr 1fr 1fr;
            }
            
            /* Listas */
            ul, ol {
                margin-left: 20px;
                margin-bottom: 15px;
            }
            
            li {
                margin-bottom: 6px;
            }
            
            /* Tabelas */
            table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 20px;
                font-size: 10pt;
            }
            
            th, td {
                border: 1px solid #d1d5db;
                padding: 12px 8px;
                text-align: left;
            }
            
            th {
                background: #f3f4f6;
                font-weight: 600;
                color: #374151;
            }
            
            tr:nth-child(even) {
                background: #f9fafb;
            }
            
            /* Métricas */
            .metric {
                text-align: center;
                padding: 15px;
                background: white;
                border-radius: 8px;
                border: 1px solid #e5e7eb;
            }
            
            .metric-value {
                font-size: 24pt;
                font-weight: 700;
                color: #1e40af;
                display: block;
            }
            
            .metric-label {
                font-size: 10pt;
                color: #6b7280;
                font-weight: 500;
            }
            
            /* Status e Tags */
            .status {
                display: inline-block;
                padding: 4px 12px;
                border-radius: 20px;
                font-size: 9pt;
                font-weight: 500;
            }
            
            .status-high {
                background: #fee2e2;
                color: #991b1b;
            }
            
            .status-medium {
                background: #fef3c7;
                color: #92400e;
            }
            
            .status-low {
                background: #d1fae5;
                color: #065f46;
            }
            
            /* Footer */
            .footer {
                margin-top: 40px;
                padding-top: 20px;
                border-top: 1px solid #e5e7eb;
                text-align: center;
                font-size: 9pt;
                color: #6b7280;
            }
            
            /* Impressão */
            @media print {
                .page {
                    margin: 0;
                    padding: 15mm;
                }
                
                .section {
                    page-break-inside: avoid;
                }
                
                .header {
                    margin-bottom: 30px;
                }
            }
            
            /* Utilitários */
            .text-center { text-align: center; }
            .text-right { text-align: right; }
            .font-bold { font-weight: 600; }
            .text-sm { font-size: 9pt; }
            .text-lg { font-size: 13pt; }
            .mt-4 { margin-top: 16px; }
            .mb-4 { margin-bottom: 16px; }
            .p-4 { padding: 16px; }
        </style>
        """
    
    def _generate_header(self, project_name: str, metadata: Dict[str, Any]) -> str:
        """Gera cabeçalho do relatório"""
        current_date = datetime.now().strftime("%d/%m/%Y")
        
        return f"""
        <div class="page">
            <div class="header">
                <h1>Relatório de Projeto</h1>
                <div class="subtitle">{project_name}</div>
                <div style="margin-top: 15px; font-size: 10pt; color: #6b7280;">
                    Gerado em {current_date} • ScopeAI Platform v{metadata.get('version', '1.0')}
                </div>
            </div>
        """
    
    def _generate_executive_summary(self, project_data: Dict[str, Any], estimate_scope: Dict[str, Any]) -> str:
        """Gera resumo executivo"""
        project_name = project_data.get("nome", "Projeto")
        description = project_data.get("descricao", "Descrição não disponível")
        
        # Extrair métricas principais
        total_cost = estimate_scope.get("investimento_total", {}).get("total_projeto", 0)
        duration = estimate_scope.get("cronograma", {}).get("duracao_meses", 0)
        team_size = estimate_scope.get("equipe_necessaria", {}).get("total_profissionais", 0)
        
        return f"""
        <div class="section">
            <h2 class="section-title">Resumo Executivo</h2>
            
            <div class="highlight-card">
                <h3 style="color: #1e40af; margin-bottom: 10px;">{project_name}</h3>
                <p style="line-height: 1.7;">{description}</p>
            </div>
            
            <div class="grid grid-3">
                <div class="metric">
                    <span class="metric-value">R$ {total_cost:,.0f}</span>
                    <span class="metric-label">Investimento Total</span>
                </div>
                <div class="metric">
                    <span class="metric-value">{duration} meses</span>
                    <span class="metric-label">Duração Estimada</span>
                </div>
                <div class="metric">
                    <span class="metric-value">{team_size}</span>
                    <span class="metric-label">Profissionais</span>
                </div>
            </div>
        </div>
        """
    
    def _generate_project_overview(self, project_data: Dict[str, Any]) -> str:
        """Gera visão geral do projeto"""
        technologies = project_data.get("tecnologias", [])
        stacks = project_data.get("stacks", [])
        
        tech_list = ""
        if technologies:
            tech_list = "<ul>" + "".join([f"<li>{tech}</li>" for tech in technologies[:10]]) + "</ul>"
        
        return f"""
        <div class="section">
            <h2 class="section-title">Visão Geral do Projeto</h2>
            
            <div class="subsection">
                <h3 class="subsection-title">Objetivos e Escopo</h3>
                <div class="card">
                    <p>{project_data.get('descricao', 'Descrição detalhada não disponível')}</p>
                </div>
            </div>
            
            {f'''
            <div class="subsection">
                <h3 class="subsection-title">Tecnologias Principais</h3>
                <div class="card">
                    {tech_list}
                </div>
            </div>
            ''' if technologies else ''}
        </div>
        """
    
    def _generate_technical_details(self, project_data: Dict[str, Any], estimate_scope: Dict[str, Any]) -> str:
        """Gera detalhes técnicos"""
        architecture = estimate_scope.get("arquitetura", {})
        stacks = estimate_scope.get("stacks_tecnologicas", [])
        
        return f"""
        <div class="section">
            <h2 class="section-title">Detalhes Técnicos</h2>
            
            <div class="subsection">
                <h3 class="subsection-title">Arquitetura do Sistema</h3>
                <div class="card">
                    <p><strong>Tipo:</strong> {architecture.get('tipo_arquitetura', 'A definir')}</p>
                    <p><strong>Tecnologias Principais:</strong></p>
                    <ul>
                        {self._format_list(architecture.get('tecnologias_principais', []))}
                    </ul>
                </div>
            </div>
            
            {self._generate_tech_stacks_table(stacks)}
        </div>
        """
    
    def _generate_tech_stacks_table(self, stacks: List[Dict[str, Any]]) -> str:
        """Gera tabela de stacks tecnológicas"""
        if not stacks:
            return ""
        
        rows = ""
        for stack in stacks[:8]:  # Limitar para não sobrecarregar
            name = stack.get("nome", stack.get("categoria", "Stack"))
            description = stack.get("descricao", stack.get("motivo", ""))[:100]
            complexity = stack.get("complexidade", "Média")
            
            rows += f"""
            <tr>
                <td><strong>{name}</strong></td>
                <td>{description}...</td>
                <td><span class="status status-medium">{complexity}</span></td>
            </tr>
            """
        
        return f"""
        <div class="subsection">
            <h3 class="subsection-title">Stacks Tecnológicas</h3>
            <table>
                <thead>
                    <tr>
                        <th>Tecnologia</th>
                        <th>Descrição</th>
                        <th>Complexidade</th>
                    </tr>
                </thead>
                <tbody>
                    {rows}
                </tbody>
            </table>
        </div>
        """
    
    def _generate_timeline_budget(self, estimate_scope: Dict[str, Any]) -> str:
        """Gera cronograma e orçamento"""
        timeline = estimate_scope.get("cronograma", {})
        investment = estimate_scope.get("investimento_total", {})
        
        return f"""
        <div class="section">
            <h2 class="section-title">Cronograma & Orçamento</h2>
            
            <div class="grid grid-2">
                <div class="card">
                    <h3 class="subsection-title">Cronograma</h3>
                    <p><strong>Duração Total:</strong> {timeline.get('duracao_meses', 0)} meses</p>
                    <p><strong>Total de Sprints:</strong> {timeline.get('total_sprints', 0)}</p>
                    <p><strong>Sprints por Mês:</strong> {timeline.get('sprints_por_mes', 2)}</p>
                </div>
                
                <div class="card">
                    <h3 class="subsection-title">Investimento</h3>
                    <p><strong>Total do Projeto:</strong> R$ {investment.get('total_projeto', 0):,.0f}</p>
                    <p><strong>Desenvolvimento:</strong> R$ {investment.get('desenvolvimento', 0):,.0f}</p>
                    <p><strong>Infraestrutura:</strong> R$ {investment.get('infraestrutura_setup', 0):,.0f}</p>
                    <p><strong>Contingência:</strong> R$ {investment.get('contingencia_20pct', 0):,.0f}</p>
                </div>
            </div>
        </div>
        """
    
    def _generate_team_structure(self, estimate_scope: Dict[str, Any]) -> str:
        """Gera estrutura da equipe"""
        team = estimate_scope.get("equipe_necessaria", {})
        formation = team.get("formacao", [])
        
        if not formation:
            return ""
        
        team_rows = ""
        for member in formation[:6]:  # Limitar para PDF
            role = member.get("area", member.get("tipo", "Profissional"))
            quantity = member.get("quantidade", 1)
            seniority = member.get("senioridade", ["Pleno"])
            
            team_rows += f"""
            <tr>
                <td><strong>{role}</strong></td>
                <td>{quantity}</td>
                <td>{', '.join(seniority) if isinstance(seniority, list) else seniority}</td>
            </tr>
            """
        
        return f"""
        <div class="section">
            <h2 class="section-title">Estrutura da Equipe</h2>
            
            <div class="card">
                <p><strong>Total de Profissionais:</strong> {team.get('total_profissionais', 0)}</p>
                <p><strong>Custo Mensal:</strong> R$ {team.get('custo_mensal_total', 0):,.0f}</p>
            </div>
            
            <table>
                <thead>
                    <tr>
                        <th>Função</th>
                        <th>Quantidade</th>
                        <th>Senioridade</th>
                    </tr>
                </thead>
                <tbody>
                    {team_rows}
                </tbody>
            </table>
        </div>
        """
    
    def _generate_risk_analysis(self, estimate_scope: Dict[str, Any]) -> str:
        """Gera análise de riscos"""
        risks = estimate_scope.get("analise_riscos", {})
        risk_factor = risks.get("fator_risco_total", 5)
        identified_risks = risks.get("riscos_identificados", [])
        
        risk_level = "Baixo" if risk_factor <= 3 else "Médio" if risk_factor <= 6 else "Alto"
        risk_class = "status-low" if risk_factor <= 3 else "status-medium" if risk_factor <= 6 else "status-high"
        
        return f"""
        <div class="section">
            <h2 class="section-title">Análise de Riscos</h2>
            
            <div class="card">
                <p><strong>Nível de Risco Geral:</strong> 
                    <span class="status {risk_class}">{risk_level}</span>
                    (Fator: {risk_factor}/10)
                </p>
                
                {f'''
                <div class="mt-4">
                    <h4>Riscos Identificados:</h4>
                    <ul>
                        {self._format_list(identified_risks[:5])}
                    </ul>
                </div>
                ''' if identified_risks else ''}
            </div>
        </div>
        """
    
    def _generate_recommendations(self, estimate_scope: Dict[str, Any]) -> str:
        """Gera recomendações"""
        recommendations = estimate_scope.get("recomendacoes", [])
        
        if not recommendations:
            return ""
        
        return f"""
        <div class="section">
            <h2 class="section-title">Recomendações</h2>
            
            <div class="card">
                <ul>
                    {self._format_list(recommendations[:8])}
                </ul>
            </div>
        </div>
        """
    
    def _generate_footer(self, metadata: Dict[str, Any]) -> str:
        """Gera rodapé do relatório"""
        return f"""
            <div class="footer">
                <p>Relatório gerado automaticamente pela <strong>ScopeAI Platform</strong></p>
                <p>Versão {metadata.get('version', '1.0')} • {datetime.now().strftime('%d/%m/%Y às %H:%M')}</p>
                <p style="margin-top: 10px; font-size: 8pt; color: #9ca3af;">
                    Este documento contém informações confidenciais e deve ser tratado com a devida proteção.
                </p>
            </div>
        </div>
        """
    
    def _format_list(self, items: List[str]) -> str:
        """Formata lista de itens para HTML"""
        if not items:
            return "<li>Nenhum item disponível</li>"
        
        return "".join([f"<li>{item}</li>" for item in items if item]) 