import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { MermaidRenderResult, MermaidRenderStrategy } from './mermaid-render.strategy';

@Injectable()
export class RemoteMermaidStrategy implements MermaidRenderStrategy {
	readonly name = 'Remote Mermaid (mermaid.ink)';
	readonly supportsDownload = true;
	readonly requiresCodeCleaning = true;

	private readonly MERMAID_LIVE_BASE = 'https://mermaid.ink';
	private readonly MERMAID_LIVE_SVG = `${this.MERMAID_LIVE_BASE}/svg`;

	constructor(private http: HttpClient) {}

	render(code: string): Observable<MermaidRenderResult> {
		if (!code || code.trim() === '') {
			return of({
				content: '',
				success: false,
				error: 'Código Mermaid vazio',
			});
		}

		try {
			// Codificar o diagrama para URL
			const encodedDiagram = this.encodeBase64(code);
			const svgUrl = `${this.MERMAID_LIVE_SVG}/${encodedDiagram}`;

			return this.http.get(svgUrl, { responseType: 'text' }).pipe(
				map(svgText => {
					if (svgText && svgText.includes('<svg')) {
						return {
							content: svgText,
							success: true,
						};
					} else {
						return {
							content: '',
							success: false,
							error: 'Resposta inválida do serviço de renderização',
						};
					}
				}),
				catchError((error: HttpErrorResponse) => {
					console.error('❌ Erro ao renderizar diagrama via Mermaid Live:', error);
					return of({
						content: '',
						success: false,
						error: 'Erro ao conectar com o serviço de renderização',
					});
				}),
			);
		} catch (error) {
			console.error('❌ Erro inesperado ao renderizar diagrama:', error);
			return of({
				content: '',
				success: false,
				error: 'Erro inesperado na renderização',
			});
		}
	}

	/**
	 * Remove emojis do código Mermaid para compatibilidade com a API
	 * A API Mermaid Live não suporta emojis e retorna 404
	 */
	cleanCode(text: string): string {
		// Regex para remover emojis (Unicode ranges para emojis)
		const emojiRegex =
			/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu;

		// Remover emojis preservando a estrutura
		let cleanText = text.replace(emojiRegex, '');

		// Limpar espaços duplos mas preservar quebras de linha
		cleanText = cleanText.replace(/[ \t]+/g, ' ');

		// Remover espaços no início e fim de cada linha
		cleanText = cleanText.replace(/^[ \t]+|[ \t]+$/gm, '');

		return cleanText.trim();
	}

	private encodeBase64(text: string): string {
		try {
			// Usar btoa para codificação Base64
			return btoa(unescape(encodeURIComponent(text)));
		} catch (error) {
			console.error('❌ Erro ao codificar diagrama:', error);
			throw new Error('Erro na codificação do diagrama');
		}
	}
}
