"""
WebSocket Manager para broadcast de notificações em tempo real
"""
import asyncio
import json
import logging
from typing import Dict, Any, Set
from fastapi import WebSocket

logger = logging.getLogger(__name__)


class WebSocketManager:
    """Manager para conexões WebSocket e broadcast de mensagens"""

    def __init__(self):
        self.active_connections: Set[WebSocket] = set()

    async def connect(self, websocket: WebSocket):
        """Aceitar nova conexão WebSocket"""
        await websocket.accept()
        self.active_connections.add(websocket)
        logger.info(
            f"Nova conexão WebSocket. Total: {len(self.active_connections)}")

    def disconnect(self, websocket: WebSocket):
        """Remover conexão WebSocket"""
        self.active_connections.discard(websocket)
        logger.info(
            f"Conexão WebSocket removida. Total: {len(self.active_connections)}")

    async def send_personal_message(self, message: str, websocket: WebSocket):
        """Enviar mensagem para conexão específica"""
        try:
            await websocket.send_text(message)
        except Exception as e:
            logger.error(f"Erro ao enviar mensagem pessoal: {e}")
            self.disconnect(websocket)

    async def broadcast(self, message: Dict[str, Any]):
        """Broadcast mensagem para todas as conexões ativas"""
        if not self.active_connections:
            logger.debug("Nenhuma conexão WebSocket ativa para broadcast")
            return

        message_str = json.dumps(message)
        disconnected = set()

        for connection in self.active_connections:
            try:
                await connection.send_text(message_str)
            except Exception as e:
                logger.error(f"Erro ao enviar broadcast: {e}")
                disconnected.add(connection)

        # Remover conexões com problema
        for connection in disconnected:
            self.disconnect(connection)

        logger.info(
            f"Broadcast enviado para {len(self.active_connections)} conexões")

    async def broadcast_processing_update(self, client_id: str, stage: str, progress: int, message: str):
        """Broadcast atualização de processamento"""
        update_message = {
            "type": "processing_progress",
            "clientId": client_id,
            "data": {
                "clientId": client_id,
                "isProcessing": progress < 100,
                "statusMessage": message,
                "progressPercentage": progress,
                "canViewReport": progress == 100,
                "phase": stage,
                "timestamp": "now"
            }
        }
        await self.broadcast(update_message)

    async def broadcast_processing_started(self, client_id: str, client_name: str):
        """Broadcast início de processamento"""
        message = {
            "type": "processing_started",
            "clientId": client_id,
            "data": {
                "clientId": client_id,
                "isProcessing": True,
                "statusMessage": f"Iniciando análise completa para {client_name}",
                "progressPercentage": 0,
                "canViewReport": False,
                "phase": "started",
                "timestamp": "now"
            }
        }
        await self.broadcast(message)

    async def broadcast_processing_complete(self, client_id: str, client_name: str):
        """Broadcast conclusão de processamento"""
        message = {
            "type": "processing_complete",
            "clientId": client_id,
            "data": {
                "clientId": client_id,
                "isProcessing": False,
                "statusMessage": f"Análise concluída para {client_name}",
                "progressPercentage": 100,
                "canViewReport": True,
                "phase": "completed",
                "timestamp": "now"
            }
        }
        await self.broadcast(message)

    async def broadcast_processing_error(self, client_id: str, error_message: str):
        """Broadcast erro de processamento"""
        message = {
            "type": "processing_error",
            "clientId": client_id,
            "data": {
                "clientId": client_id,
                "isProcessing": False,
                "statusMessage": f"Erro no processamento: {error_message}",
                "progressPercentage": 0,
                "canViewReport": False,
                "phase": "error",
                "timestamp": "now"
            }
        }
        await self.broadcast(message)


# Instância global
websocket_manager = WebSocketManager()
