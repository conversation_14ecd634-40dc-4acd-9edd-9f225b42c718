#!/usr/bin/env python3
"""
Detector de Mudanças para Enriquecimento Contínuo

Implementa algoritmos para detectar mudanças significativas em dados 
de empresas que justifiquem nova coleta de informações.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

from clients.db import clients_collection
from clients.async_db import motor_clients_collection

# Configurar logging
logger = logging.getLogger(__name__)


class ChangeType(Enum):
    """Tipos de mudanças detectáveis"""
    PRICING_CHANGE = "pricing_change"
    BUSINESS_MODEL_CHANGE = "business_model_change"
    NEW_PARTNERSHIPS = "new_partnerships"
    COMPANY_GROWTH = "company_growth"
    MARKET_EXPANSION = "market_expansion"


@dataclass
class DetectedChange:
    """Mudança detectada em uma empresa"""
    company_id: str
    company_name: str
    change_type: ChangeType
    confidence_score: float  # 0-1
    description: str
    detected_at: datetime
    evidence: Dict[str, Any]


class ChangeDetector:
    """
    Detector de Mudanças em Dados de Empresas

    Analisa dados históricos e sinais externos para identificar
    mudanças significativas que justifiquem atualização de dados.
    """

    def __init__(self, confidence_threshold: float = 0.7):
        """
        Inicializa o detector de mudanças

        Args:
            confidence_threshold: Limite mínimo de confiança para reportar mudanças
        """
        self.confidence_threshold = confidence_threshold
        self.change_indicators = {
            ChangeType.PRICING_CHANGE: self._detect_pricing_changes,
            ChangeType.BUSINESS_MODEL_CHANGE: self._detect_business_model_changes,
            ChangeType.NEW_PARTNERSHIPS: self._detect_new_partnerships,
            ChangeType.COMPANY_GROWTH: self._detect_company_growth,
            ChangeType.MARKET_EXPANSION: self._detect_market_expansion
        }

    async def detect_changes_for_company(self, company_id: str) -> List[DetectedChange]:
        """
        Detecta mudanças para uma empresa específica

        Args:
            company_id: ID da empresa a analisar

        Returns:
            Lista de mudanças detectadas
        """
        try:
            # Buscar dados da empresa - ASYNC
            company_data = await motor_clients_collection.find_one({"_id": company_id})

            if not company_data:
                logger.warning(f"Empresa {company_id} não encontrada")
                return []

            detected_changes = []

            # Executar cada detector
            for change_type, detector_func in self.change_indicators.items():
                changes = await detector_func(company_data)
                detected_changes.extend(changes)

            # Filtrar por confiança
            filtered_changes = [
                change for change in detected_changes
                if change.confidence_score >= self.confidence_threshold
            ]

            logger.info(
                f"Detectadas {len(filtered_changes)} mudanças para empresa {company_id}")
            return filtered_changes

        except Exception as e:
            logger.error(
                f"Erro ao detectar mudanças para empresa {company_id}: {e}")
            return []

    async def detect_changes_for_all_companies(self,
                                               limit: Optional[int] = None) -> List[DetectedChange]:
        """
        Detecta mudanças para todas as empresas

        Args:
            limit: Número máximo de empresas a analisar

        Returns:
            Lista consolidada de mudanças detectadas
        """
        try:
            # Buscar empresas ativas
            pipeline = [
                {"$match": {"status": {"$ne": "inactive"}}},
                {"$project": {"_id": 1, "name": 1}}
            ]

            if limit:
                pipeline.append({"$limit": limit})

            companies = await motor_clients_collection.aggregate(pipeline).to_list(length=None)

            all_changes = []

            for company in companies:
                company_id = str(company["_id"])
                changes = await self.detect_changes_for_company(company_id)
                all_changes.extend(changes)

            logger.info(f"Detectadas {len(all_changes)} mudanças no total")
            return all_changes

        except Exception as e:
            logger.error(
                f"Erro ao detectar mudanças para todas as empresas: {e}")
            return []

    async def _detect_pricing_changes(self, company_data: Dict) -> List[DetectedChange]:
        """
        Detecta mudanças em estratégias de pricing

        Args:
            company_data: Dados da empresa

        Returns:
            Lista de mudanças detectadas em pricing
        """
        changes = []

        try:
            # Analisar histórico de pricing
            reports = company_data.get("reports", [])
            pricing_history = []

            for report in reports:
                if "dados_pricing" in report:
                    pricing_data = report["dados_pricing"]
                    if "processed_at" in pricing_data:
                        pricing_history.append(pricing_data)

            # Verificar se há pelo menos 2 registros para comparação
            if len(pricing_history) >= 2:
                # Ordenar por data
                pricing_history.sort(key=lambda x: x.get("processed_at", ""))

                latest = pricing_history[-1]
                previous = pricing_history[-2]

                # Comparar estratégias principais
                latest_strategy = latest.get("resumo_pricing", {}).get(
                    "estrategia_principal", "")
                previous_strategy = previous.get(
                    "resumo_pricing", {}).get("estrategia_principal", "")

                if latest_strategy != previous_strategy and latest_strategy and previous_strategy:
                    changes.append(DetectedChange(
                        company_id=str(company_data["_id"]),
                        company_name=company_data.get("name", "Unknown"),
                        change_type=ChangeType.PRICING_CHANGE,
                        confidence_score=0.8,
                        description=f"Mudança de estratégia de pricing: {previous_strategy} → {latest_strategy}",
                        detected_at=datetime.utcnow(),
                        evidence={
                            "previous_strategy": previous_strategy,
                            "current_strategy": latest_strategy,
                            "detection_method": "historical_comparison"
                        }
                    ))

            # Verificar se dados de pricing estão muito desatualizados
            if pricing_history:
                latest_update = pricing_history[-1].get("processed_at")
                if isinstance(latest_update, str):
                    latest_update = datetime.fromisoformat(
                        latest_update.replace('Z', '+00:00'))

                days_old = (datetime.utcnow() - latest_update).days

                if days_old > 90:  # Mais de 3 meses
                    changes.append(DetectedChange(
                        company_id=str(company_data["_id"]),
                        company_name=company_data.get("name", "Unknown"),
                        change_type=ChangeType.PRICING_CHANGE,
                        confidence_score=0.6,
                        description=f"Dados de pricing desatualizados há {days_old} dias",
                        detected_at=datetime.utcnow(),
                        evidence={
                            "days_since_update": days_old,
                            "last_update": latest_update.isoformat(),
                            "detection_method": "staleness_check"
                        }
                    ))

        except Exception as e:
            logger.error(f"Erro ao detectar mudanças de pricing: {e}")

        return changes

    async def _detect_business_model_changes(self, company_data: Dict) -> List[DetectedChange]:
        """
        Detecta mudanças no modelo de negócio

        Args:
            company_data: Dados da empresa

        Returns:
            Lista de mudanças detectadas no modelo de negócio
        """
        changes = []

        try:
            # Analisar histórico do modelo de negócio
            reports = company_data.get("reports", [])
            model_history = []

            for report in reports:
                if "dados_modelo_negocio" in report:
                    model_data = report["dados_modelo_negocio"]
                    if "processed_at" in model_data:
                        model_history.append(model_data)

            # Verificar mudanças no tipo principal
            if len(model_history) >= 2:
                model_history.sort(key=lambda x: x.get("processed_at", ""))

                latest = model_history[-1]
                previous = model_history[-2]

                # Comparar tipos principais
                latest_type = latest.get(
                    "classificacao_modelo", {}).get("tipo_principal", "")
                previous_type = previous.get(
                    "classificacao_modelo", {}).get("tipo_principal", "")

                if latest_type != previous_type and latest_type and previous_type:
                    changes.append(DetectedChange(
                        company_id=str(company_data["_id"]),
                        company_name=company_data.get("name", "Unknown"),
                        change_type=ChangeType.BUSINESS_MODEL_CHANGE,
                        confidence_score=0.9,
                        description=f"Mudança no modelo de negócio: {previous_type} → {latest_type}",
                        detected_at=datetime.utcnow(),
                        evidence={
                            "previous_model": previous_type,
                            "current_model": latest_type,
                            "detection_method": "model_type_comparison"
                        }
                    ))

        except Exception as e:
            logger.error(
                f"Erro ao detectar mudanças no modelo de negócio: {e}")

        return changes

    async def _detect_new_partnerships(self, company_data: Dict) -> List[DetectedChange]:
        """
        Detecta novas parcerias anunciadas

        Args:
            company_data: Dados da empresa

        Returns:
            Lista de mudanças detectadas em parcerias
        """
        changes = []

        try:
            # Verificar se dados de parcerias estão desatualizados
            reports = company_data.get("reports", [])
            partnerships_data = None

            for report in reports:
                if "dados_parcerias" in report:
                    partnerships_data = report["dados_parcerias"]
                    break

            if partnerships_data:
                last_update = partnerships_data.get("processed_at")
                if isinstance(last_update, str):
                    last_update = datetime.fromisoformat(
                        last_update.replace('Z', '+00:00'))

                days_old = (datetime.utcnow() - last_update).days

                # Se dados estão antigos, pode haver novas parcerias
                if days_old > 60:  # Mais de 2 meses
                    changes.append(DetectedChange(
                        company_id=str(company_data["_id"]),
                        company_name=company_data.get("name", "Unknown"),
                        change_type=ChangeType.NEW_PARTNERSHIPS,
                        confidence_score=0.5,
                        description=f"Possíveis novas parcerias (dados antigos há {days_old} dias)",
                        detected_at=datetime.utcnow(),
                        evidence={
                            "days_since_update": days_old,
                            "last_update": last_update.isoformat(),
                            "detection_method": "staleness_indicator"
                        }
                    ))

        except Exception as e:
            logger.error(f"Erro ao detectar novas parcerias: {e}")

        return changes

    async def _detect_company_growth(self, company_data: Dict) -> List[DetectedChange]:
        """
        Detecta sinais de crescimento da empresa

        Args:
            company_data: Dados da empresa

        Returns:
            Lista de mudanças detectadas relacionadas a crescimento
        """
        # Implementação placeholder - poderia analisar métricas de crescimento
        return []

    async def _detect_market_expansion(self, company_data: Dict) -> List[DetectedChange]:
        """
        Detecta expansão para novos mercados

        Args:
            company_data: Dados da empresa

        Returns:
            Lista de mudanças detectadas relacionadas a expansão
        """
        # Implementação placeholder - poderia analisar dados geográficos
        return []

    def get_detection_summary(self, changes: List[DetectedChange]) -> Dict[str, Any]:
        """
        Gera resumo das mudanças detectadas

        Args:
            changes: Lista de mudanças

        Returns:
            Resumo estatístico das mudanças
        """
        if not changes:
            return {"total_changes": 0, "by_type": {}, "average_confidence": 0}

        # Agrupar por tipo
        by_type = {}
        for change in changes:
            change_type = change.change_type.value
            if change_type not in by_type:
                by_type[change_type] = []
            by_type[change_type].append(change)

        # Calcular estatísticas
        total_changes = len(changes)
        average_confidence = sum(
            c.confidence_score for c in changes) / total_changes

        type_summary = {
            change_type: {
                "count": len(changes_list),
                "average_confidence": sum(c.confidence_score for c in changes_list) / len(changes_list)
            }
            for change_type, changes_list in by_type.items()
        }

        return {
            "total_changes": total_changes,
            "by_type": type_summary,
            "average_confidence": round(average_confidence, 3),
            "high_confidence_changes": len([c for c in changes if c.confidence_score >= 0.8])
        }
