import { IAgenteJustificativa, IEstimativaDetalhada } from './generated-project.interface';

/**
 * Interface para projeto conforme retornado pelo backend
 */
export interface IBackendProject {
	_id?: string;
	id?: string;
	nome_projeto?: string;
	title?: string;
	cliente_nome?: string;
	client?: string;
	cliente_sector?: string;
	sector?: string;
	resumo?: string;
	summary?: string;
	tags?: string[];
	agentes_justificativa?: IAgenteJustificativa[]; // Array de objetos com justificativas dos agentes
	progresso?: number;
	progress?: number;
	equipe?: string;
	team?: string;
	status?: string;
	created_at?: string;
	createdAt?: string;
	updated_at?: string;
	updatedAt?: string;
	justificativa?: string;
	importancia?: string;
	pontuacao?: number;
	agente_responsavel?: string;
	area_especialidade?: string;
	detalhamento?: string;
	beneficios?: string | unknown;
	cliente_company?: string;
	cliente_id?: string;

	// 🎯 Campos de estimativa (novos campos padronizados)
	estimate_status?: 'processing' | 'completed' | 'error' | string;
	estimate_requested?: boolean;
	estimate_resultado?: IEstimativaDetalhada;
	estimate_solicitada_em?: string;
	estimate_atualizada_em?: string;
	estimate_error?: string;

	// 🎯 Campos de estimativa (campos antigos para compatibilidade)
	estimativa_status?: 'processando' | 'concluida' | 'erro' | string;
	estimativa_solicitada?: boolean;
	estimativa_detalhada?: IEstimativaDetalhada;
	estimativa_solicitada_em?: string;
	estimativa_concluida_em?: string;
	estimativa_erro?: string;

	// 🎯 NOVO: Campo "projeto" estruturado do Team Agno
	projeto?: IProjeto;
	
	// Campo para identificar origem do projeto
	ai_generated?: boolean;
}

/**
 * 🎯 NOVA Interface para o campo "projeto" estruturado
 */
export interface IProjeto {
	nome?: string;
	descricao?: string;
	requisitos?: string;
	prazo_de_entrega?: string;
	sprint_por_mes?: number;
	total_de_sprints?: number;
	total_de_horas_de_capacity?: number;
	tecnologias?: string[];
	prd?: string;
	diagrama?: string;
	stacks?: IProjetoStack[];
	custos_estimados_r$?: IProjetoCustos;
	roadmap?: IProjetoRoadmap;
	analise_riscos?: IProjetoRiscos;
}

export interface IProjetoStack {
	stack?: string;
	quantidade_de_desenvolvedores?: number;
	tecnologias?: string[];
	desenvolvedores?: IProjetoDesenvolvedor[];
	diagrama?: string;
	motivo?: string;
}

export interface IProjetoDesenvolvedor {
	quantidade?: string;
	senioridade?: string[];
	modalidade?: string;
	duracao?: string;
	motivo?: string;
}

export interface IProjetoCustos {
	total_estimada?: number;
	detalhamento?: Record<string, number>;
}

export interface IProjetoRoadmap {
	fases?: IProjetoFase[];
}

export interface IProjetoFase {
	nome?: string;
	duracao_semanas?: number | null;
	atividades?: string[];
}

export interface IProjetoRiscos {
	riscos?: string[];
	mitigacoes?: string[];
}

/**
 * Interface para resposta da API /projects
 */
export interface IProjectsApiResponse {
	projects: IBackendProject[];
	total_projects: number;
}

/**
 * 🎯 Interface para dados processados da estimativa (frontend)
 */
export interface IEstimativaProcessada {
	metricas_principais: {
		total_sprints: number;
		duracao_estimada: string;
		custo_total: string;
		complexidade: string;
		equipe_minima: number;
	};
	cronograma: {
		sprints: Array<{
			numero: number;
			descricao: string;
		}>;
		fases: string[];
		milestones: string[];
	};
	custos: {
		total: string;
		por_papel: Array<{
			papel: string;
			valor: string;
		}>;
		breakdown: Record<string, any>;
	};
	arquitetura: Record<string, any>;
	tecnologias: string[];
	prd: string;
	diagramas_mermaid: Array<{
		id: string;
		titulo: string;
		codigo: string;
	}>;
	equipe: {
		total_pessoas: number;
		papeis: Array<{
			papel: string;
			quantidade: number;
		}>;
		senioridades: Record<string, any>;
	};
	riscos: string[];
	features_principais: string[];
}

/**
 * 🎯 Interface para projeto detalhado com estimativa processada
 */
export interface IProjectWithDetailedEstimate extends IBackendProject {
	has_detailed_estimate: boolean;
	estimativa_processada?: IEstimativaProcessada;
}

/**
 * 📊 Interface para status da estimativa com dados processados
 */
export interface IEstimateStatusWithDetails {
	project_id: string;
	project_title: string;
	estimativa_solicitada: boolean;
	estimativa_status: string;
	has_estimativa: boolean;
	agents_count: number;
	started_at?: string;
	finished_at?: string;
	processing_duration?: string;
	estimated_time?: string;
	error_message?: string;
	estimativa_processada?: IEstimativaProcessada;
}
