{"version": 3, "sources": ["../../../../../../node_modules/mermaid/dist/chunks/mermaid.core/chunk-K557N5IZ.mjs"], "sourcesContent": ["import { __name } from \"./chunk-YTJNT7DU.mjs\";\n\n// src/utils/subGraphTitleMargins.ts\nvar getSubGraphTitleMargins = /* @__PURE__ */__name(({\n  flowchart\n}) => {\n  const subGraphTitleTopMargin = flowchart?.subGraphTitleMargin?.top ?? 0;\n  const subGraphTitleBottomMargin = flowchart?.subGraphTitleMargin?.bottom ?? 0;\n  const subGraphTitleTotalMargin = subGraphTitleTopMargin + subGraphTitleBottomMargin;\n  return {\n    subGraphTitleTopMargin,\n    subGraphTitleBottomMargin,\n    subGraphTitleTotalMargin\n  };\n}, \"getSubGraphTitleMargins\");\nexport { getSubGraphTitleMargins };"], "mappings": ";;;;;AAGA,IAAI,0BAAyC,OAAO,CAAC;AAAA,EACnD;AACF,MAAM;AACJ,QAAM,yBAAyB,WAAW,qBAAqB,OAAO;AACtE,QAAM,4BAA4B,WAAW,qBAAqB,UAAU;AAC5E,QAAM,2BAA2B,yBAAyB;AAC1D,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF,GAAG,yBAAyB;", "names": []}