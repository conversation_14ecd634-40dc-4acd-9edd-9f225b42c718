"""
Visual Analysis Tools para Agno

Tools customizadas para análise de dados visuais:
- Processamento de análise UI/UX 
- Extração de insights de design
- Análise de responsividade
- Identificação de problemas visuais
"""

import logging
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class VisualInsights(BaseModel):
    """Schema para insights visuais processados"""
    layout_quality: str = Field(..., description="Qualidade do layout")
    user_experience: str = Field(...,
                                 description="Qualidade da experiência do usuário")
    responsive_design: str = Field(...,
                                   description="Qualidade do design responsivo")
    visual_hierarchy: Optional[str] = Field(
        None, description="Qualidade da hierarquia visual")

    # Issues visuais
    ui_issues: List[str] = Field(default_factory=list)
    ux_issues: List[str] = Field(default_factory=list)
    design_recommendations: List[str] = Field(default_factory=list)

    # Scores
    overall_visual_score: float = Field(
        default=0.0, description="Score visual geral (0-100)")
    accessibility_visual_score: float = Field(
        default=0.0, description="Score visual de acessibilidade")


class VisualAnalysisTools:
    """
    Tools para análise de dados visuais

    Processa dados de análise visual (screenshots, UX analysis)
    e extrai insights para recomendações arquiteturais.
    """

    def analyze_visual_data(self, visual_analysis: Dict[str, Any]) -> VisualInsights:
        """
        Analisa dados de análise visual e extrai insights estruturados

        Args:
            visual_analysis: Dados de análise visual

        Returns:
            Insights visuais processados
        """
        try:
            logger.info("Iniciando análise de dados visuais")

            # Extrair análise UI/UX
            ui_ux_data = visual_analysis.get("analise_ui_ux", {})
            layout_quality = ui_ux_data.get("layout_quality", "Não avaliado")
            user_experience = ui_ux_data.get("user_experience", "Não avaliado")
            responsive_design = ui_ux_data.get(
                "responsive_design", "Não avaliado")
            visual_hierarchy = ui_ux_data.get("visual_hierarchy")

            # Extrair recomendações de design
            design_recommendations = visual_analysis.get(
                "recomendacoes_design", [])

            # Extrair issues
            ui_issues, ux_issues = self._extract_visual_issues(visual_analysis)

            # Calcular scores
            overall_score = self._calculate_visual_score(ui_ux_data)
            accessibility_score = self._calculate_accessibility_visual_score(
                visual_analysis)

            insights = VisualInsights(
                layout_quality=layout_quality,
                user_experience=user_experience,
                responsive_design=responsive_design,
                visual_hierarchy=visual_hierarchy,
                ui_issues=ui_issues,
                ux_issues=ux_issues,
                design_recommendations=design_recommendations,
                overall_visual_score=overall_score,
                accessibility_visual_score=accessibility_score
            )

            logger.info(f"Análise visual concluída - Score: {overall_score}")
            return insights

        except Exception as e:
            logger.error(f"Erro na análise visual: {str(e)}")
            raise

    def _extract_visual_issues(self, visual_analysis: Dict[str, Any]) -> tuple[List[str], List[str]]:
        """Extrai issues de UI e UX"""
        try:
            ui_issues = []
            ux_issues = []

            # Issues gerais
            issues = visual_analysis.get("issues", [])
            for issue in issues:
                if any(keyword in issue.lower() for keyword in ["layout", "design", "visual", "cor"]):
                    ui_issues.append(issue)
                elif any(keyword in issue.lower() for keyword in ["navegação", "usabilidade", "experiência"]):
                    ux_issues.append(issue)
                else:
                    ui_issues.append(issue)  # Default para UI

            # Issues específicos de UI/UX
            ui_ux_data = visual_analysis.get("analise_ui_ux", {})

            # Verificar qualidade baixa
            if ui_ux_data.get("layout_quality", "").lower() in ["ruim", "péssimo", "baixo"]:
                ui_issues.append("Layout de baixa qualidade detectado")

            if ui_ux_data.get("user_experience", "").lower() in ["ruim", "péssimo", "baixo"]:
                ux_issues.append("Experiência do usuário prejudicada")

            if ui_ux_data.get("responsive_design", "").lower() in ["ruim", "péssimo", "não"]:
                ui_issues.append("Design responsivo inadequado")

            return ui_issues, ux_issues

        except Exception:
            return [], []

    def _calculate_visual_score(self, ui_ux_data: Dict[str, Any]) -> float:
        """Calcula score visual geral baseado na análise UI/UX"""
        try:
            score_map = {
                "excelente": 100,
                "muito bom": 90,
                "bom": 80,
                "regular": 60,
                "ruim": 40,
                "péssimo": 20,
                "baixo": 30,
                "médio": 60,
                "alto": 85
            }

            layout_quality = ui_ux_data.get("layout_quality", "").lower()
            user_experience = ui_ux_data.get("user_experience", "").lower()
            responsive_design = ui_ux_data.get("responsive_design", "").lower()

            scores = []

            # Score do layout
            layout_score = score_map.get(layout_quality, 50)
            scores.append(layout_score)

            # Score da UX
            ux_score = score_map.get(user_experience, 50)
            scores.append(ux_score)

            # Score do responsive
            responsive_score = score_map.get(responsive_design, 50)
            scores.append(responsive_score)

            # Média ponderada (UX tem peso maior)
            final_score = (layout_score * 0.3 + ux_score *
                           0.5 + responsive_score * 0.2)

            return round(final_score, 1)

        except Exception:
            return 50.0  # Score neutro

    def _calculate_accessibility_visual_score(self, visual_analysis: Dict[str, Any]) -> float:
        """Calcula score de acessibilidade visual"""
        try:
            score = 75.0  # Score base

            # Verificar problemas de acessibilidade visual
            accessibility_issues = [
                "contraste",
                "cor",
                "fonte",
                "tamanho",
                "legibilidade"
            ]

            issues = visual_analysis.get("issues", [])
            design_recs = visual_analysis.get("recomendacoes_design", [])

            # Reduzir score por issues de acessibilidade
            for issue in issues + design_recs:
                issue_lower = issue.lower()
                for acc_keyword in accessibility_issues:
                    if acc_keyword in issue_lower:
                        score -= 10
                        break

            return max(score, 0.0)

        except Exception:
            return 50.0

    def get_visual_priorities(self, visual_analysis: Dict[str, Any]) -> List[str]:
        """
        Identifica prioridades visuais baseadas na análise

        Args:
            visual_analysis: Dados de análise visual

        Returns:
            Lista de prioridades ordenadas
        """
        try:
            priorities = []

            ui_ux_data = visual_analysis.get("analise_ui_ux", {})

            # Prioridades baseadas em qualidade baixa
            if ui_ux_data.get("user_experience", "").lower() in ["ruim", "péssimo"]:
                priorities.append("Melhorar experiência do usuário - CRÍTICO")

            if ui_ux_data.get("responsive_design", "").lower() in ["ruim", "péssimo", "não"]:
                priorities.append("Implementar design responsivo - ALTO")

            if ui_ux_data.get("layout_quality", "").lower() in ["ruim", "péssimo"]:
                priorities.append("Reestruturar layout - ALTO")

            # Adicionar recomendações de design como prioridades
            design_recs = visual_analysis.get("recomendacoes_design", [])
            for rec in design_recs[:3]:  # Top 3
                priorities.append(f"Design: {rec}")

            return priorities

        except Exception as e:
            logger.error(f"Erro ao calcular prioridades visuais: {str(e)}")
            return []

    def assess_mobile_readiness(self, visual_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Avalia prontidão mobile baseada na análise visual

        Args:
            visual_analysis: Dados de análise visual

        Returns:
            Avaliação de prontidão mobile
        """
        try:
            ui_ux_data = visual_analysis.get("analise_ui_ux", {})
            responsive_design = ui_ux_data.get("responsive_design", "").lower()

            # Determinar nível de prontidão mobile
            if responsive_design in ["excelente", "muito bom"]:
                readiness_level = "Excelente"
                readiness_score = 95
            elif responsive_design in ["bom"]:
                readiness_level = "Bom"
                readiness_score = 80
            elif responsive_design in ["regular", "médio"]:
                readiness_level = "Regular"
                readiness_score = 60
            elif responsive_design in ["ruim"]:
                readiness_level = "Ruim"
                readiness_score = 30
            else:
                readiness_level = "Não implementado"
                readiness_score = 10

            # Identificar problemas mobile específicos
            mobile_issues = []
            design_recs = visual_analysis.get("recomendacoes_design", [])

            for rec in design_recs:
                if any(keyword in rec.lower() for keyword in ["mobile", "responsivo", "tela", "dispositivo"]):
                    mobile_issues.append(rec)

            return {
                "readiness_level": readiness_level,
                "readiness_score": readiness_score,
                "mobile_issues": mobile_issues,
                "requires_mobile_optimization": readiness_score < 70
            }

        except Exception as e:
            logger.error(f"Erro ao avaliar prontidão mobile: {str(e)}")
            return {
                "readiness_level": "Indeterminado",
                "readiness_score": 50,
                "mobile_issues": [],
                "requires_mobile_optimization": True
            }
