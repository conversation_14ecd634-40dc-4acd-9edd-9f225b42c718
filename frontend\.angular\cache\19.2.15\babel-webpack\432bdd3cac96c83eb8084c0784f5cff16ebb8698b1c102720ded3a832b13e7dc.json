{"ast": null, "code": "import _asyncToGenerator from \"C:/projetos/scope-ai/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { By } from '@angular/platform-browser';\nimport { MessageService } from 'primeng/api';\nimport { ButtonModule } from 'primeng/button';\nimport { DialogModule } from 'primeng/dialog';\nimport { PodcastModalComponent } from './podcast-modal.component';\ndescribe('PodcastModalComponent', () => {\n  let component;\n  let fixture;\n  let messageService;\n  const mockData = {\n    visible: true,\n    generating: false,\n    url: 'http://localhost:8040/podcasts/123/audio',\n    title: 'Projeto Teste',\n    projectId: 'proj123'\n  };\n  beforeEach(/*#__PURE__*/_asyncToGenerator(function* () {\n    const messageServiceSpy = jasmine.createSpyObj('MessageService', ['add']);\n    yield TestBed.configureTestingModule({\n      imports: [PodcastModalComponent, HttpClientTestingModule, DialogModule, ButtonModule],\n      providers: [{\n        provide: MessageService,\n        useValue: messageServiceSpy\n      }]\n    }).compileComponents();\n    messageService = TestBed.inject(MessageService);\n    fixture = TestBed.createComponent(PodcastModalComponent);\n    component = fixture.componentInstance;\n  }));\n  it('deve criar o componente', () => {\n    expect(component).toBeTruthy();\n  });\n  describe('Abertura e Fechamento', () => {\n    it('deve exibir modal quando visible é true', () => {\n      component.data = {\n        ...mockData,\n        visible: true\n      };\n      fixture.detectChanges();\n      const dialog = fixture.debugElement.query(By.css('p-dialog'));\n      expect(dialog).toBeTruthy();\n      expect(dialog.componentInstance.visible).toBe(true);\n    });\n    it('deve ocultar modal quando visible é false', () => {\n      component.data = {\n        ...mockData,\n        visible: false\n      };\n      fixture.detectChanges();\n      const dialog = fixture.debugElement.query(By.css('p-dialog'));\n      expect(dialog.componentInstance.visible).toBe(false);\n    });\n    it('deve emitir evento close ao fechar', () => {\n      spyOn(component.close, 'emit');\n      component.onClose();\n      expect(component.close.emit).toHaveBeenCalled();\n    });\n    it('deve chamar onClose quando dialog emite onHide', () => {\n      spyOn(component, 'onClose');\n      component.data = {\n        ...mockData,\n        visible: true\n      };\n      fixture.detectChanges();\n      const dialog = fixture.debugElement.query(By.css('p-dialog'));\n      dialog.triggerEventHandler('onHide', null);\n      expect(component.onClose).toHaveBeenCalled();\n    });\n  });\n  describe('Estados Visuais', () => {\n    it('deve mostrar spinner quando generating é true', () => {\n      component.data = {\n        ...mockData,\n        generating: true,\n        url: ''\n      };\n      fixture.detectChanges();\n      const spinner = fixture.debugElement.query(By.css('.pi-spinner'));\n      const loadingText = fixture.nativeElement.querySelector('p.font-semibold');\n      expect(spinner).toBeTruthy();\n      expect(loadingText?.textContent).toContain('Gerando Podcast...');\n    });\n    it('deve mostrar player quando url existe e não está gerando', () => {\n      component.data = {\n        ...mockData,\n        generating: false\n      };\n      fixture.detectChanges();\n      const audioElement = fixture.debugElement.query(By.css('audio'));\n      expect(audioElement).toBeTruthy();\n      expect(audioElement.nativeElement.src).toBe(mockData.url);\n    });\n    it('deve mostrar estado vazio quando sem url e sem generating', () => {\n      component.data = {\n        ...mockData,\n        url: '',\n        generating: false\n      };\n      fixture.detectChanges();\n      const emptyState = fixture.nativeElement.querySelector('.pi-microphone.text-gray-400');\n      const emptyText = fixture.nativeElement.querySelector('p.text-gray-600');\n      expect(emptyState).toBeTruthy();\n      expect(emptyText?.textContent).toContain('Nenhum podcast disponível');\n    });\n    it('deve mostrar botão gerar quando projectId existe e sem url', () => {\n      component.data = {\n        ...mockData,\n        url: '',\n        projectId: 'proj123'\n      };\n      fixture.detectChanges();\n      const generateButton = fixture.debugElement.query(By.css('button[label=\"Gerar Podcast\"]'));\n      expect(generateButton).toBeTruthy();\n    });\n    it('não deve mostrar botão gerar quando projectId não existe', () => {\n      component.data = {\n        ...mockData,\n        url: '',\n        projectId: undefined\n      };\n      fixture.detectChanges();\n      const generateButton = fixture.debugElement.query(By.css('button[label=\"Gerar Podcast\"]'));\n      expect(generateButton).toBeFalsy();\n    });\n  });\n  describe('Integração com Player', () => {\n    it('deve configurar autoplay no audio element', () => {\n      component.data = mockData;\n      fixture.detectChanges();\n      const audioElement = fixture.nativeElement.querySelector('audio');\n      expect(audioElement.autoplay).toBe(true);\n    });\n    it('deve usar URL fornecida como source', () => {\n      component.data = mockData;\n      fixture.detectChanges();\n      const audioElement = fixture.nativeElement.querySelector('audio');\n      const sourceElement = audioElement.querySelector('source');\n      expect(audioElement.src).toContain(mockData.url);\n      expect(sourceElement.src).toContain(mockData.url);\n      expect(sourceElement.type).toBe('audio/mpeg');\n    });\n    it('deve ter atributo preload configurado como auto', () => {\n      component.data = mockData;\n      fixture.detectChanges();\n      const audioElement = fixture.nativeElement.querySelector('audio');\n      expect(audioElement.preload).toBe('auto');\n    });\n    it('deve exibir mensagem de erro para navegadores incompatíveis', () => {\n      component.data = mockData;\n      fixture.detectChanges();\n      const fallbackText = fixture.nativeElement.querySelector('audio p.text-red-600');\n      expect(fallbackText?.textContent).toContain('Seu navegador não suporta o elemento de áudio.');\n    });\n  });\n  describe('Eventos', () => {\n    it('deve emitir generate com projectId correto', () => {\n      spyOn(component.generate, 'emit');\n      component.data = {\n        ...mockData,\n        projectId: 'proj456'\n      };\n      component.generatePodcast();\n      expect(component.generate.emit).toHaveBeenCalledWith('proj456');\n    });\n    it('não deve emitir generate se projectId não existir', () => {\n      spyOn(component.generate, 'emit');\n      component.data = {\n        ...mockData,\n        projectId: undefined\n      };\n      component.generatePodcast();\n      expect(component.generate.emit).not.toHaveBeenCalled();\n    });\n    it('deve chamar generatePodcast quando botão gerar é clicado', () => {\n      spyOn(component, 'generatePodcast');\n      component.data = {\n        ...mockData,\n        url: '',\n        projectId: 'proj123'\n      };\n      fixture.detectChanges();\n      const generateButton = fixture.debugElement.query(By.css('button[label=\"Gerar Podcast\"]'));\n      generateButton.nativeElement.click();\n      expect(component.generatePodcast).toHaveBeenCalled();\n    });\n  });\n  describe('Header e Título', () => {\n    it('deve exibir título do projeto no header', () => {\n      component.data = {\n        ...mockData,\n        title: 'Meu Projeto Especial'\n      };\n      fixture.detectChanges();\n      const titleElement = fixture.nativeElement.querySelector('.text-sm.text-gray-600.m-0.mt-1');\n      expect(titleElement?.textContent).toBe('Meu Projeto Especial');\n    });\n    it('deve sanitizar título para evitar XSS', () => {\n      const maliciousTitle = '<script>alert(\"xss\")</script>Projeto';\n      component.data = {\n        ...mockData,\n        title: maliciousTitle\n      };\n      fixture.detectChanges();\n      const titleElement = fixture.nativeElement.querySelector('.text-sm.text-gray-600.m-0.mt-1');\n      // Angular sanitiza automaticamente, então o script não deve ser executado\n      expect(titleElement?.innerHTML).not.toContain('<script>');\n      expect(titleElement?.textContent).toContain('Projeto');\n    });\n    it('deve exibir ícone de microfone no header', () => {\n      component.data = mockData;\n      fixture.detectChanges();\n      const icon = fixture.nativeElement.querySelector('.pi-microphone.text-purple-600');\n      expect(icon).toBeTruthy();\n    });\n  });\n  describe('Responsividade', () => {\n    it('deve aplicar classes responsivas ao modal', () => {\n      component.data = mockData;\n      fixture.detectChanges();\n      const dialog = fixture.debugElement.query(By.css('p-dialog'));\n      expect(dialog.componentInstance.style).toEqual({\n        width: '90vw',\n        maxWidth: '600px'\n      });\n    });\n    it('deve aplicar padding responsivo no container', () => {\n      component.data = mockData;\n      fixture.detectChanges();\n      // Verifica media query no CSS\n      const styles = fixture.nativeElement.querySelector('style');\n      expect(component.styles[0]).toContain('@media (max-width: 640px)');\n    });\n  });\n  describe('Limpeza e Destruição', () => {\n    it('deve implementar OnDestroy', () => {\n      expect(component.ngOnDestroy).toBeDefined();\n    });\n    it('deve chamar ngOnDestroy sem erros', () => {\n      expect(() => component.ngOnDestroy()).not.toThrow();\n    });\n  });\n  describe('Botão Fechar', () => {\n    it('deve exibir botão fechar com ícone e label corretos', () => {\n      component.data = mockData;\n      fixture.detectChanges();\n      const closeButton = fixture.debugElement.query(By.css('button[label=\"Fechar\"]'));\n      expect(closeButton).toBeTruthy();\n      expect(closeButton.nativeElement.classList).toContain('p-button-outlined');\n      expect(closeButton.nativeElement.classList).toContain('p-button-secondary');\n    });\n    it('deve chamar onClose quando botão fechar é clicado', () => {\n      spyOn(component, 'onClose');\n      component.data = mockData;\n      fixture.detectChanges();\n      const closeButton = fixture.debugElement.query(By.css('button[label=\"Fechar\"]'));\n      closeButton.nativeElement.click();\n      expect(component.onClose).toHaveBeenCalled();\n    });\n  });\n  describe('Métodos Legados (para remover)', () => {\n    it('deve ter métodos legados marcados para remoção', () => {\n      // Estes testes documentam métodos que devem ser removidos\n      expect(component.playProjectPodcast).toBeDefined();\n      expect(component.generateProjectPodcast).toBeDefined();\n      expect(component.monitorPodcastGeneration).toBeDefined();\n      expect(component.openPodcastPlayer).toBeDefined();\n      // TODO: Remover estes métodos na MT-4\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "TestBed", "By", "MessageService", "ButtonModule", "DialogModule", "PodcastModalComponent", "describe", "component", "fixture", "messageService", "mockData", "visible", "generating", "url", "title", "projectId", "beforeEach", "_asyncToGenerator", "messageServiceSpy", "jasmine", "createSpyObj", "configureTestingModule", "imports", "providers", "provide", "useValue", "compileComponents", "inject", "createComponent", "componentInstance", "it", "expect", "toBeTruthy", "data", "detectChanges", "dialog", "debugElement", "query", "css", "toBe", "spyOn", "close", "onClose", "emit", "toHaveBeenCalled", "triggerEventHandler", "spinner", "loadingText", "nativeElement", "querySelector", "textContent", "toContain", "audioElement", "src", "emptyState", "emptyText", "generateButton", "undefined", "toBeFalsy", "autoplay", "sourceElement", "type", "preload", "fallbackText", "generate", "generatePodcast", "toHaveBeenCalledWith", "not", "click", "titleElement", "malicious<PERSON>itle", "innerHTML", "icon", "style", "toEqual", "width", "max<PERSON><PERSON><PERSON>", "styles", "ngOnDestroy", "toBeDefined", "toThrow", "closeButton", "classList", "playProjectPodcast", "generateProjectPodcast", "monitorPodcastGeneration", "openPodcastPlayer"], "sources": ["C:\\projetos\\scope-ai\\frontend\\src\\app\\features\\projects\\components\\podcast-modal\\podcast-modal.component.spec.ts"], "sourcesContent": ["import { HttpClientTestingModule } from '@angular/common/http/testing';\r\nimport { ComponentFixture, TestBed } from '@angular/core/testing';\r\nimport { By } from '@angular/platform-browser';\r\nimport { MessageService } from 'primeng/api';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { DialogModule } from 'primeng/dialog';\r\nimport { PodcastModalComponent, PodcastModalData } from './podcast-modal.component';\r\n\r\ndescribe('PodcastModalComponent', () => {\r\n\tlet component: PodcastModalComponent;\r\n\tlet fixture: ComponentFixture<PodcastModalComponent>;\r\n\tlet messageService: jasmine.SpyObj<MessageService>;\r\n\r\n\tconst mockData: PodcastModalData = {\r\n\t\tvisible: true,\r\n\t\tgenerating: false,\r\n\t\turl: 'http://localhost:8040/podcasts/123/audio',\r\n\t\ttitle: 'Projeto Teste',\r\n\t\tprojectId: 'proj123',\r\n\t};\r\n\r\n\tbeforeEach(async () => {\r\n\t\tconst messageServiceSpy = jasmine.createSpyObj('MessageService', ['add']);\r\n\r\n\t\tawait TestBed.configureTestingModule({\r\n\t\t\timports: [PodcastModalComponent, HttpClientTestingModule, DialogModule, ButtonModule],\r\n\t\t\tproviders: [{ provide: MessageService, useValue: messageServiceSpy }],\r\n\t\t}).compileComponents();\r\n\r\n\t\tmessageService = TestBed.inject(MessageService) as jasmine.SpyObj<MessageService>;\r\n\t\tfixture = TestBed.createComponent(PodcastModalComponent);\r\n\t\tcomponent = fixture.componentInstance;\r\n\t});\r\n\r\n\tit('deve criar o componente', () => {\r\n\t\texpect(component).toBeTruthy();\r\n\t});\r\n\r\n\tdescribe('Abertura e Fechamento', () => {\r\n\t\tit('deve exibir modal quando visible é true', () => {\r\n\t\t\tcomponent.data = { ...mockData, visible: true };\r\n\t\t\tfixture.detectChanges();\r\n\r\n\t\t\tconst dialog = fixture.debugElement.query(By.css('p-dialog'));\r\n\t\t\texpect(dialog).toBeTruthy();\r\n\t\t\texpect(dialog.componentInstance.visible).toBe(true);\r\n\t\t});\r\n\r\n\t\tit('deve ocultar modal quando visible é false', () => {\r\n\t\t\tcomponent.data = { ...mockData, visible: false };\r\n\t\t\tfixture.detectChanges();\r\n\r\n\t\t\tconst dialog = fixture.debugElement.query(By.css('p-dialog'));\r\n\t\t\texpect(dialog.componentInstance.visible).toBe(false);\r\n\t\t});\r\n\r\n\t\tit('deve emitir evento close ao fechar', () => {\r\n\t\t\tspyOn(component.close, 'emit');\r\n\t\t\tcomponent.onClose();\r\n\r\n\t\t\texpect(component.close.emit).toHaveBeenCalled();\r\n\t\t});\r\n\r\n\t\tit('deve chamar onClose quando dialog emite onHide', () => {\r\n\t\t\tspyOn(component, 'onClose');\r\n\t\t\tcomponent.data = { ...mockData, visible: true };\r\n\t\t\tfixture.detectChanges();\r\n\r\n\t\t\tconst dialog = fixture.debugElement.query(By.css('p-dialog'));\r\n\t\t\tdialog.triggerEventHandler('onHide', null);\r\n\r\n\t\t\texpect(component.onClose).toHaveBeenCalled();\r\n\t\t});\r\n\t});\r\n\r\n\tdescribe('Estados Visuais', () => {\r\n\t\tit('deve mostrar spinner quando generating é true', () => {\r\n\t\t\tcomponent.data = { ...mockData, generating: true, url: '' };\r\n\t\t\tfixture.detectChanges();\r\n\r\n\t\t\tconst spinner = fixture.debugElement.query(By.css('.pi-spinner'));\r\n\t\t\tconst loadingText = fixture.nativeElement.querySelector('p.font-semibold');\r\n\r\n\t\t\texpect(spinner).toBeTruthy();\r\n\t\t\texpect(loadingText?.textContent).toContain('Gerando Podcast...');\r\n\t\t});\r\n\r\n\t\tit('deve mostrar player quando url existe e não está gerando', () => {\r\n\t\t\tcomponent.data = { ...mockData, generating: false };\r\n\t\t\tfixture.detectChanges();\r\n\r\n\t\t\tconst audioElement = fixture.debugElement.query(By.css('audio'));\r\n\t\t\texpect(audioElement).toBeTruthy();\r\n\t\t\texpect(audioElement.nativeElement.src).toBe(mockData.url);\r\n\t\t});\r\n\r\n\t\tit('deve mostrar estado vazio quando sem url e sem generating', () => {\r\n\t\t\tcomponent.data = { ...mockData, url: '', generating: false };\r\n\t\t\tfixture.detectChanges();\r\n\r\n\t\t\tconst emptyState = fixture.nativeElement.querySelector('.pi-microphone.text-gray-400');\r\n\t\t\tconst emptyText = fixture.nativeElement.querySelector('p.text-gray-600');\r\n\r\n\t\t\texpect(emptyState).toBeTruthy();\r\n\t\t\texpect(emptyText?.textContent).toContain('Nenhum podcast disponível');\r\n\t\t});\r\n\r\n\t\tit('deve mostrar botão gerar quando projectId existe e sem url', () => {\r\n\t\t\tcomponent.data = { ...mockData, url: '', projectId: 'proj123' };\r\n\t\t\tfixture.detectChanges();\r\n\r\n\t\t\tconst generateButton = fixture.debugElement.query(By.css('button[label=\"Gerar Podcast\"]'));\r\n\t\t\texpect(generateButton).toBeTruthy();\r\n\t\t});\r\n\r\n\t\tit('não deve mostrar botão gerar quando projectId não existe', () => {\r\n\t\t\tcomponent.data = { ...mockData, url: '', projectId: undefined };\r\n\t\t\tfixture.detectChanges();\r\n\r\n\t\t\tconst generateButton = fixture.debugElement.query(By.css('button[label=\"Gerar Podcast\"]'));\r\n\t\t\texpect(generateButton).toBeFalsy();\r\n\t\t});\r\n\t});\r\n\r\n\tdescribe('Integração com Player', () => {\r\n\t\tit('deve configurar autoplay no audio element', () => {\r\n\t\t\tcomponent.data = mockData;\r\n\t\t\tfixture.detectChanges();\r\n\r\n\t\t\tconst audioElement: HTMLAudioElement = fixture.nativeElement.querySelector('audio');\r\n\t\t\texpect(audioElement.autoplay).toBe(true);\r\n\t\t});\r\n\r\n\t\tit('deve usar URL fornecida como source', () => {\r\n\t\t\tcomponent.data = mockData;\r\n\t\t\tfixture.detectChanges();\r\n\r\n\t\t\tconst audioElement: HTMLAudioElement = fixture.nativeElement.querySelector('audio');\r\n\t\t\tconst sourceElement: HTMLSourceElement = audioElement.querySelector('source')!;\r\n\r\n\t\t\texpect(audioElement.src).toContain(mockData.url);\r\n\t\t\texpect(sourceElement.src).toContain(mockData.url);\r\n\t\t\texpect(sourceElement.type).toBe('audio/mpeg');\r\n\t\t});\r\n\r\n\t\tit('deve ter atributo preload configurado como auto', () => {\r\n\t\t\tcomponent.data = mockData;\r\n\t\t\tfixture.detectChanges();\r\n\r\n\t\t\tconst audioElement: HTMLAudioElement = fixture.nativeElement.querySelector('audio');\r\n\t\t\texpect(audioElement.preload).toBe('auto');\r\n\t\t});\r\n\r\n\t\tit('deve exibir mensagem de erro para navegadores incompatíveis', () => {\r\n\t\t\tcomponent.data = mockData;\r\n\t\t\tfixture.detectChanges();\r\n\r\n\t\t\tconst fallbackText = fixture.nativeElement.querySelector('audio p.text-red-600');\r\n\t\t\texpect(fallbackText?.textContent).toContain('Seu navegador não suporta o elemento de áudio.');\r\n\t\t});\r\n\t});\r\n\r\n\tdescribe('Eventos', () => {\r\n\t\tit('deve emitir generate com projectId correto', () => {\r\n\t\t\tspyOn(component.generate, 'emit');\r\n\t\t\tcomponent.data = { ...mockData, projectId: 'proj456' };\r\n\r\n\t\t\tcomponent.generatePodcast();\r\n\r\n\t\t\texpect(component.generate.emit).toHaveBeenCalledWith('proj456');\r\n\t\t});\r\n\r\n\t\tit('não deve emitir generate se projectId não existir', () => {\r\n\t\t\tspyOn(component.generate, 'emit');\r\n\t\t\tcomponent.data = { ...mockData, projectId: undefined };\r\n\r\n\t\t\tcomponent.generatePodcast();\r\n\r\n\t\t\texpect(component.generate.emit).not.toHaveBeenCalled();\r\n\t\t});\r\n\r\n\t\tit('deve chamar generatePodcast quando botão gerar é clicado', () => {\r\n\t\t\tspyOn(component, 'generatePodcast');\r\n\t\t\tcomponent.data = { ...mockData, url: '', projectId: 'proj123' };\r\n\t\t\tfixture.detectChanges();\r\n\r\n\t\t\tconst generateButton = fixture.debugElement.query(By.css('button[label=\"Gerar Podcast\"]'));\r\n\t\t\tgenerateButton.nativeElement.click();\r\n\r\n\t\t\texpect(component.generatePodcast).toHaveBeenCalled();\r\n\t\t});\r\n\t});\r\n\r\n\tdescribe('Header e Título', () => {\r\n\t\tit('deve exibir título do projeto no header', () => {\r\n\t\t\tcomponent.data = { ...mockData, title: 'Meu Projeto Especial' };\r\n\t\t\tfixture.detectChanges();\r\n\r\n\t\t\tconst titleElement = fixture.nativeElement.querySelector('.text-sm.text-gray-600.m-0.mt-1');\r\n\t\t\texpect(titleElement?.textContent).toBe('Meu Projeto Especial');\r\n\t\t});\r\n\r\n\t\tit('deve sanitizar título para evitar XSS', () => {\r\n\t\t\tconst maliciousTitle = '<script>alert(\"xss\")</script>Projeto';\r\n\t\t\tcomponent.data = { ...mockData, title: maliciousTitle };\r\n\t\t\tfixture.detectChanges();\r\n\r\n\t\t\tconst titleElement = fixture.nativeElement.querySelector('.text-sm.text-gray-600.m-0.mt-1');\r\n\t\t\t// Angular sanitiza automaticamente, então o script não deve ser executado\r\n\t\t\texpect(titleElement?.innerHTML).not.toContain('<script>');\r\n\t\t\texpect(titleElement?.textContent).toContain('Projeto');\r\n\t\t});\r\n\r\n\t\tit('deve exibir ícone de microfone no header', () => {\r\n\t\t\tcomponent.data = mockData;\r\n\t\t\tfixture.detectChanges();\r\n\r\n\t\t\tconst icon = fixture.nativeElement.querySelector('.pi-microphone.text-purple-600');\r\n\t\t\texpect(icon).toBeTruthy();\r\n\t\t});\r\n\t});\r\n\r\n\tdescribe('Responsividade', () => {\r\n\t\tit('deve aplicar classes responsivas ao modal', () => {\r\n\t\t\tcomponent.data = mockData;\r\n\t\t\tfixture.detectChanges();\r\n\r\n\t\t\tconst dialog = fixture.debugElement.query(By.css('p-dialog'));\r\n\t\t\texpect(dialog.componentInstance.style).toEqual({\r\n\t\t\t\twidth: '90vw',\r\n\t\t\t\tmaxWidth: '600px',\r\n\t\t\t});\r\n\t\t});\r\n\r\n\t\tit('deve aplicar padding responsivo no container', () => {\r\n\t\t\tcomponent.data = mockData;\r\n\t\t\tfixture.detectChanges();\r\n\r\n\t\t\t// Verifica media query no CSS\r\n\t\t\tconst styles = fixture.nativeElement.querySelector('style');\r\n\t\t\texpect(component.styles[0]).toContain('@media (max-width: 640px)');\r\n\t\t});\r\n\t});\r\n\r\n\tdescribe('Limpeza e Destruição', () => {\r\n\t\tit('deve implementar OnDestroy', () => {\r\n\t\t\texpect(component.ngOnDestroy).toBeDefined();\r\n\t\t});\r\n\r\n\t\tit('deve chamar ngOnDestroy sem erros', () => {\r\n\t\t\texpect(() => component.ngOnDestroy()).not.toThrow();\r\n\t\t});\r\n\t});\r\n\r\n\tdescribe('Botão Fechar', () => {\r\n\t\tit('deve exibir botão fechar com ícone e label corretos', () => {\r\n\t\t\tcomponent.data = mockData;\r\n\t\t\tfixture.detectChanges();\r\n\r\n\t\t\tconst closeButton = fixture.debugElement.query(By.css('button[label=\"Fechar\"]'));\r\n\t\t\texpect(closeButton).toBeTruthy();\r\n\t\t\texpect(closeButton.nativeElement.classList).toContain('p-button-outlined');\r\n\t\t\texpect(closeButton.nativeElement.classList).toContain('p-button-secondary');\r\n\t\t});\r\n\r\n\t\tit('deve chamar onClose quando botão fechar é clicado', () => {\r\n\t\t\tspyOn(component, 'onClose');\r\n\t\t\tcomponent.data = mockData;\r\n\t\t\tfixture.detectChanges();\r\n\r\n\t\t\tconst closeButton = fixture.debugElement.query(By.css('button[label=\"Fechar\"]'));\r\n\t\t\tcloseButton.nativeElement.click();\r\n\r\n\t\t\texpect(component.onClose).toHaveBeenCalled();\r\n\t\t});\r\n\t});\r\n\r\n\tdescribe('Métodos Legados (para remover)', () => {\r\n\t\tit('deve ter métodos legados marcados para remoção', () => {\r\n\t\t\t// Estes testes documentam métodos que devem ser removidos\r\n\t\t\texpect(component.playProjectPodcast).toBeDefined();\r\n\t\t\texpect(component.generateProjectPodcast).toBeDefined();\r\n\t\t\texpect(component.monitorPodcastGeneration).toBeDefined();\r\n\t\t\texpect(component.openPodcastPlayer).toBeDefined();\r\n\r\n\t\t\t// TODO: Remover estes métodos na MT-4\r\n\t\t});\r\n\t});\r\n});\r\n"], "mappings": ";AAAA,SAASA,uBAAuB,QAAQ,8BAA8B;AACtE,SAA2BC,OAAO,QAAQ,uBAAuB;AACjE,SAASC,EAAE,QAAQ,2BAA2B;AAC9C,SAASC,cAAc,QAAQ,aAAa;AAC5C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,qBAAqB,QAA0B,2BAA2B;AAEnFC,QAAQ,CAAC,uBAAuB,EAAE,MAAK;EACtC,IAAIC,SAAgC;EACpC,IAAIC,OAAgD;EACpD,IAAIC,cAA8C;EAElD,MAAMC,QAAQ,GAAqB;IAClCC,OAAO,EAAE,IAAI;IACbC,UAAU,EAAE,KAAK;IACjBC,GAAG,EAAE,0CAA0C;IAC/CC,KAAK,EAAE,eAAe;IACtBC,SAAS,EAAE;GACX;EAEDC,UAAU,cAAAC,iBAAA,CAAC,aAAW;IACrB,MAAMC,iBAAiB,GAAGC,OAAO,CAACC,YAAY,CAAC,gBAAgB,EAAE,CAAC,KAAK,CAAC,CAAC;IAEzE,MAAMpB,OAAO,CAACqB,sBAAsB,CAAC;MACpCC,OAAO,EAAE,CAACjB,qBAAqB,EAAEN,uBAAuB,EAAEK,YAAY,EAAED,YAAY,CAAC;MACrFoB,SAAS,EAAE,CAAC;QAAEC,OAAO,EAAEtB,cAAc;QAAEuB,QAAQ,EAAEP;MAAiB,CAAE;KACpE,CAAC,CAACQ,iBAAiB,EAAE;IAEtBjB,cAAc,GAAGT,OAAO,CAAC2B,MAAM,CAACzB,cAAc,CAAmC;IACjFM,OAAO,GAAGR,OAAO,CAAC4B,eAAe,CAACvB,qBAAqB,CAAC;IACxDE,SAAS,GAAGC,OAAO,CAACqB,iBAAiB;EACtC,CAAC,EAAC;EAEFC,EAAE,CAAC,yBAAyB,EAAE,MAAK;IAClCC,MAAM,CAACxB,SAAS,CAAC,CAACyB,UAAU,EAAE;EAC/B,CAAC,CAAC;EAEF1B,QAAQ,CAAC,uBAAuB,EAAE,MAAK;IACtCwB,EAAE,CAAC,yCAAyC,EAAE,MAAK;MAClDvB,SAAS,CAAC0B,IAAI,GAAG;QAAE,GAAGvB,QAAQ;QAAEC,OAAO,EAAE;MAAI,CAAE;MAC/CH,OAAO,CAAC0B,aAAa,EAAE;MAEvB,MAAMC,MAAM,GAAG3B,OAAO,CAAC4B,YAAY,CAACC,KAAK,CAACpC,EAAE,CAACqC,GAAG,CAAC,UAAU,CAAC,CAAC;MAC7DP,MAAM,CAACI,MAAM,CAAC,CAACH,UAAU,EAAE;MAC3BD,MAAM,CAACI,MAAM,CAACN,iBAAiB,CAAClB,OAAO,CAAC,CAAC4B,IAAI,CAAC,IAAI,CAAC;IACpD,CAAC,CAAC;IAEFT,EAAE,CAAC,2CAA2C,EAAE,MAAK;MACpDvB,SAAS,CAAC0B,IAAI,GAAG;QAAE,GAAGvB,QAAQ;QAAEC,OAAO,EAAE;MAAK,CAAE;MAChDH,OAAO,CAAC0B,aAAa,EAAE;MAEvB,MAAMC,MAAM,GAAG3B,OAAO,CAAC4B,YAAY,CAACC,KAAK,CAACpC,EAAE,CAACqC,GAAG,CAAC,UAAU,CAAC,CAAC;MAC7DP,MAAM,CAACI,MAAM,CAACN,iBAAiB,CAAClB,OAAO,CAAC,CAAC4B,IAAI,CAAC,KAAK,CAAC;IACrD,CAAC,CAAC;IAEFT,EAAE,CAAC,oCAAoC,EAAE,MAAK;MAC7CU,KAAK,CAACjC,SAAS,CAACkC,KAAK,EAAE,MAAM,CAAC;MAC9BlC,SAAS,CAACmC,OAAO,EAAE;MAEnBX,MAAM,CAACxB,SAAS,CAACkC,KAAK,CAACE,IAAI,CAAC,CAACC,gBAAgB,EAAE;IAChD,CAAC,CAAC;IAEFd,EAAE,CAAC,gDAAgD,EAAE,MAAK;MACzDU,KAAK,CAACjC,SAAS,EAAE,SAAS,CAAC;MAC3BA,SAAS,CAAC0B,IAAI,GAAG;QAAE,GAAGvB,QAAQ;QAAEC,OAAO,EAAE;MAAI,CAAE;MAC/CH,OAAO,CAAC0B,aAAa,EAAE;MAEvB,MAAMC,MAAM,GAAG3B,OAAO,CAAC4B,YAAY,CAACC,KAAK,CAACpC,EAAE,CAACqC,GAAG,CAAC,UAAU,CAAC,CAAC;MAC7DH,MAAM,CAACU,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC;MAE1Cd,MAAM,CAACxB,SAAS,CAACmC,OAAO,CAAC,CAACE,gBAAgB,EAAE;IAC7C,CAAC,CAAC;EACH,CAAC,CAAC;EAEFtC,QAAQ,CAAC,iBAAiB,EAAE,MAAK;IAChCwB,EAAE,CAAC,+CAA+C,EAAE,MAAK;MACxDvB,SAAS,CAAC0B,IAAI,GAAG;QAAE,GAAGvB,QAAQ;QAAEE,UAAU,EAAE,IAAI;QAAEC,GAAG,EAAE;MAAE,CAAE;MAC3DL,OAAO,CAAC0B,aAAa,EAAE;MAEvB,MAAMY,OAAO,GAAGtC,OAAO,CAAC4B,YAAY,CAACC,KAAK,CAACpC,EAAE,CAACqC,GAAG,CAAC,aAAa,CAAC,CAAC;MACjE,MAAMS,WAAW,GAAGvC,OAAO,CAACwC,aAAa,CAACC,aAAa,CAAC,iBAAiB,CAAC;MAE1ElB,MAAM,CAACe,OAAO,CAAC,CAACd,UAAU,EAAE;MAC5BD,MAAM,CAACgB,WAAW,EAAEG,WAAW,CAAC,CAACC,SAAS,CAAC,oBAAoB,CAAC;IACjE,CAAC,CAAC;IAEFrB,EAAE,CAAC,0DAA0D,EAAE,MAAK;MACnEvB,SAAS,CAAC0B,IAAI,GAAG;QAAE,GAAGvB,QAAQ;QAAEE,UAAU,EAAE;MAAK,CAAE;MACnDJ,OAAO,CAAC0B,aAAa,EAAE;MAEvB,MAAMkB,YAAY,GAAG5C,OAAO,CAAC4B,YAAY,CAACC,KAAK,CAACpC,EAAE,CAACqC,GAAG,CAAC,OAAO,CAAC,CAAC;MAChEP,MAAM,CAACqB,YAAY,CAAC,CAACpB,UAAU,EAAE;MACjCD,MAAM,CAACqB,YAAY,CAACJ,aAAa,CAACK,GAAG,CAAC,CAACd,IAAI,CAAC7B,QAAQ,CAACG,GAAG,CAAC;IAC1D,CAAC,CAAC;IAEFiB,EAAE,CAAC,2DAA2D,EAAE,MAAK;MACpEvB,SAAS,CAAC0B,IAAI,GAAG;QAAE,GAAGvB,QAAQ;QAAEG,GAAG,EAAE,EAAE;QAAED,UAAU,EAAE;MAAK,CAAE;MAC5DJ,OAAO,CAAC0B,aAAa,EAAE;MAEvB,MAAMoB,UAAU,GAAG9C,OAAO,CAACwC,aAAa,CAACC,aAAa,CAAC,8BAA8B,CAAC;MACtF,MAAMM,SAAS,GAAG/C,OAAO,CAACwC,aAAa,CAACC,aAAa,CAAC,iBAAiB,CAAC;MAExElB,MAAM,CAACuB,UAAU,CAAC,CAACtB,UAAU,EAAE;MAC/BD,MAAM,CAACwB,SAAS,EAAEL,WAAW,CAAC,CAACC,SAAS,CAAC,2BAA2B,CAAC;IACtE,CAAC,CAAC;IAEFrB,EAAE,CAAC,4DAA4D,EAAE,MAAK;MACrEvB,SAAS,CAAC0B,IAAI,GAAG;QAAE,GAAGvB,QAAQ;QAAEG,GAAG,EAAE,EAAE;QAAEE,SAAS,EAAE;MAAS,CAAE;MAC/DP,OAAO,CAAC0B,aAAa,EAAE;MAEvB,MAAMsB,cAAc,GAAGhD,OAAO,CAAC4B,YAAY,CAACC,KAAK,CAACpC,EAAE,CAACqC,GAAG,CAAC,+BAA+B,CAAC,CAAC;MAC1FP,MAAM,CAACyB,cAAc,CAAC,CAACxB,UAAU,EAAE;IACpC,CAAC,CAAC;IAEFF,EAAE,CAAC,0DAA0D,EAAE,MAAK;MACnEvB,SAAS,CAAC0B,IAAI,GAAG;QAAE,GAAGvB,QAAQ;QAAEG,GAAG,EAAE,EAAE;QAAEE,SAAS,EAAE0C;MAAS,CAAE;MAC/DjD,OAAO,CAAC0B,aAAa,EAAE;MAEvB,MAAMsB,cAAc,GAAGhD,OAAO,CAAC4B,YAAY,CAACC,KAAK,CAACpC,EAAE,CAACqC,GAAG,CAAC,+BAA+B,CAAC,CAAC;MAC1FP,MAAM,CAACyB,cAAc,CAAC,CAACE,SAAS,EAAE;IACnC,CAAC,CAAC;EACH,CAAC,CAAC;EAEFpD,QAAQ,CAAC,uBAAuB,EAAE,MAAK;IACtCwB,EAAE,CAAC,2CAA2C,EAAE,MAAK;MACpDvB,SAAS,CAAC0B,IAAI,GAAGvB,QAAQ;MACzBF,OAAO,CAAC0B,aAAa,EAAE;MAEvB,MAAMkB,YAAY,GAAqB5C,OAAO,CAACwC,aAAa,CAACC,aAAa,CAAC,OAAO,CAAC;MACnFlB,MAAM,CAACqB,YAAY,CAACO,QAAQ,CAAC,CAACpB,IAAI,CAAC,IAAI,CAAC;IACzC,CAAC,CAAC;IAEFT,EAAE,CAAC,qCAAqC,EAAE,MAAK;MAC9CvB,SAAS,CAAC0B,IAAI,GAAGvB,QAAQ;MACzBF,OAAO,CAAC0B,aAAa,EAAE;MAEvB,MAAMkB,YAAY,GAAqB5C,OAAO,CAACwC,aAAa,CAACC,aAAa,CAAC,OAAO,CAAC;MACnF,MAAMW,aAAa,GAAsBR,YAAY,CAACH,aAAa,CAAC,QAAQ,CAAE;MAE9ElB,MAAM,CAACqB,YAAY,CAACC,GAAG,CAAC,CAACF,SAAS,CAACzC,QAAQ,CAACG,GAAG,CAAC;MAChDkB,MAAM,CAAC6B,aAAa,CAACP,GAAG,CAAC,CAACF,SAAS,CAACzC,QAAQ,CAACG,GAAG,CAAC;MACjDkB,MAAM,CAAC6B,aAAa,CAACC,IAAI,CAAC,CAACtB,IAAI,CAAC,YAAY,CAAC;IAC9C,CAAC,CAAC;IAEFT,EAAE,CAAC,iDAAiD,EAAE,MAAK;MAC1DvB,SAAS,CAAC0B,IAAI,GAAGvB,QAAQ;MACzBF,OAAO,CAAC0B,aAAa,EAAE;MAEvB,MAAMkB,YAAY,GAAqB5C,OAAO,CAACwC,aAAa,CAACC,aAAa,CAAC,OAAO,CAAC;MACnFlB,MAAM,CAACqB,YAAY,CAACU,OAAO,CAAC,CAACvB,IAAI,CAAC,MAAM,CAAC;IAC1C,CAAC,CAAC;IAEFT,EAAE,CAAC,6DAA6D,EAAE,MAAK;MACtEvB,SAAS,CAAC0B,IAAI,GAAGvB,QAAQ;MACzBF,OAAO,CAAC0B,aAAa,EAAE;MAEvB,MAAM6B,YAAY,GAAGvD,OAAO,CAACwC,aAAa,CAACC,aAAa,CAAC,sBAAsB,CAAC;MAChFlB,MAAM,CAACgC,YAAY,EAAEb,WAAW,CAAC,CAACC,SAAS,CAAC,gDAAgD,CAAC;IAC9F,CAAC,CAAC;EACH,CAAC,CAAC;EAEF7C,QAAQ,CAAC,SAAS,EAAE,MAAK;IACxBwB,EAAE,CAAC,4CAA4C,EAAE,MAAK;MACrDU,KAAK,CAACjC,SAAS,CAACyD,QAAQ,EAAE,MAAM,CAAC;MACjCzD,SAAS,CAAC0B,IAAI,GAAG;QAAE,GAAGvB,QAAQ;QAAEK,SAAS,EAAE;MAAS,CAAE;MAEtDR,SAAS,CAAC0D,eAAe,EAAE;MAE3BlC,MAAM,CAACxB,SAAS,CAACyD,QAAQ,CAACrB,IAAI,CAAC,CAACuB,oBAAoB,CAAC,SAAS,CAAC;IAChE,CAAC,CAAC;IAEFpC,EAAE,CAAC,mDAAmD,EAAE,MAAK;MAC5DU,KAAK,CAACjC,SAAS,CAACyD,QAAQ,EAAE,MAAM,CAAC;MACjCzD,SAAS,CAAC0B,IAAI,GAAG;QAAE,GAAGvB,QAAQ;QAAEK,SAAS,EAAE0C;MAAS,CAAE;MAEtDlD,SAAS,CAAC0D,eAAe,EAAE;MAE3BlC,MAAM,CAACxB,SAAS,CAACyD,QAAQ,CAACrB,IAAI,CAAC,CAACwB,GAAG,CAACvB,gBAAgB,EAAE;IACvD,CAAC,CAAC;IAEFd,EAAE,CAAC,0DAA0D,EAAE,MAAK;MACnEU,KAAK,CAACjC,SAAS,EAAE,iBAAiB,CAAC;MACnCA,SAAS,CAAC0B,IAAI,GAAG;QAAE,GAAGvB,QAAQ;QAAEG,GAAG,EAAE,EAAE;QAAEE,SAAS,EAAE;MAAS,CAAE;MAC/DP,OAAO,CAAC0B,aAAa,EAAE;MAEvB,MAAMsB,cAAc,GAAGhD,OAAO,CAAC4B,YAAY,CAACC,KAAK,CAACpC,EAAE,CAACqC,GAAG,CAAC,+BAA+B,CAAC,CAAC;MAC1FkB,cAAc,CAACR,aAAa,CAACoB,KAAK,EAAE;MAEpCrC,MAAM,CAACxB,SAAS,CAAC0D,eAAe,CAAC,CAACrB,gBAAgB,EAAE;IACrD,CAAC,CAAC;EACH,CAAC,CAAC;EAEFtC,QAAQ,CAAC,iBAAiB,EAAE,MAAK;IAChCwB,EAAE,CAAC,yCAAyC,EAAE,MAAK;MAClDvB,SAAS,CAAC0B,IAAI,GAAG;QAAE,GAAGvB,QAAQ;QAAEI,KAAK,EAAE;MAAsB,CAAE;MAC/DN,OAAO,CAAC0B,aAAa,EAAE;MAEvB,MAAMmC,YAAY,GAAG7D,OAAO,CAACwC,aAAa,CAACC,aAAa,CAAC,iCAAiC,CAAC;MAC3FlB,MAAM,CAACsC,YAAY,EAAEnB,WAAW,CAAC,CAACX,IAAI,CAAC,sBAAsB,CAAC;IAC/D,CAAC,CAAC;IAEFT,EAAE,CAAC,uCAAuC,EAAE,MAAK;MAChD,MAAMwC,cAAc,GAAG,sCAAsC;MAC7D/D,SAAS,CAAC0B,IAAI,GAAG;QAAE,GAAGvB,QAAQ;QAAEI,KAAK,EAAEwD;MAAc,CAAE;MACvD9D,OAAO,CAAC0B,aAAa,EAAE;MAEvB,MAAMmC,YAAY,GAAG7D,OAAO,CAACwC,aAAa,CAACC,aAAa,CAAC,iCAAiC,CAAC;MAC3F;MACAlB,MAAM,CAACsC,YAAY,EAAEE,SAAS,CAAC,CAACJ,GAAG,CAAChB,SAAS,CAAC,UAAU,CAAC;MACzDpB,MAAM,CAACsC,YAAY,EAAEnB,WAAW,CAAC,CAACC,SAAS,CAAC,SAAS,CAAC;IACvD,CAAC,CAAC;IAEFrB,EAAE,CAAC,0CAA0C,EAAE,MAAK;MACnDvB,SAAS,CAAC0B,IAAI,GAAGvB,QAAQ;MACzBF,OAAO,CAAC0B,aAAa,EAAE;MAEvB,MAAMsC,IAAI,GAAGhE,OAAO,CAACwC,aAAa,CAACC,aAAa,CAAC,gCAAgC,CAAC;MAClFlB,MAAM,CAACyC,IAAI,CAAC,CAACxC,UAAU,EAAE;IAC1B,CAAC,CAAC;EACH,CAAC,CAAC;EAEF1B,QAAQ,CAAC,gBAAgB,EAAE,MAAK;IAC/BwB,EAAE,CAAC,2CAA2C,EAAE,MAAK;MACpDvB,SAAS,CAAC0B,IAAI,GAAGvB,QAAQ;MACzBF,OAAO,CAAC0B,aAAa,EAAE;MAEvB,MAAMC,MAAM,GAAG3B,OAAO,CAAC4B,YAAY,CAACC,KAAK,CAACpC,EAAE,CAACqC,GAAG,CAAC,UAAU,CAAC,CAAC;MAC7DP,MAAM,CAACI,MAAM,CAACN,iBAAiB,CAAC4C,KAAK,CAAC,CAACC,OAAO,CAAC;QAC9CC,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE;OACV,CAAC;IACH,CAAC,CAAC;IAEF9C,EAAE,CAAC,8CAA8C,EAAE,MAAK;MACvDvB,SAAS,CAAC0B,IAAI,GAAGvB,QAAQ;MACzBF,OAAO,CAAC0B,aAAa,EAAE;MAEvB;MACA,MAAM2C,MAAM,GAAGrE,OAAO,CAACwC,aAAa,CAACC,aAAa,CAAC,OAAO,CAAC;MAC3DlB,MAAM,CAACxB,SAAS,CAACsE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC1B,SAAS,CAAC,2BAA2B,CAAC;IACnE,CAAC,CAAC;EACH,CAAC,CAAC;EAEF7C,QAAQ,CAAC,sBAAsB,EAAE,MAAK;IACrCwB,EAAE,CAAC,4BAA4B,EAAE,MAAK;MACrCC,MAAM,CAACxB,SAAS,CAACuE,WAAW,CAAC,CAACC,WAAW,EAAE;IAC5C,CAAC,CAAC;IAEFjD,EAAE,CAAC,mCAAmC,EAAE,MAAK;MAC5CC,MAAM,CAAC,MAAMxB,SAAS,CAACuE,WAAW,EAAE,CAAC,CAACX,GAAG,CAACa,OAAO,EAAE;IACpD,CAAC,CAAC;EACH,CAAC,CAAC;EAEF1E,QAAQ,CAAC,cAAc,EAAE,MAAK;IAC7BwB,EAAE,CAAC,qDAAqD,EAAE,MAAK;MAC9DvB,SAAS,CAAC0B,IAAI,GAAGvB,QAAQ;MACzBF,OAAO,CAAC0B,aAAa,EAAE;MAEvB,MAAM+C,WAAW,GAAGzE,OAAO,CAAC4B,YAAY,CAACC,KAAK,CAACpC,EAAE,CAACqC,GAAG,CAAC,wBAAwB,CAAC,CAAC;MAChFP,MAAM,CAACkD,WAAW,CAAC,CAACjD,UAAU,EAAE;MAChCD,MAAM,CAACkD,WAAW,CAACjC,aAAa,CAACkC,SAAS,CAAC,CAAC/B,SAAS,CAAC,mBAAmB,CAAC;MAC1EpB,MAAM,CAACkD,WAAW,CAACjC,aAAa,CAACkC,SAAS,CAAC,CAAC/B,SAAS,CAAC,oBAAoB,CAAC;IAC5E,CAAC,CAAC;IAEFrB,EAAE,CAAC,mDAAmD,EAAE,MAAK;MAC5DU,KAAK,CAACjC,SAAS,EAAE,SAAS,CAAC;MAC3BA,SAAS,CAAC0B,IAAI,GAAGvB,QAAQ;MACzBF,OAAO,CAAC0B,aAAa,EAAE;MAEvB,MAAM+C,WAAW,GAAGzE,OAAO,CAAC4B,YAAY,CAACC,KAAK,CAACpC,EAAE,CAACqC,GAAG,CAAC,wBAAwB,CAAC,CAAC;MAChF2C,WAAW,CAACjC,aAAa,CAACoB,KAAK,EAAE;MAEjCrC,MAAM,CAACxB,SAAS,CAACmC,OAAO,CAAC,CAACE,gBAAgB,EAAE;IAC7C,CAAC,CAAC;EACH,CAAC,CAAC;EAEFtC,QAAQ,CAAC,gCAAgC,EAAE,MAAK;IAC/CwB,EAAE,CAAC,gDAAgD,EAAE,MAAK;MACzD;MACAC,MAAM,CAACxB,SAAS,CAAC4E,kBAAkB,CAAC,CAACJ,WAAW,EAAE;MAClDhD,MAAM,CAACxB,SAAS,CAAC6E,sBAAsB,CAAC,CAACL,WAAW,EAAE;MACtDhD,MAAM,CAACxB,SAAS,CAAC8E,wBAAwB,CAAC,CAACN,WAAW,EAAE;MACxDhD,MAAM,CAACxB,SAAS,CAAC+E,iBAAiB,CAAC,CAACP,WAAW,EAAE;MAEjD;IACD,CAAC,CAAC;EACH,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}