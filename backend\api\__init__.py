"""
Interface API Padronizada - ScopeAI

Este módulo implementa uma API RESTful padronizada que serve como 
ponte entre todos os módulos do backend e o frontend Angular.

Componentes:
- APIRouter: Roteamento central unificado
- BaseController: Classe base para controladores
- ResponseFormatter: Formatação consistente de respostas
- ErrorHandler: Tratamento centralizado de erros
- Middlewares: Autenticação e validação
- Controllers: Controladores específicos por módulo
"""

from .router import APIRouter
from .base_controller import BaseController
from .response_formatter import ResponseFormatter, APIResponse
from .error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, APIError
from .middlewares.auth_middleware import AuthMiddleware
from .middlewares.validation_middleware import ValidationMiddleware

__all__ = [
    "APIRouter",
    "BaseController",
    "ResponseFormatter",
    "APIResponse",
    "ErrorHandler",
    "APIError",
    "AuthMiddleware",
    "ValidationMiddleware"
]
