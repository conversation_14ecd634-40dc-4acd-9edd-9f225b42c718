import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { AccordionModule } from 'primeng/accordion';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { DividerModule } from 'primeng/divider';
import { TabViewModule } from 'primeng/tabview';
import { TagModule } from 'primeng/tag';
import { TimelineModule } from 'primeng/timeline';
import { IFeature, IIntegration, ITimelineEvent } from './about.interface';

@Component({
	selector: 'app-about-home',
	templateUrl: './about-home.component.html',
	standalone: true,
	imports: [
		CommonModule,
		CardModule,
		TabViewModule,
		DividerModule,
		TimelineModule,
		ButtonModule,
		TagModule,
		AccordionModule,
	],
})
export class AboutHomeComponent implements OnInit {
	public systemVersion: string = '1.0.0';
	public lastUpdate: string = '20/05/2025';

	public events: Array<ITimelineEvent> = [];
	public currentFeatures: Array<IFeature> = [];
	public futureFeatures: Array<IFeature> = [];
	public integrations: Array<IIntegration> = [];

	ngOnInit(): void {
		this.loadRoadmapEvents();
		this.loadCurrentFeatures();
		this.loadFutureFeatures();
		this.loadIntegrations();
	}

	private loadRoadmapEvents(): void {
		this.events = [
			{
				status: 'Concluído',
				date: 'Março/2025',
				icon: 'pi pi-check-circle',
				color: '#22C55E',
				description:
					'Lançamento inicial do ScopeAI com módulos básicos de gerenciamento de projetos e clientes.',
			},
			{
				status: 'Concluído',
				date: 'Abril/2025',
				icon: 'pi pi-check-circle',
				color: '#22C55E',
				description: 'Implementação do módulo de configurações personalizáveis e perfis de usuário.',
			},
			{
				status: 'Em andamento',
				date: 'Maio/2025',
				icon: 'pi pi-sync',
				color: '#F59E0B',
				description: 'Refinamento da experiência do usuário e correções de bugs.',
			},
			{
				status: 'Planejado',
				date: 'Junho/2025',
				icon: 'pi pi-calendar',
				color: '#3B82F6',
				description:
					'Integração de inteligência artificial para análise de padrões de projetos e recomendações.',
			},
			{
				status: 'Planejado',
				date: 'Setembro/2025',
				icon: 'pi pi-calendar',
				color: '#3B82F6',
				description:
					'Implementação de reconhecimento facial para identificação de clientes e análise de comportamento.',
			},
			{
				status: 'Planejado',
				date: 'Dezembro/2025',
				icon: 'pi pi-calendar',
				color: '#3B82F6',
				description: 'Lançamento de assistente de voz baseado em IA para estimativas de escopo em tempo real.',
			},
		];
	}

	private loadCurrentFeatures(): void {
		this.currentFeatures = [
			{
				id: 'dashboard',
				title: 'Dashboard Inteligente',
				description:
					'Visão consolidada de projetos, clientes e métricas-chave com gráficos interativos para acompanhamento de desempenho.',
				icon: 'pi pi-chart-bar',
				status: 'disponível',
			},
			{
				id: 'projects',
				title: 'Gerenciamento de Projetos',
				description:
					'Gestão completa de projetos com acompanhamento de prazos, recursos, orçamentos e status em tempo real.',
				icon: 'pi pi-briefcase',
				status: 'disponível',
			},
			{
				id: 'clients',
				title: 'Gestão de Clientes',
				description:
					'Cadastro e acompanhamento de clientes, contatos e histórico de interações para melhorar relacionamentos comerciais.',
				icon: 'pi pi-users',
				status: 'disponível',
			},
			{
				id: 'settings',
				title: 'Configurações Personalizáveis',
				description:
					'Personalização completa do sistema, incluindo preferências de interface, notificações e integrações com serviços externos.',
				icon: 'pi pi-cog',
				status: 'disponível',
			},
		];
	}

	private loadFutureFeatures(): void {
		this.futureFeatures = [
			{
				id: 'ml-analysis',
				title: 'Análise Preditiva com Machine Learning',
				description:
					'Uso de algoritmos de aprendizado de máquina para analisar dados históricos de projetos e clientes, identificando padrões de comportamento, frequência de negociação e potencial de novos negócios.',
				icon: 'pi pi-chart-line',
				status: 'em desenvolvimento',
			},
			{
				id: 'facial-recognition',
				title: 'Reconhecimento Facial em Tempo Real',
				description:
					'Tecnologia de visão computacional para identificar clientes em reuniões presenciais ou virtuais, fornecendo informações relevantes sobre histórico de interações e preferências para personalizar o atendimento.',
				icon: 'pi pi-camera',
				status: 'planejado',
			},
			{
				id: 'voice-assistant',
				title: 'Assistente de Voz para Estimativas',
				description:
					'Interface conversacional por voz que permite discutir e gerar estimativas de escopo de projeto em tempo real durante reuniões, utilizando processamento de linguagem natural avançado.',
				icon: 'pi pi-volume-up',
				status: 'planejado',
			},
			{
				id: 'sentiment-analysis',
				title: 'Análise de Sentimento em Reuniões',
				description:
					'Algoritmos de análise de sentimento para avaliar o tom emocional durante reuniões com clientes, auxiliando na identificação do momento ideal para negociações ou apresentação de propostas.',
				icon: 'pi pi-heart',
				status: 'planejado',
			},
			{
				id: 'auto-documentation',
				title: 'Documentação Automática de Projetos',
				description:
					'Geração automática de documentação de projetos baseada em conversas, reuniões e atividades registradas no sistema, utilizando IA generativa para criar documentos profissionais.',
				icon: 'pi pi-file-pdf',
				status: 'planejado',
			},
			{
				id: 'personalized-proposals',
				title: 'Geração de Propostas Personalizadas',
				description:
					'Sistema de criação automática de propostas comerciais adaptadas ao perfil de cada cliente, utilizando dados históricos e preferências identificadas por algoritmos de IA.',
				icon: 'pi pi-file-edit',
				status: 'planejado',
			},
		];
	}

	private loadIntegrations(): void {
		this.integrations = [
			{
				title: 'GitHub',
				description: 'Integração com repositórios para acompanhamento de código e tarefas técnicas.',
				icon: 'pi pi-github',
				available: true,
			},
			{
				title: 'Microsoft Teams',
				description: 'Comunicação e compartilhamento de atualizações de projeto direto no canal da equipe.',
				icon: 'pi pi-microsoft',
				available: true,
			},
			{
				title: 'Google Workspace',
				description: 'Sincronização com documentos, planilhas e calendário do Google.',
				icon: 'pi pi-google',
				available: false,
			},
			{
				title: 'ChatGPT API',
				description: 'Utilização de modelos avançados de IA para assistência e geração de conteúdo.',
				icon: 'pi pi-comment',
				available: false,
			},
			{
				title: 'Zoom',
				description: 'Análise automática de reuniões para extração de insights e pontos de ação.',
				icon: 'pi pi-video',
				available: false,
			},
		];
	}

	public getStatusSeverity(status: string): string {
		switch (status) {
			case 'disponível':
				return 'success';
			case 'em desenvolvimento':
				return 'warning';
			case 'planejado':
				return 'info';
			default:
				return 'secondary';
		}
	}

	public getAvailabilityStatus(available: boolean): string {
		return available ? 'success' : 'secondary';
	}

	public getAvailabilityText(available: boolean): string {
		return available ? 'Disponível' : 'Em breve';
	}
}
