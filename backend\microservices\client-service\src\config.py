"""
Configuration module for Client Service

Uses Pydantic Settings for environment variable management
and configuration validation.
"""

from functools import lru_cache
from typing import Optional, List

from pydantic import Field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """
    Configurações do Client Service.

    Reutiliza as variáveis de ambiente do backend principal (/backend/.env)
    mapeando para os nomes esperados pelo microserviço.
    """

    model_config = SettingsConfigDict(
        env_file="../../../.env",
        env_file_encoding="utf-8",
        case_sensitive=False,
    )

    # Service Info
    service_name: str = Field(default="client-service", alias="SERVICE_NAME")
    service_version: str = Field(default="0.1.0", alias="SERVICE_VERSION")
    environment: str = Field(default="development", alias="ENVIRONMENT")
    debug: bool = Field(default=True, alias="DEBUG")

    # API Settings
    api_v2_prefix: str = Field(default="/api/v2", alias="API_V2_PREFIX")
    api_title: str = "Client Service API"
    api_description: str = "Microservice for client management - ScopeAI"

    # Server Settings
    host: str = Field(default="0.0.0.0", alias="SERVER_HOST")
    # Porta diferente para não conflitar
    port: int = Field(default=8001, alias="SERVER_PORT")
    workers: int = Field(default=1, description="Number of workers")

    # MongoDB Settings
    mongodb_uri: str = Field(alias="MONGODB_ATLAS_CONNECTION_URI")
    mongodb_database: str = Field(
        default="innovation_scope", alias="MONGODB_DATABASE_NAME")
    mongodb_collection_prefix: str = Field(
        default="v2_", alias="MONGODB_COLLECTION_PREFIX")
    mongodb_max_pool_size: int = Field(
        default=10, alias="MONGODB_MAX_POOL_SIZE")
    mongodb_min_pool_size: int = Field(
        default=1, alias="MONGODB_MIN_POOL_SIZE")

    # Redis Settings
    redis_url: str = Field(alias="REDIS_CLOUD_CONNECTION_URI")
    redis_cache_ttl: int = Field(default=3600, alias="REDIS_CACHE_TTL")

    # RabbitMQ Settings
    rabbitmq_url: str = Field(
        default="amqp://guest:guest@localhost:5672/",
        description="RabbitMQ connection URL"
    )
    rabbitmq_exchange: str = Field(
        default="scopeai.events",
        description="RabbitMQ exchange name"
    )
    rabbitmq_queue_prefix: str = Field(
        default="client-service",
        description="Queue name prefix"
    )

    # JWT Settings
    jwt_secret_key: str = Field(
        default="your-secret-key-here-change-in-production",
        alias="JWT_SECRET_KEY"
    )
    jwt_algorithm: str = Field(default="HS256", alias="JWT_ALGORITHM")
    jwt_expiration_minutes: int = Field(
        default=1440, alias="JWT_EXPIRATION_MINUTES")

    # External Services
    perplexity_api_key: str = Field(alias="PERPLEXITY_API_KEY")
    perplexity_api_url: str = Field(
        default="https://api.perplexity.ai",
        alias="PERPLEXITY_API_URL"
    )
    perplexity_timeout: int = Field(default=300, alias="PERPLEXITY_TIMEOUT")

    # Rate Limiting
    rate_limit_enabled: bool = Field(default=True, alias="RATE_LIMIT_ENABLED")
    rate_limit_requests: int = Field(default=100, alias="RATE_LIMIT_REQUESTS")
    rate_limit_window: int = Field(default=60, alias="RATE_LIMIT_WINDOW")

    # Logging
    log_level: str = Field(default="INFO", alias="LOG_LEVEL")
    log_format: str = Field(default="json", alias="LOG_FORMAT")
    log_file: Optional[str] = Field(default=None)

    # Monitoring
    metrics_enabled: bool = Field(default=True, alias="METRICS_ENABLED")
    metrics_port: int = Field(default=9090, alias="METRICS_PORT")
    tracing_enabled: bool = Field(default=True)
    tracing_endpoint: Optional[str] = Field(default=None)

    # Feature Flags
    feature_cache_enabled: bool = Field(
        default=True, alias="FEATURE_CACHE_ENABLED")
    feature_events_enabled: bool = Field(
        default=True, alias="FEATURE_EVENTS_ENABLED")
    feature_legacy_api_enabled: bool = Field(
        default=True, alias="FEATURE_LEGACY_API_ENABLED")

    # CORS - permitir o frontend
    cors_origins: List[str] = Field(
        default=["http://localhost:4200", "http://localhost:3000"],
        description="Allowed CORS origins"
    )

    @field_validator("environment")
    @classmethod
    def validate_environment(cls, v: str) -> str:
        """Validate environment name"""
        allowed = ["development", "staging", "production", "test"]
        if v not in allowed:
            raise ValueError(f"Environment must be one of {allowed}")
        return v

    @field_validator("log_level")
    @classmethod
    def validate_log_level(cls, v: str) -> str:
        """Validate log level"""
        allowed = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        v = v.upper()
        if v not in allowed:
            raise ValueError(f"Log level must be one of {allowed}")
        return v

    @property
    def mongodb_clients_collection(self) -> str:
        """Get clients collection name with prefix"""
        return f"{self.mongodb_collection_prefix}clients"

    @property
    def mongodb_reports_collection(self) -> str:
        """Get reports collection name with prefix"""
        return f"{self.mongodb_collection_prefix}reports"

    @property
    def is_production(self) -> bool:
        """Check if running in production"""
        return self.environment == "production"

    @property
    def is_development(self) -> bool:
        """Check if running in development"""
        return self.environment == "development"

    @property
    def cors_origins_list(self) -> List[str]:
        """Parse CORS origins from comma-separated string if needed."""
        if isinstance(self.cors_origins, str):
            return [origin.strip() for origin in self.cors_origins.split(",")]
        return self.cors_origins


@lru_cache()
def get_settings() -> Settings:
    """
    Get cached settings instance

    Returns:
        Settings: Application settings
    """
    return Settings()


# Convenience export
settings = get_settings()
