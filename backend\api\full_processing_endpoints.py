"""
Endpoints para processamento completo de clientes
Integra todos os componentes em um fluxo unificado
"""
import asyncio
import logging
from datetime import datetime, UTC
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse
from bson import ObjectId

# Importar orquestrador completo
from tools.orchestration.full_flow_orchestrator import FullFlowOrchestrator, ProcessingStage, ProcessingStatus

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/clients", tags=["full-processing"])


@router.post("/{client_id}/full-processing")
async def start_full_processing(
    client_id: str,
    background_tasks: BackgroundTasks
):
    """
    Inicia o processamento completo de um cliente

    Fluxo completo de 18-25 minutos:
    1. Dossiê expandido com Perplexity
    2. Pesquisas paralelas de mercado e produtos
    3. Diagnósticos técnicos (Lighthouse + Screenshots + IA visual)
    4. Geração de PDF consolidado
    5. Notificação de coleta completa
    6. Execução do time de agentes Agno
    7. Geração final de cards de projetos
    """
    try:
        # Importar async db
        from clients.async_db import async_database

        # Verificar se cliente existe
        try:
            client_obj_id = ObjectId(client_id)
        except Exception:
            raise HTTPException(
                status_code=400, detail="ID de cliente inválido")

        client = await async_database.find_client_by_id(client_id)
        if not client:
            raise HTTPException(
                status_code=404, detail="Cliente não encontrado")

        # Verificar se já não está em processamento
        current_status = client.get("full_processing_status")
        if current_status == ProcessingStatus.IN_PROGRESS.value:
            return JSONResponse(
                status_code=200,
                content={
                    "message": "Processamento já em andamento",
                    "client_id": client_id,
                    "status": current_status,
                    "progress": client.get("full_processing_progress", 0)
                }
            )

        # Marcar como iniciado
        await async_database.update_client(
            client_id,
            {
                "full_processing_status": ProcessingStatus.IN_PROGRESS.value,
                "full_processing_started_at": datetime.now(UTC),
                "full_processing_progress": 0,
                "updated_at": datetime.now(UTC)
            }
        )

        # Iniciar processamento em background
        background_tasks.add_task(
            execute_full_processing,
            client_id,
            client
        )

        return JSONResponse(
            status_code=202,
            content={
                "message": "Processamento completo iniciado",
                "client_id": client_id,
                "estimated_time": "18-25 minutos",
                "status": ProcessingStatus.IN_PROGRESS.value
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao iniciar processamento completo: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Erro interno: {str(e)}"
        )


@router.get("/{client_id}/full-processing/status")
async def get_processing_status(client_id: str):
    """
    Consulta o status do processamento completo
    """
    try:
        from clients.async_db import async_database

        try:
            client_obj_id = ObjectId(client_id)
        except Exception:
            raise HTTPException(
                status_code=400, detail="ID de cliente inválido")

        client = await async_database.find_client_by_id(client_id)
        if not client:
            raise HTTPException(
                status_code=404, detail="Cliente não encontrado")

        return {
            "client_id": client_id,
            "status": client.get("full_processing_status", ProcessingStatus.PENDING.value),
            "progress": client.get("full_processing_progress", 0),
            "stage": client.get("full_processing_stage", "pending"),
            "message": client.get("full_processing_message", "Aguardando início"),
            "started_at": client.get("full_processing_started_at"),
            "completed_at": client.get("full_processing_completed_at"),
            "error": client.get("full_processing_error")
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao consultar status: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Erro interno: {str(e)}"
        )


@router.get("/{client_id}/full-processing/results")
async def get_processing_results(client_id: str):
    """
    Consulta os resultados do processamento completo
    """
    try:
        from clients.async_db import async_database

        try:
            client_obj_id = ObjectId(client_id)
        except Exception:
            raise HTTPException(
                status_code=400, detail="ID de cliente inválido")

        client = await async_database.find_client_by_id(client_id)
        if not client:
            raise HTTPException(
                status_code=404, detail="Cliente não encontrado")

        status = client.get("full_processing_status")
        if status != ProcessingStatus.COMPLETED.value:
            raise HTTPException(
                status_code=400,
                detail=f"Processamento não concluído. Status atual: {status}"
            )

        return {
            "client_id": client_id,
            "status": status,
            "processing_time": client.get("full_processing_time"),
            "cards_generated": client.get("full_processing_cards_count", 0),
            "pdf_path": client.get("full_processing_pdf_path"),
            "results_summary": client.get("full_processing_results", {}),
            "completed_at": client.get("full_processing_completed_at")
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao consultar resultados: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Erro interno: {str(e)}"
        )


async def execute_full_processing(client_id: str, client_data: Dict[str, Any]):
    """
    Executa o processamento completo em background
    """
    orchestrator = FullFlowOrchestrator()

    try:
        logger.info(
            f"Iniciando processamento completo para cliente {client_id}")

        # Executar fluxo completo
        result = await orchestrator.process_client_full_flow(client_id, client_data)

        # Atualizar resultado no banco
        from clients.async_db import async_database

        if result["status"] == "success":
            await async_database.update_client(
                client_id,
                {
                    "full_processing_status": ProcessingStatus.COMPLETED.value,
                    "full_processing_completed_at": datetime.now(UTC),
                    "full_processing_time": result.get("processing_time"),
                    "full_processing_cards_count": result.get("cards_generated", 0),
                    "full_processing_pdf_path": result.get("pdf_path"),
                    "full_processing_results": result,
                    "updated_at": datetime.now(UTC)
                }
            )
            logger.info(
                f"Processamento completo finalizado com sucesso para cliente {client_id}")
        else:
            await async_database.update_client(
                client_id,
                {
                    "full_processing_status": ProcessingStatus.ERROR.value,
                    "full_processing_error": result.get("message", "Erro desconhecido"),
                    "updated_at": datetime.now(UTC)
                }
            )
            logger.error(
                f"Processamento completo falhou para cliente {client_id}: {result.get('message')}")

    except Exception as e:
        logger.error(
            f"Erro crítico no processamento completo para cliente {client_id}: {str(e)}")

        try:
            from clients.async_db import async_database
            await async_database.update_client(
                client_id,
                {
                    "full_processing_status": ProcessingStatus.ERROR.value,
                    "full_processing_error": str(e),
                    "updated_at": datetime.now(UTC)
                }
            )
        except Exception:
            pass  # Evitar erro secundário
