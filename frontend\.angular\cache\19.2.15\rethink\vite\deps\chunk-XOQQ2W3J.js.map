{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-usestyle.mjs", "../../../../../../node_modules/primeng/fesm2022/primeng-base.mjs"], "sourcesContent": ["import { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, Injectable } from '@angular/core';\nimport { setAttributes, setAttribute } from '@primeuix/utils';\nlet _id = 0;\nclass UseStyle {\n  document = inject(DOCUMENT);\n  use(css, options = {}) {\n    let isLoaded = false;\n    let cssRef = css;\n    let styleRef = null;\n    const {\n      immediate = true,\n      manual = false,\n      name = `style_${++_id}`,\n      id = undefined,\n      media = undefined,\n      nonce = undefined,\n      first = false,\n      props = {}\n    } = options;\n    if (!this.document) return;\n    styleRef = this.document.querySelector(`style[data-primeng-style-id=\"${name}\"]`) || id && this.document.getElementById(id) || this.document.createElement('style');\n    if (!styleRef.isConnected) {\n      cssRef = css;\n      setAttributes(styleRef, {\n        type: 'text/css',\n        media,\n        nonce\n      });\n      const HEAD = this.document.head;\n      first && HEAD.firstChild ? HEAD.insertBefore(styleRef, HEAD.firstChild) : HEAD.appendChild(styleRef);\n      setAttribute(styleRef, 'data-primeng-style-id', name);\n    }\n    if (styleRef.textContent !== cssRef) {\n      styleRef.textContent = cssRef;\n    }\n    return {\n      id,\n      name,\n      el: styleRef,\n      css: cssRef\n    };\n  }\n  static ɵfac = function UseStyle_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || UseStyle)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: UseStyle,\n    factory: UseStyle.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(UseStyle, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { UseStyle };\n", "import * as i0 from '@angular/core';\nimport { inject, Injectable } from '@angular/core';\nimport { dt, Theme } from '@primeuix/styled';\nimport { resolve, minifyCSS } from '@primeuix/utils';\nimport { UseStyle } from 'primeng/usestyle';\nvar base = {\n  _loadedStyleNames: new Set(),\n  getLoadedStyleNames() {\n    return this._loadedStyleNames;\n  },\n  isStyleNameLoaded(name) {\n    return this._loadedStyleNames.has(name);\n  },\n  setLoadedStyleName(name) {\n    this._loadedStyleNames.add(name);\n  },\n  deleteLoadedStyleName(name) {\n    this._loadedStyleNames.delete(name);\n  },\n  clearLoadedStyleNames() {\n    this._loadedStyleNames.clear();\n  }\n};\nconst theme = ({\n  dt\n}) => `\n*,\n::before,\n::after {\n    box-sizing: border-box;\n}\n\n/* Non ng overlay animations */\n.p-connected-overlay {\n    opacity: 0;\n    transform: scaleY(0.8);\n    transition: transform 0.12s cubic-bezier(0, 0, 0.2, 1),\n        opacity 0.12s cubic-bezier(0, 0, 0.2, 1);\n}\n\n.p-connected-overlay-visible {\n    opacity: 1;\n    transform: scaleY(1);\n}\n\n.p-connected-overlay-hidden {\n    opacity: 0;\n    transform: scaleY(1);\n    transition: opacity 0.1s linear;\n}\n\n/* NG based overlay animations */\n.p-connected-overlay-enter-from {\n    opacity: 0;\n    transform: scaleY(0.8);\n}\n\n.p-connected-overlay-leave-to {\n    opacity: 0;\n}\n\n.p-connected-overlay-enter-active {\n    transition: transform 0.12s cubic-bezier(0, 0, 0.2, 1),\n        opacity 0.12s cubic-bezier(0, 0, 0.2, 1);\n}\n\n.p-connected-overlay-leave-active {\n    transition: opacity 0.1s linear;\n}\n\n/* Toggleable Content */\n.p-toggleable-content-enter-from,\n.p-toggleable-content-leave-to {\n    max-height: 0;\n}\n\n.p-toggleable-content-enter-to,\n.p-toggleable-content-leave-from {\n    max-height: 1000px;\n}\n\n.p-toggleable-content-leave-active {\n    overflow: hidden;\n    transition: max-height 0.45s cubic-bezier(0, 1, 0, 1);\n}\n\n.p-toggleable-content-enter-active {\n    overflow: hidden;\n    transition: max-height 1s ease-in-out;\n}\n\n.p-disabled,\n.p-disabled * {\n    cursor: default;\n    pointer-events: none;\n    user-select: none;\n}\n\n.p-disabled,\n.p-component:disabled {\n    opacity: ${dt('disabled.opacity')};\n}\n\n.pi {\n    font-size: ${dt('icon.size')};\n}\n\n.p-icon {\n    width: ${dt('icon.size')};\n    height: ${dt('icon.size')};\n}\n\n.p-unselectable-text {\n    user-select: none;\n}\n\n.p-overlay-mask {\n    background: ${dt('mask.background')};\n    color: ${dt('mask.color')};\n    position: fixed;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n}\n\n.p-overlay-mask-enter {\n    animation: p-overlay-mask-enter-animation ${dt('mask.transition.duration')} forwards;\n}\n\n.p-overlay-mask-leave {\n    animation: p-overlay-mask-leave-animation ${dt('mask.transition.duration')} forwards;\n}\n/* Temporarily disabled, distrupts PrimeNG overlay animations */\n/* @keyframes p-overlay-mask-enter-animation {\n    from {\n        background: transparent;\n    }\n    to {\n        background: ${dt('mask.background')};\n    }\n}\n@keyframes p-overlay-mask-leave-animation {\n    from {\n        background: ${dt('mask.background')};\n    }\n    to {\n        background: transparent;\n    }\n}*/\n\n.p-iconwrapper {\n    display: inline-flex;\n    justify-content: center;\n    align-items: center;\n}\n`;\nconst css = ({\n  dt\n}) => `\n.p-hidden-accessible {\n    border: 0;\n    clip: rect(0 0 0 0);\n    height: 1px;\n    margin: -1px;\n    overflow: hidden;\n    padding: 0;\n    position: absolute;\n    width: 1px;\n}\n\n.p-hidden-accessible input,\n.p-hidden-accessible select {\n    transform: scale(0);\n}\n\n.p-overflow-hidden {\n    overflow: hidden;\n    padding-right: ${dt('scrollbar.width')};\n}\n\n/* @todo move to baseiconstyle.ts */\n\n.p-icon {\n    display: inline-block;\n    vertical-align: baseline;\n}\n\n.p-icon-spin {\n    -webkit-animation: p-icon-spin 2s infinite linear;\n    animation: p-icon-spin 2s infinite linear;\n}\n\n@-webkit-keyframes p-icon-spin {\n    0% {\n        -webkit-transform: rotate(0deg);\n        transform: rotate(0deg);\n    }\n    100% {\n        -webkit-transform: rotate(359deg);\n        transform: rotate(359deg);\n    }\n}\n\n@keyframes p-icon-spin {\n    0% {\n        -webkit-transform: rotate(0deg);\n        transform: rotate(0deg);\n    }\n    100% {\n        -webkit-transform: rotate(359deg);\n        transform: rotate(359deg);\n    }\n}\n`;\nclass BaseStyle {\n  name = 'base';\n  useStyle = inject(UseStyle);\n  theme = undefined;\n  css = undefined;\n  classes = {};\n  inlineStyles = {};\n  load = (style, options = {}, transform = cs => cs) => {\n    const computedStyle = transform(resolve(style, {\n      dt\n    }));\n    return computedStyle ? this.useStyle.use(minifyCSS(computedStyle), {\n      name: this.name,\n      ...options\n    }) : {};\n  };\n  loadCSS = (options = {}) => {\n    return this.load(this.css, options);\n  };\n  loadTheme = (options = {}, style = '') => {\n    return this.load(this.theme, options, (computedStyle = '') => Theme.transformCSS(options.name || this.name, `${computedStyle}${style}`));\n  };\n  loadGlobalCSS = (options = {}) => {\n    return this.load(css, options);\n  };\n  loadGlobalTheme = (options = {}, style = '') => {\n    return this.load(theme, options, (computedStyle = '') => Theme.transformCSS(options.name || this.name, `${computedStyle}${style}`));\n  };\n  getCommonTheme = params => {\n    return Theme.getCommon(this.name, params);\n  };\n  getComponentTheme = params => {\n    return Theme.getComponent(this.name, params);\n  };\n  getDirectiveTheme = params => {\n    return Theme.getDirective(this.name, params);\n  };\n  getPresetTheme = (preset, selector, params) => {\n    return Theme.getCustomPreset(this.name, preset, selector, params);\n  };\n  getLayerOrderThemeCSS = () => {\n    return Theme.getLayerOrderCSS(this.name);\n  };\n  getStyleSheet = (extendedCSS = '', props = {}) => {\n    if (this.css) {\n      const _css = resolve(this.css, {\n        dt\n      });\n      const _style = minifyCSS(`${_css}${extendedCSS}`);\n      const _props = Object.entries(props).reduce((acc, [k, v]) => acc.push(`${k}=\"${v}\"`) && acc, []).join(' ');\n      return `<style type=\"text/css\" data-primeng-style-id=\"${this.name}\" ${_props}>${_style}</style>`;\n    }\n    return '';\n  };\n  getCommonThemeStyleSheet = (params, props = {}) => {\n    return Theme.getCommonStyleSheet(this.name, params, props);\n  };\n  getThemeStyleSheet = (params, props = {}) => {\n    let css = [Theme.getStyleSheet(this.name, params, props)];\n    if (this.theme) {\n      const name = this.name === 'base' ? 'global-style' : `${this.name}-style`;\n      const _css = resolve(this.theme, {\n        dt\n      });\n      const _style = minifyCSS(Theme.transformCSS(name, _css));\n      const _props = Object.entries(props).reduce((acc, [k, v]) => acc.push(`${k}=\"${v}\"`) && acc, []).join(' ');\n      css.push(`<style type=\"text/css\" data-primeng-style-id=\"${name}\" ${_props}>${_style}</style>`);\n    }\n    return css.join('');\n  };\n  static ɵfac = function BaseStyle_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || BaseStyle)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: BaseStyle,\n    factory: BaseStyle.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BaseStyle, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { base as Base, BaseStyle };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAI,MAAM;AACV,IAAM,WAAN,MAAM,UAAS;AAAA,EACb,WAAW,OAAO,QAAQ;AAAA,EAC1B,IAAIA,MAAK,UAAU,CAAC,GAAG;AACrB,QAAI,WAAW;AACf,QAAI,SAASA;AACb,QAAI,WAAW;AACf,UAAM;AAAA,MACJ,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,OAAO,SAAS,EAAE,GAAG;AAAA,MACrB,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ,CAAC;AAAA,IACX,IAAI;AACJ,QAAI,CAAC,KAAK,SAAU;AACpB,eAAW,KAAK,SAAS,cAAc,gCAAgC,IAAI,IAAI,KAAK,MAAM,KAAK,SAAS,eAAe,EAAE,KAAK,KAAK,SAAS,cAAc,OAAO;AACjK,QAAI,CAAC,SAAS,aAAa;AACzB,eAASA;AACT,oBAAc,UAAU;AAAA,QACtB,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF,CAAC;AACD,YAAM,OAAO,KAAK,SAAS;AAC3B,eAAS,KAAK,aAAa,KAAK,aAAa,UAAU,KAAK,UAAU,IAAI,KAAK,YAAY,QAAQ;AACnG,mBAAa,UAAU,yBAAyB,IAAI;AAAA,IACtD;AACA,QAAI,SAAS,gBAAgB,QAAQ;AACnC,eAAS,cAAc;AAAA,IACzB;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,IAAI;AAAA,MACJ,KAAK;AAAA,IACP;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,iBAAiB,mBAAmB;AACzD,WAAO,KAAK,qBAAqB,WAAU;AAAA,EAC7C;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,UAAS;AAAA,IAClB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACvDH,IAAI,OAAO;AAAA,EACT,mBAAmB,oBAAI,IAAI;AAAA,EAC3B,sBAAsB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,kBAAkB,MAAM;AACtB,WAAO,KAAK,kBAAkB,IAAI,IAAI;AAAA,EACxC;AAAA,EACA,mBAAmB,MAAM;AACvB,SAAK,kBAAkB,IAAI,IAAI;AAAA,EACjC;AAAA,EACA,sBAAsB,MAAM;AAC1B,SAAK,kBAAkB,OAAO,IAAI;AAAA,EACpC;AAAA,EACA,wBAAwB;AACtB,SAAK,kBAAkB,MAAM;AAAA,EAC/B;AACF;AACA,IAAM,QAAQ,CAAC;AAAA,EACb,IAAAC;AACF,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,eA2ESA,IAAG,kBAAkB,CAAC;AAAA;AAAA;AAAA;AAAA,iBAIpBA,IAAG,WAAW,CAAC;AAAA;AAAA;AAAA;AAAA,aAInBA,IAAG,WAAW,CAAC;AAAA,cACdA,IAAG,WAAW,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAQXA,IAAG,iBAAiB,CAAC;AAAA,aAC1BA,IAAG,YAAY,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gDASmBA,IAAG,0BAA0B,CAAC;AAAA;AAAA;AAAA;AAAA,gDAI9BA,IAAG,0BAA0B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBAQxDA,IAAG,iBAAiB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,sBAKrBA,IAAG,iBAAiB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAa3C,IAAM,MAAM,CAAC;AAAA,EACX,IAAAA;AACF,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAmBeA,IAAG,iBAAiB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqC1C,IAAM,YAAN,MAAM,WAAU;AAAA,EACd,OAAO;AAAA,EACP,WAAW,OAAO,QAAQ;AAAA,EAC1B,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU,CAAC;AAAA,EACX,eAAe,CAAC;AAAA,EAChB,OAAO,CAAC,OAAO,UAAU,CAAC,GAAG,YAAY,QAAM,OAAO;AACpD,UAAM,gBAAgB,UAAU,QAAQ,OAAO;AAAA,MAC7C;AAAA,IACF,CAAC,CAAC;AACF,WAAO,gBAAgB,KAAK,SAAS,IAAI,UAAU,aAAa,GAAG;AAAA,MACjE,MAAM,KAAK;AAAA,OACR,QACJ,IAAI,CAAC;AAAA,EACR;AAAA,EACA,UAAU,CAAC,UAAU,CAAC,MAAM;AAC1B,WAAO,KAAK,KAAK,KAAK,KAAK,OAAO;AAAA,EACpC;AAAA,EACA,YAAY,CAAC,UAAU,CAAC,GAAG,QAAQ,OAAO;AACxC,WAAO,KAAK,KAAK,KAAK,OAAO,SAAS,CAAC,gBAAgB,OAAO,eAAM,aAAa,QAAQ,QAAQ,KAAK,MAAM,GAAG,aAAa,GAAG,KAAK,EAAE,CAAC;AAAA,EACzI;AAAA,EACA,gBAAgB,CAAC,UAAU,CAAC,MAAM;AAChC,WAAO,KAAK,KAAK,KAAK,OAAO;AAAA,EAC/B;AAAA,EACA,kBAAkB,CAAC,UAAU,CAAC,GAAG,QAAQ,OAAO;AAC9C,WAAO,KAAK,KAAK,OAAO,SAAS,CAAC,gBAAgB,OAAO,eAAM,aAAa,QAAQ,QAAQ,KAAK,MAAM,GAAG,aAAa,GAAG,KAAK,EAAE,CAAC;AAAA,EACpI;AAAA,EACA,iBAAiB,YAAU;AACzB,WAAO,eAAM,UAAU,KAAK,MAAM,MAAM;AAAA,EAC1C;AAAA,EACA,oBAAoB,YAAU;AAC5B,WAAO,eAAM,aAAa,KAAK,MAAM,MAAM;AAAA,EAC7C;AAAA,EACA,oBAAoB,YAAU;AAC5B,WAAO,eAAM,aAAa,KAAK,MAAM,MAAM;AAAA,EAC7C;AAAA,EACA,iBAAiB,CAAC,QAAQ,UAAU,WAAW;AAC7C,WAAO,eAAM,gBAAgB,KAAK,MAAM,QAAQ,UAAU,MAAM;AAAA,EAClE;AAAA,EACA,wBAAwB,MAAM;AAC5B,WAAO,eAAM,iBAAiB,KAAK,IAAI;AAAA,EACzC;AAAA,EACA,gBAAgB,CAAC,cAAc,IAAI,QAAQ,CAAC,MAAM;AAChD,QAAI,KAAK,KAAK;AACZ,YAAM,OAAO,QAAQ,KAAK,KAAK;AAAA,QAC7B;AAAA,MACF,CAAC;AACD,YAAM,SAAS,UAAU,GAAG,IAAI,GAAG,WAAW,EAAE;AAChD,YAAM,SAAS,OAAO,QAAQ,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,IAAI,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,KAAK,KAAK,CAAC,CAAC,EAAE,KAAK,GAAG;AACzG,aAAO,iDAAiD,KAAK,IAAI,KAAK,MAAM,IAAI,MAAM;AAAA,IACxF;AACA,WAAO;AAAA,EACT;AAAA,EACA,2BAA2B,CAAC,QAAQ,QAAQ,CAAC,MAAM;AACjD,WAAO,eAAM,oBAAoB,KAAK,MAAM,QAAQ,KAAK;AAAA,EAC3D;AAAA,EACA,qBAAqB,CAAC,QAAQ,QAAQ,CAAC,MAAM;AAC3C,QAAIC,OAAM,CAAC,eAAM,cAAc,KAAK,MAAM,QAAQ,KAAK,CAAC;AACxD,QAAI,KAAK,OAAO;AACd,YAAM,OAAO,KAAK,SAAS,SAAS,iBAAiB,GAAG,KAAK,IAAI;AACjE,YAAM,OAAO,QAAQ,KAAK,OAAO;AAAA,QAC/B;AAAA,MACF,CAAC;AACD,YAAM,SAAS,UAAU,eAAM,aAAa,MAAM,IAAI,CAAC;AACvD,YAAM,SAAS,OAAO,QAAQ,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,IAAI,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,KAAK,KAAK,CAAC,CAAC,EAAE,KAAK,GAAG;AACzG,MAAAA,KAAI,KAAK,iDAAiD,IAAI,KAAK,MAAM,IAAI,MAAM,UAAU;AAAA,IAC/F;AACA,WAAOA,KAAI,KAAK,EAAE;AAAA,EACpB;AAAA,EACA,OAAO,OAAO,SAAS,kBAAkB,mBAAmB;AAC1D,WAAO,KAAK,qBAAqB,YAAW;AAAA,EAC9C;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,WAAU;AAAA,IACnB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["css", "dt", "css"]}