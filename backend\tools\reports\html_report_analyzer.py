"""
Métodos auxiliares para análise de dados do dossiê
"""
from datetime import datetime
import json
from typing import List, Dict, Any
import re


class DataAnalyzer:
    """Analisador de dados do dossiê para extração de insights"""

    @staticmethod
    def format_dns_servers(servers: List[str]) -> str:
        """Formata lista de servidores DNS"""
        if not servers:
            return "<li>Nenhum servidor DNS encontrado</li>"
        # Limita a 5 servidores
        return "\n".join([f"<li>{server}</li>" for server in servers[:5]])

    @staticmethod
    def calculate_domain_age(creation_date: str) -> str:
        """Calcula idade do domínio"""
        if not creation_date:
            return "Não informado"

        try:
            # Tenta vários formatos de data
            for fmt in ["%Y-%m-%d", "%Y-%m-%dT%H:%M:%S", "%d/%m/%Y"]:
                try:
                    date_obj = datetime.strptime(
                        creation_date.split('T')[0], fmt)
                    break
                except ValueError:
                    continue
            else:
                return "Data inválida"

            age = datetime.now() - date_obj
            years = age.days // 365
            months = (age.days % 365) // 30

            if years > 0:
                return f"{years} anos e {months} meses"
            elif months > 0:
                return f"{months} meses"
            else:
                return f"{age.days} dias"

        except Exception:
            return "Não foi possível calcular"

    @staticmethod
    def analyze_business_model(content: Dict) -> str:
        """Analisa modelo de negócio baseado no conteúdo"""
        # Extrai dados para análise
        website_content = content.get("website_analysis", {})
        meta_description = website_content.get("meta_description", "")
        title = website_content.get("title", "")

        # Palavras-chave que indicam diferentes modelos de negócio
        b2b_keywords = ["consultoria", "empresa",
                        "corporativo", "soluções", "serviços"]
        b2c_keywords = ["cliente", "produto", "comprar", "loja", "vendas"]
        tech_keywords = ["tecnologia", "software",
                         "desenvolvimento", "sistema", "digital"]

        text_to_analyze = f"{meta_description} {title}".lower()

        b2b_score = sum(
            1 for keyword in b2b_keywords if keyword in text_to_analyze)
        b2c_score = sum(
            1 for keyword in b2c_keywords if keyword in text_to_analyze)
        tech_score = sum(
            1 for keyword in tech_keywords if keyword in text_to_analyze)

        if tech_score >= 2:
            model = "Empresa de Tecnologia/Software"
        elif b2b_score > b2c_score:
            model = "Business-to-Business (B2B)"
        elif b2c_score > b2b_score:
            model = "Business-to-Consumer (B2C)"
        else:
            model = "Modelo Híbrido ou Diversificado"

        return f"{model} - Baseado na análise de conteúdo e palavras-chave identificadas no website."

    @staticmethod
    def extract_business_characteristics(content: Dict) -> str:
        """Extrai características do negócio"""
        characteristics = []

        # Analisa características técnicas
        whois_data = content.get("whois", {})
        if whois_data.get("registrar"):
            characteristics.append(
                f"<li>Registrador confiável: {whois_data.get('registrar')}</li>")

        # Analisa presença online
        website_analysis = content.get("website_analysis", {})
        if website_analysis.get("has_ssl", False):
            characteristics.append(
                "<li>Implementação de segurança SSL/TLS</li>")

        if website_analysis.get("responsive", False):
            characteristics.append(
                "<li>Website responsivo para dispositivos móveis</li>")

        # Analisa maturidade digital
        social_media = content.get("social_media", {})
        if social_media:
            active_platforms = [
                platform for platform, data in social_media.items() if data.get("active", False)]
            if active_platforms:
                characteristics.append(
                    f"<li>Presença ativa em {len(active_platforms)} plataformas sociais</li>")

        if not characteristics:
            characteristics.append(
                "<li>Empresa com infraestrutura digital básica estabelecida</li>")
            characteristics.append(
                "<li>Oportunidades identificadas para expansão digital</li>")

        return "\n".join(characteristics)

    @staticmethod
    def identify_market_segment(content: Dict) -> str:
        """Identifica segmento de mercado"""
        website_analysis = content.get("website_analysis", {})
        meta_keywords = website_analysis.get("meta_keywords", "")
        description = website_analysis.get("meta_description", "")

        text_analysis = f"{meta_keywords} {description}".lower()

        # Mapeamento de segmentos
        segments = {
            "Tecnologia": ["software", "desenvolvimento", "sistema", "aplicativo", "digital", "tech"],
            "Consultoria": ["consultoria", "assessoria", "estratégia", "gestão", "planejamento"],
            "E-commerce": ["loja", "vendas", "produtos", "comprar", "shopping"],
            "Educação": ["educação", "curso", "treinamento", "ensino", "aprendizagem"],
            "Saúde": ["saúde", "médico", "clínica", "hospital", "tratamento"],
            "Financeiro": ["financeiro", "banco", "investimento", "crédito", "economia"]
        }

        segment_scores = {}
        for segment, keywords in segments.items():
            score = sum(1 for keyword in keywords if keyword in text_analysis)
            if score > 0:
                segment_scores[segment] = score

        if segment_scores:
            primary_segment = max(segment_scores, key=segment_scores.get)
            return f"Segmento Primário: {primary_segment}. Análise baseada em palavras-chave e conteúdo do website."
        else:
            return "Segmento Diversificado ou Nicho Específico. Requer análise adicional para categorização precisa."

    @staticmethod
    def assess_tech_focus(content: Dict) -> str:
        """Avalia foco tecnológico"""
        website_analysis = content.get("website_analysis", {})
        technologies = website_analysis.get("technologies", [])

        if len(technologies) > 10:
            return "Alto - Utiliza tecnologias avançadas e diversificadas"
        elif len(technologies) > 5:
            return "Médio - Stack tecnológico equilibrado"
        elif len(technologies) > 0:
            return "Básico - Tecnologias essenciais implementadas"
        else:
            return "Limitado - Infraestrutura tecnológica básica"

    @staticmethod
    def assess_operation_scale(content: Dict) -> str:
        """Avalia escala de operação"""
        whois_data = content.get("whois", {})
        creation_date = whois_data.get("creation_date", "")

        # Calcula tempo de operação
        if creation_date:
            try:
                date_obj = datetime.strptime(
                    creation_date.split('T')[0], "%Y-%m-%d")
                years = (datetime.now() - date_obj).days // 365

                if years >= 10:
                    return "Estabelecida - Mais de 10 anos de operação"
                elif years >= 5:
                    return "Consolidada - 5-10 anos de operação"
                elif years >= 2:
                    return "Em Crescimento - 2-5 anos de operação"
                else:
                    return "Startup/Nova - Menos de 2 anos"
            except:
                pass

        return "Escala não determinada - Dados insuficientes"

    @staticmethod
    def generate_competitive_insights(content: Dict) -> str:
        """Gera insights competitivos"""
        insights = []

        # Análise de tecnologias
        website_analysis = content.get("website_analysis", {})
        technologies = website_analysis.get("technologies", [])

        modern_tech = ["React", "Vue", "Angular",
                       "Node.js", "Python", "AWS", "Google Cloud"]
        modern_count = sum(1 for tech in technologies if any(
            modern in str(tech) for modern in modern_tech))

        if modern_count >= 3:
            insights.append(
                "✅ Utilização de tecnologias modernas posiciona a empresa competitivamente")
        elif modern_count >= 1:
            insights.append(
                "⚠️ Stack tecnológico parcialmente atualizado - oportunidade de modernização")
        else:
            insights.append(
                "🔄 Stack tecnológico requer modernização para competitividade")

        # Análise de SEO
        seo_data = website_analysis.get("seo", {})
        if seo_data.get("meta_description") and seo_data.get("title"):
            insights.append("✅ Elementos básicos de SEO implementados")
        else:
            insights.append("⚠️ Elementos de SEO requerem otimização")

        # Análise de segurança
        if website_analysis.get("has_ssl"):
            insights.append(
                "✅ Segurança SSL implementada - padrão da indústria")
        else:
            insights.append(
                "🚨 Falta de SSL representa risco de segurança e SEO")

        if not insights:
            insights.append(
                "Análise competitiva em andamento - dados adicionais necessários")

        return "<ul>" + "".join([f"<li>{insight}</li>" for insight in insights]) + "</ul>"

    @staticmethod
    def analyze_website_presence(content: Dict) -> str:
        """Analisa presença do website"""
        website_analysis = content.get("website_analysis", {})

        metrics = []

        # SSL/Segurança
        if website_analysis.get("has_ssl"):
            metrics.append(
                "<div class='metric-positive'>✅ SSL/HTTPS Ativo</div>")
        else:
            metrics.append(
                "<div class='metric-negative'>❌ SSL/HTTPS Ausente</div>")

        # Responsividade
        if website_analysis.get("responsive"):
            metrics.append(
                "<div class='metric-positive'>✅ Design Responsivo</div>")
        else:
            metrics.append(
                "<div class='metric-warning'>⚠️ Responsividade Limitada</div>")

        # SEO Básico
        meta_desc = website_analysis.get("meta_description", "")
        title = website_analysis.get("title", "")

        if meta_desc and title:
            metrics.append(
                "<div class='metric-positive'>✅ Meta Tags Configuradas</div>")
        else:
            metrics.append(
                "<div class='metric-warning'>⚠️ Meta Tags Incompletas</div>")

        # Tecnologias
        technologies = website_analysis.get("technologies", [])
        tech_count = len(technologies)
        if tech_count > 10:
            metrics.append(
                f"<div class='metric-positive'>✅ Stack Tecnológico Robusto ({tech_count} tecnologias)</div>")
        elif tech_count > 5:
            metrics.append(
                f"<div class='metric-neutral'>📊 Stack Tecnológico Equilibrado ({tech_count} tecnologias)</div>")
        else:
            metrics.append(
                f"<div class='metric-warning'>⚠️ Stack Tecnológico Básico ({tech_count} tecnologias)</div>")

        return "\n".join(metrics)

    @staticmethod
    def analyze_social_media_presence(digital_data: Dict) -> str:
        """Analisa presença em redes sociais"""
        social_media = digital_data.get("social_media", {})

        if not social_media:
            return "<div class='metric-warning'>⚠️ Dados de redes sociais não disponíveis ou limitados</div>"

        platforms = []
        for platform, data in social_media.items():
            if isinstance(data, dict):
                active = data.get("active", False)
                followers = data.get("followers", 0)

                if active:
                    if followers > 10000:
                        platforms.append(
                            f"<div class='metric-positive'>✅ {platform.title()}: Ativo com {followers:,} seguidores</div>")
                    elif followers > 1000:
                        platforms.append(
                            f"<div class='metric-neutral'>📊 {platform.title()}: Ativo com {followers:,} seguidores</div>")
                    else:
                        platforms.append(
                            f"<div class='metric-warning'>⚠️ {platform.title()}: Ativo mas com baixo engajamento</div>")
                else:
                    platforms.append(
                        f"<div class='metric-negative'>❌ {platform.title()}: Inativo ou não encontrado</div>")

        if not platforms:
            platforms.append(
                "<div class='metric-warning'>⚠️ Presença social limitada identificada</div>")

        return "\n".join(platforms)

    @staticmethod
    def analyze_digital_channels(digital_data: Dict, dossier_content: Dict) -> str:
        """Analisa canais digitais"""
        channels = []

        # Website principal
        website = dossier_content.get("website_analysis", {})
        if website:
            channels.append("""
            <div class="channel-card">
                <h4>🌐 Website Principal</h4>
                <p><strong>Status:</strong> Ativo e funcional</p>
                <p><strong>Tecnologias:</strong> {count} identificadas</p>
                <p><strong>SEO:</strong> {seo_status}</p>
            </div>
            """.format(
                count=len(website.get("technologies", [])),
                seo_status="Configurado" if website.get(
                    "meta_description") else "Requer otimização"
            ))

        # E-mail marketing
        channels.append("""
        <div class="channel-card">
            <h4>📧 E-mail Marketing</h4>
            <p><strong>Status:</strong> Não identificado</p>
            <p><strong>Oportunidade:</strong> Canal com alto ROI potencial</p>
        </div>
        """)

        # Blog/Conteúdo
        channels.append("""
        <div class="channel-card">
            <h4>📝 Blog/Conteúdo</h4>
            <p><strong>Status:</strong> Não identificado</p>
            <p><strong>Recomendação:</strong> Implementar estratégia de conteúdo</p>
        </div>
        """)

        return "\n".join(channels)

    @staticmethod
    def analyze_content_strategy(content: Dict) -> str:
        """Analisa estratégia de conteúdo"""
        website_analysis = content.get("website_analysis", {})

        findings = []

        # Análise de meta description
        meta_desc = website_analysis.get("meta_description", "")
        if meta_desc:
            word_count = len(meta_desc.split())
            if 20 <= word_count <= 30:
                findings.append(
                    "✅ Meta description com tamanho adequado para SEO")
            else:
                findings.append(
                    "⚠️ Meta description requer otimização de tamanho")
        else:
            findings.append(
                "❌ Meta description ausente - impacto negativo no SEO")

        # Análise de título
        title = website_analysis.get("title", "")
        if title:
            if len(title) <= 60:
                findings.append("✅ Título da página otimizado para SEO")
            else:
                findings.append(
                    "⚠️ Título muito longo - pode ser truncado nos resultados de busca")
        else:
            findings.append("❌ Título da página ausente")

        # Análise de palavras-chave
        keywords = website_analysis.get("meta_keywords", "")
        if keywords:
            findings.append(
                "📊 Meta keywords presentes (baixa relevância para SEO moderno)")
        else:
            findings.append(
                "💡 Oportunidade: Implementar estratégia de palavras-chave")

        return "<ul>" + "".join([f"<li>{finding}</li>" for finding in findings]) + "</ul>"

    @staticmethod
    def analyze_digital_reputation(content: Dict) -> str:
        """Analisa reputação digital"""
        metrics = []

        # Análise de domínio
        whois_data = content.get("whois", {})
        domain_age = DataAnalyzer.calculate_domain_age(
            whois_data.get("creation_date", ""))

        if "anos" in domain_age.lower():
            metrics.append(
                "✅ Domínio estabelecido - contribui para autoridade")
        elif "meses" in domain_age.lower():
            metrics.append(
                "📊 Domínio relativamente novo - construindo autoridade")
        else:
            metrics.append(
                "⚠️ Domínio muito recente - requer construção de autoridade")

        # Análise de SSL
        website_analysis = content.get("website_analysis", {})
        if website_analysis.get("has_ssl"):
            metrics.append("✅ Certificado SSL ativo - confiança do usuário")
        else:
            metrics.append("❌ Sem SSL - pode impactar confiança e SEO")

        # Análise de tecnologias
        technologies = website_analysis.get("technologies", [])
        modern_tech_count = sum(1 for tech in technologies if any(modern in str(
            tech).lower() for modern in ["react", "vue", "angular", "node", "python"]))

        if modern_tech_count >= 3:
            metrics.append("✅ Uso de tecnologias modernas - imagem inovadora")
        elif modern_tech_count >= 1:
            metrics.append("📊 Stack tecnológico parcialmente moderno")
        else:
            metrics.append(
                "⚠️ Tecnologias desatualizadas podem impactar percepção")

        return "<div class='reputation-metrics'>" + "".join([f"<div class='reputation-item'>{metric}</div>" for metric in metrics]) + "</div>"
