{"version": 3, "sources": ["../../../../../../node_modules/mermaid/dist/chunks/mermaid.core/chunk-XZIHB7SX.mjs"], "sourcesContent": ["import { __name } from \"./chunk-YTJNT7DU.mjs\";\n\n// src/utils/imperativeState.ts\nvar ImperativeState = class {\n  /**\n   * @param init - Function that creates the default state.\n   */\n  constructor(init) {\n    this.init = init;\n    this.records = this.init();\n  }\n  static {\n    __name(this, \"ImperativeState\");\n  }\n  reset() {\n    this.records = this.init();\n  }\n};\nexport { ImperativeState };"], "mappings": ";;;;;AAGA,IAAI,kBAAkB,MAAM;AAAA;AAAA;AAAA;AAAA,EAI1B,YAAY,MAAM;AAChB,SAAK,OAAO;AACZ,SAAK,UAAU,KAAK,KAAK;AAAA,EAC3B;AAAA,EACA,OAAO;AACL,WAAO,MAAM,iBAAiB;AAAA,EAChC;AAAA,EACA,QAAQ;AACN,SAAK,UAAU,KAAK,KAAK;AAAA,EAC3B;AACF;", "names": []}