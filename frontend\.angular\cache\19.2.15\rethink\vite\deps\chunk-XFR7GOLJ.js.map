{"version": 3, "sources": ["../../../../../../node_modules/@mermaid-js/parser/dist/chunks/mermaid-parser.core/chunk-ROXG7S4E.mjs"], "sourcesContent": ["import { AbstractMermaidTokenBuilder, AbstractMermaidValueConverter, MermaidGeneratedSharedModule, PieGeneratedModule, __name } from \"./chunk-7PKI6E2E.mjs\";\n\n// src/language/pie/module.ts\nimport { EmptyFileSystem, createDefaultCoreModule, createDefaultSharedCoreModule, inject } from \"langium\";\n\n// src/language/pie/tokenBuilder.ts\nvar PieTokenBuilder = class extends AbstractMermaidTokenBuilder {\n  static {\n    __name(this, \"PieTokenBuilder\");\n  }\n  constructor() {\n    super([\"pie\", \"showData\"]);\n  }\n};\n\n// src/language/pie/valueConverter.ts\nvar PieValueConverter = class extends AbstractMermaidValueConverter {\n  static {\n    __name(this, \"PieValueConverter\");\n  }\n  runCustomConverter(rule, input, _cstNode) {\n    if (rule.name !== \"PIE_SECTION_LABEL\") {\n      return void 0;\n    }\n    return input.replace(/\"/g, \"\").trim();\n  }\n};\n\n// src/language/pie/module.ts\nvar PieModule = {\n  parser: {\n    TokenBuilder: /* @__PURE__ */__name(() => new PieTokenBuilder(), \"TokenBuilder\"),\n    ValueConverter: /* @__PURE__ */__name(() => new PieValueConverter(), \"ValueConverter\")\n  }\n};\nfunction createPieServices(context = EmptyFileSystem) {\n  const shared = inject(createDefaultSharedCoreModule(context), MermaidGeneratedSharedModule);\n  const Pie = inject(createDefaultCoreModule({\n    shared\n  }), PieGeneratedModule, PieModule);\n  shared.ServiceRegistry.register(Pie);\n  return {\n    shared,\n    Pie\n  };\n}\n__name(createPieServices, \"createPieServices\");\nexport { PieModule, createPieServices };"], "mappings": ";;;;;;;;;;;;;;AAMA,IAAI,kBAAkB,cAAc,4BAA4B;AAAA,EAC9D,OAAO;AACL,WAAO,MAAM,iBAAiB;AAAA,EAChC;AAAA,EACA,cAAc;AACZ,UAAM,CAAC,OAAO,UAAU,CAAC;AAAA,EAC3B;AACF;AAGA,IAAI,oBAAoB,cAAc,8BAA8B;AAAA,EAClE,OAAO;AACL,WAAO,MAAM,mBAAmB;AAAA,EAClC;AAAA,EACA,mBAAmB,MAAM,OAAO,UAAU;AACxC,QAAI,KAAK,SAAS,qBAAqB;AACrC,aAAO;AAAA,IACT;AACA,WAAO,MAAM,QAAQ,MAAM,EAAE,EAAE,KAAK;AAAA,EACtC;AACF;AAGA,IAAI,YAAY;AAAA,EACd,QAAQ;AAAA,IACN,cAA6B,OAAO,MAAM,IAAI,gBAAgB,GAAG,cAAc;AAAA,IAC/E,gBAA+B,OAAO,MAAM,IAAI,kBAAkB,GAAG,gBAAgB;AAAA,EACvF;AACF;AACA,SAAS,kBAAkB,UAAU,iBAAiB;AACpD,QAAM,SAAS,OAAO,8BAA8B,OAAO,GAAG,4BAA4B;AAC1F,QAAM,MAAM,OAAO,wBAAwB;AAAA,IACzC;AAAA,EACF,CAAC,GAAG,oBAAoB,SAAS;AACjC,SAAO,gBAAgB,SAAS,GAAG;AACnC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,OAAO,mBAAmB,mBAAmB;", "names": []}