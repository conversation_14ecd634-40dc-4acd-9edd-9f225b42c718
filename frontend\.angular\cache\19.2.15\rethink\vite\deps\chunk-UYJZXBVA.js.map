{"version": 3, "sources": ["../../../../../../node_modules/@mermaid-js/parser/dist/chunks/mermaid-parser.core/chunk-2NYFTIL2.mjs"], "sourcesContent": ["import { AbstractMermaidTokenBuilder, CommonValueConverter, GitGraphGeneratedModule, MermaidGeneratedSharedModule, __name } from \"./chunk-7PKI6E2E.mjs\";\n\n// src/language/gitGraph/module.ts\nimport { inject, createDefaultCoreModule, createDefaultSharedCoreModule, EmptyFileSystem } from \"langium\";\n\n// src/language/gitGraph/tokenBuilder.ts\nvar GitGraphTokenBuilder = class extends AbstractMermaidTokenBuilder {\n  static {\n    __name(this, \"GitGraphTokenBuilder\");\n  }\n  constructor() {\n    super([\"gitGraph\"]);\n  }\n};\n\n// src/language/gitGraph/module.ts\nvar GitGraphModule = {\n  parser: {\n    TokenBuilder: /* @__PURE__ */__name(() => new GitGraphTokenBuilder(), \"TokenBuilder\"),\n    ValueConverter: /* @__PURE__ */__name(() => new CommonValueConverter(), \"ValueConverter\")\n  }\n};\nfunction createGitGraphServices(context = EmptyFileSystem) {\n  const shared = inject(createDefaultSharedCoreModule(context), MermaidGeneratedSharedModule);\n  const GitGraph = inject(createDefaultCoreModule({\n    shared\n  }), GitGraphGeneratedModule, GitGraphModule);\n  shared.ServiceRegistry.register(GitGraph);\n  return {\n    shared,\n    GitGraph\n  };\n}\n__name(createGitGraphServices, \"createGitGraphServices\");\nexport { GitGraphModule, createGitGraphServices };"], "mappings": ";;;;;;;;;;;;;;AAMA,IAAI,uBAAuB,cAAc,4BAA4B;AAAA,EACnE,OAAO;AACL,WAAO,MAAM,sBAAsB;AAAA,EACrC;AAAA,EACA,cAAc;AACZ,UAAM,CAAC,UAAU,CAAC;AAAA,EACpB;AACF;AAGA,IAAI,iBAAiB;AAAA,EACnB,QAAQ;AAAA,IACN,cAA6B,OAAO,MAAM,IAAI,qBAAqB,GAAG,cAAc;AAAA,IACpF,gBAA+B,OAAO,MAAM,IAAI,qBAAqB,GAAG,gBAAgB;AAAA,EAC1F;AACF;AACA,SAAS,uBAAuB,UAAU,iBAAiB;AACzD,QAAM,SAAS,OAAO,8BAA8B,OAAO,GAAG,4BAA4B;AAC1F,QAAM,WAAW,OAAO,wBAAwB;AAAA,IAC9C;AAAA,EACF,CAAC,GAAG,yBAAyB,cAAc;AAC3C,SAAO,gBAAgB,SAAS,QAAQ;AACxC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,OAAO,wBAAwB,wBAAwB;", "names": []}