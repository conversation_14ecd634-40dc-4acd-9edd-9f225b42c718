"""Exceções customizadas do domínio de clientes."""


class DomainError(Exception):
    """Exceção base para erros de domínio."""

    def __init__(self, message: str, code: str = "DOMAIN_ERROR") -> None:
        """Inicializa exceção de domínio.

        Args:
            message: Mensagem de erro
            code: Código único do erro
        """
        super().__init__(message)
        self.message = message
        self.code = code


class ClientNotFoundError(DomainError):
    """Exceção lançada quando cliente não é encontrado."""

    def __init__(self, client_id: str) -> None:
        """Inicializa exceção.

        Args:
            client_id: ID do cliente não encontrado
        """
        super().__init__(
            message=f"Cliente não encontrado: {client_id}",
            code="CLIENT_NOT_FOUND"
        )
        self.client_id = client_id


class DuplicateClientUrlError(DomainError):
    """Exceção lançada quando tenta criar cliente com URL duplicada."""

    def __init__(self, url: str) -> None:
        """Inicializa exceção.

        Args:
            url: URL duplicada
        """
        super().__init__(
            message=f"Já existe um cliente com esta URL: {url}",
            code="DUPLICATE_CLIENT_URL"
        )
        self.url = url


class InvalidClientContextError(DomainError):
    """Exceção lançada quando contexto do cliente é inválido."""

    def __init__(self, reason: str) -> None:
        """Inicializa exceção.

        Args:
            reason: Razão da invalidez
        """
        super().__init__(
            message=f"Contexto do cliente inválido: {reason}",
            code="INVALID_CLIENT_CONTEXT"
        )


class ClientAnalysisInProgressError(DomainError):
    """Exceção lançada quando tenta iniciar análise com outra em progresso."""

    def __init__(self, client_id: str) -> None:
        """Inicializa exceção.

        Args:
            client_id: ID do cliente
        """
        super().__init__(
            message=f"Cliente {client_id} já tem análise em progresso",
            code="ANALYSIS_IN_PROGRESS"
        )
        self.client_id = client_id


class InvalidStatusTransitionError(DomainError):
    """Exceção lançada quando tenta fazer transição de status inválida."""

    def __init__(self, current_status: str, target_status: str) -> None:
        """Inicializa exceção.

        Args:
            current_status: Status atual
            target_status: Status alvo
        """
        super().__init__(
            message=f"Transição inválida de {current_status} para {target_status}",
            code="INVALID_STATUS_TRANSITION"
        )
        self.current_status = current_status
        self.target_status = target_status


class ClientArchivedException(DomainError):
    """Exceção lançada quando tenta modificar cliente arquivado."""

    def __init__(self, client_id: str) -> None:
        """Inicializa exceção.

        Args:
            client_id: ID do cliente arquivado
        """
        super().__init__(
            message=f"Cliente {client_id} está arquivado e não pode ser modificado",
            code="CLIENT_ARCHIVED"
        )
        self.client_id = client_id


class MaxAnalysisRetriesExceeded(DomainError):
    """Exceção lançada quando excede o máximo de tentativas de análise."""

    def __init__(self, client_id: str, max_retries: int) -> None:
        """Inicializa exceção.

        Args:
            client_id: ID do cliente
            max_retries: Número máximo de tentativas
        """
        super().__init__(
            message=f"Cliente {client_id} excedeu o máximo de {max_retries} tentativas de análise",
            code="MAX_RETRIES_EXCEEDED"
        )
        self.client_id = client_id
        self.max_retries = max_retries


class AnalysisTimeoutError(DomainError):
    """Exceção lançada quando análise excede o timeout."""

    def __init__(self, client_id: str, timeout_minutes: int) -> None:
        """Inicializa exceção.

        Args:
            client_id: ID do cliente
            timeout_minutes: Timeout em minutos
        """
        super().__init__(
            message=f"Análise do cliente {client_id} excedeu o timeout de {timeout_minutes} minutos",
            code="ANALYSIS_TIMEOUT"
        )
        self.client_id = client_id
        self.timeout_minutes = timeout_minutes
