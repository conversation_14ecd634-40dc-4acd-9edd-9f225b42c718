"""
Sistema de Diagnósticos Paralelos
Reduz tempo de análise técnica de 6-10 minutos para 2-3 minutos
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
# concurrent.futures não utilizado - removido
import aiohttp

# Imports opcionais com fallback
try:
    from .lighthouse_analyzer import LighthouseAnalyzer
except ImportError:
    LighthouseAnalyzer = None

try:
    from .screenshot_service import ScreenshotService
except ImportError:
    ScreenshotService = None

try:
    from .visual_ai_analyzer import VisualAIAnalyzer
except ImportError:
    VisualAIAnalyzer = None

try:
    from ..enrichment.enricher import DataEnricher
except ImportError:
    DataEnricher = None

logger = logging.getLogger(__name__)


class ParallelDiagnostics:
    """
    🚀 Sistema de diagnósticos paralelos

    Executa simultaneamente:
    - Análise Lighthouse (Performance, SEO, Accessibility)
    - Screenshots automatizados
    - Análise visual com IA
    - Enriquecimento de dados do website

    Reduz tempo de 6-10 minutos para 2-3 minutos
    """

    def __init__(self):
        # Inicializar serviços disponíveis
        self.lighthouse = LighthouseAnalyzer() if LighthouseAnalyzer else None
        self.screenshot = ScreenshotService() if ScreenshotService else None
        self.visual_ai = VisualAIAnalyzer() if VisualAIAnalyzer else None
        self.enricher = DataEnricher() if DataEnricher else None
        self.max_workers = 4  # Número máximo de workers paralelos

        # Log de serviços disponíveis
        available_services = []
        if self.lighthouse:
            available_services.append("Lighthouse")
        if self.screenshot:
            available_services.append("Screenshot")
        if self.visual_ai:
            available_services.append("Visual AI")
        if self.enricher:
            available_services.append("Website Enricher")

        logger.info(
            f"🔧 Serviços de diagnóstico disponíveis: {', '.join(available_services) if available_services else 'Nenhum'}")

    async def executar_diagnosticos_completos(self, site_url: str, empresa: str) -> Dict[str, Any]:
        """
        🎯 Executa todos os diagnósticos em paralelo

        Args:
            site_url: URL do site para análise
            empresa: Nome da empresa

        Returns:
            Resultado consolidado de todos os diagnósticos
        """
        start_time = datetime.now()
        logger.info(f"🚀 Iniciando diagnósticos paralelos para: {site_url}")

        if not site_url or site_url == 'Dado não encontrado':
            logger.warning(
                "⚠️ URL não fornecida, retornando diagnóstico vazio")
            return self._get_empty_diagnostics()

        try:
            # 🔥 EXECUÇÃO PARALELA DE TODOS OS DIAGNÓSTICOS
            tasks = [
                self._executar_lighthouse_async(site_url),
                self._executar_screenshot_async(site_url),
                self._executar_visual_ai_async(site_url),
                self._executar_enrichment_async(site_url),
                self._executar_performance_check_async(site_url),
                self._executar_security_check_async(site_url)
            ]

            logger.info(
                f"📊 Executando {len(tasks)} diagnósticos em paralelo...")
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Processar resultados
            (lighthouse_result, screenshot_result, visual_ai_result,
             enrichment_result, performance_result, security_result) = results

            # Consolidar resultado final
            diagnosticos_completos = {
                "empresa": empresa,
                "site_url": site_url,
                "timestamp": datetime.now().isoformat(),
                "lighthouse": self._safe_result(lighthouse_result),
                "screenshots": self._safe_result(screenshot_result),
                "visual_analysis": self._safe_result(visual_ai_result),
                "website_data": self._safe_result(enrichment_result),
                "performance": self._safe_result(performance_result),
                "security": self._safe_result(security_result),
                "processing_time_seconds": (datetime.now() - start_time).total_seconds(),
                "version": "2.0_parallel"
            }

            # Gerar score consolidado
            diagnosticos_completos["overall_score"] = self._calculate_overall_score(
                diagnosticos_completos)

            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()

            logger.info(
                f"✅ Diagnósticos paralelos concluídos em {processing_time:.2f} segundos")

            return diagnosticos_completos

        except Exception as e:
            logger.error(f"❌ Erro nos diagnósticos paralelos: {str(e)}")
            return self._get_error_diagnostics(str(e))

    async def _executar_lighthouse_async(self, site_url: str) -> Dict[str, Any]:
        """Executa análise Lighthouse de forma assíncrona"""
        if not self.lighthouse:
            logger.warning("⚠️ Lighthouse não disponível")
            return {"error": "Lighthouse não disponível", "scores": {}}

        try:
            logger.info(f"🔍 Iniciando análise Lighthouse para: {site_url}")

            # Executar em thread separada para não bloquear
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                self.lighthouse.analyze_website,
                site_url
            )

            logger.info("✅ Análise Lighthouse concluída")
            return result

        except Exception as e:
            logger.error(f"❌ Erro na análise Lighthouse: {str(e)}")
            return {"error": str(e), "scores": {}}

    async def _executar_screenshot_async(self, site_url: str) -> Dict[str, Any]:
        """Executa screenshots de forma assíncrona"""
        if not self.screenshot:
            logger.warning("⚠️ Screenshot service não disponível")
            return {"error": "Screenshot service não disponível", "screenshots": []}

        try:
            logger.info(f"📸 Iniciando screenshots para: {site_url}")

            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                self.screenshot.capture_multiple_screenshots,
                site_url
            )

            logger.info("✅ Screenshots concluídos")
            return result

        except Exception as e:
            logger.error(f"❌ Erro nos screenshots: {str(e)}")
            return {"error": str(e), "screenshots": []}

    async def _executar_visual_ai_async(self, site_url: str) -> Dict[str, Any]:
        """Executa análise visual com IA de forma assíncrona"""
        if not self.visual_ai:
            logger.warning("⚠️ Visual AI analyzer não disponível")
            return {"error": "Visual AI analyzer não disponível", "analysis": {}}

        try:
            logger.info(f"🤖 Iniciando análise visual IA para: {site_url}")

            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                self.visual_ai.analyze_website_visual,
                site_url
            )

            logger.info("✅ Análise visual IA concluída")
            return result

        except Exception as e:
            logger.error(f"❌ Erro na análise visual IA: {str(e)}")
            return {"error": str(e), "analysis": {}}

    async def _executar_enrichment_async(self, site_url: str) -> Dict[str, Any]:
        """Executa enriquecimento de dados de forma assíncrona"""
        if not self.enricher:
            logger.warning("⚠️ Website enricher não disponível")
            return {"error": "Website enricher não disponível", "data": {}}

        try:
            logger.info(
                f"🔍 Iniciando enriquecimento de dados para: {site_url}")

            loop = asyncio.get_event_loop()
            # Adaptar chamada para interface DataEnricher
            result = await loop.run_in_executor(
                None,
                lambda url: self.enricher.enrich_data("website", {"url": url}) if hasattr(
                    self.enricher, 'enrich_data') else {"data": {}, "url": url},
                site_url
            )

            logger.info("✅ Enriquecimento de dados concluído")
            return result

        except Exception as e:
            logger.error(f"❌ Erro no enriquecimento: {str(e)}")
            return {"error": str(e), "data": {}}

    async def _executar_performance_check_async(self, site_url: str) -> Dict[str, Any]:
        """Executa verificação de performance de forma assíncrona"""
        try:
            logger.info(f"⚡ Iniciando check de performance para: {site_url}")

            async with aiohttp.ClientSession() as session:
                start_time = datetime.now()

                async with session.get(site_url, timeout=30) as response:
                    load_time = (datetime.now() - start_time).total_seconds()

                    result = {
                        "status_code": response.status,
                        "load_time_seconds": load_time,
                        "response_size_bytes": len(await response.read()),
                        "headers": dict(response.headers),
                        "performance_grade": self._grade_performance(load_time)
                    }

                    logger.info(
                        f"✅ Performance check concluído: {load_time:.2f}s")
                    return result

        except Exception as e:
            logger.error(f"❌ Erro no performance check: {str(e)}")
            return {"error": str(e), "load_time_seconds": None}

    async def _executar_security_check_async(self, site_url: str) -> Dict[str, Any]:
        """Executa verificação de segurança de forma assíncrona"""
        try:
            logger.info(f"🔒 Iniciando security check para: {site_url}")

            async with aiohttp.ClientSession() as session:
                async with session.get(site_url, timeout=30) as response:
                    headers = dict(response.headers)

                    security_checks = {
                        "https_enabled": site_url.startswith('https://'),
                        "hsts_header": 'strict-transport-security' in headers,
                        "x_frame_options": 'x-frame-options' in headers,
                        "x_content_type_options": 'x-content-type-options' in headers,
                        "content_security_policy": 'content-security-policy' in headers,
                        "ssl_grade": "A" if site_url.startswith('https://') else "F"
                    }

                    security_score = sum(
                        security_checks.values()) / len(security_checks) * 100

                    result = {
                        **security_checks,
                        "security_score": security_score,
                        "security_grade": self._grade_security(security_score)
                    }

                    logger.info(
                        f"✅ Security check concluído: {security_score:.1f}%")
                    return result

        except Exception as e:
            logger.error(f"❌ Erro no security check: {str(e)}")
            return {"error": str(e), "security_score": 0}

    def _safe_result(self, result) -> Dict[str, Any]:
        """Retorna resultado seguro, tratando exceções"""
        if isinstance(result, Exception):
            return {"error": str(result), "success": False}
        return result if result else {}

    def _grade_performance(self, load_time: float) -> str:
        """Atribui nota para performance baseada no tempo de carregamento"""
        if load_time < 1.0:
            return "A"
        elif load_time < 2.0:
            return "B"
        elif load_time < 3.0:
            return "C"
        elif load_time < 5.0:
            return "D"
        else:
            return "F"

    def _grade_security(self, score: float) -> str:
        """Atribui nota para segurança baseada no score"""
        if score >= 90:
            return "A"
        elif score >= 80:
            return "B"
        elif score >= 70:
            return "C"
        elif score >= 60:
            return "D"
        else:
            return "F"

    def _calculate_overall_score(self, diagnostics: Dict[str, Any]) -> Dict[str, Any]:
        """Calcula score geral baseado em todos os diagnósticos"""
        scores = []

        # Lighthouse scores
        if "lighthouse" in diagnostics and "scores" in diagnostics["lighthouse"]:
            lighthouse_scores = diagnostics["lighthouse"]["scores"]
            for score in lighthouse_scores.values():
                if isinstance(score, (int, float)):
                    scores.append(score)

        # Performance score
        if "performance" in diagnostics and "load_time_seconds" in diagnostics["performance"]:
            load_time = diagnostics["performance"]["load_time_seconds"]
            if load_time:
                perf_score = max(0, 100 - (load_time * 20)
                                 )  # Penalizar tempo alto
                scores.append(perf_score)

        # Security score
        if "security" in diagnostics and "security_score" in diagnostics["security"]:
            scores.append(diagnostics["security"]["security_score"])

        overall_score = sum(scores) / len(scores) if scores else 0

        return {
            "overall_score": round(overall_score, 1),
            # Converter para escala de tempo
            "grade": self._grade_performance(overall_score / 20),
            "components_analyzed": len(scores)
        }

    def _get_empty_diagnostics(self) -> Dict[str, Any]:
        """Retorna estrutura vazia para diagnósticos"""
        return {
            "empresa": "",
            "site_url": "",
            "timestamp": datetime.now().isoformat(),
            "lighthouse": {},
            "screenshots": {},
            "visual_analysis": {},
            "website_data": {},
            "performance": {},
            "security": {},
            "overall_score": {"overall_score": 0, "grade": "N/A"},
            "processing_time_seconds": 0,
            "version": "2.0_parallel",
            "status": "empty"
        }

    def _get_error_diagnostics(self, error_msg: str) -> Dict[str, Any]:
        """Retorna estrutura de erro para diagnósticos"""
        return {
            **self._get_empty_diagnostics(),
            "error": error_msg,
            "status": "error"
        }
