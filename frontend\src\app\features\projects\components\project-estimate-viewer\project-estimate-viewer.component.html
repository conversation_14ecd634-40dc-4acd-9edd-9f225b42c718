<!-- <PERSON>dal de Estimativa Detalhada -->
<p-dialog
	[visible]="visible"
	[modal]="true"
	[closable]="true"
	[draggable]="false"
	[resizable]="false"
	styleClass="estimativa-modal"
	[style]="{ width: '95vw', maxWidth: '1400px', height: '90vh' }"
	(onHide)="fecharEstimativa()"
>
	<ng-template pTemplate="header">
		<div class="flex items-center gap-3">
			<i class="pi pi-chart-line text-blue-600 text-2xl"></i>
			<div>
				<h2 class="text-xl font-bold text-gray-900 m-0">Estimativa Detalhada</h2>
				<p class="text-sm text-gray-600 m-0 mt-1">{{ projectTitle }}</p>
			</div>
		</div>
	</ng-template>

	<ng-template pTemplate="content">
		@if (estimativa) {
			<div class="estimativa-content">
				<!-- Tabs de Navegação -->
				<p-tabView>
					<!-- Tab 1: <PERSON>is<PERSON> Geral -->
					<p-tabPanel header="📊 Visão Geral" leftIcon="pi pi-chart-bar">
						<div class="space-y-6">
							<!-- Métricas Principais -->
							<div class="grid grid-cols-1 md:grid-cols-4 gap-4">
								<div
									class="bg-gradient-to-br from-blue-50 to-blue-100 p-6 rounded-lg border border-blue-200"
								>
									<div class="flex items-center justify-between">
										<div>
											<h3 class="text-lg font-semibold text-blue-800">Investimento Total</h3>
											<p class="text-2xl font-bold text-blue-600">
												R$
												{{ estimativa.metricas_principais.custo_total | number: '1.0-0' }}
											</p>
										</div>
										<i class="pi pi-dollar text-3xl text-blue-400"></i>
									</div>
								</div>

								<div
									class="bg-gradient-to-br from-green-50 to-green-100 p-6 rounded-lg border border-green-200"
								>
									<div class="flex items-center justify-between">
										<div>
											<h3 class="text-lg font-semibold text-green-800">Duração</h3>
											<p class="text-2xl font-bold text-green-600">
												{{ estimativa.metricas_principais.duracao_estimada || 'N/A' }}
											</p>
										</div>
										<i class="pi pi-clock text-3xl text-green-400"></i>
									</div>
								</div>

								<div
									class="bg-gradient-to-br from-purple-50 to-purple-100 p-6 rounded-lg border border-purple-200"
								>
									<div class="flex items-center justify-between">
										<div>
											<h3 class="text-lg font-semibold text-purple-800">Complexidade</h3>
											<p class="text-2xl font-bold text-purple-600">
												{{ estimativa.metricas_principais.complexidade }}
											</p>
										</div>
										<i class="pi pi-cog text-3xl text-purple-400"></i>
									</div>
								</div>

								<div
									class="bg-gradient-to-br from-orange-50 to-orange-100 p-6 rounded-lg border border-orange-200"
								>
									<div class="flex items-center justify-between">
										<div>
											<h3 class="text-lg font-semibold text-orange-800">Equipe</h3>
											<p class="text-2xl font-bold text-orange-600">
												{{ estimativa.metricas_principais.equipe_minima }}
											</p>
										</div>
										<i class="pi pi-users text-3xl text-orange-400"></i>
									</div>
								</div>
							</div>

							<!-- Diagramas com Mermaid Live API -->
							@if (estimativa.diagramas_mermaid && estimativa.diagramas_mermaid.length > 0) {
								<div class="space-y-8">
									@for (diagram of estimativa.diagramas_mermaid; track diagram; let i = $index) {
										<div class="relative">
											<p-card [header]="diagram.titulo || 'Diagrama ' + (i + 1)">
												<!-- Componente Mermaid Live API - 100% Confiável -->
												<app-mermaid-diagram [diagram]="diagram.codigo" [theme]="'default'">
												</app-mermaid-diagram>
											</p-card>
										</div>
									}
								</div>
							}
						</div>
					</p-tabPanel>

					<!-- Tab 2: Cronograma -->
					<p-tabPanel header="📅 Cronograma" leftIcon="pi pi-calendar">
						<div class="space-y-6">
							<p class="text-gray-600">Cronograma detalhado será implementado em breve.</p>
						</div>
					</p-tabPanel>

					<!-- Tab 3: Arquitetura -->
					<p-tabPanel header="🏗️ Arquitetura" leftIcon="pi pi-sitemap">
						<div class="space-y-6">
							<p class="text-gray-600">Detalhes de arquitetura serão implementados em breve.</p>
						</div>
					</p-tabPanel>
				</p-tabView>
			</div>
		} @else {
			<!-- Loading State -->
			<div class="flex items-center justify-center h-64">
				<div class="text-center">
					<i class="pi pi-spin pi-spinner text-4xl text-blue-500 mb-4"></i>
					<p class="text-gray-600">Carregando estimativa detalhada...</p>
				</div>
			</div>
		}
	</ng-template>

	<ng-template pTemplate="footer">
		<div class="flex justify-end gap-3">
			<button
				pButton
				label="Fechar"
				icon="pi pi-times"
				class="p-button-outlined p-button-secondary"
				(click)="fecharEstimativa()"
			></button>
		</div>
	</ng-template>
</p-dialog>
