"""
ErrorHandler - Tratamento Centralizado de Erros

Este módulo é responsável por capturar, classificar e formatar 
todas as exceções da API de forma consistente.
"""

import logging
import traceback
from typing import Dict, Any, Optional, Type, Union
from datetime import datetime
from fastapi import HTTPException, Request
from fastapi.responses import JSONResponse
from pydantic import ValidationError
from .response_formatter import response_formatter


logger = logging.getLogger(__name__)


class APIError(Exception):
    """Exceção customizada para erros da API"""

    def __init__(
        self,
        message: str,
        status_code: int = 500,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Inicializa exceção da API

        Args:
            message: Mensagem do erro
            status_code: Código HTTP
            error_code: Código interno do erro
            details: Detalhes adicionais
        """
        self.message = message
        self.status_code = status_code
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class ValidationAPIError(APIError):
    """Exceção para erros de validação"""

    def __init__(self, message: str, validation_errors: list):
        super().__init__(message, status_code=422, error_code="VALIDATION_ERROR")
        self.validation_errors = validation_errors


class NotFoundAPIError(APIError):
    """Exceção para recursos não encontrados"""

    def __init__(self, resource: str, resource_id: Optional[str] = None):
        message = f"{resource} não encontrado"
        if resource_id:
            message += f" (ID: {resource_id})"
        super().__init__(message, status_code=404, error_code="NOT_FOUND")


class UnauthorizedAPIError(APIError):
    """Exceção para acesso não autorizado"""

    def __init__(self, message: str = "Acesso não autorizado"):
        super().__init__(message, status_code=401, error_code="UNAUTHORIZED")


class ForbiddenAPIError(APIError):
    """Exceção para acesso proibido"""

    def __init__(self, message: str = "Acesso proibido"):
        super().__init__(message, status_code=403, error_code="FORBIDDEN")


class BusinessRuleError(APIError):
    """Exceção para violação de regras de negócio"""

    def __init__(self, message: str, rule_code: Optional[str] = None):
        super().__init__(message, status_code=422,
                         error_code=f"BUSINESS_RULE_{rule_code}" if rule_code else "BUSINESS_RULE")


class ExternalServiceError(APIError):
    """Exceção para erros em serviços externos"""

    def __init__(self, service: str, message: str = "Serviço temporariamente indisponível"):
        super().__init__(f"{service}: {message}",
                         status_code=503, error_code="EXTERNAL_SERVICE_ERROR")
        self.service = service


class ErrorHandler:
    """
    Tratador Centralizado de Erros da API

    Responsável por:
    - Capturar exceções não tratadas
    - Classificar tipos de erro
    - Formatar respostas de erro consistentes
    - Registrar logs estruturados
    - Não vazar informações sensíveis
    """

    def __init__(self):
        """Inicializa o ErrorHandler"""
        # Mapeamento de exceções para códigos HTTP
        self.error_mapping: Dict[Type[Exception], int] = {
            ValueError: 400,
            TypeError: 400,
            KeyError: 400,
            FileNotFoundError: 404,
            PermissionError: 403,
            ConnectionError: 503,
            TimeoutError: 504,
        }

    def handle_api_error(self, error: APIError, request_id: Optional[str] = None) -> JSONResponse:
        """
        Trata exceções APIError customizadas

        Args:
            error: Exceção APIError
            request_id: ID da requisição

        Returns:
            JSONResponse formatada
        """
        try:
            logger.warning(
                f"APIError: {error.message}",
                extra={
                    "error_code": error.error_code,
                    "status_code": error.status_code,
                    "request_id": request_id,
                    "details": error.details
                }
            )

            # Tratamento especial para erros de validação
            if isinstance(error, ValidationAPIError):
                response_data = response_formatter.validation_error(
                    validation_errors=error.validation_errors,
                    message=error.message,
                    request_id=request_id
                )
            else:
                response_data = response_formatter.error(
                    message=error.message,
                    errors=[error.message],
                    error_code=error.error_code,
                    request_id=request_id
                )

            return JSONResponse(
                status_code=error.status_code,
                content=response_data
            )

        except Exception as e:
            logger.error(f"Erro ao processar APIError: {str(e)}")
            return self._critical_error_response(request_id)

    def handle_http_exception(self, error: HTTPException, request_id: Optional[str] = None) -> JSONResponse:
        """
        Trata exceções HTTPException do FastAPI

        Args:
            error: Exceção HTTPException
            request_id: ID da requisição

        Returns:
            JSONResponse formatada
        """
        try:
            logger.warning(
                f"HTTPException: {error.detail}",
                extra={
                    "status_code": error.status_code,
                    "request_id": request_id
                }
            )

            response_data = response_formatter.error(
                message=str(error.detail),
                errors=[str(error.detail)],
                request_id=request_id
            )

            return JSONResponse(
                status_code=error.status_code,
                content=response_data
            )

        except Exception as e:
            logger.error(f"Erro ao processar HTTPException: {str(e)}")
            return self._critical_error_response(request_id)

    def handle_validation_error(self, error: ValidationError, request_id: Optional[str] = None) -> JSONResponse:
        """
        Trata erros de validação do Pydantic

        Args:
            error: Erro de validação Pydantic
            request_id: ID da requisição

        Returns:
            JSONResponse formatada
        """
        try:
            # Extrair erros de validação em formato legível
            validation_errors = []
            for err in error.errors():
                field = ".".join(str(loc) for loc in err['loc'])
                message = err['msg']
                validation_errors.append(f"{field}: {message}")

            logger.warning(
                f"Validation error: {len(validation_errors)} erros",
                extra={
                    "validation_errors": validation_errors,
                    "request_id": request_id
                }
            )

            response_data = response_formatter.validation_error(
                validation_errors=validation_errors,
                message="Dados de entrada inválidos",
                request_id=request_id
            )

            return JSONResponse(
                status_code=422,
                content=response_data
            )

        except Exception as e:
            logger.error(f"Erro ao processar ValidationError: {str(e)}")
            return self._critical_error_response(request_id)

    def handle_generic_exception(self, error: Exception, request_id: Optional[str] = None) -> JSONResponse:
        """
        Trata exceções genéricas não capturadas

        Args:
            error: Exceção genérica
            request_id: ID da requisição

        Returns:
            JSONResponse formatada
        """
        try:
            # Mapear tipo de exceção para código HTTP
            status_code = 500
            for exception_type, code in self.error_mapping.items():
                if isinstance(error, exception_type):
                    status_code = code
                    break

            error_type = type(error).__name__

            # Log detalhado do erro
            logger.error(
                f"Unhandled exception: {error_type}",
                extra={
                    "error_message": str(error),
                    "error_type": error_type,
                    "status_code": status_code,
                    "request_id": request_id,
                    "traceback": traceback.format_exc()
                }
            )

            # Não vazar detalhes internos em produção
            if status_code == 500:
                message = "Erro interno do servidor"
            else:
                message = str(error)

            response_data = response_formatter.error(
                message=message,
                errors=[message],
                request_id=request_id
            )

            return JSONResponse(
                status_code=status_code,
                content=response_data
            )

        except Exception as e:
            logger.critical(f"Erro crítico no ErrorHandler: {str(e)}")
            return self._critical_error_response(request_id)

    def _critical_error_response(self, request_id: Optional[str] = None) -> JSONResponse:
        """
        Resposta de fallback para erros críticos

        Args:
            request_id: ID da requisição

        Returns:
            JSONResponse básica de erro
        """
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "message": "Erro crítico do sistema",
                "metadata": {
                    "timestamp": datetime.now().isoformat(),
                    "version": "1.0",
                    "request_id": request_id
                },
                "errors": ["Erro crítico do sistema"]
            }
        )

    async def exception_handler(self, request: Request, exc: Exception) -> JSONResponse:
        """
        Handler principal de exceções para FastAPI

        Args:
            request: Requisição FastAPI
            exc: Exceção capturada

        Returns:
            JSONResponse formatada
        """
        try:
            # Extrair request_id se disponível
            request_id = getattr(request.state, 'request_id', None)

            # Classificar e tratar exceção
            if isinstance(exc, APIError):
                return self.handle_api_error(exc, request_id)
            elif isinstance(exc, HTTPException):
                return self.handle_http_exception(exc, request_id)
            elif isinstance(exc, ValidationError):
                return self.handle_validation_error(exc, request_id)
            else:
                return self.handle_generic_exception(exc, request_id)

        except Exception as critical_error:
            logger.critical(
                f"Falha crítica no exception handler: {str(critical_error)}")
            return self._critical_error_response()


# Instância global do error handler
error_handler = ErrorHandler()
