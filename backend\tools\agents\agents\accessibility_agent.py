"""
Accessibility Agent - Agente especializado em acessibilidade web

Agente focado em análise e conformidade WCAG:
- Análise de acessibilidade Lighthouse
- Conformidade WCAG 2.1
- Testes com leitores de tela
- Navegação por teclado
"""

import logging
from typing import Dict, Any, Optional
from agno.agent import Agent
from ...shared.model_config import model_config
from agno.tools.reasoning import ReasoningTools

from ..tools.lighthouse_tools import LighthouseAnalysisTools

logger = logging.getLogger(__name__)


class AccessibilityAgent:
    """Agente especializado em acessibilidade web usando Agno"""

    def __init__(self, model_name: Optional[str] = None):
        """
        Inicializa o Accessibility Agent usando modelos do settings.py

        Args:
            model_name: Modelo específico (opcional, usa automático se None)
        """
        self.lighthouse_tools = LighthouseAnalysisTools()
        
        # Obter configuração de modelo baseada no settings.py
        agno_config = model_config.create_agno_config("analysis")
        
        if model_name:
            available_models = [
                model_config.get_openai_model("analysis"),
                model_config.get_cohere_model("analysis"), 
                model_config.get_mistral_model("analysis")
            ]
            if model_name in available_models:
                self.model_name = model_name
            else:
                logger.warning(f"Modelo {model_name} não disponível no settings.py, usando automático")
                self.model_name = agno_config["model_id"]
        else:
            self.model_name = agno_config["model_id"]

        # Criar modelo baseado no provider
        if agno_config["provider"] == "openai":
            from agno.models.openai import OpenAI
            model_instance = OpenAI(id=self.model_name, api_key=agno_config["api_key"])
        elif agno_config["provider"] == "cohere":
            from agno.models.cohere import Cohere
            model_instance = Cohere(id=self.model_name, api_key=agno_config["api_key"])
        elif agno_config["provider"] == "mistral":
            from agno.models.mistral import Mistral
            model_instance = Mistral(id=self.model_name, api_key=agno_config["api_key"])
        else:
            from agno.models.openai import OpenAI
            model_instance = OpenAI(id="gpt-4o-mini", api_key=agno_config["api_key"])

        self.agent = Agent(
            name="Accessibility Specialist",
            role="Expert in web accessibility and WCAG compliance",
            model=model_instance,
            tools=[ReasoningTools()],
            instructions=[
                "You are a web accessibility expert specializing in WCAG 2.1 compliance",
                "Analyze accessibility data and identify barriers for users with disabilities",
                "Provide specific recommendations following WCAG guidelines",
                "Focus on practical solutions for screen readers, keyboard navigation, and visual accessibility",
                "Consider legal compliance requirements and user impact",
                "Provide implementation priorities based on severity and user impact"
            ],
            description="Specialist in web accessibility, WCAG compliance, and inclusive design",
            markdown=True,
            show_tool_calls=True
        )

        logger.info(f"Accessibility Agent inicializado com {agno_config['provider']}:{self.model_name}")

    def analyze_accessibility(self, lighthouse_data: Dict[str, Any], company_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Analisa acessibilidade usando dados Lighthouse"""
        try:
            lighthouse_metrics = self.lighthouse_tools.analyze_lighthouse_data(
                lighthouse_data)
            acc_score = lighthouse_metrics.accessibility_score
            acc_issues = lighthouse_metrics.accessibility_issues

            company_info = company_context or {}

            analysis_prompt = f"""
Analise a acessibilidade web baseado nos dados Lighthouse:

**SCORE DE ACESSIBILIDADE:** {acc_score}/100

**ISSUES IDENTIFICADOS:**
{chr(10).join(f"- {issue}" for issue in acc_issues)}

Forneça análise detalhada incluindo:
1. **Conformidade WCAG** - nível atual e gaps
2. **Barreiras críticas** para usuários com deficiências
3. **Prioridades de correção** por impacto no usuário
4. **Recomendações específicas** com implementação
5. **Testes necessários** (screen readers, navegação por teclado)

Foque em soluções práticas e conformidade legal.
"""

            response = self.agent.run(analysis_prompt)

            return {
                "agent_type": "AccessibilityAgent",
                "accessibility_score": acc_score,
                "accessibility_issues": acc_issues,
                "agent_analysis": response.content,
                "wcag_level": "AA" if acc_score >= 90 else "A" if acc_score >= 75 else "Non-compliant"
            }

        except Exception as e:
            logger.error(f"Erro na análise de acessibilidade: {str(e)}")
            raise
