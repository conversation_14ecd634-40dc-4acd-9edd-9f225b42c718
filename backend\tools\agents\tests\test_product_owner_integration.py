"""
Testes de Integração para Product Owner Agent

Testes de integração ponta a ponta cobrindo:
- Fluxo completo de análise estratégica
- Integração com ProductAnalysisTools
- Integração com sistema de storage
- Testes com datasets reais simulados
- Cenários de performance e robustez
"""

import pytest
import asyncio
import logging
from unittest.mock import Mock, patch, AsyncMock
from typing import Dict, Any

from ..agents.product_owner_agent import ProductOwnerAgent
from ..tools.product_analysis_tools import ProductAnalysisTools, ProductInsights, RoadmapPriority
from ..storage import AgentStorageManager


class TestProductOwnerIntegration:
    """Testes de integração para o Product Owner Agent"""

    @pytest.fixture
    def agent(self):
        """Fixture para agente real (sem mocks excessivos)"""
        return ProductOwnerAgent()

    @pytest.fixture
    def sample_enterprise_data(self):
        """Dataset completo simulando empresa enterprise"""
        return {
            "product_data": {
                "features": [
                    "Advanced Analytics", "API Management", "User Authentication",
                    "Dashboard", "Reporting", "Data Export", "Mobile App",
                    "Real-time Collaboration", "AI Integration", "Security Controls",
                    "Workflow Automation", "Third-party Integrations", "Custom Fields",
                    "Advanced Search", "Audit Trail", "Role Management"
                ],
                "business_metrics": {
                    "active_users": 15000,
                    "retention_rate": 82,
                    "nps_score": 65,
                    "monthly_growth_rate": 15,
                    "user_satisfaction": 85,
                    "average_feature_usage": 72,
                    "support_rating": 4.5
                },
                "user_feedback": [
                    "Need better mobile experience",
                    "Want AI-powered insights",
                    "Better integration capabilities",
                    "More customization options"
                ],
                "roadmap_requests": [
                    "Machine Learning insights", "Advanced automation",
                    "Multi-tenant architecture", "Enhanced security"
                ],
                "age_months": 36,
                "unique_features": [
                    "AI-powered analytics", "Real-time collaboration",
                    "Advanced workflow automation"
                ],
                "tech_stack": [
                    "React", "Node.js", "Python", "AI", "ML", "cloud",
                    "microservices", "kubernetes"
                ],
                "patents": 5,
                "user_requests": [
                    "Advanced reporting", "Mobile optimization",
                    "API improvements", "Better integrations"
                ],
                "performance_metrics": {
                    "speed_advantage": True,
                    "reliability_score": 98
                },
                "pricing_tier": "enterprise",
                "target_audience": "Large enterprises",
                "current_segments": ["Enterprise", "Mid-market", "Government"]
            },
            "market_data": {
                "trends": [
                    "AI and ML automation", "Digital transformation",
                    "Cloud-first architecture", "API-driven integration",
                    "Data-driven decision making", "Remote collaboration"
                ],
                "growth_areas": [
                    "AI automation", "Enterprise SaaS", "Data analytics",
                    "API management"
                ],
                "target_segments": ["Enterprise", "SMB", "Government", "Healthcare"],
                "market_size": "Very Large",
                "growth_rate": 22
            },
            "competitor_data": {
                "strong_competitors": ["Salesforce", "Microsoft", "Oracle"],
                "emerging_players": ["DataRobot", "Snowflake"],
                "market_leaders": ["Salesforce", "Microsoft"],
                "common_features": [
                    "Analytics", "API management", "AI integration",
                    "Enterprise security", "Workflow automation"
                ]
            },
            "company_context": {
                "name": "DataPro Enterprise",
                "sector": "Enterprise SaaS",
                "business_model": "Subscription + Professional Services",
                "website": "https://datapro.com"
            }
        }

    @pytest.fixture
    def sample_startup_data(self):
        """Dataset simulando startup em crescimento"""
        return {
            "product_data": {
                "features": [
                    "Basic dashboard", "User authentication", "Simple reporting",
                    "API access", "Mobile responsive"
                ],
                "business_metrics": {
                    "active_users": 500,
                    "retention_rate": 60,
                    "nps_score": 25,
                    "monthly_growth_rate": 35,
                    "user_satisfaction": 70,
                    "average_feature_usage": 45,
                    "support_rating": 3.8
                },
                "user_feedback": [
                    "Need more features", "Performance issues",
                    "Want better UX", "Missing integrations"
                ],
                "roadmap_requests": [
                    "Advanced analytics", "Better mobile app", "Integrations"
                ],
                "age_months": 8,
                "unique_features": ["Simplified workflow"],
                "tech_stack": ["React", "Node.js", "cloud"],
                "patents": 0,
                "user_requests": [
                    "Advanced features", "Better performance", "More integrations"
                ],
                "performance_metrics": {
                    "speed_advantage": False,
                    "reliability_score": 85
                },
                "pricing_tier": "freemium",
                "target_audience": "SMB and startups",
                "current_segments": ["SMB"]
            },
            "market_data": {
                "trends": ["SaaS adoption", "Digital transformation", "Remote work"],
                "growth_areas": ["SMB SaaS", "Remote tools"],
                "target_segments": ["SMB", "Startups", "Freelancers"],
                "market_size": "Medium",
                "growth_rate": 28
            },
            "competitor_data": {
                "strong_competitors": ["Notion", "Airtable"],
                "emerging_players": ["Linear", "Height"],
                "market_leaders": ["Notion", "Monday.com"],
                "common_features": ["Templates", "Collaboration", "Mobile app"]
            },
            "company_context": {
                "name": "StartupTool",
                "sector": "Productivity SaaS",
                "business_model": "Freemium",
                "website": "https://startuptool.com"
            }
        }

    @pytest.mark.asyncio
    async def test_full_enterprise_analysis_flow(self, agent, sample_enterprise_data):
        """Testa fluxo completo de análise para empresa enterprise"""
        with patch.object(agent, 'agent') as mock_agent:
            # Mock da resposta do agente
            mock_response = Mock()
            mock_response.content = """
            # DIAGNÓSTICO ESTRATÉGICO
            
            **Posição Atual:** Líder de mercado em segmento enterprise
            **Forças:** AI integration, alta confiabilidade, base sólida de usuários
            **Fraquezas:** Mobile experience limitada, integrações podem melhorar
            
            # OPORTUNIDADES ESTRATÉGICAS
            
            **Curto Prazo (3-6 meses):**
            1. Mobile optimization - Alto impacto, esforço médio, ROI 300%
            2. API improvements - Alto impacto, esforço baixo, ROI 400%
            3. Advanced integrations - Médio impacto, esforço médio, ROI 250%
            
            **Médio Prazo (6-12 meses):**
            1. ML-powered insights - Alto impacto, esforço alto, ROI 500%
            2. Multi-tenant architecture - Alto impacto, esforço alto, ROI 400%
            
            **Longo Prazo (12+ meses):**
            1. AI platform expansion - Transformacional, esforço muito alto, ROI 800%
            
            # ROADMAP ESTRATÉGICO
            
            **Q1:** Mobile optimization, API improvements
            **Q2-Q3:** ML insights development, architecture improvements  
            **Q4+:** AI platform development
            
            # MÉTRICAS DE SUCESSO
            
            - Mobile usage: +150%
            - API adoption: +200%
            - Customer satisfaction: +15%
            """
            mock_agent.run.return_value = mock_response

            # Executar análise completa
            result = agent.analyze_product_strategy(
                sample_enterprise_data["product_data"],
                sample_enterprise_data["market_data"],
                sample_enterprise_data["competitor_data"],
                sample_enterprise_data["company_context"]
            )

            # Verificações abrangentes
            assert result is not None
            assert result["agent_type"] == "ProductOwnerAgent"

            # Verificar dados de entrada processados
            assert "product_insights" in result
            assert "market_trends" in result
            assert "strategic_analysis" in result

            # Verificar scores
            assert 0 <= result["product_market_fit_score"] <= 100
            assert 0 <= result["innovation_score"] <= 100
            assert 0 <= result["user_value_score"] <= 100

            # Verificar análise estratégica
            assert "DIAGNÓSTICO ESTRATÉGICO" in result["strategic_analysis"]
            assert "ROADMAP ESTRATÉGICO" in result["strategic_analysis"]

            # Verificar categorização estratégica
            assert result["strategy_category"] in [
                "Líder de Mercado - Manter posição e inovar",
                "Posição Forte - Expandir e otimizar",
                "Crescimento - Focar em product-market fit",
                "Inovador - Buscar encaixe no mercado",
                "Desenvolvimento - Melhorar produto e posicionamento"
            ]

    @pytest.mark.asyncio
    async def test_startup_growth_scenario(self, agent, sample_startup_data):
        """Testa cenário de startup em crescimento"""
        with patch.object(agent, 'agent') as mock_agent:
            mock_response = Mock()
            mock_response.content = """
            # DIAGNÓSTICO ESTRATÉGICO
            
            **Posição Atual:** Startup em crescimento com potential PMF
            **Forças:** Crescimento rápido, simplicidade, foco no SMB
            **Fraquezas:** Features limitadas, baixo NPS, performance
            
            # OPORTUNIDADES ESTRATÉGICAS
            
            **Curto Prazo:**
            1. Performance optimization - Crítico para retenção
            2. Core features expansion - Essencial para competir
            3. UX improvements - Alto impacto na satisfação
            
            **Roadmap Focado em PMF:**
            - Melhorar core product
            - Expandir features essenciais
            - Otimizar performance e UX
            """
            mock_agent.run.return_value = mock_response

            result = agent.analyze_product_strategy(
                sample_startup_data["product_data"],
                sample_startup_data["market_data"],
                sample_startup_data["competitor_data"],
                sample_startup_data["company_context"]
            )

            # Verificações específicas para startup
            # Score esperado mais baixo
            assert result["analysis_summary"]["overall_score"] < 75
            assert "product_insights" in result

            # Startup deve ter strategy category focada em crescimento
            expected_categories = [
                "Crescimento - Focar em product-market fit",
                "Desenvolvimento - Melhorar produto e posicionamento"
            ]
            assert result["strategy_category"] in expected_categories

    def test_roadmap_prioritization_integration(self, agent):
        """Testa integração completa de priorização de roadmap"""
        features_data = [
            {
                "name": "AI Analytics Platform",
                "business_value": 10,
                "effort": 8,
                "user_impact": 9,
                "category": "AI"
            },
            {
                "name": "Mobile App 2.0",
                "business_value": 8,
                "effort": 6,
                "user_impact": 8,
                "category": "Mobile"
            },
            {
                "name": "API Gateway",
                "business_value": 7,
                "effort": 4,
                "user_impact": 7,
                "category": "Infrastructure"
            },
            {
                "name": "Advanced Security",
                "business_value": 9,
                "effort": 7,
                "user_impact": 6,
                "category": "Security"
            }
        ]

        with patch.object(agent, 'agent') as mock_agent:
            mock_response = Mock()
            mock_response.content = """
            # ROADMAP PRIORIZADO
            
            ## Sprint 1-2 (Quick Wins)
            - API Gateway: Alta prioridade, baixo esforço
            - Foundations para features maiores
            
            ## Sprint 3-4 (Impact Features)
            - Mobile App 2.0: Médio esforço, alto impacto
            - Advanced Security: Preparação para enterprise
            
            ## Sprint 5-6 (Strategic Initiatives)
            - AI Analytics Platform: Alto esforço, transformacional
            """
            mock_agent.run.return_value = mock_response

            result = agent.create_feature_roadmap(features_data)

            # Verificações
            assert "roadmap_analysis" in result
            assert "prioritized_features" in result
            assert result["total_features"] == 4

            # Verificar ordenação por prioridade
            priorities = result["prioritized_features"]
            for i in range(len(priorities) - 1):
                assert priorities[i]["priority_score"] >= priorities[i +
                                                                     1]["priority_score"]

    def test_market_opportunities_integration(self, agent, sample_enterprise_data):
        """Testa integração de análise de oportunidades de mercado"""
        with patch.object(agent, 'agent') as mock_agent:
            mock_response = Mock()
            mock_response.content = """
            # OPORTUNIDADES DE MERCADO
            
            ## Expansão de Segmentos
            - Healthcare: Regulamentações específicas, alto potencial
            - Government: Contratos de longo prazo, margem premium
            
            ## Inovações Tecnológicas
            - Edge computing integration
            - Advanced ML capabilities
            - Real-time collaboration 2.0
            
            ## Parcerias Estratégicas
            - Cloud providers (AWS, Azure)
            - System integrators
            - Industry-specific partners
            """
            mock_agent.run.return_value = mock_response

            result = agent.assess_market_opportunities(
                sample_enterprise_data["market_data"],
                sample_enterprise_data["product_data"],
                sample_enterprise_data["company_context"]
            )

            # Verificações
            assert "market_opportunity_analysis" in result
            assert "opportunity_score" in result
            assert result["opportunity_score"] > 50  # Alto potencial esperado
            assert "current_positioning" in result

    @pytest.mark.asyncio
    async def test_storage_integration(self, agent, sample_enterprise_data):
        """Testa integração com sistema de storage"""
        # Este teste simula a integração real com storage
        with patch('backend.tools.agents.storage.AgentStorageManager') as mock_storage_manager:
            mock_storage = AsyncMock()
            mock_storage_manager.return_value.__aenter__.return_value = mock_storage
            mock_storage.save_analysis_result.return_value = "analysis_id_123"

            with patch.object(agent, 'agent') as mock_agent:
                mock_response = Mock()
                mock_response.content = "Análise estratégica para storage"
                mock_agent.run.return_value = mock_response

                # Executar análise
                result = agent.analyze_product_strategy(
                    sample_enterprise_data["product_data"],
                    sample_enterprise_data["market_data"],
                    sample_enterprise_data["competitor_data"],
                    sample_enterprise_data["company_context"]
                )

                # Simular salvamento no storage
                from ..storage import save_analysis_async

                # Verificar se o resultado pode ser salvo (formato correto)
                assert "agent_type" in result
                assert "product_insights" in result
                assert "strategic_analysis" in result

    def test_performance_with_large_dataset(self, agent):
        """Testa performance com dataset grande"""
        large_product_data = {
            "features": [f"Feature_{i}" for i in range(100)],
            "business_metrics": {
                "active_users": 100000,
                "retention_rate": 85,
                "nps_score": 70
            },
            "user_feedback": [f"Feedback_{i}" for i in range(50)],
            "roadmap_requests": [f"Request_{i}" for i in range(30)]
        }

        large_market_data = {
            "trends": [f"Trend_{i}" for i in range(20)],
            "growth_areas": [f"Area_{i}" for i in range(10)]
        }

        with patch.object(agent, 'agent') as mock_agent:
            mock_response = Mock()
            mock_response.content = "Análise de dataset grande processada com sucesso"
            mock_agent.run.return_value = mock_response

            # Executar com timeout para verificar performance
            import time
            start_time = time.time()

            result = agent.analyze_product_strategy(
                large_product_data,
                large_market_data
            )

            end_time = time.time()
            processing_time = end_time - start_time

            # Verificações de performance
            assert processing_time < 10.0  # Não deve demorar mais que 10 segundos
            assert result is not None
            assert len(result["product_insights"]
                       ["feature_gaps"]) <= 10  # Limitar output

    def test_concurrent_analysis_requests(self, agent, sample_enterprise_data):
        """Testa requisições concorrentes"""
        import concurrent.futures

        def run_analysis():
            with patch.object(agent, 'agent') as mock_agent:
                mock_response = Mock()
                mock_response.content = f"Análise concorrente"
                mock_agent.run.return_value = mock_response

                return agent.analyze_product_strategy(
                    sample_enterprise_data["product_data"]
                )

        # Executar múltiplas análises em paralelo
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            futures = [executor.submit(run_analysis) for _ in range(3)]
            results = [future.result() for future in futures]

        # Verificar que todas as análises foram processadas
        assert len(results) == 3
        for result in results:
            assert result is not None
            assert result["agent_type"] == "ProductOwnerAgent"

    def test_error_recovery_integration(self, agent, sample_enterprise_data):
        """Testa recuperação de erros em cenários reais"""
        # Simular falha inicial seguida de sucesso
        call_count = 0

        def mock_run_with_failure(prompt):
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                raise Exception("Falha temporária de rede")

            mock_response = Mock()
            mock_response.content = "Análise bem-sucedida após retry"
            return mock_response

        with patch.object(agent, 'agent') as mock_agent:
            mock_agent.run.side_effect = mock_run_with_failure

            # Primeira tentativa deve falhar
            with pytest.raises(Exception, match="Falha temporária"):
                agent.analyze_product_strategy(
                    sample_enterprise_data["product_data"])

            # Segunda tentativa deve funcionar
            result = agent.analyze_product_strategy(
                sample_enterprise_data["product_data"])
            assert result is not None
            assert "strategic_analysis" in result

    def test_recommendations_extraction_integration(self, agent, sample_enterprise_data):
        """Testa extração integrada de recomendações"""
        with patch.object(agent, 'agent') as mock_agent:
            mock_response = Mock()
            mock_response.content = "Análise com recomendações extraíveis"
            mock_agent.run.return_value = mock_response

            # Executar análise completa
            result = agent.analyze_product_strategy(
                sample_enterprise_data["product_data"],
                sample_enterprise_data["market_data"],
                sample_enterprise_data["competitor_data"],
                sample_enterprise_data["company_context"]
            )

            # Extrair recomendações
            recommendations = agent.get_strategic_recommendations_summary(
                result)

            # Verificações
            assert isinstance(recommendations, list)
            if recommendations:  # Se há recomendações
                for rec in recommendations:
                    assert "category" in rec
                    assert "recommendation" in rec
                    assert "priority" in rec
                    assert "type" in rec
                    assert rec["priority"] in [
                        "Critical", "High", "Medium", "Low"]
                    assert rec["type"] in ["Strategic", "Tactical"]
