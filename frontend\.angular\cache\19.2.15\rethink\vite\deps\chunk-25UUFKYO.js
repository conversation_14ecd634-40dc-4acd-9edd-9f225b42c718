import {
  __name,
  getConfig2,
  select_default
} from "./chunk-ULV4NQHW.js";

// node_modules/mermaid/dist/chunks/mermaid.core/chunk-7B677QYD.mjs
var selectSvgElement = __name((id) => {
  const {
    securityLevel
  } = getConfig2();
  let root = select_default("body");
  if (securityLevel === "sandbox") {
    const sandboxElement = select_default(`#i${id}`);
    const doc = sandboxElement.node()?.contentDocument ?? document;
    root = select_default(doc.body);
  }
  const svg = root.select(`#${id}`);
  return svg;
}, "selectSvgElement");

export {
  selectSvgElement
};
//# sourceMappingURL=chunk-25UUFKYO.js.map
