{"ast": null, "code": "/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { ɵgetDOM as _getDOM, DOCUMENT, PlatformLocation } from '@angular/common';\nimport { MockPlatformLocation } from '@angular/common/testing';\nimport * as i0 from '@angular/core';\nimport { Inject, Injectable, createPlatformFactory, APP_ID, ɵinternalProvideZoneChangeDetection as _internalProvideZoneChangeDetection, ɵChangeDetectionSchedulerImpl as _ChangeDetectionSchedulerImpl, ɵChangeDetectionScheduler as _ChangeDetectionScheduler, NgModule } from '@angular/core';\nimport { TestComponentRenderer } from '@angular/core/testing';\nimport { platformBrowser, BrowserModule } from './browser-D-u-fknz.mjs';\nimport './dom_renderer-DGKzginR.mjs';\n\n/**\n * A DOM based implementation of the TestComponentRenderer.\n */\nclass DOMTestComponentRenderer extends TestComponentRenderer {\n  _doc;\n  constructor(_doc) {\n    super();\n    this._doc = _doc;\n  }\n  insertRootElement(rootElId) {\n    this.removeAllRootElementsImpl();\n    const rootElement = _getDOM().getDefaultDocument().createElement('div');\n    rootElement.setAttribute('id', rootElId);\n    this._doc.body.appendChild(rootElement);\n  }\n  removeAllRootElements() {\n    // Check whether the `DOCUMENT` instance retrieved from DI contains\n    // the necessary function to complete the cleanup. In tests that don't\n    // interact with DOM, the `DOCUMENT` might be mocked and some functions\n    // might be missing. For such tests, DOM cleanup is not required and\n    // we skip the logic if there are missing functions.\n    if (typeof this._doc.querySelectorAll === 'function') {\n      this.removeAllRootElementsImpl();\n    }\n  }\n  removeAllRootElementsImpl() {\n    const oldRoots = this._doc.querySelectorAll('[id^=root]');\n    for (let i = 0; i < oldRoots.length; i++) {\n      _getDOM().remove(oldRoots[i]);\n    }\n  }\n  static ɵfac = function DOMTestComponentRenderer_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DOMTestComponentRenderer)(i0.ɵɵinject(DOCUMENT));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DOMTestComponentRenderer,\n    factory: DOMTestComponentRenderer.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DOMTestComponentRenderer, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/**\n * Platform for testing\n *\n * @publicApi\n */\nconst platformBrowserTesting = createPlatformFactory(platformBrowser, 'browserTesting');\n/**\n * NgModule for testing.\n *\n * @publicApi\n */\nclass BrowserTestingModule {\n  static ɵfac = function BrowserTestingModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || BrowserTestingModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: BrowserTestingModule,\n    exports: [BrowserModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [{\n      provide: APP_ID,\n      useValue: 'a'\n    }, _internalProvideZoneChangeDetection({}), {\n      provide: _ChangeDetectionScheduler,\n      useExisting: _ChangeDetectionSchedulerImpl\n    }, {\n      provide: PlatformLocation,\n      useClass: MockPlatformLocation\n    }, {\n      provide: TestComponentRenderer,\n      useClass: DOMTestComponentRenderer\n    }],\n    imports: [BrowserModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserTestingModule, [{\n    type: NgModule,\n    args: [{\n      exports: [BrowserModule],\n      providers: [{\n        provide: APP_ID,\n        useValue: 'a'\n      }, _internalProvideZoneChangeDetection({}), {\n        provide: _ChangeDetectionScheduler,\n        useExisting: _ChangeDetectionSchedulerImpl\n      }, {\n        provide: PlatformLocation,\n        useClass: MockPlatformLocation\n      }, {\n        provide: TestComponentRenderer,\n        useClass: DOMTestComponentRenderer\n      }]\n    }]\n  }], null, null);\n})();\nexport { BrowserTestingModule, platformBrowserTesting };", "map": {"version": 3, "names": ["ɵgetDOM", "_getDOM", "DOCUMENT", "PlatformLocation", "MockPlatformLocation", "i0", "Inject", "Injectable", "createPlatformFactory", "APP_ID", "ɵinternalProvideZoneChangeDetection", "_internalProvideZoneChangeDetection", "ɵChangeDetectionSchedulerImpl", "_ChangeDetectionSchedulerImpl", "ɵChangeDetectionScheduler", "_ChangeDetectionScheduler", "NgModule", "TestComponent<PERSON><PERSON><PERSON>", "platformBrowser", "BrowserModule", "DOMTestComponentRenderer", "_doc", "constructor", "insertRootElement", "rootElId", "removeAllRootElementsImpl", "rootElement", "getDefaultDocument", "createElement", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "removeAllRootElements", "querySelectorAll", "oldRoots", "i", "length", "remove", "ɵfac", "DOMTestComponentRenderer_Factory", "__ngFactoryType__", "ɵɵinject", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "undefined", "decorators", "args", "platformBrowserTesting", "BrowserTestingModule", "BrowserTestingModule_Factory", "ɵmod", "ɵɵdefineNgModule", "exports", "ɵinj", "ɵɵdefineInjector", "providers", "provide", "useValue", "useExisting", "useClass", "imports"], "sources": ["C:/projetos/scope-ai/frontend/node_modules/@angular/platform-browser/fesm2022/testing.mjs"], "sourcesContent": ["/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { ɵgetDOM as _getDOM, DOCUMENT, PlatformLocation } from '@angular/common';\nimport { MockPlatformLocation } from '@angular/common/testing';\nimport * as i0 from '@angular/core';\nimport { Inject, Injectable, createPlatformFactory, APP_ID, ɵinternalProvideZoneChangeDetection as _internalProvideZoneChangeDetection, ɵChangeDetectionSchedulerImpl as _ChangeDetectionSchedulerImpl, ɵChangeDetectionScheduler as _ChangeDetectionScheduler, NgModule } from '@angular/core';\nimport { TestComponentRenderer } from '@angular/core/testing';\nimport { platformBrowser, BrowserModule } from './browser-D-u-fknz.mjs';\nimport './dom_renderer-DGKzginR.mjs';\n\n/**\n * A DOM based implementation of the TestComponentRenderer.\n */\nclass DOMTestComponentRenderer extends TestComponentRenderer {\n    _doc;\n    constructor(_doc) {\n        super();\n        this._doc = _doc;\n    }\n    insertRootElement(rootElId) {\n        this.removeAllRootElementsImpl();\n        const rootElement = _getDOM().getDefaultDocument().createElement('div');\n        rootElement.setAttribute('id', rootElId);\n        this._doc.body.appendChild(rootElement);\n    }\n    removeAllRootElements() {\n        // Check whether the `DOCUMENT` instance retrieved from DI contains\n        // the necessary function to complete the cleanup. In tests that don't\n        // interact with DOM, the `DOCUMENT` might be mocked and some functions\n        // might be missing. For such tests, DOM cleanup is not required and\n        // we skip the logic if there are missing functions.\n        if (typeof this._doc.querySelectorAll === 'function') {\n            this.removeAllRootElementsImpl();\n        }\n    }\n    removeAllRootElementsImpl() {\n        const oldRoots = this._doc.querySelectorAll('[id^=root]');\n        for (let i = 0; i < oldRoots.length; i++) {\n            _getDOM().remove(oldRoots[i]);\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: DOMTestComponentRenderer, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: DOMTestComponentRenderer });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: DOMTestComponentRenderer, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n\n/**\n * Platform for testing\n *\n * @publicApi\n */\nconst platformBrowserTesting = createPlatformFactory(platformBrowser, 'browserTesting');\n/**\n * NgModule for testing.\n *\n * @publicApi\n */\nclass BrowserTestingModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: BrowserTestingModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.14\", ngImport: i0, type: BrowserTestingModule, exports: [BrowserModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: BrowserTestingModule, providers: [\n            { provide: APP_ID, useValue: 'a' },\n            _internalProvideZoneChangeDetection({}),\n            { provide: _ChangeDetectionScheduler, useExisting: _ChangeDetectionSchedulerImpl },\n            { provide: PlatformLocation, useClass: MockPlatformLocation },\n            { provide: TestComponentRenderer, useClass: DOMTestComponentRenderer },\n        ], imports: [BrowserModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: BrowserTestingModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [BrowserModule],\n                    providers: [\n                        { provide: APP_ID, useValue: 'a' },\n                        _internalProvideZoneChangeDetection({}),\n                        { provide: _ChangeDetectionScheduler, useExisting: _ChangeDetectionSchedulerImpl },\n                        { provide: PlatformLocation, useClass: MockPlatformLocation },\n                        { provide: TestComponentRenderer, useClass: DOMTestComponentRenderer },\n                    ],\n                }]\n        }] });\n\nexport { BrowserTestingModule, platformBrowserTesting };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,OAAO,IAAIC,OAAO,EAAEC,QAAQ,EAAEC,gBAAgB,QAAQ,iBAAiB;AAChF,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,UAAU,EAAEC,qBAAqB,EAAEC,MAAM,EAAEC,mCAAmC,IAAIC,mCAAmC,EAAEC,6BAA6B,IAAIC,6BAA6B,EAAEC,yBAAyB,IAAIC,yBAAyB,EAAEC,QAAQ,QAAQ,eAAe;AAC/R,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,eAAe,EAAEC,aAAa,QAAQ,wBAAwB;AACvE,OAAO,6BAA6B;;AAEpC;AACA;AACA;AACA,MAAMC,wBAAwB,SAASH,qBAAqB,CAAC;EACzDI,IAAI;EACJC,WAAWA,CAACD,IAAI,EAAE;IACd,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,IAAI,GAAGA,IAAI;EACpB;EACAE,iBAAiBA,CAACC,QAAQ,EAAE;IACxB,IAAI,CAACC,yBAAyB,CAAC,CAAC;IAChC,MAAMC,WAAW,GAAGzB,OAAO,CAAC,CAAC,CAAC0B,kBAAkB,CAAC,CAAC,CAACC,aAAa,CAAC,KAAK,CAAC;IACvEF,WAAW,CAACG,YAAY,CAAC,IAAI,EAAEL,QAAQ,CAAC;IACxC,IAAI,CAACH,IAAI,CAACS,IAAI,CAACC,WAAW,CAACL,WAAW,CAAC;EAC3C;EACAM,qBAAqBA,CAAA,EAAG;IACpB;IACA;IACA;IACA;IACA;IACA,IAAI,OAAO,IAAI,CAACX,IAAI,CAACY,gBAAgB,KAAK,UAAU,EAAE;MAClD,IAAI,CAACR,yBAAyB,CAAC,CAAC;IACpC;EACJ;EACAA,yBAAyBA,CAAA,EAAG;IACxB,MAAMS,QAAQ,GAAG,IAAI,CAACb,IAAI,CAACY,gBAAgB,CAAC,YAAY,CAAC;IACzD,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,QAAQ,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;MACtClC,OAAO,CAAC,CAAC,CAACoC,MAAM,CAACH,QAAQ,CAACC,CAAC,CAAC,CAAC;IACjC;EACJ;EACA,OAAOG,IAAI,YAAAC,iCAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAyFpB,wBAAwB,EAAlCf,EAAE,CAAAoC,QAAA,CAAkDvC,QAAQ;EAAA;EACtJ,OAAOwC,KAAK,kBAD8ErC,EAAE,CAAAsC,kBAAA;IAAAC,KAAA,EACYxB,wBAAwB;IAAAyB,OAAA,EAAxBzB,wBAAwB,CAAAkB;EAAA;AACpI;AACA;EAAA,QAAAQ,SAAA,oBAAAA,SAAA,KAH8FzC,EAAE,CAAA0C,iBAAA,CAGJ3B,wBAAwB,EAAc,CAAC;IACvH4B,IAAI,EAAEzC;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEyC,IAAI,EAAEC,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CF,IAAI,EAAE1C,MAAM;MACZ6C,IAAI,EAAE,CAACjD,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA;AACA;AACA;AACA;AACA,MAAMkD,sBAAsB,GAAG5C,qBAAqB,CAACU,eAAe,EAAE,gBAAgB,CAAC;AACvF;AACA;AACA;AACA;AACA;AACA,MAAMmC,oBAAoB,CAAC;EACvB,OAAOf,IAAI,YAAAgB,6BAAAd,iBAAA;IAAA,YAAAA,iBAAA,IAAyFa,oBAAoB;EAAA;EACxH,OAAOE,IAAI,kBAvB+ElD,EAAE,CAAAmD,gBAAA;IAAAR,IAAA,EAuBSK,oBAAoB;IAAAI,OAAA,GAAYtC,aAAa;EAAA;EAClJ,OAAOuC,IAAI,kBAxB+ErD,EAAE,CAAAsD,gBAAA;IAAAC,SAAA,EAwB0C,CAC9H;MAAEC,OAAO,EAAEpD,MAAM;MAAEqD,QAAQ,EAAE;IAAI,CAAC,EAClCnD,mCAAmC,CAAC,CAAC,CAAC,CAAC,EACvC;MAAEkD,OAAO,EAAE9C,yBAAyB;MAAEgD,WAAW,EAAElD;IAA8B,CAAC,EAClF;MAAEgD,OAAO,EAAE1D,gBAAgB;MAAE6D,QAAQ,EAAE5D;IAAqB,CAAC,EAC7D;MAAEyD,OAAO,EAAE5C,qBAAqB;MAAE+C,QAAQ,EAAE5C;IAAyB,CAAC,CACzE;IAAA6C,OAAA,GAAY9C,aAAa;EAAA;AAClC;AACA;EAAA,QAAA2B,SAAA,oBAAAA,SAAA,KAhC8FzC,EAAE,CAAA0C,iBAAA,CAgCJM,oBAAoB,EAAc,CAAC;IACnHL,IAAI,EAAEhC,QAAQ;IACdmC,IAAI,EAAE,CAAC;MACCM,OAAO,EAAE,CAACtC,aAAa,CAAC;MACxByC,SAAS,EAAE,CACP;QAAEC,OAAO,EAAEpD,MAAM;QAAEqD,QAAQ,EAAE;MAAI,CAAC,EAClCnD,mCAAmC,CAAC,CAAC,CAAC,CAAC,EACvC;QAAEkD,OAAO,EAAE9C,yBAAyB;QAAEgD,WAAW,EAAElD;MAA8B,CAAC,EAClF;QAAEgD,OAAO,EAAE1D,gBAAgB;QAAE6D,QAAQ,EAAE5D;MAAqB,CAAC,EAC7D;QAAEyD,OAAO,EAAE5C,qBAAqB;QAAE+C,QAAQ,EAAE5C;MAAyB,CAAC;IAE9E,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASiC,oBAAoB,EAAED,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}