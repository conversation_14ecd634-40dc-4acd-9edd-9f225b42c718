"""
Product Analysis Tools para Agno

Tools customizadas para análise de produtos e mercado:
- Análise de features e funcionalidades atuais
- Identificação de gaps competitivos
- Análise de oportunidades de mercado
- Priorização de roadmap baseada em dados
"""

import logging
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field
from datetime import datetime

logger = logging.getLogger(__name__)


class ProductInsights(BaseModel):
    """Schema para insights de produto processados"""
    product_maturity: str = Field(...,
                                  description="Nível de maturidade do produto")
    market_positioning: str = Field(...,
                                    description="Posicionamento no mercado")
    competitive_advantage: str = Field(...,
                                       description="Vantagem competitiva identificada")
    feature_completeness: Optional[str] = Field(
        None, description="Completude das funcionalidades")

    # Gaps e oportunidades
    feature_gaps: List[str] = Field(
        default_factory=list, description="Gaps de funcionalidades")
    market_opportunities: List[str] = Field(
        default_factory=list, description="Oportunidades de mercado")
    competitive_threats: List[str] = Field(
        default_factory=list, description="Ameaças competitivas")

    # Scores
    product_market_fit_score: float = Field(
        default=0.0, description="Score de product-market fit (0-100)")
    innovation_score: float = Field(
        default=0.0, description="Score de inovação (0-100)")
    user_value_score: float = Field(
        default=0.0, description="Score de valor para usuário (0-100)")


class RoadmapPriority(BaseModel):
    """Schema para priorização de roadmap"""
    feature: str = Field(..., description="Nome da funcionalidade")
    business_value: int = Field(..., ge=1, le=10,
                                description="Valor de negócio (1-10)")
    effort: int = Field(..., ge=1, le=10,
                        description="Esforço estimado (1-10)")
    user_impact: int = Field(..., ge=1, le=10,
                             description="Impacto no usuário (1-10)")
    priority_score: float = Field(
        default=0.0, description="Score de prioridade calculado")
    category: str = Field(
        default="Feature", description="Categoria da funcionalidade")


class ProductAnalysisTools:
    """
    Tools para análise de produtos e mercado

    Processa dados de produtos, concorrentes e mercado
    para gerar insights estratégicos e roadmaps priorizados.
    """

    def analyze_product_data(self,
                             product_data: Dict[str, Any],
                             market_data: Optional[Dict[str, Any]] = None,
                             competitor_data: Optional[Dict[str, Any]] = None) -> ProductInsights:
        """
        Analisa dados de produto e extrai insights estratégicos

        Args:
            product_data: Dados do produto atual
            market_data: Dados de mercado (opcional)
            competitor_data: Dados dos concorrentes (opcional)

        Returns:
            Insights de produto processados
        """
        try:
            logger.info("Iniciando análise de dados de produto")

            # Extrair informações básicas do produto
            features = product_data.get("features", [])
            user_feedback = product_data.get("user_feedback", [])
            business_metrics = product_data.get("business_metrics", {})

            # Analisar maturidade do produto
            product_maturity = self._assess_product_maturity(product_data)

            # Analisar posicionamento no mercado
            market_positioning = self._analyze_market_positioning(
                product_data, market_data)

            # Identificar vantagem competitiva
            competitive_advantage = self._identify_competitive_advantage(
                product_data, competitor_data)

            # Avaliar completude de funcionalidades
            feature_completeness = self._assess_feature_completeness(features)

            # Identificar gaps e oportunidades
            feature_gaps = self._identify_feature_gaps(
                product_data, competitor_data)
            market_opportunities = self._identify_market_opportunities(
                market_data, product_data)
            competitive_threats = self._identify_competitive_threats(
                competitor_data)

            # Calcular scores
            product_market_fit_score = self._calculate_product_market_fit(
                product_data, market_data)
            innovation_score = self._calculate_innovation_score(
                product_data, competitor_data)
            user_value_score = self._calculate_user_value_score(product_data)

            insights = ProductInsights(
                product_maturity=product_maturity,
                market_positioning=market_positioning,
                competitive_advantage=competitive_advantage,
                feature_completeness=feature_completeness,
                feature_gaps=feature_gaps,
                market_opportunities=market_opportunities,
                competitive_threats=competitive_threats,
                product_market_fit_score=product_market_fit_score,
                innovation_score=innovation_score,
                user_value_score=user_value_score
            )

            logger.info(
                f"Análise de produto concluída - Product-Market Fit: {product_market_fit_score}")
            return insights

        except Exception as e:
            logger.error(f"Erro na análise de produto: {str(e)}")
            raise

    def _assess_product_maturity(self, product_data: Dict[str, Any]) -> str:
        """Avalia a maturidade do produto"""
        try:
            features_count = len(product_data.get("features", []))
            user_base = product_data.get(
                "business_metrics", {}).get("active_users", 0)
            product_age_months = product_data.get("age_months", 0)

            # Critérios de maturidade
            if features_count >= 20 and user_base >= 10000 and product_age_months >= 24:
                return "Maduro"
            elif features_count >= 10 and user_base >= 1000 and product_age_months >= 12:
                return "Em crescimento"
            elif features_count >= 5 and product_age_months >= 6:
                return "Em desenvolvimento"
            else:
                return "Inicial/MVP"

        except Exception:
            return "Indeterminado"

    def _analyze_market_positioning(self, product_data: Dict[str, Any], market_data: Optional[Dict[str, Any]]) -> str:
        """Analisa posicionamento no mercado"""
        try:
            if not market_data:
                return "Posicionamento não analisado - dados de mercado não fornecidos"

            market_size = market_data.get("market_size", "")
            target_audience = product_data.get("target_audience", "")
            pricing_tier = product_data.get("pricing_tier", "").lower()

            # Determinar posicionamento baseado em dados
            if "premium" in pricing_tier or "enterprise" in pricing_tier:
                return "Premium/Enterprise - Foco em valor e qualidade"
            elif "free" in pricing_tier or "freemium" in pricing_tier:
                return "Freemium - Foco em adoção e escala"
            elif "mid" in pricing_tier or "standard" in pricing_tier:
                return "Mid-market - Equilibrio entre valor e preço"
            else:
                return "Posicionamento em definição"

        except Exception:
            return "Análise de posicionamento indisponível"

    def _identify_competitive_advantage(self, product_data: Dict[str, Any], competitor_data: Optional[Dict[str, Any]]) -> str:
        """Identifica vantagem competitiva"""
        try:
            unique_features = product_data.get("unique_features", [])
            performance_metrics = product_data.get("performance_metrics", {})

            if unique_features:
                return f"Funcionalidades únicas: {', '.join(unique_features[:3])}"
            elif performance_metrics.get("speed_advantage"):
                return "Vantagem de performance e velocidade"
            elif performance_metrics.get("reliability_score", 0) > 95:
                return "Alta confiabilidade e disponibilidade"
            else:
                return "Vantagem competitiva em identificação"

        except Exception:
            return "Análise de vantagem competitiva indisponível"

    def _assess_feature_completeness(self, features: List[str]) -> str:
        """Avalia completude das funcionalidades"""
        try:
            feature_count = len(features)

            if feature_count >= 25:
                return "Completo - Rica funcionalidade"
            elif feature_count >= 15:
                return "Abrangente - Funcionalidades essenciais cobertas"
            elif feature_count >= 8:
                return "Básico - Funcionalidades fundamentais"
            else:
                return "Limitado - Necessita expansão"

        except Exception:
            return "Não avaliado"

    def _identify_feature_gaps(self, product_data: Dict[str, Any], competitor_data: Optional[Dict[str, Any]]) -> List[str]:
        """Identifica gaps de funcionalidades"""
        try:
            gaps = []
            current_features = set(product_data.get("features", []))
            user_requests = product_data.get("user_requests", [])

            # Gaps baseados em solicitações de usuários
            for request in user_requests[:5]:  # Top 5 requests
                if request not in current_features:
                    gaps.append(f"Funcionalidade solicitada: {request}")

            # Gaps baseados em concorrentes (se disponível)
            if competitor_data:
                competitor_features = set(
                    competitor_data.get("common_features", []))
                missing_features = competitor_features - current_features
                for feature in list(missing_features)[:3]:  # Top 3
                    gaps.append(f"Funcionalidade comum no mercado: {feature}")

            # Gaps técnicos comuns
            common_gaps = [
                "API integrations",
                "Mobile app",
                "Analytics dashboard",
                "Automation features",
                "Advanced reporting"
            ]

            for gap in common_gaps:
                if gap.lower() not in [f.lower() for f in current_features]:
                    gaps.append(f"Gap técnico comum: {gap}")
                    if len(gaps) >= 5:  # Limitar a 5 gaps
                        break

            return gaps

        except Exception:
            return ["Análise de gaps não disponível"]

    def _identify_market_opportunities(self, market_data: Optional[Dict[str, Any]], product_data: Dict[str, Any]) -> List[str]:
        """Identifica oportunidades de mercado"""
        try:
            opportunities = []

            if not market_data:
                return ["Análise de mercado não disponível - dados necessários"]

            trends = market_data.get("trends", [])
            growth_areas = market_data.get("growth_areas", [])
            target_segments = market_data.get("target_segments", [])

            # Oportunidades baseadas em trends
            for trend in trends[:3]:
                opportunities.append(f"Trend de mercado: {trend}")

            # Oportunidades baseadas em áreas de crescimento
            for area in growth_areas[:2]:
                opportunities.append(f"Área de crescimento: {area}")

            # Oportunidades de segmentos
            current_segments = set(product_data.get("current_segments", []))
            for segment in target_segments:
                if segment not in current_segments:
                    opportunities.append(f"Novo segmento: {segment}")
                    if len(opportunities) >= 5:
                        break

            return opportunities

        except Exception:
            return ["Análise de oportunidades indisponível"]

    def _identify_competitive_threats(self, competitor_data: Optional[Dict[str, Any]]) -> List[str]:
        """Identifica ameaças competitivas"""
        try:
            threats = []

            if not competitor_data:
                return ["Análise competitiva não disponível"]

            strong_competitors = competitor_data.get("strong_competitors", [])
            emerging_players = competitor_data.get("emerging_players", [])
            market_leaders = competitor_data.get("market_leaders", [])

            # Ameaças de líderes de mercado
            for leader in market_leaders[:2]:
                threats.append(f"Líder de mercado: {leader}")

            # Ameaças de concorrentes fortes
            for competitor in strong_competitors[:2]:
                threats.append(f"Concorrente forte: {competitor}")

            # Ameaças de novos entrantes
            for player in emerging_players[:1]:
                threats.append(f"Novo entrante: {player}")

            return threats

        except Exception:
            return ["Análise de ameaças indisponível"]

    def _calculate_product_market_fit(self, product_data: Dict[str, Any], market_data: Optional[Dict[str, Any]]) -> float:
        """Calcula score de product-market fit"""
        try:
            score = 50.0  # Score base

            # Métricas de negócio
            metrics = product_data.get("business_metrics", {})
            retention_rate = metrics.get("retention_rate", 0)
            nps_score = metrics.get("nps_score", 0)
            growth_rate = metrics.get("monthly_growth_rate", 0)

            # Ajustar score baseado em métricas
            if retention_rate > 80:
                score += 20
            elif retention_rate > 60:
                score += 10

            if nps_score > 50:
                score += 15
            elif nps_score > 0:
                score += 5

            if growth_rate > 20:
                score += 15
            elif growth_rate > 10:
                score += 10
            elif growth_rate > 5:
                score += 5

            return min(score, 100.0)

        except Exception:
            return 50.0

    def _calculate_innovation_score(self, product_data: Dict[str, Any], competitor_data: Optional[Dict[str, Any]]) -> float:
        """Calcula score de inovação"""
        try:
            score = 50.0  # Score base

            unique_features = product_data.get("unique_features", [])
            tech_stack = product_data.get("tech_stack", [])
            patents = product_data.get("patents", 0)

            # Score baseado em funcionalidades únicas
            score += min(len(unique_features) * 10, 30)

            # Score baseado em tecnologias modernas
            modern_tech_keywords = [
                "ai", "ml", "blockchain", "iot", "cloud", "microservices"]
            modern_tech_count = sum(1 for tech in tech_stack if any(
                keyword in tech.lower() for keyword in modern_tech_keywords))
            score += min(modern_tech_count * 5, 20)

            # Score baseado em patentes
            score += min(patents * 2, 10)

            return min(score, 100.0)

        except Exception:
            return 50.0

    def _calculate_user_value_score(self, product_data: Dict[str, Any]) -> float:
        """Calcula score de valor para usuário"""
        try:
            score = 50.0  # Score base

            metrics = product_data.get("business_metrics", {})
            user_satisfaction = metrics.get("user_satisfaction", 0)
            feature_usage = metrics.get("average_feature_usage", 0)
            support_rating = metrics.get("support_rating", 0)

            # Score baseado em satisfação
            if user_satisfaction > 85:
                score += 25
            elif user_satisfaction > 70:
                score += 15
            elif user_satisfaction > 50:
                score += 5

            # Score baseado em uso de features
            if feature_usage > 80:
                score += 15
            elif feature_usage > 60:
                score += 10
            elif feature_usage > 40:
                score += 5

            # Score baseado em suporte
            if support_rating > 4.5:
                score += 10
            elif support_rating > 4.0:
                score += 5

            return min(score, 100.0)

        except Exception:
            return 50.0

    def prioritize_roadmap_features(self, features_data: List[Dict[str, Any]]) -> List[RoadmapPriority]:
        """
        Prioriza funcionalidades para roadmap baseado em critérios de produto

        Args:
            features_data: Lista de funcionalidades com dados de priorização

        Returns:
            Lista de funcionalidades priorizadas
        """
        try:
            priorities = []

            for feature_data in features_data:
                feature_name = feature_data.get("name", "Feature sem nome")
                business_value = feature_data.get("business_value", 5)
                effort = feature_data.get("effort", 5)
                user_impact = feature_data.get("user_impact", 5)
                category = feature_data.get("category", "Feature")

                # Calcular score de prioridade (valor/esforço * impacto)
                priority_score = (business_value / max(effort, 1)
                                  ) * (user_impact / 10) * 10

                priority = RoadmapPriority(
                    feature=feature_name,
                    business_value=business_value,
                    effort=effort,
                    user_impact=user_impact,
                    priority_score=round(priority_score, 2),
                    category=category
                )

                priorities.append(priority)

            # Ordenar por score de prioridade (maior primeiro)
            priorities.sort(key=lambda x: x.priority_score, reverse=True)

            logger.info(f"Roadmap priorizado com {len(priorities)} features")
            return priorities

        except Exception as e:
            logger.error(f"Erro ao priorizar roadmap: {str(e)}")
            return []

    def analyze_market_trends(self, market_data: Dict[str, Any], industry: str = "technology") -> Dict[str, Any]:
        """
        Analisa trends de mercado para oportunidades estratégicas

        Args:
            market_data: Dados de mercado
            industry: Setor da indústria

        Returns:
            Análise de trends de mercado
        """
        try:
            trends = market_data.get("trends", [])
            market_size = market_data.get("market_size", 0)
            growth_rate = market_data.get("growth_rate", 0)

            # Categorizar trends por relevância
            high_impact_trends = []
            medium_impact_trends = []

            trend_keywords = {
                "high_impact": ["ai", "automation", "digital transformation", "cloud", "mobile"],
                "medium_impact": ["analytics", "integration", "security", "scalability"]
            }

            for trend in trends:
                trend_lower = trend.lower()
                if any(keyword in trend_lower for keyword in trend_keywords["high_impact"]):
                    high_impact_trends.append(trend)
                elif any(keyword in trend_lower for keyword in trend_keywords["medium_impact"]):
                    medium_impact_trends.append(trend)

            # Analisar potencial de mercado
            market_potential = "Alto" if growth_rate > 15 else "Médio" if growth_rate > 5 else "Baixo"

            return {
                "industry": industry,
                "market_potential": market_potential,
                "market_size": market_size,
                "growth_rate": growth_rate,
                "high_impact_trends": high_impact_trends,
                "medium_impact_trends": medium_impact_trends,
                "trends_count": len(trends),
                "analysis_timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Erro na análise de trends: {str(e)}")
            return {"error": str(e)}
