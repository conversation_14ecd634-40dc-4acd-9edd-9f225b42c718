import { <PERSON><PERSON><PERSON>, NgFor } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';

import { CardModule } from 'primeng/card';
import { ChipModule } from 'primeng/chip';
import { ProgressBarModule } from 'primeng/progressbar';
import { TagModule } from 'primeng/tag';
import { TooltipModule } from 'primeng/tooltip';

import { IGeneratedProject, IProjectCardClickEvent } from '../../interfaces/generated-project.interface';

@Component({
	selector: 'app-project-card',
	standalone: true,
	imports: [NgClass, NgFor, CardModule, ChipModule, ProgressBarModule, TagModule, TooltipModule],
	templateUrl: './project-card.component.html',
	styleUrls: ['./project-card.component.scss'],
})
export class ProjectCardComponent {
	@Input() public project!: IGeneratedProject;
	@Input() public showActions = true;
	@Input() public clickable = true;
	@Output() public cardClick = new EventEmitter<IProjectCardClickEvent>();

	/**
	 * Manipula clique no card
	 */
	public onCardClick(): void {
		if (!this.clickable) return;

		this.cardClick.emit({
			project: this.project,
			action: 'nextPhase',
		});
	}

	/**
	 * Manipula visualização de detalhes
	 */
	public onViewDetails(event: Event): void {
		event.stopPropagation();
		this.cardClick.emit({
			project: this.project,
			action: 'view',
		});
	}

	/**
	 * 🎯 Manipula visualização da estimativa detalhada
	 */
	public onViewEstimate(event: Event): void {
		event.stopPropagation();
		this.cardClick.emit({
			project: this.project,
			action: 'viewEstimate',
		});
	}

	/**
	 * Trunca o resumo se exceder 90 caracteres
	 */
	public getTruncatedSummary(): string {
		if (!this.project || !this.project.resumo) return '';
		if (this.project.resumo.length <= 90) {
			return this.project.resumo;
		}
		return this.project.resumo.substring(0, 87) + '...';
	}

	/**
	 * Retorna classe CSS para o status
	 */
	public getStatusSeverity(): string {
		// TODO: Mapear status para severidades corretas (e.g., info, success, warning, danger)
		if (!this.project || !this.project.status) return 'info';
		const status = this.project.status.toLowerCase();
		switch (status) {
			case 'novo':
			case 'sugestão':
				return 'info';
			case 'em análise':
				return 'warning';
			case 'aprovado':
			case 'em execução':
				return 'success';
			case 'concluído':
				return 'primary';
			case 'rejeitado':
			case 'cancelado':
				return 'danger';
			default:
				return 'secondary';
		}
	}

	/**
	 * Retorna severidade para o badge de importância
	 */
	public getImportanceSeverity(): string {
		if (!this.project || !this.project.importancia) return 'info';

		const importancia = this.project.importancia.toLowerCase();

		if (importancia.includes('alta') || importancia.includes('high')) return 'danger';
		if (importancia.includes('média') || importancia.includes('medium')) return 'warning';
		if (importancia.includes('baixa') || importancia.includes('low')) return 'info';

		return 'info'; // Default
	}

	/**
	 * Formata data de criação
	 */
	public getFormattedDate(): string {
		if (!this.project || !this.project.created_at) return 'Data indisponível';
		const date = new Date(this.project.created_at);
		return date.toLocaleDateString('pt-BR', {
			day: '2-digit',
			month: '2-digit',
			year: 'numeric',
		});
	}

	/**
	 * Retorna estilo de gradiente baseado no tipo de projeto
	 */
	public getGradientStyle(): string {
		// Verificar se o projeto foi gerado/refinado pela IA
		if (this.project.ai_generated === true) {
			// Verde limão para projetos gerados pela IA
			return 'linear-gradient(to bottom right, #e2ff6f, #d4ff00, #c3f000)';
		}

		// Azul profissional para sugestões automáticas
		return 'linear-gradient(to bottom right, #3b82f6, #2563eb, #1d4ed8)';
	}
}
