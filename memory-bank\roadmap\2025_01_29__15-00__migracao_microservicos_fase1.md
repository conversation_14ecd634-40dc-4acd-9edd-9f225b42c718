# [TASK-001] Migração para Microserviços - Fase 1: Client Service

**Criado**: 2025-01-29 15:00  
**Status**: 🔄 EM ANDAMENTO  
**Prioridade**: P1  
**Estimativa**: 40h  
**Tags**: [architecture, microservices, ddd, backend]  
**Blocked By**: None

## Objetivo

Extrair toda a lógica relacionada a clientes do monolito atual para um microserviço independente, seguindo os princípios de Domain-Driven Design (DDD) e Clean Architecture, mantendo 100% de compatibilidade com o sistema atual durante a transição.

## Contexto

O backend atual é um monolito com arquivos massivos:
- `routes.py`: 2.417 linhas (God Route)
- `parsers.py`: 2.878 linhas
- `schemas.py`: 2.554 linhas
- `perplexity.py`: 2.070 linhas

A lógica de clientes está espalhada por todos esses arquivos, violando princípios SOLID e dificultando manutenção e escalabilidade.

## Microtarefas Detalhadas

### MT-1: Draft & Review Micro-task Prompt ✅
**Status**: ✅ CONCLUÍDO  
**Tempo**: 15min  
**Score**: 10/10

Microprompt interno definido:
```
Extract client domain logic from monolith to independent microservice following DDD principles.
Maintain 100% compatibility using Strangler Fig Pattern.
Focus on: entity modeling, use cases, repositories, API contracts.
```

### MT-2: Reasoning Prelude (ToT) ✅
**Status**: ✅ CONCLUÍDO  
**Tempo**: 45min  
**Score**: 9/10

Tree of Thought aplicado com sucesso explorando 4 branches arquiteturais:

**Decisões Tomadas**:
1. **Arquitetura**: Clean Architecture com layers horizontais (Domain, Application, Infrastructure, API)
2. **Comunicação**: Híbrida - REST para queries, eventos assíncronos (RabbitMQ) para comandos pesados
3. **Database**: Estratégia transitória - shared DB com collections separadas (clients_v2), migração gradual
4. **API Versioning**: URL path versioning (/api/v2/clients) com roteamento no API Gateway

**Justificativas**:
- Clean Architecture oferece melhor testabilidade e separação de responsabilidades
- Comunicação híbrida balança simplicidade com performance
- Database transitório permite migração sem breaking changes
- URL versioning é explícito e permite múltiplas versões simultâneas

### MT-3: Repo Scan - Mapear Dependências ✅
**Status**: ✅ CONCLUÍDO  
**Tempo**: 2h  
**Score**: 9/10

Mapeamento completo documentado em: `backend/microservices/client-service/docs/dependency-mapping.md`

**Principais Descobertas**:
- 16 endpoints mapeados no domínio de clientes
- 2.417 linhas em routes.py com múltiplas responsabilidades
- Dependências críticas: Perplexity API, PDF Generator, WebSocket Manager
- 25+ eventos WebSocket diferentes
- 5 background tasks principais
- GridFS para armazenamento de PDFs
- Acoplamento circular entre routes.py e api.report_routes

**Riscos Identificados**:
1. Alta complexidade com arquivos de 2k+ linhas
2. Lógica de negócio espalhada
3. Estado compartilhado (cache, websocket)
4. Múltiplas dependências externas

### MT-4: Criar Estrutura do Microserviço ✅
**Status**: ✅ CONCLUÍDO  
**Tempo**: 1h  
**Score**: 10/10

Estrutura completa criada em `/backend/microservices/client-service/` seguindo Clean Architecture.

**Decisão Importante**: Reutilizar configurações existentes do backend principal:
- ❌ NÃO criar arquivos duplicados (pyproject.toml, .env, Dockerfile)
- ✅ Usar imports relativos para acessar configurações
- ✅ Microserviço como parte do backend, não projeto isolado
- ✅ Benefício: evita drift de configuração, simplifica manutenção

**Arquivos Criados**:
```
backend/microservices/client-service/
├── src/
│   ├── domain/
│   │   ├── entities/
│   │   ├── value_objects/
│   │   ├── events/
│   │   └── repositories/
│   ├── application/
│   │   ├── use_cases/
│   │   ├── dtos/
│   │   └── services/
│   ├── infrastructure/
│   │   ├── persistence/
│   │   ├── external/
│   │   └── messaging/
│   ├── api/
│   │   ├── controllers/
│   │   ├── routes/
│   │   ├── middleware/
│   │   ├── main.py ✅
│   │   └── logging.yaml ✅
│   └── config.py ✅
├── tests/
├── docs/
│   └── dependency-mapping.md ✅
├── README.md ✅
└── __init__.py ✅
```

**Configurações Implementadas**:
- `src/config.py`: Mapeamento de variáveis do `/backend/.env`
- `src/api/main.py`: FastAPI base com health checks
- `README.md`: Documentação clara sobre reutilização de infra
- `.projectrules`: Nova regra sobre microserviços e configs

### MT-5: Implementar Domain Layer
**Status**: ⏳ PENDENTE  
**Estimativa**: 8h

- [ ] Client entity com business rules
- [ ] Value objects para tipo safety
- [ ] Domain events
- [ ] Repository interface
- [ ] Domain services

### MT-6: Implementar Application Layer
**Status**: ⏳ PENDENTE  
**Estimativa**: 6h

- [ ] Use cases com lógica de negócio
- [ ] DTOs para comunicação
- [ ] Application services
- [ ] Command/Query handlers

### MT-7: Implementar Infrastructure Layer
**Status**: ⏳ PENDENTE  
**Estimativa**: 8h

- [ ] MongoDB repository implementation
- [ ] Perplexity service adapter
- [ ] Event publisher (RabbitMQ ready)
- [ ] Configuration management

### MT-8: Criar API REST Completa
**Status**: ⏳ PENDENTE  
**Estimativa**: 4h

Endpoints:
- `POST /api/v1/clients` - criar cliente
- `GET /api/v1/clients` - listar clientes
- `GET /api/v1/clients/{id}` - obter cliente
- `PUT /api/v1/clients/{id}` - atualizar cliente
- `DELETE /api/v1/clients/{id}` - deletar cliente
- `POST /api/v1/clients/{id}/analyze` - iniciar análise

### MT-9: Implementar Testes
**Status**: ⏳ PENDENTE  
**Estimativa**: 6h

- [ ] Unit tests (domain layer) - 95% coverage
- [ ] Integration tests (infrastructure)
- [ ] API tests (end-to-end)
- [ ] Contract tests
- [ ] Performance tests

### MT-10: Documentar APIs (OpenAPI)
**Status**: ⏳ PENDENTE  
**Estimativa**: 2h

- [ ] OpenAPI 3.0 specification
- [ ] Swagger UI setup
- [ ] API versioning docs
- [ ] Migration guide

### MT-11: Self-score & Log
**Status**: ⏳ PENDENTE  
**Estimativa**: 1h

Critérios de avaliação:
- Code quality (SOLID, DRY, Clean)
- Test coverage
- Documentation completeness
- Performance metrics
- Zero breaking changes

## Estratégia de Migração

### Fase 1: Parallel Run (Current)
1. Novo microserviço rodando em paralelo
2. Proxy no API Gateway para rotear requisições
3. Feature flag para ativar/desativar por cliente
4. Monitoramento de paridade

### Fase 2: Gradual Migration
1. Novos clientes usam microserviço
2. Migração batch de clientes existentes
3. Sincronização de dados temporária
4. Rollback capability

### Fase 3: Cleanup
1. Remover código do monolito
2. Desativar sync temporário
3. Otimizar performance
4. Documentar lições aprendidas

## Riscos e Mitigações

### Risco 1: Data Consistency
**Mitigação**: Event sourcing + saga pattern para garantir consistência eventual

### Risco 2: Performance Degradation
**Mitigação**: Cache layer + otimização de queries + monitoring

### Risco 3: Integration Complexity
**Mitigação**: Contract testing + versioning + backward compatibility

## Dependências

- Docker ambiente configurado
- MongoDB connection string
- RabbitMQ (pode ser mockado inicialmente)
- OpenAPI tools

## Definition of Done

- [ ] Código implementado seguindo DDD/Clean Architecture
- [ ] Testes com coverage ≥ 85%
- [ ] API documentada com OpenAPI
- [ ] Zero breaking changes confirmado
- [ ] Performance benchmark ≤ 10% degradação
- [ ] Code review aprovado
- [ ] Deployed em ambiente dev

## Notas de Implementação

1. Usar `pydantic` v2 para validação
2. Implementar correlation IDs para tracing
3. Health checks e readiness probes
4. Graceful shutdown handling
5. Structured logging (JSON)

## Referências

- [DDD in Python](https://github.com/pgorecki/python-ddd)
- [Clean Architecture Python](https://github.com/Enforcer/clean-architecture)
- [Strangler Fig Pattern](https://martinfowler.com/bliki/StranglerFigApplication.html)
- [Event Sourcing](https://microservices.io/patterns/data/event-sourcing.html) 