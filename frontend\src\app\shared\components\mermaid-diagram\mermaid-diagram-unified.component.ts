import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { AfterViewInit, Component, inject, Input, OnChanges, OnDestroy, OnInit, signal } from '@angular/core';
import { Dom<PERSON>anitizer, SafeHtml } from '@angular/platform-browser';
import { Subject, takeUntil } from 'rxjs';
import { LocalMermaidStrategy } from './strategies/local-mermaid.strategy';
import { MermaidRenderResult, MermaidRenderStrategy } from './strategies/mermaid-render.strategy';
import { RemoteMermaidStrategy } from './strategies/remote-mermaid.strategy';

export type MermaidRenderMode = 'local' | 'remote' | 'auto';

@Component({
	selector: 'app-mermaid-diagram',
	standalone: true,
	imports: [CommonModule],
	template: `
		<div class="mermaid-container">
			<div class="diagram-header">
				<h6>📊 Diagrama de Arquitetura</h6>
				<div class="header-actions">
					<!-- Selector de estratégia -->
					<select
						class="strategy-selector"
						[value]="renderMode"
						(change)="onRenderModeChange($event)"
						title="Modo de renderização"
					>
						<option value="auto">Auto</option>
						<option value="local">Local</option>
						<option value="remote">Remoto</option>
					</select>

					<button type="button" class="action-btn" (click)="toggleView()" title="Alternar visualização">
						<i class="pi" [ngClass]="showRendered() ? 'pi-code' : 'pi-eye'"></i>
						{{ showRendered() ? 'Código' : 'Diagrama' }}
					</button>

					<button type="button" class="action-btn" (click)="copyDiagramCode()" title="Copiar código Mermaid">
						<i class="pi pi-copy"></i>
						Copiar
					</button>

					@if (currentStrategy?.supportsDownload && svgContent()) {
						<button type="button" class="action-btn" (click)="downloadSvg()" title="Download SVG">
							<i class="pi pi-download"></i>
							Download
						</button>
					}

					<button type="button" class="action-btn" (click)="refreshDiagram()" title="Atualizar diagrama">
						<i class="pi pi-refresh"></i>
						Atualizar
					</button>
				</div>
			</div>

			<!-- Diagrama Renderizado -->
			<div class="diagram-content" [ngClass]="{ 'show-rendered': showRendered(), 'show-code': !showRendered() }">
				<div class="rendered-diagram">
					@if (loading()) {
						<div class="loading-state">
							<i class="pi pi-spin pi-spinner"></i>
							<p>Renderizando diagrama...</p>
							<small>{{ currentStrategy?.name }}</small>
						</div>
					} @else if (errorMessage()) {
						<div class="error-state">
							<i class="pi pi-exclamation-triangle"></i>
							<p>{{ errorMessage() }}</p>
							<small>Mostrando código original como fallback</small>
							<button class="retry-btn" (click)="refreshDiagram()">
								<i class="pi pi-refresh"></i>
								Tentar Novamente
							</button>
							@if (renderMode === 'auto' && currentStrategy && currentStrategy.name.includes('Remote')) {
								<button class="retry-btn local-fallback" (click)="fallbackToLocal()">
									<i class="pi pi-desktop"></i>
									Tentar Local
								</button>
							}
						</div>
					} @else if (svgContent()) {
						<div class="svg-container" [innerHTML]="svgContent()"></div>
					} @else {
						<div class="empty-state">
							<i class="pi pi-image"></i>
							<p>Nenhum diagrama para exibir</p>
						</div>
					}
				</div>

				<!-- Fallback: Mostrar código original -->
				<div class="diagram-fallback">
					<div class="diagram-notice">
						<i class="pi pi-info-circle"></i>
						<span>Código Mermaid - Copie para usar em outros editores</span>
						<a href="https://mermaid.live/" target="_blank" class="mermaid-link"> Abrir no Mermaid Live </a>
					</div>

					<pre class="diagram-code"><code>{{ cleanDiagramCode() }}</code></pre>
				</div>
			</div>
		</div>
	`,
	styles: [
		`
			.mermaid-container {
				width: 100%;
				background: #f8f9fa;
				border: 1px solid #e0e0e0;
				border-radius: 8px;
				overflow: hidden;
			}

			.diagram-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 0.75rem 1rem;
				background: linear-gradient(135deg, #3b82f6, #6366f1);
				color: white;
			}

			.diagram-header h6 {
				margin: 0;
				font-size: 0.9rem;
				font-weight: 600;
			}

			.header-actions {
				display: flex;
				gap: 0.5rem;
				align-items: center;
			}

			.strategy-selector {
				background: rgba(255, 255, 255, 0.2);
				border: 1px solid rgba(255, 255, 255, 0.3);
				color: white;
				padding: 0.25rem 0.5rem;
				border-radius: 4px;
				font-size: 0.8rem;
				cursor: pointer;
			}

			.strategy-selector option {
				background: #3b82f6;
				color: white;
			}

			.action-btn {
				background: rgba(255, 255, 255, 0.2);
				border: 1px solid rgba(255, 255, 255, 0.3);
				color: white;
				padding: 0.25rem 0.5rem;
				border-radius: 4px;
				cursor: pointer;
				font-size: 0.8rem;
				transition: all 0.2s;
				display: flex;
				align-items: center;
				gap: 0.25rem;
			}

			.action-btn:hover:not(:disabled) {
				background: rgba(255, 255, 255, 0.3);
			}

			.action-btn:disabled {
				opacity: 0.5;
				cursor: not-allowed;
			}

			.diagram-content {
				position: relative;
			}

			.rendered-diagram {
				padding: 1rem;
				background: white;
				min-height: 200px;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			.svg-container {
				width: 100%;
				height: 100%;
				display: flex;
				justify-content: center;
				align-items: center;
			}

			.svg-container svg {
				max-width: 100%;
				height: auto;
			}

			.loading-state,
			.error-state,
			.empty-state {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				padding: 2rem;
				text-align: center;
			}

			.loading-state i {
				font-size: 2rem;
				margin-bottom: 1rem;
				color: #3b82f6;
			}

			.error-state i {
				font-size: 2rem;
				margin-bottom: 1rem;
				color: #dc3545;
			}

			.empty-state i {
				font-size: 2rem;
				margin-bottom: 1rem;
				color: #6c757d;
			}

			.retry-btn {
				margin-top: 0.5rem;
				padding: 0.5rem 1rem;
				background: #dc3545;
				color: white;
				border: none;
				border-radius: 4px;
				cursor: pointer;
				display: flex;
				align-items: center;
				gap: 0.5rem;
				font-size: 0.8rem;
			}

			.retry-btn:hover {
				background: #c82333;
			}

			.local-fallback {
				background: #28a745;
				margin-left: 0.5rem;
			}

			.local-fallback:hover {
				background: #218838;
			}

			.show-rendered .rendered-diagram {
				display: flex;
			}

			.show-rendered .diagram-fallback {
				display: none;
			}

			.show-code .rendered-diagram {
				display: none;
			}

			.show-code .diagram-fallback {
				display: block;
			}

			.diagram-fallback {
				padding: 1rem;
			}

			.diagram-notice {
				display: flex;
				align-items: center;
				gap: 0.5rem;
				margin-bottom: 1rem;
				padding: 0.75rem;
				background: #e0f2fe;
				border: 1px solid #b3e5fc;
				border-radius: 6px;
				font-size: 0.85rem;
				color: #0277bd;
			}

			.mermaid-link {
				margin-left: auto;
				color: #1565c0;
				text-decoration: none;
				font-weight: 500;
			}

			.mermaid-link:hover {
				text-decoration: underline;
			}

			.diagram-code {
				background: #2d3748;
				color: #e2e8f0;
				padding: 1rem;
				border-radius: 6px;
				font-family: 'Courier New', monospace;
				font-size: 0.85rem;
				line-height: 1.4;
				overflow-x: auto;
				white-space: pre-wrap;
				word-break: break-word;
				margin: 0;
			}

			.diagram-code code {
				background: none;
				color: inherit;
				padding: 0;
			}

			/* Responsividade */
			@media (max-width: 768px) {
				.diagram-header {
					padding: 0.5rem;
					flex-direction: column;
					gap: 0.5rem;
				}

				.header-actions {
					flex-wrap: wrap;
				}

				.diagram-header h6 {
					font-size: 0.8rem;
				}

				.action-btn,
				.strategy-selector {
					padding: 0.2rem 0.4rem;
					font-size: 0.7rem;
				}
			}
		`,
	],
	providers: [LocalMermaidStrategy, RemoteMermaidStrategy],
})
export class MermaidDiagramUnifiedComponent implements OnInit, AfterViewInit, OnChanges, OnDestroy {
	@Input() diagram: string = '';
	@Input() renderMode: MermaidRenderMode = 'auto';
	@Input() theme: 'default' | 'dark' | 'forest' | 'neutral' = 'default';

	// Signals para estado reativo
	public cleanDiagramCode = signal<string>('');
	public showRendered = signal<boolean>(true);
	public loading = signal<boolean>(false);
	public errorMessage = signal<string>('');
	public svgContent = signal<SafeHtml>('');

	// Services
	private http = inject(HttpClient);
	private sanitizer = inject(DomSanitizer);
	private localStrategy = inject(LocalMermaidStrategy);
	private remoteStrategy = inject(RemoteMermaidStrategy);

	// Estado interno
	private destroy$ = new Subject<void>();
	public currentStrategy: MermaidRenderStrategy | null = null;

	ngOnInit(): void {
		this.processDiagram();
		this.selectStrategy();
	}

	ngAfterViewInit(): void {
		if (this.showRendered() && this.cleanDiagramCode()) {
			this.renderDiagram();
		}
	}

	ngOnChanges(): void {
		this.processDiagram();
		this.selectStrategy();
		if (this.showRendered()) {
			this.renderDiagram();
		}
	}

	ngOnDestroy(): void {
		this.destroy$.next();
		this.destroy$.complete();
	}

	public toggleView(): void {
		this.showRendered.set(!this.showRendered());
		if (this.showRendered() && this.cleanDiagramCode() && !this.svgContent()) {
			this.renderDiagram();
		}
	}

	public refreshDiagram(): void {
		if (this.cleanDiagramCode()) {
			this.renderDiagram();
		}
	}

	public onRenderModeChange(event: Event): void {
		const target = event.target as HTMLSelectElement;
		this.renderMode = target.value as MermaidRenderMode;
		this.selectStrategy();
		this.renderDiagram();
	}

	public fallbackToLocal(): void {
		this.currentStrategy = this.localStrategy;
		this.renderDiagram();
	}

	public downloadSvg(): void {
		if (this.svgContent() && this.currentStrategy?.supportsDownload) {
			const svgBlob = new Blob([this.svgContent() as string], { type: 'image/svg+xml' });
			const url = URL.createObjectURL(svgBlob);
			const link = document.createElement('a');
			link.download = 'mermaid-diagram.svg';
			link.href = url;
			link.click();
			URL.revokeObjectURL(url);
		}
	}

	public async copyDiagramCode(): Promise<void> {
		try {
			await navigator.clipboard.writeText(this.cleanDiagramCode());
			// Visual feedback poderia ser implementado aqui
			console.log('✅ Código copiado para clipboard');
		} catch (error) {
			console.error('Erro ao copiar código:', error);
			// Fallback: mostrar o código em um prompt
			prompt('Copie o código Mermaid abaixo:', this.cleanDiagramCode());
		}
	}

	private selectStrategy(): void {
		switch (this.renderMode) {
			case 'local':
				this.currentStrategy = this.localStrategy;
				break;
			case 'remote':
				this.currentStrategy = this.remoteStrategy;
				break;
			case 'auto':
			default:
				// Auto: tentar remoto primeiro, fallback para local
				this.currentStrategy = this.remoteStrategy;
				break;
		}
	}

	private processDiagram(): void {
		if (!this.diagram) {
			this.cleanDiagramCode.set('Nenhum diagrama disponível');
			return;
		}

		// Extrair apenas o código Mermaid (remover prefixo "mermaid" se existir)
		let cleanCode = this.diagram.replace(/^mermaid\s*\n?/, '').trim();

		// Aplicar limpeza se a estratégia atual requerer
		if (this.currentStrategy?.requiresCodeCleaning && 'cleanCode' in this.currentStrategy) {
			cleanCode = (this.currentStrategy as any).cleanCode(cleanCode);
		}

		this.cleanDiagramCode.set(cleanCode || 'Diagrama vazio');
	}

	private renderDiagram(): void {
		if (!this.cleanDiagramCode() || this.cleanDiagramCode() === 'Nenhum diagrama disponível') {
			return;
		}

		if (!this.currentStrategy) {
			this.selectStrategy();
		}

		this.loading.set(true);
		this.errorMessage.set('');

		this.currentStrategy!.render(this.cleanDiagramCode())
			.pipe(takeUntil(this.destroy$))
			.subscribe({
				next: (result: MermaidRenderResult) => {
					this.loading.set(false);

					if (result.success && result.content) {
						const safeSvg = this.sanitizer.bypassSecurityTrustHtml(result.content);
						this.svgContent.set(safeSvg);
						this.errorMessage.set('');
					} else {
						this.errorMessage.set(result.error || 'Erro desconhecido na renderização');

						// Auto fallback para local se remoto falhou
						if (this.renderMode === 'auto' && this.currentStrategy === this.remoteStrategy) {
							console.log('🔄 Remote failed, falling back to local strategy');
							this.fallbackToLocal();
						}
					}
				},
				error: error => {
					this.loading.set(false);
					this.errorMessage.set('Erro inesperado: ' + error.message);

					// Auto fallback para local se remoto falhou
					if (this.renderMode === 'auto' && this.currentStrategy === this.remoteStrategy) {
						console.log('🔄 Remote error, falling back to local strategy');
						this.fallbackToLocal();
					}
				},
			});
	}
}
