# 📊 Report Viewer Component

## 🎯 Visão Geral

O `ReportViewerComponent` é um componente profissional para visualização de relatórios executivos em formato Markdown, substituindo a abordagem anterior de apenas download de PDFs.

## ✨ Funcionalidades

### 🔍 **Visualização Inline**
- Modal responsivo e profissional
- Renderização de Markdown em tempo real
- Layout organizado com header, ações e conteúdo

### 📱 **Interface Responsiva**
- Design adaptável para desktop e mobile
- Modal redimensionável
- Scroll interno para relatórios longos

### 🛠️ **Ações Disponíveis**
- **Visualizar**: Renderização inline do Markdown
- **Baixar PDF**: Download do relatório em PDF
- **Imprimir**: Impressão otimizada
- **Copiar**: Cópia do conteúdo para área de transferência

### 🔄 **Estados de Loading**
- Loading spinner durante carregamento
- Estados de erro com retry
- Feedback visual para todas as operações

## 🚀 Como Usar

### 1. **Importação**
```typescript
import { ReportViewerComponent, ReportData } from './report-viewer.component';
```

### 2. **Template**
```html
<app-report-viewer
  [visible]="reportViewerVisible"
  [reportData]="currentReportData"
  (onHide)="closeReportViewer()"
></app-report-viewer>
```

### 3. **Component**
```typescript
export class MyComponent {
  reportViewerVisible = signal(false);
  currentReportData = signal<ReportData | null>(null);

  showReport(reportData: ReportData) {
    this.currentReportData.set(reportData);
    this.reportViewerVisible.set(true);
  }

  closeReportViewer() {
    this.reportViewerVisible.set(false);
    this.currentReportData.set(null);
  }
}
```

## 📋 Interface ReportData

```typescript
interface ReportData {
  client_id: string;
  client_name: string;
  markdown_content: string;
  metadata: {
    generated_at: string;
    file_info: {
      filename: string;
      upload_date: string;
      content_type: string;
      length: number;
    };
    report_type: string;
  };
}
```

## 🎨 Customização

### **Estilos CSS**
O componente usa classes Tailwind CSS e PrimeNG. Principais classes:

- `.report-viewer-dialog`: Container principal
- `.report-content-wrapper`: Wrapper do conteúdo
- `.report-header`: Header com informações do cliente
- `.report-actions`: Barra de ações
- `.report-markdown-content`: Container do Markdown

### **Print Styles**
Estilos otimizados para impressão:
- Oculta botões de ação
- Remove limitações de altura
- Layout otimizado para papel

## 🔧 Integração com Backend

### **Endpoints Utilizados**
1. `GET /reports/clients/{client_id}/markdown` - Buscar relatório existente
2. `POST /reports/clients/{client_id}/generate-markdown` - Gerar novo relatório

### **Fluxo de Dados**
1. Tentar buscar relatório existente
2. Se não encontrar (404), gerar novo automaticamente
3. Exibir conteúdo renderizado
4. Tratar erros graciosamente

## 🐛 Tratamento de Erros

### **Cenários Cobertos**
- Relatório não encontrado
- Erro de rede
- Conteúdo vazio
- Falha na geração

### **Fallbacks**
- Retry automático para geração
- Mensagens de erro amigáveis
- Estados visuais claros

## 📱 Responsividade

### **Breakpoints**
- **Desktop**: Modal 95vw, max 1400px
- **Tablet**: Ajuste automático
- **Mobile**: Full width com scroll

### **Otimizações Mobile**
- Touch-friendly buttons
- Scroll otimizado
- Layout adaptável

## 🔮 Próximas Melhorias

1. **Export para Word/Excel**
2. **Compartilhamento via email**
3. **Comentários inline**
4. **Versionamento de relatórios**
5. **Templates customizáveis**
6. **Integração com IA para insights**

## 📊 Métricas de Performance

### **Tempos Esperados**
- Carregamento de relatório existente: < 2s
- Geração de novo relatório: 30-60s
- Renderização de Markdown: < 500ms

### **Otimizações**
- Lazy loading do conteúdo
- Cache de relatórios
- Compressão de dados

---

**Versão**: 1.0.0  
**Data**: 2025-06-13  
**Responsável**: Engenheiro Sênior IA
