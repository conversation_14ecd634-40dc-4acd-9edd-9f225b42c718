<div
	class="h-full bg-white border-r border-gray-200 transition-all duration-300 shadow-sm"
	[ngClass]="{ 'w-64': visible(), 'w-0 overflow-hidden': !visible() }"
>
	<div class="flex flex-col h-full">
		<!-- Sidebar Header -->
		<div class="p-4 border-b border-gray-200">
			<div class="text-lg font-medium text-gray-800">Menu</div>
		</div>

		<!-- Navigation Links -->
		<div class="flex-1 overflow-y-auto p-3">
			<ul class="space-y-1">
				@for (item of menuItems; track item['routerLink']) {
					<li>
						<a
							[routerLink]="item['routerLink']"
							routerLinkActive="bg-blue-50 text-blue-700"
							class="flex items-center px-4 py-3 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors"
							[attr.aria-disabled]="item.disabled ? true : null"
							[ngClass]="{ 'pointer-events-none opacity-50': item.disabled }"
						>
							<i [class]="'mr-3 text-lg ' + item.icon"></i>
							<span>{{ item.label }}</span>
						</a>
					</li>
				}
			</ul>
		</div>

		<!-- Sidebar Footer -->
		<div class="p-4 border-t border-gray-200">
			<div class="flex items-center justify-between">
				<span class="text-sm text-gray-500">Scope-AI v1.0</span>
			</div>
		</div>
	</div>
</div>
