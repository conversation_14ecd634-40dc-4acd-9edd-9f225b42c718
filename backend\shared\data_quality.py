from enum import Enum
from dataclasses import dataclass
from datetime import datetime
from typing import Dict, Any, Optional, List
import logging

logger = logging.getLogger(__name__)

class DataQuality(Enum):
    """Classificação da qualidade/origem dos dados"""
    REAL_DATA = "real_data"                    # Dados obtidos de APIs reais
    ENHANCED_FALLBACK = "enhanced_fallback"    # Fallback baseado em dados reais processados
    BASIC_FALLBACK = "basic_fallback"          # Fallback básico baseado em dados reais históricos
    ERROR_FALLBACK = "error_fallback"          # Fallback devido a erro de API (usando dados reais em cache)

@dataclass
class DataMetadata:
    """Metadados para rastreamento de qualidade dos dados"""
    quality: DataQuality
    source: str
    timestamp: datetime
    confidence_score: float  # 0.0 a 1.0
    fallback_reason: Optional[str] = None
    api_response_time: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "quality": self.quality.value,
            "source": self.source,
            "timestamp": self.timestamp.isoformat(),
            "confidence_score": self.confidence_score,
            "fallback_reason": self.fallback_reason,
            "api_response_time": self.api_response_time
        }

class DataQualityManager:
    """Gerenciador de qualidade de dados"""
    
    def __init__(self):
        self.quality_stats = {
            "total_requests": 0,
            "real_data_count": 0,
            "fallback_count": 0,
            "error_count": 0
        }
    
    def create_metadata(
        self,
        quality: DataQuality,
        source: str,
        confidence_score: float,
        fallback_reason: Optional[str] = None,
        api_response_time: Optional[float] = None
    ) -> DataMetadata:
        """Criar metadados para os dados"""
        metadata = DataMetadata(
            quality=quality,
            source=source,
            timestamp=datetime.utcnow(),
            confidence_score=confidence_score,
            fallback_reason=fallback_reason,
            api_response_time=api_response_time
        )
        
        self._update_stats(quality)
        self._log_data_usage(metadata)
        
        return metadata
    
    def _update_stats(self, quality: DataQuality):
        """Atualizar estatísticas de uso"""
        self.quality_stats["total_requests"] += 1
        
        if quality == DataQuality.REAL_DATA:
            self.quality_stats["real_data_count"] += 1
        elif quality in [DataQuality.ENHANCED_FALLBACK, DataQuality.BASIC_FALLBACK]:
            self.quality_stats["fallback_count"] += 1
        elif quality == DataQuality.ERROR_FALLBACK:
            self.quality_stats["error_count"] += 1
    
    def _log_data_usage(self, metadata: DataMetadata):
        """Log do uso de dados"""
        if metadata.quality in [DataQuality.BASIC_FALLBACK, DataQuality.ERROR_FALLBACK]:
            logger.warning(f"FALLBACK usado: {metadata.source} - {metadata.fallback_reason}")
        elif metadata.quality == DataQuality.ENHANCED_FALLBACK:
            logger.info(f"Enhanced fallback usado: {metadata.source}")
        else:
            logger.debug(f"Dados reais obtidos: {metadata.source}")
    
    def get_quality_stats(self) -> Dict[str, Any]:
        """Obter estatísticas de qualidade"""
        total = self.quality_stats["total_requests"]
        if total == 0:
            return self.quality_stats
        
        return {
            **self.quality_stats,
            "real_data_percentage": (self.quality_stats["real_data_count"] / total) * 100,
            "fallback_percentage": (self.quality_stats["fallback_count"] / total) * 100,
            "error_percentage": (self.quality_stats["error_count"] / total) * 100
        }
    
    def should_alert_admin(self) -> bool:
        """Verificar se deve alertar administrador sobre qualidade dos dados"""
        stats = self.get_quality_stats()
        total = stats["total_requests"]
        
        if total < 10:  # Aguardar um mínimo de requests
            return False
        
        # Alertar se mais de 30% são fallbacks 
        problematic_percentage = stats.get("fallback_percentage", 0) + stats.get("error_percentage", 0)
        return problematic_percentage > 30

# Instância global do gerenciador
quality_manager = DataQualityManager()

def wrap_with_quality(
    data: Dict[str, Any],
    quality: DataQuality,
    source: str,
    confidence_score: float,
    fallback_reason: Optional[str] = None,
    api_response_time: Optional[float] = None,
    user_id: Optional[str] = None,
    service: Optional[str] = None
) -> Dict[str, Any]:
    """Envolver dados com metadados de qualidade"""
    metadata = quality_manager.create_metadata(
        quality=quality,
        source=source,
        confidence_score=confidence_score,
        fallback_reason=fallback_reason,
        api_response_time=api_response_time
    )
    
    # Registrar no monitor de fallbacks se não for dados reais
    if quality != DataQuality.REAL_DATA:
        try:
            from .fallback_monitor import fallback_monitor
            fallback_monitor.log_fallback(
                source=source,
                reason=fallback_reason or "Fallback sem razão especificada",
                quality=quality,
                confidence_score=confidence_score,
                service=service or "unknown",
                user_id=user_id
            )
        except Exception as e:
            # Não falhar se o monitor tiver problemas
            logger.warning(f"Erro ao registrar fallback no monitor: {e}")
    
    return {
        "data": data,
        "metadata": metadata.to_dict(),
        "_quality_score": confidence_score,
        "_is_real_data": quality == DataQuality.REAL_DATA
    } 