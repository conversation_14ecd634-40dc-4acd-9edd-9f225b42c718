# Active Context - ScopeAI

**Versão**: 1.0.0
**Última Atualização**: 2025-01-29
**Sprint Atual**: Refatoração Arquitetural

## 🔄 Contexto Atual

### Branch Ativo
- **feat-microservices**: Iniciando migração para arquitetura de microserviços

### Último Estado Conhecido
- Sistema 100% funcional em arquitetura monolítica
- Performance otimizada com conversão assíncrona (266% speedup)
- Frontend Angular 19 com control flow moderno
- Backend FastAPI com Python 3.12
- MongoDB Atlas + GridFS operacional

## 📝 Decisões Recentes

### Arquiteturais (2025-01)
1. **Migração para Microserviços**: Aprovada estratégia Strangler Fig Pattern
2. **DDD + Hexagonal**: Implementação iniciada no módulo Clients
3. **API Gateway**: Router.py será transformado em gateway inteligente
4. **Event-Driven**: Adoção de eventos de domínio para comunicação

### Técnicas (2025-01)
1. **Memory Bank**: Sistema de documentação estruturada implementado
2. **Task Lifecycle**: Arquivamento obrigatório + política de retenção 6 meses
3. **Reasoning Techniques**: Adoção obrigatória de mcp_Sequential_Thinking
4. **Code Quality**: Sistema de scoring 0-30 pontos por tarefa

### Operacionais (2025-01)
1. **PDF Background Service**: Geração automática quando reports completos
2. **WebSocket Notifications**: Sistema unificado de eventos
3. **Knowledge Management**: Integração completa com embeddings OpenAI
4. **Error Handling**: Padrão híbrido sync/async para BackgroundTasks

## 🚧 Trabalho em Progresso

### Refatoração Backend (P1)
- [ ] Fase 1: Core domain + base classes ✅
- [ ] Fase 2: Módulo Clients com DDD
- [ ] Fase 3: Módulo Projects isolado
- [ ] Fase 4: API Gateway implementation
- [ ] Fase 5: Event bus + messaging

### Melhorias Imediatas (P2)
- [ ] Substituir Perplexity por SearchSociety
- [ ] Implementar RAG com ChromaDB
- [ ] Automated Validation com Playwright
- [ ] GraphRAG com Neo4j
- [ ] Observabilidade com Agno Monitor

## 🎯 Próximos Passos

### Sprint Atual (2 semanas)
1. **Completar módulo Clients DDD**
   - Infrastructure layer (MongoDB adapters)
   - Application services
   - API controllers
   - Testes de integração

2. **Documentar arquitetura**
   - ADRs (Architecture Decision Records)
   - Diagramas C4 model
   - Guia de migração

3. **Preparar módulo Projects**
   - Análise de dependências
   - Design de interfaces
   - Plano de migração

### Próximo Sprint
1. Extrair módulo Projects
2. Implementar API Gateway básico
3. Setup mensageria (RabbitMQ/Kafka)
4. Migrar primeiro fluxo end-to-end

## 🐛 Issues Conhecidas

### Críticas
- [ ] Campo "progresso" inicial incorreto (5-25% em vez de 0%)
- [ ] Imports de async_db.py quebrados em alguns lugares

### Melhorias
- [ ] Frontend precisa lazy loading para performance
- [ ] Cache Redis não está otimizado
- [ ] Logs muito verbosos em produção

## 📊 Métricas Sprint

- **Velocity**: 45 story points
- **Bug Rate**: 2.3 bugs/feature
- **Test Coverage**: 73%
- **Tech Debt**: 15% do backlog
- **Build Time**: 4.9s frontend, 1.2s backend

## 🔗 Links Úteis

- [Plano Refatoração](../backend/docs/refactoring-microservices-plan.md)
- [Architecture Decisions](../docs/architecture/decisions/)
- [API Documentation](http://localhost:8040/docs)
- [Monitoring Dashboard](http://localhost:3000/grafana) 