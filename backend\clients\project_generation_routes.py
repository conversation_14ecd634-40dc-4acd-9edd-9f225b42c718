"""
Rotas para Geração de Projetos com Sistema de Agentes

Este módulo contém endpoints especializados para gerar sugestões de projetos
usando um team de agentes especializados que analisam o dossiê completo do cliente.
"""

import asyncio
import logging
from datetime import datetime, UTC, timezone
from typing import Dict, Any, List, Optional
from uuid import uuid4

from fastapi import APIRouter, HTTPException, status, BackgroundTasks
from bson import ObjectId

# Imports do sistema existente
from .db import clients_collection, projetos_collection
from shared.websocket_manager import websocket_manager
from utils.mermaid_validator import validate_mermaid_diagram, fix_mermaid_diagram

# Sistema de agentes (será implementado)
from .project_agents_team import ProjectSuggestionTeam
# TODO: Corrigir importação quando estrutura de agentes estiver estável
# from tools.agents.orchestration.orchestrator import AgentOrchestrator
# from tools.agents.orchestration.schemas import OrchestrationConfig

router = APIRouter()
logger = logging.getLogger(__name__)


def _ensure_timezone(dt):
    """
    Garante que um datetime tenha timezone.
    Se não tiver timezone, assume UTC.
    """
    if dt is None:
        return None

    if isinstance(dt, datetime):
        if dt.tzinfo is None:
            # Se não tem timezone, assume UTC
            return dt.replace(tzinfo=UTC)
        else:
            # Se já tem timezone, retorna como está
            return dt

    return dt


def _calculate_duration_safely(start_dt, end_dt=None):
    """
    Calcula duração entre duas datas de forma segura,
    garantindo compatibilidade de timezone.
    """
    if start_dt is None:
        return "N/A"

    start_dt = _ensure_timezone(start_dt)

    if end_dt is None:
        end_dt = datetime.now(UTC)
    else:
        end_dt = _ensure_timezone(end_dt)

    try:
        duration = end_dt - start_dt
        return str(duration)
    except Exception as e:
        logger.warning(f"Erro ao calcular duração: {e}")
        return "N/A"


def _extrair_secao(texto: str, secao: str) -> str:
    """
    Extrai uma seção específica do resultado dos agentes.
    """
    try:
        texto = str(texto).upper()
        secao = secao.upper()

        # Procurar por padrões como "**ARQUITETURA**", "## ARQUITETURA", "1. ARQUITETURA", etc.
        import re
        patterns = [
            f"\\*\\*{secao}.*?\\*\\*",
            f"#+\\s*{secao}",
            f"\\d+\\.\\s*{secao}",
            secao
        ]

        for pattern in patterns:
            match = re.search(pattern, texto)
            if match:
                start = match.end()
                # Procurar o fim da seção (próximo header ou fim do texto)
                next_section = re.search(r'(\*\*|\#+|\d+\.)', texto[start:])
                if next_section:
                    end = start + next_section.start()
                    return texto[start:end].strip()
                else:
                    # Se não encontrar próxima seção, pegar uma quantidade limitada
                    return texto[start:start+500].strip()

        return f"Seção {secao} não encontrada de forma estruturada"

    except Exception as e:
        logger.warning(f"Erro ao extrair seção {secao}: {e}")
        return f"Erro ao extrair seção {secao}"


def _processar_estimativa_para_frontend(resultado_completo: str) -> dict:
    """
    🎯 Processar estimativa rica para interface do frontend

    Extrai e estrutura todos os elementos ricos da estimativa Team Agno:
    - Métricas principais
    - Diagramas Mermaid
    - PRD formatado
    - Timeline de sprints
    - Custos detalhados
    - Stack tecnológico
    """
    import re
    import json

    try:
        dados_processados = {
            "metricas_principais": {},
            "cronograma": {},
            "custos": {},
            "arquitetura": {},
            "tecnologias": [],
            "prd": "",
            "diagramas_mermaid": [],
            "equipe": {},
            "riscos": [],
            "features_principais": []
        }

        # 1. EXTRAIR ESCOPO_DO_PROJETO principal
        escopo_match = re.search(
            r'<ESCOPO_DO_PROJETO>(.*?)</ESCOPO_DO_PROJETO>', resultado_completo, re.DOTALL)
        if escopo_match:
            escopo_content = escopo_match.group(1)

            # Extrair métricas principais
            dados_processados["metricas_principais"] = _extrair_metricas_principais(
                escopo_content)

            # Extrair cronograma/sprints
            dados_processados["cronograma"] = _extrair_cronograma(
                escopo_content)

            # Extrair custos estimados
            dados_processados["custos"] = _extrair_custos(escopo_content)

            # Extrair PRD
            prd_match = re.search(r'```markdown(.*?)```',
                                  escopo_content, re.DOTALL)
            if prd_match:
                dados_processados["prd"] = prd_match.group(1).strip()

            # Extrair diagramas Mermaid com validação
            mermaid_matches = re.findall(
                r'```mermaid(.*?)```', escopo_content, re.DOTALL)
            for i, mermaid in enumerate(mermaid_matches):
                codigo_limpo = mermaid.strip()

                # Validar e corrigir diagrama
                validacao = validate_mermaid_diagram(codigo_limpo)
                if not validacao["is_valid"]:
                    logger.warning(
                        f"Diagrama {i+1} inválido: {validacao['errors']}")
                    # Tentar corrigir automaticamente
                    if validacao["diagram_type"]:
                        codigo_limpo = fix_mermaid_diagram(
                            codigo_limpo, validacao["diagram_type"])
                        logger.info(
                            f"Diagrama {i+1} corrigido automaticamente")

                dados_processados["diagramas_mermaid"].append({
                    "id": f"diagram_{i+1}",
                    "titulo": f"Diagrama {i+1}",
                    "codigo": codigo_limpo,
                    "validacao": validacao
                })

            # Extrair tecnologias
            dados_processados["tecnologias"] = _extrair_tecnologias(
                escopo_content)

            # Extrair equipe necessária
            dados_processados["equipe"] = _extrair_equipe(escopo_content)

            # Extrair features principais
            dados_processados["features_principais"] = _extrair_features(
                escopo_content)

            # Extrair riscos
            dados_processados["riscos"] = _extrair_riscos(escopo_content)

        return dados_processados

    except Exception as e:
        logger.error(f"Erro ao processar estimativa para frontend: {e}")
        return {
            "error": True,
            "message": "Erro ao processar dados da estimativa",
            "raw_content": resultado_completo[:1000] + "..." if len(resultado_completo) > 1000 else resultado_completo
        }


def _processar_estimativa_para_frontend_v2(dados_processamento: dict) -> dict:
    """
    🎯 Processar estimativa rica para interface do frontend (V2)

    Esta versão trabalha com o novo formato onde o projeto é salvo como objeto JSON.
    Mantém compatibilidade com o formato antigo baseado em string.

    Args:
        dados_processamento: Pode ser:
            - {"projeto": {...}, "estimate_resultado": {...}} (novo formato)
            - {"resultado_completo": "...", ...} (formato antigo)
    """
    try:
        # 🔧 CORREÇÃO: Verificar se temos o objeto projeto diretamente
        if "projeto" in dados_processamento and isinstance(dados_processamento["projeto"], dict):
            # Novo formato: trabalhar com o objeto JSON estruturado
            projeto_obj = dados_processamento["projeto"]
            logger.info(
                f"✅ Processando dados estruturados - projeto: {projeto_obj.get('nome', 'N/A')}")

            dados_processados = {
                "metricas_principais": {
                    "total_sprints": int(projeto_obj.get("total_de_sprints", 0)),
                    "duracao_estimada": projeto_obj.get("prazo_de_entrega", ""),
                    "custo_total": "",  # Será calculado a partir das stacks
                    "complexidade": "",
                    "equipe_minima": 0,  # Será calculado a partir das stacks
                    "horas_capacity": float(projeto_obj.get("total_de_horas_de_capacity", 0))
                },
                "cronograma": {
                    "sprints": [],
                    "fases": [],
                    "milestones": [],
                    "sprints_por_mes": int(projeto_obj.get("sprint_por_mes", 0))
                },
                "custos": {
                    "total": "",
                    "por_papel": [],
                    "breakdown": {}
                },
                "arquitetura": {
                    "descricao": projeto_obj.get("descricao", ""),
                    "requisitos": projeto_obj.get("requisitos", "")
                },
                "tecnologias": projeto_obj.get("tecnologias", []),
                "prd": projeto_obj.get("prd", ""),
                "diagramas_mermaid": [],
                "equipe": {
                    "total_pessoas": 0,
                    "papeis": [],
                    "senioridades": {}
                },
                "riscos": [],
                "features_principais": [],
                "stacks": projeto_obj.get("stacks", [])
            }

            # Processar sprints
            total_sprints = int(projeto_obj.get("total_de_sprints", 0))
            for i in range(1, total_sprints + 1):
                dados_processados["cronograma"]["sprints"].append({
                    "numero": i,
                    "descricao": f"Sprint {i} - Desenvolvimento iterativo"
                })

            # Processar diagrama principal com validação
            if projeto_obj.get("diagrama_sistema"):
                codigo_limpo = projeto_obj["diagrama_sistema"].replace(
                    "```mermaid", "").replace("```", "").strip()

                # Validar e corrigir diagrama
                validacao = validate_mermaid_diagram(codigo_limpo, "flowchart")
                if not validacao["is_valid"]:
                    logger.warning(
                        f"Diagrama sistema inválido: {validacao['errors']}")
                    codigo_limpo = fix_mermaid_diagram(
                        codigo_limpo, "flowchart")
                    logger.info("Diagrama sistema corrigido automaticamente")

                dados_processados["diagramas_mermaid"].append({
                    "id": "diagram_sistema",
                    "titulo": "Arquitetura Geral do Sistema",
                    "codigo": codigo_limpo,
                    "validacao": validacao
                })

            # Processar diagrama de dados (ERD) com validação
            if projeto_obj.get("diagrama_dados"):
                codigo_limpo = projeto_obj["diagrama_dados"].replace(
                    "```mermaid", "").replace("```", "").strip()

                # Validar e corrigir diagrama ERD
                validacao = validate_mermaid_diagram(codigo_limpo, "erDiagram")
                if not validacao["is_valid"]:
                    logger.warning(
                        f"Diagrama ERD inválido: {validacao['errors']}")
                    codigo_limpo = fix_mermaid_diagram(
                        codigo_limpo, "erDiagram")
                    logger.info("Diagrama ERD corrigido automaticamente")

                dados_processados["diagramas_mermaid"].append({
                    "id": "diagram_erd",
                    "titulo": "Diagrama de Entidades e Relacionamentos",
                    "codigo": codigo_limpo,
                    "validacao": validacao
                })

            # Processar stacks e calcular equipe/custos
            total_devs = 0
            for stack in projeto_obj.get("stacks", []):
                # Adicionar diagrama de sequência da stack com validação
                if stack.get("diagrama_sequencia"):
                    codigo_limpo = stack["diagrama_sequencia"].replace(
                        "```mermaid", "").replace("```", "").strip()

                    # Validar e corrigir diagrama de sequência
                    validacao = validate_mermaid_diagram(
                        codigo_limpo, "sequenceDiagram")
                    if not validacao["is_valid"]:
                        logger.warning(
                            f"Diagrama sequência da stack {stack.get('stack')} inválido: {validacao['errors']}")
                        codigo_limpo = fix_mermaid_diagram(
                            codigo_limpo, "sequenceDiagram")
                        logger.info(
                            f"Diagrama sequência da stack {stack.get('stack')} corrigido automaticamente")

                    dados_processados["diagramas_mermaid"].append({
                        "id": f"diagram_seq_{stack.get('stack', '').lower().replace(' ', '_')}",
                        "titulo": f"Sequência - {stack.get('stack', '')}",
                        "codigo": codigo_limpo,
                        "validacao": validacao
                    })

                # Processar desenvolvedores da stack
                for dev_group in stack.get("desenvolvedores", []):
                    # 🔧 CORREÇÃO: Tratar quantidade como string ou int
                    try:
                        qtd_raw = dev_group.get("quantidade", 0)
                        if isinstance(qtd_raw, str):
                            # Extrair número da string se necessário
                            import re
                            numbers = re.findall(r'\d+', qtd_raw)
                            qtd = int(numbers[0]) if numbers else 0
                        else:
                            qtd = int(qtd_raw)
                    except (ValueError, TypeError):
                        qtd = 0
                        logger.warning(
                            f"Erro ao processar quantidade de desenvolvedores: {dev_group.get('quantidade')}")

                    total_devs += qtd

                    # Adicionar à equipe
                    papel = f"{stack.get('stack', '')} Developer"
                    dados_processados["equipe"]["papeis"].append({
                        "papel": papel,
                        "quantidade": qtd,
                        "senioridade": dev_group.get("senioridade", []),
                        "modalidade": dev_group.get("modalidade", ""),
                        "duracao": dev_group.get("duracao", "")
                    })

            dados_processados["equipe"]["total_pessoas"] = total_devs
            dados_processados["metricas_principais"]["equipe_minima"] = total_devs

            # Adicionar informações do projeto
            dados_processados["projeto_info"] = {
                "nome": projeto_obj.get("nome", ""),
                "descricao": projeto_obj.get("descricao", ""),
                "requisitos": projeto_obj.get("requisitos", "")
            }

            return dados_processados

        else:
            # Formato antigo: usar a função original
            resultado_completo = dados_processamento.get(
                "resultado_completo", "")
            logger.info("⚠️ Usando processamento antigo baseado em markdown")
            return _processar_estimativa_para_frontend(resultado_completo)

    except Exception as e:
        logger.error(f"Erro ao processar estimativa V2 para frontend: {e}")
        # Fallback para versão antiga
        resultado_completo = dados_processamento.get("resultado_completo", "")
        if resultado_completo:
            logger.info("🔄 Fallback para processamento antigo")
            return _processar_estimativa_para_frontend(resultado_completo)
        else:
            logger.error("❌ Nenhum dado disponível para processamento")
            return {
                "error": True,
                "message": "Erro ao processar dados da estimativa",
                "details": str(e)
            }


def _extrair_metricas_principais(conteudo: str) -> dict:
    """Extrair métricas principais do projeto"""
    import re

    metricas = {
        "total_sprints": 0,
        "duracao_estimada": "",
        "custo_total": "",
        "complexidade": "",
        "equipe_minima": 0
    }

    try:
        # Total de Sprints
        sprint_match = re.search(
            r'total[_\s]*sprints?[:\s]*(\d+)', conteudo, re.IGNORECASE)
        if sprint_match:
            metricas["total_sprints"] = int(sprint_match.group(1))

        # Duração
        duracao_patterns = [
            r'duração[:\s]*([0-9\-\s\w]+)',
            r'prazo[:\s]*([0-9\-\s\w]+)',
            r'tempo[:\s]*([0-9\-\s\w]+)'
        ]
        for pattern in duracao_patterns:
            match = re.search(pattern, conteudo, re.IGNORECASE)
            if match:
                metricas["duracao_estimada"] = match.group(1).strip()
                break

        # Custo Total
        custo_patterns = [
            r'R\$[\s]*([0-9.,]+)',
            r'total[:\s]*R\$[\s]*([0-9.,]+)',
            r'custo[:\s]*R\$[\s]*([0-9.,]+)'
        ]
        for pattern in custo_patterns:
            match = re.search(pattern, conteudo, re.IGNORECASE)
            if match:
                metricas["custo_total"] = f"R$ {match.group(1)}"
                break

    except Exception as e:
        logger.warning(f"Erro ao extrair métricas: {e}")

    return metricas


def _extrair_cronograma(conteudo: str) -> dict:
    """Extrair informações de cronograma e sprints"""
    import re

    cronograma = {
        "sprints": [],
        "fases": [],
        "milestones": []
    }

    try:
        # Procurar por número total de sprints
        sprint_total_patterns = [
            r'(\d+)\s*sprints?[\s,]',
            r'Sprint\s*(\d+)\s*-\s*Sprint\s*(\d+)',
            r'Total[:\s]*(\d+)\s*sprints?'
        ]

        max_sprints = 0
        for pattern in sprint_total_patterns:
            matches = re.findall(pattern, conteudo, re.IGNORECASE)
            for match in matches:
                if isinstance(match, tuple):
                    try:
                        max_sprints = max(max_sprints, max(
                            [int(x) for x in match if x.isdigit()]))
                    except:
                        pass
                else:
                    try:
                        max_sprints = max(max_sprints, int(match))
                    except:
                        pass

        # Se encontrou total de sprints, gerar sprints básicas
        if max_sprints > 0:
            for i in range(1, max_sprints + 1):
                cronograma["sprints"].append({
                    "numero": i,
                    "descricao": f"Sprint {i} - Desenvolvimento iterativo"
                })

        # Procurar por sprints específicas mencionadas
        sprint_specific_patterns = [
            r'Sprint\s*(\d+)[:\-\s]*([^\n\r]+?)(?=Sprint|\n|\r|$)',
            r'sprint\s*(\d+)[:\s]*([^\n]+)',
            r'(\d+)[ºª]?\s*sprint[:\s]*([^\n]+)'
        ]

        sprints_especificas = {}
        for pattern in sprint_specific_patterns:
            matches = re.findall(
                pattern, conteudo, re.IGNORECASE | re.MULTILINE)
            for match in matches:
                try:
                    numero = int(match[0])
                    descricao = match[1].strip()
                    # Filtrar descrições muito curtas
                    if descricao and len(descricao) > 5:
                        sprints_especificas[numero] = descricao
                except ValueError:
                    pass

        # Atualizar sprints com descrições específicas
        for sprint in cronograma["sprints"]:
            if sprint["numero"] in sprints_especificas:
                sprint["descricao"] = sprints_especificas[sprint["numero"]]

        # Se não encontrou sprints via total, buscar sprints individuais
        if not cronograma["sprints"] and sprints_especificas:
            for numero, descricao in sorted(sprints_especificas.items()):
                cronograma["sprints"].append({
                    "numero": numero,
                    "descricao": descricao
                })

        # Procurar por fases do projeto
        fases_conhecidas = [
            "Planejamento", "Análise", "Design", "Desenvolvimento",
            "Testes", "Deploy", "Produção", "Manutenção", "Arquitetura",
            "Prototipagem", "Integração", "Validação"
        ]

        fases_encontradas = set()
        for fase in fases_conhecidas:
            if re.search(rf'\b{fase}\b', conteudo, re.IGNORECASE):
                fases_encontradas.add(fase)

        cronograma["fases"] = sorted(list(fases_encontradas))

        # Procurar por milestones
        milestone_patterns = [
            r'milestone[:\s]*([^\n]+)',
            r'entrega[:\s]*([^\n]+)',
            r'marco[:\s]*([^\n]+)',
            r'(MVP|Alpha|Beta|Release|Launch)'
        ]

        milestones = set()
        for pattern in milestone_patterns:
            matches = re.findall(pattern, conteudo, re.IGNORECASE)
            for match in matches:
                if isinstance(match, str) and match.strip():
                    milestones.add(match.strip())

        cronograma["milestones"] = list(milestones)

    except Exception as e:
        logger.warning(f"Erro ao extrair cronograma: {e}")

    return cronograma


def _extrair_custos(conteudo: str) -> dict:
    """Extrair breakdown de custos"""
    import re

    custos = {
        "total": "",
        "por_papel": [],
        "breakdown": {}
    }

    try:
        # Procurar valores monetários
        valores = re.findall(r'R\$[\s]*([0-9.,]+)', conteudo)
        if valores:
            # Último valor (geralmente total)
            custos["total"] = f"R$ {valores[-1]}"

        # Procurar custos por papel/função
        papel_patterns = [
            r'(desenvolvedor|tech\s*lead|designer|analista|arquiteto|devops)[:\s]*R\$[\s]*([0-9.,]+)',
            r'(senior|junior|pleno)[:\s]*R\$[\s]*([0-9.,]+)'
        ]

        for pattern in papel_patterns:
            matches = re.findall(pattern, conteudo, re.IGNORECASE)
            for match in matches:
                custos["por_papel"].append({
                    "papel": match[0].strip(),
                    "valor": f"R$ {match[1]}"
                })

    except Exception as e:
        logger.warning(f"Erro ao extrair custos: {e}")

    return custos


def _extrair_tecnologias(conteudo: str) -> list:
    """Extrair stack tecnológico"""
    import re

    tecnologias = []

    try:
        # Lista de tecnologias conhecidas
        techs_conhecidas = [
            'React', 'Angular', 'Vue', 'Next.js', 'Node.js', 'Python', 'Django',
            'Flask', 'FastAPI', 'PostgreSQL', 'MongoDB', 'MySQL', 'Redis',
            'Docker', 'Kubernetes', 'AWS', 'Azure', 'GCP', 'JavaScript',
            'TypeScript', 'Java', 'Spring', 'Express', 'React Native',
            'Flutter', 'GraphQL', 'REST', 'JWT', 'OAuth2', 'Nginx',
            'Jenkins', 'GitLab', 'GitHub', 'Terraform', 'Material-UI'
        ]

        techs_encontradas = set()

        # Buscar tecnologias conhecidas no texto
        for tech in techs_conhecidas:
            pattern = rf'\b{re.escape(tech)}\b'
            if re.search(pattern, conteudo, re.IGNORECASE):
                techs_encontradas.add(tech)

        # Buscar patterns específicos
        stack_patterns = [
            r'Frontend[:\s]*([^\n]+)',
            r'Backend[:\s]*([^\n]+)',
            r'Mobile[:\s]*([^\n]+)',
            r'Banco[:\s]*([^\n]+)',
            r'Database[:\s]*([^\n]+)',
            r'Infraestrutura[:\s]*([^\n]+)'
        ]

        for pattern in stack_patterns:
            matches = re.findall(pattern, conteudo, re.IGNORECASE)
            for match in matches:
                # Extrair tecnologias da linha encontrada
                for tech in techs_conhecidas:
                    if re.search(rf'\b{re.escape(tech)}\b', match, re.IGNORECASE):
                        techs_encontradas.add(tech)

        tecnologias = sorted(list(techs_encontradas))

    except Exception as e:
        logger.warning(f"Erro ao extrair tecnologias: {e}")

    return tecnologias


def _extrair_equipe(conteudo: str) -> dict:
    """Extrair composição da equipe"""
    import re

    equipe = {
        "total_pessoas": 0,
        "papeis": [],
        "senioridades": {}
    }

    try:
        # Procurar por papéis e pessoas
        papeis_patterns = [
            r'(\d+)[\s]*x?[\s]*(desenvolvedor|designer|analista|arquiteto|tech\s*lead|devops|qa)',
            r'(desenvolvedor|designer|analista|arquiteto|tech\s*lead|devops|qa)[:\s]*(\d+)'
        ]

        for pattern in papeis_patterns:
            matches = re.findall(pattern, conteudo, re.IGNORECASE)
            for match in matches:
                if match[0].isdigit():
                    qtd = int(match[0])
                    papel = match[1]
                else:
                    qtd = int(match[1])
                    papel = match[0]

                equipe["papeis"].append({
                    "papel": papel.strip(),
                    "quantidade": qtd
                })
                equipe["total_pessoas"] += qtd

    except Exception as e:
        logger.warning(f"Erro ao extrair equipe: {e}")

    return equipe


def _extrair_features(conteudo: str) -> list:
    """Extrair features principais do projeto"""
    import re

    features = []

    try:
        # Procurar por listas de features/funcionalidades
        feature_patterns = [
            r'[-*]\s*([^\n]+)',
            r'\d+\.\s*([^\n]+)'
        ]

        features_temp = []
        for pattern in feature_patterns:
            matches = re.findall(pattern, conteudo)
            features_temp.extend([match.strip()
                                 for match in matches if len(match.strip()) > 10])

        # Filtrar e limitar features mais relevantes
        features = features_temp[:8] if features_temp else []

    except Exception as e:
        logger.warning(f"Erro ao extrair features: {e}")

    return features


def _extrair_riscos(conteudo: str) -> list:
    """Extrair análise de riscos"""
    import re

    riscos = []

    try:
        # Procurar por seção de riscos
        risco_match = re.search(
            r'riscos?[:\s]*([^<]*)', conteudo, re.IGNORECASE | re.DOTALL)
        if risco_match:
            risco_text = risco_match.group(1)

            # Extrair itens de risco
            risco_items = re.findall(r'[-*]\s*([^\n]+)', risco_text)
            riscos = [item.strip()
                      for item in risco_items if len(item.strip()) > 5][:5]

    except Exception as e:
        logger.warning(f"Erro ao extrair riscos: {e}")

    return riscos


@router.post("/clients/{client_id}/generate-projects-advanced")
async def generate_projects_advanced(client_id: str, background_tasks: BackgroundTasks):
    """
    🚀 Geração Avançada de Projetos com Sistema de Agentes

    Utiliza um team especializado de agentes AI para analisar o dossiê completo
    do cliente e sugerir projetos personalizados e estratégicos.

    Agentes envolvidos:
    - Business Analyst Agent: Analisa negócio e oportunidades
    - Market Research Agent: Pesquisa tendências e mercado
    - Technology Scout Agent: Identifica tecnologias adequadas
    - Project Architect Agent: Estrutura projetos viáveis
    - ROI Calculator Agent: Calcula viabilidade financeira
    - Strategy Validator Agent: Valida estratégia geral
    """
    try:
        # 1. Validar se cliente existe
        client = clients_collection.find_one({"_id": ObjectId(client_id)})
        if not client:
            raise HTTPException(
                status_code=404,
                detail="Cliente não encontrado"
            )

        # 2. Validar se cliente tem dados suficientes
        validation_result = _validate_client_data(client)
        if not validation_result["valid"]:
            raise HTTPException(
                status_code=400,
                detail=f"Dados insuficientes: {validation_result['missing']}"
            )

        # 3. Verificar se já está processando
        if client.get("project_generation_status") == "generating":
            return {
                "message": "Geração de projetos já está em andamento",
                "client_id": client_id,
                "status": "already_generating",
                "estimated_completion": "5-8 minutos"
            }

        # 4. Gerar ID único para esta orquestração
        orchestration_id = str(uuid4())

        # 5. Atualizar status no banco
        clients_collection.update_one(
            {"_id": ObjectId(client_id)},
            {
                "$set": {
                    "project_generation_status": "generating",
                    "project_orchestration_id": orchestration_id,
                    "project_generation_started_at": datetime.now(UTC),
                    "updated_at": datetime.now(UTC)
                }
            }
        )

        # 6. Notificar início via WebSocket
        await websocket_manager.broadcast({
            "type": "projects_generation_started",
            "clientId": client_id,
            "clientName": client.get("name", ""),
            "orchestrationId": orchestration_id,
            "message": f"🤖 Iniciando análise avançada para {client.get('name', '')}...",
            "estimatedDuration": "5-8 minutos",
            "phase": "initialization",
            "timestamp": datetime.now(UTC).isoformat()
        })

        # 7. Executar geração em background
        background_tasks.add_task(
            execute_project_generation_orchestration,
            client_id,
            orchestration_id,
            client
        )

        return {
            "message": "Geração avançada de projetos iniciada com sucesso",
            "client_id": client_id,
            "orchestration_id": orchestration_id,
            "status": "generating",
            "estimated_duration": "5-8 minutos",
            "agents_involved": [
                "Business Analyst",
                "Market Research",
                "Technology Scout",
                "Project Architect",
                "ROI Calculator",
                "Strategy Validator"
            ]
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao iniciar geração avançada de projetos: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Erro interno do servidor"
        )


@router.get("/clients/{client_id}/project-generation-status")
async def get_project_generation_status(client_id: str):
    """
    📊 Consultar Status da Geração de Projetos

    Retorna o status detalhado da geração de projetos em andamento,
    incluindo fase atual, progresso e resultados parciais.
    """
    try:
        client = clients_collection.find_one({"_id": ObjectId(client_id)})
        if not client:
            raise HTTPException(
                status_code=404, detail="Cliente não encontrado")

        orchestration_id = client.get("project_orchestration_id")
        generation_status = client.get(
            "project_generation_status", "not_started")

        # Se tem orquestração ativa, buscar status detalhado
        detailed_status = None
        if orchestration_id and generation_status == "generating":
            # Aqui buscaríamos status do orquestrador
            # detailed_status = orchestrator.get_orchestration_status(orchestration_id)
            pass

        return {
            "client_id": client_id,
            "status": generation_status,
            "orchestration_id": orchestration_id,
            "started_at": client.get("project_generation_started_at"),
            "completed_at": client.get("project_generation_completed_at"),
            "projects_count": len(client.get("projects", [])),
            "detailed_status": detailed_status,
            "error": client.get("project_generation_error")
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao consultar status: {str(e)}")
        raise HTTPException(status_code=500, detail="Erro interno do servidor")


async def execute_project_generation_orchestration(
    client_id: str,
    orchestration_id: str,
    client_data: Dict[str, Any]
):
    """
    🎯 Executa a Orquestração Completa de Geração de Projetos

    Esta função coordena todo o processo de análise e geração de projetos,
    utilizando múltiplos agentes especializados em fases sequenciais.
    """
    try:
        logger.info(
            f"🚀 Iniciando orquestração {orchestration_id} para cliente {client_id}")

        # 1. Preparar dados do cliente para análise
        analysis_data = _prepare_client_analysis_data(client_data)

        # 2. Configurar orquestração específica para projetos
        config = _create_project_generation_config()

        # 3. Inicializar sistema de agentes (será implementado)
        project_team = ProjectSuggestionTeam()

        # 4. Executar análise em fases
        await _execute_project_analysis_phases(
            client_id,
            orchestration_id,
            analysis_data,
            project_team
        )

        logger.info(f"✅ Orquestração {orchestration_id} concluída com sucesso")

    except Exception as e:
        logger.error(f"❌ Erro na orquestração {orchestration_id}: {str(e)}")

        # Atualizar status de erro
        clients_collection.update_one(
            {"_id": ObjectId(client_id)},
            {
                "$set": {
                    "project_generation_status": "error",
                    "project_generation_error": str(e),
                    "project_generation_completed_at": datetime.now(UTC),
                    "updated_at": datetime.now(UTC)
                }
            }
        )

        # Notificar erro
        await websocket_manager.broadcast({
            "type": "projects_generation_error",
            "clientId": client_id,
            "orchestrationId": orchestration_id,
            "error": str(e),
            "message": f"❌ Erro na geração de projetos: {str(e)}",
            "timestamp": datetime.now(UTC).isoformat()
        })


def _validate_client_data(client: Dict[str, Any]) -> Dict[str, Any]:
    """
    ✅ Validar se cliente tem dados suficientes para gerar projetos
    """
    missing = []

    # Dados básicos obrigatórios
    if not client.get("name"):
        missing.append("nome do cliente")

    if not client.get("sector"):
        missing.append("setor de atuação")

    # Relatório obrigatório
    reports = client.get("reports", [])
    if not reports:
        missing.append("relatório de análise")

    # Status adequado
    if client.get("status") not in ["Novo", "Ativo"]:
        missing.append("status adequado (deve ser 'Novo' ou 'Ativo')")

    return {
        "valid": len(missing) == 0,
        "missing": missing
    }


def _prepare_client_analysis_data(client_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    📋 Preparar dados estruturados para análise dos agentes
    """
    # Extrair último relatório (mais completo)
    reports = client_data.get("reports", [])
    latest_report = reports[-1] if reports else {}

    return {
        "client_id": str(client_data.get("_id", "")),
        "client_name": client_data.get("name", ""),
        "sector": client_data.get("sector", "Geral"),
        "company_size": _estimate_company_size(client_data),
        "location": {
            "city": client_data.get("city", ""),
            "state": client_data.get("state", "")
        },
        "contact_info": {
            "website": client_data.get("site", ""),
            "email": client_data.get("email", ""),
            "phone": client_data.get("phone", "")
        },
        "business_profile": latest_report.get("dossie_expandido", {}),
        "market_analysis": latest_report.get("analise_mercado", {}),
        "technical_profile": latest_report.get("perfil_tecnico", {}),
        "funding_data": client_data.get("funding_data", {}),
        "current_projects": client_data.get("projects", []),
        "analysis_timestamp": datetime.now(UTC).isoformat()
    }


def _estimate_company_size(client_data: Dict[str, Any]) -> str:
    """
    📏 Estimar porte da empresa baseado em dados disponíveis
    """
    # Lógica simples para estimar porte
    # Pode ser melhorada com mais dados

    if client_data.get("funding_data", {}).get("total_funding", 0) > 1000000:
        return "Grande"
    elif client_data.get("funding_data", {}).get("total_funding", 0) > 100000:
        return "Média"
    else:
        return "Pequena"


def _create_project_generation_config() -> Dict[str, Any]:
    """
    ⚙️ Configuração específica para orquestração de geração de projetos
    """
    return {
        "max_execution_time_minutes": 10,
        "parallel_execution": False,  # Execução sequencial para melhor contexto
        "continue_on_agent_failure": True,
        "minimum_projects_count": 2,
        "maximum_projects_count": 5,
        "focus_areas": [
            "digital_transformation",
            "process_automation",
            "market_expansion",
            "technology_adoption",
            "operational_efficiency"
        ]
    }


async def _execute_project_analysis_phases(
    client_id: str,
    orchestration_id: str,
    analysis_data: Dict[str, Any],
    project_team: 'ProjectSuggestionTeam'
):
    """
    🔄 Executar fases sequenciais de análise e geração de projetos
    """
    phases = [
        {
            "id": "business_analysis",
            "name": "Análise de Negócio",
            "description": "Análise profunda do negócio e identificação de oportunidades"
        },
        {
            "id": "market_research",
            "name": "Pesquisa de Mercado",
            "description": "Análise de tendências e oportunidades de mercado"
        },
        {
            "id": "technology_assessment",
            "name": "Avaliação Tecnológica",
            "description": "Identificação de tecnologias e soluções adequadas"
        },
        {
            "id": "project_design",
            "name": "Design de Projetos",
            "description": "Estruturação e design dos projetos sugeridos"
        },
        {
            "id": "validation",
            "name": "Validação e Priorização",
            "description": "Validação de viabilidade e priorização dos projetos"
        }
    ]

    phase_results = {}

    for i, phase in enumerate(phases):
        try:
            # Notificar início da fase
            await websocket_manager.broadcast({
                "type": "project_generation_phase_update",
                "clientId": client_id,
                "orchestrationId": orchestration_id,
                "phase": phase["id"],
                "phaseName": phase["name"],
                "phaseDescription": phase["description"],
                "progress": int((i / len(phases)) * 100),
                "message": f"🔄 {phase['name']}: {phase['description']}",
                "timestamp": datetime.now(UTC).isoformat()
            })

            # Executar fase (será implementado)
            # phase_result = await project_team.execute_phase(phase["id"], analysis_data, phase_results)
            # phase_results[phase["id"]] = phase_result

            # Simular tempo de processamento
            await asyncio.sleep(2)

        except Exception as e:
            logger.error(f"Erro na fase {phase['id']}: {str(e)}")
            # Continuar com próxima fase

    # Gerar projetos finais baseados em todas as fases
    final_projects = await _generate_final_projects(analysis_data, phase_results)

    # Salvar projetos no banco
    await _save_generated_projects(client_id, orchestration_id, final_projects)


async def _generate_final_projects(
    analysis_data: Dict[str, Any],
    phase_results: Dict[str, Any]
) -> List[Dict[str, Any]]:
    """
    🎨 Gerar projetos finais consolidados usando ArchitectTeam REAL
    """
    try:
        # Importar ArchitectTeam
        from ..tools.agents.teams.architect_team import ArchitectTeam

        # Criar instância do team de arquitetos
        architect_team = ArchitectTeam()

        # Preparar prompt para análise
        client_name = analysis_data.get("client_name", "Cliente")
        sector = analysis_data.get("sector", "Geral")
        company_size = analysis_data.get("company_size", "Média")
        lighthouse_data = analysis_data.get("lighthouse_analysis", {})

        analysis_prompt = f"""
        Analise os dados do cliente e gere 3 projetos de transformação digital customizados:
        
        **Cliente:** {client_name}
        **Setor:** {sector}
        **Tamanho:** {company_size}
        **Análise Lighthouse:** {lighthouse_data}
        
        Para cada projeto, retorne JSON no formato:
        {{
            "title": "Nome do Projeto",
            "summary": "Resumo executivo",
            "description": "Descrição detalhada",
            "category": "Categoria principal",
            "tags": ["tag1", "tag2"],
            "priority": "alta/média/baixa",
            "estimated_effort": "alto/médio/baixo",
            "estimated_duration": "X meses",
            "estimated_budget": "R$ X - R$ Y",
            "roi_potential": "alto/médio/baixo",
            "risk_level": "alto/médio/baixo",
            "technologies": ["tech1", "tech2"],
            "success_metrics": ["métrica1", "métrica2"],
            "implementation_phases": ["fase1", "fase2"]
        }}
        
        Base os projetos nas necessidades REAIS identificadas na análise.
        """

        # Usar o ArchitectTeam para gerar projetos
        response = await architect_team.agent.run(analysis_prompt)

        # Processar resposta
        if hasattr(response, 'content'):
            content = response.content
        else:
            content = str(response)

        # Tentar extrair JSON da resposta
        import json
        import re
        content_clean = content.replace(
            "```json", "").replace("```", "").strip()
        try:
            projeto_data = json.loads(content_clean)
        except json.JSONDecodeError:
            # Usar regex para encontrar o maior bloco JSON dentro do conteúdo
            match = re.search(r"\{.*\}", content, re.DOTALL)
            if match:
                json_str = match.group(0).replace("```", "").strip()
                projeto_data = json.loads(json_str)
            else:
                raise ValueError("Não foi possível extrair JSON do resultado")

        # Extrair o objeto projeto do JSON
        projeto_obj = projeto_data.get("projeto", projeto_data)

        # Salvar estimativa no projeto com estrutura correta
        estimativa_detalhada = {
            "gerada_em": datetime.now(UTC).isoformat(),
            "tipo": "team_agno_estimate",
            "agentes_consultados": response.formatted_tool_calls if hasattr(response, 'formatted_tool_calls') else [],
            "status": "concluida",
            "versao": "1.0",
            # Manter compatibilidade com código existente
            "resultado_completo": response.content if hasattr(response, 'content') else str(response)
        }

        # Preparar dados para salvar
        update_data = {
            "estimate_resultado": estimativa_detalhada,
            "estimate_status": "completed",
            "estimate_atualizada_em": datetime.now(UTC),
            "estimate_requested": False,
            "updated_at": datetime.now(UTC)
        }

        # Adicionar campo projeto apenas se foi processado com sucesso
        if projeto_obj is not None:
            update_data["projeto"] = projeto_obj

        # Salvar estimativa no projeto (campos padronizados)
        projetos_collection.update_one(
            {"_id": ObjectId(client_id)},
            {"$set": update_data}
        )

        # Notificar conclusão
        await websocket_manager.broadcast({
            "type": "project_estimate_completed",
            "clientId": client_id,
            "clientName": client_name,
            "message": f"✅ Estimativa detalhada concluída para projeto: {client_name}",
            "timestamp": datetime.now(UTC).isoformat(),
            "action": "refresh_project"
        })

        logger.info(
            f"✅ Estimativa Team Agno CONCLUÍDA para projeto: {client_name}")

    except Exception as e:
        # Timestamp de erro
        error_time = datetime.now(UTC)

        # Marcar como erro (campos padronizados)
        projetos_collection.update_one(
            {"_id": ObjectId(client_id)},
            {
                "$set": {
                    "estimate_status": "error",
                    "estimate_error": str(e),
                    "estimate_requested": False,
                    "estimate_atualizada_em": error_time,
                    "updated_at": error_time
                }
            }
        )

        # Notificar erro
        await websocket_manager.broadcast({
            "type": "project_estimate_error",
            "clientId": client_id,
            "clientName": client_name,
            "message": f"❌ Erro na estimativa do projeto: {str(e)}",
            "timestamp": datetime.now(UTC).isoformat()
        })


async def _save_generated_projects(
    client_id: str,
    orchestration_id: str,
    projects: List[Dict[str, Any]]
):
    """
    💾 Salvar projetos gerados no banco e notificar conclusão
    """
    try:
        # Atualizar banco com projetos gerados
        clients_collection.update_one(
            {"_id": ObjectId(client_id)},
            {
                "$set": {
                    "projects": projects,
                    "project_generation_status": "completed",
                    "project_generation_completed_at": datetime.now(UTC),
                    "projects_count": len(projects),
                    "last_project_generation": orchestration_id,
                    "updated_at": datetime.now(UTC)
                }
            }
        )

        # Buscar nome do cliente para notificação
        client = clients_collection.find_one({"_id": ObjectId(client_id)})
        client_name = client.get("name", "Cliente") if client else "Cliente"

        # Notificar conclusão
        await websocket_manager.broadcast({
            "type": "projects_generation_completed",
            "clientId": client_id,
            "clientName": client_name,
            "orchestrationId": orchestration_id,
            "projectsCount": len(projects),
            "projects": projects,
            "message": f"🎉 {len(projects)} projetos gerados com sucesso para {client_name}!",
            "action": "refresh_projects",
            "timestamp": datetime.now(UTC).isoformat()
        })

        logger.info(
            f"✅ {len(projects)} projetos salvos para cliente {client_id}")

    except Exception as e:
        logger.error(f"❌ Erro ao salvar projetos: {str(e)}")
        raise

# ===================================================================
# 🎯 ROTAS PARA ESTIMATIVAS DE PROJETOS ESPECÍFICOS
# ===================================================================


@router.post("/projects/{project_id}/generate-estimate")
def request_project_estimate(project_id: str, background_tasks: BackgroundTasks):
    """
    🎯 ENDPOINT CORRETO: Gerar estimativa detalhada para PROJETO específico

    Este é o endpoint que deveria ser usado pelo frontend quando 
    o usuário clica em "Gerar Estimativa" no card de um projeto.
    """
    try:
        # Verificar se o projeto existe
        projeto = projetos_collection.find_one({"_id": ObjectId(project_id)})
        if not projeto:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Projeto não encontrado"
            )

        # Verificar se já está processando estimativa ou se já existe resultado
        estimate_requested = projeto.get("estimate_requested") or projeto.get(
            "estimativa_solicitada", False)
        estimate_result = projeto.get(
            "estimate_resultado") or projeto.get("estimativa_detalhada")
        estimate_status = projeto.get(
            "estimate_status") or projeto.get("estimativa_status")

        if estimate_result:
            return {
                "message": "Estimativa já existe para este projeto",
                "project_id": project_id,
                "project_title": projeto.get("titulo", ""),
                "status": "estimativa_completa",
                "has_estimate": True
            }

        if estimate_status == "processing" or estimate_status == "processando":
            return {
                "message": "Estimativa já está sendo processada para este projeto",
                "project_id": project_id,
                "project_title": projeto.get("titulo", ""),
                "status": "estimativa_em_andamento"
            }

        # Buscar dados do cliente relacionado
        cliente = clients_collection.find_one(
            {"_id": ObjectId(projeto.get("cliente_id"))})
        cliente_info = {
            "nome": cliente.get("name", "") if cliente else "",
            "setor": cliente.get("sector", "") if cliente else "",
            "cidade": cliente.get("city", "") if cliente else "",
            "estado": cliente.get("state", "") if cliente else ""
        }

        # Timestamp com timezone
        now_utc = datetime.now(UTC)

        # Marcar como solicitado (campos padronizados)
        projetos_collection.update_one(
            {"_id": ObjectId(project_id)},
            {
                "$set": {
                    "estimate_requested": True,
                    "estimate_status": "processing",
                    "estimate_solicitada_em": now_utc,
                    "updated_at": now_utc
                }
            }
        )

        # Executar estimativa em background
        background_tasks.add_task(
            processar_estimativa_projeto,
            ObjectId(project_id),
            projeto.get("titulo", ""),
            projeto.get("description", ""),
            projeto.get("area_aplicacao", ""),
            projeto.get("objetivos", []),
            projeto.get("publico_alvo", ""),
            cliente_info
        )

        return {
            "message": "Estimativa Team Agno foi solicitada para o projeto",
            "project_id": project_id,
            "project_title": projeto.get("titulo", ""),
            "client_name": cliente_info["nome"],
            "status": "estimativa_iniciada",
            "estimated_time": "2-3 minutos",
            "agents_count": 10,
            "notification_info": "Você será notificado quando a estimativa estiver pronta"
        }

    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="ID de projeto inválido"
        )

    except Exception as e:
        logger.error(f"❌ Erro ao solicitar estimativa do projeto: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        )


@router.get("/projects/{project_id}/estimate-status")
def get_project_estimate_status(project_id: str):
    """
    📊 Verificar status da estimativa de um projeto específico
    """
    try:
        # Verificar se o projeto existe
        projeto = projetos_collection.find_one({"_id": ObjectId(project_id)})
        if not projeto:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Projeto não encontrado"
            )

        # Verificar status da estimativa (usar múltiplos campos possíveis)
        estimate_status = projeto.get("estimate_status") or projeto.get(
            "estimativa_status", "não_solicitada")
        estimate_requested = projeto.get("estimate_requested") or projeto.get(
            "estimativa_solicitada", False)
        estimate_result = projeto.get(
            "estimate_resultado") or projeto.get("estimativa_detalhada")

        status_info = {
            "project_id": project_id,
            "project_title": projeto.get("titulo"),
            "estimativa_solicitada": estimate_requested,
            "estimativa_status": estimate_status,
            "has_estimativa": bool(estimate_result),
            "agents_count": 10
        }

        # Informações baseadas no status
        if estimate_status == "processing" or estimate_status == "processando":
            # Tentar múltiplos nomes de campo para data de início
            solicitada_em = (projeto.get("estimate_solicitada_em") or
                             projeto.get("estimativa_solicitada_em") or
                             projeto.get("estimativa_iniciada_em"))

            if solicitada_em:
                solicitada_em = _ensure_timezone(solicitada_em)
                status_info["started_at"] = solicitada_em.isoformat()
                status_info["processing_duration"] = _calculate_duration_safely(
                    solicitada_em)
                status_info["estimated_time"] = "2-3 minutos"

        elif estimate_status == "completed" or estimate_status == "concluida":
            # Tentar múltiplos nomes de campo para data de conclusão
            concluida_em = (projeto.get("estimate_atualizada_em") or
                            projeto.get("estimativa_concluida_em") or
                            projeto.get("estimate_completed_at"))

            if concluida_em:
                concluida_em = _ensure_timezone(concluida_em)
                status_info["finished_at"] = concluida_em.isoformat()

            # 🎯 ADICIONAR DADOS PROCESSADOS PARA FRONTEND
            if estimate_result:
                resultado_completo = estimate_result.get(
                    "resultado_completo", "")
                status_info["estimativa_processada"] = _processar_estimativa_para_frontend_v2(
                    estimate_result)

        elif estimate_status == "error" or estimate_status == "erro":
            # Tentar múltiplos nomes de campo para erro
            erro = projeto.get("estimate_error") or projeto.get(
                "estimativa_erro")
            if erro:
                status_info["error_message"] = erro

        return status_info

    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="ID de projeto inválido"
        )

    except Exception as e:
        logger.error(f"❌ Erro ao verificar status da estimativa: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        )


@router.get("/projects/{project_id}")
def get_project_by_id(project_id: str):
    """
    📋 Buscar dados completos de um projeto específico por ID

    Retorna estrutura otimizada para o frontend com estimativa processada
    """
    try:
        # Verificar se o projeto existe
        projeto = projetos_collection.find_one({"_id": ObjectId(project_id)})
        if not projeto:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Projeto não encontrado"
            )

        # Converter ObjectId para string
        projeto["_id"] = str(projeto["_id"])
        if projeto.get("cliente_id"):
            projeto["cliente_id"] = str(projeto["cliente_id"])

        # Buscar dados do cliente se existir
        if projeto.get("cliente_id"):
            try:
                cliente = clients_collection.find_one(
                    {"_id": ObjectId(projeto["cliente_id"])})
                if cliente:
                    projeto["cliente_nome"] = cliente.get("name", "")
                    projeto["cliente_sector"] = cliente.get("sector", "")
            except:
                # Se não conseguir buscar cliente, continuar sem erro
                pass

        # 🎯 PROCESSAR ESTIMATIVA PARA FRONTEND
        estimate_result = projeto.get("estimate_resultado")
        if estimate_result:
            # 🔧 CORREÇÃO: Verificar se existe campo "projeto" estruturado
            if "projeto" in projeto and isinstance(projeto["projeto"], dict):
                # Usar dados estruturados do campo "projeto"
                dados_para_processamento = {
                    "projeto": projeto["projeto"],
                    "estimate_resultado": estimate_result
                }
                logger.info("✅ Usando dados estruturados do campo 'projeto'")
            else:
                # Fallback para dados do estimate_result
                dados_para_processamento = estimate_result
                logger.info("⚠️ Usando fallback - dados do estimate_resultado")

            # Processar estrutura rica da estimativa
            projeto["estimativa_processada"] = _processar_estimativa_para_frontend_v2(
                dados_para_processamento)

            # Adicionar flag indicando que tem estimativa
            projeto["has_detailed_estimate"] = True
        else:
            projeto["has_detailed_estimate"] = False

        # 🎯 GARANTIR que o campo "projeto" seja incluído na resposta
        # O campo "projeto" já está salvo diretamente no documento MongoDB
        # Ele é automaticamente incluído na resposta do endpoint

        return projeto

    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="ID de projeto inválido"
        )

    except Exception as e:
        logger.error(f"❌ Erro ao buscar projeto: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        )


async def processar_estimativa_projeto(project_id: ObjectId, titulo: str, description: str, area_aplicacao: str, objetivos: list, publico_alvo: str, cliente_info: dict):
    """
    🎯 FUNÇÃO CORRETA: Processar estimativa Team Agno para PROJETO específico

    Esta é a função que deveria estar sendo usada - analisando projetos específicos,
    não clientes genéricos.
    """
    try:
        # Verificar se o projeto existe
        projeto = projetos_collection.find_one({"_id": project_id})
        if not projeto:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Projeto não encontrado"
            )

        logger.info(f"🎯 Iniciando estimativa Team Agno para projeto: {titulo}")

        # Timestamp com timezone
        now_utc = datetime.now(UTC)

        # Atualizar status do projeto (campos padronizados)
        projetos_collection.update_one(
            {"_id": project_id},
            {
                "$set": {
                    "estimate_status": "processing",
                    "estimate_solicitada_em": now_utc,
                    "updated_at": now_utc
                }
            }
        )

        # Notificar início da estimativa
        await websocket_manager.broadcast({
            "type": "project_estimate_started",
            "projectId": str(project_id),
            "projectTitle": titulo,
            "message": f"🎯 Iniciando estimativa detalhada para projeto: {titulo}",
            "timestamp": datetime.now(UTC).isoformat(),
            "estimated_time": "2-3 minutos",
            "agents_count": 10
        })

        # 🤖 EXECUTAR TEAM AGNO COM DADOS DO PROJETO
        import sys
        from pathlib import Path

        # Adicionar path se necessário
        current_dir = Path(__file__).parent
        backend_dir = current_dir.parent
        if str(backend_dir) not in sys.path:
            sys.path.insert(0, str(backend_dir))

        # Importar o team configurado
        from teams.project_team import team

        # Notificar que os agentes estão trabalhando
        await websocket_manager.broadcast({
            "type": "project_estimate_working",
            "projectId": str(project_id),
            "message": "10 especialistas analisando o projeto...",
            "timestamp": datetime.now(UTC).isoformat()
        })

        # Montar prompt estruturado com dados específicos do projeto
        objetivos_texto = "\n".join(
            [f"- {obj}" for obj in objetivos]) if objetivos else "Não especificado"

        prompt_projeto = f"""
🎯 PROJETO ESPECÍFICO: {titulo}

📋 DESCRIÇÃO DETALHADA: {description}

🏢 ÁREA DE APLICAÇÃO: {area_aplicacao}

🎯 OBJETIVOS ESPECÍFICOS:
{objetivos_texto}

👥 PÚBLICO-ALVO: {publico_alvo}

🏢 INFORMAÇÕES DO CLIENTE:
- Nome: {cliente_info['nome']}
- Setor: {cliente_info['setor']}
- Localização: {cliente_info['cidade']}, {cliente_info['estado']}

🚨 INSTRUÇÕES CRÍTICAS:
- O nome do projeto no JSON DEVE SER EXATAMENTE: "{titulo}"
- NÃO use nomes genéricos como "Projeto Rethink" ou similares
- Todos os dados devem ser ESPECÍFICOS para este projeto: {titulo}
- A descrição deve refletir EXATAMENTE o escopo deste projeto específico
- As tecnologias devem ser adequadas aos objetivos específicos listados acima

📊 MISSÃO: Gere uma estimativa DETALHADA e ESPECÍFICA de escopo para este projeto incluindo:
1. Arquitetura técnica adequada aos objetivos específicos deste projeto
2. Stack tecnológico recomendado para os requisitos específicos
3. Cronograma detalhado em sprints baseado na complexidade real
4. Equipe necessária por especialidade para este tipo de projeto
5. Custos estimados em R$ baseados no escopo real
6. Roadmap de implementação específico para os objetivos
7. Análise de riscos e dependências específicas do projeto

⚠️ IMPORTANTE: O campo "nome" no JSON final deve ser "{titulo}" e não um nome genérico!
"""

        # 🔧 CORREÇÃO: Truncar o prompt para evitar erros 400/503 nos LLMs
        if len(prompt_projeto) > 12000:
            # Truncamento simples para evitar context overflow
            prompt_truncado = prompt_projeto[:6000] + \
                "\n\n[... CONTEXTO TRUNCADO ...]\n\n" + prompt_projeto[-6000:]
        else:
            prompt_truncado = prompt_projeto

        # Executar análise Team Agno com dados específicos do projeto
        resultado_agno = await asyncio.to_thread(team.run, prompt_truncado)

        # Timestamp de conclusão
        completion_time = datetime.now(UTC)

        # Debug: Logar o tipo e conteúdo da resposta
        logger.info(f"🔍 Tipo de resultado_agno: {type(resultado_agno)}")
        logger.info(f"🔍 Atributos disponíveis: {dir(resultado_agno)}")
        if hasattr(resultado_agno, 'content'):
            logger.info(
                f"🔍 Primeiros 500 caracteres do content: {resultado_agno.content[:500]}")

        # Processar resultado JSON do Team Agno
        projeto_obj = None  # Inicializar variável
        try:
            # O Agno pode retornar o resultado de diferentes formas
            if hasattr(resultado_agno, 'parsed'):
                # Se tem propriedade parsed, usar ela
                projeto_data = resultado_agno.parsed
            elif hasattr(resultado_agno, 'json'):
                # Se tem propriedade json, usar ela
                projeto_data = resultado_agno.json
            elif hasattr(resultado_agno, 'content'):
                # Se tem apenas content como string, fazer parse
                import json
                import re
                content = resultado_agno.content
                # Remover cercas de código Markdown ``` e labels de linguagem, se existirem
                content_clean = content.replace(
                    "```json", "").replace("```", "").strip()
                try:
                    projeto_data = json.loads(content_clean)
                except json.JSONDecodeError:
                    # Usar regex para encontrar o maior bloco JSON dentro do conteúdo
                    match = re.search(r"\{.*\}", content, re.DOTALL)
                    if match:
                        json_str = match.group(0).replace("```", "").strip()
                        projeto_data = json.loads(json_str)
                    else:
                        raise ValueError(
                            "Não foi possível extrair JSON do resultado")
            else:
                # Último recurso: tentar fazer parse direto
                import json
                projeto_data = json.loads(str(resultado_agno))

            # Extrair o objeto projeto do JSON
            projeto_obj = projeto_data.get("projeto", projeto_data)

            # 🔧 CORREÇÃO CRÍTICA: Garantir que o nome do projeto seja específico
            if projeto_obj and isinstance(projeto_obj, dict):
                # Verificar se o nome está genérico e corrigir
                nome_atual = projeto_obj.get("nome", "")
                if nome_atual in ["Projeto Rethink", "Rethink", ""] or "rethink" in nome_atual.lower():
                    projeto_obj["nome"] = titulo
                    logger.warning(
                        f"⚠️ Nome genérico detectado '{nome_atual}', corrigido para '{titulo}'")

                # Garantir que a descrição seja específica
                desc_atual = projeto_obj.get("descricao", "")
                if "plataforma tecnológica" in desc_atual.lower() and len(desc_atual) < 200:
                    projeto_obj["descricao"] = description
                    logger.warning(
                        f"⚠️ Descrição genérica detectada, corrigida para descrição específica")

            # Salvar estimativa no projeto com estrutura correta
            estimativa_detalhada = {
                "gerada_em": completion_time.isoformat(),
                "tipo": "team_agno_estimate",
                "agentes_consultados": resultado_agno.formatted_tool_calls if hasattr(resultado_agno, 'formatted_tool_calls') else [],
                "status": "concluida",
                "versao": "1.0",
                # Manter compatibilidade com código existente
                "resultado_completo": resultado_agno.content if hasattr(resultado_agno, 'content') else str(resultado_agno)
            }

        except Exception as parse_error:
            logger.error(f"Erro ao processar JSON do Team Agno: {parse_error}")
            # Fallback: salvar como estava antes se houver erro no parse
            projeto_obj = None  # Não salvar campo projeto se houver erro
            estimativa_detalhada = {
                "gerada_em": completion_time.isoformat(),
                "tipo": "team_agno_estimate",
                "projeto_titulo": titulo,
                "projeto_description": description,
                "resultado_completo": resultado_agno.content if hasattr(resultado_agno, 'content') else str(resultado_agno),
                "agentes_consultados": resultado_agno.formatted_tool_calls if hasattr(resultado_agno, 'formatted_tool_calls') else [],
                "status": "concluida",
                "versao": "1.0",
                "parse_error": str(parse_error)
            }

        # Preparar dados para salvar
        update_data = {
            "estimate_resultado": estimativa_detalhada,
            "estimate_status": "completed",
            "estimate_atualizada_em": completion_time,
            "estimate_requested": False,
            "updated_at": completion_time
        }

        # Adicionar campo projeto apenas se foi processado com sucesso
        if projeto_obj is not None:
            update_data["projeto"] = projeto_obj

        # Salvar estimativa no projeto (campos padronizados)
        projetos_collection.update_one(
            {"_id": project_id},
            {"$set": update_data}
        )

        # Notificar conclusão
        await websocket_manager.broadcast({
            "type": "project_estimate_completed",
            "projectId": str(project_id),
            "projectTitle": titulo,
            "clientName": cliente_info['nome'],
            "message": f"✅ Estimativa detalhada concluída para projeto: {titulo}",
            "timestamp": datetime.now(UTC).isoformat(),
            "action": "refresh_project"
        })

        logger.info(f"✅ Estimativa Team Agno CONCLUÍDA para projeto: {titulo}")

    except Exception as e:
        # Timestamp de erro
        error_time = datetime.now(UTC)

        # Marcar como erro (campos padronizados)
        projetos_collection.update_one(
            {"_id": project_id},
            {
                "$set": {
                    "estimate_status": "error",
                    "estimate_error": str(e),
                    "estimate_requested": False,
                    "estimate_atualizada_em": error_time,
                    "updated_at": error_time
                }
            }
        )

        # Notificar erro
        await websocket_manager.broadcast({
            "type": "project_estimate_error",
            "projectId": str(project_id),
            "projectTitle": titulo,
            "message": f"❌ Erro na estimativa do projeto: {str(e)}",
            "timestamp": datetime.now(UTC).isoformat()
        })
