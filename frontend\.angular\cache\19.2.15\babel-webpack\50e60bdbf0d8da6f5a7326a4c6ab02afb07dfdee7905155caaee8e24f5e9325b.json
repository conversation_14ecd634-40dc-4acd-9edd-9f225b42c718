{"ast": null, "code": "import { createErrorClass } from './createErrorClass';\nexport const UnsubscriptionError = createErrorClass(_super => function UnsubscriptionErrorImpl(errors) {\n  _super(this);\n  this.message = errors ? `${errors.length} errors occurred during unsubscription:\n${errors.map((err, i) => `${i + 1}) ${err.toString()}`).join('\\n  ')}` : '';\n  this.name = 'UnsubscriptionError';\n  this.errors = errors;\n});", "map": {"version": 3, "names": ["createErrorClass", "UnsubscriptionError", "_super", "UnsubscriptionErrorImpl", "errors", "message", "length", "map", "err", "i", "toString", "join", "name"], "sources": ["C:/projetos/scope-ai/frontend/node_modules/rxjs/dist/esm/internal/util/UnsubscriptionError.js"], "sourcesContent": ["import { createErrorClass } from './createErrorClass';\nexport const UnsubscriptionError = createErrorClass((_super) => function UnsubscriptionErrorImpl(errors) {\n    _super(this);\n    this.message = errors\n        ? `${errors.length} errors occurred during unsubscription:\n${errors.map((err, i) => `${i + 1}) ${err.toString()}`).join('\\n  ')}`\n        : '';\n    this.name = 'UnsubscriptionError';\n    this.errors = errors;\n});\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,oBAAoB;AACrD,OAAO,MAAMC,mBAAmB,GAAGD,gBAAgB,CAAEE,MAAM,IAAK,SAASC,uBAAuBA,CAACC,MAAM,EAAE;EACrGF,MAAM,CAAC,IAAI,CAAC;EACZ,IAAI,CAACG,OAAO,GAAGD,MAAM,GACf,GAAGA,MAAM,CAACE,MAAM;AAC1B,EAAEF,MAAM,CAACG,GAAG,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAK,GAAGA,CAAC,GAAG,CAAC,KAAKD,GAAG,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC,EAAE,GAC5D,EAAE;EACR,IAAI,CAACC,IAAI,GAAG,qBAAqB;EACjC,IAAI,CAACR,MAAM,GAAGA,MAAM;AACxB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}