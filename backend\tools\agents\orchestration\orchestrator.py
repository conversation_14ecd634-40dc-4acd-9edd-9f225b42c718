"""
Agent Orchestrator - Coordenador Principal dos Agentes

Responsável por coordenar a execução dos 6 agentes especializados,
gerenciando fases, dependências e resultados consolidados.
"""

import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List
from uuid import uuid4

from .schemas import (
    OrchestrationConfig, OrchestrationResult, OrchestrationStatus,
    AgentExecutionResult, AgentExecutionStatus
)
from .context import OrchestrationContext
from .execution_plan import ExecutionPlan
from .runner import AgentRunner
from ..schemas import TechnicalDiagnosisInput

logger = logging.getLogger(__name__)


class AgentOrchestrator:
    """
    Coordenador principal dos agentes especializados.

    Responsável por:
    - Coordenar execução dos 6 agentes em fases
    - Gerenciar dependências entre agentes e fases
    - Consolidar resultados finais
    - Monitorar progresso e tratamento de erros
    """

    def __init__(self, config: Optional[OrchestrationConfig] = None):
        """
        Inicializa o orquestrador

        Args:
            config: Configuração da orquestração. Se None, usa configuração padrão
        """
        self.config = config or OrchestrationConfig.get_default_config()
        self.execution_plan = ExecutionPlan(self.config)
        self.agent_runner = AgentRunner(self.config)

        # Orquestrações ativas
        self.active_orchestrations: Dict[str, OrchestrationResult] = {}

        logger.info("AgentOrchestrator inicializado")

    async def start_orchestration(
        self,
        client_id: str,
        client_data: Dict[str, Any],
        orchestration_id: Optional[str] = None
    ) -> OrchestrationResult:
        """
        Inicia uma nova orquestração dos agentes

        Args:
            client_id: ID do cliente sendo analisado
            client_data: Dados do cliente
            orchestration_id: ID personalizado (opcional)

        Returns:
            Resultado da orquestração (inicial, será atualizado conforme progresso)
        """
        # Criar ID único se não fornecido
        orch_id = orchestration_id or str(uuid4())

        logger.info(
            f"Iniciando orquestração {orch_id} para cliente {client_id}")

        try:
            # Criar contexto da orquestração
            context = OrchestrationContext(
                client_id=client_id,
                client_data=client_data,
                config=self.config
            )

            # Criar resultado inicial
            result = OrchestrationResult(
                orchestration_id=orch_id,
                client_id=client_id,
                status=OrchestrationStatus.RUNNING,
                config=self.config,
                start_time=datetime.now(timezone.utc),
                end_time=None,
                total_execution_time_ms=None,
                error_summary=None,
                final_report_id=None
            )

            # Registrar orquestração ativa
            self.active_orchestrations[orch_id] = result

            # Executar orquestração em background
            asyncio.create_task(self._execute_orchestration(context, result))

            logger.info(f"Orquestração {orch_id} iniciada com sucesso")
            return result

        except Exception as e:
            logger.error(f"Erro ao iniciar orquestração {orch_id}: {e}")

            # Criar resultado de erro
            error_result = OrchestrationResult(
                orchestration_id=orch_id,
                client_id=client_id,
                status=OrchestrationStatus.FAILED,
                config=self.config,
                start_time=datetime.now(timezone.utc),
                end_time=datetime.now(timezone.utc),
                error_summary=f"Erro na inicialização: {str(e)}",
                total_execution_time_ms=None,
                final_report_id=None
            )

            self.active_orchestrations[orch_id] = error_result
            return error_result

    async def _execute_orchestration(
        self,
        context: OrchestrationContext,
        result: OrchestrationResult
    ):
        """
        Executa a orquestração completa

        Args:
            context: Contexto da orquestração
            result: Objeto de resultado a ser atualizado
        """
        try:
            logger.info(f"Executando orquestração {result.orchestration_id}")

            # Validar plano de execução
            if not self.execution_plan.validate():
                raise ValueError("Plano de execução inválido")

            # Executar fases sequencialmente
            for phase in self.config.phases:
                logger.info(f"Iniciando fase {phase.phase_id}: {phase.name}")

                try:
                    # Verificar dependências da fase
                    if not self._check_phase_dependencies(phase, result):
                        logger.error(
                            f"Dependências da fase {phase.phase_id} não satisfeitas")
                        if not self.config.continue_on_failure:
                            raise ValueError(
                                f"Dependências da fase {phase.phase_id} falharam")
                        continue

                    # Executar agentes da fase
                    if phase.parallel:
                        await self._execute_phase_parallel(phase, context, result)
                    else:
                        await self._execute_phase_sequential(phase, context, result)

                    logger.info(f"Fase {phase.phase_id} concluída")

                except Exception as e:
                    logger.error(f"Erro na fase {phase.phase_id}: {e}")
                    result.error_summary = f"Erro na fase {phase.phase_id}: {str(e)}"

                    if not self.config.continue_on_failure:
                        result.status = OrchestrationStatus.FAILED
                        break

            # Finalizar orquestração
            await self._finalize_orchestration(result)

        except Exception as e:
            logger.error(
                f"Erro crítico na orquestração {result.orchestration_id}: {e}")
            result.status = OrchestrationStatus.FAILED
            result.error_summary = f"Erro crítico: {str(e)}"
            await self._finalize_orchestration(result)

    def _check_phase_dependencies(
        self,
        phase,
        result: OrchestrationResult
    ) -> bool:
        """
        Verifica se dependências da fase foram satisfeitas

        Args:
            phase: Fase a verificar
            result: Resultado atual da orquestração

        Returns:
            True se dependências estão satisfeitas
        """
        for dep_phase_id in phase.dependencies:
            # Encontrar agentes da fase dependente
            dep_agents = []
            for config_phase in self.config.phases:
                if str(config_phase.phase_id) == dep_phase_id:
                    dep_agents = config_phase.agents
                    break

            # Verificar se todos agentes da fase dependente completaram
            for agent_name in dep_agents:
                agent_result = result.agent_results.get(agent_name)
                if not agent_result or agent_result.status != AgentExecutionStatus.COMPLETED:
                    logger.warning(
                        f"Agente {agent_name} da fase {dep_phase_id} não completou")
                    return False

        return True

    async def _execute_phase_parallel(
        self,
        phase,
        context: OrchestrationContext,
        result: OrchestrationResult
    ):
        """
        Executa agentes de uma fase em paralelo

        Args:
            phase: Fase a executar
            context: Contexto da orquestração
            result: Resultado da orquestração
        """
        logger.info(
            f"Executando fase {phase.phase_id} em paralelo com {len(phase.agents)} agentes")

        # Criar tasks para execução paralela
        tasks = []
        for agent_name in phase.agents:
            task = self.agent_runner.run_agent(
                agent_name, context, phase.phase_id)
            tasks.append(task)

        try:
            # Executar com timeout da fase
            agent_results = await asyncio.wait_for(
                asyncio.gather(*tasks, return_exceptions=True),
                timeout=phase.timeout_seconds
            )

            # Processar resultados
            for i, agent_result in enumerate(agent_results):
                agent_name = phase.agents[i]

                if isinstance(agent_result, Exception):
                    logger.error(
                        f"Erro no agente {agent_name}: {agent_result}")
                    # Criar resultado de erro
                    error_result = AgentExecutionResult(
                        agent_name=agent_name,
                        status=AgentExecutionStatus.FAILED,
                        start_time=datetime.now(timezone.utc),
                        end_time=datetime.now(timezone.utc),
                        phase_id=phase.phase_id,
                        error_message=str(agent_result),
                        execution_time_ms=0,
                        result_data={},
                        analysis_id="",
                        input_data_size_kb=0.0,
                        output_data_size_kb=0.0
                    )
                    result.add_agent_result(error_result)
                else:
                    # Adicionar resultado bem-sucedido (deve ser AgentExecutionResult)
                    if isinstance(agent_result, AgentExecutionResult):
                        result.add_agent_result(agent_result)

                        # Atualizar contexto com resultado
                        if agent_result.result_data:
                            context.store_agent_result(
                                agent_name, agent_result.result_data)

        except asyncio.TimeoutError:
            logger.error(
                f"Timeout na fase {phase.phase_id} após {phase.timeout_seconds}s")
            raise

    async def _execute_phase_sequential(
        self,
        phase,
        context: OrchestrationContext,
        result: OrchestrationResult
    ):
        """
        Executa agentes de uma fase sequencialmente

        Args:
            phase: Fase a executar
            context: Contexto da orquestração
            result: Resultado da orquestração
        """
        logger.info(
            f"Executando fase {phase.phase_id} sequencialmente com {len(phase.agents)} agentes")

        for agent_name in phase.agents:
            try:
                logger.info(f"Executando agente {agent_name}")

                # Executar agente individual
                agent_result = await self.agent_runner.run_agent(
                    agent_name, context, phase.phase_id
                )

                # Adicionar resultado
                result.add_agent_result(agent_result)

                # Atualizar contexto com resultado
                if agent_result.status == AgentExecutionStatus.COMPLETED:
                    context.store_agent_result(
                        agent_name, agent_result.result_data or {})
                    logger.info(f"Agente {agent_name} completado com sucesso")
                else:
                    logger.error(
                        f"Agente {agent_name} falhou: {agent_result.error_message}")

                    if not self.config.continue_on_failure:
                        raise ValueError(f"Agente {agent_name} falhou")

            except Exception as e:
                logger.error(f"Erro crítico no agente {agent_name}: {e}")

                # Criar resultado de erro
                error_result = AgentExecutionResult(
                    agent_name=agent_name,
                    status=AgentExecutionStatus.FAILED,
                    start_time=datetime.now(timezone.utc),
                    end_time=datetime.now(timezone.utc),
                    phase_id=phase.phase_id,
                    error_message=str(e),
                    execution_time_ms=0,
                    result_data={},
                    analysis_id="",
                    input_data_size_kb=0.0,
                    output_data_size_kb=0.0
                )
                result.add_agent_result(error_result)

                if not self.config.continue_on_failure:
                    raise

    async def _finalize_orchestration(self, result: OrchestrationResult):
        """
        Finaliza a orquestração e define status final

        Args:
            result: Resultado da orquestração
        """
        logger.info(f"Finalizando orquestração {result.orchestration_id}")

        # Definir timestamp final
        result.end_time = datetime.now(timezone.utc)

        # Calcular tempo total de execução
        if result.start_time:
            total_time = (result.end_time -
                          result.start_time).total_seconds() * 1000
            result.total_execution_time_ms = int(total_time)

        # Determinar status final baseado nos resultados dos agentes
        if result.status != OrchestrationStatus.FAILED:
            if result.failed_agents == 0:
                result.status = OrchestrationStatus.COMPLETED
                logger.info(
                    f"Orquestração {result.orchestration_id} completada com sucesso")
            elif result.successful_agents > 0:
                result.status = OrchestrationStatus.PARTIAL_SUCCESS
                logger.warning(
                    f"Orquestração {result.orchestration_id} completada parcialmente")
            else:
                result.status = OrchestrationStatus.FAILED
                logger.error(
                    f"Orquestração {result.orchestration_id} falhou completamente")

        # Atualizar timestamp final
        result.last_update = datetime.now(timezone.utc)

        # Log final
        progress = result.get_execution_progress()
        logger.info(
            f"Orquestração {result.orchestration_id} finalizada - "
            f"Status: {result.status.value} - "
            f"Progresso: {progress['progress_percentage']}% - "
            f"Tempo: {result.total_execution_time_ms}ms"
        )

    def get_orchestration_status(self, orchestration_id: str) -> Optional[OrchestrationResult]:
        """
        Obtém status atual de uma orquestração

        Args:
            orchestration_id: ID da orquestração

        Returns:
            Resultado atual da orquestração ou None se não encontrada
        """
        return self.active_orchestrations.get(orchestration_id)

    def list_active_orchestrations(self) -> Dict[str, OrchestrationResult]:
        """
        Lista todas as orquestrações ativas

        Returns:
            Dicionário com orquestrações ativas
        """
        return self.active_orchestrations.copy()

    async def cancel_orchestration(self, orchestration_id: str) -> bool:
        """
        Cancela uma orquestração ativa

        Args:
            orchestration_id: ID da orquestração a cancelar

        Returns:
            True se cancelamento foi bem-sucedido
        """
        result = self.active_orchestrations.get(orchestration_id)
        if not result:
            logger.warning(
                f"Orquestração {orchestration_id} não encontrada para cancelamento")
            return False

        if result.status in [OrchestrationStatus.COMPLETED, OrchestrationStatus.FAILED]:
            logger.warning(
                f"Orquestração {orchestration_id} já finalizada, não pode ser cancelada")
            return False

        logger.info(f"Cancelando orquestração {orchestration_id}")

        # Atualizar status
        result.status = OrchestrationStatus.CANCELLED
        result.end_time = datetime.now(timezone.utc)
        result.error_summary = "Orquestração cancelada pelo usuário"

        # Calcular tempo até cancelamento
        if result.start_time:
            total_time = (result.end_time -
                          result.start_time).total_seconds() * 1000
            result.total_execution_time_ms = int(total_time)

        logger.info(f"Orquestração {orchestration_id} cancelada com sucesso")
        return True

    def cleanup_completed_orchestrations(self, max_completed: int = 10):
        """
        Limpa orquestrações completadas antigas

        Args:
            max_completed: Máximo de orquestrações completadas a manter
        """
        completed = [
            (orch_id, result) for orch_id, result in self.active_orchestrations.items()
            if result.status in [
                OrchestrationStatus.COMPLETED,
                OrchestrationStatus.FAILED,
                OrchestrationStatus.CANCELLED
            ]
        ]

        if len(completed) > max_completed:
            # Ordenar por timestamp e remover as mais antigas
            completed.sort(
                key=lambda x: x[1].end_time or datetime.min.replace(tzinfo=timezone.utc))

            to_remove = len(completed) - max_completed
            for i in range(to_remove):
                orch_id = completed[i][0]
                del self.active_orchestrations[orch_id]
                logger.debug(f"Orquestração {orch_id} removida do cache")

    def get_health_status(self) -> Dict[str, Any]:
        """
        Retorna status de saúde do orquestrador

        Returns:
            Informações de saúde do sistema
        """
        active_count = len([
            r for r in self.active_orchestrations.values()
            if r.status == OrchestrationStatus.RUNNING
        ])

        completed_count = len([
            r for r in self.active_orchestrations.values()
            if r.status == OrchestrationStatus.COMPLETED
        ])

        failed_count = len([
            r for r in self.active_orchestrations.values()
            if r.status == OrchestrationStatus.FAILED
        ])

        agent_health = self.agent_runner.get_agent_health()

        return {
            "orchestrator_status": "healthy",
            "active_orchestrations": active_count,
            "completed_orchestrations": completed_count,
            "failed_orchestrations": failed_count,
            "total_orchestrations": len(self.active_orchestrations),
            "agent_health": agent_health,
            "config": {
                "phases": len(self.config.phases),
                "max_retries": self.config.max_retries_per_agent,
                "timeout_per_agent": self.config.timeout_per_agent_seconds,
                "continue_on_failure": self.config.continue_on_failure
            }
        }
