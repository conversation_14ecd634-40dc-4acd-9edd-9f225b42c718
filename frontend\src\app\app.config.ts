import { registerLocaleData } from '@angular/common';
import { provideHttpClient, withFetch, withXsrfConfiguration } from '@angular/common/http';
import localePtExtra from '@angular/common/locales/extra/pt';
import localePt from '@angular/common/locales/pt';
import { ApplicationConfig, LOCALE_ID, provideZoneChangeDetection } from '@angular/core';
import { provideAnimations } from '@angular/platform-browser/animations';
import {
	PreloadAllModules,
	provideRouter,
	withComponentInputBinding,
	withInMemoryScrolling,
	withPreloading,
	withRouterConfig,
	withViewTransitions,
} from '@angular/router';
import { RETHINK } from '@core/config/rethink.config';
import { MessageService } from 'primeng/api';
import { providePrimeNG } from 'primeng/config';
import { ROUTES } from './app.routes';

registerLocaleData(localePt, 'pt-BR', localePtExtra);

export const APP_CONFIG: ApplicationConfig = {
	providers: [
		{ provide: LOCALE_ID, useValue: 'pt-BR' },
		provideZoneChangeDetection({
			eventCoalescing: true,
			runCoalescing: true,
		}),
		provideRouter(
			ROUTES,
			withPreloading(PreloadAllModules),
			withViewTransitions(),
			withRouterConfig({
				paramsInheritanceStrategy: 'always',
				urlUpdateStrategy: 'eager',
				onSameUrlNavigation: 'reload',
			}),
			withComponentInputBinding(),
			withInMemoryScrolling({
				scrollPositionRestoration: 'enabled',
				anchorScrolling: 'enabled',
			}),
		),
		provideAnimations(),
		{
			provide: MessageService,
			useClass: MessageService,
		},
		provideHttpClient(
			// withInterceptors([]),
			withFetch(),
			withXsrfConfiguration({
				cookieName: 'XSRF-TOKEN',
				headerName: 'X-XSRF-TOKEN',
			}),
		),
		providePrimeNG({
			theme: {
				preset: RETHINK,
				options: {
					darkModeSelector: '.dark',
					cssLayer: {
						name: 'primeng',
						order: 'tailwind-base, primeng, tailwind-utilities',
					},
				},
			},
		}),
	],
};
