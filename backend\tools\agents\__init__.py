"""
Sistema de Agentes Agno para Scope AI

Este módulo implementa o Time Agno de agentes especializados usando
a infraestrutura oficial do Agno para análise arquitetural automatizada.

Componentes:
- Tools: Ferramentas customizadas para análise técnica
- Agents: Agentes especializados usando Agno Agent class  
- Teams: Time coordenado usando Agno Team class
- Storage: Persistência MongoDB das análises
"""

from .tools import (
    LighthouseAnalysisTools,
    VisualAnalysisTools,
    ArchitecturalRecommendationTools
)

from .agents import (
    PerformanceAgent,
    AccessibilityAgent,
    SEOSpecialistAgent,
    SecurityAgent
)

from .teams import ArchitectTeam

from .storage import (
    AgentStorage,
    AgentStorageManager,
    save_analysis_sync,
    get_analysis_sync,
    save_analysis_async,
    get_analysis_async
)

from .schemas import (
    TechnicalDiagnosisInput,
    ArchitecturalRecommendation,
    AnalysisResult,
    AgentConfig
)

__all__ = [
    # Tools
    "LighthouseAnalysisTools",
    "VisualAnalysisTools",
    "ArchitecturalRecommendationTools",

    # Agents
    "PerformanceAgent",
    "AccessibilityAgent",
    "SEOSpecialistAgent",
    "SecurityAgent",

    # Teams
    "ArchitectTeam",

    # Storage
    "AgentStorage",
    "AgentStorageManager",
    "save_analysis_sync",
    "get_analysis_sync",
    "save_analysis_async",
    "get_analysis_async",

    # Schemas
    "TechnicalDiagnosisInput",
    "ArchitecturalRecommendation",
    "AnalysisResult",
    "AgentConfig"
]
