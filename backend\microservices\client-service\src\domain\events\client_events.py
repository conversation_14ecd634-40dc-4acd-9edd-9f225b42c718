"""Eventos específicos do domínio de clientes."""

from dataclasses import dataclass, field
from typing import Dict, Any, Optional

from .base import DomainEvent


@dataclass(frozen=True)
class ClientCreatedEvent(DomainEvent):
    """Evento emitido quando um novo cliente é criado."""

    client_name: str = field(default="")
    client_url: str = field(default="")
    client_context: str = field(default="")
    initial_status: str = field(default="draft")


@dataclass(frozen=True)
class ClientUpdatedEvent(DomainEvent):
    """Evento emitido quando dados do cliente são atualizados."""

    updated_fields: Dict[str, Any] = field(default_factory=dict)
    previous_values: Dict[str, Any] = field(default_factory=dict)


@dataclass(frozen=True)
class ClientAnalysisRequestedEvent(DomainEvent):
    """Evento emitido quando uma análise é solicitada."""

    analysis_type: str = field(default="")
    requested_by: Optional[str] = field(default=None)
    priority: str = field(default="normal")


@dataclass(frozen=True)
class ClientAnalysisCompletedEvent(DomainEvent):
    """Evento emitido quando uma análise é concluída com sucesso."""

    analysis_type: str = field(default="")
    duration_seconds: float = field(default=0.0)
    result_summary: Dict[str, Any] = field(default_factory=dict)
    data_size_bytes: Optional[int] = field(default=None)


@dataclass(frozen=True)
class ClientAnalysisFailedEvent(DomainEvent):
    """Evento emitido quando uma análise falha."""

    analysis_type: str = field(default="")
    error_message: str = field(default="")
    error_code: str = field(default="")
    duration_seconds: float = field(default=0.0)
    retry_count: int = field(default=0)
    will_retry: bool = field(default=False)


@dataclass(frozen=True)
class ClientArchivedEvent(DomainEvent):
    """Evento emitido quando um cliente é arquivado."""

    reason: str = field(default="")
    archived_by: Optional[str] = field(default=None)
    can_be_restored: bool = field(default=True)
