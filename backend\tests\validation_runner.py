#!/usr/bin/env python3
"""
Runner <PERSON> Val<PERSON>ção da Conversão Assíncrona - ScopeAI MT-5
"""

import asyncio
import time
import sys
from datetime import datetime


async def test_async_performance():
    """Testa performance básica assíncrona"""
    print("🧪 Testando performance assíncrona...")
    
    async def async_op(op_id):
        await asyncio.sleep(0.1)
        return f"async_{op_id}"
    
    def sync_op(op_id):
        time.sleep(0.1)
        return f"sync_{op_id}"
    
    # Teste sequencial
    start = time.time()
    sync_results = [sync_op(i) for i in range(3)]
    sync_time = time.time() - start
    
    # Teste concorrente
    start = time.time()
    async_results = await asyncio.gather(*[async_op(i) for i in range(3)])
    async_time = time.time() - start
    
    speedup = sync_time / async_time
    print(f"  📊 Sync: {sync_time:.3f}s, Async: {async_time:.3f}s, Speedup: {speedup:.2f}x")
    
    assert speedup > 1.5, f"Speedup insuficiente: {speedup:.2f}x"
    print("  ✅ Performance test passou")


async def test_concurrent_operations():
    """Testa operações concorrentes"""
    print("🧪 Testando operações concorrentes...")
    
    async def task(task_id):
        await asyncio.sleep(0.05)
        return {"task": task_id}
    
    start = time.time()
    results = await asyncio.gather(*[task(i) for i in range(5)])
    elapsed = time.time() - start
    
    assert len(results) == 5
    assert elapsed < 0.2
    
    print(f"  📊 5 operações em {elapsed:.3f}s")
    print("  ✅ Concurrency test passou")


async def test_error_handling():
    """Testa tratamento de erros"""
    print("🧪 Testando error handling...")
    
    async def failing_task():
        await asyncio.sleep(0.01)
        raise ValueError("Erro intencional")
    
    async def success_task():
        await asyncio.sleep(0.01)
        return {"success": True}
    
    results = await asyncio.gather(
        failing_task(),
        success_task(),
        return_exceptions=True
    )
    
    successes = sum(1 for r in results if isinstance(r, dict))
    failures = sum(1 for r in results if isinstance(r, Exception))
    
    assert successes == 1
    assert failures == 1
    
    print(f"  📊 {successes} sucessos, {failures} falhas esperadas")
    print("  ✅ Error handling test passou")


async def test_task_manager():
    """Testa TaskManager se disponível"""
    print("🧪 Testando TaskManager...")
    
    try:
        from shared.task_manager import create_background_task, TaskPriority
        
        async def simple_task(data):
            await asyncio.sleep(0.1)
            return {"processed": data}
        
        task_id = await create_background_task(
            "validation_test",
            simple_task,
            TaskPriority.MEDIUM,
            "test_client",
            "Test Client",
            1,
            "test_data"
        )
        
        assert task_id is not None
        print(f"  ✅ TaskManager funcional - Task: {task_id[:8]}...")
        
    except ImportError:
        print("  ⚠️ TaskManager não disponível - pulando teste")


async def main():
    """Executa todos os testes de validação"""
    print("🚀 VALIDAÇÃO DA CONVERSÃO ASSÍNCRONA - ScopeAI MT-5")
    print(f"⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*50)
    
    tests = [
        test_async_performance,
        test_concurrent_operations,
        test_error_handling,
        test_task_manager
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            await test()
            passed += 1
        except Exception as e:
            print(f"  ❌ Teste falhou: {e}")
    
    print("\n" + "="*50)
    print(f"📊 RESUMO: {passed}/{total} testes passaram")
    
    if passed == total:
        print("🎉 TODOS OS TESTES PASSARAM!")
        return 0
    elif passed >= total // 2:
        print("⚠️ MAIORIA DOS TESTES PASSOU")
        return 1
    else:
        print("❌ MUITOS TESTES FALHARAM")
        return 2


if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(result)
    except Exception as e:
        print(f"💥 Erro: {e}")
        sys.exit(1) 