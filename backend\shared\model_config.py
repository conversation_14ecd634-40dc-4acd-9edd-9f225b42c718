"""
Configuração centralizada de modelos de IA

Sistema para mapear modelos disponíveis no settings.py 
para diferentes fornecedores e casos de uso.
"""

import logging
from typing import Dict, Any, Optional, Tuple
from config.settings import env

logger = logging.getLogger(__name__)


class ModelConfig:
    """
    Configuração centralizada de modelos de IA

    Mapeia modelos do settings.py para diferentes fornecedores
    e fornece fallbacks automáticos.
    """

    def __init__(self):
        """Inicializar configuração de modelos"""
        self._validate_settings()

    def _validate_settings(self):
        """Validar se pelo menos uma API está configurada"""
        apis_available = []

        if env.OPENAI_API_KEY:
            apis_available.append("OpenAI")
        if env.PERPLEXITY_API_KEY:
            apis_available.append("Perplexity")

        if not apis_available:
            logger.warning("⚠️ Nenhuma API de IA configurada!")
        else:
            logger.info(f"✅ APIs disponíveis: {', '.join(apis_available)}")

    def get_best_model(self, use_case: str = "general") -> <PERSON><PERSON>[str, str, Dict[str, Any]]:
        """
        Obter o melhor modelo disponível para um caso de uso

        Args:
            use_case: Caso de uso ("general", "analysis", "writing", "code")

        Returns:
            Tuple: (provider, model_id, config)
        """
        # Ordem de preferência por caso de uso (apenas modelos usados)
        preferences = {
            "general": [
                ("openai", env.GPT_4o, {"api_key": env.OPENAI_API_KEY}),
                ("openai", env.GPT_4_1_MINI, {"api_key": env.OPENAI_API_KEY}),
            ],
            "analysis": [
                ("openai", env.GPT_4o, {"api_key": env.OPENAI_API_KEY}),
                ("openai", env.GPT_4_1_MINI, {"api_key": env.OPENAI_API_KEY})
            ],
            "writing": [
                ("openai", env.GPT_4o, {"api_key": env.OPENAI_API_KEY}),
                ("openai", env.GPT_4_1_MINI, {"api_key": env.OPENAI_API_KEY})
            ],
            "code": [
                ("openai", env.GPT_4o, {"api_key": env.OPENAI_API_KEY}),
                ("openai", env.GPT_4_1_MINI, {"api_key": env.OPENAI_API_KEY}),
            ]
        }

        # Obter lista de preferências
        model_list = preferences.get(use_case, preferences["general"])

        # Tentar encontrar modelo disponível
        for provider, model_env_var, config in model_list:
            # Para outros providers, verificar API key
            if model_env_var and config.get("api_key"):
                logger.info(
                    f"Usando modelo {provider}:{model_env_var} para {use_case}")
                return provider, model_env_var, config

        # Fallback emergency - GPT-4o-mini sem validação
        logger.warning(
            f"Nenhum modelo configurado para {use_case}, usando fallback GPT-4o-mini")
        return "openai", "gpt-4o-mini", {"api_key": env.OPENAI_API_KEY or ""}

    def get_openai_model(self, use_case: str = "general") -> str:
        """Obter modelo OpenAI específico"""
        provider, model_id, config = self.get_best_model(use_case)

        if provider == "openai":
            return model_id
        else:
            # Fallback para OpenAI se disponível
            if env.OPENAI_API_KEY:
                if env.GPT_4o:
                    return env.GPT_4o
                elif env.GPT_4_1_MINI:
                    return env.GPT_4_1_MINI

            logger.warning("OpenAI não disponível, retornando modelo genérico")
            return "gpt-4o-mini"

    def get_perplexity_model(self) -> str:
        """Obter modelo Perplexity (geralmente fixo)"""
        if env.PERPLEXITY_API_KEY:
            return "llama-3.1-sonar-small-128k-online"
        else:
            logger.warning("Perplexity não disponível")
            return "llama-3.1-sonar-small-128k-online"

    def create_agno_config(self, use_case: str = "analysis") -> Dict[str, Any]:
        """
        Criar configuração para bibliotecas Agno baseada em modelos disponíveis

        Como Agno suporta diferentes providers, vamos mapear para o melhor disponível
        """
        provider, model_id, config = self.get_best_model(use_case)

        # Mapear para configuração Agno
        agno_config = {
            "model_id": model_id,
            "api_key": config.get("api_key"),
            "provider": provider,
            "use_case": use_case
        }

        # Configurações específicas por provider
        if provider == "openai":
            agno_config.update({
                "temperature": 0.3,
                "max_tokens": 2000,
                "top_p": 0.9
            })

        return agno_config

    def get_model_info(self) -> Dict[str, Any]:
        """Obter informações sobre modelos configurados"""
        info = {
            "available_providers": [],
            "models_configured": {},
            "recommended_usage": {}
        }

        # Verificar providers disponíveis
        if env.OPENAI_API_KEY:
            info["available_providers"].append("OpenAI")
            info["models_configured"]["openai"] = {
                "gpt_4o": env.GPT_4o,
                "gpt_4_1_mini": env.GPT_4_1_MINI
            }

        if env.PERPLEXITY_API_KEY:
            info["available_providers"].append("Perplexity")
            info["models_configured"]["perplexity"] = {
                "llama_sonar": "llama-3.1-sonar-small-128k-online"
            }

        # Recomendações de uso
        info["recommended_usage"] = {
            "general_analysis": self.get_best_model("analysis")[1],
            "content_writing": self.get_best_model("writing")[1],
            "code_generation": self.get_best_model("code")[1],
            "general_purpose": self.get_best_model("general")[1]
        }

        return info


# Instância global
model_config = ModelConfig()
