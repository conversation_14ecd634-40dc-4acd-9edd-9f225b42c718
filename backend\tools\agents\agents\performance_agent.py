"""
Performance Agent - Agente especializado em performance web

Agente do time Agno focado em análise e otimização de performance:
- Análise de Core Web Vitals
- Otimizações de carregamento
- Recomendações de infraestrutura
- Monitoramento de métricas
"""

import logging
from typing import Dict, Any, Optional
from agno.agent import Agent
from ...shared.model_config import model_config
from agno.tools.reasoning import ReasoningTools

from ..tools.lighthouse_tools import LighthouseAnalysisTools

logger = logging.getLogger(__name__)


class PerformanceAgent:
    """
    Agente especializado em performance web usando Agno

    Analisa dados de performance e gera recomendações
    específicas para otimização de velocidade e Core Web Vitals.
    """

    def __init__(self, model_name: Optional[str] = None):
        """
        Inicializa o Performance Agent usando modelos do settings.py

        Args:
            model_name: Modelo específico (opcional, usa automático se None)
        """
        self.lighthouse_tools = LighthouseAnalysisTools()
        
        # Obter configuração de modelo baseada no settings.py
        agno_config = model_config.create_agno_config("analysis")
        
        if model_name:
            available_models = [
                model_config.get_openai_model("analysis"),
                model_config.get_cohere_model("analysis"), 
                model_config.get_mistral_model("analysis")
            ]
            if model_name in available_models:
                self.model_name = model_name
            else:
                logger.warning(f"Modelo {model_name} não disponível no settings.py, usando automático")
                self.model_name = agno_config["model_id"]
        else:
            self.model_name = agno_config["model_id"]

        # Criar modelo baseado no provider
        if agno_config["provider"] == "openai":
            from agno.models.openai import OpenAI
            model_instance = OpenAI(id=self.model_name, api_key=agno_config["api_key"])
        elif agno_config["provider"] == "cohere":
            from agno.models.cohere import Cohere
            model_instance = Cohere(id=self.model_name, api_key=agno_config["api_key"])
        elif agno_config["provider"] == "mistral":
            from agno.models.mistral import Mistral
            model_instance = Mistral(id=self.model_name, api_key=agno_config["api_key"])
        else:
            from agno.models.openai import OpenAI
            model_instance = OpenAI(id="gpt-4o-mini", api_key=agno_config["api_key"])

        # Criar agente Agno especializado
        self.agent = Agent(
            name="Performance Specialist",
            role="Expert in web performance optimization and Core Web Vitals analysis",
            model=model_instance,
            tools=[ReasoningTools()],
            instructions=[
                "You are a web performance expert specializing in Core Web Vitals optimization",
                "Analyze Lighthouse data and identify critical performance bottlenecks",
                "Provide specific, actionable recommendations with implementation steps",
                "Focus on LCP, FCP, CLS, and FID metrics",
                "Consider both technical and business impact in your recommendations",
                "Always provide estimated timeframes and expected improvements",
                "Use structured analysis and reasoning for complex performance issues"
            ],
            description="Specialist in web performance optimization, Core Web Vitals, and loading speed analysis",
            markdown=True,
            show_tool_calls=True
        )

        logger.info(f"Performance Agent inicializado com {agno_config['provider']}:{self.model_name}")

    def analyze_performance(self, lighthouse_data: Dict[str, Any], company_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Analisa dados de performance usando o agente Agno

        Args:
            lighthouse_data: Dados brutos do Lighthouse
            company_context: Contexto da empresa (opcional)

        Returns:
            Análise completa de performance
        """
        try:
            logger.info(
                "Iniciando análise de performance com Performance Agent")

            # Processar dados Lighthouse com as tools
            lighthouse_metrics = self.lighthouse_tools.analyze_lighthouse_data(
                lighthouse_data)
            critical_issues = self.lighthouse_tools.get_critical_issues(
                lighthouse_data)
            priority_score = self.lighthouse_tools.calculate_priority_score(
                lighthouse_data)

            # Preparar contexto para o agente
            company_info = company_context or {}
            company_name = company_info.get("name", "a empresa")
            sector = company_info.get("sector", "tecnologia")

            # Criar prompt estruturado para análise
            analysis_prompt = f"""
Analise os seguintes dados de performance para {company_name} (setor: {sector}):

**MÉTRICAS LIGHTHOUSE:**
- Performance Score: {lighthouse_metrics.performance_score}/100
- LCP: {lighthouse_metrics.lcp}ms
- FCP: {lighthouse_metrics.fcp}ms  
- CLS: {lighthouse_metrics.cls}
- FID: {lighthouse_metrics.fid}ms

**ISSUES CRÍTICOS IDENTIFICADOS:**
{chr(10).join(f"- {issue}" for issue in critical_issues)}

**OPORTUNIDADES DE MELHORIA:**
{chr(10).join(f"- {opp}" for opp in lighthouse_metrics.opportunities)}

**SCORE DE PRIORIDADE:** {priority_score}/100

Por favor, forneça uma análise detalhada incluindo:

1. **Diagnóstico dos problemas principais** (foque nos 3 mais críticos)
2. **Recomendações específicas** com passos de implementação
3. **Priorização** baseada no impacto vs esforço
4. **Timeline estimado** para cada recomendação
5. **Métricas esperadas** após implementação

Use seu conhecimento especializado em performance web para fornecer insights técnicos precisos e acionáveis.
"""

            # Executar análise com o agente Agno
            analysis_response = self.agent.run(analysis_prompt)

            # Estruturar resultado
            result = {
                "agent_type": "PerformanceAgent",
                "lighthouse_metrics": lighthouse_metrics.dict(),
                "critical_issues": critical_issues,
                "priority_score": priority_score,
                "agent_analysis": analysis_response.content,
                "company_context": company_info,
                "analysis_timestamp": "2024-01-01T00:00:00Z"  # Placeholder
            }

            logger.info(
                f"Análise de performance concluída - Score: {lighthouse_metrics.performance_score}")
            return result

        except Exception as e:
            logger.error(f"Erro na análise de performance: {str(e)}")
            raise

    def get_quick_wins(self, lighthouse_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Identifica quick wins para performance

        Args:
            lighthouse_data: Dados do Lighthouse

        Returns:
            Lista de quick wins priorizadas
        """
        try:
            lighthouse_metrics = self.lighthouse_tools.analyze_lighthouse_data(
                lighthouse_data)

            quick_wins_prompt = f"""
Baseado nestes dados de performance, identifique 3-5 "quick wins" (melhorias rápidas e de alto impacto):

Performance Score: {lighthouse_metrics.performance_score}/100
Core Web Vitals:
- LCP: {lighthouse_metrics.lcp}ms
- CLS: {lighthouse_metrics.cls}  
- FCP: {lighthouse_metrics.fcp}ms

Oportunidades detectadas:
{chr(10).join(f"- {opp}" for opp in lighthouse_metrics.opportunities)}

Para cada quick win, forneça:
1. Nome da otimização
2. Impacto esperado (baixo/médio/alto)
3. Esforço necessário (1-5 dias)
4. Passos específicos de implementação

Foque em melhorias que podem ser implementadas em menos de 1 semana com impacto significativo.
"""

            response = self.agent.run(quick_wins_prompt)

            return {
                "quick_wins_analysis": response.content,
                "baseline_score": lighthouse_metrics.performance_score,
                "focus_areas": ["LCP", "CLS", "FCP"] if lighthouse_metrics.performance_score < 70 else ["Otimizações finas"]
            }

        except Exception as e:
            logger.error(f"Erro ao identificar quick wins: {str(e)}")
            return {"error": str(e)}

    def benchmark_against_industry(self, lighthouse_data: Dict[str, Any], industry: str = "e-commerce") -> Dict[str, Any]:
        """
        Compara performance com benchmarks da indústria

        Args:
            lighthouse_data: Dados do Lighthouse
            industry: Setor da empresa

        Returns:
            Comparação com benchmarks
        """
        try:
            lighthouse_metrics = self.lighthouse_tools.analyze_lighthouse_data(
                lighthouse_data)

            benchmark_prompt = f"""
Compare estas métricas de performance com benchmarks típicos da indústria {industry}:

**MÉTRICAS ATUAIS:**
- Performance Score: {lighthouse_metrics.performance_score}/100
- LCP: {lighthouse_metrics.lcp}ms
- FCP: {lighthouse_metrics.fcp}ms
- CLS: {lighthouse_metrics.cls}

Forneça:
1. **Posicionamento vs indústria** (percentil estimado)
2. **Gaps críticos** em relação aos líderes do setor
3. **Metas realistas** para alcançar top 25% da indústria
4. **Impacto no negócio** de melhorar para o benchmark do setor

Base sua análise em conhecimento de benchmarks típicos para o setor {industry}.
"""

            response = self.agent.run(benchmark_prompt)

            return {
                "benchmark_analysis": response.content,
                "current_metrics": lighthouse_metrics.dict(),
                "industry": industry
            }

        except Exception as e:
            logger.error(f"Erro no benchmark: {str(e)}")
            return {"error": str(e)}
