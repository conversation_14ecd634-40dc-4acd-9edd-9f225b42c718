{"version": 3, "sources": ["../../../../../../node_modules/dagre-d3-es/src/graphlib/json.js", "../../../../../../node_modules/mermaid/dist/chunks/mermaid.core/dagre-OKDRZEBW.mjs"], "sourcesContent": ["import * as _ from 'lodash-es';\nimport { Graph } from './graph.js';\nexport { write, read };\nfunction write(g) {\n  var json = {\n    options: {\n      directed: g.isDirected(),\n      multigraph: g.isMultigraph(),\n      compound: g.isCompound()\n    },\n    nodes: writeNodes(g),\n    edges: writeEdges(g)\n  };\n  if (!_.isUndefined(g.graph())) {\n    json.value = _.clone(g.graph());\n  }\n  return json;\n}\nfunction writeNodes(g) {\n  return _.map(g.nodes(), function (v) {\n    var nodeValue = g.node(v);\n    var parent = g.parent(v);\n    var node = {\n      v: v\n    };\n    if (!_.isUndefined(nodeValue)) {\n      node.value = nodeValue;\n    }\n    if (!_.isUndefined(parent)) {\n      node.parent = parent;\n    }\n    return node;\n  });\n}\nfunction writeEdges(g) {\n  return _.map(g.edges(), function (e) {\n    var edgeValue = g.edge(e);\n    var edge = {\n      v: e.v,\n      w: e.w\n    };\n    if (!_.isUndefined(e.name)) {\n      edge.name = e.name;\n    }\n    if (!_.isUndefined(edgeValue)) {\n      edge.value = edgeValue;\n    }\n    return edge;\n  });\n}\nfunction read(json) {\n  var g = new Graph(json.options).setGraph(json.value);\n  _.each(json.nodes, function (entry) {\n    g.setNode(entry.v, entry.value);\n    if (entry.parent) {\n      g.setParent(entry.v, entry.parent);\n    }\n  });\n  _.each(json.edges, function (entry) {\n    g.setEdge({\n      v: entry.v,\n      w: entry.w,\n      name: entry.name\n    }, entry.value);\n  });\n  return g;\n}", "import { clear as clear2, insertEdge, insertEdgeLabel, markers_default, positionEdgeLabel } from \"./chunk-IIMUDSI4.mjs\";\nimport \"./chunk-VV3M67IP.mjs\";\nimport { clear, clear2 as clear3, insertCluster, insertNode, positionNode, setNodeElem, updateNodeBounds } from \"./chunk-HRU6DDCH.mjs\";\nimport { getSubGraphTitleMargins } from \"./chunk-K557N5IZ.mjs\";\nimport \"./chunk-H2D2JQ3I.mjs\";\nimport \"./chunk-C3MQ5ANM.mjs\";\nimport \"./chunk-O4NI6UNU.mjs\";\nimport { __name, getConfig2 as getConfig, log } from \"./chunk-YTJNT7DU.mjs\";\n\n// src/rendering-util/layout-algorithms/dagre/index.js\nimport { layout as dagreLayout } from \"dagre-d3-es/src/dagre/index.js\";\nimport * as graphlibJson2 from \"dagre-d3-es/src/graphlib/json.js\";\nimport * as graphlib2 from \"dagre-d3-es/src/graphlib/index.js\";\n\n// src/rendering-util/layout-algorithms/dagre/mermaid-graphlib.js\nimport * as graphlib from \"dagre-d3-es/src/graphlib/index.js\";\nimport * as graphlibJson from \"dagre-d3-es/src/graphlib/json.js\";\nvar clusterDb = /* @__PURE__ */new Map();\nvar descendants = /* @__PURE__ */new Map();\nvar parents = /* @__PURE__ */new Map();\nvar clear4 = /* @__PURE__ */__name(() => {\n  descendants.clear();\n  parents.clear();\n  clusterDb.clear();\n}, \"clear\");\nvar isDescendant = /* @__PURE__ */__name((id, ancestorId) => {\n  const ancestorDescendants = descendants.get(ancestorId) || [];\n  log.trace(\"In isDescendant\", ancestorId, \" \", id, \" = \", ancestorDescendants.includes(id));\n  return ancestorDescendants.includes(id);\n}, \"isDescendant\");\nvar edgeInCluster = /* @__PURE__ */__name((edge, clusterId) => {\n  const clusterDescendants = descendants.get(clusterId) || [];\n  log.info(\"Descendants of \", clusterId, \" is \", clusterDescendants);\n  log.info(\"Edge is \", edge);\n  if (edge.v === clusterId || edge.w === clusterId) {\n    return false;\n  }\n  if (!clusterDescendants) {\n    log.debug(\"Tilt, \", clusterId, \",not in descendants\");\n    return false;\n  }\n  return clusterDescendants.includes(edge.v) || isDescendant(edge.v, clusterId) || isDescendant(edge.w, clusterId) || clusterDescendants.includes(edge.w);\n}, \"edgeInCluster\");\nvar copy = /* @__PURE__ */__name((clusterId, graph, newGraph, rootId) => {\n  log.warn(\"Copying children of \", clusterId, \"root\", rootId, \"data\", graph.node(clusterId), rootId);\n  const nodes = graph.children(clusterId) || [];\n  if (clusterId !== rootId) {\n    nodes.push(clusterId);\n  }\n  log.warn(\"Copying (nodes) clusterId\", clusterId, \"nodes\", nodes);\n  nodes.forEach(node => {\n    if (graph.children(node).length > 0) {\n      copy(node, graph, newGraph, rootId);\n    } else {\n      const data = graph.node(node);\n      log.info(\"cp \", node, \" to \", rootId, \" with parent \", clusterId);\n      newGraph.setNode(node, data);\n      if (rootId !== graph.parent(node)) {\n        log.warn(\"Setting parent\", node, graph.parent(node));\n        newGraph.setParent(node, graph.parent(node));\n      }\n      if (clusterId !== rootId && node !== clusterId) {\n        log.debug(\"Setting parent\", node, clusterId);\n        newGraph.setParent(node, clusterId);\n      } else {\n        log.info(\"In copy \", clusterId, \"root\", rootId, \"data\", graph.node(clusterId), rootId);\n        log.debug(\"Not Setting parent for node=\", node, \"cluster!==rootId\", clusterId !== rootId, \"node!==clusterId\", node !== clusterId);\n      }\n      const edges = graph.edges(node);\n      log.debug(\"Copying Edges\", edges);\n      edges.forEach(edge => {\n        log.info(\"Edge\", edge);\n        const data2 = graph.edge(edge.v, edge.w, edge.name);\n        log.info(\"Edge data\", data2, rootId);\n        try {\n          if (edgeInCluster(edge, rootId)) {\n            log.info(\"Copying as \", edge.v, edge.w, data2, edge.name);\n            newGraph.setEdge(edge.v, edge.w, data2, edge.name);\n            log.info(\"newGraph edges \", newGraph.edges(), newGraph.edge(newGraph.edges()[0]));\n          } else {\n            log.info(\"Skipping copy of edge \", edge.v, \"-->\", edge.w, \" rootId: \", rootId, \" clusterId:\", clusterId);\n          }\n        } catch (e) {\n          log.error(e);\n        }\n      });\n    }\n    log.debug(\"Removing node\", node);\n    graph.removeNode(node);\n  });\n}, \"copy\");\nvar extractDescendants = /* @__PURE__ */__name((id, graph) => {\n  const children = graph.children(id);\n  let res = [...children];\n  for (const child of children) {\n    parents.set(child, id);\n    res = [...res, ...extractDescendants(child, graph)];\n  }\n  return res;\n}, \"extractDescendants\");\nvar findCommonEdges = /* @__PURE__ */__name((graph, id1, id2) => {\n  const edges1 = graph.edges().filter(edge => edge.v === id1 || edge.w === id1);\n  const edges2 = graph.edges().filter(edge => edge.v === id2 || edge.w === id2);\n  const edges1Prim = edges1.map(edge => {\n    return {\n      v: edge.v === id1 ? id2 : edge.v,\n      w: edge.w === id1 ? id1 : edge.w\n    };\n  });\n  const edges2Prim = edges2.map(edge => {\n    return {\n      v: edge.v,\n      w: edge.w\n    };\n  });\n  const result = edges1Prim.filter(edgeIn1 => {\n    return edges2Prim.some(edge => edgeIn1.v === edge.v && edgeIn1.w === edge.w);\n  });\n  return result;\n}, \"findCommonEdges\");\nvar findNonClusterChild = /* @__PURE__ */__name((id, graph, clusterId) => {\n  const children = graph.children(id);\n  log.trace(\"Searching children of id \", id, children);\n  if (children.length < 1) {\n    return id;\n  }\n  let reserve;\n  for (const child of children) {\n    const _id = findNonClusterChild(child, graph, clusterId);\n    const commonEdges = findCommonEdges(graph, clusterId, _id);\n    if (_id) {\n      if (commonEdges.length > 0) {\n        reserve = _id;\n      } else {\n        return _id;\n      }\n    }\n  }\n  return reserve;\n}, \"findNonClusterChild\");\nvar getAnchorId = /* @__PURE__ */__name(id => {\n  if (!clusterDb.has(id)) {\n    return id;\n  }\n  if (!clusterDb.get(id).externalConnections) {\n    return id;\n  }\n  if (clusterDb.has(id)) {\n    return clusterDb.get(id).id;\n  }\n  return id;\n}, \"getAnchorId\");\nvar adjustClustersAndEdges = /* @__PURE__ */__name((graph, depth) => {\n  if (!graph || depth > 10) {\n    log.debug(\"Opting out, no graph \");\n    return;\n  } else {\n    log.debug(\"Opting in, graph \");\n  }\n  graph.nodes().forEach(function (id) {\n    const children = graph.children(id);\n    if (children.length > 0) {\n      log.warn(\"Cluster identified\", id, \" Replacement id in edges: \", findNonClusterChild(id, graph, id));\n      descendants.set(id, extractDescendants(id, graph));\n      clusterDb.set(id, {\n        id: findNonClusterChild(id, graph, id),\n        clusterData: graph.node(id)\n      });\n    }\n  });\n  graph.nodes().forEach(function (id) {\n    const children = graph.children(id);\n    const edges = graph.edges();\n    if (children.length > 0) {\n      log.debug(\"Cluster identified\", id, descendants);\n      edges.forEach(edge => {\n        const d1 = isDescendant(edge.v, id);\n        const d2 = isDescendant(edge.w, id);\n        if (d1 ^ d2) {\n          log.warn(\"Edge: \", edge, \" leaves cluster \", id);\n          log.warn(\"Descendants of XXX \", id, \": \", descendants.get(id));\n          clusterDb.get(id).externalConnections = true;\n        }\n      });\n    } else {\n      log.debug(\"Not a cluster \", id, descendants);\n    }\n  });\n  for (let id of clusterDb.keys()) {\n    const nonClusterChild = clusterDb.get(id).id;\n    const parent = graph.parent(nonClusterChild);\n    if (parent !== id && clusterDb.has(parent) && !clusterDb.get(parent).externalConnections) {\n      clusterDb.get(id).id = parent;\n    }\n  }\n  graph.edges().forEach(function (e) {\n    const edge = graph.edge(e);\n    log.warn(\"Edge \" + e.v + \" -> \" + e.w + \": \" + JSON.stringify(e));\n    log.warn(\"Edge \" + e.v + \" -> \" + e.w + \": \" + JSON.stringify(graph.edge(e)));\n    let v = e.v;\n    let w = e.w;\n    log.warn(\"Fix XXX\", clusterDb, \"ids:\", e.v, e.w, \"Translating: \", clusterDb.get(e.v), \" --- \", clusterDb.get(e.w));\n    if (clusterDb.get(e.v) || clusterDb.get(e.w)) {\n      log.warn(\"Fixing and trying - removing XXX\", e.v, e.w, e.name);\n      v = getAnchorId(e.v);\n      w = getAnchorId(e.w);\n      graph.removeEdge(e.v, e.w, e.name);\n      if (v !== e.v) {\n        const parent = graph.parent(v);\n        clusterDb.get(parent).externalConnections = true;\n        edge.fromCluster = e.v;\n      }\n      if (w !== e.w) {\n        const parent = graph.parent(w);\n        clusterDb.get(parent).externalConnections = true;\n        edge.toCluster = e.w;\n      }\n      log.warn(\"Fix Replacing with XXX\", v, w, e.name);\n      graph.setEdge(v, w, edge, e.name);\n    }\n  });\n  log.warn(\"Adjusted Graph\", graphlibJson.write(graph));\n  extractor(graph, 0);\n  log.trace(clusterDb);\n}, \"adjustClustersAndEdges\");\nvar extractor = /* @__PURE__ */__name((graph, depth) => {\n  log.warn(\"extractor - \", depth, graphlibJson.write(graph), graph.children(\"D\"));\n  if (depth > 10) {\n    log.error(\"Bailing out\");\n    return;\n  }\n  let nodes = graph.nodes();\n  let hasChildren = false;\n  for (const node of nodes) {\n    const children = graph.children(node);\n    hasChildren = hasChildren || children.length > 0;\n  }\n  if (!hasChildren) {\n    log.debug(\"Done, no node has children\", graph.nodes());\n    return;\n  }\n  log.debug(\"Nodes = \", nodes, depth);\n  for (const node of nodes) {\n    log.debug(\"Extracting node\", node, clusterDb, clusterDb.has(node) && !clusterDb.get(node).externalConnections, !graph.parent(node), graph.node(node), graph.children(\"D\"), \" Depth \", depth);\n    if (!clusterDb.has(node)) {\n      log.debug(\"Not a cluster\", node, depth);\n    } else if (!clusterDb.get(node).externalConnections && graph.children(node) && graph.children(node).length > 0) {\n      log.warn(\"Cluster without external connections, without a parent and with children\", node, depth);\n      const graphSettings = graph.graph();\n      let dir = graphSettings.rankdir === \"TB\" ? \"LR\" : \"TB\";\n      if (clusterDb.get(node)?.clusterData?.dir) {\n        dir = clusterDb.get(node).clusterData.dir;\n        log.warn(\"Fixing dir\", clusterDb.get(node).clusterData.dir, dir);\n      }\n      const clusterGraph = new graphlib.Graph({\n        multigraph: true,\n        compound: true\n      }).setGraph({\n        rankdir: dir,\n        nodesep: 50,\n        ranksep: 50,\n        marginx: 8,\n        marginy: 8\n      }).setDefaultEdgeLabel(function () {\n        return {};\n      });\n      log.warn(\"Old graph before copy\", graphlibJson.write(graph));\n      copy(node, graph, clusterGraph, node);\n      graph.setNode(node, {\n        clusterNode: true,\n        id: node,\n        clusterData: clusterDb.get(node).clusterData,\n        label: clusterDb.get(node).label,\n        graph: clusterGraph\n      });\n      log.warn(\"New graph after copy node: (\", node, \")\", graphlibJson.write(clusterGraph));\n      log.debug(\"Old graph after copy\", graphlibJson.write(graph));\n    } else {\n      log.warn(\"Cluster ** \", node, \" **not meeting the criteria !externalConnections:\", !clusterDb.get(node).externalConnections, \" no parent: \", !graph.parent(node), \" children \", graph.children(node) && graph.children(node).length > 0, graph.children(\"D\"), depth);\n      log.debug(clusterDb);\n    }\n  }\n  nodes = graph.nodes();\n  log.warn(\"New list of nodes\", nodes);\n  for (const node of nodes) {\n    const data = graph.node(node);\n    log.warn(\" Now next level\", node, data);\n    if (data?.clusterNode) {\n      extractor(data.graph, depth + 1);\n    }\n  }\n}, \"extractor\");\nvar sorter = /* @__PURE__ */__name((graph, nodes) => {\n  if (nodes.length === 0) {\n    return [];\n  }\n  let result = Object.assign([], nodes);\n  nodes.forEach(node => {\n    const children = graph.children(node);\n    const sorted = sorter(graph, children);\n    result = [...result, ...sorted];\n  });\n  return result;\n}, \"sorter\");\nvar sortNodesByHierarchy = /* @__PURE__ */__name(graph => sorter(graph, graph.children()), \"sortNodesByHierarchy\");\n\n// src/rendering-util/layout-algorithms/dagre/index.js\nvar recursiveRender = /* @__PURE__ */__name(async (_elem, graph, diagramType, id, parentCluster, siteConfig) => {\n  log.warn(\"Graph in recursive render:XAX\", graphlibJson2.write(graph), parentCluster);\n  const dir = graph.graph().rankdir;\n  log.trace(\"Dir in recursive render - dir:\", dir);\n  const elem = _elem.insert(\"g\").attr(\"class\", \"root\");\n  if (!graph.nodes()) {\n    log.info(\"No nodes found for\", graph);\n  } else {\n    log.info(\"Recursive render XXX\", graph.nodes());\n  }\n  if (graph.edges().length > 0) {\n    log.info(\"Recursive edges\", graph.edge(graph.edges()[0]));\n  }\n  const clusters = elem.insert(\"g\").attr(\"class\", \"clusters\");\n  const edgePaths = elem.insert(\"g\").attr(\"class\", \"edgePaths\");\n  const edgeLabels = elem.insert(\"g\").attr(\"class\", \"edgeLabels\");\n  const nodes = elem.insert(\"g\").attr(\"class\", \"nodes\");\n  await Promise.all(graph.nodes().map(async function (v) {\n    const node = graph.node(v);\n    if (parentCluster !== void 0) {\n      const data = JSON.parse(JSON.stringify(parentCluster.clusterData));\n      log.trace(\"Setting data for parent cluster XXX\\n Node.id = \", v, \"\\n data=\", data.height, \"\\nParent cluster\", parentCluster.height);\n      graph.setNode(parentCluster.id, data);\n      if (!graph.parent(v)) {\n        log.trace(\"Setting parent\", v, parentCluster.id);\n        graph.setParent(v, parentCluster.id, data);\n      }\n    }\n    log.info(\"(Insert) Node XXX\" + v + \": \" + JSON.stringify(graph.node(v)));\n    if (node?.clusterNode) {\n      log.info(\"Cluster identified XBX\", v, node.width, graph.node(v));\n      const {\n        ranksep,\n        nodesep\n      } = graph.graph();\n      node.graph.setGraph({\n        ...node.graph.graph(),\n        ranksep: ranksep + 25,\n        nodesep\n      });\n      const o = await recursiveRender(nodes, node.graph, diagramType, id, graph.node(v), siteConfig);\n      const newEl = o.elem;\n      updateNodeBounds(node, newEl);\n      node.diff = o.diff || 0;\n      log.info(\"New compound node after recursive render XAX\", v, \"width\",\n      // node,\n      node.width, \"height\", node.height\n      // node.x,\n      // node.y\n      );\n      setNodeElem(newEl, node);\n    } else {\n      if (graph.children(v).length > 0) {\n        log.trace(\"Cluster - the non recursive path XBX\", v, node.id, node, node.width, \"Graph:\", graph);\n        log.trace(findNonClusterChild(node.id, graph));\n        clusterDb.set(node.id, {\n          id: findNonClusterChild(node.id, graph),\n          node\n        });\n      } else {\n        log.trace(\"Node - the non recursive path XAX\", v, nodes, graph.node(v), dir);\n        await insertNode(nodes, graph.node(v), {\n          config: siteConfig,\n          dir\n        });\n      }\n    }\n  }));\n  const processEdges = /* @__PURE__ */__name(async () => {\n    const edgePromises = graph.edges().map(async function (e) {\n      const edge = graph.edge(e.v, e.w, e.name);\n      log.info(\"Edge \" + e.v + \" -> \" + e.w + \": \" + JSON.stringify(e));\n      log.info(\"Edge \" + e.v + \" -> \" + e.w + \": \", e, \" \", JSON.stringify(graph.edge(e)));\n      log.info(\"Fix\", clusterDb, \"ids:\", e.v, e.w, \"Translating: \", clusterDb.get(e.v), clusterDb.get(e.w));\n      await insertEdgeLabel(edgeLabels, edge);\n    });\n    await Promise.all(edgePromises);\n  }, \"processEdges\");\n  await processEdges();\n  log.info(\"Graph before layout:\", JSON.stringify(graphlibJson2.write(graph)));\n  log.info(\"############################################# XXX\");\n  log.info(\"###                Layout                 ### XXX\");\n  log.info(\"############################################# XXX\");\n  dagreLayout(graph);\n  log.info(\"Graph after layout:\", JSON.stringify(graphlibJson2.write(graph)));\n  let diff = 0;\n  let {\n    subGraphTitleTotalMargin\n  } = getSubGraphTitleMargins(siteConfig);\n  await Promise.all(sortNodesByHierarchy(graph).map(async function (v) {\n    const node = graph.node(v);\n    log.info(\"Position XBX => \" + v + \": (\" + node.x, \",\" + node.y, \") width: \", node.width, \" height: \", node.height);\n    if (node?.clusterNode) {\n      node.y += subGraphTitleTotalMargin;\n      log.info(\"A tainted cluster node XBX1\", v, node.id, node.width, node.height, node.x, node.y, graph.parent(v));\n      clusterDb.get(node.id).node = node;\n      positionNode(node);\n    } else {\n      if (graph.children(v).length > 0) {\n        log.info(\"A pure cluster node XBX1\", v, node.id, node.x, node.y, node.width, node.height, graph.parent(v));\n        node.height += subGraphTitleTotalMargin;\n        graph.node(node.parentId);\n        const halfPadding = node?.padding / 2 || 0;\n        const labelHeight = node?.labelBBox?.height || 0;\n        const offsetY = labelHeight - halfPadding || 0;\n        log.debug(\"OffsetY\", offsetY, \"labelHeight\", labelHeight, \"halfPadding\", halfPadding);\n        await insertCluster(clusters, node);\n        clusterDb.get(node.id).node = node;\n      } else {\n        const parent = graph.node(node.parentId);\n        node.y += subGraphTitleTotalMargin / 2;\n        log.info(\"A regular node XBX1 - using the padding\", node.id, \"parent\", node.parentId, node.width, node.height, node.x, node.y, \"offsetY\", node.offsetY, \"parent\", parent, parent?.offsetY, node);\n        positionNode(node);\n      }\n    }\n  }));\n  graph.edges().forEach(function (e) {\n    const edge = graph.edge(e);\n    log.info(\"Edge \" + e.v + \" -> \" + e.w + \": \" + JSON.stringify(edge), edge);\n    edge.points.forEach(point => point.y += subGraphTitleTotalMargin / 2);\n    const startNode = graph.node(e.v);\n    var endNode = graph.node(e.w);\n    const paths = insertEdge(edgePaths, edge, clusterDb, diagramType, startNode, endNode, id);\n    positionEdgeLabel(edge, paths);\n  });\n  graph.nodes().forEach(function (v) {\n    const n = graph.node(v);\n    log.info(v, n.type, n.diff);\n    if (n.isGroup) {\n      diff = n.diff;\n    }\n  });\n  log.warn(\"Returning from recursive render XAX\", elem, diff);\n  return {\n    elem,\n    diff\n  };\n}, \"recursiveRender\");\nvar render = /* @__PURE__ */__name(async (data4Layout, svg) => {\n  const graph = new graphlib2.Graph({\n    multigraph: true,\n    compound: true\n  }).setGraph({\n    rankdir: data4Layout.direction,\n    nodesep: data4Layout.config?.nodeSpacing || data4Layout.config?.flowchart?.nodeSpacing || data4Layout.nodeSpacing,\n    ranksep: data4Layout.config?.rankSpacing || data4Layout.config?.flowchart?.rankSpacing || data4Layout.rankSpacing,\n    marginx: 8,\n    marginy: 8\n  }).setDefaultEdgeLabel(function () {\n    return {};\n  });\n  const element = svg.select(\"g\");\n  markers_default(element, data4Layout.markers, data4Layout.type, data4Layout.diagramId);\n  clear3();\n  clear2();\n  clear();\n  clear4();\n  data4Layout.nodes.forEach(node => {\n    graph.setNode(node.id, {\n      ...node\n    });\n    if (node.parentId) {\n      graph.setParent(node.id, node.parentId);\n    }\n  });\n  log.debug(\"Edges:\", data4Layout.edges);\n  data4Layout.edges.forEach(edge => {\n    if (edge.start === edge.end) {\n      const nodeId = edge.start;\n      const specialId1 = nodeId + \"---\" + nodeId + \"---1\";\n      const specialId2 = nodeId + \"---\" + nodeId + \"---2\";\n      const node = graph.node(nodeId);\n      graph.setNode(specialId1, {\n        domId: specialId1,\n        id: specialId1,\n        parentId: node.parentId,\n        labelStyle: \"\",\n        label: \"\",\n        padding: 0,\n        shape: \"labelRect\",\n        // shape: 'rect',\n        style: \"\",\n        width: 10,\n        height: 10\n      });\n      graph.setParent(specialId1, node.parentId);\n      graph.setNode(specialId2, {\n        domId: specialId2,\n        id: specialId2,\n        parentId: node.parentId,\n        labelStyle: \"\",\n        padding: 0,\n        // shape: 'rect',\n        shape: \"labelRect\",\n        label: \"\",\n        style: \"\",\n        width: 10,\n        height: 10\n      });\n      graph.setParent(specialId2, node.parentId);\n      const edge1 = structuredClone(edge);\n      const edgeMid = structuredClone(edge);\n      const edge2 = structuredClone(edge);\n      edge1.label = \"\";\n      edge1.arrowTypeEnd = \"none\";\n      edge1.id = nodeId + \"-cyclic-special-1\";\n      edgeMid.arrowTypeStart = \"none\";\n      edgeMid.arrowTypeEnd = \"none\";\n      edgeMid.id = nodeId + \"-cyclic-special-mid\";\n      edge2.label = \"\";\n      if (node.isGroup) {\n        edge1.fromCluster = nodeId;\n        edge2.toCluster = nodeId;\n      }\n      edge2.id = nodeId + \"-cyclic-special-2\";\n      edge2.arrowTypeStart = \"none\";\n      graph.setEdge(nodeId, specialId1, edge1, nodeId + \"-cyclic-special-0\");\n      graph.setEdge(specialId1, specialId2, edgeMid, nodeId + \"-cyclic-special-1\");\n      graph.setEdge(specialId2, nodeId, edge2, nodeId + \"-cyc<lic-special-2\");\n    } else {\n      graph.setEdge(edge.start, edge.end, {\n        ...edge\n      }, edge.id);\n    }\n  });\n  log.warn(\"Graph at first:\", JSON.stringify(graphlibJson2.write(graph)));\n  adjustClustersAndEdges(graph);\n  log.warn(\"Graph after XAX:\", JSON.stringify(graphlibJson2.write(graph)));\n  const siteConfig = getConfig();\n  await recursiveRender(element, graph, data4Layout.type, data4Layout.diagramId, void 0, siteConfig);\n}, \"render\");\nexport { render };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,SAAS,MAAM,GAAG;AAChB,MAAI,OAAO;AAAA,IACT,SAAS;AAAA,MACP,UAAU,EAAE,WAAW;AAAA,MACvB,YAAY,EAAE,aAAa;AAAA,MAC3B,UAAU,EAAE,WAAW;AAAA,IACzB;AAAA,IACA,OAAO,WAAW,CAAC;AAAA,IACnB,OAAO,WAAW,CAAC;AAAA,EACrB;AACA,MAAI,CAAG,oBAAY,EAAE,MAAM,CAAC,GAAG;AAC7B,SAAK,QAAU,cAAM,EAAE,MAAM,CAAC;AAAA,EAChC;AACA,SAAO;AACT;AACA,SAAS,WAAW,GAAG;AACrB,SAAS,YAAI,EAAE,MAAM,GAAG,SAAU,GAAG;AACnC,QAAI,YAAY,EAAE,KAAK,CAAC;AACxB,QAAI,SAAS,EAAE,OAAO,CAAC;AACvB,QAAI,OAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI,CAAG,oBAAY,SAAS,GAAG;AAC7B,WAAK,QAAQ;AAAA,IACf;AACA,QAAI,CAAG,oBAAY,MAAM,GAAG;AAC1B,WAAK,SAAS;AAAA,IAChB;AACA,WAAO;AAAA,EACT,CAAC;AACH;AACA,SAAS,WAAW,GAAG;AACrB,SAAS,YAAI,EAAE,MAAM,GAAG,SAAU,GAAG;AACnC,QAAI,YAAY,EAAE,KAAK,CAAC;AACxB,QAAI,OAAO;AAAA,MACT,GAAG,EAAE;AAAA,MACL,GAAG,EAAE;AAAA,IACP;AACA,QAAI,CAAG,oBAAY,EAAE,IAAI,GAAG;AAC1B,WAAK,OAAO,EAAE;AAAA,IAChB;AACA,QAAI,CAAG,oBAAY,SAAS,GAAG;AAC7B,WAAK,QAAQ;AAAA,IACf;AACA,WAAO;AAAA,EACT,CAAC;AACH;;;AChCA,IAAI,YAA2B,oBAAI,IAAI;AACvC,IAAI,cAA6B,oBAAI,IAAI;AACzC,IAAI,UAAyB,oBAAI,IAAI;AACrC,IAAI,SAAwB,OAAO,MAAM;AACvC,cAAY,MAAM;AAClB,UAAQ,MAAM;AACd,YAAU,MAAM;AAClB,GAAG,OAAO;AACV,IAAI,eAA8B,OAAO,CAAC,IAAI,eAAe;AAC3D,QAAM,sBAAsB,YAAY,IAAI,UAAU,KAAK,CAAC;AAC5D,MAAI,MAAM,mBAAmB,YAAY,KAAK,IAAI,OAAO,oBAAoB,SAAS,EAAE,CAAC;AACzF,SAAO,oBAAoB,SAAS,EAAE;AACxC,GAAG,cAAc;AACjB,IAAI,gBAA+B,OAAO,CAAC,MAAM,cAAc;AAC7D,QAAM,qBAAqB,YAAY,IAAI,SAAS,KAAK,CAAC;AAC1D,MAAI,KAAK,mBAAmB,WAAW,QAAQ,kBAAkB;AACjE,MAAI,KAAK,YAAY,IAAI;AACzB,MAAI,KAAK,MAAM,aAAa,KAAK,MAAM,WAAW;AAChD,WAAO;AAAA,EACT;AACA,MAAI,CAAC,oBAAoB;AACvB,QAAI,MAAM,UAAU,WAAW,qBAAqB;AACpD,WAAO;AAAA,EACT;AACA,SAAO,mBAAmB,SAAS,KAAK,CAAC,KAAK,aAAa,KAAK,GAAG,SAAS,KAAK,aAAa,KAAK,GAAG,SAAS,KAAK,mBAAmB,SAAS,KAAK,CAAC;AACxJ,GAAG,eAAe;AAClB,IAAI,OAAsB,OAAO,CAAC,WAAW,OAAO,UAAU,WAAW;AACvE,MAAI,KAAK,wBAAwB,WAAW,QAAQ,QAAQ,QAAQ,MAAM,KAAK,SAAS,GAAG,MAAM;AACjG,QAAM,QAAQ,MAAM,SAAS,SAAS,KAAK,CAAC;AAC5C,MAAI,cAAc,QAAQ;AACxB,UAAM,KAAK,SAAS;AAAA,EACtB;AACA,MAAI,KAAK,6BAA6B,WAAW,SAAS,KAAK;AAC/D,QAAM,QAAQ,UAAQ;AACpB,QAAI,MAAM,SAAS,IAAI,EAAE,SAAS,GAAG;AACnC,WAAK,MAAM,OAAO,UAAU,MAAM;AAAA,IACpC,OAAO;AACL,YAAM,OAAO,MAAM,KAAK,IAAI;AAC5B,UAAI,KAAK,OAAO,MAAM,QAAQ,QAAQ,iBAAiB,SAAS;AAChE,eAAS,QAAQ,MAAM,IAAI;AAC3B,UAAI,WAAW,MAAM,OAAO,IAAI,GAAG;AACjC,YAAI,KAAK,kBAAkB,MAAM,MAAM,OAAO,IAAI,CAAC;AACnD,iBAAS,UAAU,MAAM,MAAM,OAAO,IAAI,CAAC;AAAA,MAC7C;AACA,UAAI,cAAc,UAAU,SAAS,WAAW;AAC9C,YAAI,MAAM,kBAAkB,MAAM,SAAS;AAC3C,iBAAS,UAAU,MAAM,SAAS;AAAA,MACpC,OAAO;AACL,YAAI,KAAK,YAAY,WAAW,QAAQ,QAAQ,QAAQ,MAAM,KAAK,SAAS,GAAG,MAAM;AACrF,YAAI,MAAM,gCAAgC,MAAM,oBAAoB,cAAc,QAAQ,oBAAoB,SAAS,SAAS;AAAA,MAClI;AACA,YAAM,QAAQ,MAAM,MAAM,IAAI;AAC9B,UAAI,MAAM,iBAAiB,KAAK;AAChC,YAAM,QAAQ,UAAQ;AACpB,YAAI,KAAK,QAAQ,IAAI;AACrB,cAAM,QAAQ,MAAM,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI;AAClD,YAAI,KAAK,aAAa,OAAO,MAAM;AACnC,YAAI;AACF,cAAI,cAAc,MAAM,MAAM,GAAG;AAC/B,gBAAI,KAAK,eAAe,KAAK,GAAG,KAAK,GAAG,OAAO,KAAK,IAAI;AACxD,qBAAS,QAAQ,KAAK,GAAG,KAAK,GAAG,OAAO,KAAK,IAAI;AACjD,gBAAI,KAAK,mBAAmB,SAAS,MAAM,GAAG,SAAS,KAAK,SAAS,MAAM,EAAE,CAAC,CAAC,CAAC;AAAA,UAClF,OAAO;AACL,gBAAI,KAAK,0BAA0B,KAAK,GAAG,OAAO,KAAK,GAAG,aAAa,QAAQ,eAAe,SAAS;AAAA,UACzG;AAAA,QACF,SAAS,GAAG;AACV,cAAI,MAAM,CAAC;AAAA,QACb;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAI,MAAM,iBAAiB,IAAI;AAC/B,UAAM,WAAW,IAAI;AAAA,EACvB,CAAC;AACH,GAAG,MAAM;AACT,IAAI,qBAAoC,OAAO,CAAC,IAAI,UAAU;AAC5D,QAAM,WAAW,MAAM,SAAS,EAAE;AAClC,MAAI,MAAM,CAAC,GAAG,QAAQ;AACtB,aAAW,SAAS,UAAU;AAC5B,YAAQ,IAAI,OAAO,EAAE;AACrB,UAAM,CAAC,GAAG,KAAK,GAAG,mBAAmB,OAAO,KAAK,CAAC;AAAA,EACpD;AACA,SAAO;AACT,GAAG,oBAAoB;AACvB,IAAI,kBAAiC,OAAO,CAAC,OAAO,KAAK,QAAQ;AAC/D,QAAM,SAAS,MAAM,MAAM,EAAE,OAAO,UAAQ,KAAK,MAAM,OAAO,KAAK,MAAM,GAAG;AAC5E,QAAM,SAAS,MAAM,MAAM,EAAE,OAAO,UAAQ,KAAK,MAAM,OAAO,KAAK,MAAM,GAAG;AAC5E,QAAM,aAAa,OAAO,IAAI,UAAQ;AACpC,WAAO;AAAA,MACL,GAAG,KAAK,MAAM,MAAM,MAAM,KAAK;AAAA,MAC/B,GAAG,KAAK,MAAM,MAAM,MAAM,KAAK;AAAA,IACjC;AAAA,EACF,CAAC;AACD,QAAM,aAAa,OAAO,IAAI,UAAQ;AACpC,WAAO;AAAA,MACL,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,IACV;AAAA,EACF,CAAC;AACD,QAAM,SAAS,WAAW,OAAO,aAAW;AAC1C,WAAO,WAAW,KAAK,UAAQ,QAAQ,MAAM,KAAK,KAAK,QAAQ,MAAM,KAAK,CAAC;AAAA,EAC7E,CAAC;AACD,SAAO;AACT,GAAG,iBAAiB;AACpB,IAAI,sBAAqC,OAAO,CAAC,IAAI,OAAO,cAAc;AACxE,QAAM,WAAW,MAAM,SAAS,EAAE;AAClC,MAAI,MAAM,6BAA6B,IAAI,QAAQ;AACnD,MAAI,SAAS,SAAS,GAAG;AACvB,WAAO;AAAA,EACT;AACA,MAAI;AACJ,aAAW,SAAS,UAAU;AAC5B,UAAM,MAAM,oBAAoB,OAAO,OAAO,SAAS;AACvD,UAAM,cAAc,gBAAgB,OAAO,WAAW,GAAG;AACzD,QAAI,KAAK;AACP,UAAI,YAAY,SAAS,GAAG;AAC1B,kBAAU;AAAA,MACZ,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT,GAAG,qBAAqB;AACxB,IAAI,cAA6B,OAAO,QAAM;AAC5C,MAAI,CAAC,UAAU,IAAI,EAAE,GAAG;AACtB,WAAO;AAAA,EACT;AACA,MAAI,CAAC,UAAU,IAAI,EAAE,EAAE,qBAAqB;AAC1C,WAAO;AAAA,EACT;AACA,MAAI,UAAU,IAAI,EAAE,GAAG;AACrB,WAAO,UAAU,IAAI,EAAE,EAAE;AAAA,EAC3B;AACA,SAAO;AACT,GAAG,aAAa;AAChB,IAAI,yBAAwC,OAAO,CAAC,OAAO,UAAU;AACnE,MAAI,CAAC,SAAS,QAAQ,IAAI;AACxB,QAAI,MAAM,uBAAuB;AACjC;AAAA,EACF,OAAO;AACL,QAAI,MAAM,mBAAmB;AAAA,EAC/B;AACA,QAAM,MAAM,EAAE,QAAQ,SAAU,IAAI;AAClC,UAAM,WAAW,MAAM,SAAS,EAAE;AAClC,QAAI,SAAS,SAAS,GAAG;AACvB,UAAI,KAAK,sBAAsB,IAAI,8BAA8B,oBAAoB,IAAI,OAAO,EAAE,CAAC;AACnG,kBAAY,IAAI,IAAI,mBAAmB,IAAI,KAAK,CAAC;AACjD,gBAAU,IAAI,IAAI;AAAA,QAChB,IAAI,oBAAoB,IAAI,OAAO,EAAE;AAAA,QACrC,aAAa,MAAM,KAAK,EAAE;AAAA,MAC5B,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACD,QAAM,MAAM,EAAE,QAAQ,SAAU,IAAI;AAClC,UAAM,WAAW,MAAM,SAAS,EAAE;AAClC,UAAM,QAAQ,MAAM,MAAM;AAC1B,QAAI,SAAS,SAAS,GAAG;AACvB,UAAI,MAAM,sBAAsB,IAAI,WAAW;AAC/C,YAAM,QAAQ,UAAQ;AACpB,cAAM,KAAK,aAAa,KAAK,GAAG,EAAE;AAClC,cAAM,KAAK,aAAa,KAAK,GAAG,EAAE;AAClC,YAAI,KAAK,IAAI;AACX,cAAI,KAAK,UAAU,MAAM,oBAAoB,EAAE;AAC/C,cAAI,KAAK,uBAAuB,IAAI,MAAM,YAAY,IAAI,EAAE,CAAC;AAC7D,oBAAU,IAAI,EAAE,EAAE,sBAAsB;AAAA,QAC1C;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,UAAI,MAAM,kBAAkB,IAAI,WAAW;AAAA,IAC7C;AAAA,EACF,CAAC;AACD,WAAS,MAAM,UAAU,KAAK,GAAG;AAC/B,UAAM,kBAAkB,UAAU,IAAI,EAAE,EAAE;AAC1C,UAAM,SAAS,MAAM,OAAO,eAAe;AAC3C,QAAI,WAAW,MAAM,UAAU,IAAI,MAAM,KAAK,CAAC,UAAU,IAAI,MAAM,EAAE,qBAAqB;AACxF,gBAAU,IAAI,EAAE,EAAE,KAAK;AAAA,IACzB;AAAA,EACF;AACA,QAAM,MAAM,EAAE,QAAQ,SAAU,GAAG;AACjC,UAAM,OAAO,MAAM,KAAK,CAAC;AACzB,QAAI,KAAK,UAAU,EAAE,IAAI,SAAS,EAAE,IAAI,OAAO,KAAK,UAAU,CAAC,CAAC;AAChE,QAAI,KAAK,UAAU,EAAE,IAAI,SAAS,EAAE,IAAI,OAAO,KAAK,UAAU,MAAM,KAAK,CAAC,CAAC,CAAC;AAC5E,QAAI,IAAI,EAAE;AACV,QAAI,IAAI,EAAE;AACV,QAAI,KAAK,WAAW,WAAW,QAAQ,EAAE,GAAG,EAAE,GAAG,iBAAiB,UAAU,IAAI,EAAE,CAAC,GAAG,SAAS,UAAU,IAAI,EAAE,CAAC,CAAC;AACjH,QAAI,UAAU,IAAI,EAAE,CAAC,KAAK,UAAU,IAAI,EAAE,CAAC,GAAG;AAC5C,UAAI,KAAK,oCAAoC,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI;AAC7D,UAAI,YAAY,EAAE,CAAC;AACnB,UAAI,YAAY,EAAE,CAAC;AACnB,YAAM,WAAW,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI;AACjC,UAAI,MAAM,EAAE,GAAG;AACb,cAAM,SAAS,MAAM,OAAO,CAAC;AAC7B,kBAAU,IAAI,MAAM,EAAE,sBAAsB;AAC5C,aAAK,cAAc,EAAE;AAAA,MACvB;AACA,UAAI,MAAM,EAAE,GAAG;AACb,cAAM,SAAS,MAAM,OAAO,CAAC;AAC7B,kBAAU,IAAI,MAAM,EAAE,sBAAsB;AAC5C,aAAK,YAAY,EAAE;AAAA,MACrB;AACA,UAAI,KAAK,0BAA0B,GAAG,GAAG,EAAE,IAAI;AAC/C,YAAM,QAAQ,GAAG,GAAG,MAAM,EAAE,IAAI;AAAA,IAClC;AAAA,EACF,CAAC;AACD,MAAI,KAAK,kBAA+B,MAAM,KAAK,CAAC;AACpD,YAAU,OAAO,CAAC;AAClB,MAAI,MAAM,SAAS;AACrB,GAAG,wBAAwB;AAC3B,IAAI,YAA2B,OAAO,CAAC,OAAO,UAAU;AACtD,MAAI,KAAK,gBAAgB,OAAoB,MAAM,KAAK,GAAG,MAAM,SAAS,GAAG,CAAC;AAC9E,MAAI,QAAQ,IAAI;AACd,QAAI,MAAM,aAAa;AACvB;AAAA,EACF;AACA,MAAI,QAAQ,MAAM,MAAM;AACxB,MAAI,cAAc;AAClB,aAAW,QAAQ,OAAO;AACxB,UAAM,WAAW,MAAM,SAAS,IAAI;AACpC,kBAAc,eAAe,SAAS,SAAS;AAAA,EACjD;AACA,MAAI,CAAC,aAAa;AAChB,QAAI,MAAM,8BAA8B,MAAM,MAAM,CAAC;AACrD;AAAA,EACF;AACA,MAAI,MAAM,YAAY,OAAO,KAAK;AAClC,aAAW,QAAQ,OAAO;AACxB,QAAI,MAAM,mBAAmB,MAAM,WAAW,UAAU,IAAI,IAAI,KAAK,CAAC,UAAU,IAAI,IAAI,EAAE,qBAAqB,CAAC,MAAM,OAAO,IAAI,GAAG,MAAM,KAAK,IAAI,GAAG,MAAM,SAAS,GAAG,GAAG,WAAW,KAAK;AAC3L,QAAI,CAAC,UAAU,IAAI,IAAI,GAAG;AACxB,UAAI,MAAM,iBAAiB,MAAM,KAAK;AAAA,IACxC,WAAW,CAAC,UAAU,IAAI,IAAI,EAAE,uBAAuB,MAAM,SAAS,IAAI,KAAK,MAAM,SAAS,IAAI,EAAE,SAAS,GAAG;AAC9G,UAAI,KAAK,4EAA4E,MAAM,KAAK;AAChG,YAAM,gBAAgB,MAAM,MAAM;AAClC,UAAI,MAAM,cAAc,YAAY,OAAO,OAAO;AAClD,UAAI,UAAU,IAAI,IAAI,GAAG,aAAa,KAAK;AACzC,cAAM,UAAU,IAAI,IAAI,EAAE,YAAY;AACtC,YAAI,KAAK,cAAc,UAAU,IAAI,IAAI,EAAE,YAAY,KAAK,GAAG;AAAA,MACjE;AACA,YAAM,eAAe,IAAa,MAAM;AAAA,QACtC,YAAY;AAAA,QACZ,UAAU;AAAA,MACZ,CAAC,EAAE,SAAS;AAAA,QACV,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS;AAAA,MACX,CAAC,EAAE,oBAAoB,WAAY;AACjC,eAAO,CAAC;AAAA,MACV,CAAC;AACD,UAAI,KAAK,yBAAsC,MAAM,KAAK,CAAC;AAC3D,WAAK,MAAM,OAAO,cAAc,IAAI;AACpC,YAAM,QAAQ,MAAM;AAAA,QAClB,aAAa;AAAA,QACb,IAAI;AAAA,QACJ,aAAa,UAAU,IAAI,IAAI,EAAE;AAAA,QACjC,OAAO,UAAU,IAAI,IAAI,EAAE;AAAA,QAC3B,OAAO;AAAA,MACT,CAAC;AACD,UAAI,KAAK,gCAAgC,MAAM,KAAkB,MAAM,YAAY,CAAC;AACpF,UAAI,MAAM,wBAAqC,MAAM,KAAK,CAAC;AAAA,IAC7D,OAAO;AACL,UAAI,KAAK,eAAe,MAAM,qDAAqD,CAAC,UAAU,IAAI,IAAI,EAAE,qBAAqB,gBAAgB,CAAC,MAAM,OAAO,IAAI,GAAG,cAAc,MAAM,SAAS,IAAI,KAAK,MAAM,SAAS,IAAI,EAAE,SAAS,GAAG,MAAM,SAAS,GAAG,GAAG,KAAK;AACnQ,UAAI,MAAM,SAAS;AAAA,IACrB;AAAA,EACF;AACA,UAAQ,MAAM,MAAM;AACpB,MAAI,KAAK,qBAAqB,KAAK;AACnC,aAAW,QAAQ,OAAO;AACxB,UAAM,OAAO,MAAM,KAAK,IAAI;AAC5B,QAAI,KAAK,mBAAmB,MAAM,IAAI;AACtC,QAAI,MAAM,aAAa;AACrB,gBAAU,KAAK,OAAO,QAAQ,CAAC;AAAA,IACjC;AAAA,EACF;AACF,GAAG,WAAW;AACd,IAAI,SAAwB,OAAO,CAAC,OAAO,UAAU;AACnD,MAAI,MAAM,WAAW,GAAG;AACtB,WAAO,CAAC;AAAA,EACV;AACA,MAAI,SAAS,OAAO,OAAO,CAAC,GAAG,KAAK;AACpC,QAAM,QAAQ,UAAQ;AACpB,UAAM,WAAW,MAAM,SAAS,IAAI;AACpC,UAAM,SAAS,OAAO,OAAO,QAAQ;AACrC,aAAS,CAAC,GAAG,QAAQ,GAAG,MAAM;AAAA,EAChC,CAAC;AACD,SAAO;AACT,GAAG,QAAQ;AACX,IAAI,uBAAsC,OAAO,WAAS,OAAO,OAAO,MAAM,SAAS,CAAC,GAAG,sBAAsB;AAGjH,IAAI,kBAAiC,OAAO,CAAO,OAAO,OAAO,aAAa,IAAI,eAAe,eAAe;AAC9G,MAAI,KAAK,iCAA+C,MAAM,KAAK,GAAG,aAAa;AACnF,QAAM,MAAM,MAAM,MAAM,EAAE;AAC1B,MAAI,MAAM,kCAAkC,GAAG;AAC/C,QAAM,OAAO,MAAM,OAAO,GAAG,EAAE,KAAK,SAAS,MAAM;AACnD,MAAI,CAAC,MAAM,MAAM,GAAG;AAClB,QAAI,KAAK,sBAAsB,KAAK;AAAA,EACtC,OAAO;AACL,QAAI,KAAK,wBAAwB,MAAM,MAAM,CAAC;AAAA,EAChD;AACA,MAAI,MAAM,MAAM,EAAE,SAAS,GAAG;AAC5B,QAAI,KAAK,mBAAmB,MAAM,KAAK,MAAM,MAAM,EAAE,CAAC,CAAC,CAAC;AAAA,EAC1D;AACA,QAAM,WAAW,KAAK,OAAO,GAAG,EAAE,KAAK,SAAS,UAAU;AAC1D,QAAM,YAAY,KAAK,OAAO,GAAG,EAAE,KAAK,SAAS,WAAW;AAC5D,QAAM,aAAa,KAAK,OAAO,GAAG,EAAE,KAAK,SAAS,YAAY;AAC9D,QAAM,QAAQ,KAAK,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO;AACpD,QAAM,QAAQ,IAAI,MAAM,MAAM,EAAE,IAAI,SAAgB,GAAG;AAAA;AACrD,YAAM,OAAO,MAAM,KAAK,CAAC;AACzB,UAAI,kBAAkB,QAAQ;AAC5B,cAAM,OAAO,KAAK,MAAM,KAAK,UAAU,cAAc,WAAW,CAAC;AACjE,YAAI,MAAM,oDAAoD,GAAG,YAAY,KAAK,QAAQ,oBAAoB,cAAc,MAAM;AAClI,cAAM,QAAQ,cAAc,IAAI,IAAI;AACpC,YAAI,CAAC,MAAM,OAAO,CAAC,GAAG;AACpB,cAAI,MAAM,kBAAkB,GAAG,cAAc,EAAE;AAC/C,gBAAM,UAAU,GAAG,cAAc,IAAI,IAAI;AAAA,QAC3C;AAAA,MACF;AACA,UAAI,KAAK,sBAAsB,IAAI,OAAO,KAAK,UAAU,MAAM,KAAK,CAAC,CAAC,CAAC;AACvE,UAAI,MAAM,aAAa;AACrB,YAAI,KAAK,0BAA0B,GAAG,KAAK,OAAO,MAAM,KAAK,CAAC,CAAC;AAC/D,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI,MAAM,MAAM;AAChB,aAAK,MAAM,SAAS,iCACf,KAAK,MAAM,MAAM,IADF;AAAA,UAElB,SAAS,UAAU;AAAA,UACnB;AAAA,QACF,EAAC;AACD,cAAM,IAAI,MAAM,gBAAgB,OAAO,KAAK,OAAO,aAAa,IAAI,MAAM,KAAK,CAAC,GAAG,UAAU;AAC7F,cAAM,QAAQ,EAAE;AAChB,yBAAiB,MAAM,KAAK;AAC5B,aAAK,OAAO,EAAE,QAAQ;AACtB,YAAI;AAAA,UAAK;AAAA,UAAgD;AAAA,UAAG;AAAA;AAAA,UAE5D,KAAK;AAAA,UAAO;AAAA,UAAU,KAAK;AAAA;AAAA;AAAA,QAG3B;AACA,oBAAY,OAAO,IAAI;AAAA,MACzB,OAAO;AACL,YAAI,MAAM,SAAS,CAAC,EAAE,SAAS,GAAG;AAChC,cAAI,MAAM,wCAAwC,GAAG,KAAK,IAAI,MAAM,KAAK,OAAO,UAAU,KAAK;AAC/F,cAAI,MAAM,oBAAoB,KAAK,IAAI,KAAK,CAAC;AAC7C,oBAAU,IAAI,KAAK,IAAI;AAAA,YACrB,IAAI,oBAAoB,KAAK,IAAI,KAAK;AAAA,YACtC;AAAA,UACF,CAAC;AAAA,QACH,OAAO;AACL,cAAI,MAAM,qCAAqC,GAAG,OAAO,MAAM,KAAK,CAAC,GAAG,GAAG;AAC3E,gBAAM,WAAW,OAAO,MAAM,KAAK,CAAC,GAAG;AAAA,YACrC,QAAQ;AAAA,YACR;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAAA,GAAC,CAAC;AACF,QAAM,eAA8B,OAAO,MAAY;AACrD,UAAM,eAAe,MAAM,MAAM,EAAE,IAAI,SAAgB,GAAG;AAAA;AACxD,cAAM,OAAO,MAAM,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI;AACxC,YAAI,KAAK,UAAU,EAAE,IAAI,SAAS,EAAE,IAAI,OAAO,KAAK,UAAU,CAAC,CAAC;AAChE,YAAI,KAAK,UAAU,EAAE,IAAI,SAAS,EAAE,IAAI,MAAM,GAAG,KAAK,KAAK,UAAU,MAAM,KAAK,CAAC,CAAC,CAAC;AACnF,YAAI,KAAK,OAAO,WAAW,QAAQ,EAAE,GAAG,EAAE,GAAG,iBAAiB,UAAU,IAAI,EAAE,CAAC,GAAG,UAAU,IAAI,EAAE,CAAC,CAAC;AACpG,cAAM,gBAAgB,YAAY,IAAI;AAAA,MACxC;AAAA,KAAC;AACD,UAAM,QAAQ,IAAI,YAAY;AAAA,EAChC,IAAG,cAAc;AACjB,QAAM,aAAa;AACnB,MAAI,KAAK,wBAAwB,KAAK,UAAwB,MAAM,KAAK,CAAC,CAAC;AAC3E,MAAI,KAAK,mDAAmD;AAC5D,MAAI,KAAK,mDAAmD;AAC5D,MAAI,KAAK,mDAAmD;AAC5D,SAAY,KAAK;AACjB,MAAI,KAAK,uBAAuB,KAAK,UAAwB,MAAM,KAAK,CAAC,CAAC;AAC1E,MAAI,OAAO;AACX,MAAI;AAAA,IACF;AAAA,EACF,IAAI,wBAAwB,UAAU;AACtC,QAAM,QAAQ,IAAI,qBAAqB,KAAK,EAAE,IAAI,SAAgB,GAAG;AAAA;AACnE,YAAM,OAAO,MAAM,KAAK,CAAC;AACzB,UAAI,KAAK,qBAAqB,IAAI,QAAQ,KAAK,GAAG,MAAM,KAAK,GAAG,aAAa,KAAK,OAAO,aAAa,KAAK,MAAM;AACjH,UAAI,MAAM,aAAa;AACrB,aAAK,KAAK;AACV,YAAI,KAAK,+BAA+B,GAAG,KAAK,IAAI,KAAK,OAAO,KAAK,QAAQ,KAAK,GAAG,KAAK,GAAG,MAAM,OAAO,CAAC,CAAC;AAC5G,kBAAU,IAAI,KAAK,EAAE,EAAE,OAAO;AAC9B,qBAAa,IAAI;AAAA,MACnB,OAAO;AACL,YAAI,MAAM,SAAS,CAAC,EAAE,SAAS,GAAG;AAChC,cAAI,KAAK,4BAA4B,GAAG,KAAK,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,OAAO,KAAK,QAAQ,MAAM,OAAO,CAAC,CAAC;AACzG,eAAK,UAAU;AACf,gBAAM,KAAK,KAAK,QAAQ;AACxB,gBAAM,cAAc,MAAM,UAAU,KAAK;AACzC,gBAAM,cAAc,MAAM,WAAW,UAAU;AAC/C,gBAAM,UAAU,cAAc,eAAe;AAC7C,cAAI,MAAM,WAAW,SAAS,eAAe,aAAa,eAAe,WAAW;AACpF,gBAAM,cAAc,UAAU,IAAI;AAClC,oBAAU,IAAI,KAAK,EAAE,EAAE,OAAO;AAAA,QAChC,OAAO;AACL,gBAAM,SAAS,MAAM,KAAK,KAAK,QAAQ;AACvC,eAAK,KAAK,2BAA2B;AACrC,cAAI,KAAK,2CAA2C,KAAK,IAAI,UAAU,KAAK,UAAU,KAAK,OAAO,KAAK,QAAQ,KAAK,GAAG,KAAK,GAAG,WAAW,KAAK,SAAS,UAAU,QAAQ,QAAQ,SAAS,IAAI;AAC/L,uBAAa,IAAI;AAAA,QACnB;AAAA,MACF;AAAA,IACF;AAAA,GAAC,CAAC;AACF,QAAM,MAAM,EAAE,QAAQ,SAAU,GAAG;AACjC,UAAM,OAAO,MAAM,KAAK,CAAC;AACzB,QAAI,KAAK,UAAU,EAAE,IAAI,SAAS,EAAE,IAAI,OAAO,KAAK,UAAU,IAAI,GAAG,IAAI;AACzE,SAAK,OAAO,QAAQ,WAAS,MAAM,KAAK,2BAA2B,CAAC;AACpE,UAAM,YAAY,MAAM,KAAK,EAAE,CAAC;AAChC,QAAI,UAAU,MAAM,KAAK,EAAE,CAAC;AAC5B,UAAM,QAAQ,WAAW,WAAW,MAAM,WAAW,aAAa,WAAW,SAAS,EAAE;AACxF,sBAAkB,MAAM,KAAK;AAAA,EAC/B,CAAC;AACD,QAAM,MAAM,EAAE,QAAQ,SAAU,GAAG;AACjC,UAAM,IAAI,MAAM,KAAK,CAAC;AACtB,QAAI,KAAK,GAAG,EAAE,MAAM,EAAE,IAAI;AAC1B,QAAI,EAAE,SAAS;AACb,aAAO,EAAE;AAAA,IACX;AAAA,EACF,CAAC;AACD,MAAI,KAAK,uCAAuC,MAAM,IAAI;AAC1D,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF,IAAG,iBAAiB;AACpB,IAAI,SAAwB,OAAO,CAAO,aAAa,QAAQ;AAC7D,QAAM,QAAQ,IAAc,MAAM;AAAA,IAChC,YAAY;AAAA,IACZ,UAAU;AAAA,EACZ,CAAC,EAAE,SAAS;AAAA,IACV,SAAS,YAAY;AAAA,IACrB,SAAS,YAAY,QAAQ,eAAe,YAAY,QAAQ,WAAW,eAAe,YAAY;AAAA,IACtG,SAAS,YAAY,QAAQ,eAAe,YAAY,QAAQ,WAAW,eAAe,YAAY;AAAA,IACtG,SAAS;AAAA,IACT,SAAS;AAAA,EACX,CAAC,EAAE,oBAAoB,WAAY;AACjC,WAAO,CAAC;AAAA,EACV,CAAC;AACD,QAAM,UAAU,IAAI,OAAO,GAAG;AAC9B,kBAAgB,SAAS,YAAY,SAAS,YAAY,MAAM,YAAY,SAAS;AACrF,SAAO;AACP,EAAAA,OAAO;AACP,QAAM;AACN,SAAO;AACP,cAAY,MAAM,QAAQ,UAAQ;AAChC,UAAM,QAAQ,KAAK,IAAI,mBAClB,KACJ;AACD,QAAI,KAAK,UAAU;AACjB,YAAM,UAAU,KAAK,IAAI,KAAK,QAAQ;AAAA,IACxC;AAAA,EACF,CAAC;AACD,MAAI,MAAM,UAAU,YAAY,KAAK;AACrC,cAAY,MAAM,QAAQ,UAAQ;AAChC,QAAI,KAAK,UAAU,KAAK,KAAK;AAC3B,YAAM,SAAS,KAAK;AACpB,YAAM,aAAa,SAAS,QAAQ,SAAS;AAC7C,YAAM,aAAa,SAAS,QAAQ,SAAS;AAC7C,YAAM,OAAO,MAAM,KAAK,MAAM;AAC9B,YAAM,QAAQ,YAAY;AAAA,QACxB,OAAO;AAAA,QACP,IAAI;AAAA,QACJ,UAAU,KAAK;AAAA,QACf,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,OAAO;AAAA;AAAA,QAEP,OAAO;AAAA,QACP,OAAO;AAAA,QACP,QAAQ;AAAA,MACV,CAAC;AACD,YAAM,UAAU,YAAY,KAAK,QAAQ;AACzC,YAAM,QAAQ,YAAY;AAAA,QACxB,OAAO;AAAA,QACP,IAAI;AAAA,QACJ,UAAU,KAAK;AAAA,QACf,YAAY;AAAA,QACZ,SAAS;AAAA;AAAA,QAET,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,QACP,QAAQ;AAAA,MACV,CAAC;AACD,YAAM,UAAU,YAAY,KAAK,QAAQ;AACzC,YAAM,QAAQ,gBAAgB,IAAI;AAClC,YAAM,UAAU,gBAAgB,IAAI;AACpC,YAAM,QAAQ,gBAAgB,IAAI;AAClC,YAAM,QAAQ;AACd,YAAM,eAAe;AACrB,YAAM,KAAK,SAAS;AACpB,cAAQ,iBAAiB;AACzB,cAAQ,eAAe;AACvB,cAAQ,KAAK,SAAS;AACtB,YAAM,QAAQ;AACd,UAAI,KAAK,SAAS;AAChB,cAAM,cAAc;AACpB,cAAM,YAAY;AAAA,MACpB;AACA,YAAM,KAAK,SAAS;AACpB,YAAM,iBAAiB;AACvB,YAAM,QAAQ,QAAQ,YAAY,OAAO,SAAS,mBAAmB;AACrE,YAAM,QAAQ,YAAY,YAAY,SAAS,SAAS,mBAAmB;AAC3E,YAAM,QAAQ,YAAY,QAAQ,OAAO,SAAS,oBAAoB;AAAA,IACxE,OAAO;AACL,YAAM,QAAQ,KAAK,OAAO,KAAK,KAAK,mBAC/B,OACF,KAAK,EAAE;AAAA,IACZ;AAAA,EACF,CAAC;AACD,MAAI,KAAK,mBAAmB,KAAK,UAAwB,MAAM,KAAK,CAAC,CAAC;AACtE,yBAAuB,KAAK;AAC5B,MAAI,KAAK,oBAAoB,KAAK,UAAwB,MAAM,KAAK,CAAC,CAAC;AACvE,QAAM,aAAa,WAAU;AAC7B,QAAM,gBAAgB,SAAS,OAAO,YAAY,MAAM,YAAY,WAAW,QAAQ,UAAU;AACnG,IAAG,QAAQ;", "names": ["clear"]}