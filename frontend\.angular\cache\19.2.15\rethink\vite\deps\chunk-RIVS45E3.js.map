{"version": 3, "sources": ["../../../../../../node_modules/roughjs/bundled/rough.esm.js", "../../../../../../node_modules/mermaid/dist/chunks/mermaid.core/chunk-HRU6DDCH.mjs"], "sourcesContent": ["function t(t, e, s) {\n  if (t && t.length) {\n    const [n, o] = e,\n      a = Math.PI / 180 * s,\n      h = Math.cos(a),\n      r = Math.sin(a);\n    for (const e of t) {\n      const [t, s] = e;\n      e[0] = (t - n) * h - (s - o) * r + n, e[1] = (t - n) * r + (s - o) * h + o;\n    }\n  }\n}\nfunction e(t, e) {\n  return t[0] === e[0] && t[1] === e[1];\n}\nfunction s(s, n, o, a = 1) {\n  const h = o,\n    r = Math.max(n, .1),\n    i = s[0] && s[0][0] && \"number\" == typeof s[0][0] ? [s] : s,\n    c = [0, 0];\n  if (h) for (const e of i) t(e, c, h);\n  const l = function (t, s, n) {\n    const o = [];\n    for (const s of t) {\n      const t = [...s];\n      e(t[0], t[t.length - 1]) || t.push([t[0][0], t[0][1]]), t.length > 2 && o.push(t);\n    }\n    const a = [];\n    s = Math.max(s, .1);\n    const h = [];\n    for (const t of o) for (let e = 0; e < t.length - 1; e++) {\n      const s = t[e],\n        n = t[e + 1];\n      if (s[1] !== n[1]) {\n        const t = Math.min(s[1], n[1]);\n        h.push({\n          ymin: t,\n          ymax: Math.max(s[1], n[1]),\n          x: t === s[1] ? s[0] : n[0],\n          islope: (n[0] - s[0]) / (n[1] - s[1])\n        });\n      }\n    }\n    if (h.sort((t, e) => t.ymin < e.ymin ? -1 : t.ymin > e.ymin ? 1 : t.x < e.x ? -1 : t.x > e.x ? 1 : t.ymax === e.ymax ? 0 : (t.ymax - e.ymax) / Math.abs(t.ymax - e.ymax)), !h.length) return a;\n    let r = [],\n      i = h[0].ymin,\n      c = 0;\n    for (; r.length || h.length;) {\n      if (h.length) {\n        let t = -1;\n        for (let e = 0; e < h.length && !(h[e].ymin > i); e++) t = e;\n        h.splice(0, t + 1).forEach(t => {\n          r.push({\n            s: i,\n            edge: t\n          });\n        });\n      }\n      if (r = r.filter(t => !(t.edge.ymax <= i)), r.sort((t, e) => t.edge.x === e.edge.x ? 0 : (t.edge.x - e.edge.x) / Math.abs(t.edge.x - e.edge.x)), (1 !== n || c % s == 0) && r.length > 1) for (let t = 0; t < r.length; t += 2) {\n        const e = t + 1;\n        if (e >= r.length) break;\n        const s = r[t].edge,\n          n = r[e].edge;\n        a.push([[Math.round(s.x), i], [Math.round(n.x), i]]);\n      }\n      i += n, r.forEach(t => {\n        t.edge.x = t.edge.x + n * t.edge.islope;\n      }), c++;\n    }\n    return a;\n  }(i, r, a);\n  if (h) {\n    for (const e of i) t(e, c, -h);\n    !function (e, s, n) {\n      const o = [];\n      e.forEach(t => o.push(...t)), t(o, s, n);\n    }(l, c, -h);\n  }\n  return l;\n}\nfunction n(t, e) {\n  var n;\n  const o = e.hachureAngle + 90;\n  let a = e.hachureGap;\n  a < 0 && (a = 4 * e.strokeWidth), a = Math.round(Math.max(a, .1));\n  let h = 1;\n  return e.roughness >= 1 && ((null === (n = e.randomizer) || void 0 === n ? void 0 : n.next()) || Math.random()) > .7 && (h = a), s(t, a, o, h || 1);\n}\nclass o {\n  constructor(t) {\n    this.helper = t;\n  }\n  fillPolygons(t, e) {\n    return this._fillPolygons(t, e);\n  }\n  _fillPolygons(t, e) {\n    const s = n(t, e);\n    return {\n      type: \"fillSketch\",\n      ops: this.renderLines(s, e)\n    };\n  }\n  renderLines(t, e) {\n    const s = [];\n    for (const n of t) s.push(...this.helper.doubleLineOps(n[0][0], n[0][1], n[1][0], n[1][1], e));\n    return s;\n  }\n}\nfunction a(t) {\n  const e = t[0],\n    s = t[1];\n  return Math.sqrt(Math.pow(e[0] - s[0], 2) + Math.pow(e[1] - s[1], 2));\n}\nclass h extends o {\n  fillPolygons(t, e) {\n    let s = e.hachureGap;\n    s < 0 && (s = 4 * e.strokeWidth), s = Math.max(s, .1);\n    const o = n(t, Object.assign({}, e, {\n        hachureGap: s\n      })),\n      h = Math.PI / 180 * e.hachureAngle,\n      r = [],\n      i = .5 * s * Math.cos(h),\n      c = .5 * s * Math.sin(h);\n    for (const [t, e] of o) a([t, e]) && r.push([[t[0] - i, t[1] + c], [...e]], [[t[0] + i, t[1] - c], [...e]]);\n    return {\n      type: \"fillSketch\",\n      ops: this.renderLines(r, e)\n    };\n  }\n}\nclass r extends o {\n  fillPolygons(t, e) {\n    const s = this._fillPolygons(t, e),\n      n = Object.assign({}, e, {\n        hachureAngle: e.hachureAngle + 90\n      }),\n      o = this._fillPolygons(t, n);\n    return s.ops = s.ops.concat(o.ops), s;\n  }\n}\nclass i {\n  constructor(t) {\n    this.helper = t;\n  }\n  fillPolygons(t, e) {\n    const s = n(t, e = Object.assign({}, e, {\n      hachureAngle: 0\n    }));\n    return this.dotsOnLines(s, e);\n  }\n  dotsOnLines(t, e) {\n    const s = [];\n    let n = e.hachureGap;\n    n < 0 && (n = 4 * e.strokeWidth), n = Math.max(n, .1);\n    let o = e.fillWeight;\n    o < 0 && (o = e.strokeWidth / 2);\n    const h = n / 4;\n    for (const r of t) {\n      const t = a(r),\n        i = t / n,\n        c = Math.ceil(i) - 1,\n        l = t - c * n,\n        u = (r[0][0] + r[1][0]) / 2 - n / 4,\n        p = Math.min(r[0][1], r[1][1]);\n      for (let t = 0; t < c; t++) {\n        const a = p + l + t * n,\n          r = u - h + 2 * Math.random() * h,\n          i = a - h + 2 * Math.random() * h,\n          c = this.helper.ellipse(r, i, o, o, e);\n        s.push(...c.ops);\n      }\n    }\n    return {\n      type: \"fillSketch\",\n      ops: s\n    };\n  }\n}\nclass c {\n  constructor(t) {\n    this.helper = t;\n  }\n  fillPolygons(t, e) {\n    const s = n(t, e);\n    return {\n      type: \"fillSketch\",\n      ops: this.dashedLine(s, e)\n    };\n  }\n  dashedLine(t, e) {\n    const s = e.dashOffset < 0 ? e.hachureGap < 0 ? 4 * e.strokeWidth : e.hachureGap : e.dashOffset,\n      n = e.dashGap < 0 ? e.hachureGap < 0 ? 4 * e.strokeWidth : e.hachureGap : e.dashGap,\n      o = [];\n    return t.forEach(t => {\n      const h = a(t),\n        r = Math.floor(h / (s + n)),\n        i = (h + n - r * (s + n)) / 2;\n      let c = t[0],\n        l = t[1];\n      c[0] > l[0] && (c = t[1], l = t[0]);\n      const u = Math.atan((l[1] - c[1]) / (l[0] - c[0]));\n      for (let t = 0; t < r; t++) {\n        const a = t * (s + n),\n          h = a + s,\n          r = [c[0] + a * Math.cos(u) + i * Math.cos(u), c[1] + a * Math.sin(u) + i * Math.sin(u)],\n          l = [c[0] + h * Math.cos(u) + i * Math.cos(u), c[1] + h * Math.sin(u) + i * Math.sin(u)];\n        o.push(...this.helper.doubleLineOps(r[0], r[1], l[0], l[1], e));\n      }\n    }), o;\n  }\n}\nclass l {\n  constructor(t) {\n    this.helper = t;\n  }\n  fillPolygons(t, e) {\n    const s = e.hachureGap < 0 ? 4 * e.strokeWidth : e.hachureGap,\n      o = e.zigzagOffset < 0 ? s : e.zigzagOffset,\n      a = n(t, e = Object.assign({}, e, {\n        hachureGap: s + o\n      }));\n    return {\n      type: \"fillSketch\",\n      ops: this.zigzagLines(a, o, e)\n    };\n  }\n  zigzagLines(t, e, s) {\n    const n = [];\n    return t.forEach(t => {\n      const o = a(t),\n        h = Math.round(o / (2 * e));\n      let r = t[0],\n        i = t[1];\n      r[0] > i[0] && (r = t[1], i = t[0]);\n      const c = Math.atan((i[1] - r[1]) / (i[0] - r[0]));\n      for (let t = 0; t < h; t++) {\n        const o = 2 * t * e,\n          a = 2 * (t + 1) * e,\n          h = Math.sqrt(2 * Math.pow(e, 2)),\n          i = [r[0] + o * Math.cos(c), r[1] + o * Math.sin(c)],\n          l = [r[0] + a * Math.cos(c), r[1] + a * Math.sin(c)],\n          u = [i[0] + h * Math.cos(c + Math.PI / 4), i[1] + h * Math.sin(c + Math.PI / 4)];\n        n.push(...this.helper.doubleLineOps(i[0], i[1], u[0], u[1], s), ...this.helper.doubleLineOps(u[0], u[1], l[0], l[1], s));\n      }\n    }), n;\n  }\n}\nconst u = {};\nclass p {\n  constructor(t) {\n    this.seed = t;\n  }\n  next() {\n    return this.seed ? (2 ** 31 - 1 & (this.seed = Math.imul(48271, this.seed))) / 2 ** 31 : Math.random();\n  }\n}\nconst f = 0,\n  d = 1,\n  g = 2,\n  M = {\n    A: 7,\n    a: 7,\n    C: 6,\n    c: 6,\n    H: 1,\n    h: 1,\n    L: 2,\n    l: 2,\n    M: 2,\n    m: 2,\n    Q: 4,\n    q: 4,\n    S: 4,\n    s: 4,\n    T: 2,\n    t: 2,\n    V: 1,\n    v: 1,\n    Z: 0,\n    z: 0\n  };\nfunction k(t, e) {\n  return t.type === e;\n}\nfunction b(t) {\n  const e = [],\n    s = function (t) {\n      const e = new Array();\n      for (; \"\" !== t;) if (t.match(/^([ \\t\\r\\n,]+)/)) t = t.substr(RegExp.$1.length);else if (t.match(/^([aAcChHlLmMqQsStTvVzZ])/)) e[e.length] = {\n        type: f,\n        text: RegExp.$1\n      }, t = t.substr(RegExp.$1.length);else {\n        if (!t.match(/^(([-+]?[0-9]+(\\.[0-9]*)?|[-+]?\\.[0-9]+)([eE][-+]?[0-9]+)?)/)) return [];\n        e[e.length] = {\n          type: d,\n          text: `${parseFloat(RegExp.$1)}`\n        }, t = t.substr(RegExp.$1.length);\n      }\n      return e[e.length] = {\n        type: g,\n        text: \"\"\n      }, e;\n    }(t);\n  let n = \"BOD\",\n    o = 0,\n    a = s[o];\n  for (; !k(a, g);) {\n    let h = 0;\n    const r = [];\n    if (\"BOD\" === n) {\n      if (\"M\" !== a.text && \"m\" !== a.text) return b(\"M0,0\" + t);\n      o++, h = M[a.text], n = a.text;\n    } else k(a, d) ? h = M[n] : (o++, h = M[a.text], n = a.text);\n    if (!(o + h < s.length)) throw new Error(\"Path data ended short\");\n    for (let t = o; t < o + h; t++) {\n      const e = s[t];\n      if (!k(e, d)) throw new Error(\"Param not a number: \" + n + \",\" + e.text);\n      r[r.length] = +e.text;\n    }\n    if (\"number\" != typeof M[n]) throw new Error(\"Bad segment: \" + n);\n    {\n      const t = {\n        key: n,\n        data: r\n      };\n      e.push(t), o += h, a = s[o], \"M\" === n && (n = \"L\"), \"m\" === n && (n = \"l\");\n    }\n  }\n  return e;\n}\nfunction y(t) {\n  let e = 0,\n    s = 0,\n    n = 0,\n    o = 0;\n  const a = [];\n  for (const {\n    key: h,\n    data: r\n  } of t) switch (h) {\n    case \"M\":\n      a.push({\n        key: \"M\",\n        data: [...r]\n      }), [e, s] = r, [n, o] = r;\n      break;\n    case \"m\":\n      e += r[0], s += r[1], a.push({\n        key: \"M\",\n        data: [e, s]\n      }), n = e, o = s;\n      break;\n    case \"L\":\n      a.push({\n        key: \"L\",\n        data: [...r]\n      }), [e, s] = r;\n      break;\n    case \"l\":\n      e += r[0], s += r[1], a.push({\n        key: \"L\",\n        data: [e, s]\n      });\n      break;\n    case \"C\":\n      a.push({\n        key: \"C\",\n        data: [...r]\n      }), e = r[4], s = r[5];\n      break;\n    case \"c\":\n      {\n        const t = r.map((t, n) => n % 2 ? t + s : t + e);\n        a.push({\n          key: \"C\",\n          data: t\n        }), e = t[4], s = t[5];\n        break;\n      }\n    case \"Q\":\n      a.push({\n        key: \"Q\",\n        data: [...r]\n      }), e = r[2], s = r[3];\n      break;\n    case \"q\":\n      {\n        const t = r.map((t, n) => n % 2 ? t + s : t + e);\n        a.push({\n          key: \"Q\",\n          data: t\n        }), e = t[2], s = t[3];\n        break;\n      }\n    case \"A\":\n      a.push({\n        key: \"A\",\n        data: [...r]\n      }), e = r[5], s = r[6];\n      break;\n    case \"a\":\n      e += r[5], s += r[6], a.push({\n        key: \"A\",\n        data: [r[0], r[1], r[2], r[3], r[4], e, s]\n      });\n      break;\n    case \"H\":\n      a.push({\n        key: \"H\",\n        data: [...r]\n      }), e = r[0];\n      break;\n    case \"h\":\n      e += r[0], a.push({\n        key: \"H\",\n        data: [e]\n      });\n      break;\n    case \"V\":\n      a.push({\n        key: \"V\",\n        data: [...r]\n      }), s = r[0];\n      break;\n    case \"v\":\n      s += r[0], a.push({\n        key: \"V\",\n        data: [s]\n      });\n      break;\n    case \"S\":\n      a.push({\n        key: \"S\",\n        data: [...r]\n      }), e = r[2], s = r[3];\n      break;\n    case \"s\":\n      {\n        const t = r.map((t, n) => n % 2 ? t + s : t + e);\n        a.push({\n          key: \"S\",\n          data: t\n        }), e = t[2], s = t[3];\n        break;\n      }\n    case \"T\":\n      a.push({\n        key: \"T\",\n        data: [...r]\n      }), e = r[0], s = r[1];\n      break;\n    case \"t\":\n      e += r[0], s += r[1], a.push({\n        key: \"T\",\n        data: [e, s]\n      });\n      break;\n    case \"Z\":\n    case \"z\":\n      a.push({\n        key: \"Z\",\n        data: []\n      }), e = n, s = o;\n  }\n  return a;\n}\nfunction m(t) {\n  const e = [];\n  let s = \"\",\n    n = 0,\n    o = 0,\n    a = 0,\n    h = 0,\n    r = 0,\n    i = 0;\n  for (const {\n    key: c,\n    data: l\n  } of t) {\n    switch (c) {\n      case \"M\":\n        e.push({\n          key: \"M\",\n          data: [...l]\n        }), [n, o] = l, [a, h] = l;\n        break;\n      case \"C\":\n        e.push({\n          key: \"C\",\n          data: [...l]\n        }), n = l[4], o = l[5], r = l[2], i = l[3];\n        break;\n      case \"L\":\n        e.push({\n          key: \"L\",\n          data: [...l]\n        }), [n, o] = l;\n        break;\n      case \"H\":\n        n = l[0], e.push({\n          key: \"L\",\n          data: [n, o]\n        });\n        break;\n      case \"V\":\n        o = l[0], e.push({\n          key: \"L\",\n          data: [n, o]\n        });\n        break;\n      case \"S\":\n        {\n          let t = 0,\n            a = 0;\n          \"C\" === s || \"S\" === s ? (t = n + (n - r), a = o + (o - i)) : (t = n, a = o), e.push({\n            key: \"C\",\n            data: [t, a, ...l]\n          }), r = l[0], i = l[1], n = l[2], o = l[3];\n          break;\n        }\n      case \"T\":\n        {\n          const [t, a] = l;\n          let h = 0,\n            c = 0;\n          \"Q\" === s || \"T\" === s ? (h = n + (n - r), c = o + (o - i)) : (h = n, c = o);\n          const u = n + 2 * (h - n) / 3,\n            p = o + 2 * (c - o) / 3,\n            f = t + 2 * (h - t) / 3,\n            d = a + 2 * (c - a) / 3;\n          e.push({\n            key: \"C\",\n            data: [u, p, f, d, t, a]\n          }), r = h, i = c, n = t, o = a;\n          break;\n        }\n      case \"Q\":\n        {\n          const [t, s, a, h] = l,\n            c = n + 2 * (t - n) / 3,\n            u = o + 2 * (s - o) / 3,\n            p = a + 2 * (t - a) / 3,\n            f = h + 2 * (s - h) / 3;\n          e.push({\n            key: \"C\",\n            data: [c, u, p, f, a, h]\n          }), r = t, i = s, n = a, o = h;\n          break;\n        }\n      case \"A\":\n        {\n          const t = Math.abs(l[0]),\n            s = Math.abs(l[1]),\n            a = l[2],\n            h = l[3],\n            r = l[4],\n            i = l[5],\n            c = l[6];\n          if (0 === t || 0 === s) e.push({\n            key: \"C\",\n            data: [n, o, i, c, i, c]\n          }), n = i, o = c;else if (n !== i || o !== c) {\n            x(n, o, i, c, t, s, a, h, r).forEach(function (t) {\n              e.push({\n                key: \"C\",\n                data: t\n              });\n            }), n = i, o = c;\n          }\n          break;\n        }\n      case \"Z\":\n        e.push({\n          key: \"Z\",\n          data: []\n        }), n = a, o = h;\n    }\n    s = c;\n  }\n  return e;\n}\nfunction w(t, e, s) {\n  return [t * Math.cos(s) - e * Math.sin(s), t * Math.sin(s) + e * Math.cos(s)];\n}\nfunction x(t, e, s, n, o, a, h, r, i, c) {\n  const l = (u = h, Math.PI * u / 180);\n  var u;\n  let p = [],\n    f = 0,\n    d = 0,\n    g = 0,\n    M = 0;\n  if (c) [f, d, g, M] = c;else {\n    [t, e] = w(t, e, -l), [s, n] = w(s, n, -l);\n    const h = (t - s) / 2,\n      c = (e - n) / 2;\n    let u = h * h / (o * o) + c * c / (a * a);\n    u > 1 && (u = Math.sqrt(u), o *= u, a *= u);\n    const p = o * o,\n      k = a * a,\n      b = p * k - p * c * c - k * h * h,\n      y = p * c * c + k * h * h,\n      m = (r === i ? -1 : 1) * Math.sqrt(Math.abs(b / y));\n    g = m * o * c / a + (t + s) / 2, M = m * -a * h / o + (e + n) / 2, f = Math.asin(parseFloat(((e - M) / a).toFixed(9))), d = Math.asin(parseFloat(((n - M) / a).toFixed(9))), t < g && (f = Math.PI - f), s < g && (d = Math.PI - d), f < 0 && (f = 2 * Math.PI + f), d < 0 && (d = 2 * Math.PI + d), i && f > d && (f -= 2 * Math.PI), !i && d > f && (d -= 2 * Math.PI);\n  }\n  let k = d - f;\n  if (Math.abs(k) > 120 * Math.PI / 180) {\n    const t = d,\n      e = s,\n      r = n;\n    d = i && d > f ? f + 120 * Math.PI / 180 * 1 : f + 120 * Math.PI / 180 * -1, p = x(s = g + o * Math.cos(d), n = M + a * Math.sin(d), e, r, o, a, h, 0, i, [d, t, g, M]);\n  }\n  k = d - f;\n  const b = Math.cos(f),\n    y = Math.sin(f),\n    m = Math.cos(d),\n    P = Math.sin(d),\n    v = Math.tan(k / 4),\n    S = 4 / 3 * o * v,\n    O = 4 / 3 * a * v,\n    L = [t, e],\n    T = [t + S * y, e - O * b],\n    D = [s + S * P, n - O * m],\n    A = [s, n];\n  if (T[0] = 2 * L[0] - T[0], T[1] = 2 * L[1] - T[1], c) return [T, D, A].concat(p);\n  {\n    p = [T, D, A].concat(p);\n    const t = [];\n    for (let e = 0; e < p.length; e += 3) {\n      const s = w(p[e][0], p[e][1], l),\n        n = w(p[e + 1][0], p[e + 1][1], l),\n        o = w(p[e + 2][0], p[e + 2][1], l);\n      t.push([s[0], s[1], n[0], n[1], o[0], o[1]]);\n    }\n    return t;\n  }\n}\nconst P = {\n  randOffset: function (t, e) {\n    return G(t, e);\n  },\n  randOffsetWithRange: function (t, e, s) {\n    return E(t, e, s);\n  },\n  ellipse: function (t, e, s, n, o) {\n    const a = T(s, n, o);\n    return D(t, e, o, a).opset;\n  },\n  doubleLineOps: function (t, e, s, n, o) {\n    return $(t, e, s, n, o, !0);\n  }\n};\nfunction v(t, e, s, n, o) {\n  return {\n    type: \"path\",\n    ops: $(t, e, s, n, o)\n  };\n}\nfunction S(t, e, s) {\n  const n = (t || []).length;\n  if (n > 2) {\n    const o = [];\n    for (let e = 0; e < n - 1; e++) o.push(...$(t[e][0], t[e][1], t[e + 1][0], t[e + 1][1], s));\n    return e && o.push(...$(t[n - 1][0], t[n - 1][1], t[0][0], t[0][1], s)), {\n      type: \"path\",\n      ops: o\n    };\n  }\n  return 2 === n ? v(t[0][0], t[0][1], t[1][0], t[1][1], s) : {\n    type: \"path\",\n    ops: []\n  };\n}\nfunction O(t, e, s, n, o) {\n  return function (t, e) {\n    return S(t, !0, e);\n  }([[t, e], [t + s, e], [t + s, e + n], [t, e + n]], o);\n}\nfunction L(t, e) {\n  if (t.length) {\n    const s = \"number\" == typeof t[0][0] ? [t] : t,\n      n = j(s[0], 1 * (1 + .2 * e.roughness), e),\n      o = e.disableMultiStroke ? [] : j(s[0], 1.5 * (1 + .22 * e.roughness), z(e));\n    for (let t = 1; t < s.length; t++) {\n      const a = s[t];\n      if (a.length) {\n        const t = j(a, 1 * (1 + .2 * e.roughness), e),\n          s = e.disableMultiStroke ? [] : j(a, 1.5 * (1 + .22 * e.roughness), z(e));\n        for (const e of t) \"move\" !== e.op && n.push(e);\n        for (const t of s) \"move\" !== t.op && o.push(t);\n      }\n    }\n    return {\n      type: \"path\",\n      ops: n.concat(o)\n    };\n  }\n  return {\n    type: \"path\",\n    ops: []\n  };\n}\nfunction T(t, e, s) {\n  const n = Math.sqrt(2 * Math.PI * Math.sqrt((Math.pow(t / 2, 2) + Math.pow(e / 2, 2)) / 2)),\n    o = Math.ceil(Math.max(s.curveStepCount, s.curveStepCount / Math.sqrt(200) * n)),\n    a = 2 * Math.PI / o;\n  let h = Math.abs(t / 2),\n    r = Math.abs(e / 2);\n  const i = 1 - s.curveFitting;\n  return h += G(h * i, s), r += G(r * i, s), {\n    increment: a,\n    rx: h,\n    ry: r\n  };\n}\nfunction D(t, e, s, n) {\n  const [o, a] = F(n.increment, t, e, n.rx, n.ry, 1, n.increment * E(.1, E(.4, 1, s), s), s);\n  let h = q(o, null, s);\n  if (!s.disableMultiStroke && 0 !== s.roughness) {\n    const [o] = F(n.increment, t, e, n.rx, n.ry, 1.5, 0, s),\n      a = q(o, null, s);\n    h = h.concat(a);\n  }\n  return {\n    estimatedPoints: a,\n    opset: {\n      type: \"path\",\n      ops: h\n    }\n  };\n}\nfunction A(t, e, s, n, o, a, h, r, i) {\n  const c = t,\n    l = e;\n  let u = Math.abs(s / 2),\n    p = Math.abs(n / 2);\n  u += G(.01 * u, i), p += G(.01 * p, i);\n  let f = o,\n    d = a;\n  for (; f < 0;) f += 2 * Math.PI, d += 2 * Math.PI;\n  d - f > 2 * Math.PI && (f = 0, d = 2 * Math.PI);\n  const g = 2 * Math.PI / i.curveStepCount,\n    M = Math.min(g / 2, (d - f) / 2),\n    k = V(M, c, l, u, p, f, d, 1, i);\n  if (!i.disableMultiStroke) {\n    const t = V(M, c, l, u, p, f, d, 1.5, i);\n    k.push(...t);\n  }\n  return h && (r ? k.push(...$(c, l, c + u * Math.cos(f), l + p * Math.sin(f), i), ...$(c, l, c + u * Math.cos(d), l + p * Math.sin(d), i)) : k.push({\n    op: \"lineTo\",\n    data: [c, l]\n  }, {\n    op: \"lineTo\",\n    data: [c + u * Math.cos(f), l + p * Math.sin(f)]\n  })), {\n    type: \"path\",\n    ops: k\n  };\n}\nfunction _(t, e) {\n  const s = m(y(b(t))),\n    n = [];\n  let o = [0, 0],\n    a = [0, 0];\n  for (const {\n    key: t,\n    data: h\n  } of s) switch (t) {\n    case \"M\":\n      a = [h[0], h[1]], o = [h[0], h[1]];\n      break;\n    case \"L\":\n      n.push(...$(a[0], a[1], h[0], h[1], e)), a = [h[0], h[1]];\n      break;\n    case \"C\":\n      {\n        const [t, s, o, r, i, c] = h;\n        n.push(...Z(t, s, o, r, i, c, a, e)), a = [i, c];\n        break;\n      }\n    case \"Z\":\n      n.push(...$(a[0], a[1], o[0], o[1], e)), a = [o[0], o[1]];\n  }\n  return {\n    type: \"path\",\n    ops: n\n  };\n}\nfunction I(t, e) {\n  const s = [];\n  for (const n of t) if (n.length) {\n    const t = e.maxRandomnessOffset || 0,\n      o = n.length;\n    if (o > 2) {\n      s.push({\n        op: \"move\",\n        data: [n[0][0] + G(t, e), n[0][1] + G(t, e)]\n      });\n      for (let a = 1; a < o; a++) s.push({\n        op: \"lineTo\",\n        data: [n[a][0] + G(t, e), n[a][1] + G(t, e)]\n      });\n    }\n  }\n  return {\n    type: \"fillPath\",\n    ops: s\n  };\n}\nfunction C(t, e) {\n  return function (t, e) {\n    let s = t.fillStyle || \"hachure\";\n    if (!u[s]) switch (s) {\n      case \"zigzag\":\n        u[s] || (u[s] = new h(e));\n        break;\n      case \"cross-hatch\":\n        u[s] || (u[s] = new r(e));\n        break;\n      case \"dots\":\n        u[s] || (u[s] = new i(e));\n        break;\n      case \"dashed\":\n        u[s] || (u[s] = new c(e));\n        break;\n      case \"zigzag-line\":\n        u[s] || (u[s] = new l(e));\n        break;\n      default:\n        s = \"hachure\", u[s] || (u[s] = new o(e));\n    }\n    return u[s];\n  }(e, P).fillPolygons(t, e);\n}\nfunction z(t) {\n  const e = Object.assign({}, t);\n  return e.randomizer = void 0, t.seed && (e.seed = t.seed + 1), e;\n}\nfunction W(t) {\n  return t.randomizer || (t.randomizer = new p(t.seed || 0)), t.randomizer.next();\n}\nfunction E(t, e, s, n = 1) {\n  return s.roughness * n * (W(s) * (e - t) + t);\n}\nfunction G(t, e, s = 1) {\n  return E(-t, t, e, s);\n}\nfunction $(t, e, s, n, o, a = !1) {\n  const h = a ? o.disableMultiStrokeFill : o.disableMultiStroke,\n    r = R(t, e, s, n, o, !0, !1);\n  if (h) return r;\n  const i = R(t, e, s, n, o, !0, !0);\n  return r.concat(i);\n}\nfunction R(t, e, s, n, o, a, h) {\n  const r = Math.pow(t - s, 2) + Math.pow(e - n, 2),\n    i = Math.sqrt(r);\n  let c = 1;\n  c = i < 200 ? 1 : i > 500 ? .4 : -.0016668 * i + 1.233334;\n  let l = o.maxRandomnessOffset || 0;\n  l * l * 100 > r && (l = i / 10);\n  const u = l / 2,\n    p = .2 + .2 * W(o);\n  let f = o.bowing * o.maxRandomnessOffset * (n - e) / 200,\n    d = o.bowing * o.maxRandomnessOffset * (t - s) / 200;\n  f = G(f, o, c), d = G(d, o, c);\n  const g = [],\n    M = () => G(u, o, c),\n    k = () => G(l, o, c),\n    b = o.preserveVertices;\n  return a && (h ? g.push({\n    op: \"move\",\n    data: [t + (b ? 0 : M()), e + (b ? 0 : M())]\n  }) : g.push({\n    op: \"move\",\n    data: [t + (b ? 0 : G(l, o, c)), e + (b ? 0 : G(l, o, c))]\n  })), h ? g.push({\n    op: \"bcurveTo\",\n    data: [f + t + (s - t) * p + M(), d + e + (n - e) * p + M(), f + t + 2 * (s - t) * p + M(), d + e + 2 * (n - e) * p + M(), s + (b ? 0 : M()), n + (b ? 0 : M())]\n  }) : g.push({\n    op: \"bcurveTo\",\n    data: [f + t + (s - t) * p + k(), d + e + (n - e) * p + k(), f + t + 2 * (s - t) * p + k(), d + e + 2 * (n - e) * p + k(), s + (b ? 0 : k()), n + (b ? 0 : k())]\n  }), g;\n}\nfunction j(t, e, s) {\n  if (!t.length) return [];\n  const n = [];\n  n.push([t[0][0] + G(e, s), t[0][1] + G(e, s)]), n.push([t[0][0] + G(e, s), t[0][1] + G(e, s)]);\n  for (let o = 1; o < t.length; o++) n.push([t[o][0] + G(e, s), t[o][1] + G(e, s)]), o === t.length - 1 && n.push([t[o][0] + G(e, s), t[o][1] + G(e, s)]);\n  return q(n, null, s);\n}\nfunction q(t, e, s) {\n  const n = t.length,\n    o = [];\n  if (n > 3) {\n    const a = [],\n      h = 1 - s.curveTightness;\n    o.push({\n      op: \"move\",\n      data: [t[1][0], t[1][1]]\n    });\n    for (let e = 1; e + 2 < n; e++) {\n      const s = t[e];\n      a[0] = [s[0], s[1]], a[1] = [s[0] + (h * t[e + 1][0] - h * t[e - 1][0]) / 6, s[1] + (h * t[e + 1][1] - h * t[e - 1][1]) / 6], a[2] = [t[e + 1][0] + (h * t[e][0] - h * t[e + 2][0]) / 6, t[e + 1][1] + (h * t[e][1] - h * t[e + 2][1]) / 6], a[3] = [t[e + 1][0], t[e + 1][1]], o.push({\n        op: \"bcurveTo\",\n        data: [a[1][0], a[1][1], a[2][0], a[2][1], a[3][0], a[3][1]]\n      });\n    }\n    if (e && 2 === e.length) {\n      const t = s.maxRandomnessOffset;\n      o.push({\n        op: \"lineTo\",\n        data: [e[0] + G(t, s), e[1] + G(t, s)]\n      });\n    }\n  } else 3 === n ? (o.push({\n    op: \"move\",\n    data: [t[1][0], t[1][1]]\n  }), o.push({\n    op: \"bcurveTo\",\n    data: [t[1][0], t[1][1], t[2][0], t[2][1], t[2][0], t[2][1]]\n  })) : 2 === n && o.push(...R(t[0][0], t[0][1], t[1][0], t[1][1], s, !0, !0));\n  return o;\n}\nfunction F(t, e, s, n, o, a, h, r) {\n  const i = [],\n    c = [];\n  if (0 === r.roughness) {\n    t /= 4, c.push([e + n * Math.cos(-t), s + o * Math.sin(-t)]);\n    for (let a = 0; a <= 2 * Math.PI; a += t) {\n      const t = [e + n * Math.cos(a), s + o * Math.sin(a)];\n      i.push(t), c.push(t);\n    }\n    c.push([e + n * Math.cos(0), s + o * Math.sin(0)]), c.push([e + n * Math.cos(t), s + o * Math.sin(t)]);\n  } else {\n    const l = G(.5, r) - Math.PI / 2;\n    c.push([G(a, r) + e + .9 * n * Math.cos(l - t), G(a, r) + s + .9 * o * Math.sin(l - t)]);\n    const u = 2 * Math.PI + l - .01;\n    for (let h = l; h < u; h += t) {\n      const t = [G(a, r) + e + n * Math.cos(h), G(a, r) + s + o * Math.sin(h)];\n      i.push(t), c.push(t);\n    }\n    c.push([G(a, r) + e + n * Math.cos(l + 2 * Math.PI + .5 * h), G(a, r) + s + o * Math.sin(l + 2 * Math.PI + .5 * h)]), c.push([G(a, r) + e + .98 * n * Math.cos(l + h), G(a, r) + s + .98 * o * Math.sin(l + h)]), c.push([G(a, r) + e + .9 * n * Math.cos(l + .5 * h), G(a, r) + s + .9 * o * Math.sin(l + .5 * h)]);\n  }\n  return [c, i];\n}\nfunction V(t, e, s, n, o, a, h, r, i) {\n  const c = a + G(.1, i),\n    l = [];\n  l.push([G(r, i) + e + .9 * n * Math.cos(c - t), G(r, i) + s + .9 * o * Math.sin(c - t)]);\n  for (let a = c; a <= h; a += t) l.push([G(r, i) + e + n * Math.cos(a), G(r, i) + s + o * Math.sin(a)]);\n  return l.push([e + n * Math.cos(h), s + o * Math.sin(h)]), l.push([e + n * Math.cos(h), s + o * Math.sin(h)]), q(l, null, i);\n}\nfunction Z(t, e, s, n, o, a, h, r) {\n  const i = [],\n    c = [r.maxRandomnessOffset || 1, (r.maxRandomnessOffset || 1) + .3];\n  let l = [0, 0];\n  const u = r.disableMultiStroke ? 1 : 2,\n    p = r.preserveVertices;\n  for (let f = 0; f < u; f++) 0 === f ? i.push({\n    op: \"move\",\n    data: [h[0], h[1]]\n  }) : i.push({\n    op: \"move\",\n    data: [h[0] + (p ? 0 : G(c[0], r)), h[1] + (p ? 0 : G(c[0], r))]\n  }), l = p ? [o, a] : [o + G(c[f], r), a + G(c[f], r)], i.push({\n    op: \"bcurveTo\",\n    data: [t + G(c[f], r), e + G(c[f], r), s + G(c[f], r), n + G(c[f], r), l[0], l[1]]\n  });\n  return i;\n}\nfunction Q(t) {\n  return [...t];\n}\nfunction H(t, e = 0) {\n  const s = t.length;\n  if (s < 3) throw new Error(\"A curve must have at least three points.\");\n  const n = [];\n  if (3 === s) n.push(Q(t[0]), Q(t[1]), Q(t[2]), Q(t[2]));else {\n    const s = [];\n    s.push(t[0], t[0]);\n    for (let e = 1; e < t.length; e++) s.push(t[e]), e === t.length - 1 && s.push(t[e]);\n    const o = [],\n      a = 1 - e;\n    n.push(Q(s[0]));\n    for (let t = 1; t + 2 < s.length; t++) {\n      const e = s[t];\n      o[0] = [e[0], e[1]], o[1] = [e[0] + (a * s[t + 1][0] - a * s[t - 1][0]) / 6, e[1] + (a * s[t + 1][1] - a * s[t - 1][1]) / 6], o[2] = [s[t + 1][0] + (a * s[t][0] - a * s[t + 2][0]) / 6, s[t + 1][1] + (a * s[t][1] - a * s[t + 2][1]) / 6], o[3] = [s[t + 1][0], s[t + 1][1]], n.push(o[1], o[2], o[3]);\n    }\n  }\n  return n;\n}\nfunction N(t, e) {\n  return Math.pow(t[0] - e[0], 2) + Math.pow(t[1] - e[1], 2);\n}\nfunction B(t, e, s) {\n  const n = N(e, s);\n  if (0 === n) return N(t, e);\n  let o = ((t[0] - e[0]) * (s[0] - e[0]) + (t[1] - e[1]) * (s[1] - e[1])) / n;\n  return o = Math.max(0, Math.min(1, o)), N(t, J(e, s, o));\n}\nfunction J(t, e, s) {\n  return [t[0] + (e[0] - t[0]) * s, t[1] + (e[1] - t[1]) * s];\n}\nfunction K(t, e, s, n) {\n  const o = n || [];\n  if (function (t, e) {\n    const s = t[e + 0],\n      n = t[e + 1],\n      o = t[e + 2],\n      a = t[e + 3];\n    let h = 3 * n[0] - 2 * s[0] - a[0];\n    h *= h;\n    let r = 3 * n[1] - 2 * s[1] - a[1];\n    r *= r;\n    let i = 3 * o[0] - 2 * a[0] - s[0];\n    i *= i;\n    let c = 3 * o[1] - 2 * a[1] - s[1];\n    return c *= c, h < i && (h = i), r < c && (r = c), h + r;\n  }(t, e) < s) {\n    const s = t[e + 0];\n    if (o.length) {\n      (a = o[o.length - 1], h = s, Math.sqrt(N(a, h))) > 1 && o.push(s);\n    } else o.push(s);\n    o.push(t[e + 3]);\n  } else {\n    const n = .5,\n      a = t[e + 0],\n      h = t[e + 1],\n      r = t[e + 2],\n      i = t[e + 3],\n      c = J(a, h, n),\n      l = J(h, r, n),\n      u = J(r, i, n),\n      p = J(c, l, n),\n      f = J(l, u, n),\n      d = J(p, f, n);\n    K([a, c, p, d], 0, s, o), K([d, f, u, i], 0, s, o);\n  }\n  var a, h;\n  return o;\n}\nfunction U(t, e) {\n  return X(t, 0, t.length, e);\n}\nfunction X(t, e, s, n, o) {\n  const a = o || [],\n    h = t[e],\n    r = t[s - 1];\n  let i = 0,\n    c = 1;\n  for (let n = e + 1; n < s - 1; ++n) {\n    const e = B(t[n], h, r);\n    e > i && (i = e, c = n);\n  }\n  return Math.sqrt(i) > n ? (X(t, e, c + 1, n, a), X(t, c, s, n, a)) : (a.length || a.push(h), a.push(r)), a;\n}\nfunction Y(t, e = .15, s) {\n  const n = [],\n    o = (t.length - 1) / 3;\n  for (let s = 0; s < o; s++) {\n    K(t, 3 * s, e, n);\n  }\n  return s && s > 0 ? X(n, 0, n.length, s) : n;\n}\nconst tt = \"none\";\nclass et {\n  constructor(t) {\n    this.defaultOptions = {\n      maxRandomnessOffset: 2,\n      roughness: 1,\n      bowing: 1,\n      stroke: \"#000\",\n      strokeWidth: 1,\n      curveTightness: 0,\n      curveFitting: .95,\n      curveStepCount: 9,\n      fillStyle: \"hachure\",\n      fillWeight: -1,\n      hachureAngle: -41,\n      hachureGap: -1,\n      dashOffset: -1,\n      dashGap: -1,\n      zigzagOffset: -1,\n      seed: 0,\n      disableMultiStroke: !1,\n      disableMultiStrokeFill: !1,\n      preserveVertices: !1,\n      fillShapeRoughnessGain: .8\n    }, this.config = t || {}, this.config.options && (this.defaultOptions = this._o(this.config.options));\n  }\n  static newSeed() {\n    return Math.floor(Math.random() * 2 ** 31);\n  }\n  _o(t) {\n    return t ? Object.assign({}, this.defaultOptions, t) : this.defaultOptions;\n  }\n  _d(t, e, s) {\n    return {\n      shape: t,\n      sets: e || [],\n      options: s || this.defaultOptions\n    };\n  }\n  line(t, e, s, n, o) {\n    const a = this._o(o);\n    return this._d(\"line\", [v(t, e, s, n, a)], a);\n  }\n  rectangle(t, e, s, n, o) {\n    const a = this._o(o),\n      h = [],\n      r = O(t, e, s, n, a);\n    if (a.fill) {\n      const o = [[t, e], [t + s, e], [t + s, e + n], [t, e + n]];\n      \"solid\" === a.fillStyle ? h.push(I([o], a)) : h.push(C([o], a));\n    }\n    return a.stroke !== tt && h.push(r), this._d(\"rectangle\", h, a);\n  }\n  ellipse(t, e, s, n, o) {\n    const a = this._o(o),\n      h = [],\n      r = T(s, n, a),\n      i = D(t, e, a, r);\n    if (a.fill) if (\"solid\" === a.fillStyle) {\n      const s = D(t, e, a, r).opset;\n      s.type = \"fillPath\", h.push(s);\n    } else h.push(C([i.estimatedPoints], a));\n    return a.stroke !== tt && h.push(i.opset), this._d(\"ellipse\", h, a);\n  }\n  circle(t, e, s, n) {\n    const o = this.ellipse(t, e, s, s, n);\n    return o.shape = \"circle\", o;\n  }\n  linearPath(t, e) {\n    const s = this._o(e);\n    return this._d(\"linearPath\", [S(t, !1, s)], s);\n  }\n  arc(t, e, s, n, o, a, h = !1, r) {\n    const i = this._o(r),\n      c = [],\n      l = A(t, e, s, n, o, a, h, !0, i);\n    if (h && i.fill) if (\"solid\" === i.fillStyle) {\n      const h = Object.assign({}, i);\n      h.disableMultiStroke = !0;\n      const r = A(t, e, s, n, o, a, !0, !1, h);\n      r.type = \"fillPath\", c.push(r);\n    } else c.push(function (t, e, s, n, o, a, h) {\n      const r = t,\n        i = e;\n      let c = Math.abs(s / 2),\n        l = Math.abs(n / 2);\n      c += G(.01 * c, h), l += G(.01 * l, h);\n      let u = o,\n        p = a;\n      for (; u < 0;) u += 2 * Math.PI, p += 2 * Math.PI;\n      p - u > 2 * Math.PI && (u = 0, p = 2 * Math.PI);\n      const f = (p - u) / h.curveStepCount,\n        d = [];\n      for (let t = u; t <= p; t += f) d.push([r + c * Math.cos(t), i + l * Math.sin(t)]);\n      return d.push([r + c * Math.cos(p), i + l * Math.sin(p)]), d.push([r, i]), C([d], h);\n    }(t, e, s, n, o, a, i));\n    return i.stroke !== tt && c.push(l), this._d(\"arc\", c, i);\n  }\n  curve(t, e) {\n    const s = this._o(e),\n      n = [],\n      o = L(t, s);\n    if (s.fill && s.fill !== tt) if (\"solid\" === s.fillStyle) {\n      const e = L(t, Object.assign(Object.assign({}, s), {\n        disableMultiStroke: !0,\n        roughness: s.roughness ? s.roughness + s.fillShapeRoughnessGain : 0\n      }));\n      n.push({\n        type: \"fillPath\",\n        ops: this._mergedShape(e.ops)\n      });\n    } else {\n      const e = [],\n        o = t;\n      if (o.length) {\n        const t = \"number\" == typeof o[0][0] ? [o] : o;\n        for (const n of t) n.length < 3 ? e.push(...n) : 3 === n.length ? e.push(...Y(H([n[0], n[0], n[1], n[2]]), 10, (1 + s.roughness) / 2)) : e.push(...Y(H(n), 10, (1 + s.roughness) / 2));\n      }\n      e.length && n.push(C([e], s));\n    }\n    return s.stroke !== tt && n.push(o), this._d(\"curve\", n, s);\n  }\n  polygon(t, e) {\n    const s = this._o(e),\n      n = [],\n      o = S(t, !0, s);\n    return s.fill && (\"solid\" === s.fillStyle ? n.push(I([t], s)) : n.push(C([t], s))), s.stroke !== tt && n.push(o), this._d(\"polygon\", n, s);\n  }\n  path(t, e) {\n    const s = this._o(e),\n      n = [];\n    if (!t) return this._d(\"path\", n, s);\n    t = (t || \"\").replace(/\\n/g, \" \").replace(/(-\\s)/g, \"-\").replace(\"/(ss)/g\", \" \");\n    const o = s.fill && \"transparent\" !== s.fill && s.fill !== tt,\n      a = s.stroke !== tt,\n      h = !!(s.simplification && s.simplification < 1),\n      r = function (t, e, s) {\n        const n = m(y(b(t))),\n          o = [];\n        let a = [],\n          h = [0, 0],\n          r = [];\n        const i = () => {\n            r.length >= 4 && a.push(...Y(r, e)), r = [];\n          },\n          c = () => {\n            i(), a.length && (o.push(a), a = []);\n          };\n        for (const {\n          key: t,\n          data: e\n        } of n) switch (t) {\n          case \"M\":\n            c(), h = [e[0], e[1]], a.push(h);\n            break;\n          case \"L\":\n            i(), a.push([e[0], e[1]]);\n            break;\n          case \"C\":\n            if (!r.length) {\n              const t = a.length ? a[a.length - 1] : h;\n              r.push([t[0], t[1]]);\n            }\n            r.push([e[0], e[1]]), r.push([e[2], e[3]]), r.push([e[4], e[5]]);\n            break;\n          case \"Z\":\n            i(), a.push([h[0], h[1]]);\n        }\n        if (c(), !s) return o;\n        const l = [];\n        for (const t of o) {\n          const e = U(t, s);\n          e.length && l.push(e);\n        }\n        return l;\n      }(t, 1, h ? 4 - 4 * (s.simplification || 1) : (1 + s.roughness) / 2),\n      i = _(t, s);\n    if (o) if (\"solid\" === s.fillStyle) {\n      if (1 === r.length) {\n        const e = _(t, Object.assign(Object.assign({}, s), {\n          disableMultiStroke: !0,\n          roughness: s.roughness ? s.roughness + s.fillShapeRoughnessGain : 0\n        }));\n        n.push({\n          type: \"fillPath\",\n          ops: this._mergedShape(e.ops)\n        });\n      } else n.push(I(r, s));\n    } else n.push(C(r, s));\n    return a && (h ? r.forEach(t => {\n      n.push(S(t, !1, s));\n    }) : n.push(i)), this._d(\"path\", n, s);\n  }\n  opsToPath(t, e) {\n    let s = \"\";\n    for (const n of t.ops) {\n      const t = \"number\" == typeof e && e >= 0 ? n.data.map(t => +t.toFixed(e)) : n.data;\n      switch (n.op) {\n        case \"move\":\n          s += `M${t[0]} ${t[1]} `;\n          break;\n        case \"bcurveTo\":\n          s += `C${t[0]} ${t[1]}, ${t[2]} ${t[3]}, ${t[4]} ${t[5]} `;\n          break;\n        case \"lineTo\":\n          s += `L${t[0]} ${t[1]} `;\n      }\n    }\n    return s.trim();\n  }\n  toPaths(t) {\n    const e = t.sets || [],\n      s = t.options || this.defaultOptions,\n      n = [];\n    for (const t of e) {\n      let e = null;\n      switch (t.type) {\n        case \"path\":\n          e = {\n            d: this.opsToPath(t),\n            stroke: s.stroke,\n            strokeWidth: s.strokeWidth,\n            fill: tt\n          };\n          break;\n        case \"fillPath\":\n          e = {\n            d: this.opsToPath(t),\n            stroke: tt,\n            strokeWidth: 0,\n            fill: s.fill || tt\n          };\n          break;\n        case \"fillSketch\":\n          e = this.fillSketch(t, s);\n      }\n      e && n.push(e);\n    }\n    return n;\n  }\n  fillSketch(t, e) {\n    let s = e.fillWeight;\n    return s < 0 && (s = e.strokeWidth / 2), {\n      d: this.opsToPath(t),\n      stroke: e.fill || tt,\n      strokeWidth: s,\n      fill: tt\n    };\n  }\n  _mergedShape(t) {\n    return t.filter((t, e) => 0 === e || \"move\" !== t.op);\n  }\n}\nclass st {\n  constructor(t, e) {\n    this.canvas = t, this.ctx = this.canvas.getContext(\"2d\"), this.gen = new et(e);\n  }\n  draw(t) {\n    const e = t.sets || [],\n      s = t.options || this.getDefaultOptions(),\n      n = this.ctx,\n      o = t.options.fixedDecimalPlaceDigits;\n    for (const a of e) switch (a.type) {\n      case \"path\":\n        n.save(), n.strokeStyle = \"none\" === s.stroke ? \"transparent\" : s.stroke, n.lineWidth = s.strokeWidth, s.strokeLineDash && n.setLineDash(s.strokeLineDash), s.strokeLineDashOffset && (n.lineDashOffset = s.strokeLineDashOffset), this._drawToContext(n, a, o), n.restore();\n        break;\n      case \"fillPath\":\n        {\n          n.save(), n.fillStyle = s.fill || \"\";\n          const e = \"curve\" === t.shape || \"polygon\" === t.shape || \"path\" === t.shape ? \"evenodd\" : \"nonzero\";\n          this._drawToContext(n, a, o, e), n.restore();\n          break;\n        }\n      case \"fillSketch\":\n        this.fillSketch(n, a, s);\n    }\n  }\n  fillSketch(t, e, s) {\n    let n = s.fillWeight;\n    n < 0 && (n = s.strokeWidth / 2), t.save(), s.fillLineDash && t.setLineDash(s.fillLineDash), s.fillLineDashOffset && (t.lineDashOffset = s.fillLineDashOffset), t.strokeStyle = s.fill || \"\", t.lineWidth = n, this._drawToContext(t, e, s.fixedDecimalPlaceDigits), t.restore();\n  }\n  _drawToContext(t, e, s, n = \"nonzero\") {\n    t.beginPath();\n    for (const n of e.ops) {\n      const e = \"number\" == typeof s && s >= 0 ? n.data.map(t => +t.toFixed(s)) : n.data;\n      switch (n.op) {\n        case \"move\":\n          t.moveTo(e[0], e[1]);\n          break;\n        case \"bcurveTo\":\n          t.bezierCurveTo(e[0], e[1], e[2], e[3], e[4], e[5]);\n          break;\n        case \"lineTo\":\n          t.lineTo(e[0], e[1]);\n      }\n    }\n    \"fillPath\" === e.type ? t.fill(n) : t.stroke();\n  }\n  get generator() {\n    return this.gen;\n  }\n  getDefaultOptions() {\n    return this.gen.defaultOptions;\n  }\n  line(t, e, s, n, o) {\n    const a = this.gen.line(t, e, s, n, o);\n    return this.draw(a), a;\n  }\n  rectangle(t, e, s, n, o) {\n    const a = this.gen.rectangle(t, e, s, n, o);\n    return this.draw(a), a;\n  }\n  ellipse(t, e, s, n, o) {\n    const a = this.gen.ellipse(t, e, s, n, o);\n    return this.draw(a), a;\n  }\n  circle(t, e, s, n) {\n    const o = this.gen.circle(t, e, s, n);\n    return this.draw(o), o;\n  }\n  linearPath(t, e) {\n    const s = this.gen.linearPath(t, e);\n    return this.draw(s), s;\n  }\n  polygon(t, e) {\n    const s = this.gen.polygon(t, e);\n    return this.draw(s), s;\n  }\n  arc(t, e, s, n, o, a, h = !1, r) {\n    const i = this.gen.arc(t, e, s, n, o, a, h, r);\n    return this.draw(i), i;\n  }\n  curve(t, e) {\n    const s = this.gen.curve(t, e);\n    return this.draw(s), s;\n  }\n  path(t, e) {\n    const s = this.gen.path(t, e);\n    return this.draw(s), s;\n  }\n}\nconst nt = \"http://www.w3.org/2000/svg\";\nclass ot {\n  constructor(t, e) {\n    this.svg = t, this.gen = new et(e);\n  }\n  draw(t) {\n    const e = t.sets || [],\n      s = t.options || this.getDefaultOptions(),\n      n = this.svg.ownerDocument || window.document,\n      o = n.createElementNS(nt, \"g\"),\n      a = t.options.fixedDecimalPlaceDigits;\n    for (const h of e) {\n      let e = null;\n      switch (h.type) {\n        case \"path\":\n          e = n.createElementNS(nt, \"path\"), e.setAttribute(\"d\", this.opsToPath(h, a)), e.setAttribute(\"stroke\", s.stroke), e.setAttribute(\"stroke-width\", s.strokeWidth + \"\"), e.setAttribute(\"fill\", \"none\"), s.strokeLineDash && e.setAttribute(\"stroke-dasharray\", s.strokeLineDash.join(\" \").trim()), s.strokeLineDashOffset && e.setAttribute(\"stroke-dashoffset\", `${s.strokeLineDashOffset}`);\n          break;\n        case \"fillPath\":\n          e = n.createElementNS(nt, \"path\"), e.setAttribute(\"d\", this.opsToPath(h, a)), e.setAttribute(\"stroke\", \"none\"), e.setAttribute(\"stroke-width\", \"0\"), e.setAttribute(\"fill\", s.fill || \"\"), \"curve\" !== t.shape && \"polygon\" !== t.shape || e.setAttribute(\"fill-rule\", \"evenodd\");\n          break;\n        case \"fillSketch\":\n          e = this.fillSketch(n, h, s);\n      }\n      e && o.appendChild(e);\n    }\n    return o;\n  }\n  fillSketch(t, e, s) {\n    let n = s.fillWeight;\n    n < 0 && (n = s.strokeWidth / 2);\n    const o = t.createElementNS(nt, \"path\");\n    return o.setAttribute(\"d\", this.opsToPath(e, s.fixedDecimalPlaceDigits)), o.setAttribute(\"stroke\", s.fill || \"\"), o.setAttribute(\"stroke-width\", n + \"\"), o.setAttribute(\"fill\", \"none\"), s.fillLineDash && o.setAttribute(\"stroke-dasharray\", s.fillLineDash.join(\" \").trim()), s.fillLineDashOffset && o.setAttribute(\"stroke-dashoffset\", `${s.fillLineDashOffset}`), o;\n  }\n  get generator() {\n    return this.gen;\n  }\n  getDefaultOptions() {\n    return this.gen.defaultOptions;\n  }\n  opsToPath(t, e) {\n    return this.gen.opsToPath(t, e);\n  }\n  line(t, e, s, n, o) {\n    const a = this.gen.line(t, e, s, n, o);\n    return this.draw(a);\n  }\n  rectangle(t, e, s, n, o) {\n    const a = this.gen.rectangle(t, e, s, n, o);\n    return this.draw(a);\n  }\n  ellipse(t, e, s, n, o) {\n    const a = this.gen.ellipse(t, e, s, n, o);\n    return this.draw(a);\n  }\n  circle(t, e, s, n) {\n    const o = this.gen.circle(t, e, s, n);\n    return this.draw(o);\n  }\n  linearPath(t, e) {\n    const s = this.gen.linearPath(t, e);\n    return this.draw(s);\n  }\n  polygon(t, e) {\n    const s = this.gen.polygon(t, e);\n    return this.draw(s);\n  }\n  arc(t, e, s, n, o, a, h = !1, r) {\n    const i = this.gen.arc(t, e, s, n, o, a, h, r);\n    return this.draw(i);\n  }\n  curve(t, e) {\n    const s = this.gen.curve(t, e);\n    return this.draw(s);\n  }\n  path(t, e) {\n    const s = this.gen.path(t, e);\n    return this.draw(s);\n  }\n}\nvar at = {\n  canvas: (t, e) => new st(t, e),\n  svg: (t, e) => new ot(t, e),\n  generator: t => new et(t),\n  newSeed: () => et.newSeed()\n};\nexport { at as default };", "import { getSubGraphTitleMargins } from \"./chunk-K557N5IZ.mjs\";\nimport { getIconSVG } from \"./chunk-H2D2JQ3I.mjs\";\nimport { createText } from \"./chunk-C3MQ5ANM.mjs\";\nimport { calculateTextWidth, decodeEntities, handleUndefinedAttr, parseFontSize } from \"./chunk-O4NI6UNU.mjs\";\nimport { __name, common_default, defaultConfig_default, evaluate, getConfig, getConfig2, hasKatex, log, parseGenericTypes, renderKatex, sanitizeText, sanitizeText2 } from \"./chunk-YTJNT7DU.mjs\";\n\n// src/rendering-util/rendering-elements/shapes/util.ts\nimport { select } from \"d3\";\nvar labelHelper = /* @__PURE__ */__name(async (parent, node, _classes) => {\n  let cssClasses;\n  const useHtmlLabels = node.useHtmlLabels || evaluate(getConfig2()?.htmlLabels);\n  if (!_classes) {\n    cssClasses = \"node default\";\n  } else {\n    cssClasses = _classes;\n  }\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", cssClasses).attr(\"id\", node.domId || node.id);\n  const labelEl = shapeSvg.insert(\"g\").attr(\"class\", \"label\").attr(\"style\", handleUndefinedAttr(node.labelStyle));\n  let label;\n  if (node.label === void 0) {\n    label = \"\";\n  } else {\n    label = typeof node.label === \"string\" ? node.label : node.label[0];\n  }\n  const text2 = await createText(labelEl, sanitizeText(decodeEntities(label), getConfig2()), {\n    useHtmlLabels,\n    width: node.width || getConfig2().flowchart?.wrappingWidth,\n    // @ts-expect-error -- This is currently not used. Should this be `classes` instead?\n    cssClasses: \"markdown-node-label\",\n    style: node.labelStyle,\n    addSvgBackground: !!node.icon || !!node.img\n  });\n  let bbox = text2.getBBox();\n  const halfPadding = (node?.padding ?? 0) / 2;\n  if (useHtmlLabels) {\n    const div = text2.children[0];\n    const dv = select(text2);\n    const images = div.getElementsByTagName(\"img\");\n    if (images) {\n      const noImgText = label.replace(/<img[^>]*>/g, \"\").trim() === \"\";\n      await Promise.all([...images].map(img => new Promise(res => {\n        function setupImage() {\n          img.style.display = \"flex\";\n          img.style.flexDirection = \"column\";\n          if (noImgText) {\n            const bodyFontSize = getConfig2().fontSize ? getConfig2().fontSize : window.getComputedStyle(document.body).fontSize;\n            const enlargingFactor = 5;\n            const [parsedBodyFontSize = defaultConfig_default.fontSize] = parseFontSize(bodyFontSize);\n            const width = parsedBodyFontSize * enlargingFactor + \"px\";\n            img.style.minWidth = width;\n            img.style.maxWidth = width;\n          } else {\n            img.style.width = \"100%\";\n          }\n          res(img);\n        }\n        __name(setupImage, \"setupImage\");\n        setTimeout(() => {\n          if (img.complete) {\n            setupImage();\n          }\n        });\n        img.addEventListener(\"error\", setupImage);\n        img.addEventListener(\"load\", setupImage);\n      })));\n    }\n    bbox = div.getBoundingClientRect();\n    dv.attr(\"width\", bbox.width);\n    dv.attr(\"height\", bbox.height);\n  }\n  if (useHtmlLabels) {\n    labelEl.attr(\"transform\", \"translate(\" + -bbox.width / 2 + \", \" + -bbox.height / 2 + \")\");\n  } else {\n    labelEl.attr(\"transform\", \"translate(0, \" + -bbox.height / 2 + \")\");\n  }\n  if (node.centerLabel) {\n    labelEl.attr(\"transform\", \"translate(\" + -bbox.width / 2 + \", \" + -bbox.height / 2 + \")\");\n  }\n  labelEl.insert(\"rect\", \":first-child\");\n  return {\n    shapeSvg,\n    bbox,\n    halfPadding,\n    label: labelEl\n  };\n}, \"labelHelper\");\nvar insertLabel = /* @__PURE__ */__name(async (parent, label, options) => {\n  const useHtmlLabels = options.useHtmlLabels || evaluate(getConfig2()?.flowchart?.htmlLabels);\n  const labelEl = parent.insert(\"g\").attr(\"class\", \"label\").attr(\"style\", options.labelStyle || \"\");\n  const text2 = await createText(labelEl, sanitizeText(decodeEntities(label), getConfig2()), {\n    useHtmlLabels,\n    width: options.width || getConfig2()?.flowchart?.wrappingWidth,\n    style: options.labelStyle,\n    addSvgBackground: !!options.icon || !!options.img\n  });\n  let bbox = text2.getBBox();\n  const halfPadding = options.padding / 2;\n  if (evaluate(getConfig2()?.flowchart?.htmlLabels)) {\n    const div = text2.children[0];\n    const dv = select(text2);\n    bbox = div.getBoundingClientRect();\n    dv.attr(\"width\", bbox.width);\n    dv.attr(\"height\", bbox.height);\n  }\n  if (useHtmlLabels) {\n    labelEl.attr(\"transform\", \"translate(\" + -bbox.width / 2 + \", \" + -bbox.height / 2 + \")\");\n  } else {\n    labelEl.attr(\"transform\", \"translate(0, \" + -bbox.height / 2 + \")\");\n  }\n  if (options.centerLabel) {\n    labelEl.attr(\"transform\", \"translate(\" + -bbox.width / 2 + \", \" + -bbox.height / 2 + \")\");\n  }\n  labelEl.insert(\"rect\", \":first-child\");\n  return {\n    shapeSvg: parent,\n    bbox,\n    halfPadding,\n    label: labelEl\n  };\n}, \"insertLabel\");\nvar updateNodeBounds = /* @__PURE__ */__name((node, element) => {\n  const bbox = element.node().getBBox();\n  node.width = bbox.width;\n  node.height = bbox.height;\n}, \"updateNodeBounds\");\nvar getNodeClasses = /* @__PURE__ */__name((node, extra) => (node.look === \"handDrawn\" ? \"rough-node\" : \"node\") + \" \" + node.cssClasses + \" \" + (extra || \"\"), \"getNodeClasses\");\nfunction createPathFromPoints(points) {\n  const pointStrings = points.map((p, i) => `${i === 0 ? \"M\" : \"L\"}${p.x},${p.y}`);\n  pointStrings.push(\"Z\");\n  return pointStrings.join(\" \");\n}\n__name(createPathFromPoints, \"createPathFromPoints\");\nfunction generateFullSineWavePoints(x1, y1, x2, y2, amplitude, numCycles) {\n  const points = [];\n  const steps = 50;\n  const deltaX = x2 - x1;\n  const deltaY = y2 - y1;\n  const cycleLength = deltaX / numCycles;\n  const frequency = 2 * Math.PI / cycleLength;\n  const midY = y1 + deltaY / 2;\n  for (let i = 0; i <= steps; i++) {\n    const t = i / steps;\n    const x = x1 + t * deltaX;\n    const y = midY + amplitude * Math.sin(frequency * (x - x1));\n    points.push({\n      x,\n      y\n    });\n  }\n  return points;\n}\n__name(generateFullSineWavePoints, \"generateFullSineWavePoints\");\nfunction generateCirclePoints(centerX, centerY, radius, numPoints, startAngle, endAngle) {\n  const points = [];\n  const startAngleRad = startAngle * Math.PI / 180;\n  const endAngleRad = endAngle * Math.PI / 180;\n  const angleRange = endAngleRad - startAngleRad;\n  const angleStep = angleRange / (numPoints - 1);\n  for (let i = 0; i < numPoints; i++) {\n    const angle = startAngleRad + i * angleStep;\n    const x = centerX + radius * Math.cos(angle);\n    const y = centerY + radius * Math.sin(angle);\n    points.push({\n      x: -x,\n      y: -y\n    });\n  }\n  return points;\n}\n__name(generateCirclePoints, \"generateCirclePoints\");\n\n// src/rendering-util/rendering-elements/clusters.js\nimport { select as select3 } from \"d3\";\nimport rough from \"roughjs\";\n\n// src/rendering-util/rendering-elements/intersect/intersect-rect.js\nvar intersectRect = /* @__PURE__ */__name((node, point) => {\n  var x = node.x;\n  var y = node.y;\n  var dx = point.x - x;\n  var dy = point.y - y;\n  var w = node.width / 2;\n  var h = node.height / 2;\n  var sx, sy;\n  if (Math.abs(dy) * w > Math.abs(dx) * h) {\n    if (dy < 0) {\n      h = -h;\n    }\n    sx = dy === 0 ? 0 : h * dx / dy;\n    sy = h;\n  } else {\n    if (dx < 0) {\n      w = -w;\n    }\n    sx = w;\n    sy = dx === 0 ? 0 : w * dy / dx;\n  }\n  return {\n    x: x + sx,\n    y: y + sy\n  };\n}, \"intersectRect\");\nvar intersect_rect_default = intersectRect;\n\n// src/rendering-util/rendering-elements/createLabel.js\nimport { select as select2 } from \"d3\";\nfunction applyStyle(dom, styleFn) {\n  if (styleFn) {\n    dom.attr(\"style\", styleFn);\n  }\n}\n__name(applyStyle, \"applyStyle\");\nasync function addHtmlLabel(node) {\n  const fo = select2(document.createElementNS(\"http://www.w3.org/2000/svg\", \"foreignObject\"));\n  const div = fo.append(\"xhtml:div\");\n  let label = node.label;\n  if (node.label && hasKatex(node.label)) {\n    label = await renderKatex(node.label.replace(common_default.lineBreakRegex, \"\\n\"), getConfig2());\n  }\n  const labelClass = node.isNode ? \"nodeLabel\" : \"edgeLabel\";\n  div.html('<span class=\"' + labelClass + '\" ' + (node.labelStyle ? 'style=\"' + node.labelStyle + '\"' : \"\") +\n  // codeql [js/html-constructed-from-input] : false positive\n  \">\" + label + \"</span>\");\n  applyStyle(div, node.labelStyle);\n  div.style(\"display\", \"inline-block\");\n  div.style(\"padding-right\", \"1px\");\n  div.style(\"white-space\", \"nowrap\");\n  div.attr(\"xmlns\", \"http://www.w3.org/1999/xhtml\");\n  return fo.node();\n}\n__name(addHtmlLabel, \"addHtmlLabel\");\nvar createLabel = /* @__PURE__ */__name(async (_vertexText, style, isTitle, isNode) => {\n  let vertexText = _vertexText || \"\";\n  if (typeof vertexText === \"object\") {\n    vertexText = vertexText[0];\n  }\n  if (evaluate(getConfig2().flowchart.htmlLabels)) {\n    vertexText = vertexText.replace(/\\\\n|\\n/g, \"<br />\");\n    log.info(\"vertexText\" + vertexText);\n    const node = {\n      isNode,\n      label: decodeEntities(vertexText).replace(/fa[blrs]?:fa-[\\w-]+/g, s => `<i class='${s.replace(\":\", \" \")}'></i>`),\n      labelStyle: style ? style.replace(\"fill:\", \"color:\") : style\n    };\n    let vertexNode = await addHtmlLabel(node);\n    return vertexNode;\n  } else {\n    const svgLabel = document.createElementNS(\"http://www.w3.org/2000/svg\", \"text\");\n    svgLabel.setAttribute(\"style\", style.replace(\"color:\", \"fill:\"));\n    let rows = [];\n    if (typeof vertexText === \"string\") {\n      rows = vertexText.split(/\\\\n|\\n|<br\\s*\\/?>/gi);\n    } else if (Array.isArray(vertexText)) {\n      rows = vertexText;\n    } else {\n      rows = [];\n    }\n    for (const row of rows) {\n      const tspan = document.createElementNS(\"http://www.w3.org/2000/svg\", \"tspan\");\n      tspan.setAttributeNS(\"http://www.w3.org/XML/1998/namespace\", \"xml:space\", \"preserve\");\n      tspan.setAttribute(\"dy\", \"1em\");\n      tspan.setAttribute(\"x\", \"0\");\n      if (isTitle) {\n        tspan.setAttribute(\"class\", \"title-row\");\n      } else {\n        tspan.setAttribute(\"class\", \"row\");\n      }\n      tspan.textContent = row.trim();\n      svgLabel.appendChild(tspan);\n    }\n    return svgLabel;\n  }\n}, \"createLabel\");\nvar createLabel_default = createLabel;\n\n// src/rendering-util/rendering-elements/shapes/roundedRectPath.ts\nvar createRoundedRectPathD = /* @__PURE__ */__name((x, y, totalWidth, totalHeight, radius) => [\"M\", x + radius, y,\n// Move to the first point\n\"H\", x + totalWidth - radius,\n// Draw horizontal line to the beginning of the right corner\n\"A\", radius, radius, 0, 0, 1, x + totalWidth, y + radius,\n// Draw arc to the right top corner\n\"V\", y + totalHeight - radius,\n// Draw vertical line down to the beginning of the right bottom corner\n\"A\", radius, radius, 0, 0, 1, x + totalWidth - radius, y + totalHeight,\n// Draw arc to the right bottom corner\n\"H\", x + radius,\n// Draw horizontal line to the beginning of the left bottom corner\n\"A\", radius, radius, 0, 0, 1, x, y + totalHeight - radius,\n// Draw arc to the left bottom corner\n\"V\", y + radius,\n// Draw vertical line up to the beginning of the left top corner\n\"A\", radius, radius, 0, 0, 1, x + radius, y,\n// Draw arc to the left top corner\n\"Z\"\n// Close the path\n].join(\" \"), \"createRoundedRectPathD\");\n\n// src/rendering-util/rendering-elements/shapes/handDrawnShapeStyles.ts\nvar solidStateFill = /* @__PURE__ */__name(color => {\n  const {\n    handDrawnSeed\n  } = getConfig2();\n  return {\n    fill: color,\n    hachureAngle: 120,\n    // angle of hachure,\n    hachureGap: 4,\n    fillWeight: 2,\n    roughness: 0.7,\n    stroke: color,\n    seed: handDrawnSeed\n  };\n}, \"solidStateFill\");\nvar compileStyles = /* @__PURE__ */__name(node => {\n  const stylesMap = styles2Map([...(node.cssCompiledStyles || []), ...(node.cssStyles || [])]);\n  return {\n    stylesMap,\n    stylesArray: [...stylesMap]\n  };\n}, \"compileStyles\");\nvar styles2Map = /* @__PURE__ */__name(styles => {\n  const styleMap = /* @__PURE__ */new Map();\n  styles.forEach(style => {\n    const [key, value] = style.split(\":\");\n    styleMap.set(key.trim(), value?.trim());\n  });\n  return styleMap;\n}, \"styles2Map\");\nvar isLabelStyle = /* @__PURE__ */__name(key => {\n  return key === \"color\" || key === \"font-size\" || key === \"font-family\" || key === \"font-weight\" || key === \"font-style\" || key === \"text-decoration\" || key === \"text-align\" || key === \"text-transform\" || key === \"line-height\" || key === \"letter-spacing\" || key === \"word-spacing\" || key === \"text-shadow\" || key === \"text-overflow\" || key === \"white-space\" || key === \"word-wrap\" || key === \"word-break\" || key === \"overflow-wrap\" || key === \"hyphens\";\n}, \"isLabelStyle\");\nvar styles2String = /* @__PURE__ */__name(node => {\n  const {\n    stylesArray\n  } = compileStyles(node);\n  const labelStyles = [];\n  const nodeStyles = [];\n  const borderStyles = [];\n  const backgroundStyles = [];\n  stylesArray.forEach(style => {\n    const key = style[0];\n    if (isLabelStyle(key)) {\n      labelStyles.push(style.join(\":\") + \" !important\");\n    } else {\n      nodeStyles.push(style.join(\":\") + \" !important\");\n      if (key.includes(\"stroke\")) {\n        borderStyles.push(style.join(\":\") + \" !important\");\n      }\n      if (key === \"fill\") {\n        backgroundStyles.push(style.join(\":\") + \" !important\");\n      }\n    }\n  });\n  return {\n    labelStyles: labelStyles.join(\";\"),\n    nodeStyles: nodeStyles.join(\";\"),\n    stylesArray,\n    borderStyles,\n    backgroundStyles\n  };\n}, \"styles2String\");\nvar userNodeOverrides = /* @__PURE__ */__name((node, options) => {\n  const {\n    themeVariables,\n    handDrawnSeed\n  } = getConfig2();\n  const {\n    nodeBorder,\n    mainBkg\n  } = themeVariables;\n  const {\n    stylesMap\n  } = compileStyles(node);\n  const result = Object.assign({\n    roughness: 0.7,\n    fill: stylesMap.get(\"fill\") || mainBkg,\n    fillStyle: \"hachure\",\n    // solid fill\n    fillWeight: 4,\n    hachureGap: 5.2,\n    stroke: stylesMap.get(\"stroke\") || nodeBorder,\n    seed: handDrawnSeed,\n    strokeWidth: stylesMap.get(\"stroke-width\")?.replace(\"px\", \"\") || 1.3,\n    fillLineDash: [0, 0]\n  }, options);\n  return result;\n}, \"userNodeOverrides\");\n\n// src/rendering-util/rendering-elements/clusters.js\nvar rect = /* @__PURE__ */__name(async (parent, node) => {\n  log.info(\"Creating subgraph rect for \", node.id, node);\n  const siteConfig = getConfig2();\n  const {\n    themeVariables,\n    handDrawnSeed\n  } = siteConfig;\n  const {\n    clusterBkg,\n    clusterBorder\n  } = themeVariables;\n  const {\n    labelStyles,\n    nodeStyles,\n    borderStyles,\n    backgroundStyles\n  } = styles2String(node);\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", \"cluster \" + node.cssClasses).attr(\"id\", node.id).attr(\"data-look\", node.look);\n  const useHtmlLabels = evaluate(siteConfig.flowchart.htmlLabels);\n  const labelEl = shapeSvg.insert(\"g\").attr(\"class\", \"cluster-label \");\n  const text2 = await createText(labelEl, node.label, {\n    style: node.labelStyle,\n    useHtmlLabels,\n    isNode: true\n  });\n  let bbox = text2.getBBox();\n  if (evaluate(siteConfig.flowchart.htmlLabels)) {\n    const div = text2.children[0];\n    const dv = select3(text2);\n    bbox = div.getBoundingClientRect();\n    dv.attr(\"width\", bbox.width);\n    dv.attr(\"height\", bbox.height);\n  }\n  const width = node.width <= bbox.width + node.padding ? bbox.width + node.padding : node.width;\n  if (node.width <= bbox.width + node.padding) {\n    node.diff = (width - node.width) / 2 - node.padding;\n  } else {\n    node.diff = -node.padding;\n  }\n  const height = node.height;\n  const x = node.x - width / 2;\n  const y = node.y - height / 2;\n  log.trace(\"Data \", node, JSON.stringify(node));\n  let rect2;\n  if (node.look === \"handDrawn\") {\n    const rc = rough.svg(shapeSvg);\n    const options = userNodeOverrides(node, {\n      roughness: 0.7,\n      fill: clusterBkg,\n      // fill: 'red',\n      stroke: clusterBorder,\n      fillWeight: 3,\n      seed: handDrawnSeed\n    });\n    const roughNode = rc.path(createRoundedRectPathD(x, y, width, height, 0), options);\n    rect2 = shapeSvg.insert(() => {\n      log.debug(\"Rough node insert CXC\", roughNode);\n      return roughNode;\n    }, \":first-child\");\n    rect2.select(\"path:nth-child(2)\").attr(\"style\", borderStyles.join(\";\"));\n    rect2.select(\"path\").attr(\"style\", backgroundStyles.join(\";\").replace(\"fill\", \"stroke\"));\n  } else {\n    rect2 = shapeSvg.insert(\"rect\", \":first-child\");\n    rect2.attr(\"style\", nodeStyles).attr(\"rx\", node.rx).attr(\"ry\", node.ry).attr(\"x\", x).attr(\"y\", y).attr(\"width\", width).attr(\"height\", height);\n  }\n  const {\n    subGraphTitleTopMargin\n  } = getSubGraphTitleMargins(siteConfig);\n  labelEl.attr(\"transform\",\n  // This puts the label on top of the box instead of inside it\n  `translate(${node.x - bbox.width / 2}, ${node.y - node.height / 2 + subGraphTitleTopMargin})`);\n  if (labelStyles) {\n    const span = labelEl.select(\"span\");\n    if (span) {\n      span.attr(\"style\", labelStyles);\n    }\n  }\n  const rectBox = rect2.node().getBBox();\n  node.offsetX = 0;\n  node.width = rectBox.width;\n  node.height = rectBox.height;\n  node.offsetY = bbox.height - node.padding / 2;\n  node.intersect = function (point) {\n    return intersect_rect_default(node, point);\n  };\n  return {\n    cluster: shapeSvg,\n    labelBBox: bbox\n  };\n}, \"rect\");\nvar noteGroup = /* @__PURE__ */__name((parent, node) => {\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", \"note-cluster\").attr(\"id\", node.id);\n  const rect2 = shapeSvg.insert(\"rect\", \":first-child\");\n  const padding = 0 * node.padding;\n  const halfPadding = padding / 2;\n  rect2.attr(\"rx\", node.rx).attr(\"ry\", node.ry).attr(\"x\", node.x - node.width / 2 - halfPadding).attr(\"y\", node.y - node.height / 2 - halfPadding).attr(\"width\", node.width + padding).attr(\"height\", node.height + padding).attr(\"fill\", \"none\");\n  const rectBox = rect2.node().getBBox();\n  node.width = rectBox.width;\n  node.height = rectBox.height;\n  node.intersect = function (point) {\n    return intersect_rect_default(node, point);\n  };\n  return {\n    cluster: shapeSvg,\n    labelBBox: {\n      width: 0,\n      height: 0\n    }\n  };\n}, \"noteGroup\");\nvar roundedWithTitle = /* @__PURE__ */__name(async (parent, node) => {\n  const siteConfig = getConfig2();\n  const {\n    themeVariables,\n    handDrawnSeed\n  } = siteConfig;\n  const {\n    altBackground,\n    compositeBackground,\n    compositeTitleBackground,\n    nodeBorder\n  } = themeVariables;\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", node.cssClasses).attr(\"id\", node.id).attr(\"data-id\", node.id).attr(\"data-look\", node.look);\n  const outerRectG = shapeSvg.insert(\"g\", \":first-child\");\n  const label = shapeSvg.insert(\"g\").attr(\"class\", \"cluster-label\");\n  let innerRect = shapeSvg.append(\"rect\");\n  const text2 = label.node().appendChild(await createLabel_default(node.label, node.labelStyle, void 0, true));\n  let bbox = text2.getBBox();\n  if (evaluate(siteConfig.flowchart.htmlLabels)) {\n    const div = text2.children[0];\n    const dv = select3(text2);\n    bbox = div.getBoundingClientRect();\n    dv.attr(\"width\", bbox.width);\n    dv.attr(\"height\", bbox.height);\n  }\n  const padding = 0 * node.padding;\n  const halfPadding = padding / 2;\n  const width = (node.width <= bbox.width + node.padding ? bbox.width + node.padding : node.width) + padding;\n  if (node.width <= bbox.width + node.padding) {\n    node.diff = (width - node.width) / 2 - node.padding;\n  } else {\n    node.diff = -node.padding;\n  }\n  const height = node.height + padding;\n  const innerHeight = node.height + padding - bbox.height - 6;\n  const x = node.x - width / 2;\n  const y = node.y - height / 2;\n  node.width = width;\n  const innerY = node.y - node.height / 2 - halfPadding + bbox.height + 2;\n  let rect2;\n  if (node.look === \"handDrawn\") {\n    const isAlt = node.cssClasses.includes(\"statediagram-cluster-alt\");\n    const rc = rough.svg(shapeSvg);\n    const roughOuterNode = node.rx || node.ry ? rc.path(createRoundedRectPathD(x, y, width, height, 10), {\n      roughness: 0.7,\n      fill: compositeTitleBackground,\n      fillStyle: \"solid\",\n      stroke: nodeBorder,\n      seed: handDrawnSeed\n    }) : rc.rectangle(x, y, width, height, {\n      seed: handDrawnSeed\n    });\n    rect2 = shapeSvg.insert(() => roughOuterNode, \":first-child\");\n    const roughInnerNode = rc.rectangle(x, innerY, width, innerHeight, {\n      fill: isAlt ? altBackground : compositeBackground,\n      fillStyle: isAlt ? \"hachure\" : \"solid\",\n      stroke: nodeBorder,\n      seed: handDrawnSeed\n    });\n    rect2 = shapeSvg.insert(() => roughOuterNode, \":first-child\");\n    innerRect = shapeSvg.insert(() => roughInnerNode);\n  } else {\n    rect2 = outerRectG.insert(\"rect\", \":first-child\");\n    const outerRectClass = \"outer\";\n    rect2.attr(\"class\", outerRectClass).attr(\"x\", x).attr(\"y\", y).attr(\"width\", width).attr(\"height\", height).attr(\"data-look\", node.look);\n    innerRect.attr(\"class\", \"inner\").attr(\"x\", x).attr(\"y\", innerY).attr(\"width\", width).attr(\"height\", innerHeight);\n  }\n  label.attr(\"transform\", `translate(${node.x - bbox.width / 2}, ${y + 1 - (evaluate(siteConfig.flowchart.htmlLabels) ? 0 : 3)})`);\n  const rectBox = rect2.node().getBBox();\n  node.height = rectBox.height;\n  node.offsetX = 0;\n  node.offsetY = bbox.height - node.padding / 2;\n  node.labelBBox = bbox;\n  node.intersect = function (point) {\n    return intersect_rect_default(node, point);\n  };\n  return {\n    cluster: shapeSvg,\n    labelBBox: bbox\n  };\n}, \"roundedWithTitle\");\nvar kanbanSection = /* @__PURE__ */__name(async (parent, node) => {\n  log.info(\"Creating subgraph rect for \", node.id, node);\n  const siteConfig = getConfig2();\n  const {\n    themeVariables,\n    handDrawnSeed\n  } = siteConfig;\n  const {\n    clusterBkg,\n    clusterBorder\n  } = themeVariables;\n  const {\n    labelStyles,\n    nodeStyles,\n    borderStyles,\n    backgroundStyles\n  } = styles2String(node);\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", \"cluster \" + node.cssClasses).attr(\"id\", node.id).attr(\"data-look\", node.look);\n  const useHtmlLabels = evaluate(siteConfig.flowchart.htmlLabels);\n  const labelEl = shapeSvg.insert(\"g\").attr(\"class\", \"cluster-label \");\n  const text2 = await createText(labelEl, node.label, {\n    style: node.labelStyle,\n    useHtmlLabels,\n    isNode: true,\n    width: node.width\n  });\n  let bbox = text2.getBBox();\n  if (evaluate(siteConfig.flowchart.htmlLabels)) {\n    const div = text2.children[0];\n    const dv = select3(text2);\n    bbox = div.getBoundingClientRect();\n    dv.attr(\"width\", bbox.width);\n    dv.attr(\"height\", bbox.height);\n  }\n  const width = node.width <= bbox.width + node.padding ? bbox.width + node.padding : node.width;\n  if (node.width <= bbox.width + node.padding) {\n    node.diff = (width - node.width) / 2 - node.padding;\n  } else {\n    node.diff = -node.padding;\n  }\n  const height = node.height;\n  const x = node.x - width / 2;\n  const y = node.y - height / 2;\n  log.trace(\"Data \", node, JSON.stringify(node));\n  let rect2;\n  if (node.look === \"handDrawn\") {\n    const rc = rough.svg(shapeSvg);\n    const options = userNodeOverrides(node, {\n      roughness: 0.7,\n      fill: clusterBkg,\n      // fill: 'red',\n      stroke: clusterBorder,\n      fillWeight: 4,\n      seed: handDrawnSeed\n    });\n    const roughNode = rc.path(createRoundedRectPathD(x, y, width, height, node.rx), options);\n    rect2 = shapeSvg.insert(() => {\n      log.debug(\"Rough node insert CXC\", roughNode);\n      return roughNode;\n    }, \":first-child\");\n    rect2.select(\"path:nth-child(2)\").attr(\"style\", borderStyles.join(\";\"));\n    rect2.select(\"path\").attr(\"style\", backgroundStyles.join(\";\").replace(\"fill\", \"stroke\"));\n  } else {\n    rect2 = shapeSvg.insert(\"rect\", \":first-child\");\n    rect2.attr(\"style\", nodeStyles).attr(\"rx\", node.rx).attr(\"ry\", node.ry).attr(\"x\", x).attr(\"y\", y).attr(\"width\", width).attr(\"height\", height);\n  }\n  const {\n    subGraphTitleTopMargin\n  } = getSubGraphTitleMargins(siteConfig);\n  labelEl.attr(\"transform\",\n  // This puts the label on top of the box instead of inside it\n  `translate(${node.x - bbox.width / 2}, ${node.y - node.height / 2 + subGraphTitleTopMargin})`);\n  if (labelStyles) {\n    const span = labelEl.select(\"span\");\n    if (span) {\n      span.attr(\"style\", labelStyles);\n    }\n  }\n  const rectBox = rect2.node().getBBox();\n  node.offsetX = 0;\n  node.width = rectBox.width;\n  node.height = rectBox.height;\n  node.offsetY = bbox.height - node.padding / 2;\n  node.intersect = function (point) {\n    return intersect_rect_default(node, point);\n  };\n  return {\n    cluster: shapeSvg,\n    labelBBox: bbox\n  };\n}, \"kanbanSection\");\nvar divider = /* @__PURE__ */__name((parent, node) => {\n  const siteConfig = getConfig2();\n  const {\n    themeVariables,\n    handDrawnSeed\n  } = siteConfig;\n  const {\n    nodeBorder\n  } = themeVariables;\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", node.cssClasses).attr(\"id\", node.id).attr(\"data-look\", node.look);\n  const outerRectG = shapeSvg.insert(\"g\", \":first-child\");\n  const padding = 0 * node.padding;\n  const width = node.width + padding;\n  node.diff = -node.padding;\n  const height = node.height + padding;\n  const x = node.x - width / 2;\n  const y = node.y - height / 2;\n  node.width = width;\n  let rect2;\n  if (node.look === \"handDrawn\") {\n    const rc = rough.svg(shapeSvg);\n    const roughOuterNode = rc.rectangle(x, y, width, height, {\n      fill: \"lightgrey\",\n      roughness: 0.5,\n      strokeLineDash: [5],\n      stroke: nodeBorder,\n      seed: handDrawnSeed\n    });\n    rect2 = shapeSvg.insert(() => roughOuterNode, \":first-child\");\n  } else {\n    rect2 = outerRectG.insert(\"rect\", \":first-child\");\n    const outerRectClass = \"divider\";\n    rect2.attr(\"class\", outerRectClass).attr(\"x\", x).attr(\"y\", y).attr(\"width\", width).attr(\"height\", height).attr(\"data-look\", node.look);\n  }\n  const rectBox = rect2.node().getBBox();\n  node.height = rectBox.height;\n  node.offsetX = 0;\n  node.offsetY = 0;\n  node.intersect = function (point) {\n    return intersect_rect_default(node, point);\n  };\n  return {\n    cluster: shapeSvg,\n    labelBBox: {}\n  };\n}, \"divider\");\nvar squareRect = rect;\nvar shapes = {\n  rect,\n  squareRect,\n  roundedWithTitle,\n  noteGroup,\n  divider,\n  kanbanSection\n};\nvar clusterElems = /* @__PURE__ */new Map();\nvar insertCluster = /* @__PURE__ */__name(async (elem, node) => {\n  const shape = node.shape || \"rect\";\n  const cluster = await shapes[shape](elem, node);\n  clusterElems.set(node.id, cluster);\n  return cluster;\n}, \"insertCluster\");\nvar clear = /* @__PURE__ */__name(() => {\n  clusterElems = /* @__PURE__ */new Map();\n}, \"clear\");\n\n// src/rendering-util/rendering-elements/intersect/intersect-node.js\nfunction intersectNode(node, point) {\n  return node.intersect(point);\n}\n__name(intersectNode, \"intersectNode\");\nvar intersect_node_default = intersectNode;\n\n// src/rendering-util/rendering-elements/intersect/intersect-ellipse.js\nfunction intersectEllipse(node, rx, ry, point) {\n  var cx = node.x;\n  var cy = node.y;\n  var px = cx - point.x;\n  var py = cy - point.y;\n  var det = Math.sqrt(rx * rx * py * py + ry * ry * px * px);\n  var dx = Math.abs(rx * ry * px / det);\n  if (point.x < cx) {\n    dx = -dx;\n  }\n  var dy = Math.abs(rx * ry * py / det);\n  if (point.y < cy) {\n    dy = -dy;\n  }\n  return {\n    x: cx + dx,\n    y: cy + dy\n  };\n}\n__name(intersectEllipse, \"intersectEllipse\");\nvar intersect_ellipse_default = intersectEllipse;\n\n// src/rendering-util/rendering-elements/intersect/intersect-circle.js\nfunction intersectCircle(node, rx, point) {\n  return intersect_ellipse_default(node, rx, rx, point);\n}\n__name(intersectCircle, \"intersectCircle\");\nvar intersect_circle_default = intersectCircle;\n\n// src/rendering-util/rendering-elements/intersect/intersect-line.js\nfunction intersectLine(p1, p2, q1, q2) {\n  var a1, a2, b1, b2, c1, c2;\n  var r1, r2, r3, r4;\n  var denom, offset, num;\n  var x, y;\n  a1 = p2.y - p1.y;\n  b1 = p1.x - p2.x;\n  c1 = p2.x * p1.y - p1.x * p2.y;\n  r3 = a1 * q1.x + b1 * q1.y + c1;\n  r4 = a1 * q2.x + b1 * q2.y + c1;\n  if (r3 !== 0 && r4 !== 0 && sameSign(r3, r4)) {\n    return;\n  }\n  a2 = q2.y - q1.y;\n  b2 = q1.x - q2.x;\n  c2 = q2.x * q1.y - q1.x * q2.y;\n  r1 = a2 * p1.x + b2 * p1.y + c2;\n  r2 = a2 * p2.x + b2 * p2.y + c2;\n  if (r1 !== 0 && r2 !== 0 && sameSign(r1, r2)) {\n    return;\n  }\n  denom = a1 * b2 - a2 * b1;\n  if (denom === 0) {\n    return;\n  }\n  offset = Math.abs(denom / 2);\n  num = b1 * c2 - b2 * c1;\n  x = num < 0 ? (num - offset) / denom : (num + offset) / denom;\n  num = a2 * c1 - a1 * c2;\n  y = num < 0 ? (num - offset) / denom : (num + offset) / denom;\n  return {\n    x,\n    y\n  };\n}\n__name(intersectLine, \"intersectLine\");\nfunction sameSign(r1, r2) {\n  return r1 * r2 > 0;\n}\n__name(sameSign, \"sameSign\");\nvar intersect_line_default = intersectLine;\n\n// src/rendering-util/rendering-elements/intersect/intersect-polygon.js\nfunction intersectPolygon(node, polyPoints, point) {\n  let x1 = node.x;\n  let y1 = node.y;\n  let intersections = [];\n  let minX = Number.POSITIVE_INFINITY;\n  let minY = Number.POSITIVE_INFINITY;\n  if (typeof polyPoints.forEach === \"function\") {\n    polyPoints.forEach(function (entry) {\n      minX = Math.min(minX, entry.x);\n      minY = Math.min(minY, entry.y);\n    });\n  } else {\n    minX = Math.min(minX, polyPoints.x);\n    minY = Math.min(minY, polyPoints.y);\n  }\n  let left = x1 - node.width / 2 - minX;\n  let top = y1 - node.height / 2 - minY;\n  for (let i = 0; i < polyPoints.length; i++) {\n    let p1 = polyPoints[i];\n    let p2 = polyPoints[i < polyPoints.length - 1 ? i + 1 : 0];\n    let intersect = intersect_line_default(node, point, {\n      x: left + p1.x,\n      y: top + p1.y\n    }, {\n      x: left + p2.x,\n      y: top + p2.y\n    });\n    if (intersect) {\n      intersections.push(intersect);\n    }\n  }\n  if (!intersections.length) {\n    return node;\n  }\n  if (intersections.length > 1) {\n    intersections.sort(function (p, q) {\n      let pdx = p.x - point.x;\n      let pdy = p.y - point.y;\n      let distp = Math.sqrt(pdx * pdx + pdy * pdy);\n      let qdx = q.x - point.x;\n      let qdy = q.y - point.y;\n      let distq = Math.sqrt(qdx * qdx + qdy * qdy);\n      return distp < distq ? -1 : distp === distq ? 0 : 1;\n    });\n  }\n  return intersections[0];\n}\n__name(intersectPolygon, \"intersectPolygon\");\nvar intersect_polygon_default = intersectPolygon;\n\n// src/rendering-util/rendering-elements/intersect/index.js\nvar intersect_default = {\n  node: intersect_node_default,\n  circle: intersect_circle_default,\n  ellipse: intersect_ellipse_default,\n  polygon: intersect_polygon_default,\n  rect: intersect_rect_default\n};\n\n// src/rendering-util/rendering-elements/shapes/anchor.ts\nimport rough2 from \"roughjs\";\nfunction anchor(parent, node) {\n  const {\n    labelStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const classes = getNodeClasses(node);\n  let cssClasses = classes;\n  if (!classes) {\n    cssClasses = \"anchor\";\n  }\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", cssClasses).attr(\"id\", node.domId || node.id);\n  const radius = 1;\n  const {\n    cssStyles\n  } = node;\n  const rc = rough2.svg(shapeSvg);\n  const options = userNodeOverrides(node, {\n    fill: \"black\",\n    stroke: \"none\",\n    fillStyle: \"solid\"\n  });\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n  }\n  const roughNode = rc.circle(0, 0, radius * 2, options);\n  const circleElem = shapeSvg.insert(() => roughNode, \":first-child\");\n  circleElem.attr(\"class\", \"anchor\").attr(\"style\", handleUndefinedAttr(cssStyles));\n  updateNodeBounds(node, circleElem);\n  node.intersect = function (point) {\n    log.info(\"Circle intersect\", node, radius, point);\n    return intersect_default.circle(node, radius, point);\n  };\n  return shapeSvg;\n}\n__name(anchor, \"anchor\");\n\n// src/rendering-util/rendering-elements/shapes/bowTieRect.ts\nimport rough3 from \"roughjs\";\nfunction generateArcPoints(x1, y1, x2, y2, rx, ry, clockwise) {\n  const numPoints = 20;\n  const midX = (x1 + x2) / 2;\n  const midY = (y1 + y2) / 2;\n  const angle = Math.atan2(y2 - y1, x2 - x1);\n  const dx = (x2 - x1) / 2;\n  const dy = (y2 - y1) / 2;\n  const transformedX = dx / rx;\n  const transformedY = dy / ry;\n  const distance = Math.sqrt(transformedX ** 2 + transformedY ** 2);\n  if (distance > 1) {\n    throw new Error(\"The given radii are too small to create an arc between the points.\");\n  }\n  const scaledCenterDistance = Math.sqrt(1 - distance ** 2);\n  const centerX = midX + scaledCenterDistance * ry * Math.sin(angle) * (clockwise ? -1 : 1);\n  const centerY = midY - scaledCenterDistance * rx * Math.cos(angle) * (clockwise ? -1 : 1);\n  const startAngle = Math.atan2((y1 - centerY) / ry, (x1 - centerX) / rx);\n  const endAngle = Math.atan2((y2 - centerY) / ry, (x2 - centerX) / rx);\n  let angleRange = endAngle - startAngle;\n  if (clockwise && angleRange < 0) {\n    angleRange += 2 * Math.PI;\n  }\n  if (!clockwise && angleRange > 0) {\n    angleRange -= 2 * Math.PI;\n  }\n  const points = [];\n  for (let i = 0; i < numPoints; i++) {\n    const t = i / (numPoints - 1);\n    const angle2 = startAngle + t * angleRange;\n    const x = centerX + rx * Math.cos(angle2);\n    const y = centerY + ry * Math.sin(angle2);\n    points.push({\n      x,\n      y\n    });\n  }\n  return points;\n}\n__name(generateArcPoints, \"generateArcPoints\");\nasync function bowTieRect(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const {\n    shapeSvg,\n    bbox\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = bbox.width + node.padding + 20;\n  const h = bbox.height + node.padding;\n  const ry = h / 2;\n  const rx = ry / (2.5 + h / 50);\n  const {\n    cssStyles\n  } = node;\n  const points = [{\n    x: w / 2,\n    y: -h / 2\n  }, {\n    x: -w / 2,\n    y: -h / 2\n  }, ...generateArcPoints(-w / 2, -h / 2, -w / 2, h / 2, rx, ry, false), {\n    x: w / 2,\n    y: h / 2\n  }, ...generateArcPoints(w / 2, h / 2, w / 2, -h / 2, rx, ry, true)];\n  const rc = rough3.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const bowTieRectPath = createPathFromPoints(points);\n  const bowTieRectShapePath = rc.path(bowTieRectPath, options);\n  const bowTieRectShape = shapeSvg.insert(() => bowTieRectShapePath, \":first-child\");\n  bowTieRectShape.attr(\"class\", \"basic label-container\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    bowTieRectShape.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    bowTieRectShape.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  bowTieRectShape.attr(\"transform\", `translate(${rx / 2}, 0)`);\n  updateNodeBounds(node, bowTieRectShape);\n  node.intersect = function (point) {\n    const pos = intersect_default.polygon(node, points, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(bowTieRect, \"bowTieRect\");\n\n// src/rendering-util/rendering-elements/shapes/card.ts\nimport rough4 from \"roughjs\";\n\n// src/rendering-util/rendering-elements/shapes/insertPolygonShape.ts\nfunction insertPolygonShape(parent, w, h, points) {\n  return parent.insert(\"polygon\", \":first-child\").attr(\"points\", points.map(function (d) {\n    return d.x + \",\" + d.y;\n  }).join(\" \")).attr(\"class\", \"label-container\").attr(\"transform\", \"translate(\" + -w / 2 + \",\" + h / 2 + \")\");\n}\n__name(insertPolygonShape, \"insertPolygonShape\");\n\n// src/rendering-util/rendering-elements/shapes/card.ts\nasync function card(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const {\n    shapeSvg,\n    bbox\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const h = bbox.height + node.padding;\n  const padding = 12;\n  const w = bbox.width + node.padding + padding;\n  const left = 0;\n  const right = w;\n  const top = -h;\n  const bottom = 0;\n  const points = [{\n    x: left + padding,\n    y: top\n  }, {\n    x: right,\n    y: top\n  }, {\n    x: right,\n    y: bottom\n  }, {\n    x: left,\n    y: bottom\n  }, {\n    x: left,\n    y: top + padding\n  }, {\n    x: left + padding,\n    y: top\n  }];\n  let polygon;\n  const {\n    cssStyles\n  } = node;\n  if (node.look === \"handDrawn\") {\n    const rc = rough4.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const pathData = createPathFromPoints(points);\n    const roughNode = rc.path(pathData, options);\n    polygon = shapeSvg.insert(() => roughNode, \":first-child\").attr(\"transform\", `translate(${-w / 2}, ${h / 2})`);\n    if (cssStyles) {\n      polygon.attr(\"style\", cssStyles);\n    }\n  } else {\n    polygon = insertPolygonShape(shapeSvg, w, h, points);\n  }\n  if (nodeStyles) {\n    polygon.attr(\"style\", nodeStyles);\n  }\n  updateNodeBounds(node, polygon);\n  node.intersect = function (point) {\n    return intersect_default.polygon(node, points, point);\n  };\n  return shapeSvg;\n}\n__name(card, \"card\");\n\n// src/rendering-util/rendering-elements/shapes/choice.ts\nimport rough5 from \"roughjs\";\nfunction choice(parent, node) {\n  const {\n    nodeStyles\n  } = styles2String(node);\n  node.label = \"\";\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", getNodeClasses(node)).attr(\"id\", node.domId ?? node.id);\n  const {\n    cssStyles\n  } = node;\n  const s = Math.max(28, node.width ?? 0);\n  const points = [{\n    x: 0,\n    y: s / 2\n  }, {\n    x: s / 2,\n    y: 0\n  }, {\n    x: 0,\n    y: -s / 2\n  }, {\n    x: -s / 2,\n    y: 0\n  }];\n  const rc = rough5.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const choicePath = createPathFromPoints(points);\n  const roughNode = rc.path(choicePath, options);\n  const choiceShape = shapeSvg.insert(() => roughNode, \":first-child\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    choiceShape.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    choiceShape.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  node.width = 28;\n  node.height = 28;\n  node.intersect = function (point) {\n    return intersect_default.polygon(node, points, point);\n  };\n  return shapeSvg;\n}\n__name(choice, \"choice\");\n\n// src/rendering-util/rendering-elements/shapes/circle.ts\nimport rough6 from \"roughjs\";\nasync function circle(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const {\n    shapeSvg,\n    bbox,\n    halfPadding\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const radius = bbox.width / 2 + halfPadding;\n  let circleElem;\n  const {\n    cssStyles\n  } = node;\n  if (node.look === \"handDrawn\") {\n    const rc = rough6.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const roughNode = rc.circle(0, 0, radius * 2, options);\n    circleElem = shapeSvg.insert(() => roughNode, \":first-child\");\n    circleElem.attr(\"class\", \"basic label-container\").attr(\"style\", handleUndefinedAttr(cssStyles));\n  } else {\n    circleElem = shapeSvg.insert(\"circle\", \":first-child\").attr(\"class\", \"basic label-container\").attr(\"style\", nodeStyles).attr(\"r\", radius).attr(\"cx\", 0).attr(\"cy\", 0);\n  }\n  updateNodeBounds(node, circleElem);\n  node.intersect = function (point) {\n    log.info(\"Circle intersect\", node, radius, point);\n    return intersect_default.circle(node, radius, point);\n  };\n  return shapeSvg;\n}\n__name(circle, \"circle\");\n\n// src/rendering-util/rendering-elements/shapes/crossedCircle.ts\nimport rough7 from \"roughjs\";\nfunction createLine(r) {\n  const xAxis45 = Math.cos(Math.PI / 4);\n  const yAxis45 = Math.sin(Math.PI / 4);\n  const lineLength = r * 2;\n  const pointQ1 = {\n    x: lineLength / 2 * xAxis45,\n    y: lineLength / 2 * yAxis45\n  };\n  const pointQ2 = {\n    x: -(lineLength / 2) * xAxis45,\n    y: lineLength / 2 * yAxis45\n  };\n  const pointQ3 = {\n    x: -(lineLength / 2) * xAxis45,\n    y: -(lineLength / 2) * yAxis45\n  };\n  const pointQ4 = {\n    x: lineLength / 2 * xAxis45,\n    y: -(lineLength / 2) * yAxis45\n  };\n  return `M ${pointQ2.x},${pointQ2.y} L ${pointQ4.x},${pointQ4.y}\n                   M ${pointQ1.x},${pointQ1.y} L ${pointQ3.x},${pointQ3.y}`;\n}\n__name(createLine, \"createLine\");\nfunction crossedCircle(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  node.label = \"\";\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", getNodeClasses(node)).attr(\"id\", node.domId ?? node.id);\n  const radius = Math.max(30, node?.width ?? 0);\n  const {\n    cssStyles\n  } = node;\n  const rc = rough7.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const circleNode = rc.circle(0, 0, radius * 2, options);\n  const linePath = createLine(radius);\n  const lineNode = rc.path(linePath, options);\n  const crossedCircle2 = shapeSvg.insert(() => circleNode, \":first-child\");\n  crossedCircle2.insert(() => lineNode);\n  if (cssStyles && node.look !== \"handDrawn\") {\n    crossedCircle2.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    crossedCircle2.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  updateNodeBounds(node, crossedCircle2);\n  node.intersect = function (point) {\n    log.info(\"crossedCircle intersect\", node, {\n      radius,\n      point\n    });\n    const pos = intersect_default.circle(node, radius, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(crossedCircle, \"crossedCircle\");\n\n// src/rendering-util/rendering-elements/shapes/curlyBraceLeft.ts\nimport rough8 from \"roughjs\";\nfunction generateCirclePoints2(centerX, centerY, radius, numPoints = 100, startAngle = 0, endAngle = 180) {\n  const points = [];\n  const startAngleRad = startAngle * Math.PI / 180;\n  const endAngleRad = endAngle * Math.PI / 180;\n  const angleRange = endAngleRad - startAngleRad;\n  const angleStep = angleRange / (numPoints - 1);\n  for (let i = 0; i < numPoints; i++) {\n    const angle = startAngleRad + i * angleStep;\n    const x = centerX + radius * Math.cos(angle);\n    const y = centerY + radius * Math.sin(angle);\n    points.push({\n      x: -x,\n      y: -y\n    });\n  }\n  return points;\n}\n__name(generateCirclePoints2, \"generateCirclePoints\");\nasync function curlyBraceLeft(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const {\n    shapeSvg,\n    bbox,\n    label\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = bbox.width + (node.padding ?? 0);\n  const h = bbox.height + (node.padding ?? 0);\n  const radius = Math.max(5, h * 0.1);\n  const {\n    cssStyles\n  } = node;\n  const points = [...generateCirclePoints2(w / 2, -h / 2, radius, 30, -90, 0), {\n    x: -w / 2 - radius,\n    y: radius\n  }, ...generateCirclePoints2(w / 2 + radius * 2, -radius, radius, 20, -180, -270), ...generateCirclePoints2(w / 2 + radius * 2, radius, radius, 20, -90, -180), {\n    x: -w / 2 - radius,\n    y: -h / 2\n  }, ...generateCirclePoints2(w / 2, h / 2, radius, 20, 0, 90)];\n  const rectPoints = [{\n    x: w / 2,\n    y: -h / 2 - radius\n  }, {\n    x: -w / 2,\n    y: -h / 2 - radius\n  }, ...generateCirclePoints2(w / 2, -h / 2, radius, 20, -90, 0), {\n    x: -w / 2 - radius,\n    y: -radius\n  }, ...generateCirclePoints2(w / 2 + w * 0.1, -radius, radius, 20, -180, -270), ...generateCirclePoints2(w / 2 + w * 0.1, radius, radius, 20, -90, -180), {\n    x: -w / 2 - radius,\n    y: h / 2\n  }, ...generateCirclePoints2(w / 2, h / 2, radius, 20, 0, 90), {\n    x: -w / 2,\n    y: h / 2 + radius\n  }, {\n    x: w / 2,\n    y: h / 2 + radius\n  }];\n  const rc = rough8.svg(shapeSvg);\n  const options = userNodeOverrides(node, {\n    fill: \"none\"\n  });\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const curlyBraceLeftPath = createPathFromPoints(points);\n  const newCurlyBracePath = curlyBraceLeftPath.replace(\"Z\", \"\");\n  const curlyBraceLeftNode = rc.path(newCurlyBracePath, options);\n  const rectPath = createPathFromPoints(rectPoints);\n  const rectShape = rc.path(rectPath, {\n    ...options\n  });\n  const curlyBraceLeftShape = shapeSvg.insert(\"g\", \":first-child\");\n  curlyBraceLeftShape.insert(() => rectShape, \":first-child\").attr(\"stroke-opacity\", 0);\n  curlyBraceLeftShape.insert(() => curlyBraceLeftNode, \":first-child\");\n  curlyBraceLeftShape.attr(\"class\", \"text\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    curlyBraceLeftShape.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    curlyBraceLeftShape.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  curlyBraceLeftShape.attr(\"transform\", `translate(${radius}, 0)`);\n  label.attr(\"transform\", `translate(${-w / 2 + radius - (bbox.x - (bbox.left ?? 0))},${-h / 2 + (node.padding ?? 0) / 2 - (bbox.y - (bbox.top ?? 0))})`);\n  updateNodeBounds(node, curlyBraceLeftShape);\n  node.intersect = function (point) {\n    const pos = intersect_default.polygon(node, rectPoints, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(curlyBraceLeft, \"curlyBraceLeft\");\n\n// src/rendering-util/rendering-elements/shapes/curlyBraceRight.ts\nimport rough9 from \"roughjs\";\nfunction generateCirclePoints3(centerX, centerY, radius, numPoints = 100, startAngle = 0, endAngle = 180) {\n  const points = [];\n  const startAngleRad = startAngle * Math.PI / 180;\n  const endAngleRad = endAngle * Math.PI / 180;\n  const angleRange = endAngleRad - startAngleRad;\n  const angleStep = angleRange / (numPoints - 1);\n  for (let i = 0; i < numPoints; i++) {\n    const angle = startAngleRad + i * angleStep;\n    const x = centerX + radius * Math.cos(angle);\n    const y = centerY + radius * Math.sin(angle);\n    points.push({\n      x,\n      y\n    });\n  }\n  return points;\n}\n__name(generateCirclePoints3, \"generateCirclePoints\");\nasync function curlyBraceRight(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const {\n    shapeSvg,\n    bbox,\n    label\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = bbox.width + (node.padding ?? 0);\n  const h = bbox.height + (node.padding ?? 0);\n  const radius = Math.max(5, h * 0.1);\n  const {\n    cssStyles\n  } = node;\n  const points = [...generateCirclePoints3(w / 2, -h / 2, radius, 20, -90, 0), {\n    x: w / 2 + radius,\n    y: -radius\n  }, ...generateCirclePoints3(w / 2 + radius * 2, -radius, radius, 20, -180, -270), ...generateCirclePoints3(w / 2 + radius * 2, radius, radius, 20, -90, -180), {\n    x: w / 2 + radius,\n    y: h / 2\n  }, ...generateCirclePoints3(w / 2, h / 2, radius, 20, 0, 90)];\n  const rectPoints = [{\n    x: -w / 2,\n    y: -h / 2 - radius\n  }, {\n    x: w / 2,\n    y: -h / 2 - radius\n  }, ...generateCirclePoints3(w / 2, -h / 2, radius, 20, -90, 0), {\n    x: w / 2 + radius,\n    y: -radius\n  }, ...generateCirclePoints3(w / 2 + radius * 2, -radius, radius, 20, -180, -270), ...generateCirclePoints3(w / 2 + radius * 2, radius, radius, 20, -90, -180), {\n    x: w / 2 + radius,\n    y: h / 2\n  }, ...generateCirclePoints3(w / 2, h / 2, radius, 20, 0, 90), {\n    x: w / 2,\n    y: h / 2 + radius\n  }, {\n    x: -w / 2,\n    y: h / 2 + radius\n  }];\n  const rc = rough9.svg(shapeSvg);\n  const options = userNodeOverrides(node, {\n    fill: \"none\"\n  });\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const curlyBraceRightPath = createPathFromPoints(points);\n  const newCurlyBracePath = curlyBraceRightPath.replace(\"Z\", \"\");\n  const curlyBraceRightNode = rc.path(newCurlyBracePath, options);\n  const rectPath = createPathFromPoints(rectPoints);\n  const rectShape = rc.path(rectPath, {\n    ...options\n  });\n  const curlyBraceRightShape = shapeSvg.insert(\"g\", \":first-child\");\n  curlyBraceRightShape.insert(() => rectShape, \":first-child\").attr(\"stroke-opacity\", 0);\n  curlyBraceRightShape.insert(() => curlyBraceRightNode, \":first-child\");\n  curlyBraceRightShape.attr(\"class\", \"text\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    curlyBraceRightShape.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    curlyBraceRightShape.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  curlyBraceRightShape.attr(\"transform\", `translate(${-radius}, 0)`);\n  label.attr(\"transform\", `translate(${-w / 2 + (node.padding ?? 0) / 2 - (bbox.x - (bbox.left ?? 0))},${-h / 2 + (node.padding ?? 0) / 2 - (bbox.y - (bbox.top ?? 0))})`);\n  updateNodeBounds(node, curlyBraceRightShape);\n  node.intersect = function (point) {\n    const pos = intersect_default.polygon(node, rectPoints, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(curlyBraceRight, \"curlyBraceRight\");\n\n// src/rendering-util/rendering-elements/shapes/curlyBraces.ts\nimport rough10 from \"roughjs\";\nfunction generateCirclePoints4(centerX, centerY, radius, numPoints = 100, startAngle = 0, endAngle = 180) {\n  const points = [];\n  const startAngleRad = startAngle * Math.PI / 180;\n  const endAngleRad = endAngle * Math.PI / 180;\n  const angleRange = endAngleRad - startAngleRad;\n  const angleStep = angleRange / (numPoints - 1);\n  for (let i = 0; i < numPoints; i++) {\n    const angle = startAngleRad + i * angleStep;\n    const x = centerX + radius * Math.cos(angle);\n    const y = centerY + radius * Math.sin(angle);\n    points.push({\n      x: -x,\n      y: -y\n    });\n  }\n  return points;\n}\n__name(generateCirclePoints4, \"generateCirclePoints\");\nasync function curlyBraces(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const {\n    shapeSvg,\n    bbox,\n    label\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = bbox.width + (node.padding ?? 0);\n  const h = bbox.height + (node.padding ?? 0);\n  const radius = Math.max(5, h * 0.1);\n  const {\n    cssStyles\n  } = node;\n  const leftCurlyBracePoints = [...generateCirclePoints4(w / 2, -h / 2, radius, 30, -90, 0), {\n    x: -w / 2 - radius,\n    y: radius\n  }, ...generateCirclePoints4(w / 2 + radius * 2, -radius, radius, 20, -180, -270), ...generateCirclePoints4(w / 2 + radius * 2, radius, radius, 20, -90, -180), {\n    x: -w / 2 - radius,\n    y: -h / 2\n  }, ...generateCirclePoints4(w / 2, h / 2, radius, 20, 0, 90)];\n  const rightCurlyBracePoints = [...generateCirclePoints4(-w / 2 + radius + radius / 2, -h / 2, radius, 20, -90, -180), {\n    x: w / 2 - radius / 2,\n    y: radius\n  }, ...generateCirclePoints4(-w / 2 - radius / 2, -radius, radius, 20, 0, 90), ...generateCirclePoints4(-w / 2 - radius / 2, radius, radius, 20, -90, 0), {\n    x: w / 2 - radius / 2,\n    y: -radius\n  }, ...generateCirclePoints4(-w / 2 + radius + radius / 2, h / 2, radius, 30, -180, -270)];\n  const rectPoints = [{\n    x: w / 2,\n    y: -h / 2 - radius\n  }, {\n    x: -w / 2,\n    y: -h / 2 - radius\n  }, ...generateCirclePoints4(w / 2, -h / 2, radius, 20, -90, 0), {\n    x: -w / 2 - radius,\n    y: -radius\n  }, ...generateCirclePoints4(w / 2 + radius * 2, -radius, radius, 20, -180, -270), ...generateCirclePoints4(w / 2 + radius * 2, radius, radius, 20, -90, -180), {\n    x: -w / 2 - radius,\n    y: h / 2\n  }, ...generateCirclePoints4(w / 2, h / 2, radius, 20, 0, 90), {\n    x: -w / 2,\n    y: h / 2 + radius\n  }, {\n    x: w / 2 - radius - radius / 2,\n    y: h / 2 + radius\n  }, ...generateCirclePoints4(-w / 2 + radius + radius / 2, -h / 2, radius, 20, -90, -180), {\n    x: w / 2 - radius / 2,\n    y: radius\n  }, ...generateCirclePoints4(-w / 2 - radius / 2, -radius, radius, 20, 0, 90), ...generateCirclePoints4(-w / 2 - radius / 2, radius, radius, 20, -90, 0), {\n    x: w / 2 - radius / 2,\n    y: -radius\n  }, ...generateCirclePoints4(-w / 2 + radius + radius / 2, h / 2, radius, 30, -180, -270)];\n  const rc = rough10.svg(shapeSvg);\n  const options = userNodeOverrides(node, {\n    fill: \"none\"\n  });\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const leftCurlyBracePath = createPathFromPoints(leftCurlyBracePoints);\n  const newLeftCurlyBracePath = leftCurlyBracePath.replace(\"Z\", \"\");\n  const leftCurlyBraceNode = rc.path(newLeftCurlyBracePath, options);\n  const rightCurlyBracePath = createPathFromPoints(rightCurlyBracePoints);\n  const newRightCurlyBracePath = rightCurlyBracePath.replace(\"Z\", \"\");\n  const rightCurlyBraceNode = rc.path(newRightCurlyBracePath, options);\n  const rectPath = createPathFromPoints(rectPoints);\n  const rectShape = rc.path(rectPath, {\n    ...options\n  });\n  const curlyBracesShape = shapeSvg.insert(\"g\", \":first-child\");\n  curlyBracesShape.insert(() => rectShape, \":first-child\").attr(\"stroke-opacity\", 0);\n  curlyBracesShape.insert(() => leftCurlyBraceNode, \":first-child\");\n  curlyBracesShape.insert(() => rightCurlyBraceNode, \":first-child\");\n  curlyBracesShape.attr(\"class\", \"text\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    curlyBracesShape.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    curlyBracesShape.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  curlyBracesShape.attr(\"transform\", `translate(${radius - radius / 4}, 0)`);\n  label.attr(\"transform\", `translate(${-w / 2 + (node.padding ?? 0) / 2 - (bbox.x - (bbox.left ?? 0))},${-h / 2 + (node.padding ?? 0) / 2 - (bbox.y - (bbox.top ?? 0))})`);\n  updateNodeBounds(node, curlyBracesShape);\n  node.intersect = function (point) {\n    const pos = intersect_default.polygon(node, rectPoints, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(curlyBraces, \"curlyBraces\");\n\n// src/rendering-util/rendering-elements/shapes/curvedTrapezoid.ts\nimport rough11 from \"roughjs\";\nasync function curvedTrapezoid(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const {\n    shapeSvg,\n    bbox\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const minWidth = 80,\n    minHeight = 20;\n  const w = Math.max(minWidth, (bbox.width + (node.padding ?? 0) * 2) * 1.25, node?.width ?? 0);\n  const h = Math.max(minHeight, bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const radius = h / 2;\n  const {\n    cssStyles\n  } = node;\n  const rc = rough11.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const totalWidth = w,\n    totalHeight = h;\n  const rw = totalWidth - radius;\n  const tw = totalHeight / 4;\n  const points = [{\n    x: rw,\n    y: 0\n  }, {\n    x: tw,\n    y: 0\n  }, {\n    x: 0,\n    y: totalHeight / 2\n  }, {\n    x: tw,\n    y: totalHeight\n  }, {\n    x: rw,\n    y: totalHeight\n  }, ...generateCirclePoints(-rw, -totalHeight / 2, radius, 50, 270, 90)];\n  const pathData = createPathFromPoints(points);\n  const shapeNode = rc.path(pathData, options);\n  const polygon = shapeSvg.insert(() => shapeNode, \":first-child\");\n  polygon.attr(\"class\", \"basic label-container\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    polygon.selectChildren(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    polygon.selectChildren(\"path\").attr(\"style\", nodeStyles);\n  }\n  polygon.attr(\"transform\", `translate(${-w / 2}, ${-h / 2})`);\n  updateNodeBounds(node, polygon);\n  node.intersect = function (point) {\n    const pos = intersect_default.polygon(node, points, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(curvedTrapezoid, \"curvedTrapezoid\");\n\n// src/rendering-util/rendering-elements/shapes/cylinder.ts\nimport rough12 from \"roughjs\";\nvar createCylinderPathD = /* @__PURE__ */__name((x, y, width, height, rx, ry) => {\n  return [`M${x},${y + ry}`, `a${rx},${ry} 0,0,0 ${width},0`, `a${rx},${ry} 0,0,0 ${-width},0`, `l0,${height}`, `a${rx},${ry} 0,0,0 ${width},0`, `l0,${-height}`].join(\" \");\n}, \"createCylinderPathD\");\nvar createOuterCylinderPathD = /* @__PURE__ */__name((x, y, width, height, rx, ry) => {\n  return [`M${x},${y + ry}`, `M${x + width},${y + ry}`, `a${rx},${ry} 0,0,0 ${-width},0`, `l0,${height}`, `a${rx},${ry} 0,0,0 ${width},0`, `l0,${-height}`].join(\" \");\n}, \"createOuterCylinderPathD\");\nvar createInnerCylinderPathD = /* @__PURE__ */__name((x, y, width, height, rx, ry) => {\n  return [`M${x - width / 2},${-height / 2}`, `a${rx},${ry} 0,0,0 ${width},0`].join(\" \");\n}, \"createInnerCylinderPathD\");\nasync function cylinder(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const {\n    shapeSvg,\n    bbox,\n    label\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + node.padding, node.width ?? 0);\n  const rx = w / 2;\n  const ry = rx / (2.5 + w / 50);\n  const h = Math.max(bbox.height + ry + node.padding, node.height ?? 0);\n  let cylinder2;\n  const {\n    cssStyles\n  } = node;\n  if (node.look === \"handDrawn\") {\n    const rc = rough12.svg(shapeSvg);\n    const outerPathData = createOuterCylinderPathD(0, 0, w, h, rx, ry);\n    const innerPathData = createInnerCylinderPathD(0, ry, w, h, rx, ry);\n    const outerNode = rc.path(outerPathData, userNodeOverrides(node, {}));\n    const innerLine = rc.path(innerPathData, userNodeOverrides(node, {\n      fill: \"none\"\n    }));\n    cylinder2 = shapeSvg.insert(() => innerLine, \":first-child\");\n    cylinder2 = shapeSvg.insert(() => outerNode, \":first-child\");\n    cylinder2.attr(\"class\", \"basic label-container\");\n    if (cssStyles) {\n      cylinder2.attr(\"style\", cssStyles);\n    }\n  } else {\n    const pathData = createCylinderPathD(0, 0, w, h, rx, ry);\n    cylinder2 = shapeSvg.insert(\"path\", \":first-child\").attr(\"d\", pathData).attr(\"class\", \"basic label-container\").attr(\"style\", handleUndefinedAttr(cssStyles)).attr(\"style\", nodeStyles);\n  }\n  cylinder2.attr(\"label-offset-y\", ry);\n  cylinder2.attr(\"transform\", `translate(${-w / 2}, ${-(h / 2 + ry)})`);\n  updateNodeBounds(node, cylinder2);\n  label.attr(\"transform\", `translate(${-(bbox.width / 2) - (bbox.x - (bbox.left ?? 0))}, ${-(bbox.height / 2) + (node.padding ?? 0) / 1.5 - (bbox.y - (bbox.top ?? 0))})`);\n  node.intersect = function (point) {\n    const pos = intersect_default.rect(node, point);\n    const x = pos.x - (node.x ?? 0);\n    if (rx != 0 && (Math.abs(x) < (node.width ?? 0) / 2 || Math.abs(x) == (node.width ?? 0) / 2 && Math.abs(pos.y - (node.y ?? 0)) > (node.height ?? 0) / 2 - ry)) {\n      let y = ry * ry * (1 - x * x / (rx * rx));\n      if (y > 0) {\n        y = Math.sqrt(y);\n      }\n      y = ry - y;\n      if (point.y - (node.y ?? 0) > 0) {\n        y = -y;\n      }\n      pos.y += y;\n    }\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(cylinder, \"cylinder\");\n\n// src/rendering-util/rendering-elements/shapes/dividedRect.ts\nimport rough13 from \"roughjs\";\nasync function dividedRectangle(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const {\n    shapeSvg,\n    bbox,\n    label\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const rectOffset = h * 0.2;\n  const x = -w / 2;\n  const y = -h / 2 - rectOffset / 2;\n  const {\n    cssStyles\n  } = node;\n  const rc = rough13.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const pts = [{\n    x,\n    y: y + rectOffset\n  }, {\n    x: -x,\n    y: y + rectOffset\n  }, {\n    x: -x,\n    y: -y\n  }, {\n    x,\n    y: -y\n  }, {\n    x,\n    y\n  }, {\n    x: -x,\n    y\n  }, {\n    x: -x,\n    y: y + rectOffset\n  }];\n  const poly = rc.polygon(pts.map(p => [p.x, p.y]), options);\n  const polygon = shapeSvg.insert(() => poly, \":first-child\");\n  polygon.attr(\"class\", \"basic label-container\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    polygon.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    polygon.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  label.attr(\"transform\", `translate(${x + (node.padding ?? 0) / 2 - (bbox.x - (bbox.left ?? 0))}, ${y + rectOffset + (node.padding ?? 0) / 2 - (bbox.y - (bbox.top ?? 0))})`);\n  updateNodeBounds(node, polygon);\n  node.intersect = function (point) {\n    const pos = intersect_default.rect(node, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(dividedRectangle, \"dividedRectangle\");\n\n// src/rendering-util/rendering-elements/shapes/doubleCircle.ts\nimport rough14 from \"roughjs\";\nasync function doublecircle(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const {\n    shapeSvg,\n    bbox,\n    halfPadding\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const gap = 5;\n  const outerRadius = bbox.width / 2 + halfPadding + gap;\n  const innerRadius = bbox.width / 2 + halfPadding;\n  let circleGroup;\n  const {\n    cssStyles\n  } = node;\n  if (node.look === \"handDrawn\") {\n    const rc = rough14.svg(shapeSvg);\n    const outerOptions = userNodeOverrides(node, {\n      roughness: 0.2,\n      strokeWidth: 2.5\n    });\n    const innerOptions = userNodeOverrides(node, {\n      roughness: 0.2,\n      strokeWidth: 1.5\n    });\n    const outerRoughNode = rc.circle(0, 0, outerRadius * 2, outerOptions);\n    const innerRoughNode = rc.circle(0, 0, innerRadius * 2, innerOptions);\n    circleGroup = shapeSvg.insert(\"g\", \":first-child\");\n    circleGroup.attr(\"class\", handleUndefinedAttr(node.cssClasses)).attr(\"style\", handleUndefinedAttr(cssStyles));\n    circleGroup.node()?.appendChild(outerRoughNode);\n    circleGroup.node()?.appendChild(innerRoughNode);\n  } else {\n    circleGroup = shapeSvg.insert(\"g\", \":first-child\");\n    const outerCircle = circleGroup.insert(\"circle\", \":first-child\");\n    const innerCircle = circleGroup.insert(\"circle\");\n    circleGroup.attr(\"class\", \"basic label-container\").attr(\"style\", nodeStyles);\n    outerCircle.attr(\"class\", \"outer-circle\").attr(\"style\", nodeStyles).attr(\"r\", outerRadius).attr(\"cx\", 0).attr(\"cy\", 0);\n    innerCircle.attr(\"class\", \"inner-circle\").attr(\"style\", nodeStyles).attr(\"r\", innerRadius).attr(\"cx\", 0).attr(\"cy\", 0);\n  }\n  updateNodeBounds(node, circleGroup);\n  node.intersect = function (point) {\n    log.info(\"DoubleCircle intersect\", node, outerRadius, point);\n    return intersect_default.circle(node, outerRadius, point);\n  };\n  return shapeSvg;\n}\n__name(doublecircle, \"doublecircle\");\n\n// src/rendering-util/rendering-elements/shapes/filledCircle.ts\nimport rough15 from \"roughjs\";\nfunction filledCircle(parent, node, {\n  config: {\n    themeVariables\n  }\n}) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.label = \"\";\n  node.labelStyle = labelStyles;\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", getNodeClasses(node)).attr(\"id\", node.domId ?? node.id);\n  const radius = 7;\n  const {\n    cssStyles\n  } = node;\n  const rc = rough15.svg(shapeSvg);\n  const {\n    nodeBorder\n  } = themeVariables;\n  const options = userNodeOverrides(node, {\n    fillStyle: \"solid\"\n  });\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n  }\n  const circleNode = rc.circle(0, 0, radius * 2, options);\n  const filledCircle2 = shapeSvg.insert(() => circleNode, \":first-child\");\n  filledCircle2.selectAll(\"path\").attr(\"style\", `fill: ${nodeBorder} !important;`);\n  if (cssStyles && cssStyles.length > 0 && node.look !== \"handDrawn\") {\n    filledCircle2.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    filledCircle2.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  updateNodeBounds(node, filledCircle2);\n  node.intersect = function (point) {\n    log.info(\"filledCircle intersect\", node, {\n      radius,\n      point\n    });\n    const pos = intersect_default.circle(node, radius, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(filledCircle, \"filledCircle\");\n\n// src/rendering-util/rendering-elements/shapes/flippedTriangle.ts\nimport rough16 from \"roughjs\";\nasync function flippedTriangle(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const {\n    shapeSvg,\n    bbox,\n    label\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = bbox.width + (node.padding ?? 0);\n  const h = w + bbox.height;\n  const tw = w + bbox.height;\n  const points = [{\n    x: 0,\n    y: -h\n  }, {\n    x: tw,\n    y: -h\n  }, {\n    x: tw / 2,\n    y: 0\n  }];\n  const {\n    cssStyles\n  } = node;\n  const rc = rough16.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const pathData = createPathFromPoints(points);\n  const roughNode = rc.path(pathData, options);\n  const flippedTriangle2 = shapeSvg.insert(() => roughNode, \":first-child\").attr(\"transform\", `translate(${-h / 2}, ${h / 2})`);\n  if (cssStyles && node.look !== \"handDrawn\") {\n    flippedTriangle2.selectChildren(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    flippedTriangle2.selectChildren(\"path\").attr(\"style\", nodeStyles);\n  }\n  node.width = w;\n  node.height = h;\n  updateNodeBounds(node, flippedTriangle2);\n  label.attr(\"transform\", `translate(${-bbox.width / 2 - (bbox.x - (bbox.left ?? 0))}, ${-h / 2 + (node.padding ?? 0) / 2 + (bbox.y - (bbox.top ?? 0))})`);\n  node.intersect = function (point) {\n    log.info(\"Triangle intersect\", node, points, point);\n    return intersect_default.polygon(node, points, point);\n  };\n  return shapeSvg;\n}\n__name(flippedTriangle, \"flippedTriangle\");\n\n// src/rendering-util/rendering-elements/shapes/forkJoin.ts\nimport rough17 from \"roughjs\";\nfunction forkJoin(parent, node, {\n  dir,\n  config: {\n    state: state2,\n    themeVariables\n  }\n}) {\n  const {\n    nodeStyles\n  } = styles2String(node);\n  node.label = \"\";\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", getNodeClasses(node)).attr(\"id\", node.domId ?? node.id);\n  const {\n    cssStyles\n  } = node;\n  let width = Math.max(70, node?.width ?? 0);\n  let height = Math.max(10, node?.height ?? 0);\n  if (dir === \"LR\") {\n    width = Math.max(10, node?.width ?? 0);\n    height = Math.max(70, node?.height ?? 0);\n  }\n  const x = -1 * width / 2;\n  const y = -1 * height / 2;\n  const rc = rough17.svg(shapeSvg);\n  const options = userNodeOverrides(node, {\n    stroke: themeVariables.lineColor,\n    fill: themeVariables.lineColor\n  });\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const roughNode = rc.rectangle(x, y, width, height, options);\n  const shape = shapeSvg.insert(() => roughNode, \":first-child\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    shape.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    shape.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  updateNodeBounds(node, shape);\n  const padding = state2?.padding ?? 0;\n  if (node.width && node.height) {\n    node.width += padding / 2 || 0;\n    node.height += padding / 2 || 0;\n  }\n  node.intersect = function (point) {\n    return intersect_default.rect(node, point);\n  };\n  return shapeSvg;\n}\n__name(forkJoin, \"forkJoin\");\n\n// src/rendering-util/rendering-elements/shapes/halfRoundedRectangle.ts\nimport rough18 from \"roughjs\";\nasync function halfRoundedRectangle(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const minWidth = 80,\n    minHeight = 50;\n  const {\n    shapeSvg,\n    bbox\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(minWidth, bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(minHeight, bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const radius = h / 2;\n  const {\n    cssStyles\n  } = node;\n  const rc = rough18.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const points = [{\n    x: -w / 2,\n    y: -h / 2\n  }, {\n    x: w / 2 - radius,\n    y: -h / 2\n  }, ...generateCirclePoints(-w / 2 + radius, 0, radius, 50, 90, 270), {\n    x: w / 2 - radius,\n    y: h / 2\n  }, {\n    x: -w / 2,\n    y: h / 2\n  }];\n  const pathData = createPathFromPoints(points);\n  const shapeNode = rc.path(pathData, options);\n  const polygon = shapeSvg.insert(() => shapeNode, \":first-child\");\n  polygon.attr(\"class\", \"basic label-container\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    polygon.selectChildren(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    polygon.selectChildren(\"path\").attr(\"style\", nodeStyles);\n  }\n  updateNodeBounds(node, polygon);\n  node.intersect = function (point) {\n    log.info(\"Pill intersect\", node, {\n      radius,\n      point\n    });\n    const pos = intersect_default.polygon(node, points, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(halfRoundedRectangle, \"halfRoundedRectangle\");\n\n// src/rendering-util/rendering-elements/shapes/hexagon.ts\nimport rough19 from \"roughjs\";\nvar createHexagonPathD = /* @__PURE__ */__name((x, y, width, height, m) => {\n  return [`M${x + m},${y}`, `L${x + width - m},${y}`, `L${x + width},${y - height / 2}`, `L${x + width - m},${y - height}`, `L${x + m},${y - height}`, `L${x},${y - height / 2}`, \"Z\"].join(\" \");\n}, \"createHexagonPathD\");\nasync function hexagon(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const {\n    shapeSvg,\n    bbox\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const f = 4;\n  const h = bbox.height + node.padding;\n  const m = h / f;\n  const w = bbox.width + 2 * m + node.padding;\n  const points = [{\n    x: m,\n    y: 0\n  }, {\n    x: w - m,\n    y: 0\n  }, {\n    x: w,\n    y: -h / 2\n  }, {\n    x: w - m,\n    y: -h\n  }, {\n    x: m,\n    y: -h\n  }, {\n    x: 0,\n    y: -h / 2\n  }];\n  let polygon;\n  const {\n    cssStyles\n  } = node;\n  if (node.look === \"handDrawn\") {\n    const rc = rough19.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const pathData = createHexagonPathD(0, 0, w, h, m);\n    const roughNode = rc.path(pathData, options);\n    polygon = shapeSvg.insert(() => roughNode, \":first-child\").attr(\"transform\", `translate(${-w / 2}, ${h / 2})`);\n    if (cssStyles) {\n      polygon.attr(\"style\", cssStyles);\n    }\n  } else {\n    polygon = insertPolygonShape(shapeSvg, w, h, points);\n  }\n  if (nodeStyles) {\n    polygon.attr(\"style\", nodeStyles);\n  }\n  node.width = w;\n  node.height = h;\n  updateNodeBounds(node, polygon);\n  node.intersect = function (point) {\n    return intersect_default.polygon(node, points, point);\n  };\n  return shapeSvg;\n}\n__name(hexagon, \"hexagon\");\n\n// src/rendering-util/rendering-elements/shapes/hourglass.ts\nimport rough20 from \"roughjs\";\nasync function hourglass(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.label = \"\";\n  node.labelStyle = labelStyles;\n  const {\n    shapeSvg\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(30, node?.width ?? 0);\n  const h = Math.max(30, node?.height ?? 0);\n  const {\n    cssStyles\n  } = node;\n  const rc = rough20.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const points = [{\n    x: 0,\n    y: 0\n  }, {\n    x: w,\n    y: 0\n  }, {\n    x: 0,\n    y: h\n  }, {\n    x: w,\n    y: h\n  }];\n  const pathData = createPathFromPoints(points);\n  const shapeNode = rc.path(pathData, options);\n  const polygon = shapeSvg.insert(() => shapeNode, \":first-child\");\n  polygon.attr(\"class\", \"basic label-container\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    polygon.selectChildren(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    polygon.selectChildren(\"path\").attr(\"style\", nodeStyles);\n  }\n  polygon.attr(\"transform\", `translate(${-w / 2}, ${-h / 2})`);\n  updateNodeBounds(node, polygon);\n  node.intersect = function (point) {\n    log.info(\"Pill intersect\", node, {\n      points\n    });\n    const pos = intersect_default.polygon(node, points, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(hourglass, \"hourglass\");\n\n// src/rendering-util/rendering-elements/shapes/icon.ts\nimport rough21 from \"roughjs\";\nasync function icon(parent, node, {\n  config: {\n    themeVariables,\n    flowchart\n  }\n}) {\n  const {\n    labelStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const assetHeight = node.assetHeight ?? 48;\n  const assetWidth = node.assetWidth ?? 48;\n  const iconSize = Math.max(assetHeight, assetWidth);\n  const defaultWidth = flowchart?.wrappingWidth;\n  node.width = Math.max(iconSize, defaultWidth ?? 0);\n  const {\n    shapeSvg,\n    bbox,\n    label\n  } = await labelHelper(parent, node, \"icon-shape default\");\n  const topLabel = node.pos === \"t\";\n  const height = iconSize;\n  const width = iconSize;\n  const {\n    nodeBorder\n  } = themeVariables;\n  const {\n    stylesMap\n  } = compileStyles(node);\n  const x = -width / 2;\n  const y = -height / 2;\n  const labelPadding = node.label ? 8 : 0;\n  const rc = rough21.svg(shapeSvg);\n  const options = userNodeOverrides(node, {\n    stroke: \"none\",\n    fill: \"none\"\n  });\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const iconNode = rc.rectangle(x, y, width, height, options);\n  const outerWidth = Math.max(width, bbox.width);\n  const outerHeight = height + bbox.height + labelPadding;\n  const outerNode = rc.rectangle(-outerWidth / 2, -outerHeight / 2, outerWidth, outerHeight, {\n    ...options,\n    fill: \"transparent\",\n    stroke: \"none\"\n  });\n  const iconShape = shapeSvg.insert(() => iconNode, \":first-child\");\n  const outerShape = shapeSvg.insert(() => outerNode);\n  if (node.icon) {\n    const iconElem = shapeSvg.append(\"g\");\n    iconElem.html(`<g>${await getIconSVG(node.icon, {\n      height: iconSize,\n      width: iconSize,\n      fallbackPrefix: \"\"\n    })}</g>`);\n    const iconBBox = iconElem.node().getBBox();\n    const iconWidth = iconBBox.width;\n    const iconHeight = iconBBox.height;\n    const iconX = iconBBox.x;\n    const iconY = iconBBox.y;\n    iconElem.attr(\"transform\", `translate(${-iconWidth / 2 - iconX},${topLabel ? bbox.height / 2 + labelPadding / 2 - iconHeight / 2 - iconY : -bbox.height / 2 - labelPadding / 2 - iconHeight / 2 - iconY})`);\n    iconElem.attr(\"style\", `color: ${stylesMap.get(\"stroke\") ?? nodeBorder};`);\n  }\n  label.attr(\"transform\", `translate(${-bbox.width / 2 - (bbox.x - (bbox.left ?? 0))},${topLabel ? -outerHeight / 2 : outerHeight / 2 - bbox.height})`);\n  iconShape.attr(\"transform\", `translate(${0},${topLabel ? bbox.height / 2 + labelPadding / 2 : -bbox.height / 2 - labelPadding / 2})`);\n  updateNodeBounds(node, outerShape);\n  node.intersect = function (point) {\n    log.info(\"iconSquare intersect\", node, point);\n    if (!node.label) {\n      return intersect_default.rect(node, point);\n    }\n    const dx = node.x ?? 0;\n    const dy = node.y ?? 0;\n    const nodeHeight = node.height ?? 0;\n    let points = [];\n    if (topLabel) {\n      points = [{\n        x: dx - bbox.width / 2,\n        y: dy - nodeHeight / 2\n      }, {\n        x: dx + bbox.width / 2,\n        y: dy - nodeHeight / 2\n      }, {\n        x: dx + bbox.width / 2,\n        y: dy - nodeHeight / 2 + bbox.height + labelPadding\n      }, {\n        x: dx + width / 2,\n        y: dy - nodeHeight / 2 + bbox.height + labelPadding\n      }, {\n        x: dx + width / 2,\n        y: dy + nodeHeight / 2\n      }, {\n        x: dx - width / 2,\n        y: dy + nodeHeight / 2\n      }, {\n        x: dx - width / 2,\n        y: dy - nodeHeight / 2 + bbox.height + labelPadding\n      }, {\n        x: dx - bbox.width / 2,\n        y: dy - nodeHeight / 2 + bbox.height + labelPadding\n      }];\n    } else {\n      points = [{\n        x: dx - width / 2,\n        y: dy - nodeHeight / 2\n      }, {\n        x: dx + width / 2,\n        y: dy - nodeHeight / 2\n      }, {\n        x: dx + width / 2,\n        y: dy - nodeHeight / 2 + height\n      }, {\n        x: dx + bbox.width / 2,\n        y: dy - nodeHeight / 2 + height\n      }, {\n        x: dx + bbox.width / 2 / 2,\n        y: dy + nodeHeight / 2\n      }, {\n        x: dx - bbox.width / 2,\n        y: dy + nodeHeight / 2\n      }, {\n        x: dx - bbox.width / 2,\n        y: dy - nodeHeight / 2 + height\n      }, {\n        x: dx - width / 2,\n        y: dy - nodeHeight / 2 + height\n      }];\n    }\n    const pos = intersect_default.polygon(node, points, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(icon, \"icon\");\n\n// src/rendering-util/rendering-elements/shapes/iconCircle.ts\nimport rough22 from \"roughjs\";\nasync function iconCircle(parent, node, {\n  config: {\n    themeVariables,\n    flowchart\n  }\n}) {\n  const {\n    labelStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const assetHeight = node.assetHeight ?? 48;\n  const assetWidth = node.assetWidth ?? 48;\n  const iconSize = Math.max(assetHeight, assetWidth);\n  const defaultWidth = flowchart?.wrappingWidth;\n  node.width = Math.max(iconSize, defaultWidth ?? 0);\n  const {\n    shapeSvg,\n    bbox,\n    label\n  } = await labelHelper(parent, node, \"icon-shape default\");\n  const padding = 20;\n  const labelPadding = node.label ? 8 : 0;\n  const topLabel = node.pos === \"t\";\n  const {\n    nodeBorder,\n    mainBkg\n  } = themeVariables;\n  const {\n    stylesMap\n  } = compileStyles(node);\n  const rc = rough22.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const fill = stylesMap.get(\"fill\");\n  options.stroke = fill ?? mainBkg;\n  const iconElem = shapeSvg.append(\"g\");\n  if (node.icon) {\n    iconElem.html(`<g>${await getIconSVG(node.icon, {\n      height: iconSize,\n      width: iconSize,\n      fallbackPrefix: \"\"\n    })}</g>`);\n  }\n  const iconBBox = iconElem.node().getBBox();\n  const iconWidth = iconBBox.width;\n  const iconHeight = iconBBox.height;\n  const iconX = iconBBox.x;\n  const iconY = iconBBox.y;\n  const diameter = Math.max(iconWidth, iconHeight) * Math.SQRT2 + padding * 2;\n  const iconNode = rc.circle(0, 0, diameter, options);\n  const outerWidth = Math.max(diameter, bbox.width);\n  const outerHeight = diameter + bbox.height + labelPadding;\n  const outerNode = rc.rectangle(-outerWidth / 2, -outerHeight / 2, outerWidth, outerHeight, {\n    ...options,\n    fill: \"transparent\",\n    stroke: \"none\"\n  });\n  const iconShape = shapeSvg.insert(() => iconNode, \":first-child\");\n  const outerShape = shapeSvg.insert(() => outerNode);\n  iconElem.attr(\"transform\", `translate(${-iconWidth / 2 - iconX},${topLabel ? bbox.height / 2 + labelPadding / 2 - iconHeight / 2 - iconY : -bbox.height / 2 - labelPadding / 2 - iconHeight / 2 - iconY})`);\n  iconElem.attr(\"style\", `color: ${stylesMap.get(\"stroke\") ?? nodeBorder};`);\n  label.attr(\"transform\", `translate(${-bbox.width / 2 - (bbox.x - (bbox.left ?? 0))},${topLabel ? -outerHeight / 2 : outerHeight / 2 - bbox.height})`);\n  iconShape.attr(\"transform\", `translate(${0},${topLabel ? bbox.height / 2 + labelPadding / 2 : -bbox.height / 2 - labelPadding / 2})`);\n  updateNodeBounds(node, outerShape);\n  node.intersect = function (point) {\n    log.info(\"iconSquare intersect\", node, point);\n    const pos = intersect_default.rect(node, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(iconCircle, \"iconCircle\");\n\n// src/rendering-util/rendering-elements/shapes/iconRounded.ts\nimport rough23 from \"roughjs\";\nasync function iconRounded(parent, node, {\n  config: {\n    themeVariables,\n    flowchart\n  }\n}) {\n  const {\n    labelStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const assetHeight = node.assetHeight ?? 48;\n  const assetWidth = node.assetWidth ?? 48;\n  const iconSize = Math.max(assetHeight, assetWidth);\n  const defaultWidth = flowchart?.wrappingWidth;\n  node.width = Math.max(iconSize, defaultWidth ?? 0);\n  const {\n    shapeSvg,\n    bbox,\n    halfPadding,\n    label\n  } = await labelHelper(parent, node, \"icon-shape default\");\n  const topLabel = node.pos === \"t\";\n  const height = iconSize + halfPadding * 2;\n  const width = iconSize + halfPadding * 2;\n  const {\n    nodeBorder,\n    mainBkg\n  } = themeVariables;\n  const {\n    stylesMap\n  } = compileStyles(node);\n  const x = -width / 2;\n  const y = -height / 2;\n  const labelPadding = node.label ? 8 : 0;\n  const rc = rough23.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const fill = stylesMap.get(\"fill\");\n  options.stroke = fill ?? mainBkg;\n  const iconNode = rc.path(createRoundedRectPathD(x, y, width, height, 5), options);\n  const outerWidth = Math.max(width, bbox.width);\n  const outerHeight = height + bbox.height + labelPadding;\n  const outerNode = rc.rectangle(-outerWidth / 2, -outerHeight / 2, outerWidth, outerHeight, {\n    ...options,\n    fill: \"transparent\",\n    stroke: \"none\"\n  });\n  const iconShape = shapeSvg.insert(() => iconNode, \":first-child\").attr(\"class\", \"icon-shape2\");\n  const outerShape = shapeSvg.insert(() => outerNode);\n  if (node.icon) {\n    const iconElem = shapeSvg.append(\"g\");\n    iconElem.html(`<g>${await getIconSVG(node.icon, {\n      height: iconSize,\n      width: iconSize,\n      fallbackPrefix: \"\"\n    })}</g>`);\n    const iconBBox = iconElem.node().getBBox();\n    const iconWidth = iconBBox.width;\n    const iconHeight = iconBBox.height;\n    const iconX = iconBBox.x;\n    const iconY = iconBBox.y;\n    iconElem.attr(\"transform\", `translate(${-iconWidth / 2 - iconX},${topLabel ? bbox.height / 2 + labelPadding / 2 - iconHeight / 2 - iconY : -bbox.height / 2 - labelPadding / 2 - iconHeight / 2 - iconY})`);\n    iconElem.attr(\"style\", `color: ${stylesMap.get(\"stroke\") ?? nodeBorder};`);\n  }\n  label.attr(\"transform\", `translate(${-bbox.width / 2 - (bbox.x - (bbox.left ?? 0))},${topLabel ? -outerHeight / 2 : outerHeight / 2 - bbox.height})`);\n  iconShape.attr(\"transform\", `translate(${0},${topLabel ? bbox.height / 2 + labelPadding / 2 : -bbox.height / 2 - labelPadding / 2})`);\n  updateNodeBounds(node, outerShape);\n  node.intersect = function (point) {\n    log.info(\"iconSquare intersect\", node, point);\n    if (!node.label) {\n      return intersect_default.rect(node, point);\n    }\n    const dx = node.x ?? 0;\n    const dy = node.y ?? 0;\n    const nodeHeight = node.height ?? 0;\n    let points = [];\n    if (topLabel) {\n      points = [{\n        x: dx - bbox.width / 2,\n        y: dy - nodeHeight / 2\n      }, {\n        x: dx + bbox.width / 2,\n        y: dy - nodeHeight / 2\n      }, {\n        x: dx + bbox.width / 2,\n        y: dy - nodeHeight / 2 + bbox.height + labelPadding\n      }, {\n        x: dx + width / 2,\n        y: dy - nodeHeight / 2 + bbox.height + labelPadding\n      }, {\n        x: dx + width / 2,\n        y: dy + nodeHeight / 2\n      }, {\n        x: dx - width / 2,\n        y: dy + nodeHeight / 2\n      }, {\n        x: dx - width / 2,\n        y: dy - nodeHeight / 2 + bbox.height + labelPadding\n      }, {\n        x: dx - bbox.width / 2,\n        y: dy - nodeHeight / 2 + bbox.height + labelPadding\n      }];\n    } else {\n      points = [{\n        x: dx - width / 2,\n        y: dy - nodeHeight / 2\n      }, {\n        x: dx + width / 2,\n        y: dy - nodeHeight / 2\n      }, {\n        x: dx + width / 2,\n        y: dy - nodeHeight / 2 + height\n      }, {\n        x: dx + bbox.width / 2,\n        y: dy - nodeHeight / 2 + height\n      }, {\n        x: dx + bbox.width / 2 / 2,\n        y: dy + nodeHeight / 2\n      }, {\n        x: dx - bbox.width / 2,\n        y: dy + nodeHeight / 2\n      }, {\n        x: dx - bbox.width / 2,\n        y: dy - nodeHeight / 2 + height\n      }, {\n        x: dx - width / 2,\n        y: dy - nodeHeight / 2 + height\n      }];\n    }\n    const pos = intersect_default.polygon(node, points, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(iconRounded, \"iconRounded\");\n\n// src/rendering-util/rendering-elements/shapes/iconSquare.ts\nimport rough24 from \"roughjs\";\nasync function iconSquare(parent, node, {\n  config: {\n    themeVariables,\n    flowchart\n  }\n}) {\n  const {\n    labelStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const assetHeight = node.assetHeight ?? 48;\n  const assetWidth = node.assetWidth ?? 48;\n  const iconSize = Math.max(assetHeight, assetWidth);\n  const defaultWidth = flowchart?.wrappingWidth;\n  node.width = Math.max(iconSize, defaultWidth ?? 0);\n  const {\n    shapeSvg,\n    bbox,\n    halfPadding,\n    label\n  } = await labelHelper(parent, node, \"icon-shape default\");\n  const topLabel = node.pos === \"t\";\n  const height = iconSize + halfPadding * 2;\n  const width = iconSize + halfPadding * 2;\n  const {\n    nodeBorder,\n    mainBkg\n  } = themeVariables;\n  const {\n    stylesMap\n  } = compileStyles(node);\n  const x = -width / 2;\n  const y = -height / 2;\n  const labelPadding = node.label ? 8 : 0;\n  const rc = rough24.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const fill = stylesMap.get(\"fill\");\n  options.stroke = fill ?? mainBkg;\n  const iconNode = rc.path(createRoundedRectPathD(x, y, width, height, 0.1), options);\n  const outerWidth = Math.max(width, bbox.width);\n  const outerHeight = height + bbox.height + labelPadding;\n  const outerNode = rc.rectangle(-outerWidth / 2, -outerHeight / 2, outerWidth, outerHeight, {\n    ...options,\n    fill: \"transparent\",\n    stroke: \"none\"\n  });\n  const iconShape = shapeSvg.insert(() => iconNode, \":first-child\");\n  const outerShape = shapeSvg.insert(() => outerNode);\n  if (node.icon) {\n    const iconElem = shapeSvg.append(\"g\");\n    iconElem.html(`<g>${await getIconSVG(node.icon, {\n      height: iconSize,\n      width: iconSize,\n      fallbackPrefix: \"\"\n    })}</g>`);\n    const iconBBox = iconElem.node().getBBox();\n    const iconWidth = iconBBox.width;\n    const iconHeight = iconBBox.height;\n    const iconX = iconBBox.x;\n    const iconY = iconBBox.y;\n    iconElem.attr(\"transform\", `translate(${-iconWidth / 2 - iconX},${topLabel ? bbox.height / 2 + labelPadding / 2 - iconHeight / 2 - iconY : -bbox.height / 2 - labelPadding / 2 - iconHeight / 2 - iconY})`);\n    iconElem.attr(\"style\", `color: ${stylesMap.get(\"stroke\") ?? nodeBorder};`);\n  }\n  label.attr(\"transform\", `translate(${-bbox.width / 2 - (bbox.x - (bbox.left ?? 0))},${topLabel ? -outerHeight / 2 : outerHeight / 2 - bbox.height})`);\n  iconShape.attr(\"transform\", `translate(${0},${topLabel ? bbox.height / 2 + labelPadding / 2 : -bbox.height / 2 - labelPadding / 2})`);\n  updateNodeBounds(node, outerShape);\n  node.intersect = function (point) {\n    log.info(\"iconSquare intersect\", node, point);\n    if (!node.label) {\n      return intersect_default.rect(node, point);\n    }\n    const dx = node.x ?? 0;\n    const dy = node.y ?? 0;\n    const nodeHeight = node.height ?? 0;\n    let points = [];\n    if (topLabel) {\n      points = [{\n        x: dx - bbox.width / 2,\n        y: dy - nodeHeight / 2\n      }, {\n        x: dx + bbox.width / 2,\n        y: dy - nodeHeight / 2\n      }, {\n        x: dx + bbox.width / 2,\n        y: dy - nodeHeight / 2 + bbox.height + labelPadding\n      }, {\n        x: dx + width / 2,\n        y: dy - nodeHeight / 2 + bbox.height + labelPadding\n      }, {\n        x: dx + width / 2,\n        y: dy + nodeHeight / 2\n      }, {\n        x: dx - width / 2,\n        y: dy + nodeHeight / 2\n      }, {\n        x: dx - width / 2,\n        y: dy - nodeHeight / 2 + bbox.height + labelPadding\n      }, {\n        x: dx - bbox.width / 2,\n        y: dy - nodeHeight / 2 + bbox.height + labelPadding\n      }];\n    } else {\n      points = [{\n        x: dx - width / 2,\n        y: dy - nodeHeight / 2\n      }, {\n        x: dx + width / 2,\n        y: dy - nodeHeight / 2\n      }, {\n        x: dx + width / 2,\n        y: dy - nodeHeight / 2 + height\n      }, {\n        x: dx + bbox.width / 2,\n        y: dy - nodeHeight / 2 + height\n      }, {\n        x: dx + bbox.width / 2 / 2,\n        y: dy + nodeHeight / 2\n      }, {\n        x: dx - bbox.width / 2,\n        y: dy + nodeHeight / 2\n      }, {\n        x: dx - bbox.width / 2,\n        y: dy - nodeHeight / 2 + height\n      }, {\n        x: dx - width / 2,\n        y: dy - nodeHeight / 2 + height\n      }];\n    }\n    const pos = intersect_default.polygon(node, points, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(iconSquare, \"iconSquare\");\n\n// src/rendering-util/rendering-elements/shapes/imageSquare.ts\nimport rough25 from \"roughjs\";\nasync function imageSquare(parent, node, {\n  config: {\n    flowchart\n  }\n}) {\n  const img = new Image();\n  img.src = node?.img ?? \"\";\n  await img.decode();\n  const imageNaturalWidth = Number(img.naturalWidth.toString().replace(\"px\", \"\"));\n  const imageNaturalHeight = Number(img.naturalHeight.toString().replace(\"px\", \"\"));\n  node.imageAspectRatio = imageNaturalWidth / imageNaturalHeight;\n  const {\n    labelStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const defaultWidth = flowchart?.wrappingWidth;\n  node.defaultWidth = flowchart?.wrappingWidth;\n  const imageRawWidth = Math.max(node.label ? defaultWidth ?? 0 : 0, node?.assetWidth ?? imageNaturalWidth);\n  const imageWidth = node.constraint === \"on\" ? node?.assetHeight ? node.assetHeight * node.imageAspectRatio : imageRawWidth : imageRawWidth;\n  const imageHeight = node.constraint === \"on\" ? imageWidth / node.imageAspectRatio : node?.assetHeight ?? imageNaturalHeight;\n  node.width = Math.max(imageWidth, defaultWidth ?? 0);\n  const {\n    shapeSvg,\n    bbox,\n    label\n  } = await labelHelper(parent, node, \"image-shape default\");\n  const topLabel = node.pos === \"t\";\n  const x = -imageWidth / 2;\n  const y = -imageHeight / 2;\n  const labelPadding = node.label ? 8 : 0;\n  const rc = rough25.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const imageNode = rc.rectangle(x, y, imageWidth, imageHeight, options);\n  const outerWidth = Math.max(imageWidth, bbox.width);\n  const outerHeight = imageHeight + bbox.height + labelPadding;\n  const outerNode = rc.rectangle(-outerWidth / 2, -outerHeight / 2, outerWidth, outerHeight, {\n    ...options,\n    fill: \"none\",\n    stroke: \"none\"\n  });\n  const iconShape = shapeSvg.insert(() => imageNode, \":first-child\");\n  const outerShape = shapeSvg.insert(() => outerNode);\n  if (node.img) {\n    const image = shapeSvg.append(\"image\");\n    image.attr(\"href\", node.img);\n    image.attr(\"width\", imageWidth);\n    image.attr(\"height\", imageHeight);\n    image.attr(\"preserveAspectRatio\", \"none\");\n    image.attr(\"transform\", `translate(${-imageWidth / 2},${topLabel ? outerHeight / 2 - imageHeight : -outerHeight / 2})`);\n  }\n  label.attr(\"transform\", `translate(${-bbox.width / 2 - (bbox.x - (bbox.left ?? 0))},${topLabel ? -imageHeight / 2 - bbox.height / 2 - labelPadding / 2 : imageHeight / 2 - bbox.height / 2 + labelPadding / 2})`);\n  iconShape.attr(\"transform\", `translate(${0},${topLabel ? bbox.height / 2 + labelPadding / 2 : -bbox.height / 2 - labelPadding / 2})`);\n  updateNodeBounds(node, outerShape);\n  node.intersect = function (point) {\n    log.info(\"iconSquare intersect\", node, point);\n    if (!node.label) {\n      return intersect_default.rect(node, point);\n    }\n    const dx = node.x ?? 0;\n    const dy = node.y ?? 0;\n    const nodeHeight = node.height ?? 0;\n    let points = [];\n    if (topLabel) {\n      points = [{\n        x: dx - bbox.width / 2,\n        y: dy - nodeHeight / 2\n      }, {\n        x: dx + bbox.width / 2,\n        y: dy - nodeHeight / 2\n      }, {\n        x: dx + bbox.width / 2,\n        y: dy - nodeHeight / 2 + bbox.height + labelPadding\n      }, {\n        x: dx + imageWidth / 2,\n        y: dy - nodeHeight / 2 + bbox.height + labelPadding\n      }, {\n        x: dx + imageWidth / 2,\n        y: dy + nodeHeight / 2\n      }, {\n        x: dx - imageWidth / 2,\n        y: dy + nodeHeight / 2\n      }, {\n        x: dx - imageWidth / 2,\n        y: dy - nodeHeight / 2 + bbox.height + labelPadding\n      }, {\n        x: dx - bbox.width / 2,\n        y: dy - nodeHeight / 2 + bbox.height + labelPadding\n      }];\n    } else {\n      points = [{\n        x: dx - imageWidth / 2,\n        y: dy - nodeHeight / 2\n      }, {\n        x: dx + imageWidth / 2,\n        y: dy - nodeHeight / 2\n      }, {\n        x: dx + imageWidth / 2,\n        y: dy - nodeHeight / 2 + imageHeight\n      }, {\n        x: dx + bbox.width / 2,\n        y: dy - nodeHeight / 2 + imageHeight\n      }, {\n        x: dx + bbox.width / 2 / 2,\n        y: dy + nodeHeight / 2\n      }, {\n        x: dx - bbox.width / 2,\n        y: dy + nodeHeight / 2\n      }, {\n        x: dx - bbox.width / 2,\n        y: dy - nodeHeight / 2 + imageHeight\n      }, {\n        x: dx - imageWidth / 2,\n        y: dy - nodeHeight / 2 + imageHeight\n      }];\n    }\n    const pos = intersect_default.polygon(node, points, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(imageSquare, \"imageSquare\");\n\n// src/rendering-util/rendering-elements/shapes/invertedTrapezoid.ts\nimport rough26 from \"roughjs\";\nasync function inv_trapezoid(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const {\n    shapeSvg,\n    bbox\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const points = [{\n    x: 0,\n    y: 0\n  }, {\n    x: w,\n    y: 0\n  }, {\n    x: w + 3 * h / 6,\n    y: -h\n  }, {\n    x: -3 * h / 6,\n    y: -h\n  }];\n  let polygon;\n  const {\n    cssStyles\n  } = node;\n  if (node.look === \"handDrawn\") {\n    const rc = rough26.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const pathData = createPathFromPoints(points);\n    const roughNode = rc.path(pathData, options);\n    polygon = shapeSvg.insert(() => roughNode, \":first-child\").attr(\"transform\", `translate(${-w / 2}, ${h / 2})`);\n    if (cssStyles) {\n      polygon.attr(\"style\", cssStyles);\n    }\n  } else {\n    polygon = insertPolygonShape(shapeSvg, w, h, points);\n  }\n  if (nodeStyles) {\n    polygon.attr(\"style\", nodeStyles);\n  }\n  node.width = w;\n  node.height = h;\n  updateNodeBounds(node, polygon);\n  node.intersect = function (point) {\n    return intersect_default.polygon(node, points, point);\n  };\n  return shapeSvg;\n}\n__name(inv_trapezoid, \"inv_trapezoid\");\n\n// src/rendering-util/rendering-elements/shapes/drawRect.ts\nimport rough27 from \"roughjs\";\nasync function drawRect(parent, node, options) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const {\n    shapeSvg,\n    bbox\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const totalWidth = Math.max(bbox.width + options.labelPaddingX * 2, node?.width || 0);\n  const totalHeight = Math.max(bbox.height + options.labelPaddingY * 2, node?.height || 0);\n  const x = -totalWidth / 2;\n  const y = -totalHeight / 2;\n  let rect2;\n  let {\n    rx,\n    ry\n  } = node;\n  const {\n    cssStyles\n  } = node;\n  if (options?.rx && options.ry) {\n    rx = options.rx;\n    ry = options.ry;\n  }\n  if (node.look === \"handDrawn\") {\n    const rc = rough27.svg(shapeSvg);\n    const options2 = userNodeOverrides(node, {});\n    const roughNode = rx || ry ? rc.path(createRoundedRectPathD(x, y, totalWidth, totalHeight, rx || 0), options2) : rc.rectangle(x, y, totalWidth, totalHeight, options2);\n    rect2 = shapeSvg.insert(() => roughNode, \":first-child\");\n    rect2.attr(\"class\", \"basic label-container\").attr(\"style\", handleUndefinedAttr(cssStyles));\n  } else {\n    rect2 = shapeSvg.insert(\"rect\", \":first-child\");\n    rect2.attr(\"class\", \"basic label-container\").attr(\"style\", nodeStyles).attr(\"rx\", handleUndefinedAttr(rx)).attr(\"ry\", handleUndefinedAttr(ry)).attr(\"x\", x).attr(\"y\", y).attr(\"width\", totalWidth).attr(\"height\", totalHeight);\n  }\n  updateNodeBounds(node, rect2);\n  node.intersect = function (point) {\n    return intersect_default.rect(node, point);\n  };\n  return shapeSvg;\n}\n__name(drawRect, \"drawRect\");\n\n// src/rendering-util/rendering-elements/shapes/labelRect.ts\nasync function labelRect(parent, node) {\n  const {\n    shapeSvg,\n    bbox,\n    label\n  } = await labelHelper(parent, node, \"label\");\n  const rect2 = shapeSvg.insert(\"rect\", \":first-child\");\n  const totalWidth = 0.1;\n  const totalHeight = 0.1;\n  rect2.attr(\"width\", totalWidth).attr(\"height\", totalHeight);\n  shapeSvg.attr(\"class\", \"label edgeLabel\");\n  label.attr(\"transform\", `translate(${-(bbox.width / 2) - (bbox.x - (bbox.left ?? 0))}, ${-(bbox.height / 2) - (bbox.y - (bbox.top ?? 0))})`);\n  updateNodeBounds(node, rect2);\n  node.intersect = function (point) {\n    return intersect_default.rect(node, point);\n  };\n  return shapeSvg;\n}\n__name(labelRect, \"labelRect\");\n\n// src/rendering-util/rendering-elements/shapes/leanLeft.ts\nimport rough28 from \"roughjs\";\nasync function lean_left(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const {\n    shapeSvg,\n    bbox\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0), node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0), node?.height ?? 0);\n  const points = [{\n    x: 0,\n    y: 0\n  }, {\n    x: w + 3 * h / 6,\n    y: 0\n  }, {\n    x: w,\n    y: -h\n  }, {\n    x: -(3 * h) / 6,\n    y: -h\n  }];\n  let polygon;\n  const {\n    cssStyles\n  } = node;\n  if (node.look === \"handDrawn\") {\n    const rc = rough28.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const pathData = createPathFromPoints(points);\n    const roughNode = rc.path(pathData, options);\n    polygon = shapeSvg.insert(() => roughNode, \":first-child\").attr(\"transform\", `translate(${-w / 2}, ${h / 2})`);\n    if (cssStyles) {\n      polygon.attr(\"style\", cssStyles);\n    }\n  } else {\n    polygon = insertPolygonShape(shapeSvg, w, h, points);\n  }\n  if (nodeStyles) {\n    polygon.attr(\"style\", nodeStyles);\n  }\n  node.width = w;\n  node.height = h;\n  updateNodeBounds(node, polygon);\n  node.intersect = function (point) {\n    return intersect_default.polygon(node, points, point);\n  };\n  return shapeSvg;\n}\n__name(lean_left, \"lean_left\");\n\n// src/rendering-util/rendering-elements/shapes/leanRight.ts\nimport rough29 from \"roughjs\";\nasync function lean_right(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const {\n    shapeSvg,\n    bbox\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0), node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0), node?.height ?? 0);\n  const points = [{\n    x: -3 * h / 6,\n    y: 0\n  }, {\n    x: w,\n    y: 0\n  }, {\n    x: w + 3 * h / 6,\n    y: -h\n  }, {\n    x: 0,\n    y: -h\n  }];\n  let polygon;\n  const {\n    cssStyles\n  } = node;\n  if (node.look === \"handDrawn\") {\n    const rc = rough29.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const pathData = createPathFromPoints(points);\n    const roughNode = rc.path(pathData, options);\n    polygon = shapeSvg.insert(() => roughNode, \":first-child\").attr(\"transform\", `translate(${-w / 2}, ${h / 2})`);\n    if (cssStyles) {\n      polygon.attr(\"style\", cssStyles);\n    }\n  } else {\n    polygon = insertPolygonShape(shapeSvg, w, h, points);\n  }\n  if (nodeStyles) {\n    polygon.attr(\"style\", nodeStyles);\n  }\n  node.width = w;\n  node.height = h;\n  updateNodeBounds(node, polygon);\n  node.intersect = function (point) {\n    return intersect_default.polygon(node, points, point);\n  };\n  return shapeSvg;\n}\n__name(lean_right, \"lean_right\");\n\n// src/rendering-util/rendering-elements/shapes/lightningBolt.ts\nimport rough30 from \"roughjs\";\nfunction lightningBolt(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.label = \"\";\n  node.labelStyle = labelStyles;\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", getNodeClasses(node)).attr(\"id\", node.domId ?? node.id);\n  const {\n    cssStyles\n  } = node;\n  const width = Math.max(35, node?.width ?? 0);\n  const height = Math.max(35, node?.height ?? 0);\n  const gap = 7;\n  const points = [{\n    x: width,\n    y: 0\n  }, {\n    x: 0,\n    y: height + gap / 2\n  }, {\n    x: width - 2 * gap,\n    y: height + gap / 2\n  }, {\n    x: 0,\n    y: 2 * height\n  }, {\n    x: width,\n    y: height - gap / 2\n  }, {\n    x: 2 * gap,\n    y: height - gap / 2\n  }];\n  const rc = rough30.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const linePath = createPathFromPoints(points);\n  const lineNode = rc.path(linePath, options);\n  const lightningBolt2 = shapeSvg.insert(() => lineNode, \":first-child\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    lightningBolt2.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    lightningBolt2.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  lightningBolt2.attr(\"transform\", `translate(-${width / 2},${-height})`);\n  updateNodeBounds(node, lightningBolt2);\n  node.intersect = function (point) {\n    log.info(\"lightningBolt intersect\", node, point);\n    const pos = intersect_default.polygon(node, points, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(lightningBolt, \"lightningBolt\");\n\n// src/rendering-util/rendering-elements/shapes/linedCylinder.ts\nimport rough31 from \"roughjs\";\nvar createCylinderPathD2 = /* @__PURE__ */__name((x, y, width, height, rx, ry, outerOffset) => {\n  return [`M${x},${y + ry}`, `a${rx},${ry} 0,0,0 ${width},0`, `a${rx},${ry} 0,0,0 ${-width},0`, `l0,${height}`, `a${rx},${ry} 0,0,0 ${width},0`, `l0,${-height}`, `M${x},${y + ry + outerOffset}`, `a${rx},${ry} 0,0,0 ${width},0`].join(\" \");\n}, \"createCylinderPathD\");\nvar createOuterCylinderPathD2 = /* @__PURE__ */__name((x, y, width, height, rx, ry, outerOffset) => {\n  return [`M${x},${y + ry}`, `M${x + width},${y + ry}`, `a${rx},${ry} 0,0,0 ${-width},0`, `l0,${height}`, `a${rx},${ry} 0,0,0 ${width},0`, `l0,${-height}`, `M${x},${y + ry + outerOffset}`, `a${rx},${ry} 0,0,0 ${width},0`].join(\" \");\n}, \"createOuterCylinderPathD\");\nvar createInnerCylinderPathD2 = /* @__PURE__ */__name((x, y, width, height, rx, ry) => {\n  return [`M${x - width / 2},${-height / 2}`, `a${rx},${ry} 0,0,0 ${width},0`].join(\" \");\n}, \"createInnerCylinderPathD\");\nasync function linedCylinder(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const {\n    shapeSvg,\n    bbox,\n    label\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0), node.width ?? 0);\n  const rx = w / 2;\n  const ry = rx / (2.5 + w / 50);\n  const h = Math.max(bbox.height + ry + (node.padding ?? 0), node.height ?? 0);\n  const outerOffset = h * 0.1;\n  let cylinder2;\n  const {\n    cssStyles\n  } = node;\n  if (node.look === \"handDrawn\") {\n    const rc = rough31.svg(shapeSvg);\n    const outerPathData = createOuterCylinderPathD2(0, 0, w, h, rx, ry, outerOffset);\n    const innerPathData = createInnerCylinderPathD2(0, ry, w, h, rx, ry);\n    const options = userNodeOverrides(node, {});\n    const outerNode = rc.path(outerPathData, options);\n    const innerLine = rc.path(innerPathData, options);\n    const innerLineEl = shapeSvg.insert(() => innerLine, \":first-child\");\n    innerLineEl.attr(\"class\", \"line\");\n    cylinder2 = shapeSvg.insert(() => outerNode, \":first-child\");\n    cylinder2.attr(\"class\", \"basic label-container\");\n    if (cssStyles) {\n      cylinder2.attr(\"style\", cssStyles);\n    }\n  } else {\n    const pathData = createCylinderPathD2(0, 0, w, h, rx, ry, outerOffset);\n    cylinder2 = shapeSvg.insert(\"path\", \":first-child\").attr(\"d\", pathData).attr(\"class\", \"basic label-container\").attr(\"style\", handleUndefinedAttr(cssStyles)).attr(\"style\", nodeStyles);\n  }\n  cylinder2.attr(\"label-offset-y\", ry);\n  cylinder2.attr(\"transform\", `translate(${-w / 2}, ${-(h / 2 + ry)})`);\n  updateNodeBounds(node, cylinder2);\n  label.attr(\"transform\", `translate(${-(bbox.width / 2) - (bbox.x - (bbox.left ?? 0))}, ${-(bbox.height / 2) + ry - (bbox.y - (bbox.top ?? 0))})`);\n  node.intersect = function (point) {\n    const pos = intersect_default.rect(node, point);\n    const x = pos.x - (node.x ?? 0);\n    if (rx != 0 && (Math.abs(x) < (node.width ?? 0) / 2 || Math.abs(x) == (node.width ?? 0) / 2 && Math.abs(pos.y - (node.y ?? 0)) > (node.height ?? 0) / 2 - ry)) {\n      let y = ry * ry * (1 - x * x / (rx * rx));\n      if (y > 0) {\n        y = Math.sqrt(y);\n      }\n      y = ry - y;\n      if (point.y - (node.y ?? 0) > 0) {\n        y = -y;\n      }\n      pos.y += y;\n    }\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(linedCylinder, \"linedCylinder\");\n\n// src/rendering-util/rendering-elements/shapes/linedWaveEdgedRect.ts\nimport rough32 from \"roughjs\";\nasync function linedWaveEdgedRect(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const {\n    shapeSvg,\n    bbox,\n    label\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const waveAmplitude = h / 4;\n  const finalH = h + waveAmplitude;\n  const {\n    cssStyles\n  } = node;\n  const rc = rough32.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const points = [{\n    x: -w / 2 - w / 2 * 0.1,\n    y: -finalH / 2\n  }, {\n    x: -w / 2 - w / 2 * 0.1,\n    y: finalH / 2\n  }, ...generateFullSineWavePoints(-w / 2 - w / 2 * 0.1, finalH / 2, w / 2 + w / 2 * 0.1, finalH / 2, waveAmplitude, 0.8), {\n    x: w / 2 + w / 2 * 0.1,\n    y: -finalH / 2\n  }, {\n    x: -w / 2 - w / 2 * 0.1,\n    y: -finalH / 2\n  }, {\n    x: -w / 2,\n    y: -finalH / 2\n  }, {\n    x: -w / 2,\n    y: finalH / 2 * 1.1\n  }, {\n    x: -w / 2,\n    y: -finalH / 2\n  }];\n  const poly = rc.polygon(points.map(p => [p.x, p.y]), options);\n  const waveEdgeRect = shapeSvg.insert(() => poly, \":first-child\");\n  waveEdgeRect.attr(\"class\", \"basic label-container\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    waveEdgeRect.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    waveEdgeRect.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  waveEdgeRect.attr(\"transform\", `translate(0,${-waveAmplitude / 2})`);\n  label.attr(\"transform\", `translate(${-w / 2 + (node.padding ?? 0) + w / 2 * 0.1 / 2 - (bbox.x - (bbox.left ?? 0))},${-h / 2 + (node.padding ?? 0) - waveAmplitude / 2 - (bbox.y - (bbox.top ?? 0))})`);\n  updateNodeBounds(node, waveEdgeRect);\n  node.intersect = function (point) {\n    const pos = intersect_default.polygon(node, points, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(linedWaveEdgedRect, \"linedWaveEdgedRect\");\n\n// src/rendering-util/rendering-elements/shapes/multiRect.ts\nimport rough33 from \"roughjs\";\nasync function multiRect(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const {\n    shapeSvg,\n    bbox,\n    label\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const rectOffset = 5;\n  const x = -w / 2;\n  const y = -h / 2;\n  const {\n    cssStyles\n  } = node;\n  const rc = rough33.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  const outerPathPoints = [{\n    x: x - rectOffset,\n    y: y + rectOffset\n  }, {\n    x: x - rectOffset,\n    y: y + h + rectOffset\n  }, {\n    x: x + w - rectOffset,\n    y: y + h + rectOffset\n  }, {\n    x: x + w - rectOffset,\n    y: y + h\n  }, {\n    x: x + w,\n    y: y + h\n  }, {\n    x: x + w,\n    y: y + h - rectOffset\n  }, {\n    x: x + w + rectOffset,\n    y: y + h - rectOffset\n  }, {\n    x: x + w + rectOffset,\n    y: y - rectOffset\n  }, {\n    x: x + rectOffset,\n    y: y - rectOffset\n  }, {\n    x: x + rectOffset,\n    y\n  }, {\n    x,\n    y\n  }, {\n    x,\n    y: y + rectOffset\n  }];\n  const innerPathPoints = [{\n    x,\n    y: y + rectOffset\n  }, {\n    x: x + w - rectOffset,\n    y: y + rectOffset\n  }, {\n    x: x + w - rectOffset,\n    y: y + h\n  }, {\n    x: x + w,\n    y: y + h\n  }, {\n    x: x + w,\n    y\n  }, {\n    x,\n    y\n  }];\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const outerPath = createPathFromPoints(outerPathPoints);\n  const outerNode = rc.path(outerPath, options);\n  const innerPath = createPathFromPoints(innerPathPoints);\n  const innerNode = rc.path(innerPath, {\n    ...options,\n    fill: \"none\"\n  });\n  const multiRect2 = shapeSvg.insert(() => innerNode, \":first-child\");\n  multiRect2.insert(() => outerNode, \":first-child\");\n  multiRect2.attr(\"class\", \"basic label-container\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    multiRect2.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    multiRect2.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  label.attr(\"transform\", `translate(${-(bbox.width / 2) - rectOffset - (bbox.x - (bbox.left ?? 0))}, ${-(bbox.height / 2) + rectOffset - (bbox.y - (bbox.top ?? 0))})`);\n  updateNodeBounds(node, multiRect2);\n  node.intersect = function (point) {\n    const pos = intersect_default.polygon(node, outerPathPoints, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(multiRect, \"multiRect\");\n\n// src/rendering-util/rendering-elements/shapes/multiWaveEdgedRectangle.ts\nimport rough34 from \"roughjs\";\nasync function multiWaveEdgedRectangle(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const {\n    shapeSvg,\n    bbox,\n    label\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const waveAmplitude = h / 4;\n  const finalH = h + waveAmplitude;\n  const x = -w / 2;\n  const y = -finalH / 2;\n  const rectOffset = 5;\n  const {\n    cssStyles\n  } = node;\n  const wavePoints = generateFullSineWavePoints(x - rectOffset, y + finalH + rectOffset, x + w - rectOffset, y + finalH + rectOffset, waveAmplitude, 0.8);\n  const lastWavePoint = wavePoints?.[wavePoints.length - 1];\n  const outerPathPoints = [{\n    x: x - rectOffset,\n    y: y + rectOffset\n  }, {\n    x: x - rectOffset,\n    y: y + finalH + rectOffset\n  }, ...wavePoints, {\n    x: x + w - rectOffset,\n    y: lastWavePoint.y - rectOffset\n  }, {\n    x: x + w,\n    y: lastWavePoint.y - rectOffset\n  }, {\n    x: x + w,\n    y: lastWavePoint.y - 2 * rectOffset\n  }, {\n    x: x + w + rectOffset,\n    y: lastWavePoint.y - 2 * rectOffset\n  }, {\n    x: x + w + rectOffset,\n    y: y - rectOffset\n  }, {\n    x: x + rectOffset,\n    y: y - rectOffset\n  }, {\n    x: x + rectOffset,\n    y\n  }, {\n    x,\n    y\n  }, {\n    x,\n    y: y + rectOffset\n  }];\n  const innerPathPoints = [{\n    x,\n    y: y + rectOffset\n  }, {\n    x: x + w - rectOffset,\n    y: y + rectOffset\n  }, {\n    x: x + w - rectOffset,\n    y: lastWavePoint.y - rectOffset\n  }, {\n    x: x + w,\n    y: lastWavePoint.y - rectOffset\n  }, {\n    x: x + w,\n    y\n  }, {\n    x,\n    y\n  }];\n  const rc = rough34.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const outerPath = createPathFromPoints(outerPathPoints);\n  const outerNode = rc.path(outerPath, options);\n  const innerPath = createPathFromPoints(innerPathPoints);\n  const innerNode = rc.path(innerPath, options);\n  const shape = shapeSvg.insert(() => outerNode, \":first-child\");\n  shape.insert(() => innerNode);\n  shape.attr(\"class\", \"basic label-container\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    shape.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    shape.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  shape.attr(\"transform\", `translate(0,${-waveAmplitude / 2})`);\n  label.attr(\"transform\", `translate(${-(bbox.width / 2) - rectOffset - (bbox.x - (bbox.left ?? 0))}, ${-(bbox.height / 2) + rectOffset - waveAmplitude / 2 - (bbox.y - (bbox.top ?? 0))})`);\n  updateNodeBounds(node, shape);\n  node.intersect = function (point) {\n    const pos = intersect_default.polygon(node, outerPathPoints, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(multiWaveEdgedRectangle, \"multiWaveEdgedRectangle\");\n\n// src/rendering-util/rendering-elements/shapes/note.ts\nimport rough35 from \"roughjs\";\nasync function note(parent, node, {\n  config: {\n    themeVariables\n  }\n}) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const useHtmlLabels = node.useHtmlLabels || getConfig().flowchart?.htmlLabels !== false;\n  if (!useHtmlLabels) {\n    node.centerLabel = true;\n  }\n  const {\n    shapeSvg,\n    bbox\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const totalWidth = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const totalHeight = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const x = -totalWidth / 2;\n  const y = -totalHeight / 2;\n  const {\n    cssStyles\n  } = node;\n  const rc = rough35.svg(shapeSvg);\n  const options = userNodeOverrides(node, {\n    fill: themeVariables.noteBkgColor,\n    stroke: themeVariables.noteBorderColor\n  });\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const noteShapeNode = rc.rectangle(x, y, totalWidth, totalHeight, options);\n  const rect2 = shapeSvg.insert(() => noteShapeNode, \":first-child\");\n  rect2.attr(\"class\", \"basic label-container\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    rect2.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    rect2.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  updateNodeBounds(node, rect2);\n  node.intersect = function (point) {\n    return intersect_default.rect(node, point);\n  };\n  return shapeSvg;\n}\n__name(note, \"note\");\n\n// src/rendering-util/rendering-elements/shapes/question.ts\nimport rough36 from \"roughjs\";\nvar createDecisionBoxPathD = /* @__PURE__ */__name((x, y, size) => {\n  return [`M${x + size / 2},${y}`, `L${x + size},${y - size / 2}`, `L${x + size / 2},${y - size}`, `L${x},${y - size / 2}`, \"Z\"].join(\" \");\n}, \"createDecisionBoxPathD\");\nasync function question(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const {\n    shapeSvg,\n    bbox\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const s = w + h;\n  const points = [{\n    x: s / 2,\n    y: 0\n  }, {\n    x: s,\n    y: -s / 2\n  }, {\n    x: s / 2,\n    y: -s\n  }, {\n    x: 0,\n    y: -s / 2\n  }];\n  let polygon;\n  const {\n    cssStyles\n  } = node;\n  if (node.look === \"handDrawn\") {\n    const rc = rough36.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const pathData = createDecisionBoxPathD(0, 0, s);\n    const roughNode = rc.path(pathData, options);\n    polygon = shapeSvg.insert(() => roughNode, \":first-child\").attr(\"transform\", `translate(${-s / 2}, ${s / 2})`);\n    if (cssStyles) {\n      polygon.attr(\"style\", cssStyles);\n    }\n  } else {\n    polygon = insertPolygonShape(shapeSvg, s, s, points);\n  }\n  if (nodeStyles) {\n    polygon.attr(\"style\", nodeStyles);\n  }\n  updateNodeBounds(node, polygon);\n  node.intersect = function (point) {\n    log.debug(\"APA12 Intersect called SPLIT\\npoint:\", point, \"\\nnode:\\n\", node, \"\\nres:\", intersect_default.polygon(node, points, point));\n    return intersect_default.polygon(node, points, point);\n  };\n  return shapeSvg;\n}\n__name(question, \"question\");\n\n// src/rendering-util/rendering-elements/shapes/rectLeftInvArrow.ts\nimport rough37 from \"roughjs\";\nasync function rect_left_inv_arrow(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const {\n    shapeSvg,\n    bbox,\n    label\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0), node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0), node?.height ?? 0);\n  const x = -w / 2;\n  const y = -h / 2;\n  const notch = y / 2;\n  const points = [{\n    x: x + notch,\n    y\n  }, {\n    x,\n    y: 0\n  }, {\n    x: x + notch,\n    y: -y\n  }, {\n    x: -x,\n    y: -y\n  }, {\n    x: -x,\n    y\n  }];\n  const {\n    cssStyles\n  } = node;\n  const rc = rough37.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const pathData = createPathFromPoints(points);\n  const roughNode = rc.path(pathData, options);\n  const polygon = shapeSvg.insert(() => roughNode, \":first-child\");\n  polygon.attr(\"class\", \"basic label-container\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    polygon.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    polygon.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  polygon.attr(\"transform\", `translate(${-notch / 2},0)`);\n  label.attr(\"transform\", `translate(${-notch / 2 - bbox.width / 2 - (bbox.x - (bbox.left ?? 0))}, ${-(bbox.height / 2) - (bbox.y - (bbox.top ?? 0))})`);\n  updateNodeBounds(node, polygon);\n  node.intersect = function (point) {\n    return intersect_default.polygon(node, points, point);\n  };\n  return shapeSvg;\n}\n__name(rect_left_inv_arrow, \"rect_left_inv_arrow\");\n\n// src/rendering-util/rendering-elements/shapes/rectWithTitle.ts\nimport { select as select4 } from \"d3\";\nimport rough38 from \"roughjs\";\nasync function rectWithTitle(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  let classes;\n  if (!node.cssClasses) {\n    classes = \"node default\";\n  } else {\n    classes = \"node \" + node.cssClasses;\n  }\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", classes).attr(\"id\", node.domId || node.id);\n  const g = shapeSvg.insert(\"g\");\n  const label = shapeSvg.insert(\"g\").attr(\"class\", \"label\").attr(\"style\", nodeStyles);\n  const description = node.description;\n  const title = node.label;\n  const text2 = label.node().appendChild(await createLabel_default(title, node.labelStyle, true, true));\n  let bbox = {\n    width: 0,\n    height: 0\n  };\n  if (evaluate(getConfig2()?.flowchart?.htmlLabels)) {\n    const div2 = text2.children[0];\n    const dv2 = select4(text2);\n    bbox = div2.getBoundingClientRect();\n    dv2.attr(\"width\", bbox.width);\n    dv2.attr(\"height\", bbox.height);\n  }\n  log.info(\"Text 2\", description);\n  const textRows = description || [];\n  const titleBox = text2.getBBox();\n  const descr = label.node().appendChild(await createLabel_default(textRows.join ? textRows.join(\"<br/>\") : textRows, node.labelStyle, true, true));\n  const div = descr.children[0];\n  const dv = select4(descr);\n  bbox = div.getBoundingClientRect();\n  dv.attr(\"width\", bbox.width);\n  dv.attr(\"height\", bbox.height);\n  const halfPadding = (node.padding || 0) / 2;\n  select4(descr).attr(\"transform\", \"translate( \" + (bbox.width > titleBox.width ? 0 : (titleBox.width - bbox.width) / 2) + \", \" + (titleBox.height + halfPadding + 5) + \")\");\n  select4(text2).attr(\"transform\", \"translate( \" + (bbox.width < titleBox.width ? 0 : -(titleBox.width - bbox.width) / 2) + \", 0)\");\n  bbox = label.node().getBBox();\n  label.attr(\"transform\", \"translate(\" + -bbox.width / 2 + \", \" + (-bbox.height / 2 - halfPadding + 3) + \")\");\n  const totalWidth = bbox.width + (node.padding || 0);\n  const totalHeight = bbox.height + (node.padding || 0);\n  const x = -bbox.width / 2 - halfPadding;\n  const y = -bbox.height / 2 - halfPadding;\n  let rect2;\n  let innerLine;\n  if (node.look === \"handDrawn\") {\n    const rc = rough38.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const roughNode = rc.path(createRoundedRectPathD(x, y, totalWidth, totalHeight, node.rx || 0), options);\n    const roughLine = rc.line(-bbox.width / 2 - halfPadding, -bbox.height / 2 - halfPadding + titleBox.height + halfPadding, bbox.width / 2 + halfPadding, -bbox.height / 2 - halfPadding + titleBox.height + halfPadding, options);\n    innerLine = shapeSvg.insert(() => {\n      log.debug(\"Rough node insert CXC\", roughNode);\n      return roughLine;\n    }, \":first-child\");\n    rect2 = shapeSvg.insert(() => {\n      log.debug(\"Rough node insert CXC\", roughNode);\n      return roughNode;\n    }, \":first-child\");\n  } else {\n    rect2 = g.insert(\"rect\", \":first-child\");\n    innerLine = g.insert(\"line\");\n    rect2.attr(\"class\", \"outer title-state\").attr(\"style\", nodeStyles).attr(\"x\", -bbox.width / 2 - halfPadding).attr(\"y\", -bbox.height / 2 - halfPadding).attr(\"width\", bbox.width + (node.padding || 0)).attr(\"height\", bbox.height + (node.padding || 0));\n    innerLine.attr(\"class\", \"divider\").attr(\"x1\", -bbox.width / 2 - halfPadding).attr(\"x2\", bbox.width / 2 + halfPadding).attr(\"y1\", -bbox.height / 2 - halfPadding + titleBox.height + halfPadding).attr(\"y2\", -bbox.height / 2 - halfPadding + titleBox.height + halfPadding);\n  }\n  updateNodeBounds(node, rect2);\n  node.intersect = function (point) {\n    return intersect_default.rect(node, point);\n  };\n  return shapeSvg;\n}\n__name(rectWithTitle, \"rectWithTitle\");\n\n// src/rendering-util/rendering-elements/shapes/roundedRect.ts\nasync function roundedRect(parent, node) {\n  const options = {\n    rx: 5,\n    ry: 5,\n    classes: \"\",\n    labelPaddingX: (node?.padding || 0) * 1,\n    labelPaddingY: (node?.padding || 0) * 1\n  };\n  return drawRect(parent, node, options);\n}\n__name(roundedRect, \"roundedRect\");\n\n// src/rendering-util/rendering-elements/shapes/shadedProcess.ts\nimport rough39 from \"roughjs\";\nasync function shadedProcess(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const {\n    shapeSvg,\n    bbox,\n    label\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const halfPadding = node?.padding ?? 0;\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const x = -bbox.width / 2 - halfPadding;\n  const y = -bbox.height / 2 - halfPadding;\n  const {\n    cssStyles\n  } = node;\n  const rc = rough39.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const points = [{\n    x,\n    y\n  }, {\n    x: x + w + 8,\n    y\n  }, {\n    x: x + w + 8,\n    y: y + h\n  }, {\n    x: x - 8,\n    y: y + h\n  }, {\n    x: x - 8,\n    y\n  }, {\n    x,\n    y\n  }, {\n    x,\n    y: y + h\n  }];\n  const roughNode = rc.polygon(points.map(p => [p.x, p.y]), options);\n  const rect2 = shapeSvg.insert(() => roughNode, \":first-child\");\n  rect2.attr(\"class\", \"basic label-container\").attr(\"style\", handleUndefinedAttr(cssStyles));\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    rect2.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  if (cssStyles && node.look !== \"handDrawn\") {\n    rect2.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  label.attr(\"transform\", `translate(${-w / 2 + 4 + (node.padding ?? 0) - (bbox.x - (bbox.left ?? 0))},${-h / 2 + (node.padding ?? 0) - (bbox.y - (bbox.top ?? 0))})`);\n  updateNodeBounds(node, rect2);\n  node.intersect = function (point) {\n    return intersect_default.rect(node, point);\n  };\n  return shapeSvg;\n}\n__name(shadedProcess, \"shadedProcess\");\n\n// src/rendering-util/rendering-elements/shapes/slopedRect.ts\nimport rough40 from \"roughjs\";\nasync function slopedRect(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const {\n    shapeSvg,\n    bbox,\n    label\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const x = -w / 2;\n  const y = -h / 2;\n  const {\n    cssStyles\n  } = node;\n  const rc = rough40.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const points = [{\n    x,\n    y\n  }, {\n    x,\n    y: y + h\n  }, {\n    x: x + w,\n    y: y + h\n  }, {\n    x: x + w,\n    y: y - h / 2\n  }];\n  const pathData = createPathFromPoints(points);\n  const shapeNode = rc.path(pathData, options);\n  const polygon = shapeSvg.insert(() => shapeNode, \":first-child\");\n  polygon.attr(\"class\", \"basic label-container\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    polygon.selectChildren(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    polygon.selectChildren(\"path\").attr(\"style\", nodeStyles);\n  }\n  polygon.attr(\"transform\", `translate(0, ${h / 4})`);\n  label.attr(\"transform\", `translate(${-w / 2 + (node.padding ?? 0) - (bbox.x - (bbox.left ?? 0))}, ${-h / 4 + (node.padding ?? 0) - (bbox.y - (bbox.top ?? 0))})`);\n  updateNodeBounds(node, polygon);\n  node.intersect = function (point) {\n    const pos = intersect_default.polygon(node, points, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(slopedRect, \"slopedRect\");\n\n// src/rendering-util/rendering-elements/shapes/squareRect.ts\nasync function squareRect2(parent, node) {\n  const options = {\n    rx: 0,\n    ry: 0,\n    classes: \"\",\n    labelPaddingX: (node?.padding || 0) * 2,\n    labelPaddingY: (node?.padding || 0) * 1\n  };\n  return drawRect(parent, node, options);\n}\n__name(squareRect2, \"squareRect\");\n\n// src/rendering-util/rendering-elements/shapes/stadium.ts\nimport rough41 from \"roughjs\";\nasync function stadium(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const {\n    shapeSvg,\n    bbox\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const h = bbox.height + node.padding;\n  const w = bbox.width + h / 4 + node.padding;\n  let rect2;\n  const {\n    cssStyles\n  } = node;\n  if (node.look === \"handDrawn\") {\n    const rc = rough41.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const pathData = createRoundedRectPathD(-w / 2, -h / 2, w, h, h / 2);\n    const roughNode = rc.path(pathData, options);\n    rect2 = shapeSvg.insert(() => roughNode, \":first-child\");\n    rect2.attr(\"class\", \"basic label-container\").attr(\"style\", handleUndefinedAttr(cssStyles));\n  } else {\n    rect2 = shapeSvg.insert(\"rect\", \":first-child\");\n    rect2.attr(\"class\", \"basic label-container\").attr(\"style\", nodeStyles).attr(\"rx\", h / 2).attr(\"ry\", h / 2).attr(\"x\", -w / 2).attr(\"y\", -h / 2).attr(\"width\", w).attr(\"height\", h);\n  }\n  updateNodeBounds(node, rect2);\n  node.intersect = function (point) {\n    return intersect_default.rect(node, point);\n  };\n  return shapeSvg;\n}\n__name(stadium, \"stadium\");\n\n// src/rendering-util/rendering-elements/shapes/state.ts\nasync function state(parent, node) {\n  const options = {\n    rx: 5,\n    ry: 5,\n    classes: \"flowchart-node\"\n  };\n  return drawRect(parent, node, options);\n}\n__name(state, \"state\");\n\n// src/rendering-util/rendering-elements/shapes/stateEnd.ts\nimport rough42 from \"roughjs\";\nfunction stateEnd(parent, node, {\n  config: {\n    themeVariables\n  }\n}) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const {\n    cssStyles\n  } = node;\n  const {\n    lineColor,\n    stateBorder,\n    nodeBorder\n  } = themeVariables;\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", \"node default\").attr(\"id\", node.domId || node.id);\n  const rc = rough42.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const roughNode = rc.circle(0, 0, 14, {\n    ...options,\n    stroke: lineColor,\n    strokeWidth: 2\n  });\n  const innerFill = stateBorder ?? nodeBorder;\n  const roughInnerNode = rc.circle(0, 0, 5, {\n    ...options,\n    fill: innerFill,\n    stroke: innerFill,\n    strokeWidth: 2,\n    fillStyle: \"solid\"\n  });\n  const circle2 = shapeSvg.insert(() => roughNode, \":first-child\");\n  circle2.insert(() => roughInnerNode);\n  if (cssStyles) {\n    circle2.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles) {\n    circle2.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  updateNodeBounds(node, circle2);\n  node.intersect = function (point) {\n    return intersect_default.circle(node, 7, point);\n  };\n  return shapeSvg;\n}\n__name(stateEnd, \"stateEnd\");\n\n// src/rendering-util/rendering-elements/shapes/stateStart.ts\nimport rough43 from \"roughjs\";\nfunction stateStart(parent, node, {\n  config: {\n    themeVariables\n  }\n}) {\n  const {\n    lineColor\n  } = themeVariables;\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", \"node default\").attr(\"id\", node.domId || node.id);\n  let circle2;\n  if (node.look === \"handDrawn\") {\n    const rc = rough43.svg(shapeSvg);\n    const roughNode = rc.circle(0, 0, 14, solidStateFill(lineColor));\n    circle2 = shapeSvg.insert(() => roughNode);\n    circle2.attr(\"class\", \"state-start\").attr(\"r\", 7).attr(\"width\", 14).attr(\"height\", 14);\n  } else {\n    circle2 = shapeSvg.insert(\"circle\", \":first-child\");\n    circle2.attr(\"class\", \"state-start\").attr(\"r\", 7).attr(\"width\", 14).attr(\"height\", 14);\n  }\n  updateNodeBounds(node, circle2);\n  node.intersect = function (point) {\n    return intersect_default.circle(node, 7, point);\n  };\n  return shapeSvg;\n}\n__name(stateStart, \"stateStart\");\n\n// src/rendering-util/rendering-elements/shapes/subroutine.ts\nimport rough44 from \"roughjs\";\nasync function subroutine(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const {\n    shapeSvg,\n    bbox\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const halfPadding = (node?.padding || 0) / 2;\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const x = -bbox.width / 2 - halfPadding;\n  const y = -bbox.height / 2 - halfPadding;\n  const points = [{\n    x: 0,\n    y: 0\n  }, {\n    x: w,\n    y: 0\n  }, {\n    x: w,\n    y: -h\n  }, {\n    x: 0,\n    y: -h\n  }, {\n    x: 0,\n    y: 0\n  }, {\n    x: -8,\n    y: 0\n  }, {\n    x: w + 8,\n    y: 0\n  }, {\n    x: w + 8,\n    y: -h\n  }, {\n    x: -8,\n    y: -h\n  }, {\n    x: -8,\n    y: 0\n  }];\n  if (node.look === \"handDrawn\") {\n    const rc = rough44.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const roughNode = rc.rectangle(x - 8, y, w + 16, h, options);\n    const l1 = rc.line(x, y, x, y + h, options);\n    const l2 = rc.line(x + w, y, x + w, y + h, options);\n    shapeSvg.insert(() => l1, \":first-child\");\n    shapeSvg.insert(() => l2, \":first-child\");\n    const rect2 = shapeSvg.insert(() => roughNode, \":first-child\");\n    const {\n      cssStyles\n    } = node;\n    rect2.attr(\"class\", \"basic label-container\").attr(\"style\", handleUndefinedAttr(cssStyles));\n    updateNodeBounds(node, rect2);\n  } else {\n    const el = insertPolygonShape(shapeSvg, w, h, points);\n    if (nodeStyles) {\n      el.attr(\"style\", nodeStyles);\n    }\n    updateNodeBounds(node, el);\n  }\n  node.intersect = function (point) {\n    return intersect_default.polygon(node, points, point);\n  };\n  return shapeSvg;\n}\n__name(subroutine, \"subroutine\");\n\n// src/rendering-util/rendering-elements/shapes/taggedRect.ts\nimport rough45 from \"roughjs\";\nasync function taggedRect(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const {\n    shapeSvg,\n    bbox\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const x = -w / 2;\n  const y = -h / 2;\n  const tagWidth = 0.2 * h;\n  const tagHeight = 0.2 * h;\n  const {\n    cssStyles\n  } = node;\n  const rc = rough45.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  const rectPoints = [{\n    x: x - tagWidth / 2,\n    y\n  }, {\n    x: x + w + tagWidth / 2,\n    y\n  }, {\n    x: x + w + tagWidth / 2,\n    y: y + h\n  }, {\n    x: x - tagWidth / 2,\n    y: y + h\n  }];\n  const tagPoints = [{\n    x: x + w - tagWidth / 2,\n    y: y + h\n  }, {\n    x: x + w + tagWidth / 2,\n    y: y + h\n  }, {\n    x: x + w + tagWidth / 2,\n    y: y + h - tagHeight\n  }];\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const rectPath = createPathFromPoints(rectPoints);\n  const rectNode = rc.path(rectPath, options);\n  const tagPath = createPathFromPoints(tagPoints);\n  const tagNode = rc.path(tagPath, {\n    ...options,\n    fillStyle: \"solid\"\n  });\n  const taggedRect2 = shapeSvg.insert(() => tagNode, \":first-child\");\n  taggedRect2.insert(() => rectNode, \":first-child\");\n  taggedRect2.attr(\"class\", \"basic label-container\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    taggedRect2.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    taggedRect2.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  updateNodeBounds(node, taggedRect2);\n  node.intersect = function (point) {\n    const pos = intersect_default.polygon(node, rectPoints, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(taggedRect, \"taggedRect\");\n\n// src/rendering-util/rendering-elements/shapes/taggedWaveEdgedRectangle.ts\nimport rough46 from \"roughjs\";\nasync function taggedWaveEdgedRectangle(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const {\n    shapeSvg,\n    bbox,\n    label\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const waveAmplitude = h / 4;\n  const tagWidth = 0.2 * w;\n  const tagHeight = 0.2 * h;\n  const finalH = h + waveAmplitude;\n  const {\n    cssStyles\n  } = node;\n  const rc = rough46.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const points = [{\n    x: -w / 2 - w / 2 * 0.1,\n    y: finalH / 2\n  }, ...generateFullSineWavePoints(-w / 2 - w / 2 * 0.1, finalH / 2, w / 2 + w / 2 * 0.1, finalH / 2, waveAmplitude, 0.8), {\n    x: w / 2 + w / 2 * 0.1,\n    y: -finalH / 2\n  }, {\n    x: -w / 2 - w / 2 * 0.1,\n    y: -finalH / 2\n  }];\n  const x = -w / 2 + w / 2 * 0.1;\n  const y = -finalH / 2 - tagHeight * 0.4;\n  const tagPoints = [{\n    x: x + w - tagWidth,\n    y: (y + h) * 1.4\n  }, {\n    x: x + w,\n    y: y + h - tagHeight\n  }, {\n    x: x + w,\n    y: (y + h) * 0.9\n  }, ...generateFullSineWavePoints(x + w, (y + h) * 1.3, x + w - tagWidth, (y + h) * 1.5, -h * 0.03, 0.5)];\n  const waveEdgeRectPath = createPathFromPoints(points);\n  const waveEdgeRectNode = rc.path(waveEdgeRectPath, options);\n  const taggedWaveEdgeRectPath = createPathFromPoints(tagPoints);\n  const taggedWaveEdgeRectNode = rc.path(taggedWaveEdgeRectPath, {\n    ...options,\n    fillStyle: \"solid\"\n  });\n  const waveEdgeRect = shapeSvg.insert(() => taggedWaveEdgeRectNode, \":first-child\");\n  waveEdgeRect.insert(() => waveEdgeRectNode, \":first-child\");\n  waveEdgeRect.attr(\"class\", \"basic label-container\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    waveEdgeRect.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    waveEdgeRect.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  waveEdgeRect.attr(\"transform\", `translate(0,${-waveAmplitude / 2})`);\n  label.attr(\"transform\", `translate(${-w / 2 + (node.padding ?? 0) - (bbox.x - (bbox.left ?? 0))},${-h / 2 + (node.padding ?? 0) - waveAmplitude / 2 - (bbox.y - (bbox.top ?? 0))})`);\n  updateNodeBounds(node, waveEdgeRect);\n  node.intersect = function (point) {\n    const pos = intersect_default.polygon(node, points, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(taggedWaveEdgedRectangle, \"taggedWaveEdgedRectangle\");\n\n// src/rendering-util/rendering-elements/shapes/text.ts\nasync function text(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const {\n    shapeSvg,\n    bbox\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const totalWidth = Math.max(bbox.width + node.padding, node?.width || 0);\n  const totalHeight = Math.max(bbox.height + node.padding, node?.height || 0);\n  const x = -totalWidth / 2;\n  const y = -totalHeight / 2;\n  const rect2 = shapeSvg.insert(\"rect\", \":first-child\");\n  rect2.attr(\"class\", \"text\").attr(\"style\", nodeStyles).attr(\"rx\", 0).attr(\"ry\", 0).attr(\"x\", x).attr(\"y\", y).attr(\"width\", totalWidth).attr(\"height\", totalHeight);\n  updateNodeBounds(node, rect2);\n  node.intersect = function (point) {\n    return intersect_default.rect(node, point);\n  };\n  return shapeSvg;\n}\n__name(text, \"text\");\n\n// src/rendering-util/rendering-elements/shapes/tiltedCylinder.ts\nimport rough47 from \"roughjs\";\nvar createCylinderPathD3 = /* @__PURE__ */__name((x, y, width, height, rx, ry) => {\n  return `M${x},${y}\n    a${rx},${ry} 0,0,1 ${0},${-height}\n    l${width},${0}\n    a${rx},${ry} 0,0,1 ${0},${height}\n    M${width},${-height}\n    a${rx},${ry} 0,0,0 ${0},${height}\n    l${-width},${0}`;\n}, \"createCylinderPathD\");\nvar createOuterCylinderPathD3 = /* @__PURE__ */__name((x, y, width, height, rx, ry) => {\n  return [`M${x},${y}`, `M${x + width},${y}`, `a${rx},${ry} 0,0,0 ${0},${-height}`, `l${-width},0`, `a${rx},${ry} 0,0,0 ${0},${height}`, `l${width},0`].join(\" \");\n}, \"createOuterCylinderPathD\");\nvar createInnerCylinderPathD3 = /* @__PURE__ */__name((x, y, width, height, rx, ry) => {\n  return [`M${x + width / 2},${-height / 2}`, `a${rx},${ry} 0,0,0 0,${height}`].join(\" \");\n}, \"createInnerCylinderPathD\");\nasync function tiltedCylinder(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const {\n    shapeSvg,\n    bbox,\n    label,\n    halfPadding\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const labelPadding = node.look === \"neo\" ? halfPadding * 2 : halfPadding;\n  const h = bbox.height + labelPadding;\n  const ry = h / 2;\n  const rx = ry / (2.5 + h / 50);\n  const w = bbox.width + rx + labelPadding;\n  const {\n    cssStyles\n  } = node;\n  let cylinder2;\n  if (node.look === \"handDrawn\") {\n    const rc = rough47.svg(shapeSvg);\n    const outerPathData = createOuterCylinderPathD3(0, 0, w, h, rx, ry);\n    const innerPathData = createInnerCylinderPathD3(0, 0, w, h, rx, ry);\n    const outerNode = rc.path(outerPathData, userNodeOverrides(node, {}));\n    const innerLine = rc.path(innerPathData, userNodeOverrides(node, {\n      fill: \"none\"\n    }));\n    cylinder2 = shapeSvg.insert(() => innerLine, \":first-child\");\n    cylinder2 = shapeSvg.insert(() => outerNode, \":first-child\");\n    cylinder2.attr(\"class\", \"basic label-container\");\n    if (cssStyles) {\n      cylinder2.attr(\"style\", cssStyles);\n    }\n  } else {\n    const pathData = createCylinderPathD3(0, 0, w, h, rx, ry);\n    cylinder2 = shapeSvg.insert(\"path\", \":first-child\").attr(\"d\", pathData).attr(\"class\", \"basic label-container\").attr(\"style\", handleUndefinedAttr(cssStyles)).attr(\"style\", nodeStyles);\n    cylinder2.attr(\"class\", \"basic label-container\");\n    if (cssStyles) {\n      cylinder2.selectAll(\"path\").attr(\"style\", cssStyles);\n    }\n    if (nodeStyles) {\n      cylinder2.selectAll(\"path\").attr(\"style\", nodeStyles);\n    }\n  }\n  cylinder2.attr(\"label-offset-x\", rx);\n  cylinder2.attr(\"transform\", `translate(${-w / 2}, ${h / 2} )`);\n  label.attr(\"transform\", `translate(${-(bbox.width / 2) - rx - (bbox.x - (bbox.left ?? 0))}, ${-(bbox.height / 2) - (bbox.y - (bbox.top ?? 0))})`);\n  updateNodeBounds(node, cylinder2);\n  node.intersect = function (point) {\n    const pos = intersect_default.rect(node, point);\n    const y = pos.y - (node.y ?? 0);\n    if (ry != 0 && (Math.abs(y) < (node.height ?? 0) / 2 || Math.abs(y) == (node.height ?? 0) / 2 && Math.abs(pos.x - (node.x ?? 0)) > (node.width ?? 0) / 2 - rx)) {\n      let x = rx * rx * (1 - y * y / (ry * ry));\n      if (x != 0) {\n        x = Math.sqrt(Math.abs(x));\n      }\n      x = rx - x;\n      if (point.x - (node.x ?? 0) > 0) {\n        x = -x;\n      }\n      pos.x += x;\n    }\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(tiltedCylinder, \"tiltedCylinder\");\n\n// src/rendering-util/rendering-elements/shapes/trapezoid.ts\nimport rough48 from \"roughjs\";\nasync function trapezoid(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const {\n    shapeSvg,\n    bbox\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const points = [{\n    x: -3 * h / 6,\n    y: 0\n  }, {\n    x: w + 3 * h / 6,\n    y: 0\n  }, {\n    x: w,\n    y: -h\n  }, {\n    x: 0,\n    y: -h\n  }];\n  let polygon;\n  const {\n    cssStyles\n  } = node;\n  if (node.look === \"handDrawn\") {\n    const rc = rough48.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const pathData = createPathFromPoints(points);\n    const roughNode = rc.path(pathData, options);\n    polygon = shapeSvg.insert(() => roughNode, \":first-child\").attr(\"transform\", `translate(${-w / 2}, ${h / 2})`);\n    if (cssStyles) {\n      polygon.attr(\"style\", cssStyles);\n    }\n  } else {\n    polygon = insertPolygonShape(shapeSvg, w, h, points);\n  }\n  if (nodeStyles) {\n    polygon.attr(\"style\", nodeStyles);\n  }\n  node.width = w;\n  node.height = h;\n  updateNodeBounds(node, polygon);\n  node.intersect = function (point) {\n    return intersect_default.polygon(node, points, point);\n  };\n  return shapeSvg;\n}\n__name(trapezoid, \"trapezoid\");\n\n// src/rendering-util/rendering-elements/shapes/trapezoidalPentagon.ts\nimport rough49 from \"roughjs\";\nasync function trapezoidalPentagon(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const {\n    shapeSvg,\n    bbox\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const minWidth = 60,\n    minHeight = 20;\n  const w = Math.max(minWidth, bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(minHeight, bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const {\n    cssStyles\n  } = node;\n  const rc = rough49.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const points = [{\n    x: -w / 2 * 0.8,\n    y: -h / 2\n  }, {\n    x: w / 2 * 0.8,\n    y: -h / 2\n  }, {\n    x: w / 2,\n    y: -h / 2 * 0.6\n  }, {\n    x: w / 2,\n    y: h / 2\n  }, {\n    x: -w / 2,\n    y: h / 2\n  }, {\n    x: -w / 2,\n    y: -h / 2 * 0.6\n  }];\n  const pathData = createPathFromPoints(points);\n  const shapeNode = rc.path(pathData, options);\n  const polygon = shapeSvg.insert(() => shapeNode, \":first-child\");\n  polygon.attr(\"class\", \"basic label-container\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    polygon.selectChildren(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    polygon.selectChildren(\"path\").attr(\"style\", nodeStyles);\n  }\n  updateNodeBounds(node, polygon);\n  node.intersect = function (point) {\n    const pos = intersect_default.polygon(node, points, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(trapezoidalPentagon, \"trapezoidalPentagon\");\n\n// src/rendering-util/rendering-elements/shapes/triangle.ts\nimport rough50 from \"roughjs\";\nasync function triangle(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const {\n    shapeSvg,\n    bbox,\n    label\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const useHtmlLabels = evaluate(getConfig2().flowchart?.htmlLabels);\n  const w = bbox.width + (node.padding ?? 0);\n  const h = w + bbox.height;\n  const tw = w + bbox.height;\n  const points = [{\n    x: 0,\n    y: 0\n  }, {\n    x: tw,\n    y: 0\n  }, {\n    x: tw / 2,\n    y: -h\n  }];\n  const {\n    cssStyles\n  } = node;\n  const rc = rough50.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const pathData = createPathFromPoints(points);\n  const roughNode = rc.path(pathData, options);\n  const polygon = shapeSvg.insert(() => roughNode, \":first-child\").attr(\"transform\", `translate(${-h / 2}, ${h / 2})`);\n  if (cssStyles && node.look !== \"handDrawn\") {\n    polygon.selectChildren(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    polygon.selectChildren(\"path\").attr(\"style\", nodeStyles);\n  }\n  node.width = w;\n  node.height = h;\n  updateNodeBounds(node, polygon);\n  label.attr(\"transform\", `translate(${-bbox.width / 2 - (bbox.x - (bbox.left ?? 0))}, ${h / 2 - (bbox.height + (node.padding ?? 0) / (useHtmlLabels ? 2 : 1) - (bbox.y - (bbox.top ?? 0)))})`);\n  node.intersect = function (point) {\n    log.info(\"Triangle intersect\", node, points, point);\n    return intersect_default.polygon(node, points, point);\n  };\n  return shapeSvg;\n}\n__name(triangle, \"triangle\");\n\n// src/rendering-util/rendering-elements/shapes/waveEdgedRectangle.ts\nimport rough51 from \"roughjs\";\nasync function waveEdgedRectangle(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const {\n    shapeSvg,\n    bbox,\n    label\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const waveAmplitude = h / 8;\n  const finalH = h + waveAmplitude;\n  const {\n    cssStyles\n  } = node;\n  const minWidth = 70;\n  const widthDif = minWidth - w;\n  const extraW = widthDif > 0 ? widthDif / 2 : 0;\n  const rc = rough51.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const points = [{\n    x: -w / 2 - extraW,\n    y: finalH / 2\n  }, ...generateFullSineWavePoints(-w / 2 - extraW, finalH / 2, w / 2 + extraW, finalH / 2, waveAmplitude, 0.8), {\n    x: w / 2 + extraW,\n    y: -finalH / 2\n  }, {\n    x: -w / 2 - extraW,\n    y: -finalH / 2\n  }];\n  const waveEdgeRectPath = createPathFromPoints(points);\n  const waveEdgeRectNode = rc.path(waveEdgeRectPath, options);\n  const waveEdgeRect = shapeSvg.insert(() => waveEdgeRectNode, \":first-child\");\n  waveEdgeRect.attr(\"class\", \"basic label-container\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    waveEdgeRect.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    waveEdgeRect.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  waveEdgeRect.attr(\"transform\", `translate(0,${-waveAmplitude / 2})`);\n  label.attr(\"transform\", `translate(${-w / 2 + (node.padding ?? 0) - (bbox.x - (bbox.left ?? 0))},${-h / 2 + (node.padding ?? 0) - waveAmplitude - (bbox.y - (bbox.top ?? 0))})`);\n  updateNodeBounds(node, waveEdgeRect);\n  node.intersect = function (point) {\n    const pos = intersect_default.polygon(node, points, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(waveEdgedRectangle, \"waveEdgedRectangle\");\n\n// src/rendering-util/rendering-elements/shapes/waveRectangle.ts\nimport rough52 from \"roughjs\";\nasync function waveRectangle(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const {\n    shapeSvg,\n    bbox\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const minWidth = 100;\n  const minHeight = 50;\n  const baseWidth = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const baseHeight = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const aspectRatio = baseWidth / baseHeight;\n  let w = baseWidth;\n  let h = baseHeight;\n  if (w > h * aspectRatio) {\n    h = w / aspectRatio;\n  } else {\n    w = h * aspectRatio;\n  }\n  w = Math.max(w, minWidth);\n  h = Math.max(h, minHeight);\n  const waveAmplitude = Math.min(h * 0.2, h / 4);\n  const finalH = h + waveAmplitude * 2;\n  const {\n    cssStyles\n  } = node;\n  const rc = rough52.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const points = [{\n    x: -w / 2,\n    y: finalH / 2\n  }, ...generateFullSineWavePoints(-w / 2, finalH / 2, w / 2, finalH / 2, waveAmplitude, 1), {\n    x: w / 2,\n    y: -finalH / 2\n  }, ...generateFullSineWavePoints(w / 2, -finalH / 2, -w / 2, -finalH / 2, waveAmplitude, -1)];\n  const waveRectPath = createPathFromPoints(points);\n  const waveRectNode = rc.path(waveRectPath, options);\n  const waveRect = shapeSvg.insert(() => waveRectNode, \":first-child\");\n  waveRect.attr(\"class\", \"basic label-container\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    waveRect.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    waveRect.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  updateNodeBounds(node, waveRect);\n  node.intersect = function (point) {\n    const pos = intersect_default.polygon(node, points, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(waveRectangle, \"waveRectangle\");\n\n// src/rendering-util/rendering-elements/shapes/windowPane.ts\nimport rough53 from \"roughjs\";\nasync function windowPane(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const {\n    shapeSvg,\n    bbox,\n    label\n  } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const rectOffset = 5;\n  const x = -w / 2;\n  const y = -h / 2;\n  const {\n    cssStyles\n  } = node;\n  const rc = rough53.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  const outerPathPoints = [{\n    x: x - rectOffset,\n    y: y - rectOffset\n  }, {\n    x: x - rectOffset,\n    y: y + h\n  }, {\n    x: x + w,\n    y: y + h\n  }, {\n    x: x + w,\n    y: y - rectOffset\n  }];\n  const path = `M${x - rectOffset},${y - rectOffset} L${x + w},${y - rectOffset} L${x + w},${y + h} L${x - rectOffset},${y + h} L${x - rectOffset},${y - rectOffset}\n                M${x - rectOffset},${y} L${x + w},${y}\n                M${x},${y - rectOffset} L${x},${y + h}`;\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const no = rc.path(path, options);\n  const windowPane2 = shapeSvg.insert(() => no, \":first-child\");\n  windowPane2.attr(\"transform\", `translate(${rectOffset / 2}, ${rectOffset / 2})`);\n  windowPane2.attr(\"class\", \"basic label-container\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    windowPane2.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    windowPane2.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  label.attr(\"transform\", `translate(${-(bbox.width / 2) + rectOffset / 2 - (bbox.x - (bbox.left ?? 0))}, ${-(bbox.height / 2) + rectOffset / 2 - (bbox.y - (bbox.top ?? 0))})`);\n  updateNodeBounds(node, windowPane2);\n  node.intersect = function (point) {\n    const pos = intersect_default.polygon(node, outerPathPoints, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(windowPane, \"windowPane\");\n\n// src/rendering-util/rendering-elements/shapes/erBox.ts\nimport rough54 from \"roughjs\";\nimport { select as select5 } from \"d3\";\nasync function erBox(parent, node) {\n  const entityNode = node;\n  if (entityNode.alias) {\n    node.label = entityNode.alias;\n  }\n  if (node.look === \"handDrawn\") {\n    const {\n      themeVariables: themeVariables2\n    } = getConfig();\n    const {\n      background\n    } = themeVariables2;\n    const backgroundNode = {\n      ...node,\n      id: node.id + \"-background\",\n      look: \"default\",\n      cssStyles: [\"stroke: none\", `fill: ${background}`]\n    };\n    await erBox(parent, backgroundNode);\n  }\n  const config = getConfig();\n  node.useHtmlLabels = config.htmlLabels;\n  let PADDING = config.er?.diagramPadding ?? 10;\n  let TEXT_PADDING = config.er?.entityPadding ?? 6;\n  const {\n    cssStyles\n  } = node;\n  const {\n    labelStyles\n  } = styles2String(node);\n  if (entityNode.attributes.length === 0 && node.label) {\n    const options2 = {\n      rx: 0,\n      ry: 0,\n      labelPaddingX: PADDING,\n      labelPaddingY: PADDING * 1.5,\n      classes: \"\"\n    };\n    if (calculateTextWidth(node.label, config) + options2.labelPaddingX * 2 < config.er.minEntityWidth) {\n      node.width = config.er.minEntityWidth;\n    }\n    const shapeSvg2 = await drawRect(parent, node, options2);\n    if (!evaluate(config.htmlLabels)) {\n      const textElement = shapeSvg2.select(\"text\");\n      const bbox = textElement.node()?.getBBox();\n      textElement.attr(\"transform\", `translate(${-bbox.width / 2}, 0)`);\n    }\n    return shapeSvg2;\n  }\n  if (!config.htmlLabels) {\n    PADDING *= 1.25;\n    TEXT_PADDING *= 1.25;\n  }\n  let cssClasses = getNodeClasses(node);\n  if (!cssClasses) {\n    cssClasses = \"node default\";\n  }\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", cssClasses).attr(\"id\", node.domId || node.id);\n  const nameBBox = await addText(shapeSvg, node.label ?? \"\", config, 0, 0, [\"name\"], labelStyles);\n  nameBBox.height += TEXT_PADDING;\n  let yOffset = 0;\n  const yOffsets = [];\n  let maxTypeWidth = 0;\n  let maxNameWidth = 0;\n  let maxKeysWidth = 0;\n  let maxCommentWidth = 0;\n  let keysPresent = true;\n  let commentPresent = true;\n  for (const attribute of entityNode.attributes) {\n    const typeBBox = await addText(shapeSvg, attribute.type, config, 0, yOffset, [\"attribute-type\"], labelStyles);\n    maxTypeWidth = Math.max(maxTypeWidth, typeBBox.width + PADDING);\n    const nameBBox2 = await addText(shapeSvg, attribute.name, config, 0, yOffset, [\"attribute-name\"], labelStyles);\n    maxNameWidth = Math.max(maxNameWidth, nameBBox2.width + PADDING);\n    const keysBBox = await addText(shapeSvg, attribute.keys.join(), config, 0, yOffset, [\"attribute-keys\"], labelStyles);\n    maxKeysWidth = Math.max(maxKeysWidth, keysBBox.width + PADDING);\n    const commentBBox = await addText(shapeSvg, attribute.comment, config, 0, yOffset, [\"attribute-comment\"], labelStyles);\n    maxCommentWidth = Math.max(maxCommentWidth, commentBBox.width + PADDING);\n    yOffset += Math.max(typeBBox.height, nameBBox2.height, keysBBox.height, commentBBox.height) + TEXT_PADDING;\n    yOffsets.push(yOffset);\n  }\n  yOffsets.pop();\n  let totalWidthSections = 4;\n  if (maxKeysWidth <= PADDING) {\n    keysPresent = false;\n    maxKeysWidth = 0;\n    totalWidthSections--;\n  }\n  if (maxCommentWidth <= PADDING) {\n    commentPresent = false;\n    maxCommentWidth = 0;\n    totalWidthSections--;\n  }\n  const shapeBBox = shapeSvg.node().getBBox();\n  if (nameBBox.width + PADDING * 2 - (maxTypeWidth + maxNameWidth + maxKeysWidth + maxCommentWidth) > 0) {\n    const difference = nameBBox.width + PADDING * 2 - (maxTypeWidth + maxNameWidth + maxKeysWidth + maxCommentWidth);\n    maxTypeWidth += difference / totalWidthSections;\n    maxNameWidth += difference / totalWidthSections;\n    if (maxKeysWidth > 0) {\n      maxKeysWidth += difference / totalWidthSections;\n    }\n    if (maxCommentWidth > 0) {\n      maxCommentWidth += difference / totalWidthSections;\n    }\n  }\n  const maxWidth = maxTypeWidth + maxNameWidth + maxKeysWidth + maxCommentWidth;\n  const rc = rough54.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const w = Math.max(shapeBBox.width + PADDING * 2, node?.width || 0, maxWidth);\n  const h = Math.max(shapeBBox.height + (yOffsets[0] || yOffset) + TEXT_PADDING, node?.height || 0);\n  const x = -w / 2;\n  const y = -h / 2;\n  shapeSvg.selectAll(\"g:not(:first-child)\").each((_, i, nodes) => {\n    const text2 = select5(nodes[i]);\n    const transform = text2.attr(\"transform\");\n    let translateX = 0;\n    let translateY = 0;\n    if (transform) {\n      const regex = RegExp(/translate\\(([^,]+),([^)]+)\\)/);\n      const translate = regex.exec(transform);\n      if (translate) {\n        translateX = parseFloat(translate[1]);\n        translateY = parseFloat(translate[2]);\n        if (text2.attr(\"class\").includes(\"attribute-name\")) {\n          translateX += maxTypeWidth;\n        } else if (text2.attr(\"class\").includes(\"attribute-keys\")) {\n          translateX += maxTypeWidth + maxNameWidth;\n        } else if (text2.attr(\"class\").includes(\"attribute-comment\")) {\n          translateX += maxTypeWidth + maxNameWidth + maxKeysWidth;\n        }\n      }\n    }\n    text2.attr(\"transform\", `translate(${x + PADDING / 2 + translateX}, ${translateY + y + nameBBox.height + TEXT_PADDING / 2})`);\n  });\n  shapeSvg.select(\".name\").attr(\"transform\", \"translate(\" + -nameBBox.width / 2 + \", \" + (y + TEXT_PADDING / 2) + \")\");\n  const roughRect = rc.rectangle(x, y, w, h, options);\n  const rect2 = shapeSvg.insert(() => roughRect, \":first-child\").attr(\"style\", cssStyles.join(\"\"));\n  const {\n    themeVariables\n  } = getConfig();\n  const {\n    rowEven,\n    rowOdd,\n    nodeBorder\n  } = themeVariables;\n  yOffsets.push(0);\n  for (const [i, yOffset2] of yOffsets.entries()) {\n    if (i === 0 && yOffsets.length > 1) {\n      continue;\n    }\n    const isEven = i % 2 === 0 && yOffset2 !== 0;\n    const roughRect2 = rc.rectangle(x, nameBBox.height + y + yOffset2, w, nameBBox.height, {\n      ...options,\n      fill: isEven ? rowEven : rowOdd,\n      stroke: nodeBorder\n    });\n    shapeSvg.insert(() => roughRect2, \"g.label\").attr(\"style\", cssStyles.join(\"\")).attr(\"class\", `row-rect-${i % 2 === 0 ? \"even\" : \"odd\"}`);\n  }\n  let roughLine = rc.line(x, nameBBox.height + y, w + x, nameBBox.height + y, options);\n  shapeSvg.insert(() => roughLine).attr(\"class\", \"divider\");\n  roughLine = rc.line(maxTypeWidth + x, nameBBox.height + y, maxTypeWidth + x, h + y, options);\n  shapeSvg.insert(() => roughLine).attr(\"class\", \"divider\");\n  if (keysPresent) {\n    roughLine = rc.line(maxTypeWidth + maxNameWidth + x, nameBBox.height + y, maxTypeWidth + maxNameWidth + x, h + y, options);\n    shapeSvg.insert(() => roughLine).attr(\"class\", \"divider\");\n  }\n  if (commentPresent) {\n    roughLine = rc.line(maxTypeWidth + maxNameWidth + maxKeysWidth + x, nameBBox.height + y, maxTypeWidth + maxNameWidth + maxKeysWidth + x, h + y, options);\n    shapeSvg.insert(() => roughLine).attr(\"class\", \"divider\");\n  }\n  for (const yOffset2 of yOffsets) {\n    roughLine = rc.line(x, nameBBox.height + y + yOffset2, w + x, nameBBox.height + y + yOffset2, options);\n    shapeSvg.insert(() => roughLine).attr(\"class\", \"divider\");\n  }\n  updateNodeBounds(node, rect2);\n  node.intersect = function (point) {\n    return intersect_default.rect(node, point);\n  };\n  return shapeSvg;\n}\n__name(erBox, \"erBox\");\nasync function addText(shapeSvg, labelText, config, translateX = 0, translateY = 0, classes = [], style = \"\") {\n  const label = shapeSvg.insert(\"g\").attr(\"class\", `label ${classes.join(\" \")}`).attr(\"transform\", `translate(${translateX}, ${translateY})`).attr(\"style\", style);\n  if (labelText !== parseGenericTypes(labelText)) {\n    labelText = parseGenericTypes(labelText);\n    labelText = labelText.replaceAll(\"<\", \"&lt;\").replaceAll(\">\", \"&gt;\");\n  }\n  const text2 = label.node().appendChild(await createText(label, labelText, {\n    width: calculateTextWidth(labelText, config) + 100,\n    style,\n    useHtmlLabels: config.htmlLabels\n  }, config));\n  if (labelText.includes(\"&lt;\") || labelText.includes(\"&gt;\")) {\n    let child = text2.children[0];\n    child.textContent = child.textContent.replaceAll(\"&lt;\", \"<\").replaceAll(\"&gt;\", \">\");\n    while (child.childNodes[0]) {\n      child = child.childNodes[0];\n      child.textContent = child.textContent.replaceAll(\"&lt;\", \"<\").replaceAll(\"&gt;\", \">\");\n    }\n  }\n  let bbox = text2.getBBox();\n  if (evaluate(config.htmlLabels)) {\n    const div = text2.children[0];\n    div.style.textAlign = \"start\";\n    const dv = select5(text2);\n    bbox = div.getBoundingClientRect();\n    dv.attr(\"width\", bbox.width);\n    dv.attr(\"height\", bbox.height);\n  }\n  return bbox;\n}\n__name(addText, \"addText\");\n\n// src/rendering-util/rendering-elements/shapes/classBox.ts\nimport { select as select7 } from \"d3\";\nimport rough55 from \"roughjs\";\n\n// src/diagrams/class/shapeUtil.ts\nimport { select as select6 } from \"d3\";\nasync function textHelper(parent, node, config, useHtmlLabels, GAP = config.class.padding ?? 12) {\n  const TEXT_PADDING = !useHtmlLabels ? 3 : 0;\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", getNodeClasses(node)).attr(\"id\", node.domId || node.id);\n  let annotationGroup = null;\n  let labelGroup = null;\n  let membersGroup = null;\n  let methodsGroup = null;\n  let annotationGroupHeight = 0;\n  let labelGroupHeight = 0;\n  let membersGroupHeight = 0;\n  annotationGroup = shapeSvg.insert(\"g\").attr(\"class\", \"annotation-group text\");\n  if (node.annotations.length > 0) {\n    const annotation = node.annotations[0];\n    await addText2(annotationGroup, {\n      text: `\\xAB${annotation}\\xBB`\n    }, 0);\n    const annotationGroupBBox = annotationGroup.node().getBBox();\n    annotationGroupHeight = annotationGroupBBox.height;\n  }\n  labelGroup = shapeSvg.insert(\"g\").attr(\"class\", \"label-group text\");\n  await addText2(labelGroup, node, 0, [\"font-weight: bolder\"]);\n  const labelGroupBBox = labelGroup.node().getBBox();\n  labelGroupHeight = labelGroupBBox.height;\n  membersGroup = shapeSvg.insert(\"g\").attr(\"class\", \"members-group text\");\n  let yOffset = 0;\n  for (const member of node.members) {\n    const height = await addText2(membersGroup, member, yOffset, [member.parseClassifier()]);\n    yOffset += height + TEXT_PADDING;\n  }\n  membersGroupHeight = membersGroup.node().getBBox().height;\n  if (membersGroupHeight <= 0) {\n    membersGroupHeight = GAP / 2;\n  }\n  methodsGroup = shapeSvg.insert(\"g\").attr(\"class\", \"methods-group text\");\n  let methodsYOffset = 0;\n  for (const method of node.methods) {\n    const height = await addText2(methodsGroup, method, methodsYOffset, [method.parseClassifier()]);\n    methodsYOffset += height + TEXT_PADDING;\n  }\n  let bbox = shapeSvg.node().getBBox();\n  if (annotationGroup !== null) {\n    const annotationGroupBBox = annotationGroup.node().getBBox();\n    annotationGroup.attr(\"transform\", `translate(${-annotationGroupBBox.width / 2})`);\n  }\n  labelGroup.attr(\"transform\", `translate(${-labelGroupBBox.width / 2}, ${annotationGroupHeight})`);\n  bbox = shapeSvg.node().getBBox();\n  membersGroup.attr(\"transform\", `translate(${0}, ${annotationGroupHeight + labelGroupHeight + GAP * 2})`);\n  bbox = shapeSvg.node().getBBox();\n  methodsGroup.attr(\"transform\", `translate(${0}, ${annotationGroupHeight + labelGroupHeight + (membersGroupHeight ? membersGroupHeight + GAP * 4 : GAP * 2)})`);\n  bbox = shapeSvg.node().getBBox();\n  return {\n    shapeSvg,\n    bbox\n  };\n}\n__name(textHelper, \"textHelper\");\nasync function addText2(parentGroup, node, yOffset, styles = []) {\n  const textEl = parentGroup.insert(\"g\").attr(\"class\", \"label\").attr(\"style\", styles.join(\"; \"));\n  const config = getConfig();\n  let useHtmlLabels = \"useHtmlLabels\" in node ? node.useHtmlLabels : evaluate(config.htmlLabels) ?? true;\n  let textContent = \"\";\n  if (\"text\" in node) {\n    textContent = node.text;\n  } else {\n    textContent = node.label;\n  }\n  if (!useHtmlLabels && textContent.startsWith(\"\\\\\")) {\n    textContent = textContent.substring(1);\n  }\n  if (hasKatex(textContent)) {\n    useHtmlLabels = true;\n  }\n  const text2 = await createText(textEl, sanitizeText2(decodeEntities(textContent)), {\n    width: calculateTextWidth(textContent, config) + 50,\n    // Add room for error when splitting text into multiple lines\n    classes: \"markdown-node-label\",\n    useHtmlLabels\n  }, config);\n  let bbox;\n  let numberOfLines = 1;\n  if (!useHtmlLabels) {\n    if (styles.includes(\"font-weight: bolder\")) {\n      select6(text2).selectAll(\"tspan\").attr(\"font-weight\", \"\");\n    }\n    numberOfLines = text2.children.length;\n    const textChild = text2.children[0];\n    if (text2.textContent === \"\" || text2.textContent.includes(\"&gt\")) {\n      textChild.textContent = textContent[0] + textContent.substring(1).replaceAll(\"&gt;\", \">\").replaceAll(\"&lt;\", \"<\").trim();\n      const preserveSpace = textContent[1] === \" \";\n      if (preserveSpace) {\n        textChild.textContent = textChild.textContent[0] + \" \" + textChild.textContent.substring(1);\n      }\n    }\n    if (textChild.textContent === \"undefined\") {\n      textChild.textContent = \"\";\n    }\n    bbox = text2.getBBox();\n  } else {\n    const div = text2.children[0];\n    const dv = select6(text2);\n    numberOfLines = div.innerHTML.split(\"<br>\").length;\n    if (div.innerHTML.includes(\"</math>\")) {\n      numberOfLines += div.innerHTML.split(\"<mrow>\").length - 1;\n    }\n    const images = div.getElementsByTagName(\"img\");\n    if (images) {\n      const noImgText = textContent.replace(/<img[^>]*>/g, \"\").trim() === \"\";\n      await Promise.all([...images].map(img => new Promise(res => {\n        function setupImage() {\n          img.style.display = \"flex\";\n          img.style.flexDirection = \"column\";\n          if (noImgText) {\n            const bodyFontSize = config.fontSize?.toString() ?? window.getComputedStyle(document.body).fontSize;\n            const enlargingFactor = 5;\n            const width = parseInt(bodyFontSize, 10) * enlargingFactor + \"px\";\n            img.style.minWidth = width;\n            img.style.maxWidth = width;\n          } else {\n            img.style.width = \"100%\";\n          }\n          res(img);\n        }\n        __name(setupImage, \"setupImage\");\n        setTimeout(() => {\n          if (img.complete) {\n            setupImage();\n          }\n        });\n        img.addEventListener(\"error\", setupImage);\n        img.addEventListener(\"load\", setupImage);\n      })));\n    }\n    bbox = div.getBoundingClientRect();\n    dv.attr(\"width\", bbox.width);\n    dv.attr(\"height\", bbox.height);\n  }\n  textEl.attr(\"transform\", \"translate(0,\" + (-bbox.height / (2 * numberOfLines) + yOffset) + \")\");\n  return bbox.height;\n}\n__name(addText2, \"addText\");\n\n// src/rendering-util/rendering-elements/shapes/classBox.ts\nasync function classBox(parent, node) {\n  const config = getConfig2();\n  const PADDING = config.class.padding ?? 12;\n  const GAP = PADDING;\n  const useHtmlLabels = node.useHtmlLabels ?? evaluate(config.htmlLabels) ?? true;\n  const classNode = node;\n  classNode.annotations = classNode.annotations ?? [];\n  classNode.members = classNode.members ?? [];\n  classNode.methods = classNode.methods ?? [];\n  const {\n    shapeSvg,\n    bbox\n  } = await textHelper(parent, node, config, useHtmlLabels, GAP);\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  node.cssStyles = classNode.styles || \"\";\n  const styles = classNode.styles?.join(\";\") || nodeStyles || \"\";\n  if (!node.cssStyles) {\n    node.cssStyles = styles.replaceAll(\"!important\", \"\").split(\";\");\n  }\n  const renderExtraBox = classNode.members.length === 0 && classNode.methods.length === 0 && !config.class?.hideEmptyMembersBox;\n  const rc = rough55.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const w = bbox.width;\n  let h = bbox.height;\n  if (classNode.members.length === 0 && classNode.methods.length === 0) {\n    h += GAP;\n  } else if (classNode.members.length > 0 && classNode.methods.length === 0) {\n    h += GAP * 2;\n  }\n  const x = -w / 2;\n  const y = -h / 2;\n  const roughRect = rc.rectangle(x - PADDING, y - PADDING - (renderExtraBox ? PADDING : classNode.members.length === 0 && classNode.methods.length === 0 ? -PADDING / 2 : 0), w + 2 * PADDING, h + 2 * PADDING + (renderExtraBox ? PADDING * 2 : classNode.members.length === 0 && classNode.methods.length === 0 ? -PADDING : 0), options);\n  const rect2 = shapeSvg.insert(() => roughRect, \":first-child\");\n  rect2.attr(\"class\", \"basic label-container\");\n  const rectBBox = rect2.node().getBBox();\n  shapeSvg.selectAll(\".text\").each((_, i, nodes) => {\n    const text2 = select7(nodes[i]);\n    const transform = text2.attr(\"transform\");\n    let translateY = 0;\n    if (transform) {\n      const regex = RegExp(/translate\\(([^,]+),([^)]+)\\)/);\n      const translate = regex.exec(transform);\n      if (translate) {\n        translateY = parseFloat(translate[2]);\n      }\n    }\n    let newTranslateY = translateY + y + PADDING - (renderExtraBox ? PADDING : classNode.members.length === 0 && classNode.methods.length === 0 ? -PADDING / 2 : 0);\n    if (!useHtmlLabels) {\n      newTranslateY -= 4;\n    }\n    let newTranslateX = x;\n    if (text2.attr(\"class\").includes(\"label-group\") || text2.attr(\"class\").includes(\"annotation-group\")) {\n      newTranslateX = -text2.node()?.getBBox().width / 2 || 0;\n      shapeSvg.selectAll(\"text\").each(function (_2, i2, nodes2) {\n        if (window.getComputedStyle(nodes2[i2]).textAnchor === \"middle\") {\n          newTranslateX = 0;\n        }\n      });\n    }\n    text2.attr(\"transform\", `translate(${newTranslateX}, ${newTranslateY})`);\n  });\n  const annotationGroupHeight = shapeSvg.select(\".annotation-group\").node().getBBox().height - (renderExtraBox ? PADDING / 2 : 0) || 0;\n  const labelGroupHeight = shapeSvg.select(\".label-group\").node().getBBox().height - (renderExtraBox ? PADDING / 2 : 0) || 0;\n  const membersGroupHeight = shapeSvg.select(\".members-group\").node().getBBox().height - (renderExtraBox ? PADDING / 2 : 0) || 0;\n  if (classNode.members.length > 0 || classNode.methods.length > 0 || renderExtraBox) {\n    const roughLine = rc.line(rectBBox.x, annotationGroupHeight + labelGroupHeight + y + PADDING, rectBBox.x + rectBBox.width, annotationGroupHeight + labelGroupHeight + y + PADDING, options);\n    const line = shapeSvg.insert(() => roughLine);\n    line.attr(\"class\", \"divider\").attr(\"style\", styles);\n  }\n  if (renderExtraBox || classNode.members.length > 0 || classNode.methods.length > 0) {\n    const roughLine = rc.line(rectBBox.x, annotationGroupHeight + labelGroupHeight + membersGroupHeight + y + GAP * 2 + PADDING, rectBBox.x + rectBBox.width, annotationGroupHeight + labelGroupHeight + membersGroupHeight + y + PADDING + GAP * 2, options);\n    const line = shapeSvg.insert(() => roughLine);\n    line.attr(\"class\", \"divider\").attr(\"style\", styles);\n  }\n  if (classNode.look !== \"handDrawn\") {\n    shapeSvg.selectAll(\"path\").attr(\"style\", styles);\n  }\n  rect2.select(\":nth-child(2)\").attr(\"style\", styles);\n  shapeSvg.selectAll(\".divider\").select(\"path\").attr(\"style\", styles);\n  if (node.labelStyle) {\n    shapeSvg.selectAll(\"span\").attr(\"style\", node.labelStyle);\n  } else {\n    shapeSvg.selectAll(\"span\").attr(\"style\", styles);\n  }\n  if (!useHtmlLabels) {\n    const colorRegex = RegExp(/color\\s*:\\s*([^;]*)/);\n    const match = colorRegex.exec(styles);\n    if (match) {\n      const colorStyle = match[0].replace(\"color\", \"fill\");\n      shapeSvg.selectAll(\"tspan\").attr(\"style\", colorStyle);\n    } else if (labelStyles) {\n      const match2 = colorRegex.exec(labelStyles);\n      if (match2) {\n        const colorStyle = match2[0].replace(\"color\", \"fill\");\n        shapeSvg.selectAll(\"tspan\").attr(\"style\", colorStyle);\n      }\n    }\n  }\n  updateNodeBounds(node, rect2);\n  node.intersect = function (point) {\n    return intersect_default.rect(node, point);\n  };\n  return shapeSvg;\n}\n__name(classBox, \"classBox\");\n\n// src/rendering-util/rendering-elements/shapes/requirementBox.ts\nimport rough56 from \"roughjs\";\nimport { select as select8 } from \"d3\";\nasync function requirementBox(parent, node) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const requirementNode = node;\n  const elementNode = node;\n  const padding = 20;\n  const gap = 20;\n  const isRequirementNode = \"verifyMethod\" in node;\n  const classes = getNodeClasses(node);\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", classes).attr(\"id\", node.domId ?? node.id);\n  let typeHeight;\n  if (isRequirementNode) {\n    typeHeight = await addText3(shapeSvg, `&lt;&lt;${requirementNode.type}&gt;&gt;`, 0, node.labelStyle);\n  } else {\n    typeHeight = await addText3(shapeSvg, \"&lt;&lt;Element&gt;&gt;\", 0, node.labelStyle);\n  }\n  let accumulativeHeight = typeHeight;\n  const nameHeight = await addText3(shapeSvg, requirementNode.name, accumulativeHeight, node.labelStyle + \"; font-weight: bold;\");\n  accumulativeHeight += nameHeight + gap;\n  if (isRequirementNode) {\n    const idHeight = await addText3(shapeSvg, `${requirementNode.requirementId ? `Id: ${requirementNode.requirementId}` : \"\"}`, accumulativeHeight, node.labelStyle);\n    accumulativeHeight += idHeight;\n    const textHeight = await addText3(shapeSvg, `${requirementNode.text ? `Text: ${requirementNode.text}` : \"\"}`, accumulativeHeight, node.labelStyle);\n    accumulativeHeight += textHeight;\n    const riskHeight = await addText3(shapeSvg, `${requirementNode.risk ? `Risk: ${requirementNode.risk}` : \"\"}`, accumulativeHeight, node.labelStyle);\n    accumulativeHeight += riskHeight;\n    await addText3(shapeSvg, `${requirementNode.verifyMethod ? `Verification: ${requirementNode.verifyMethod}` : \"\"}`, accumulativeHeight, node.labelStyle);\n  } else {\n    const typeHeight2 = await addText3(shapeSvg, `${elementNode.type ? `Type: ${elementNode.type}` : \"\"}`, accumulativeHeight, node.labelStyle);\n    accumulativeHeight += typeHeight2;\n    await addText3(shapeSvg, `${elementNode.docRef ? `Doc Ref: ${elementNode.docRef}` : \"\"}`, accumulativeHeight, node.labelStyle);\n  }\n  const totalWidth = (shapeSvg.node()?.getBBox().width ?? 200) + padding;\n  const totalHeight = (shapeSvg.node()?.getBBox().height ?? 200) + padding;\n  const x = -totalWidth / 2;\n  const y = -totalHeight / 2;\n  const rc = rough56.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const roughRect = rc.rectangle(x, y, totalWidth, totalHeight, options);\n  const rect2 = shapeSvg.insert(() => roughRect, \":first-child\");\n  rect2.attr(\"class\", \"basic label-container\").attr(\"style\", nodeStyles);\n  shapeSvg.selectAll(\".label\").each((_, i, nodes) => {\n    const text2 = select8(nodes[i]);\n    const transform = text2.attr(\"transform\");\n    let translateX = 0;\n    let translateY = 0;\n    if (transform) {\n      const regex = RegExp(/translate\\(([^,]+),([^)]+)\\)/);\n      const translate = regex.exec(transform);\n      if (translate) {\n        translateX = parseFloat(translate[1]);\n        translateY = parseFloat(translate[2]);\n      }\n    }\n    const newTranslateY = translateY - totalHeight / 2;\n    let newTranslateX = x + padding / 2;\n    if (i === 0 || i === 1) {\n      newTranslateX = translateX;\n    }\n    text2.attr(\"transform\", `translate(${newTranslateX}, ${newTranslateY + padding})`);\n  });\n  if (accumulativeHeight > typeHeight + nameHeight + gap) {\n    const roughLine = rc.line(x, y + typeHeight + nameHeight + gap, x + totalWidth, y + typeHeight + nameHeight + gap, options);\n    const dividerLine = shapeSvg.insert(() => roughLine);\n    dividerLine.attr(\"style\", nodeStyles);\n  }\n  updateNodeBounds(node, rect2);\n  node.intersect = function (point) {\n    return intersect_default.rect(node, point);\n  };\n  return shapeSvg;\n}\n__name(requirementBox, \"requirementBox\");\nasync function addText3(parentGroup, inputText, yOffset, style = \"\") {\n  if (inputText === \"\") {\n    return 0;\n  }\n  const textEl = parentGroup.insert(\"g\").attr(\"class\", \"label\").attr(\"style\", style);\n  const config = getConfig2();\n  const useHtmlLabels = config.htmlLabels ?? true;\n  const text2 = await createText(textEl, sanitizeText2(decodeEntities(inputText)), {\n    width: calculateTextWidth(inputText, config) + 50,\n    // Add room for error when splitting text into multiple lines\n    classes: \"markdown-node-label\",\n    useHtmlLabels,\n    style\n  }, config);\n  let bbox;\n  if (!useHtmlLabels) {\n    const textChild = text2.children[0];\n    for (const child of textChild.children) {\n      child.textContent = child.textContent.replaceAll(\"&gt;\", \">\").replaceAll(\"&lt;\", \"<\");\n      if (style) {\n        child.setAttribute(\"style\", style);\n      }\n    }\n    bbox = text2.getBBox();\n    bbox.height += 6;\n  } else {\n    const div = text2.children[0];\n    const dv = select8(text2);\n    bbox = div.getBoundingClientRect();\n    dv.attr(\"width\", bbox.width);\n    dv.attr(\"height\", bbox.height);\n  }\n  textEl.attr(\"transform\", `translate(${-bbox.width / 2},${-bbox.height / 2 + yOffset})`);\n  return bbox.height;\n}\n__name(addText3, \"addText\");\n\n// src/rendering-util/rendering-elements/shapes/kanbanItem.ts\nimport rough57 from \"roughjs\";\nvar colorFromPriority = /* @__PURE__ */__name(priority => {\n  switch (priority) {\n    case \"Very High\":\n      return \"red\";\n    case \"High\":\n      return \"orange\";\n    case \"Medium\":\n      return null;\n    // no stroke\n    case \"Low\":\n      return \"blue\";\n    case \"Very Low\":\n      return \"lightblue\";\n  }\n}, \"colorFromPriority\");\nasync function kanbanItem(parent, kanbanNode, {\n  config\n}) {\n  const {\n    labelStyles,\n    nodeStyles\n  } = styles2String(kanbanNode);\n  kanbanNode.labelStyle = labelStyles || \"\";\n  const labelPaddingX = 10;\n  const orgWidth = kanbanNode.width;\n  kanbanNode.width = (kanbanNode.width ?? 200) - 10;\n  const {\n    shapeSvg,\n    bbox,\n    label: labelElTitle\n  } = await labelHelper(parent, kanbanNode, getNodeClasses(kanbanNode));\n  const padding = kanbanNode.padding || 10;\n  let ticketUrl = \"\";\n  let link;\n  if (\"ticket\" in kanbanNode && kanbanNode.ticket && config?.kanban?.ticketBaseUrl) {\n    ticketUrl = config?.kanban?.ticketBaseUrl.replace(\"#TICKET#\", kanbanNode.ticket);\n    link = shapeSvg.insert(\"svg:a\", \":first-child\").attr(\"class\", \"kanban-ticket-link\").attr(\"xlink:href\", ticketUrl).attr(\"target\", \"_blank\");\n  }\n  const options = {\n    useHtmlLabels: kanbanNode.useHtmlLabels,\n    labelStyle: kanbanNode.labelStyle || \"\",\n    width: kanbanNode.width,\n    img: kanbanNode.img,\n    padding: kanbanNode.padding || 8,\n    centerLabel: false\n  };\n  let labelEl, bbox2;\n  if (link) {\n    ({\n      label: labelEl,\n      bbox: bbox2\n    } = await insertLabel(link, \"ticket\" in kanbanNode && kanbanNode.ticket || \"\", options));\n  } else {\n    ({\n      label: labelEl,\n      bbox: bbox2\n    } = await insertLabel(shapeSvg, \"ticket\" in kanbanNode && kanbanNode.ticket || \"\", options));\n  }\n  const {\n    label: labelElAssigned,\n    bbox: bboxAssigned\n  } = await insertLabel(shapeSvg, \"assigned\" in kanbanNode && kanbanNode.assigned || \"\", options);\n  kanbanNode.width = orgWidth;\n  const labelPaddingY = 10;\n  const totalWidth = kanbanNode?.width || 0;\n  const heightAdj = Math.max(bbox2.height, bboxAssigned.height) / 2;\n  const totalHeight = Math.max(bbox.height + labelPaddingY * 2, kanbanNode?.height || 0) + heightAdj;\n  const x = -totalWidth / 2;\n  const y = -totalHeight / 2;\n  labelElTitle.attr(\"transform\", \"translate(\" + (padding - totalWidth / 2) + \", \" + (-heightAdj - bbox.height / 2) + \")\");\n  labelEl.attr(\"transform\", \"translate(\" + (padding - totalWidth / 2) + \", \" + (-heightAdj + bbox.height / 2) + \")\");\n  labelElAssigned.attr(\"transform\", \"translate(\" + (padding + totalWidth / 2 - bboxAssigned.width - 2 * labelPaddingX) + \", \" + (-heightAdj + bbox.height / 2) + \")\");\n  let rect2;\n  const {\n    rx,\n    ry\n  } = kanbanNode;\n  const {\n    cssStyles\n  } = kanbanNode;\n  if (kanbanNode.look === \"handDrawn\") {\n    const rc = rough57.svg(shapeSvg);\n    const options2 = userNodeOverrides(kanbanNode, {});\n    const roughNode = rx || ry ? rc.path(createRoundedRectPathD(x, y, totalWidth, totalHeight, rx || 0), options2) : rc.rectangle(x, y, totalWidth, totalHeight, options2);\n    rect2 = shapeSvg.insert(() => roughNode, \":first-child\");\n    rect2.attr(\"class\", \"basic label-container\").attr(\"style\", cssStyles ? cssStyles : null);\n  } else {\n    rect2 = shapeSvg.insert(\"rect\", \":first-child\");\n    rect2.attr(\"class\", \"basic label-container __APA__\").attr(\"style\", nodeStyles).attr(\"rx\", rx ?? 5).attr(\"ry\", ry ?? 5).attr(\"x\", x).attr(\"y\", y).attr(\"width\", totalWidth).attr(\"height\", totalHeight);\n    const priority = \"priority\" in kanbanNode && kanbanNode.priority;\n    if (priority) {\n      const line = shapeSvg.append(\"line\");\n      const lineX = x + 2;\n      const y1 = y + Math.floor((rx ?? 0) / 2);\n      const y2 = y + totalHeight - Math.floor((rx ?? 0) / 2);\n      line.attr(\"x1\", lineX).attr(\"y1\", y1).attr(\"x2\", lineX).attr(\"y2\", y2).attr(\"stroke-width\", \"4\").attr(\"stroke\", colorFromPriority(priority));\n    }\n  }\n  updateNodeBounds(kanbanNode, rect2);\n  kanbanNode.height = totalHeight;\n  kanbanNode.intersect = function (point) {\n    return intersect_default.rect(kanbanNode, point);\n  };\n  return shapeSvg;\n}\n__name(kanbanItem, \"kanbanItem\");\n\n// src/rendering-util/rendering-elements/shapes.ts\nvar shapesDefs = [{\n  semanticName: \"Process\",\n  name: \"Rectangle\",\n  shortName: \"rect\",\n  description: \"Standard process shape\",\n  aliases: [\"proc\", \"process\", \"rectangle\"],\n  internalAliases: [\"squareRect\"],\n  handler: squareRect2\n}, {\n  semanticName: \"Event\",\n  name: \"Rounded Rectangle\",\n  shortName: \"rounded\",\n  description: \"Represents an event\",\n  aliases: [\"event\"],\n  internalAliases: [\"roundedRect\"],\n  handler: roundedRect\n}, {\n  semanticName: \"Terminal Point\",\n  name: \"Stadium\",\n  shortName: \"stadium\",\n  description: \"Terminal point\",\n  aliases: [\"terminal\", \"pill\"],\n  handler: stadium\n}, {\n  semanticName: \"Subprocess\",\n  name: \"Framed Rectangle\",\n  shortName: \"fr-rect\",\n  description: \"Subprocess\",\n  aliases: [\"subprocess\", \"subproc\", \"framed-rectangle\", \"subroutine\"],\n  handler: subroutine\n}, {\n  semanticName: \"Database\",\n  name: \"Cylinder\",\n  shortName: \"cyl\",\n  description: \"Database storage\",\n  aliases: [\"db\", \"database\", \"cylinder\"],\n  handler: cylinder\n}, {\n  semanticName: \"Start\",\n  name: \"Circle\",\n  shortName: \"circle\",\n  description: \"Starting point\",\n  aliases: [\"circ\"],\n  handler: circle\n}, {\n  semanticName: \"Decision\",\n  name: \"Diamond\",\n  shortName: \"diam\",\n  description: \"Decision-making step\",\n  aliases: [\"decision\", \"diamond\", \"question\"],\n  handler: question\n}, {\n  semanticName: \"Prepare Conditional\",\n  name: \"Hexagon\",\n  shortName: \"hex\",\n  description: \"Preparation or condition step\",\n  aliases: [\"hexagon\", \"prepare\"],\n  handler: hexagon\n}, {\n  semanticName: \"Data Input/Output\",\n  name: \"Lean Right\",\n  shortName: \"lean-r\",\n  description: \"Represents input or output\",\n  aliases: [\"lean-right\", \"in-out\"],\n  internalAliases: [\"lean_right\"],\n  handler: lean_right\n}, {\n  semanticName: \"Data Input/Output\",\n  name: \"Lean Left\",\n  shortName: \"lean-l\",\n  description: \"Represents output or input\",\n  aliases: [\"lean-left\", \"out-in\"],\n  internalAliases: [\"lean_left\"],\n  handler: lean_left\n}, {\n  semanticName: \"Priority Action\",\n  name: \"Trapezoid Base Bottom\",\n  shortName: \"trap-b\",\n  description: \"Priority action\",\n  aliases: [\"priority\", \"trapezoid-bottom\", \"trapezoid\"],\n  handler: trapezoid\n}, {\n  semanticName: \"Manual Operation\",\n  name: \"Trapezoid Base Top\",\n  shortName: \"trap-t\",\n  description: \"Represents a manual task\",\n  aliases: [\"manual\", \"trapezoid-top\", \"inv-trapezoid\"],\n  internalAliases: [\"inv_trapezoid\"],\n  handler: inv_trapezoid\n}, {\n  semanticName: \"Stop\",\n  name: \"Double Circle\",\n  shortName: \"dbl-circ\",\n  description: \"Represents a stop point\",\n  aliases: [\"double-circle\"],\n  internalAliases: [\"doublecircle\"],\n  handler: doublecircle\n}, {\n  semanticName: \"Text Block\",\n  name: \"Text Block\",\n  shortName: \"text\",\n  description: \"Text block\",\n  handler: text\n}, {\n  semanticName: \"Card\",\n  name: \"Notched Rectangle\",\n  shortName: \"notch-rect\",\n  description: \"Represents a card\",\n  aliases: [\"card\", \"notched-rectangle\"],\n  handler: card\n}, {\n  semanticName: \"Lined/Shaded Process\",\n  name: \"Lined Rectangle\",\n  shortName: \"lin-rect\",\n  description: \"Lined process shape\",\n  aliases: [\"lined-rectangle\", \"lined-process\", \"lin-proc\", \"shaded-process\"],\n  handler: shadedProcess\n}, {\n  semanticName: \"Start\",\n  name: \"Small Circle\",\n  shortName: \"sm-circ\",\n  description: \"Small starting point\",\n  aliases: [\"start\", \"small-circle\"],\n  internalAliases: [\"stateStart\"],\n  handler: stateStart\n}, {\n  semanticName: \"Stop\",\n  name: \"Framed Circle\",\n  shortName: \"fr-circ\",\n  description: \"Stop point\",\n  aliases: [\"stop\", \"framed-circle\"],\n  internalAliases: [\"stateEnd\"],\n  handler: stateEnd\n}, {\n  semanticName: \"Fork/Join\",\n  name: \"Filled Rectangle\",\n  shortName: \"fork\",\n  description: \"Fork or join in process flow\",\n  aliases: [\"join\"],\n  internalAliases: [\"forkJoin\"],\n  handler: forkJoin\n}, {\n  semanticName: \"Collate\",\n  name: \"Hourglass\",\n  shortName: \"hourglass\",\n  description: \"Represents a collate operation\",\n  aliases: [\"hourglass\", \"collate\"],\n  handler: hourglass\n}, {\n  semanticName: \"Comment\",\n  name: \"Curly Brace\",\n  shortName: \"brace\",\n  description: \"Adds a comment\",\n  aliases: [\"comment\", \"brace-l\"],\n  handler: curlyBraceLeft\n}, {\n  semanticName: \"Comment Right\",\n  name: \"Curly Brace\",\n  shortName: \"brace-r\",\n  description: \"Adds a comment\",\n  handler: curlyBraceRight\n}, {\n  semanticName: \"Comment with braces on both sides\",\n  name: \"Curly Braces\",\n  shortName: \"braces\",\n  description: \"Adds a comment\",\n  handler: curlyBraces\n}, {\n  semanticName: \"Com Link\",\n  name: \"Lightning Bolt\",\n  shortName: \"bolt\",\n  description: \"Communication link\",\n  aliases: [\"com-link\", \"lightning-bolt\"],\n  handler: lightningBolt\n}, {\n  semanticName: \"Document\",\n  name: \"Document\",\n  shortName: \"doc\",\n  description: \"Represents a document\",\n  aliases: [\"doc\", \"document\"],\n  handler: waveEdgedRectangle\n}, {\n  semanticName: \"Delay\",\n  name: \"Half-Rounded Rectangle\",\n  shortName: \"delay\",\n  description: \"Represents a delay\",\n  aliases: [\"half-rounded-rectangle\"],\n  handler: halfRoundedRectangle\n}, {\n  semanticName: \"Direct Access Storage\",\n  name: \"Horizontal Cylinder\",\n  shortName: \"h-cyl\",\n  description: \"Direct access storage\",\n  aliases: [\"das\", \"horizontal-cylinder\"],\n  handler: tiltedCylinder\n}, {\n  semanticName: \"Disk Storage\",\n  name: \"Lined Cylinder\",\n  shortName: \"lin-cyl\",\n  description: \"Disk storage\",\n  aliases: [\"disk\", \"lined-cylinder\"],\n  handler: linedCylinder\n}, {\n  semanticName: \"Display\",\n  name: \"Curved Trapezoid\",\n  shortName: \"curv-trap\",\n  description: \"Represents a display\",\n  aliases: [\"curved-trapezoid\", \"display\"],\n  handler: curvedTrapezoid\n}, {\n  semanticName: \"Divided Process\",\n  name: \"Divided Rectangle\",\n  shortName: \"div-rect\",\n  description: \"Divided process shape\",\n  aliases: [\"div-proc\", \"divided-rectangle\", \"divided-process\"],\n  handler: dividedRectangle\n}, {\n  semanticName: \"Extract\",\n  name: \"Triangle\",\n  shortName: \"tri\",\n  description: \"Extraction process\",\n  aliases: [\"extract\", \"triangle\"],\n  handler: triangle\n}, {\n  semanticName: \"Internal Storage\",\n  name: \"Window Pane\",\n  shortName: \"win-pane\",\n  description: \"Internal storage\",\n  aliases: [\"internal-storage\", \"window-pane\"],\n  handler: windowPane\n}, {\n  semanticName: \"Junction\",\n  name: \"Filled Circle\",\n  shortName: \"f-circ\",\n  description: \"Junction point\",\n  aliases: [\"junction\", \"filled-circle\"],\n  handler: filledCircle\n}, {\n  semanticName: \"Loop Limit\",\n  name: \"Trapezoidal Pentagon\",\n  shortName: \"notch-pent\",\n  description: \"Loop limit step\",\n  aliases: [\"loop-limit\", \"notched-pentagon\"],\n  handler: trapezoidalPentagon\n}, {\n  semanticName: \"Manual File\",\n  name: \"Flipped Triangle\",\n  shortName: \"flip-tri\",\n  description: \"Manual file operation\",\n  aliases: [\"manual-file\", \"flipped-triangle\"],\n  handler: flippedTriangle\n}, {\n  semanticName: \"Manual Input\",\n  name: \"Sloped Rectangle\",\n  shortName: \"sl-rect\",\n  description: \"Manual input step\",\n  aliases: [\"manual-input\", \"sloped-rectangle\"],\n  handler: slopedRect\n}, {\n  semanticName: \"Multi-Document\",\n  name: \"Stacked Document\",\n  shortName: \"docs\",\n  description: \"Multiple documents\",\n  aliases: [\"documents\", \"st-doc\", \"stacked-document\"],\n  handler: multiWaveEdgedRectangle\n}, {\n  semanticName: \"Multi-Process\",\n  name: \"Stacked Rectangle\",\n  shortName: \"st-rect\",\n  description: \"Multiple processes\",\n  aliases: [\"procs\", \"processes\", \"stacked-rectangle\"],\n  handler: multiRect\n}, {\n  semanticName: \"Stored Data\",\n  name: \"Bow Tie Rectangle\",\n  shortName: \"bow-rect\",\n  description: \"Stored data\",\n  aliases: [\"stored-data\", \"bow-tie-rectangle\"],\n  handler: bowTieRect\n}, {\n  semanticName: \"Summary\",\n  name: \"Crossed Circle\",\n  shortName: \"cross-circ\",\n  description: \"Summary\",\n  aliases: [\"summary\", \"crossed-circle\"],\n  handler: crossedCircle\n}, {\n  semanticName: \"Tagged Document\",\n  name: \"Tagged Document\",\n  shortName: \"tag-doc\",\n  description: \"Tagged document\",\n  aliases: [\"tag-doc\", \"tagged-document\"],\n  handler: taggedWaveEdgedRectangle\n}, {\n  semanticName: \"Tagged Process\",\n  name: \"Tagged Rectangle\",\n  shortName: \"tag-rect\",\n  description: \"Tagged process\",\n  aliases: [\"tagged-rectangle\", \"tag-proc\", \"tagged-process\"],\n  handler: taggedRect\n}, {\n  semanticName: \"Paper Tape\",\n  name: \"Flag\",\n  shortName: \"flag\",\n  description: \"Paper tape\",\n  aliases: [\"paper-tape\"],\n  handler: waveRectangle\n}, {\n  semanticName: \"Odd\",\n  name: \"Odd\",\n  shortName: \"odd\",\n  description: \"Odd shape\",\n  internalAliases: [\"rect_left_inv_arrow\"],\n  handler: rect_left_inv_arrow\n}, {\n  semanticName: \"Lined Document\",\n  name: \"Lined Document\",\n  shortName: \"lin-doc\",\n  description: \"Lined document\",\n  aliases: [\"lined-document\"],\n  handler: linedWaveEdgedRect\n}];\nvar generateShapeMap = /* @__PURE__ */__name(() => {\n  const undocumentedShapes = {\n    // States\n    state,\n    choice,\n    note,\n    // Rectangles\n    rectWithTitle,\n    labelRect,\n    // Icons\n    iconSquare,\n    iconCircle,\n    icon,\n    iconRounded,\n    imageSquare,\n    anchor,\n    // Kanban diagram\n    kanbanItem,\n    // class diagram\n    classBox,\n    // er diagram\n    erBox,\n    // Requirement diagram\n    requirementBox\n  };\n  const entries = [...Object.entries(undocumentedShapes), ...shapesDefs.flatMap(shape => {\n    const aliases = [shape.shortName, ...(\"aliases\" in shape ? shape.aliases : []), ...(\"internalAliases\" in shape ? shape.internalAliases : [])];\n    return aliases.map(alias => [alias, shape.handler]);\n  })];\n  return Object.fromEntries(entries);\n}, \"generateShapeMap\");\nvar shapes2 = generateShapeMap();\nfunction isValidShape(shape) {\n  return shape in shapes2;\n}\n__name(isValidShape, \"isValidShape\");\n\n// src/rendering-util/rendering-elements/nodes.ts\nvar nodeElems = /* @__PURE__ */new Map();\nasync function insertNode(elem, node, renderOptions) {\n  let newEl;\n  let el;\n  if (node.shape === \"rect\") {\n    if (node.rx && node.ry) {\n      node.shape = \"roundedRect\";\n    } else {\n      node.shape = \"squareRect\";\n    }\n  }\n  const shapeHandler = node.shape ? shapes2[node.shape] : void 0;\n  if (!shapeHandler) {\n    throw new Error(`No such shape: ${node.shape}. Please check your syntax.`);\n  }\n  if (node.link) {\n    let target;\n    if (renderOptions.config.securityLevel === \"sandbox\") {\n      target = \"_top\";\n    } else if (node.linkTarget) {\n      target = node.linkTarget || \"_blank\";\n    }\n    newEl = elem.insert(\"svg:a\").attr(\"xlink:href\", node.link).attr(\"target\", target ?? null);\n    el = await shapeHandler(newEl, node, renderOptions);\n  } else {\n    el = await shapeHandler(elem, node, renderOptions);\n    newEl = el;\n  }\n  if (node.tooltip) {\n    el.attr(\"title\", node.tooltip);\n  }\n  nodeElems.set(node.id, newEl);\n  if (node.haveCallback) {\n    newEl.attr(\"class\", newEl.attr(\"class\") + \" clickable\");\n  }\n  return newEl;\n}\n__name(insertNode, \"insertNode\");\nvar setNodeElem = /* @__PURE__ */__name((elem, node) => {\n  nodeElems.set(node.id, elem);\n}, \"setNodeElem\");\nvar clear2 = /* @__PURE__ */__name(() => {\n  nodeElems.clear();\n}, \"clear\");\nvar positionNode = /* @__PURE__ */__name(node => {\n  const el = nodeElems.get(node.id);\n  log.trace(\"Transforming node\", node.diff, node, \"translate(\" + (node.x - node.width / 2 - 5) + \", \" + node.width / 2 + \")\");\n  const padding = 8;\n  const diff = node.diff || 0;\n  if (node.clusterNode) {\n    el.attr(\"transform\", \"translate(\" + (node.x + diff - node.width / 2) + \", \" + (node.y - node.height / 2 - padding) + \")\");\n  } else {\n    el.attr(\"transform\", \"translate(\" + node.x + \", \" + node.y + \")\");\n  }\n  return diff;\n}, \"positionNode\");\nexport { labelHelper, updateNodeBounds, isLabelStyle, createLabel_default, isValidShape, insertCluster, clear, insertNode, setNodeElem, clear2, positionNode };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAAS,EAAEA,IAAGC,IAAGC,IAAG;AAClB,MAAIF,MAAKA,GAAE,QAAQ;AACjB,UAAM,CAACG,IAAGC,EAAC,IAAIH,IACbI,KAAI,KAAK,KAAK,MAAMH,IACpBI,KAAI,KAAK,IAAID,EAAC,GACdE,KAAI,KAAK,IAAIF,EAAC;AAChB,eAAWJ,MAAKD,IAAG;AACjB,YAAM,CAACA,IAAGE,EAAC,IAAID;AACf,MAAAA,GAAE,CAAC,KAAKD,KAAIG,MAAKG,MAAKJ,KAAIE,MAAKG,KAAIJ,IAAGF,GAAE,CAAC,KAAKD,KAAIG,MAAKI,MAAKL,KAAIE,MAAKE,KAAIF;AAAA,IAC3E;AAAA,EACF;AACF;AACA,SAAS,EAAEJ,IAAGC,IAAG;AACf,SAAOD,GAAE,CAAC,MAAMC,GAAE,CAAC,KAAKD,GAAE,CAAC,MAAMC,GAAE,CAAC;AACtC;AACA,SAAS,EAAEC,IAAGC,IAAGC,IAAGC,KAAI,GAAG;AACzB,QAAMC,KAAIF,IACRG,KAAI,KAAK,IAAIJ,IAAG,GAAE,GAClBK,KAAIN,GAAE,CAAC,KAAKA,GAAE,CAAC,EAAE,CAAC,KAAK,YAAY,OAAOA,GAAE,CAAC,EAAE,CAAC,IAAI,CAACA,EAAC,IAAIA,IAC1DO,KAAI,CAAC,GAAG,CAAC;AACX,MAAIH,GAAG,YAAWL,MAAKO,GAAG,GAAEP,IAAGQ,IAAGH,EAAC;AACnC,QAAMI,KAAI,SAAUV,IAAGE,IAAGC,IAAG;AAC3B,UAAMC,KAAI,CAAC;AACX,eAAWF,MAAKF,IAAG;AACjB,YAAMA,KAAI,CAAC,GAAGE,EAAC;AACf,QAAEF,GAAE,CAAC,GAAGA,GAAEA,GAAE,SAAS,CAAC,CAAC,KAAKA,GAAE,KAAK,CAACA,GAAE,CAAC,EAAE,CAAC,GAAGA,GAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGA,GAAE,SAAS,KAAKI,GAAE,KAAKJ,EAAC;AAAA,IAClF;AACA,UAAMK,KAAI,CAAC;AACX,IAAAH,KAAI,KAAK,IAAIA,IAAG,GAAE;AAClB,UAAMI,KAAI,CAAC;AACX,eAAWN,MAAKI,GAAG,UAASH,KAAI,GAAGA,KAAID,GAAE,SAAS,GAAGC,MAAK;AACxD,YAAMC,KAAIF,GAAEC,EAAC,GACXE,KAAIH,GAAEC,KAAI,CAAC;AACb,UAAIC,GAAE,CAAC,MAAMC,GAAE,CAAC,GAAG;AACjB,cAAMH,KAAI,KAAK,IAAIE,GAAE,CAAC,GAAGC,GAAE,CAAC,CAAC;AAC7B,QAAAG,GAAE,KAAK;AAAA,UACL,MAAMN;AAAA,UACN,MAAM,KAAK,IAAIE,GAAE,CAAC,GAAGC,GAAE,CAAC,CAAC;AAAA,UACzB,GAAGH,OAAME,GAAE,CAAC,IAAIA,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,UAC1B,SAASA,GAAE,CAAC,IAAID,GAAE,CAAC,MAAMC,GAAE,CAAC,IAAID,GAAE,CAAC;AAAA,QACrC,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAII,GAAE,KAAK,CAACN,IAAGC,OAAMD,GAAE,OAAOC,GAAE,OAAO,KAAKD,GAAE,OAAOC,GAAE,OAAO,IAAID,GAAE,IAAIC,GAAE,IAAI,KAAKD,GAAE,IAAIC,GAAE,IAAI,IAAID,GAAE,SAASC,GAAE,OAAO,KAAKD,GAAE,OAAOC,GAAE,QAAQ,KAAK,IAAID,GAAE,OAAOC,GAAE,IAAI,CAAC,GAAG,CAACK,GAAE,OAAQ,QAAOD;AAC7L,QAAIE,KAAI,CAAC,GACPC,KAAIF,GAAE,CAAC,EAAE,MACTG,KAAI;AACN,WAAOF,GAAE,UAAUD,GAAE,UAAS;AAC5B,UAAIA,GAAE,QAAQ;AACZ,YAAIN,KAAI;AACR,iBAASC,KAAI,GAAGA,KAAIK,GAAE,UAAU,EAAEA,GAAEL,EAAC,EAAE,OAAOO,KAAIP,KAAK,CAAAD,KAAIC;AAC3D,QAAAK,GAAE,OAAO,GAAGN,KAAI,CAAC,EAAE,QAAQ,CAAAA,OAAK;AAC9B,UAAAO,GAAE,KAAK;AAAA,YACL,GAAGC;AAAA,YACH,MAAMR;AAAA,UACR,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,UAAIO,KAAIA,GAAE,OAAO,CAAAP,OAAK,EAAEA,GAAE,KAAK,QAAQQ,GAAE,GAAGD,GAAE,KAAK,CAACP,IAAGC,OAAMD,GAAE,KAAK,MAAMC,GAAE,KAAK,IAAI,KAAKD,GAAE,KAAK,IAAIC,GAAE,KAAK,KAAK,KAAK,IAAID,GAAE,KAAK,IAAIC,GAAE,KAAK,CAAC,CAAC,IAAI,MAAME,MAAKM,KAAIP,MAAK,MAAMK,GAAE,SAAS,EAAG,UAASP,KAAI,GAAGA,KAAIO,GAAE,QAAQP,MAAK,GAAG;AAC9N,cAAMC,KAAID,KAAI;AACd,YAAIC,MAAKM,GAAE,OAAQ;AACnB,cAAML,KAAIK,GAAEP,EAAC,EAAE,MACbG,KAAII,GAAEN,EAAC,EAAE;AACX,QAAAI,GAAE,KAAK,CAAC,CAAC,KAAK,MAAMH,GAAE,CAAC,GAAGM,EAAC,GAAG,CAAC,KAAK,MAAML,GAAE,CAAC,GAAGK,EAAC,CAAC,CAAC;AAAA,MACrD;AACA,MAAAA,MAAKL,IAAGI,GAAE,QAAQ,CAAAP,OAAK;AACrB,QAAAA,GAAE,KAAK,IAAIA,GAAE,KAAK,IAAIG,KAAIH,GAAE,KAAK;AAAA,MACnC,CAAC,GAAGS;AAAA,IACN;AACA,WAAOJ;AAAA,EACT,EAAEG,IAAGD,IAAGF,EAAC;AACT,MAAIC,IAAG;AACL,eAAWL,MAAKO,GAAG,GAAEP,IAAGQ,IAAG,CAACH,EAAC;AAC7B,KAAC,SAAUL,IAAGC,IAAGC,IAAG;AAClB,YAAMC,KAAI,CAAC;AACX,MAAAH,GAAE,QAAQ,CAAAD,OAAKI,GAAE,KAAK,GAAGJ,EAAC,CAAC,GAAG,EAAEI,IAAGF,IAAGC,EAAC;AAAA,IACzC,EAAEO,IAAGD,IAAG,CAACH,EAAC;AAAA,EACZ;AACA,SAAOI;AACT;AACA,SAAS,EAAEV,IAAGC,IAAG;AACf,MAAIE;AACJ,QAAMC,KAAIH,GAAE,eAAe;AAC3B,MAAII,KAAIJ,GAAE;AACV,EAAAI,KAAI,MAAMA,KAAI,IAAIJ,GAAE,cAAcI,KAAI,KAAK,MAAM,KAAK,IAAIA,IAAG,GAAE,CAAC;AAChE,MAAIC,KAAI;AACR,SAAOL,GAAE,aAAa,OAAO,UAAUE,KAAIF,GAAE,eAAe,WAAWE,KAAI,SAASA,GAAE,KAAK,MAAM,KAAK,OAAO,KAAK,QAAOG,KAAID,KAAI,EAAEL,IAAGK,IAAGD,IAAGE,MAAK,CAAC;AACpJ;AACA,IAAM,IAAN,MAAQ;AAAA,EACN,YAAYN,IAAG;AACb,SAAK,SAASA;AAAA,EAChB;AAAA,EACA,aAAaA,IAAGC,IAAG;AACjB,WAAO,KAAK,cAAcD,IAAGC,EAAC;AAAA,EAChC;AAAA,EACA,cAAcD,IAAGC,IAAG;AAClB,UAAMC,KAAI,EAAEF,IAAGC,EAAC;AAChB,WAAO;AAAA,MACL,MAAM;AAAA,MACN,KAAK,KAAK,YAAYC,IAAGD,EAAC;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,YAAYD,IAAGC,IAAG;AAChB,UAAMC,KAAI,CAAC;AACX,eAAWC,MAAKH,GAAG,CAAAE,GAAE,KAAK,GAAG,KAAK,OAAO,cAAcC,GAAE,CAAC,EAAE,CAAC,GAAGA,GAAE,CAAC,EAAE,CAAC,GAAGA,GAAE,CAAC,EAAE,CAAC,GAAGA,GAAE,CAAC,EAAE,CAAC,GAAGF,EAAC,CAAC;AAC7F,WAAOC;AAAA,EACT;AACF;AACA,SAAS,EAAEF,IAAG;AACZ,QAAMC,KAAID,GAAE,CAAC,GACXE,KAAIF,GAAE,CAAC;AACT,SAAO,KAAK,KAAK,KAAK,IAAIC,GAAE,CAAC,IAAIC,GAAE,CAAC,GAAG,CAAC,IAAI,KAAK,IAAID,GAAE,CAAC,IAAIC,GAAE,CAAC,GAAG,CAAC,CAAC;AACtE;AACA,IAAM,IAAN,cAAgB,EAAE;AAAA,EAChB,aAAaF,IAAGC,IAAG;AACjB,QAAIC,KAAID,GAAE;AACV,IAAAC,KAAI,MAAMA,KAAI,IAAID,GAAE,cAAcC,KAAI,KAAK,IAAIA,IAAG,GAAE;AACpD,UAAME,KAAI,EAAEJ,IAAG,OAAO,OAAO,CAAC,GAAGC,IAAG;AAAA,MAChC,YAAYC;AAAA,IACd,CAAC,CAAC,GACFI,KAAI,KAAK,KAAK,MAAML,GAAE,cACtBM,KAAI,CAAC,GACLC,KAAI,MAAKN,KAAI,KAAK,IAAII,EAAC,GACvBG,KAAI,MAAKP,KAAI,KAAK,IAAII,EAAC;AACzB,eAAW,CAACN,IAAGC,EAAC,KAAKG,GAAG,GAAE,CAACJ,IAAGC,EAAC,CAAC,KAAKM,GAAE,KAAK,CAAC,CAACP,GAAE,CAAC,IAAIQ,IAAGR,GAAE,CAAC,IAAIS,EAAC,GAAG,CAAC,GAAGR,EAAC,CAAC,GAAG,CAAC,CAACD,GAAE,CAAC,IAAIQ,IAAGR,GAAE,CAAC,IAAIS,EAAC,GAAG,CAAC,GAAGR,EAAC,CAAC,CAAC;AAC1G,WAAO;AAAA,MACL,MAAM;AAAA,MACN,KAAK,KAAK,YAAYM,IAAGN,EAAC;AAAA,IAC5B;AAAA,EACF;AACF;AACA,IAAM,IAAN,cAAgB,EAAE;AAAA,EAChB,aAAaD,IAAGC,IAAG;AACjB,UAAMC,KAAI,KAAK,cAAcF,IAAGC,EAAC,GAC/BE,KAAI,OAAO,OAAO,CAAC,GAAGF,IAAG;AAAA,MACvB,cAAcA,GAAE,eAAe;AAAA,IACjC,CAAC,GACDG,KAAI,KAAK,cAAcJ,IAAGG,EAAC;AAC7B,WAAOD,GAAE,MAAMA,GAAE,IAAI,OAAOE,GAAE,GAAG,GAAGF;AAAA,EACtC;AACF;AACA,IAAM,IAAN,MAAQ;AAAA,EACN,YAAYF,IAAG;AACb,SAAK,SAASA;AAAA,EAChB;AAAA,EACA,aAAaA,IAAGC,IAAG;AACjB,UAAMC,KAAI,EAAEF,IAAGC,KAAI,OAAO,OAAO,CAAC,GAAGA,IAAG;AAAA,MACtC,cAAc;AAAA,IAChB,CAAC,CAAC;AACF,WAAO,KAAK,YAAYC,IAAGD,EAAC;AAAA,EAC9B;AAAA,EACA,YAAYD,IAAGC,IAAG;AAChB,UAAMC,KAAI,CAAC;AACX,QAAIC,KAAIF,GAAE;AACV,IAAAE,KAAI,MAAMA,KAAI,IAAIF,GAAE,cAAcE,KAAI,KAAK,IAAIA,IAAG,GAAE;AACpD,QAAIC,KAAIH,GAAE;AACV,IAAAG,KAAI,MAAMA,KAAIH,GAAE,cAAc;AAC9B,UAAMK,KAAIH,KAAI;AACd,eAAWI,MAAKP,IAAG;AACjB,YAAMA,KAAI,EAAEO,EAAC,GACXC,KAAIR,KAAIG,IACRM,KAAI,KAAK,KAAKD,EAAC,IAAI,GACnBE,KAAIV,KAAIS,KAAIN,IACZQ,MAAKJ,GAAE,CAAC,EAAE,CAAC,IAAIA,GAAE,CAAC,EAAE,CAAC,KAAK,IAAIJ,KAAI,GAClCS,KAAI,KAAK,IAAIL,GAAE,CAAC,EAAE,CAAC,GAAGA,GAAE,CAAC,EAAE,CAAC,CAAC;AAC/B,eAASP,KAAI,GAAGA,KAAIS,IAAGT,MAAK;AAC1B,cAAMK,KAAIO,KAAIF,KAAIV,KAAIG,IACpBI,KAAII,KAAIL,KAAI,IAAI,KAAK,OAAO,IAAIA,IAChCE,KAAIH,KAAIC,KAAI,IAAI,KAAK,OAAO,IAAIA,IAChCG,KAAI,KAAK,OAAO,QAAQF,IAAGC,IAAGJ,IAAGA,IAAGH,EAAC;AACvC,QAAAC,GAAE,KAAK,GAAGO,GAAE,GAAG;AAAA,MACjB;AAAA,IACF;AACA,WAAO;AAAA,MACL,MAAM;AAAA,MACN,KAAKP;AAAA,IACP;AAAA,EACF;AACF;AACA,IAAM,IAAN,MAAQ;AAAA,EACN,YAAYF,IAAG;AACb,SAAK,SAASA;AAAA,EAChB;AAAA,EACA,aAAaA,IAAGC,IAAG;AACjB,UAAMC,KAAI,EAAEF,IAAGC,EAAC;AAChB,WAAO;AAAA,MACL,MAAM;AAAA,MACN,KAAK,KAAK,WAAWC,IAAGD,EAAC;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,WAAWD,IAAGC,IAAG;AACf,UAAMC,KAAID,GAAE,aAAa,IAAIA,GAAE,aAAa,IAAI,IAAIA,GAAE,cAAcA,GAAE,aAAaA,GAAE,YACnFE,KAAIF,GAAE,UAAU,IAAIA,GAAE,aAAa,IAAI,IAAIA,GAAE,cAAcA,GAAE,aAAaA,GAAE,SAC5EG,KAAI,CAAC;AACP,WAAOJ,GAAE,QAAQ,CAAAA,OAAK;AACpB,YAAMM,KAAI,EAAEN,EAAC,GACXO,KAAI,KAAK,MAAMD,MAAKJ,KAAIC,GAAE,GAC1BK,MAAKF,KAAIH,KAAII,MAAKL,KAAIC,OAAM;AAC9B,UAAIM,KAAIT,GAAE,CAAC,GACTU,KAAIV,GAAE,CAAC;AACT,MAAAS,GAAE,CAAC,IAAIC,GAAE,CAAC,MAAMD,KAAIT,GAAE,CAAC,GAAGU,KAAIV,GAAE,CAAC;AACjC,YAAMW,KAAI,KAAK,MAAMD,GAAE,CAAC,IAAID,GAAE,CAAC,MAAMC,GAAE,CAAC,IAAID,GAAE,CAAC,EAAE;AACjD,eAAST,KAAI,GAAGA,KAAIO,IAAGP,MAAK;AAC1B,cAAMK,KAAIL,MAAKE,KAAIC,KACjBG,KAAID,KAAIH,IACRK,KAAI,CAACE,GAAE,CAAC,IAAIJ,KAAI,KAAK,IAAIM,EAAC,IAAIH,KAAI,KAAK,IAAIG,EAAC,GAAGF,GAAE,CAAC,IAAIJ,KAAI,KAAK,IAAIM,EAAC,IAAIH,KAAI,KAAK,IAAIG,EAAC,CAAC,GACvFD,KAAI,CAACD,GAAE,CAAC,IAAIH,KAAI,KAAK,IAAIK,EAAC,IAAIH,KAAI,KAAK,IAAIG,EAAC,GAAGF,GAAE,CAAC,IAAIH,KAAI,KAAK,IAAIK,EAAC,IAAIH,KAAI,KAAK,IAAIG,EAAC,CAAC;AACzF,QAAAP,GAAE,KAAK,GAAG,KAAK,OAAO,cAAcG,GAAE,CAAC,GAAGA,GAAE,CAAC,GAAGG,GAAE,CAAC,GAAGA,GAAE,CAAC,GAAGT,EAAC,CAAC;AAAA,MAChE;AAAA,IACF,CAAC,GAAGG;AAAA,EACN;AACF;AACA,IAAM,IAAN,MAAQ;AAAA,EACN,YAAYJ,IAAG;AACb,SAAK,SAASA;AAAA,EAChB;AAAA,EACA,aAAaA,IAAGC,IAAG;AACjB,UAAMC,KAAID,GAAE,aAAa,IAAI,IAAIA,GAAE,cAAcA,GAAE,YACjDG,KAAIH,GAAE,eAAe,IAAIC,KAAID,GAAE,cAC/BI,KAAI,EAAEL,IAAGC,KAAI,OAAO,OAAO,CAAC,GAAGA,IAAG;AAAA,MAChC,YAAYC,KAAIE;AAAA,IAClB,CAAC,CAAC;AACJ,WAAO;AAAA,MACL,MAAM;AAAA,MACN,KAAK,KAAK,YAAYC,IAAGD,IAAGH,EAAC;AAAA,IAC/B;AAAA,EACF;AAAA,EACA,YAAYD,IAAGC,IAAGC,IAAG;AACnB,UAAMC,KAAI,CAAC;AACX,WAAOH,GAAE,QAAQ,CAAAA,OAAK;AACpB,YAAMI,KAAI,EAAEJ,EAAC,GACXM,KAAI,KAAK,MAAMF,MAAK,IAAIH,GAAE;AAC5B,UAAIM,KAAIP,GAAE,CAAC,GACTQ,KAAIR,GAAE,CAAC;AACT,MAAAO,GAAE,CAAC,IAAIC,GAAE,CAAC,MAAMD,KAAIP,GAAE,CAAC,GAAGQ,KAAIR,GAAE,CAAC;AACjC,YAAMS,KAAI,KAAK,MAAMD,GAAE,CAAC,IAAID,GAAE,CAAC,MAAMC,GAAE,CAAC,IAAID,GAAE,CAAC,EAAE;AACjD,eAASP,KAAI,GAAGA,KAAIM,IAAGN,MAAK;AAC1B,cAAMI,KAAI,IAAIJ,KAAIC,IAChBI,KAAI,KAAKL,KAAI,KAAKC,IAClBK,KAAI,KAAK,KAAK,IAAI,KAAK,IAAIL,IAAG,CAAC,CAAC,GAChCO,KAAI,CAACD,GAAE,CAAC,IAAIH,KAAI,KAAK,IAAIK,EAAC,GAAGF,GAAE,CAAC,IAAIH,KAAI,KAAK,IAAIK,EAAC,CAAC,GACnDC,KAAI,CAACH,GAAE,CAAC,IAAIF,KAAI,KAAK,IAAII,EAAC,GAAGF,GAAE,CAAC,IAAIF,KAAI,KAAK,IAAII,EAAC,CAAC,GACnDE,KAAI,CAACH,GAAE,CAAC,IAAIF,KAAI,KAAK,IAAIG,KAAI,KAAK,KAAK,CAAC,GAAGD,GAAE,CAAC,IAAIF,KAAI,KAAK,IAAIG,KAAI,KAAK,KAAK,CAAC,CAAC;AACjF,QAAAN,GAAE,KAAK,GAAG,KAAK,OAAO,cAAcK,GAAE,CAAC,GAAGA,GAAE,CAAC,GAAGG,GAAE,CAAC,GAAGA,GAAE,CAAC,GAAGT,EAAC,GAAG,GAAG,KAAK,OAAO,cAAcS,GAAE,CAAC,GAAGA,GAAE,CAAC,GAAGD,GAAE,CAAC,GAAGA,GAAE,CAAC,GAAGR,EAAC,CAAC;AAAA,MACzH;AAAA,IACF,CAAC,GAAGC;AAAA,EACN;AACF;AACA,IAAM,IAAI,CAAC;AACX,IAAM,IAAN,MAAQ;AAAA,EACN,YAAYH,IAAG;AACb,SAAK,OAAOA;AAAA,EACd;AAAA,EACA,OAAO;AACL,WAAO,KAAK,QAAQ,KAAK,KAAK,KAAK,KAAK,OAAO,KAAK,KAAK,OAAO,KAAK,IAAI,MAAM,KAAK,KAAK,KAAK,OAAO;AAAA,EACvG;AACF;AACA,IAAM,IAAI;AAAV,IACE,IAAI;AADN,IAEE,IAAI;AAFN,IAGE,IAAI;AAAA,EACF,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AACL;AACF,SAAS,EAAEA,IAAGC,IAAG;AACf,SAAOD,GAAE,SAASC;AACpB;AACA,SAAS,EAAED,IAAG;AACZ,QAAMC,KAAI,CAAC,GACTC,KAAI,SAAUF,IAAG;AACf,UAAMC,KAAI,IAAI,MAAM;AACpB,WAAO,OAAOD,KAAI,KAAIA,GAAE,MAAM,gBAAgB,EAAG,CAAAA,KAAIA,GAAE,OAAO,OAAO,GAAG,MAAM;AAAA,aAAWA,GAAE,MAAM,2BAA2B,EAAG,CAAAC,GAAEA,GAAE,MAAM,IAAI;AAAA,MAC3I,MAAM;AAAA,MACN,MAAM,OAAO;AAAA,IACf,GAAGD,KAAIA,GAAE,OAAO,OAAO,GAAG,MAAM;AAAA,SAAO;AACrC,UAAI,CAACA,GAAE,MAAM,6DAA6D,EAAG,QAAO,CAAC;AACrF,MAAAC,GAAEA,GAAE,MAAM,IAAI;AAAA,QACZ,MAAM;AAAA,QACN,MAAM,GAAG,WAAW,OAAO,EAAE,CAAC;AAAA,MAChC,GAAGD,KAAIA,GAAE,OAAO,OAAO,GAAG,MAAM;AAAA,IAClC;AACA,WAAOC,GAAEA,GAAE,MAAM,IAAI;AAAA,MACnB,MAAM;AAAA,MACN,MAAM;AAAA,IACR,GAAGA;AAAA,EACL,EAAED,EAAC;AACL,MAAIG,KAAI,OACNC,KAAI,GACJC,KAAIH,GAAEE,EAAC;AACT,SAAO,CAAC,EAAEC,IAAG,CAAC,KAAI;AAChB,QAAIC,KAAI;AACR,UAAMC,KAAI,CAAC;AACX,QAAI,UAAUJ,IAAG;AACf,UAAI,QAAQE,GAAE,QAAQ,QAAQA,GAAE,KAAM,QAAO,EAAE,SAASL,EAAC;AACzD,MAAAI,MAAKE,KAAI,EAAED,GAAE,IAAI,GAAGF,KAAIE,GAAE;AAAA,IAC5B,MAAO,GAAEA,IAAG,CAAC,IAAIC,KAAI,EAAEH,EAAC,KAAKC,MAAKE,KAAI,EAAED,GAAE,IAAI,GAAGF,KAAIE,GAAE;AACvD,QAAI,EAAED,KAAIE,KAAIJ,GAAE,QAAS,OAAM,IAAI,MAAM,uBAAuB;AAChE,aAASF,KAAII,IAAGJ,KAAII,KAAIE,IAAGN,MAAK;AAC9B,YAAMC,KAAIC,GAAEF,EAAC;AACb,UAAI,CAAC,EAAEC,IAAG,CAAC,EAAG,OAAM,IAAI,MAAM,yBAAyBE,KAAI,MAAMF,GAAE,IAAI;AACvE,MAAAM,GAAEA,GAAE,MAAM,IAAI,CAACN,GAAE;AAAA,IACnB;AACA,QAAI,YAAY,OAAO,EAAEE,EAAC,EAAG,OAAM,IAAI,MAAM,kBAAkBA,EAAC;AAChE;AACE,YAAMH,KAAI;AAAA,QACR,KAAKG;AAAA,QACL,MAAMI;AAAA,MACR;AACA,MAAAN,GAAE,KAAKD,EAAC,GAAGI,MAAKE,IAAGD,KAAIH,GAAEE,EAAC,GAAG,QAAQD,OAAMA,KAAI,MAAM,QAAQA,OAAMA,KAAI;AAAA,IACzE;AAAA,EACF;AACA,SAAOF;AACT;AACA,SAAS,EAAED,IAAG;AACZ,MAAIC,KAAI,GACNC,KAAI,GACJC,KAAI,GACJC,KAAI;AACN,QAAMC,KAAI,CAAC;AACX,aAAW;AAAA,IACT,KAAKC;AAAA,IACL,MAAMC;AAAA,EACR,KAAKP,GAAG,SAAQM,IAAG;AAAA,IACjB,KAAK;AACH,MAAAD,GAAE,KAAK;AAAA,QACL,KAAK;AAAA,QACL,MAAM,CAAC,GAAGE,EAAC;AAAA,MACb,CAAC,GAAG,CAACN,IAAGC,EAAC,IAAIK,IAAG,CAACJ,IAAGC,EAAC,IAAIG;AACzB;AAAA,IACF,KAAK;AACH,MAAAN,MAAKM,GAAE,CAAC,GAAGL,MAAKK,GAAE,CAAC,GAAGF,GAAE,KAAK;AAAA,QAC3B,KAAK;AAAA,QACL,MAAM,CAACJ,IAAGC,EAAC;AAAA,MACb,CAAC,GAAGC,KAAIF,IAAGG,KAAIF;AACf;AAAA,IACF,KAAK;AACH,MAAAG,GAAE,KAAK;AAAA,QACL,KAAK;AAAA,QACL,MAAM,CAAC,GAAGE,EAAC;AAAA,MACb,CAAC,GAAG,CAACN,IAAGC,EAAC,IAAIK;AACb;AAAA,IACF,KAAK;AACH,MAAAN,MAAKM,GAAE,CAAC,GAAGL,MAAKK,GAAE,CAAC,GAAGF,GAAE,KAAK;AAAA,QAC3B,KAAK;AAAA,QACL,MAAM,CAACJ,IAAGC,EAAC;AAAA,MACb,CAAC;AACD;AAAA,IACF,KAAK;AACH,MAAAG,GAAE,KAAK;AAAA,QACL,KAAK;AAAA,QACL,MAAM,CAAC,GAAGE,EAAC;AAAA,MACb,CAAC,GAAGN,KAAIM,GAAE,CAAC,GAAGL,KAAIK,GAAE,CAAC;AACrB;AAAA,IACF,KAAK,KACH;AACE,YAAMP,KAAIO,GAAE,IAAI,CAACP,IAAGG,OAAMA,KAAI,IAAIH,KAAIE,KAAIF,KAAIC,EAAC;AAC/C,MAAAI,GAAE,KAAK;AAAA,QACL,KAAK;AAAA,QACL,MAAML;AAAA,MACR,CAAC,GAAGC,KAAID,GAAE,CAAC,GAAGE,KAAIF,GAAE,CAAC;AACrB;AAAA,IACF;AAAA,IACF,KAAK;AACH,MAAAK,GAAE,KAAK;AAAA,QACL,KAAK;AAAA,QACL,MAAM,CAAC,GAAGE,EAAC;AAAA,MACb,CAAC,GAAGN,KAAIM,GAAE,CAAC,GAAGL,KAAIK,GAAE,CAAC;AACrB;AAAA,IACF,KAAK,KACH;AACE,YAAMP,KAAIO,GAAE,IAAI,CAACP,IAAGG,OAAMA,KAAI,IAAIH,KAAIE,KAAIF,KAAIC,EAAC;AAC/C,MAAAI,GAAE,KAAK;AAAA,QACL,KAAK;AAAA,QACL,MAAML;AAAA,MACR,CAAC,GAAGC,KAAID,GAAE,CAAC,GAAGE,KAAIF,GAAE,CAAC;AACrB;AAAA,IACF;AAAA,IACF,KAAK;AACH,MAAAK,GAAE,KAAK;AAAA,QACL,KAAK;AAAA,QACL,MAAM,CAAC,GAAGE,EAAC;AAAA,MACb,CAAC,GAAGN,KAAIM,GAAE,CAAC,GAAGL,KAAIK,GAAE,CAAC;AACrB;AAAA,IACF,KAAK;AACH,MAAAN,MAAKM,GAAE,CAAC,GAAGL,MAAKK,GAAE,CAAC,GAAGF,GAAE,KAAK;AAAA,QAC3B,KAAK;AAAA,QACL,MAAM,CAACE,GAAE,CAAC,GAAGA,GAAE,CAAC,GAAGA,GAAE,CAAC,GAAGA,GAAE,CAAC,GAAGA,GAAE,CAAC,GAAGN,IAAGC,EAAC;AAAA,MAC3C,CAAC;AACD;AAAA,IACF,KAAK;AACH,MAAAG,GAAE,KAAK;AAAA,QACL,KAAK;AAAA,QACL,MAAM,CAAC,GAAGE,EAAC;AAAA,MACb,CAAC,GAAGN,KAAIM,GAAE,CAAC;AACX;AAAA,IACF,KAAK;AACH,MAAAN,MAAKM,GAAE,CAAC,GAAGF,GAAE,KAAK;AAAA,QAChB,KAAK;AAAA,QACL,MAAM,CAACJ,EAAC;AAAA,MACV,CAAC;AACD;AAAA,IACF,KAAK;AACH,MAAAI,GAAE,KAAK;AAAA,QACL,KAAK;AAAA,QACL,MAAM,CAAC,GAAGE,EAAC;AAAA,MACb,CAAC,GAAGL,KAAIK,GAAE,CAAC;AACX;AAAA,IACF,KAAK;AACH,MAAAL,MAAKK,GAAE,CAAC,GAAGF,GAAE,KAAK;AAAA,QAChB,KAAK;AAAA,QACL,MAAM,CAACH,EAAC;AAAA,MACV,CAAC;AACD;AAAA,IACF,KAAK;AACH,MAAAG,GAAE,KAAK;AAAA,QACL,KAAK;AAAA,QACL,MAAM,CAAC,GAAGE,EAAC;AAAA,MACb,CAAC,GAAGN,KAAIM,GAAE,CAAC,GAAGL,KAAIK,GAAE,CAAC;AACrB;AAAA,IACF,KAAK,KACH;AACE,YAAMP,KAAIO,GAAE,IAAI,CAACP,IAAGG,OAAMA,KAAI,IAAIH,KAAIE,KAAIF,KAAIC,EAAC;AAC/C,MAAAI,GAAE,KAAK;AAAA,QACL,KAAK;AAAA,QACL,MAAML;AAAA,MACR,CAAC,GAAGC,KAAID,GAAE,CAAC,GAAGE,KAAIF,GAAE,CAAC;AACrB;AAAA,IACF;AAAA,IACF,KAAK;AACH,MAAAK,GAAE,KAAK;AAAA,QACL,KAAK;AAAA,QACL,MAAM,CAAC,GAAGE,EAAC;AAAA,MACb,CAAC,GAAGN,KAAIM,GAAE,CAAC,GAAGL,KAAIK,GAAE,CAAC;AACrB;AAAA,IACF,KAAK;AACH,MAAAN,MAAKM,GAAE,CAAC,GAAGL,MAAKK,GAAE,CAAC,GAAGF,GAAE,KAAK;AAAA,QAC3B,KAAK;AAAA,QACL,MAAM,CAACJ,IAAGC,EAAC;AAAA,MACb,CAAC;AACD;AAAA,IACF,KAAK;AAAA,IACL,KAAK;AACH,MAAAG,GAAE,KAAK;AAAA,QACL,KAAK;AAAA,QACL,MAAM,CAAC;AAAA,MACT,CAAC,GAAGJ,KAAIE,IAAGD,KAAIE;AAAA,EACnB;AACA,SAAOC;AACT;AACA,SAAS,EAAEL,IAAG;AACZ,QAAMC,KAAI,CAAC;AACX,MAAIC,KAAI,IACNC,KAAI,GACJC,KAAI,GACJC,KAAI,GACJC,KAAI,GACJC,KAAI,GACJC,KAAI;AACN,aAAW;AAAA,IACT,KAAKC;AAAA,IACL,MAAMC;AAAA,EACR,KAAKV,IAAG;AACN,YAAQS,IAAG;AAAA,MACT,KAAK;AACH,QAAAR,GAAE,KAAK;AAAA,UACL,KAAK;AAAA,UACL,MAAM,CAAC,GAAGS,EAAC;AAAA,QACb,CAAC,GAAG,CAACP,IAAGC,EAAC,IAAIM,IAAG,CAACL,IAAGC,EAAC,IAAII;AACzB;AAAA,MACF,KAAK;AACH,QAAAT,GAAE,KAAK;AAAA,UACL,KAAK;AAAA,UACL,MAAM,CAAC,GAAGS,EAAC;AAAA,QACb,CAAC,GAAGP,KAAIO,GAAE,CAAC,GAAGN,KAAIM,GAAE,CAAC,GAAGH,KAAIG,GAAE,CAAC,GAAGF,KAAIE,GAAE,CAAC;AACzC;AAAA,MACF,KAAK;AACH,QAAAT,GAAE,KAAK;AAAA,UACL,KAAK;AAAA,UACL,MAAM,CAAC,GAAGS,EAAC;AAAA,QACb,CAAC,GAAG,CAACP,IAAGC,EAAC,IAAIM;AACb;AAAA,MACF,KAAK;AACH,QAAAP,KAAIO,GAAE,CAAC,GAAGT,GAAE,KAAK;AAAA,UACf,KAAK;AAAA,UACL,MAAM,CAACE,IAAGC,EAAC;AAAA,QACb,CAAC;AACD;AAAA,MACF,KAAK;AACH,QAAAA,KAAIM,GAAE,CAAC,GAAGT,GAAE,KAAK;AAAA,UACf,KAAK;AAAA,UACL,MAAM,CAACE,IAAGC,EAAC;AAAA,QACb,CAAC;AACD;AAAA,MACF,KAAK,KACH;AACE,YAAIJ,KAAI,GACNK,KAAI;AACN,gBAAQH,MAAK,QAAQA,MAAKF,KAAIG,MAAKA,KAAII,KAAIF,KAAID,MAAKA,KAAII,QAAOR,KAAIG,IAAGE,KAAID,KAAIH,GAAE,KAAK;AAAA,UACnF,KAAK;AAAA,UACL,MAAM,CAACD,IAAGK,IAAG,GAAGK,EAAC;AAAA,QACnB,CAAC,GAAGH,KAAIG,GAAE,CAAC,GAAGF,KAAIE,GAAE,CAAC,GAAGP,KAAIO,GAAE,CAAC,GAAGN,KAAIM,GAAE,CAAC;AACzC;AAAA,MACF;AAAA,MACF,KAAK,KACH;AACE,cAAM,CAACV,IAAGK,EAAC,IAAIK;AACf,YAAIJ,KAAI,GACNG,KAAI;AACN,gBAAQP,MAAK,QAAQA,MAAKI,KAAIH,MAAKA,KAAII,KAAIE,KAAIL,MAAKA,KAAII,QAAOF,KAAIH,IAAGM,KAAIL;AAC1E,cAAMO,KAAIR,KAAI,KAAKG,KAAIH,MAAK,GAC1BS,KAAIR,KAAI,KAAKK,KAAIL,MAAK,GACtBS,KAAIb,KAAI,KAAKM,KAAIN,MAAK,GACtBc,KAAIT,KAAI,KAAKI,KAAIJ,MAAK;AACxB,QAAAJ,GAAE,KAAK;AAAA,UACL,KAAK;AAAA,UACL,MAAM,CAACU,IAAGC,IAAGC,IAAGC,IAAGd,IAAGK,EAAC;AAAA,QACzB,CAAC,GAAGE,KAAID,IAAGE,KAAIC,IAAGN,KAAIH,IAAGI,KAAIC;AAC7B;AAAA,MACF;AAAA,MACF,KAAK,KACH;AACE,cAAM,CAACL,IAAGE,IAAGG,IAAGC,EAAC,IAAII,IACnBD,KAAIN,KAAI,KAAKH,KAAIG,MAAK,GACtBQ,KAAIP,KAAI,KAAKF,KAAIE,MAAK,GACtBQ,KAAIP,KAAI,KAAKL,KAAIK,MAAK,GACtBQ,KAAIP,KAAI,KAAKJ,KAAII,MAAK;AACxB,QAAAL,GAAE,KAAK;AAAA,UACL,KAAK;AAAA,UACL,MAAM,CAACQ,IAAGE,IAAGC,IAAGC,IAAGR,IAAGC,EAAC;AAAA,QACzB,CAAC,GAAGC,KAAIP,IAAGQ,KAAIN,IAAGC,KAAIE,IAAGD,KAAIE;AAC7B;AAAA,MACF;AAAA,MACF,KAAK,KACH;AACE,cAAMN,KAAI,KAAK,IAAIU,GAAE,CAAC,CAAC,GACrBR,KAAI,KAAK,IAAIQ,GAAE,CAAC,CAAC,GACjBL,KAAIK,GAAE,CAAC,GACPJ,KAAII,GAAE,CAAC,GACPH,KAAIG,GAAE,CAAC,GACPF,KAAIE,GAAE,CAAC,GACPD,KAAIC,GAAE,CAAC;AACT,YAAI,MAAMV,MAAK,MAAME,GAAG,CAAAD,GAAE,KAAK;AAAA,UAC7B,KAAK;AAAA,UACL,MAAM,CAACE,IAAGC,IAAGI,IAAGC,IAAGD,IAAGC,EAAC;AAAA,QACzB,CAAC,GAAGN,KAAIK,IAAGJ,KAAIK;AAAA,iBAAWN,OAAMK,MAAKJ,OAAMK,IAAG;AAC5C,YAAEN,IAAGC,IAAGI,IAAGC,IAAGT,IAAGE,IAAGG,IAAGC,IAAGC,EAAC,EAAE,QAAQ,SAAUP,IAAG;AAChD,YAAAC,GAAE,KAAK;AAAA,cACL,KAAK;AAAA,cACL,MAAMD;AAAA,YACR,CAAC;AAAA,UACH,CAAC,GAAGG,KAAIK,IAAGJ,KAAIK;AAAA,QACjB;AACA;AAAA,MACF;AAAA,MACF,KAAK;AACH,QAAAR,GAAE,KAAK;AAAA,UACL,KAAK;AAAA,UACL,MAAM,CAAC;AAAA,QACT,CAAC,GAAGE,KAAIE,IAAGD,KAAIE;AAAA,IACnB;AACA,IAAAJ,KAAIO;AAAA,EACN;AACA,SAAOR;AACT;AACA,SAAS,EAAED,IAAGC,IAAGC,IAAG;AAClB,SAAO,CAACF,KAAI,KAAK,IAAIE,EAAC,IAAID,KAAI,KAAK,IAAIC,EAAC,GAAGF,KAAI,KAAK,IAAIE,EAAC,IAAID,KAAI,KAAK,IAAIC,EAAC,CAAC;AAC9E;AACA,SAAS,EAAEF,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,IAAG;AACvC,QAAMC,MAAKC,KAAIL,IAAG,KAAK,KAAKK,KAAI;AAChC,MAAIA;AACJ,MAAIC,KAAI,CAAC,GACPC,KAAI,GACJC,KAAI,GACJC,KAAI,GACJC,KAAI;AACN,MAAIP,GAAG,EAACI,IAAGC,IAAGC,IAAGC,EAAC,IAAIP;AAAA,OAAO;AAC3B,KAACT,IAAGC,EAAC,IAAI,EAAED,IAAGC,IAAG,CAACS,EAAC,GAAG,CAACR,IAAGC,EAAC,IAAI,EAAED,IAAGC,IAAG,CAACO,EAAC;AACzC,UAAMJ,MAAKN,KAAIE,MAAK,GAClBO,MAAKR,KAAIE,MAAK;AAChB,QAAIQ,KAAIL,KAAIA,MAAKF,KAAIA,MAAKK,KAAIA,MAAKJ,KAAIA;AACvC,IAAAM,KAAI,MAAMA,KAAI,KAAK,KAAKA,EAAC,GAAGP,MAAKO,IAAGN,MAAKM;AACzC,UAAMC,KAAIR,KAAIA,IACZa,KAAIZ,KAAIA,IACRa,KAAIN,KAAIK,KAAIL,KAAIH,KAAIA,KAAIQ,KAAIX,KAAIA,IAChCa,KAAIP,KAAIH,KAAIA,KAAIQ,KAAIX,KAAIA,IACxBc,MAAKb,OAAMC,KAAI,KAAK,KAAK,KAAK,KAAK,KAAK,IAAIU,KAAIC,EAAC,CAAC;AACpD,IAAAJ,KAAIK,KAAIhB,KAAIK,KAAIJ,MAAKL,KAAIE,MAAK,GAAGc,KAAII,KAAI,CAACf,KAAIC,KAAIF,MAAKH,KAAIE,MAAK,GAAGU,KAAI,KAAK,KAAK,aAAaZ,KAAIe,MAAKX,IAAG,QAAQ,CAAC,CAAC,CAAC,GAAGS,KAAI,KAAK,KAAK,aAAaX,KAAIa,MAAKX,IAAG,QAAQ,CAAC,CAAC,CAAC,GAAGL,KAAIe,OAAMF,KAAI,KAAK,KAAKA,KAAIX,KAAIa,OAAMD,KAAI,KAAK,KAAKA,KAAID,KAAI,MAAMA,KAAI,IAAI,KAAK,KAAKA,KAAIC,KAAI,MAAMA,KAAI,IAAI,KAAK,KAAKA,KAAIN,MAAKK,KAAIC,OAAMD,MAAK,IAAI,KAAK,KAAK,CAACL,MAAKM,KAAID,OAAMC,MAAK,IAAI,KAAK;AAAA,EACvW;AACA,MAAIG,KAAIH,KAAID;AACZ,MAAI,KAAK,IAAII,EAAC,IAAI,MAAM,KAAK,KAAK,KAAK;AACrC,UAAMjB,KAAIc,IACRb,KAAIC,IACJK,KAAIJ;AACN,IAAAW,KAAIN,MAAKM,KAAID,KAAIA,KAAI,MAAM,KAAK,KAAK,MAAM,IAAIA,KAAI,MAAM,KAAK,KAAK,MAAM,IAAID,KAAI,EAAEV,KAAIa,KAAIX,KAAI,KAAK,IAAIU,EAAC,GAAGX,KAAIa,KAAIX,KAAI,KAAK,IAAIS,EAAC,GAAGb,IAAGM,IAAGH,IAAGC,IAAGC,IAAG,GAAGE,IAAG,CAACM,IAAGd,IAAGe,IAAGC,EAAC,CAAC;AAAA,EACxK;AACA,EAAAC,KAAIH,KAAID;AACR,QAAMK,KAAI,KAAK,IAAIL,EAAC,GAClBM,KAAI,KAAK,IAAIN,EAAC,GACdO,KAAI,KAAK,IAAIN,EAAC,GACdO,KAAI,KAAK,IAAIP,EAAC,GACdQ,KAAI,KAAK,IAAIL,KAAI,CAAC,GAClBM,KAAI,IAAI,IAAInB,KAAIkB,IAChBE,KAAI,IAAI,IAAInB,KAAIiB,IAChBG,KAAI,CAACzB,IAAGC,EAAC,GACTyB,KAAI,CAAC1B,KAAIuB,KAAIJ,IAAGlB,KAAIuB,KAAIN,EAAC,GACzBS,KAAI,CAACzB,KAAIqB,KAAIF,IAAGlB,KAAIqB,KAAIJ,EAAC,GACzBQ,KAAI,CAAC1B,IAAGC,EAAC;AACX,MAAIuB,GAAE,CAAC,IAAI,IAAID,GAAE,CAAC,IAAIC,GAAE,CAAC,GAAGA,GAAE,CAAC,IAAI,IAAID,GAAE,CAAC,IAAIC,GAAE,CAAC,GAAGjB,GAAG,QAAO,CAACiB,IAAGC,IAAGC,EAAC,EAAE,OAAOhB,EAAC;AAChF;AACE,IAAAA,KAAI,CAACc,IAAGC,IAAGC,EAAC,EAAE,OAAOhB,EAAC;AACtB,UAAMZ,KAAI,CAAC;AACX,aAASC,KAAI,GAAGA,KAAIW,GAAE,QAAQX,MAAK,GAAG;AACpC,YAAMC,KAAI,EAAEU,GAAEX,EAAC,EAAE,CAAC,GAAGW,GAAEX,EAAC,EAAE,CAAC,GAAGS,EAAC,GAC7BP,KAAI,EAAES,GAAEX,KAAI,CAAC,EAAE,CAAC,GAAGW,GAAEX,KAAI,CAAC,EAAE,CAAC,GAAGS,EAAC,GACjCN,KAAI,EAAEQ,GAAEX,KAAI,CAAC,EAAE,CAAC,GAAGW,GAAEX,KAAI,CAAC,EAAE,CAAC,GAAGS,EAAC;AACnC,MAAAV,GAAE,KAAK,CAACE,GAAE,CAAC,GAAGA,GAAE,CAAC,GAAGC,GAAE,CAAC,GAAGA,GAAE,CAAC,GAAGC,GAAE,CAAC,GAAGA,GAAE,CAAC,CAAC,CAAC;AAAA,IAC7C;AACA,WAAOJ;AAAA,EACT;AACF;AACA,IAAM,IAAI;AAAA,EACR,YAAY,SAAUA,IAAGC,IAAG;AAC1B,WAAO,EAAED,IAAGC,EAAC;AAAA,EACf;AAAA,EACA,qBAAqB,SAAUD,IAAGC,IAAGC,IAAG;AACtC,WAAO,EAAEF,IAAGC,IAAGC,EAAC;AAAA,EAClB;AAAA,EACA,SAAS,SAAUF,IAAGC,IAAGC,IAAGC,IAAGC,IAAG;AAChC,UAAMC,KAAI,EAAEH,IAAGC,IAAGC,EAAC;AACnB,WAAO,EAAEJ,IAAGC,IAAGG,IAAGC,EAAC,EAAE;AAAA,EACvB;AAAA,EACA,eAAe,SAAUL,IAAGC,IAAGC,IAAGC,IAAGC,IAAG;AACtC,WAAO,EAAEJ,IAAGC,IAAGC,IAAGC,IAAGC,IAAG,IAAE;AAAA,EAC5B;AACF;AACA,SAAS,EAAEJ,IAAGC,IAAGC,IAAGC,IAAGC,IAAG;AACxB,SAAO;AAAA,IACL,MAAM;AAAA,IACN,KAAK,EAAEJ,IAAGC,IAAGC,IAAGC,IAAGC,EAAC;AAAA,EACtB;AACF;AACA,SAAS,EAAEJ,IAAGC,IAAGC,IAAG;AAClB,QAAMC,MAAKH,MAAK,CAAC,GAAG;AACpB,MAAIG,KAAI,GAAG;AACT,UAAMC,KAAI,CAAC;AACX,aAASH,KAAI,GAAGA,KAAIE,KAAI,GAAGF,KAAK,CAAAG,GAAE,KAAK,GAAG,EAAEJ,GAAEC,EAAC,EAAE,CAAC,GAAGD,GAAEC,EAAC,EAAE,CAAC,GAAGD,GAAEC,KAAI,CAAC,EAAE,CAAC,GAAGD,GAAEC,KAAI,CAAC,EAAE,CAAC,GAAGC,EAAC,CAAC;AAC1F,WAAOD,MAAKG,GAAE,KAAK,GAAG,EAAEJ,GAAEG,KAAI,CAAC,EAAE,CAAC,GAAGH,GAAEG,KAAI,CAAC,EAAE,CAAC,GAAGH,GAAE,CAAC,EAAE,CAAC,GAAGA,GAAE,CAAC,EAAE,CAAC,GAAGE,EAAC,CAAC,GAAG;AAAA,MACvE,MAAM;AAAA,MACN,KAAKE;AAAA,IACP;AAAA,EACF;AACA,SAAO,MAAMD,KAAI,EAAEH,GAAE,CAAC,EAAE,CAAC,GAAGA,GAAE,CAAC,EAAE,CAAC,GAAGA,GAAE,CAAC,EAAE,CAAC,GAAGA,GAAE,CAAC,EAAE,CAAC,GAAGE,EAAC,IAAI;AAAA,IAC1D,MAAM;AAAA,IACN,KAAK,CAAC;AAAA,EACR;AACF;AACA,SAAS,EAAEF,IAAGC,IAAGC,IAAGC,IAAGC,IAAG;AACxB,SAAO,SAAUJ,IAAGC,IAAG;AACrB,WAAO,EAAED,IAAG,MAAIC,EAAC;AAAA,EACnB,EAAE,CAAC,CAACD,IAAGC,EAAC,GAAG,CAACD,KAAIE,IAAGD,EAAC,GAAG,CAACD,KAAIE,IAAGD,KAAIE,EAAC,GAAG,CAACH,IAAGC,KAAIE,EAAC,CAAC,GAAGC,EAAC;AACvD;AACA,SAAS,EAAEJ,IAAGC,IAAG;AACf,MAAID,GAAE,QAAQ;AACZ,UAAME,KAAI,YAAY,OAAOF,GAAE,CAAC,EAAE,CAAC,IAAI,CAACA,EAAC,IAAIA,IAC3CG,KAAI,EAAED,GAAE,CAAC,GAAG,KAAK,IAAI,MAAKD,GAAE,YAAYA,EAAC,GACzCG,KAAIH,GAAE,qBAAqB,CAAC,IAAI,EAAEC,GAAE,CAAC,GAAG,OAAO,IAAI,OAAMD,GAAE,YAAY,EAAEA,EAAC,CAAC;AAC7E,aAASD,KAAI,GAAGA,KAAIE,GAAE,QAAQF,MAAK;AACjC,YAAMK,KAAIH,GAAEF,EAAC;AACb,UAAIK,GAAE,QAAQ;AACZ,cAAML,KAAI,EAAEK,IAAG,KAAK,IAAI,MAAKJ,GAAE,YAAYA,EAAC,GAC1CC,KAAID,GAAE,qBAAqB,CAAC,IAAI,EAAEI,IAAG,OAAO,IAAI,OAAMJ,GAAE,YAAY,EAAEA,EAAC,CAAC;AAC1E,mBAAWA,MAAKD,GAAG,YAAWC,GAAE,MAAME,GAAE,KAAKF,EAAC;AAC9C,mBAAWD,MAAKE,GAAG,YAAWF,GAAE,MAAMI,GAAE,KAAKJ,EAAC;AAAA,MAChD;AAAA,IACF;AACA,WAAO;AAAA,MACL,MAAM;AAAA,MACN,KAAKG,GAAE,OAAOC,EAAC;AAAA,IACjB;AAAA,EACF;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN,KAAK,CAAC;AAAA,EACR;AACF;AACA,SAAS,EAAEJ,IAAGC,IAAGC,IAAG;AAClB,QAAMC,KAAI,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,KAAK,IAAIH,KAAI,GAAG,CAAC,IAAI,KAAK,IAAIC,KAAI,GAAG,CAAC,KAAK,CAAC,CAAC,GACxFG,KAAI,KAAK,KAAK,KAAK,IAAIF,GAAE,gBAAgBA,GAAE,iBAAiB,KAAK,KAAK,GAAG,IAAIC,EAAC,CAAC,GAC/EE,KAAI,IAAI,KAAK,KAAKD;AACpB,MAAIE,KAAI,KAAK,IAAIN,KAAI,CAAC,GACpBO,KAAI,KAAK,IAAIN,KAAI,CAAC;AACpB,QAAMO,KAAI,IAAIN,GAAE;AAChB,SAAOI,MAAK,EAAEA,KAAIE,IAAGN,EAAC,GAAGK,MAAK,EAAEA,KAAIC,IAAGN,EAAC,GAAG;AAAA,IACzC,WAAWG;AAAA,IACX,IAAIC;AAAA,IACJ,IAAIC;AAAA,EACN;AACF;AACA,SAAS,EAAEP,IAAGC,IAAGC,IAAGC,IAAG;AACrB,QAAM,CAACC,IAAGC,EAAC,IAAI,EAAEF,GAAE,WAAWH,IAAGC,IAAGE,GAAE,IAAIA,GAAE,IAAI,GAAGA,GAAE,YAAY,EAAE,KAAI,EAAE,KAAI,GAAGD,EAAC,GAAGA,EAAC,GAAGA,EAAC;AACzF,MAAII,KAAI,EAAEF,IAAG,MAAMF,EAAC;AACpB,MAAI,CAACA,GAAE,sBAAsB,MAAMA,GAAE,WAAW;AAC9C,UAAM,CAACE,EAAC,IAAI,EAAED,GAAE,WAAWH,IAAGC,IAAGE,GAAE,IAAIA,GAAE,IAAI,KAAK,GAAGD,EAAC,GACpDG,KAAI,EAAED,IAAG,MAAMF,EAAC;AAClB,IAAAI,KAAIA,GAAE,OAAOD,EAAC;AAAA,EAChB;AACA,SAAO;AAAA,IACL,iBAAiBA;AAAA,IACjB,OAAO;AAAA,MACL,MAAM;AAAA,MACN,KAAKC;AAAA,IACP;AAAA,EACF;AACF;AACA,SAAS,EAAEN,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,IAAG;AACpC,QAAMC,KAAIT,IACRU,KAAIT;AACN,MAAIU,KAAI,KAAK,IAAIT,KAAI,CAAC,GACpBU,KAAI,KAAK,IAAIT,KAAI,CAAC;AACpB,EAAAQ,MAAK,EAAE,OAAMA,IAAGH,EAAC,GAAGI,MAAK,EAAE,OAAMA,IAAGJ,EAAC;AACrC,MAAIK,KAAIT,IACNU,KAAIT;AACN,SAAOQ,KAAI,IAAI,CAAAA,MAAK,IAAI,KAAK,IAAIC,MAAK,IAAI,KAAK;AAC/C,EAAAA,KAAID,KAAI,IAAI,KAAK,OAAOA,KAAI,GAAGC,KAAI,IAAI,KAAK;AAC5C,QAAMC,KAAI,IAAI,KAAK,KAAKP,GAAE,gBACxBQ,KAAI,KAAK,IAAID,KAAI,IAAID,KAAID,MAAK,CAAC,GAC/BI,KAAI,EAAED,IAAGP,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,IAAG,GAAGN,EAAC;AACjC,MAAI,CAACA,GAAE,oBAAoB;AACzB,UAAMR,KAAI,EAAEgB,IAAGP,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,IAAG,KAAKN,EAAC;AACvC,IAAAS,GAAE,KAAK,GAAGjB,EAAC;AAAA,EACb;AACA,SAAOM,OAAMC,KAAIU,GAAE,KAAK,GAAG,EAAER,IAAGC,IAAGD,KAAIE,KAAI,KAAK,IAAIE,EAAC,GAAGH,KAAIE,KAAI,KAAK,IAAIC,EAAC,GAAGL,EAAC,GAAG,GAAG,EAAEC,IAAGC,IAAGD,KAAIE,KAAI,KAAK,IAAIG,EAAC,GAAGJ,KAAIE,KAAI,KAAK,IAAIE,EAAC,GAAGN,EAAC,CAAC,IAAIS,GAAE,KAAK;AAAA,IACjJ,IAAI;AAAA,IACJ,MAAM,CAACR,IAAGC,EAAC;AAAA,EACb,GAAG;AAAA,IACD,IAAI;AAAA,IACJ,MAAM,CAACD,KAAIE,KAAI,KAAK,IAAIE,EAAC,GAAGH,KAAIE,KAAI,KAAK,IAAIC,EAAC,CAAC;AAAA,EACjD,CAAC,IAAI;AAAA,IACH,MAAM;AAAA,IACN,KAAKI;AAAA,EACP;AACF;AACA,SAAS,EAAEjB,IAAGC,IAAG;AACf,QAAMC,KAAI,EAAE,EAAE,EAAEF,EAAC,CAAC,CAAC,GACjBG,KAAI,CAAC;AACP,MAAIC,KAAI,CAAC,GAAG,CAAC,GACXC,KAAI,CAAC,GAAG,CAAC;AACX,aAAW;AAAA,IACT,KAAKL;AAAA,IACL,MAAMM;AAAA,EACR,KAAKJ,GAAG,SAAQF,IAAG;AAAA,IACjB,KAAK;AACH,MAAAK,KAAI,CAACC,GAAE,CAAC,GAAGA,GAAE,CAAC,CAAC,GAAGF,KAAI,CAACE,GAAE,CAAC,GAAGA,GAAE,CAAC,CAAC;AACjC;AAAA,IACF,KAAK;AACH,MAAAH,GAAE,KAAK,GAAG,EAAEE,GAAE,CAAC,GAAGA,GAAE,CAAC,GAAGC,GAAE,CAAC,GAAGA,GAAE,CAAC,GAAGL,EAAC,CAAC,GAAGI,KAAI,CAACC,GAAE,CAAC,GAAGA,GAAE,CAAC,CAAC;AACxD;AAAA,IACF,KAAK,KACH;AACE,YAAM,CAACN,IAAGE,IAAGE,IAAGG,IAAGC,IAAGC,EAAC,IAAIH;AAC3B,MAAAH,GAAE,KAAK,GAAG,EAAEH,IAAGE,IAAGE,IAAGG,IAAGC,IAAGC,IAAGJ,IAAGJ,EAAC,CAAC,GAAGI,KAAI,CAACG,IAAGC,EAAC;AAC/C;AAAA,IACF;AAAA,IACF,KAAK;AACH,MAAAN,GAAE,KAAK,GAAG,EAAEE,GAAE,CAAC,GAAGA,GAAE,CAAC,GAAGD,GAAE,CAAC,GAAGA,GAAE,CAAC,GAAGH,EAAC,CAAC,GAAGI,KAAI,CAACD,GAAE,CAAC,GAAGA,GAAE,CAAC,CAAC;AAAA,EAC5D;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN,KAAKD;AAAA,EACP;AACF;AACA,SAAS,EAAEH,IAAGC,IAAG;AACf,QAAMC,KAAI,CAAC;AACX,aAAWC,MAAKH,GAAG,KAAIG,GAAE,QAAQ;AAC/B,UAAMH,KAAIC,GAAE,uBAAuB,GACjCG,KAAID,GAAE;AACR,QAAIC,KAAI,GAAG;AACT,MAAAF,GAAE,KAAK;AAAA,QACL,IAAI;AAAA,QACJ,MAAM,CAACC,GAAE,CAAC,EAAE,CAAC,IAAI,EAAEH,IAAGC,EAAC,GAAGE,GAAE,CAAC,EAAE,CAAC,IAAI,EAAEH,IAAGC,EAAC,CAAC;AAAA,MAC7C,CAAC;AACD,eAASI,KAAI,GAAGA,KAAID,IAAGC,KAAK,CAAAH,GAAE,KAAK;AAAA,QACjC,IAAI;AAAA,QACJ,MAAM,CAACC,GAAEE,EAAC,EAAE,CAAC,IAAI,EAAEL,IAAGC,EAAC,GAAGE,GAAEE,EAAC,EAAE,CAAC,IAAI,EAAEL,IAAGC,EAAC,CAAC;AAAA,MAC7C,CAAC;AAAA,IACH;AAAA,EACF;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN,KAAKC;AAAA,EACP;AACF;AACA,SAAS,EAAEF,IAAGC,IAAG;AACf,SAAO,SAAUD,IAAGC,IAAG;AACrB,QAAIC,KAAIF,GAAE,aAAa;AACvB,QAAI,CAAC,EAAEE,EAAC,EAAG,SAAQA,IAAG;AAAA,MACpB,KAAK;AACH,UAAEA,EAAC,MAAM,EAAEA,EAAC,IAAI,IAAI,EAAED,EAAC;AACvB;AAAA,MACF,KAAK;AACH,UAAEC,EAAC,MAAM,EAAEA,EAAC,IAAI,IAAI,EAAED,EAAC;AACvB;AAAA,MACF,KAAK;AACH,UAAEC,EAAC,MAAM,EAAEA,EAAC,IAAI,IAAI,EAAED,EAAC;AACvB;AAAA,MACF,KAAK;AACH,UAAEC,EAAC,MAAM,EAAEA,EAAC,IAAI,IAAI,EAAED,EAAC;AACvB;AAAA,MACF,KAAK;AACH,UAAEC,EAAC,MAAM,EAAEA,EAAC,IAAI,IAAI,EAAED,EAAC;AACvB;AAAA,MACF;AACE,QAAAC,KAAI,WAAW,EAAEA,EAAC,MAAM,EAAEA,EAAC,IAAI,IAAI,EAAED,EAAC;AAAA,IAC1C;AACA,WAAO,EAAEC,EAAC;AAAA,EACZ,EAAED,IAAG,CAAC,EAAE,aAAaD,IAAGC,EAAC;AAC3B;AACA,SAAS,EAAED,IAAG;AACZ,QAAMC,KAAI,OAAO,OAAO,CAAC,GAAGD,EAAC;AAC7B,SAAOC,GAAE,aAAa,QAAQD,GAAE,SAASC,GAAE,OAAOD,GAAE,OAAO,IAAIC;AACjE;AACA,SAAS,EAAED,IAAG;AACZ,SAAOA,GAAE,eAAeA,GAAE,aAAa,IAAI,EAAEA,GAAE,QAAQ,CAAC,IAAIA,GAAE,WAAW,KAAK;AAChF;AACA,SAAS,EAAEA,IAAGC,IAAGC,IAAGC,KAAI,GAAG;AACzB,SAAOD,GAAE,YAAYC,MAAK,EAAED,EAAC,KAAKD,KAAID,MAAKA;AAC7C;AACA,SAAS,EAAEA,IAAGC,IAAGC,KAAI,GAAG;AACtB,SAAO,EAAE,CAACF,IAAGA,IAAGC,IAAGC,EAAC;AACtB;AACA,SAAS,EAAEF,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,KAAI,OAAI;AAChC,QAAMC,KAAID,KAAID,GAAE,yBAAyBA,GAAE,oBACzCG,KAAI,EAAEP,IAAGC,IAAGC,IAAGC,IAAGC,IAAG,MAAI,KAAE;AAC7B,MAAIE,GAAG,QAAOC;AACd,QAAMC,KAAI,EAAER,IAAGC,IAAGC,IAAGC,IAAGC,IAAG,MAAI,IAAE;AACjC,SAAOG,GAAE,OAAOC,EAAC;AACnB;AACA,SAAS,EAAER,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,IAAG;AAC9B,QAAMC,KAAI,KAAK,IAAIP,KAAIE,IAAG,CAAC,IAAI,KAAK,IAAID,KAAIE,IAAG,CAAC,GAC9CK,KAAI,KAAK,KAAKD,EAAC;AACjB,MAAIE,KAAI;AACR,EAAAA,KAAID,KAAI,MAAM,IAAIA,KAAI,MAAM,MAAK,YAAYA,KAAI;AACjD,MAAIE,KAAIN,GAAE,uBAAuB;AACjC,EAAAM,KAAIA,KAAI,MAAMH,OAAMG,KAAIF,KAAI;AAC5B,QAAMG,KAAID,KAAI,GACZE,KAAI,MAAK,MAAK,EAAER,EAAC;AACnB,MAAIS,KAAIT,GAAE,SAASA,GAAE,uBAAuBD,KAAIF,MAAK,KACnDa,KAAIV,GAAE,SAASA,GAAE,uBAAuBJ,KAAIE,MAAK;AACnD,EAAAW,KAAI,EAAEA,IAAGT,IAAGK,EAAC,GAAGK,KAAI,EAAEA,IAAGV,IAAGK,EAAC;AAC7B,QAAMM,KAAI,CAAC,GACTC,KAAI,MAAM,EAAEL,IAAGP,IAAGK,EAAC,GACnBQ,KAAI,MAAM,EAAEP,IAAGN,IAAGK,EAAC,GACnBS,KAAId,GAAE;AACR,SAAOC,OAAMC,KAAIS,GAAE,KAAK;AAAA,IACtB,IAAI;AAAA,IACJ,MAAM,CAACf,MAAKkB,KAAI,IAAIF,GAAE,IAAIf,MAAKiB,KAAI,IAAIF,GAAE,EAAE;AAAA,EAC7C,CAAC,IAAID,GAAE,KAAK;AAAA,IACV,IAAI;AAAA,IACJ,MAAM,CAACf,MAAKkB,KAAI,IAAI,EAAER,IAAGN,IAAGK,EAAC,IAAIR,MAAKiB,KAAI,IAAI,EAAER,IAAGN,IAAGK,EAAC,EAAE;AAAA,EAC3D,CAAC,IAAIH,KAAIS,GAAE,KAAK;AAAA,IACd,IAAI;AAAA,IACJ,MAAM,CAACF,KAAIb,MAAKE,KAAIF,MAAKY,KAAII,GAAE,GAAGF,KAAIb,MAAKE,KAAIF,MAAKW,KAAII,GAAE,GAAGH,KAAIb,KAAI,KAAKE,KAAIF,MAAKY,KAAII,GAAE,GAAGF,KAAIb,KAAI,KAAKE,KAAIF,MAAKW,KAAII,GAAE,GAAGd,MAAKgB,KAAI,IAAIF,GAAE,IAAIb,MAAKe,KAAI,IAAIF,GAAE,EAAE;AAAA,EACjK,CAAC,IAAID,GAAE,KAAK;AAAA,IACV,IAAI;AAAA,IACJ,MAAM,CAACF,KAAIb,MAAKE,KAAIF,MAAKY,KAAIK,GAAE,GAAGH,KAAIb,MAAKE,KAAIF,MAAKW,KAAIK,GAAE,GAAGJ,KAAIb,KAAI,KAAKE,KAAIF,MAAKY,KAAIK,GAAE,GAAGH,KAAIb,KAAI,KAAKE,KAAIF,MAAKW,KAAIK,GAAE,GAAGf,MAAKgB,KAAI,IAAID,GAAE,IAAId,MAAKe,KAAI,IAAID,GAAE,EAAE;AAAA,EACjK,CAAC,GAAGF;AACN;AACA,SAAS,EAAEf,IAAGC,IAAGC,IAAG;AAClB,MAAI,CAACF,GAAE,OAAQ,QAAO,CAAC;AACvB,QAAMG,KAAI,CAAC;AACX,EAAAA,GAAE,KAAK,CAACH,GAAE,CAAC,EAAE,CAAC,IAAI,EAAEC,IAAGC,EAAC,GAAGF,GAAE,CAAC,EAAE,CAAC,IAAI,EAAEC,IAAGC,EAAC,CAAC,CAAC,GAAGC,GAAE,KAAK,CAACH,GAAE,CAAC,EAAE,CAAC,IAAI,EAAEC,IAAGC,EAAC,GAAGF,GAAE,CAAC,EAAE,CAAC,IAAI,EAAEC,IAAGC,EAAC,CAAC,CAAC;AAC7F,WAASE,KAAI,GAAGA,KAAIJ,GAAE,QAAQI,KAAK,CAAAD,GAAE,KAAK,CAACH,GAAEI,EAAC,EAAE,CAAC,IAAI,EAAEH,IAAGC,EAAC,GAAGF,GAAEI,EAAC,EAAE,CAAC,IAAI,EAAEH,IAAGC,EAAC,CAAC,CAAC,GAAGE,OAAMJ,GAAE,SAAS,KAAKG,GAAE,KAAK,CAACH,GAAEI,EAAC,EAAE,CAAC,IAAI,EAAEH,IAAGC,EAAC,GAAGF,GAAEI,EAAC,EAAE,CAAC,IAAI,EAAEH,IAAGC,EAAC,CAAC,CAAC;AACtJ,SAAO,EAAEC,IAAG,MAAMD,EAAC;AACrB;AACA,SAAS,EAAEF,IAAGC,IAAGC,IAAG;AAClB,QAAMC,KAAIH,GAAE,QACVI,KAAI,CAAC;AACP,MAAID,KAAI,GAAG;AACT,UAAME,KAAI,CAAC,GACTC,KAAI,IAAIJ,GAAE;AACZ,IAAAE,GAAE,KAAK;AAAA,MACL,IAAI;AAAA,MACJ,MAAM,CAACJ,GAAE,CAAC,EAAE,CAAC,GAAGA,GAAE,CAAC,EAAE,CAAC,CAAC;AAAA,IACzB,CAAC;AACD,aAASC,KAAI,GAAGA,KAAI,IAAIE,IAAGF,MAAK;AAC9B,YAAMC,KAAIF,GAAEC,EAAC;AACb,MAAAI,GAAE,CAAC,IAAI,CAACH,GAAE,CAAC,GAAGA,GAAE,CAAC,CAAC,GAAGG,GAAE,CAAC,IAAI,CAACH,GAAE,CAAC,KAAKI,KAAIN,GAAEC,KAAI,CAAC,EAAE,CAAC,IAAIK,KAAIN,GAAEC,KAAI,CAAC,EAAE,CAAC,KAAK,GAAGC,GAAE,CAAC,KAAKI,KAAIN,GAAEC,KAAI,CAAC,EAAE,CAAC,IAAIK,KAAIN,GAAEC,KAAI,CAAC,EAAE,CAAC,KAAK,CAAC,GAAGI,GAAE,CAAC,IAAI,CAACL,GAAEC,KAAI,CAAC,EAAE,CAAC,KAAKK,KAAIN,GAAEC,EAAC,EAAE,CAAC,IAAIK,KAAIN,GAAEC,KAAI,CAAC,EAAE,CAAC,KAAK,GAAGD,GAAEC,KAAI,CAAC,EAAE,CAAC,KAAKK,KAAIN,GAAEC,EAAC,EAAE,CAAC,IAAIK,KAAIN,GAAEC,KAAI,CAAC,EAAE,CAAC,KAAK,CAAC,GAAGI,GAAE,CAAC,IAAI,CAACL,GAAEC,KAAI,CAAC,EAAE,CAAC,GAAGD,GAAEC,KAAI,CAAC,EAAE,CAAC,CAAC,GAAGG,GAAE,KAAK;AAAA,QACrR,IAAI;AAAA,QACJ,MAAM,CAACC,GAAE,CAAC,EAAE,CAAC,GAAGA,GAAE,CAAC,EAAE,CAAC,GAAGA,GAAE,CAAC,EAAE,CAAC,GAAGA,GAAE,CAAC,EAAE,CAAC,GAAGA,GAAE,CAAC,EAAE,CAAC,GAAGA,GAAE,CAAC,EAAE,CAAC,CAAC;AAAA,MAC7D,CAAC;AAAA,IACH;AACA,QAAIJ,MAAK,MAAMA,GAAE,QAAQ;AACvB,YAAMD,KAAIE,GAAE;AACZ,MAAAE,GAAE,KAAK;AAAA,QACL,IAAI;AAAA,QACJ,MAAM,CAACH,GAAE,CAAC,IAAI,EAAED,IAAGE,EAAC,GAAGD,GAAE,CAAC,IAAI,EAAED,IAAGE,EAAC,CAAC;AAAA,MACvC,CAAC;AAAA,IACH;AAAA,EACF,MAAO,OAAMC,MAAKC,GAAE,KAAK;AAAA,IACvB,IAAI;AAAA,IACJ,MAAM,CAACJ,GAAE,CAAC,EAAE,CAAC,GAAGA,GAAE,CAAC,EAAE,CAAC,CAAC;AAAA,EACzB,CAAC,GAAGI,GAAE,KAAK;AAAA,IACT,IAAI;AAAA,IACJ,MAAM,CAACJ,GAAE,CAAC,EAAE,CAAC,GAAGA,GAAE,CAAC,EAAE,CAAC,GAAGA,GAAE,CAAC,EAAE,CAAC,GAAGA,GAAE,CAAC,EAAE,CAAC,GAAGA,GAAE,CAAC,EAAE,CAAC,GAAGA,GAAE,CAAC,EAAE,CAAC,CAAC;AAAA,EAC7D,CAAC,KAAK,MAAMG,MAAKC,GAAE,KAAK,GAAG,EAAEJ,GAAE,CAAC,EAAE,CAAC,GAAGA,GAAE,CAAC,EAAE,CAAC,GAAGA,GAAE,CAAC,EAAE,CAAC,GAAGA,GAAE,CAAC,EAAE,CAAC,GAAGE,IAAG,MAAI,IAAE,CAAC;AAC3E,SAAOE;AACT;AACA,SAAS,EAAEJ,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,IAAG;AACjC,QAAMC,KAAI,CAAC,GACTC,KAAI,CAAC;AACP,MAAI,MAAMF,GAAE,WAAW;AACrB,IAAAP,MAAK,GAAGS,GAAE,KAAK,CAACR,KAAIE,KAAI,KAAK,IAAI,CAACH,EAAC,GAAGE,KAAIE,KAAI,KAAK,IAAI,CAACJ,EAAC,CAAC,CAAC;AAC3D,aAASK,KAAI,GAAGA,MAAK,IAAI,KAAK,IAAIA,MAAKL,IAAG;AACxC,YAAMA,KAAI,CAACC,KAAIE,KAAI,KAAK,IAAIE,EAAC,GAAGH,KAAIE,KAAI,KAAK,IAAIC,EAAC,CAAC;AACnD,MAAAG,GAAE,KAAKR,EAAC,GAAGS,GAAE,KAAKT,EAAC;AAAA,IACrB;AACA,IAAAS,GAAE,KAAK,CAACR,KAAIE,KAAI,KAAK,IAAI,CAAC,GAAGD,KAAIE,KAAI,KAAK,IAAI,CAAC,CAAC,CAAC,GAAGK,GAAE,KAAK,CAACR,KAAIE,KAAI,KAAK,IAAIH,EAAC,GAAGE,KAAIE,KAAI,KAAK,IAAIJ,EAAC,CAAC,CAAC;AAAA,EACvG,OAAO;AACL,UAAMU,KAAI,EAAE,KAAIH,EAAC,IAAI,KAAK,KAAK;AAC/B,IAAAE,GAAE,KAAK,CAAC,EAAEJ,IAAGE,EAAC,IAAIN,KAAI,MAAKE,KAAI,KAAK,IAAIO,KAAIV,EAAC,GAAG,EAAEK,IAAGE,EAAC,IAAIL,KAAI,MAAKE,KAAI,KAAK,IAAIM,KAAIV,EAAC,CAAC,CAAC;AACvF,UAAMW,KAAI,IAAI,KAAK,KAAKD,KAAI;AAC5B,aAASJ,KAAII,IAAGJ,KAAIK,IAAGL,MAAKN,IAAG;AAC7B,YAAMA,KAAI,CAAC,EAAEK,IAAGE,EAAC,IAAIN,KAAIE,KAAI,KAAK,IAAIG,EAAC,GAAG,EAAED,IAAGE,EAAC,IAAIL,KAAIE,KAAI,KAAK,IAAIE,EAAC,CAAC;AACvE,MAAAE,GAAE,KAAKR,EAAC,GAAGS,GAAE,KAAKT,EAAC;AAAA,IACrB;AACA,IAAAS,GAAE,KAAK,CAAC,EAAEJ,IAAGE,EAAC,IAAIN,KAAIE,KAAI,KAAK,IAAIO,KAAI,IAAI,KAAK,KAAK,MAAKJ,EAAC,GAAG,EAAED,IAAGE,EAAC,IAAIL,KAAIE,KAAI,KAAK,IAAIM,KAAI,IAAI,KAAK,KAAK,MAAKJ,EAAC,CAAC,CAAC,GAAGG,GAAE,KAAK,CAAC,EAAEJ,IAAGE,EAAC,IAAIN,KAAI,OAAME,KAAI,KAAK,IAAIO,KAAIJ,EAAC,GAAG,EAAED,IAAGE,EAAC,IAAIL,KAAI,OAAME,KAAI,KAAK,IAAIM,KAAIJ,EAAC,CAAC,CAAC,GAAGG,GAAE,KAAK,CAAC,EAAEJ,IAAGE,EAAC,IAAIN,KAAI,MAAKE,KAAI,KAAK,IAAIO,KAAI,MAAKJ,EAAC,GAAG,EAAED,IAAGE,EAAC,IAAIL,KAAI,MAAKE,KAAI,KAAK,IAAIM,KAAI,MAAKJ,EAAC,CAAC,CAAC;AAAA,EACrT;AACA,SAAO,CAACG,IAAGD,EAAC;AACd;AACA,SAAS,EAAER,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,IAAG;AACpC,QAAMC,KAAIJ,KAAI,EAAE,KAAIG,EAAC,GACnBE,KAAI,CAAC;AACP,EAAAA,GAAE,KAAK,CAAC,EAAEH,IAAGC,EAAC,IAAIP,KAAI,MAAKE,KAAI,KAAK,IAAIM,KAAIT,EAAC,GAAG,EAAEO,IAAGC,EAAC,IAAIN,KAAI,MAAKE,KAAI,KAAK,IAAIK,KAAIT,EAAC,CAAC,CAAC;AACvF,WAASK,KAAII,IAAGJ,MAAKC,IAAGD,MAAKL,GAAG,CAAAU,GAAE,KAAK,CAAC,EAAEH,IAAGC,EAAC,IAAIP,KAAIE,KAAI,KAAK,IAAIE,EAAC,GAAG,EAAEE,IAAGC,EAAC,IAAIN,KAAIE,KAAI,KAAK,IAAIC,EAAC,CAAC,CAAC;AACrG,SAAOK,GAAE,KAAK,CAACT,KAAIE,KAAI,KAAK,IAAIG,EAAC,GAAGJ,KAAIE,KAAI,KAAK,IAAIE,EAAC,CAAC,CAAC,GAAGI,GAAE,KAAK,CAACT,KAAIE,KAAI,KAAK,IAAIG,EAAC,GAAGJ,KAAIE,KAAI,KAAK,IAAIE,EAAC,CAAC,CAAC,GAAG,EAAEI,IAAG,MAAMF,EAAC;AAC7H;AACA,SAAS,EAAER,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,IAAG;AACjC,QAAMC,KAAI,CAAC,GACTC,KAAI,CAACF,GAAE,uBAAuB,IAAIA,GAAE,uBAAuB,KAAK,GAAE;AACpE,MAAIG,KAAI,CAAC,GAAG,CAAC;AACb,QAAMC,KAAIJ,GAAE,qBAAqB,IAAI,GACnCK,KAAIL,GAAE;AACR,WAASM,KAAI,GAAGA,KAAIF,IAAGE,KAAK,OAAMA,KAAIL,GAAE,KAAK;AAAA,IAC3C,IAAI;AAAA,IACJ,MAAM,CAACF,GAAE,CAAC,GAAGA,GAAE,CAAC,CAAC;AAAA,EACnB,CAAC,IAAIE,GAAE,KAAK;AAAA,IACV,IAAI;AAAA,IACJ,MAAM,CAACF,GAAE,CAAC,KAAKM,KAAI,IAAI,EAAEH,GAAE,CAAC,GAAGF,EAAC,IAAID,GAAE,CAAC,KAAKM,KAAI,IAAI,EAAEH,GAAE,CAAC,GAAGF,EAAC,EAAE;AAAA,EACjE,CAAC,GAAGG,KAAIE,KAAI,CAACR,IAAGC,EAAC,IAAI,CAACD,KAAI,EAAEK,GAAEI,EAAC,GAAGN,EAAC,GAAGF,KAAI,EAAEI,GAAEI,EAAC,GAAGN,EAAC,CAAC,GAAGC,GAAE,KAAK;AAAA,IAC5D,IAAI;AAAA,IACJ,MAAM,CAACR,KAAI,EAAES,GAAEI,EAAC,GAAGN,EAAC,GAAGN,KAAI,EAAEQ,GAAEI,EAAC,GAAGN,EAAC,GAAGL,KAAI,EAAEO,GAAEI,EAAC,GAAGN,EAAC,GAAGJ,KAAI,EAAEM,GAAEI,EAAC,GAAGN,EAAC,GAAGG,GAAE,CAAC,GAAGA,GAAE,CAAC,CAAC;AAAA,EACnF,CAAC;AACD,SAAOF;AACT;AACA,SAAS,EAAER,IAAG;AACZ,SAAO,CAAC,GAAGA,EAAC;AACd;AACA,SAAS,EAAEA,IAAGC,KAAI,GAAG;AACnB,QAAMC,KAAIF,GAAE;AACZ,MAAIE,KAAI,EAAG,OAAM,IAAI,MAAM,0CAA0C;AACrE,QAAMC,KAAI,CAAC;AACX,MAAI,MAAMD,GAAG,CAAAC,GAAE,KAAK,EAAEH,GAAE,CAAC,CAAC,GAAG,EAAEA,GAAE,CAAC,CAAC,GAAG,EAAEA,GAAE,CAAC,CAAC,GAAG,EAAEA,GAAE,CAAC,CAAC,CAAC;AAAA,OAAO;AAC3D,UAAME,KAAI,CAAC;AACX,IAAAA,GAAE,KAAKF,GAAE,CAAC,GAAGA,GAAE,CAAC,CAAC;AACjB,aAASC,KAAI,GAAGA,KAAID,GAAE,QAAQC,KAAK,CAAAC,GAAE,KAAKF,GAAEC,EAAC,CAAC,GAAGA,OAAMD,GAAE,SAAS,KAAKE,GAAE,KAAKF,GAAEC,EAAC,CAAC;AAClF,UAAMG,KAAI,CAAC,GACTC,KAAI,IAAIJ;AACV,IAAAE,GAAE,KAAK,EAAED,GAAE,CAAC,CAAC,CAAC;AACd,aAASF,KAAI,GAAGA,KAAI,IAAIE,GAAE,QAAQF,MAAK;AACrC,YAAMC,KAAIC,GAAEF,EAAC;AACb,MAAAI,GAAE,CAAC,IAAI,CAACH,GAAE,CAAC,GAAGA,GAAE,CAAC,CAAC,GAAGG,GAAE,CAAC,IAAI,CAACH,GAAE,CAAC,KAAKI,KAAIH,GAAEF,KAAI,CAAC,EAAE,CAAC,IAAIK,KAAIH,GAAEF,KAAI,CAAC,EAAE,CAAC,KAAK,GAAGC,GAAE,CAAC,KAAKI,KAAIH,GAAEF,KAAI,CAAC,EAAE,CAAC,IAAIK,KAAIH,GAAEF,KAAI,CAAC,EAAE,CAAC,KAAK,CAAC,GAAGI,GAAE,CAAC,IAAI,CAACF,GAAEF,KAAI,CAAC,EAAE,CAAC,KAAKK,KAAIH,GAAEF,EAAC,EAAE,CAAC,IAAIK,KAAIH,GAAEF,KAAI,CAAC,EAAE,CAAC,KAAK,GAAGE,GAAEF,KAAI,CAAC,EAAE,CAAC,KAAKK,KAAIH,GAAEF,EAAC,EAAE,CAAC,IAAIK,KAAIH,GAAEF,KAAI,CAAC,EAAE,CAAC,KAAK,CAAC,GAAGI,GAAE,CAAC,IAAI,CAACF,GAAEF,KAAI,CAAC,EAAE,CAAC,GAAGE,GAAEF,KAAI,CAAC,EAAE,CAAC,CAAC,GAAGG,GAAE,KAAKC,GAAE,CAAC,GAAGA,GAAE,CAAC,GAAGA,GAAE,CAAC,CAAC;AAAA,IACzS;AAAA,EACF;AACA,SAAOD;AACT;AACA,SAAS,EAAEH,IAAGC,IAAG;AACf,SAAO,KAAK,IAAID,GAAE,CAAC,IAAIC,GAAE,CAAC,GAAG,CAAC,IAAI,KAAK,IAAID,GAAE,CAAC,IAAIC,GAAE,CAAC,GAAG,CAAC;AAC3D;AACA,SAAS,EAAED,IAAGC,IAAGC,IAAG;AAClB,QAAMC,KAAI,EAAEF,IAAGC,EAAC;AAChB,MAAI,MAAMC,GAAG,QAAO,EAAEH,IAAGC,EAAC;AAC1B,MAAIG,OAAMJ,GAAE,CAAC,IAAIC,GAAE,CAAC,MAAMC,GAAE,CAAC,IAAID,GAAE,CAAC,MAAMD,GAAE,CAAC,IAAIC,GAAE,CAAC,MAAMC,GAAE,CAAC,IAAID,GAAE,CAAC,MAAME;AAC1E,SAAOC,KAAI,KAAK,IAAI,GAAG,KAAK,IAAI,GAAGA,EAAC,CAAC,GAAG,EAAEJ,IAAG,EAAEC,IAAGC,IAAGE,EAAC,CAAC;AACzD;AACA,SAAS,EAAEJ,IAAGC,IAAGC,IAAG;AAClB,SAAO,CAACF,GAAE,CAAC,KAAKC,GAAE,CAAC,IAAID,GAAE,CAAC,KAAKE,IAAGF,GAAE,CAAC,KAAKC,GAAE,CAAC,IAAID,GAAE,CAAC,KAAKE,EAAC;AAC5D;AACA,SAAS,EAAEF,IAAGC,IAAGC,IAAGC,IAAG;AACrB,QAAMC,KAAID,MAAK,CAAC;AAChB,MAAI,SAAUH,IAAGC,IAAG;AAClB,UAAMC,KAAIF,GAAEC,KAAI,CAAC,GACfE,KAAIH,GAAEC,KAAI,CAAC,GACXG,KAAIJ,GAAEC,KAAI,CAAC,GACXI,KAAIL,GAAEC,KAAI,CAAC;AACb,QAAIK,KAAI,IAAIH,GAAE,CAAC,IAAI,IAAID,GAAE,CAAC,IAAIG,GAAE,CAAC;AACjC,IAAAC,MAAKA;AACL,QAAIC,KAAI,IAAIJ,GAAE,CAAC,IAAI,IAAID,GAAE,CAAC,IAAIG,GAAE,CAAC;AACjC,IAAAE,MAAKA;AACL,QAAIC,KAAI,IAAIJ,GAAE,CAAC,IAAI,IAAIC,GAAE,CAAC,IAAIH,GAAE,CAAC;AACjC,IAAAM,MAAKA;AACL,QAAIC,KAAI,IAAIL,GAAE,CAAC,IAAI,IAAIC,GAAE,CAAC,IAAIH,GAAE,CAAC;AACjC,WAAOO,MAAKA,IAAGH,KAAIE,OAAMF,KAAIE,KAAID,KAAIE,OAAMF,KAAIE,KAAIH,KAAIC;AAAA,EACzD,EAAEP,IAAGC,EAAC,IAAIC,IAAG;AACX,UAAMA,KAAIF,GAAEC,KAAI,CAAC;AACjB,QAAIG,GAAE,QAAQ;AACZ,OAACC,KAAID,GAAEA,GAAE,SAAS,CAAC,GAAGE,KAAIJ,IAAG,KAAK,KAAK,EAAEG,IAAGC,EAAC,CAAC,KAAK,KAAKF,GAAE,KAAKF,EAAC;AAAA,IAClE,MAAO,CAAAE,GAAE,KAAKF,EAAC;AACf,IAAAE,GAAE,KAAKJ,GAAEC,KAAI,CAAC,CAAC;AAAA,EACjB,OAAO;AACL,UAAME,KAAI,KACRE,KAAIL,GAAEC,KAAI,CAAC,GACXK,KAAIN,GAAEC,KAAI,CAAC,GACXM,KAAIP,GAAEC,KAAI,CAAC,GACXO,KAAIR,GAAEC,KAAI,CAAC,GACXQ,KAAI,EAAEJ,IAAGC,IAAGH,EAAC,GACbO,KAAI,EAAEJ,IAAGC,IAAGJ,EAAC,GACbQ,KAAI,EAAEJ,IAAGC,IAAGL,EAAC,GACbS,KAAI,EAAEH,IAAGC,IAAGP,EAAC,GACbU,KAAI,EAAEH,IAAGC,IAAGR,EAAC,GACbW,KAAI,EAAEF,IAAGC,IAAGV,EAAC;AACf,MAAE,CAACE,IAAGI,IAAGG,IAAGE,EAAC,GAAG,GAAGZ,IAAGE,EAAC,GAAG,EAAE,CAACU,IAAGD,IAAGF,IAAGH,EAAC,GAAG,GAAGN,IAAGE,EAAC;AAAA,EACnD;AACA,MAAIC,IAAGC;AACP,SAAOF;AACT;AACA,SAAS,EAAEJ,IAAGC,IAAG;AACf,SAAO,EAAED,IAAG,GAAGA,GAAE,QAAQC,EAAC;AAC5B;AACA,SAAS,EAAED,IAAGC,IAAGC,IAAGC,IAAGC,IAAG;AACxB,QAAMC,KAAID,MAAK,CAAC,GACdE,KAAIN,GAAEC,EAAC,GACPM,KAAIP,GAAEE,KAAI,CAAC;AACb,MAAIM,KAAI,GACNC,KAAI;AACN,WAASN,KAAIF,KAAI,GAAGE,KAAID,KAAI,GAAG,EAAEC,IAAG;AAClC,UAAMF,KAAI,EAAED,GAAEG,EAAC,GAAGG,IAAGC,EAAC;AACtB,IAAAN,KAAIO,OAAMA,KAAIP,IAAGQ,KAAIN;AAAA,EACvB;AACA,SAAO,KAAK,KAAKK,EAAC,IAAIL,MAAK,EAAEH,IAAGC,IAAGQ,KAAI,GAAGN,IAAGE,EAAC,GAAG,EAAEL,IAAGS,IAAGP,IAAGC,IAAGE,EAAC,MAAMA,GAAE,UAAUA,GAAE,KAAKC,EAAC,GAAGD,GAAE,KAAKE,EAAC,IAAIF;AAC3G;AACA,SAAS,EAAEL,IAAGC,KAAI,MAAKC,IAAG;AACxB,QAAMC,KAAI,CAAC,GACTC,MAAKJ,GAAE,SAAS,KAAK;AACvB,WAASE,KAAI,GAAGA,KAAIE,IAAGF,MAAK;AAC1B,MAAEF,IAAG,IAAIE,IAAGD,IAAGE,EAAC;AAAA,EAClB;AACA,SAAOD,MAAKA,KAAI,IAAI,EAAEC,IAAG,GAAGA,GAAE,QAAQD,EAAC,IAAIC;AAC7C;AACA,IAAM,KAAK;AACX,IAAM,KAAN,MAAS;AAAA,EACP,YAAYH,IAAG;AACb,SAAK,iBAAiB;AAAA,MACpB,qBAAqB;AAAA,MACrB,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,cAAc;AAAA,MACd,MAAM;AAAA,MACN,oBAAoB;AAAA,MACpB,wBAAwB;AAAA,MACxB,kBAAkB;AAAA,MAClB,wBAAwB;AAAA,IAC1B,GAAG,KAAK,SAASA,MAAK,CAAC,GAAG,KAAK,OAAO,YAAY,KAAK,iBAAiB,KAAK,GAAG,KAAK,OAAO,OAAO;AAAA,EACrG;AAAA,EACA,OAAO,UAAU;AACf,WAAO,KAAK,MAAM,KAAK,OAAO,IAAI,KAAK,EAAE;AAAA,EAC3C;AAAA,EACA,GAAGA,IAAG;AACJ,WAAOA,KAAI,OAAO,OAAO,CAAC,GAAG,KAAK,gBAAgBA,EAAC,IAAI,KAAK;AAAA,EAC9D;AAAA,EACA,GAAGA,IAAGC,IAAGC,IAAG;AACV,WAAO;AAAA,MACL,OAAOF;AAAA,MACP,MAAMC,MAAK,CAAC;AAAA,MACZ,SAASC,MAAK,KAAK;AAAA,IACrB;AAAA,EACF;AAAA,EACA,KAAKF,IAAGC,IAAGC,IAAGC,IAAGC,IAAG;AAClB,UAAMC,KAAI,KAAK,GAAGD,EAAC;AACnB,WAAO,KAAK,GAAG,QAAQ,CAAC,EAAEJ,IAAGC,IAAGC,IAAGC,IAAGE,EAAC,CAAC,GAAGA,EAAC;AAAA,EAC9C;AAAA,EACA,UAAUL,IAAGC,IAAGC,IAAGC,IAAGC,IAAG;AACvB,UAAMC,KAAI,KAAK,GAAGD,EAAC,GACjBE,KAAI,CAAC,GACLC,KAAI,EAAEP,IAAGC,IAAGC,IAAGC,IAAGE,EAAC;AACrB,QAAIA,GAAE,MAAM;AACV,YAAMD,KAAI,CAAC,CAACJ,IAAGC,EAAC,GAAG,CAACD,KAAIE,IAAGD,EAAC,GAAG,CAACD,KAAIE,IAAGD,KAAIE,EAAC,GAAG,CAACH,IAAGC,KAAIE,EAAC,CAAC;AACzD,kBAAYE,GAAE,YAAYC,GAAE,KAAK,EAAE,CAACF,EAAC,GAAGC,EAAC,CAAC,IAAIC,GAAE,KAAK,EAAE,CAACF,EAAC,GAAGC,EAAC,CAAC;AAAA,IAChE;AACA,WAAOA,GAAE,WAAW,MAAMC,GAAE,KAAKC,EAAC,GAAG,KAAK,GAAG,aAAaD,IAAGD,EAAC;AAAA,EAChE;AAAA,EACA,QAAQL,IAAGC,IAAGC,IAAGC,IAAGC,IAAG;AACrB,UAAMC,KAAI,KAAK,GAAGD,EAAC,GACjBE,KAAI,CAAC,GACLC,KAAI,EAAEL,IAAGC,IAAGE,EAAC,GACbG,KAAI,EAAER,IAAGC,IAAGI,IAAGE,EAAC;AAClB,QAAIF,GAAE,KAAM,KAAI,YAAYA,GAAE,WAAW;AACvC,YAAMH,KAAI,EAAEF,IAAGC,IAAGI,IAAGE,EAAC,EAAE;AACxB,MAAAL,GAAE,OAAO,YAAYI,GAAE,KAAKJ,EAAC;AAAA,IAC/B,MAAO,CAAAI,GAAE,KAAK,EAAE,CAACE,GAAE,eAAe,GAAGH,EAAC,CAAC;AACvC,WAAOA,GAAE,WAAW,MAAMC,GAAE,KAAKE,GAAE,KAAK,GAAG,KAAK,GAAG,WAAWF,IAAGD,EAAC;AAAA,EACpE;AAAA,EACA,OAAOL,IAAGC,IAAGC,IAAGC,IAAG;AACjB,UAAMC,KAAI,KAAK,QAAQJ,IAAGC,IAAGC,IAAGA,IAAGC,EAAC;AACpC,WAAOC,GAAE,QAAQ,UAAUA;AAAA,EAC7B;AAAA,EACA,WAAWJ,IAAGC,IAAG;AACf,UAAMC,KAAI,KAAK,GAAGD,EAAC;AACnB,WAAO,KAAK,GAAG,cAAc,CAAC,EAAED,IAAG,OAAIE,EAAC,CAAC,GAAGA,EAAC;AAAA,EAC/C;AAAA,EACA,IAAIF,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,KAAI,OAAIC,IAAG;AAC/B,UAAMC,KAAI,KAAK,GAAGD,EAAC,GACjBE,KAAI,CAAC,GACLC,KAAI,EAAEV,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,IAAG,MAAIE,EAAC;AAClC,QAAIF,MAAKE,GAAE,KAAM,KAAI,YAAYA,GAAE,WAAW;AAC5C,YAAMF,KAAI,OAAO,OAAO,CAAC,GAAGE,EAAC;AAC7B,MAAAF,GAAE,qBAAqB;AACvB,YAAMC,KAAI,EAAEP,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,IAAG,MAAI,OAAIC,EAAC;AACvC,MAAAC,GAAE,OAAO,YAAYE,GAAE,KAAKF,EAAC;AAAA,IAC/B,MAAO,CAAAE,GAAE,KAAK,SAAUT,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,IAAG;AAC3C,YAAMC,KAAIP,IACRQ,KAAIP;AACN,UAAIQ,KAAI,KAAK,IAAIP,KAAI,CAAC,GACpBQ,KAAI,KAAK,IAAIP,KAAI,CAAC;AACpB,MAAAM,MAAK,EAAE,OAAMA,IAAGH,EAAC,GAAGI,MAAK,EAAE,OAAMA,IAAGJ,EAAC;AACrC,UAAIK,KAAIP,IACNQ,KAAIP;AACN,aAAOM,KAAI,IAAI,CAAAA,MAAK,IAAI,KAAK,IAAIC,MAAK,IAAI,KAAK;AAC/C,MAAAA,KAAID,KAAI,IAAI,KAAK,OAAOA,KAAI,GAAGC,KAAI,IAAI,KAAK;AAC5C,YAAMC,MAAKD,KAAID,MAAKL,GAAE,gBACpBQ,KAAI,CAAC;AACP,eAASd,KAAIW,IAAGX,MAAKY,IAAGZ,MAAKa,GAAG,CAAAC,GAAE,KAAK,CAACP,KAAIE,KAAI,KAAK,IAAIT,EAAC,GAAGQ,KAAIE,KAAI,KAAK,IAAIV,EAAC,CAAC,CAAC;AACjF,aAAOc,GAAE,KAAK,CAACP,KAAIE,KAAI,KAAK,IAAIG,EAAC,GAAGJ,KAAIE,KAAI,KAAK,IAAIE,EAAC,CAAC,CAAC,GAAGE,GAAE,KAAK,CAACP,IAAGC,EAAC,CAAC,GAAG,EAAE,CAACM,EAAC,GAAGR,EAAC;AAAA,IACrF,EAAEN,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,IAAGG,EAAC,CAAC;AACtB,WAAOA,GAAE,WAAW,MAAMC,GAAE,KAAKC,EAAC,GAAG,KAAK,GAAG,OAAOD,IAAGD,EAAC;AAAA,EAC1D;AAAA,EACA,MAAMR,IAAGC,IAAG;AACV,UAAMC,KAAI,KAAK,GAAGD,EAAC,GACjBE,KAAI,CAAC,GACLC,KAAI,EAAEJ,IAAGE,EAAC;AACZ,QAAIA,GAAE,QAAQA,GAAE,SAAS,GAAI,KAAI,YAAYA,GAAE,WAAW;AACxD,YAAMD,KAAI,EAAED,IAAG,OAAO,OAAO,OAAO,OAAO,CAAC,GAAGE,EAAC,GAAG;AAAA,QACjD,oBAAoB;AAAA,QACpB,WAAWA,GAAE,YAAYA,GAAE,YAAYA,GAAE,yBAAyB;AAAA,MACpE,CAAC,CAAC;AACF,MAAAC,GAAE,KAAK;AAAA,QACL,MAAM;AAAA,QACN,KAAK,KAAK,aAAaF,GAAE,GAAG;AAAA,MAC9B,CAAC;AAAA,IACH,OAAO;AACL,YAAMA,KAAI,CAAC,GACTG,KAAIJ;AACN,UAAII,GAAE,QAAQ;AACZ,cAAMJ,KAAI,YAAY,OAAOI,GAAE,CAAC,EAAE,CAAC,IAAI,CAACA,EAAC,IAAIA;AAC7C,mBAAWD,MAAKH,GAAG,CAAAG,GAAE,SAAS,IAAIF,GAAE,KAAK,GAAGE,EAAC,IAAI,MAAMA,GAAE,SAASF,GAAE,KAAK,GAAG,EAAE,EAAE,CAACE,GAAE,CAAC,GAAGA,GAAE,CAAC,GAAGA,GAAE,CAAC,GAAGA,GAAE,CAAC,CAAC,CAAC,GAAG,KAAK,IAAID,GAAE,aAAa,CAAC,CAAC,IAAID,GAAE,KAAK,GAAG,EAAE,EAAEE,EAAC,GAAG,KAAK,IAAID,GAAE,aAAa,CAAC,CAAC;AAAA,MACvL;AACA,MAAAD,GAAE,UAAUE,GAAE,KAAK,EAAE,CAACF,EAAC,GAAGC,EAAC,CAAC;AAAA,IAC9B;AACA,WAAOA,GAAE,WAAW,MAAMC,GAAE,KAAKC,EAAC,GAAG,KAAK,GAAG,SAASD,IAAGD,EAAC;AAAA,EAC5D;AAAA,EACA,QAAQF,IAAGC,IAAG;AACZ,UAAMC,KAAI,KAAK,GAAGD,EAAC,GACjBE,KAAI,CAAC,GACLC,KAAI,EAAEJ,IAAG,MAAIE,EAAC;AAChB,WAAOA,GAAE,SAAS,YAAYA,GAAE,YAAYC,GAAE,KAAK,EAAE,CAACH,EAAC,GAAGE,EAAC,CAAC,IAAIC,GAAE,KAAK,EAAE,CAACH,EAAC,GAAGE,EAAC,CAAC,IAAIA,GAAE,WAAW,MAAMC,GAAE,KAAKC,EAAC,GAAG,KAAK,GAAG,WAAWD,IAAGD,EAAC;AAAA,EAC3I;AAAA,EACA,KAAKF,IAAGC,IAAG;AACT,UAAMC,KAAI,KAAK,GAAGD,EAAC,GACjBE,KAAI,CAAC;AACP,QAAI,CAACH,GAAG,QAAO,KAAK,GAAG,QAAQG,IAAGD,EAAC;AACnC,IAAAF,MAAKA,MAAK,IAAI,QAAQ,OAAO,GAAG,EAAE,QAAQ,UAAU,GAAG,EAAE,QAAQ,WAAW,GAAG;AAC/E,UAAMI,KAAIF,GAAE,QAAQ,kBAAkBA,GAAE,QAAQA,GAAE,SAAS,IACzDG,KAAIH,GAAE,WAAW,IACjBI,KAAI,CAAC,EAAEJ,GAAE,kBAAkBA,GAAE,iBAAiB,IAC9CK,KAAI,SAAUP,IAAGC,IAAGC,IAAG;AACrB,YAAMC,KAAI,EAAE,EAAE,EAAEH,EAAC,CAAC,CAAC,GACjBI,KAAI,CAAC;AACP,UAAIC,KAAI,CAAC,GACPC,KAAI,CAAC,GAAG,CAAC,GACTC,KAAI,CAAC;AACP,YAAMC,KAAI,MAAM;AACZ,QAAAD,GAAE,UAAU,KAAKF,GAAE,KAAK,GAAG,EAAEE,IAAGN,EAAC,CAAC,GAAGM,KAAI,CAAC;AAAA,MAC5C,GACAE,KAAI,MAAM;AACR,QAAAD,GAAE,GAAGH,GAAE,WAAWD,GAAE,KAAKC,EAAC,GAAGA,KAAI,CAAC;AAAA,MACpC;AACF,iBAAW;AAAA,QACT,KAAKL;AAAA,QACL,MAAMC;AAAA,MACR,KAAKE,GAAG,SAAQH,IAAG;AAAA,QACjB,KAAK;AACH,UAAAS,GAAE,GAAGH,KAAI,CAACL,GAAE,CAAC,GAAGA,GAAE,CAAC,CAAC,GAAGI,GAAE,KAAKC,EAAC;AAC/B;AAAA,QACF,KAAK;AACH,UAAAE,GAAE,GAAGH,GAAE,KAAK,CAACJ,GAAE,CAAC,GAAGA,GAAE,CAAC,CAAC,CAAC;AACxB;AAAA,QACF,KAAK;AACH,cAAI,CAACM,GAAE,QAAQ;AACb,kBAAMP,KAAIK,GAAE,SAASA,GAAEA,GAAE,SAAS,CAAC,IAAIC;AACvC,YAAAC,GAAE,KAAK,CAACP,GAAE,CAAC,GAAGA,GAAE,CAAC,CAAC,CAAC;AAAA,UACrB;AACA,UAAAO,GAAE,KAAK,CAACN,GAAE,CAAC,GAAGA,GAAE,CAAC,CAAC,CAAC,GAAGM,GAAE,KAAK,CAACN,GAAE,CAAC,GAAGA,GAAE,CAAC,CAAC,CAAC,GAAGM,GAAE,KAAK,CAACN,GAAE,CAAC,GAAGA,GAAE,CAAC,CAAC,CAAC;AAC/D;AAAA,QACF,KAAK;AACH,UAAAO,GAAE,GAAGH,GAAE,KAAK,CAACC,GAAE,CAAC,GAAGA,GAAE,CAAC,CAAC,CAAC;AAAA,MAC5B;AACA,UAAIG,GAAE,GAAG,CAACP,GAAG,QAAOE;AACpB,YAAMM,KAAI,CAAC;AACX,iBAAWV,MAAKI,IAAG;AACjB,cAAMH,KAAI,EAAED,IAAGE,EAAC;AAChB,QAAAD,GAAE,UAAUS,GAAE,KAAKT,EAAC;AAAA,MACtB;AACA,aAAOS;AAAA,IACT,EAAEV,IAAG,GAAGM,KAAI,IAAI,KAAKJ,GAAE,kBAAkB,MAAM,IAAIA,GAAE,aAAa,CAAC,GACnEM,KAAI,EAAER,IAAGE,EAAC;AACZ,QAAIE,GAAG,KAAI,YAAYF,GAAE,WAAW;AAClC,UAAI,MAAMK,GAAE,QAAQ;AAClB,cAAMN,KAAI,EAAED,IAAG,OAAO,OAAO,OAAO,OAAO,CAAC,GAAGE,EAAC,GAAG;AAAA,UACjD,oBAAoB;AAAA,UACpB,WAAWA,GAAE,YAAYA,GAAE,YAAYA,GAAE,yBAAyB;AAAA,QACpE,CAAC,CAAC;AACF,QAAAC,GAAE,KAAK;AAAA,UACL,MAAM;AAAA,UACN,KAAK,KAAK,aAAaF,GAAE,GAAG;AAAA,QAC9B,CAAC;AAAA,MACH,MAAO,CAAAE,GAAE,KAAK,EAAEI,IAAGL,EAAC,CAAC;AAAA,IACvB,MAAO,CAAAC,GAAE,KAAK,EAAEI,IAAGL,EAAC,CAAC;AACrB,WAAOG,OAAMC,KAAIC,GAAE,QAAQ,CAAAP,OAAK;AAC9B,MAAAG,GAAE,KAAK,EAAEH,IAAG,OAAIE,EAAC,CAAC;AAAA,IACpB,CAAC,IAAIC,GAAE,KAAKK,EAAC,IAAI,KAAK,GAAG,QAAQL,IAAGD,EAAC;AAAA,EACvC;AAAA,EACA,UAAUF,IAAGC,IAAG;AACd,QAAIC,KAAI;AACR,eAAWC,MAAKH,GAAE,KAAK;AACrB,YAAMA,KAAI,YAAY,OAAOC,MAAKA,MAAK,IAAIE,GAAE,KAAK,IAAI,CAAAH,OAAK,CAACA,GAAE,QAAQC,EAAC,CAAC,IAAIE,GAAE;AAC9E,cAAQA,GAAE,IAAI;AAAA,QACZ,KAAK;AACH,UAAAD,MAAK,IAAIF,GAAE,CAAC,CAAC,IAAIA,GAAE,CAAC,CAAC;AACrB;AAAA,QACF,KAAK;AACH,UAAAE,MAAK,IAAIF,GAAE,CAAC,CAAC,IAAIA,GAAE,CAAC,CAAC,KAAKA,GAAE,CAAC,CAAC,IAAIA,GAAE,CAAC,CAAC,KAAKA,GAAE,CAAC,CAAC,IAAIA,GAAE,CAAC,CAAC;AACvD;AAAA,QACF,KAAK;AACH,UAAAE,MAAK,IAAIF,GAAE,CAAC,CAAC,IAAIA,GAAE,CAAC,CAAC;AAAA,MACzB;AAAA,IACF;AACA,WAAOE,GAAE,KAAK;AAAA,EAChB;AAAA,EACA,QAAQF,IAAG;AACT,UAAMC,KAAID,GAAE,QAAQ,CAAC,GACnBE,KAAIF,GAAE,WAAW,KAAK,gBACtBG,KAAI,CAAC;AACP,eAAWH,MAAKC,IAAG;AACjB,UAAIA,KAAI;AACR,cAAQD,GAAE,MAAM;AAAA,QACd,KAAK;AACH,UAAAC,KAAI;AAAA,YACF,GAAG,KAAK,UAAUD,EAAC;AAAA,YACnB,QAAQE,GAAE;AAAA,YACV,aAAaA,GAAE;AAAA,YACf,MAAM;AAAA,UACR;AACA;AAAA,QACF,KAAK;AACH,UAAAD,KAAI;AAAA,YACF,GAAG,KAAK,UAAUD,EAAC;AAAA,YACnB,QAAQ;AAAA,YACR,aAAa;AAAA,YACb,MAAME,GAAE,QAAQ;AAAA,UAClB;AACA;AAAA,QACF,KAAK;AACH,UAAAD,KAAI,KAAK,WAAWD,IAAGE,EAAC;AAAA,MAC5B;AACA,MAAAD,MAAKE,GAAE,KAAKF,EAAC;AAAA,IACf;AACA,WAAOE;AAAA,EACT;AAAA,EACA,WAAWH,IAAGC,IAAG;AACf,QAAIC,KAAID,GAAE;AACV,WAAOC,KAAI,MAAMA,KAAID,GAAE,cAAc,IAAI;AAAA,MACvC,GAAG,KAAK,UAAUD,EAAC;AAAA,MACnB,QAAQC,GAAE,QAAQ;AAAA,MAClB,aAAaC;AAAA,MACb,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA,aAAaF,IAAG;AACd,WAAOA,GAAE,OAAO,CAACA,IAAGC,OAAM,MAAMA,MAAK,WAAWD,GAAE,EAAE;AAAA,EACtD;AACF;AACA,IAAM,KAAN,MAAS;AAAA,EACP,YAAYA,IAAGC,IAAG;AAChB,SAAK,SAASD,IAAG,KAAK,MAAM,KAAK,OAAO,WAAW,IAAI,GAAG,KAAK,MAAM,IAAI,GAAGC,EAAC;AAAA,EAC/E;AAAA,EACA,KAAKD,IAAG;AACN,UAAMC,KAAID,GAAE,QAAQ,CAAC,GACnBE,KAAIF,GAAE,WAAW,KAAK,kBAAkB,GACxCG,KAAI,KAAK,KACTC,KAAIJ,GAAE,QAAQ;AAChB,eAAWK,MAAKJ,GAAG,SAAQI,GAAE,MAAM;AAAA,MACjC,KAAK;AACH,QAAAF,GAAE,KAAK,GAAGA,GAAE,cAAc,WAAWD,GAAE,SAAS,gBAAgBA,GAAE,QAAQC,GAAE,YAAYD,GAAE,aAAaA,GAAE,kBAAkBC,GAAE,YAAYD,GAAE,cAAc,GAAGA,GAAE,yBAAyBC,GAAE,iBAAiBD,GAAE,uBAAuB,KAAK,eAAeC,IAAGE,IAAGD,EAAC,GAAGD,GAAE,QAAQ;AAC3Q;AAAA,MACF,KAAK,YACH;AACE,QAAAA,GAAE,KAAK,GAAGA,GAAE,YAAYD,GAAE,QAAQ;AAClC,cAAMD,KAAI,YAAYD,GAAE,SAAS,cAAcA,GAAE,SAAS,WAAWA,GAAE,QAAQ,YAAY;AAC3F,aAAK,eAAeG,IAAGE,IAAGD,IAAGH,EAAC,GAAGE,GAAE,QAAQ;AAC3C;AAAA,MACF;AAAA,MACF,KAAK;AACH,aAAK,WAAWA,IAAGE,IAAGH,EAAC;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,WAAWF,IAAGC,IAAGC,IAAG;AAClB,QAAIC,KAAID,GAAE;AACV,IAAAC,KAAI,MAAMA,KAAID,GAAE,cAAc,IAAIF,GAAE,KAAK,GAAGE,GAAE,gBAAgBF,GAAE,YAAYE,GAAE,YAAY,GAAGA,GAAE,uBAAuBF,GAAE,iBAAiBE,GAAE,qBAAqBF,GAAE,cAAcE,GAAE,QAAQ,IAAIF,GAAE,YAAYG,IAAG,KAAK,eAAeH,IAAGC,IAAGC,GAAE,uBAAuB,GAAGF,GAAE,QAAQ;AAAA,EACjR;AAAA,EACA,eAAeA,IAAGC,IAAGC,IAAGC,KAAI,WAAW;AACrC,IAAAH,GAAE,UAAU;AACZ,eAAWG,MAAKF,GAAE,KAAK;AACrB,YAAMA,KAAI,YAAY,OAAOC,MAAKA,MAAK,IAAIC,GAAE,KAAK,IAAI,CAAAH,OAAK,CAACA,GAAE,QAAQE,EAAC,CAAC,IAAIC,GAAE;AAC9E,cAAQA,GAAE,IAAI;AAAA,QACZ,KAAK;AACH,UAAAH,GAAE,OAAOC,GAAE,CAAC,GAAGA,GAAE,CAAC,CAAC;AACnB;AAAA,QACF,KAAK;AACH,UAAAD,GAAE,cAAcC,GAAE,CAAC,GAAGA,GAAE,CAAC,GAAGA,GAAE,CAAC,GAAGA,GAAE,CAAC,GAAGA,GAAE,CAAC,GAAGA,GAAE,CAAC,CAAC;AAClD;AAAA,QACF,KAAK;AACH,UAAAD,GAAE,OAAOC,GAAE,CAAC,GAAGA,GAAE,CAAC,CAAC;AAAA,MACvB;AAAA,IACF;AACA,mBAAeA,GAAE,OAAOD,GAAE,KAAKG,EAAC,IAAIH,GAAE,OAAO;AAAA,EAC/C;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,oBAAoB;AAClB,WAAO,KAAK,IAAI;AAAA,EAClB;AAAA,EACA,KAAKA,IAAGC,IAAGC,IAAGC,IAAGC,IAAG;AAClB,UAAMC,KAAI,KAAK,IAAI,KAAKL,IAAGC,IAAGC,IAAGC,IAAGC,EAAC;AACrC,WAAO,KAAK,KAAKC,EAAC,GAAGA;AAAA,EACvB;AAAA,EACA,UAAUL,IAAGC,IAAGC,IAAGC,IAAGC,IAAG;AACvB,UAAMC,KAAI,KAAK,IAAI,UAAUL,IAAGC,IAAGC,IAAGC,IAAGC,EAAC;AAC1C,WAAO,KAAK,KAAKC,EAAC,GAAGA;AAAA,EACvB;AAAA,EACA,QAAQL,IAAGC,IAAGC,IAAGC,IAAGC,IAAG;AACrB,UAAMC,KAAI,KAAK,IAAI,QAAQL,IAAGC,IAAGC,IAAGC,IAAGC,EAAC;AACxC,WAAO,KAAK,KAAKC,EAAC,GAAGA;AAAA,EACvB;AAAA,EACA,OAAOL,IAAGC,IAAGC,IAAGC,IAAG;AACjB,UAAMC,KAAI,KAAK,IAAI,OAAOJ,IAAGC,IAAGC,IAAGC,EAAC;AACpC,WAAO,KAAK,KAAKC,EAAC,GAAGA;AAAA,EACvB;AAAA,EACA,WAAWJ,IAAGC,IAAG;AACf,UAAMC,KAAI,KAAK,IAAI,WAAWF,IAAGC,EAAC;AAClC,WAAO,KAAK,KAAKC,EAAC,GAAGA;AAAA,EACvB;AAAA,EACA,QAAQF,IAAGC,IAAG;AACZ,UAAMC,KAAI,KAAK,IAAI,QAAQF,IAAGC,EAAC;AAC/B,WAAO,KAAK,KAAKC,EAAC,GAAGA;AAAA,EACvB;AAAA,EACA,IAAIF,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,KAAI,OAAIC,IAAG;AAC/B,UAAMC,KAAI,KAAK,IAAI,IAAIR,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,EAAC;AAC7C,WAAO,KAAK,KAAKC,EAAC,GAAGA;AAAA,EACvB;AAAA,EACA,MAAMR,IAAGC,IAAG;AACV,UAAMC,KAAI,KAAK,IAAI,MAAMF,IAAGC,EAAC;AAC7B,WAAO,KAAK,KAAKC,EAAC,GAAGA;AAAA,EACvB;AAAA,EACA,KAAKF,IAAGC,IAAG;AACT,UAAMC,KAAI,KAAK,IAAI,KAAKF,IAAGC,EAAC;AAC5B,WAAO,KAAK,KAAKC,EAAC,GAAGA;AAAA,EACvB;AACF;AACA,IAAM,KAAK;AACX,IAAM,KAAN,MAAS;AAAA,EACP,YAAYF,IAAGC,IAAG;AAChB,SAAK,MAAMD,IAAG,KAAK,MAAM,IAAI,GAAGC,EAAC;AAAA,EACnC;AAAA,EACA,KAAKD,IAAG;AACN,UAAMC,KAAID,GAAE,QAAQ,CAAC,GACnBE,KAAIF,GAAE,WAAW,KAAK,kBAAkB,GACxCG,KAAI,KAAK,IAAI,iBAAiB,OAAO,UACrCC,KAAID,GAAE,gBAAgB,IAAI,GAAG,GAC7BE,KAAIL,GAAE,QAAQ;AAChB,eAAWM,MAAKL,IAAG;AACjB,UAAIA,KAAI;AACR,cAAQK,GAAE,MAAM;AAAA,QACd,KAAK;AACH,UAAAL,KAAIE,GAAE,gBAAgB,IAAI,MAAM,GAAGF,GAAE,aAAa,KAAK,KAAK,UAAUK,IAAGD,EAAC,CAAC,GAAGJ,GAAE,aAAa,UAAUC,GAAE,MAAM,GAAGD,GAAE,aAAa,gBAAgBC,GAAE,cAAc,EAAE,GAAGD,GAAE,aAAa,QAAQ,MAAM,GAAGC,GAAE,kBAAkBD,GAAE,aAAa,oBAAoBC,GAAE,eAAe,KAAK,GAAG,EAAE,KAAK,CAAC,GAAGA,GAAE,wBAAwBD,GAAE,aAAa,qBAAqB,GAAGC,GAAE,oBAAoB,EAAE;AAC1X;AAAA,QACF,KAAK;AACH,UAAAD,KAAIE,GAAE,gBAAgB,IAAI,MAAM,GAAGF,GAAE,aAAa,KAAK,KAAK,UAAUK,IAAGD,EAAC,CAAC,GAAGJ,GAAE,aAAa,UAAU,MAAM,GAAGA,GAAE,aAAa,gBAAgB,GAAG,GAAGA,GAAE,aAAa,QAAQC,GAAE,QAAQ,EAAE,GAAG,YAAYF,GAAE,SAAS,cAAcA,GAAE,SAASC,GAAE,aAAa,aAAa,SAAS;AAChR;AAAA,QACF,KAAK;AACH,UAAAA,KAAI,KAAK,WAAWE,IAAGG,IAAGJ,EAAC;AAAA,MAC/B;AACA,MAAAD,MAAKG,GAAE,YAAYH,EAAC;AAAA,IACtB;AACA,WAAOG;AAAA,EACT;AAAA,EACA,WAAWJ,IAAGC,IAAGC,IAAG;AAClB,QAAIC,KAAID,GAAE;AACV,IAAAC,KAAI,MAAMA,KAAID,GAAE,cAAc;AAC9B,UAAME,KAAIJ,GAAE,gBAAgB,IAAI,MAAM;AACtC,WAAOI,GAAE,aAAa,KAAK,KAAK,UAAUH,IAAGC,GAAE,uBAAuB,CAAC,GAAGE,GAAE,aAAa,UAAUF,GAAE,QAAQ,EAAE,GAAGE,GAAE,aAAa,gBAAgBD,KAAI,EAAE,GAAGC,GAAE,aAAa,QAAQ,MAAM,GAAGF,GAAE,gBAAgBE,GAAE,aAAa,oBAAoBF,GAAE,aAAa,KAAK,GAAG,EAAE,KAAK,CAAC,GAAGA,GAAE,sBAAsBE,GAAE,aAAa,qBAAqB,GAAGF,GAAE,kBAAkB,EAAE,GAAGE;AAAA,EAC3W;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,oBAAoB;AAClB,WAAO,KAAK,IAAI;AAAA,EAClB;AAAA,EACA,UAAUJ,IAAGC,IAAG;AACd,WAAO,KAAK,IAAI,UAAUD,IAAGC,EAAC;AAAA,EAChC;AAAA,EACA,KAAKD,IAAGC,IAAGC,IAAGC,IAAGC,IAAG;AAClB,UAAMC,KAAI,KAAK,IAAI,KAAKL,IAAGC,IAAGC,IAAGC,IAAGC,EAAC;AACrC,WAAO,KAAK,KAAKC,EAAC;AAAA,EACpB;AAAA,EACA,UAAUL,IAAGC,IAAGC,IAAGC,IAAGC,IAAG;AACvB,UAAMC,KAAI,KAAK,IAAI,UAAUL,IAAGC,IAAGC,IAAGC,IAAGC,EAAC;AAC1C,WAAO,KAAK,KAAKC,EAAC;AAAA,EACpB;AAAA,EACA,QAAQL,IAAGC,IAAGC,IAAGC,IAAGC,IAAG;AACrB,UAAMC,KAAI,KAAK,IAAI,QAAQL,IAAGC,IAAGC,IAAGC,IAAGC,EAAC;AACxC,WAAO,KAAK,KAAKC,EAAC;AAAA,EACpB;AAAA,EACA,OAAOL,IAAGC,IAAGC,IAAGC,IAAG;AACjB,UAAMC,KAAI,KAAK,IAAI,OAAOJ,IAAGC,IAAGC,IAAGC,EAAC;AACpC,WAAO,KAAK,KAAKC,EAAC;AAAA,EACpB;AAAA,EACA,WAAWJ,IAAGC,IAAG;AACf,UAAMC,KAAI,KAAK,IAAI,WAAWF,IAAGC,EAAC;AAClC,WAAO,KAAK,KAAKC,EAAC;AAAA,EACpB;AAAA,EACA,QAAQF,IAAGC,IAAG;AACZ,UAAMC,KAAI,KAAK,IAAI,QAAQF,IAAGC,EAAC;AAC/B,WAAO,KAAK,KAAKC,EAAC;AAAA,EACpB;AAAA,EACA,IAAIF,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,KAAI,OAAIC,IAAG;AAC/B,UAAMC,KAAI,KAAK,IAAI,IAAIR,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,IAAGC,EAAC;AAC7C,WAAO,KAAK,KAAKC,EAAC;AAAA,EACpB;AAAA,EACA,MAAMR,IAAGC,IAAG;AACV,UAAMC,KAAI,KAAK,IAAI,MAAMF,IAAGC,EAAC;AAC7B,WAAO,KAAK,KAAKC,EAAC;AAAA,EACpB;AAAA,EACA,KAAKF,IAAGC,IAAG;AACT,UAAMC,KAAI,KAAK,IAAI,KAAKF,IAAGC,EAAC;AAC5B,WAAO,KAAK,KAAKC,EAAC;AAAA,EACpB;AACF;AACA,IAAI,KAAK;AAAA,EACP,QAAQ,CAACF,IAAGC,OAAM,IAAI,GAAGD,IAAGC,EAAC;AAAA,EAC7B,KAAK,CAACD,IAAGC,OAAM,IAAI,GAAGD,IAAGC,EAAC;AAAA,EAC1B,WAAW,CAAAD,OAAK,IAAI,GAAGA,EAAC;AAAA,EACxB,SAAS,MAAM,GAAG,QAAQ;AAC5B;;;AC38CA,IAAI,cAA6B,OAAO,CAAO,QAAQ,MAAM,aAAa;AACxE,MAAI;AACJ,QAAM,gBAAgB,KAAK,iBAAiB,SAAS,WAAW,GAAG,UAAU;AAC7E,MAAI,CAAC,UAAU;AACb,iBAAa;AAAA,EACf,OAAO;AACL,iBAAa;AAAA,EACf;AACA,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,UAAU,EAAE,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AAC9F,QAAM,UAAU,SAAS,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO,EAAE,KAAK,SAAS,oBAAoB,KAAK,UAAU,CAAC;AAC9G,MAAI;AACJ,MAAI,KAAK,UAAU,QAAQ;AACzB,YAAQ;AAAA,EACV,OAAO;AACL,YAAQ,OAAO,KAAK,UAAU,WAAW,KAAK,QAAQ,KAAK,MAAM,CAAC;AAAA,EACpE;AACA,QAAM,QAAQ,MAAM,WAAW,SAAS,aAAa,eAAe,KAAK,GAAG,WAAW,CAAC,GAAG;AAAA,IACzF;AAAA,IACA,OAAO,KAAK,SAAS,WAAW,EAAE,WAAW;AAAA;AAAA,IAE7C,YAAY;AAAA,IACZ,OAAO,KAAK;AAAA,IACZ,kBAAkB,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC,KAAK;AAAA,EAC1C,CAAC;AACD,MAAI,OAAO,MAAM,QAAQ;AACzB,QAAM,eAAe,MAAM,WAAW,KAAK;AAC3C,MAAI,eAAe;AACjB,UAAM,MAAM,MAAM,SAAS,CAAC;AAC5B,UAAM,KAAK,eAAO,KAAK;AACvB,UAAM,SAAS,IAAI,qBAAqB,KAAK;AAC7C,QAAI,QAAQ;AACV,YAAM,YAAY,MAAM,QAAQ,eAAe,EAAE,EAAE,KAAK,MAAM;AAC9D,YAAM,QAAQ,IAAI,CAAC,GAAG,MAAM,EAAE,IAAI,SAAO,IAAI,QAAQ,SAAO;AAC1D,iBAAS,aAAa;AACpB,cAAI,MAAM,UAAU;AACpB,cAAI,MAAM,gBAAgB;AAC1B,cAAI,WAAW;AACb,kBAAM,eAAe,WAAW,EAAE,WAAW,WAAW,EAAE,WAAW,OAAO,iBAAiB,SAAS,IAAI,EAAE;AAC5G,kBAAM,kBAAkB;AACxB,kBAAM,CAAC,qBAAqB,sBAAsB,QAAQ,IAAI,cAAc,YAAY;AACxF,kBAAM,QAAQ,qBAAqB,kBAAkB;AACrD,gBAAI,MAAM,WAAW;AACrB,gBAAI,MAAM,WAAW;AAAA,UACvB,OAAO;AACL,gBAAI,MAAM,QAAQ;AAAA,UACpB;AACA,cAAI,GAAG;AAAA,QACT;AACA,eAAO,YAAY,YAAY;AAC/B,mBAAW,MAAM;AACf,cAAI,IAAI,UAAU;AAChB,uBAAW;AAAA,UACb;AAAA,QACF,CAAC;AACD,YAAI,iBAAiB,SAAS,UAAU;AACxC,YAAI,iBAAiB,QAAQ,UAAU;AAAA,MACzC,CAAC,CAAC,CAAC;AAAA,IACL;AACA,WAAO,IAAI,sBAAsB;AACjC,OAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,OAAG,KAAK,UAAU,KAAK,MAAM;AAAA,EAC/B;AACA,MAAI,eAAe;AACjB,YAAQ,KAAK,aAAa,eAAe,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,SAAS,IAAI,GAAG;AAAA,EAC1F,OAAO;AACL,YAAQ,KAAK,aAAa,kBAAkB,CAAC,KAAK,SAAS,IAAI,GAAG;AAAA,EACpE;AACA,MAAI,KAAK,aAAa;AACpB,YAAQ,KAAK,aAAa,eAAe,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,SAAS,IAAI,GAAG;AAAA,EAC1F;AACA,UAAQ,OAAO,QAAQ,cAAc;AACrC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO;AAAA,EACT;AACF,IAAG,aAAa;AAChB,IAAI,cAA6B,OAAO,CAAO,QAAQ,OAAO,YAAY;AACxE,QAAM,gBAAgB,QAAQ,iBAAiB,SAAS,WAAW,GAAG,WAAW,UAAU;AAC3F,QAAM,UAAU,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO,EAAE,KAAK,SAAS,QAAQ,cAAc,EAAE;AAChG,QAAM,QAAQ,MAAM,WAAW,SAAS,aAAa,eAAe,KAAK,GAAG,WAAW,CAAC,GAAG;AAAA,IACzF;AAAA,IACA,OAAO,QAAQ,SAAS,WAAW,GAAG,WAAW;AAAA,IACjD,OAAO,QAAQ;AAAA,IACf,kBAAkB,CAAC,CAAC,QAAQ,QAAQ,CAAC,CAAC,QAAQ;AAAA,EAChD,CAAC;AACD,MAAI,OAAO,MAAM,QAAQ;AACzB,QAAM,cAAc,QAAQ,UAAU;AACtC,MAAI,SAAS,WAAW,GAAG,WAAW,UAAU,GAAG;AACjD,UAAM,MAAM,MAAM,SAAS,CAAC;AAC5B,UAAM,KAAK,eAAO,KAAK;AACvB,WAAO,IAAI,sBAAsB;AACjC,OAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,OAAG,KAAK,UAAU,KAAK,MAAM;AAAA,EAC/B;AACA,MAAI,eAAe;AACjB,YAAQ,KAAK,aAAa,eAAe,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,SAAS,IAAI,GAAG;AAAA,EAC1F,OAAO;AACL,YAAQ,KAAK,aAAa,kBAAkB,CAAC,KAAK,SAAS,IAAI,GAAG;AAAA,EACpE;AACA,MAAI,QAAQ,aAAa;AACvB,YAAQ,KAAK,aAAa,eAAe,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,SAAS,IAAI,GAAG;AAAA,EAC1F;AACA,UAAQ,OAAO,QAAQ,cAAc;AACrC,SAAO;AAAA,IACL,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA,OAAO;AAAA,EACT;AACF,IAAG,aAAa;AAChB,IAAI,mBAAkC,OAAO,CAAC,MAAM,YAAY;AAC9D,QAAM,OAAO,QAAQ,KAAK,EAAE,QAAQ;AACpC,OAAK,QAAQ,KAAK;AAClB,OAAK,SAAS,KAAK;AACrB,GAAG,kBAAkB;AACrB,IAAI,iBAAgC,OAAO,CAAC,MAAM,WAAW,KAAK,SAAS,cAAc,eAAe,UAAU,MAAM,KAAK,aAAa,OAAO,SAAS,KAAK,gBAAgB;AAC/K,SAAS,qBAAqB,QAAQ;AACpC,QAAM,eAAe,OAAO,IAAI,CAAC6B,IAAGC,OAAM,GAAGA,OAAM,IAAI,MAAM,GAAG,GAAGD,GAAE,CAAC,IAAIA,GAAE,CAAC,EAAE;AAC/E,eAAa,KAAK,GAAG;AACrB,SAAO,aAAa,KAAK,GAAG;AAC9B;AACA,OAAO,sBAAsB,sBAAsB;AACnD,SAAS,2BAA2B,IAAI,IAAI,IAAI,IAAI,WAAW,WAAW;AACxE,QAAM,SAAS,CAAC;AAChB,QAAM,QAAQ;AACd,QAAM,SAAS,KAAK;AACpB,QAAM,SAAS,KAAK;AACpB,QAAM,cAAc,SAAS;AAC7B,QAAM,YAAY,IAAI,KAAK,KAAK;AAChC,QAAM,OAAO,KAAK,SAAS;AAC3B,WAASC,KAAI,GAAGA,MAAK,OAAOA,MAAK;AAC/B,UAAMC,KAAID,KAAI;AACd,UAAME,KAAI,KAAKD,KAAI;AACnB,UAAME,KAAI,OAAO,YAAY,KAAK,IAAI,aAAaD,KAAI,GAAG;AAC1D,WAAO,KAAK;AAAA,MACV,GAAAA;AAAA,MACA,GAAAC;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,OAAO,4BAA4B,4BAA4B;AAC/D,SAAS,qBAAqB,SAAS,SAAS,QAAQ,WAAW,YAAY,UAAU;AACvF,QAAM,SAAS,CAAC;AAChB,QAAM,gBAAgB,aAAa,KAAK,KAAK;AAC7C,QAAM,cAAc,WAAW,KAAK,KAAK;AACzC,QAAM,aAAa,cAAc;AACjC,QAAM,YAAY,cAAc,YAAY;AAC5C,WAASH,KAAI,GAAGA,KAAI,WAAWA,MAAK;AAClC,UAAM,QAAQ,gBAAgBA,KAAI;AAClC,UAAME,KAAI,UAAU,SAAS,KAAK,IAAI,KAAK;AAC3C,UAAMC,KAAI,UAAU,SAAS,KAAK,IAAI,KAAK;AAC3C,WAAO,KAAK;AAAA,MACV,GAAG,CAACD;AAAA,MACJ,GAAG,CAACC;AAAA,IACN,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,OAAO,sBAAsB,sBAAsB;AAOnD,IAAI,gBAA+B,OAAO,CAAC,MAAM,UAAU;AACzD,MAAID,KAAI,KAAK;AACb,MAAIC,KAAI,KAAK;AACb,MAAI,KAAK,MAAM,IAAID;AACnB,MAAI,KAAK,MAAM,IAAIC;AACnB,MAAIC,KAAI,KAAK,QAAQ;AACrB,MAAIC,KAAI,KAAK,SAAS;AACtB,MAAI,IAAI;AACR,MAAI,KAAK,IAAI,EAAE,IAAID,KAAI,KAAK,IAAI,EAAE,IAAIC,IAAG;AACvC,QAAI,KAAK,GAAG;AACV,MAAAA,KAAI,CAACA;AAAA,IACP;AACA,SAAK,OAAO,IAAI,IAAIA,KAAI,KAAK;AAC7B,SAAKA;AAAA,EACP,OAAO;AACL,QAAI,KAAK,GAAG;AACV,MAAAD,KAAI,CAACA;AAAA,IACP;AACA,SAAKA;AACL,SAAK,OAAO,IAAI,IAAIA,KAAI,KAAK;AAAA,EAC/B;AACA,SAAO;AAAA,IACL,GAAGF,KAAI;AAAA,IACP,GAAGC,KAAI;AAAA,EACT;AACF,GAAG,eAAe;AAClB,IAAI,yBAAyB;AAI7B,SAAS,WAAW,KAAK,SAAS;AAChC,MAAI,SAAS;AACX,QAAI,KAAK,SAAS,OAAO;AAAA,EAC3B;AACF;AACA,OAAO,YAAY,YAAY;AAC/B,SAAe,aAAa,MAAM;AAAA;AAChC,UAAM,KAAK,eAAQ,SAAS,gBAAgB,8BAA8B,eAAe,CAAC;AAC1F,UAAM,MAAM,GAAG,OAAO,WAAW;AACjC,QAAI,QAAQ,KAAK;AACjB,QAAI,KAAK,SAAS,SAAS,KAAK,KAAK,GAAG;AACtC,cAAQ,MAAM,YAAY,KAAK,MAAM,QAAQ,eAAe,gBAAgB,IAAI,GAAG,WAAW,CAAC;AAAA,IACjG;AACA,UAAM,aAAa,KAAK,SAAS,cAAc;AAC/C,QAAI,KAAK,kBAAkB,aAAa,QAAQ,KAAK,aAAa,YAAY,KAAK,aAAa,MAAM;AAAA,IAEtG,MAAM,QAAQ,SAAS;AACvB,eAAW,KAAK,KAAK,UAAU;AAC/B,QAAI,MAAM,WAAW,cAAc;AACnC,QAAI,MAAM,iBAAiB,KAAK;AAChC,QAAI,MAAM,eAAe,QAAQ;AACjC,QAAI,KAAK,SAAS,8BAA8B;AAChD,WAAO,GAAG,KAAK;AAAA,EACjB;AAAA;AACA,OAAO,cAAc,cAAc;AACnC,IAAI,cAA6B,OAAO,CAAO,aAAa,OAAO,SAAS,WAAW;AACrF,MAAI,aAAa,eAAe;AAChC,MAAI,OAAO,eAAe,UAAU;AAClC,iBAAa,WAAW,CAAC;AAAA,EAC3B;AACA,MAAI,SAAS,WAAW,EAAE,UAAU,UAAU,GAAG;AAC/C,iBAAa,WAAW,QAAQ,WAAW,QAAQ;AACnD,QAAI,KAAK,eAAe,UAAU;AAClC,UAAM,OAAO;AAAA,MACX;AAAA,MACA,OAAO,eAAe,UAAU,EAAE,QAAQ,wBAAwB,CAAAG,OAAK,aAAaA,GAAE,QAAQ,KAAK,GAAG,CAAC,QAAQ;AAAA,MAC/G,YAAY,QAAQ,MAAM,QAAQ,SAAS,QAAQ,IAAI;AAAA,IACzD;AACA,QAAI,aAAa,MAAM,aAAa,IAAI;AACxC,WAAO;AAAA,EACT,OAAO;AACL,UAAM,WAAW,SAAS,gBAAgB,8BAA8B,MAAM;AAC9E,aAAS,aAAa,SAAS,MAAM,QAAQ,UAAU,OAAO,CAAC;AAC/D,QAAI,OAAO,CAAC;AACZ,QAAI,OAAO,eAAe,UAAU;AAClC,aAAO,WAAW,MAAM,qBAAqB;AAAA,IAC/C,WAAW,MAAM,QAAQ,UAAU,GAAG;AACpC,aAAO;AAAA,IACT,OAAO;AACL,aAAO,CAAC;AAAA,IACV;AACA,eAAW,OAAO,MAAM;AACtB,YAAM,QAAQ,SAAS,gBAAgB,8BAA8B,OAAO;AAC5E,YAAM,eAAe,wCAAwC,aAAa,UAAU;AACpF,YAAM,aAAa,MAAM,KAAK;AAC9B,YAAM,aAAa,KAAK,GAAG;AAC3B,UAAI,SAAS;AACX,cAAM,aAAa,SAAS,WAAW;AAAA,MACzC,OAAO;AACL,cAAM,aAAa,SAAS,KAAK;AAAA,MACnC;AACA,YAAM,cAAc,IAAI,KAAK;AAC7B,eAAS,YAAY,KAAK;AAAA,IAC5B;AACA,WAAO;AAAA,EACT;AACF,IAAG,aAAa;AAChB,IAAI,sBAAsB;AAG1B,IAAI,yBAAwC,OAAO,CAACJ,IAAGC,IAAG,YAAY,aAAa,WAAW;AAAA,EAAC;AAAA,EAAKD,KAAI;AAAA,EAAQC;AAAA;AAAA,EAEhH;AAAA,EAAKD,KAAI,aAAa;AAAA;AAAA,EAEtB;AAAA,EAAK;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAG;AAAA,EAAG;AAAA,EAAGA,KAAI;AAAA,EAAYC,KAAI;AAAA;AAAA,EAElD;AAAA,EAAKA,KAAI,cAAc;AAAA;AAAA,EAEvB;AAAA,EAAK;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAG;AAAA,EAAG;AAAA,EAAGD,KAAI,aAAa;AAAA,EAAQC,KAAI;AAAA;AAAA,EAE3D;AAAA,EAAKD,KAAI;AAAA;AAAA,EAET;AAAA,EAAK;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAG;AAAA,EAAG;AAAA,EAAGA;AAAA,EAAGC,KAAI,cAAc;AAAA;AAAA,EAEnD;AAAA,EAAKA,KAAI;AAAA;AAAA,EAET;AAAA,EAAK;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAG;AAAA,EAAG;AAAA,EAAGD,KAAI;AAAA,EAAQC;AAAA;AAAA,EAE1C;AAAA;AAEA,EAAE,KAAK,GAAG,GAAG,wBAAwB;AAGrC,IAAI,iBAAgC,OAAO,WAAS;AAClD,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,WAAW;AACf,SAAO;AAAA,IACL,MAAM;AAAA,IACN,cAAc;AAAA;AAAA,IAEd,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,MAAM;AAAA,EACR;AACF,GAAG,gBAAgB;AACnB,IAAI,gBAA+B,OAAO,UAAQ;AAChD,QAAM,YAAY,WAAW,CAAC,GAAI,KAAK,qBAAqB,CAAC,GAAI,GAAI,KAAK,aAAa,CAAC,CAAE,CAAC;AAC3F,SAAO;AAAA,IACL;AAAA,IACA,aAAa,CAAC,GAAG,SAAS;AAAA,EAC5B;AACF,GAAG,eAAe;AAClB,IAAI,aAA4B,OAAO,YAAU;AAC/C,QAAM,WAA0B,oBAAI,IAAI;AACxC,SAAO,QAAQ,WAAS;AACtB,UAAM,CAAC,KAAK,KAAK,IAAI,MAAM,MAAM,GAAG;AACpC,aAAS,IAAI,IAAI,KAAK,GAAG,OAAO,KAAK,CAAC;AAAA,EACxC,CAAC;AACD,SAAO;AACT,GAAG,YAAY;AACf,IAAI,eAA8B,OAAO,SAAO;AAC9C,SAAO,QAAQ,WAAW,QAAQ,eAAe,QAAQ,iBAAiB,QAAQ,iBAAiB,QAAQ,gBAAgB,QAAQ,qBAAqB,QAAQ,gBAAgB,QAAQ,oBAAoB,QAAQ,iBAAiB,QAAQ,oBAAoB,QAAQ,kBAAkB,QAAQ,iBAAiB,QAAQ,mBAAmB,QAAQ,iBAAiB,QAAQ,eAAe,QAAQ,gBAAgB,QAAQ,mBAAmB,QAAQ;AAC5b,GAAG,cAAc;AACjB,IAAI,gBAA+B,OAAO,UAAQ;AAChD,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,cAAc,IAAI;AACtB,QAAM,cAAc,CAAC;AACrB,QAAM,aAAa,CAAC;AACpB,QAAM,eAAe,CAAC;AACtB,QAAM,mBAAmB,CAAC;AAC1B,cAAY,QAAQ,WAAS;AAC3B,UAAM,MAAM,MAAM,CAAC;AACnB,QAAI,aAAa,GAAG,GAAG;AACrB,kBAAY,KAAK,MAAM,KAAK,GAAG,IAAI,aAAa;AAAA,IAClD,OAAO;AACL,iBAAW,KAAK,MAAM,KAAK,GAAG,IAAI,aAAa;AAC/C,UAAI,IAAI,SAAS,QAAQ,GAAG;AAC1B,qBAAa,KAAK,MAAM,KAAK,GAAG,IAAI,aAAa;AAAA,MACnD;AACA,UAAI,QAAQ,QAAQ;AAClB,yBAAiB,KAAK,MAAM,KAAK,GAAG,IAAI,aAAa;AAAA,MACvD;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO;AAAA,IACL,aAAa,YAAY,KAAK,GAAG;AAAA,IACjC,YAAY,WAAW,KAAK,GAAG;AAAA,IAC/B;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF,GAAG,eAAe;AAClB,IAAI,oBAAmC,OAAO,CAAC,MAAM,YAAY;AAC/D,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,WAAW;AACf,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,cAAc,IAAI;AACtB,QAAM,SAAS,OAAO,OAAO;AAAA,IAC3B,WAAW;AAAA,IACX,MAAM,UAAU,IAAI,MAAM,KAAK;AAAA,IAC/B,WAAW;AAAA;AAAA,IAEX,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,QAAQ,UAAU,IAAI,QAAQ,KAAK;AAAA,IACnC,MAAM;AAAA,IACN,aAAa,UAAU,IAAI,cAAc,GAAG,QAAQ,MAAM,EAAE,KAAK;AAAA,IACjE,cAAc,CAAC,GAAG,CAAC;AAAA,EACrB,GAAG,OAAO;AACV,SAAO;AACT,GAAG,mBAAmB;AAGtB,IAAI,OAAsB,OAAO,CAAO,QAAQ,SAAS;AACvD,MAAI,KAAK,+BAA+B,KAAK,IAAI,IAAI;AACrD,QAAM,aAAa,WAAW;AAC9B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,cAAc,IAAI;AACtB,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,aAAa,KAAK,UAAU,EAAE,KAAK,MAAM,KAAK,EAAE,EAAE,KAAK,aAAa,KAAK,IAAI;AAC/H,QAAM,gBAAgB,SAAS,WAAW,UAAU,UAAU;AAC9D,QAAM,UAAU,SAAS,OAAO,GAAG,EAAE,KAAK,SAAS,gBAAgB;AACnE,QAAM,QAAQ,MAAM,WAAW,SAAS,KAAK,OAAO;AAAA,IAClD,OAAO,KAAK;AAAA,IACZ;AAAA,IACA,QAAQ;AAAA,EACV,CAAC;AACD,MAAI,OAAO,MAAM,QAAQ;AACzB,MAAI,SAAS,WAAW,UAAU,UAAU,GAAG;AAC7C,UAAM,MAAM,MAAM,SAAS,CAAC;AAC5B,UAAM,KAAK,eAAQ,KAAK;AACxB,WAAO,IAAI,sBAAsB;AACjC,OAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,OAAG,KAAK,UAAU,KAAK,MAAM;AAAA,EAC/B;AACA,QAAM,QAAQ,KAAK,SAAS,KAAK,QAAQ,KAAK,UAAU,KAAK,QAAQ,KAAK,UAAU,KAAK;AACzF,MAAI,KAAK,SAAS,KAAK,QAAQ,KAAK,SAAS;AAC3C,SAAK,QAAQ,QAAQ,KAAK,SAAS,IAAI,KAAK;AAAA,EAC9C,OAAO;AACL,SAAK,OAAO,CAAC,KAAK;AAAA,EACpB;AACA,QAAM,SAAS,KAAK;AACpB,QAAMD,KAAI,KAAK,IAAI,QAAQ;AAC3B,QAAMC,KAAI,KAAK,IAAI,SAAS;AAC5B,MAAI,MAAM,SAAS,MAAM,KAAK,UAAU,IAAI,CAAC;AAC7C,MAAI;AACJ,MAAI,KAAK,SAAS,aAAa;AAC7B,UAAM,KAAK,GAAM,IAAI,QAAQ;AAC7B,UAAM,UAAU,kBAAkB,MAAM;AAAA,MACtC,WAAW;AAAA,MACX,MAAM;AAAA;AAAA,MAEN,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AACD,UAAM,YAAY,GAAG,KAAK,uBAAuBD,IAAGC,IAAG,OAAO,QAAQ,CAAC,GAAG,OAAO;AACjF,YAAQ,SAAS,OAAO,MAAM;AAC5B,UAAI,MAAM,yBAAyB,SAAS;AAC5C,aAAO;AAAA,IACT,GAAG,cAAc;AACjB,UAAM,OAAO,mBAAmB,EAAE,KAAK,SAAS,aAAa,KAAK,GAAG,CAAC;AACtE,UAAM,OAAO,MAAM,EAAE,KAAK,SAAS,iBAAiB,KAAK,GAAG,EAAE,QAAQ,QAAQ,QAAQ,CAAC;AAAA,EACzF,OAAO;AACL,YAAQ,SAAS,OAAO,QAAQ,cAAc;AAC9C,UAAM,KAAK,SAAS,UAAU,EAAE,KAAK,MAAM,KAAK,EAAE,EAAE,KAAK,MAAM,KAAK,EAAE,EAAE,KAAK,KAAKD,EAAC,EAAE,KAAK,KAAKC,EAAC,EAAE,KAAK,SAAS,KAAK,EAAE,KAAK,UAAU,MAAM;AAAA,EAC9I;AACA,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,wBAAwB,UAAU;AACtC,UAAQ;AAAA,IAAK;AAAA;AAAA,IAEb,aAAa,KAAK,IAAI,KAAK,QAAQ,CAAC,KAAK,KAAK,IAAI,KAAK,SAAS,IAAI,sBAAsB;AAAA,EAAG;AAC7F,MAAI,aAAa;AACf,UAAM,OAAO,QAAQ,OAAO,MAAM;AAClC,QAAI,MAAM;AACR,WAAK,KAAK,SAAS,WAAW;AAAA,IAChC;AAAA,EACF;AACA,QAAM,UAAU,MAAM,KAAK,EAAE,QAAQ;AACrC,OAAK,UAAU;AACf,OAAK,QAAQ,QAAQ;AACrB,OAAK,SAAS,QAAQ;AACtB,OAAK,UAAU,KAAK,SAAS,KAAK,UAAU;AAC5C,OAAK,YAAY,SAAU,OAAO;AAChC,WAAO,uBAAuB,MAAM,KAAK;AAAA,EAC3C;AACA,SAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,EACb;AACF,IAAG,MAAM;AACT,IAAI,YAA2B,OAAO,CAAC,QAAQ,SAAS;AACtD,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,cAAc,EAAE,KAAK,MAAM,KAAK,EAAE;AACpF,QAAM,QAAQ,SAAS,OAAO,QAAQ,cAAc;AACpD,QAAM,UAAU,IAAI,KAAK;AACzB,QAAM,cAAc,UAAU;AAC9B,QAAM,KAAK,MAAM,KAAK,EAAE,EAAE,KAAK,MAAM,KAAK,EAAE,EAAE,KAAK,KAAK,KAAK,IAAI,KAAK,QAAQ,IAAI,WAAW,EAAE,KAAK,KAAK,KAAK,IAAI,KAAK,SAAS,IAAI,WAAW,EAAE,KAAK,SAAS,KAAK,QAAQ,OAAO,EAAE,KAAK,UAAU,KAAK,SAAS,OAAO,EAAE,KAAK,QAAQ,MAAM;AAC9O,QAAM,UAAU,MAAM,KAAK,EAAE,QAAQ;AACrC,OAAK,QAAQ,QAAQ;AACrB,OAAK,SAAS,QAAQ;AACtB,OAAK,YAAY,SAAU,OAAO;AAChC,WAAO,uBAAuB,MAAM,KAAK;AAAA,EAC3C;AACA,SAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,MACT,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AAAA,EACF;AACF,GAAG,WAAW;AACd,IAAI,mBAAkC,OAAO,CAAO,QAAQ,SAAS;AACnE,QAAM,aAAa,WAAW;AAC9B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,KAAK,UAAU,EAAE,KAAK,MAAM,KAAK,EAAE,EAAE,KAAK,WAAW,KAAK,EAAE,EAAE,KAAK,aAAa,KAAK,IAAI;AAC3I,QAAM,aAAa,SAAS,OAAO,KAAK,cAAc;AACtD,QAAM,QAAQ,SAAS,OAAO,GAAG,EAAE,KAAK,SAAS,eAAe;AAChE,MAAI,YAAY,SAAS,OAAO,MAAM;AACtC,QAAM,QAAQ,MAAM,KAAK,EAAE,YAAY,MAAM,oBAAoB,KAAK,OAAO,KAAK,YAAY,QAAQ,IAAI,CAAC;AAC3G,MAAI,OAAO,MAAM,QAAQ;AACzB,MAAI,SAAS,WAAW,UAAU,UAAU,GAAG;AAC7C,UAAM,MAAM,MAAM,SAAS,CAAC;AAC5B,UAAM,KAAK,eAAQ,KAAK;AACxB,WAAO,IAAI,sBAAsB;AACjC,OAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,OAAG,KAAK,UAAU,KAAK,MAAM;AAAA,EAC/B;AACA,QAAM,UAAU,IAAI,KAAK;AACzB,QAAM,cAAc,UAAU;AAC9B,QAAM,SAAS,KAAK,SAAS,KAAK,QAAQ,KAAK,UAAU,KAAK,QAAQ,KAAK,UAAU,KAAK,SAAS;AACnG,MAAI,KAAK,SAAS,KAAK,QAAQ,KAAK,SAAS;AAC3C,SAAK,QAAQ,QAAQ,KAAK,SAAS,IAAI,KAAK;AAAA,EAC9C,OAAO;AACL,SAAK,OAAO,CAAC,KAAK;AAAA,EACpB;AACA,QAAM,SAAS,KAAK,SAAS;AAC7B,QAAM,cAAc,KAAK,SAAS,UAAU,KAAK,SAAS;AAC1D,QAAMD,KAAI,KAAK,IAAI,QAAQ;AAC3B,QAAMC,KAAI,KAAK,IAAI,SAAS;AAC5B,OAAK,QAAQ;AACb,QAAM,SAAS,KAAK,IAAI,KAAK,SAAS,IAAI,cAAc,KAAK,SAAS;AACtE,MAAI;AACJ,MAAI,KAAK,SAAS,aAAa;AAC7B,UAAM,QAAQ,KAAK,WAAW,SAAS,0BAA0B;AACjE,UAAM,KAAK,GAAM,IAAI,QAAQ;AAC7B,UAAM,iBAAiB,KAAK,MAAM,KAAK,KAAK,GAAG,KAAK,uBAAuBD,IAAGC,IAAG,OAAO,QAAQ,EAAE,GAAG;AAAA,MACnG,WAAW;AAAA,MACX,MAAM;AAAA,MACN,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,MAAM;AAAA,IACR,CAAC,IAAI,GAAG,UAAUD,IAAGC,IAAG,OAAO,QAAQ;AAAA,MACrC,MAAM;AAAA,IACR,CAAC;AACD,YAAQ,SAAS,OAAO,MAAM,gBAAgB,cAAc;AAC5D,UAAM,iBAAiB,GAAG,UAAUD,IAAG,QAAQ,OAAO,aAAa;AAAA,MACjE,MAAM,QAAQ,gBAAgB;AAAA,MAC9B,WAAW,QAAQ,YAAY;AAAA,MAC/B,QAAQ;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AACD,YAAQ,SAAS,OAAO,MAAM,gBAAgB,cAAc;AAC5D,gBAAY,SAAS,OAAO,MAAM,cAAc;AAAA,EAClD,OAAO;AACL,YAAQ,WAAW,OAAO,QAAQ,cAAc;AAChD,UAAM,iBAAiB;AACvB,UAAM,KAAK,SAAS,cAAc,EAAE,KAAK,KAAKA,EAAC,EAAE,KAAK,KAAKC,EAAC,EAAE,KAAK,SAAS,KAAK,EAAE,KAAK,UAAU,MAAM,EAAE,KAAK,aAAa,KAAK,IAAI;AACrI,cAAU,KAAK,SAAS,OAAO,EAAE,KAAK,KAAKD,EAAC,EAAE,KAAK,KAAK,MAAM,EAAE,KAAK,SAAS,KAAK,EAAE,KAAK,UAAU,WAAW;AAAA,EACjH;AACA,QAAM,KAAK,aAAa,aAAa,KAAK,IAAI,KAAK,QAAQ,CAAC,KAAKC,KAAI,KAAK,SAAS,WAAW,UAAU,UAAU,IAAI,IAAI,EAAE,GAAG;AAC/H,QAAM,UAAU,MAAM,KAAK,EAAE,QAAQ;AACrC,OAAK,SAAS,QAAQ;AACtB,OAAK,UAAU;AACf,OAAK,UAAU,KAAK,SAAS,KAAK,UAAU;AAC5C,OAAK,YAAY;AACjB,OAAK,YAAY,SAAU,OAAO;AAChC,WAAO,uBAAuB,MAAM,KAAK;AAAA,EAC3C;AACA,SAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,EACb;AACF,IAAG,kBAAkB;AACrB,IAAI,gBAA+B,OAAO,CAAO,QAAQ,SAAS;AAChE,MAAI,KAAK,+BAA+B,KAAK,IAAI,IAAI;AACrD,QAAM,aAAa,WAAW;AAC9B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,cAAc,IAAI;AACtB,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,aAAa,KAAK,UAAU,EAAE,KAAK,MAAM,KAAK,EAAE,EAAE,KAAK,aAAa,KAAK,IAAI;AAC/H,QAAM,gBAAgB,SAAS,WAAW,UAAU,UAAU;AAC9D,QAAM,UAAU,SAAS,OAAO,GAAG,EAAE,KAAK,SAAS,gBAAgB;AACnE,QAAM,QAAQ,MAAM,WAAW,SAAS,KAAK,OAAO;AAAA,IAClD,OAAO,KAAK;AAAA,IACZ;AAAA,IACA,QAAQ;AAAA,IACR,OAAO,KAAK;AAAA,EACd,CAAC;AACD,MAAI,OAAO,MAAM,QAAQ;AACzB,MAAI,SAAS,WAAW,UAAU,UAAU,GAAG;AAC7C,UAAM,MAAM,MAAM,SAAS,CAAC;AAC5B,UAAM,KAAK,eAAQ,KAAK;AACxB,WAAO,IAAI,sBAAsB;AACjC,OAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,OAAG,KAAK,UAAU,KAAK,MAAM;AAAA,EAC/B;AACA,QAAM,QAAQ,KAAK,SAAS,KAAK,QAAQ,KAAK,UAAU,KAAK,QAAQ,KAAK,UAAU,KAAK;AACzF,MAAI,KAAK,SAAS,KAAK,QAAQ,KAAK,SAAS;AAC3C,SAAK,QAAQ,QAAQ,KAAK,SAAS,IAAI,KAAK;AAAA,EAC9C,OAAO;AACL,SAAK,OAAO,CAAC,KAAK;AAAA,EACpB;AACA,QAAM,SAAS,KAAK;AACpB,QAAMD,KAAI,KAAK,IAAI,QAAQ;AAC3B,QAAMC,KAAI,KAAK,IAAI,SAAS;AAC5B,MAAI,MAAM,SAAS,MAAM,KAAK,UAAU,IAAI,CAAC;AAC7C,MAAI;AACJ,MAAI,KAAK,SAAS,aAAa;AAC7B,UAAM,KAAK,GAAM,IAAI,QAAQ;AAC7B,UAAM,UAAU,kBAAkB,MAAM;AAAA,MACtC,WAAW;AAAA,MACX,MAAM;AAAA;AAAA,MAEN,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AACD,UAAM,YAAY,GAAG,KAAK,uBAAuBD,IAAGC,IAAG,OAAO,QAAQ,KAAK,EAAE,GAAG,OAAO;AACvF,YAAQ,SAAS,OAAO,MAAM;AAC5B,UAAI,MAAM,yBAAyB,SAAS;AAC5C,aAAO;AAAA,IACT,GAAG,cAAc;AACjB,UAAM,OAAO,mBAAmB,EAAE,KAAK,SAAS,aAAa,KAAK,GAAG,CAAC;AACtE,UAAM,OAAO,MAAM,EAAE,KAAK,SAAS,iBAAiB,KAAK,GAAG,EAAE,QAAQ,QAAQ,QAAQ,CAAC;AAAA,EACzF,OAAO;AACL,YAAQ,SAAS,OAAO,QAAQ,cAAc;AAC9C,UAAM,KAAK,SAAS,UAAU,EAAE,KAAK,MAAM,KAAK,EAAE,EAAE,KAAK,MAAM,KAAK,EAAE,EAAE,KAAK,KAAKD,EAAC,EAAE,KAAK,KAAKC,EAAC,EAAE,KAAK,SAAS,KAAK,EAAE,KAAK,UAAU,MAAM;AAAA,EAC9I;AACA,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,wBAAwB,UAAU;AACtC,UAAQ;AAAA,IAAK;AAAA;AAAA,IAEb,aAAa,KAAK,IAAI,KAAK,QAAQ,CAAC,KAAK,KAAK,IAAI,KAAK,SAAS,IAAI,sBAAsB;AAAA,EAAG;AAC7F,MAAI,aAAa;AACf,UAAM,OAAO,QAAQ,OAAO,MAAM;AAClC,QAAI,MAAM;AACR,WAAK,KAAK,SAAS,WAAW;AAAA,IAChC;AAAA,EACF;AACA,QAAM,UAAU,MAAM,KAAK,EAAE,QAAQ;AACrC,OAAK,UAAU;AACf,OAAK,QAAQ,QAAQ;AACrB,OAAK,SAAS,QAAQ;AACtB,OAAK,UAAU,KAAK,SAAS,KAAK,UAAU;AAC5C,OAAK,YAAY,SAAU,OAAO;AAChC,WAAO,uBAAuB,MAAM,KAAK;AAAA,EAC3C;AACA,SAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,EACb;AACF,IAAG,eAAe;AAClB,IAAI,UAAyB,OAAO,CAAC,QAAQ,SAAS;AACpD,QAAM,aAAa,WAAW;AAC9B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,KAAK,UAAU,EAAE,KAAK,MAAM,KAAK,EAAE,EAAE,KAAK,aAAa,KAAK,IAAI;AAClH,QAAM,aAAa,SAAS,OAAO,KAAK,cAAc;AACtD,QAAM,UAAU,IAAI,KAAK;AACzB,QAAM,QAAQ,KAAK,QAAQ;AAC3B,OAAK,OAAO,CAAC,KAAK;AAClB,QAAM,SAAS,KAAK,SAAS;AAC7B,QAAMD,KAAI,KAAK,IAAI,QAAQ;AAC3B,QAAMC,KAAI,KAAK,IAAI,SAAS;AAC5B,OAAK,QAAQ;AACb,MAAI;AACJ,MAAI,KAAK,SAAS,aAAa;AAC7B,UAAM,KAAK,GAAM,IAAI,QAAQ;AAC7B,UAAM,iBAAiB,GAAG,UAAUD,IAAGC,IAAG,OAAO,QAAQ;AAAA,MACvD,MAAM;AAAA,MACN,WAAW;AAAA,MACX,gBAAgB,CAAC,CAAC;AAAA,MAClB,QAAQ;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AACD,YAAQ,SAAS,OAAO,MAAM,gBAAgB,cAAc;AAAA,EAC9D,OAAO;AACL,YAAQ,WAAW,OAAO,QAAQ,cAAc;AAChD,UAAM,iBAAiB;AACvB,UAAM,KAAK,SAAS,cAAc,EAAE,KAAK,KAAKD,EAAC,EAAE,KAAK,KAAKC,EAAC,EAAE,KAAK,SAAS,KAAK,EAAE,KAAK,UAAU,MAAM,EAAE,KAAK,aAAa,KAAK,IAAI;AAAA,EACvI;AACA,QAAM,UAAU,MAAM,KAAK,EAAE,QAAQ;AACrC,OAAK,SAAS,QAAQ;AACtB,OAAK,UAAU;AACf,OAAK,UAAU;AACf,OAAK,YAAY,SAAU,OAAO;AAChC,WAAO,uBAAuB,MAAM,KAAK;AAAA,EAC3C;AACA,SAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW,CAAC;AAAA,EACd;AACF,GAAG,SAAS;AACZ,IAAI,aAAa;AACjB,IAAI,SAAS;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAI,eAA8B,oBAAI,IAAI;AAC1C,IAAI,gBAA+B,OAAO,CAAO,MAAM,SAAS;AAC9D,QAAM,QAAQ,KAAK,SAAS;AAC5B,QAAM,UAAU,MAAM,OAAO,KAAK,EAAE,MAAM,IAAI;AAC9C,eAAa,IAAI,KAAK,IAAI,OAAO;AACjC,SAAO;AACT,IAAG,eAAe;AAClB,IAAI,QAAuB,OAAO,MAAM;AACtC,iBAA8B,oBAAI,IAAI;AACxC,GAAG,OAAO;AAGV,SAAS,cAAc,MAAM,OAAO;AAClC,SAAO,KAAK,UAAU,KAAK;AAC7B;AACA,OAAO,eAAe,eAAe;AACrC,IAAI,yBAAyB;AAG7B,SAAS,iBAAiB,MAAM,IAAI,IAAI,OAAO;AAC7C,MAAI,KAAK,KAAK;AACd,MAAI,KAAK,KAAK;AACd,MAAI,KAAK,KAAK,MAAM;AACpB,MAAI,KAAK,KAAK,MAAM;AACpB,MAAI,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AACzD,MAAI,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,GAAG;AACpC,MAAI,MAAM,IAAI,IAAI;AAChB,SAAK,CAAC;AAAA,EACR;AACA,MAAI,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,GAAG;AACpC,MAAI,MAAM,IAAI,IAAI;AAChB,SAAK,CAAC;AAAA,EACR;AACA,SAAO;AAAA,IACL,GAAG,KAAK;AAAA,IACR,GAAG,KAAK;AAAA,EACV;AACF;AACA,OAAO,kBAAkB,kBAAkB;AAC3C,IAAI,4BAA4B;AAGhC,SAAS,gBAAgB,MAAM,IAAI,OAAO;AACxC,SAAO,0BAA0B,MAAM,IAAI,IAAI,KAAK;AACtD;AACA,OAAO,iBAAiB,iBAAiB;AACzC,IAAI,2BAA2B;AAG/B,SAAS,cAAc,IAAI,IAAI,IAAI,IAAI;AACrC,MAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACxB,MAAI,IAAI,IAAI,IAAI;AAChB,MAAI,OAAO,QAAQ;AACnB,MAAID,IAAGC;AACP,OAAK,GAAG,IAAI,GAAG;AACf,OAAK,GAAG,IAAI,GAAG;AACf,OAAK,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG;AAC7B,OAAK,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI;AAC7B,OAAK,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI;AAC7B,MAAI,OAAO,KAAK,OAAO,KAAK,SAAS,IAAI,EAAE,GAAG;AAC5C;AAAA,EACF;AACA,OAAK,GAAG,IAAI,GAAG;AACf,OAAK,GAAG,IAAI,GAAG;AACf,OAAK,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG;AAC7B,OAAK,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI;AAC7B,OAAK,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI;AAC7B,MAAI,OAAO,KAAK,OAAO,KAAK,SAAS,IAAI,EAAE,GAAG;AAC5C;AAAA,EACF;AACA,UAAQ,KAAK,KAAK,KAAK;AACvB,MAAI,UAAU,GAAG;AACf;AAAA,EACF;AACA,WAAS,KAAK,IAAI,QAAQ,CAAC;AAC3B,QAAM,KAAK,KAAK,KAAK;AACrB,EAAAD,KAAI,MAAM,KAAK,MAAM,UAAU,SAAS,MAAM,UAAU;AACxD,QAAM,KAAK,KAAK,KAAK;AACrB,EAAAC,KAAI,MAAM,KAAK,MAAM,UAAU,SAAS,MAAM,UAAU;AACxD,SAAO;AAAA,IACL,GAAAD;AAAA,IACA,GAAAC;AAAA,EACF;AACF;AACA,OAAO,eAAe,eAAe;AACrC,SAAS,SAAS,IAAI,IAAI;AACxB,SAAO,KAAK,KAAK;AACnB;AACA,OAAO,UAAU,UAAU;AAC3B,IAAI,yBAAyB;AAG7B,SAAS,iBAAiB,MAAM,YAAY,OAAO;AACjD,MAAI,KAAK,KAAK;AACd,MAAI,KAAK,KAAK;AACd,MAAI,gBAAgB,CAAC;AACrB,MAAI,OAAO,OAAO;AAClB,MAAI,OAAO,OAAO;AAClB,MAAI,OAAO,WAAW,YAAY,YAAY;AAC5C,eAAW,QAAQ,SAAU,OAAO;AAClC,aAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,aAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAAA,IAC/B,CAAC;AAAA,EACH,OAAO;AACL,WAAO,KAAK,IAAI,MAAM,WAAW,CAAC;AAClC,WAAO,KAAK,IAAI,MAAM,WAAW,CAAC;AAAA,EACpC;AACA,MAAI,OAAO,KAAK,KAAK,QAAQ,IAAI;AACjC,MAAI,MAAM,KAAK,KAAK,SAAS,IAAI;AACjC,WAASH,KAAI,GAAGA,KAAI,WAAW,QAAQA,MAAK;AAC1C,QAAI,KAAK,WAAWA,EAAC;AACrB,QAAI,KAAK,WAAWA,KAAI,WAAW,SAAS,IAAIA,KAAI,IAAI,CAAC;AACzD,QAAI,YAAY,uBAAuB,MAAM,OAAO;AAAA,MAClD,GAAG,OAAO,GAAG;AAAA,MACb,GAAG,MAAM,GAAG;AAAA,IACd,GAAG;AAAA,MACD,GAAG,OAAO,GAAG;AAAA,MACb,GAAG,MAAM,GAAG;AAAA,IACd,CAAC;AACD,QAAI,WAAW;AACb,oBAAc,KAAK,SAAS;AAAA,IAC9B;AAAA,EACF;AACA,MAAI,CAAC,cAAc,QAAQ;AACzB,WAAO;AAAA,EACT;AACA,MAAI,cAAc,SAAS,GAAG;AAC5B,kBAAc,KAAK,SAAUD,IAAGQ,IAAG;AACjC,UAAI,MAAMR,GAAE,IAAI,MAAM;AACtB,UAAI,MAAMA,GAAE,IAAI,MAAM;AACtB,UAAI,QAAQ,KAAK,KAAK,MAAM,MAAM,MAAM,GAAG;AAC3C,UAAI,MAAMQ,GAAE,IAAI,MAAM;AACtB,UAAI,MAAMA,GAAE,IAAI,MAAM;AACtB,UAAI,QAAQ,KAAK,KAAK,MAAM,MAAM,MAAM,GAAG;AAC3C,aAAO,QAAQ,QAAQ,KAAK,UAAU,QAAQ,IAAI;AAAA,IACpD,CAAC;AAAA,EACH;AACA,SAAO,cAAc,CAAC;AACxB;AACA,OAAO,kBAAkB,kBAAkB;AAC3C,IAAI,4BAA4B;AAGhC,IAAI,oBAAoB;AAAA,EACtB,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,MAAM;AACR;AAIA,SAAS,OAAO,QAAQ,MAAM;AAC5B,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,cAAc,IAAI;AACtB,OAAK,aAAa;AAClB,QAAM,UAAU,eAAe,IAAI;AACnC,MAAI,aAAa;AACjB,MAAI,CAAC,SAAS;AACZ,iBAAa;AAAA,EACf;AACA,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,UAAU,EAAE,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AAC9F,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,KAAK,GAAO,IAAI,QAAQ;AAC9B,QAAM,UAAU,kBAAkB,MAAM;AAAA,IACtC,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,EACb,CAAC;AACD,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,YAAY,GAAG,OAAO,GAAG,GAAG,SAAS,GAAG,OAAO;AACrD,QAAM,aAAa,SAAS,OAAO,MAAM,WAAW,cAAc;AAClE,aAAW,KAAK,SAAS,QAAQ,EAAE,KAAK,SAAS,oBAAoB,SAAS,CAAC;AAC/E,mBAAiB,MAAM,UAAU;AACjC,OAAK,YAAY,SAAU,OAAO;AAChC,QAAI,KAAK,oBAAoB,MAAM,QAAQ,KAAK;AAChD,WAAO,kBAAkB,OAAO,MAAM,QAAQ,KAAK;AAAA,EACrD;AACA,SAAO;AACT;AACA,OAAO,QAAQ,QAAQ;AAIvB,SAAS,kBAAkB,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,WAAW;AAC5D,QAAM,YAAY;AAClB,QAAM,QAAQ,KAAK,MAAM;AACzB,QAAM,QAAQ,KAAK,MAAM;AACzB,QAAM,QAAQ,KAAK,MAAM,KAAK,IAAI,KAAK,EAAE;AACzC,QAAM,MAAM,KAAK,MAAM;AACvB,QAAM,MAAM,KAAK,MAAM;AACvB,QAAM,eAAe,KAAK;AAC1B,QAAM,eAAe,KAAK;AAC1B,QAAM,WAAW,KAAK,KAAK,gBAAgB,IAAI,gBAAgB,CAAC;AAChE,MAAI,WAAW,GAAG;AAChB,UAAM,IAAI,MAAM,oEAAoE;AAAA,EACtF;AACA,QAAM,uBAAuB,KAAK,KAAK,IAAI,YAAY,CAAC;AACxD,QAAM,UAAU,OAAO,uBAAuB,KAAK,KAAK,IAAI,KAAK,KAAK,YAAY,KAAK;AACvF,QAAM,UAAU,OAAO,uBAAuB,KAAK,KAAK,IAAI,KAAK,KAAK,YAAY,KAAK;AACvF,QAAM,aAAa,KAAK,OAAO,KAAK,WAAW,KAAK,KAAK,WAAW,EAAE;AACtE,QAAM,WAAW,KAAK,OAAO,KAAK,WAAW,KAAK,KAAK,WAAW,EAAE;AACpE,MAAI,aAAa,WAAW;AAC5B,MAAI,aAAa,aAAa,GAAG;AAC/B,kBAAc,IAAI,KAAK;AAAA,EACzB;AACA,MAAI,CAAC,aAAa,aAAa,GAAG;AAChC,kBAAc,IAAI,KAAK;AAAA,EACzB;AACA,QAAM,SAAS,CAAC;AAChB,WAASP,KAAI,GAAGA,KAAI,WAAWA,MAAK;AAClC,UAAMC,KAAID,MAAK,YAAY;AAC3B,UAAM,SAAS,aAAaC,KAAI;AAChC,UAAMC,KAAI,UAAU,KAAK,KAAK,IAAI,MAAM;AACxC,UAAMC,KAAI,UAAU,KAAK,KAAK,IAAI,MAAM;AACxC,WAAO,KAAK;AAAA,MACV,GAAAD;AAAA,MACA,GAAAC;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,OAAO,mBAAmB,mBAAmB;AAC7C,SAAe,WAAW,QAAQ,MAAM;AAAA;AACtC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAMC,KAAI,KAAK,QAAQ,KAAK,UAAU;AACtC,UAAMC,KAAI,KAAK,SAAS,KAAK;AAC7B,UAAM,KAAKA,KAAI;AACf,UAAM,KAAK,MAAM,MAAMA,KAAI;AAC3B,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,SAAS,CAAC;AAAA,MACd,GAAGD,KAAI;AAAA,MACP,GAAG,CAACC,KAAI;AAAA,IACV,GAAG;AAAA,MACD,GAAG,CAACD,KAAI;AAAA,MACR,GAAG,CAACC,KAAI;AAAA,IACV,GAAG,GAAG,kBAAkB,CAACD,KAAI,GAAG,CAACC,KAAI,GAAG,CAACD,KAAI,GAAGC,KAAI,GAAG,IAAI,IAAI,KAAK,GAAG;AAAA,MACrE,GAAGD,KAAI;AAAA,MACP,GAAGC,KAAI;AAAA,IACT,GAAG,GAAG,kBAAkBD,KAAI,GAAGC,KAAI,GAAGD,KAAI,GAAG,CAACC,KAAI,GAAG,IAAI,IAAI,IAAI,CAAC;AAClE,UAAM,KAAK,GAAO,IAAI,QAAQ;AAC9B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,QAAI,KAAK,SAAS,aAAa;AAC7B,cAAQ,YAAY;AACpB,cAAQ,YAAY;AAAA,IACtB;AACA,UAAM,iBAAiB,qBAAqB,MAAM;AAClD,UAAM,sBAAsB,GAAG,KAAK,gBAAgB,OAAO;AAC3D,UAAM,kBAAkB,SAAS,OAAO,MAAM,qBAAqB,cAAc;AACjF,oBAAgB,KAAK,SAAS,uBAAuB;AACrD,QAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,sBAAgB,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,IAC3D;AACA,QAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,sBAAgB,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,IAC5D;AACA,oBAAgB,KAAK,aAAa,aAAa,KAAK,CAAC,MAAM;AAC3D,qBAAiB,MAAM,eAAe;AACtC,SAAK,YAAY,SAAU,OAAO;AAChC,YAAM,MAAM,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AACzD,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,YAAY,YAAY;AAM/B,SAAS,mBAAmB,QAAQD,IAAGC,IAAG,QAAQ;AAChD,SAAO,OAAO,OAAO,WAAW,cAAc,EAAE,KAAK,UAAU,OAAO,IAAI,SAAUG,IAAG;AACrF,WAAOA,GAAE,IAAI,MAAMA,GAAE;AAAA,EACvB,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,SAAS,iBAAiB,EAAE,KAAK,aAAa,eAAe,CAACJ,KAAI,IAAI,MAAMC,KAAI,IAAI,GAAG;AAC5G;AACA,OAAO,oBAAoB,oBAAoB;AAG/C,SAAe,KAAK,QAAQ,MAAM;AAAA;AAChC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAMA,KAAI,KAAK,SAAS,KAAK;AAC7B,UAAM,UAAU;AAChB,UAAMD,KAAI,KAAK,QAAQ,KAAK,UAAU;AACtC,UAAM,OAAO;AACb,UAAM,QAAQA;AACd,UAAM,MAAM,CAACC;AACb,UAAM,SAAS;AACf,UAAM,SAAS,CAAC;AAAA,MACd,GAAG,OAAO;AAAA,MACV,GAAG;AAAA,IACL,GAAG;AAAA,MACD,GAAG;AAAA,MACH,GAAG;AAAA,IACL,GAAG;AAAA,MACD,GAAG;AAAA,MACH,GAAG;AAAA,IACL,GAAG;AAAA,MACD,GAAG;AAAA,MACH,GAAG;AAAA,IACL,GAAG;AAAA,MACD,GAAG;AAAA,MACH,GAAG,MAAM;AAAA,IACX,GAAG;AAAA,MACD,GAAG,OAAO;AAAA,MACV,GAAG;AAAA,IACL,CAAC;AACD,QAAI;AACJ,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,KAAK,SAAS,aAAa;AAC7B,YAAM,KAAK,GAAO,IAAI,QAAQ;AAC9B,YAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,YAAM,WAAW,qBAAqB,MAAM;AAC5C,YAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAC3C,gBAAU,SAAS,OAAO,MAAM,WAAW,cAAc,EAAE,KAAK,aAAa,aAAa,CAACD,KAAI,CAAC,KAAKC,KAAI,CAAC,GAAG;AAC7G,UAAI,WAAW;AACb,gBAAQ,KAAK,SAAS,SAAS;AAAA,MACjC;AAAA,IACF,OAAO;AACL,gBAAU,mBAAmB,UAAUD,IAAGC,IAAG,MAAM;AAAA,IACrD;AACA,QAAI,YAAY;AACd,cAAQ,KAAK,SAAS,UAAU;AAAA,IAClC;AACA,qBAAiB,MAAM,OAAO;AAC9B,SAAK,YAAY,SAAU,OAAO;AAChC,aAAO,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AAAA,IACtD;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,MAAM,MAAM;AAInB,SAAS,OAAO,QAAQ,MAAM;AAC5B,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,cAAc,IAAI;AACtB,OAAK,QAAQ;AACb,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,eAAe,IAAI,CAAC,EAAE,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AACxG,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAMC,KAAI,KAAK,IAAI,IAAI,KAAK,SAAS,CAAC;AACtC,QAAM,SAAS,CAAC;AAAA,IACd,GAAG;AAAA,IACH,GAAGA,KAAI;AAAA,EACT,GAAG;AAAA,IACD,GAAGA,KAAI;AAAA,IACP,GAAG;AAAA,EACL,GAAG;AAAA,IACD,GAAG;AAAA,IACH,GAAG,CAACA,KAAI;AAAA,EACV,GAAG;AAAA,IACD,GAAG,CAACA,KAAI;AAAA,IACR,GAAG;AAAA,EACL,CAAC;AACD,QAAM,KAAK,GAAO,IAAI,QAAQ;AAC9B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,aAAa,qBAAqB,MAAM;AAC9C,QAAM,YAAY,GAAG,KAAK,YAAY,OAAO;AAC7C,QAAM,cAAc,SAAS,OAAO,MAAM,WAAW,cAAc;AACnE,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,gBAAY,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACvD;AACA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,gBAAY,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACxD;AACA,OAAK,QAAQ;AACb,OAAK,SAAS;AACd,OAAK,YAAY,SAAU,OAAO;AAChC,WAAO,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AAAA,EACtD;AACA,SAAO;AACT;AACA,OAAO,QAAQ,QAAQ;AAIvB,SAAe,OAAO,QAAQ,MAAM;AAAA;AAClC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAM,SAAS,KAAK,QAAQ,IAAI;AAChC,QAAI;AACJ,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,KAAK,SAAS,aAAa;AAC7B,YAAM,KAAK,GAAO,IAAI,QAAQ;AAC9B,YAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,YAAM,YAAY,GAAG,OAAO,GAAG,GAAG,SAAS,GAAG,OAAO;AACrD,mBAAa,SAAS,OAAO,MAAM,WAAW,cAAc;AAC5D,iBAAW,KAAK,SAAS,uBAAuB,EAAE,KAAK,SAAS,oBAAoB,SAAS,CAAC;AAAA,IAChG,OAAO;AACL,mBAAa,SAAS,OAAO,UAAU,cAAc,EAAE,KAAK,SAAS,uBAAuB,EAAE,KAAK,SAAS,UAAU,EAAE,KAAK,KAAK,MAAM,EAAE,KAAK,MAAM,CAAC,EAAE,KAAK,MAAM,CAAC;AAAA,IACtK;AACA,qBAAiB,MAAM,UAAU;AACjC,SAAK,YAAY,SAAU,OAAO;AAChC,UAAI,KAAK,oBAAoB,MAAM,QAAQ,KAAK;AAChD,aAAO,kBAAkB,OAAO,MAAM,QAAQ,KAAK;AAAA,IACrD;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,QAAQ,QAAQ;AAIvB,SAAS,WAAWG,IAAG;AACrB,QAAM,UAAU,KAAK,IAAI,KAAK,KAAK,CAAC;AACpC,QAAM,UAAU,KAAK,IAAI,KAAK,KAAK,CAAC;AACpC,QAAM,aAAaA,KAAI;AACvB,QAAM,UAAU;AAAA,IACd,GAAG,aAAa,IAAI;AAAA,IACpB,GAAG,aAAa,IAAI;AAAA,EACtB;AACA,QAAM,UAAU;AAAA,IACd,GAAG,EAAE,aAAa,KAAK;AAAA,IACvB,GAAG,aAAa,IAAI;AAAA,EACtB;AACA,QAAM,UAAU;AAAA,IACd,GAAG,EAAE,aAAa,KAAK;AAAA,IACvB,GAAG,EAAE,aAAa,KAAK;AAAA,EACzB;AACA,QAAM,UAAU;AAAA,IACd,GAAG,aAAa,IAAI;AAAA,IACpB,GAAG,EAAE,aAAa,KAAK;AAAA,EACzB;AACA,SAAO,KAAK,QAAQ,CAAC,IAAI,QAAQ,CAAC,MAAM,QAAQ,CAAC,IAAI,QAAQ,CAAC;AAAA,uBACzC,QAAQ,CAAC,IAAI,QAAQ,CAAC,MAAM,QAAQ,CAAC,IAAI,QAAQ,CAAC;AACzE;AACA,OAAO,YAAY,YAAY;AAC/B,SAAS,cAAc,QAAQ,MAAM;AACnC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,cAAc,IAAI;AACtB,OAAK,aAAa;AAClB,OAAK,QAAQ;AACb,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,eAAe,IAAI,CAAC,EAAE,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AACxG,QAAM,SAAS,KAAK,IAAI,IAAI,MAAM,SAAS,CAAC;AAC5C,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,KAAK,GAAO,IAAI,QAAQ;AAC9B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,aAAa,GAAG,OAAO,GAAG,GAAG,SAAS,GAAG,OAAO;AACtD,QAAM,WAAW,WAAW,MAAM;AAClC,QAAM,WAAW,GAAG,KAAK,UAAU,OAAO;AAC1C,QAAM,iBAAiB,SAAS,OAAO,MAAM,YAAY,cAAc;AACvE,iBAAe,OAAO,MAAM,QAAQ;AACpC,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,mBAAe,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EAC1D;AACA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,mBAAe,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EAC3D;AACA,mBAAiB,MAAM,cAAc;AACrC,OAAK,YAAY,SAAU,OAAO;AAChC,QAAI,KAAK,2BAA2B,MAAM;AAAA,MACxC;AAAA,MACA;AAAA,IACF,CAAC;AACD,UAAM,MAAM,kBAAkB,OAAO,MAAM,QAAQ,KAAK;AACxD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,OAAO,eAAe,eAAe;AAIrC,SAAS,sBAAsB,SAAS,SAAS,QAAQ,YAAY,KAAK,aAAa,GAAG,WAAW,KAAK;AACxG,QAAM,SAAS,CAAC;AAChB,QAAM,gBAAgB,aAAa,KAAK,KAAK;AAC7C,QAAM,cAAc,WAAW,KAAK,KAAK;AACzC,QAAM,aAAa,cAAc;AACjC,QAAM,YAAY,cAAc,YAAY;AAC5C,WAAST,KAAI,GAAGA,KAAI,WAAWA,MAAK;AAClC,UAAM,QAAQ,gBAAgBA,KAAI;AAClC,UAAME,KAAI,UAAU,SAAS,KAAK,IAAI,KAAK;AAC3C,UAAMC,KAAI,UAAU,SAAS,KAAK,IAAI,KAAK;AAC3C,WAAO,KAAK;AAAA,MACV,GAAG,CAACD;AAAA,MACJ,GAAG,CAACC;AAAA,IACN,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,OAAO,uBAAuB,sBAAsB;AACpD,SAAe,eAAe,QAAQ,MAAM;AAAA;AAC1C,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAMC,KAAI,KAAK,SAAS,KAAK,WAAW;AACxC,UAAMC,KAAI,KAAK,UAAU,KAAK,WAAW;AACzC,UAAM,SAAS,KAAK,IAAI,GAAGA,KAAI,GAAG;AAClC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,SAAS,CAAC,GAAG,sBAAsBD,KAAI,GAAG,CAACC,KAAI,GAAG,QAAQ,IAAI,KAAK,CAAC,GAAG;AAAA,MAC3E,GAAG,CAACD,KAAI,IAAI;AAAA,MACZ,GAAG;AAAA,IACL,GAAG,GAAG,sBAAsBA,KAAI,IAAI,SAAS,GAAG,CAAC,QAAQ,QAAQ,IAAI,MAAM,IAAI,GAAG,GAAG,sBAAsBA,KAAI,IAAI,SAAS,GAAG,QAAQ,QAAQ,IAAI,KAAK,IAAI,GAAG;AAAA,MAC7J,GAAG,CAACA,KAAI,IAAI;AAAA,MACZ,GAAG,CAACC,KAAI;AAAA,IACV,GAAG,GAAG,sBAAsBD,KAAI,GAAGC,KAAI,GAAG,QAAQ,IAAI,GAAG,EAAE,CAAC;AAC5D,UAAM,aAAa,CAAC;AAAA,MAClB,GAAGD,KAAI;AAAA,MACP,GAAG,CAACC,KAAI,IAAI;AAAA,IACd,GAAG;AAAA,MACD,GAAG,CAACD,KAAI;AAAA,MACR,GAAG,CAACC,KAAI,IAAI;AAAA,IACd,GAAG,GAAG,sBAAsBD,KAAI,GAAG,CAACC,KAAI,GAAG,QAAQ,IAAI,KAAK,CAAC,GAAG;AAAA,MAC9D,GAAG,CAACD,KAAI,IAAI;AAAA,MACZ,GAAG,CAAC;AAAA,IACN,GAAG,GAAG,sBAAsBA,KAAI,IAAIA,KAAI,KAAK,CAAC,QAAQ,QAAQ,IAAI,MAAM,IAAI,GAAG,GAAG,sBAAsBA,KAAI,IAAIA,KAAI,KAAK,QAAQ,QAAQ,IAAI,KAAK,IAAI,GAAG;AAAA,MACvJ,GAAG,CAACA,KAAI,IAAI;AAAA,MACZ,GAAGC,KAAI;AAAA,IACT,GAAG,GAAG,sBAAsBD,KAAI,GAAGC,KAAI,GAAG,QAAQ,IAAI,GAAG,EAAE,GAAG;AAAA,MAC5D,GAAG,CAACD,KAAI;AAAA,MACR,GAAGC,KAAI,IAAI;AAAA,IACb,GAAG;AAAA,MACD,GAAGD,KAAI;AAAA,MACP,GAAGC,KAAI,IAAI;AAAA,IACb,CAAC;AACD,UAAM,KAAK,GAAO,IAAI,QAAQ;AAC9B,UAAM,UAAU,kBAAkB,MAAM;AAAA,MACtC,MAAM;AAAA,IACR,CAAC;AACD,QAAI,KAAK,SAAS,aAAa;AAC7B,cAAQ,YAAY;AACpB,cAAQ,YAAY;AAAA,IACtB;AACA,UAAM,qBAAqB,qBAAqB,MAAM;AACtD,UAAM,oBAAoB,mBAAmB,QAAQ,KAAK,EAAE;AAC5D,UAAM,qBAAqB,GAAG,KAAK,mBAAmB,OAAO;AAC7D,UAAM,WAAW,qBAAqB,UAAU;AAChD,UAAM,YAAY,GAAG,KAAK,UAAU,mBAC/B,QACJ;AACD,UAAM,sBAAsB,SAAS,OAAO,KAAK,cAAc;AAC/D,wBAAoB,OAAO,MAAM,WAAW,cAAc,EAAE,KAAK,kBAAkB,CAAC;AACpF,wBAAoB,OAAO,MAAM,oBAAoB,cAAc;AACnE,wBAAoB,KAAK,SAAS,MAAM;AACxC,QAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,0BAAoB,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,IAC/D;AACA,QAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,0BAAoB,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,IAChE;AACA,wBAAoB,KAAK,aAAa,aAAa,MAAM,MAAM;AAC/D,UAAM,KAAK,aAAa,aAAa,CAACD,KAAI,IAAI,UAAU,KAAK,KAAK,KAAK,QAAQ,GAAG,IAAI,CAACC,KAAI,KAAK,KAAK,WAAW,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG;AACtJ,qBAAiB,MAAM,mBAAmB;AAC1C,SAAK,YAAY,SAAU,OAAO;AAChC,YAAM,MAAM,kBAAkB,QAAQ,MAAM,YAAY,KAAK;AAC7D,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,gBAAgB,gBAAgB;AAIvC,SAAS,sBAAsB,SAAS,SAAS,QAAQ,YAAY,KAAK,aAAa,GAAG,WAAW,KAAK;AACxG,QAAM,SAAS,CAAC;AAChB,QAAM,gBAAgB,aAAa,KAAK,KAAK;AAC7C,QAAM,cAAc,WAAW,KAAK,KAAK;AACzC,QAAM,aAAa,cAAc;AACjC,QAAM,YAAY,cAAc,YAAY;AAC5C,WAASL,KAAI,GAAGA,KAAI,WAAWA,MAAK;AAClC,UAAM,QAAQ,gBAAgBA,KAAI;AAClC,UAAME,KAAI,UAAU,SAAS,KAAK,IAAI,KAAK;AAC3C,UAAMC,KAAI,UAAU,SAAS,KAAK,IAAI,KAAK;AAC3C,WAAO,KAAK;AAAA,MACV,GAAAD;AAAA,MACA,GAAAC;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,OAAO,uBAAuB,sBAAsB;AACpD,SAAe,gBAAgB,QAAQ,MAAM;AAAA;AAC3C,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAMC,KAAI,KAAK,SAAS,KAAK,WAAW;AACxC,UAAMC,KAAI,KAAK,UAAU,KAAK,WAAW;AACzC,UAAM,SAAS,KAAK,IAAI,GAAGA,KAAI,GAAG;AAClC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,SAAS,CAAC,GAAG,sBAAsBD,KAAI,GAAG,CAACC,KAAI,GAAG,QAAQ,IAAI,KAAK,CAAC,GAAG;AAAA,MAC3E,GAAGD,KAAI,IAAI;AAAA,MACX,GAAG,CAAC;AAAA,IACN,GAAG,GAAG,sBAAsBA,KAAI,IAAI,SAAS,GAAG,CAAC,QAAQ,QAAQ,IAAI,MAAM,IAAI,GAAG,GAAG,sBAAsBA,KAAI,IAAI,SAAS,GAAG,QAAQ,QAAQ,IAAI,KAAK,IAAI,GAAG;AAAA,MAC7J,GAAGA,KAAI,IAAI;AAAA,MACX,GAAGC,KAAI;AAAA,IACT,GAAG,GAAG,sBAAsBD,KAAI,GAAGC,KAAI,GAAG,QAAQ,IAAI,GAAG,EAAE,CAAC;AAC5D,UAAM,aAAa,CAAC;AAAA,MAClB,GAAG,CAACD,KAAI;AAAA,MACR,GAAG,CAACC,KAAI,IAAI;AAAA,IACd,GAAG;AAAA,MACD,GAAGD,KAAI;AAAA,MACP,GAAG,CAACC,KAAI,IAAI;AAAA,IACd,GAAG,GAAG,sBAAsBD,KAAI,GAAG,CAACC,KAAI,GAAG,QAAQ,IAAI,KAAK,CAAC,GAAG;AAAA,MAC9D,GAAGD,KAAI,IAAI;AAAA,MACX,GAAG,CAAC;AAAA,IACN,GAAG,GAAG,sBAAsBA,KAAI,IAAI,SAAS,GAAG,CAAC,QAAQ,QAAQ,IAAI,MAAM,IAAI,GAAG,GAAG,sBAAsBA,KAAI,IAAI,SAAS,GAAG,QAAQ,QAAQ,IAAI,KAAK,IAAI,GAAG;AAAA,MAC7J,GAAGA,KAAI,IAAI;AAAA,MACX,GAAGC,KAAI;AAAA,IACT,GAAG,GAAG,sBAAsBD,KAAI,GAAGC,KAAI,GAAG,QAAQ,IAAI,GAAG,EAAE,GAAG;AAAA,MAC5D,GAAGD,KAAI;AAAA,MACP,GAAGC,KAAI,IAAI;AAAA,IACb,GAAG;AAAA,MACD,GAAG,CAACD,KAAI;AAAA,MACR,GAAGC,KAAI,IAAI;AAAA,IACb,CAAC;AACD,UAAM,KAAK,GAAO,IAAI,QAAQ;AAC9B,UAAM,UAAU,kBAAkB,MAAM;AAAA,MACtC,MAAM;AAAA,IACR,CAAC;AACD,QAAI,KAAK,SAAS,aAAa;AAC7B,cAAQ,YAAY;AACpB,cAAQ,YAAY;AAAA,IACtB;AACA,UAAM,sBAAsB,qBAAqB,MAAM;AACvD,UAAM,oBAAoB,oBAAoB,QAAQ,KAAK,EAAE;AAC7D,UAAM,sBAAsB,GAAG,KAAK,mBAAmB,OAAO;AAC9D,UAAM,WAAW,qBAAqB,UAAU;AAChD,UAAM,YAAY,GAAG,KAAK,UAAU,mBAC/B,QACJ;AACD,UAAM,uBAAuB,SAAS,OAAO,KAAK,cAAc;AAChE,yBAAqB,OAAO,MAAM,WAAW,cAAc,EAAE,KAAK,kBAAkB,CAAC;AACrF,yBAAqB,OAAO,MAAM,qBAAqB,cAAc;AACrE,yBAAqB,KAAK,SAAS,MAAM;AACzC,QAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,2BAAqB,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,IAChE;AACA,QAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,2BAAqB,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,IACjE;AACA,yBAAqB,KAAK,aAAa,aAAa,CAAC,MAAM,MAAM;AACjE,UAAM,KAAK,aAAa,aAAa,CAACD,KAAI,KAAK,KAAK,WAAW,KAAK,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,IAAI,CAACC,KAAI,KAAK,KAAK,WAAW,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG;AACvK,qBAAiB,MAAM,oBAAoB;AAC3C,SAAK,YAAY,SAAU,OAAO;AAChC,YAAM,MAAM,kBAAkB,QAAQ,MAAM,YAAY,KAAK;AAC7D,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,iBAAiB,iBAAiB;AAIzC,SAAS,sBAAsB,SAAS,SAAS,QAAQ,YAAY,KAAK,aAAa,GAAG,WAAW,KAAK;AACxG,QAAM,SAAS,CAAC;AAChB,QAAM,gBAAgB,aAAa,KAAK,KAAK;AAC7C,QAAM,cAAc,WAAW,KAAK,KAAK;AACzC,QAAM,aAAa,cAAc;AACjC,QAAM,YAAY,cAAc,YAAY;AAC5C,WAASL,KAAI,GAAGA,KAAI,WAAWA,MAAK;AAClC,UAAM,QAAQ,gBAAgBA,KAAI;AAClC,UAAME,KAAI,UAAU,SAAS,KAAK,IAAI,KAAK;AAC3C,UAAMC,KAAI,UAAU,SAAS,KAAK,IAAI,KAAK;AAC3C,WAAO,KAAK;AAAA,MACV,GAAG,CAACD;AAAA,MACJ,GAAG,CAACC;AAAA,IACN,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,OAAO,uBAAuB,sBAAsB;AACpD,SAAe,YAAY,QAAQ,MAAM;AAAA;AACvC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAMC,KAAI,KAAK,SAAS,KAAK,WAAW;AACxC,UAAMC,KAAI,KAAK,UAAU,KAAK,WAAW;AACzC,UAAM,SAAS,KAAK,IAAI,GAAGA,KAAI,GAAG;AAClC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,uBAAuB,CAAC,GAAG,sBAAsBD,KAAI,GAAG,CAACC,KAAI,GAAG,QAAQ,IAAI,KAAK,CAAC,GAAG;AAAA,MACzF,GAAG,CAACD,KAAI,IAAI;AAAA,MACZ,GAAG;AAAA,IACL,GAAG,GAAG,sBAAsBA,KAAI,IAAI,SAAS,GAAG,CAAC,QAAQ,QAAQ,IAAI,MAAM,IAAI,GAAG,GAAG,sBAAsBA,KAAI,IAAI,SAAS,GAAG,QAAQ,QAAQ,IAAI,KAAK,IAAI,GAAG;AAAA,MAC7J,GAAG,CAACA,KAAI,IAAI;AAAA,MACZ,GAAG,CAACC,KAAI;AAAA,IACV,GAAG,GAAG,sBAAsBD,KAAI,GAAGC,KAAI,GAAG,QAAQ,IAAI,GAAG,EAAE,CAAC;AAC5D,UAAM,wBAAwB,CAAC,GAAG,sBAAsB,CAACD,KAAI,IAAI,SAAS,SAAS,GAAG,CAACC,KAAI,GAAG,QAAQ,IAAI,KAAK,IAAI,GAAG;AAAA,MACpH,GAAGD,KAAI,IAAI,SAAS;AAAA,MACpB,GAAG;AAAA,IACL,GAAG,GAAG,sBAAsB,CAACA,KAAI,IAAI,SAAS,GAAG,CAAC,QAAQ,QAAQ,IAAI,GAAG,EAAE,GAAG,GAAG,sBAAsB,CAACA,KAAI,IAAI,SAAS,GAAG,QAAQ,QAAQ,IAAI,KAAK,CAAC,GAAG;AAAA,MACvJ,GAAGA,KAAI,IAAI,SAAS;AAAA,MACpB,GAAG,CAAC;AAAA,IACN,GAAG,GAAG,sBAAsB,CAACA,KAAI,IAAI,SAAS,SAAS,GAAGC,KAAI,GAAG,QAAQ,IAAI,MAAM,IAAI,CAAC;AACxF,UAAM,aAAa,CAAC;AAAA,MAClB,GAAGD,KAAI;AAAA,MACP,GAAG,CAACC,KAAI,IAAI;AAAA,IACd,GAAG;AAAA,MACD,GAAG,CAACD,KAAI;AAAA,MACR,GAAG,CAACC,KAAI,IAAI;AAAA,IACd,GAAG,GAAG,sBAAsBD,KAAI,GAAG,CAACC,KAAI,GAAG,QAAQ,IAAI,KAAK,CAAC,GAAG;AAAA,MAC9D,GAAG,CAACD,KAAI,IAAI;AAAA,MACZ,GAAG,CAAC;AAAA,IACN,GAAG,GAAG,sBAAsBA,KAAI,IAAI,SAAS,GAAG,CAAC,QAAQ,QAAQ,IAAI,MAAM,IAAI,GAAG,GAAG,sBAAsBA,KAAI,IAAI,SAAS,GAAG,QAAQ,QAAQ,IAAI,KAAK,IAAI,GAAG;AAAA,MAC7J,GAAG,CAACA,KAAI,IAAI;AAAA,MACZ,GAAGC,KAAI;AAAA,IACT,GAAG,GAAG,sBAAsBD,KAAI,GAAGC,KAAI,GAAG,QAAQ,IAAI,GAAG,EAAE,GAAG;AAAA,MAC5D,GAAG,CAACD,KAAI;AAAA,MACR,GAAGC,KAAI,IAAI;AAAA,IACb,GAAG;AAAA,MACD,GAAGD,KAAI,IAAI,SAAS,SAAS;AAAA,MAC7B,GAAGC,KAAI,IAAI;AAAA,IACb,GAAG,GAAG,sBAAsB,CAACD,KAAI,IAAI,SAAS,SAAS,GAAG,CAACC,KAAI,GAAG,QAAQ,IAAI,KAAK,IAAI,GAAG;AAAA,MACxF,GAAGD,KAAI,IAAI,SAAS;AAAA,MACpB,GAAG;AAAA,IACL,GAAG,GAAG,sBAAsB,CAACA,KAAI,IAAI,SAAS,GAAG,CAAC,QAAQ,QAAQ,IAAI,GAAG,EAAE,GAAG,GAAG,sBAAsB,CAACA,KAAI,IAAI,SAAS,GAAG,QAAQ,QAAQ,IAAI,KAAK,CAAC,GAAG;AAAA,MACvJ,GAAGA,KAAI,IAAI,SAAS;AAAA,MACpB,GAAG,CAAC;AAAA,IACN,GAAG,GAAG,sBAAsB,CAACA,KAAI,IAAI,SAAS,SAAS,GAAGC,KAAI,GAAG,QAAQ,IAAI,MAAM,IAAI,CAAC;AACxF,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,UAAU,kBAAkB,MAAM;AAAA,MACtC,MAAM;AAAA,IACR,CAAC;AACD,QAAI,KAAK,SAAS,aAAa;AAC7B,cAAQ,YAAY;AACpB,cAAQ,YAAY;AAAA,IACtB;AACA,UAAM,qBAAqB,qBAAqB,oBAAoB;AACpE,UAAM,wBAAwB,mBAAmB,QAAQ,KAAK,EAAE;AAChE,UAAM,qBAAqB,GAAG,KAAK,uBAAuB,OAAO;AACjE,UAAM,sBAAsB,qBAAqB,qBAAqB;AACtE,UAAM,yBAAyB,oBAAoB,QAAQ,KAAK,EAAE;AAClE,UAAM,sBAAsB,GAAG,KAAK,wBAAwB,OAAO;AACnE,UAAM,WAAW,qBAAqB,UAAU;AAChD,UAAM,YAAY,GAAG,KAAK,UAAU,mBAC/B,QACJ;AACD,UAAM,mBAAmB,SAAS,OAAO,KAAK,cAAc;AAC5D,qBAAiB,OAAO,MAAM,WAAW,cAAc,EAAE,KAAK,kBAAkB,CAAC;AACjF,qBAAiB,OAAO,MAAM,oBAAoB,cAAc;AAChE,qBAAiB,OAAO,MAAM,qBAAqB,cAAc;AACjE,qBAAiB,KAAK,SAAS,MAAM;AACrC,QAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,uBAAiB,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,IAC5D;AACA,QAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,uBAAiB,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,IAC7D;AACA,qBAAiB,KAAK,aAAa,aAAa,SAAS,SAAS,CAAC,MAAM;AACzE,UAAM,KAAK,aAAa,aAAa,CAACD,KAAI,KAAK,KAAK,WAAW,KAAK,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,IAAI,CAACC,KAAI,KAAK,KAAK,WAAW,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG;AACvK,qBAAiB,MAAM,gBAAgB;AACvC,SAAK,YAAY,SAAU,OAAO;AAChC,YAAM,MAAM,kBAAkB,QAAQ,MAAM,YAAY,KAAK;AAC7D,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,aAAa,aAAa;AAIjC,SAAe,gBAAgB,QAAQ,MAAM;AAAA;AAC3C,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAM,WAAW,IACf,YAAY;AACd,UAAMD,KAAI,KAAK,IAAI,WAAW,KAAK,SAAS,KAAK,WAAW,KAAK,KAAK,MAAM,MAAM,SAAS,CAAC;AAC5F,UAAMC,KAAI,KAAK,IAAI,WAAW,KAAK,UAAU,KAAK,WAAW,KAAK,GAAG,MAAM,UAAU,CAAC;AACtF,UAAM,SAASA,KAAI;AACnB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,QAAI,KAAK,SAAS,aAAa;AAC7B,cAAQ,YAAY;AACpB,cAAQ,YAAY;AAAA,IACtB;AACA,UAAM,aAAaD,IACjB,cAAcC;AAChB,UAAM,KAAK,aAAa;AACxB,UAAM,KAAK,cAAc;AACzB,UAAM,SAAS,CAAC;AAAA,MACd,GAAG;AAAA,MACH,GAAG;AAAA,IACL,GAAG;AAAA,MACD,GAAG;AAAA,MACH,GAAG;AAAA,IACL,GAAG;AAAA,MACD,GAAG;AAAA,MACH,GAAG,cAAc;AAAA,IACnB,GAAG;AAAA,MACD,GAAG;AAAA,MACH,GAAG;AAAA,IACL,GAAG;AAAA,MACD,GAAG;AAAA,MACH,GAAG;AAAA,IACL,GAAG,GAAG,qBAAqB,CAAC,IAAI,CAAC,cAAc,GAAG,QAAQ,IAAI,KAAK,EAAE,CAAC;AACtE,UAAM,WAAW,qBAAqB,MAAM;AAC5C,UAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAC3C,UAAM,UAAU,SAAS,OAAO,MAAM,WAAW,cAAc;AAC/D,YAAQ,KAAK,SAAS,uBAAuB;AAC7C,QAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,cAAQ,eAAe,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,IACxD;AACA,QAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,cAAQ,eAAe,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,IACzD;AACA,YAAQ,KAAK,aAAa,aAAa,CAACD,KAAI,CAAC,KAAK,CAACC,KAAI,CAAC,GAAG;AAC3D,qBAAiB,MAAM,OAAO;AAC9B,SAAK,YAAY,SAAU,OAAO;AAChC,YAAM,MAAM,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AACzD,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,iBAAiB,iBAAiB;AAIzC,IAAI,sBAAqC,OAAO,CAACH,IAAGC,IAAG,OAAO,QAAQ,IAAI,OAAO;AAC/E,SAAO,CAAC,IAAID,EAAC,IAAIC,KAAI,EAAE,IAAI,IAAI,EAAE,IAAI,EAAE,UAAU,KAAK,MAAM,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC,KAAK,MAAM,MAAM,MAAM,IAAI,IAAI,EAAE,IAAI,EAAE,UAAU,KAAK,MAAM,MAAM,CAAC,MAAM,EAAE,EAAE,KAAK,GAAG;AAC1K,GAAG,qBAAqB;AACxB,IAAI,2BAA0C,OAAO,CAACD,IAAGC,IAAG,OAAO,QAAQ,IAAI,OAAO;AACpF,SAAO,CAAC,IAAID,EAAC,IAAIC,KAAI,EAAE,IAAI,IAAID,KAAI,KAAK,IAAIC,KAAI,EAAE,IAAI,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC,KAAK,MAAM,MAAM,MAAM,IAAI,IAAI,EAAE,IAAI,EAAE,UAAU,KAAK,MAAM,MAAM,CAAC,MAAM,EAAE,EAAE,KAAK,GAAG;AACpK,GAAG,0BAA0B;AAC7B,IAAI,2BAA0C,OAAO,CAACD,IAAGC,IAAG,OAAO,QAAQ,IAAI,OAAO;AACpF,SAAO,CAAC,IAAID,KAAI,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,EAAE,IAAI,EAAE,UAAU,KAAK,IAAI,EAAE,KAAK,GAAG;AACvF,GAAG,0BAA0B;AAC7B,SAAe,SAAS,QAAQ,MAAM;AAAA;AACpC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAME,KAAI,KAAK,IAAI,KAAK,QAAQ,KAAK,SAAS,KAAK,SAAS,CAAC;AAC7D,UAAM,KAAKA,KAAI;AACf,UAAM,KAAK,MAAM,MAAMA,KAAI;AAC3B,UAAMC,KAAI,KAAK,IAAI,KAAK,SAAS,KAAK,KAAK,SAAS,KAAK,UAAU,CAAC;AACpE,QAAI;AACJ,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,KAAK,SAAS,aAAa;AAC7B,YAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,YAAM,gBAAgB,yBAAyB,GAAG,GAAGD,IAAGC,IAAG,IAAI,EAAE;AACjE,YAAM,gBAAgB,yBAAyB,GAAG,IAAID,IAAGC,IAAG,IAAI,EAAE;AAClE,YAAM,YAAY,GAAG,KAAK,eAAe,kBAAkB,MAAM,CAAC,CAAC,CAAC;AACpE,YAAM,YAAY,GAAG,KAAK,eAAe,kBAAkB,MAAM;AAAA,QAC/D,MAAM;AAAA,MACR,CAAC,CAAC;AACF,kBAAY,SAAS,OAAO,MAAM,WAAW,cAAc;AAC3D,kBAAY,SAAS,OAAO,MAAM,WAAW,cAAc;AAC3D,gBAAU,KAAK,SAAS,uBAAuB;AAC/C,UAAI,WAAW;AACb,kBAAU,KAAK,SAAS,SAAS;AAAA,MACnC;AAAA,IACF,OAAO;AACL,YAAM,WAAW,oBAAoB,GAAG,GAAGD,IAAGC,IAAG,IAAI,EAAE;AACvD,kBAAY,SAAS,OAAO,QAAQ,cAAc,EAAE,KAAK,KAAK,QAAQ,EAAE,KAAK,SAAS,uBAAuB,EAAE,KAAK,SAAS,oBAAoB,SAAS,CAAC,EAAE,KAAK,SAAS,UAAU;AAAA,IACvL;AACA,cAAU,KAAK,kBAAkB,EAAE;AACnC,cAAU,KAAK,aAAa,aAAa,CAACD,KAAI,CAAC,KAAK,EAAEC,KAAI,IAAI,GAAG,GAAG;AACpE,qBAAiB,MAAM,SAAS;AAChC,UAAM,KAAK,aAAa,aAAa,EAAE,KAAK,QAAQ,MAAM,KAAK,KAAK,KAAK,QAAQ,GAAG,KAAK,EAAE,KAAK,SAAS,MAAM,KAAK,WAAW,KAAK,OAAO,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG;AACvK,SAAK,YAAY,SAAU,OAAO;AAChC,YAAM,MAAM,kBAAkB,KAAK,MAAM,KAAK;AAC9C,YAAMH,KAAI,IAAI,KAAK,KAAK,KAAK;AAC7B,UAAI,MAAM,MAAM,KAAK,IAAIA,EAAC,KAAK,KAAK,SAAS,KAAK,KAAK,KAAK,IAAIA,EAAC,MAAM,KAAK,SAAS,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,EAAE,KAAK,KAAK,UAAU,KAAK,IAAI,KAAK;AAC7J,YAAIC,KAAI,KAAK,MAAM,IAAID,KAAIA,MAAK,KAAK;AACrC,YAAIC,KAAI,GAAG;AACT,UAAAA,KAAI,KAAK,KAAKA,EAAC;AAAA,QACjB;AACA,QAAAA,KAAI,KAAKA;AACT,YAAI,MAAM,KAAK,KAAK,KAAK,KAAK,GAAG;AAC/B,UAAAA,KAAI,CAACA;AAAA,QACP;AACA,YAAI,KAAKA;AAAA,MACX;AACA,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,UAAU,UAAU;AAI3B,SAAe,iBAAiB,QAAQ,MAAM;AAAA;AAC5C,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAMC,KAAI,KAAK,QAAQ,KAAK;AAC5B,UAAMC,KAAI,KAAK,SAAS,KAAK;AAC7B,UAAM,aAAaA,KAAI;AACvB,UAAMH,KAAI,CAACE,KAAI;AACf,UAAMD,KAAI,CAACE,KAAI,IAAI,aAAa;AAChC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,QAAI,KAAK,SAAS,aAAa;AAC7B,cAAQ,YAAY;AACpB,cAAQ,YAAY;AAAA,IACtB;AACA,UAAM,MAAM,CAAC;AAAA,MACX,GAAAH;AAAA,MACA,GAAGC,KAAI;AAAA,IACT,GAAG;AAAA,MACD,GAAG,CAACD;AAAA,MACJ,GAAGC,KAAI;AAAA,IACT,GAAG;AAAA,MACD,GAAG,CAACD;AAAA,MACJ,GAAG,CAACC;AAAA,IACN,GAAG;AAAA,MACD,GAAAD;AAAA,MACA,GAAG,CAACC;AAAA,IACN,GAAG;AAAA,MACD,GAAAD;AAAA,MACA,GAAAC;AAAA,IACF,GAAG;AAAA,MACD,GAAG,CAACD;AAAA,MACJ,GAAAC;AAAA,IACF,GAAG;AAAA,MACD,GAAG,CAACD;AAAA,MACJ,GAAGC,KAAI;AAAA,IACT,CAAC;AACD,UAAM,OAAO,GAAG,QAAQ,IAAI,IAAI,CAAAJ,OAAK,CAACA,GAAE,GAAGA,GAAE,CAAC,CAAC,GAAG,OAAO;AACzD,UAAM,UAAU,SAAS,OAAO,MAAM,MAAM,cAAc;AAC1D,YAAQ,KAAK,SAAS,uBAAuB;AAC7C,QAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,cAAQ,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,IACnD;AACA,QAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,cAAQ,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,IACpD;AACA,UAAM,KAAK,aAAa,aAAaG,MAAK,KAAK,WAAW,KAAK,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,KAAKC,KAAI,cAAc,KAAK,WAAW,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG;AAC3K,qBAAiB,MAAM,OAAO;AAC9B,SAAK,YAAY,SAAU,OAAO;AAChC,YAAM,MAAM,kBAAkB,KAAK,MAAM,KAAK;AAC9C,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,kBAAkB,kBAAkB;AAI3C,SAAe,aAAa,QAAQ,MAAM;AAAA;AACxC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAM,MAAM;AACZ,UAAM,cAAc,KAAK,QAAQ,IAAI,cAAc;AACnD,UAAM,cAAc,KAAK,QAAQ,IAAI;AACrC,QAAI;AACJ,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,KAAK,SAAS,aAAa;AAC7B,YAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,YAAM,eAAe,kBAAkB,MAAM;AAAA,QAC3C,WAAW;AAAA,QACX,aAAa;AAAA,MACf,CAAC;AACD,YAAM,eAAe,kBAAkB,MAAM;AAAA,QAC3C,WAAW;AAAA,QACX,aAAa;AAAA,MACf,CAAC;AACD,YAAM,iBAAiB,GAAG,OAAO,GAAG,GAAG,cAAc,GAAG,YAAY;AACpE,YAAM,iBAAiB,GAAG,OAAO,GAAG,GAAG,cAAc,GAAG,YAAY;AACpE,oBAAc,SAAS,OAAO,KAAK,cAAc;AACjD,kBAAY,KAAK,SAAS,oBAAoB,KAAK,UAAU,CAAC,EAAE,KAAK,SAAS,oBAAoB,SAAS,CAAC;AAC5G,kBAAY,KAAK,GAAG,YAAY,cAAc;AAC9C,kBAAY,KAAK,GAAG,YAAY,cAAc;AAAA,IAChD,OAAO;AACL,oBAAc,SAAS,OAAO,KAAK,cAAc;AACjD,YAAM,cAAc,YAAY,OAAO,UAAU,cAAc;AAC/D,YAAM,cAAc,YAAY,OAAO,QAAQ;AAC/C,kBAAY,KAAK,SAAS,uBAAuB,EAAE,KAAK,SAAS,UAAU;AAC3E,kBAAY,KAAK,SAAS,cAAc,EAAE,KAAK,SAAS,UAAU,EAAE,KAAK,KAAK,WAAW,EAAE,KAAK,MAAM,CAAC,EAAE,KAAK,MAAM,CAAC;AACrH,kBAAY,KAAK,SAAS,cAAc,EAAE,KAAK,SAAS,UAAU,EAAE,KAAK,KAAK,WAAW,EAAE,KAAK,MAAM,CAAC,EAAE,KAAK,MAAM,CAAC;AAAA,IACvH;AACA,qBAAiB,MAAM,WAAW;AAClC,SAAK,YAAY,SAAU,OAAO;AAChC,UAAI,KAAK,0BAA0B,MAAM,aAAa,KAAK;AAC3D,aAAO,kBAAkB,OAAO,MAAM,aAAa,KAAK;AAAA,IAC1D;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,cAAc,cAAc;AAInC,SAAS,aAAa,QAAQ,MAAM;AAAA,EAClC,QAAQ;AAAA,IACN;AAAA,EACF;AACF,GAAG;AACD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,cAAc,IAAI;AACtB,OAAK,QAAQ;AACb,OAAK,aAAa;AAClB,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,eAAe,IAAI,CAAC,EAAE,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AACxG,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,UAAU,kBAAkB,MAAM;AAAA,IACtC,WAAW;AAAA,EACb,CAAC;AACD,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,aAAa,GAAG,OAAO,GAAG,GAAG,SAAS,GAAG,OAAO;AACtD,QAAM,gBAAgB,SAAS,OAAO,MAAM,YAAY,cAAc;AACtE,gBAAc,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS,UAAU,cAAc;AAC/E,MAAI,aAAa,UAAU,SAAS,KAAK,KAAK,SAAS,aAAa;AAClE,kBAAc,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACzD;AACA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,kBAAc,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EAC1D;AACA,mBAAiB,MAAM,aAAa;AACpC,OAAK,YAAY,SAAU,OAAO;AAChC,QAAI,KAAK,0BAA0B,MAAM;AAAA,MACvC;AAAA,MACA;AAAA,IACF,CAAC;AACD,UAAM,MAAM,kBAAkB,OAAO,MAAM,QAAQ,KAAK;AACxD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,OAAO,cAAc,cAAc;AAInC,SAAe,gBAAgB,QAAQ,MAAM;AAAA;AAC3C,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAMC,KAAI,KAAK,SAAS,KAAK,WAAW;AACxC,UAAMC,KAAID,KAAI,KAAK;AACnB,UAAM,KAAKA,KAAI,KAAK;AACpB,UAAM,SAAS,CAAC;AAAA,MACd,GAAG;AAAA,MACH,GAAG,CAACC;AAAA,IACN,GAAG;AAAA,MACD,GAAG;AAAA,MACH,GAAG,CAACA;AAAA,IACN,GAAG;AAAA,MACD,GAAG,KAAK;AAAA,MACR,GAAG;AAAA,IACL,CAAC;AACD,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,QAAI,KAAK,SAAS,aAAa;AAC7B,cAAQ,YAAY;AACpB,cAAQ,YAAY;AAAA,IACtB;AACA,UAAM,WAAW,qBAAqB,MAAM;AAC5C,UAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAC3C,UAAM,mBAAmB,SAAS,OAAO,MAAM,WAAW,cAAc,EAAE,KAAK,aAAa,aAAa,CAACA,KAAI,CAAC,KAAKA,KAAI,CAAC,GAAG;AAC5H,QAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,uBAAiB,eAAe,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,IACjE;AACA,QAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,uBAAiB,eAAe,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,IAClE;AACA,SAAK,QAAQD;AACb,SAAK,SAASC;AACd,qBAAiB,MAAM,gBAAgB;AACvC,UAAM,KAAK,aAAa,aAAa,CAAC,KAAK,QAAQ,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,KAAK,CAACA,KAAI,KAAK,KAAK,WAAW,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG;AACvJ,SAAK,YAAY,SAAU,OAAO;AAChC,UAAI,KAAK,sBAAsB,MAAM,QAAQ,KAAK;AAClD,aAAO,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AAAA,IACtD;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,iBAAiB,iBAAiB;AAIzC,SAAS,SAAS,QAAQ,MAAM;AAAA,EAC9B;AAAA,EACA,QAAQ;AAAA,IACN,OAAO;AAAA,IACP;AAAA,EACF;AACF,GAAG;AACD,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,cAAc,IAAI;AACtB,OAAK,QAAQ;AACb,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,eAAe,IAAI,CAAC,EAAE,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AACxG,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,MAAI,QAAQ,KAAK,IAAI,IAAI,MAAM,SAAS,CAAC;AACzC,MAAI,SAAS,KAAK,IAAI,IAAI,MAAM,UAAU,CAAC;AAC3C,MAAI,QAAQ,MAAM;AAChB,YAAQ,KAAK,IAAI,IAAI,MAAM,SAAS,CAAC;AACrC,aAAS,KAAK,IAAI,IAAI,MAAM,UAAU,CAAC;AAAA,EACzC;AACA,QAAMH,KAAI,KAAK,QAAQ;AACvB,QAAMC,KAAI,KAAK,SAAS;AACxB,QAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,QAAM,UAAU,kBAAkB,MAAM;AAAA,IACtC,QAAQ,eAAe;AAAA,IACvB,MAAM,eAAe;AAAA,EACvB,CAAC;AACD,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,YAAY,GAAG,UAAUD,IAAGC,IAAG,OAAO,QAAQ,OAAO;AAC3D,QAAM,QAAQ,SAAS,OAAO,MAAM,WAAW,cAAc;AAC7D,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,UAAM,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACjD;AACA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,UAAM,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EAClD;AACA,mBAAiB,MAAM,KAAK;AAC5B,QAAM,UAAU,QAAQ,WAAW;AACnC,MAAI,KAAK,SAAS,KAAK,QAAQ;AAC7B,SAAK,SAAS,UAAU,KAAK;AAC7B,SAAK,UAAU,UAAU,KAAK;AAAA,EAChC;AACA,OAAK,YAAY,SAAU,OAAO;AAChC,WAAO,kBAAkB,KAAK,MAAM,KAAK;AAAA,EAC3C;AACA,SAAO;AACT;AACA,OAAO,UAAU,UAAU;AAI3B,SAAe,qBAAqB,QAAQ,MAAM;AAAA;AAChD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM,WAAW,IACf,YAAY;AACd,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAMC,KAAI,KAAK,IAAI,UAAU,KAAK,SAAS,KAAK,WAAW,KAAK,GAAG,MAAM,SAAS,CAAC;AACnF,UAAMC,KAAI,KAAK,IAAI,WAAW,KAAK,UAAU,KAAK,WAAW,KAAK,GAAG,MAAM,UAAU,CAAC;AACtF,UAAM,SAASA,KAAI;AACnB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,QAAI,KAAK,SAAS,aAAa;AAC7B,cAAQ,YAAY;AACpB,cAAQ,YAAY;AAAA,IACtB;AACA,UAAM,SAAS,CAAC;AAAA,MACd,GAAG,CAACD,KAAI;AAAA,MACR,GAAG,CAACC,KAAI;AAAA,IACV,GAAG;AAAA,MACD,GAAGD,KAAI,IAAI;AAAA,MACX,GAAG,CAACC,KAAI;AAAA,IACV,GAAG,GAAG,qBAAqB,CAACD,KAAI,IAAI,QAAQ,GAAG,QAAQ,IAAI,IAAI,GAAG,GAAG;AAAA,MACnE,GAAGA,KAAI,IAAI;AAAA,MACX,GAAGC,KAAI;AAAA,IACT,GAAG;AAAA,MACD,GAAG,CAACD,KAAI;AAAA,MACR,GAAGC,KAAI;AAAA,IACT,CAAC;AACD,UAAM,WAAW,qBAAqB,MAAM;AAC5C,UAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAC3C,UAAM,UAAU,SAAS,OAAO,MAAM,WAAW,cAAc;AAC/D,YAAQ,KAAK,SAAS,uBAAuB;AAC7C,QAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,cAAQ,eAAe,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,IACxD;AACA,QAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,cAAQ,eAAe,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,IACzD;AACA,qBAAiB,MAAM,OAAO;AAC9B,SAAK,YAAY,SAAU,OAAO;AAChC,UAAI,KAAK,kBAAkB,MAAM;AAAA,QAC/B;AAAA,QACA;AAAA,MACF,CAAC;AACD,YAAM,MAAM,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AACzD,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,sBAAsB,sBAAsB;AAInD,IAAI,qBAAoC,OAAO,CAACH,IAAGC,IAAG,OAAO,QAAQO,OAAM;AACzE,SAAO,CAAC,IAAIR,KAAIQ,EAAC,IAAIP,EAAC,IAAI,IAAID,KAAI,QAAQQ,EAAC,IAAIP,EAAC,IAAI,IAAID,KAAI,KAAK,IAAIC,KAAI,SAAS,CAAC,IAAI,IAAID,KAAI,QAAQQ,EAAC,IAAIP,KAAI,MAAM,IAAI,IAAID,KAAIQ,EAAC,IAAIP,KAAI,MAAM,IAAI,IAAID,EAAC,IAAIC,KAAI,SAAS,CAAC,IAAI,GAAG,EAAE,KAAK,GAAG;AAC/L,GAAG,oBAAoB;AACvB,SAAe,QAAQ,QAAQ,MAAM;AAAA;AACnC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAMQ,KAAI;AACV,UAAMN,KAAI,KAAK,SAAS,KAAK;AAC7B,UAAMK,KAAIL,KAAIM;AACd,UAAMP,KAAI,KAAK,QAAQ,IAAIM,KAAI,KAAK;AACpC,UAAM,SAAS,CAAC;AAAA,MACd,GAAGA;AAAA,MACH,GAAG;AAAA,IACL,GAAG;AAAA,MACD,GAAGN,KAAIM;AAAA,MACP,GAAG;AAAA,IACL,GAAG;AAAA,MACD,GAAGN;AAAA,MACH,GAAG,CAACC,KAAI;AAAA,IACV,GAAG;AAAA,MACD,GAAGD,KAAIM;AAAA,MACP,GAAG,CAACL;AAAA,IACN,GAAG;AAAA,MACD,GAAGK;AAAA,MACH,GAAG,CAACL;AAAA,IACN,GAAG;AAAA,MACD,GAAG;AAAA,MACH,GAAG,CAACA,KAAI;AAAA,IACV,CAAC;AACD,QAAI;AACJ,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,KAAK,SAAS,aAAa;AAC7B,YAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,YAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,YAAM,WAAW,mBAAmB,GAAG,GAAGD,IAAGC,IAAGK,EAAC;AACjD,YAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAC3C,gBAAU,SAAS,OAAO,MAAM,WAAW,cAAc,EAAE,KAAK,aAAa,aAAa,CAACN,KAAI,CAAC,KAAKC,KAAI,CAAC,GAAG;AAC7G,UAAI,WAAW;AACb,gBAAQ,KAAK,SAAS,SAAS;AAAA,MACjC;AAAA,IACF,OAAO;AACL,gBAAU,mBAAmB,UAAUD,IAAGC,IAAG,MAAM;AAAA,IACrD;AACA,QAAI,YAAY;AACd,cAAQ,KAAK,SAAS,UAAU;AAAA,IAClC;AACA,SAAK,QAAQD;AACb,SAAK,SAASC;AACd,qBAAiB,MAAM,OAAO;AAC9B,SAAK,YAAY,SAAU,OAAO;AAChC,aAAO,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AAAA,IACtD;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,SAAS,SAAS;AAIzB,SAAe,UAAU,QAAQ,MAAM;AAAA;AACrC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,QAAQ;AACb,SAAK,aAAa;AAClB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAMD,KAAI,KAAK,IAAI,IAAI,MAAM,SAAS,CAAC;AACvC,UAAMC,KAAI,KAAK,IAAI,IAAI,MAAM,UAAU,CAAC;AACxC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,QAAI,KAAK,SAAS,aAAa;AAC7B,cAAQ,YAAY;AACpB,cAAQ,YAAY;AAAA,IACtB;AACA,UAAM,SAAS,CAAC;AAAA,MACd,GAAG;AAAA,MACH,GAAG;AAAA,IACL,GAAG;AAAA,MACD,GAAGD;AAAA,MACH,GAAG;AAAA,IACL,GAAG;AAAA,MACD,GAAG;AAAA,MACH,GAAGC;AAAA,IACL,GAAG;AAAA,MACD,GAAGD;AAAA,MACH,GAAGC;AAAA,IACL,CAAC;AACD,UAAM,WAAW,qBAAqB,MAAM;AAC5C,UAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAC3C,UAAM,UAAU,SAAS,OAAO,MAAM,WAAW,cAAc;AAC/D,YAAQ,KAAK,SAAS,uBAAuB;AAC7C,QAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,cAAQ,eAAe,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,IACxD;AACA,QAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,cAAQ,eAAe,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,IACzD;AACA,YAAQ,KAAK,aAAa,aAAa,CAACD,KAAI,CAAC,KAAK,CAACC,KAAI,CAAC,GAAG;AAC3D,qBAAiB,MAAM,OAAO;AAC9B,SAAK,YAAY,SAAU,OAAO;AAChC,UAAI,KAAK,kBAAkB,MAAM;AAAA,QAC/B;AAAA,MACF,CAAC;AACD,YAAM,MAAM,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AACzD,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,WAAW,WAAW;AAI7B,SAAe,KAAK,IAAQ,IAAM,IAK/B;AAAA,6CALiB,QAAQ,MAAM;AAAA,IAChC,QAAQ;AAAA,MACN;AAAA,MACA;AAAA,IACF;AAAA,EACF,GAAG;AACD,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM,cAAc,KAAK,eAAe;AACxC,UAAM,aAAa,KAAK,cAAc;AACtC,UAAM,WAAW,KAAK,IAAI,aAAa,UAAU;AACjD,UAAM,eAAe,WAAW;AAChC,SAAK,QAAQ,KAAK,IAAI,UAAU,gBAAgB,CAAC;AACjD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,oBAAoB;AACxD,UAAM,WAAW,KAAK,QAAQ;AAC9B,UAAM,SAAS;AACf,UAAM,QAAQ;AACd,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,UAAMH,KAAI,CAAC,QAAQ;AACnB,UAAMC,KAAI,CAAC,SAAS;AACpB,UAAM,eAAe,KAAK,QAAQ,IAAI;AACtC,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,UAAU,kBAAkB,MAAM;AAAA,MACtC,QAAQ;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AACD,QAAI,KAAK,SAAS,aAAa;AAC7B,cAAQ,YAAY;AACpB,cAAQ,YAAY;AAAA,IACtB;AACA,UAAM,WAAW,GAAG,UAAUD,IAAGC,IAAG,OAAO,QAAQ,OAAO;AAC1D,UAAM,aAAa,KAAK,IAAI,OAAO,KAAK,KAAK;AAC7C,UAAM,cAAc,SAAS,KAAK,SAAS;AAC3C,UAAM,YAAY,GAAG,UAAU,CAAC,aAAa,GAAG,CAAC,cAAc,GAAG,YAAY,aAAa,iCACtF,UADsF;AAAA,MAEzF,MAAM;AAAA,MACN,QAAQ;AAAA,IACV,EAAC;AACD,UAAM,YAAY,SAAS,OAAO,MAAM,UAAU,cAAc;AAChE,UAAM,aAAa,SAAS,OAAO,MAAM,SAAS;AAClD,QAAI,KAAK,MAAM;AACb,YAAM,WAAW,SAAS,OAAO,GAAG;AACpC,eAAS,KAAK,MAAM,MAAM,WAAW,KAAK,MAAM;AAAA,QAC9C,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,gBAAgB;AAAA,MAClB,CAAC,CAAC,MAAM;AACR,YAAM,WAAW,SAAS,KAAK,EAAE,QAAQ;AACzC,YAAM,YAAY,SAAS;AAC3B,YAAM,aAAa,SAAS;AAC5B,YAAM,QAAQ,SAAS;AACvB,YAAM,QAAQ,SAAS;AACvB,eAAS,KAAK,aAAa,aAAa,CAAC,YAAY,IAAI,KAAK,IAAI,WAAW,KAAK,SAAS,IAAI,eAAe,IAAI,aAAa,IAAI,QAAQ,CAAC,KAAK,SAAS,IAAI,eAAe,IAAI,aAAa,IAAI,KAAK,GAAG;AAC1M,eAAS,KAAK,SAAS,UAAU,UAAU,IAAI,QAAQ,KAAK,UAAU,GAAG;AAAA,IAC3E;AACA,UAAM,KAAK,aAAa,aAAa,CAAC,KAAK,QAAQ,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,IAAI,WAAW,CAAC,cAAc,IAAI,cAAc,IAAI,KAAK,MAAM,GAAG;AACpJ,cAAU,KAAK,aAAa,aAAa,CAAC,IAAI,WAAW,KAAK,SAAS,IAAI,eAAe,IAAI,CAAC,KAAK,SAAS,IAAI,eAAe,CAAC,GAAG;AACpI,qBAAiB,MAAM,UAAU;AACjC,SAAK,YAAY,SAAU,OAAO;AAChC,UAAI,KAAK,wBAAwB,MAAM,KAAK;AAC5C,UAAI,CAAC,KAAK,OAAO;AACf,eAAO,kBAAkB,KAAK,MAAM,KAAK;AAAA,MAC3C;AACA,YAAM,KAAK,KAAK,KAAK;AACrB,YAAM,KAAK,KAAK,KAAK;AACrB,YAAM,aAAa,KAAK,UAAU;AAClC,UAAI,SAAS,CAAC;AACd,UAAI,UAAU;AACZ,iBAAS,CAAC;AAAA,UACR,GAAG,KAAK,KAAK,QAAQ;AAAA,UACrB,GAAG,KAAK,aAAa;AAAA,QACvB,GAAG;AAAA,UACD,GAAG,KAAK,KAAK,QAAQ;AAAA,UACrB,GAAG,KAAK,aAAa;AAAA,QACvB,GAAG;AAAA,UACD,GAAG,KAAK,KAAK,QAAQ;AAAA,UACrB,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS;AAAA,QACzC,GAAG;AAAA,UACD,GAAG,KAAK,QAAQ;AAAA,UAChB,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS;AAAA,QACzC,GAAG;AAAA,UACD,GAAG,KAAK,QAAQ;AAAA,UAChB,GAAG,KAAK,aAAa;AAAA,QACvB,GAAG;AAAA,UACD,GAAG,KAAK,QAAQ;AAAA,UAChB,GAAG,KAAK,aAAa;AAAA,QACvB,GAAG;AAAA,UACD,GAAG,KAAK,QAAQ;AAAA,UAChB,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS;AAAA,QACzC,GAAG;AAAA,UACD,GAAG,KAAK,KAAK,QAAQ;AAAA,UACrB,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS;AAAA,QACzC,CAAC;AAAA,MACH,OAAO;AACL,iBAAS,CAAC;AAAA,UACR,GAAG,KAAK,QAAQ;AAAA,UAChB,GAAG,KAAK,aAAa;AAAA,QACvB,GAAG;AAAA,UACD,GAAG,KAAK,QAAQ;AAAA,UAChB,GAAG,KAAK,aAAa;AAAA,QACvB,GAAG;AAAA,UACD,GAAG,KAAK,QAAQ;AAAA,UAChB,GAAG,KAAK,aAAa,IAAI;AAAA,QAC3B,GAAG;AAAA,UACD,GAAG,KAAK,KAAK,QAAQ;AAAA,UACrB,GAAG,KAAK,aAAa,IAAI;AAAA,QAC3B,GAAG;AAAA,UACD,GAAG,KAAK,KAAK,QAAQ,IAAI;AAAA,UACzB,GAAG,KAAK,aAAa;AAAA,QACvB,GAAG;AAAA,UACD,GAAG,KAAK,KAAK,QAAQ;AAAA,UACrB,GAAG,KAAK,aAAa;AAAA,QACvB,GAAG;AAAA,UACD,GAAG,KAAK,KAAK,QAAQ;AAAA,UACrB,GAAG,KAAK,aAAa,IAAI;AAAA,QAC3B,GAAG;AAAA,UACD,GAAG,KAAK,QAAQ;AAAA,UAChB,GAAG,KAAK,aAAa,IAAI;AAAA,QAC3B,CAAC;AAAA,MACH;AACA,YAAM,MAAM,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AACzD,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,MAAM,MAAM;AAInB,SAAe,WAAW,IAAQ,IAAM,IAKrC;AAAA,6CALuB,QAAQ,MAAM;AAAA,IACtC,QAAQ;AAAA,MACN;AAAA,MACA;AAAA,IACF;AAAA,EACF,GAAG;AACD,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM,cAAc,KAAK,eAAe;AACxC,UAAM,aAAa,KAAK,cAAc;AACtC,UAAM,WAAW,KAAK,IAAI,aAAa,UAAU;AACjD,UAAM,eAAe,WAAW;AAChC,SAAK,QAAQ,KAAK,IAAI,UAAU,gBAAgB,CAAC;AACjD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,oBAAoB;AACxD,UAAM,UAAU;AAChB,UAAM,eAAe,KAAK,QAAQ,IAAI;AACtC,UAAM,WAAW,KAAK,QAAQ;AAC9B,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,QAAI,KAAK,SAAS,aAAa;AAC7B,cAAQ,YAAY;AACpB,cAAQ,YAAY;AAAA,IACtB;AACA,UAAM,OAAO,UAAU,IAAI,MAAM;AACjC,YAAQ,SAAS,QAAQ;AACzB,UAAM,WAAW,SAAS,OAAO,GAAG;AACpC,QAAI,KAAK,MAAM;AACb,eAAS,KAAK,MAAM,MAAM,WAAW,KAAK,MAAM;AAAA,QAC9C,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,gBAAgB;AAAA,MAClB,CAAC,CAAC,MAAM;AAAA,IACV;AACA,UAAM,WAAW,SAAS,KAAK,EAAE,QAAQ;AACzC,UAAM,YAAY,SAAS;AAC3B,UAAM,aAAa,SAAS;AAC5B,UAAM,QAAQ,SAAS;AACvB,UAAM,QAAQ,SAAS;AACvB,UAAM,WAAW,KAAK,IAAI,WAAW,UAAU,IAAI,KAAK,QAAQ,UAAU;AAC1E,UAAM,WAAW,GAAG,OAAO,GAAG,GAAG,UAAU,OAAO;AAClD,UAAM,aAAa,KAAK,IAAI,UAAU,KAAK,KAAK;AAChD,UAAM,cAAc,WAAW,KAAK,SAAS;AAC7C,UAAM,YAAY,GAAG,UAAU,CAAC,aAAa,GAAG,CAAC,cAAc,GAAG,YAAY,aAAa,iCACtF,UADsF;AAAA,MAEzF,MAAM;AAAA,MACN,QAAQ;AAAA,IACV,EAAC;AACD,UAAM,YAAY,SAAS,OAAO,MAAM,UAAU,cAAc;AAChE,UAAM,aAAa,SAAS,OAAO,MAAM,SAAS;AAClD,aAAS,KAAK,aAAa,aAAa,CAAC,YAAY,IAAI,KAAK,IAAI,WAAW,KAAK,SAAS,IAAI,eAAe,IAAI,aAAa,IAAI,QAAQ,CAAC,KAAK,SAAS,IAAI,eAAe,IAAI,aAAa,IAAI,KAAK,GAAG;AAC1M,aAAS,KAAK,SAAS,UAAU,UAAU,IAAI,QAAQ,KAAK,UAAU,GAAG;AACzE,UAAM,KAAK,aAAa,aAAa,CAAC,KAAK,QAAQ,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,IAAI,WAAW,CAAC,cAAc,IAAI,cAAc,IAAI,KAAK,MAAM,GAAG;AACpJ,cAAU,KAAK,aAAa,aAAa,CAAC,IAAI,WAAW,KAAK,SAAS,IAAI,eAAe,IAAI,CAAC,KAAK,SAAS,IAAI,eAAe,CAAC,GAAG;AACpI,qBAAiB,MAAM,UAAU;AACjC,SAAK,YAAY,SAAU,OAAO;AAChC,UAAI,KAAK,wBAAwB,MAAM,KAAK;AAC5C,YAAM,MAAM,kBAAkB,KAAK,MAAM,KAAK;AAC9C,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,YAAY,YAAY;AAI/B,SAAe,YAAY,IAAQ,IAAM,IAKtC;AAAA,6CALwB,QAAQ,MAAM;AAAA,IACvC,QAAQ;AAAA,MACN;AAAA,MACA;AAAA,IACF;AAAA,EACF,GAAG;AACD,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM,cAAc,KAAK,eAAe;AACxC,UAAM,aAAa,KAAK,cAAc;AACtC,UAAM,WAAW,KAAK,IAAI,aAAa,UAAU;AACjD,UAAM,eAAe,WAAW;AAChC,SAAK,QAAQ,KAAK,IAAI,UAAU,gBAAgB,CAAC;AACjD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,oBAAoB;AACxD,UAAM,WAAW,KAAK,QAAQ;AAC9B,UAAM,SAAS,WAAW,cAAc;AACxC,UAAM,QAAQ,WAAW,cAAc;AACvC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,UAAMD,KAAI,CAAC,QAAQ;AACnB,UAAMC,KAAI,CAAC,SAAS;AACpB,UAAM,eAAe,KAAK,QAAQ,IAAI;AACtC,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,QAAI,KAAK,SAAS,aAAa;AAC7B,cAAQ,YAAY;AACpB,cAAQ,YAAY;AAAA,IACtB;AACA,UAAM,OAAO,UAAU,IAAI,MAAM;AACjC,YAAQ,SAAS,QAAQ;AACzB,UAAM,WAAW,GAAG,KAAK,uBAAuBD,IAAGC,IAAG,OAAO,QAAQ,CAAC,GAAG,OAAO;AAChF,UAAM,aAAa,KAAK,IAAI,OAAO,KAAK,KAAK;AAC7C,UAAM,cAAc,SAAS,KAAK,SAAS;AAC3C,UAAM,YAAY,GAAG,UAAU,CAAC,aAAa,GAAG,CAAC,cAAc,GAAG,YAAY,aAAa,iCACtF,UADsF;AAAA,MAEzF,MAAM;AAAA,MACN,QAAQ;AAAA,IACV,EAAC;AACD,UAAM,YAAY,SAAS,OAAO,MAAM,UAAU,cAAc,EAAE,KAAK,SAAS,aAAa;AAC7F,UAAM,aAAa,SAAS,OAAO,MAAM,SAAS;AAClD,QAAI,KAAK,MAAM;AACb,YAAM,WAAW,SAAS,OAAO,GAAG;AACpC,eAAS,KAAK,MAAM,MAAM,WAAW,KAAK,MAAM;AAAA,QAC9C,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,gBAAgB;AAAA,MAClB,CAAC,CAAC,MAAM;AACR,YAAM,WAAW,SAAS,KAAK,EAAE,QAAQ;AACzC,YAAM,YAAY,SAAS;AAC3B,YAAM,aAAa,SAAS;AAC5B,YAAM,QAAQ,SAAS;AACvB,YAAM,QAAQ,SAAS;AACvB,eAAS,KAAK,aAAa,aAAa,CAAC,YAAY,IAAI,KAAK,IAAI,WAAW,KAAK,SAAS,IAAI,eAAe,IAAI,aAAa,IAAI,QAAQ,CAAC,KAAK,SAAS,IAAI,eAAe,IAAI,aAAa,IAAI,KAAK,GAAG;AAC1M,eAAS,KAAK,SAAS,UAAU,UAAU,IAAI,QAAQ,KAAK,UAAU,GAAG;AAAA,IAC3E;AACA,UAAM,KAAK,aAAa,aAAa,CAAC,KAAK,QAAQ,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,IAAI,WAAW,CAAC,cAAc,IAAI,cAAc,IAAI,KAAK,MAAM,GAAG;AACpJ,cAAU,KAAK,aAAa,aAAa,CAAC,IAAI,WAAW,KAAK,SAAS,IAAI,eAAe,IAAI,CAAC,KAAK,SAAS,IAAI,eAAe,CAAC,GAAG;AACpI,qBAAiB,MAAM,UAAU;AACjC,SAAK,YAAY,SAAU,OAAO;AAChC,UAAI,KAAK,wBAAwB,MAAM,KAAK;AAC5C,UAAI,CAAC,KAAK,OAAO;AACf,eAAO,kBAAkB,KAAK,MAAM,KAAK;AAAA,MAC3C;AACA,YAAM,KAAK,KAAK,KAAK;AACrB,YAAM,KAAK,KAAK,KAAK;AACrB,YAAM,aAAa,KAAK,UAAU;AAClC,UAAI,SAAS,CAAC;AACd,UAAI,UAAU;AACZ,iBAAS,CAAC;AAAA,UACR,GAAG,KAAK,KAAK,QAAQ;AAAA,UACrB,GAAG,KAAK,aAAa;AAAA,QACvB,GAAG;AAAA,UACD,GAAG,KAAK,KAAK,QAAQ;AAAA,UACrB,GAAG,KAAK,aAAa;AAAA,QACvB,GAAG;AAAA,UACD,GAAG,KAAK,KAAK,QAAQ;AAAA,UACrB,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS;AAAA,QACzC,GAAG;AAAA,UACD,GAAG,KAAK,QAAQ;AAAA,UAChB,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS;AAAA,QACzC,GAAG;AAAA,UACD,GAAG,KAAK,QAAQ;AAAA,UAChB,GAAG,KAAK,aAAa;AAAA,QACvB,GAAG;AAAA,UACD,GAAG,KAAK,QAAQ;AAAA,UAChB,GAAG,KAAK,aAAa;AAAA,QACvB,GAAG;AAAA,UACD,GAAG,KAAK,QAAQ;AAAA,UAChB,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS;AAAA,QACzC,GAAG;AAAA,UACD,GAAG,KAAK,KAAK,QAAQ;AAAA,UACrB,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS;AAAA,QACzC,CAAC;AAAA,MACH,OAAO;AACL,iBAAS,CAAC;AAAA,UACR,GAAG,KAAK,QAAQ;AAAA,UAChB,GAAG,KAAK,aAAa;AAAA,QACvB,GAAG;AAAA,UACD,GAAG,KAAK,QAAQ;AAAA,UAChB,GAAG,KAAK,aAAa;AAAA,QACvB,GAAG;AAAA,UACD,GAAG,KAAK,QAAQ;AAAA,UAChB,GAAG,KAAK,aAAa,IAAI;AAAA,QAC3B,GAAG;AAAA,UACD,GAAG,KAAK,KAAK,QAAQ;AAAA,UACrB,GAAG,KAAK,aAAa,IAAI;AAAA,QAC3B,GAAG;AAAA,UACD,GAAG,KAAK,KAAK,QAAQ,IAAI;AAAA,UACzB,GAAG,KAAK,aAAa;AAAA,QACvB,GAAG;AAAA,UACD,GAAG,KAAK,KAAK,QAAQ;AAAA,UACrB,GAAG,KAAK,aAAa;AAAA,QACvB,GAAG;AAAA,UACD,GAAG,KAAK,KAAK,QAAQ;AAAA,UACrB,GAAG,KAAK,aAAa,IAAI;AAAA,QAC3B,GAAG;AAAA,UACD,GAAG,KAAK,QAAQ;AAAA,UAChB,GAAG,KAAK,aAAa,IAAI;AAAA,QAC3B,CAAC;AAAA,MACH;AACA,YAAM,MAAM,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AACzD,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,aAAa,aAAa;AAIjC,SAAe,WAAW,IAAQ,IAAM,IAKrC;AAAA,6CALuB,QAAQ,MAAM;AAAA,IACtC,QAAQ;AAAA,MACN;AAAA,MACA;AAAA,IACF;AAAA,EACF,GAAG;AACD,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM,cAAc,KAAK,eAAe;AACxC,UAAM,aAAa,KAAK,cAAc;AACtC,UAAM,WAAW,KAAK,IAAI,aAAa,UAAU;AACjD,UAAM,eAAe,WAAW;AAChC,SAAK,QAAQ,KAAK,IAAI,UAAU,gBAAgB,CAAC;AACjD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,oBAAoB;AACxD,UAAM,WAAW,KAAK,QAAQ;AAC9B,UAAM,SAAS,WAAW,cAAc;AACxC,UAAM,QAAQ,WAAW,cAAc;AACvC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,UAAMD,KAAI,CAAC,QAAQ;AACnB,UAAMC,KAAI,CAAC,SAAS;AACpB,UAAM,eAAe,KAAK,QAAQ,IAAI;AACtC,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,QAAI,KAAK,SAAS,aAAa;AAC7B,cAAQ,YAAY;AACpB,cAAQ,YAAY;AAAA,IACtB;AACA,UAAM,OAAO,UAAU,IAAI,MAAM;AACjC,YAAQ,SAAS,QAAQ;AACzB,UAAM,WAAW,GAAG,KAAK,uBAAuBD,IAAGC,IAAG,OAAO,QAAQ,GAAG,GAAG,OAAO;AAClF,UAAM,aAAa,KAAK,IAAI,OAAO,KAAK,KAAK;AAC7C,UAAM,cAAc,SAAS,KAAK,SAAS;AAC3C,UAAM,YAAY,GAAG,UAAU,CAAC,aAAa,GAAG,CAAC,cAAc,GAAG,YAAY,aAAa,iCACtF,UADsF;AAAA,MAEzF,MAAM;AAAA,MACN,QAAQ;AAAA,IACV,EAAC;AACD,UAAM,YAAY,SAAS,OAAO,MAAM,UAAU,cAAc;AAChE,UAAM,aAAa,SAAS,OAAO,MAAM,SAAS;AAClD,QAAI,KAAK,MAAM;AACb,YAAM,WAAW,SAAS,OAAO,GAAG;AACpC,eAAS,KAAK,MAAM,MAAM,WAAW,KAAK,MAAM;AAAA,QAC9C,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,gBAAgB;AAAA,MAClB,CAAC,CAAC,MAAM;AACR,YAAM,WAAW,SAAS,KAAK,EAAE,QAAQ;AACzC,YAAM,YAAY,SAAS;AAC3B,YAAM,aAAa,SAAS;AAC5B,YAAM,QAAQ,SAAS;AACvB,YAAM,QAAQ,SAAS;AACvB,eAAS,KAAK,aAAa,aAAa,CAAC,YAAY,IAAI,KAAK,IAAI,WAAW,KAAK,SAAS,IAAI,eAAe,IAAI,aAAa,IAAI,QAAQ,CAAC,KAAK,SAAS,IAAI,eAAe,IAAI,aAAa,IAAI,KAAK,GAAG;AAC1M,eAAS,KAAK,SAAS,UAAU,UAAU,IAAI,QAAQ,KAAK,UAAU,GAAG;AAAA,IAC3E;AACA,UAAM,KAAK,aAAa,aAAa,CAAC,KAAK,QAAQ,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,IAAI,WAAW,CAAC,cAAc,IAAI,cAAc,IAAI,KAAK,MAAM,GAAG;AACpJ,cAAU,KAAK,aAAa,aAAa,CAAC,IAAI,WAAW,KAAK,SAAS,IAAI,eAAe,IAAI,CAAC,KAAK,SAAS,IAAI,eAAe,CAAC,GAAG;AACpI,qBAAiB,MAAM,UAAU;AACjC,SAAK,YAAY,SAAU,OAAO;AAChC,UAAI,KAAK,wBAAwB,MAAM,KAAK;AAC5C,UAAI,CAAC,KAAK,OAAO;AACf,eAAO,kBAAkB,KAAK,MAAM,KAAK;AAAA,MAC3C;AACA,YAAM,KAAK,KAAK,KAAK;AACrB,YAAM,KAAK,KAAK,KAAK;AACrB,YAAM,aAAa,KAAK,UAAU;AAClC,UAAI,SAAS,CAAC;AACd,UAAI,UAAU;AACZ,iBAAS,CAAC;AAAA,UACR,GAAG,KAAK,KAAK,QAAQ;AAAA,UACrB,GAAG,KAAK,aAAa;AAAA,QACvB,GAAG;AAAA,UACD,GAAG,KAAK,KAAK,QAAQ;AAAA,UACrB,GAAG,KAAK,aAAa;AAAA,QACvB,GAAG;AAAA,UACD,GAAG,KAAK,KAAK,QAAQ;AAAA,UACrB,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS;AAAA,QACzC,GAAG;AAAA,UACD,GAAG,KAAK,QAAQ;AAAA,UAChB,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS;AAAA,QACzC,GAAG;AAAA,UACD,GAAG,KAAK,QAAQ;AAAA,UAChB,GAAG,KAAK,aAAa;AAAA,QACvB,GAAG;AAAA,UACD,GAAG,KAAK,QAAQ;AAAA,UAChB,GAAG,KAAK,aAAa;AAAA,QACvB,GAAG;AAAA,UACD,GAAG,KAAK,QAAQ;AAAA,UAChB,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS;AAAA,QACzC,GAAG;AAAA,UACD,GAAG,KAAK,KAAK,QAAQ;AAAA,UACrB,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS;AAAA,QACzC,CAAC;AAAA,MACH,OAAO;AACL,iBAAS,CAAC;AAAA,UACR,GAAG,KAAK,QAAQ;AAAA,UAChB,GAAG,KAAK,aAAa;AAAA,QACvB,GAAG;AAAA,UACD,GAAG,KAAK,QAAQ;AAAA,UAChB,GAAG,KAAK,aAAa;AAAA,QACvB,GAAG;AAAA,UACD,GAAG,KAAK,QAAQ;AAAA,UAChB,GAAG,KAAK,aAAa,IAAI;AAAA,QAC3B,GAAG;AAAA,UACD,GAAG,KAAK,KAAK,QAAQ;AAAA,UACrB,GAAG,KAAK,aAAa,IAAI;AAAA,QAC3B,GAAG;AAAA,UACD,GAAG,KAAK,KAAK,QAAQ,IAAI;AAAA,UACzB,GAAG,KAAK,aAAa;AAAA,QACvB,GAAG;AAAA,UACD,GAAG,KAAK,KAAK,QAAQ;AAAA,UACrB,GAAG,KAAK,aAAa;AAAA,QACvB,GAAG;AAAA,UACD,GAAG,KAAK,KAAK,QAAQ;AAAA,UACrB,GAAG,KAAK,aAAa,IAAI;AAAA,QAC3B,GAAG;AAAA,UACD,GAAG,KAAK,QAAQ;AAAA,UAChB,GAAG,KAAK,aAAa,IAAI;AAAA,QAC3B,CAAC;AAAA,MACH;AACA,YAAM,MAAM,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AACzD,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,YAAY,YAAY;AAI/B,SAAe,YAAY,IAAQ,IAAM,IAItC;AAAA,6CAJwB,QAAQ,MAAM;AAAA,IACvC,QAAQ;AAAA,MACN;AAAA,IACF;AAAA,EACF,GAAG;AACD,UAAM,MAAM,IAAI,MAAM;AACtB,QAAI,MAAM,MAAM,OAAO;AACvB,UAAM,IAAI,OAAO;AACjB,UAAM,oBAAoB,OAAO,IAAI,aAAa,SAAS,EAAE,QAAQ,MAAM,EAAE,CAAC;AAC9E,UAAM,qBAAqB,OAAO,IAAI,cAAc,SAAS,EAAE,QAAQ,MAAM,EAAE,CAAC;AAChF,SAAK,mBAAmB,oBAAoB;AAC5C,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM,eAAe,WAAW;AAChC,SAAK,eAAe,WAAW;AAC/B,UAAM,gBAAgB,KAAK,IAAI,KAAK,QAAQ,gBAAgB,IAAI,GAAG,MAAM,cAAc,iBAAiB;AACxG,UAAM,aAAa,KAAK,eAAe,OAAO,MAAM,cAAc,KAAK,cAAc,KAAK,mBAAmB,gBAAgB;AAC7H,UAAM,cAAc,KAAK,eAAe,OAAO,aAAa,KAAK,mBAAmB,MAAM,eAAe;AACzG,SAAK,QAAQ,KAAK,IAAI,YAAY,gBAAgB,CAAC;AACnD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,qBAAqB;AACzD,UAAM,WAAW,KAAK,QAAQ;AAC9B,UAAMD,KAAI,CAAC,aAAa;AACxB,UAAMC,KAAI,CAAC,cAAc;AACzB,UAAM,eAAe,KAAK,QAAQ,IAAI;AACtC,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,QAAI,KAAK,SAAS,aAAa;AAC7B,cAAQ,YAAY;AACpB,cAAQ,YAAY;AAAA,IACtB;AACA,UAAM,YAAY,GAAG,UAAUD,IAAGC,IAAG,YAAY,aAAa,OAAO;AACrE,UAAM,aAAa,KAAK,IAAI,YAAY,KAAK,KAAK;AAClD,UAAM,cAAc,cAAc,KAAK,SAAS;AAChD,UAAM,YAAY,GAAG,UAAU,CAAC,aAAa,GAAG,CAAC,cAAc,GAAG,YAAY,aAAa,iCACtF,UADsF;AAAA,MAEzF,MAAM;AAAA,MACN,QAAQ;AAAA,IACV,EAAC;AACD,UAAM,YAAY,SAAS,OAAO,MAAM,WAAW,cAAc;AACjE,UAAM,aAAa,SAAS,OAAO,MAAM,SAAS;AAClD,QAAI,KAAK,KAAK;AACZ,YAAM,QAAQ,SAAS,OAAO,OAAO;AACrC,YAAM,KAAK,QAAQ,KAAK,GAAG;AAC3B,YAAM,KAAK,SAAS,UAAU;AAC9B,YAAM,KAAK,UAAU,WAAW;AAChC,YAAM,KAAK,uBAAuB,MAAM;AACxC,YAAM,KAAK,aAAa,aAAa,CAAC,aAAa,CAAC,IAAI,WAAW,cAAc,IAAI,cAAc,CAAC,cAAc,CAAC,GAAG;AAAA,IACxH;AACA,UAAM,KAAK,aAAa,aAAa,CAAC,KAAK,QAAQ,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,IAAI,WAAW,CAAC,cAAc,IAAI,KAAK,SAAS,IAAI,eAAe,IAAI,cAAc,IAAI,KAAK,SAAS,IAAI,eAAe,CAAC,GAAG;AAChN,cAAU,KAAK,aAAa,aAAa,CAAC,IAAI,WAAW,KAAK,SAAS,IAAI,eAAe,IAAI,CAAC,KAAK,SAAS,IAAI,eAAe,CAAC,GAAG;AACpI,qBAAiB,MAAM,UAAU;AACjC,SAAK,YAAY,SAAU,OAAO;AAChC,UAAI,KAAK,wBAAwB,MAAM,KAAK;AAC5C,UAAI,CAAC,KAAK,OAAO;AACf,eAAO,kBAAkB,KAAK,MAAM,KAAK;AAAA,MAC3C;AACA,YAAM,KAAK,KAAK,KAAK;AACrB,YAAM,KAAK,KAAK,KAAK;AACrB,YAAM,aAAa,KAAK,UAAU;AAClC,UAAI,SAAS,CAAC;AACd,UAAI,UAAU;AACZ,iBAAS,CAAC;AAAA,UACR,GAAG,KAAK,KAAK,QAAQ;AAAA,UACrB,GAAG,KAAK,aAAa;AAAA,QACvB,GAAG;AAAA,UACD,GAAG,KAAK,KAAK,QAAQ;AAAA,UACrB,GAAG,KAAK,aAAa;AAAA,QACvB,GAAG;AAAA,UACD,GAAG,KAAK,KAAK,QAAQ;AAAA,UACrB,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS;AAAA,QACzC,GAAG;AAAA,UACD,GAAG,KAAK,aAAa;AAAA,UACrB,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS;AAAA,QACzC,GAAG;AAAA,UACD,GAAG,KAAK,aAAa;AAAA,UACrB,GAAG,KAAK,aAAa;AAAA,QACvB,GAAG;AAAA,UACD,GAAG,KAAK,aAAa;AAAA,UACrB,GAAG,KAAK,aAAa;AAAA,QACvB,GAAG;AAAA,UACD,GAAG,KAAK,aAAa;AAAA,UACrB,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS;AAAA,QACzC,GAAG;AAAA,UACD,GAAG,KAAK,KAAK,QAAQ;AAAA,UACrB,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS;AAAA,QACzC,CAAC;AAAA,MACH,OAAO;AACL,iBAAS,CAAC;AAAA,UACR,GAAG,KAAK,aAAa;AAAA,UACrB,GAAG,KAAK,aAAa;AAAA,QACvB,GAAG;AAAA,UACD,GAAG,KAAK,aAAa;AAAA,UACrB,GAAG,KAAK,aAAa;AAAA,QACvB,GAAG;AAAA,UACD,GAAG,KAAK,aAAa;AAAA,UACrB,GAAG,KAAK,aAAa,IAAI;AAAA,QAC3B,GAAG;AAAA,UACD,GAAG,KAAK,KAAK,QAAQ;AAAA,UACrB,GAAG,KAAK,aAAa,IAAI;AAAA,QAC3B,GAAG;AAAA,UACD,GAAG,KAAK,KAAK,QAAQ,IAAI;AAAA,UACzB,GAAG,KAAK,aAAa;AAAA,QACvB,GAAG;AAAA,UACD,GAAG,KAAK,KAAK,QAAQ;AAAA,UACrB,GAAG,KAAK,aAAa;AAAA,QACvB,GAAG;AAAA,UACD,GAAG,KAAK,KAAK,QAAQ;AAAA,UACrB,GAAG,KAAK,aAAa,IAAI;AAAA,QAC3B,GAAG;AAAA,UACD,GAAG,KAAK,aAAa;AAAA,UACrB,GAAG,KAAK,aAAa,IAAI;AAAA,QAC3B,CAAC;AAAA,MACH;AACA,YAAM,MAAM,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AACzD,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,aAAa,aAAa;AAIjC,SAAe,cAAc,QAAQ,MAAM;AAAA;AACzC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAMC,KAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,GAAG,MAAM,SAAS,CAAC;AACzE,UAAMC,KAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,GAAG,MAAM,UAAU,CAAC;AAC3E,UAAM,SAAS,CAAC;AAAA,MACd,GAAG;AAAA,MACH,GAAG;AAAA,IACL,GAAG;AAAA,MACD,GAAGD;AAAA,MACH,GAAG;AAAA,IACL,GAAG;AAAA,MACD,GAAGA,KAAI,IAAIC,KAAI;AAAA,MACf,GAAG,CAACA;AAAA,IACN,GAAG;AAAA,MACD,GAAG,KAAKA,KAAI;AAAA,MACZ,GAAG,CAACA;AAAA,IACN,CAAC;AACD,QAAI;AACJ,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,KAAK,SAAS,aAAa;AAC7B,YAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,YAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,YAAM,WAAW,qBAAqB,MAAM;AAC5C,YAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAC3C,gBAAU,SAAS,OAAO,MAAM,WAAW,cAAc,EAAE,KAAK,aAAa,aAAa,CAACD,KAAI,CAAC,KAAKC,KAAI,CAAC,GAAG;AAC7G,UAAI,WAAW;AACb,gBAAQ,KAAK,SAAS,SAAS;AAAA,MACjC;AAAA,IACF,OAAO;AACL,gBAAU,mBAAmB,UAAUD,IAAGC,IAAG,MAAM;AAAA,IACrD;AACA,QAAI,YAAY;AACd,cAAQ,KAAK,SAAS,UAAU;AAAA,IAClC;AACA,SAAK,QAAQD;AACb,SAAK,SAASC;AACd,qBAAiB,MAAM,OAAO;AAC9B,SAAK,YAAY,SAAU,OAAO;AAChC,aAAO,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AAAA,IACtD;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,eAAe,eAAe;AAIrC,SAAe,SAAS,QAAQ,MAAM,SAAS;AAAA;AAC7C,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAM,aAAa,KAAK,IAAI,KAAK,QAAQ,QAAQ,gBAAgB,GAAG,MAAM,SAAS,CAAC;AACpF,UAAM,cAAc,KAAK,IAAI,KAAK,SAAS,QAAQ,gBAAgB,GAAG,MAAM,UAAU,CAAC;AACvF,UAAMH,KAAI,CAAC,aAAa;AACxB,UAAMC,KAAI,CAAC,cAAc;AACzB,QAAI;AACJ,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,SAAS,MAAM,QAAQ,IAAI;AAC7B,WAAK,QAAQ;AACb,WAAK,QAAQ;AAAA,IACf;AACA,QAAI,KAAK,SAAS,aAAa;AAC7B,YAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,YAAM,WAAW,kBAAkB,MAAM,CAAC,CAAC;AAC3C,YAAM,YAAY,MAAM,KAAK,GAAG,KAAK,uBAAuBD,IAAGC,IAAG,YAAY,aAAa,MAAM,CAAC,GAAG,QAAQ,IAAI,GAAG,UAAUD,IAAGC,IAAG,YAAY,aAAa,QAAQ;AACrK,cAAQ,SAAS,OAAO,MAAM,WAAW,cAAc;AACvD,YAAM,KAAK,SAAS,uBAAuB,EAAE,KAAK,SAAS,oBAAoB,SAAS,CAAC;AAAA,IAC3F,OAAO;AACL,cAAQ,SAAS,OAAO,QAAQ,cAAc;AAC9C,YAAM,KAAK,SAAS,uBAAuB,EAAE,KAAK,SAAS,UAAU,EAAE,KAAK,MAAM,oBAAoB,EAAE,CAAC,EAAE,KAAK,MAAM,oBAAoB,EAAE,CAAC,EAAE,KAAK,KAAKD,EAAC,EAAE,KAAK,KAAKC,EAAC,EAAE,KAAK,SAAS,UAAU,EAAE,KAAK,UAAU,WAAW;AAAA,IAC/N;AACA,qBAAiB,MAAM,KAAK;AAC5B,SAAK,YAAY,SAAU,OAAO;AAChC,aAAO,kBAAkB,KAAK,MAAM,KAAK;AAAA,IAC3C;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,UAAU,UAAU;AAG3B,SAAe,UAAU,QAAQ,MAAM;AAAA;AACrC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,OAAO;AAC3C,UAAM,QAAQ,SAAS,OAAO,QAAQ,cAAc;AACpD,UAAM,aAAa;AACnB,UAAM,cAAc;AACpB,UAAM,KAAK,SAAS,UAAU,EAAE,KAAK,UAAU,WAAW;AAC1D,aAAS,KAAK,SAAS,iBAAiB;AACxC,UAAM,KAAK,aAAa,aAAa,EAAE,KAAK,QAAQ,MAAM,KAAK,KAAK,KAAK,QAAQ,GAAG,KAAK,EAAE,KAAK,SAAS,MAAM,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG;AAC3I,qBAAiB,MAAM,KAAK;AAC5B,SAAK,YAAY,SAAU,OAAO;AAChC,aAAO,kBAAkB,KAAK,MAAM,KAAK;AAAA,IAC3C;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,WAAW,WAAW;AAI7B,SAAe,UAAU,QAAQ,MAAM;AAAA;AACrC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAMC,KAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,IAAI,MAAM,SAAS,CAAC;AACrE,UAAMC,KAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,IAAI,MAAM,UAAU,CAAC;AACvE,UAAM,SAAS,CAAC;AAAA,MACd,GAAG;AAAA,MACH,GAAG;AAAA,IACL,GAAG;AAAA,MACD,GAAGD,KAAI,IAAIC,KAAI;AAAA,MACf,GAAG;AAAA,IACL,GAAG;AAAA,MACD,GAAGD;AAAA,MACH,GAAG,CAACC;AAAA,IACN,GAAG;AAAA,MACD,GAAG,EAAE,IAAIA,MAAK;AAAA,MACd,GAAG,CAACA;AAAA,IACN,CAAC;AACD,QAAI;AACJ,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,KAAK,SAAS,aAAa;AAC7B,YAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,YAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,YAAM,WAAW,qBAAqB,MAAM;AAC5C,YAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAC3C,gBAAU,SAAS,OAAO,MAAM,WAAW,cAAc,EAAE,KAAK,aAAa,aAAa,CAACD,KAAI,CAAC,KAAKC,KAAI,CAAC,GAAG;AAC7G,UAAI,WAAW;AACb,gBAAQ,KAAK,SAAS,SAAS;AAAA,MACjC;AAAA,IACF,OAAO;AACL,gBAAU,mBAAmB,UAAUD,IAAGC,IAAG,MAAM;AAAA,IACrD;AACA,QAAI,YAAY;AACd,cAAQ,KAAK,SAAS,UAAU;AAAA,IAClC;AACA,SAAK,QAAQD;AACb,SAAK,SAASC;AACd,qBAAiB,MAAM,OAAO;AAC9B,SAAK,YAAY,SAAU,OAAO;AAChC,aAAO,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AAAA,IACtD;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,WAAW,WAAW;AAI7B,SAAe,WAAW,QAAQ,MAAM;AAAA;AACtC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAMD,KAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,IAAI,MAAM,SAAS,CAAC;AACrE,UAAMC,KAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,IAAI,MAAM,UAAU,CAAC;AACvE,UAAM,SAAS,CAAC;AAAA,MACd,GAAG,KAAKA,KAAI;AAAA,MACZ,GAAG;AAAA,IACL,GAAG;AAAA,MACD,GAAGD;AAAA,MACH,GAAG;AAAA,IACL,GAAG;AAAA,MACD,GAAGA,KAAI,IAAIC,KAAI;AAAA,MACf,GAAG,CAACA;AAAA,IACN,GAAG;AAAA,MACD,GAAG;AAAA,MACH,GAAG,CAACA;AAAA,IACN,CAAC;AACD,QAAI;AACJ,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,KAAK,SAAS,aAAa;AAC7B,YAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,YAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,YAAM,WAAW,qBAAqB,MAAM;AAC5C,YAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAC3C,gBAAU,SAAS,OAAO,MAAM,WAAW,cAAc,EAAE,KAAK,aAAa,aAAa,CAACD,KAAI,CAAC,KAAKC,KAAI,CAAC,GAAG;AAC7G,UAAI,WAAW;AACb,gBAAQ,KAAK,SAAS,SAAS;AAAA,MACjC;AAAA,IACF,OAAO;AACL,gBAAU,mBAAmB,UAAUD,IAAGC,IAAG,MAAM;AAAA,IACrD;AACA,QAAI,YAAY;AACd,cAAQ,KAAK,SAAS,UAAU;AAAA,IAClC;AACA,SAAK,QAAQD;AACb,SAAK,SAASC;AACd,qBAAiB,MAAM,OAAO;AAC9B,SAAK,YAAY,SAAU,OAAO;AAChC,aAAO,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AAAA,IACtD;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,YAAY,YAAY;AAI/B,SAAS,cAAc,QAAQ,MAAM;AACnC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,cAAc,IAAI;AACtB,OAAK,QAAQ;AACb,OAAK,aAAa;AAClB,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,eAAe,IAAI,CAAC,EAAE,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AACxG,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ,KAAK,IAAI,IAAI,MAAM,SAAS,CAAC;AAC3C,QAAM,SAAS,KAAK,IAAI,IAAI,MAAM,UAAU,CAAC;AAC7C,QAAM,MAAM;AACZ,QAAM,SAAS,CAAC;AAAA,IACd,GAAG;AAAA,IACH,GAAG;AAAA,EACL,GAAG;AAAA,IACD,GAAG;AAAA,IACH,GAAG,SAAS,MAAM;AAAA,EACpB,GAAG;AAAA,IACD,GAAG,QAAQ,IAAI;AAAA,IACf,GAAG,SAAS,MAAM;AAAA,EACpB,GAAG;AAAA,IACD,GAAG;AAAA,IACH,GAAG,IAAI;AAAA,EACT,GAAG;AAAA,IACD,GAAG;AAAA,IACH,GAAG,SAAS,MAAM;AAAA,EACpB,GAAG;AAAA,IACD,GAAG,IAAI;AAAA,IACP,GAAG,SAAS,MAAM;AAAA,EACpB,CAAC;AACD,QAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,WAAW,qBAAqB,MAAM;AAC5C,QAAM,WAAW,GAAG,KAAK,UAAU,OAAO;AAC1C,QAAM,iBAAiB,SAAS,OAAO,MAAM,UAAU,cAAc;AACrE,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,mBAAe,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EAC1D;AACA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,mBAAe,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EAC3D;AACA,iBAAe,KAAK,aAAa,cAAc,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG;AACtE,mBAAiB,MAAM,cAAc;AACrC,OAAK,YAAY,SAAU,OAAO;AAChC,QAAI,KAAK,2BAA2B,MAAM,KAAK;AAC/C,UAAM,MAAM,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AACzD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,OAAO,eAAe,eAAe;AAIrC,IAAI,uBAAsC,OAAO,CAACH,IAAGC,IAAG,OAAO,QAAQ,IAAI,IAAI,gBAAgB;AAC7F,SAAO,CAAC,IAAID,EAAC,IAAIC,KAAI,EAAE,IAAI,IAAI,EAAE,IAAI,EAAE,UAAU,KAAK,MAAM,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC,KAAK,MAAM,MAAM,MAAM,IAAI,IAAI,EAAE,IAAI,EAAE,UAAU,KAAK,MAAM,MAAM,CAAC,MAAM,IAAI,IAAID,EAAC,IAAIC,KAAI,KAAK,WAAW,IAAI,IAAI,EAAE,IAAI,EAAE,UAAU,KAAK,IAAI,EAAE,KAAK,GAAG;AAC5O,GAAG,qBAAqB;AACxB,IAAI,4BAA2C,OAAO,CAACD,IAAGC,IAAG,OAAO,QAAQ,IAAI,IAAI,gBAAgB;AAClG,SAAO,CAAC,IAAID,EAAC,IAAIC,KAAI,EAAE,IAAI,IAAID,KAAI,KAAK,IAAIC,KAAI,EAAE,IAAI,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC,KAAK,MAAM,MAAM,MAAM,IAAI,IAAI,EAAE,IAAI,EAAE,UAAU,KAAK,MAAM,MAAM,CAAC,MAAM,IAAI,IAAID,EAAC,IAAIC,KAAI,KAAK,WAAW,IAAI,IAAI,EAAE,IAAI,EAAE,UAAU,KAAK,IAAI,EAAE,KAAK,GAAG;AACtO,GAAG,0BAA0B;AAC7B,IAAI,4BAA2C,OAAO,CAACD,IAAGC,IAAG,OAAO,QAAQ,IAAI,OAAO;AACrF,SAAO,CAAC,IAAID,KAAI,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,EAAE,IAAI,EAAE,UAAU,KAAK,IAAI,EAAE,KAAK,GAAG;AACvF,GAAG,0BAA0B;AAC7B,SAAe,cAAc,QAAQ,MAAM;AAAA;AACzC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAME,KAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,IAAI,KAAK,SAAS,CAAC;AACpE,UAAM,KAAKA,KAAI;AACf,UAAM,KAAK,MAAM,MAAMA,KAAI;AAC3B,UAAMC,KAAI,KAAK,IAAI,KAAK,SAAS,MAAM,KAAK,WAAW,IAAI,KAAK,UAAU,CAAC;AAC3E,UAAM,cAAcA,KAAI;AACxB,QAAI;AACJ,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,KAAK,SAAS,aAAa;AAC7B,YAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,YAAM,gBAAgB,0BAA0B,GAAG,GAAGD,IAAGC,IAAG,IAAI,IAAI,WAAW;AAC/E,YAAM,gBAAgB,0BAA0B,GAAG,IAAID,IAAGC,IAAG,IAAI,EAAE;AACnE,YAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,YAAM,YAAY,GAAG,KAAK,eAAe,OAAO;AAChD,YAAM,YAAY,GAAG,KAAK,eAAe,OAAO;AAChD,YAAM,cAAc,SAAS,OAAO,MAAM,WAAW,cAAc;AACnE,kBAAY,KAAK,SAAS,MAAM;AAChC,kBAAY,SAAS,OAAO,MAAM,WAAW,cAAc;AAC3D,gBAAU,KAAK,SAAS,uBAAuB;AAC/C,UAAI,WAAW;AACb,kBAAU,KAAK,SAAS,SAAS;AAAA,MACnC;AAAA,IACF,OAAO;AACL,YAAM,WAAW,qBAAqB,GAAG,GAAGD,IAAGC,IAAG,IAAI,IAAI,WAAW;AACrE,kBAAY,SAAS,OAAO,QAAQ,cAAc,EAAE,KAAK,KAAK,QAAQ,EAAE,KAAK,SAAS,uBAAuB,EAAE,KAAK,SAAS,oBAAoB,SAAS,CAAC,EAAE,KAAK,SAAS,UAAU;AAAA,IACvL;AACA,cAAU,KAAK,kBAAkB,EAAE;AACnC,cAAU,KAAK,aAAa,aAAa,CAACD,KAAI,CAAC,KAAK,EAAEC,KAAI,IAAI,GAAG,GAAG;AACpE,qBAAiB,MAAM,SAAS;AAChC,UAAM,KAAK,aAAa,aAAa,EAAE,KAAK,QAAQ,MAAM,KAAK,KAAK,KAAK,QAAQ,GAAG,KAAK,EAAE,KAAK,SAAS,KAAK,MAAM,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG;AAChJ,SAAK,YAAY,SAAU,OAAO;AAChC,YAAM,MAAM,kBAAkB,KAAK,MAAM,KAAK;AAC9C,YAAMH,KAAI,IAAI,KAAK,KAAK,KAAK;AAC7B,UAAI,MAAM,MAAM,KAAK,IAAIA,EAAC,KAAK,KAAK,SAAS,KAAK,KAAK,KAAK,IAAIA,EAAC,MAAM,KAAK,SAAS,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,EAAE,KAAK,KAAK,UAAU,KAAK,IAAI,KAAK;AAC7J,YAAIC,KAAI,KAAK,MAAM,IAAID,KAAIA,MAAK,KAAK;AACrC,YAAIC,KAAI,GAAG;AACT,UAAAA,KAAI,KAAK,KAAKA,EAAC;AAAA,QACjB;AACA,QAAAA,KAAI,KAAKA;AACT,YAAI,MAAM,KAAK,KAAK,KAAK,KAAK,GAAG;AAC/B,UAAAA,KAAI,CAACA;AAAA,QACP;AACA,YAAI,KAAKA;AAAA,MACX;AACA,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,eAAe,eAAe;AAIrC,SAAe,mBAAmB,QAAQ,MAAM;AAAA;AAC9C,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAMC,KAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,GAAG,MAAM,SAAS,CAAC;AACzE,UAAMC,KAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,GAAG,MAAM,UAAU,CAAC;AAC3E,UAAM,gBAAgBA,KAAI;AAC1B,UAAM,SAASA,KAAI;AACnB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,QAAI,KAAK,SAAS,aAAa;AAC7B,cAAQ,YAAY;AACpB,cAAQ,YAAY;AAAA,IACtB;AACA,UAAM,SAAS,CAAC;AAAA,MACd,GAAG,CAACD,KAAI,IAAIA,KAAI,IAAI;AAAA,MACpB,GAAG,CAAC,SAAS;AAAA,IACf,GAAG;AAAA,MACD,GAAG,CAACA,KAAI,IAAIA,KAAI,IAAI;AAAA,MACpB,GAAG,SAAS;AAAA,IACd,GAAG,GAAG,2BAA2B,CAACA,KAAI,IAAIA,KAAI,IAAI,KAAK,SAAS,GAAGA,KAAI,IAAIA,KAAI,IAAI,KAAK,SAAS,GAAG,eAAe,GAAG,GAAG;AAAA,MACvH,GAAGA,KAAI,IAAIA,KAAI,IAAI;AAAA,MACnB,GAAG,CAAC,SAAS;AAAA,IACf,GAAG;AAAA,MACD,GAAG,CAACA,KAAI,IAAIA,KAAI,IAAI;AAAA,MACpB,GAAG,CAAC,SAAS;AAAA,IACf,GAAG;AAAA,MACD,GAAG,CAACA,KAAI;AAAA,MACR,GAAG,CAAC,SAAS;AAAA,IACf,GAAG;AAAA,MACD,GAAG,CAACA,KAAI;AAAA,MACR,GAAG,SAAS,IAAI;AAAA,IAClB,GAAG;AAAA,MACD,GAAG,CAACA,KAAI;AAAA,MACR,GAAG,CAAC,SAAS;AAAA,IACf,CAAC;AACD,UAAM,OAAO,GAAG,QAAQ,OAAO,IAAI,CAAAL,OAAK,CAACA,GAAE,GAAGA,GAAE,CAAC,CAAC,GAAG,OAAO;AAC5D,UAAM,eAAe,SAAS,OAAO,MAAM,MAAM,cAAc;AAC/D,iBAAa,KAAK,SAAS,uBAAuB;AAClD,QAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,mBAAa,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,IACxD;AACA,QAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,mBAAa,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,IACzD;AACA,iBAAa,KAAK,aAAa,eAAe,CAAC,gBAAgB,CAAC,GAAG;AACnE,UAAM,KAAK,aAAa,aAAa,CAACK,KAAI,KAAK,KAAK,WAAW,KAAKA,KAAI,IAAI,MAAM,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,IAAI,CAACC,KAAI,KAAK,KAAK,WAAW,KAAK,gBAAgB,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG;AACrM,qBAAiB,MAAM,YAAY;AACnC,SAAK,YAAY,SAAU,OAAO;AAChC,YAAM,MAAM,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AACzD,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,oBAAoB,oBAAoB;AAI/C,SAAe,UAAU,QAAQ,MAAM;AAAA;AACrC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAMD,KAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,GAAG,MAAM,SAAS,CAAC;AACzE,UAAMC,KAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,GAAG,MAAM,UAAU,CAAC;AAC3E,UAAM,aAAa;AACnB,UAAMH,KAAI,CAACE,KAAI;AACf,UAAMD,KAAI,CAACE,KAAI;AACf,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,UAAM,kBAAkB,CAAC;AAAA,MACvB,GAAGH,KAAI;AAAA,MACP,GAAGC,KAAI;AAAA,IACT,GAAG;AAAA,MACD,GAAGD,KAAI;AAAA,MACP,GAAGC,KAAIE,KAAI;AAAA,IACb,GAAG;AAAA,MACD,GAAGH,KAAIE,KAAI;AAAA,MACX,GAAGD,KAAIE,KAAI;AAAA,IACb,GAAG;AAAA,MACD,GAAGH,KAAIE,KAAI;AAAA,MACX,GAAGD,KAAIE;AAAA,IACT,GAAG;AAAA,MACD,GAAGH,KAAIE;AAAA,MACP,GAAGD,KAAIE;AAAA,IACT,GAAG;AAAA,MACD,GAAGH,KAAIE;AAAA,MACP,GAAGD,KAAIE,KAAI;AAAA,IACb,GAAG;AAAA,MACD,GAAGH,KAAIE,KAAI;AAAA,MACX,GAAGD,KAAIE,KAAI;AAAA,IACb,GAAG;AAAA,MACD,GAAGH,KAAIE,KAAI;AAAA,MACX,GAAGD,KAAI;AAAA,IACT,GAAG;AAAA,MACD,GAAGD,KAAI;AAAA,MACP,GAAGC,KAAI;AAAA,IACT,GAAG;AAAA,MACD,GAAGD,KAAI;AAAA,MACP,GAAAC;AAAA,IACF,GAAG;AAAA,MACD,GAAAD;AAAA,MACA,GAAAC;AAAA,IACF,GAAG;AAAA,MACD,GAAAD;AAAA,MACA,GAAGC,KAAI;AAAA,IACT,CAAC;AACD,UAAM,kBAAkB,CAAC;AAAA,MACvB,GAAAD;AAAA,MACA,GAAGC,KAAI;AAAA,IACT,GAAG;AAAA,MACD,GAAGD,KAAIE,KAAI;AAAA,MACX,GAAGD,KAAI;AAAA,IACT,GAAG;AAAA,MACD,GAAGD,KAAIE,KAAI;AAAA,MACX,GAAGD,KAAIE;AAAA,IACT,GAAG;AAAA,MACD,GAAGH,KAAIE;AAAA,MACP,GAAGD,KAAIE;AAAA,IACT,GAAG;AAAA,MACD,GAAGH,KAAIE;AAAA,MACP,GAAAD;AAAA,IACF,GAAG;AAAA,MACD,GAAAD;AAAA,MACA,GAAAC;AAAA,IACF,CAAC;AACD,QAAI,KAAK,SAAS,aAAa;AAC7B,cAAQ,YAAY;AACpB,cAAQ,YAAY;AAAA,IACtB;AACA,UAAM,YAAY,qBAAqB,eAAe;AACtD,UAAM,YAAY,GAAG,KAAK,WAAW,OAAO;AAC5C,UAAM,YAAY,qBAAqB,eAAe;AACtD,UAAM,YAAY,GAAG,KAAK,WAAW,iCAChC,UADgC;AAAA,MAEnC,MAAM;AAAA,IACR,EAAC;AACD,UAAM,aAAa,SAAS,OAAO,MAAM,WAAW,cAAc;AAClE,eAAW,OAAO,MAAM,WAAW,cAAc;AACjD,eAAW,KAAK,SAAS,uBAAuB;AAChD,QAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,iBAAW,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,IACtD;AACA,QAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,iBAAW,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,IACvD;AACA,UAAM,KAAK,aAAa,aAAa,EAAE,KAAK,QAAQ,KAAK,cAAc,KAAK,KAAK,KAAK,QAAQ,GAAG,KAAK,EAAE,KAAK,SAAS,KAAK,cAAc,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG;AACrK,qBAAiB,MAAM,UAAU;AACjC,SAAK,YAAY,SAAU,OAAO;AAChC,YAAM,MAAM,kBAAkB,QAAQ,MAAM,iBAAiB,KAAK;AAClE,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,WAAW,WAAW;AAI7B,SAAe,wBAAwB,QAAQ,MAAM;AAAA;AACnD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAMC,KAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,GAAG,MAAM,SAAS,CAAC;AACzE,UAAMC,KAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,GAAG,MAAM,UAAU,CAAC;AAC3E,UAAM,gBAAgBA,KAAI;AAC1B,UAAM,SAASA,KAAI;AACnB,UAAMH,KAAI,CAACE,KAAI;AACf,UAAMD,KAAI,CAAC,SAAS;AACpB,UAAM,aAAa;AACnB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,aAAa,2BAA2BD,KAAI,YAAYC,KAAI,SAAS,YAAYD,KAAIE,KAAI,YAAYD,KAAI,SAAS,YAAY,eAAe,GAAG;AACtJ,UAAM,gBAAgB,aAAa,WAAW,SAAS,CAAC;AACxD,UAAM,kBAAkB,CAAC;AAAA,MACvB,GAAGD,KAAI;AAAA,MACP,GAAGC,KAAI;AAAA,IACT,GAAG;AAAA,MACD,GAAGD,KAAI;AAAA,MACP,GAAGC,KAAI,SAAS;AAAA,IAClB,GAAG,GAAG,YAAY;AAAA,MAChB,GAAGD,KAAIE,KAAI;AAAA,MACX,GAAG,cAAc,IAAI;AAAA,IACvB,GAAG;AAAA,MACD,GAAGF,KAAIE;AAAA,MACP,GAAG,cAAc,IAAI;AAAA,IACvB,GAAG;AAAA,MACD,GAAGF,KAAIE;AAAA,MACP,GAAG,cAAc,IAAI,IAAI;AAAA,IAC3B,GAAG;AAAA,MACD,GAAGF,KAAIE,KAAI;AAAA,MACX,GAAG,cAAc,IAAI,IAAI;AAAA,IAC3B,GAAG;AAAA,MACD,GAAGF,KAAIE,KAAI;AAAA,MACX,GAAGD,KAAI;AAAA,IACT,GAAG;AAAA,MACD,GAAGD,KAAI;AAAA,MACP,GAAGC,KAAI;AAAA,IACT,GAAG;AAAA,MACD,GAAGD,KAAI;AAAA,MACP,GAAAC;AAAA,IACF,GAAG;AAAA,MACD,GAAAD;AAAA,MACA,GAAAC;AAAA,IACF,GAAG;AAAA,MACD,GAAAD;AAAA,MACA,GAAGC,KAAI;AAAA,IACT,CAAC;AACD,UAAM,kBAAkB,CAAC;AAAA,MACvB,GAAAD;AAAA,MACA,GAAGC,KAAI;AAAA,IACT,GAAG;AAAA,MACD,GAAGD,KAAIE,KAAI;AAAA,MACX,GAAGD,KAAI;AAAA,IACT,GAAG;AAAA,MACD,GAAGD,KAAIE,KAAI;AAAA,MACX,GAAG,cAAc,IAAI;AAAA,IACvB,GAAG;AAAA,MACD,GAAGF,KAAIE;AAAA,MACP,GAAG,cAAc,IAAI;AAAA,IACvB,GAAG;AAAA,MACD,GAAGF,KAAIE;AAAA,MACP,GAAAD;AAAA,IACF,GAAG;AAAA,MACD,GAAAD;AAAA,MACA,GAAAC;AAAA,IACF,CAAC;AACD,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,QAAI,KAAK,SAAS,aAAa;AAC7B,cAAQ,YAAY;AACpB,cAAQ,YAAY;AAAA,IACtB;AACA,UAAM,YAAY,qBAAqB,eAAe;AACtD,UAAM,YAAY,GAAG,KAAK,WAAW,OAAO;AAC5C,UAAM,YAAY,qBAAqB,eAAe;AACtD,UAAM,YAAY,GAAG,KAAK,WAAW,OAAO;AAC5C,UAAM,QAAQ,SAAS,OAAO,MAAM,WAAW,cAAc;AAC7D,UAAM,OAAO,MAAM,SAAS;AAC5B,UAAM,KAAK,SAAS,uBAAuB;AAC3C,QAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,YAAM,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,IACjD;AACA,QAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,YAAM,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,IAClD;AACA,UAAM,KAAK,aAAa,eAAe,CAAC,gBAAgB,CAAC,GAAG;AAC5D,UAAM,KAAK,aAAa,aAAa,EAAE,KAAK,QAAQ,KAAK,cAAc,KAAK,KAAK,KAAK,QAAQ,GAAG,KAAK,EAAE,KAAK,SAAS,KAAK,aAAa,gBAAgB,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG;AACzL,qBAAiB,MAAM,KAAK;AAC5B,SAAK,YAAY,SAAU,OAAO;AAChC,YAAM,MAAM,kBAAkB,QAAQ,MAAM,iBAAiB,KAAK;AAClE,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,yBAAyB,yBAAyB;AAIzD,SAAe,KAAK,IAAQ,IAAM,IAI/B;AAAA,6CAJiB,QAAQ,MAAM;AAAA,IAChC,QAAQ;AAAA,MACN;AAAA,IACF;AAAA,EACF,GAAG;AACD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM,gBAAgB,KAAK,iBAAiB,UAAU,EAAE,WAAW,eAAe;AAClF,QAAI,CAAC,eAAe;AAClB,WAAK,cAAc;AAAA,IACrB;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAM,aAAa,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,GAAG,MAAM,SAAS,CAAC;AAClF,UAAM,cAAc,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,GAAG,MAAM,UAAU,CAAC;AACrF,UAAMD,KAAI,CAAC,aAAa;AACxB,UAAMC,KAAI,CAAC,cAAc;AACzB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,UAAU,kBAAkB,MAAM;AAAA,MACtC,MAAM,eAAe;AAAA,MACrB,QAAQ,eAAe;AAAA,IACzB,CAAC;AACD,QAAI,KAAK,SAAS,aAAa;AAC7B,cAAQ,YAAY;AACpB,cAAQ,YAAY;AAAA,IACtB;AACA,UAAM,gBAAgB,GAAG,UAAUD,IAAGC,IAAG,YAAY,aAAa,OAAO;AACzE,UAAM,QAAQ,SAAS,OAAO,MAAM,eAAe,cAAc;AACjE,UAAM,KAAK,SAAS,uBAAuB;AAC3C,QAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,YAAM,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,IACjD;AACA,QAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,YAAM,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,IAClD;AACA,qBAAiB,MAAM,KAAK;AAC5B,SAAK,YAAY,SAAU,OAAO;AAChC,aAAO,kBAAkB,KAAK,MAAM,KAAK;AAAA,IAC3C;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,MAAM,MAAM;AAInB,IAAI,yBAAwC,OAAO,CAACD,IAAGC,IAAG,SAAS;AACjE,SAAO,CAAC,IAAID,KAAI,OAAO,CAAC,IAAIC,EAAC,IAAI,IAAID,KAAI,IAAI,IAAIC,KAAI,OAAO,CAAC,IAAI,IAAID,KAAI,OAAO,CAAC,IAAIC,KAAI,IAAI,IAAI,IAAID,EAAC,IAAIC,KAAI,OAAO,CAAC,IAAI,GAAG,EAAE,KAAK,GAAG;AACzI,GAAG,wBAAwB;AAC3B,SAAe,SAAS,QAAQ,MAAM;AAAA;AACpC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAMC,KAAI,KAAK,QAAQ,KAAK;AAC5B,UAAMC,KAAI,KAAK,SAAS,KAAK;AAC7B,UAAMC,KAAIF,KAAIC;AACd,UAAM,SAAS,CAAC;AAAA,MACd,GAAGC,KAAI;AAAA,MACP,GAAG;AAAA,IACL,GAAG;AAAA,MACD,GAAGA;AAAA,MACH,GAAG,CAACA,KAAI;AAAA,IACV,GAAG;AAAA,MACD,GAAGA,KAAI;AAAA,MACP,GAAG,CAACA;AAAA,IACN,GAAG;AAAA,MACD,GAAG;AAAA,MACH,GAAG,CAACA,KAAI;AAAA,IACV,CAAC;AACD,QAAI;AACJ,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,KAAK,SAAS,aAAa;AAC7B,YAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,YAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,YAAM,WAAW,uBAAuB,GAAG,GAAGA,EAAC;AAC/C,YAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAC3C,gBAAU,SAAS,OAAO,MAAM,WAAW,cAAc,EAAE,KAAK,aAAa,aAAa,CAACA,KAAI,CAAC,KAAKA,KAAI,CAAC,GAAG;AAC7G,UAAI,WAAW;AACb,gBAAQ,KAAK,SAAS,SAAS;AAAA,MACjC;AAAA,IACF,OAAO;AACL,gBAAU,mBAAmB,UAAUA,IAAGA,IAAG,MAAM;AAAA,IACrD;AACA,QAAI,YAAY;AACd,cAAQ,KAAK,SAAS,UAAU;AAAA,IAClC;AACA,qBAAiB,MAAM,OAAO;AAC9B,SAAK,YAAY,SAAU,OAAO;AAChC,UAAI,MAAM,wCAAwC,OAAO,aAAa,MAAM,UAAU,kBAAkB,QAAQ,MAAM,QAAQ,KAAK,CAAC;AACpI,aAAO,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AAAA,IACtD;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,UAAU,UAAU;AAI3B,SAAe,oBAAoB,QAAQ,MAAM;AAAA;AAC/C,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAMF,KAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,IAAI,MAAM,SAAS,CAAC;AACrE,UAAMC,KAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,IAAI,MAAM,UAAU,CAAC;AACvE,UAAMH,KAAI,CAACE,KAAI;AACf,UAAMD,KAAI,CAACE,KAAI;AACf,UAAM,QAAQF,KAAI;AAClB,UAAM,SAAS,CAAC;AAAA,MACd,GAAGD,KAAI;AAAA,MACP,GAAAC;AAAA,IACF,GAAG;AAAA,MACD,GAAAD;AAAA,MACA,GAAG;AAAA,IACL,GAAG;AAAA,MACD,GAAGA,KAAI;AAAA,MACP,GAAG,CAACC;AAAA,IACN,GAAG;AAAA,MACD,GAAG,CAACD;AAAA,MACJ,GAAG,CAACC;AAAA,IACN,GAAG;AAAA,MACD,GAAG,CAACD;AAAA,MACJ,GAAAC;AAAA,IACF,CAAC;AACD,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,QAAI,KAAK,SAAS,aAAa;AAC7B,cAAQ,YAAY;AACpB,cAAQ,YAAY;AAAA,IACtB;AACA,UAAM,WAAW,qBAAqB,MAAM;AAC5C,UAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAC3C,UAAM,UAAU,SAAS,OAAO,MAAM,WAAW,cAAc;AAC/D,YAAQ,KAAK,SAAS,uBAAuB;AAC7C,QAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,cAAQ,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,IACnD;AACA,QAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,cAAQ,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,IACpD;AACA,YAAQ,KAAK,aAAa,aAAa,CAAC,QAAQ,CAAC,KAAK;AACtD,UAAM,KAAK,aAAa,aAAa,CAAC,QAAQ,IAAI,KAAK,QAAQ,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,KAAK,EAAE,KAAK,SAAS,MAAM,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG;AACrJ,qBAAiB,MAAM,OAAO;AAC9B,SAAK,YAAY,SAAU,OAAO;AAChC,aAAO,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AAAA,IACtD;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,qBAAqB,qBAAqB;AAKjD,SAAe,cAAc,QAAQ,MAAM;AAAA;AACzC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,QAAI;AACJ,QAAI,CAAC,KAAK,YAAY;AACpB,gBAAU;AAAA,IACZ,OAAO;AACL,gBAAU,UAAU,KAAK;AAAA,IAC3B;AACA,UAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO,EAAE,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AAC3F,UAAMS,KAAI,SAAS,OAAO,GAAG;AAC7B,UAAM,QAAQ,SAAS,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO,EAAE,KAAK,SAAS,UAAU;AAClF,UAAM,cAAc,KAAK;AACzB,UAAM,QAAQ,KAAK;AACnB,UAAM,QAAQ,MAAM,KAAK,EAAE,YAAY,MAAM,oBAAoB,OAAO,KAAK,YAAY,MAAM,IAAI,CAAC;AACpG,QAAI,OAAO;AAAA,MACT,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AACA,QAAI,SAAS,WAAW,GAAG,WAAW,UAAU,GAAG;AACjD,YAAM,OAAO,MAAM,SAAS,CAAC;AAC7B,YAAM,MAAM,eAAQ,KAAK;AACzB,aAAO,KAAK,sBAAsB;AAClC,UAAI,KAAK,SAAS,KAAK,KAAK;AAC5B,UAAI,KAAK,UAAU,KAAK,MAAM;AAAA,IAChC;AACA,QAAI,KAAK,UAAU,WAAW;AAC9B,UAAM,WAAW,eAAe,CAAC;AACjC,UAAM,WAAW,MAAM,QAAQ;AAC/B,UAAM,QAAQ,MAAM,KAAK,EAAE,YAAY,MAAM,oBAAoB,SAAS,OAAO,SAAS,KAAK,OAAO,IAAI,UAAU,KAAK,YAAY,MAAM,IAAI,CAAC;AAChJ,UAAM,MAAM,MAAM,SAAS,CAAC;AAC5B,UAAM,KAAK,eAAQ,KAAK;AACxB,WAAO,IAAI,sBAAsB;AACjC,OAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,OAAG,KAAK,UAAU,KAAK,MAAM;AAC7B,UAAM,eAAe,KAAK,WAAW,KAAK;AAC1C,mBAAQ,KAAK,EAAE,KAAK,aAAa,iBAAiB,KAAK,QAAQ,SAAS,QAAQ,KAAK,SAAS,QAAQ,KAAK,SAAS,KAAK,QAAQ,SAAS,SAAS,cAAc,KAAK,GAAG;AACzK,mBAAQ,KAAK,EAAE,KAAK,aAAa,iBAAiB,KAAK,QAAQ,SAAS,QAAQ,IAAI,EAAE,SAAS,QAAQ,KAAK,SAAS,KAAK,MAAM;AAChI,WAAO,MAAM,KAAK,EAAE,QAAQ;AAC5B,UAAM,KAAK,aAAa,eAAe,CAAC,KAAK,QAAQ,IAAI,QAAQ,CAAC,KAAK,SAAS,IAAI,cAAc,KAAK,GAAG;AAC1G,UAAM,aAAa,KAAK,SAAS,KAAK,WAAW;AACjD,UAAM,cAAc,KAAK,UAAU,KAAK,WAAW;AACnD,UAAMV,KAAI,CAAC,KAAK,QAAQ,IAAI;AAC5B,UAAMC,KAAI,CAAC,KAAK,SAAS,IAAI;AAC7B,QAAI;AACJ,QAAI;AACJ,QAAI,KAAK,SAAS,aAAa;AAC7B,YAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,YAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,YAAM,YAAY,GAAG,KAAK,uBAAuBD,IAAGC,IAAG,YAAY,aAAa,KAAK,MAAM,CAAC,GAAG,OAAO;AACtG,YAAM,YAAY,GAAG,KAAK,CAAC,KAAK,QAAQ,IAAI,aAAa,CAAC,KAAK,SAAS,IAAI,cAAc,SAAS,SAAS,aAAa,KAAK,QAAQ,IAAI,aAAa,CAAC,KAAK,SAAS,IAAI,cAAc,SAAS,SAAS,aAAa,OAAO;AAC9N,kBAAY,SAAS,OAAO,MAAM;AAChC,YAAI,MAAM,yBAAyB,SAAS;AAC5C,eAAO;AAAA,MACT,GAAG,cAAc;AACjB,cAAQ,SAAS,OAAO,MAAM;AAC5B,YAAI,MAAM,yBAAyB,SAAS;AAC5C,eAAO;AAAA,MACT,GAAG,cAAc;AAAA,IACnB,OAAO;AACL,cAAQS,GAAE,OAAO,QAAQ,cAAc;AACvC,kBAAYA,GAAE,OAAO,MAAM;AAC3B,YAAM,KAAK,SAAS,mBAAmB,EAAE,KAAK,SAAS,UAAU,EAAE,KAAK,KAAK,CAAC,KAAK,QAAQ,IAAI,WAAW,EAAE,KAAK,KAAK,CAAC,KAAK,SAAS,IAAI,WAAW,EAAE,KAAK,SAAS,KAAK,SAAS,KAAK,WAAW,EAAE,EAAE,KAAK,UAAU,KAAK,UAAU,KAAK,WAAW,EAAE;AACtP,gBAAU,KAAK,SAAS,SAAS,EAAE,KAAK,MAAM,CAAC,KAAK,QAAQ,IAAI,WAAW,EAAE,KAAK,MAAM,KAAK,QAAQ,IAAI,WAAW,EAAE,KAAK,MAAM,CAAC,KAAK,SAAS,IAAI,cAAc,SAAS,SAAS,WAAW,EAAE,KAAK,MAAM,CAAC,KAAK,SAAS,IAAI,cAAc,SAAS,SAAS,WAAW;AAAA,IAC5Q;AACA,qBAAiB,MAAM,KAAK;AAC5B,SAAK,YAAY,SAAU,OAAO;AAChC,aAAO,kBAAkB,KAAK,MAAM,KAAK;AAAA,IAC3C;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,eAAe,eAAe;AAGrC,SAAe,YAAY,QAAQ,MAAM;AAAA;AACvC,UAAM,UAAU;AAAA,MACd,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,SAAS;AAAA,MACT,gBAAgB,MAAM,WAAW,KAAK;AAAA,MACtC,gBAAgB,MAAM,WAAW,KAAK;AAAA,IACxC;AACA,WAAO,SAAS,QAAQ,MAAM,OAAO;AAAA,EACvC;AAAA;AACA,OAAO,aAAa,aAAa;AAIjC,SAAe,cAAc,QAAQ,MAAM;AAAA;AACzC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAM,cAAc,MAAM,WAAW;AACrC,UAAMR,KAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,GAAG,MAAM,SAAS,CAAC;AACzE,UAAMC,KAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,GAAG,MAAM,UAAU,CAAC;AAC3E,UAAMH,KAAI,CAAC,KAAK,QAAQ,IAAI;AAC5B,UAAMC,KAAI,CAAC,KAAK,SAAS,IAAI;AAC7B,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,QAAI,KAAK,SAAS,aAAa;AAC7B,cAAQ,YAAY;AACpB,cAAQ,YAAY;AAAA,IACtB;AACA,UAAM,SAAS,CAAC;AAAA,MACd,GAAAD;AAAA,MACA,GAAAC;AAAA,IACF,GAAG;AAAA,MACD,GAAGD,KAAIE,KAAI;AAAA,MACX,GAAAD;AAAA,IACF,GAAG;AAAA,MACD,GAAGD,KAAIE,KAAI;AAAA,MACX,GAAGD,KAAIE;AAAA,IACT,GAAG;AAAA,MACD,GAAGH,KAAI;AAAA,MACP,GAAGC,KAAIE;AAAA,IACT,GAAG;AAAA,MACD,GAAGH,KAAI;AAAA,MACP,GAAAC;AAAA,IACF,GAAG;AAAA,MACD,GAAAD;AAAA,MACA,GAAAC;AAAA,IACF,GAAG;AAAA,MACD,GAAAD;AAAA,MACA,GAAGC,KAAIE;AAAA,IACT,CAAC;AACD,UAAM,YAAY,GAAG,QAAQ,OAAO,IAAI,CAAAN,OAAK,CAACA,GAAE,GAAGA,GAAE,CAAC,CAAC,GAAG,OAAO;AACjE,UAAM,QAAQ,SAAS,OAAO,MAAM,WAAW,cAAc;AAC7D,UAAM,KAAK,SAAS,uBAAuB,EAAE,KAAK,SAAS,oBAAoB,SAAS,CAAC;AACzF,QAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,YAAM,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,IAClD;AACA,QAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,YAAM,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,IAClD;AACA,UAAM,KAAK,aAAa,aAAa,CAACK,KAAI,IAAI,KAAK,KAAK,WAAW,MAAM,KAAK,KAAK,KAAK,QAAQ,GAAG,IAAI,CAACC,KAAI,KAAK,KAAK,WAAW,MAAM,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG;AACnK,qBAAiB,MAAM,KAAK;AAC5B,SAAK,YAAY,SAAU,OAAO;AAChC,aAAO,kBAAkB,KAAK,MAAM,KAAK;AAAA,IAC3C;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,eAAe,eAAe;AAIrC,SAAe,WAAW,QAAQ,MAAM;AAAA;AACtC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAMD,KAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,GAAG,MAAM,SAAS,CAAC;AACzE,UAAMC,KAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,GAAG,MAAM,UAAU,CAAC;AAC3E,UAAMH,KAAI,CAACE,KAAI;AACf,UAAMD,KAAI,CAACE,KAAI;AACf,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,QAAI,KAAK,SAAS,aAAa;AAC7B,cAAQ,YAAY;AACpB,cAAQ,YAAY;AAAA,IACtB;AACA,UAAM,SAAS,CAAC;AAAA,MACd,GAAAH;AAAA,MACA,GAAAC;AAAA,IACF,GAAG;AAAA,MACD,GAAAD;AAAA,MACA,GAAGC,KAAIE;AAAA,IACT,GAAG;AAAA,MACD,GAAGH,KAAIE;AAAA,MACP,GAAGD,KAAIE;AAAA,IACT,GAAG;AAAA,MACD,GAAGH,KAAIE;AAAA,MACP,GAAGD,KAAIE,KAAI;AAAA,IACb,CAAC;AACD,UAAM,WAAW,qBAAqB,MAAM;AAC5C,UAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAC3C,UAAM,UAAU,SAAS,OAAO,MAAM,WAAW,cAAc;AAC/D,YAAQ,KAAK,SAAS,uBAAuB;AAC7C,QAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,cAAQ,eAAe,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,IACxD;AACA,QAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,cAAQ,eAAe,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,IACzD;AACA,YAAQ,KAAK,aAAa,gBAAgBA,KAAI,CAAC,GAAG;AAClD,UAAM,KAAK,aAAa,aAAa,CAACD,KAAI,KAAK,KAAK,WAAW,MAAM,KAAK,KAAK,KAAK,QAAQ,GAAG,KAAK,CAACC,KAAI,KAAK,KAAK,WAAW,MAAM,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG;AAChK,qBAAiB,MAAM,OAAO;AAC9B,SAAK,YAAY,SAAU,OAAO;AAChC,YAAM,MAAM,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AACzD,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,YAAY,YAAY;AAG/B,SAAe,YAAY,QAAQ,MAAM;AAAA;AACvC,UAAM,UAAU;AAAA,MACd,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,SAAS;AAAA,MACT,gBAAgB,MAAM,WAAW,KAAK;AAAA,MACtC,gBAAgB,MAAM,WAAW,KAAK;AAAA,IACxC;AACA,WAAO,SAAS,QAAQ,MAAM,OAAO;AAAA,EACvC;AAAA;AACA,OAAO,aAAa,YAAY;AAIhC,SAAe,QAAQ,QAAQ,MAAM;AAAA;AACnC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAMA,KAAI,KAAK,SAAS,KAAK;AAC7B,UAAMD,KAAI,KAAK,QAAQC,KAAI,IAAI,KAAK;AACpC,QAAI;AACJ,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,KAAK,SAAS,aAAa;AAC7B,YAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,YAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,YAAM,WAAW,uBAAuB,CAACD,KAAI,GAAG,CAACC,KAAI,GAAGD,IAAGC,IAAGA,KAAI,CAAC;AACnE,YAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAC3C,cAAQ,SAAS,OAAO,MAAM,WAAW,cAAc;AACvD,YAAM,KAAK,SAAS,uBAAuB,EAAE,KAAK,SAAS,oBAAoB,SAAS,CAAC;AAAA,IAC3F,OAAO;AACL,cAAQ,SAAS,OAAO,QAAQ,cAAc;AAC9C,YAAM,KAAK,SAAS,uBAAuB,EAAE,KAAK,SAAS,UAAU,EAAE,KAAK,MAAMA,KAAI,CAAC,EAAE,KAAK,MAAMA,KAAI,CAAC,EAAE,KAAK,KAAK,CAACD,KAAI,CAAC,EAAE,KAAK,KAAK,CAACC,KAAI,CAAC,EAAE,KAAK,SAASD,EAAC,EAAE,KAAK,UAAUC,EAAC;AAAA,IAClL;AACA,qBAAiB,MAAM,KAAK;AAC5B,SAAK,YAAY,SAAU,OAAO;AAChC,aAAO,kBAAkB,KAAK,MAAM,KAAK;AAAA,IAC3C;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,SAAS,SAAS;AAGzB,SAAe,MAAM,QAAQ,MAAM;AAAA;AACjC,UAAM,UAAU;AAAA,MACd,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,SAAS;AAAA,IACX;AACA,WAAO,SAAS,QAAQ,MAAM,OAAO;AAAA,EACvC;AAAA;AACA,OAAO,OAAO,OAAO;AAIrB,SAAS,SAAS,QAAQ,MAAM;AAAA,EAC9B,QAAQ;AAAA,IACN;AAAA,EACF;AACF,GAAG;AACD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,cAAc,IAAI;AACtB,OAAK,aAAa;AAClB,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,cAAc,EAAE,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AAClG,QAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,YAAY,GAAG,OAAO,GAAG,GAAG,IAAI,iCACjC,UADiC;AAAA,IAEpC,QAAQ;AAAA,IACR,aAAa;AAAA,EACf,EAAC;AACD,QAAM,YAAY,eAAe;AACjC,QAAM,iBAAiB,GAAG,OAAO,GAAG,GAAG,GAAG,iCACrC,UADqC;AAAA,IAExC,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,WAAW;AAAA,EACb,EAAC;AACD,QAAM,UAAU,SAAS,OAAO,MAAM,WAAW,cAAc;AAC/D,UAAQ,OAAO,MAAM,cAAc;AACnC,MAAI,WAAW;AACb,YAAQ,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACnD;AACA,MAAI,YAAY;AACd,YAAQ,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACpD;AACA,mBAAiB,MAAM,OAAO;AAC9B,OAAK,YAAY,SAAU,OAAO;AAChC,WAAO,kBAAkB,OAAO,MAAM,GAAG,KAAK;AAAA,EAChD;AACA,SAAO;AACT;AACA,OAAO,UAAU,UAAU;AAI3B,SAAS,WAAW,QAAQ,MAAM;AAAA,EAChC,QAAQ;AAAA,IACN;AAAA,EACF;AACF,GAAG;AACD,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,cAAc,EAAE,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AAClG,MAAI;AACJ,MAAI,KAAK,SAAS,aAAa;AAC7B,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,YAAY,GAAG,OAAO,GAAG,GAAG,IAAI,eAAe,SAAS,CAAC;AAC/D,cAAU,SAAS,OAAO,MAAM,SAAS;AACzC,YAAQ,KAAK,SAAS,aAAa,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,SAAS,EAAE,EAAE,KAAK,UAAU,EAAE;AAAA,EACvF,OAAO;AACL,cAAU,SAAS,OAAO,UAAU,cAAc;AAClD,YAAQ,KAAK,SAAS,aAAa,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,SAAS,EAAE,EAAE,KAAK,UAAU,EAAE;AAAA,EACvF;AACA,mBAAiB,MAAM,OAAO;AAC9B,OAAK,YAAY,SAAU,OAAO;AAChC,WAAO,kBAAkB,OAAO,MAAM,GAAG,KAAK;AAAA,EAChD;AACA,SAAO;AACT;AACA,OAAO,YAAY,YAAY;AAI/B,SAAe,WAAW,QAAQ,MAAM;AAAA;AACtC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAM,eAAe,MAAM,WAAW,KAAK;AAC3C,UAAMD,KAAI,KAAK,QAAQ,KAAK;AAC5B,UAAMC,KAAI,KAAK,SAAS,KAAK;AAC7B,UAAMH,KAAI,CAAC,KAAK,QAAQ,IAAI;AAC5B,UAAMC,KAAI,CAAC,KAAK,SAAS,IAAI;AAC7B,UAAM,SAAS,CAAC;AAAA,MACd,GAAG;AAAA,MACH,GAAG;AAAA,IACL,GAAG;AAAA,MACD,GAAGC;AAAA,MACH,GAAG;AAAA,IACL,GAAG;AAAA,MACD,GAAGA;AAAA,MACH,GAAG,CAACC;AAAA,IACN,GAAG;AAAA,MACD,GAAG;AAAA,MACH,GAAG,CAACA;AAAA,IACN,GAAG;AAAA,MACD,GAAG;AAAA,MACH,GAAG;AAAA,IACL,GAAG;AAAA,MACD,GAAG;AAAA,MACH,GAAG;AAAA,IACL,GAAG;AAAA,MACD,GAAGD,KAAI;AAAA,MACP,GAAG;AAAA,IACL,GAAG;AAAA,MACD,GAAGA,KAAI;AAAA,MACP,GAAG,CAACC;AAAA,IACN,GAAG;AAAA,MACD,GAAG;AAAA,MACH,GAAG,CAACA;AAAA,IACN,GAAG;AAAA,MACD,GAAG;AAAA,MACH,GAAG;AAAA,IACL,CAAC;AACD,QAAI,KAAK,SAAS,aAAa;AAC7B,YAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,YAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,YAAM,YAAY,GAAG,UAAUH,KAAI,GAAGC,IAAGC,KAAI,IAAIC,IAAG,OAAO;AAC3D,YAAM,KAAK,GAAG,KAAKH,IAAGC,IAAGD,IAAGC,KAAIE,IAAG,OAAO;AAC1C,YAAM,KAAK,GAAG,KAAKH,KAAIE,IAAGD,IAAGD,KAAIE,IAAGD,KAAIE,IAAG,OAAO;AAClD,eAAS,OAAO,MAAM,IAAI,cAAc;AACxC,eAAS,OAAO,MAAM,IAAI,cAAc;AACxC,YAAM,QAAQ,SAAS,OAAO,MAAM,WAAW,cAAc;AAC7D,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,YAAM,KAAK,SAAS,uBAAuB,EAAE,KAAK,SAAS,oBAAoB,SAAS,CAAC;AACzF,uBAAiB,MAAM,KAAK;AAAA,IAC9B,OAAO;AACL,YAAM,KAAK,mBAAmB,UAAUD,IAAGC,IAAG,MAAM;AACpD,UAAI,YAAY;AACd,WAAG,KAAK,SAAS,UAAU;AAAA,MAC7B;AACA,uBAAiB,MAAM,EAAE;AAAA,IAC3B;AACA,SAAK,YAAY,SAAU,OAAO;AAChC,aAAO,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AAAA,IACtD;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,YAAY,YAAY;AAI/B,SAAe,WAAW,QAAQ,MAAM;AAAA;AACtC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAMD,KAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,GAAG,MAAM,SAAS,CAAC;AACzE,UAAMC,KAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,GAAG,MAAM,UAAU,CAAC;AAC3E,UAAMH,KAAI,CAACE,KAAI;AACf,UAAMD,KAAI,CAACE,KAAI;AACf,UAAM,WAAW,MAAMA;AACvB,UAAM,YAAY,MAAMA;AACxB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,UAAM,aAAa,CAAC;AAAA,MAClB,GAAGH,KAAI,WAAW;AAAA,MAClB,GAAAC;AAAA,IACF,GAAG;AAAA,MACD,GAAGD,KAAIE,KAAI,WAAW;AAAA,MACtB,GAAAD;AAAA,IACF,GAAG;AAAA,MACD,GAAGD,KAAIE,KAAI,WAAW;AAAA,MACtB,GAAGD,KAAIE;AAAA,IACT,GAAG;AAAA,MACD,GAAGH,KAAI,WAAW;AAAA,MAClB,GAAGC,KAAIE;AAAA,IACT,CAAC;AACD,UAAM,YAAY,CAAC;AAAA,MACjB,GAAGH,KAAIE,KAAI,WAAW;AAAA,MACtB,GAAGD,KAAIE;AAAA,IACT,GAAG;AAAA,MACD,GAAGH,KAAIE,KAAI,WAAW;AAAA,MACtB,GAAGD,KAAIE;AAAA,IACT,GAAG;AAAA,MACD,GAAGH,KAAIE,KAAI,WAAW;AAAA,MACtB,GAAGD,KAAIE,KAAI;AAAA,IACb,CAAC;AACD,QAAI,KAAK,SAAS,aAAa;AAC7B,cAAQ,YAAY;AACpB,cAAQ,YAAY;AAAA,IACtB;AACA,UAAM,WAAW,qBAAqB,UAAU;AAChD,UAAM,WAAW,GAAG,KAAK,UAAU,OAAO;AAC1C,UAAM,UAAU,qBAAqB,SAAS;AAC9C,UAAM,UAAU,GAAG,KAAK,SAAS,iCAC5B,UAD4B;AAAA,MAE/B,WAAW;AAAA,IACb,EAAC;AACD,UAAM,cAAc,SAAS,OAAO,MAAM,SAAS,cAAc;AACjE,gBAAY,OAAO,MAAM,UAAU,cAAc;AACjD,gBAAY,KAAK,SAAS,uBAAuB;AACjD,QAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,kBAAY,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,IACvD;AACA,QAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,kBAAY,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,IACxD;AACA,qBAAiB,MAAM,WAAW;AAClC,SAAK,YAAY,SAAU,OAAO;AAChC,YAAM,MAAM,kBAAkB,QAAQ,MAAM,YAAY,KAAK;AAC7D,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,YAAY,YAAY;AAI/B,SAAe,yBAAyB,QAAQ,MAAM;AAAA;AACpD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAMD,KAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,GAAG,MAAM,SAAS,CAAC;AACzE,UAAMC,KAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,GAAG,MAAM,UAAU,CAAC;AAC3E,UAAM,gBAAgBA,KAAI;AAC1B,UAAM,WAAW,MAAMD;AACvB,UAAM,YAAY,MAAMC;AACxB,UAAM,SAASA,KAAI;AACnB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,QAAI,KAAK,SAAS,aAAa;AAC7B,cAAQ,YAAY;AACpB,cAAQ,YAAY;AAAA,IACtB;AACA,UAAM,SAAS,CAAC;AAAA,MACd,GAAG,CAACD,KAAI,IAAIA,KAAI,IAAI;AAAA,MACpB,GAAG,SAAS;AAAA,IACd,GAAG,GAAG,2BAA2B,CAACA,KAAI,IAAIA,KAAI,IAAI,KAAK,SAAS,GAAGA,KAAI,IAAIA,KAAI,IAAI,KAAK,SAAS,GAAG,eAAe,GAAG,GAAG;AAAA,MACvH,GAAGA,KAAI,IAAIA,KAAI,IAAI;AAAA,MACnB,GAAG,CAAC,SAAS;AAAA,IACf,GAAG;AAAA,MACD,GAAG,CAACA,KAAI,IAAIA,KAAI,IAAI;AAAA,MACpB,GAAG,CAAC,SAAS;AAAA,IACf,CAAC;AACD,UAAMF,KAAI,CAACE,KAAI,IAAIA,KAAI,IAAI;AAC3B,UAAMD,KAAI,CAAC,SAAS,IAAI,YAAY;AACpC,UAAM,YAAY,CAAC;AAAA,MACjB,GAAGD,KAAIE,KAAI;AAAA,MACX,IAAID,KAAIE,MAAK;AAAA,IACf,GAAG;AAAA,MACD,GAAGH,KAAIE;AAAA,MACP,GAAGD,KAAIE,KAAI;AAAA,IACb,GAAG;AAAA,MACD,GAAGH,KAAIE;AAAA,MACP,IAAID,KAAIE,MAAK;AAAA,IACf,GAAG,GAAG,2BAA2BH,KAAIE,KAAID,KAAIE,MAAK,KAAKH,KAAIE,KAAI,WAAWD,KAAIE,MAAK,KAAK,CAACA,KAAI,MAAM,GAAG,CAAC;AACvG,UAAM,mBAAmB,qBAAqB,MAAM;AACpD,UAAM,mBAAmB,GAAG,KAAK,kBAAkB,OAAO;AAC1D,UAAM,yBAAyB,qBAAqB,SAAS;AAC7D,UAAM,yBAAyB,GAAG,KAAK,wBAAwB,iCAC1D,UAD0D;AAAA,MAE7D,WAAW;AAAA,IACb,EAAC;AACD,UAAM,eAAe,SAAS,OAAO,MAAM,wBAAwB,cAAc;AACjF,iBAAa,OAAO,MAAM,kBAAkB,cAAc;AAC1D,iBAAa,KAAK,SAAS,uBAAuB;AAClD,QAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,mBAAa,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,IACxD;AACA,QAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,mBAAa,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,IACzD;AACA,iBAAa,KAAK,aAAa,eAAe,CAAC,gBAAgB,CAAC,GAAG;AACnE,UAAM,KAAK,aAAa,aAAa,CAACD,KAAI,KAAK,KAAK,WAAW,MAAM,KAAK,KAAK,KAAK,QAAQ,GAAG,IAAI,CAACC,KAAI,KAAK,KAAK,WAAW,KAAK,gBAAgB,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG;AACnL,qBAAiB,MAAM,YAAY;AACnC,SAAK,YAAY,SAAU,OAAO;AAChC,YAAM,MAAM,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AACzD,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,0BAA0B,0BAA0B;AAG3D,SAAe,KAAK,QAAQ,MAAM;AAAA;AAChC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAM,aAAa,KAAK,IAAI,KAAK,QAAQ,KAAK,SAAS,MAAM,SAAS,CAAC;AACvE,UAAM,cAAc,KAAK,IAAI,KAAK,SAAS,KAAK,SAAS,MAAM,UAAU,CAAC;AAC1E,UAAMH,KAAI,CAAC,aAAa;AACxB,UAAMC,KAAI,CAAC,cAAc;AACzB,UAAM,QAAQ,SAAS,OAAO,QAAQ,cAAc;AACpD,UAAM,KAAK,SAAS,MAAM,EAAE,KAAK,SAAS,UAAU,EAAE,KAAK,MAAM,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,KAAK,KAAKD,EAAC,EAAE,KAAK,KAAKC,EAAC,EAAE,KAAK,SAAS,UAAU,EAAE,KAAK,UAAU,WAAW;AAChK,qBAAiB,MAAM,KAAK;AAC5B,SAAK,YAAY,SAAU,OAAO;AAChC,aAAO,kBAAkB,KAAK,MAAM,KAAK;AAAA,IAC3C;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,MAAM,MAAM;AAInB,IAAI,uBAAsC,OAAO,CAACD,IAAGC,IAAG,OAAO,QAAQ,IAAI,OAAO;AAChF,SAAO,IAAID,EAAC,IAAIC,EAAC;AAAA,OACZ,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC,MAAM;AAAA,OAC9B,KAAK,IAAI,CAAC;AAAA,OACV,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,MAAM;AAAA,OAC7B,KAAK,IAAI,CAAC,MAAM;AAAA,OAChB,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,MAAM;AAAA,OAC7B,CAAC,KAAK,IAAI,CAAC;AAClB,GAAG,qBAAqB;AACxB,IAAI,4BAA2C,OAAO,CAACD,IAAGC,IAAG,OAAO,QAAQ,IAAI,OAAO;AACrF,SAAO,CAAC,IAAID,EAAC,IAAIC,EAAC,IAAI,IAAID,KAAI,KAAK,IAAIC,EAAC,IAAI,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,MAAM,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,MAAM,IAAI,IAAI,KAAK,IAAI,EAAE,KAAK,GAAG;AAChK,GAAG,0BAA0B;AAC7B,IAAI,4BAA2C,OAAO,CAACD,IAAGC,IAAG,OAAO,QAAQ,IAAI,OAAO;AACrF,SAAO,CAAC,IAAID,KAAI,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,EAAE,IAAI,EAAE,YAAY,MAAM,EAAE,EAAE,KAAK,GAAG;AACxF,GAAG,0BAA0B;AAC7B,SAAe,eAAe,QAAQ,MAAM;AAAA;AAC1C,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAM,eAAe,KAAK,SAAS,QAAQ,cAAc,IAAI;AAC7D,UAAMG,KAAI,KAAK,SAAS;AACxB,UAAM,KAAKA,KAAI;AACf,UAAM,KAAK,MAAM,MAAMA,KAAI;AAC3B,UAAMD,KAAI,KAAK,QAAQ,KAAK;AAC5B,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI;AACJ,QAAI,KAAK,SAAS,aAAa;AAC7B,YAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,YAAM,gBAAgB,0BAA0B,GAAG,GAAGA,IAAGC,IAAG,IAAI,EAAE;AAClE,YAAM,gBAAgB,0BAA0B,GAAG,GAAGD,IAAGC,IAAG,IAAI,EAAE;AAClE,YAAM,YAAY,GAAG,KAAK,eAAe,kBAAkB,MAAM,CAAC,CAAC,CAAC;AACpE,YAAM,YAAY,GAAG,KAAK,eAAe,kBAAkB,MAAM;AAAA,QAC/D,MAAM;AAAA,MACR,CAAC,CAAC;AACF,kBAAY,SAAS,OAAO,MAAM,WAAW,cAAc;AAC3D,kBAAY,SAAS,OAAO,MAAM,WAAW,cAAc;AAC3D,gBAAU,KAAK,SAAS,uBAAuB;AAC/C,UAAI,WAAW;AACb,kBAAU,KAAK,SAAS,SAAS;AAAA,MACnC;AAAA,IACF,OAAO;AACL,YAAM,WAAW,qBAAqB,GAAG,GAAGD,IAAGC,IAAG,IAAI,EAAE;AACxD,kBAAY,SAAS,OAAO,QAAQ,cAAc,EAAE,KAAK,KAAK,QAAQ,EAAE,KAAK,SAAS,uBAAuB,EAAE,KAAK,SAAS,oBAAoB,SAAS,CAAC,EAAE,KAAK,SAAS,UAAU;AACrL,gBAAU,KAAK,SAAS,uBAAuB;AAC/C,UAAI,WAAW;AACb,kBAAU,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,MACrD;AACA,UAAI,YAAY;AACd,kBAAU,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,MACtD;AAAA,IACF;AACA,cAAU,KAAK,kBAAkB,EAAE;AACnC,cAAU,KAAK,aAAa,aAAa,CAACD,KAAI,CAAC,KAAKC,KAAI,CAAC,IAAI;AAC7D,UAAM,KAAK,aAAa,aAAa,EAAE,KAAK,QAAQ,KAAK,MAAM,KAAK,KAAK,KAAK,QAAQ,GAAG,KAAK,EAAE,KAAK,SAAS,MAAM,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG;AAChJ,qBAAiB,MAAM,SAAS;AAChC,SAAK,YAAY,SAAU,OAAO;AAChC,YAAM,MAAM,kBAAkB,KAAK,MAAM,KAAK;AAC9C,YAAMF,KAAI,IAAI,KAAK,KAAK,KAAK;AAC7B,UAAI,MAAM,MAAM,KAAK,IAAIA,EAAC,KAAK,KAAK,UAAU,KAAK,KAAK,KAAK,IAAIA,EAAC,MAAM,KAAK,UAAU,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,EAAE,KAAK,KAAK,SAAS,KAAK,IAAI,KAAK;AAC9J,YAAID,KAAI,KAAK,MAAM,IAAIC,KAAIA,MAAK,KAAK;AACrC,YAAID,MAAK,GAAG;AACV,UAAAA,KAAI,KAAK,KAAK,KAAK,IAAIA,EAAC,CAAC;AAAA,QAC3B;AACA,QAAAA,KAAI,KAAKA;AACT,YAAI,MAAM,KAAK,KAAK,KAAK,KAAK,GAAG;AAC/B,UAAAA,KAAI,CAACA;AAAA,QACP;AACA,YAAI,KAAKA;AAAA,MACX;AACA,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,gBAAgB,gBAAgB;AAIvC,SAAe,UAAU,QAAQ,MAAM;AAAA;AACrC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAME,KAAI,KAAK,QAAQ,KAAK;AAC5B,UAAMC,KAAI,KAAK,SAAS,KAAK;AAC7B,UAAM,SAAS,CAAC;AAAA,MACd,GAAG,KAAKA,KAAI;AAAA,MACZ,GAAG;AAAA,IACL,GAAG;AAAA,MACD,GAAGD,KAAI,IAAIC,KAAI;AAAA,MACf,GAAG;AAAA,IACL,GAAG;AAAA,MACD,GAAGD;AAAA,MACH,GAAG,CAACC;AAAA,IACN,GAAG;AAAA,MACD,GAAG;AAAA,MACH,GAAG,CAACA;AAAA,IACN,CAAC;AACD,QAAI;AACJ,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,KAAK,SAAS,aAAa;AAC7B,YAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,YAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,YAAM,WAAW,qBAAqB,MAAM;AAC5C,YAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAC3C,gBAAU,SAAS,OAAO,MAAM,WAAW,cAAc,EAAE,KAAK,aAAa,aAAa,CAACD,KAAI,CAAC,KAAKC,KAAI,CAAC,GAAG;AAC7G,UAAI,WAAW;AACb,gBAAQ,KAAK,SAAS,SAAS;AAAA,MACjC;AAAA,IACF,OAAO;AACL,gBAAU,mBAAmB,UAAUD,IAAGC,IAAG,MAAM;AAAA,IACrD;AACA,QAAI,YAAY;AACd,cAAQ,KAAK,SAAS,UAAU;AAAA,IAClC;AACA,SAAK,QAAQD;AACb,SAAK,SAASC;AACd,qBAAiB,MAAM,OAAO;AAC9B,SAAK,YAAY,SAAU,OAAO;AAChC,aAAO,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AAAA,IACtD;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,WAAW,WAAW;AAI7B,SAAe,oBAAoB,QAAQ,MAAM;AAAA;AAC/C,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAM,WAAW,IACf,YAAY;AACd,UAAMD,KAAI,KAAK,IAAI,UAAU,KAAK,SAAS,KAAK,WAAW,KAAK,GAAG,MAAM,SAAS,CAAC;AACnF,UAAMC,KAAI,KAAK,IAAI,WAAW,KAAK,UAAU,KAAK,WAAW,KAAK,GAAG,MAAM,UAAU,CAAC;AACtF,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,QAAI,KAAK,SAAS,aAAa;AAC7B,cAAQ,YAAY;AACpB,cAAQ,YAAY;AAAA,IACtB;AACA,UAAM,SAAS,CAAC;AAAA,MACd,GAAG,CAACD,KAAI,IAAI;AAAA,MACZ,GAAG,CAACC,KAAI;AAAA,IACV,GAAG;AAAA,MACD,GAAGD,KAAI,IAAI;AAAA,MACX,GAAG,CAACC,KAAI;AAAA,IACV,GAAG;AAAA,MACD,GAAGD,KAAI;AAAA,MACP,GAAG,CAACC,KAAI,IAAI;AAAA,IACd,GAAG;AAAA,MACD,GAAGD,KAAI;AAAA,MACP,GAAGC,KAAI;AAAA,IACT,GAAG;AAAA,MACD,GAAG,CAACD,KAAI;AAAA,MACR,GAAGC,KAAI;AAAA,IACT,GAAG;AAAA,MACD,GAAG,CAACD,KAAI;AAAA,MACR,GAAG,CAACC,KAAI,IAAI;AAAA,IACd,CAAC;AACD,UAAM,WAAW,qBAAqB,MAAM;AAC5C,UAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAC3C,UAAM,UAAU,SAAS,OAAO,MAAM,WAAW,cAAc;AAC/D,YAAQ,KAAK,SAAS,uBAAuB;AAC7C,QAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,cAAQ,eAAe,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,IACxD;AACA,QAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,cAAQ,eAAe,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,IACzD;AACA,qBAAiB,MAAM,OAAO;AAC9B,SAAK,YAAY,SAAU,OAAO;AAChC,YAAM,MAAM,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AACzD,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,qBAAqB,qBAAqB;AAIjD,SAAe,SAAS,QAAQ,MAAM;AAAA;AACpC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAM,gBAAgB,SAAS,WAAW,EAAE,WAAW,UAAU;AACjE,UAAMD,KAAI,KAAK,SAAS,KAAK,WAAW;AACxC,UAAMC,KAAID,KAAI,KAAK;AACnB,UAAM,KAAKA,KAAI,KAAK;AACpB,UAAM,SAAS,CAAC;AAAA,MACd,GAAG;AAAA,MACH,GAAG;AAAA,IACL,GAAG;AAAA,MACD,GAAG;AAAA,MACH,GAAG;AAAA,IACL,GAAG;AAAA,MACD,GAAG,KAAK;AAAA,MACR,GAAG,CAACC;AAAA,IACN,CAAC;AACD,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,QAAI,KAAK,SAAS,aAAa;AAC7B,cAAQ,YAAY;AACpB,cAAQ,YAAY;AAAA,IACtB;AACA,UAAM,WAAW,qBAAqB,MAAM;AAC5C,UAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAC3C,UAAM,UAAU,SAAS,OAAO,MAAM,WAAW,cAAc,EAAE,KAAK,aAAa,aAAa,CAACA,KAAI,CAAC,KAAKA,KAAI,CAAC,GAAG;AACnH,QAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,cAAQ,eAAe,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,IACxD;AACA,QAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,cAAQ,eAAe,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,IACzD;AACA,SAAK,QAAQD;AACb,SAAK,SAASC;AACd,qBAAiB,MAAM,OAAO;AAC9B,UAAM,KAAK,aAAa,aAAa,CAAC,KAAK,QAAQ,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,KAAKA,KAAI,KAAK,KAAK,UAAU,KAAK,WAAW,MAAM,gBAAgB,IAAI,MAAM,KAAK,KAAK,KAAK,OAAO,IAAI,GAAG;AAC5L,SAAK,YAAY,SAAU,OAAO;AAChC,UAAI,KAAK,sBAAsB,MAAM,QAAQ,KAAK;AAClD,aAAO,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AAAA,IACtD;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,UAAU,UAAU;AAI3B,SAAe,mBAAmB,QAAQ,MAAM;AAAA;AAC9C,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAMD,KAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,GAAG,MAAM,SAAS,CAAC;AACzE,UAAMC,KAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,GAAG,MAAM,UAAU,CAAC;AAC3E,UAAM,gBAAgBA,KAAI;AAC1B,UAAM,SAASA,KAAI;AACnB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,WAAW;AACjB,UAAM,WAAW,WAAWD;AAC5B,UAAM,SAAS,WAAW,IAAI,WAAW,IAAI;AAC7C,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,QAAI,KAAK,SAAS,aAAa;AAC7B,cAAQ,YAAY;AACpB,cAAQ,YAAY;AAAA,IACtB;AACA,UAAM,SAAS,CAAC;AAAA,MACd,GAAG,CAACA,KAAI,IAAI;AAAA,MACZ,GAAG,SAAS;AAAA,IACd,GAAG,GAAG,2BAA2B,CAACA,KAAI,IAAI,QAAQ,SAAS,GAAGA,KAAI,IAAI,QAAQ,SAAS,GAAG,eAAe,GAAG,GAAG;AAAA,MAC7G,GAAGA,KAAI,IAAI;AAAA,MACX,GAAG,CAAC,SAAS;AAAA,IACf,GAAG;AAAA,MACD,GAAG,CAACA,KAAI,IAAI;AAAA,MACZ,GAAG,CAAC,SAAS;AAAA,IACf,CAAC;AACD,UAAM,mBAAmB,qBAAqB,MAAM;AACpD,UAAM,mBAAmB,GAAG,KAAK,kBAAkB,OAAO;AAC1D,UAAM,eAAe,SAAS,OAAO,MAAM,kBAAkB,cAAc;AAC3E,iBAAa,KAAK,SAAS,uBAAuB;AAClD,QAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,mBAAa,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,IACxD;AACA,QAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,mBAAa,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,IACzD;AACA,iBAAa,KAAK,aAAa,eAAe,CAAC,gBAAgB,CAAC,GAAG;AACnE,UAAM,KAAK,aAAa,aAAa,CAACA,KAAI,KAAK,KAAK,WAAW,MAAM,KAAK,KAAK,KAAK,QAAQ,GAAG,IAAI,CAACC,KAAI,KAAK,KAAK,WAAW,KAAK,iBAAiB,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG;AAC/K,qBAAiB,MAAM,YAAY;AACnC,SAAK,YAAY,SAAU,OAAO;AAChC,YAAM,MAAM,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AACzD,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,oBAAoB,oBAAoB;AAI/C,SAAe,cAAc,QAAQ,MAAM;AAAA;AACzC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAM,WAAW;AACjB,UAAM,YAAY;AAClB,UAAM,YAAY,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,GAAG,MAAM,SAAS,CAAC;AACjF,UAAM,aAAa,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,GAAG,MAAM,UAAU,CAAC;AACpF,UAAM,cAAc,YAAY;AAChC,QAAID,KAAI;AACR,QAAIC,KAAI;AACR,QAAID,KAAIC,KAAI,aAAa;AACvB,MAAAA,KAAID,KAAI;AAAA,IACV,OAAO;AACL,MAAAA,KAAIC,KAAI;AAAA,IACV;AACA,IAAAD,KAAI,KAAK,IAAIA,IAAG,QAAQ;AACxB,IAAAC,KAAI,KAAK,IAAIA,IAAG,SAAS;AACzB,UAAM,gBAAgB,KAAK,IAAIA,KAAI,KAAKA,KAAI,CAAC;AAC7C,UAAM,SAASA,KAAI,gBAAgB;AACnC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,QAAI,KAAK,SAAS,aAAa;AAC7B,cAAQ,YAAY;AACpB,cAAQ,YAAY;AAAA,IACtB;AACA,UAAM,SAAS,CAAC;AAAA,MACd,GAAG,CAACD,KAAI;AAAA,MACR,GAAG,SAAS;AAAA,IACd,GAAG,GAAG,2BAA2B,CAACA,KAAI,GAAG,SAAS,GAAGA,KAAI,GAAG,SAAS,GAAG,eAAe,CAAC,GAAG;AAAA,MACzF,GAAGA,KAAI;AAAA,MACP,GAAG,CAAC,SAAS;AAAA,IACf,GAAG,GAAG,2BAA2BA,KAAI,GAAG,CAAC,SAAS,GAAG,CAACA,KAAI,GAAG,CAAC,SAAS,GAAG,eAAe,EAAE,CAAC;AAC5F,UAAM,eAAe,qBAAqB,MAAM;AAChD,UAAM,eAAe,GAAG,KAAK,cAAc,OAAO;AAClD,UAAM,WAAW,SAAS,OAAO,MAAM,cAAc,cAAc;AACnE,aAAS,KAAK,SAAS,uBAAuB;AAC9C,QAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,eAAS,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,IACpD;AACA,QAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,eAAS,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,IACrD;AACA,qBAAiB,MAAM,QAAQ;AAC/B,SAAK,YAAY,SAAU,OAAO;AAChC,YAAM,MAAM,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AACzD,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,eAAe,eAAe;AAIrC,SAAe,WAAW,QAAQ,MAAM;AAAA;AACtC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACxD,UAAMA,KAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,GAAG,MAAM,SAAS,CAAC;AACzE,UAAMC,KAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,GAAG,MAAM,UAAU,CAAC;AAC3E,UAAM,aAAa;AACnB,UAAMH,KAAI,CAACE,KAAI;AACf,UAAMD,KAAI,CAACE,KAAI;AACf,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,UAAM,kBAAkB,CAAC;AAAA,MACvB,GAAGH,KAAI;AAAA,MACP,GAAGC,KAAI;AAAA,IACT,GAAG;AAAA,MACD,GAAGD,KAAI;AAAA,MACP,GAAGC,KAAIE;AAAA,IACT,GAAG;AAAA,MACD,GAAGH,KAAIE;AAAA,MACP,GAAGD,KAAIE;AAAA,IACT,GAAG;AAAA,MACD,GAAGH,KAAIE;AAAA,MACP,GAAGD,KAAI;AAAA,IACT,CAAC;AACD,UAAM,OAAO,IAAID,KAAI,UAAU,IAAIC,KAAI,UAAU,KAAKD,KAAIE,EAAC,IAAID,KAAI,UAAU,KAAKD,KAAIE,EAAC,IAAID,KAAIE,EAAC,KAAKH,KAAI,UAAU,IAAIC,KAAIE,EAAC,KAAKH,KAAI,UAAU,IAAIC,KAAI,UAAU;AAAA,mBAChJD,KAAI,UAAU,IAAIC,EAAC,KAAKD,KAAIE,EAAC,IAAID,EAAC;AAAA,mBAClCD,EAAC,IAAIC,KAAI,UAAU,KAAKD,EAAC,IAAIC,KAAIE,EAAC;AACnD,QAAI,KAAK,SAAS,aAAa;AAC7B,cAAQ,YAAY;AACpB,cAAQ,YAAY;AAAA,IACtB;AACA,UAAM,KAAK,GAAG,KAAK,MAAM,OAAO;AAChC,UAAM,cAAc,SAAS,OAAO,MAAM,IAAI,cAAc;AAC5D,gBAAY,KAAK,aAAa,aAAa,aAAa,CAAC,KAAK,aAAa,CAAC,GAAG;AAC/E,gBAAY,KAAK,SAAS,uBAAuB;AACjD,QAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,kBAAY,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,IACvD;AACA,QAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,kBAAY,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,IACxD;AACA,UAAM,KAAK,aAAa,aAAa,EAAE,KAAK,QAAQ,KAAK,aAAa,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,KAAK,EAAE,KAAK,SAAS,KAAK,aAAa,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG;AAC7K,qBAAiB,MAAM,WAAW;AAClC,SAAK,YAAY,SAAU,OAAO;AAChC,YAAM,MAAM,kBAAkB,QAAQ,MAAM,iBAAiB,KAAK;AAClE,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,YAAY,YAAY;AAK/B,SAAe,MAAM,QAAQ,MAAM;AAAA;AACjC,UAAM,aAAa;AACnB,QAAI,WAAW,OAAO;AACpB,WAAK,QAAQ,WAAW;AAAA,IAC1B;AACA,QAAI,KAAK,SAAS,aAAa;AAC7B,YAAM;AAAA,QACJ,gBAAgB;AAAA,MAClB,IAAI,UAAU;AACd,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,YAAM,iBAAiB,iCAClB,OADkB;AAAA,QAErB,IAAI,KAAK,KAAK;AAAA,QACd,MAAM;AAAA,QACN,WAAW,CAAC,gBAAgB,SAAS,UAAU,EAAE;AAAA,MACnD;AACA,YAAM,MAAM,QAAQ,cAAc;AAAA,IACpC;AACA,UAAM,SAAS,UAAU;AACzB,SAAK,gBAAgB,OAAO;AAC5B,QAAI,UAAU,OAAO,IAAI,kBAAkB;AAC3C,QAAI,eAAe,OAAO,IAAI,iBAAiB;AAC/C,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,QAAI,WAAW,WAAW,WAAW,KAAK,KAAK,OAAO;AACpD,YAAM,WAAW;AAAA,QACf,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,eAAe;AAAA,QACf,eAAe,UAAU;AAAA,QACzB,SAAS;AAAA,MACX;AACA,UAAI,mBAAmB,KAAK,OAAO,MAAM,IAAI,SAAS,gBAAgB,IAAI,OAAO,GAAG,gBAAgB;AAClG,aAAK,QAAQ,OAAO,GAAG;AAAA,MACzB;AACA,YAAM,YAAY,MAAM,SAAS,QAAQ,MAAM,QAAQ;AACvD,UAAI,CAAC,SAAS,OAAO,UAAU,GAAG;AAChC,cAAM,cAAc,UAAU,OAAO,MAAM;AAC3C,cAAM,OAAO,YAAY,KAAK,GAAG,QAAQ;AACzC,oBAAY,KAAK,aAAa,aAAa,CAAC,KAAK,QAAQ,CAAC,MAAM;AAAA,MAClE;AACA,aAAO;AAAA,IACT;AACA,QAAI,CAAC,OAAO,YAAY;AACtB,iBAAW;AACX,sBAAgB;AAAA,IAClB;AACA,QAAI,aAAa,eAAe,IAAI;AACpC,QAAI,CAAC,YAAY;AACf,mBAAa;AAAA,IACf;AACA,UAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,UAAU,EAAE,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AAC9F,UAAM,WAAW,MAAM,QAAQ,UAAU,KAAK,SAAS,IAAI,QAAQ,GAAG,GAAG,CAAC,MAAM,GAAG,WAAW;AAC9F,aAAS,UAAU;AACnB,QAAI,UAAU;AACd,UAAM,WAAW,CAAC;AAClB,QAAI,eAAe;AACnB,QAAI,eAAe;AACnB,QAAI,eAAe;AACnB,QAAI,kBAAkB;AACtB,QAAI,cAAc;AAClB,QAAI,iBAAiB;AACrB,eAAW,aAAa,WAAW,YAAY;AAC7C,YAAM,WAAW,MAAM,QAAQ,UAAU,UAAU,MAAM,QAAQ,GAAG,SAAS,CAAC,gBAAgB,GAAG,WAAW;AAC5G,qBAAe,KAAK,IAAI,cAAc,SAAS,QAAQ,OAAO;AAC9D,YAAM,YAAY,MAAM,QAAQ,UAAU,UAAU,MAAM,QAAQ,GAAG,SAAS,CAAC,gBAAgB,GAAG,WAAW;AAC7G,qBAAe,KAAK,IAAI,cAAc,UAAU,QAAQ,OAAO;AAC/D,YAAM,WAAW,MAAM,QAAQ,UAAU,UAAU,KAAK,KAAK,GAAG,QAAQ,GAAG,SAAS,CAAC,gBAAgB,GAAG,WAAW;AACnH,qBAAe,KAAK,IAAI,cAAc,SAAS,QAAQ,OAAO;AAC9D,YAAM,cAAc,MAAM,QAAQ,UAAU,UAAU,SAAS,QAAQ,GAAG,SAAS,CAAC,mBAAmB,GAAG,WAAW;AACrH,wBAAkB,KAAK,IAAI,iBAAiB,YAAY,QAAQ,OAAO;AACvE,iBAAW,KAAK,IAAI,SAAS,QAAQ,UAAU,QAAQ,SAAS,QAAQ,YAAY,MAAM,IAAI;AAC9F,eAAS,KAAK,OAAO;AAAA,IACvB;AACA,aAAS,IAAI;AACb,QAAI,qBAAqB;AACzB,QAAI,gBAAgB,SAAS;AAC3B,oBAAc;AACd,qBAAe;AACf;AAAA,IACF;AACA,QAAI,mBAAmB,SAAS;AAC9B,uBAAiB;AACjB,wBAAkB;AAClB;AAAA,IACF;AACA,UAAM,YAAY,SAAS,KAAK,EAAE,QAAQ;AAC1C,QAAI,SAAS,QAAQ,UAAU,KAAK,eAAe,eAAe,eAAe,mBAAmB,GAAG;AACrG,YAAM,aAAa,SAAS,QAAQ,UAAU,KAAK,eAAe,eAAe,eAAe;AAChG,sBAAgB,aAAa;AAC7B,sBAAgB,aAAa;AAC7B,UAAI,eAAe,GAAG;AACpB,wBAAgB,aAAa;AAAA,MAC/B;AACA,UAAI,kBAAkB,GAAG;AACvB,2BAAmB,aAAa;AAAA,MAClC;AAAA,IACF;AACA,UAAM,WAAW,eAAe,eAAe,eAAe;AAC9D,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,QAAI,KAAK,SAAS,aAAa;AAC7B,cAAQ,YAAY;AACpB,cAAQ,YAAY;AAAA,IACtB;AACA,UAAMD,KAAI,KAAK,IAAI,UAAU,QAAQ,UAAU,GAAG,MAAM,SAAS,GAAG,QAAQ;AAC5E,UAAMC,KAAI,KAAK,IAAI,UAAU,UAAU,SAAS,CAAC,KAAK,WAAW,cAAc,MAAM,UAAU,CAAC;AAChG,UAAMH,KAAI,CAACE,KAAI;AACf,UAAMD,KAAI,CAACE,KAAI;AACf,aAAS,UAAU,qBAAqB,EAAE,KAAK,CAACQ,IAAGb,IAAG,UAAU;AAC9D,YAAM,QAAQ,eAAQ,MAAMA,EAAC,CAAC;AAC9B,YAAM,YAAY,MAAM,KAAK,WAAW;AACxC,UAAI,aAAa;AACjB,UAAI,aAAa;AACjB,UAAI,WAAW;AACb,cAAM,QAAQ,OAAO,8BAA8B;AACnD,cAAM,YAAY,MAAM,KAAK,SAAS;AACtC,YAAI,WAAW;AACb,uBAAa,WAAW,UAAU,CAAC,CAAC;AACpC,uBAAa,WAAW,UAAU,CAAC,CAAC;AACpC,cAAI,MAAM,KAAK,OAAO,EAAE,SAAS,gBAAgB,GAAG;AAClD,0BAAc;AAAA,UAChB,WAAW,MAAM,KAAK,OAAO,EAAE,SAAS,gBAAgB,GAAG;AACzD,0BAAc,eAAe;AAAA,UAC/B,WAAW,MAAM,KAAK,OAAO,EAAE,SAAS,mBAAmB,GAAG;AAC5D,0BAAc,eAAe,eAAe;AAAA,UAC9C;AAAA,QACF;AAAA,MACF;AACA,YAAM,KAAK,aAAa,aAAaE,KAAI,UAAU,IAAI,UAAU,KAAK,aAAaC,KAAI,SAAS,SAAS,eAAe,CAAC,GAAG;AAAA,IAC9H,CAAC;AACD,aAAS,OAAO,OAAO,EAAE,KAAK,aAAa,eAAe,CAAC,SAAS,QAAQ,IAAI,QAAQA,KAAI,eAAe,KAAK,GAAG;AACnH,UAAM,YAAY,GAAG,UAAUD,IAAGC,IAAGC,IAAGC,IAAG,OAAO;AAClD,UAAM,QAAQ,SAAS,OAAO,MAAM,WAAW,cAAc,EAAE,KAAK,SAAS,UAAU,KAAK,EAAE,CAAC;AAC/F,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,UAAU;AACd,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,aAAS,KAAK,CAAC;AACf,eAAW,CAACL,IAAG,QAAQ,KAAK,SAAS,QAAQ,GAAG;AAC9C,UAAIA,OAAM,KAAK,SAAS,SAAS,GAAG;AAClC;AAAA,MACF;AACA,YAAM,SAASA,KAAI,MAAM,KAAK,aAAa;AAC3C,YAAM,aAAa,GAAG,UAAUE,IAAG,SAAS,SAASC,KAAI,UAAUC,IAAG,SAAS,QAAQ,iCAClF,UADkF;AAAA,QAErF,MAAM,SAAS,UAAU;AAAA,QACzB,QAAQ;AAAA,MACV,EAAC;AACD,eAAS,OAAO,MAAM,YAAY,SAAS,EAAE,KAAK,SAAS,UAAU,KAAK,EAAE,CAAC,EAAE,KAAK,SAAS,YAAYJ,KAAI,MAAM,IAAI,SAAS,KAAK,EAAE;AAAA,IACzI;AACA,QAAI,YAAY,GAAG,KAAKE,IAAG,SAAS,SAASC,IAAGC,KAAIF,IAAG,SAAS,SAASC,IAAG,OAAO;AACnF,aAAS,OAAO,MAAM,SAAS,EAAE,KAAK,SAAS,SAAS;AACxD,gBAAY,GAAG,KAAK,eAAeD,IAAG,SAAS,SAASC,IAAG,eAAeD,IAAGG,KAAIF,IAAG,OAAO;AAC3F,aAAS,OAAO,MAAM,SAAS,EAAE,KAAK,SAAS,SAAS;AACxD,QAAI,aAAa;AACf,kBAAY,GAAG,KAAK,eAAe,eAAeD,IAAG,SAAS,SAASC,IAAG,eAAe,eAAeD,IAAGG,KAAIF,IAAG,OAAO;AACzH,eAAS,OAAO,MAAM,SAAS,EAAE,KAAK,SAAS,SAAS;AAAA,IAC1D;AACA,QAAI,gBAAgB;AAClB,kBAAY,GAAG,KAAK,eAAe,eAAe,eAAeD,IAAG,SAAS,SAASC,IAAG,eAAe,eAAe,eAAeD,IAAGG,KAAIF,IAAG,OAAO;AACvJ,eAAS,OAAO,MAAM,SAAS,EAAE,KAAK,SAAS,SAAS;AAAA,IAC1D;AACA,eAAW,YAAY,UAAU;AAC/B,kBAAY,GAAG,KAAKD,IAAG,SAAS,SAASC,KAAI,UAAUC,KAAIF,IAAG,SAAS,SAASC,KAAI,UAAU,OAAO;AACrG,eAAS,OAAO,MAAM,SAAS,EAAE,KAAK,SAAS,SAAS;AAAA,IAC1D;AACA,qBAAiB,MAAM,KAAK;AAC5B,SAAK,YAAY,SAAU,OAAO;AAChC,aAAO,kBAAkB,KAAK,MAAM,KAAK;AAAA,IAC3C;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,OAAO,OAAO;AACrB,SAAe,QAAQ,IAAU,IAAW,IAAkE;AAAA,6CAAvF,UAAU,WAAW,QAAQ,aAAa,GAAG,aAAa,GAAG,UAAU,CAAC,GAAG,QAAQ,IAAI;AAC5G,UAAM,QAAQ,SAAS,OAAO,GAAG,EAAE,KAAK,SAAS,SAAS,QAAQ,KAAK,GAAG,CAAC,EAAE,EAAE,KAAK,aAAa,aAAa,UAAU,KAAK,UAAU,GAAG,EAAE,KAAK,SAAS,KAAK;AAC/J,QAAI,cAAc,kBAAkB,SAAS,GAAG;AAC9C,kBAAY,kBAAkB,SAAS;AACvC,kBAAY,UAAU,WAAW,KAAK,MAAM,EAAE,WAAW,KAAK,MAAM;AAAA,IACtE;AACA,UAAM,QAAQ,MAAM,KAAK,EAAE,YAAY,MAAM,WAAW,OAAO,WAAW;AAAA,MACxE,OAAO,mBAAmB,WAAW,MAAM,IAAI;AAAA,MAC/C;AAAA,MACA,eAAe,OAAO;AAAA,IACxB,GAAG,MAAM,CAAC;AACV,QAAI,UAAU,SAAS,MAAM,KAAK,UAAU,SAAS,MAAM,GAAG;AAC5D,UAAI,QAAQ,MAAM,SAAS,CAAC;AAC5B,YAAM,cAAc,MAAM,YAAY,WAAW,QAAQ,GAAG,EAAE,WAAW,QAAQ,GAAG;AACpF,aAAO,MAAM,WAAW,CAAC,GAAG;AAC1B,gBAAQ,MAAM,WAAW,CAAC;AAC1B,cAAM,cAAc,MAAM,YAAY,WAAW,QAAQ,GAAG,EAAE,WAAW,QAAQ,GAAG;AAAA,MACtF;AAAA,IACF;AACA,QAAI,OAAO,MAAM,QAAQ;AACzB,QAAI,SAAS,OAAO,UAAU,GAAG;AAC/B,YAAM,MAAM,MAAM,SAAS,CAAC;AAC5B,UAAI,MAAM,YAAY;AACtB,YAAM,KAAK,eAAQ,KAAK;AACxB,aAAO,IAAI,sBAAsB;AACjC,SAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,SAAG,KAAK,UAAU,KAAK,MAAM;AAAA,IAC/B;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,SAAS,SAAS;AAQzB,SAAe,WAAW,IAAQ,IAAM,IAAQ,IAAiD;AAAA,6CAAvE,QAAQ,MAAM,QAAQ,eAAe,MAAM,OAAO,MAAM,WAAW,IAAI;AAC/F,UAAM,eAAe,CAAC,gBAAgB,IAAI;AAC1C,UAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,eAAe,IAAI,CAAC,EAAE,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AACxG,QAAI,kBAAkB;AACtB,QAAI,aAAa;AACjB,QAAI,eAAe;AACnB,QAAI,eAAe;AACnB,QAAI,wBAAwB;AAC5B,QAAI,mBAAmB;AACvB,QAAI,qBAAqB;AACzB,sBAAkB,SAAS,OAAO,GAAG,EAAE,KAAK,SAAS,uBAAuB;AAC5E,QAAI,KAAK,YAAY,SAAS,GAAG;AAC/B,YAAM,aAAa,KAAK,YAAY,CAAC;AACrC,YAAM,SAAS,iBAAiB;AAAA,QAC9B,MAAM,IAAO,UAAU;AAAA,MACzB,GAAG,CAAC;AACJ,YAAM,sBAAsB,gBAAgB,KAAK,EAAE,QAAQ;AAC3D,8BAAwB,oBAAoB;AAAA,IAC9C;AACA,iBAAa,SAAS,OAAO,GAAG,EAAE,KAAK,SAAS,kBAAkB;AAClE,UAAM,SAAS,YAAY,MAAM,GAAG,CAAC,qBAAqB,CAAC;AAC3D,UAAM,iBAAiB,WAAW,KAAK,EAAE,QAAQ;AACjD,uBAAmB,eAAe;AAClC,mBAAe,SAAS,OAAO,GAAG,EAAE,KAAK,SAAS,oBAAoB;AACtE,QAAI,UAAU;AACd,eAAW,UAAU,KAAK,SAAS;AACjC,YAAM,SAAS,MAAM,SAAS,cAAc,QAAQ,SAAS,CAAC,OAAO,gBAAgB,CAAC,CAAC;AACvF,iBAAW,SAAS;AAAA,IACtB;AACA,yBAAqB,aAAa,KAAK,EAAE,QAAQ,EAAE;AACnD,QAAI,sBAAsB,GAAG;AAC3B,2BAAqB,MAAM;AAAA,IAC7B;AACA,mBAAe,SAAS,OAAO,GAAG,EAAE,KAAK,SAAS,oBAAoB;AACtE,QAAI,iBAAiB;AACrB,eAAW,UAAU,KAAK,SAAS;AACjC,YAAM,SAAS,MAAM,SAAS,cAAc,QAAQ,gBAAgB,CAAC,OAAO,gBAAgB,CAAC,CAAC;AAC9F,wBAAkB,SAAS;AAAA,IAC7B;AACA,QAAI,OAAO,SAAS,KAAK,EAAE,QAAQ;AACnC,QAAI,oBAAoB,MAAM;AAC5B,YAAM,sBAAsB,gBAAgB,KAAK,EAAE,QAAQ;AAC3D,sBAAgB,KAAK,aAAa,aAAa,CAAC,oBAAoB,QAAQ,CAAC,GAAG;AAAA,IAClF;AACA,eAAW,KAAK,aAAa,aAAa,CAAC,eAAe,QAAQ,CAAC,KAAK,qBAAqB,GAAG;AAChG,WAAO,SAAS,KAAK,EAAE,QAAQ;AAC/B,iBAAa,KAAK,aAAa,aAAa,CAAC,KAAK,wBAAwB,mBAAmB,MAAM,CAAC,GAAG;AACvG,WAAO,SAAS,KAAK,EAAE,QAAQ;AAC/B,iBAAa,KAAK,aAAa,aAAa,CAAC,KAAK,wBAAwB,oBAAoB,qBAAqB,qBAAqB,MAAM,IAAI,MAAM,EAAE,GAAG;AAC7J,WAAO,SAAS,KAAK,EAAE,QAAQ;AAC/B,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AACA,OAAO,YAAY,YAAY;AAC/B,SAAe,SAAS,IAAa,IAAM,IAAsB;AAAA,6CAAzC,aAAa,MAAM,SAAS,SAAS,CAAC,GAAG;AAC/D,UAAM,SAAS,YAAY,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO,EAAE,KAAK,SAAS,OAAO,KAAK,IAAI,CAAC;AAC7F,UAAM,SAAS,UAAU;AACzB,QAAI,gBAAgB,mBAAmB,OAAO,KAAK,gBAAgB,SAAS,OAAO,UAAU,KAAK;AAClG,QAAI,cAAc;AAClB,QAAI,UAAU,MAAM;AAClB,oBAAc,KAAK;AAAA,IACrB,OAAO;AACL,oBAAc,KAAK;AAAA,IACrB;AACA,QAAI,CAAC,iBAAiB,YAAY,WAAW,IAAI,GAAG;AAClD,oBAAc,YAAY,UAAU,CAAC;AAAA,IACvC;AACA,QAAI,SAAS,WAAW,GAAG;AACzB,sBAAgB;AAAA,IAClB;AACA,UAAM,QAAQ,MAAM,WAAW,QAAQ,cAAc,eAAe,WAAW,CAAC,GAAG;AAAA,MACjF,OAAO,mBAAmB,aAAa,MAAM,IAAI;AAAA;AAAA,MAEjD,SAAS;AAAA,MACT;AAAA,IACF,GAAG,MAAM;AACT,QAAI;AACJ,QAAI,gBAAgB;AACpB,QAAI,CAAC,eAAe;AAClB,UAAI,OAAO,SAAS,qBAAqB,GAAG;AAC1C,uBAAQ,KAAK,EAAE,UAAU,OAAO,EAAE,KAAK,eAAe,EAAE;AAAA,MAC1D;AACA,sBAAgB,MAAM,SAAS;AAC/B,YAAM,YAAY,MAAM,SAAS,CAAC;AAClC,UAAI,MAAM,gBAAgB,MAAM,MAAM,YAAY,SAAS,KAAK,GAAG;AACjE,kBAAU,cAAc,YAAY,CAAC,IAAI,YAAY,UAAU,CAAC,EAAE,WAAW,QAAQ,GAAG,EAAE,WAAW,QAAQ,GAAG,EAAE,KAAK;AACvH,cAAM,gBAAgB,YAAY,CAAC,MAAM;AACzC,YAAI,eAAe;AACjB,oBAAU,cAAc,UAAU,YAAY,CAAC,IAAI,MAAM,UAAU,YAAY,UAAU,CAAC;AAAA,QAC5F;AAAA,MACF;AACA,UAAI,UAAU,gBAAgB,aAAa;AACzC,kBAAU,cAAc;AAAA,MAC1B;AACA,aAAO,MAAM,QAAQ;AAAA,IACvB,OAAO;AACL,YAAM,MAAM,MAAM,SAAS,CAAC;AAC5B,YAAM,KAAK,eAAQ,KAAK;AACxB,sBAAgB,IAAI,UAAU,MAAM,MAAM,EAAE;AAC5C,UAAI,IAAI,UAAU,SAAS,SAAS,GAAG;AACrC,yBAAiB,IAAI,UAAU,MAAM,QAAQ,EAAE,SAAS;AAAA,MAC1D;AACA,YAAM,SAAS,IAAI,qBAAqB,KAAK;AAC7C,UAAI,QAAQ;AACV,cAAM,YAAY,YAAY,QAAQ,eAAe,EAAE,EAAE,KAAK,MAAM;AACpE,cAAM,QAAQ,IAAI,CAAC,GAAG,MAAM,EAAE,IAAI,SAAO,IAAI,QAAQ,SAAO;AAC1D,mBAAS,aAAa;AACpB,gBAAI,MAAM,UAAU;AACpB,gBAAI,MAAM,gBAAgB;AAC1B,gBAAI,WAAW;AACb,oBAAM,eAAe,OAAO,UAAU,SAAS,KAAK,OAAO,iBAAiB,SAAS,IAAI,EAAE;AAC3F,oBAAM,kBAAkB;AACxB,oBAAM,QAAQ,SAAS,cAAc,EAAE,IAAI,kBAAkB;AAC7D,kBAAI,MAAM,WAAW;AACrB,kBAAI,MAAM,WAAW;AAAA,YACvB,OAAO;AACL,kBAAI,MAAM,QAAQ;AAAA,YACpB;AACA,gBAAI,GAAG;AAAA,UACT;AACA,iBAAO,YAAY,YAAY;AAC/B,qBAAW,MAAM;AACf,gBAAI,IAAI,UAAU;AAChB,yBAAW;AAAA,YACb;AAAA,UACF,CAAC;AACD,cAAI,iBAAiB,SAAS,UAAU;AACxC,cAAI,iBAAiB,QAAQ,UAAU;AAAA,QACzC,CAAC,CAAC,CAAC;AAAA,MACL;AACA,aAAO,IAAI,sBAAsB;AACjC,SAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,SAAG,KAAK,UAAU,KAAK,MAAM;AAAA,IAC/B;AACA,WAAO,KAAK,aAAa,kBAAkB,CAAC,KAAK,UAAU,IAAI,iBAAiB,WAAW,GAAG;AAC9F,WAAO,KAAK;AAAA,EACd;AAAA;AACA,OAAO,UAAU,SAAS;AAG1B,SAAe,SAAS,QAAQ,MAAM;AAAA;AACpC,UAAM,SAAS,WAAW;AAC1B,UAAM,UAAU,OAAO,MAAM,WAAW;AACxC,UAAM,MAAM;AACZ,UAAM,gBAAgB,KAAK,iBAAiB,SAAS,OAAO,UAAU,KAAK;AAC3E,UAAM,YAAY;AAClB,cAAU,cAAc,UAAU,eAAe,CAAC;AAClD,cAAU,UAAU,UAAU,WAAW,CAAC;AAC1C,cAAU,UAAU,UAAU,WAAW,CAAC;AAC1C,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,MAAM,WAAW,QAAQ,MAAM,QAAQ,eAAe,GAAG;AAC7D,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,SAAK,YAAY,UAAU,UAAU;AACrC,UAAM,SAAS,UAAU,QAAQ,KAAK,GAAG,KAAK,cAAc;AAC5D,QAAI,CAAC,KAAK,WAAW;AACnB,WAAK,YAAY,OAAO,WAAW,cAAc,EAAE,EAAE,MAAM,GAAG;AAAA,IAChE;AACA,UAAM,iBAAiB,UAAU,QAAQ,WAAW,KAAK,UAAU,QAAQ,WAAW,KAAK,CAAC,OAAO,OAAO;AAC1G,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,QAAI,KAAK,SAAS,aAAa;AAC7B,cAAQ,YAAY;AACpB,cAAQ,YAAY;AAAA,IACtB;AACA,UAAMC,KAAI,KAAK;AACf,QAAIC,KAAI,KAAK;AACb,QAAI,UAAU,QAAQ,WAAW,KAAK,UAAU,QAAQ,WAAW,GAAG;AACpE,MAAAA,MAAK;AAAA,IACP,WAAW,UAAU,QAAQ,SAAS,KAAK,UAAU,QAAQ,WAAW,GAAG;AACzE,MAAAA,MAAK,MAAM;AAAA,IACb;AACA,UAAMH,KAAI,CAACE,KAAI;AACf,UAAMD,KAAI,CAACE,KAAI;AACf,UAAM,YAAY,GAAG,UAAUH,KAAI,SAASC,KAAI,WAAW,iBAAiB,UAAU,UAAU,QAAQ,WAAW,KAAK,UAAU,QAAQ,WAAW,IAAI,CAAC,UAAU,IAAI,IAAIC,KAAI,IAAI,SAASC,KAAI,IAAI,WAAW,iBAAiB,UAAU,IAAI,UAAU,QAAQ,WAAW,KAAK,UAAU,QAAQ,WAAW,IAAI,CAAC,UAAU,IAAI,OAAO;AACxU,UAAM,QAAQ,SAAS,OAAO,MAAM,WAAW,cAAc;AAC7D,UAAM,KAAK,SAAS,uBAAuB;AAC3C,UAAM,WAAW,MAAM,KAAK,EAAE,QAAQ;AACtC,aAAS,UAAU,OAAO,EAAE,KAAK,CAACQ,IAAGb,IAAG,UAAU;AAChD,YAAM,QAAQ,eAAQ,MAAMA,EAAC,CAAC;AAC9B,YAAM,YAAY,MAAM,KAAK,WAAW;AACxC,UAAI,aAAa;AACjB,UAAI,WAAW;AACb,cAAM,QAAQ,OAAO,8BAA8B;AACnD,cAAM,YAAY,MAAM,KAAK,SAAS;AACtC,YAAI,WAAW;AACb,uBAAa,WAAW,UAAU,CAAC,CAAC;AAAA,QACtC;AAAA,MACF;AACA,UAAI,gBAAgB,aAAaG,KAAI,WAAW,iBAAiB,UAAU,UAAU,QAAQ,WAAW,KAAK,UAAU,QAAQ,WAAW,IAAI,CAAC,UAAU,IAAI;AAC7J,UAAI,CAAC,eAAe;AAClB,yBAAiB;AAAA,MACnB;AACA,UAAI,gBAAgBD;AACpB,UAAI,MAAM,KAAK,OAAO,EAAE,SAAS,aAAa,KAAK,MAAM,KAAK,OAAO,EAAE,SAAS,kBAAkB,GAAG;AACnG,wBAAgB,CAAC,MAAM,KAAK,GAAG,QAAQ,EAAE,QAAQ,KAAK;AACtD,iBAAS,UAAU,MAAM,EAAE,KAAK,SAAUY,KAAIC,KAAI,QAAQ;AACxD,cAAI,OAAO,iBAAiB,OAAOA,GAAE,CAAC,EAAE,eAAe,UAAU;AAC/D,4BAAgB;AAAA,UAClB;AAAA,QACF,CAAC;AAAA,MACH;AACA,YAAM,KAAK,aAAa,aAAa,aAAa,KAAK,aAAa,GAAG;AAAA,IACzE,CAAC;AACD,UAAM,wBAAwB,SAAS,OAAO,mBAAmB,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,iBAAiB,UAAU,IAAI,MAAM;AACnI,UAAM,mBAAmB,SAAS,OAAO,cAAc,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,iBAAiB,UAAU,IAAI,MAAM;AACzH,UAAM,qBAAqB,SAAS,OAAO,gBAAgB,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,iBAAiB,UAAU,IAAI,MAAM;AAC7H,QAAI,UAAU,QAAQ,SAAS,KAAK,UAAU,QAAQ,SAAS,KAAK,gBAAgB;AAClF,YAAM,YAAY,GAAG,KAAK,SAAS,GAAG,wBAAwB,mBAAmBZ,KAAI,SAAS,SAAS,IAAI,SAAS,OAAO,wBAAwB,mBAAmBA,KAAI,SAAS,OAAO;AAC1L,YAAM,OAAO,SAAS,OAAO,MAAM,SAAS;AAC5C,WAAK,KAAK,SAAS,SAAS,EAAE,KAAK,SAAS,MAAM;AAAA,IACpD;AACA,QAAI,kBAAkB,UAAU,QAAQ,SAAS,KAAK,UAAU,QAAQ,SAAS,GAAG;AAClF,YAAM,YAAY,GAAG,KAAK,SAAS,GAAG,wBAAwB,mBAAmB,qBAAqBA,KAAI,MAAM,IAAI,SAAS,SAAS,IAAI,SAAS,OAAO,wBAAwB,mBAAmB,qBAAqBA,KAAI,UAAU,MAAM,GAAG,OAAO;AACxP,YAAM,OAAO,SAAS,OAAO,MAAM,SAAS;AAC5C,WAAK,KAAK,SAAS,SAAS,EAAE,KAAK,SAAS,MAAM;AAAA,IACpD;AACA,QAAI,UAAU,SAAS,aAAa;AAClC,eAAS,UAAU,MAAM,EAAE,KAAK,SAAS,MAAM;AAAA,IACjD;AACA,UAAM,OAAO,eAAe,EAAE,KAAK,SAAS,MAAM;AAClD,aAAS,UAAU,UAAU,EAAE,OAAO,MAAM,EAAE,KAAK,SAAS,MAAM;AAClE,QAAI,KAAK,YAAY;AACnB,eAAS,UAAU,MAAM,EAAE,KAAK,SAAS,KAAK,UAAU;AAAA,IAC1D,OAAO;AACL,eAAS,UAAU,MAAM,EAAE,KAAK,SAAS,MAAM;AAAA,IACjD;AACA,QAAI,CAAC,eAAe;AAClB,YAAM,aAAa,OAAO,qBAAqB;AAC/C,YAAM,QAAQ,WAAW,KAAK,MAAM;AACpC,UAAI,OAAO;AACT,cAAM,aAAa,MAAM,CAAC,EAAE,QAAQ,SAAS,MAAM;AACnD,iBAAS,UAAU,OAAO,EAAE,KAAK,SAAS,UAAU;AAAA,MACtD,WAAW,aAAa;AACtB,cAAM,SAAS,WAAW,KAAK,WAAW;AAC1C,YAAI,QAAQ;AACV,gBAAM,aAAa,OAAO,CAAC,EAAE,QAAQ,SAAS,MAAM;AACpD,mBAAS,UAAU,OAAO,EAAE,KAAK,SAAS,UAAU;AAAA,QACtD;AAAA,MACF;AAAA,IACF;AACA,qBAAiB,MAAM,KAAK;AAC5B,SAAK,YAAY,SAAU,OAAO;AAChC,aAAO,kBAAkB,KAAK,MAAM,KAAK;AAAA,IAC3C;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,UAAU,UAAU;AAK3B,SAAe,eAAe,QAAQ,MAAM;AAAA;AAC1C,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,IAAI;AACtB,SAAK,aAAa;AAClB,UAAM,kBAAkB;AACxB,UAAM,cAAc;AACpB,UAAM,UAAU;AAChB,UAAM,MAAM;AACZ,UAAM,oBAAoB,kBAAkB;AAC5C,UAAM,UAAU,eAAe,IAAI;AACnC,UAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO,EAAE,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AAC3F,QAAI;AACJ,QAAI,mBAAmB;AACrB,mBAAa,MAAM,SAAS,UAAU,WAAW,gBAAgB,IAAI,YAAY,GAAG,KAAK,UAAU;AAAA,IACrG,OAAO;AACL,mBAAa,MAAM,SAAS,UAAU,2BAA2B,GAAG,KAAK,UAAU;AAAA,IACrF;AACA,QAAI,qBAAqB;AACzB,UAAM,aAAa,MAAM,SAAS,UAAU,gBAAgB,MAAM,oBAAoB,KAAK,aAAa,sBAAsB;AAC9H,0BAAsB,aAAa;AACnC,QAAI,mBAAmB;AACrB,YAAM,WAAW,MAAM,SAAS,UAAU,GAAG,gBAAgB,gBAAgB,OAAO,gBAAgB,aAAa,KAAK,EAAE,IAAI,oBAAoB,KAAK,UAAU;AAC/J,4BAAsB;AACtB,YAAM,aAAa,MAAM,SAAS,UAAU,GAAG,gBAAgB,OAAO,SAAS,gBAAgB,IAAI,KAAK,EAAE,IAAI,oBAAoB,KAAK,UAAU;AACjJ,4BAAsB;AACtB,YAAM,aAAa,MAAM,SAAS,UAAU,GAAG,gBAAgB,OAAO,SAAS,gBAAgB,IAAI,KAAK,EAAE,IAAI,oBAAoB,KAAK,UAAU;AACjJ,4BAAsB;AACtB,YAAM,SAAS,UAAU,GAAG,gBAAgB,eAAe,iBAAiB,gBAAgB,YAAY,KAAK,EAAE,IAAI,oBAAoB,KAAK,UAAU;AAAA,IACxJ,OAAO;AACL,YAAM,cAAc,MAAM,SAAS,UAAU,GAAG,YAAY,OAAO,SAAS,YAAY,IAAI,KAAK,EAAE,IAAI,oBAAoB,KAAK,UAAU;AAC1I,4BAAsB;AACtB,YAAM,SAAS,UAAU,GAAG,YAAY,SAAS,YAAY,YAAY,MAAM,KAAK,EAAE,IAAI,oBAAoB,KAAK,UAAU;AAAA,IAC/H;AACA,UAAM,cAAc,SAAS,KAAK,GAAG,QAAQ,EAAE,SAAS,OAAO;AAC/D,UAAM,eAAe,SAAS,KAAK,GAAG,QAAQ,EAAE,UAAU,OAAO;AACjE,UAAMD,KAAI,CAAC,aAAa;AACxB,UAAMC,KAAI,CAAC,cAAc;AACzB,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,QAAI,KAAK,SAAS,aAAa;AAC7B,cAAQ,YAAY;AACpB,cAAQ,YAAY;AAAA,IACtB;AACA,UAAM,YAAY,GAAG,UAAUD,IAAGC,IAAG,YAAY,aAAa,OAAO;AACrE,UAAM,QAAQ,SAAS,OAAO,MAAM,WAAW,cAAc;AAC7D,UAAM,KAAK,SAAS,uBAAuB,EAAE,KAAK,SAAS,UAAU;AACrE,aAAS,UAAU,QAAQ,EAAE,KAAK,CAACU,IAAGb,IAAG,UAAU;AACjD,YAAM,QAAQ,eAAQ,MAAMA,EAAC,CAAC;AAC9B,YAAM,YAAY,MAAM,KAAK,WAAW;AACxC,UAAI,aAAa;AACjB,UAAI,aAAa;AACjB,UAAI,WAAW;AACb,cAAM,QAAQ,OAAO,8BAA8B;AACnD,cAAM,YAAY,MAAM,KAAK,SAAS;AACtC,YAAI,WAAW;AACb,uBAAa,WAAW,UAAU,CAAC,CAAC;AACpC,uBAAa,WAAW,UAAU,CAAC,CAAC;AAAA,QACtC;AAAA,MACF;AACA,YAAM,gBAAgB,aAAa,cAAc;AACjD,UAAI,gBAAgBE,KAAI,UAAU;AAClC,UAAIF,OAAM,KAAKA,OAAM,GAAG;AACtB,wBAAgB;AAAA,MAClB;AACA,YAAM,KAAK,aAAa,aAAa,aAAa,KAAK,gBAAgB,OAAO,GAAG;AAAA,IACnF,CAAC;AACD,QAAI,qBAAqB,aAAa,aAAa,KAAK;AACtD,YAAM,YAAY,GAAG,KAAKE,IAAGC,KAAI,aAAa,aAAa,KAAKD,KAAI,YAAYC,KAAI,aAAa,aAAa,KAAK,OAAO;AAC1H,YAAM,cAAc,SAAS,OAAO,MAAM,SAAS;AACnD,kBAAY,KAAK,SAAS,UAAU;AAAA,IACtC;AACA,qBAAiB,MAAM,KAAK;AAC5B,SAAK,YAAY,SAAU,OAAO;AAChC,aAAO,kBAAkB,KAAK,MAAM,KAAK;AAAA,IAC3C;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,gBAAgB,gBAAgB;AACvC,SAAe,SAAS,aAAa,WAAW,SAAS,QAAQ,IAAI;AAAA;AACnE,QAAI,cAAc,IAAI;AACpB,aAAO;AAAA,IACT;AACA,UAAM,SAAS,YAAY,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO,EAAE,KAAK,SAAS,KAAK;AACjF,UAAM,SAAS,WAAW;AAC1B,UAAM,gBAAgB,OAAO,cAAc;AAC3C,UAAM,QAAQ,MAAM,WAAW,QAAQ,cAAc,eAAe,SAAS,CAAC,GAAG;AAAA,MAC/E,OAAO,mBAAmB,WAAW,MAAM,IAAI;AAAA;AAAA,MAE/C,SAAS;AAAA,MACT;AAAA,MACA;AAAA,IACF,GAAG,MAAM;AACT,QAAI;AACJ,QAAI,CAAC,eAAe;AAClB,YAAM,YAAY,MAAM,SAAS,CAAC;AAClC,iBAAW,SAAS,UAAU,UAAU;AACtC,cAAM,cAAc,MAAM,YAAY,WAAW,QAAQ,GAAG,EAAE,WAAW,QAAQ,GAAG;AACpF,YAAI,OAAO;AACT,gBAAM,aAAa,SAAS,KAAK;AAAA,QACnC;AAAA,MACF;AACA,aAAO,MAAM,QAAQ;AACrB,WAAK,UAAU;AAAA,IACjB,OAAO;AACL,YAAM,MAAM,MAAM,SAAS,CAAC;AAC5B,YAAM,KAAK,eAAQ,KAAK;AACxB,aAAO,IAAI,sBAAsB;AACjC,SAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,SAAG,KAAK,UAAU,KAAK,MAAM;AAAA,IAC/B;AACA,WAAO,KAAK,aAAa,aAAa,CAAC,KAAK,QAAQ,CAAC,IAAI,CAAC,KAAK,SAAS,IAAI,OAAO,GAAG;AACtF,WAAO,KAAK;AAAA,EACd;AAAA;AACA,OAAO,UAAU,SAAS;AAI1B,IAAI,oBAAmC,OAAO,cAAY;AACxD,UAAQ,UAAU;AAAA,IAChB,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA;AAAA,IAET,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,EACX;AACF,GAAG,mBAAmB;AACtB,SAAe,WAAW,IAAQ,IAAY,IAE3C;AAAA,6CAFuB,QAAQ,YAAY;AAAA,IAC5C;AAAA,EACF,GAAG;AACD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,UAAU;AAC5B,eAAW,aAAa,eAAe;AACvC,UAAM,gBAAgB;AACtB,UAAM,WAAW,WAAW;AAC5B,eAAW,SAAS,WAAW,SAAS,OAAO;AAC/C,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA,OAAO;AAAA,IACT,IAAI,MAAM,YAAY,QAAQ,YAAY,eAAe,UAAU,CAAC;AACpE,UAAM,UAAU,WAAW,WAAW;AACtC,QAAI,YAAY;AAChB,QAAI;AACJ,QAAI,YAAY,cAAc,WAAW,UAAU,QAAQ,QAAQ,eAAe;AAChF,kBAAY,QAAQ,QAAQ,cAAc,QAAQ,YAAY,WAAW,MAAM;AAC/E,aAAO,SAAS,OAAO,SAAS,cAAc,EAAE,KAAK,SAAS,oBAAoB,EAAE,KAAK,cAAc,SAAS,EAAE,KAAK,UAAU,QAAQ;AAAA,IAC3I;AACA,UAAM,UAAU;AAAA,MACd,eAAe,WAAW;AAAA,MAC1B,YAAY,WAAW,cAAc;AAAA,MACrC,OAAO,WAAW;AAAA,MAClB,KAAK,WAAW;AAAA,MAChB,SAAS,WAAW,WAAW;AAAA,MAC/B,aAAa;AAAA,IACf;AACA,QAAI,SAAS;AACb,QAAI,MAAM;AACR,OAAC;AAAA,QACC,OAAO;AAAA,QACP,MAAM;AAAA,MACR,IAAI,MAAM,YAAY,MAAM,YAAY,cAAc,WAAW,UAAU,IAAI,OAAO;AAAA,IACxF,OAAO;AACL,OAAC;AAAA,QACC,OAAO;AAAA,QACP,MAAM;AAAA,MACR,IAAI,MAAM,YAAY,UAAU,YAAY,cAAc,WAAW,UAAU,IAAI,OAAO;AAAA,IAC5F;AACA,UAAM;AAAA,MACJ,OAAO;AAAA,MACP,MAAM;AAAA,IACR,IAAI,MAAM,YAAY,UAAU,cAAc,cAAc,WAAW,YAAY,IAAI,OAAO;AAC9F,eAAW,QAAQ;AACnB,UAAM,gBAAgB;AACtB,UAAM,aAAa,YAAY,SAAS;AACxC,UAAM,YAAY,KAAK,IAAI,MAAM,QAAQ,aAAa,MAAM,IAAI;AAChE,UAAM,cAAc,KAAK,IAAI,KAAK,SAAS,gBAAgB,GAAG,YAAY,UAAU,CAAC,IAAI;AACzF,UAAMD,KAAI,CAAC,aAAa;AACxB,UAAMC,KAAI,CAAC,cAAc;AACzB,iBAAa,KAAK,aAAa,gBAAgB,UAAU,aAAa,KAAK,QAAQ,CAAC,YAAY,KAAK,SAAS,KAAK,GAAG;AACtH,YAAQ,KAAK,aAAa,gBAAgB,UAAU,aAAa,KAAK,QAAQ,CAAC,YAAY,KAAK,SAAS,KAAK,GAAG;AACjH,oBAAgB,KAAK,aAAa,gBAAgB,UAAU,aAAa,IAAI,aAAa,QAAQ,IAAI,iBAAiB,QAAQ,CAAC,YAAY,KAAK,SAAS,KAAK,GAAG;AAClK,QAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,WAAW,SAAS,aAAa;AACnC,YAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,YAAM,WAAW,kBAAkB,YAAY,CAAC,CAAC;AACjD,YAAM,YAAY,MAAM,KAAK,GAAG,KAAK,uBAAuBD,IAAGC,IAAG,YAAY,aAAa,MAAM,CAAC,GAAG,QAAQ,IAAI,GAAG,UAAUD,IAAGC,IAAG,YAAY,aAAa,QAAQ;AACrK,cAAQ,SAAS,OAAO,MAAM,WAAW,cAAc;AACvD,YAAM,KAAK,SAAS,uBAAuB,EAAE,KAAK,SAAS,YAAY,YAAY,IAAI;AAAA,IACzF,OAAO;AACL,cAAQ,SAAS,OAAO,QAAQ,cAAc;AAC9C,YAAM,KAAK,SAAS,+BAA+B,EAAE,KAAK,SAAS,UAAU,EAAE,KAAK,MAAM,MAAM,CAAC,EAAE,KAAK,MAAM,MAAM,CAAC,EAAE,KAAK,KAAKD,EAAC,EAAE,KAAK,KAAKC,EAAC,EAAE,KAAK,SAAS,UAAU,EAAE,KAAK,UAAU,WAAW;AACrM,YAAM,WAAW,cAAc,cAAc,WAAW;AACxD,UAAI,UAAU;AACZ,cAAM,OAAO,SAAS,OAAO,MAAM;AACnC,cAAM,QAAQD,KAAI;AAClB,cAAM,KAAKC,KAAI,KAAK,OAAO,MAAM,KAAK,CAAC;AACvC,cAAMa,MAAKb,KAAI,cAAc,KAAK,OAAO,MAAM,KAAK,CAAC;AACrD,aAAK,KAAK,MAAM,KAAK,EAAE,KAAK,MAAM,EAAE,EAAE,KAAK,MAAM,KAAK,EAAE,KAAK,MAAMa,GAAE,EAAE,KAAK,gBAAgB,GAAG,EAAE,KAAK,UAAU,kBAAkB,QAAQ,CAAC;AAAA,MAC7I;AAAA,IACF;AACA,qBAAiB,YAAY,KAAK;AAClC,eAAW,SAAS;AACpB,eAAW,YAAY,SAAU,OAAO;AACtC,aAAO,kBAAkB,KAAK,YAAY,KAAK;AAAA,IACjD;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,YAAY,YAAY;AAG/B,IAAI,aAAa,CAAC;AAAA,EAChB,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,QAAQ,WAAW,WAAW;AAAA,EACxC,iBAAiB,CAAC,YAAY;AAAA,EAC9B,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,OAAO;AAAA,EACjB,iBAAiB,CAAC,aAAa;AAAA,EAC/B,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,YAAY,MAAM;AAAA,EAC5B,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,cAAc,WAAW,oBAAoB,YAAY;AAAA,EACnE,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,MAAM,YAAY,UAAU;AAAA,EACtC,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,MAAM;AAAA,EAChB,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,YAAY,WAAW,UAAU;AAAA,EAC3C,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,WAAW,SAAS;AAAA,EAC9B,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,cAAc,QAAQ;AAAA,EAChC,iBAAiB,CAAC,YAAY;AAAA,EAC9B,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,aAAa,QAAQ;AAAA,EAC/B,iBAAiB,CAAC,WAAW;AAAA,EAC7B,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,YAAY,oBAAoB,WAAW;AAAA,EACrD,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,UAAU,iBAAiB,eAAe;AAAA,EACpD,iBAAiB,CAAC,eAAe;AAAA,EACjC,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,eAAe;AAAA,EACzB,iBAAiB,CAAC,cAAc;AAAA,EAChC,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,QAAQ,mBAAmB;AAAA,EACrC,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,mBAAmB,iBAAiB,YAAY,gBAAgB;AAAA,EAC1E,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,SAAS,cAAc;AAAA,EACjC,iBAAiB,CAAC,YAAY;AAAA,EAC9B,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,QAAQ,eAAe;AAAA,EACjC,iBAAiB,CAAC,UAAU;AAAA,EAC5B,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,MAAM;AAAA,EAChB,iBAAiB,CAAC,UAAU;AAAA,EAC5B,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,aAAa,SAAS;AAAA,EAChC,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,WAAW,SAAS;AAAA,EAC9B,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,YAAY,gBAAgB;AAAA,EACtC,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,OAAO,UAAU;AAAA,EAC3B,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,wBAAwB;AAAA,EAClC,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,OAAO,qBAAqB;AAAA,EACtC,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,QAAQ,gBAAgB;AAAA,EAClC,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,oBAAoB,SAAS;AAAA,EACvC,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,YAAY,qBAAqB,iBAAiB;AAAA,EAC5D,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,WAAW,UAAU;AAAA,EAC/B,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,oBAAoB,aAAa;AAAA,EAC3C,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,YAAY,eAAe;AAAA,EACrC,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,cAAc,kBAAkB;AAAA,EAC1C,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,eAAe,kBAAkB;AAAA,EAC3C,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,gBAAgB,kBAAkB;AAAA,EAC5C,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,aAAa,UAAU,kBAAkB;AAAA,EACnD,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,SAAS,aAAa,mBAAmB;AAAA,EACnD,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,eAAe,mBAAmB;AAAA,EAC5C,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,WAAW,gBAAgB;AAAA,EACrC,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,WAAW,iBAAiB;AAAA,EACtC,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,oBAAoB,YAAY,gBAAgB;AAAA,EAC1D,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,YAAY;AAAA,EACtB,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,iBAAiB,CAAC,qBAAqB;AAAA,EACvC,SAAS;AACX,GAAG;AAAA,EACD,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS,CAAC,gBAAgB;AAAA,EAC1B,SAAS;AACX,CAAC;AACD,IAAI,mBAAkC,OAAO,MAAM;AACjD,QAAM,qBAAqB;AAAA;AAAA,IAEzB;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA,EACF;AACA,QAAM,UAAU,CAAC,GAAG,OAAO,QAAQ,kBAAkB,GAAG,GAAG,WAAW,QAAQ,WAAS;AACrF,UAAM,UAAU,CAAC,MAAM,WAAW,GAAI,aAAa,QAAQ,MAAM,UAAU,CAAC,GAAI,GAAI,qBAAqB,QAAQ,MAAM,kBAAkB,CAAC,CAAE;AAC5I,WAAO,QAAQ,IAAI,WAAS,CAAC,OAAO,MAAM,OAAO,CAAC;AAAA,EACpD,CAAC,CAAC;AACF,SAAO,OAAO,YAAY,OAAO;AACnC,GAAG,kBAAkB;AACrB,IAAI,UAAU,iBAAiB;AAC/B,SAAS,aAAa,OAAO;AAC3B,SAAO,SAAS;AAClB;AACA,OAAO,cAAc,cAAc;AAGnC,IAAI,YAA2B,oBAAI,IAAI;AACvC,SAAe,WAAW,MAAM,MAAM,eAAe;AAAA;AACnD,QAAI;AACJ,QAAI;AACJ,QAAI,KAAK,UAAU,QAAQ;AACzB,UAAI,KAAK,MAAM,KAAK,IAAI;AACtB,aAAK,QAAQ;AAAA,MACf,OAAO;AACL,aAAK,QAAQ;AAAA,MACf;AAAA,IACF;AACA,UAAM,eAAe,KAAK,QAAQ,QAAQ,KAAK,KAAK,IAAI;AACxD,QAAI,CAAC,cAAc;AACjB,YAAM,IAAI,MAAM,kBAAkB,KAAK,KAAK,6BAA6B;AAAA,IAC3E;AACA,QAAI,KAAK,MAAM;AACb,UAAI;AACJ,UAAI,cAAc,OAAO,kBAAkB,WAAW;AACpD,iBAAS;AAAA,MACX,WAAW,KAAK,YAAY;AAC1B,iBAAS,KAAK,cAAc;AAAA,MAC9B;AACA,cAAQ,KAAK,OAAO,OAAO,EAAE,KAAK,cAAc,KAAK,IAAI,EAAE,KAAK,UAAU,UAAU,IAAI;AACxF,WAAK,MAAM,aAAa,OAAO,MAAM,aAAa;AAAA,IACpD,OAAO;AACL,WAAK,MAAM,aAAa,MAAM,MAAM,aAAa;AACjD,cAAQ;AAAA,IACV;AACA,QAAI,KAAK,SAAS;AAChB,SAAG,KAAK,SAAS,KAAK,OAAO;AAAA,IAC/B;AACA,cAAU,IAAI,KAAK,IAAI,KAAK;AAC5B,QAAI,KAAK,cAAc;AACrB,YAAM,KAAK,SAAS,MAAM,KAAK,OAAO,IAAI,YAAY;AAAA,IACxD;AACA,WAAO;AAAA,EACT;AAAA;AACA,OAAO,YAAY,YAAY;AAC/B,IAAI,cAA6B,OAAO,CAAC,MAAM,SAAS;AACtD,YAAU,IAAI,KAAK,IAAI,IAAI;AAC7B,GAAG,aAAa;AAChB,IAAI,SAAwB,OAAO,MAAM;AACvC,YAAU,MAAM;AAClB,GAAG,OAAO;AACV,IAAI,eAA8B,OAAO,UAAQ;AAC/C,QAAM,KAAK,UAAU,IAAI,KAAK,EAAE;AAChC,MAAI,MAAM,qBAAqB,KAAK,MAAM,MAAM,gBAAgB,KAAK,IAAI,KAAK,QAAQ,IAAI,KAAK,OAAO,KAAK,QAAQ,IAAI,GAAG;AAC1H,QAAM,UAAU;AAChB,QAAM,OAAO,KAAK,QAAQ;AAC1B,MAAI,KAAK,aAAa;AACpB,OAAG,KAAK,aAAa,gBAAgB,KAAK,IAAI,OAAO,KAAK,QAAQ,KAAK,QAAQ,KAAK,IAAI,KAAK,SAAS,IAAI,WAAW,GAAG;AAAA,EAC1H,OAAO;AACL,OAAG,KAAK,aAAa,eAAe,KAAK,IAAI,OAAO,KAAK,IAAI,GAAG;AAAA,EAClE;AACA,SAAO;AACT,GAAG,cAAc;", "names": ["t", "e", "s", "n", "o", "a", "h", "r", "i", "c", "l", "u", "p", "f", "d", "g", "M", "k", "b", "y", "m", "P", "v", "S", "O", "L", "T", "D", "A", "p", "i", "t", "x", "y", "w", "h", "s", "q", "d", "r", "m", "f", "g", "_", "_2", "i2", "y2"]}