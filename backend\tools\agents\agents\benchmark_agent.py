"""
Benchmark Agent - Agente especializado em análise competitiva

Agente do time Agno focado em benchmarking e análise competitiva:
- Análise comparativa com concorrentes
- Identificação de gaps competitivos
- Estratégias de diferenciação
- Benchmark de performance de mercado
"""

import logging
from typing import Dict, Any, Optional, List
from agno.agent import Agent
from ...shared.model_config import model_config
from agno.tools.reasoning import ReasoningTools
from typing import Optional

from ..tools import BenchmarkAnalysisTools

logger = logging.getLogger(__name__)


class BenchmarkAgent:
    """
    Agente especializado em análise competitiva usando Agno

    Analisa dados competitivos e gera insights estratégicos
    sobre posicionamento, gaps e oportunidades de diferenciação.
    """

    def __init__(self, model_name: Optional[str] = None):
        """
        Inicializa o Benchmark Agent usando modelos do settings.py

        Args:
            model_name: Modelo específico (opcional, usa automático se None)
        """
        self.benchmark_tools = BenchmarkAnalysisTools()
        
        # Obter configuração de modelo baseada no settings.py
        agno_config = model_config.create_agno_config("analysis")
        
        if model_name:
            # Validar se modelo está disponível no settings
            available_models = [
                model_config.get_openai_model("analysis"),
                model_config.get_cohere_model("analysis"), 
                model_config.get_mistral_model("analysis")
            ]
            if model_name in available_models:
                self.model_name = model_name
            else:
                logger.warning(f"Modelo {model_name} não disponível no settings.py, usando automático")
                self.model_name = agno_config["model_id"]
        else:
            self.model_name = agno_config["model_id"]

        # Criar modelo baseado no provider
        if agno_config["provider"] == "openai":
            from agno.models.openai import OpenAI
            model_instance = OpenAI(id=self.model_name, api_key=agno_config["api_key"])
        elif agno_config["provider"] == "cohere":
            from agno.models.cohere import Cohere
            model_instance = Cohere(id=self.model_name, api_key=agno_config["api_key"])
        elif agno_config["provider"] == "mistral":
            from agno.models.mistral import Mistral
            model_instance = Mistral(id=self.model_name, api_key=agno_config["api_key"])
        else:
            from agno.models.openai import OpenAI
            model_instance = OpenAI(id="gpt-4o-mini", api_key=agno_config["api_key"])
            logger.warning(f"Provider {agno_config['provider']} não suportado, usando OpenAI fallback")

        # Criar agente Agno especializado
        self.agent = Agent(
            name="Competitive Benchmark Analyst",
            role="Expert in competitive analysis, market benchmarking, and strategic differentiation",
            model=model_instance,
            tools=[ReasoningTools()],
            instructions=[
                "You are a competitive benchmark analyst specializing in market analysis and strategic positioning",
                "Analyze competitive data to identify market gaps, threats, and opportunities",
                "Provide specific, actionable recommendations for competitive advantage",
                "Focus on data-driven insights with quantitative analysis when possible",
                "Consider both short-term tactical moves and long-term strategic positioning",
                "Always provide prioritized recommendations with clear rationale",
                "Use structured analysis and reasoning for complex competitive scenarios",
                "Identify white spaces in the market and differentiation opportunities"
            ],
            description="Specialist in competitive analysis, market benchmarking, gap identification, and strategic differentiation",
            markdown=True,
            show_tool_calls=True
        )

        logger.info(f"Benchmark Agent inicializado com {agno_config['provider']}:{self.model_name}")

    def analyze_competitive_landscape(self,
                                      company_data: Dict[str, Any],
                                      competitors_data: List[Dict[str, Any]],
                                      market_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Analisa o cenário competitivo completo

        Args:
            company_data: Dados da empresa sendo analisada
            competitors_data: Lista de dados dos concorrentes
            market_context: Contexto de mercado (opcional)

        Returns:
            Análise completa do cenário competitivo
        """
        try:
            logger.info(
                "Iniciando análise do cenário competitivo com Benchmark Agent")

            # Processar dados com as ferramentas de benchmark
            competitive_scores = self.benchmark_tools.calculate_competitive_scores(
                company_data, competitors_data)

            # Analisar cada concorrente individualmente
            competitor_insights = []
            for competitor_data in competitors_data:
                insights = self.benchmark_tools.analyze_competitor_data(
                    company_data, competitor_data, market_context)
                competitor_insights.append(insights.dict())

            # Preparar contexto para análise do agente
            company_name = company_data.get("name", "a empresa")
            sector = company_data.get("sector", "tecnologia")
            num_competitors = len(competitors_data)

            # Criar prompt estruturado para análise
            analysis_prompt = f"""
Analise o cenário competitivo completo para {company_name} (setor: {sector}):

**POSICIONAMENTO ATUAL:**
- Score Competitivo: {competitive_scores.get('company_score', 0)}/100
- Ranking no Mercado: {competitive_scores.get('company_rank', 'N/A')} de {competitive_scores.get('total_analyzed', 'N/A')}
- Performance Percentile: {competitive_scores.get('performance_percentile', 0):.1f}%

**ANÁLISE DOS CONCORRENTES ({num_competitors} analisados):**
{self._format_competitor_insights(competitor_insights)}

**SCORES COMPETITIVOS:**
{self._format_competitive_scores(competitive_scores)}

**CONTEXTO DE MERCADO:**
{self._format_market_context(market_context)}

Forneça uma análise estratégica abrangente incluindo:

1. **Posicionamento Competitivo** - Onde a empresa se posiciona no mercado
2. **Principais Ameaças** - Concorrentes mais perigosos e por quê
3. **Oportunidades Identificadas** - Gaps no mercado e vantagens a explorar
4. **Recomendações Estratégicas** - 3-5 ações priorizadas para melhorar posição competitiva
5. **Análise de Risco** - Principais riscos competitivos e como mitigar

Use dados quantitativos sempre que possível e priorize recomendações por impacto vs esforço.
"""

            # Executar análise com o agente Agno
            analysis_response = self.agent.run(analysis_prompt)

            # Estruturar resultado
            result = {
                "agent_type": "BenchmarkAgent",
                "analysis_type": "competitive_landscape",
                "company_data": {
                    "name": company_name,
                    "sector": sector,
                    "competitive_score": competitive_scores.get('company_score', 0),
                    "market_rank": competitive_scores.get('company_rank', None),
                    "performance_percentile": competitive_scores.get('performance_percentile', 0)
                },
                "competitive_scores": competitive_scores,
                "competitor_insights": competitor_insights,
                "agent_analysis": analysis_response.content,
                "analysis_timestamp": "2024-01-01T00:00:00Z"  # Placeholder
            }

            logger.info(
                f"Análise competitiva concluída - Score: {competitive_scores.get('company_score', 0)}")
            return result

        except Exception as e:
            logger.error(f"Erro na análise competitiva: {str(e)}")
            raise

    def identify_gaps(self,
                      company_data: Dict[str, Any],
                      competitors_data: List[Dict[str, Any]],
                      focus_areas: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Identifica gaps competitivos específicos

        Args:
            company_data: Dados da empresa
            competitors_data: Lista de dados dos concorrentes
            focus_areas: Áreas específicas para análise (opcional)

        Returns:
            Análise detalhada de gaps competitivos
        """
        try:
            logger.info("Identificando gaps competitivos com Benchmark Agent")

            # Identificar gaps usando as ferramentas
            competitive_gaps = self.benchmark_tools.identify_competitive_gaps(
                company_data, competitors_data, focus_areas)

            # Preparar dados para análise do agente
            company_name = company_data.get("name", "a empresa")
            gaps_summary = self._summarize_gaps(competitive_gaps)

            gaps_prompt = f"""
Analise os gaps competitivos identificados para {company_name}:

**GAPS IDENTIFICADOS ({len(competitive_gaps)} no total):**
{self._format_gaps_analysis(competitive_gaps)}

**RESUMO POR CATEGORIA:**
{gaps_summary}

**ÁREAS DE FOCO SOLICITADAS:**
{', '.join(focus_areas) if focus_areas else 'Análise geral de todas as áreas'}

Forneça análise detalhada incluindo:

1. **Gaps Críticos** - Top 3 gaps que mais impactam competitividade
2. **Análise de Impacto** - Como cada gap afeta posição no mercado
3. **Roadmap de Resolução** - Ordem recomendada para resolver gaps
4. **Análise de Recursos** - Estimativa de recursos necessários
5. **Quick Wins** - Gaps que podem ser resolvidos rapidamente
6. **Investimentos Estratégicos** - Gaps que requerem investimento maior mas alto retorno

Priorize por impacto no negócio e viabilidade de implementação.
"""

            # Executar análise com o agente
            gaps_response = self.agent.run(gaps_prompt)

            result = {
                "agent_type": "BenchmarkAgent",
                "analysis_type": "gap_analysis",
                "total_gaps": len(competitive_gaps),
                "gaps_by_category": gaps_summary,
                "detailed_gaps": [gap.dict() for gap in competitive_gaps],
                "focus_areas": focus_areas or ["Análise geral"],
                "agent_analysis": gaps_response.content
            }

            logger.info(
                f"Análise de gaps concluída - {len(competitive_gaps)} gaps identificados")
            return result

        except Exception as e:
            logger.error(f"Erro na identificação de gaps: {str(e)}")
            raise

    def generate_differentiation_strategy(self,
                                          company_data: Dict[str, Any],
                                          competitors_data: List[Dict[str, Any]],
                                          market_trends: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Gera estratégias de diferenciação competitiva

        Args:
            company_data: Dados da empresa
            competitors_data: Lista de dados dos concorrentes
            market_trends: Tendências de mercado (opcional)

        Returns:
            Estratégias de diferenciação recomendadas
        """
        try:
            logger.info(
                "Gerando estratégias de diferenciação com Benchmark Agent")

            # Gerar oportunidades usando as ferramentas
            differentiation_strategies = self.benchmark_tools.generate_differentiation_opportunities(
                company_data, competitors_data, market_trends)

            # Analisar forças únicas da empresa
            unique_strengths = self.benchmark_tools._identify_unique_strengths(
                company_data, competitors_data)
            white_spaces = self.benchmark_tools._identify_market_white_spaces(
                company_data, competitors_data)

            company_name = company_data.get("name", "a empresa")

            strategy_prompt = f"""
Desenvolva estratégias de diferenciação competitiva para {company_name}:

**ESTRATÉGIAS IDENTIFICADAS ({len(differentiation_strategies)} geradas):**
{self._format_differentiation_strategies(differentiation_strategies)}

**FORÇAS ÚNICAS DA EMPRESA:**
{chr(10).join(f"- {strength}" for strength in unique_strengths)}

**WHITE SPACES NO MERCADO:**
{chr(10).join(f"- {space}" for space in white_spaces)}

**TENDÊNCIAS DE MERCADO:**
{chr(10).join(f"- {trend}" for trend in market_trends) if market_trends else "Não fornecidas"}

Desenvolva um plano estratégico de diferenciação incluindo:

1. **Estratégia Principal** - Foco primário de diferenciação
2. **Vantagens Competitivas** - Como criar vantagens defensáveis
3. **Execução Táctica** - Passos específicos para implementação
4. **Métricas de Sucesso** - KPIs para medir progresso
5. **Timeline Estratégico** - Fases de implementação (curto, médio, longo prazo)
6. **Análise de Risco** - Possíveis obstáculos e como contornar
7. **Investimento Necessário** - Recursos requeridos para execução

Priorize estratégias com maior potencial de ROI e menor risco competitivo.
"""

            # Executar análise com o agente
            strategy_response = self.agent.run(strategy_prompt)

            result = {
                "agent_type": "BenchmarkAgent",
                "analysis_type": "differentiation_strategy",
                "total_strategies": len(differentiation_strategies),
                "detailed_strategies": [strategy.dict() for strategy in differentiation_strategies],
                "unique_strengths": unique_strengths,
                "market_white_spaces": white_spaces,
                "market_trends": market_trends or [],
                "agent_analysis": strategy_response.content
            }

            logger.info(
                f"Estratégias de diferenciação geradas - {len(differentiation_strategies)} estratégias")
            return result

        except Exception as e:
            logger.error(f"Erro na geração de estratégias: {str(e)}")
            raise

    def benchmark_performance(self,
                              company_data: Dict[str, Any],
                              competitors_data: List[Dict[str, Any]],
                              metrics_focus: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Realiza benchmark de performance vs concorrentes

        Args:
            company_data: Dados da empresa
            competitors_data: Lista de dados dos concorrentes
            metrics_focus: Métricas específicas para focar (opcional)

        Returns:
            Análise de benchmark de performance
        """
        try:
            logger.info(
                "Executando benchmark de performance com Benchmark Agent")

            # Calcular scores competitivos
            competitive_scores = self.benchmark_tools.calculate_competitive_scores(
                company_data, competitors_data)

            # Analisar cada concorrente para performance
            performance_analysis = []
            for competitor_data in competitors_data:
                insights = self.benchmark_tools.analyze_competitor_data(
                    company_data, competitor_data)
                performance_analysis.append({
                    "name": insights.competitor_name,
                    "overall_score": insights.overall_score,
                    "feature_score": insights.feature_score,
                    "performance_score": insights.performance_score,
                    "market_position": insights.market_position,
                    "growth_trend": insights.growth_trend
                })

            company_name = company_data.get("name", "a empresa")

            benchmark_prompt = f"""
Analise o benchmark de performance para {company_name}:

**PERFORMANCE ATUAL:**
- Score Geral: {competitive_scores.get('company_score', 0)}/100
- Posição no Ranking: {competitive_scores.get('company_rank', 'N/A')} de {competitive_scores.get('total_analyzed', 'N/A')}
- Gap para Líder: {competitive_scores.get('score_gap_to_leader', 0):.1f} pontos
- Percentile de Performance: {competitive_scores.get('performance_percentile', 0):.1f}%

**ANÁLISE COMPARATIVA DE PERFORMANCE:**
{self._format_performance_analysis(performance_analysis)}

**MÉTRICAS DE FOCO:**
{', '.join(metrics_focus) if metrics_focus else 'Análise geral de performance'}

Forneça análise de benchmark incluindo:

1. **Posicionamento Atual** - Como a empresa se compara aos concorrentes
2. **Líderes de Mercado** - Quem está performando melhor e por quê
3. **Gaps de Performance** - Onde a empresa está perdendo para concorrentes
4. **Oportunidades de Melhoria** - Áreas com maior potencial de ganho
5. **Metas de Performance** - Benchmarks realistas para alcançar
6. **Roadmap de Melhoria** - Passos para melhorar positioning competitivo

Foque em insights acionáveis com impacto mensurável na competitividade.
"""

            # Executar análise com o agente
            benchmark_response = self.agent.run(benchmark_prompt)

            result = {
                "agent_type": "BenchmarkAgent",
                "analysis_type": "performance_benchmark",
                "competitive_scores": competitive_scores,
                "performance_analysis": performance_analysis,
                "metrics_focus": metrics_focus or ["Análise geral"],
                "agent_analysis": benchmark_response.content,
                "benchmark_summary": {
                    "company_score": competitive_scores.get('company_score', 0),
                    "market_rank": competitive_scores.get('company_rank', None),
                    "performance_percentile": competitive_scores.get('performance_percentile', 0),
                    "gap_to_leader": competitive_scores.get('score_gap_to_leader', 0)
                }
            }

            logger.info(
                f"Benchmark de performance concluído - Ranking: {competitive_scores.get('company_rank', 'N/A')}")
            return result

        except Exception as e:
            logger.error(f"Erro no benchmark de performance: {str(e)}")
            raise

    # Métodos de apoio para formatação

    def _format_competitor_insights(self, competitor_insights: List[Dict[str, Any]]) -> str:
        """Formata insights dos concorrentes para o prompt"""
        formatted = []
        for insight in competitor_insights:
            formatted.append(f"""
**{insight.get('competitor_name', 'Unnamed')}:**
- Posição: {insight.get('market_position', 'N/A')}
- Score Geral: {insight.get('overall_score', 0)}/100
- Forças: {', '.join(insight.get('strength_areas', []))}
- Fraquezas: {', '.join(insight.get('weakness_areas', []))}
- Tendência: {insight.get('growth_trend', 'N/A')}
""")
        return '\n'.join(formatted)

    def _format_competitive_scores(self, competitive_scores: Dict[str, Any]) -> str:
        """Formata scores competitivos para o prompt"""
        competitor_scores = competitive_scores.get('competitor_scores', [])
        formatted = []
        for i, comp in enumerate(competitor_scores[:5], 1):  # Top 5
            formatted.append(
                f"{i}. {comp.get('name', 'Unnamed')}: {comp.get('score', 0)}/100")
        return '\n'.join(formatted)

    def _format_market_context(self, market_context: Optional[Dict[str, Any]]) -> str:
        """Formata contexto de mercado para o prompt"""
        if not market_context:
            return "Contexto de mercado não fornecido"

        context_items = []
        if 'market_size' in market_context:
            context_items.append(
                f"Tamanho do mercado: {market_context['market_size']}")
        if 'growth_rate' in market_context:
            context_items.append(
                f"Taxa de crescimento: {market_context['growth_rate']}%")
        if 'trends' in market_context:
            context_items.append(
                f"Tendências: {', '.join(market_context['trends'])}")

        return '\n'.join(context_items) if context_items else "Dados limitados de contexto"

    def _summarize_gaps(self, competitive_gaps) -> Dict[str, int]:
        """Sumariza gaps por categoria"""
        summary = {}
        for gap in competitive_gaps:
            gap_type = gap.gap_type
            summary[gap_type] = summary.get(gap_type, 0) + 1
        return summary

    def _format_gaps_analysis(self, competitive_gaps) -> str:
        """Formata análise de gaps para o prompt"""
        formatted = []
        for gap in competitive_gaps[:10]:  # Top 10 gaps
            formatted.append(f"""
**{gap.gap_type} - {gap.impact_level} Impacto / {gap.urgency}:**
- {gap.description}
- Ação recomendada: {gap.recommended_action}
- Timeline: {gap.timeline_estimate}
""")
        return '\n'.join(formatted)

    def _format_differentiation_strategies(self, strategies) -> str:
        """Formata estratégias de diferenciação para o prompt"""
        formatted = []
        for strategy in strategies:
            formatted.append(f"""
**{strategy.strategy_name}:**
- Foco: {strategy.focus_area}
- Vantagem: {strategy.competitive_advantage}
- ROI Esperado: {strategy.expected_roi}%
- Risco: {strategy.risk_level}
- Complexidade: {strategy.implementation_complexity}
""")
        return '\n'.join(formatted)

    def _format_performance_analysis(self, performance_analysis: List[Dict[str, Any]]) -> str:
        """Formata análise de performance para o prompt"""
        formatted = []
        # Ordenar por score geral
        sorted_analysis = sorted(performance_analysis, key=lambda x: x.get(
            'overall_score', 0), reverse=True)

        for analysis in sorted_analysis:
            formatted.append(f"""
**{analysis.get('name', 'Unnamed')}:**
- Score Geral: {analysis.get('overall_score', 0)}/100
- Score de Features: {analysis.get('feature_score', 0)}/100
- Score de Performance: {analysis.get('performance_score', 0)}/100
- Posição: {analysis.get('market_position', 'N/A')}
- Tendência: {analysis.get('growth_trend', 'N/A')}
""")
        return '\n'.join(formatted)
