"""
Technical Writer Tools para Agno

Tools customizadas para consolidação de insights e redação técnica:
- Consolidação de análises de múltiplos agentes
- Priorização de recomendações por impacto e esforço
- Identificação de temas e padrões comuns
- Estruturação de relatórios finais estratégicos
"""

import logging
from typing import Dict, Any, List, Optional, Union
from pydantic import BaseModel, Field
from datetime import datetime

logger = logging.getLogger(__name__)


class ConsolidatedInsight(BaseModel):
    """Schema para insights consolidados de múltiplos agentes"""
    insight_id: str = Field(..., description="ID único do insight")
    source_agents: List[str] = Field(
        ..., description="Agentes que contribuíram para este insight")
    category: str = Field(...,
                          description="Categoria do insight (técnico, UX, negócio, etc.)")
    title: str = Field(..., description="Título resumido do insight")
    description: str = Field(..., description="Descrição detalhada do insight")

    # Análise de relevância
    impact_score: float = Field(
        default=0.0, description="Score de impacto (0-100)")
    confidence_score: float = Field(
        default=0.0, description="Score de confiança (0-100)")
    evidence_strength: str = Field(
        default="Médio", description="Força da evidência")

    # Contexto estratégico
    business_value: str = Field(
        default="", description="Valor de negócio identificado")
    user_impact: str = Field(default="", description="Impacto no usuário")
    technical_implications: str = Field(
        default="", description="Implicações técnicas")

    # Convergência entre agentes
    agent_consensus: float = Field(
        default=0.0, description="Nível de consenso entre agentes (0-100)")
    conflicting_views: List[str] = Field(
        default_factory=list, description="Visões conflitantes entre agentes")


class PrioritizedRecommendation(BaseModel):
    """Schema para recomendações priorizadas"""
    recommendation_id: str = Field(..., description="ID único da recomendação")
    title: str = Field(..., description="Título da recomendação")
    description: str = Field(..., description="Descrição detalhada")
    source_insights: List[str] = Field(
        ..., description="IDs dos insights que geraram esta recomendação")

    # Matriz de priorização
    impact_score: float = Field(...,
                                description="Score de impacto no negócio (0-100)")
    effort_score: float = Field(...,
                                description="Score de esforço necessário (0-100)")
    priority_score: float = Field(
        default=0.0, description="Score de prioridade calculado")

    # Classificação
    urgency: str = Field(...,
                         description="Urgência (Crítico, Importante, Desejável)")
    complexity: str = Field(...,
                            description="Complexidade (Baixa, Média, Alta)")
    risk_level: str = Field(...,
                            description="Nível de risco (Baixo, Médio, Alto)")

    # Implementação
    estimated_timeline: str = Field(
        default="", description="Timeline estimado")
    required_resources: List[str] = Field(
        default_factory=list, description="Recursos necessários")
    success_metrics: List[str] = Field(
        default_factory=list, description="Métricas de sucesso")

    # Dependencies
    dependencies: List[str] = Field(
        default_factory=list, description="Dependências de outras recomendações")
    blocking_factors: List[str] = Field(
        default_factory=list, description="Fatores que podem bloquear implementação")


class CommonTheme(BaseModel):
    """Schema para temas comuns identificados entre análises"""
    theme_id: str = Field(..., description="ID único do tema")
    theme_name: str = Field(..., description="Nome do tema")
    description: str = Field(..., description="Descrição do tema")

    # Ocorrência entre agentes
    mentioned_by_agents: List[str] = Field(...,
                                           description="Agentes que mencionaram este tema")
    frequency_score: float = Field(
        default=0.0, description="Frequência de menção (0-100)")
    consistency_score: float = Field(
        default=0.0, description="Consistência entre agentes (0-100)")

    # Insights relacionados
    related_insights: List[str] = Field(
        default_factory=list, description="IDs de insights relacionados")
    key_recommendations: List[str] = Field(
        default_factory=list, description="Recomendações principais deste tema")

    # Análise estratégica
    strategic_importance: str = Field(
        default="Médio", description="Importância estratégica")
    business_area: str = Field(
        default="", description="Área de negócio impactada")
    user_experience_impact: str = Field(
        default="", description="Impacto na experiência do usuário")


class FinalReportSection(BaseModel):
    """Schema para seções do relatório final"""
    section_id: str = Field(..., description="ID da seção")
    title: str = Field(..., description="Título da seção")
    content: str = Field(..., description="Conteúdo da seção")
    insights_included: List[str] = Field(
        default_factory=list, description="IDs dos insights incluídos")
    recommendations_included: List[str] = Field(
        default_factory=list, description="IDs das recomendações incluídas")


class FinalReport(BaseModel):
    """Schema para o relatório final consolidado"""
    report_id: str = Field(..., description="ID único do relatório")
    company_name: str = Field(..., description="Nome da empresa analisada")
    analysis_date: str = Field(..., description="Data da análise")

    # Resumo executivo
    executive_summary: str = Field(..., description="Resumo executivo")
    key_findings: List[str] = Field(..., description="Principais descobertas")
    top_recommendations: List[str] = Field(...,
                                           description="Top 5 recomendações")

    # Seções do relatório
    sections: List[FinalReportSection] = Field(
        ..., description="Seções detalhadas do relatório")

    # Metadados
    total_insights: int = Field(
        default=0, description="Total de insights consolidados")
    total_recommendations: int = Field(
        default=0, description="Total de recomendações")
    agents_analyzed: List[str] = Field(
        default_factory=list, description="Agentes que contribuíram")

    # Priorização estratégica
    immediate_actions: List[str] = Field(
        default_factory=list, description="Ações imediatas (0-30 dias)")
    short_term_goals: List[str] = Field(
        default_factory=list, description="Objetivos curto prazo (1-3 meses)")
    long_term_vision: List[str] = Field(
        default_factory=list, description="Visão longo prazo (6+ meses)")


class TechnicalWriterTools:
    """
    Tools para consolidação de insights e redação técnica

    Processa análises de múltiplos agentes especializados,
    identifica padrões comuns e gera relatórios finais estratégicos.
    """

    def __init__(self):
        """Inicializa as ferramentas de redação técnica"""
        self.priority_weights = {
            "impact": 0.4,
            "effort": -0.3,  # Negativo porque menor esforço = maior prioridade
            "urgency": 0.2,
            "consensus": 0.1
        }

        self.theme_categories = [
            "Arquitetura Técnica",
            "Experiência do Usuário",
            "Estratégia de Produto",
            "SEO e Performance",
            "Competitividade",
            "Inovação",
            "Segurança",
            "Escalabilidade"
        ]

        logger.info("TechnicalWriterTools inicializado")

    def consolidate_agent_insights(self,
                                   agent_analyses: Dict[str, Dict[str, Any]],
                                   company_context: Optional[Dict[str, Any]] = None) -> List[ConsolidatedInsight]:
        """
        Consolida insights de múltiplos agentes especializados

        Args:
            agent_analyses: Dicionário com análises de cada agente
            company_context: Contexto da empresa (opcional)

        Returns:
            Lista de insights consolidados
        """
        try:
            logger.info(
                f"Consolidando insights de {len(agent_analyses)} agentes")

            consolidated_insights = []
            insight_counter = 1

            # Extrair insights de cada agente
            for agent_name, analysis in agent_analyses.items():
                agent_insights = self._extract_insights_from_agent(
                    agent_name, analysis)

                for insight_data in agent_insights:
                    # Verificar se já existe insight similar
                    existing_insight = self._find_similar_insight(
                        insight_data, consolidated_insights)

                    if existing_insight:
                        # Merge com insight existente
                        self._merge_insights(
                            existing_insight, insight_data, agent_name)
                    else:
                        # Criar novo insight consolidado
                        insight = ConsolidatedInsight(
                            insight_id=f"insight_{insight_counter:03d}",
                            source_agents=[agent_name],
                            category=insight_data.get("category", "Geral"),
                            title=insight_data.get("title", ""),
                            description=insight_data.get("description", ""),
                            impact_score=insight_data.get(
                                "impact_score", 50.0),
                            confidence_score=insight_data.get(
                                "confidence_score", 70.0),
                            evidence_strength=insight_data.get(
                                "evidence_strength", "Médio"),
                            business_value=insight_data.get(
                                "business_value", ""),
                            user_impact=insight_data.get("user_impact", ""),
                            technical_implications=insight_data.get(
                                "technical_implications", ""),
                            agent_consensus=100.0  # Único agente inicialmente
                        )

                        consolidated_insights.append(insight)
                        insight_counter += 1

            # Calcular consenso final entre agentes
            self._calculate_final_consensus(consolidated_insights)

            # Ordenar por relevância
            consolidated_insights.sort(key=lambda x: (
                x.impact_score * x.agent_consensus / 100), reverse=True)

            logger.info(
                f"Consolidação concluída - {len(consolidated_insights)} insights únicos")
            return consolidated_insights

        except Exception as e:
            logger.error(f"Erro na consolidação de insights: {str(e)}")
            return []

    def prioritize_recommendations(self,
                                   consolidated_insights: List[ConsolidatedInsight],
                                   business_objectives: Optional[List[str]] = None) -> List[PrioritizedRecommendation]:
        """
        Prioriza recomendações baseadas em insights consolidados

        Args:
            consolidated_insights: Lista de insights consolidados
            business_objectives: Objetivos de negócio (opcional)

        Returns:
            Lista de recomendações priorizadas
        """
        try:
            logger.info(
                f"Priorizando recomendações baseadas em {len(consolidated_insights)} insights")

            recommendations = []
            rec_counter = 1

            # Gerar recomendações a partir dos insights
            for insight in consolidated_insights:
                insight_recommendations = self._generate_recommendations_from_insight(
                    insight)

                for rec_data in insight_recommendations:
                    recommendation = PrioritizedRecommendation(
                        recommendation_id=f"rec_{rec_counter:03d}",
                        title=rec_data.get("title", ""),
                        description=rec_data.get("description", ""),
                        source_insights=[insight.insight_id],
                        impact_score=rec_data.get(
                            "impact_score", insight.impact_score),
                        effort_score=rec_data.get("effort_score", 50.0),
                        urgency=rec_data.get("urgency", "Importante"),
                        complexity=rec_data.get("complexity", "Média"),
                        risk_level=rec_data.get("risk_level", "Médio"),
                        estimated_timeline=rec_data.get(
                            "timeline", "2-4 semanas"),
                        required_resources=rec_data.get("resources", []),
                        success_metrics=rec_data.get("metrics", [])
                    )

                    # Calcular score de prioridade
                    recommendation.priority_score = self._calculate_priority_score(
                        recommendation)

                    recommendations.append(recommendation)
                    rec_counter += 1

            # Identificar dependências entre recomendações
            self._identify_recommendation_dependencies(recommendations)

            # Ordenar por prioridade
            recommendations.sort(key=lambda x: x.priority_score, reverse=True)

            # Aplicar filtros baseados em objetivos de negócio
            if business_objectives:
                recommendations = self._filter_by_business_objectives(
                    recommendations, business_objectives)

            logger.info(
                f"Priorização concluída - {len(recommendations)} recomendações")
            return recommendations

        except Exception as e:
            logger.error(f"Erro na priorização de recomendações: {str(e)}")
            return []

    def identify_common_themes(self,
                               agent_analyses: Dict[str, Dict[str, Any]],
                               min_frequency: float = 2) -> List[CommonTheme]:
        """
        Identifica temas comuns entre análises de diferentes agentes

        Args:
            agent_analyses: Análises dos agentes
            min_frequency: Frequência mínima para considerar um tema

        Returns:
            Lista de temas comuns identificados
        """
        try:
            logger.info(
                f"Identificando temas comuns entre {len(agent_analyses)} agentes")

            # Extrair temas de cada agente
            all_themes = {}

            for agent_name, analysis in agent_analyses.items():
                agent_themes = self._extract_themes_from_agent(
                    agent_name, analysis)

                for theme_name, theme_data in agent_themes.items():
                    if theme_name not in all_themes:
                        all_themes[theme_name] = {
                            "agents": [],
                            "descriptions": [],
                            "insights": [],
                            "recommendations": []
                        }

                    all_themes[theme_name]["agents"].append(agent_name)
                    all_themes[theme_name]["descriptions"].append(
                        theme_data.get("description", ""))
                    all_themes[theme_name]["insights"].extend(
                        theme_data.get("insights", []))
                    all_themes[theme_name]["recommendations"].extend(
                        theme_data.get("recommendations", []))

            # Filtrar temas com frequência suficiente
            common_themes = []
            theme_counter = 1

            for theme_name, theme_data in all_themes.items():
                frequency = len(theme_data["agents"])

                if frequency >= min_frequency:
                    # Calcular scores de consistência
                    consistency_score = self._calculate_theme_consistency(
                        theme_data["descriptions"])
                    frequency_score = min(
                        (frequency / len(agent_analyses)) * 100, 100)

                    theme = CommonTheme(
                        theme_id=f"theme_{theme_counter:03d}",
                        theme_name=theme_name,
                        description=self._consolidate_theme_description(
                            theme_data["descriptions"]),
                        mentioned_by_agents=theme_data["agents"],
                        frequency_score=frequency_score,
                        consistency_score=consistency_score,
                        related_insights=list(set(theme_data["insights"])),
                        key_recommendations=list(
                            set(theme_data["recommendations"])),
                        strategic_importance=self._assess_strategic_importance(
                            theme_name, frequency, consistency_score),
                        business_area=self._categorize_business_area(
                            theme_name),
                        user_experience_impact=self._assess_ux_impact(
                            theme_name)
                    )

                    common_themes.append(theme)
                    theme_counter += 1

            # Ordenar por relevância estratégica
            common_themes.sort(key=lambda x: (
                x.frequency_score * x.consistency_score), reverse=True)

            logger.info(f"Identificados {len(common_themes)} temas comuns")
            return common_themes

        except Exception as e:
            logger.error(f"Erro na identificação de temas: {str(e)}")
            return []

    def generate_final_report_structure(self,
                                        consolidated_insights: List[ConsolidatedInsight],
                                        prioritized_recommendations: List[PrioritizedRecommendation],
                                        common_themes: List[CommonTheme],
                                        company_name: str = "Empresa") -> FinalReport:
        """
        Gera estrutura do relatório final consolidado

        Args:
            consolidated_insights: Insights consolidados
            prioritized_recommendations: Recomendações priorizadas
            common_themes: Temas comuns identificados
            company_name: Nome da empresa

        Returns:
            Estrutura do relatório final
        """
        try:
            logger.info("Gerando estrutura do relatório final")

            # Criar resumo executivo
            executive_summary = self._generate_executive_summary(
                consolidated_insights, prioritized_recommendations, common_themes
            )

            # Identificar principais descobertas
            key_findings = self._extract_key_findings(
                consolidated_insights, common_themes)

            # Top 5 recomendações
            top_recommendations = [
                rec.title for rec in prioritized_recommendations[:5]]

            # Criar seções do relatório
            sections = []

            # Seção 1: Análise Estratégica
            strategic_section = self._create_strategic_analysis_section(
                consolidated_insights, common_themes
            )
            sections.append(strategic_section)

            # Seção 2: Recomendações Priorizadas
            recommendations_section = self._create_recommendations_section(
                prioritized_recommendations
            )
            sections.append(recommendations_section)

            # Seção 3: Temas Críticos
            themes_section = self._create_themes_section(common_themes)
            sections.append(themes_section)

            # Seção 4: Plano de Ação
            action_plan_section = self._create_action_plan_section(
                prioritized_recommendations
            )
            sections.append(action_plan_section)

            # Categorizar ações por timeline
            immediate_actions, short_term_goals, long_term_vision = self._categorize_actions_by_timeline(
                prioritized_recommendations
            )

            # Criar relatório final
            final_report = FinalReport(
                report_id=f"report_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                company_name=company_name,
                analysis_date=datetime.now().strftime('%Y-%m-%d'),
                executive_summary=executive_summary,
                key_findings=key_findings,
                top_recommendations=top_recommendations,
                sections=sections,
                total_insights=len(consolidated_insights),
                total_recommendations=len(prioritized_recommendations),
                agents_analyzed=self._get_unique_agents(consolidated_insights),
                immediate_actions=immediate_actions,
                short_term_goals=short_term_goals,
                long_term_vision=long_term_vision
            )

            logger.info(
                f"Relatório final estruturado - {len(sections)} seções")
            return final_report

        except Exception as e:
            logger.error(f"Erro na geração do relatório: {str(e)}")
            raise

    # Métodos privados de apoio

    def _extract_insights_from_agent(self, agent_name: str, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extrai insights específicos da análise de um agente"""
        insights = []

        try:
            # Diferentes agentes podem ter estruturas diferentes
            if "agent_analysis" in analysis:
                content = analysis["agent_analysis"]
                # Parse do conteúdo da análise do agente para extrair insights
                insights.extend(
                    self._parse_agent_content_for_insights(agent_name, content))

            # Procurar em outras seções da análise
            for key, value in analysis.items():
                if key.endswith("_analysis") or key.endswith("_insights"):
                    if isinstance(value, str):
                        insights.extend(
                            self._parse_text_for_insights(agent_name, value))
                    elif isinstance(value, list):
                        for item in value:
                            if isinstance(item, dict):
                                insights.append(
                                    self._convert_dict_to_insight(agent_name, item))

        except Exception as e:
            logger.warning(
                f"Erro ao extrair insights do agente {agent_name}: {str(e)}")

        return insights

    def _parse_agent_content_for_insights(self, agent_name: str, content: str) -> List[Dict[str, Any]]:
        """Parse do conteúdo do agente para extrair insights estruturados"""
        insights = []

        # Categorizar por tipo de agente
        if "Architect" in agent_name or "architect" in agent_name.lower():
            category = "Arquitetura Técnica"
        elif "UX" in agent_name or "UI" in agent_name:
            category = "Experiência do Usuário"
        elif "Product" in agent_name or "product" in agent_name.lower():
            category = "Estratégia de Produto"
        elif "SEO" in agent_name or "seo" in agent_name.lower():
            category = "SEO e Performance"
        elif "Benchmark" in agent_name or "benchmark" in agent_name.lower():
            category = "Competitividade"
        else:
            category = "Geral"

        # Extrair insights baseados em palavras-chave e padrões
        lines = content.split('\n')
        current_insight = {}

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Identificar início de novo insight
            if any(keyword in line.lower() for keyword in ['recomend', 'sugest', 'deveria', 'implementar', 'melhorar']):
                if current_insight:
                    insights.append(current_insight)

                current_insight = {
                    "category": category,
                    "title": line[:100] + "..." if len(line) > 100 else line,
                    "description": line,
                    "impact_score": 60.0,  # Score padrão
                    "confidence_score": 75.0,
                    "evidence_strength": "Médio"
                }
            elif current_insight and line:
                # Adicionar à descrição do insight atual
                current_insight["description"] += f" {line}"

        # Adicionar último insight se existir
        if current_insight:
            insights.append(current_insight)

        return insights

    def _find_similar_insight(self, insight_data: Dict[str, Any], existing_insights: List[ConsolidatedInsight]) -> Optional[ConsolidatedInsight]:
        """Procura insight similar nos já consolidados"""
        insight_title = insight_data.get("title", "").lower()
        insight_category = insight_data.get("category", "")

        for existing in existing_insights:
            # Comparar por categoria e similaridade de título
            if existing.category == insight_category:
                existing_title = existing.title.lower()

                # Verificar se há palavras-chave em comum
                insight_words = set(insight_title.split())
                existing_words = set(existing_title.split())

                if len(insight_words & existing_words) >= 2:  # Pelo menos 2 palavras em comum
                    return existing

        return None

    def _merge_insights(self, existing_insight: ConsolidatedInsight, new_insight_data: Dict[str, Any], agent_name: str):
        """Merge insight novo com existente"""
        # Adicionar agente à lista
        if agent_name not in existing_insight.source_agents:
            existing_insight.source_agents.append(agent_name)

        # Atualizar scores (média ponderada)
        weight = 1.0 / len(existing_insight.source_agents)
        existing_insight.impact_score = (existing_insight.impact_score * (1 - weight) +
                                         new_insight_data.get("impact_score", 50.0) * weight)

        # Atualizar consenso
        existing_insight.agent_consensus = len(
            existing_insight.source_agents) * 20  # Mais agentes = mais consenso

        # Enriquecer descrição se necessário
        new_desc = new_insight_data.get("description", "")
        if new_desc and new_desc not in existing_insight.description:
            existing_insight.description += f" [Perspectiva {agent_name}: {new_desc[:200]}...]"

    def _calculate_final_consensus(self, insights: List[ConsolidatedInsight]):
        """Calcula consenso final entre agentes para cada insight"""
        for insight in insights:
            # Consenso baseado no número de agentes que mencionaram
            num_agents = len(insight.source_agents)
            max_possible = 5  # Número máximo de agentes no sistema

            insight.agent_consensus = min(
                (num_agents / max_possible) * 100, 100)

    def _generate_recommendations_from_insight(self, insight: ConsolidatedInsight) -> List[Dict[str, Any]]:
        """Gera recomendações específicas baseadas em um insight"""
        recommendations = []

        # Gerar recomendação principal baseada no insight
        main_rec = {
            "title": f"Implementar: {insight.title}",
            "description": f"Baseado na análise de {', '.join(insight.source_agents)}: {insight.description}",
            "impact_score": insight.impact_score,
            "effort_score": self._estimate_effort_score(insight),
            "urgency": self._determine_urgency(insight),
            "complexity": self._assess_complexity(insight),
            "risk_level": self._assess_risk_level(insight),
            "timeline": self._estimate_timeline(insight),
            "resources": self._identify_required_resources(insight),
            "metrics": self._suggest_success_metrics(insight)
        }

        recommendations.append(main_rec)

        return recommendations

    def _calculate_priority_score(self, recommendation: PrioritizedRecommendation) -> float:
        """Calcula score de prioridade usando matriz impacto vs esforço"""
        # Normalizar urgência para score
        urgency_scores = {"Crítico": 100, "Importante": 70, "Desejável": 40}
        urgency_score = urgency_scores.get(recommendation.urgency, 50)

        # Calcular score composto
        priority_score = (
            recommendation.impact_score * self.priority_weights["impact"] +
            # Negativo
            recommendation.effort_score * self.priority_weights["effort"] +
            urgency_score * self.priority_weights["urgency"] +
            50 * self.priority_weights["consensus"]  # Score base de consenso
        )

        return max(0, min(100, priority_score))

    def _estimate_effort_score(self, insight: ConsolidatedInsight) -> float:
        """Estima esforço necessário baseado no insight"""
        # Analisar complexidade baseada na categoria e descrição
        category_efforts = {
            "Arquitetura Técnica": 80,
            "Experiência do Usuário": 60,
            "Estratégia de Produto": 70,
            "SEO e Performance": 40,
            "Competitividade": 50
        }

        base_effort = category_efforts.get(insight.category, 60)

        # Ajustar baseado na descrição
        description = insight.description.lower()
        if any(word in description for word in ['refatorar', 'reescrever', 'migrar']):
            base_effort += 20
        elif any(word in description for word in ['otimizar', 'melhorar', 'ajustar']):
            base_effort += 10

        return min(100, base_effort)

    def _determine_urgency(self, insight: ConsolidatedInsight) -> str:
        """Determina urgência baseada no impacto e evidência"""
        if insight.impact_score > 80 and insight.agent_consensus > 80:
            return "Crítico"
        elif insight.impact_score > 60 or insight.agent_consensus > 60:
            return "Importante"
        else:
            return "Desejável"

    def _assess_complexity(self, insight: ConsolidatedInsight) -> str:
        """Avalia complexidade baseada na categoria e descrição"""
        if insight.category == "Arquitetura Técnica":
            return "Alta"
        elif insight.category in ["SEO e Performance", "Competitividade"]:
            return "Baixa"
        else:
            return "Média"

    def _assess_risk_level(self, insight: ConsolidatedInsight) -> str:
        """Avalia nível de risco da implementação"""
        if insight.evidence_strength == "Baixo" or insight.agent_consensus < 50:
            return "Alto"
        elif insight.category == "Arquitetura Técnica":
            return "Médio"
        else:
            return "Baixo"

    def _estimate_timeline(self, insight: ConsolidatedInsight) -> str:
        """Estima timeline baseado na categoria e complexidade"""
        timelines = {
            "SEO e Performance": "1-2 semanas",
            "Competitividade": "2-3 semanas",
            "Experiência do Usuário": "3-4 semanas",
            "Estratégia de Produto": "4-6 semanas",
            "Arquitetura Técnica": "6-8 semanas"
        }

        return timelines.get(insight.category, "2-4 semanas")

    def _identify_required_resources(self, insight: ConsolidatedInsight) -> List[str]:
        """Identifica recursos necessários baseados na categoria"""
        resources_map = {
            "Arquitetura Técnica": ["Desenvolvedor Senior", "Arquiteto de Software", "DevOps"],
            "Experiência do Usuário": ["UX Designer", "UI Designer", "Desenvolvedor Frontend"],
            "Estratégia de Produto": ["Product Manager", "Analista de Negócios", "Stakeholders"],
            "SEO e Performance": ["Especialista SEO", "Desenvolvedor", "Analista Web"],
            "Competitividade": ["Analista de Mercado", "Product Manager", "Marketing"]
        }

        return resources_map.get(insight.category, ["Equipe Multidisciplinar"])

    def _suggest_success_metrics(self, insight: ConsolidatedInsight) -> List[str]:
        """Sugere métricas de sucesso baseadas na categoria"""
        metrics_map = {
            "Arquitetura Técnica": ["Tempo de resposta", "Disponibilidade", "Escalabilidade"],
            "Experiência do Usuário": ["Satisfação do usuário", "Taxa de conversão", "Tempo na página"],
            "Estratégia de Produto": ["Engajamento", "Retenção", "Revenue per user"],
            "SEO e Performance": ["Ranking SEO", "Page Speed", "Taxa de clique"],
            "Competitividade": ["Market share", "NPS", "Diferenciação percebida"]
        }

        return metrics_map.get(insight.category, ["ROI", "Satisfação stakeholders"])

    def _extract_themes_from_agent(self, agent_name: str, analysis: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """Extrai temas da análise de um agente específico"""
        themes = {}

        # Temas comuns por agente
        agent_themes_map = {
            "architect": ["Escalabilidade", "Performance", "Segurança", "Arquitetura"],
            "ux": ["Usabilidade", "Acessibilidade", "Interface", "Experiência"],
            "product": ["Estratégia", "Roadmap", "Features", "Métricas"],
            "seo": ["Otimização", "Conteúdo", "Performance", "Rankings"],
            "benchmark": ["Competitividade", "Diferenciação", "Mercado", "Posicionamento"]
        }

        # Identificar tipo de agente
        agent_type = None
        for key in agent_themes_map.keys():
            if key in agent_name.lower():
                agent_type = key
                break

        if agent_type:
            potential_themes = agent_themes_map[agent_type]

            # Procurar menções dos temas na análise
            analysis_text = str(analysis).lower()

            for theme in potential_themes:
                if theme.lower() in analysis_text:
                    themes[theme] = {
                        "description": f"Tema {theme} identificado na análise do {agent_name}",
                        "insights": [f"insight_{theme.lower()}"],
                        "recommendations": [f"rec_{theme.lower()}"]
                    }

        return themes

    def _calculate_theme_consistency(self, descriptions: List[str]) -> float:
        """Calcula consistência entre descrições de tema"""
        if len(descriptions) <= 1:
            return 100.0

        # Simular análise de consistência baseada em palavras comuns
        all_words = []
        for desc in descriptions:
            all_words.extend(desc.lower().split())

        unique_words = set(all_words)
        common_words = []

        for word in unique_words:
            count = all_words.count(word)
            # Aparece em pelo menos 50% das descrições
            if count >= len(descriptions) * 0.5:
                common_words.append(word)

        consistency = min(
            (len(common_words) / max(len(unique_words), 1)) * 100, 100)
        return consistency

    def _consolidate_theme_description(self, descriptions: List[str]) -> str:
        """Consolida múltiplas descrições em uma única"""
        if not descriptions:
            return ""

        if len(descriptions) == 1:
            return descriptions[0]

        # Criar descrição consolidada
        return f"Tema identificado por múltiplos agentes: {descriptions[0][:100]}..."

    def _assess_strategic_importance(self, theme_name: str, frequency: int, consistency: float) -> str:
        """Avalia importância estratégica do tema"""
        importance_score = (frequency * 20) + (consistency * 0.5)

        if importance_score > 80:
            return "Crítico"
        elif importance_score > 60:
            return "Alto"
        elif importance_score > 40:
            return "Médio"
        else:
            return "Baixo"

    def _categorize_business_area(self, theme_name: str) -> str:
        """Categoriza tema por área de negócio"""
        business_areas = {
            "Tecnologia": ["Performance", "Arquitetura", "Segurança", "Escalabilidade"],
            "Produto": ["Features", "Roadmap", "Estratégia", "Métricas"],
            "Marketing": ["SEO", "Conteúdo", "Competitividade", "Posicionamento"],
            "Experiência": ["Usabilidade", "Interface", "Acessibilidade", "UX"]
        }

        for area, keywords in business_areas.items():
            if any(keyword.lower() in theme_name.lower() for keyword in keywords):
                return area

        return "Geral"

    def _assess_ux_impact(self, theme_name: str) -> str:
        """Avalia impacto na experiência do usuário"""
        ux_keywords = ["usabilidade", "interface",
                       "experiência", "acessibilidade", "performance"]

        if any(keyword in theme_name.lower() for keyword in ux_keywords):
            return "Alto impacto na experiência do usuário"
        else:
            return "Impacto indireto na experiência do usuário"

    def _generate_executive_summary(self,
                                    insights: List[ConsolidatedInsight],
                                    recommendations: List[PrioritizedRecommendation],
                                    themes: List[CommonTheme]) -> str:
        """Gera resumo executivo baseado na análise"""
        top_insights = insights[:3]
        top_themes = themes[:3]

        summary = f"""
Análise estratégica abrangente identificou {len(insights)} insights críticos e {len(recommendations)} recomendações acionáveis.

Principais descobertas:
- {len(themes)} temas estratégicos identificados com consenso entre agentes
- {len([r for r in recommendations if r.urgency == 'Crítico'])} recomendações críticas requerem ação imediata
- Convergência entre agentes indica alta confiabilidade das análises

Foco estratégico recomendado:
{', '.join([theme.theme_name for theme in top_themes])}

Próximos passos críticos definidos com base em análise multidisciplinar de especialistas.
"""
        return summary.strip()

    def _extract_key_findings(self, insights: List[ConsolidatedInsight], themes: List[CommonTheme]) -> List[str]:
        """Extrai principais descobertas"""
        findings = []

        # Top insights por consenso
        top_insights = sorted(
            insights, key=lambda x: x.agent_consensus, reverse=True)[:3]
        for insight in top_insights:
            findings.append(f"{insight.category}: {insight.title}")

        # Temas mais frequentes
        top_themes = sorted(
            themes, key=lambda x: x.frequency_score, reverse=True)[:2]
        for theme in top_themes:
            findings.append(
                f"Tema crítico: {theme.theme_name} (mencionado por {len(theme.mentioned_by_agents)} agentes)")

        return findings

    def _create_strategic_analysis_section(self,
                                           insights: List[ConsolidatedInsight],
                                           themes: List[CommonTheme]) -> FinalReportSection:
        """Cria seção de análise estratégica"""
        content = f"""
## Análise Estratégica

### Insights Consolidados ({len(insights)} identificados)

Os insights foram consolidados a partir da análise de múltiplos especialistas, priorizados por impacto e consenso:

**Top 5 Insights por Relevância:**
"""

        top_insights = insights[:5]
        for i, insight in enumerate(top_insights, 1):
            content += f"""
{i}. **{insight.title}** (Consenso: {insight.agent_consensus:.0f}%)
   - Fonte: {', '.join(insight.source_agents)}
   - Impacto: {insight.impact_score:.0f}/100
   - {insight.description[:200]}...
"""

        content += f"""

### Temas Estratégicos Identificados ({len(themes)} temas)

Análise de convergência entre especialistas revelou os seguintes temas críticos:
"""

        for theme in themes[:3]:
            content += f"""
**{theme.theme_name}** - {theme.strategic_importance} Importância
- Frequência: {theme.frequency_score:.0f}% | Consistência: {theme.consistency_score:.0f}%
- Mencionado por: {', '.join(theme.mentioned_by_agents)}
- {theme.description}
"""

        return FinalReportSection(
            section_id="strategic_analysis",
            title="Análise Estratégica",
            content=content,
            insights_included=[insight.insight_id for insight in top_insights],
            recommendations_included=[]
        )

    def _create_recommendations_section(self, recommendations: List[PrioritizedRecommendation]) -> FinalReportSection:
        """Cria seção de recomendações priorizadas"""
        content = f"""
## Recomendações Priorizadas

### Matriz de Priorização (Impacto vs Esforço)

{len(recommendations)} recomendações identificadas e priorizadas baseadas em análise multidisciplinar:

**Recomendações Críticas (Ação Imediata):**
"""

        critical_recs = [
            r for r in recommendations if r.urgency == "Crítico"][:3]
        for i, rec in enumerate(critical_recs, 1):
            content += f"""
{i}. **{rec.title}**
   - Prioridade: {rec.priority_score:.0f}/100 | Urgência: {rec.urgency}
   - Impacto: {rec.impact_score:.0f}/100 | Esforço: {rec.effort_score:.0f}/100
   - Timeline: {rec.estimated_timeline}
   - Recursos: {', '.join(rec.required_resources)}
   - {rec.description[:150]}...
"""

        content += f"""

**Top 10 Recomendações por Prioridade:**
"""

        for i, rec in enumerate(recommendations[:10], 1):
            status = "🔴" if rec.urgency == "Crítico" else "🟡" if rec.urgency == "Importante" else "🟢"
            content += f"{i}. {status} {rec.title} (Prioridade: {rec.priority_score:.0f})\n"

        return FinalReportSection(
            section_id="prioritized_recommendations",
            title="Recomendações Priorizadas",
            content=content,
            insights_included=[],
            recommendations_included=[
                rec.recommendation_id for rec in recommendations[:10]]
        )

    def _create_themes_section(self, themes: List[CommonTheme]) -> FinalReportSection:
        """Cria seção de temas críticos"""
        content = f"""
## Temas Críticos e Convergência de Especialistas

### Análise de Consenso

{len(themes)} temas identificados com convergência entre especialistas:

"""

        for theme in themes:
            agents_list = ', '.join(theme.mentioned_by_agents)
            content += f"""
### {theme.theme_name}
- **Consenso:** {theme.frequency_score:.0f}% frequência, {theme.consistency_score:.0f}% consistência
- **Especialistas:** {agents_list}
- **Área de Negócio:** {theme.business_area}
- **Importância Estratégica:** {theme.strategic_importance}
- **Descrição:** {theme.description}
- **Impacto UX:** {theme.user_experience_impact}

"""

        return FinalReportSection(
            section_id="critical_themes",
            title="Temas Críticos",
            content=content,
            insights_included=[],
            recommendations_included=[]
        )

    def _create_action_plan_section(self, recommendations: List[PrioritizedRecommendation]) -> FinalReportSection:
        """Cria seção de plano de ação"""
        immediate, short_term, long_term = self._categorize_actions_by_timeline(
            recommendations)

        content = f"""
## Plano de Ação Estratégico

### Roadmap de Implementação

**Ações Imediatas (0-30 dias):**
"""

        for action in immediate[:5]:
            content += f"- {action}\n"

        content += f"""

**Objetivos Curto Prazo (1-3 meses):**
"""

        for goal in short_term[:5]:
            content += f"- {goal}\n"

        content += f"""

**Visão Longo Prazo (6+ meses):**
"""

        for vision in long_term[:3]:
            content += f"- {vision}\n"

        content += f"""

### Métricas de Acompanhamento

**KPIs Principais:**
- Implementação de recomendações críticas: Meta 80% em 30 dias
- Satisfação stakeholders: Acompanhamento quinzenal
- ROI das implementações: Avaliação trimestral

**Governance:**
- Review semanal do progresso
- Ajustes de priorização conforme necessário
- Comunicação regular com stakeholders
"""

        return FinalReportSection(
            section_id="action_plan",
            title="Plano de Ação",
            content=content,
            insights_included=[],
            recommendations_included=[
                rec.recommendation_id for rec in recommendations[:15]]
        )

    def _categorize_actions_by_timeline(self, recommendations: List[PrioritizedRecommendation]) -> tuple:
        """Categoriza ações por timeline"""
        immediate = []
        short_term = []
        long_term = []

        for rec in recommendations:
            if rec.urgency == "Crítico" or "1-2 semanas" in rec.estimated_timeline:
                immediate.append(rec.title)
            elif "semanas" in rec.estimated_timeline or rec.urgency == "Importante":
                short_term.append(rec.title)
            else:
                long_term.append(rec.title)

        return immediate, short_term, long_term

    def _get_unique_agents(self, insights: List[ConsolidatedInsight]) -> List[str]:
        """Obtém lista única de agentes que contribuíram"""
        agents = set()
        for insight in insights:
            agents.update(insight.source_agents)
        return list(agents)

    def _identify_recommendation_dependencies(self, recommendations: List[PrioritizedRecommendation]):
        """Identifica dependências entre recomendações"""
        # Implementação básica - pode ser expandida
        for rec in recommendations:
            if "arquitetura" in rec.title.lower() or "infraestrutura" in rec.title.lower():
                # Recomendações arquiteturais geralmente são prerequisitos
                for other_rec in recommendations:
                    if other_rec != rec and "implementar" in other_rec.title.lower():
                        other_rec.dependencies.append(rec.recommendation_id)

    def _filter_by_business_objectives(self,
                                       recommendations: List[PrioritizedRecommendation],
                                       objectives: List[str]) -> List[PrioritizedRecommendation]:
        """Filtra recomendações baseadas em objetivos de negócio"""
        # Implementação básica - prioriza recomendações alinhadas com objetivos
        filtered = []

        for rec in recommendations:
            relevance_score = 0
            for objective in objectives:
                if any(word in rec.description.lower() for word in objective.lower().split()):
                    relevance_score += 1

            if relevance_score > 0:
                # Boost na prioridade para recomendações alinhadas
                rec.priority_score += relevance_score * 5
                filtered.append(rec)

        # Se nenhuma recomendação foi filtrada, retorna todas
        return filtered if filtered else recommendations

    def _parse_text_for_insights(self, agent_name: str, text: str) -> List[Dict[str, Any]]:
        """Parse genérico de texto para extrair insights"""
        insights = []

        # Procurar por padrões de insights/recomendações
        sentences = text.split('.')
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) > 20 and any(keyword in sentence.lower() for keyword in
                                          ['recomend', 'sugest', 'melhor', 'implement', 'optim']):

                insight = {
                    "category": "Geral",
                    "title": sentence[:80] + "..." if len(sentence) > 80 else sentence,
                    "description": sentence,
                    "impact_score": 50.0,
                    "confidence_score": 60.0,
                    "evidence_strength": "Médio"
                }
                insights.append(insight)

        return insights

    def _convert_dict_to_insight(self, agent_name: str, item: Dict[str, Any]) -> Dict[str, Any]:
        """Converte item de dicionário em insight estruturado"""
        return {
            "category": item.get("category", "Geral"),
            "title": item.get("title", item.get("name", "Insight sem título")),
            "description": item.get("description", str(item)),
            "impact_score": item.get("impact", item.get("score", 50.0)),
            "confidence_score": 70.0,
            "evidence_strength": "Médio"
        }
