# Client Service - Microserviço de Gestão de Clientes

Microserviço responsável pela gestão de clientes do ScopeAI, implementado seguindo os princípios de Domain-Driven Design (DDD) e Clean Architecture.

> **📌 Nota Importante**: Este microserviço é parte do backend ScopeAI e **reutiliza toda a infraestrutura de configuração existente** para evitar duplicações.

## 📋 Configuração Compartilhada

Este microserviço **NÃO possui configurações próprias**, mas reutiliza:

- **Dependências**: Definidas em `/backend/pyproject.toml`
- **Variáveis de ambiente**: Configuradas em `/backend/.env`
- **Docker**: Usa o Dockerfile principal em `/backend/Dockerfile`
- **Docker Compose**: Definido na raiz do projeto `/docker-compose.yml`
- **Git Ignore**: Usa o `.gitignore` principal do projeto

Para adicionar novas dependências específicas do microserviço, adicione ao `pyproject.toml` principal com um comentário indicando o uso.

## 🏗️ Arquitetura

O serviço segue Clean Architecture com as seguintes camadas:

```
src/
├── domain/          # Entidades, Value Objects, Domain Events
├── application/     # Use Cases, DTOs, Application Services
├── infrastructure/  # Repositories, External Services, Messaging
└── api/            # Controllers, Routes, Middleware
```

### Princípios

- **Dependency Rule**: Dependências sempre apontam para dentro
- **Domain-Driven Design**: Foco no domínio de negócio
- **SOLID Principles**: Single Responsibility, Open/Closed, etc.
- **Event-Driven**: Comunicação assíncrona via eventos

## 🚀 Início Rápido

### Pré-requisitos

- Python 3.12+
- Docker & Docker Compose
- MongoDB
- Redis (opcional, para cache)
- RabbitMQ (opcional, para mensageria)

### Instalação Local

1. Clone o repositório e navegue até o backend:
```bash
cd backend
```

2. Instale as dependências (do projeto principal):
```bash
poetry install
```

3. As variáveis de ambiente já estão configuradas em `/backend/.env`

4. Execute o serviço:
```bash
# Do diretório backend/
poetry run uvicorn microservices.client_service.src.api.main:app --reload --port 8001
```

### Execução com Docker

O microserviço será incluído no build do Docker principal do backend:

```bash
# Da raiz do projeto
docker-compose up backend
```

## 📚 API Documentation

A documentação da API está disponível em:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc
- OpenAPI Schema: http://localhost:8000/openapi.json

### Endpoints Principais

#### Clientes
- `POST /api/v2/clients` - Criar novo cliente
- `GET /api/v2/clients` - Listar clientes
- `GET /api/v2/clients/{id}` - Obter cliente por ID
- `PUT /api/v2/clients/{id}` - Atualizar cliente
- `DELETE /api/v2/clients/{id}` - Deletar cliente

#### Análise
- `POST /api/v2/clients/{id}/analyze` - Iniciar análise do cliente

#### Health Check
- `GET /health` - Status do serviço
- `GET /metrics` - Métricas Prometheus

## 🧪 Testes

### Executar todos os testes
```bash
poetry run pytest
```

### Com coverage
```bash
poetry run pytest --cov
```

### Apenas testes unitários
```bash
poetry run pytest tests/unit
```

### Apenas testes de integração
```bash
poetry run pytest tests/integration
```

## 🔧 Desenvolvimento

### Estrutura de Código

1. **Domain Layer** (`src/domain/`)
   - Entidades de negócio puras
   - Value Objects imutáveis
   - Domain Events
   - Repository interfaces

2. **Application Layer** (`src/application/`)
   - Use Cases (comandos e queries)
   - DTOs para transferência de dados
   - Application Services
   - Interfaces de serviços externos

3. **Infrastructure Layer** (`src/infrastructure/`)
   - Implementação de repositories
   - Adaptadores para serviços externos
   - Sistema de mensageria
   - Configuração de banco de dados

4. **API Layer** (`src/api/`)
   - Controllers REST
   - Definição de rotas
   - Middleware (auth, rate limiting, etc.)
   - Validação de requests

### Convenções de Código

- Use type hints em todas as funções
- Docstrings para módulos, classes e funções públicas
- Formato com Black: `poetry run black src/`
- Lint com Flake8: `poetry run flake8 src/`
- Type check com MyPy: `poetry run mypy src/`

### Git Workflow

1. Crie uma branch: `git checkout -b feat-your-feature`
2. Commit mudanças: `git commit -m "feat(domain): add client entity"`
3. Push: `git push origin feat-your-feature`
4. Abra um Pull Request

## 📊 Monitoramento

### Logs
- Formato JSON estruturado
- Níveis: DEBUG, INFO, WARNING, ERROR, CRITICAL
- Correlation ID para rastreamento

### Métricas
- Endpoint Prometheus: http://localhost:9090/metrics
- Métricas customizadas de negócio
- Latência de APIs externas

### Health Checks
- Liveness: `/health/live`
- Readiness: `/health/ready`
- Dependencies: MongoDB, Redis, RabbitMQ

## 🔐 Segurança

- JWT para autenticação
- RBAC para autorização
- Rate limiting por IP
- CORS configurável
- Sanitização de inputs

## 🚢 Deploy

### Variáveis de Ambiente Obrigatórias

```bash
ENVIRONMENT=production
MONGODB_URI=mongodb://...
JWT_SECRET_KEY=your-secret-key
PERPLEXITY_API_KEY=your-api-key
```

### Docker Compose

```yaml
version: '3.8'
services:
  client-service:
    image: scopeai/client-service:latest
    ports:
      - "8000:8000"
    env_file:
      - .env
    depends_on:
      - mongodb
      - redis
      - rabbitmq
```

## 📝 Migração do Monolito

Este microserviço faz parte da migração do monolito ScopeAI usando o padrão Strangler Fig:

1. **Fase 1**: Parallel run com monolito
2. **Fase 2**: Migração gradual de clientes
3. **Fase 3**: Desativação do código legado

Durante a migração:
- Database compartilhado temporário
- Sincronização via eventos
- Feature flags para controle

## 🤝 Contribuindo

1. Fork o projeto
2. Crie sua feature branch
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request

## 📄 License

Este projeto é parte do ScopeAI e segue a mesma licença do projeto principal. 