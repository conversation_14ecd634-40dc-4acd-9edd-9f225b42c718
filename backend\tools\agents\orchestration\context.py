"""
Contexto de Orquestração

Gerencia o contexto compartilhado entre agentes durante a execução,
incluindo dados de entrada, resultados intermediários e metadados.
"""

from typing import Dict, Any, Optional, List
from datetime import datetime
import logging
from .schemas import OrchestrationResult, AgentExecutionResult, OrchestrationConfig

logger = logging.getLogger(__name__)


class OrchestrationContext:
    """
    Contexto compartilhado durante a execução da orquestração.

    Responsável por:
    - Armazenar dados de entrada do cliente
    - Gerenciar resultados intermediários dos agentes
    - Fornecer acesso a dependências entre agentes
    - Manter histórico de execução
    """

    def __init__(
        self,
        client_id: str,
        config: OrchestrationConfig,
        client_data: Dict[str, Any],
        orchestration_result: Optional[OrchestrationResult] = None
    ):
        self.client_id = client_id
        self.config = config
        self.client_data = client_data

        if orchestration_result:
            self.orchestration_result = orchestration_result
        else:
            self.orchestration_result = OrchestrationResult(
                client_id=client_id,
                config=config
            )

        # Dados compartilhados entre agentes
        self._shared_data: Dict[str, Any] = {}

        # Cache de resultados de agentes
        self._agent_results_cache: Dict[str, Dict[str, Any]] = {}

        # Metadados de execução
        self._execution_metadata: Dict[str, Any] = {
            "created_at": datetime.now(),
            "total_agents": sum(len(phase.agents) for phase in config.phases),
            "execution_phases": len(config.phases)
        }

        logger.info(f"OrchestrationContext criado para cliente {client_id}")

    @property
    def orchestration_id(self) -> str:
        """ID único da orquestração"""
        return self.orchestration_result.orchestration_id

    def get_client_data(self) -> Dict[str, Any]:
        """Retorna dados do cliente"""
        return self.client_data.copy()

    def get_shared_data(self, key: Optional[str] = None) -> Any:
        """
        Retorna dados compartilhados entre agentes

        Args:
            key: Chave específica ou None para todos os dados
        """
        if key is None:
            return self._shared_data.copy()
        return self._shared_data.get(key)

    def set_shared_data(self, key: str, value: Any):
        """Define dados compartilhados entre agentes"""
        self._shared_data[key] = value
        logger.debug(f"Dados compartilhados atualizados: {key}")

    def get_agent_result(self, agent_name: str) -> Optional[Dict[str, Any]]:
        """
        Retorna resultado de um agente específico

        Args:
            agent_name: Nome do agente

        Returns:
            Resultado do agente ou None se não executado
        """
        if agent_name in self._agent_results_cache:
            return self._agent_results_cache[agent_name]

        if agent_name in self.orchestration_result.agent_results:
            result = self.orchestration_result.agent_results[agent_name]
            if result.result_data:
                self._agent_results_cache[agent_name] = result.result_data
                return result.result_data

        return None

    def get_dependencies_results(self, phase_dependencies: List[str]) -> Dict[str, Dict[str, Any]]:
        """
        Retorna resultados dos agentes das fases dependentes

        Args:
            phase_dependencies: Lista de IDs de fases dependentes

        Returns:
            Dict com resultados dos agentes das fases dependentes
        """
        dependencies_results = {}

        for phase_dep_id in phase_dependencies:
            # Encontrar fase dependente
            dep_phase = None
            for phase in self.config.phases:
                if str(phase.phase_id) == phase_dep_id:
                    dep_phase = phase
                    break

            if dep_phase:
                for agent_name in dep_phase.agents:
                    result = self.get_agent_result(agent_name)
                    if result:
                        dependencies_results[agent_name] = result
                        logger.debug(
                            f"Dependência {agent_name} disponível para fase")

        return dependencies_results

    def add_agent_result(self, agent_result: AgentExecutionResult):
        """
        Adiciona resultado de um agente ao contexto

        Args:
            agent_result: Resultado da execução do agente
        """
        # Adicionar ao resultado da orquestração
        self.orchestration_result.add_agent_result(agent_result)

        # Atualizar cache se há dados
        if agent_result.result_data:
            self._agent_results_cache[agent_result.agent_name] = agent_result.result_data

        logger.info(
            f"Resultado do agente {agent_result.agent_name} adicionado ao contexto")

    def get_execution_metadata(self) -> Dict[str, Any]:
        """Retorna metadados de execução"""
        current_metadata = self._execution_metadata.copy()
        current_metadata.update({
            "current_time": datetime.now(),
            "successful_agents": self.orchestration_result.successful_agents,
            "failed_agents": self.orchestration_result.failed_agents,
            "current_status": self.orchestration_result.status.value
        })
        return current_metadata

    def prepare_agent_input(
        self,
        agent_name: str,
        phase_dependencies: List[str]
    ) -> Dict[str, Any]:
        """
        Prepara dados de entrada para um agente específico

        Args:
            agent_name: Nome do agente a executar
            phase_dependencies: Dependências da fase do agente

        Returns:
            Dict com dados preparados para o agente
        """
        # Dados base do cliente
        agent_input = {
            "client_data": self.get_client_data(),
            "client_id": self.client_id,
            "orchestration_id": self.orchestration_id
        }

        # Adicionar dados compartilhados
        shared_data = self.get_shared_data()
        if shared_data:
            agent_input["shared_data"] = shared_data

        # Adicionar resultados de dependências
        dependencies = self.get_dependencies_results(phase_dependencies)
        if dependencies:
            agent_input["dependencies_results"] = dependencies

        # Metadados específicos do agente
        agent_input["agent_metadata"] = {
            "agent_name": agent_name,
            "execution_order": len(self.orchestration_result.agent_results) + 1,
            "total_agents": self._execution_metadata["total_agents"]
        }

        logger.debug(f"Dados preparados para agente {agent_name}")
        return agent_input

    def get_context_summary(self) -> Dict[str, Any]:
        """Retorna resumo do contexto atual"""
        return {
            "orchestration_id": self.orchestration_id,
            "client_id": self.client_id,
            "status": self.orchestration_result.status.value,
            "completed_agents": list(self._agent_results_cache.keys()),
            "shared_data_keys": list(self._shared_data.keys()),
            "execution_metadata": self.get_execution_metadata()
        }

    def cleanup(self):
        """Limpa recursos do contexto"""
        logger.info(
            f"Limpeza do contexto da orquestração {self.orchestration_id}")
        self._agent_results_cache.clear()
        self._shared_data.clear()

    def store_agent_result(self, agent_name: str, result_data: Dict[str, Any]):
        """
        Armazena resultado de um agente no contexto compartilhado

        Args:
            agent_name: Nome do agente
            result_data: Dados do resultado
        """
        self._agent_results_cache[agent_name] = result_data
        logger.debug(
            f"Resultado do agente {agent_name} armazenado no contexto")


class ContextualData:
    """Helper para criar dados contextuais específicos por tipo de agente"""

    @staticmethod
    def for_technical_architect(context: OrchestrationContext) -> Dict[str, Any]:
        """Prepara dados específicos para Technical Architect Agent"""
        base_input = context.prepare_agent_input("technical_architect", [])

        # Dados específicos para análise técnica
        client_data = base_input["client_data"]

        return {
            **base_input,
            "analysis_focus": "architectural_recommendations",
            "site_url": client_data.get("site", ""),
            "company_name": client_data.get("nome", ""),
            "sector": client_data.get("setor", "")
        }

    @staticmethod
    def for_uxui_designer(context: OrchestrationContext) -> Dict[str, Any]:
        """Prepara dados específicos para UX/UI Designer Agent"""
        base_input = context.prepare_agent_input("uxui_designer", [])

        return {
            **base_input,
            "analysis_focus": "visual_ux_analysis",
            "include_mobile": True,
            "include_accessibility": True
        }

    @staticmethod
    def for_product_owner(context: OrchestrationContext) -> Dict[str, Any]:
        """Prepara dados específicos para Product Owner Agent"""
        base_input = context.prepare_agent_input("product_owner", ["1"])

        # Buscar contexto técnico e visual das dependências
        tech_context = context.get_agent_result("technical_architect")
        ux_context = context.get_agent_result("uxui_designer")

        return {
            **base_input,
            "analysis_focus": "product_strategy",
            "technical_context": tech_context,
            "ux_context": ux_context
        }

    @staticmethod
    def for_seo_specialist(context: OrchestrationContext) -> Dict[str, Any]:
        """Prepara dados específicos para SEO Specialist Agent"""
        base_input = context.prepare_agent_input("seo_specialist", ["1"])

        # Buscar dados técnicos para análise SEO
        tech_result = context.get_agent_result("technical_architect")
        lighthouse_data = None
        if tech_result and "lighthouse_data" in tech_result:
            lighthouse_data = tech_result["lighthouse_data"]

        return {
            **base_input,
            "analysis_focus": "seo_optimization",
            "lighthouse_data": lighthouse_data
        }

    @staticmethod
    def for_benchmark_agent(context: OrchestrationContext) -> Dict[str, Any]:
        """Prepara dados específicos para Benchmark Agent"""
        base_input = context.prepare_agent_input("benchmark_agent", ["1"])

        return {
            **base_input,
            "analysis_focus": "competitive_analysis"
        }

    @staticmethod
    def for_technical_writer(context: OrchestrationContext) -> Dict[str, Any]:
        """Prepara dados específicos para Technical Writer Agent"""
        base_input = context.prepare_agent_input(
            "technical_writer", ["2", "3"])

        # Coletar todos os resultados dos agentes anteriores
        all_agent_results = {}
        for agent_name in ["technical_architect", "uxui_designer", "product_owner",
                           "seo_specialist", "benchmark_agent"]:
            result = context.get_agent_result(agent_name)
            if result:
                all_agent_results[agent_name] = result

        return {
            **base_input,
            "analysis_focus": "insights_consolidation",
            "all_agent_results": all_agent_results
        }
