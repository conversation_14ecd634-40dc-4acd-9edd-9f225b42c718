import { Observable } from 'rxjs';

export interface MermaidRenderResult {
	content: string;
	success: boolean;
	error?: string;
}

export interface MermaidRenderStrategy {
	/**
	 * Renderiza um diagrama Mermaid
	 * @param code Código Mermaid limpo
	 * @returns Observable com resultado da renderização
	 */
	render(code: string): Observable<MermaidRenderResult>;

	/**
	 * Nome da estratégia para debug/logging
	 */
	readonly name: string;

	/**
	 * Se suporta download de SVG
	 */
	readonly supportsDownload: boolean;

	/**
	 * Se requer processamento de código (ex: remover emojis)
	 */
	readonly requiresCodeCleaning: boolean;
}
