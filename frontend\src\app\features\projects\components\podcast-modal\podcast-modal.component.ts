import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnDestroy, Output } from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';

/**
 * Interface para os dados do modal de podcast
 */
export interface PodcastModalData {
	/** Controla a visibilidade do modal */
	visible: boolean;
	/** Indica se o podcast está sendo gerado */
	generating: boolean;
	/** URL do arquivo de áudio do podcast */
	url: string;
	/** Título do projeto para exibição */
	title: string;
	/** ID do projeto (opcional, usado para gerar podcast) */
	projectId?: string;
}

/**
 * Componente de modal para reprodução de podcasts
 *
 * @example
 * ```html
 * <app-podcast-modal
 *   [data]="{ visible: true, url: 'audio.mp3', title: 'Meu Projeto' }"
 *   (close)="fecharModal()"
 *   (generate)="gerarPodcast($event)"
 * ></app-podcast-modal>
 * ```
 */
@Component({
	selector: 'app-podcast-modal',
	standalone: true,
	imports: [CommonModule, DialogModule, ButtonModule],
	template: `
		<p-dialog
			[visible]="data.visible"
			[modal]="true"
			[closable]="true"
			[draggable]="false"
			[resizable]="false"
			styleClass="podcast-modal"
			[style]="{ width: '90vw', maxWidth: '600px' }"
			(onHide)="onClose()"
		>
			<ng-template pTemplate="header">
				<div class="flex items-center gap-3">
					<i class="pi pi-microphone text-purple-600 text-2xl"></i>
					<div>
						<h2 class="text-xl font-bold text-gray-900 m-0">Podcast do Projeto</h2>
						<p class="text-sm text-gray-600 m-0 mt-1">{{ data.title }}</p>
					</div>
				</div>
			</ng-template>

			<ng-template pTemplate="content">
				<div class="podcast-player-container">
					<!-- Player Container -->
					<div
						class="bg-gradient-to-br from-purple-50 to-indigo-50 rounded-2xl p-8 text-center border border-purple-200"
					>
						<!-- Podcast Icon -->
						<div class="mb-6">
							<div
								class="w-20 h-20 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto shadow-lg"
							>
								<i class="pi pi-microphone text-white text-3xl"></i>
							</div>
						</div>

						<!-- Project Info -->
						<div class="mb-6">
							<h3 class="text-lg font-semibold text-gray-900 mb-2">{{ data.title }}</h3>
							<p class="text-sm text-gray-600">Análise detalhada do projeto em formato de podcast</p>
						</div>

						<!-- Loading State -->
						@if (data.generating) {
							<div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
								<div class="flex flex-col items-center gap-4">
									<i class="pi pi-spin pi-spinner text-3xl text-purple-600"></i>
									<div class="text-center">
										<p class="font-semibold text-gray-900">Gerando Podcast...</p>
										<p class="text-sm text-gray-600">
											Aguarde 1-2 minutos enquanto criamos o áudio
										</p>
									</div>
								</div>
							</div>
						}

						<!-- Audio Player -->
						@if (data.url && !data.generating) {
							<div class="bg-white rounded-xl p-4 shadow-sm border border-gray-200">
								<audio
									controls
									autoplay
									class="w-full"
									[src]="data.url"
									preload="auto"
									(error)="onAudioError($event)"
								>
									<source [src]="data.url" type="audio/mpeg" />
									<p class="text-red-600 text-sm">Seu navegador não suporta o elemento de áudio.</p>
								</audio>

								<!-- Nota sobre autoplay no Safari -->
								<div class="mt-2 text-xs text-gray-500 text-center">
									<p>⚠️ Alguns navegadores bloqueiam autoplay. Clique no play se necessário.</p>
								</div>
							</div>
						}

						<!-- Empty State -->
						@if (!data.url && !data.generating) {
							<div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
								<div class="text-center">
									<i class="pi pi-microphone text-4xl text-gray-400 mb-4"></i>
									<p class="text-gray-600 mb-4">Nenhum podcast disponível</p>
									@if (data.projectId) {
										<button
											pButton
											label="Gerar Podcast"
											icon="pi pi-play"
											class="p-button-outlined p-button-primary"
											(click)="generatePodcast()"
											[attr.aria-label]="'Gerar podcast para ' + data.title"
										></button>
									}
								</div>
							</div>
						}

						<!-- Error State -->
						@if (audioError) {
							<div class="bg-red-50 rounded-xl p-4 mt-4 border border-red-200">
								<div class="flex items-center gap-2 text-red-700">
									<i class="pi pi-exclamation-triangle"></i>
									<p class="text-sm">{{ audioError }}</p>
								</div>
							</div>
						}

						<!-- Player Info -->
						<div class="mt-4 text-xs text-gray-500">
							<p>💡 Use os controles de áudio para pausar, ajustar volume ou pular trechos</p>
						</div>
					</div>

					<!-- Additional Actions -->
					<div class="flex justify-center gap-3 mt-6">
						<button
							pButton
							label="Fechar"
							icon="pi pi-times"
							class="p-button-outlined p-button-secondary"
							(click)="onClose()"
							[attr.aria-label]="'Fechar modal do podcast'"
						></button>
					</div>
				</div>
			</ng-template>
		</p-dialog>
	`,
	styles: [
		`
			:host ::ng-deep .podcast-modal .p-dialog-content {
				padding: 0;
			}

			:host ::ng-deep .podcast-modal .p-dialog-header {
				padding: 1.5rem 1.5rem 1rem 1.5rem;
				border-bottom: 1px solid #e5e7eb;
			}

			.podcast-player-container {
				padding: 1.5rem;
			}

			audio {
				height: 54px;
			}

			audio::-webkit-media-controls-panel {
				background-color: #f9fafb;
				border-radius: 8px;
			}

			/* Responsive adjustments */
			@media (max-width: 640px) {
				.podcast-player-container {
					padding: 1rem;
				}

				:host ::ng-deep .podcast-modal .p-dialog-header {
					padding: 1rem;
				}

				.bg-gradient-to-br {
					padding: 1.5rem;
				}
			}
		`,
	],
})
export class PodcastModalComponent implements OnDestroy {
	/** Dados do modal incluindo visibilidade, URL do áudio e título */
	@Input() data: PodcastModalData = {
		visible: false,
		generating: false,
		url: '',
		title: '',
	};

	/** Evento emitido quando o modal é fechado */
	@Output() close = new EventEmitter<void>();

	/** Evento emitido quando o usuário solicita geração de podcast */
	@Output() generate = new EventEmitter<string>();

	/** Mensagem de erro do áudio (se houver) */
	public audioError: string | null = null;

	ngOnDestroy(): void {
		// Limpar estado de erro ao destruir componente
		this.audioError = null;
	}

	/**
	 * Fecha o modal e emite evento de fechamento
	 */
	public onClose(): void {
		this.audioError = null;
		this.close.emit();
	}

	/**
	 * Solicita geração de podcast para o projeto
	 * Emite o ID do projeto se disponível
	 */
	public generatePodcast(): void {
		if (this.data.projectId) {
			this.generate.emit(this.data.projectId);
		}
	}

	/**
	 * Trata erros no carregamento do áudio
	 * @param event - Evento de erro do elemento audio
	 */
	public onAudioError(event: Event): void {
		console.error('Erro ao carregar áudio:', event);
		this.audioError = 'Erro ao carregar o áudio. Por favor, tente novamente.';
	}
}
