"""
UX/UI Designer Agent - Agente especializado em design de interface e experiência do usuário

Agente focado em análise visual e melhorias de UX/UI:
- Análise de screenshots multi-resolução
- Identificação de problemas de UX/UI
- Recomendações baseadas em princípios de design
- Propostas de melhorias visuais
"""

import logging
from typing import Dict, Any, Optional, List
from agno.agent import Agent
from ...shared.model_config import model_config
from agno.tools.reasoning import ReasoningTools

from ..tools import VisualAnalysisTools

logger = logging.getLogger(__name__)


class UXUIDesignerAgent:
    """Agente especializado em UX/UI Design usando Agno"""

    def __init__(self, model_name: Optional[str] = None):
        """
        Inicializa o UX/UI Designer Agent usando modelos do settings.py

        Args:
            model_name: Modelo específico (opcional, usa automático se None)
        """
        self.visual_tools = VisualAnalysisTools()
        
        # Obter configuração de modelo baseada no settings.py
        agno_config = model_config.create_agno_config("analysis")
        
        if model_name:
            available_models = [
                model_config.get_openai_model("analysis"),
                model_config.get_cohere_model("analysis"), 
                model_config.get_mistral_model("analysis")
            ]
            if model_name in available_models:
                self.model_name = model_name
            else:
                logger.warning(f"Modelo {model_name} não disponível no settings.py, usando automático")
                self.model_name = agno_config["model_id"]
        else:
            self.model_name = agno_config["model_id"]

        # Criar modelo baseado no provider
        if agno_config["provider"] == "openai":
            from agno.models.openai import OpenAI
            model_instance = OpenAI(id=self.model_name, api_key=agno_config["api_key"])
        elif agno_config["provider"] == "cohere":
            from agno.models.cohere import Cohere
            model_instance = Cohere(id=self.model_name, api_key=agno_config["api_key"])
        elif agno_config["provider"] == "mistral":
            from agno.models.mistral import Mistral
            model_instance = Mistral(id=self.model_name, api_key=agno_config["api_key"])
        else:
            from agno.models.openai import OpenAI
            model_instance = OpenAI(id="gpt-4o-mini", api_key=agno_config["api_key"])

        self.agent = Agent(
            name="UX/UI Designer Specialist",
            role="Expert in user experience and interface design",
            model=model_instance,
            tools=[ReasoningTools()],
            instructions=[
                "You are a UX/UI design expert specializing in user interface analysis and improvement",
                "Analyze visual data and screenshots to identify design problems and opportunities",
                "Provide specific design recommendations based on established UX/UI principles",
                "Focus on usability, accessibility, visual hierarchy, and user experience",
                "Consider mobile-first design and responsive design principles",
                "Provide actionable recommendations with clear implementation steps",
                "Prioritize improvements based on user impact and business value"
            ],
            description="Specialist in UX/UI design, visual analysis, and user experience optimization",
            markdown=True,
            show_tool_calls=True
        )

        logger.info(f"UX/UI Designer Agent inicializado com {agno_config['provider']}:{self.model_name}")

    def analyze_ux_ui_design(self, visual_analysis: Dict[str, Any], lighthouse_data: Optional[Dict[str, Any]] = None, company_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Analisa UX/UI design usando dados visuais e métricas

        Args:
            visual_analysis: Dados de análise visual (screenshots, UI analysis)
            lighthouse_data: Dados opcionais do Lighthouse para contexto adicional
            company_context: Contexto da empresa para recomendações personalizadas

        Returns:
            Análise completa de UX/UI com recomendações
        """
        try:
            # Processar dados visuais usando tools existentes
            visual_insights = self.visual_tools.analyze_visual_data(
                visual_analysis)
            visual_priorities = self.visual_tools.get_visual_priorities(
                visual_analysis)
            mobile_readiness = self.visual_tools.assess_mobile_readiness(
                visual_analysis)

            # Extrair informações da empresa
            company_info = company_context or {}
            company_name = company_info.get("name", "Empresa")
            sector = company_info.get("sector", "Não especificado")

            # Extrair dados visuais específicos
            ui_ux_data = visual_analysis.get("analise_ui_ux", {})
            screenshots_urls = visual_analysis.get("screenshots_urls", [])
            design_recommendations = visual_analysis.get(
                "recomendacoes_design", [])

            # Preparar contexto do Lighthouse se disponível
            lighthouse_context = ""
            if lighthouse_data:
                performance_score = lighthouse_data.get(
                    "performance", {}).get("score", 0) * 100
                accessibility_score = lighthouse_data.get(
                    "accessibility", {}).get("score", 0) * 100
                lighthouse_context = f"""
**CONTEXTO LIGHTHOUSE:**
- Performance Score: {performance_score:.0f}/100
- Accessibility Score: {accessibility_score:.0f}/100
"""

            # Construir prompt especializado para UX/UI
            analysis_prompt = f"""
Você é um UX/UI Designer especializado em avaliar interfaces e propor melhorias. Analise os seguintes dados visuais do site da empresa {company_name} (setor: {sector}) e forneça recomendações detalhadas de design:

**ANÁLISE VISUAL PROCESSADA:**
- Layout Quality: {visual_insights.layout_quality}
- User Experience: {visual_insights.user_experience} 
- Responsive Design: {visual_insights.responsive_design}
- Visual Hierarchy: {visual_insights.visual_hierarchy or "Não avaliado"}
- Score Visual Geral: {visual_insights.overall_visual_score}/100

**PROBLEMAS DE UI IDENTIFICADOS:**
{chr(10).join(f"- {issue}" for issue in visual_insights.ui_issues) if visual_insights.ui_issues else "- Nenhum problema crítico identificado"}

**PROBLEMAS DE UX IDENTIFICADOS:**
{chr(10).join(f"- {issue}" for issue in visual_insights.ux_issues) if visual_insights.ux_issues else "- Nenhum problema crítico identificado"}

**PRIORIDADES VISUAIS:**
{chr(10).join(f"- {priority}" for priority in visual_priorities) if visual_priorities else "- Sem prioridades específicas identificadas"}

**MOBILE READINESS:**
{mobile_readiness.get("assessment", "Não avaliado")}

{lighthouse_context}

**RECOMENDAÇÕES EXISTENTES:**
{chr(10).join(f"- {rec}" for rec in design_recommendations) if design_recommendations else "- Nenhuma recomendação prévia"}

Forneça uma análise completa incluindo:

1. **PROBLEMAS UX/UI IDENTIFICADOS:**
   - Liste os principais problemas de usabilidade e design
   - Classifique por impacto no usuário (Alto/Médio/Baixo)

2. **PRINCÍPIOS DE DESIGN APLICÁVEIS:**
   - Identifique quais princípios de UX/UI estão sendo violados
   - Hierarquia visual, consistência, feedback, simplicidade, etc.

3. **RECOMENDAÇÕES ESPECÍFICAS DE MELHORIA:**
   - Para cada problema, forneça solução específica
   - Inclua exemplos práticos de implementação
   - Priorize por impacto e facilidade de implementação

4. **MELHORIAS DE EXPERIÊNCIA DO USUÁRIO:**
   - Otimizações de fluxo de navegação
   - Melhorias de interação e feedback
   - Redução de fricção na jornada do usuário

5. **IMPLEMENTAÇÃO RESPONSIVA:**
   - Recomendações para mobile-first design
   - Otimizações para diferentes resoluções
   - Testes necessários em dispositivos

6. **MÉTRICAS DE SUCESSO:**
   - Como medir o impacto das melhorias
   - KPIs de UX/UI relevantes

Foque em soluções práticas e que agreguem valor real ao usuário final.
"""

            # Executar análise com o agente
            response = self.agent.run(analysis_prompt)

            # Preparar resultado estruturado
            result = {
                "agent_type": "UXUIDesignerAgent",
                "visual_insights": visual_insights.model_dump(),
                "visual_priorities": visual_priorities,
                "mobile_readiness": mobile_readiness,
                "overall_visual_score": visual_insights.overall_visual_score,
                "accessibility_visual_score": visual_insights.accessibility_visual_score,
                "agent_analysis": response.content,
                "ui_issues_count": len(visual_insights.ui_issues),
                "ux_issues_count": len(visual_insights.ux_issues),
                "design_category": self._categorize_design_maturity(visual_insights.overall_visual_score),
                "analysis_summary": {
                    "layout_quality": visual_insights.layout_quality,
                    "user_experience": visual_insights.user_experience,
                    "responsive_design": visual_insights.responsive_design,
                    "total_issues": len(visual_insights.ui_issues) + len(visual_insights.ux_issues)
                }
            }

            logger.info(
                f"Análise UX/UI concluída - Score: {visual_insights.overall_visual_score}/100")
            return result

        except Exception as e:
            logger.error(f"Erro na análise UX/UI: {str(e)}")
            raise

    def _categorize_design_maturity(self, visual_score: float) -> str:
        """Categoriza a maturidade do design baseado no score visual"""
        if visual_score >= 90:
            return "Excelente - Design maduro"
        elif visual_score >= 75:
            return "Bom - Design sólido com oportunidades"
        elif visual_score >= 60:
            return "Regular - Necessita melhorias"
        elif visual_score >= 40:
            return "Ruim - Problemas significativos"
        else:
            return "Crítico - Redesign necessário"

    def get_design_recommendations_summary(self, analysis_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Extrai um resumo estruturado das recomendações de design

        Args:
            analysis_result: Resultado da análise UX/UI

        Returns:
            Lista de recomendações estruturadas
        """
        try:
            visual_insights = analysis_result.get("visual_insights", {})
            ui_issues = visual_insights.get("ui_issues", [])
            ux_issues = visual_insights.get("ux_issues", [])

            recommendations = []

            # Recomendações baseadas em issues de UI
            for issue in ui_issues:
                recommendations.append({
                    "category": "Interface Design",
                    "issue": issue,
                    "priority": "Medium" if "baixa qualidade" in issue.lower() else "High",
                    "type": "UI_Issue"
                })

            # Recomendações baseadas em issues de UX
            for issue in ux_issues:
                recommendations.append({
                    "category": "User Experience",
                    "issue": issue,
                    "priority": "High" if "experiência" in issue.lower() else "Medium",
                    "type": "UX_Issue"
                })

            # Recomendação geral baseada no score
            visual_score = visual_insights.get("overall_visual_score", 0)
            if visual_score < 70:
                recommendations.append({
                    "category": "Design Strategy",
                    "issue": f"Score visual baixo ({visual_score}/100) indica necessidade de redesign",
                    "priority": "High",
                    "type": "Strategic"
                })

            return recommendations

        except Exception as e:
            logger.error(f"Erro ao extrair recomendações: {str(e)}")
            return []
