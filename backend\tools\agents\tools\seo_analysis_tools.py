"""
SEO Analysis Tools para Agno

Tools customizadas para análise de SEO e otimização:
- Análise técnica de SEO (meta tags, headings, linking)
- Análise de Core Web Vitals específica para SEO
- Análise de keywords e estratégia de conteúdo
- Análise de backlinks e autoridade de domínio
- Análise de SEO local
- Identificação de oportunidades e problemas críticos
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from ..schemas import (
    SEOInsights,
    SEOTechnicalAnalysis,
    KeywordAnalysis,
    BacklinkProfile,
    CoreWebVitalsAnalysis,
    LocalSEOAnalysis,
    SEOStrategy
)

logger = logging.getLogger(__name__)


class SEOAnalysisTools:
    """
    Tools para análise completa de SEO

    Processa dados de Lighthouse, análise técnica e métricas 
    para gerar insights de SEO e estratégias de otimização.
    """

    def analyze_seo_data(self,
                         lighthouse_data: Dict[str, Any],
                         technical_data: Optional[Dict[str, Any]] = None,
                         keyword_data: Optional[Dict[str, Any]] = None,
                         backlink_data: Optional[Dict[str, Any]] = None,
                         local_data: Optional[Dict[str, Any]] = None) -> SEOInsights:
        """
        Analisa dados de SEO e extrai insights completos

        Args:
            lighthouse_data: Dados do Lighthouse (performance, SEO, accessibility)
            technical_data: Dados técnicos de SEO (meta tags, headings, etc.)
            keyword_data: Dados de keywords e conteúdo
            backlink_data: Dados de backlinks e autoridade
            local_data: Dados de SEO local (opcional)

        Returns:
            Insights completos de SEO
        """
        try:
            logger.info("Iniciando análise completa de SEO")

            # Análise técnica de SEO
            technical_analysis = self._analyze_technical_seo(
                lighthouse_data, technical_data)

            # Análise de keywords
            keyword_analysis = self._analyze_keywords(keyword_data)

            # Análise de backlinks
            backlink_profile = self._analyze_backlinks(backlink_data)

            # Análise de Core Web Vitals
            core_web_vitals = self._analyze_core_web_vitals(lighthouse_data)

            # Análise de SEO local (se disponível)
            local_seo = None
            if local_data:
                local_seo = self._analyze_local_seo(local_data)

            # Calcular scores agregados
            overall_seo_score = self._calculate_overall_seo_score(
                technical_analysis, keyword_analysis, core_web_vitals)

            content_quality_score = self._calculate_content_quality_score(
                keyword_analysis, technical_analysis)

            user_experience_score = self._calculate_ux_score(
                core_web_vitals, lighthouse_data)

            # Identificar problemas e oportunidades
            critical_issues = self._identify_critical_issues(
                technical_analysis, core_web_vitals, keyword_analysis)

            quick_wins = self._identify_quick_wins(
                technical_analysis, lighthouse_data)

            long_term_opportunities = self._identify_long_term_opportunities(
                keyword_analysis, backlink_profile)

            # Análise competitiva
            competitive_advantages = self._identify_competitive_advantages(
                backlink_profile, keyword_analysis)

            competitive_gaps = self._identify_competitive_gaps(
                technical_analysis, keyword_analysis)

            # Determinar fontes de dados
            data_sources = ["Google Lighthouse"]
            if technical_data:
                data_sources.append("Análise Técnica")
            if keyword_data:
                data_sources.append("Análise de Keywords")
            if backlink_data:
                data_sources.append("Análise de Backlinks")
            if local_data:
                data_sources.append("SEO Local")

            insights = SEOInsights(
                technical_analysis=technical_analysis,
                keyword_analysis=keyword_analysis,
                backlink_profile=backlink_profile,
                core_web_vitals=core_web_vitals,
                local_seo=local_seo,
                overall_seo_score=overall_seo_score,
                content_quality_score=content_quality_score,
                user_experience_score=user_experience_score,
                critical_issues=critical_issues,
                quick_wins=quick_wins,
                long_term_opportunities=long_term_opportunities,
                competitive_advantages=competitive_advantages,
                competitive_gaps=competitive_gaps,
                data_sources=data_sources
            )

            logger.info(
                f"Análise de SEO concluída - Score geral: {overall_seo_score}")
            return insights

        except Exception as e:
            logger.error(f"Erro na análise de SEO: {str(e)}")
            raise

    def _analyze_technical_seo(self,
                               lighthouse_data: Dict[str, Any],
                               technical_data: Optional[Dict[str, Any]]) -> SEOTechnicalAnalysis:
        """Analisa aspectos técnicos de SEO"""
        try:
            # Dados base do Lighthouse
            seo_audits = lighthouse_data.get("audits", {})

            # Meta tags score
            meta_tags_score = self._calculate_meta_tags_score(
                seo_audits, technical_data)

            # Heading structure score
            heading_structure_score = self._calculate_heading_structure_score(
                seo_audits, technical_data)

            # Internal linking score
            internal_linking_score = self._calculate_internal_linking_score(
                technical_data)

            # Image optimization score
            image_optimization_score = self._calculate_image_optimization_score(
                seo_audits)

            # Status de robots.txt e sitemap
            robots_txt_status = self._check_robots_txt_status(
                seo_audits, technical_data)
            sitemap_status = self._check_sitemap_status(
                seo_audits, technical_data)

            # Schema markup score
            schema_markup_score = self._calculate_schema_markup_score(
                seo_audits, technical_data)

            # Canonical URLs score
            canonical_urls_score = self._calculate_canonical_urls_score(
                seo_audits, technical_data)

            return SEOTechnicalAnalysis(
                meta_tags_score=meta_tags_score,
                heading_structure_score=heading_structure_score,
                internal_linking_score=internal_linking_score,
                image_optimization_score=image_optimization_score,
                robots_txt_status=robots_txt_status,
                sitemap_status=sitemap_status,
                schema_markup_score=schema_markup_score,
                canonical_urls_score=canonical_urls_score
            )

        except Exception as e:
            logger.error(f"Erro na análise técnica de SEO: {str(e)}")
            # Retornar análise padrão em caso de erro
            return SEOTechnicalAnalysis(
                meta_tags_score=50.0,
                heading_structure_score=50.0,
                internal_linking_score=50.0,
                image_optimization_score=50.0,
                robots_txt_status="Não verificado",
                sitemap_status="Não verificado",
                schema_markup_score=50.0,
                canonical_urls_score=50.0
            )

    def _analyze_keywords(self, keyword_data: Optional[Dict[str, Any]]) -> KeywordAnalysis:
        """Analisa estratégia de keywords"""
        try:
            if not keyword_data:
                # Retornar análise padrão se não houver dados
                return KeywordAnalysis(
                    primary_keywords=[],
                    keyword_density_score=50.0,
                    long_tail_opportunities=[],
                    keyword_cannibalization_issues=[],
                    search_intent_alignment=50.0,
                    competitive_keyword_gaps=[]
                )

            # Extrair keywords primárias
            primary_keywords = keyword_data.get("primary_keywords", [])

            # Calcular densidade de keywords
            keyword_density_score = self._calculate_keyword_density_score(
                keyword_data)

            # Identificar oportunidades de long tail
            long_tail_opportunities = self._identify_long_tail_opportunities(
                keyword_data)

            # Identificar problemas de canibalização
            cannibalization_issues = self._identify_keyword_cannibalization(
                keyword_data)

            # Analisar alinhamento com intenção de busca
            search_intent_alignment = self._calculate_search_intent_alignment(
                keyword_data)

            # Identificar gaps competitivos
            competitive_gaps = self._identify_competitive_keyword_gaps(
                keyword_data)

            return KeywordAnalysis(
                primary_keywords=primary_keywords,
                keyword_density_score=keyword_density_score,
                long_tail_opportunities=long_tail_opportunities,
                keyword_cannibalization_issues=cannibalization_issues,
                search_intent_alignment=search_intent_alignment,
                competitive_keyword_gaps=competitive_gaps
            )

        except Exception as e:
            logger.error(f"Erro na análise de keywords: {str(e)}")
            return KeywordAnalysis(
                primary_keywords=[],
                keyword_density_score=50.0,
                long_tail_opportunities=[],
                keyword_cannibalization_issues=[],
                search_intent_alignment=50.0,
                competitive_keyword_gaps=[]
            )

    def _analyze_backlinks(self, backlink_data: Optional[Dict[str, Any]]) -> BacklinkProfile:
        """Analisa perfil de backlinks"""
        try:
            if not backlink_data:
                # Retornar perfil padrão se não houver dados
                return BacklinkProfile(
                    total_backlinks=0,
                    referring_domains=0,
                    domain_authority_avg=50.0,
                    toxic_links_percentage=0.0,
                    anchor_text_diversity=50.0,
                    link_growth_trend="Sem dados"
                )

            total_backlinks = backlink_data.get("total_backlinks", 0)
            referring_domains = backlink_data.get("referring_domains", 0)

            # Calcular autoridade média dos domínios
            domain_authority_avg = self._calculate_domain_authority_average(
                backlink_data)

            # Calcular porcentagem de links tóxicos
            toxic_links_percentage = self._calculate_toxic_links_percentage(
                backlink_data)

            # Calcular diversidade de anchor text
            anchor_text_diversity = self._calculate_anchor_text_diversity(
                backlink_data)

            # Analisar tendência de crescimento
            link_growth_trend = self._analyze_link_growth_trend(backlink_data)

            return BacklinkProfile(
                total_backlinks=total_backlinks,
                referring_domains=referring_domains,
                domain_authority_avg=domain_authority_avg,
                toxic_links_percentage=toxic_links_percentage,
                anchor_text_diversity=anchor_text_diversity,
                link_growth_trend=link_growth_trend
            )

        except Exception as e:
            logger.error(f"Erro na análise de backlinks: {str(e)}")
            return BacklinkProfile(
                total_backlinks=0,
                referring_domains=0,
                domain_authority_avg=50.0,
                toxic_links_percentage=0.0,
                anchor_text_diversity=50.0,
                link_growth_trend="Erro na análise"
            )

    def _analyze_core_web_vitals(self, lighthouse_data: Dict[str, Any]) -> CoreWebVitalsAnalysis:
        """Analisa Core Web Vitals para SEO"""
        try:
            audits = lighthouse_data.get("audits", {})

            # Extrair métricas do Lighthouse
            lcp_score = self._extract_lighthouse_score(
                audits, "largest-contentful-paint")
            fid_score = self._extract_lighthouse_score(audits, "first-input-delay") or \
                self._extract_lighthouse_score(audits, "max-potential-fid")
            cls_score = self._extract_lighthouse_score(
                audits, "cumulative-layout-shift")

            # Scores de velocidade
            mobile_speed_score = lighthouse_data.get("categories", {}).get(
                "performance", {}).get("score", 0) * 100
            # Usar mesmo valor se não houver dados desktop separados
            desktop_speed_score = mobile_speed_score

            # Score de usabilidade mobile
            mobile_usability_score = lighthouse_data.get("categories", {}).get(
                "accessibility", {}).get("score", 0) * 100

            return CoreWebVitalsAnalysis(
                lcp_score=lcp_score,
                fid_score=fid_score,
                cls_score=cls_score,
                mobile_speed_score=mobile_speed_score,
                desktop_speed_score=desktop_speed_score,
                mobile_usability_score=mobile_usability_score
            )

        except Exception as e:
            logger.error(f"Erro na análise de Core Web Vitals: {str(e)}")
            return CoreWebVitalsAnalysis(
                lcp_score=50.0,
                fid_score=50.0,
                cls_score=50.0,
                mobile_speed_score=50.0,
                desktop_speed_score=50.0,
                mobile_usability_score=50.0
            )

    def _analyze_local_seo(self, local_data: Dict[str, Any]) -> LocalSEOAnalysis:
        """Analisa SEO local"""
        try:
            gmb_score = local_data.get("google_my_business_score", 50.0)
            citations_score = local_data.get("local_citations_score", 50.0)
            nap_consistency = local_data.get("nap_consistency_score", 50.0)
            reviews_score = local_data.get("local_reviews_score", 50.0)
            local_keywords = local_data.get(
                "local_keywords_optimization", 50.0)

            return LocalSEOAnalysis(
                google_my_business_score=gmb_score,
                local_citations_score=citations_score,
                nap_consistency_score=nap_consistency,
                local_reviews_score=reviews_score,
                local_keywords_optimization=local_keywords
            )

        except Exception as e:
            logger.error(f"Erro na análise de SEO local: {str(e)}")
            return LocalSEOAnalysis(
                google_my_business_score=50.0,
                local_citations_score=50.0,
                nap_consistency_score=50.0,
                local_reviews_score=50.0,
                local_keywords_optimization=50.0
            )

    # Métodos de cálculo específicos

    def _calculate_meta_tags_score(self, seo_audits: Dict[str, Any], technical_data: Optional[Dict[str, Any]]) -> float:
        """Calcula score de meta tags"""
        score = 50.0

        # Verificar title tag
        if "document-title" in seo_audits:
            title_audit = seo_audits["document-title"]
            if title_audit.get("score", 0) == 1:
                score += 15

        # Verificar meta description
        if "meta-description" in seo_audits:
            desc_audit = seo_audits["meta-description"]
            if desc_audit.get("score", 0) == 1:
                score += 15

        # Dados técnicos adicionais
        if technical_data:
            if technical_data.get("has_title_tag"):
                score += 10
            if technical_data.get("has_meta_description"):
                score += 10

        return min(score, 100.0)

    def _calculate_heading_structure_score(self, seo_audits: Dict[str, Any], technical_data: Optional[Dict[str, Any]]) -> float:
        """Calcula score da estrutura de headings"""
        score = 50.0

        # Verificar estrutura de headings do Lighthouse
        if "heading-order" in seo_audits:
            heading_audit = seo_audits["heading-order"]
            if heading_audit.get("score", 0) == 1:
                score += 25

        # Dados técnicos adicionais
        if technical_data:
            h1_count = technical_data.get("h1_count", 0)
            has_h2 = technical_data.get("has_h2", False)

            if h1_count == 1:  # H1 único
                score += 15
            elif h1_count > 1:
                score -= 10  # Penalizar múltiplos H1

            if has_h2:
                score += 10

        return min(max(score, 0.0), 100.0)

    def _calculate_internal_linking_score(self, technical_data: Optional[Dict[str, Any]]) -> float:
        """Calcula score de linkagem interna"""
        if not technical_data:
            return 50.0

        score = 50.0

        internal_links_count = technical_data.get("internal_links_count", 0)
        has_breadcrumbs = technical_data.get("has_breadcrumbs", False)

        # Score baseado na quantidade de links internos
        if internal_links_count >= 20:
            score += 20
        elif internal_links_count >= 10:
            score += 15
        elif internal_links_count >= 5:
            score += 10

        # Bonus por breadcrumbs
        if has_breadcrumbs:
            score += 15

        return min(score, 100.0)

    def _calculate_image_optimization_score(self, seo_audits: Dict[str, Any]) -> float:
        """Calcula score de otimização de imagens"""
        score = 50.0

        # Verificar alt text das imagens
        if "image-alt" in seo_audits:
            alt_audit = seo_audits["image-alt"]
            if alt_audit.get("score", 0) == 1:
                score += 25

        # Verificar otimização de imagens
        if "uses-optimized-images" in seo_audits:
            opt_audit = seo_audits["uses-optimized-images"]
            if opt_audit.get("score", 0) > 0.8:
                score += 15

        # Verificar formato moderno de imagens
        if "uses-webp-images" in seo_audits:
            webp_audit = seo_audits["uses-webp-images"]
            if webp_audit.get("score", 0) > 0.8:
                score += 10

        return min(score, 100.0)

    def _check_robots_txt_status(self, seo_audits: Dict[str, Any], technical_data: Optional[Dict[str, Any]]) -> str:
        """Verifica status do robots.txt"""
        if "robots-txt" in seo_audits:
            robots_audit = seo_audits["robots-txt"]
            if robots_audit.get("score", 0) == 1:
                return "Presente e válido"
            else:
                return "Presente com problemas"

        if technical_data and technical_data.get("has_robots_txt"):
            return "Presente"

        return "Não encontrado"

    def _check_sitemap_status(self, seo_audits: Dict[str, Any], technical_data: Optional[Dict[str, Any]]) -> str:
        """Verifica status do sitemap XML"""
        if technical_data:
            if technical_data.get("has_sitemap_xml"):
                return "Presente"
            elif technical_data.get("sitemap_submitted"):
                return "Submetido ao Google"

        return "Não encontrado"

    def _calculate_schema_markup_score(self, seo_audits: Dict[str, Any], technical_data: Optional[Dict[str, Any]]) -> float:
        """Calcula score de dados estruturados"""
        score = 50.0

        # Verificar structured data do Lighthouse
        if "structured-data" in seo_audits:
            structured_audit = seo_audits["structured-data"]
            if structured_audit.get("score", 0) == 1:
                score += 30

        # Dados técnicos adicionais
        if technical_data:
            schema_types = technical_data.get("schema_types", [])
            score += min(len(schema_types) * 5, 20)

        return min(score, 100.0)

    def _calculate_canonical_urls_score(self, seo_audits: Dict[str, Any], technical_data: Optional[Dict[str, Any]]) -> float:
        """Calcula score de URLs canônicas"""
        score = 50.0

        # Verificar canonical do Lighthouse
        if "canonical" in seo_audits:
            canonical_audit = seo_audits["canonical"]
            if canonical_audit.get("score", 0) == 1:
                score += 30

        # Dados técnicos adicionais
        if technical_data and technical_data.get("has_canonical_tags"):
            score += 20

        return min(score, 100.0)

    def _calculate_keyword_density_score(self, keyword_data: Dict[str, Any]) -> float:
        """Calcula score de densidade de keywords"""
        density = keyword_data.get("keyword_density", 0)

        # Densidade ideal entre 0.5% e 2.5%
        if 0.5 <= density <= 2.5:
            return 100.0
        elif 0.3 <= density < 0.5 or 2.5 < density <= 3.5:
            return 75.0
        elif 0.1 <= density < 0.3 or 3.5 < density <= 5.0:
            return 50.0
        else:
            return 25.0

    def _identify_long_tail_opportunities(self, keyword_data: Dict[str, Any]) -> List[str]:
        """Identifica oportunidades de long tail keywords"""
        opportunities = []

        suggested_keywords = keyword_data.get("suggested_keywords", [])
        for keyword in suggested_keywords:
            if len(keyword.split()) >= 3:  # Long tail tem 3+ palavras
                opportunities.append(keyword)

        # Adicionar oportunidades baseadas em perguntas
        question_keywords = keyword_data.get("question_keywords", [])
        opportunities.extend(question_keywords[:5])

        return opportunities[:10]  # Limitar a 10 oportunidades

    def _identify_keyword_cannibalization(self, keyword_data: Dict[str, Any]) -> List[str]:
        """Identifica problemas de canibalização de keywords"""
        issues = []

        duplicate_keywords = keyword_data.get("duplicate_keywords", [])
        for keyword in duplicate_keywords:
            issues.append(f"Múltiplas páginas competindo por: {keyword}")

        return issues[:5]  # Limitar a 5 problemas

    def _calculate_search_intent_alignment(self, keyword_data: Dict[str, Any]) -> float:
        """Calcula alinhamento com intenção de busca"""
        intent_score = keyword_data.get("search_intent_score", 50.0)
        content_relevance = keyword_data.get("content_relevance", 50.0)

        return (intent_score + content_relevance) / 2

    def _identify_competitive_keyword_gaps(self, keyword_data: Dict[str, Any]) -> List[str]:
        """Identifica gaps de keywords competitivas"""
        gaps = []

        competitor_keywords = keyword_data.get("competitor_keywords", [])
        current_keywords = set(keyword_data.get("current_keywords", []))

        for keyword in competitor_keywords:
            if keyword not in current_keywords:
                gaps.append(f"Concorrentes rankeiam para: {keyword}")

        return gaps[:8]  # Limitar a 8 gaps

    def _calculate_domain_authority_average(self, backlink_data: Dict[str, Any]) -> float:
        """Calcula autoridade média dos domínios"""
        domain_authorities = backlink_data.get("domain_authorities", [])
        if domain_authorities:
            return sum(domain_authorities) / len(domain_authorities)
        return 50.0

    def _calculate_toxic_links_percentage(self, backlink_data: Dict[str, Any]) -> float:
        """Calcula porcentagem de links tóxicos"""
        total_links = backlink_data.get("total_backlinks", 1)
        toxic_links = backlink_data.get("toxic_links", 0)

        return (toxic_links / total_links) * 100

    def _calculate_anchor_text_diversity(self, backlink_data: Dict[str, Any]) -> float:
        """Calcula diversidade de anchor text"""
        anchor_texts = backlink_data.get("anchor_texts", [])
        unique_anchors = len(set(anchor_texts))
        total_anchors = len(anchor_texts) if anchor_texts else 1

        diversity = (unique_anchors / total_anchors) * 100
        return min(diversity, 100.0)

    def _analyze_link_growth_trend(self, backlink_data: Dict[str, Any]) -> str:
        """Analisa tendência de crescimento de links"""
        monthly_growth = backlink_data.get("monthly_link_growth", [])

        if not monthly_growth or len(monthly_growth) < 2:
            return "Dados insuficientes"

        recent_growth = sum(
            monthly_growth[-3:]) / 3 if len(monthly_growth) >= 3 else monthly_growth[-1]

        if recent_growth > 10:
            return "Crescimento forte"
        elif recent_growth > 5:
            return "Crescimento moderado"
        elif recent_growth > 0:
            return "Crescimento lento"
        else:
            return "Declínio ou estagnação"

    def _extract_lighthouse_score(self, audits: Dict[str, Any], audit_name: str) -> float:
        """Extrai score do Lighthouse para uma métrica específica"""
        if audit_name in audits:
            audit = audits[audit_name]
            score = audit.get("score")
            if score is not None:
                return score * 100
        return 50.0

    # Métodos de agregação e identificação

    def _calculate_overall_seo_score(self,
                                     technical: SEOTechnicalAnalysis,
                                     keywords: KeywordAnalysis,
                                     vitals: CoreWebVitalsAnalysis) -> float:
        """Calcula score geral de SEO"""
        technical_score = (
            technical.meta_tags_score +
            technical.heading_structure_score +
            technical.internal_linking_score +
            technical.schema_markup_score +
            technical.canonical_urls_score
        ) / 5

        keyword_score = (
            keywords.keyword_density_score +
            keywords.search_intent_alignment
        ) / 2

        vitals_score = (
            vitals.lcp_score +
            vitals.fid_score +
            vitals.cls_score +
            vitals.mobile_speed_score
        ) / 4

        # Peso: 40% técnico, 30% keywords, 30% vitals
        overall = (technical_score * 0.4) + \
            (keyword_score * 0.3) + (vitals_score * 0.3)
        return round(overall, 1)

    def _calculate_content_quality_score(self,
                                         keywords: KeywordAnalysis,
                                         technical: SEOTechnicalAnalysis) -> float:
        """Calcula score de qualidade do conteúdo"""
        keyword_quality = (
            keywords.keyword_density_score +
            keywords.search_intent_alignment
        ) / 2

        content_structure = (
            technical.heading_structure_score +
            technical.meta_tags_score
        ) / 2

        return (keyword_quality + content_structure) / 2

    def _calculate_ux_score(self,
                            vitals: CoreWebVitalsAnalysis,
                            lighthouse_data: Dict[str, Any]) -> float:
        """Calcula score de experiência do usuário"""
        vitals_score = (
            vitals.lcp_score +
            vitals.fid_score +
            vitals.cls_score +
            vitals.mobile_usability_score
        ) / 4

        # Adicionar dados de acessibilidade se disponível
        accessibility_score = lighthouse_data.get("categories", {}).get(
            "accessibility", {}).get("score", 0.5) * 100

        return (vitals_score + accessibility_score) / 2

    def _identify_critical_issues(self,
                                  technical: SEOTechnicalAnalysis,
                                  vitals: CoreWebVitalsAnalysis,
                                  keywords: KeywordAnalysis) -> List[str]:
        """Identifica problemas críticos de SEO"""
        issues = []

        # Problemas técnicos críticos
        if technical.meta_tags_score < 50:
            issues.append("Meta tags ausentes ou inadequadas")

        if technical.heading_structure_score < 40:
            issues.append("Estrutura de headings problemática")

        if technical.robots_txt_status == "Não encontrado":
            issues.append("Arquivo robots.txt não encontrado")

        # Problemas de Core Web Vitals críticos
        if vitals.lcp_score < 50:
            issues.append("LCP (Largest Contentful Paint) muito lento")

        if vitals.cls_score < 50:
            issues.append("CLS (Cumulative Layout Shift) alto")

        if vitals.mobile_speed_score < 50:
            issues.append("Velocidade mobile crítica")

        # Problemas de keywords críticos
        if keywords.keyword_density_score < 30:
            issues.append("Densidade de keywords inadequada")

        if keywords.search_intent_alignment < 40:
            issues.append("Conteúdo desalinhado com intenção de busca")

        return issues

    def _identify_quick_wins(self,
                             technical: SEOTechnicalAnalysis,
                             lighthouse_data: Dict[str, Any]) -> List[str]:
        """Identifica oportunidades de vitórias rápidas"""
        wins = []

        # Quick wins técnicos
        if technical.meta_tags_score < 80:
            wins.append("Otimizar meta descriptions e titles")

        if technical.image_optimization_score < 70:
            wins.append("Adicionar alt text nas imagens")

        if technical.schema_markup_score < 60:
            wins.append("Implementar dados estruturados básicos")

        if technical.sitemap_status == "Não encontrado":
            wins.append("Criar e submeter sitemap XML")

        # Quick wins baseados no Lighthouse
        audits = lighthouse_data.get("audits", {})
        if "meta-description" in audits and audits["meta-description"].get("score", 1) < 1:
            wins.append("Adicionar meta descriptions faltantes")

        if "document-title" in audits and audits["document-title"].get("score", 1) < 1:
            wins.append("Corrigir title tags problemáticos")

        return wins

    def _identify_long_term_opportunities(self,
                                          keywords: KeywordAnalysis,
                                          backlinks: BacklinkProfile) -> List[str]:
        """Identifica oportunidades de longo prazo"""
        opportunities = []

        # Oportunidades de keywords
        if len(keywords.long_tail_opportunities) > 0:
            opportunities.append(
                "Desenvolver conteúdo para long tail keywords")

        if len(keywords.competitive_keyword_gaps) > 0:
            opportunities.append("Competir por keywords dos concorrentes")

        # Oportunidades de backlinks
        if backlinks.total_backlinks < 100:
            opportunities.append("Estratégia agressiva de link building")

        if backlinks.domain_authority_avg < 40:
            opportunities.append("Focar em backlinks de alta autoridade")

        if backlinks.anchor_text_diversity < 60:
            opportunities.append("Diversificar anchor text dos backlinks")

        # Oportunidades de conteúdo
        opportunities.append("Criar cluster de conteúdo temático")
        opportunities.append("Implementar estratégia de E-A-T")

        return opportunities

    def _identify_competitive_advantages(self,
                                         backlinks: BacklinkProfile,
                                         keywords: KeywordAnalysis) -> List[str]:
        """Identifica vantagens competitivas"""
        advantages = []

        # Vantagens de backlinks
        if backlinks.domain_authority_avg > 60:
            advantages.append("Backlinks de alta autoridade")

        if backlinks.toxic_links_percentage < 5:
            advantages.append("Perfil de backlinks limpo")

        if backlinks.anchor_text_diversity > 80:
            advantages.append("Excelente diversidade de anchor text")

        # Vantagens de keywords
        if keywords.search_intent_alignment > 80:
            advantages.append("Forte alinhamento com intenção de busca")

        if keywords.keyword_density_score > 85:
            advantages.append("Otimização de keywords bem executada")

        return advantages

    def _identify_competitive_gaps(self,
                                   technical: SEOTechnicalAnalysis,
                                   keywords: KeywordAnalysis) -> List[str]:
        """Identifica gaps competitivos"""
        gaps = []

        # Gaps técnicos
        if technical.schema_markup_score < 70:
            gaps.append("Implementação limitada de dados estruturados")

        if technical.internal_linking_score < 60:
            gaps.append("Estratégia de linkagem interna fraca")

        # Gaps de keywords
        if len(keywords.competitive_keyword_gaps) > 5:
            gaps.append("Muitos gaps de keywords competitivas")

        if keywords.search_intent_alignment < 60:
            gaps.append("Desalinhamento com intenção de busca")

        return gaps

    def create_seo_strategy(self, seo_insights: SEOInsights) -> SEOStrategy:
        """
        Cria estratégia de SEO baseada nos insights

        Args:
            seo_insights: Insights completos de SEO

        Returns:
            Estratégia de SEO recomendada
        """
        try:
            logger.info("Gerando estratégia de SEO")

            # Ações de curto prazo (1-3 meses)
            short_term = []
            for issue in seo_insights.critical_issues:
                short_term.append({
                    "action": f"Corrigir: {issue}",
                    "priority": "Alta",
                    "effort": "Baixo a Médio",
                    "timeline": "1-2 semanas"
                })

            for win in seo_insights.quick_wins:
                short_term.append({
                    "action": win,
                    "priority": "Média",
                    "effort": "Baixo",
                    "timeline": "1-4 semanas"
                })

            # Ações de médio prazo (3-6 meses)
            medium_term = [
                {
                    "action": "Desenvolver cluster de conteúdo",
                    "priority": "Alta",
                    "effort": "Alto",
                    "timeline": "2-3 meses"
                },
                {
                    "action": "Implementar estratégia de link building",
                    "priority": "Alta",
                    "effort": "Alto",
                    "timeline": "3-6 meses"
                },
                {
                    "action": "Otimizar Core Web Vitals",
                    "priority": "Média",
                    "effort": "Médio",
                    "timeline": "1-2 meses"
                }
            ]

            # Ações de longo prazo (6+ meses)
            long_term = []
            for opportunity in seo_insights.long_term_opportunities:
                long_term.append({
                    "action": opportunity,
                    "priority": "Média",
                    "effort": "Alto",
                    "timeline": "6-12 meses"
                })

            # Keywords prioritárias
            priority_keywords = seo_insights.keyword_analysis.primary_keywords[:10]

            # Estratégia de conteúdo
            content_strategy = {
                "focus": "E-A-T e autoridade tópica",
                "content_types": ["Blog posts", "Guias técnicos", "Case studies"],
                "frequency": "2-3 conteúdos por semana",
                "optimization": "Long tail keywords e intenção de busca"
            }

            # Estratégia de link building
            link_building_strategy = {
                "focus": "Backlinks de alta autoridade",
                "tactics": ["Guest posting", "Digital PR", "Resource page outreach"],
                "target_da": "40+",
                "monthly_goal": "5-10 novos backlinks de qualidade"
            }

            # Melhorias técnicas prioritárias
            technical_improvements = [
                "Implementar dados estruturados",
                "Otimizar Core Web Vitals",
                "Melhorar arquitetura de informação",
                "Implementar HTTPS",
                "Otimizar crawlability"
            ]

            # Resultados esperados
            expected_outcomes = {
                "SEO Score": f"+{20-30} pontos em 6 meses",
                "Organic Traffic": "+30-50% em 6-12 meses",
                "Keyword Rankings": "Top 10 para keywords prioritárias",
                "Core Web Vitals": "Todos na zona verde",
                "Domain Authority": f"+{5-10} pontos em 12 meses"
            }

            # KPIs de monitoramento
            monitoring_kpis = [
                "Organic traffic growth",
                "Keyword ranking positions",
                "Core Web Vitals scores",
                "Backlink profile quality",
                "Technical SEO score",
                "Click-through rates",
                "Bounce rate e tempo na página"
            ]

            strategy = SEOStrategy(
                short_term_actions=short_term,
                medium_term_actions=medium_term,
                long_term_actions=long_term,
                priority_keywords=priority_keywords,
                content_strategy=content_strategy,
                link_building_strategy=link_building_strategy,
                technical_improvements=technical_improvements,
                expected_outcomes=expected_outcomes,
                monitoring_kpis=monitoring_kpis
            )

            logger.info("Estratégia de SEO gerada com sucesso")
            return strategy

        except Exception as e:
            logger.error(f"Erro ao gerar estratégia de SEO: {str(e)}")
            raise
