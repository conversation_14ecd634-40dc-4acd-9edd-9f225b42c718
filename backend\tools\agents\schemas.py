"""
Schemas Pydantic para Sistema de Agentes Agno

Define modelos de dados para validação e serialização:
- Entrada de dados técnicos
- Recomendações arquiteturais  
- Resultados de análise
- Configurações de agentes
"""

from typing import Dict, List, Any, Optional, Literal
from datetime import datetime
from pydantic import BaseModel, Field, validator, field_validator
from enum import Enum
import uuid


class PriorityLevel(str, Enum):
    """Níveis de prioridade para recomendações"""
    CRITICAL = "crítica"
    HIGH = "alta"
    MEDIUM = "média"
    LOW = "baixa"


class EffortLevel(str, Enum):
    """Níveis de esforço para implementação"""
    LOW = "baixo"
    MEDIUM = "médio"
    HIGH = "alto"
    VERY_HIGH = "muito alto"


class RecommendationCategory(str, Enum):
    """Categorias de recomendações arquiteturais"""
    PERFORMANCE = "Performance"
    ACCESSIBILITY = "Acessibilidade"
    SECURITY = "Segurança"
    SEO = "SEO"
    SCALABILITY = "Escalabilidade"
    MAINTAINABILITY = "Manutenibilidade"
    USER_EXPERIENCE = "Experiência do Usuário"
    INFRASTRUCTURE = "Infraestrutura"
    UI_DESIGN = "Design de Interface"
    UX_DESIGN = "Design de Experiência"
    VISUAL_DESIGN = "Design Visual"
    RESPONSIVE_DESIGN = "Design Responsivo"
    # Categorias específicas do Product Owner Agent
    PRODUCT_STRATEGY = "Estratégia de Produto"
    FEATURE_PRIORITIZATION = "Priorização de Funcionalidades"
    ROADMAP_PLANNING = "Planejamento de Roadmap"
    MARKET_OPPORTUNITY = "Oportunidade de Mercado"
    COMPETITIVE_ANALYSIS = "Análise Competitiva"
    USER_RESEARCH = "Pesquisa com Usuários"
    BUSINESS_MODEL = "Modelo de Negócio"
    # Categorias específicas do SEO Specialist Agent
    SEO_TECHNICAL = "SEO Técnico"
    SEO_CONTENT = "SEO de Conteúdo"
    SEO_PERFORMANCE = "Performance SEO"
    KEYWORD_STRATEGY = "Estratégia de Keywords"
    LINK_BUILDING = "Link Building"
    LOCAL_SEO = "SEO Local"
    STRUCTURED_DATA = "Dados Estruturados"
    MOBILE_SEO = "SEO Mobile"


class TechnicalDiagnosisInput(BaseModel):
    """
    Modelo de entrada para dados de diagnóstico técnico

    Recebe dados estruturados de:
    - Lighthouse (performance, acessibilidade, SEO, best practices)
    - Análise visual (screenshots, UX analysis)
    - Metadata da empresa e setor
    """
    # Dados Lighthouse
    lighthouse_data: Dict[str, Any] = Field(
        ...,
        description="Dados completos da análise Lighthouse"
    )

    # Análise visual
    visual_analysis: Dict[str, Any] = Field(
        ...,
        description="Análise visual dos screenshots por IA"
    )

    # Relatório consolidado
    consolidated_report: Dict[str, Any] = Field(
        ...,
        description="Relatório consolidado com score geral e métricas"
    )

    # Metadata da empresa
    company_metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Metadados da empresa (nome, setor, site, etc.)"
    )

    # Stack tecnológico detectado (se disponível)
    tech_stack: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Stack tecnológico detectado"
    )

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


class TechnicalRecommendation(BaseModel):
    """
    Modelo para uma recomendação técnica específica
    """
    category: RecommendationCategory = Field(
        ...,
        description="Categoria da recomendação"
    )

    title: str = Field(
        ...,
        min_length=5,
        max_length=100,
        description="Título conciso da recomendação"
    )

    description: str = Field(
        ...,
        min_length=20,
        max_length=500,
        description="Descrição detalhada do problema e solução"
    )

    technologies: List[str] = Field(
        default_factory=list,
        description="Tecnologias/ferramentas recomendadas"
    )

    implementation_steps: List[str] = Field(
        default_factory=list,
        description="Passos para implementação"
    )

    effort: EffortLevel = Field(
        ...,
        description="Nível de esforço estimado"
    )

    priority: PriorityLevel = Field(
        ...,
        description="Prioridade da implementação"
    )

    expected_impact: str = Field(
        ...,
        min_length=10,
        max_length=200,
        description="Impacto esperado da implementação"
    )

    estimated_timeframe: str = Field(
        ...,
        description="Estimativa de tempo para implementação (ex: '1-2 semanas')"
    )

    metrics_improvement: Optional[Dict[str, str]] = Field(
        default=None,
        description="Melhorias métricas esperadas (ex: {'performance': '+20 pontos'})"
    )


class ArchitecturalRecommendation(BaseModel):
    """
    Modelo completo para recomendações arquiteturais do agente
    """
    # Análise de problemas identificados
    identified_problems: List[str] = Field(
        ...,
        description="Lista de problemas técnicos identificados"
    )

    # Score geral atual
    current_overall_score: float = Field(
        ...,
        ge=0.0,
        le=100.0,
        description="Score geral atual (0-100)"
    )

    # Impacto no negócio
    business_impact: str = Field(
        ...,
        min_length=20,
        max_length=300,
        description="Descrição do impacto nos negócios"
    )

    # Recomendações técnicas específicas
    technical_recommendations: List[TechnicalRecommendation] = Field(
        ...,
        description="Lista de recomendações técnicas priorizadas"
    )

    # Análise de stack tecnológico
    stack_analysis: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Análise do stack tecnológico atual"
    )

    # Recomendações de arquitetura geral
    architectural_principles: List[str] = Field(
        default_factory=list,
        description="Princípios arquiteturais recomendados"
    )

    # Métricas de melhoria esperadas
    expected_improvements: Dict[str, str] = Field(
        default_factory=dict,
        description="Melhorias esperadas por categoria"
    )

    # Timeline geral de implementação
    implementation_timeline: str = Field(
        ...,
        description="Timeline geral para implementação das melhorias"
    )

    @field_validator('technical_recommendations')
    @classmethod
    def validate_recommendations_priority(cls, v):
        """Valida que existe pelo menos uma recomendação de alta prioridade"""
        if not v:
            raise ValueError(
                "Deve existir pelo menos uma recomendação técnica")

        if len(v) > 10:
            raise ValueError("Máximo de 10 recomendações técnicas permitidas")

        high_priority_count = sum(
            1 for rec in v
            if rec.priority in [PriorityLevel.CRITICAL, PriorityLevel.HIGH]
        )
        if high_priority_count == 0:
            raise ValueError(
                "Deve existir pelo menos uma recomendação de prioridade alta ou crítica")
        return v


class AnalysisResult(BaseModel):
    """
    Modelo completo para resultado de análise do agente
    """
    # ID único da análise
    analysis_id: str = Field(
        ...,
        description="ID único da análise"
    )

    # Informações da empresa analisada
    company_info: Dict[str, Any] = Field(
        ...,
        description="Informações da empresa analisada"
    )

    # Recomendações arquiteturais
    recommendations: ArchitecturalRecommendation = Field(
        ...,
        description="Recomendações arquiteturais geradas"
    )

    # Metadata da análise
    analysis_metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Metadados da análise (timestamp, modelo usado, etc.)"
    )

    # Score de confiança da análise
    confidence_score: float = Field(
        default=0.8,
        ge=0.0,
        le=1.0,
        description="Score de confiança da análise (0-1)"
    )

    # Timestamp da análise
    created_at: datetime = Field(
        default_factory=datetime.utcnow,
        description="Timestamp de criação da análise"
    )

    # Versão do agente/modelo usado
    agent_version: str = Field(
        default="1.0",
        description="Versão do agente que gerou a análise"
    )


class AgentConfig(BaseModel):
    """
    Configuração para agentes Agno
    """
    # Configurações de modelo IA
    model_name: str = Field(
        default="command-r-plus",
        description="Nome do modelo de IA a usar"
    )

    temperature: float = Field(
        default=0.3,
        ge=0.0,
        le=1.0,
        description="Temperatura para geração de texto"
    )

    max_tokens: int = Field(
        default=4000,
        ge=100,
        le=8000,
        description="Máximo de tokens para resposta"
    )

    # Configurações de análise
    min_confidence_score: float = Field(
        default=0.7,
        ge=0.0,
        le=1.0,
        description="Score mínimo de confiança para aceitar análise"
    )

    max_recommendations: int = Field(
        default=8,
        ge=1,
        le=15,
        description="Número máximo de recomendações a gerar"
    )

    # Configurações de retry
    max_retries: int = Field(
        default=3,
        ge=1,
        le=10,
        description="Número máximo de tentativas"
    )

    retry_delay: float = Field(
        default=1.0,
        ge=0.1,
        le=10.0,
        description="Delay entre tentativas (segundos)"
    )

    # Debug/logging
    debug_mode: bool = Field(
        default=False,
        description="Modo debug habilitado"
    )

    log_level: Literal["DEBUG", "INFO", "WARNING", "ERROR"] = Field(
        default="INFO",
        description="Nível de logging"
    )


# ============================================================================
# SCHEMAS ESPECÍFICOS PARA SEO SPECIALIST AGENT
# ============================================================================

class SEOTechnicalAnalysis(BaseModel):
    """Análise técnica de SEO"""
    meta_tags_score: float = Field(
        ..., ge=0.0, le=100.0, description="Score de meta tags (0-100)"
    )
    heading_structure_score: float = Field(
        ..., ge=0.0, le=100.0, description="Score da estrutura de headings (0-100)"
    )
    internal_linking_score: float = Field(
        ..., ge=0.0, le=100.0, description="Score de linkagem interna (0-100)"
    )
    image_optimization_score: float = Field(
        ..., ge=0.0, le=100.0, description="Score de otimização de imagens (0-100)"
    )
    robots_txt_status: str = Field(
        ..., description="Status do robots.txt"
    )
    sitemap_status: str = Field(
        ..., description="Status do sitemap XML"
    )
    schema_markup_score: float = Field(
        ..., ge=0.0, le=100.0, description="Score de dados estruturados (0-100)"
    )
    canonical_urls_score: float = Field(
        ..., ge=0.0, le=100.0, description="Score de URLs canônicas (0-100)"
    )


class KeywordAnalysis(BaseModel):
    """Análise de estratégia de keywords"""
    primary_keywords: List[str] = Field(
        default_factory=list, description="Keywords primárias identificadas"
    )
    keyword_density_score: float = Field(
        ..., ge=0.0, le=100.0, description="Score de densidade de keywords (0-100)"
    )
    long_tail_opportunities: List[str] = Field(
        default_factory=list, description="Oportunidades de long tail keywords"
    )
    keyword_cannibalization_issues: List[str] = Field(
        default_factory=list, description="Problemas de canibalização de keywords"
    )
    search_intent_alignment: float = Field(
        ..., ge=0.0, le=100.0, description="Alinhamento com intenção de busca (0-100)"
    )
    competitive_keyword_gaps: List[str] = Field(
        default_factory=list, description="Gaps de keywords competitivas"
    )


class BacklinkProfile(BaseModel):
    """Perfil de backlinks"""
    total_backlinks: int = Field(
        ..., ge=0, description="Total de backlinks"
    )
    referring_domains: int = Field(
        ..., ge=0, description="Domínios referenciadores únicos"
    )
    domain_authority_avg: float = Field(
        ..., ge=0.0, le=100.0, description="Autoridade média dos domínios (0-100)"
    )
    toxic_links_percentage: float = Field(
        ..., ge=0.0, le=100.0, description="Porcentagem de links tóxicos (0-100)"
    )
    anchor_text_diversity: float = Field(
        ..., ge=0.0, le=100.0, description="Diversidade de anchor text (0-100)"
    )
    link_growth_trend: str = Field(
        ..., description="Tendência de crescimento de links"
    )


class CoreWebVitalsAnalysis(BaseModel):
    """Análise de Core Web Vitals para SEO"""
    lcp_score: float = Field(
        ..., ge=0.0, le=100.0, description="Score LCP - Largest Contentful Paint (0-100)"
    )
    fid_score: float = Field(
        ..., ge=0.0, le=100.0, description="Score FID - First Input Delay (0-100)"
    )
    cls_score: float = Field(
        ..., ge=0.0, le=100.0, description="Score CLS - Cumulative Layout Shift (0-100)"
    )
    mobile_speed_score: float = Field(
        ..., ge=0.0, le=100.0, description="Score de velocidade mobile (0-100)"
    )
    desktop_speed_score: float = Field(
        ..., ge=0.0, le=100.0, description="Score de velocidade desktop (0-100)"
    )
    mobile_usability_score: float = Field(
        ..., ge=0.0, le=100.0, description="Score de usabilidade mobile (0-100)"
    )


class LocalSEOAnalysis(BaseModel):
    """Análise de SEO local"""
    google_my_business_score: float = Field(
        ..., ge=0.0, le=100.0, description="Score do Google My Business (0-100)"
    )
    local_citations_score: float = Field(
        ..., ge=0.0, le=100.0, description="Score de citações locais (0-100)"
    )
    nap_consistency_score: float = Field(
        ..., ge=0.0, le=100.0, description="Score de consistência NAP (0-100)"
    )
    local_reviews_score: float = Field(
        ..., ge=0.0, le=100.0, description="Score de avaliações locais (0-100)"
    )
    local_keywords_optimization: float = Field(
        ..., ge=0.0, le=100.0, description="Otimização para keywords locais (0-100)"
    )


class SEOInsights(BaseModel):
    """
    Schema principal para insights de análise SEO
    Similar ao ProductInsights para manter consistência
    """
    # Análises técnicas
    technical_analysis: SEOTechnicalAnalysis = Field(
        ..., description="Análise técnica completa de SEO"
    )

    # Análise de keywords
    keyword_analysis: KeywordAnalysis = Field(
        ..., description="Análise de estratégia de keywords"
    )

    # Perfil de backlinks
    backlink_profile: BacklinkProfile = Field(
        ..., description="Análise de perfil de backlinks"
    )

    # Core Web Vitals
    core_web_vitals: CoreWebVitalsAnalysis = Field(
        ..., description="Análise de Core Web Vitals"
    )

    # SEO Local (opcional)
    local_seo: Optional[LocalSEOAnalysis] = Field(
        default=None, description="Análise de SEO local (se aplicável)"
    )

    # Scores agregados
    overall_seo_score: float = Field(
        ..., ge=0.0, le=100.0, description="Score geral de SEO (0-100)"
    )

    content_quality_score: float = Field(
        ..., ge=0.0, le=100.0, description="Score de qualidade do conteúdo (0-100)"
    )

    user_experience_score: float = Field(
        ..., ge=0.0, le=100.0, description="Score de experiência do usuário (0-100)"
    )

    # Oportunidades e problemas
    critical_issues: List[str] = Field(
        default_factory=list, description="Problemas críticos identificados"
    )

    quick_wins: List[str] = Field(
        default_factory=list, description="Oportunidades de vitórias rápidas"
    )

    long_term_opportunities: List[str] = Field(
        default_factory=list, description="Oportunidades de longo prazo"
    )

    # Análise competitiva
    competitive_advantages: List[str] = Field(
        default_factory=list, description="Vantagens competitivas identificadas"
    )

    competitive_gaps: List[str] = Field(
        default_factory=list, description="Gaps competitivos a serem explorados"
    )

    # Metadata
    analysis_date: datetime = Field(
        default_factory=datetime.utcnow, description="Data da análise"
    )

    data_sources: List[str] = Field(
        default_factory=list, description="Fontes de dados utilizadas"
    )


class SEOStrategy(BaseModel):
    """Schema para estratégia de SEO recomendada"""
    short_term_actions: List[Dict[str, Any]] = Field(
        default_factory=list, description="Ações de curto prazo (1-3 meses)"
    )

    medium_term_actions: List[Dict[str, Any]] = Field(
        default_factory=list, description="Ações de médio prazo (3-6 meses)"
    )

    long_term_actions: List[Dict[str, Any]] = Field(
        default_factory=list, description="Ações de longo prazo (6+ meses)"
    )

    priority_keywords: List[str] = Field(
        default_factory=list, description="Keywords prioritárias para foco"
    )

    content_strategy: Dict[str, Any] = Field(
        default_factory=dict, description="Estratégia de conteúdo recomendada"
    )

    link_building_strategy: Dict[str, Any] = Field(
        default_factory=dict, description="Estratégia de link building"
    )

    technical_improvements: List[str] = Field(
        default_factory=list, description="Melhorias técnicas prioritárias"
    )

    expected_outcomes: Dict[str, str] = Field(
        default_factory=dict, description="Resultados esperados por categoria"
    )

    monitoring_kpis: List[str] = Field(
        default_factory=list, description="KPIs para monitoramento"
    )


class ProjectCardStatus(str, Enum):
    """Status de um card de projeto"""
    NOVO = "novo"
    EM_ANDAMENTO = "em_andamento"
    REVISAO = "revisao"
    CONCLUIDO = "concluido"
    CANCELADO = "cancelado"
    PAUSADO = "pausado"


class ProjectCard(BaseModel):
    """
    Schema para cards de projetos gerados automaticamente

    Representa uma sugestão de projeto baseada na análise consolidada
    dos agentes especializados do time Agno.
    """
    # Identificação única
    card_id: str = Field(
        default_factory=lambda: str(uuid.uuid4()),
        description="ID único do card de projeto"
    )

    # Informações básicas
    status: ProjectCardStatus = Field(
        default=ProjectCardStatus.NOVO,
        description="Status atual do card de projeto"
    )

    title: str = Field(
        ...,
        min_length=5,
        max_length=50,
        description="Título claro e específico do projeto"
    )

    client: str = Field(
        ...,
        min_length=2,
        max_length=100,
        description="Nome da empresa cliente"
    )

    sector: str = Field(
        ...,
        min_length=2,
        max_length=50,
        description="Setor de atuação da empresa"
    )

    summary: str = Field(
        ...,
        min_length=20,
        max_length=90,
        description="Resumo conciso do objetivo do projeto"
    )

    tags: List[str] = Field(...,
                            description="Exatamente 3 tags que categorizam o projeto")

    @field_validator('tags')
    @classmethod
    def validate_tags_count(cls, v):
        """Validar que há exatamente 3 tags"""
        if len(v) != 3:
            raise ValueError("Deve haver exatamente 3 tags")
        return v

    # Progresso e equipe
    progress: int = Field(
        default=0,
        ge=0,
        le=100,
        description="Progresso do projeto em porcentagem (0-100)"
    )

    team: str = Field(
        default="Não definida",
        description="Equipe responsável pelo projeto"
    )

    # Metadados
    client_id: str = Field(
        ...,
        description="ID do cliente no sistema"
    )

    source_analysis_id: str = Field(
        ...,
        description="ID da análise consolidada que gerou este card"
    )

    created_at: datetime = Field(
        default_factory=datetime.utcnow,
        description="Data e hora de criação do card"
    )

    updated_at: datetime = Field(
        default_factory=datetime.utcnow,
        description="Data e hora da última atualização"
    )

    # Detalhes do projeto (opcionais)
    priority: PriorityLevel = Field(
        default=PriorityLevel.MEDIUM,
        description="Prioridade do projeto"
    )

    estimated_effort: EffortLevel = Field(
        default=EffortLevel.MEDIUM,
        description="Nível de esforço estimado"
    )

    estimated_duration: Optional[str] = Field(
        default=None,
        description="Duração estimada do projeto (ex: '3-6 meses')"
    )

    recommendations_used: List[str] = Field(
        default_factory=list,
        description="IDs das recomendações que originaram este projeto"
    )

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ProjectCardsResponse(BaseModel):
    """
    Response para geração de múltiplos cards de projetos
    """
    cards: List[ProjectCard] = Field(
        ...,
        description="Lista de cards de projetos gerados"
    )

    generation_metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Metadados sobre o processo de geração"
    )

    total_cards: int = Field(
        ...,
        ge=0,
        description="Número total de cards gerados"
    )

    source_analysis_id: str = Field(
        ...,
        description="ID da análise que originou os cards"
    )

    generated_at: datetime = Field(
        default_factory=datetime.utcnow,
        description="Timestamp de geração dos cards"
    )

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
