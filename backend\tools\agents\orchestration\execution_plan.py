"""
Plano de Execução para Orquestração de Agentes

Gerencia a sequência de execução dos agentes, suas dependências
e a lógica de validação do plano.
"""

from typing import Dict, List, Set, Optional, Any
import logging
from dataclasses import dataclass, field
from .schemas import ExecutionPhase, OrchestrationConfig

logger = logging.getLogger(__name__)


@dataclass
class PhaseExecutionInfo:
    """Informações de execução de uma fase"""
    phase: ExecutionPhase
    status: str = "pending"  # pending, running, completed, failed
    start_time: Optional[str] = None
    end_time: Optional[str] = None
    completed_agents: List[str] = field(default_factory=list)
    failed_agents: List[str] = field(default_factory=list)


class ExecutionPlan:
    """
    Gerencia o plano de execução da orquestração.

    Responsável por:
    - Validar dependências entre fases
    - Determinar ordem de execução
    - Controlar estado das fases
    - Identificar próximas fases executáveis
    """

    def __init__(self, config: OrchestrationConfig):
        self.config = config
        self.phases_info: Dict[int, PhaseExecutionInfo] = {}

        # Inicializar informações das fases
        for phase in config.phases:
            self.phases_info[phase.phase_id] = PhaseExecutionInfo(phase=phase)

        # Validar plano na inicialização
        self._validate_execution_plan()

        logger.info(f"ExecutionPlan criado com {len(config.phases)} fases")

    def _validate_execution_plan(self) -> bool:
        """
        Valida o plano de execução verificando:
        - Dependências circulares
        - Fases dependentes existem
        - Agentes únicos por fase
        """
        # Verificar dependências circulares
        if self._has_circular_dependencies():
            raise ValueError(
                "Dependências circulares detectadas no plano de execução")

        # Verificar se todas as dependências existem
        for phase in self.config.phases:
            for dep_id in phase.dependencies:
                if int(dep_id) not in self.phases_info:
                    raise ValueError(
                        f"Fase {phase.phase_id} depende da fase {dep_id} que não existe")

        # Verificar agentes únicos
        all_agents = []
        for phase in self.config.phases:
            all_agents.extend(phase.agents)

        if len(all_agents) != len(set(all_agents)):
            duplicates = [
                agent for agent in all_agents if all_agents.count(agent) > 1]
            raise ValueError(f"Agentes duplicados encontrados: {duplicates}")

        logger.info("Plano de execução validado com sucesso")
        return True

    def validate(self) -> bool:
        """
        Método público para validação do plano de execução

        Returns:
            True se o plano é válido
        """
        return self._validate_execution_plan()

    def _has_circular_dependencies(self) -> bool:
        """Detecta dependências circulares usando algoritmo DFS"""
        visited = set()
        rec_stack = set()

        def has_cycle(phase_id: int) -> bool:
            visited.add(phase_id)
            rec_stack.add(phase_id)

            # Buscar fase
            phase = None
            for p in self.config.phases:
                if p.phase_id == phase_id:
                    phase = p
                    break

            if phase:
                for dep_str in phase.dependencies:
                    dep_id = int(dep_str)
                    if dep_id not in visited:
                        if has_cycle(dep_id):
                            return True
                    elif dep_id in rec_stack:
                        return True

            rec_stack.remove(phase_id)
            return False

        for phase in self.config.phases:
            if phase.phase_id not in visited:
                if has_cycle(phase.phase_id):
                    return True

        return False

    def get_ready_phases(self) -> List[ExecutionPhase]:
        """
        Retorna fases que estão prontas para execução
        (todas suas dependências foram completadas)
        """
        ready_phases = []

        for phase in self.config.phases:
            phase_info = self.phases_info[phase.phase_id]

            # Pular se já está executando ou completa
            if phase_info.status in ["running", "completed"]:
                continue

            # Verificar se todas as dependências foram completadas
            dependencies_met = True
            for dep_id_str in phase.dependencies:
                dep_id = int(dep_id_str)
                dep_info = self.phases_info.get(dep_id)

                if not dep_info or dep_info.status != "completed":
                    dependencies_met = False
                    break

            if dependencies_met:
                ready_phases.append(phase)
                logger.debug(
                    f"Fase {phase.phase_id} ({phase.name}) pronta para execução")

        return ready_phases

    def get_next_agents(self) -> List[str]:
        """
        Retorna próximos agentes que podem ser executados
        baseado nas fases prontas
        """
        ready_phases = self.get_ready_phases()
        next_agents = []

        for phase in ready_phases:
            phase_info = self.phases_info[phase.phase_id]

            # Agentes ainda não executados nesta fase
            remaining_agents = [
                agent for agent in phase.agents
                if agent not in phase_info.completed_agents
                and agent not in phase_info.failed_agents
            ]

            next_agents.extend(remaining_agents)

        return next_agents

    def mark_phase_started(self, phase_id: int):
        """Marca fase como iniciada"""
        if phase_id in self.phases_info:
            self.phases_info[phase_id].status = "running"
            logger.info(f"Fase {phase_id} marcada como iniciada")

    def mark_agent_completed(self, agent_name: str, success: bool = True):
        """
        Marca agente como completado e atualiza status da fase

        Args:
            agent_name: Nome do agente
            success: Se foi executado com sucesso
        """
        # Encontrar fase do agente
        agent_phase = None
        for phase in self.config.phases:
            if agent_name in phase.agents:
                agent_phase = phase
                break

        if not agent_phase:
            logger.warning(f"Fase não encontrada para agente {agent_name}")
            return

        phase_info = self.phases_info[agent_phase.phase_id]

        # Adicionar à lista apropriada
        if success and agent_name not in phase_info.completed_agents:
            phase_info.completed_agents.append(agent_name)
            logger.info(f"Agente {agent_name} marcado como completado")
        elif not success and agent_name not in phase_info.failed_agents:
            phase_info.failed_agents.append(agent_name)
            logger.warning(f"Agente {agent_name} marcado como falha")

        # Verificar se fase está completa
        self._check_phase_completion(agent_phase.phase_id)

    def _check_phase_completion(self, phase_id: int):
        """Verifica se uma fase foi completada"""
        phase_info = self.phases_info[phase_id]
        phase = phase_info.phase

        total_agents = len(phase.agents)
        completed_agents = len(phase_info.completed_agents)
        failed_agents = len(phase_info.failed_agents)

        # Fase completa se todos os agentes foram processados
        if completed_agents + failed_agents == total_agents:
            if failed_agents == 0:
                phase_info.status = "completed"
                logger.info(
                    f"Fase {phase_id} ({phase.name}) completada com sucesso")
            else:
                # Determinar se continuar baseado na configuração
                if self.config.continue_on_failure and completed_agents > 0:
                    phase_info.status = "completed"
                    logger.warning(
                        f"Fase {phase_id} completada com {failed_agents} falhas "
                        f"({completed_agents} sucessos)"
                    )
                else:
                    phase_info.status = "failed"
                    logger.error(f"Fase {phase_id} falhou")

    def get_execution_summary(self) -> Dict[str, Any]:
        """Retorna resumo do estado da execução"""
        total_phases = len(self.config.phases)
        completed_phases = len(
            [p for p in self.phases_info.values() if p.status == "completed"])
        running_phases = len(
            [p for p in self.phases_info.values() if p.status == "running"])
        failed_phases = len(
            [p for p in self.phases_info.values() if p.status == "failed"])

        # Contar agentes
        total_agents = sum(len(phase.agents) for phase in self.config.phases)
        completed_agents = sum(len(info.completed_agents)
                               for info in self.phases_info.values())
        failed_agents = sum(len(info.failed_agents)
                            for info in self.phases_info.values())

        return {
            "total_phases": total_phases,
            "completed_phases": completed_phases,
            "running_phases": running_phases,
            "failed_phases": failed_phases,
            "total_agents": total_agents,
            "completed_agents": completed_agents,
            "failed_agents": failed_agents,
            "progress_percentage": round((completed_agents / total_agents * 100) if total_agents > 0 else 0, 2)
        }

    def get_phase_by_agent(self, agent_name: str) -> Optional[ExecutionPhase]:
        """Retorna a fase que contém um agente específico"""
        for phase in self.config.phases:
            if agent_name in phase.agents:
                return phase
        return None

    def is_execution_complete(self) -> bool:
        """Verifica se toda a execução foi completada"""
        for phase_info in self.phases_info.values():
            if phase_info.status not in ["completed", "failed"]:
                return False
        return True

    def has_critical_failure(self) -> bool:
        """
        Verifica se há falha crítica que deve parar a execução
        (quando continue_on_failure=False e há falhas)
        """
        if self.config.continue_on_failure:
            return False

        return any(
            phase_info.status == "failed"
            for phase_info in self.phases_info.values()
        )

    def get_blocked_phases(self) -> List[ExecutionPhase]:
        """Retorna fases que estão bloqueadas por dependências não atendidas"""
        blocked_phases = []

        for phase in self.config.phases:
            phase_info = self.phases_info[phase.phase_id]

            if phase_info.status == "pending":
                # Verificar dependências
                blocked_by_deps = False
                for dep_id_str in phase.dependencies:
                    dep_id = int(dep_id_str)
                    dep_info = self.phases_info.get(dep_id)

                    if not dep_info or dep_info.status != "completed":
                        blocked_by_deps = True
                        break

                if blocked_by_deps:
                    blocked_phases.append(phase)

        return blocked_phases

    def reset_phase(self, phase_id: int):
        """Reset uma fase para permitir reexecução"""
        if phase_id in self.phases_info:
            phase_info = self.phases_info[phase_id]
            phase_info.status = "pending"
            phase_info.completed_agents.clear()
            phase_info.failed_agents.clear()
            phase_info.start_time = None
            phase_info.end_time = None
            logger.info(f"Fase {phase_id} resetada para reexecução")


class ExecutionPlanBuilder:
    """Builder para criar planos de execução customizados"""

    @staticmethod
    def create_default_plan() -> OrchestrationConfig:
        """Cria plano padrão otimizado para Team Agno"""
        return OrchestrationConfig.get_default_config()

    @staticmethod
    def create_quick_analysis_plan() -> OrchestrationConfig:
        """Cria plano para análise rápida (apenas agentes essenciais)"""
        quick_phases = [
            ExecutionPhase(
                phase_id=1,
                name="Análise Rápida",
                agents=["technical_architect", "uxui_designer"],
                dependencies=[],
                parallel=True,
                timeout_seconds=180
            )
        ]

        return OrchestrationConfig(
            phases=quick_phases,
            max_retries_per_agent=2,
            timeout_per_agent_seconds=90,
            continue_on_failure=True,
            enable_parallel_execution=True
        )

    @staticmethod
    def create_sequential_plan() -> OrchestrationConfig:
        """Cria plano totalmente sequencial (sem paralelismo)"""
        sequential_phases = [
            ExecutionPhase(
                phase_id=1,
                name="Technical Architect",
                agents=["technical_architect"],
                dependencies=[],
                parallel=False
            ),
            ExecutionPhase(
                phase_id=2,
                name="UX/UI Designer",
                agents=["uxui_designer"],
                dependencies=["1"],
                parallel=False
            ),
            ExecutionPhase(
                phase_id=3,
                name="Product Owner",
                agents=["product_owner"],
                dependencies=["1", "2"],
                parallel=False
            ),
            ExecutionPhase(
                phase_id=4,
                name="SEO Specialist",
                agents=["seo_specialist"],
                dependencies=["1", "2", "3"],
                parallel=False
            ),
            ExecutionPhase(
                phase_id=5,
                name="Benchmark Agent",
                agents=["benchmark_agent"],
                dependencies=["1", "2", "3", "4"],
                parallel=False
            ),
            ExecutionPhase(
                phase_id=6,
                name="Technical Writer",
                agents=["technical_writer"],
                dependencies=["1", "2", "3", "4", "5"],
                parallel=False
            )
        ]

        return OrchestrationConfig(phases=sequential_phases)

    @staticmethod
    def create_custom_plan(
        agents_config: Dict[str, List[str]],
        enable_parallel: bool = True
    ) -> OrchestrationConfig:
        """
        Cria plano customizado baseado em configuração de agentes

        Args:
            agents_config: Dict com fases e seus agentes
                Exemplo: {
                    "phase_1": ["technical_architect", "uxui_designer"],
                    "phase_2": ["product_owner", "seo_specialist"]
                }
            enable_parallel: Se fases podem executar em paralelo
        """
        phases = []

        for i, (phase_name, agents) in enumerate(agents_config.items(), 1):
            dependencies = [str(i - 1)] if i > 1 else []

            phase = ExecutionPhase(
                phase_id=i,
                name=phase_name,
                agents=agents,
                dependencies=dependencies,
                parallel=enable_parallel
            )
            phases.append(phase)

        return OrchestrationConfig(phases=phases)
