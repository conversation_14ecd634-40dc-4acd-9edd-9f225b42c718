{"ast": null, "code": "// src/uuid/index.ts\nvar lastIds = {};\nfunction uuid(prefix = \"pui_id_\") {\n  if (!lastIds.hasOwnProperty(prefix)) {\n    lastIds[prefix] = 0;\n  }\n  lastIds[prefix]++;\n  return `${prefix}${lastIds[prefix]}`;\n}\nexport { uuid };", "map": {"version": 3, "names": ["lastIds", "uuid", "prefix", "hasOwnProperty"], "sources": ["C:/projetos/scope-ai/frontend/node_modules/@primeuix/utils/uuid/index.mjs"], "sourcesContent": ["// src/uuid/index.ts\nvar lastIds = {};\nfunction uuid(prefix = \"pui_id_\") {\n  if (!lastIds.hasOwnProperty(prefix)) {\n    lastIds[prefix] = 0;\n  }\n  lastIds[prefix]++;\n  return `${prefix}${lastIds[prefix]}`;\n}\nexport {\n  uuid\n};\n"], "mappings": "AAAA;AACA,IAAIA,OAAO,GAAG,CAAC,CAAC;AAChB,SAASC,IAAIA,CAACC,MAAM,GAAG,SAAS,EAAE;EAChC,IAAI,CAACF,OAAO,CAACG,cAAc,CAACD,MAAM,CAAC,EAAE;IACnCF,OAAO,CAACE,MAAM,CAAC,GAAG,CAAC;EACrB;EACAF,OAAO,CAACE,MAAM,CAAC,EAAE;EACjB,OAAO,GAAGA,MAAM,GAAGF,OAAO,CAACE,MAAM,CAAC,EAAE;AACtC;AACA,SACED,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}