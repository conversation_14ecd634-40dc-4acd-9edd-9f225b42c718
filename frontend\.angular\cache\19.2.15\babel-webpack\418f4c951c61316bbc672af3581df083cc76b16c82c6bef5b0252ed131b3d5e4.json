{"ast": null, "code": "// src/classnames/index.ts\nfunction classNames(...args) {\n  if (args) {\n    let classes = [];\n    for (let i = 0; i < args.length; i++) {\n      let className = args[i];\n      if (!className) {\n        continue;\n      }\n      const type = typeof className;\n      if (type === \"string\" || type === \"number\") {\n        classes.push(className);\n      } else if (type === \"object\") {\n        const _classes = Array.isArray(className) ? [classNames(...className)] : Object.entries(className).map(([key, value]) => value ? key : void 0);\n        classes = _classes.length ? classes.concat(_classes.filter(c => !!c)) : classes;\n      }\n    }\n    return classes.join(\" \").trim();\n  }\n  return void 0;\n}\nexport { classNames };", "map": {"version": 3, "names": ["classNames", "args", "classes", "i", "length", "className", "type", "push", "_classes", "Array", "isArray", "Object", "entries", "map", "key", "value", "concat", "filter", "c", "join", "trim"], "sources": ["C:/projetos/scope-ai/frontend/node_modules/@primeuix/utils/classnames/index.mjs"], "sourcesContent": ["// src/classnames/index.ts\nfunction classNames(...args) {\n  if (args) {\n    let classes = [];\n    for (let i = 0; i < args.length; i++) {\n      let className = args[i];\n      if (!className) {\n        continue;\n      }\n      const type = typeof className;\n      if (type === \"string\" || type === \"number\") {\n        classes.push(className);\n      } else if (type === \"object\") {\n        const _classes = Array.isArray(className) ? [classNames(...className)] : Object.entries(className).map(([key, value]) => value ? key : void 0);\n        classes = _classes.length ? classes.concat(_classes.filter((c) => !!c)) : classes;\n      }\n    }\n    return classes.join(\" \").trim();\n  }\n  return void 0;\n}\nexport {\n  classNames\n};\n"], "mappings": "AAAA;AACA,SAASA,UAAUA,CAAC,GAAGC,IAAI,EAAE;EAC3B,IAAIA,IAAI,EAAE;IACR,IAAIC,OAAO,GAAG,EAAE;IAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MACpC,IAAIE,SAAS,GAAGJ,IAAI,CAACE,CAAC,CAAC;MACvB,IAAI,CAACE,SAAS,EAAE;QACd;MACF;MACA,MAAMC,IAAI,GAAG,OAAOD,SAAS;MAC7B,IAAIC,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,QAAQ,EAAE;QAC1CJ,OAAO,CAACK,IAAI,CAACF,SAAS,CAAC;MACzB,CAAC,MAAM,IAAIC,IAAI,KAAK,QAAQ,EAAE;QAC5B,MAAME,QAAQ,GAAGC,KAAK,CAACC,OAAO,CAACL,SAAS,CAAC,GAAG,CAACL,UAAU,CAAC,GAAGK,SAAS,CAAC,CAAC,GAAGM,MAAM,CAACC,OAAO,CAACP,SAAS,CAAC,CAACQ,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAKA,KAAK,GAAGD,GAAG,GAAG,KAAK,CAAC,CAAC;QAC9IZ,OAAO,GAAGM,QAAQ,CAACJ,MAAM,GAAGF,OAAO,CAACc,MAAM,CAACR,QAAQ,CAACS,MAAM,CAAEC,CAAC,IAAK,CAAC,CAACA,CAAC,CAAC,CAAC,GAAGhB,OAAO;MACnF;IACF;IACA,OAAOA,OAAO,CAACiB,IAAI,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,CAAC;EACjC;EACA,OAAO,KAAK,CAAC;AACf;AACA,SACEpB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}