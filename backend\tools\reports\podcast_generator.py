"""
Podcast Generation Service
Generates persuasive audio podcasts about project importance
"""
import os
import json
import tempfile
from typing import Dict, List, Tuple, Optional
from datetime import datetime
import asyncio
from pathlib import Path

from openai import AsyncOpenAI
from pydantic import BaseModel, Field
import edge_tts
from pydub import AudioSegment
from pydub.effects import normalize
import logging

from config.settings import env

logger = logging.getLogger(__name__)


class PodcastSegment(BaseModel):
    """Represents a single segment of the podcast"""
    speaker: str = Field(description="Host1 or Host2")
    text: str = Field(description="What the speaker says")
    emotion: str = Field(default="neutral", description="Emotion/tone for this segment")


class PodcastScript(BaseModel):
    """Complete podcast script with metadata"""
    title: str
    duration_estimate: str
    segments: List[PodcastSegment]
    key_points: List[str]


class PodcastGenerator:
    """Generates persuasive podcasts about project importance"""
    
    def __init__(self):
        self.client = AsyncOpenAI(api_key=env.OPENAI_API_KEY)
        # Voice mapping for edge-tts
        self.voices = {
            "Host1": "pt-BR-AntonioNeural",     # Male voice - <PERSON>
            "Host2": "pt-BR-FranciscaNeural"    # Female voice - Amanda
        }
        self.temp_dir = Path(tempfile.gettempdir()) / "scope_ai_podcasts"
        self.temp_dir.mkdir(exist_ok=True)
        
    async def generate_script(self, project_data: Dict) -> PodcastScript:
        """Generate a persuasive podcast script about the project"""
        
        # Extract key information from project data
        project_name = project_data.get("name", "Projeto")
        client_name = project_data.get("client", {}).get("name", "Cliente")
        description = project_data.get("description", "")
        
        # Get insights from various agents
        insights = self._extract_insights(project_data)
        
        prompt = f"""
        Você é um roteirista especializado em criar podcasts dinâmicos e envolventes sobre negócios e tecnologia.
        
        Crie um roteiro de podcast que seja uma conversa NATURAL e ESPONTÂNEA entre dois especialistas
        discutindo este projeto da empresa {client_name}. A conversa deve fluir como se fossem amigos 
        especialistas batendo um papo informal, mas informativo.
        
        Projeto: {project_name}
        Descrição: {description}
        
        Insights importantes:
        {json.dumps(insights, indent=2, ensure_ascii=False)}
        
        DIRETRIZES PARA TORNAR MAIS DINÂMICO:
        1. Use linguagem coloquial e natural - "cara", "olha só", "imagina", "nossa"
        2. Interrupções e complementos mútuos - um completa a frase do outro
        3. Perguntas retóricas - "você já pensou que...", "mas sabe o que é interessante?"
        4. Analogias do dia a dia - compare com situações conhecidas
        5. Momentos de surpresa genuína - "sério?", "não acredito!", "que incrível!"
        6. Mudanças de ritmo - trechos mais empolgados e outros mais reflexivos
        7. Referencias a experiências pessoais - "já vi isso acontecer", "lembro de um caso"
        
        O podcast deve:
        - Durar entre 4-6 minutos (conteúdo substancial e completo)
        - Ser uma conversa que você gostaria de escutar no carro
        - Ter pelo menos 4 momentos de empolgação genuína
        - Incluir 3-4 analogias criativas
        - Mencionar consequências práticas (não apenas teoria)
        - Ter ritmo variado (não monotônico)
        - Explorar diferentes ângulos do projeto
        - Incluir exemplos concretos e cenários específicos
        
        PERSONALIDADES dos hosts:
        - Host1 (Eduardo Catrinck): Entusiasta de tecnologia, usa analogias, linguagem descontraída
        - Host2 (Amanda): Estrategista de negócios, perspicaz, faz perguntas provocativas
        
        IMPORTANTE: Gere pelo menos 12-15 segments para garantir duração adequada (4-6 minutos).
        Cada segment deve ter entre 15-25 palavras para manter o ritmo natural.
        
        Retorne um objeto JSON com EXATAMENTE esta estrutura:
        {{
            "title": "Título criativo e chamativo do episódio",
            "duration_estimate": "4-6 minutos",
            "segments": [
                {{
                    "speaker": "Host1" ou "Host2",
                    "text": "O que o host vai falar (linguagem natural e coloquial, 15-25 palavras)",
                    "emotion": "neutral" ou "enthusiastic" ou "concerned" ou "optimistic" ou "excited" ou "curious"
                }}
            ],
            "key_points": [
                "Benefício prático concreto",
                "Consequência de não fazer",
                "Oportunidade de mercado",
                "Vantagem competitiva",
                "Diferencial no mercado"
            ]
        }}
        """
        
        response = await self.client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "Você é um roteirista de podcasts de negócios."},
                {"role": "user", "content": prompt}
            ],
            response_format={"type": "json_object"},
            temperature=0.8
        )
        
        try:
            script_data = json.loads(response.choices[0].message.content)
            
            # Log the response for debugging
            logger.info(f"OpenAI response: {json.dumps(script_data, ensure_ascii=False)[:500]}...")
            
            # Handle nested response format
            if "PodcastScript" in script_data:
                script_data = script_data["PodcastScript"]
            
            # Ensure all required fields exist
            if "segments" not in script_data or not script_data["segments"]:
                # Create default segments if missing
                script_data["segments"] = [
                    {
                        "speaker": "Host1",
                        "text": "Olá! Hoje vamos falar sobre um projeto muito importante.",
                        "emotion": "enthusiastic"
                    }
                ]
            
            # Ensure each segment has required fields
            for segment in script_data.get("segments", []):
                if "emotion" not in segment:
                    segment["emotion"] = "neutral"
            
            return PodcastScript(**script_data)
            
        except Exception as e:
            logger.error(f"Error parsing script data: {e}")
            logger.error(f"Raw response: {response.choices[0].message.content}")
            
            # Return a default script on error
            return PodcastScript(
                title=f"Batendo um Papo sobre {project_data.get('name', 'este Projeto Incrível')}",
                duration_estimate="5 minutos",
                segments=[
                    PodcastSegment(
                        speaker="Host1",
                        text=f"E aí Amanda! Cara, você precisa ver esse projeto da {project_data.get('client', {}).get('name', 'nossa empresa')}.",
                        emotion="enthusiastic"
                    ),
                    PodcastSegment(
                        speaker="Host2", 
                        text="Opa! Conta aí Eduardo, o que tem de especial? Estou curiosa!",
                        emotion="curious"
                    ),
                    PodcastSegment(
                        speaker="Host1",
                        text=f"O projeto {project_data.get('name', 'esse aqui')} é simplesmente revolucionário no que eles fazem.",
                        emotion="excited"
                    ),
                    PodcastSegment(
                        speaker="Host2",
                        text="Sério? Me dá mais detalhes, quero entender melhor essa revolução.",
                        emotion="curious"
                    ),
                    PodcastSegment(
                        speaker="Host1",
                        text=f"Olha só, {project_data.get('description', 'vai modernizar completamente como eles trabalham')}.",
                        emotion="neutral"
                    ),
                    PodcastSegment(
                        speaker="Host2",
                        text="Nossa, já estou imaginando o impacto! Esse tipo de coisa muda o jogo completamente.",
                        emotion="optimistic"
                    ),
                    PodcastSegment(
                        speaker="Host1", 
                        text="Exatamente! É daqueles projetos que você olha e pensa: por que ninguém fez antes?",
                        emotion="excited"
                    ),
                    PodcastSegment(
                        speaker="Host2",
                        text="E o timing, Eduardo? Será que é o momento certo para eles investirem nisso?",
                        emotion="concerned"
                    ),
                    PodcastSegment(
                        speaker="Host1",
                        text="Perfeito! Se não fizerem agora, vão perder uma janela de oportunidade única no mercado.",
                        emotion="concerned"
                    ),
                    PodcastSegment(
                        speaker="Host2",
                        text="Imagina os clientes deles tendo essa experiência... vai ser um diferencial e tanto!",
                        emotion="enthusiastic"
                    ),
                    PodcastSegment(
                        speaker="Host1",
                        text="Sim! E a concorrência não vai conseguir alcançar tão facilmente quando estiver implementado.",
                        emotion="optimistic"
                    ),
                    PodcastSegment(
                        speaker="Host2", 
                        text="Cara, projetos assim realmente separam empresas que lideram das que só seguem tendências.",
                        emotion="enthusiastic"
                    ),
                    PodcastSegment(
                        speaker="Host1",
                        text="É isso aí Amanda! Investimento estratégico que vai render frutos por muito tempo.",
                        emotion="optimistic"
                    ),
                    PodcastSegment(
                        speaker="Host2",
                        text="Adorei essa conversa! Sempre bom ver projetos que realmente fazem a diferença no mercado.",
                        emotion="enthusiastic"
                    )
                ],
                key_points=[
                    "Projeto revolucionário no segmento",
                    "Timing perfeito para implementação", 
                    "Impacto direto na experiência do cliente",
                    "Vantagem competitiva sustentável",
                    "Diferencial único no mercado"
                ]
            )
    
    def _extract_insights(self, project_data: Dict) -> Dict:
        """Extract key insights from agent analyses"""
        insights = {
            "market_opportunity": [],
            "competitive_advantages": [],
            "user_benefits": [],
            "technical_highlights": [],
            "risks_without_project": []
        }
        
        # Extract from market research
        if "market_research" in project_data:
            research = project_data["market_research"]
            if "market_size" in research:
                insights["market_opportunity"].append(f"Mercado de {research['market_size']}")
            if "competitors" in research:
                insights["competitive_advantages"].append(
                    f"{len(research['competitors'])} competidores identificados"
                )
        
        # Extract from agent analyses
        if "agent_analyses" in project_data:
            analyses = project_data["agent_analyses"]
            
            # UX insights
            if "ux_analysis" in analyses:
                ux = analyses["ux_analysis"]
                if "user_benefits" in ux:
                    insights["user_benefits"].extend(ux["user_benefits"][:3])
            
            # Performance insights
            if "performance_analysis" in analyses:
                perf = analyses["performance_analysis"]
                if "improvements" in perf:
                    insights["technical_highlights"].append(
                        f"Melhoria de {perf['improvements'].get('expected_gain', 'significativa')} em performance"
                    )
            
            # SEO insights
            if "seo_analysis" in analyses:
                seo = analyses["seo_analysis"]
                if "opportunities" in seo:
                    insights["market_opportunity"].append(
                        f"{len(seo['opportunities'])} oportunidades de SEO identificadas"
                    )
        
        # Business risks
        insights["risks_without_project"] = [
            "Perda de competitividade no mercado",
            "Clientes migrando para concorrentes",
            "Oportunidades de mercado desperdiçadas"
        ]
        
        return insights
    
    async def generate_audio(self, script: PodcastScript) -> bytes:
        """Generate audio from the podcast script using edge-tts"""
        
        audio_segments = []
        
        # Generate audio for each segment
        for i, segment in enumerate(script.segments):
            logger.info(f"Generating audio for segment {i+1}/{len(script.segments)}")
            
            voice = self.voices.get(segment.speaker, self.voices["Host1"])
            output_file = self.temp_dir / f"segment_{i}.mp3"
            
            # Adjust rate and pitch based on emotion
            rate = "+0%"
            pitch = "+0Hz"
            
            if segment.emotion == "excited":
                rate = "+10%"
                pitch = "+20Hz"
            elif segment.emotion == "serious":
                rate = "-5%"
                pitch = "-10Hz"
            elif segment.emotion == "enthusiastic":
                rate = "+5%"
                pitch = "+10Hz"
            
            # Generate speech
            communicate = edge_tts.Communicate(
                segment.text,
                voice,
                rate=rate,
                pitch=pitch
            )
            await communicate.save(str(output_file))
            
            # Load and add to segments
            audio = AudioSegment.from_mp3(str(output_file))
            audio_segments.append(audio)
            
            # Add small pause between speakers
            if i < len(script.segments) - 1:
                # Check if next speaker is different
                if script.segments[i+1].speaker != segment.speaker:
                    silence = AudioSegment.silent(duration=300)  # 300ms pause
                    audio_segments.append(silence)
                else:
                    silence = AudioSegment.silent(duration=100)  # 100ms pause
                    audio_segments.append(silence)
        
        # Combine all segments
        logger.info("Combining audio segments...")
        combined = AudioSegment.empty()
        for segment in audio_segments:
            combined += segment
        
        # Apply audio enhancements
        combined = self._enhance_audio(combined)
        
        # Add intro/outro music (optional)
        combined = self._add_background_music(combined)
        
        # Export to bytes
        output_buffer = combined.export(format="mp3", bitrate="192k")
        audio_bytes = output_buffer.read()
        
        # Cleanup temp files
        for file in self.temp_dir.glob("segment_*.mp3"):
            file.unlink()
        
        return audio_bytes
    
    def _enhance_audio(self, audio: AudioSegment) -> AudioSegment:
        """Apply audio enhancements"""
        # Normalize audio levels
        audio = normalize(audio)
        
        # Add slight compression effect
        # This makes quiet parts louder and loud parts quieter
        audio = audio.compress_dynamic_range()
        
        # Ensure consistent volume
        target_dBFS = -20.0
        change_in_dBFS = target_dBFS - audio.dBFS
        audio = audio.apply_gain(change_in_dBFS)
        
        return audio
    
    def _add_background_music(self, audio: AudioSegment) -> AudioSegment:
        """Add subtle background music (if available)"""
        # For now, just add fade in/out effects
        # In future, we could add actual background music
        
        # Fade in at the beginning (1 second)
        audio = audio.fade_in(1000)
        
        # Fade out at the end (2 seconds)
        audio = audio.fade_out(2000)
        
        return audio
    
    async def generate_podcast(self, project_data: Dict) -> Tuple[bytes, PodcastScript]:
        """Generate complete podcast (script + audio)"""
        logger.info(f"Generating podcast for project: {project_data.get('name', 'Unknown')}")
        
        # Generate script
        script = await self.generate_script(project_data)
        logger.info(f"Generated script with {len(script.segments)} segments")
        
        # Generate audio
        audio_bytes = await self.generate_audio(script)
        logger.info(f"Generated audio: {len(audio_bytes) / 1024 / 1024:.2f} MB")
        
        return audio_bytes, script
    
    def estimate_duration(self, script: PodcastScript) -> int:
        """Estimate podcast duration in seconds"""
        # Rough estimation: 150 words per minute
        total_words = sum(len(segment.text.split()) for segment in script.segments)
        duration_minutes = total_words / 150
        return int(duration_minutes * 60)