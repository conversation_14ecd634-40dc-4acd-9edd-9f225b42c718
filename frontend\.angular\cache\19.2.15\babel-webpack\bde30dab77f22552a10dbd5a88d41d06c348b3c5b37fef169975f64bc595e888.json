{"ast": null, "code": "import _asyncToGenerator from \"C:/projetos/scope-ai/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"C:/projetos/scope-ai/frontend/src/app/features/projects/components/podcast-modal/podcast-modal.component.ts.scss?ngResource!=!C:\\\\projetos\\\\scope-ai\\\\frontend\\\\node_modules\\\\@ngtools\\\\webpack\\\\src\\\\loaders\\\\inline-resource.js?data=CgkJCTpob3N0IDo6bmctZGVlcCAucG9kY2FzdC1tb2RhbCAucC1kaWFsb2ctY29udGVudCB7CgkJCQlwYWRkaW5nOiAwOwoJCQl9CgoJCQk6aG9zdCA6Om5nLWRlZXAgLnBvZGNhc3QtbW9kYWwgLnAtZGlhbG9nLWhlYWRlciB7CgkJCQlwYWRkaW5nOiAxLjVyZW0gMS41cmVtIDFyZW0gMS41cmVtOwoJCQkJYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlNWU3ZWI7CgkJCX0KCgkJCS5wb2RjYXN0LXBsYXllci1jb250YWluZXIgewoJCQkJcGFkZGluZzogMS41cmVtOwoJCQl9CgoJCQlhdWRpbyB7CgkJCQloZWlnaHQ6IDU0cHg7CgkJCX0KCgkJCWF1ZGlvOjotd2Via2l0LW1lZGlhLWNvbnRyb2xzLXBhbmVsIHsKCQkJCWJhY2tncm91bmQtY29sb3I6ICNmOWZhZmI7CgkJCQlib3JkZXItcmFkaXVzOiA4cHg7CgkJCX0KCgkJCS5sb2FkaW5nLWRvdHMgewoJCQkJZGlzcGxheTogaW5saW5lLWJsb2NrOwoJCQkJcG9zaXRpb246IHJlbGF0aXZlOwoJCQkJd2lkdGg6IDQwcHg7CgkJCQloZWlnaHQ6IDEwcHg7CgkJCX0KCgkJCS5sb2FkaW5nLWRvdHMgZGl2IHsKCQkJCXBvc2l0aW9uOiBhYnNvbHV0ZTsKCQkJCXRvcDogMDsKCQkJCXdpZHRoOiA4cHg7CgkJCQloZWlnaHQ6IDhweDsKCQkJCWJvcmRlci1yYWRpdXM6IDUwJTsKCQkJCWJhY2tncm91bmQ6ICM4YjVjZjY7CgkJCQlhbmltYXRpb246IGxvYWRpbmctZG90cyAxLjJzIGluZmluaXRlIGVhc2UtaW4tb3V0OwoJCQl9CgoJCQkubG9hZGluZy1kb3RzIGRpdjpudGgtY2hpbGQoMSkgewoJCQkJbGVmdDogMDsKCQkJCWFuaW1hdGlvbi1kZWxheTogLTAuMjRzOwoJCQl9CgkJCS5sb2FkaW5nLWRvdHMgZGl2Om50aC1jaGlsZCgyKSB7CgkJCQlsZWZ0OiAxNnB4OwoJCQkJYW5pbWF0aW9uLWRlbGF5OiAtMC4xMnM7CgkJCX0KCQkJLmxvYWRpbmctZG90cyBkaXY6bnRoLWNoaWxkKDMpIHsKCQkJCWxlZnQ6IDMycHg7CgkJCQlhbmltYXRpb24tZGVsYXk6IDA7CgkJCX0KCgkJCUBrZXlmcmFtZXMgbG9hZGluZy1kb3RzIHsKCQkJCTAlLAoJCQkJODAlLAoJCQkJMTAwJSB7CgkJCQkJdHJhbnNmb3JtOiBzY2FsZSgwKTsKCQkJCX0KCQkJCTQwJSB7CgkJCQkJdHJhbnNmb3JtOiBzY2FsZSgxKTsKCQkJCX0KCQkJfQoKCQkJLyogUmVzcG9uc2l2ZSBhZGp1c3RtZW50cyAqLwoJCQlAbWVkaWEgKG1heC13aWR0aDogNjQwcHgpIHsKCQkJCS5wb2RjYXN0LXBsYXllci1jb250YWluZXIgewoJCQkJCXBhZGRpbmc6IDFyZW07CgkJCQl9CgoJCQkJOmhvc3QgOjpuZy1kZWVwIC5wb2RjYXN0LW1vZGFsIC5wLWRpYWxvZy1oZWFkZXIgewoJCQkJCXBhZGRpbmc6IDFyZW07CgkJCQl9CgoJCQkJLmJnLWdyYWRpZW50LXRvLWJyIHsKCQkJCQlwYWRkaW5nOiAxLjVyZW07CgkJCQl9CgkJCX0KCQk%3D!C:/projetos/scope-ai/frontend/src/app/features/projects/components/podcast-modal/podcast-modal.component.ts\";\nimport { CommonModule } from '@angular/common';\nimport { HttpClient } from '@angular/common/http';\nimport { Component, EventEmitter, Input, Output, signal } from '@angular/core';\nimport { MessageService } from 'primeng/api';\nimport { ButtonModule } from 'primeng/button';\nimport { DialogModule } from 'primeng/dialog';\nlet PodcastModalComponent = class PodcastModalComponent {\n  http;\n  messageService;\n  data = {\n    visible: false,\n    generating: false,\n    url: '',\n    title: ''\n  };\n  close = new EventEmitter();\n  generate = new EventEmitter();\n  // Internal state\n  generating = signal(false);\n  constructor(http, messageService) {\n    this.http = http;\n    this.messageService = messageService;\n  }\n  ngOnDestroy() {\n    // Cleanup any ongoing requests or timers\n  }\n  onClose() {\n    this.close.emit();\n  }\n  generatePodcast() {\n    if (this.data.projectId) {\n      this.generate.emit(this.data.projectId);\n    }\n  }\n  /**\n   * Reproduz podcast existente ou gera novo\n   */\n  playProjectPodcast(projectId) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!projectId) return;\n      try {\n        // Verificar se já existe podcast gerado\n        const response = yield _this.http.get(`http://localhost:8040/podcasts/projects/${projectId}/podcast`).toPromise();\n        if (response?.data?.has_podcast && response?.data?.podcast_id) {\n          // Podcast já existe, reproduzir\n          _this.openPodcastPlayer(response.data.podcast_id, _this.data.title);\n        } else {\n          // Gerar novo podcast\n          yield _this.generateProjectPodcast(projectId);\n        }\n      } catch (error) {\n        console.error('Erro ao verificar podcast:', error);\n        // Se erro, tentar gerar novo\n        yield _this.generateProjectPodcast(projectId);\n      }\n    })();\n  }\n  /**\n   * Gera podcast para o projeto\n   */\n  generateProjectPodcast(projectId) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!projectId) return;\n      try {\n        _this2.generating.set(true);\n        _this2.messageService.add({\n          severity: 'info',\n          summary: '🎙️ Gerando Podcast',\n          detail: 'Criando podcast persuasivo sobre o projeto. Aguarde 1-2 minutos...',\n          life: 8000\n        });\n        // Solicitar geração do podcast\n        const response = yield _this2.http.post(`http://localhost:8040/podcasts/projects/${projectId}/podcast/generate`, {}).toPromise();\n        if (response?.data?.podcast_id) {\n          // Monitorar status da geração\n          yield _this2.monitorPodcastGeneration(response.data.podcast_id);\n        }\n      } catch (error) {\n        console.error('Erro ao gerar podcast:', error);\n        _this2.generating.set(false);\n        _this2.messageService.add({\n          severity: 'error',\n          summary: 'Erro ao Gerar Podcast',\n          detail: 'Não foi possível gerar o podcast. Tente novamente.',\n          life: 5000\n        });\n      }\n    })();\n  }\n  /**\n   * Monitora status da geração do podcast\n   */\n  monitorPodcastGeneration(podcastId) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const _checkStatus = /*#__PURE__*/function () {\n        var _ref = _asyncToGenerator(function* () {\n          try {\n            const status = yield _this3.http.get(`http://localhost:8040/podcasts/${podcastId}/status`).toPromise();\n            if (status?.data?.status === 'completed') {\n              _this3.generating.set(false);\n              _this3.messageService.add({\n                severity: 'success',\n                summary: '✅ Podcast Pronto!',\n                detail: 'O podcast foi gerado com sucesso.',\n                life: 3000\n              });\n              // Abrir player\n              _this3.openPodcastPlayer(podcastId, _this3.data.title);\n            } else if (status?.data?.status === 'error') {\n              _this3.generating.set(false);\n              _this3.messageService.add({\n                severity: 'error',\n                summary: 'Erro na Geração',\n                detail: 'Ocorreu um erro durante a geração do podcast.',\n                life: 5000\n              });\n            } else {\n              // Ainda processando, verificar novamente\n              setTimeout(() => _checkStatus(), 5000);\n            }\n          } catch (error) {\n            console.error('Erro ao verificar status:', error);\n            _this3.generating.set(false);\n          }\n        });\n        return function checkStatus() {\n          return _ref.apply(this, arguments);\n        };\n      }();\n      // Iniciar verificação\n      setTimeout(() => _checkStatus(), 3000);\n    })();\n  }\n  /**\n   * Abre player de áudio para o podcast\n   */\n  openPodcastPlayer(podcastId, projectTitle) {\n    // Configurar dados do podcast\n    const audioUrl = `http://localhost:8040/podcasts/${podcastId}/audio`;\n    // Sanitizar o título para evitar caracteres especiais\n    const sanitizedTitle = projectTitle.replace(/[<>&\"']/g, '');\n    // Emitir dados atualizados para o parent component\n    this.data = {\n      ...this.data,\n      url: audioUrl,\n      title: sanitizedTitle,\n      generating: false\n    };\n  }\n  static ctorParameters = () => [{\n    type: HttpClient\n  }, {\n    type: MessageService\n  }];\n  static propDecorators = {\n    data: [{\n      type: Input\n    }],\n    close: [{\n      type: Output\n    }],\n    generate: [{\n      type: Output\n    }]\n  };\n};\nPodcastModalComponent = __decorate([Component({\n  selector: 'app-podcast-modal',\n  standalone: true,\n  imports: [CommonModule, DialogModule, ButtonModule],\n  template: `\n\t\t<p-dialog\n\t\t\t[visible]=\"data.visible\"\n\t\t\t[modal]=\"true\"\n\t\t\t[closable]=\"true\"\n\t\t\t[draggable]=\"false\"\n\t\t\t[resizable]=\"false\"\n\t\t\tstyleClass=\"podcast-modal\"\n\t\t\t[style]=\"{ width: '90vw', maxWidth: '600px' }\"\n\t\t\t(onHide)=\"onClose()\"\n\t\t>\n\t\t\t<ng-template pTemplate=\"header\">\n\t\t\t\t<div class=\"flex items-center gap-3\">\n\t\t\t\t\t<i class=\"pi pi-microphone text-purple-600 text-2xl\"></i>\n\t\t\t\t\t<div>\n\t\t\t\t\t\t<h2 class=\"text-xl font-bold text-gray-900 m-0\">Podcast do Projeto</h2>\n\t\t\t\t\t\t<p class=\"text-sm text-gray-600 m-0 mt-1\">{{ data.title }}</p>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</ng-template>\n\n\t\t\t<ng-template pTemplate=\"content\">\n\t\t\t\t<div class=\"podcast-player-container\">\n\t\t\t\t\t<!-- Player Container -->\n\t\t\t\t\t<div\n\t\t\t\t\t\tclass=\"bg-gradient-to-br from-purple-50 to-indigo-50 rounded-2xl p-8 text-center border border-purple-200\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<!-- Podcast Icon -->\n\t\t\t\t\t\t<div class=\"mb-6\">\n\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\tclass=\"w-20 h-20 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto shadow-lg\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<i class=\"pi pi-microphone text-white text-3xl\"></i>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t<!-- Project Info -->\n\t\t\t\t\t\t<div class=\"mb-6\">\n\t\t\t\t\t\t\t<h3 class=\"text-lg font-semibold text-gray-900 mb-2\">{{ data.title }}</h3>\n\t\t\t\t\t\t\t<p class=\"text-sm text-gray-600\">Análise detalhada do projeto em formato de podcast</p>\n\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t<!-- Loading State -->\n\t\t\t\t\t\t@if (data.generating) {\n\t\t\t\t\t\t\t<div class=\"bg-white rounded-xl p-6 shadow-sm border border-gray-200\">\n\t\t\t\t\t\t\t\t<div class=\"flex flex-col items-center gap-4\">\n\t\t\t\t\t\t\t\t\t<i class=\"pi pi-spin pi-spinner text-3xl text-purple-600\"></i>\n\t\t\t\t\t\t\t\t\t<div class=\"text-center\">\n\t\t\t\t\t\t\t\t\t\t<p class=\"font-semibold text-gray-900\">Gerando Podcast...</p>\n\t\t\t\t\t\t\t\t\t\t<p class=\"text-sm text-gray-600\">\n\t\t\t\t\t\t\t\t\t\t\tAguarde 1-2 minutos enquanto criamos o áudio\n\t\t\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t<!-- Audio Player -->\n\t\t\t\t\t\t@if (data.url && !data.generating) {\n\t\t\t\t\t\t\t<div class=\"bg-white rounded-xl p-4 shadow-sm border border-gray-200\">\n\t\t\t\t\t\t\t\t<audio controls autoplay class=\"w-full\" [src]=\"data.url\" preload=\"auto\">\n\t\t\t\t\t\t\t\t\t<source [src]=\"data.url\" type=\"audio/mpeg\" />\n\t\t\t\t\t\t\t\t\t<p class=\"text-red-600 text-sm\">Seu navegador não suporta o elemento de áudio.</p>\n\t\t\t\t\t\t\t\t</audio>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t<!-- Empty State -->\n\t\t\t\t\t\t@if (!data.url && !data.generating) {\n\t\t\t\t\t\t\t<div class=\"bg-white rounded-xl p-6 shadow-sm border border-gray-200\">\n\t\t\t\t\t\t\t\t<div class=\"text-center\">\n\t\t\t\t\t\t\t\t\t<i class=\"pi pi-microphone text-4xl text-gray-400 mb-4\"></i>\n\t\t\t\t\t\t\t\t\t<p class=\"text-gray-600 mb-4\">Nenhum podcast disponível</p>\n\t\t\t\t\t\t\t\t\t@if (data.projectId) {\n\t\t\t\t\t\t\t\t\t\t<button\n\t\t\t\t\t\t\t\t\t\t\tpButton\n\t\t\t\t\t\t\t\t\t\t\tlabel=\"Gerar Podcast\"\n\t\t\t\t\t\t\t\t\t\t\ticon=\"pi pi-play\"\n\t\t\t\t\t\t\t\t\t\t\tclass=\"p-button-outlined p-button-primary\"\n\t\t\t\t\t\t\t\t\t\t\t(click)=\"generatePodcast()\"\n\t\t\t\t\t\t\t\t\t\t></button>\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t<!-- Player Info -->\n\t\t\t\t\t\t<div class=\"mt-4 text-xs text-gray-500\">\n\t\t\t\t\t\t\t<p>💡 Use os controles de áudio para pausar, ajustar volume ou pular trechos</p>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\n\t\t\t\t\t<!-- Additional Actions -->\n\t\t\t\t\t<div class=\"flex justify-center gap-3 mt-6\">\n\t\t\t\t\t\t<button\n\t\t\t\t\t\t\tpButton\n\t\t\t\t\t\t\tlabel=\"Fechar\"\n\t\t\t\t\t\t\ticon=\"pi pi-times\"\n\t\t\t\t\t\t\tclass=\"p-button-outlined p-button-secondary\"\n\t\t\t\t\t\t\t(click)=\"onClose()\"\n\t\t\t\t\t\t></button>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</ng-template>\n\t\t</p-dialog>\n\t`,\n  styles: [__NG_CLI_RESOURCE__0]\n})], PodcastModalComponent);\nexport { PodcastModalComponent };", "map": {"version": 3, "names": ["CommonModule", "HttpClient", "Component", "EventEmitter", "Input", "Output", "signal", "MessageService", "ButtonModule", "DialogModule", "PodcastModalComponent", "http", "messageService", "data", "visible", "generating", "url", "title", "close", "generate", "constructor", "ngOnDestroy", "onClose", "emit", "generatePodcast", "projectId", "playProjectPodcast", "_this", "_asyncToGenerator", "response", "get", "to<PERSON>romise", "has_podcast", "podcast_id", "openPodcastPlayer", "generateProjectPodcast", "error", "console", "_this2", "set", "add", "severity", "summary", "detail", "life", "post", "monitorPodcastGeneration", "podcastId", "_this3", "checkStatus", "_ref", "status", "setTimeout", "apply", "arguments", "projectTitle", "audioUrl", "sanitizedTitle", "replace", "__decorate", "selector", "standalone", "imports", "template"], "sources": ["C:\\projetos\\scope-ai\\frontend\\src\\app\\features\\projects\\components\\podcast-modal\\podcast-modal.component.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { Component, EventEmitter, Input, OnDestroy, Output, signal } from '@angular/core';\r\nimport { MessageService } from 'primeng/api';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { DialogModule } from 'primeng/dialog';\r\n\r\nexport interface PodcastModalData {\r\n\tvisible: boolean;\r\n\tgenerating: boolean;\r\n\turl: string;\r\n\ttitle: string;\r\n\tprojectId?: string;\r\n}\r\n\r\nexport interface PodcastModalEvents {\r\n\tonClose: () => void;\r\n\tonGenerate?: (projectId: string) => void;\r\n}\r\n\r\n@Component({\r\n\tselector: 'app-podcast-modal',\r\n\tstandalone: true,\r\n\timports: [CommonModule, DialogModule, ButtonModule],\r\n\ttemplate: `\r\n\t\t<p-dialog\r\n\t\t\t[visible]=\"data.visible\"\r\n\t\t\t[modal]=\"true\"\r\n\t\t\t[closable]=\"true\"\r\n\t\t\t[draggable]=\"false\"\r\n\t\t\t[resizable]=\"false\"\r\n\t\t\tstyleClass=\"podcast-modal\"\r\n\t\t\t[style]=\"{ width: '90vw', maxWidth: '600px' }\"\r\n\t\t\t(onHide)=\"onClose()\"\r\n\t\t>\r\n\t\t\t<ng-template pTemplate=\"header\">\r\n\t\t\t\t<div class=\"flex items-center gap-3\">\r\n\t\t\t\t\t<i class=\"pi pi-microphone text-purple-600 text-2xl\"></i>\r\n\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<h2 class=\"text-xl font-bold text-gray-900 m-0\">Podcast do Projeto</h2>\r\n\t\t\t\t\t\t<p class=\"text-sm text-gray-600 m-0 mt-1\">{{ data.title }}</p>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</ng-template>\r\n\r\n\t\t\t<ng-template pTemplate=\"content\">\r\n\t\t\t\t<div class=\"podcast-player-container\">\r\n\t\t\t\t\t<!-- Player Container -->\r\n\t\t\t\t\t<div\r\n\t\t\t\t\t\tclass=\"bg-gradient-to-br from-purple-50 to-indigo-50 rounded-2xl p-8 text-center border border-purple-200\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<!-- Podcast Icon -->\r\n\t\t\t\t\t\t<div class=\"mb-6\">\r\n\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\tclass=\"w-20 h-20 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto shadow-lg\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<i class=\"pi pi-microphone text-white text-3xl\"></i>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<!-- Project Info -->\r\n\t\t\t\t\t\t<div class=\"mb-6\">\r\n\t\t\t\t\t\t\t<h3 class=\"text-lg font-semibold text-gray-900 mb-2\">{{ data.title }}</h3>\r\n\t\t\t\t\t\t\t<p class=\"text-sm text-gray-600\">Análise detalhada do projeto em formato de podcast</p>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<!-- Loading State -->\r\n\t\t\t\t\t\t@if (data.generating) {\r\n\t\t\t\t\t\t\t<div class=\"bg-white rounded-xl p-6 shadow-sm border border-gray-200\">\r\n\t\t\t\t\t\t\t\t<div class=\"flex flex-col items-center gap-4\">\r\n\t\t\t\t\t\t\t\t\t<i class=\"pi pi-spin pi-spinner text-3xl text-purple-600\"></i>\r\n\t\t\t\t\t\t\t\t\t<div class=\"text-center\">\r\n\t\t\t\t\t\t\t\t\t\t<p class=\"font-semibold text-gray-900\">Gerando Podcast...</p>\r\n\t\t\t\t\t\t\t\t\t\t<p class=\"text-sm text-gray-600\">\r\n\t\t\t\t\t\t\t\t\t\t\tAguarde 1-2 minutos enquanto criamos o áudio\r\n\t\t\t\t\t\t\t\t\t\t</p>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t<!-- Audio Player -->\r\n\t\t\t\t\t\t@if (data.url && !data.generating) {\r\n\t\t\t\t\t\t\t<div class=\"bg-white rounded-xl p-4 shadow-sm border border-gray-200\">\r\n\t\t\t\t\t\t\t\t<audio controls autoplay class=\"w-full\" [src]=\"data.url\" preload=\"auto\">\r\n\t\t\t\t\t\t\t\t\t<source [src]=\"data.url\" type=\"audio/mpeg\" />\r\n\t\t\t\t\t\t\t\t\t<p class=\"text-red-600 text-sm\">Seu navegador não suporta o elemento de áudio.</p>\r\n\t\t\t\t\t\t\t\t</audio>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t<!-- Empty State -->\r\n\t\t\t\t\t\t@if (!data.url && !data.generating) {\r\n\t\t\t\t\t\t\t<div class=\"bg-white rounded-xl p-6 shadow-sm border border-gray-200\">\r\n\t\t\t\t\t\t\t\t<div class=\"text-center\">\r\n\t\t\t\t\t\t\t\t\t<i class=\"pi pi-microphone text-4xl text-gray-400 mb-4\"></i>\r\n\t\t\t\t\t\t\t\t\t<p class=\"text-gray-600 mb-4\">Nenhum podcast disponível</p>\r\n\t\t\t\t\t\t\t\t\t@if (data.projectId) {\r\n\t\t\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\t\t\tpButton\r\n\t\t\t\t\t\t\t\t\t\t\tlabel=\"Gerar Podcast\"\r\n\t\t\t\t\t\t\t\t\t\t\ticon=\"pi pi-play\"\r\n\t\t\t\t\t\t\t\t\t\t\tclass=\"p-button-outlined p-button-primary\"\r\n\t\t\t\t\t\t\t\t\t\t\t(click)=\"generatePodcast()\"\r\n\t\t\t\t\t\t\t\t\t\t></button>\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t<!-- Player Info -->\r\n\t\t\t\t\t\t<div class=\"mt-4 text-xs text-gray-500\">\r\n\t\t\t\t\t\t\t<p>💡 Use os controles de áudio para pausar, ajustar volume ou pular trechos</p>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t<!-- Additional Actions -->\r\n\t\t\t\t\t<div class=\"flex justify-center gap-3 mt-6\">\r\n\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\tpButton\r\n\t\t\t\t\t\t\tlabel=\"Fechar\"\r\n\t\t\t\t\t\t\ticon=\"pi pi-times\"\r\n\t\t\t\t\t\t\tclass=\"p-button-outlined p-button-secondary\"\r\n\t\t\t\t\t\t\t(click)=\"onClose()\"\r\n\t\t\t\t\t\t></button>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</ng-template>\r\n\t\t</p-dialog>\r\n\t`,\r\n\tstyles: [\r\n\t\t`\r\n\t\t\t:host ::ng-deep .podcast-modal .p-dialog-content {\r\n\t\t\t\tpadding: 0;\r\n\t\t\t}\r\n\r\n\t\t\t:host ::ng-deep .podcast-modal .p-dialog-header {\r\n\t\t\t\tpadding: 1.5rem 1.5rem 1rem 1.5rem;\r\n\t\t\t\tborder-bottom: 1px solid #e5e7eb;\r\n\t\t\t}\r\n\r\n\t\t\t.podcast-player-container {\r\n\t\t\t\tpadding: 1.5rem;\r\n\t\t\t}\r\n\r\n\t\t\taudio {\r\n\t\t\t\theight: 54px;\r\n\t\t\t}\r\n\r\n\t\t\taudio::-webkit-media-controls-panel {\r\n\t\t\t\tbackground-color: #f9fafb;\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t}\r\n\r\n\t\t\t.loading-dots {\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\twidth: 40px;\r\n\t\t\t\theight: 10px;\r\n\t\t\t}\r\n\r\n\t\t\t.loading-dots div {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttop: 0;\r\n\t\t\t\twidth: 8px;\r\n\t\t\t\theight: 8px;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t\tbackground: #8b5cf6;\r\n\t\t\t\tanimation: loading-dots 1.2s infinite ease-in-out;\r\n\t\t\t}\r\n\r\n\t\t\t.loading-dots div:nth-child(1) {\r\n\t\t\t\tleft: 0;\r\n\t\t\t\tanimation-delay: -0.24s;\r\n\t\t\t}\r\n\t\t\t.loading-dots div:nth-child(2) {\r\n\t\t\t\tleft: 16px;\r\n\t\t\t\tanimation-delay: -0.12s;\r\n\t\t\t}\r\n\t\t\t.loading-dots div:nth-child(3) {\r\n\t\t\t\tleft: 32px;\r\n\t\t\t\tanimation-delay: 0;\r\n\t\t\t}\r\n\r\n\t\t\t@keyframes loading-dots {\r\n\t\t\t\t0%,\r\n\t\t\t\t80%,\r\n\t\t\t\t100% {\r\n\t\t\t\t\ttransform: scale(0);\r\n\t\t\t\t}\r\n\t\t\t\t40% {\r\n\t\t\t\t\ttransform: scale(1);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t/* Responsive adjustments */\r\n\t\t\t@media (max-width: 640px) {\r\n\t\t\t\t.podcast-player-container {\r\n\t\t\t\t\tpadding: 1rem;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t:host ::ng-deep .podcast-modal .p-dialog-header {\r\n\t\t\t\t\tpadding: 1rem;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.bg-gradient-to-br {\r\n\t\t\t\t\tpadding: 1.5rem;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t`,\r\n\t],\r\n})\r\nexport class PodcastModalComponent implements OnDestroy {\r\n\t@Input() data: PodcastModalData = {\r\n\t\tvisible: false,\r\n\t\tgenerating: false,\r\n\t\turl: '',\r\n\t\ttitle: '',\r\n\t};\r\n\r\n\t@Output() close = new EventEmitter<void>();\r\n\t@Output() generate = new EventEmitter<string>();\r\n\r\n\t// Internal state\r\n\tprivate generating = signal<boolean>(false);\r\n\r\n\tconstructor(\r\n\t\tprivate http: HttpClient,\r\n\t\tprivate messageService: MessageService,\r\n\t) {}\r\n\r\n\tngOnDestroy(): void {\r\n\t\t// Cleanup any ongoing requests or timers\r\n\t}\r\n\r\n\tpublic onClose(): void {\r\n\t\tthis.close.emit();\r\n\t}\r\n\r\n\tpublic generatePodcast(): void {\r\n\t\tif (this.data.projectId) {\r\n\t\t\tthis.generate.emit(this.data.projectId);\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Reproduz podcast existente ou gera novo\r\n\t */\r\n\tpublic async playProjectPodcast(projectId: string): Promise<void> {\r\n\t\tif (!projectId) return;\r\n\r\n\t\ttry {\r\n\t\t\t// Verificar se já existe podcast gerado\r\n\t\t\tconst response = await this.http\r\n\t\t\t\t.get<any>(`http://localhost:8040/podcasts/projects/${projectId}/podcast`)\r\n\t\t\t\t.toPromise();\r\n\r\n\t\t\tif (response?.data?.has_podcast && response?.data?.podcast_id) {\r\n\t\t\t\t// Podcast já existe, reproduzir\r\n\t\t\t\tthis.openPodcastPlayer(response.data.podcast_id, this.data.title);\r\n\t\t\t} else {\r\n\t\t\t\t// Gerar novo podcast\r\n\t\t\t\tawait this.generateProjectPodcast(projectId);\r\n\t\t\t}\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('Erro ao verificar podcast:', error);\r\n\t\t\t// Se erro, tentar gerar novo\r\n\t\t\tawait this.generateProjectPodcast(projectId);\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Gera podcast para o projeto\r\n\t */\r\n\tprivate async generateProjectPodcast(projectId: string): Promise<void> {\r\n\t\tif (!projectId) return;\r\n\r\n\t\ttry {\r\n\t\t\tthis.generating.set(true);\r\n\r\n\t\t\tthis.messageService.add({\r\n\t\t\t\tseverity: 'info',\r\n\t\t\t\tsummary: '🎙️ Gerando Podcast',\r\n\t\t\t\tdetail: 'Criando podcast persuasivo sobre o projeto. Aguarde 1-2 minutos...',\r\n\t\t\t\tlife: 8000,\r\n\t\t\t});\r\n\r\n\t\t\t// Solicitar geração do podcast\r\n\t\t\tconst response = await this.http\r\n\t\t\t\t.post<any>(`http://localhost:8040/podcasts/projects/${projectId}/podcast/generate`, {})\r\n\t\t\t\t.toPromise();\r\n\r\n\t\t\tif (response?.data?.podcast_id) {\r\n\t\t\t\t// Monitorar status da geração\r\n\t\t\t\tawait this.monitorPodcastGeneration(response.data.podcast_id);\r\n\t\t\t}\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('Erro ao gerar podcast:', error);\r\n\t\t\tthis.generating.set(false);\r\n\r\n\t\t\tthis.messageService.add({\r\n\t\t\t\tseverity: 'error',\r\n\t\t\t\tsummary: 'Erro ao Gerar Podcast',\r\n\t\t\t\tdetail: 'Não foi possível gerar o podcast. Tente novamente.',\r\n\t\t\t\tlife: 5000,\r\n\t\t\t});\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Monitora status da geração do podcast\r\n\t */\r\n\tprivate async monitorPodcastGeneration(podcastId: string): Promise<void> {\r\n\t\tconst checkStatus = async (): Promise<void> => {\r\n\t\t\ttry {\r\n\t\t\t\tconst status = await this.http\r\n\t\t\t\t\t.get<any>(`http://localhost:8040/podcasts/${podcastId}/status`)\r\n\t\t\t\t\t.toPromise();\r\n\r\n\t\t\t\tif (status?.data?.status === 'completed') {\r\n\t\t\t\t\tthis.generating.set(false);\r\n\r\n\t\t\t\t\tthis.messageService.add({\r\n\t\t\t\t\t\tseverity: 'success',\r\n\t\t\t\t\t\tsummary: '✅ Podcast Pronto!',\r\n\t\t\t\t\t\tdetail: 'O podcast foi gerado com sucesso.',\r\n\t\t\t\t\t\tlife: 3000,\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\t// Abrir player\r\n\t\t\t\t\tthis.openPodcastPlayer(podcastId, this.data.title);\r\n\t\t\t\t} else if (status?.data?.status === 'error') {\r\n\t\t\t\t\tthis.generating.set(false);\r\n\r\n\t\t\t\t\tthis.messageService.add({\r\n\t\t\t\t\t\tseverity: 'error',\r\n\t\t\t\t\t\tsummary: 'Erro na Geração',\r\n\t\t\t\t\t\tdetail: 'Ocorreu um erro durante a geração do podcast.',\r\n\t\t\t\t\t\tlife: 5000,\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// Ainda processando, verificar novamente\r\n\t\t\t\t\tsetTimeout(() => checkStatus(), 5000);\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('Erro ao verificar status:', error);\r\n\t\t\t\tthis.generating.set(false);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t// Iniciar verificação\r\n\t\tsetTimeout(() => checkStatus(), 3000);\r\n\t}\r\n\r\n\t/**\r\n\t * Abre player de áudio para o podcast\r\n\t */\r\n\tprivate openPodcastPlayer(podcastId: string, projectTitle: string): void {\r\n\t\t// Configurar dados do podcast\r\n\t\tconst audioUrl = `http://localhost:8040/podcasts/${podcastId}/audio`;\r\n\r\n\t\t// Sanitizar o título para evitar caracteres especiais\r\n\t\tconst sanitizedTitle = projectTitle.replace(/[<>&\"']/g, '');\r\n\r\n\t\t// Emitir dados atualizados para o parent component\r\n\t\tthis.data = {\r\n\t\t\t...this.data,\r\n\t\t\turl: audioUrl,\r\n\t\t\ttitle: sanitizedTitle,\r\n\t\t\tgenerating: false,\r\n\t\t};\r\n\t}\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAaC,MAAM,EAAEC,MAAM,QAAQ,eAAe;AACzF,SAASC,cAAc,QAAQ,aAAa;AAC5C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,YAAY,QAAQ,gBAAgB;AA+MtC,IAAMC,qBAAqB,GAA3B,MAAMA,qBAAqB;EAexBC,IAAA;EACAC,cAAA;EAfAC,IAAI,GAAqB;IACjCC,OAAO,EAAE,KAAK;IACdC,UAAU,EAAE,KAAK;IACjBC,GAAG,EAAE,EAAE;IACPC,KAAK,EAAE;GACP;EAESC,KAAK,GAAG,IAAIf,YAAY,EAAQ;EAChCgB,QAAQ,GAAG,IAAIhB,YAAY,EAAU;EAE/C;EACQY,UAAU,GAAGT,MAAM,CAAU,KAAK,CAAC;EAE3Cc,YACST,IAAgB,EAChBC,cAA8B;IAD9B,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,cAAc,GAAdA,cAAc;EACpB;EAEHS,WAAWA,CAAA;IACV;EAAA;EAGMC,OAAOA,CAAA;IACb,IAAI,CAACJ,KAAK,CAACK,IAAI,EAAE;EAClB;EAEOC,eAAeA,CAAA;IACrB,IAAI,IAAI,CAACX,IAAI,CAACY,SAAS,EAAE;MACxB,IAAI,CAACN,QAAQ,CAACI,IAAI,CAAC,IAAI,CAACV,IAAI,CAACY,SAAS,CAAC;IACxC;EACD;EAEA;;;EAGaC,kBAAkBA,CAACD,SAAiB;IAAA,IAAAE,KAAA;IAAA,OAAAC,iBAAA;MAChD,IAAI,CAACH,SAAS,EAAE;MAEhB,IAAI;QACH;QACA,MAAMI,QAAQ,SAASF,KAAI,CAAChB,IAAI,CAC9BmB,GAAG,CAAM,2CAA2CL,SAAS,UAAU,CAAC,CACxEM,SAAS,EAAE;QAEb,IAAIF,QAAQ,EAAEhB,IAAI,EAAEmB,WAAW,IAAIH,QAAQ,EAAEhB,IAAI,EAAEoB,UAAU,EAAE;UAC9D;UACAN,KAAI,CAACO,iBAAiB,CAACL,QAAQ,CAAChB,IAAI,CAACoB,UAAU,EAAEN,KAAI,CAACd,IAAI,CAACI,KAAK,CAAC;QAClE,CAAC,MAAM;UACN;UACA,MAAMU,KAAI,CAACQ,sBAAsB,CAACV,SAAS,CAAC;QAC7C;MACD,CAAC,CAAC,OAAOW,KAAK,EAAE;QACfC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD;QACA,MAAMT,KAAI,CAACQ,sBAAsB,CAACV,SAAS,CAAC;MAC7C;IAAC;EACF;EAEA;;;EAGcU,sBAAsBA,CAACV,SAAiB;IAAA,IAAAa,MAAA;IAAA,OAAAV,iBAAA;MACrD,IAAI,CAACH,SAAS,EAAE;MAEhB,IAAI;QACHa,MAAI,CAACvB,UAAU,CAACwB,GAAG,CAAC,IAAI,CAAC;QAEzBD,MAAI,CAAC1B,cAAc,CAAC4B,GAAG,CAAC;UACvBC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,qBAAqB;UAC9BC,MAAM,EAAE,oEAAoE;UAC5EC,IAAI,EAAE;SACN,CAAC;QAEF;QACA,MAAMf,QAAQ,SAASS,MAAI,CAAC3B,IAAI,CAC9BkC,IAAI,CAAM,2CAA2CpB,SAAS,mBAAmB,EAAE,EAAE,CAAC,CACtFM,SAAS,EAAE;QAEb,IAAIF,QAAQ,EAAEhB,IAAI,EAAEoB,UAAU,EAAE;UAC/B;UACA,MAAMK,MAAI,CAACQ,wBAAwB,CAACjB,QAAQ,CAAChB,IAAI,CAACoB,UAAU,CAAC;QAC9D;MACD,CAAC,CAAC,OAAOG,KAAK,EAAE;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9CE,MAAI,CAACvB,UAAU,CAACwB,GAAG,CAAC,KAAK,CAAC;QAE1BD,MAAI,CAAC1B,cAAc,CAAC4B,GAAG,CAAC;UACvBC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,uBAAuB;UAChCC,MAAM,EAAE,oDAAoD;UAC5DC,IAAI,EAAE;SACN,CAAC;MACH;IAAC;EACF;EAEA;;;EAGcE,wBAAwBA,CAACC,SAAiB;IAAA,IAAAC,MAAA;IAAA,OAAApB,iBAAA;MACvD,MAAMqB,YAAW;QAAA,IAAAC,IAAA,GAAAtB,iBAAA,CAAG,aAA0B;UAC7C,IAAI;YACH,MAAMuB,MAAM,SAASH,MAAI,CAACrC,IAAI,CAC5BmB,GAAG,CAAM,kCAAkCiB,SAAS,SAAS,CAAC,CAC9DhB,SAAS,EAAE;YAEb,IAAIoB,MAAM,EAAEtC,IAAI,EAAEsC,MAAM,KAAK,WAAW,EAAE;cACzCH,MAAI,CAACjC,UAAU,CAACwB,GAAG,CAAC,KAAK,CAAC;cAE1BS,MAAI,CAACpC,cAAc,CAAC4B,GAAG,CAAC;gBACvBC,QAAQ,EAAE,SAAS;gBACnBC,OAAO,EAAE,mBAAmB;gBAC5BC,MAAM,EAAE,mCAAmC;gBAC3CC,IAAI,EAAE;eACN,CAAC;cAEF;cACAI,MAAI,CAACd,iBAAiB,CAACa,SAAS,EAAEC,MAAI,CAACnC,IAAI,CAACI,KAAK,CAAC;YACnD,CAAC,MAAM,IAAIkC,MAAM,EAAEtC,IAAI,EAAEsC,MAAM,KAAK,OAAO,EAAE;cAC5CH,MAAI,CAACjC,UAAU,CAACwB,GAAG,CAAC,KAAK,CAAC;cAE1BS,MAAI,CAACpC,cAAc,CAAC4B,GAAG,CAAC;gBACvBC,QAAQ,EAAE,OAAO;gBACjBC,OAAO,EAAE,iBAAiB;gBAC1BC,MAAM,EAAE,+CAA+C;gBACvDC,IAAI,EAAE;eACN,CAAC;YACH,CAAC,MAAM;cACN;cACAQ,UAAU,CAAC,MAAMH,YAAW,EAAE,EAAE,IAAI,CAAC;YACtC;UACD,CAAC,CAAC,OAAOb,KAAK,EAAE;YACfC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;YACjDY,MAAI,CAACjC,UAAU,CAACwB,GAAG,CAAC,KAAK,CAAC;UAC3B;QACD,CAAC;QAAA,gBAnCKU,WAAWA,CAAA;UAAA,OAAAC,IAAA,CAAAG,KAAA,OAAAC,SAAA;QAAA;MAAA,GAmChB;MAED;MACAF,UAAU,CAAC,MAAMH,YAAW,EAAE,EAAE,IAAI,CAAC;IAAC;EACvC;EAEA;;;EAGQf,iBAAiBA,CAACa,SAAiB,EAAEQ,YAAoB;IAChE;IACA,MAAMC,QAAQ,GAAG,kCAAkCT,SAAS,QAAQ;IAEpE;IACA,MAAMU,cAAc,GAAGF,YAAY,CAACG,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;IAE3D;IACA,IAAI,CAAC7C,IAAI,GAAG;MACX,GAAG,IAAI,CAACA,IAAI;MACZG,GAAG,EAAEwC,QAAQ;MACbvC,KAAK,EAAEwC,cAAc;MACrB1C,UAAU,EAAE;KACZ;EACF;;;;;;;;YA9JCX;IAAK;;YAOLC;IAAM;;YACNA;IAAM;;;AATKK,qBAAqB,GAAAiD,UAAA,EAhMjCzD,SAAS,CAAC;EACV0D,QAAQ,EAAE,mBAAmB;EAC7BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAC9D,YAAY,EAAES,YAAY,EAAED,YAAY,CAAC;EACnDuD,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAyGT;;CAkFD,CAAC,C,EACWrD,qBAAqB,CAgKjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}