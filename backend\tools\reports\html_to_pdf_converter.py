"""
Conversor de HTML para PDF com formatação profissional
"""
import logging
from datetime import datetime
from typing import Dict, Any
from weasyprint import HTML, CSS
from io import BytesIO

logger = logging.getLogger(__name__)


class HTMLToPDFConverter:
    """Converte HTML estruturado para PDF"""
    
    def convert_to_pdf(self, html_content: str, metadata: Dict[str, Any]) -> bytes:
        """
        Converte HTML para PDF com estilo profissional
        
        Args:
            html_content: Conteúdo HTML estruturado
            metadata: Metadados do relatório
            
        Returns:
            bytes: Conteúdo do PDF
        """
        try:
            logger.info(f"🔄 Convertendo HTML para PDF - {metadata.get('client_name', 'Cliente')}")
            
            # Criar documento HTML completo com estilos
            full_html = self._create_full_document(html_content, metadata)
            
            # CSS para o PDF
            css = CSS(string=self._get_pdf_styles())
            
            # Converter para PDF
            html = HTML(string=full_html)
            pdf_document = html.render(stylesheets=[css])
            
            # Obter bytes do PDF
            pdf_bytes = BytesIO()
            pdf_document.write_pdf(pdf_bytes)
            
            result = pdf_bytes.getvalue()
            logger.info(f"✅ PDF gerado com sucesso - {len(result)} bytes")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Erro ao converter HTML para PDF: {str(e)}")
            raise
    
    def _create_full_document(self, content: str, metadata: Dict[str, Any]) -> str:
        """Cria documento HTML completo"""
        return f"""
        <!DOCTYPE html>
        <html lang="pt-BR">
        <head>
            <meta charset="UTF-8">
            <title>Relatório Executivo - {metadata.get('client_name', 'Cliente')}</title>
        </head>
        <body>
            <header class="main-header">
                <div class="header-content">
                    <div class="logo-section">
                        <h1 class="company-logo">SCOPE AI</h1>
                        <p class="company-tagline">Inteligência Artificial para Análise Empresarial</p>
                    </div>
                    <div class="header-info">
                        <p class="report-date">{self._format_date_portuguese(datetime.now())}</p>
                        <p class="report-type">Relatório Executivo</p>
                        <p class="client-name">{metadata.get('client_name', 'Cliente')}</p>
                    </div>
                </div>
            </header>
            
            <div class="content-wrapper">
                {content}
            </div>
        </body>
        </html>
        """
    
    def _get_pdf_styles(self) -> str:
        """Retorna estilos CSS para o PDF"""
        return """
        /* Configuração de página */
        @page {
            size: A4;
            margin: 2cm;
            
            @bottom-center {
                content: "Página " counter(page);
                font-size: 9pt;
                color: #666;
            }
        }
        
        /* Reset e base */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            font-size: 11pt;
            line-height: 1.6;
            color: #333;
        }
        
        /* Cabeçalho principal */
        .main-header {
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #1a73e8;
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }
        
        .company-logo {
            font-size: 32pt;
            color: #1a73e8;
            font-weight: bold;
            margin: 0;
        }
        
        .company-tagline {
            font-size: 10pt;
            color: #666;
            font-style: italic;
            margin-top: 5px;
        }
        
        .header-info {
            text-align: right;
            font-size: 10pt;
            color: #666;
        }
        
        .report-type {
            font-weight: bold;
            color: #333;
            font-size: 12pt;
        }
        
        /* Seções */
        section {
            margin-bottom: 30px;
            page-break-inside: avoid;
        }
        
        section > h1 {
            font-size: 20pt;
            color: #1a73e8;
            margin: 30px 0 20px 0;
            padding-bottom: 10px;
            border-bottom: 2px solid #e0e0e0;
            page-break-after: avoid;
        }
        
        h2 {
            font-size: 14pt;
            color: #1a73e8;
            margin: 20px 0 12px 0;
            page-break-after: avoid;
        }
        
        h3 {
            font-size: 12pt;
            color: #333;
            margin: 15px 0 8px 0;
            page-break-after: avoid;
        }
        
        /* Parágrafos e texto */
        p {
            margin-bottom: 12px;
            text-align: justify;
        }
        
        .intro {
            font-size: 12pt;
            color: #555;
            margin-bottom: 20px;
        }
        
        strong {
            font-weight: bold;
            color: #222;
        }
        
        /* Listas */
        ul, ol {
            margin: 10px 0 15px 25px;
        }
        
        li {
            margin-bottom: 6px;
        }
        
        /* Tabelas */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 10pt;
        }
        
        th {
            background-color: #1a73e8;
            color: white;
            padding: 10px;
            text-align: left;
            font-weight: bold;
        }
        
        td {
            padding: 8px;
            border-bottom: 1px solid #e0e0e0;
        }
        
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .info-table td:first-child {
            width: 150px;
            font-weight: bold;
        }
        
        /* Cards de métricas */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin: 20px 0;
        }
        
        .metric-card {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        
        .metric-card h3 {
            font-size: 11pt;
            margin: 0 0 10px 0;
        }
        
        .metric-value {
            font-size: 24pt;
            font-weight: bold;
            color: #1a73e8;
            margin: 10px 0;
        }
        
        .metric-status {
            font-size: 10pt;
            padding: 3px 8px;
            border-radius: 4px;
        }
        
        .metric-status.good {
            background: #e6f4ea;
            color: #1e8e3e;
        }
        
        .metric-status.warning {
            background: #fef7e0;
            color: #f9ab00;
        }
        
        /* Caixas de destaque */
        .key-findings, .impact-summary {
            background: #f8f9fa;
            border-left: 4px solid #1a73e8;
            padding: 15px;
            margin: 15px 0;
        }
        
        /* Estilos executivos */
        .executive-overview {
            background: linear-gradient(135deg, #f8f9fa 0%, #e8f0fe 100%);
            padding: 25px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .company-header h2 {
            font-size: 24pt;
            color: #1a73e8;
            margin-bottom: 5px;
        }
        
        .domain {
            font-size: 12pt;
            color: #666;
            font-style: italic;
        }
        
        .analysis-date {
            font-size: 10pt;
            color: #888;
        }
        
        .executive-metrics {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin: 20px 0;
        }
        
        .metric-executive {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e0e0e0;
            text-align: center;
        }
        
        .metric-label {
            display: block;
            font-size: 10pt;
            color: #666;
            margin-bottom: 8px;
        }
        
        .metric-value-exec {
            display: block;
            font-size: 18pt;
            font-weight: bold;
            color: #1a73e8;
            margin-bottom: 5px;
        }
        
        .metric-trend {
            display: block;
            font-size: 9pt;
            color: #555;
        }
        
        .strategic-overview {
            margin: 25px 0;
        }
        
        .strategic-intro {
            font-size: 12pt;
            line-height: 1.7;
            color: #444;
            margin-bottom: 20px;
        }
        
        .findings-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .finding-critical, .finding-opportunities {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 20px;
        }
        
        .finding-critical h4 {
            color: #d93025;
            margin-bottom: 15px;
        }
        
        .finding-opportunities h4 {
            color: #1e8e3e;
            margin-bottom: 15px;
        }
        
        .investment-overview {
            background: #e6f4ea;
            border: 1px solid #34a853;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
        }
        
        .investment-breakdown {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .investment-amount {
            font-size: 18pt;
            font-weight: bold;
            color: #1e8e3e;
            margin: 10px 0;
        }
        
        .investment-period {
            font-size: 11pt;
            color: #666;
        }
        
        .priority-actions {
            background: #fef7e0;
            border: 1px solid #f9ab00;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
        }
        
        .priority-list {
            margin: 15px 0;
        }
        
        .priority-list li {
            margin-bottom: 12px;
            font-weight: 500;
        }
        
        .competitive-advantage {
            background: #e8f0fe;
            border: 1px solid #1a73e8;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
        }
        
        /* Prioridades */
        .priority-high h3 {
            color: #d93025;
        }
        
        .priority-medium h3 {
            color: #f9ab00;
        }
        
        .priority-low h3 {
            color: #1e8e3e;
        }
        
        /* Utilitários */
        a {
            color: #1a73e8;
            text-decoration: none;
        }
        """
    
    def _format_date_portuguese(self, date: datetime) -> str:
        """Formata data em português"""
        months = {
            1: "Janeiro", 2: "Fevereiro", 3: "Março", 4: "Abril",
            5: "Maio", 6: "Junho", 7: "Julho", 8: "Agosto",
            9: "Setembro", 10: "Outubro", 11: "Novembro", 12: "Dezembro"
        }
        
        return f"{date.day} de {months[date.month]} de {date.year}"