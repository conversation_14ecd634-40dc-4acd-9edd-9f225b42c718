{"ast": null, "code": "import _asyncToGenerator from \"C:/projetos/scope-ai/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nfunction cov_r32n2fhxm() {\n  var path = \"C:\\\\projetos\\\\scope-ai\\\\frontend\\\\src\\\\app\\\\features\\\\projects\\\\components\\\\podcast-modal\\\\podcast-modal.component.ts\";\n  var hash = \"31dbe7625b449d0348c67dbd145a484ae5040d49\";\n  var global = new Function(\"return this\")();\n  var gcv = \"__coverage__\";\n  var coverageData = {\n    path: \"C:\\\\projetos\\\\scope-ai\\\\frontend\\\\src\\\\app\\\\features\\\\projects\\\\components\\\\podcast-modal\\\\podcast-modal.component.ts\",\n    statementMap: {\n      \"0\": {\n        start: {\n          line: 9,\n          column: 28\n        },\n        end: {\n          line: 164,\n          column: 1\n        }\n      },\n      \"1\": {\n        start: {\n          line: 12,\n          column: 11\n        },\n        end: {\n          line: 17,\n          column: 5\n        }\n      },\n      \"2\": {\n        start: {\n          line: 18,\n          column: 12\n        },\n        end: {\n          line: 18,\n          column: 30\n        }\n      },\n      \"3\": {\n        start: {\n          line: 19,\n          column: 15\n        },\n        end: {\n          line: 19,\n          column: 33\n        }\n      },\n      \"4\": {\n        start: {\n          line: 21,\n          column: 17\n        },\n        end: {\n          line: 21,\n          column: 30\n        }\n      },\n      \"5\": {\n        start: {\n          line: 23,\n          column: 8\n        },\n        end: {\n          line: 23,\n          column: 25\n        }\n      },\n      \"6\": {\n        start: {\n          line: 24,\n          column: 8\n        },\n        end: {\n          line: 24,\n          column: 45\n        }\n      },\n      \"7\": {\n        start: {\n          line: 30,\n          column: 8\n        },\n        end: {\n          line: 30,\n          column: 26\n        }\n      },\n      \"8\": {\n        start: {\n          line: 33,\n          column: 8\n        },\n        end: {\n          line: 35,\n          column: 9\n        }\n      },\n      \"9\": {\n        start: {\n          line: 34,\n          column: 12\n        },\n        end: {\n          line: 34,\n          column: 52\n        }\n      },\n      \"10\": {\n        start: {\n          line: 41,\n          column: 8\n        },\n        end: {\n          line: 42,\n          column: 19\n        }\n      },\n      \"11\": {\n        start: {\n          line: 42,\n          column: 12\n        },\n        end: {\n          line: 42,\n          column: 19\n        }\n      },\n      \"12\": {\n        start: {\n          line: 43,\n          column: 8\n        },\n        end: {\n          line: 61,\n          column: 9\n        }\n      },\n      \"13\": {\n        start: {\n          line: 45,\n          column: 29\n        },\n        end: {\n          line: 47,\n          column: 28\n        }\n      },\n      \"14\": {\n        start: {\n          line: 48,\n          column: 12\n        },\n        end: {\n          line: 55,\n          column: 13\n        }\n      },\n      \"15\": {\n        start: {\n          line: 50,\n          column: 16\n        },\n        end: {\n          line: 50,\n          column: 82\n        }\n      },\n      \"16\": {\n        start: {\n          line: 54,\n          column: 16\n        },\n        end: {\n          line: 54,\n          column: 61\n        }\n      },\n      \"17\": {\n        start: {\n          line: 58,\n          column: 12\n        },\n        end: {\n          line: 58,\n          column: 63\n        }\n      },\n      \"18\": {\n        start: {\n          line: 60,\n          column: 12\n        },\n        end: {\n          line: 60,\n          column: 57\n        }\n      },\n      \"19\": {\n        start: {\n          line: 67,\n          column: 8\n        },\n        end: {\n          line: 68,\n          column: 19\n        }\n      },\n      \"20\": {\n        start: {\n          line: 68,\n          column: 12\n        },\n        end: {\n          line: 68,\n          column: 19\n        }\n      },\n      \"21\": {\n        start: {\n          line: 69,\n          column: 8\n        },\n        end: {\n          line: 95,\n          column: 9\n        }\n      },\n      \"22\": {\n        start: {\n          line: 70,\n          column: 12\n        },\n        end: {\n          line: 70,\n          column: 38\n        }\n      },\n      \"23\": {\n        start: {\n          line: 71,\n          column: 12\n        },\n        end: {\n          line: 76,\n          column: 15\n        }\n      },\n      \"24\": {\n        start: {\n          line: 78,\n          column: 29\n        },\n        end: {\n          line: 80,\n          column: 28\n        }\n      },\n      \"25\": {\n        start: {\n          line: 81,\n          column: 12\n        },\n        end: {\n          line: 84,\n          column: 13\n        }\n      },\n      \"26\": {\n        start: {\n          line: 83,\n          column: 16\n        },\n        end: {\n          line: 83,\n          column: 78\n        }\n      },\n      \"27\": {\n        start: {\n          line: 87,\n          column: 12\n        },\n        end: {\n          line: 87,\n          column: 59\n        }\n      },\n      \"28\": {\n        start: {\n          line: 88,\n          column: 12\n        },\n        end: {\n          line: 88,\n          column: 39\n        }\n      },\n      \"29\": {\n        start: {\n          line: 89,\n          column: 12\n        },\n        end: {\n          line: 94,\n          column: 15\n        }\n      },\n      \"30\": {\n        start: {\n          line: 101,\n          column: 28\n        },\n        end: {\n          line: 135,\n          column: 9\n        }\n      },\n      \"31\": {\n        start: {\n          line: 102,\n          column: 12\n        },\n        end: {\n          line: 134,\n          column: 13\n        }\n      },\n      \"32\": {\n        start: {\n          line: 103,\n          column: 31\n        },\n        end: {\n          line: 105,\n          column: 32\n        }\n      },\n      \"33\": {\n        start: {\n          line: 106,\n          column: 16\n        },\n        end: {\n          line: 129,\n          column: 17\n        }\n      },\n      \"34\": {\n        start: {\n          line: 107,\n          column: 20\n        },\n        end: {\n          line: 107,\n          column: 47\n        }\n      },\n      \"35\": {\n        start: {\n          line: 108,\n          column: 20\n        },\n        end: {\n          line: 113,\n          column: 23\n        }\n      },\n      \"36\": {\n        start: {\n          line: 115,\n          column: 20\n        },\n        end: {\n          line: 115,\n          column: 71\n        }\n      },\n      \"37\": {\n        start: {\n          line: 117,\n          column: 21\n        },\n        end: {\n          line: 129,\n          column: 17\n        }\n      },\n      \"38\": {\n        start: {\n          line: 118,\n          column: 20\n        },\n        end: {\n          line: 118,\n          column: 47\n        }\n      },\n      \"39\": {\n        start: {\n          line: 119,\n          column: 20\n        },\n        end: {\n          line: 124,\n          column: 23\n        }\n      },\n      \"40\": {\n        start: {\n          line: 128,\n          column: 20\n        },\n        end: {\n          line: 128,\n          column: 58\n        }\n      },\n      \"41\": {\n        start: {\n          line: 128,\n          column: 37\n        },\n        end: {\n          line: 128,\n          column: 50\n        }\n      },\n      \"42\": {\n        start: {\n          line: 132,\n          column: 16\n        },\n        end: {\n          line: 132,\n          column: 66\n        }\n      },\n      \"43\": {\n        start: {\n          line: 133,\n          column: 16\n        },\n        end: {\n          line: 133,\n          column: 43\n        }\n      },\n      \"44\": {\n        start: {\n          line: 137,\n          column: 8\n        },\n        end: {\n          line: 137,\n          column: 46\n        }\n      },\n      \"45\": {\n        start: {\n          line: 137,\n          column: 25\n        },\n        end: {\n          line: 137,\n          column: 38\n        }\n      },\n      \"46\": {\n        start: {\n          line: 144,\n          column: 25\n        },\n        end: {\n          line: 144,\n          column: 76\n        }\n      },\n      \"47\": {\n        start: {\n          line: 146,\n          column: 31\n        },\n        end: {\n          line: 146,\n          column: 67\n        }\n      },\n      \"48\": {\n        start: {\n          line: 148,\n          column: 8\n        },\n        end: {\n          line: 153,\n          column: 10\n        }\n      },\n      \"49\": {\n        start: {\n          line: 155,\n          column: 28\n        },\n        end: {\n          line: 158,\n          column: 5\n        }\n      },\n      \"50\": {\n        start: {\n          line: 155,\n          column: 34\n        },\n        end: {\n          line: 158,\n          column: 5\n        }\n      },\n      \"51\": {\n        start: {\n          line: 159,\n          column: 28\n        },\n        end: {\n          line: 163,\n          column: 5\n        }\n      },\n      \"52\": {\n        start: {\n          line: 165,\n          column: 0\n        },\n        end: {\n          line: 278,\n          column: 26\n        }\n      }\n    },\n    fnMap: {\n      \"0\": {\n        name: \"(anonymous_0)\",\n        decl: {\n          start: {\n            line: 22,\n            column: 4\n          },\n          end: {\n            line: 22,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 22,\n            column: 38\n          },\n          end: {\n            line: 25,\n            column: 5\n          }\n        },\n        line: 22\n      },\n      \"1\": {\n        name: \"(anonymous_1)\",\n        decl: {\n          start: {\n            line: 26,\n            column: 4\n          },\n          end: {\n            line: 26,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 26,\n            column: 18\n          },\n          end: {\n            line: 28,\n            column: 5\n          }\n        },\n        line: 26\n      },\n      \"2\": {\n        name: \"(anonymous_2)\",\n        decl: {\n          start: {\n            line: 29,\n            column: 4\n          },\n          end: {\n            line: 29,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 29,\n            column: 14\n          },\n          end: {\n            line: 31,\n            column: 5\n          }\n        },\n        line: 29\n      },\n      \"3\": {\n        name: \"(anonymous_3)\",\n        decl: {\n          start: {\n            line: 32,\n            column: 4\n          },\n          end: {\n            line: 32,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 32,\n            column: 22\n          },\n          end: {\n            line: 36,\n            column: 5\n          }\n        },\n        line: 32\n      },\n      \"4\": {\n        name: \"(anonymous_4)\",\n        decl: {\n          start: {\n            line: 40,\n            column: 4\n          },\n          end: {\n            line: 40,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 40,\n            column: 40\n          },\n          end: {\n            line: 62,\n            column: 5\n          }\n        },\n        line: 40\n      },\n      \"5\": {\n        name: \"(anonymous_5)\",\n        decl: {\n          start: {\n            line: 66,\n            column: 4\n          },\n          end: {\n            line: 66,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 66,\n            column: 44\n          },\n          end: {\n            line: 96,\n            column: 5\n          }\n        },\n        line: 66\n      },\n      \"6\": {\n        name: \"(anonymous_6)\",\n        decl: {\n          start: {\n            line: 100,\n            column: 4\n          },\n          end: {\n            line: 100,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 100,\n            column: 46\n          },\n          end: {\n            line: 138,\n            column: 5\n          }\n        },\n        line: 100\n      },\n      \"7\": {\n        name: \"(anonymous_7)\",\n        decl: {\n          start: {\n            line: 101,\n            column: 28\n          },\n          end: {\n            line: 101,\n            column: 29\n          }\n        },\n        loc: {\n          start: {\n            line: 101,\n            column: 40\n          },\n          end: {\n            line: 135,\n            column: 9\n          }\n        },\n        line: 101\n      },\n      \"8\": {\n        name: \"(anonymous_8)\",\n        decl: {\n          start: {\n            line: 128,\n            column: 31\n          },\n          end: {\n            line: 128,\n            column: 32\n          }\n        },\n        loc: {\n          start: {\n            line: 128,\n            column: 37\n          },\n          end: {\n            line: 128,\n            column: 50\n          }\n        },\n        line: 128\n      },\n      \"9\": {\n        name: \"(anonymous_9)\",\n        decl: {\n          start: {\n            line: 137,\n            column: 19\n          },\n          end: {\n            line: 137,\n            column: 20\n          }\n        },\n        loc: {\n          start: {\n            line: 137,\n            column: 25\n          },\n          end: {\n            line: 137,\n            column: 38\n          }\n        },\n        line: 137\n      },\n      \"10\": {\n        name: \"(anonymous_10)\",\n        decl: {\n          start: {\n            line: 142,\n            column: 4\n          },\n          end: {\n            line: 142,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 142,\n            column: 47\n          },\n          end: {\n            line: 154,\n            column: 5\n          }\n        },\n        line: 142\n      },\n      \"11\": {\n        name: \"(anonymous_11)\",\n        decl: {\n          start: {\n            line: 155,\n            column: 28\n          },\n          end: {\n            line: 155,\n            column: 29\n          }\n        },\n        loc: {\n          start: {\n            line: 155,\n            column: 34\n          },\n          end: {\n            line: 158,\n            column: 5\n          }\n        },\n        line: 155\n      }\n    },\n    branchMap: {\n      \"0\": {\n        loc: {\n          start: {\n            line: 33,\n            column: 8\n          },\n          end: {\n            line: 35,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 33,\n            column: 8\n          },\n          end: {\n            line: 35,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 33\n      },\n      \"1\": {\n        loc: {\n          start: {\n            line: 41,\n            column: 8\n          },\n          end: {\n            line: 42,\n            column: 19\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 41,\n            column: 8\n          },\n          end: {\n            line: 42,\n            column: 19\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 41\n      },\n      \"2\": {\n        loc: {\n          start: {\n            line: 48,\n            column: 12\n          },\n          end: {\n            line: 55,\n            column: 13\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 48,\n            column: 12\n          },\n          end: {\n            line: 55,\n            column: 13\n          }\n        }, {\n          start: {\n            line: 52,\n            column: 17\n          },\n          end: {\n            line: 55,\n            column: 13\n          }\n        }],\n        line: 48\n      },\n      \"3\": {\n        loc: {\n          start: {\n            line: 48,\n            column: 16\n          },\n          end: {\n            line: 48,\n            column: 73\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 48,\n            column: 16\n          },\n          end: {\n            line: 48,\n            column: 43\n          }\n        }, {\n          start: {\n            line: 48,\n            column: 47\n          },\n          end: {\n            line: 48,\n            column: 73\n          }\n        }],\n        line: 48\n      },\n      \"4\": {\n        loc: {\n          start: {\n            line: 67,\n            column: 8\n          },\n          end: {\n            line: 68,\n            column: 19\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 67,\n            column: 8\n          },\n          end: {\n            line: 68,\n            column: 19\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 67\n      },\n      \"5\": {\n        loc: {\n          start: {\n            line: 81,\n            column: 12\n          },\n          end: {\n            line: 84,\n            column: 13\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 81,\n            column: 12\n          },\n          end: {\n            line: 84,\n            column: 13\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 81\n      },\n      \"6\": {\n        loc: {\n          start: {\n            line: 106,\n            column: 16\n          },\n          end: {\n            line: 129,\n            column: 17\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 106,\n            column: 16\n          },\n          end: {\n            line: 129,\n            column: 17\n          }\n        }, {\n          start: {\n            line: 117,\n            column: 21\n          },\n          end: {\n            line: 129,\n            column: 17\n          }\n        }],\n        line: 106\n      },\n      \"7\": {\n        loc: {\n          start: {\n            line: 117,\n            column: 21\n          },\n          end: {\n            line: 129,\n            column: 17\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 117,\n            column: 21\n          },\n          end: {\n            line: 129,\n            column: 17\n          }\n        }, {\n          start: {\n            line: 126,\n            column: 21\n          },\n          end: {\n            line: 129,\n            column: 17\n          }\n        }],\n        line: 117\n      }\n    },\n    s: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0,\n      \"18\": 0,\n      \"19\": 0,\n      \"20\": 0,\n      \"21\": 0,\n      \"22\": 0,\n      \"23\": 0,\n      \"24\": 0,\n      \"25\": 0,\n      \"26\": 0,\n      \"27\": 0,\n      \"28\": 0,\n      \"29\": 0,\n      \"30\": 0,\n      \"31\": 0,\n      \"32\": 0,\n      \"33\": 0,\n      \"34\": 0,\n      \"35\": 0,\n      \"36\": 0,\n      \"37\": 0,\n      \"38\": 0,\n      \"39\": 0,\n      \"40\": 0,\n      \"41\": 0,\n      \"42\": 0,\n      \"43\": 0,\n      \"44\": 0,\n      \"45\": 0,\n      \"46\": 0,\n      \"47\": 0,\n      \"48\": 0,\n      \"49\": 0,\n      \"50\": 0,\n      \"51\": 0,\n      \"52\": 0\n    },\n    f: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0\n    },\n    b: {\n      \"0\": [0, 0],\n      \"1\": [0, 0],\n      \"2\": [0, 0],\n      \"3\": [0, 0],\n      \"4\": [0, 0],\n      \"5\": [0, 0],\n      \"6\": [0, 0],\n      \"7\": [0, 0]\n    },\n    inputSourceMap: {\n      version: 3,\n      file: \"podcast-modal.component.js\",\n      sourceRoot: \"\",\n      sources: [\"C:\\\\projetos\\\\scope-ai\\\\frontend\\\\src\\\\app\\\\features\\\\projects\\\\components\\\\podcast-modal\\\\podcast-modal.component.ts\"],\n      names: [],\n      mappings: \";;AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAC/C,OAAO,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAClD,OAAO,EAAE,SAAS,EAAE,YAAY,EAAE,KAAK,EAAa,MAAM,EAAE,MAAM,EAAE,MAAM,eAAe,CAAC;AAC1F,OAAO,EAAE,cAAc,EAAE,MAAM,aAAa,CAAC;AAC7C,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAC9C,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AA+MvC,IAAM,qBAAqB,GAA3B,MAAM,qBAAqB;IAexB;IACA;IAfA,IAAI,GAAqB;QACjC,OAAO,EAAE,KAAK;QACd,UAAU,EAAE,KAAK;QACjB,GAAG,EAAE,EAAE;QACP,KAAK,EAAE,EAAE;KACT;IAES,KAAK,GAAG,IAAI,YAAY,EAAQ;IAChC,QAAQ,GAAG,IAAI,YAAY,EAAU;IAE/C,iBAAiB;IACT,UAAU,GAAG,MAAM,CAAU,KAAK,CAAC,CAAC;IAE5C,YACS,IAAgB,EAChB,cAA8B;QAD9B,SAAI,GAAJ,IAAI,CAAY;QAChB,mBAAc,GAAd,cAAc,CAAgB;IACpC,CAAC;IAEJ,WAAW;QACV,yCAAyC;IAC1C,CAAC;IAEM,OAAO;QACb,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;IACnB,CAAC;IAEM,eAAe;QACrB,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACzB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACzC,CAAC;IACF,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,kBAAkB,CAAC,SAAiB;QAChD,IAAI,CAAC,SAAS;YAAE,OAAO;QAEvB,IAAI,CAAC;YACJ,wCAAwC;YACxC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,IAAI;iBAC9B,GAAG,CAAM,2CAA2C,SAAS,UAAU,CAAC;iBACxE,SAAS,EAAE,CAAC;YAEd,IAAI,QAAQ,EAAE,IAAI,EAAE,WAAW,IAAI,QAAQ,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;gBAC/D,gCAAgC;gBAChC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnE,CAAC;iBAAM,CAAC;gBACP,qBAAqB;gBACrB,MAAM,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;YAC9C,CAAC;QACF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAChB,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,6BAA6B;YAC7B,MAAM,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;QAC9C,CAAC;IACF,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAAC,SAAiB;QACrD,IAAI,CAAC,SAAS;YAAE,OAAO;QAEvB,IAAI,CAAC;YACJ,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAE1B,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;gBACvB,QAAQ,EAAE,MAAM;gBAChB,OAAO,EAAE,qBAAqB;gBAC9B,MAAM,EAAE,oEAAoE;gBAC5E,IAAI,EAAE,IAAI;aACV,CAAC,CAAC;YAEH,+BAA+B;YAC/B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,IAAI;iBAC9B,IAAI,CAAM,2CAA2C,SAAS,mBAAmB,EAAE,EAAE,CAAC;iBACtF,SAAS,EAAE,CAAC;YAEd,IAAI,QAAQ,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;gBAChC,8BAA8B;gBAC9B,MAAM,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC/D,CAAC;QACF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAChB,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC/C,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAE3B,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;gBACvB,QAAQ,EAAE,OAAO;gBACjB,OAAO,EAAE,uBAAuB;gBAChC,MAAM,EAAE,oDAAoD;gBAC5D,IAAI,EAAE,IAAI;aACV,CAAC,CAAC;QACJ,CAAC;IACF,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,wBAAwB,CAAC,SAAiB;QACvD,MAAM,WAAW,GAAG,KAAK,IAAmB,EAAE;YAC7C,IAAI,CAAC;gBACJ,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI;qBAC5B,GAAG,CAAM,kCAAkC,SAAS,SAAS,CAAC;qBAC9D,SAAS,EAAE,CAAC;gBAEd,IAAI,MAAM,EAAE,IAAI,EAAE,MAAM,KAAK,WAAW,EAAE,CAAC;oBAC1C,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;oBAE3B,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;wBACvB,QAAQ,EAAE,SAAS;wBACnB,OAAO,EAAE,mBAAmB;wBAC5B,MAAM,EAAE,mCAAmC;wBAC3C,IAAI,EAAE,IAAI;qBACV,CAAC,CAAC;oBAEH,eAAe;oBACf,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACpD,CAAC;qBAAM,IAAI,MAAM,EAAE,IAAI,EAAE,MAAM,KAAK,OAAO,EAAE,CAAC;oBAC7C,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;oBAE3B,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;wBACvB,QAAQ,EAAE,OAAO;wBACjB,OAAO,EAAE,iBAAiB;wBAC1B,MAAM,EAAE,+CAA+C;wBACvD,IAAI,EAAE,IAAI;qBACV,CAAC,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACP,yCAAyC;oBACzC,UAAU,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC;gBACvC,CAAC;YACF,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBAChB,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;gBAClD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAC5B,CAAC;QACF,CAAC,CAAC;QAEF,sBAAsB;QACtB,UAAU,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,SAAiB,EAAE,YAAoB;QAChE,8BAA8B;QAC9B,MAAM,QAAQ,GAAG,kCAAkC,SAAS,QAAQ,CAAC;QAErE,sDAAsD;QACtD,MAAM,cAAc,GAAG,YAAY,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;QAE5D,mDAAmD;QACnD,IAAI,CAAC,IAAI,GAAG;YACX,GAAG,IAAI,CAAC,IAAI;YACZ,GAAG,EAAE,QAAQ;YACb,KAAK,EAAE,cAAc;YACrB,UAAU,EAAE,KAAK;SACjB,CAAC;IACH,CAAC;;;;;;uBA9JA,KAAK;wBAOL,MAAM;2BACN,MAAM;;;AATK,qBAAqB;IAhMjC,SAAS,CAAC;QACV,QAAQ,EAAE,mBAAmB;QAC7B,UAAU,EAAE,IAAI;QAChB,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,CAAC;QACnD,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAyGT;;KAkFD,CAAC;GACW,qBAAqB,CAgKjC\",\n      sourcesContent: [\"import { CommonModule } from '@angular/common';\\r\\nimport { HttpClient } from '@angular/common/http';\\r\\nimport { Component, EventEmitter, Input, OnDestroy, Output, signal } from '@angular/core';\\r\\nimport { MessageService } from 'primeng/api';\\r\\nimport { ButtonModule } from 'primeng/button';\\r\\nimport { DialogModule } from 'primeng/dialog';\\r\\n\\r\\nexport interface PodcastModalData {\\r\\n\\tvisible: boolean;\\r\\n\\tgenerating: boolean;\\r\\n\\turl: string;\\r\\n\\ttitle: string;\\r\\n\\tprojectId?: string;\\r\\n}\\r\\n\\r\\nexport interface PodcastModalEvents {\\r\\n\\tonClose: () => void;\\r\\n\\tonGenerate?: (projectId: string) => void;\\r\\n}\\r\\n\\r\\n@Component({\\r\\n\\tselector: 'app-podcast-modal',\\r\\n\\tstandalone: true,\\r\\n\\timports: [CommonModule, DialogModule, ButtonModule],\\r\\n\\ttemplate: `\\r\\n\\t\\t<p-dialog\\r\\n\\t\\t\\t[visible]=\\\"data.visible\\\"\\r\\n\\t\\t\\t[modal]=\\\"true\\\"\\r\\n\\t\\t\\t[closable]=\\\"true\\\"\\r\\n\\t\\t\\t[draggable]=\\\"false\\\"\\r\\n\\t\\t\\t[resizable]=\\\"false\\\"\\r\\n\\t\\t\\tstyleClass=\\\"podcast-modal\\\"\\r\\n\\t\\t\\t[style]=\\\"{ width: '90vw', maxWidth: '600px' }\\\"\\r\\n\\t\\t\\t(onHide)=\\\"onClose()\\\"\\r\\n\\t\\t>\\r\\n\\t\\t\\t<ng-template pTemplate=\\\"header\\\">\\r\\n\\t\\t\\t\\t<div class=\\\"flex items-center gap-3\\\">\\r\\n\\t\\t\\t\\t\\t<i class=\\\"pi pi-microphone text-purple-600 text-2xl\\\"></i>\\r\\n\\t\\t\\t\\t\\t<div>\\r\\n\\t\\t\\t\\t\\t\\t<h2 class=\\\"text-xl font-bold text-gray-900 m-0\\\">Podcast do Projeto</h2>\\r\\n\\t\\t\\t\\t\\t\\t<p class=\\\"text-sm text-gray-600 m-0 mt-1\\\">{{ data.title }}</p>\\r\\n\\t\\t\\t\\t\\t</div>\\r\\n\\t\\t\\t\\t</div>\\r\\n\\t\\t\\t</ng-template>\\r\\n\\r\\n\\t\\t\\t<ng-template pTemplate=\\\"content\\\">\\r\\n\\t\\t\\t\\t<div class=\\\"podcast-player-container\\\">\\r\\n\\t\\t\\t\\t\\t<!-- Player Container -->\\r\\n\\t\\t\\t\\t\\t<div\\r\\n\\t\\t\\t\\t\\t\\tclass=\\\"bg-gradient-to-br from-purple-50 to-indigo-50 rounded-2xl p-8 text-center border border-purple-200\\\"\\r\\n\\t\\t\\t\\t\\t>\\r\\n\\t\\t\\t\\t\\t\\t<!-- Podcast Icon -->\\r\\n\\t\\t\\t\\t\\t\\t<div class=\\\"mb-6\\\">\\r\\n\\t\\t\\t\\t\\t\\t\\t<div\\r\\n\\t\\t\\t\\t\\t\\t\\t\\tclass=\\\"w-20 h-20 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto shadow-lg\\\"\\r\\n\\t\\t\\t\\t\\t\\t\\t>\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t<i class=\\\"pi pi-microphone text-white text-3xl\\\"></i>\\r\\n\\t\\t\\t\\t\\t\\t\\t</div>\\r\\n\\t\\t\\t\\t\\t\\t</div>\\r\\n\\r\\n\\t\\t\\t\\t\\t\\t<!-- Project Info -->\\r\\n\\t\\t\\t\\t\\t\\t<div class=\\\"mb-6\\\">\\r\\n\\t\\t\\t\\t\\t\\t\\t<h3 class=\\\"text-lg font-semibold text-gray-900 mb-2\\\">{{ data.title }}</h3>\\r\\n\\t\\t\\t\\t\\t\\t\\t<p class=\\\"text-sm text-gray-600\\\">An\\xE1lise detalhada do projeto em formato de podcast</p>\\r\\n\\t\\t\\t\\t\\t\\t</div>\\r\\n\\r\\n\\t\\t\\t\\t\\t\\t<!-- Loading State -->\\r\\n\\t\\t\\t\\t\\t\\t@if (data.generating) {\\r\\n\\t\\t\\t\\t\\t\\t\\t<div class=\\\"bg-white rounded-xl p-6 shadow-sm border border-gray-200\\\">\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t<div class=\\\"flex flex-col items-center gap-4\\\">\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t<i class=\\\"pi pi-spin pi-spinner text-3xl text-purple-600\\\"></i>\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t<div class=\\\"text-center\\\">\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t<p class=\\\"font-semibold text-gray-900\\\">Gerando Podcast...</p>\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t<p class=\\\"text-sm text-gray-600\\\">\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\tAguarde 1-2 minutos enquanto criamos o \\xE1udio\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t</p>\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t</div>\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t</div>\\r\\n\\t\\t\\t\\t\\t\\t\\t</div>\\r\\n\\t\\t\\t\\t\\t\\t}\\r\\n\\r\\n\\t\\t\\t\\t\\t\\t<!-- Audio Player -->\\r\\n\\t\\t\\t\\t\\t\\t@if (data.url && !data.generating) {\\r\\n\\t\\t\\t\\t\\t\\t\\t<div class=\\\"bg-white rounded-xl p-4 shadow-sm border border-gray-200\\\">\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t<audio controls autoplay class=\\\"w-full\\\" [src]=\\\"data.url\\\" preload=\\\"auto\\\">\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t<source [src]=\\\"data.url\\\" type=\\\"audio/mpeg\\\" />\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t<p class=\\\"text-red-600 text-sm\\\">Seu navegador n\\xE3o suporta o elemento de \\xE1udio.</p>\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t</audio>\\r\\n\\t\\t\\t\\t\\t\\t\\t</div>\\r\\n\\t\\t\\t\\t\\t\\t}\\r\\n\\r\\n\\t\\t\\t\\t\\t\\t<!-- Empty State -->\\r\\n\\t\\t\\t\\t\\t\\t@if (!data.url && !data.generating) {\\r\\n\\t\\t\\t\\t\\t\\t\\t<div class=\\\"bg-white rounded-xl p-6 shadow-sm border border-gray-200\\\">\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t<div class=\\\"text-center\\\">\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t<i class=\\\"pi pi-microphone text-4xl text-gray-400 mb-4\\\"></i>\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t<p class=\\\"text-gray-600 mb-4\\\">Nenhum podcast dispon\\xEDvel</p>\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t@if (data.projectId) {\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t<button\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\tpButton\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\tlabel=\\\"Gerar Podcast\\\"\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\ticon=\\\"pi pi-play\\\"\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\tclass=\\\"p-button-outlined p-button-primary\\\"\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t(click)=\\\"generatePodcast()\\\"\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t></button>\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t}\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t</div>\\r\\n\\t\\t\\t\\t\\t\\t\\t</div>\\r\\n\\t\\t\\t\\t\\t\\t}\\r\\n\\r\\n\\t\\t\\t\\t\\t\\t<!-- Player Info -->\\r\\n\\t\\t\\t\\t\\t\\t<div class=\\\"mt-4 text-xs text-gray-500\\\">\\r\\n\\t\\t\\t\\t\\t\\t\\t<p>\\uD83D\\uDCA1 Use os controles de \\xE1udio para pausar, ajustar volume ou pular trechos</p>\\r\\n\\t\\t\\t\\t\\t\\t</div>\\r\\n\\t\\t\\t\\t\\t</div>\\r\\n\\r\\n\\t\\t\\t\\t\\t<!-- Additional Actions -->\\r\\n\\t\\t\\t\\t\\t<div class=\\\"flex justify-center gap-3 mt-6\\\">\\r\\n\\t\\t\\t\\t\\t\\t<button\\r\\n\\t\\t\\t\\t\\t\\t\\tpButton\\r\\n\\t\\t\\t\\t\\t\\t\\tlabel=\\\"Fechar\\\"\\r\\n\\t\\t\\t\\t\\t\\t\\ticon=\\\"pi pi-times\\\"\\r\\n\\t\\t\\t\\t\\t\\t\\tclass=\\\"p-button-outlined p-button-secondary\\\"\\r\\n\\t\\t\\t\\t\\t\\t\\t(click)=\\\"onClose()\\\"\\r\\n\\t\\t\\t\\t\\t\\t></button>\\r\\n\\t\\t\\t\\t\\t</div>\\r\\n\\t\\t\\t\\t</div>\\r\\n\\t\\t\\t</ng-template>\\r\\n\\t\\t</p-dialog>\\r\\n\\t`,\\r\\n\\tstyles: [\\r\\n\\t\\t`\\r\\n\\t\\t\\t:host ::ng-deep .podcast-modal .p-dialog-content {\\r\\n\\t\\t\\t\\tpadding: 0;\\r\\n\\t\\t\\t}\\r\\n\\r\\n\\t\\t\\t:host ::ng-deep .podcast-modal .p-dialog-header {\\r\\n\\t\\t\\t\\tpadding: 1.5rem 1.5rem 1rem 1.5rem;\\r\\n\\t\\t\\t\\tborder-bottom: 1px solid #e5e7eb;\\r\\n\\t\\t\\t}\\r\\n\\r\\n\\t\\t\\t.podcast-player-container {\\r\\n\\t\\t\\t\\tpadding: 1.5rem;\\r\\n\\t\\t\\t}\\r\\n\\r\\n\\t\\t\\taudio {\\r\\n\\t\\t\\t\\theight: 54px;\\r\\n\\t\\t\\t}\\r\\n\\r\\n\\t\\t\\taudio::-webkit-media-controls-panel {\\r\\n\\t\\t\\t\\tbackground-color: #f9fafb;\\r\\n\\t\\t\\t\\tborder-radius: 8px;\\r\\n\\t\\t\\t}\\r\\n\\r\\n\\t\\t\\t.loading-dots {\\r\\n\\t\\t\\t\\tdisplay: inline-block;\\r\\n\\t\\t\\t\\tposition: relative;\\r\\n\\t\\t\\t\\twidth: 40px;\\r\\n\\t\\t\\t\\theight: 10px;\\r\\n\\t\\t\\t}\\r\\n\\r\\n\\t\\t\\t.loading-dots div {\\r\\n\\t\\t\\t\\tposition: absolute;\\r\\n\\t\\t\\t\\ttop: 0;\\r\\n\\t\\t\\t\\twidth: 8px;\\r\\n\\t\\t\\t\\theight: 8px;\\r\\n\\t\\t\\t\\tborder-radius: 50%;\\r\\n\\t\\t\\t\\tbackground: #8b5cf6;\\r\\n\\t\\t\\t\\tanimation: loading-dots 1.2s infinite ease-in-out;\\r\\n\\t\\t\\t}\\r\\n\\r\\n\\t\\t\\t.loading-dots div:nth-child(1) {\\r\\n\\t\\t\\t\\tleft: 0;\\r\\n\\t\\t\\t\\tanimation-delay: -0.24s;\\r\\n\\t\\t\\t}\\r\\n\\t\\t\\t.loading-dots div:nth-child(2) {\\r\\n\\t\\t\\t\\tleft: 16px;\\r\\n\\t\\t\\t\\tanimation-delay: -0.12s;\\r\\n\\t\\t\\t}\\r\\n\\t\\t\\t.loading-dots div:nth-child(3) {\\r\\n\\t\\t\\t\\tleft: 32px;\\r\\n\\t\\t\\t\\tanimation-delay: 0;\\r\\n\\t\\t\\t}\\r\\n\\r\\n\\t\\t\\t@keyframes loading-dots {\\r\\n\\t\\t\\t\\t0%,\\r\\n\\t\\t\\t\\t80%,\\r\\n\\t\\t\\t\\t100% {\\r\\n\\t\\t\\t\\t\\ttransform: scale(0);\\r\\n\\t\\t\\t\\t}\\r\\n\\t\\t\\t\\t40% {\\r\\n\\t\\t\\t\\t\\ttransform: scale(1);\\r\\n\\t\\t\\t\\t}\\r\\n\\t\\t\\t}\\r\\n\\r\\n\\t\\t\\t/* Responsive adjustments */\\r\\n\\t\\t\\t@media (max-width: 640px) {\\r\\n\\t\\t\\t\\t.podcast-player-container {\\r\\n\\t\\t\\t\\t\\tpadding: 1rem;\\r\\n\\t\\t\\t\\t}\\r\\n\\r\\n\\t\\t\\t\\t:host ::ng-deep .podcast-modal .p-dialog-header {\\r\\n\\t\\t\\t\\t\\tpadding: 1rem;\\r\\n\\t\\t\\t\\t}\\r\\n\\r\\n\\t\\t\\t\\t.bg-gradient-to-br {\\r\\n\\t\\t\\t\\t\\tpadding: 1.5rem;\\r\\n\\t\\t\\t\\t}\\r\\n\\t\\t\\t}\\r\\n\\t\\t`,\\r\\n\\t],\\r\\n})\\r\\nexport class PodcastModalComponent implements OnDestroy {\\r\\n\\t@Input() data: PodcastModalData = {\\r\\n\\t\\tvisible: false,\\r\\n\\t\\tgenerating: false,\\r\\n\\t\\turl: '',\\r\\n\\t\\ttitle: '',\\r\\n\\t};\\r\\n\\r\\n\\t@Output() close = new EventEmitter<void>();\\r\\n\\t@Output() generate = new EventEmitter<string>();\\r\\n\\r\\n\\t// Internal state\\r\\n\\tprivate generating = signal<boolean>(false);\\r\\n\\r\\n\\tconstructor(\\r\\n\\t\\tprivate http: HttpClient,\\r\\n\\t\\tprivate messageService: MessageService,\\r\\n\\t) {}\\r\\n\\r\\n\\tngOnDestroy(): void {\\r\\n\\t\\t// Cleanup any ongoing requests or timers\\r\\n\\t}\\r\\n\\r\\n\\tpublic onClose(): void {\\r\\n\\t\\tthis.close.emit();\\r\\n\\t}\\r\\n\\r\\n\\tpublic generatePodcast(): void {\\r\\n\\t\\tif (this.data.projectId) {\\r\\n\\t\\t\\tthis.generate.emit(this.data.projectId);\\r\\n\\t\\t}\\r\\n\\t}\\r\\n\\r\\n\\t/**\\r\\n\\t * Reproduz podcast existente ou gera novo\\r\\n\\t */\\r\\n\\tpublic async playProjectPodcast(projectId: string): Promise<void> {\\r\\n\\t\\tif (!projectId) return;\\r\\n\\r\\n\\t\\ttry {\\r\\n\\t\\t\\t// Verificar se j\\xE1 existe podcast gerado\\r\\n\\t\\t\\tconst response = await this.http\\r\\n\\t\\t\\t\\t.get<any>(`http://localhost:8040/podcasts/projects/${projectId}/podcast`)\\r\\n\\t\\t\\t\\t.toPromise();\\r\\n\\r\\n\\t\\t\\tif (response?.data?.has_podcast && response?.data?.podcast_id) {\\r\\n\\t\\t\\t\\t// Podcast j\\xE1 existe, reproduzir\\r\\n\\t\\t\\t\\tthis.openPodcastPlayer(response.data.podcast_id, this.data.title);\\r\\n\\t\\t\\t} else {\\r\\n\\t\\t\\t\\t// Gerar novo podcast\\r\\n\\t\\t\\t\\tawait this.generateProjectPodcast(projectId);\\r\\n\\t\\t\\t}\\r\\n\\t\\t} catch (error) {\\r\\n\\t\\t\\tconsole.error('Erro ao verificar podcast:', error);\\r\\n\\t\\t\\t// Se erro, tentar gerar novo\\r\\n\\t\\t\\tawait this.generateProjectPodcast(projectId);\\r\\n\\t\\t}\\r\\n\\t}\\r\\n\\r\\n\\t/**\\r\\n\\t * Gera podcast para o projeto\\r\\n\\t */\\r\\n\\tprivate async generateProjectPodcast(projectId: string): Promise<void> {\\r\\n\\t\\tif (!projectId) return;\\r\\n\\r\\n\\t\\ttry {\\r\\n\\t\\t\\tthis.generating.set(true);\\r\\n\\r\\n\\t\\t\\tthis.messageService.add({\\r\\n\\t\\t\\t\\tseverity: 'info',\\r\\n\\t\\t\\t\\tsummary: '\\uD83C\\uDF99\\uFE0F Gerando Podcast',\\r\\n\\t\\t\\t\\tdetail: 'Criando podcast persuasivo sobre o projeto. Aguarde 1-2 minutos...',\\r\\n\\t\\t\\t\\tlife: 8000,\\r\\n\\t\\t\\t});\\r\\n\\r\\n\\t\\t\\t// Solicitar gera\\xE7\\xE3o do podcast\\r\\n\\t\\t\\tconst response = await this.http\\r\\n\\t\\t\\t\\t.post<any>(`http://localhost:8040/podcasts/projects/${projectId}/podcast/generate`, {})\\r\\n\\t\\t\\t\\t.toPromise();\\r\\n\\r\\n\\t\\t\\tif (response?.data?.podcast_id) {\\r\\n\\t\\t\\t\\t// Monitorar status da gera\\xE7\\xE3o\\r\\n\\t\\t\\t\\tawait this.monitorPodcastGeneration(response.data.podcast_id);\\r\\n\\t\\t\\t}\\r\\n\\t\\t} catch (error) {\\r\\n\\t\\t\\tconsole.error('Erro ao gerar podcast:', error);\\r\\n\\t\\t\\tthis.generating.set(false);\\r\\n\\r\\n\\t\\t\\tthis.messageService.add({\\r\\n\\t\\t\\t\\tseverity: 'error',\\r\\n\\t\\t\\t\\tsummary: 'Erro ao Gerar Podcast',\\r\\n\\t\\t\\t\\tdetail: 'N\\xE3o foi poss\\xEDvel gerar o podcast. Tente novamente.',\\r\\n\\t\\t\\t\\tlife: 5000,\\r\\n\\t\\t\\t});\\r\\n\\t\\t}\\r\\n\\t}\\r\\n\\r\\n\\t/**\\r\\n\\t * Monitora status da gera\\xE7\\xE3o do podcast\\r\\n\\t */\\r\\n\\tprivate async monitorPodcastGeneration(podcastId: string): Promise<void> {\\r\\n\\t\\tconst checkStatus = async (): Promise<void> => {\\r\\n\\t\\t\\ttry {\\r\\n\\t\\t\\t\\tconst status = await this.http\\r\\n\\t\\t\\t\\t\\t.get<any>(`http://localhost:8040/podcasts/${podcastId}/status`)\\r\\n\\t\\t\\t\\t\\t.toPromise();\\r\\n\\r\\n\\t\\t\\t\\tif (status?.data?.status === 'completed') {\\r\\n\\t\\t\\t\\t\\tthis.generating.set(false);\\r\\n\\r\\n\\t\\t\\t\\t\\tthis.messageService.add({\\r\\n\\t\\t\\t\\t\\t\\tseverity: 'success',\\r\\n\\t\\t\\t\\t\\t\\tsummary: '\\u2705 Podcast Pronto!',\\r\\n\\t\\t\\t\\t\\t\\tdetail: 'O podcast foi gerado com sucesso.',\\r\\n\\t\\t\\t\\t\\t\\tlife: 3000,\\r\\n\\t\\t\\t\\t\\t});\\r\\n\\r\\n\\t\\t\\t\\t\\t// Abrir player\\r\\n\\t\\t\\t\\t\\tthis.openPodcastPlayer(podcastId, this.data.title);\\r\\n\\t\\t\\t\\t} else if (status?.data?.status === 'error') {\\r\\n\\t\\t\\t\\t\\tthis.generating.set(false);\\r\\n\\r\\n\\t\\t\\t\\t\\tthis.messageService.add({\\r\\n\\t\\t\\t\\t\\t\\tseverity: 'error',\\r\\n\\t\\t\\t\\t\\t\\tsummary: 'Erro na Gera\\xE7\\xE3o',\\r\\n\\t\\t\\t\\t\\t\\tdetail: 'Ocorreu um erro durante a gera\\xE7\\xE3o do podcast.',\\r\\n\\t\\t\\t\\t\\t\\tlife: 5000,\\r\\n\\t\\t\\t\\t\\t});\\r\\n\\t\\t\\t\\t} else {\\r\\n\\t\\t\\t\\t\\t// Ainda processando, verificar novamente\\r\\n\\t\\t\\t\\t\\tsetTimeout(() => checkStatus(), 5000);\\r\\n\\t\\t\\t\\t}\\r\\n\\t\\t\\t} catch (error) {\\r\\n\\t\\t\\t\\tconsole.error('Erro ao verificar status:', error);\\r\\n\\t\\t\\t\\tthis.generating.set(false);\\r\\n\\t\\t\\t}\\r\\n\\t\\t};\\r\\n\\r\\n\\t\\t// Iniciar verifica\\xE7\\xE3o\\r\\n\\t\\tsetTimeout(() => checkStatus(), 3000);\\r\\n\\t}\\r\\n\\r\\n\\t/**\\r\\n\\t * Abre player de \\xE1udio para o podcast\\r\\n\\t */\\r\\n\\tprivate openPodcastPlayer(podcastId: string, projectTitle: string): void {\\r\\n\\t\\t// Configurar dados do podcast\\r\\n\\t\\tconst audioUrl = `http://localhost:8040/podcasts/${podcastId}/audio`;\\r\\n\\r\\n\\t\\t// Sanitizar o t\\xEDtulo para evitar caracteres especiais\\r\\n\\t\\tconst sanitizedTitle = projectTitle.replace(/[<>&\\\"']/g, '');\\r\\n\\r\\n\\t\\t// Emitir dados atualizados para o parent component\\r\\n\\t\\tthis.data = {\\r\\n\\t\\t\\t...this.data,\\r\\n\\t\\t\\turl: audioUrl,\\r\\n\\t\\t\\ttitle: sanitizedTitle,\\r\\n\\t\\t\\tgenerating: false,\\r\\n\\t\\t};\\r\\n\\t}\\r\\n}\\r\\n\"]\n    },\n    _coverageSchema: \"1a1c01bbd47fc00a2c39e90264f33305004495a9\",\n    hash: \"31dbe7625b449d0348c67dbd145a484ae5040d49\"\n  };\n  var coverage = global[gcv] || (global[gcv] = {});\n  if (!coverage[path] || coverage[path].hash !== hash) {\n    coverage[path] = coverageData;\n  }\n  var actualCoverage = coverage[path];\n  {\n    // @ts-ignore\n    cov_r32n2fhxm = function () {\n      return actualCoverage;\n    };\n  }\n  return actualCoverage;\n}\ncov_r32n2fhxm();\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"C:/projetos/scope-ai/frontend/src/app/features/projects/components/podcast-modal/podcast-modal.component.ts.scss?ngResource!=!C:\\\\projetos\\\\scope-ai\\\\frontend\\\\node_modules\\\\@ngtools\\\\webpack\\\\src\\\\loaders\\\\inline-resource.js?data=CgkJCTpob3N0IDo6bmctZGVlcCAucG9kY2FzdC1tb2RhbCAucC1kaWFsb2ctY29udGVudCB7CgkJCQlwYWRkaW5nOiAwOwoJCQl9CgoJCQk6aG9zdCA6Om5nLWRlZXAgLnBvZGNhc3QtbW9kYWwgLnAtZGlhbG9nLWhlYWRlciB7CgkJCQlwYWRkaW5nOiAxLjVyZW0gMS41cmVtIDFyZW0gMS41cmVtOwoJCQkJYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlNWU3ZWI7CgkJCX0KCgkJCS5wb2RjYXN0LXBsYXllci1jb250YWluZXIgewoJCQkJcGFkZGluZzogMS41cmVtOwoJCQl9CgoJCQlhdWRpbyB7CgkJCQloZWlnaHQ6IDU0cHg7CgkJCX0KCgkJCWF1ZGlvOjotd2Via2l0LW1lZGlhLWNvbnRyb2xzLXBhbmVsIHsKCQkJCWJhY2tncm91bmQtY29sb3I6ICNmOWZhZmI7CgkJCQlib3JkZXItcmFkaXVzOiA4cHg7CgkJCX0KCgkJCS5sb2FkaW5nLWRvdHMgewoJCQkJZGlzcGxheTogaW5saW5lLWJsb2NrOwoJCQkJcG9zaXRpb246IHJlbGF0aXZlOwoJCQkJd2lkdGg6IDQwcHg7CgkJCQloZWlnaHQ6IDEwcHg7CgkJCX0KCgkJCS5sb2FkaW5nLWRvdHMgZGl2IHsKCQkJCXBvc2l0aW9uOiBhYnNvbHV0ZTsKCQkJCXRvcDogMDsKCQkJCXdpZHRoOiA4cHg7CgkJCQloZWlnaHQ6IDhweDsKCQkJCWJvcmRlci1yYWRpdXM6IDUwJTsKCQkJCWJhY2tncm91bmQ6ICM4YjVjZjY7CgkJCQlhbmltYXRpb246IGxvYWRpbmctZG90cyAxLjJzIGluZmluaXRlIGVhc2UtaW4tb3V0OwoJCQl9CgoJCQkubG9hZGluZy1kb3RzIGRpdjpudGgtY2hpbGQoMSkgewoJCQkJbGVmdDogMDsKCQkJCWFuaW1hdGlvbi1kZWxheTogLTAuMjRzOwoJCQl9CgkJCS5sb2FkaW5nLWRvdHMgZGl2Om50aC1jaGlsZCgyKSB7CgkJCQlsZWZ0OiAxNnB4OwoJCQkJYW5pbWF0aW9uLWRlbGF5OiAtMC4xMnM7CgkJCX0KCQkJLmxvYWRpbmctZG90cyBkaXY6bnRoLWNoaWxkKDMpIHsKCQkJCWxlZnQ6IDMycHg7CgkJCQlhbmltYXRpb24tZGVsYXk6IDA7CgkJCX0KCgkJCUBrZXlmcmFtZXMgbG9hZGluZy1kb3RzIHsKCQkJCTAlLAoJCQkJODAlLAoJCQkJMTAwJSB7CgkJCQkJdHJhbnNmb3JtOiBzY2FsZSgwKTsKCQkJCX0KCQkJCTQwJSB7CgkJCQkJdHJhbnNmb3JtOiBzY2FsZSgxKTsKCQkJCX0KCQkJfQoKCQkJLyogUmVzcG9uc2l2ZSBhZGp1c3RtZW50cyAqLwoJCQlAbWVkaWEgKG1heC13aWR0aDogNjQwcHgpIHsKCQkJCS5wb2RjYXN0LXBsYXllci1jb250YWluZXIgewoJCQkJCXBhZGRpbmc6IDFyZW07CgkJCQl9CgoJCQkJOmhvc3QgOjpuZy1kZWVwIC5wb2RjYXN0LW1vZGFsIC5wLWRpYWxvZy1oZWFkZXIgewoJCQkJCXBhZGRpbmc6IDFyZW07CgkJCQl9CgoJCQkJLmJnLWdyYWRpZW50LXRvLWJyIHsKCQkJCQlwYWRkaW5nOiAxLjVyZW07CgkJCQl9CgkJCX0KCQk%3D!C:/projetos/scope-ai/frontend/src/app/features/projects/components/podcast-modal/podcast-modal.component.ts\";\nimport { CommonModule } from '@angular/common';\nimport { HttpClient } from '@angular/common/http';\nimport { Component, EventEmitter, Input, Output, signal } from '@angular/core';\nimport { MessageService } from 'primeng/api';\nimport { ButtonModule } from 'primeng/button';\nimport { DialogModule } from 'primeng/dialog';\ncov_r32n2fhxm().s[0]++;\nlet PodcastModalComponent = class PodcastModalComponent {\n  http;\n  messageService;\n  data = (cov_r32n2fhxm().s[1]++, {\n    visible: false,\n    generating: false,\n    url: '',\n    title: ''\n  });\n  close = (cov_r32n2fhxm().s[2]++, new EventEmitter());\n  generate = (cov_r32n2fhxm().s[3]++, new EventEmitter());\n  // Internal state\n  generating = (cov_r32n2fhxm().s[4]++, signal(false));\n  constructor(http, messageService) {\n    cov_r32n2fhxm().f[0]++;\n    cov_r32n2fhxm().s[5]++;\n    this.http = http;\n    cov_r32n2fhxm().s[6]++;\n    this.messageService = messageService;\n  }\n  ngOnDestroy() {\n    cov_r32n2fhxm().f[1]++;\n  } // Cleanup any ongoing requests or timers\n  onClose() {\n    cov_r32n2fhxm().f[2]++;\n    cov_r32n2fhxm().s[7]++;\n    this.close.emit();\n  }\n  generatePodcast() {\n    cov_r32n2fhxm().f[3]++;\n    cov_r32n2fhxm().s[8]++;\n    if (this.data.projectId) {\n      cov_r32n2fhxm().b[0][0]++;\n      cov_r32n2fhxm().s[9]++;\n      this.generate.emit(this.data.projectId);\n    } else {\n      cov_r32n2fhxm().b[0][1]++;\n    }\n  }\n  /**\n   * Reproduz podcast existente ou gera novo\n   */\n  playProjectPodcast(projectId) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      cov_r32n2fhxm().f[4]++;\n      cov_r32n2fhxm().s[10]++;\n      if (!projectId) {\n        cov_r32n2fhxm().b[1][0]++;\n        cov_r32n2fhxm().s[11]++;\n        return;\n      } else {\n        cov_r32n2fhxm().b[1][1]++;\n      }\n      cov_r32n2fhxm().s[12]++;\n      try {\n        // Verificar se já existe podcast gerado\n        const response = (cov_r32n2fhxm().s[13]++, yield _this.http.get(`http://localhost:8040/podcasts/projects/${projectId}/podcast`).toPromise());\n        cov_r32n2fhxm().s[14]++;\n        if ((cov_r32n2fhxm().b[3][0]++, response?.data?.has_podcast) && (cov_r32n2fhxm().b[3][1]++, response?.data?.podcast_id)) {\n          cov_r32n2fhxm().b[2][0]++;\n          cov_r32n2fhxm().s[15]++;\n          // Podcast já existe, reproduzir\n          _this.openPodcastPlayer(response.data.podcast_id, _this.data.title);\n        } else {\n          cov_r32n2fhxm().b[2][1]++;\n          cov_r32n2fhxm().s[16]++;\n          // Gerar novo podcast\n          yield _this.generateProjectPodcast(projectId);\n        }\n      } catch (error) {\n        cov_r32n2fhxm().s[17]++;\n        console.error('Erro ao verificar podcast:', error);\n        // Se erro, tentar gerar novo\n        cov_r32n2fhxm().s[18]++;\n        yield _this.generateProjectPodcast(projectId);\n      }\n    })();\n  }\n  /**\n   * Gera podcast para o projeto\n   */\n  generateProjectPodcast(projectId) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      cov_r32n2fhxm().f[5]++;\n      cov_r32n2fhxm().s[19]++;\n      if (!projectId) {\n        cov_r32n2fhxm().b[4][0]++;\n        cov_r32n2fhxm().s[20]++;\n        return;\n      } else {\n        cov_r32n2fhxm().b[4][1]++;\n      }\n      cov_r32n2fhxm().s[21]++;\n      try {\n        cov_r32n2fhxm().s[22]++;\n        _this2.generating.set(true);\n        cov_r32n2fhxm().s[23]++;\n        _this2.messageService.add({\n          severity: 'info',\n          summary: '🎙️ Gerando Podcast',\n          detail: 'Criando podcast persuasivo sobre o projeto. Aguarde 1-2 minutos...',\n          life: 8000\n        });\n        // Solicitar geração do podcast\n        const response = (cov_r32n2fhxm().s[24]++, yield _this2.http.post(`http://localhost:8040/podcasts/projects/${projectId}/podcast/generate`, {}).toPromise());\n        cov_r32n2fhxm().s[25]++;\n        if (response?.data?.podcast_id) {\n          cov_r32n2fhxm().b[5][0]++;\n          cov_r32n2fhxm().s[26]++;\n          // Monitorar status da geração\n          yield _this2.monitorPodcastGeneration(response.data.podcast_id);\n        } else {\n          cov_r32n2fhxm().b[5][1]++;\n        }\n      } catch (error) {\n        cov_r32n2fhxm().s[27]++;\n        console.error('Erro ao gerar podcast:', error);\n        cov_r32n2fhxm().s[28]++;\n        _this2.generating.set(false);\n        cov_r32n2fhxm().s[29]++;\n        _this2.messageService.add({\n          severity: 'error',\n          summary: 'Erro ao Gerar Podcast',\n          detail: 'Não foi possível gerar o podcast. Tente novamente.',\n          life: 5000\n        });\n      }\n    })();\n  }\n  /**\n   * Monitora status da geração do podcast\n   */\n  monitorPodcastGeneration(podcastId) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      cov_r32n2fhxm().f[6]++;\n      cov_r32n2fhxm().s[30]++;\n      const _checkStatus = /*#__PURE__*/function () {\n        var _ref = _asyncToGenerator(function* () {\n          cov_r32n2fhxm().f[7]++;\n          cov_r32n2fhxm().s[31]++;\n          try {\n            const status = (cov_r32n2fhxm().s[32]++, yield _this3.http.get(`http://localhost:8040/podcasts/${podcastId}/status`).toPromise());\n            cov_r32n2fhxm().s[33]++;\n            if (status?.data?.status === 'completed') {\n              cov_r32n2fhxm().b[6][0]++;\n              cov_r32n2fhxm().s[34]++;\n              _this3.generating.set(false);\n              cov_r32n2fhxm().s[35]++;\n              _this3.messageService.add({\n                severity: 'success',\n                summary: '✅ Podcast Pronto!',\n                detail: 'O podcast foi gerado com sucesso.',\n                life: 3000\n              });\n              // Abrir player\n              cov_r32n2fhxm().s[36]++;\n              _this3.openPodcastPlayer(podcastId, _this3.data.title);\n            } else {\n              cov_r32n2fhxm().b[6][1]++;\n              cov_r32n2fhxm().s[37]++;\n              if (status?.data?.status === 'error') {\n                cov_r32n2fhxm().b[7][0]++;\n                cov_r32n2fhxm().s[38]++;\n                _this3.generating.set(false);\n                cov_r32n2fhxm().s[39]++;\n                _this3.messageService.add({\n                  severity: 'error',\n                  summary: 'Erro na Geração',\n                  detail: 'Ocorreu um erro durante a geração do podcast.',\n                  life: 5000\n                });\n              } else {\n                cov_r32n2fhxm().b[7][1]++;\n                cov_r32n2fhxm().s[40]++;\n                // Ainda processando, verificar novamente\n                setTimeout(() => {\n                  cov_r32n2fhxm().f[8]++;\n                  cov_r32n2fhxm().s[41]++;\n                  return _checkStatus();\n                }, 5000);\n              }\n            }\n          } catch (error) {\n            cov_r32n2fhxm().s[42]++;\n            console.error('Erro ao verificar status:', error);\n            cov_r32n2fhxm().s[43]++;\n            _this3.generating.set(false);\n          }\n        });\n        return function checkStatus() {\n          return _ref.apply(this, arguments);\n        };\n      }();\n      // Iniciar verificação\n      cov_r32n2fhxm().s[44]++;\n      setTimeout(() => {\n        cov_r32n2fhxm().f[9]++;\n        cov_r32n2fhxm().s[45]++;\n        return _checkStatus();\n      }, 3000);\n    })();\n  }\n  /**\n   * Abre player de áudio para o podcast\n   */\n  openPodcastPlayer(podcastId, projectTitle) {\n    cov_r32n2fhxm().f[10]++;\n    // Configurar dados do podcast\n    const audioUrl = (cov_r32n2fhxm().s[46]++, `http://localhost:8040/podcasts/${podcastId}/audio`);\n    // Sanitizar o título para evitar caracteres especiais\n    const sanitizedTitle = (cov_r32n2fhxm().s[47]++, projectTitle.replace(/[<>&\"']/g, ''));\n    // Emitir dados atualizados para o parent component\n    cov_r32n2fhxm().s[48]++;\n    this.data = {\n      ...this.data,\n      url: audioUrl,\n      title: sanitizedTitle,\n      generating: false\n    };\n  }\n  static ctorParameters = (cov_r32n2fhxm().s[49]++, () => {\n    cov_r32n2fhxm().f[11]++;\n    cov_r32n2fhxm().s[50]++;\n    return [{\n      type: HttpClient\n    }, {\n      type: MessageService\n    }];\n  });\n  static propDecorators = (cov_r32n2fhxm().s[51]++, {\n    data: [{\n      type: Input\n    }],\n    close: [{\n      type: Output\n    }],\n    generate: [{\n      type: Output\n    }]\n  });\n};\ncov_r32n2fhxm().s[52]++;\nPodcastModalComponent = __decorate([Component({\n  selector: 'app-podcast-modal',\n  standalone: true,\n  imports: [CommonModule, DialogModule, ButtonModule],\n  template: `\n\t\t<p-dialog\n\t\t\t[visible]=\"data.visible\"\n\t\t\t[modal]=\"true\"\n\t\t\t[closable]=\"true\"\n\t\t\t[draggable]=\"false\"\n\t\t\t[resizable]=\"false\"\n\t\t\tstyleClass=\"podcast-modal\"\n\t\t\t[style]=\"{ width: '90vw', maxWidth: '600px' }\"\n\t\t\t(onHide)=\"onClose()\"\n\t\t>\n\t\t\t<ng-template pTemplate=\"header\">\n\t\t\t\t<div class=\"flex items-center gap-3\">\n\t\t\t\t\t<i class=\"pi pi-microphone text-purple-600 text-2xl\"></i>\n\t\t\t\t\t<div>\n\t\t\t\t\t\t<h2 class=\"text-xl font-bold text-gray-900 m-0\">Podcast do Projeto</h2>\n\t\t\t\t\t\t<p class=\"text-sm text-gray-600 m-0 mt-1\">{{ data.title }}</p>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</ng-template>\n\n\t\t\t<ng-template pTemplate=\"content\">\n\t\t\t\t<div class=\"podcast-player-container\">\n\t\t\t\t\t<!-- Player Container -->\n\t\t\t\t\t<div\n\t\t\t\t\t\tclass=\"bg-gradient-to-br from-purple-50 to-indigo-50 rounded-2xl p-8 text-center border border-purple-200\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<!-- Podcast Icon -->\n\t\t\t\t\t\t<div class=\"mb-6\">\n\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\tclass=\"w-20 h-20 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto shadow-lg\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<i class=\"pi pi-microphone text-white text-3xl\"></i>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t<!-- Project Info -->\n\t\t\t\t\t\t<div class=\"mb-6\">\n\t\t\t\t\t\t\t<h3 class=\"text-lg font-semibold text-gray-900 mb-2\">{{ data.title }}</h3>\n\t\t\t\t\t\t\t<p class=\"text-sm text-gray-600\">Análise detalhada do projeto em formato de podcast</p>\n\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t<!-- Loading State -->\n\t\t\t\t\t\t@if (data.generating) {\n\t\t\t\t\t\t\t<div class=\"bg-white rounded-xl p-6 shadow-sm border border-gray-200\">\n\t\t\t\t\t\t\t\t<div class=\"flex flex-col items-center gap-4\">\n\t\t\t\t\t\t\t\t\t<i class=\"pi pi-spin pi-spinner text-3xl text-purple-600\"></i>\n\t\t\t\t\t\t\t\t\t<div class=\"text-center\">\n\t\t\t\t\t\t\t\t\t\t<p class=\"font-semibold text-gray-900\">Gerando Podcast...</p>\n\t\t\t\t\t\t\t\t\t\t<p class=\"text-sm text-gray-600\">\n\t\t\t\t\t\t\t\t\t\t\tAguarde 1-2 minutos enquanto criamos o áudio\n\t\t\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t<!-- Audio Player -->\n\t\t\t\t\t\t@if (data.url && !data.generating) {\n\t\t\t\t\t\t\t<div class=\"bg-white rounded-xl p-4 shadow-sm border border-gray-200\">\n\t\t\t\t\t\t\t\t<audio controls autoplay class=\"w-full\" [src]=\"data.url\" preload=\"auto\">\n\t\t\t\t\t\t\t\t\t<source [src]=\"data.url\" type=\"audio/mpeg\" />\n\t\t\t\t\t\t\t\t\t<p class=\"text-red-600 text-sm\">Seu navegador não suporta o elemento de áudio.</p>\n\t\t\t\t\t\t\t\t</audio>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t<!-- Empty State -->\n\t\t\t\t\t\t@if (!data.url && !data.generating) {\n\t\t\t\t\t\t\t<div class=\"bg-white rounded-xl p-6 shadow-sm border border-gray-200\">\n\t\t\t\t\t\t\t\t<div class=\"text-center\">\n\t\t\t\t\t\t\t\t\t<i class=\"pi pi-microphone text-4xl text-gray-400 mb-4\"></i>\n\t\t\t\t\t\t\t\t\t<p class=\"text-gray-600 mb-4\">Nenhum podcast disponível</p>\n\t\t\t\t\t\t\t\t\t@if (data.projectId) {\n\t\t\t\t\t\t\t\t\t\t<button\n\t\t\t\t\t\t\t\t\t\t\tpButton\n\t\t\t\t\t\t\t\t\t\t\tlabel=\"Gerar Podcast\"\n\t\t\t\t\t\t\t\t\t\t\ticon=\"pi pi-play\"\n\t\t\t\t\t\t\t\t\t\t\tclass=\"p-button-outlined p-button-primary\"\n\t\t\t\t\t\t\t\t\t\t\t(click)=\"generatePodcast()\"\n\t\t\t\t\t\t\t\t\t\t></button>\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t<!-- Player Info -->\n\t\t\t\t\t\t<div class=\"mt-4 text-xs text-gray-500\">\n\t\t\t\t\t\t\t<p>💡 Use os controles de áudio para pausar, ajustar volume ou pular trechos</p>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\n\t\t\t\t\t<!-- Additional Actions -->\n\t\t\t\t\t<div class=\"flex justify-center gap-3 mt-6\">\n\t\t\t\t\t\t<button\n\t\t\t\t\t\t\tpButton\n\t\t\t\t\t\t\tlabel=\"Fechar\"\n\t\t\t\t\t\t\ticon=\"pi pi-times\"\n\t\t\t\t\t\t\tclass=\"p-button-outlined p-button-secondary\"\n\t\t\t\t\t\t\t(click)=\"onClose()\"\n\t\t\t\t\t\t></button>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</ng-template>\n\t\t</p-dialog>\n\t`,\n  styles: [__NG_CLI_RESOURCE__0]\n})], PodcastModalComponent);\nexport { PodcastModalComponent };", "map": {"version": 3, "names": ["cov_r32n2fhxm", "actualCoverage", "CommonModule", "HttpClient", "Component", "EventEmitter", "Input", "Output", "signal", "MessageService", "ButtonModule", "DialogModule", "s", "PodcastModalComponent", "http", "messageService", "data", "visible", "generating", "url", "title", "close", "generate", "constructor", "f", "ngOnDestroy", "onClose", "emit", "generatePodcast", "projectId", "b", "playProjectPodcast", "_this", "_asyncToGenerator", "response", "get", "to<PERSON>romise", "has_podcast", "podcast_id", "openPodcastPlayer", "generateProjectPodcast", "error", "console", "_this2", "set", "add", "severity", "summary", "detail", "life", "post", "monitorPodcastGeneration", "podcastId", "_this3", "checkStatus", "_ref", "status", "setTimeout", "apply", "arguments", "projectTitle", "audioUrl", "sanitizedTitle", "replace", "__decorate", "selector", "standalone", "imports", "template"], "sources": ["C:\\projetos\\scope-ai\\frontend\\src\\app\\features\\projects\\components\\podcast-modal\\podcast-modal.component.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { Component, EventEmitter, Input, OnDestroy, Output, signal } from '@angular/core';\r\nimport { MessageService } from 'primeng/api';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { DialogModule } from 'primeng/dialog';\r\n\r\nexport interface PodcastModalData {\r\n\tvisible: boolean;\r\n\tgenerating: boolean;\r\n\turl: string;\r\n\ttitle: string;\r\n\tprojectId?: string;\r\n}\r\n\r\nexport interface PodcastModalEvents {\r\n\tonClose: () => void;\r\n\tonGenerate?: (projectId: string) => void;\r\n}\r\n\r\n@Component({\r\n\tselector: 'app-podcast-modal',\r\n\tstandalone: true,\r\n\timports: [CommonModule, DialogModule, ButtonModule],\r\n\ttemplate: `\r\n\t\t<p-dialog\r\n\t\t\t[visible]=\"data.visible\"\r\n\t\t\t[modal]=\"true\"\r\n\t\t\t[closable]=\"true\"\r\n\t\t\t[draggable]=\"false\"\r\n\t\t\t[resizable]=\"false\"\r\n\t\t\tstyleClass=\"podcast-modal\"\r\n\t\t\t[style]=\"{ width: '90vw', maxWidth: '600px' }\"\r\n\t\t\t(onHide)=\"onClose()\"\r\n\t\t>\r\n\t\t\t<ng-template pTemplate=\"header\">\r\n\t\t\t\t<div class=\"flex items-center gap-3\">\r\n\t\t\t\t\t<i class=\"pi pi-microphone text-purple-600 text-2xl\"></i>\r\n\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<h2 class=\"text-xl font-bold text-gray-900 m-0\">Podcast do Projeto</h2>\r\n\t\t\t\t\t\t<p class=\"text-sm text-gray-600 m-0 mt-1\">{{ data.title }}</p>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</ng-template>\r\n\r\n\t\t\t<ng-template pTemplate=\"content\">\r\n\t\t\t\t<div class=\"podcast-player-container\">\r\n\t\t\t\t\t<!-- Player Container -->\r\n\t\t\t\t\t<div\r\n\t\t\t\t\t\tclass=\"bg-gradient-to-br from-purple-50 to-indigo-50 rounded-2xl p-8 text-center border border-purple-200\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<!-- Podcast Icon -->\r\n\t\t\t\t\t\t<div class=\"mb-6\">\r\n\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\tclass=\"w-20 h-20 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto shadow-lg\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<i class=\"pi pi-microphone text-white text-3xl\"></i>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<!-- Project Info -->\r\n\t\t\t\t\t\t<div class=\"mb-6\">\r\n\t\t\t\t\t\t\t<h3 class=\"text-lg font-semibold text-gray-900 mb-2\">{{ data.title }}</h3>\r\n\t\t\t\t\t\t\t<p class=\"text-sm text-gray-600\">Análise detalhada do projeto em formato de podcast</p>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<!-- Loading State -->\r\n\t\t\t\t\t\t@if (data.generating) {\r\n\t\t\t\t\t\t\t<div class=\"bg-white rounded-xl p-6 shadow-sm border border-gray-200\">\r\n\t\t\t\t\t\t\t\t<div class=\"flex flex-col items-center gap-4\">\r\n\t\t\t\t\t\t\t\t\t<i class=\"pi pi-spin pi-spinner text-3xl text-purple-600\"></i>\r\n\t\t\t\t\t\t\t\t\t<div class=\"text-center\">\r\n\t\t\t\t\t\t\t\t\t\t<p class=\"font-semibold text-gray-900\">Gerando Podcast...</p>\r\n\t\t\t\t\t\t\t\t\t\t<p class=\"text-sm text-gray-600\">\r\n\t\t\t\t\t\t\t\t\t\t\tAguarde 1-2 minutos enquanto criamos o áudio\r\n\t\t\t\t\t\t\t\t\t\t</p>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t<!-- Audio Player -->\r\n\t\t\t\t\t\t@if (data.url && !data.generating) {\r\n\t\t\t\t\t\t\t<div class=\"bg-white rounded-xl p-4 shadow-sm border border-gray-200\">\r\n\t\t\t\t\t\t\t\t<audio controls autoplay class=\"w-full\" [src]=\"data.url\" preload=\"auto\">\r\n\t\t\t\t\t\t\t\t\t<source [src]=\"data.url\" type=\"audio/mpeg\" />\r\n\t\t\t\t\t\t\t\t\t<p class=\"text-red-600 text-sm\">Seu navegador não suporta o elemento de áudio.</p>\r\n\t\t\t\t\t\t\t\t</audio>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t<!-- Empty State -->\r\n\t\t\t\t\t\t@if (!data.url && !data.generating) {\r\n\t\t\t\t\t\t\t<div class=\"bg-white rounded-xl p-6 shadow-sm border border-gray-200\">\r\n\t\t\t\t\t\t\t\t<div class=\"text-center\">\r\n\t\t\t\t\t\t\t\t\t<i class=\"pi pi-microphone text-4xl text-gray-400 mb-4\"></i>\r\n\t\t\t\t\t\t\t\t\t<p class=\"text-gray-600 mb-4\">Nenhum podcast disponível</p>\r\n\t\t\t\t\t\t\t\t\t@if (data.projectId) {\r\n\t\t\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\t\t\tpButton\r\n\t\t\t\t\t\t\t\t\t\t\tlabel=\"Gerar Podcast\"\r\n\t\t\t\t\t\t\t\t\t\t\ticon=\"pi pi-play\"\r\n\t\t\t\t\t\t\t\t\t\t\tclass=\"p-button-outlined p-button-primary\"\r\n\t\t\t\t\t\t\t\t\t\t\t(click)=\"generatePodcast()\"\r\n\t\t\t\t\t\t\t\t\t\t></button>\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t<!-- Player Info -->\r\n\t\t\t\t\t\t<div class=\"mt-4 text-xs text-gray-500\">\r\n\t\t\t\t\t\t\t<p>💡 Use os controles de áudio para pausar, ajustar volume ou pular trechos</p>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t<!-- Additional Actions -->\r\n\t\t\t\t\t<div class=\"flex justify-center gap-3 mt-6\">\r\n\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\tpButton\r\n\t\t\t\t\t\t\tlabel=\"Fechar\"\r\n\t\t\t\t\t\t\ticon=\"pi pi-times\"\r\n\t\t\t\t\t\t\tclass=\"p-button-outlined p-button-secondary\"\r\n\t\t\t\t\t\t\t(click)=\"onClose()\"\r\n\t\t\t\t\t\t></button>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</ng-template>\r\n\t\t</p-dialog>\r\n\t`,\r\n\tstyles: [\r\n\t\t`\r\n\t\t\t:host ::ng-deep .podcast-modal .p-dialog-content {\r\n\t\t\t\tpadding: 0;\r\n\t\t\t}\r\n\r\n\t\t\t:host ::ng-deep .podcast-modal .p-dialog-header {\r\n\t\t\t\tpadding: 1.5rem 1.5rem 1rem 1.5rem;\r\n\t\t\t\tborder-bottom: 1px solid #e5e7eb;\r\n\t\t\t}\r\n\r\n\t\t\t.podcast-player-container {\r\n\t\t\t\tpadding: 1.5rem;\r\n\t\t\t}\r\n\r\n\t\t\taudio {\r\n\t\t\t\theight: 54px;\r\n\t\t\t}\r\n\r\n\t\t\taudio::-webkit-media-controls-panel {\r\n\t\t\t\tbackground-color: #f9fafb;\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t}\r\n\r\n\t\t\t.loading-dots {\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\twidth: 40px;\r\n\t\t\t\theight: 10px;\r\n\t\t\t}\r\n\r\n\t\t\t.loading-dots div {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttop: 0;\r\n\t\t\t\twidth: 8px;\r\n\t\t\t\theight: 8px;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t\tbackground: #8b5cf6;\r\n\t\t\t\tanimation: loading-dots 1.2s infinite ease-in-out;\r\n\t\t\t}\r\n\r\n\t\t\t.loading-dots div:nth-child(1) {\r\n\t\t\t\tleft: 0;\r\n\t\t\t\tanimation-delay: -0.24s;\r\n\t\t\t}\r\n\t\t\t.loading-dots div:nth-child(2) {\r\n\t\t\t\tleft: 16px;\r\n\t\t\t\tanimation-delay: -0.12s;\r\n\t\t\t}\r\n\t\t\t.loading-dots div:nth-child(3) {\r\n\t\t\t\tleft: 32px;\r\n\t\t\t\tanimation-delay: 0;\r\n\t\t\t}\r\n\r\n\t\t\t@keyframes loading-dots {\r\n\t\t\t\t0%,\r\n\t\t\t\t80%,\r\n\t\t\t\t100% {\r\n\t\t\t\t\ttransform: scale(0);\r\n\t\t\t\t}\r\n\t\t\t\t40% {\r\n\t\t\t\t\ttransform: scale(1);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t/* Responsive adjustments */\r\n\t\t\t@media (max-width: 640px) {\r\n\t\t\t\t.podcast-player-container {\r\n\t\t\t\t\tpadding: 1rem;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t:host ::ng-deep .podcast-modal .p-dialog-header {\r\n\t\t\t\t\tpadding: 1rem;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.bg-gradient-to-br {\r\n\t\t\t\t\tpadding: 1.5rem;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t`,\r\n\t],\r\n})\r\nexport class PodcastModalComponent implements OnDestroy {\r\n\t@Input() data: PodcastModalData = {\r\n\t\tvisible: false,\r\n\t\tgenerating: false,\r\n\t\turl: '',\r\n\t\ttitle: '',\r\n\t};\r\n\r\n\t@Output() close = new EventEmitter<void>();\r\n\t@Output() generate = new EventEmitter<string>();\r\n\r\n\t// Internal state\r\n\tprivate generating = signal<boolean>(false);\r\n\r\n\tconstructor(\r\n\t\tprivate http: HttpClient,\r\n\t\tprivate messageService: MessageService,\r\n\t) {}\r\n\r\n\tngOnDestroy(): void {\r\n\t\t// Cleanup any ongoing requests or timers\r\n\t}\r\n\r\n\tpublic onClose(): void {\r\n\t\tthis.close.emit();\r\n\t}\r\n\r\n\tpublic generatePodcast(): void {\r\n\t\tif (this.data.projectId) {\r\n\t\t\tthis.generate.emit(this.data.projectId);\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Reproduz podcast existente ou gera novo\r\n\t */\r\n\tpublic async playProjectPodcast(projectId: string): Promise<void> {\r\n\t\tif (!projectId) return;\r\n\r\n\t\ttry {\r\n\t\t\t// Verificar se já existe podcast gerado\r\n\t\t\tconst response = await this.http\r\n\t\t\t\t.get<any>(`http://localhost:8040/podcasts/projects/${projectId}/podcast`)\r\n\t\t\t\t.toPromise();\r\n\r\n\t\t\tif (response?.data?.has_podcast && response?.data?.podcast_id) {\r\n\t\t\t\t// Podcast já existe, reproduzir\r\n\t\t\t\tthis.openPodcastPlayer(response.data.podcast_id, this.data.title);\r\n\t\t\t} else {\r\n\t\t\t\t// Gerar novo podcast\r\n\t\t\t\tawait this.generateProjectPodcast(projectId);\r\n\t\t\t}\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('Erro ao verificar podcast:', error);\r\n\t\t\t// Se erro, tentar gerar novo\r\n\t\t\tawait this.generateProjectPodcast(projectId);\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Gera podcast para o projeto\r\n\t */\r\n\tprivate async generateProjectPodcast(projectId: string): Promise<void> {\r\n\t\tif (!projectId) return;\r\n\r\n\t\ttry {\r\n\t\t\tthis.generating.set(true);\r\n\r\n\t\t\tthis.messageService.add({\r\n\t\t\t\tseverity: 'info',\r\n\t\t\t\tsummary: '🎙️ Gerando Podcast',\r\n\t\t\t\tdetail: 'Criando podcast persuasivo sobre o projeto. Aguarde 1-2 minutos...',\r\n\t\t\t\tlife: 8000,\r\n\t\t\t});\r\n\r\n\t\t\t// Solicitar geração do podcast\r\n\t\t\tconst response = await this.http\r\n\t\t\t\t.post<any>(`http://localhost:8040/podcasts/projects/${projectId}/podcast/generate`, {})\r\n\t\t\t\t.toPromise();\r\n\r\n\t\t\tif (response?.data?.podcast_id) {\r\n\t\t\t\t// Monitorar status da geração\r\n\t\t\t\tawait this.monitorPodcastGeneration(response.data.podcast_id);\r\n\t\t\t}\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('Erro ao gerar podcast:', error);\r\n\t\t\tthis.generating.set(false);\r\n\r\n\t\t\tthis.messageService.add({\r\n\t\t\t\tseverity: 'error',\r\n\t\t\t\tsummary: 'Erro ao Gerar Podcast',\r\n\t\t\t\tdetail: 'Não foi possível gerar o podcast. Tente novamente.',\r\n\t\t\t\tlife: 5000,\r\n\t\t\t});\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Monitora status da geração do podcast\r\n\t */\r\n\tprivate async monitorPodcastGeneration(podcastId: string): Promise<void> {\r\n\t\tconst checkStatus = async (): Promise<void> => {\r\n\t\t\ttry {\r\n\t\t\t\tconst status = await this.http\r\n\t\t\t\t\t.get<any>(`http://localhost:8040/podcasts/${podcastId}/status`)\r\n\t\t\t\t\t.toPromise();\r\n\r\n\t\t\t\tif (status?.data?.status === 'completed') {\r\n\t\t\t\t\tthis.generating.set(false);\r\n\r\n\t\t\t\t\tthis.messageService.add({\r\n\t\t\t\t\t\tseverity: 'success',\r\n\t\t\t\t\t\tsummary: '✅ Podcast Pronto!',\r\n\t\t\t\t\t\tdetail: 'O podcast foi gerado com sucesso.',\r\n\t\t\t\t\t\tlife: 3000,\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\t// Abrir player\r\n\t\t\t\t\tthis.openPodcastPlayer(podcastId, this.data.title);\r\n\t\t\t\t} else if (status?.data?.status === 'error') {\r\n\t\t\t\t\tthis.generating.set(false);\r\n\r\n\t\t\t\t\tthis.messageService.add({\r\n\t\t\t\t\t\tseverity: 'error',\r\n\t\t\t\t\t\tsummary: 'Erro na Geração',\r\n\t\t\t\t\t\tdetail: 'Ocorreu um erro durante a geração do podcast.',\r\n\t\t\t\t\t\tlife: 5000,\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// Ainda processando, verificar novamente\r\n\t\t\t\t\tsetTimeout(() => checkStatus(), 5000);\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('Erro ao verificar status:', error);\r\n\t\t\t\tthis.generating.set(false);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t// Iniciar verificação\r\n\t\tsetTimeout(() => checkStatus(), 3000);\r\n\t}\r\n\r\n\t/**\r\n\t * Abre player de áudio para o podcast\r\n\t */\r\n\tprivate openPodcastPlayer(podcastId: string, projectTitle: string): void {\r\n\t\t// Configurar dados do podcast\r\n\t\tconst audioUrl = `http://localhost:8040/podcasts/${podcastId}/audio`;\r\n\r\n\t\t// Sanitizar o título para evitar caracteres especiais\r\n\t\tconst sanitizedTitle = projectTitle.replace(/[<>&\"']/g, '');\r\n\r\n\t\t// Emitir dados atualizados para o parent component\r\n\t\tthis.data = {\r\n\t\t\t...this.data,\r\n\t\t\turl: audioUrl,\r\n\t\t\ttitle: sanitizedTitle,\r\n\t\t\tgenerating: false,\r\n\t\t};\r\n\t}\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAyNE;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;;;AAzNF,SAASE,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAaC,MAAM,EAAEC,MAAM,QAAQ,eAAe;AACzF,SAASC,cAAc,QAAQ,aAAa;AAC5C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,YAAY,QAAQ,gBAAgB;AAACX,aAAA,GAAAY,CAAA;AA+MvC,IAAMC,qBAAqB,GAA3B,MAAMA,qBAAqB;EAexBC,IAAA;EACAC,cAAA;EAfAC,IAAI,IAAAhB,aAAA,GAAAY,CAAA,OAAqB;IACjCK,OAAO,EAAE,KAAK;IACdC,UAAU,EAAE,KAAK;IACjBC,GAAG,EAAE,EAAE;IACPC,KAAK,EAAE;GACP;EAESC,KAAK,IAAArB,aAAA,GAAAY,CAAA,OAAG,IAAIP,YAAY,EAAQ;EAChCiB,QAAQ,IAAAtB,aAAA,GAAAY,CAAA,OAAG,IAAIP,YAAY,EAAU;EAE/C;EACQa,UAAU,IAAAlB,aAAA,GAAAY,CAAA,OAAGJ,MAAM,CAAU,KAAK,CAAC;EAE3Ce,YACST,IAAgB,EAChBC,cAA8B;IAAAf,aAAA,GAAAwB,CAAA;IAAAxB,aAAA,GAAAY,CAAA;IAD9B,KAAAE,IAAI,GAAJA,IAAI;IAAYd,aAAA,GAAAY,CAAA;IAChB,KAAAG,cAAc,GAAdA,cAAc;EACpB;EAEHU,WAAWA,CAAA;IAAAzB,aAAA,GAAAwB,CAAA;EAEX,CAAC,CADA;EAGME,OAAOA,CAAA;IAAA1B,aAAA,GAAAwB,CAAA;IAAAxB,aAAA,GAAAY,CAAA;IACb,IAAI,CAACS,KAAK,CAACM,IAAI,EAAE;EAClB;EAEOC,eAAeA,CAAA;IAAA5B,aAAA,GAAAwB,CAAA;IAAAxB,aAAA,GAAAY,CAAA;IACrB,IAAI,IAAI,CAACI,IAAI,CAACa,SAAS,EAAE;MAAA7B,aAAA,GAAA8B,CAAA;MAAA9B,aAAA,GAAAY,CAAA;MACxB,IAAI,CAACU,QAAQ,CAACK,IAAI,CAAC,IAAI,CAACX,IAAI,CAACa,SAAS,CAAC;IACxC,CAAC;MAAA7B,aAAA,GAAA8B,CAAA;IAAA;EACF;EAEA;;;EAGaC,kBAAkBA,CAACF,SAAiB;IAAA,IAAAG,KAAA;IAAA,OAAAC,iBAAA;MAAAjC,aAAA,GAAAwB,CAAA;MAAAxB,aAAA,GAAAY,CAAA;MAChD,IAAI,CAACiB,SAAS,EAAE;QAAA7B,aAAA,GAAA8B,CAAA;QAAA9B,aAAA,GAAAY,CAAA;QAAA;MAAA,CAAO;QAAAZ,aAAA,GAAA8B,CAAA;MAAA;MAAA9B,aAAA,GAAAY,CAAA;MAEvB,IAAI;QACH;QACA,MAAMsB,QAAQ,IAAAlC,aAAA,GAAAY,CAAA,cAASoB,KAAI,CAAClB,IAAI,CAC9BqB,GAAG,CAAM,2CAA2CN,SAAS,UAAU,CAAC,CACxEO,SAAS,EAAE;QAACpC,aAAA,GAAAY,CAAA;QAEd,IAAI,CAAAZ,aAAA,GAAA8B,CAAA,UAAAI,QAAQ,EAAElB,IAAI,EAAEqB,WAAW,MAAArC,aAAA,GAAA8B,CAAA,UAAII,QAAQ,EAAElB,IAAI,EAAEsB,UAAU,GAAE;UAAAtC,aAAA,GAAA8B,CAAA;UAAA9B,aAAA,GAAAY,CAAA;UAC9D;UACAoB,KAAI,CAACO,iBAAiB,CAACL,QAAQ,CAAClB,IAAI,CAACsB,UAAU,EAAEN,KAAI,CAAChB,IAAI,CAACI,KAAK,CAAC;QAClE,CAAC,MAAM;UAAApB,aAAA,GAAA8B,CAAA;UAAA9B,aAAA,GAAAY,CAAA;UACN;UACA,MAAMoB,KAAI,CAACQ,sBAAsB,CAACX,SAAS,CAAC;QAC7C;MACD,CAAC,CAAC,OAAOY,KAAK,EAAE;QAAAzC,aAAA,GAAAY,CAAA;QACf8B,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD;QAAAzC,aAAA,GAAAY,CAAA;QACA,MAAMoB,KAAI,CAACQ,sBAAsB,CAACX,SAAS,CAAC;MAC7C;IAAC;EACF;EAEA;;;EAGcW,sBAAsBA,CAACX,SAAiB;IAAA,IAAAc,MAAA;IAAA,OAAAV,iBAAA;MAAAjC,aAAA,GAAAwB,CAAA;MAAAxB,aAAA,GAAAY,CAAA;MACrD,IAAI,CAACiB,SAAS,EAAE;QAAA7B,aAAA,GAAA8B,CAAA;QAAA9B,aAAA,GAAAY,CAAA;QAAA;MAAA,CAAO;QAAAZ,aAAA,GAAA8B,CAAA;MAAA;MAAA9B,aAAA,GAAAY,CAAA;MAEvB,IAAI;QAAAZ,aAAA,GAAAY,CAAA;QACH+B,MAAI,CAACzB,UAAU,CAAC0B,GAAG,CAAC,IAAI,CAAC;QAAC5C,aAAA,GAAAY,CAAA;QAE1B+B,MAAI,CAAC5B,cAAc,CAAC8B,GAAG,CAAC;UACvBC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,qBAAqB;UAC9BC,MAAM,EAAE,oEAAoE;UAC5EC,IAAI,EAAE;SACN,CAAC;QAEF;QACA,MAAMf,QAAQ,IAAAlC,aAAA,GAAAY,CAAA,cAAS+B,MAAI,CAAC7B,IAAI,CAC9BoC,IAAI,CAAM,2CAA2CrB,SAAS,mBAAmB,EAAE,EAAE,CAAC,CACtFO,SAAS,EAAE;QAACpC,aAAA,GAAAY,CAAA;QAEd,IAAIsB,QAAQ,EAAElB,IAAI,EAAEsB,UAAU,EAAE;UAAAtC,aAAA,GAAA8B,CAAA;UAAA9B,aAAA,GAAAY,CAAA;UAC/B;UACA,MAAM+B,MAAI,CAACQ,wBAAwB,CAACjB,QAAQ,CAAClB,IAAI,CAACsB,UAAU,CAAC;QAC9D,CAAC;UAAAtC,aAAA,GAAA8B,CAAA;QAAA;MACF,CAAC,CAAC,OAAOW,KAAK,EAAE;QAAAzC,aAAA,GAAAY,CAAA;QACf8B,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAACzC,aAAA,GAAAY,CAAA;QAC/C+B,MAAI,CAACzB,UAAU,CAAC0B,GAAG,CAAC,KAAK,CAAC;QAAC5C,aAAA,GAAAY,CAAA;QAE3B+B,MAAI,CAAC5B,cAAc,CAAC8B,GAAG,CAAC;UACvBC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,uBAAuB;UAChCC,MAAM,EAAE,oDAAoD;UAC5DC,IAAI,EAAE;SACN,CAAC;MACH;IAAC;EACF;EAEA;;;EAGcE,wBAAwBA,CAACC,SAAiB;IAAA,IAAAC,MAAA;IAAA,OAAApB,iBAAA;MAAAjC,aAAA,GAAAwB,CAAA;MAAAxB,aAAA,GAAAY,CAAA;MACvD,MAAM0C,YAAW;QAAA,IAAAC,IAAA,GAAAtB,iBAAA,CAAG,aAA0B;UAAAjC,aAAA,GAAAwB,CAAA;UAAAxB,aAAA,GAAAY,CAAA;UAC7C,IAAI;YACH,MAAM4C,MAAM,IAAAxD,aAAA,GAAAY,CAAA,cAASyC,MAAI,CAACvC,IAAI,CAC5BqB,GAAG,CAAM,kCAAkCiB,SAAS,SAAS,CAAC,CAC9DhB,SAAS,EAAE;YAACpC,aAAA,GAAAY,CAAA;YAEd,IAAI4C,MAAM,EAAExC,IAAI,EAAEwC,MAAM,KAAK,WAAW,EAAE;cAAAxD,aAAA,GAAA8B,CAAA;cAAA9B,aAAA,GAAAY,CAAA;cACzCyC,MAAI,CAACnC,UAAU,CAAC0B,GAAG,CAAC,KAAK,CAAC;cAAC5C,aAAA,GAAAY,CAAA;cAE3ByC,MAAI,CAACtC,cAAc,CAAC8B,GAAG,CAAC;gBACvBC,QAAQ,EAAE,SAAS;gBACnBC,OAAO,EAAE,mBAAmB;gBAC5BC,MAAM,EAAE,mCAAmC;gBAC3CC,IAAI,EAAE;eACN,CAAC;cAEF;cAAAjD,aAAA,GAAAY,CAAA;cACAyC,MAAI,CAACd,iBAAiB,CAACa,SAAS,EAAEC,MAAI,CAACrC,IAAI,CAACI,KAAK,CAAC;YACnD,CAAC,MAAM;cAAApB,aAAA,GAAA8B,CAAA;cAAA9B,aAAA,GAAAY,CAAA;cAAA,IAAI4C,MAAM,EAAExC,IAAI,EAAEwC,MAAM,KAAK,OAAO,EAAE;gBAAAxD,aAAA,GAAA8B,CAAA;gBAAA9B,aAAA,GAAAY,CAAA;gBAC5CyC,MAAI,CAACnC,UAAU,CAAC0B,GAAG,CAAC,KAAK,CAAC;gBAAC5C,aAAA,GAAAY,CAAA;gBAE3ByC,MAAI,CAACtC,cAAc,CAAC8B,GAAG,CAAC;kBACvBC,QAAQ,EAAE,OAAO;kBACjBC,OAAO,EAAE,iBAAiB;kBAC1BC,MAAM,EAAE,+CAA+C;kBACvDC,IAAI,EAAE;iBACN,CAAC;cACH,CAAC,MAAM;gBAAAjD,aAAA,GAAA8B,CAAA;gBAAA9B,aAAA,GAAAY,CAAA;gBACN;gBACA6C,UAAU,CAAC,MAAM;kBAAAzD,aAAA,GAAAwB,CAAA;kBAAAxB,aAAA,GAAAY,CAAA;kBAAA,OAAA0C,YAAW,EAAE;gBAAF,CAAE,EAAE,IAAI,CAAC;cACtC;YAAA;UACD,CAAC,CAAC,OAAOb,KAAK,EAAE;YAAAzC,aAAA,GAAAY,CAAA;YACf8B,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;YAACzC,aAAA,GAAAY,CAAA;YAClDyC,MAAI,CAACnC,UAAU,CAAC0B,GAAG,CAAC,KAAK,CAAC;UAC3B;QACD,CAAC;QAAA,gBAnCKU,WAAWA,CAAA;UAAA,OAAAC,IAAA,CAAAG,KAAA,OAAAC,SAAA;QAAA;MAAA,GAmChB;MAED;MAAA3D,aAAA,GAAAY,CAAA;MACA6C,UAAU,CAAC,MAAM;QAAAzD,aAAA,GAAAwB,CAAA;QAAAxB,aAAA,GAAAY,CAAA;QAAA,OAAA0C,YAAW,EAAE;MAAF,CAAE,EAAE,IAAI,CAAC;IAAC;EACvC;EAEA;;;EAGQf,iBAAiBA,CAACa,SAAiB,EAAEQ,YAAoB;IAAA5D,aAAA,GAAAwB,CAAA;IAChE;IACA,MAAMqC,QAAQ,IAAA7D,aAAA,GAAAY,CAAA,QAAG,kCAAkCwC,SAAS,QAAQ;IAEpE;IACA,MAAMU,cAAc,IAAA9D,aAAA,GAAAY,CAAA,QAAGgD,YAAY,CAACG,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;IAE3D;IAAA/D,aAAA,GAAAY,CAAA;IACA,IAAI,CAACI,IAAI,GAAG;MACX,GAAG,IAAI,CAACA,IAAI;MACZG,GAAG,EAAE0C,QAAQ;MACbzC,KAAK,EAAE0C,cAAc;MACrB5C,UAAU,EAAE;KACZ;EACF;;;;;;;;;;;;YA9JCZ;IAAK;;YAOLC;IAAM;;YACNA;IAAM;;;;AATKM,qBAAqB,GAAAmD,UAAA,EAhMjC5D,SAAS,CAAC;EACV6D,QAAQ,EAAE,mBAAmB;EAC7BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACjE,YAAY,EAAES,YAAY,EAAED,YAAY,CAAC;EACnD0D,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAyGT;;CAkFD,CAAC,C,EACWvD,qBAAqB,CAgKjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}