{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-avatargroup.mjs"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, inject, HostBinding, Input, ViewEncapsulation, ChangeDetectionStrategy, Component, NgModule } from '@angular/core';\nimport { SharedModule } from 'primeng/api';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { BaseStyle } from 'primeng/base';\nconst _c0 = [\"*\"];\nconst classes = {\n  root: 'p-avatar-group p-component'\n};\nclass AvatarGroupStyle extends BaseStyle {\n  name = 'avatargroup';\n  classes = classes;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵAvatarGroupStyle_BaseFactory;\n    return function AvatarGroupStyle_Factory(__ngFactoryType__) {\n      return (ɵAvatarGroupStyle_BaseFactory || (ɵAvatarGroupStyle_BaseFactory = i0.ɵɵgetInheritedFactory(AvatarGroupStyle)))(__ngFactoryType__ || AvatarGroupStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: AvatarGroupStyle,\n    factory: AvatarGroupStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AvatarGroupStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n *\n * A set of Avatars can be displayed together using the AvatarGroup component.\n *\n * [Live Demo](https://www.primeng.org/avatar/)\n *\n * @module avatargroupstyle\n *\n */\nvar AvatarGroupClasses;\n(function (AvatarGroupClasses) {\n  AvatarGroupClasses[\"root\"] = \"p-avatar-group\";\n})(AvatarGroupClasses || (AvatarGroupClasses = {}));\n\n/**\n * AvatarGroup is a helper component for Avatar.\n * @group Components\n */\nclass AvatarGroup extends BaseComponent {\n  /**\n   * Style class of the component\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Inline style of the component.\n   * @group Props\n   */\n  style;\n  get hostClass() {\n    return this.styleClass;\n  }\n  get hostStyle() {\n    return this.style;\n  }\n  _componentStyle = inject(AvatarGroupStyle);\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵAvatarGroup_BaseFactory;\n    return function AvatarGroup_Factory(__ngFactoryType__) {\n      return (ɵAvatarGroup_BaseFactory || (ɵAvatarGroup_BaseFactory = i0.ɵɵgetInheritedFactory(AvatarGroup)))(__ngFactoryType__ || AvatarGroup);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: AvatarGroup,\n    selectors: [[\"p-avatarGroup\"], [\"p-avatar-group\"], [\"p-avatargroup\"]],\n    hostVars: 8,\n    hostBindings: function AvatarGroup_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵstyleMap(ctx.hostStyle);\n        i0.ɵɵclassMap(ctx.hostClass);\n        i0.ɵɵclassProp(\"p-avatar-group\", true)(\"p-component\", true);\n      }\n    },\n    inputs: {\n      styleClass: \"styleClass\",\n      style: \"style\"\n    },\n    features: [i0.ɵɵProvidersFeature([AvatarGroupStyle]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function AvatarGroup_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    dependencies: [CommonModule, SharedModule],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AvatarGroup, [{\n    type: Component,\n    args: [{\n      selector: 'p-avatarGroup, p-avatar-group, p-avatargroup',\n      standalone: true,\n      imports: [CommonModule, SharedModule],\n      template: ` <ng-content></ng-content> `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [AvatarGroupStyle],\n      host: {\n        '[class.p-avatar-group]': 'true',\n        '[class.p-component]': 'true'\n      }\n    }]\n  }], null, {\n    styleClass: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    hostClass: [{\n      type: HostBinding,\n      args: ['class']\n    }],\n    hostStyle: [{\n      type: HostBinding,\n      args: ['style']\n    }]\n  });\n})();\nclass AvatarGroupModule {\n  static ɵfac = function AvatarGroupModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AvatarGroupModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: AvatarGroupModule,\n    imports: [AvatarGroup, SharedModule],\n    exports: [AvatarGroup, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [AvatarGroup, SharedModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AvatarGroupModule, [{\n    type: NgModule,\n    args: [{\n      imports: [AvatarGroup, SharedModule],\n      exports: [AvatarGroup, SharedModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AvatarGroup, AvatarGroupClasses, AvatarGroupModule, AvatarGroupStyle };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,UAAU;AAAA,EACd,MAAM;AACR;AACA,IAAM,mBAAN,MAAM,0BAAyB,UAAU;AAAA,EACvC,OAAO;AAAA,EACP,UAAU;AAAA,EACV,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,yBAAyB,mBAAmB;AAC1D,cAAQ,kCAAkC,gCAAmC,sBAAsB,iBAAgB,IAAI,qBAAqB,iBAAgB;AAAA,IAC9J;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,kBAAiB;AAAA,EAC5B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAI;AAAA,CACH,SAAUA,qBAAoB;AAC7B,EAAAA,oBAAmB,MAAM,IAAI;AAC/B,GAAG,uBAAuB,qBAAqB,CAAC,EAAE;AAMlD,IAAM,cAAN,MAAM,qBAAoB,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,kBAAkB,OAAO,gBAAgB;AAAA,EACzC,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,oBAAoB,mBAAmB;AACrD,cAAQ,6BAA6B,2BAA8B,sBAAsB,YAAW,IAAI,qBAAqB,YAAW;AAAA,IAC1I;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,eAAe,GAAG,CAAC,gBAAgB,GAAG,CAAC,eAAe,CAAC;AAAA,IACpE,UAAU;AAAA,IACV,cAAc,SAAS,yBAAyB,IAAI,KAAK;AACvD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,IAAI,SAAS;AAC3B,QAAG,WAAW,IAAI,SAAS;AAC3B,QAAG,YAAY,kBAAkB,IAAI,EAAE,eAAe,IAAI;AAAA,MAC5D;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,YAAY;AAAA,MACZ,OAAO;AAAA,IACT;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC,gBAAgB,CAAC,GAAM,0BAA0B;AAAA,IACnF,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,qBAAqB,IAAI,KAAK;AAC/C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,aAAa,CAAC;AAAA,MACnB;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAc,YAAY;AAAA,IACzC,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc,YAAY;AAAA,MACpC,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,WAAW,CAAC,gBAAgB;AAAA,MAC5B,MAAM;AAAA,QACJ,0BAA0B;AAAA,QAC1B,uBAAuB;AAAA,MACzB;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,OAAO,OAAO,SAAS,0BAA0B,mBAAmB;AAClE,WAAO,KAAK,qBAAqB,oBAAmB;AAAA,EACtD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,aAAa,YAAY;AAAA,IACnC,SAAS,CAAC,aAAa,YAAY;AAAA,EACrC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,aAAa,cAAc,YAAY;AAAA,EACnD,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,aAAa,YAAY;AAAA,MACnC,SAAS,CAAC,aAAa,YAAY;AAAA,IACrC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["AvatarGroupClasses"]}