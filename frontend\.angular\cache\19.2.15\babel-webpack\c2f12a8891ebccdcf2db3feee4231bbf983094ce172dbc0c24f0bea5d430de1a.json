{"ast": null, "code": "import { Subject } from '../Subject';\nimport { Observable } from '../Observable';\nimport { defer } from './defer';\nconst DEFAULT_CONFIG = {\n  connector: () => new Subject(),\n  resetOnDisconnect: true\n};\nexport function connectable(source, config = DEFAULT_CONFIG) {\n  let connection = null;\n  const {\n    connector,\n    resetOnDisconnect = true\n  } = config;\n  let subject = connector();\n  const result = new Observable(subscriber => {\n    return subject.subscribe(subscriber);\n  });\n  result.connect = () => {\n    if (!connection || connection.closed) {\n      connection = defer(() => source).subscribe(subject);\n      if (resetOnDisconnect) {\n        connection.add(() => subject = connector());\n      }\n    }\n    return connection;\n  };\n  return result;\n}", "map": {"version": 3, "names": ["Subject", "Observable", "defer", "DEFAULT_CONFIG", "connector", "resetOnDisconnect", "connectable", "source", "config", "connection", "subject", "result", "subscriber", "subscribe", "connect", "closed", "add"], "sources": ["C:/projetos/scope-ai/frontend/node_modules/rxjs/dist/esm/internal/observable/connectable.js"], "sourcesContent": ["import { Subject } from '../Subject';\nimport { Observable } from '../Observable';\nimport { defer } from './defer';\nconst DEFAULT_CONFIG = {\n    connector: () => new Subject(),\n    resetOnDisconnect: true,\n};\nexport function connectable(source, config = DEFAULT_CONFIG) {\n    let connection = null;\n    const { connector, resetOnDisconnect = true } = config;\n    let subject = connector();\n    const result = new Observable((subscriber) => {\n        return subject.subscribe(subscriber);\n    });\n    result.connect = () => {\n        if (!connection || connection.closed) {\n            connection = defer(() => source).subscribe(subject);\n            if (resetOnDisconnect) {\n                connection.add(() => (subject = connector()));\n            }\n        }\n        return connection;\n    };\n    return result;\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,KAAK,QAAQ,SAAS;AAC/B,MAAMC,cAAc,GAAG;EACnBC,SAAS,EAAEA,CAAA,KAAM,IAAIJ,OAAO,CAAC,CAAC;EAC9BK,iBAAiB,EAAE;AACvB,CAAC;AACD,OAAO,SAASC,WAAWA,CAACC,MAAM,EAAEC,MAAM,GAAGL,cAAc,EAAE;EACzD,IAAIM,UAAU,GAAG,IAAI;EACrB,MAAM;IAAEL,SAAS;IAAEC,iBAAiB,GAAG;EAAK,CAAC,GAAGG,MAAM;EACtD,IAAIE,OAAO,GAAGN,SAAS,CAAC,CAAC;EACzB,MAAMO,MAAM,GAAG,IAAIV,UAAU,CAAEW,UAAU,IAAK;IAC1C,OAAOF,OAAO,CAACG,SAAS,CAACD,UAAU,CAAC;EACxC,CAAC,CAAC;EACFD,MAAM,CAACG,OAAO,GAAG,MAAM;IACnB,IAAI,CAACL,UAAU,IAAIA,UAAU,CAACM,MAAM,EAAE;MAClCN,UAAU,GAAGP,KAAK,CAAC,MAAMK,MAAM,CAAC,CAACM,SAAS,CAACH,OAAO,CAAC;MACnD,IAAIL,iBAAiB,EAAE;QACnBI,UAAU,CAACO,GAAG,CAAC,MAAON,OAAO,GAAGN,SAAS,CAAC,CAAE,CAAC;MACjD;IACJ;IACA,OAAOK,UAAU;EACrB,CAAC;EACD,OAAOE,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}