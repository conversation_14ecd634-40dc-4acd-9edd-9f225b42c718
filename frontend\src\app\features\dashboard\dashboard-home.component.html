<div class="flex w-full justify-between mb-3 max-md:flex-col max-md:justify-center">
	<h1 class="text-3xl font-medium text-gray-900 mb-4 md:mb-0">Dashboard</h1>
	<div class="flex flex-wrap gap-2">
		<button pButton label="Novo Projeto" icon="pi pi-plus"></button>
		<button pButton label="Relatório" icon="pi pi-file-pdf" class="p-button-outlined"></button>
	</div>
</div>

<div class="flex flex-wrap justify-center gap-4 mb-4">
	<p-card class="rounded-lg flex-grow basis-[220px] sm:basis-[250px] md:basis-[200px] lg:basis-[250px] min-w-[220px]">
		<div class="flex flex-col items-center text-center">
			<span class="text-xl text-gray-600 mb-2">Total de Projetos</span>
			<span class="text-5xl font-semibold text-gray-900 mb-2">{{ totalProjetos }}</span>
			<span class="text-green-500 font-medium">
				<i class="pi pi-arrow-up mr-1"></i>{{ totalProjetos }} projetos
			</span>
		</div>
	</p-card>

	<p-card class="rounded-lg flex-grow basis-[220px] sm:basis-[250px] md:basis-[200px] lg:basis-[250px] min-w-[220px]">
		<div class="flex flex-col items-center text-center">
			<span class="text-xl text-gray-600 mb-2">Clientes Ativos</span>
			<span class="text-5xl font-semibold text-gray-900 mb-2">{{ clientesAtivos }}</span>
			<span class="text-green-500 font-medium">
				<i class="pi pi-arrow-up mr-1"></i>{{ clientesAtivos }} clientes únicos
			</span>
		</div>
	</p-card>

	<p-card class="rounded-lg flex-grow basis-[220px] sm:basis-[250px] md:basis-[200px] lg:basis-[250px] min-w-[220px]">
		<div class="flex flex-col items-center text-center">
			<span class="text-xl text-gray-600 mb-2">Em Andamento</span>
			<span class="text-5xl font-semibold text-gray-900 mb-2">{{ projetosEmAndamento }}</span>
			<span class="text-orange-500 font-medium">
				<i class="pi pi-clock mr-1"></i>{{ projetosAtrasados }} atrasados
			</span>
		</div>
	</p-card>

	<p-card class="rounded-lg flex-grow basis-[220px] sm:basis-[250px] md:basis-[200px] lg:basis-[250px] min-w-[220px]">
		<div class="flex flex-col items-center text-center">
			<span class="text-xl text-gray-600 mb-2">Finalizados</span>
			<span class="text-5xl font-semibold text-gray-900 mb-2">{{ projetosFinalizados }}</span>
			<span class="text-blue-500 font-medium">
				<i class="pi pi-check-circle mr-1"></i>{{ projetosFinalizados }} concluídos
			</span>
		</div>
	</p-card>
</div>

<!-- Gráficos -->
<div class="flex gap-4 w-full max-lg:flex-wrap mb-4">
	<!-- CARD 1 -->
	<div class="w-full lg:w-1/2">
		<p-card header="Status dos Projetos" styleClass="h-full shadow-2 border-round-lg">
			<p-chart type="doughnut" [data]="projectStatusData" [options]="doughnutOptions" height="300px"></p-chart>
		</p-card>
	</div>

	<!-- CARD 2 -->
	<div class="w-full lg:w-1/2">
		<p-card header="Projetos por Mês" styleClass="h-full shadow-2 border-round-lg">
			<p-chart type="line" [data]="lineChartData" [options]="lineChartOptions" height="300px"></p-chart>
		</p-card>
	</div>
</div>

<!-- Projetos Recentes -->
<div class="col-12 xl:col-8 mb-3">
	<p-card header="Projetos Recentes" styleClass="shadow-2 border-round-lg">
		<p-table [value]="recentProjects" [rows]="5" styleClass="p-datatable-sm" responsivelayout="scroll">
			<ng-template pTemplate="header">
				<tr>
					<th>Projeto</th>
					<th>Cliente</th>
					<th>Status</th>
					<th>Progresso</th>
					<th>Equipe</th>
					<th>Detalhes</th>
				</tr>
			</ng-template>
			<ng-template pTemplate="body" let-project>
				<tr>
					<td>
						<div class="flex flex-column">
							<span class="font-medium">{{ project.name }}</span>
							@if (project.projeto?.nome && project.projeto.nome !== project.name) {
								<small class="text-500 mt-1">{{ project.projeto.nome }}</small>
							}
						</div>
					</td>
					<td>{{ project.client }}</td>
					<td>
						<p-tag [value]="project.status" [severity]="getSeverity(project.status)"> </p-tag>
					</td>
					<td>
						<div class="flex align-items-center">
							<p-progressBar
								[value]="project.progress"
								[showValue]="false"
								[style]="{ height: '8px', width: '80%' }"
							>
							</p-progressBar>
							<span class="ml-2">{{ project.progress }}%</span>
						</div>
					</td>
					<td>
						<p-avatarGroup styleClass="mr-1">
							@for (member of project.team.slice(0, 3); track member.name) {
								<p-avatar
									[label]="member.name.charAt(0)"
									[style]="{ 'background-color': member.color }"
									shape="circle"
									size="normal"
								>
								</p-avatar>
							}
						</p-avatarGroup>
						@if (project.team.length > 3) {
							<span class="text-sm text-500"> +{{ project.team.length - 3 }} mais </span>
						}
					</td>
					<td>
						<div class="flex flex-column gap-1">
							@if (project.projeto?.total_de_sprints) {
								<small class="text-600">
									<i class="pi pi-calendar mr-1"></i>{{ project.projeto.total_de_sprints }} sprints
								</small>
							}
							@if (project.projeto?.custos_estimados_r$?.total_estimada) {
								<small class="text-600">
									<i class="pi pi-dollar mr-1"></i>R$
									{{ project.projeto.custos_estimados_r$.total_estimada | number: '1.0-0' }}
								</small>
							}
							@if (project.projeto?.tecnologias?.length) {
								<small class="text-500">
									<i class="pi pi-cog mr-1"></i
									>{{ project.projeto.tecnologias.slice(0, 2).join(', ') }}
									@if (project.projeto.tecnologias.length > 2) {
										<span> +{{ project.projeto.tecnologias.length - 2 }}</span>
									}
								</small>
							}
						</div>
					</td>
				</tr>
			</ng-template>
		</p-table>
	</p-card>
</div>
