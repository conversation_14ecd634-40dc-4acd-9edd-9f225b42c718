<div class="card surface-0">
	<div class="text-center p-5">
		<div class="mb-3">
			<img
				src="images/scope.jpg"
				alt="ScopeAI Logo"
				width="220"
				class="mb-3 block m-auto"
				onerror="this.src='https://cdn-icons-png.flaticon.com/512/8059/8059485.png'; this.onerror=null;"
			/>
			<p class="text-xl text-700">Inteligência em Estimativas e Gestão de Projetos</p>
			<span class="text-sm text-500">Versão {{ systemVersion }} | Última atualização: {{ lastUpdate }}</span>
		</div>
		<div class="flex justify-content-center align-items-center gap-3 flex-wrap mb-4">
			<button pButton pRipple label="Manual do Usuário" icon="pi pi-book" class="p-button-outlined"></button>
			<button
				pButton
				pRipple
				label="Suporte"
				icon="pi pi-question-circle"
				class="p-button-outlined p-button-secondary"
			></button>
			<button
				pButton
				pRipple
				label="Feedback"
				icon="pi pi-comment"
				class="p-button-outlined p-button-help"
			></button>
		</div>
	</div>
</div>

<p-tabView>
	<p-tabPanel header="Sobre o ScopeAI">
		<div class="grid mt-2">
			<div class="col-12 lg:col-6 mb-3">
				<p-card header="Visão Geral" styleClass="h-full">
					<p class="text-lg line-height-3 mb-4">
						O <strong>ScopeAI</strong> é uma aplicação de gerenciamento de projetos projetada para facilitar
						o acompanhamento e a organização de projetos, clientes e suas atividades relacionadas. A
						plataforma oferece recursos para visualização de projetos e clientes, filtros, ordenação e
						gerenciamento de equipes e contatos.
					</p>
					<p class="text-lg line-height-3 mb-4">
						Nossa abordagem combina as melhores práticas de gerenciamento de projetos com tecnologias
						avançadas de inteligência artificial, permitindo estimativas mais precisas, análises preditivas
						e automação de tarefas repetitivas.
					</p>
					<p class="text-lg line-height-3">
						Desenvolvido com Angular e PrimeNG, o ScopeAI oferece uma interface moderna, responsiva e
						altamente personalizável que se adapta às necessidades específicas de cada organização e
						usuário.
					</p>
				</p-card>
			</div>
			<div class="col-12 lg:col-6">
				<p-card header="Nossa Missão" styleClass="h-full">
					<p class="text-lg line-height-3 mb-4">
						Transformar a maneira como projetos são estimados e gerenciados, combinando experiência humana
						com inteligência artificial para reduzir incertezas, aumentar a precisão das estimativas e
						proporcionar insights valiosos para tomadas de decisão estratégicas.
					</p>
					<p class="text-lg line-height-3 mb-4">
						Acreditamos que a gestão de projetos eficiente é a base para o sucesso empresarial, e que a
						tecnologia deve amplificar e complementar as habilidades humanas, não substituí-las.
					</p>
					<p class="text-lg line-height-3">
						Nosso objetivo é ajudar empresas a entregarem projetos dentro do prazo, orçamento e escopo,
						reduzindo riscos e maximizando o valor gerado para todas as partes interessadas.
					</p>
				</p-card>
			</div>
		</div>
	</p-tabPanel>

	<p-tabPanel header="Funcionalidades Atuais">
		<div class="grid mt-2">
			@for (feature of currentFeatures; track feature.id) {
				<div class="col-12 md:col-6 lg:col-3 mb-4">
					<p-card styleClass="h-full">
						<ng-template pTemplate="header">
							<div class="p-3 flex justify-content-between align-items-center">
								<i [class]="feature.icon + ' text-3xl text-primary'"></i>
								<p-tag [value]="feature.status" [severity]="getStatusSeverity(feature.status)"></p-tag>
							</div>
						</ng-template>
						<h3 class="text-xl font-semibold mb-3">{{ feature.title }}</h3>
						<p class="line-height-3">{{ feature.description }}</p>
					</p-card>
				</div>
			}
		</div>
	</p-tabPanel>

	<p-tabPanel header="Futuras Melhorias">
		<p class="text-lg line-height-3 my-3">
			O ScopeAI está em constante evolução. Conheça as tecnologias e recursos inovadores que estamos desenvolvendo
			para revolucionar a gestão de projetos e a experiência dos usuários:
		</p>

		<div class="grid mt-2">
			@for (feature of futureFeatures; track feature.id) {
				<div class="col-12 md:col-6 lg:col-4 mb-3">
					<p-card styleClass="h-full">
						<ng-template pTemplate="header">
							<div class="p-3 flex justify-content-between align-items-center gap-x-2">
								<i [class]="feature.icon + ' text-3xl text-primary'"></i>
								<p-tag [value]="feature.status" [severity]="getStatusSeverity(feature.status)"></p-tag>
							</div>
						</ng-template>
						<h3 class="text-xl font-semibold mb-3">{{ feature.title }}</h3>
						<p class="line-height-3">{{ feature.description }}</p>
					</p-card>
				</div>
			}
		</div>

		<div class="mt-5">
			<div class="text-center mb-4">
				<h3 class="text-2xl font-semibold mb-1">Integrações Planejadas</h3>
				<p class="text-lg text-600">Amplie o potencial do ScopeAI com estas integrações:</p>
			</div>

			<div class="grid mt-3">
				@for (integration of integrations; track integration.title) {
					<div class="col-12 md:col-6 lg:col-3">
						<div class="flex align-items-center gap-3 border-1 border-round p-3 surface-hover">
							<i [class]="integration.icon + ' text-2xl'"></i>
							<div class="flex-1">
								<div class="flex justify-content-between align-items-center">
									<span class="font-semibold">{{ integration.title }}</span>
									<p-tag
										[value]="getAvailabilityText(integration.available)"
										[severity]="getAvailabilityStatus(integration.available)"
									></p-tag>
								</div>
								<p class="my-2 text-sm line-height-3">{{ integration.description }}</p>
							</div>
						</div>
					</div>
				}
			</div>
		</div>
	</p-tabPanel>

	<p-tabPanel header="Roadmap">
		<div class="card">
			<div class="text-center mb-5">
				<h3 class="text-2xl font-semibold mb-1">Visão de Futuro ScopeAI</h3>
				<p class="text-lg text-600">Conheça nossa jornada de inovação e o que está por vir</p>
			</div>

			<p-timeline [value]="events" align="alternate">
				<ng-template pTemplate="marker" let-event>
					<span
						class="flex w-2rem h-2rem align-items-center justify-content-center"
						[style.background-color]="event.color"
					>
						<i [class]="event.icon + ' text-white'"></i>
					</span>
				</ng-template>
				<ng-template pTemplate="content" let-event>
					<div class="card shadow-2 surface-card">
						<div class="mb-2 flex align-items-center">
							<span class="font-semibold mr-2">{{ event.status }}</span>
							<span class="text-sm text-600">({{ event.date }})</span>
						</div>
						<p class="m-0 line-height-3">{{ event.description }}</p>
					</div>
				</ng-template>
			</p-timeline>
		</div>
	</p-tabPanel>

	<p-tabPanel header="Tecnologias">
		<div class="grid mt-3">
			<div class="col-12 md:col-6">
				<p-card header="Frontend" styleClass="h-full">
					<div class="flex flex-column gap-3">
						<div class="flex align-items-center gap-2">
							<i class="pi pi-check-circle text-primary"></i>
							<div>
								<p class="m-0 font-semibold">Angular 19</p>
								<p class="m-0 text-sm text-600">
									Framework frontend com diretivas estruturais modernas
								</p>
							</div>
						</div>
						<div class="flex align-items-center gap-2">
							<i class="pi pi-check-circle text-primary"></i>
							<div>
								<p class="m-0 font-semibold">PrimeNG</p>
								<p class="m-0 text-sm text-600">Biblioteca de componentes UI rica e personalizável</p>
							</div>
						</div>
						<div class="flex align-items-center gap-2">
							<i class="pi pi-check-circle text-primary"></i>
							<div>
								<p class="m-0 font-semibold">TypeScript</p>
								<p class="m-0 text-sm text-600">
									Linguagem fortemente tipada para desenvolvimento seguro
								</p>
							</div>
						</div>
						<div class="flex align-items-center gap-2">
							<i class="pi pi-check-circle text-primary"></i>
							<div>
								<p class="m-0 font-semibold">TensorFlow.js</p>
								<p class="m-0 text-sm text-600">Modelos de machine learning no navegador</p>
							</div>
						</div>
					</div>
				</p-card>
			</div>

			<div class="col-12 md:col-6">
				<p-card header="Backend e Inteligência Artificial" styleClass="h-full">
					<div class="flex flex-column gap-3">
						<div class="flex align-items-center gap-2">
							<i class="pi pi-check-circle text-primary"></i>
							<div>
								<p class="m-0 font-semibold">Python</p>
								<p class="m-0 text-sm text-600">Linguagem de alto nível para algoritmos complexos</p>
							</div>
						</div>
						<div class="flex align-items-center gap-2">
							<i class="pi pi-check-circle text-primary"></i>
							<div>
								<p class="m-0 font-semibold">Frameworks de IA</p>
								<p class="m-0 text-sm text-600">LangChain e Agno para orquestração de LLMs</p>
							</div>
						</div>
						<div class="flex align-items-center gap-2">
							<i class="pi pi-check-circle text-primary"></i>
							<div>
								<p class="m-0 font-semibold">RAG (Retrieval Augmented Generation)</p>
								<p class="m-0 text-sm text-600">
									Geração de conteúdo enriquecida por recuperação de conhecimento
								</p>
							</div>
						</div>
						<div class="flex align-items-center gap-2">
							<i class="pi pi-check-circle text-primary"></i>
							<div>
								<p class="m-0 font-semibold">Computer Vision</p>
								<p class="m-0 text-sm text-600">
									Tecnologias de visão computacional para análise visual
								</p>
							</div>
						</div>
					</div>
				</p-card>
			</div>
		</div>
	</p-tabPanel>
</p-tabView>

<div class="mt-6 text-center text-sm text-500">
	<p>© 2025 ScopeAI. Todos os direitos reservados.</p>
	<p>Desenvolvido por <strong>Rafael Pedro Pelizza</strong></p>
</div>
