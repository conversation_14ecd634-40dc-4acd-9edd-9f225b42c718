# Dockerfile para ScopeAI Backend (FastAPI)
FROM python:3.12-slim

# Define ambiente não interativo para o APT
ENV DEBIAN_FRONTEND=noninteractive
ENV POETRY_VERSION=2.1.3
ENV PATH="/root/.local/bin:$PATH"
ENV PYTHONUNBUFFERED=1

# Definição de diretório de trabalho
WORKDIR /scopeai

# Instalar dependências do sistema primeiro
RUN apt-get update && apt-get install -y \
	curl \
	gnupg \
	lsb-release \
	wget \
	ffmpeg \
	&& apt-get clean \
	&& rm -rf /var/lib/apt/lists/*

# Instalar Poetry
RUN curl -sSL https://install.python-poetry.org | python3 -

# Instalar Node.js e npm (necessário para Lighthouse)
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
	&& apt-get install -y nodejs

# Instalar Google Chrome estável
RUN wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | apt-key add - \
	&& echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google-chrome.list \
	&& apt-get update \
	&& apt-get install -y google-chrome-stable \
	&& apt-get clean \
	&& rm -rf /var/lib/apt/lists/*

# Instalar dependências adicionais para Chrome no container
RUN apt-get update && apt-get install -y \
	libnss3 \
	libatk-bridge2.0-0 \
	libdrm2 \
	libxkbcommon0 \
	libxcomposite1 \
	libxdamage1 \
	libxrandr2 \
	libgbm1 \
	libxss1 \
	libasound2 \
	libatspi2.0-0 \
	libgtk-3-0 \
	&& apt-get clean \
	&& rm -rf /var/lib/apt/lists/*

# Instalar Lighthouse globalmente APÓS ter Chrome
RUN npm install -g lighthouse

# Copia os arquivos de dependência primeiro (para cache)
COPY pyproject.toml poetry.lock* /scopeai/

# Instala as dependências no ambiente virtual local dentro do container
RUN poetry config virtualenvs.create false && \
	poetry install --no-interaction --no-ansi

# Instalar browsers do Playwright (após Poetry install para ter playwright disponível)
RUN playwright install chromium

# Copia o restante do código
COPY . /scopeai/

# Configura variáveis de ambiente para Chrome/Lighthouse/Playwright
ENV CHROME_BIN=/usr/bin/google-chrome-stable
ENV CHROME_PATH=/usr/bin/google-chrome-stable
ENV LIGHTHOUSE_CHROMIUM_PATH=/usr/bin/google-chrome-stable
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
ENV PUPPETEER_EXECUTABLE_PATH=/usr/bin/google-chrome-stable
ENV PLAYWRIGHT_BROWSERS_PATH=/root/.cache/ms-playwright
ENV PLAYWRIGHT_CHROMIUM_EXECUTABLE_PATH=/usr/bin/google-chrome-stable

# Expõe a porta definida
EXPOSE 8040

# Comando de inicialização - suporta reload dinâmico via variável de ambiente
CMD ["/bin/bash", "-c", "uvicorn main:app --host 0.0.0.0 --port 8040 ${UVICORN_RELOAD:-}"]
