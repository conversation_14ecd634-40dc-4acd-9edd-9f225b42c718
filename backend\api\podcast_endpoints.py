"""
Podcast Generation API Endpoints
"""
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from fastapi.responses import StreamingResponse
from typing import Dict, Optional
import json
import logging
from datetime import datetime
import io

from tools.reports.podcast_generator import PodcastGenerator
from api.base_controller import BaseController
from api.response_formatter import response_formatter
from shared.websocket_manager import websocket_manager
from clients.db import db as database
from gridfs import GridFS
from bson import ObjectId

logger = logging.getLogger(__name__)
router = APIRouter()
base_controller = BaseController()


class PodcastController:
    """Controller for podcast generation operations"""
    
    def __init__(self):
        self.generator = PodcastGenerator()
        self.db = database
        self.fs = GridFS(self.db)
        self.ws_manager = websocket_manager
    
    async def generate_podcast(
        self,
        project_id: str,
        background_tasks: BackgroundTasks
    ) -> Dict:
        """Generate podcast for a project"""
        try:
            # Get project data
            project = self.db.projetos.find_one({"_id": ObjectId(project_id)})
            if not project:
                raise HTTPException(status_code=404, detail="Project not found")
            
            # Check if podcast already exists
            existing_podcast = self.db.podcasts.find_one({
                "project_id": project_id,
                "status": "completed"
            })
            
            if existing_podcast and "audio_file_id" in existing_podcast:
                # Return existing podcast
                return {
                    "podcast_id": str(existing_podcast["_id"]),
                    "status": "completed",
                    "duration": existing_podcast.get("duration", 0),
                    "script": existing_podcast.get("script", {}),
                    "created_at": existing_podcast.get("created_at")
                }
            
            # Create podcast record
            podcast_doc = {
                "project_id": project_id,
                "status": "processing",
                "created_at": datetime.utcnow(),
                "progress": 0
            }
            podcast_id = self.db.podcasts.insert_one(podcast_doc).inserted_id
            
            # Generate podcast in background
            background_tasks.add_task(
                self._generate_podcast_async,
                project,
                str(podcast_id)
            )
            
            return {
                "podcast_id": str(podcast_id),
                "status": "processing",
                "message": "Podcast generation started"
            }
            
        except Exception as e:
            logger.error(f"Error generating podcast: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def _generate_podcast_async(self, project: Dict, podcast_id: str):
        """Generate podcast asynchronously"""
        try:
            # Send progress update
            await self.ws_manager.broadcast({
                "type": "podcast_progress",
                "project_id": str(project['_id']),
                "podcast_id": podcast_id,
                "progress": 10,
                "message": "Iniciando geração do podcast..."
            })
            
            # Prepare project data
            project_data = {
                "name": project.get("nome_projeto", ""),
                "description": project.get("resumo", "") or project.get("justificativa", ""),
                "client": {
                    "name": project.get("cliente_nome", ""),
                    "sector": project.get("cliente_sector", "")
                },
                "market_research": project.get("market_research", {}),
                "agent_analyses": project.get("agentes_justificativa", [])
            }
            
            # Update progress
            await self.ws_manager.broadcast({
                "type": "podcast_progress",
                "project_id": str(project['_id']),
                "podcast_id": podcast_id,
                "progress": 30,
                "message": "Gerando roteiro do podcast..."
            })
            
            # Generate podcast
            audio_bytes, script = await self.generator.generate_podcast(project_data)
            
            # Update progress
            await self.ws_manager.broadcast({
                "type": "podcast_progress",
                "project_id": str(project['_id']),
                "podcast_id": podcast_id,
                "progress": 80,
                "message": "Salvando áudio..."
            })
            
            # Save audio to GridFS
            audio_file_id = self.fs.put(
                audio_bytes,
                filename=f"podcast_{project.get('nome_projeto', 'projeto')}_{podcast_id}.mp3",
                content_type="audio/mpeg",
                metadata={
                    "project_id": str(project["_id"]),
                    "podcast_id": podcast_id,
                    "created_at": datetime.utcnow()
                }
            )
            
            # Calculate duration
            duration = self.generator.estimate_duration(script)
            
            # Update podcast record
            self.db.podcasts.update_one(
                {"_id": ObjectId(podcast_id)},
                {
                    "$set": {
                        "status": "completed",
                        "audio_file_id": audio_file_id,
                        "script": script.dict(),
                        "duration": duration,
                        "completed_at": datetime.utcnow()
                    }
                }
            )
            
            # Send completion update
            await self.ws_manager.broadcast({
                "type": "podcast_complete",
                "project_id": str(project['_id']),
                "podcast_id": podcast_id,
                "progress": 100,
                "message": "Podcast gerado com sucesso!",
                "duration": duration
            })
            
        except Exception as e:
            logger.error(f"Error in async podcast generation: {str(e)}")
            
            # Update podcast record with error
            self.db.podcasts.update_one(
                {"_id": ObjectId(podcast_id)},
                {
                    "$set": {
                        "status": "error",
                        "error": str(e),
                        "completed_at": datetime.utcnow()
                    }
                }
            )
            
            # Send error update
            await self.ws_manager.broadcast({
                "type": "podcast_error",
                "project_id": str(project['_id']),
                "podcast_id": podcast_id,
                "error": str(e),
                "message": "Erro ao gerar podcast"
            })
    
    async def get_podcast_status(self, podcast_id: str) -> Dict:
        """Get podcast generation status"""
        try:
            podcast = self.db.podcasts.find_one({"_id": ObjectId(podcast_id)})
            if not podcast:
                raise HTTPException(status_code=404, detail="Podcast not found")
            
            return {
                "podcast_id": str(podcast["_id"]),
                "status": podcast.get("status", "unknown"),
                "progress": podcast.get("progress", 0),
                "duration": podcast.get("duration", 0),
                "script": podcast.get("script", {}),
                "created_at": podcast.get("created_at"),
                "completed_at": podcast.get("completed_at")
            }
            
        except Exception as e:
            logger.error(f"Error getting podcast status: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def stream_podcast_audio(self, podcast_id: str):
        """Stream podcast audio file"""
        try:
            podcast = self.db.podcasts.find_one({"_id": ObjectId(podcast_id)})
            if not podcast:
                raise HTTPException(status_code=404, detail="Podcast not found")
            
            if "audio_file_id" not in podcast:
                raise HTTPException(status_code=404, detail="Audio file not found")
            
            # Get audio file from GridFS
            audio_file = self.fs.get(podcast["audio_file_id"])
            
            # Create streaming response
            return StreamingResponse(
                io.BytesIO(audio_file.read()),
                media_type="audio/mpeg",
                headers={
                    "Content-Disposition": f"inline; filename=podcast_{podcast_id}.mp3"
                }
            )
            
        except Exception as e:
            logger.error(f"Error streaming podcast audio: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))


# Initialize controller
podcast_controller = PodcastController()


@router.post("/projects/{project_id}/podcast/generate")
async def generate_podcast(
    project_id: str,
    background_tasks: BackgroundTasks
) -> Dict:
    """Generate a persuasive podcast for the project"""
    result = await podcast_controller.generate_podcast(project_id, background_tasks)
    return response_formatter.success(result)


@router.get("/{podcast_id}/status")
async def get_podcast_status(podcast_id: str) -> Dict:
    """Get podcast generation status"""
    result = await podcast_controller.get_podcast_status(podcast_id)
    return response_formatter.success(result)


@router.get("/{podcast_id}/audio")
async def stream_podcast_audio(podcast_id: str):
    """Stream podcast audio file"""
    return await podcast_controller.stream_podcast_audio(podcast_id)


@router.head("/{podcast_id}/audio")
async def head_podcast_audio(podcast_id: str):
    """Check if podcast audio exists"""
    return await podcast_controller.stream_podcast_audio(podcast_id)


@router.get("/projects/{project_id}/podcast")
async def get_project_podcast(project_id: str) -> Dict:
    """Get podcast for a project if it exists"""
    try:
        podcast = podcast_controller.db.podcasts.find_one({
            "project_id": project_id,
            "status": "completed"
        })
        
        if not podcast:
            return response_formatter.success({"has_podcast": False})
        
        return response_formatter.success({
            "has_podcast": True,
            "podcast_id": str(podcast["_id"]),
            "duration": podcast.get("duration", 0),
            "created_at": podcast.get("created_at")
        })
        
    except Exception as e:
        logger.error(f"Error getting project podcast: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))