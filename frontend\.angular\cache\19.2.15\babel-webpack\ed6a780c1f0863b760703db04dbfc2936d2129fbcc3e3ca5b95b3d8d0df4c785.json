{"ast": null, "code": "// src/dom/methods/hasClass.ts\nfunction hasClass(element, className) {\n  if (element) {\n    if (element.classList) return element.classList.contains(className);else return new RegExp(\"(^| )\" + className + \"( |$)\", \"gi\").test(element.className);\n  }\n  return false;\n}\n\n// src/dom/methods/addClass.ts\nfunction addClass(element, className) {\n  if (element && className) {\n    const fn = _className => {\n      if (!hasClass(element, _className)) {\n        if (element.classList) element.classList.add(_className);else element.className += \" \" + _className;\n      }\n    };\n    [className].flat().filter(Boolean).forEach(_classNames => _classNames.split(\" \").forEach(fn));\n  }\n}\n\n// src/dom/methods/calculateBodyScrollbarWidth.ts\nfunction calculateBodyScrollbarWidth() {\n  return window.innerWidth - document.documentElement.offsetWidth;\n}\n\n// src/dom/methods/getCSSVariableByRegex.ts\nfunction getCSSVariableByRegex(variableRegex) {\n  for (const sheet of document == null ? void 0 : document.styleSheets) {\n    try {\n      for (const rule of sheet == null ? void 0 : sheet.cssRules) {\n        for (const property of rule == null ? void 0 : rule.style) {\n          if (variableRegex.test(property)) {\n            return {\n              name: property,\n              value: rule.style.getPropertyValue(property).trim()\n            };\n          }\n        }\n      }\n    } catch (e) {}\n  }\n  return null;\n}\n\n// src/dom/helpers/blockBodyScroll.ts\nfunction blockBodyScroll(className = \"p-overflow-hidden\") {\n  const variableData = getCSSVariableByRegex(/-scrollbar-width$/);\n  (variableData == null ? void 0 : variableData.name) && document.body.style.setProperty(variableData.name, calculateBodyScrollbarWidth() + \"px\");\n  addClass(document.body, className);\n}\n\n// src/dom/helpers/saveAs.ts\nfunction saveAs(file) {\n  if (file) {\n    let link = document.createElement(\"a\");\n    if (link.download !== void 0) {\n      const {\n        name,\n        src\n      } = file;\n      link.setAttribute(\"href\", src);\n      link.setAttribute(\"download\", name);\n      link.style.display = \"none\";\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      return true;\n    }\n  }\n  return false;\n}\n\n// src/dom/helpers/exportCSV.ts\nfunction exportCSV(csv, filename) {\n  let blob = new Blob([csv], {\n    type: \"application/csv;charset=utf-8;\"\n  });\n  if (window.navigator.msSaveOrOpenBlob) {\n    navigator.msSaveOrOpenBlob(blob, filename + \".csv\");\n  } else {\n    const isDownloaded = saveAs({\n      name: filename + \".csv\",\n      src: URL.createObjectURL(blob)\n    });\n    if (!isDownloaded) {\n      csv = \"data:text/csv;charset=utf-8,\" + csv;\n      window.open(encodeURI(csv));\n    }\n  }\n}\n\n// src/dom/methods/removeClass.ts\nfunction removeClass(element, className) {\n  if (element && className) {\n    const fn = _className => {\n      if (element.classList) element.classList.remove(_className);else element.className = element.className.replace(new RegExp(\"(^|\\\\b)\" + _className.split(\" \").join(\"|\") + \"(\\\\b|$)\", \"gi\"), \" \");\n    };\n    [className].flat().filter(Boolean).forEach(_classNames => _classNames.split(\" \").forEach(fn));\n  }\n}\n\n// src/dom/helpers/unblockBodyScroll.ts\nfunction unblockBodyScroll(className = \"p-overflow-hidden\") {\n  const variableData = getCSSVariableByRegex(/-scrollbar-width$/);\n  (variableData == null ? void 0 : variableData.name) && document.body.style.removeProperty(variableData.name);\n  removeClass(document.body, className);\n}\n\n// src/dom/methods/getHiddenElementDimensions.ts\nfunction getHiddenElementDimensions(element) {\n  let dimensions = {\n    width: 0,\n    height: 0\n  };\n  if (element) {\n    element.style.visibility = \"hidden\";\n    element.style.display = \"block\";\n    dimensions.width = element.offsetWidth;\n    dimensions.height = element.offsetHeight;\n    element.style.display = \"none\";\n    element.style.visibility = \"visible\";\n  }\n  return dimensions;\n}\n\n// src/dom/methods/getViewport.ts\nfunction getViewport() {\n  let win = window,\n    d = document,\n    e = d.documentElement,\n    g = d.getElementsByTagName(\"body\")[0],\n    w = win.innerWidth || e.clientWidth || g.clientWidth,\n    h = win.innerHeight || e.clientHeight || g.clientHeight;\n  return {\n    width: w,\n    height: h\n  };\n}\n\n// src/dom/methods/getWindowScrollLeft.ts\nfunction getWindowScrollLeft() {\n  let doc = document.documentElement;\n  return (window.pageXOffset || doc.scrollLeft) - (doc.clientLeft || 0);\n}\n\n// src/dom/methods/getWindowScrollTop.ts\nfunction getWindowScrollTop() {\n  let doc = document.documentElement;\n  return (window.pageYOffset || doc.scrollTop) - (doc.clientTop || 0);\n}\n\n// src/dom/methods/absolutePosition.ts\nfunction absolutePosition(element, target, gutter = true) {\n  var _a, _b, _c, _d;\n  if (element) {\n    const elementDimensions = element.offsetParent ? {\n      width: element.offsetWidth,\n      height: element.offsetHeight\n    } : getHiddenElementDimensions(element);\n    const elementOuterHeight = elementDimensions.height;\n    const elementOuterWidth = elementDimensions.width;\n    const targetOuterHeight = target.offsetHeight;\n    const targetOuterWidth = target.offsetWidth;\n    const targetOffset = target.getBoundingClientRect();\n    const windowScrollTop = getWindowScrollTop();\n    const windowScrollLeft = getWindowScrollLeft();\n    const viewport = getViewport();\n    let top,\n      left,\n      origin = \"top\";\n    if (targetOffset.top + targetOuterHeight + elementOuterHeight > viewport.height) {\n      top = targetOffset.top + windowScrollTop - elementOuterHeight;\n      origin = \"bottom\";\n      if (top < 0) {\n        top = windowScrollTop;\n      }\n    } else {\n      top = targetOuterHeight + targetOffset.top + windowScrollTop;\n    }\n    if (targetOffset.left + elementOuterWidth > viewport.width) left = Math.max(0, targetOffset.left + windowScrollLeft + targetOuterWidth - elementOuterWidth);else left = targetOffset.left + windowScrollLeft;\n    element.style.top = top + \"px\";\n    element.style.left = left + \"px\";\n    element.style.transformOrigin = origin;\n    gutter && (element.style.marginTop = origin === \"bottom\" ? `calc(${(_b = (_a = getCSSVariableByRegex(/-anchor-gutter$/)) == null ? void 0 : _a.value) != null ? _b : \"2px\"} * -1)` : (_d = (_c = getCSSVariableByRegex(/-anchor-gutter$/)) == null ? void 0 : _c.value) != null ? _d : \"\");\n  }\n}\n\n// src/dom/methods/addStyle.ts\nfunction addStyle(element, style) {\n  if (element) {\n    if (typeof style === \"string\") {\n      element.style.cssText = style;\n    } else {\n      Object.entries(style || {}).forEach(([key, value]) => element.style[key] = value);\n    }\n  }\n}\n\n// src/dom/methods/getOuterWidth.ts\nfunction getOuterWidth(element, margin) {\n  if (element instanceof HTMLElement) {\n    let width = element.offsetWidth;\n    if (margin) {\n      let style = getComputedStyle(element);\n      width += parseFloat(style.marginLeft) + parseFloat(style.marginRight);\n    }\n    return width;\n  }\n  return 0;\n}\n\n// src/dom/methods/relativePosition.ts\nfunction relativePosition(element, target, gutter = true) {\n  var _a, _b, _c, _d;\n  if (element) {\n    const elementDimensions = element.offsetParent ? {\n      width: element.offsetWidth,\n      height: element.offsetHeight\n    } : getHiddenElementDimensions(element);\n    const targetHeight = target.offsetHeight;\n    const targetOffset = target.getBoundingClientRect();\n    const viewport = getViewport();\n    let top,\n      left,\n      origin = \"top\";\n    if (targetOffset.top + targetHeight + elementDimensions.height > viewport.height) {\n      top = -1 * elementDimensions.height;\n      origin = \"bottom\";\n      if (targetOffset.top + top < 0) {\n        top = -1 * targetOffset.top;\n      }\n    } else {\n      top = targetHeight;\n    }\n    if (elementDimensions.width > viewport.width) {\n      left = targetOffset.left * -1;\n    } else if (targetOffset.left + elementDimensions.width > viewport.width) {\n      left = (targetOffset.left + elementDimensions.width - viewport.width) * -1;\n    } else {\n      left = 0;\n    }\n    element.style.top = top + \"px\";\n    element.style.left = left + \"px\";\n    element.style.transformOrigin = origin;\n    gutter && (element.style.marginTop = origin === \"bottom\" ? `calc(${(_b = (_a = getCSSVariableByRegex(/-anchor-gutter$/)) == null ? void 0 : _a.value) != null ? _b : \"2px\"} * -1)` : (_d = (_c = getCSSVariableByRegex(/-anchor-gutter$/)) == null ? void 0 : _c.value) != null ? _d : \"\");\n  }\n}\n\n// src/dom/methods/alignOverlay.ts\nfunction alignOverlay(overlay, target, appendTo, calculateMinWidth = true) {\n  if (overlay && target) {\n    if (appendTo === \"self\") {\n      relativePosition(overlay, target);\n    } else {\n      calculateMinWidth && (overlay.style.minWidth = getOuterWidth(target) + \"px\");\n      absolutePosition(overlay, target);\n    }\n  }\n}\n\n// src/dom/methods/isElement.ts\nfunction isElement(element) {\n  return typeof HTMLElement === \"object\" ? element instanceof HTMLElement : element && typeof element === \"object\" && element !== null && element.nodeType === 1 && typeof element.nodeName === \"string\";\n}\n\n// src/dom/methods/toElement.ts\nfunction toElement(element) {\n  let target = element;\n  if (element && typeof element === \"object\") {\n    if (element.hasOwnProperty(\"current\")) {\n      target = element.current;\n    } else if (element.hasOwnProperty(\"el\")) {\n      if (element.el.hasOwnProperty(\"nativeElement\")) {\n        target = element.el.nativeElement;\n      } else {\n        target = element.el;\n      }\n    }\n  }\n  return isElement(target) ? target : void 0;\n}\n\n// src/dom/methods/appendChild.ts\nfunction appendChild(element, child) {\n  const target = toElement(element);\n  if (target) target.appendChild(child);else throw new Error(\"Cannot append \" + child + \" to \" + element);\n}\n\n// src/dom/methods/calculateScrollbarHeight.ts\nvar calculatedScrollbarHeight = void 0;\nfunction calculateScrollbarHeight(element) {\n  if (element) {\n    let style = getComputedStyle(element);\n    return element.offsetHeight - element.clientHeight - parseFloat(style.borderTopWidth) - parseFloat(style.borderBottomWidth);\n  } else {\n    if (calculatedScrollbarHeight != null) return calculatedScrollbarHeight;\n    let scrollDiv = document.createElement(\"div\");\n    addStyle(scrollDiv, {\n      width: \"100px\",\n      height: \"100px\",\n      overflow: \"scroll\",\n      position: \"absolute\",\n      top: \"-9999px\"\n    });\n    document.body.appendChild(scrollDiv);\n    let scrollbarHeight = scrollDiv.offsetHeight - scrollDiv.clientHeight;\n    document.body.removeChild(scrollDiv);\n    calculatedScrollbarHeight = scrollbarHeight;\n    return scrollbarHeight;\n  }\n}\n\n// src/dom/methods/calculateScrollbarWidth.ts\nvar calculatedScrollbarWidth = void 0;\nfunction calculateScrollbarWidth(element) {\n  if (element) {\n    let style = getComputedStyle(element);\n    return element.offsetWidth - element.clientWidth - parseFloat(style.borderLeftWidth) - parseFloat(style.borderRightWidth);\n  } else {\n    if (calculatedScrollbarWidth != null) return calculatedScrollbarWidth;\n    let scrollDiv = document.createElement(\"div\");\n    addStyle(scrollDiv, {\n      width: \"100px\",\n      height: \"100px\",\n      overflow: \"scroll\",\n      position: \"absolute\",\n      top: \"-9999px\"\n    });\n    document.body.appendChild(scrollDiv);\n    let scrollbarWidth = scrollDiv.offsetWidth - scrollDiv.clientWidth;\n    document.body.removeChild(scrollDiv);\n    calculatedScrollbarWidth = scrollbarWidth;\n    return scrollbarWidth;\n  }\n}\n\n// src/dom/methods/clearSelection.ts\nfunction clearSelection() {\n  if (window.getSelection) {\n    const selection = window.getSelection() || {};\n    if (selection.empty) {\n      selection.empty();\n    } else if (selection.removeAllRanges && selection.rangeCount > 0 && selection.getRangeAt(0).getClientRects().length > 0) {\n      selection.removeAllRanges();\n    }\n  }\n}\n\n// src/dom/methods/setAttributes.ts\nfunction setAttributes(element, attributes = {}) {\n  if (isElement(element)) {\n    const computedStyles = (rule, value) => {\n      var _a, _b;\n      const styles = ((_a = element == null ? void 0 : element.$attrs) == null ? void 0 : _a[rule]) ? [(_b = element == null ? void 0 : element.$attrs) == null ? void 0 : _b[rule]] : [];\n      return [value].flat().reduce((cv, v) => {\n        if (v !== null && v !== void 0) {\n          const type = typeof v;\n          if (type === \"string\" || type === \"number\") {\n            cv.push(v);\n          } else if (type === \"object\") {\n            const _cv = Array.isArray(v) ? computedStyles(rule, v) : Object.entries(v).map(([_k, _v]) => rule === \"style\" && (!!_v || _v === 0) ? `${_k.replace(/([a-z])([A-Z])/g, \"$1-$2\").toLowerCase()}:${_v}` : !!_v ? _k : void 0);\n            cv = _cv.length ? cv.concat(_cv.filter(c => !!c)) : cv;\n          }\n        }\n        return cv;\n      }, styles);\n    };\n    Object.entries(attributes).forEach(([key, value]) => {\n      if (value !== void 0 && value !== null) {\n        const matchedEvent = key.match(/^on(.+)/);\n        if (matchedEvent) {\n          element.addEventListener(matchedEvent[1].toLowerCase(), value);\n        } else if (key === \"p-bind\" || key === \"pBind\") {\n          setAttributes(element, value);\n        } else {\n          value = key === \"class\" ? [...new Set(computedStyles(\"class\", value))].join(\" \").trim() : key === \"style\" ? computedStyles(\"style\", value).join(\";\").trim() : value;\n          (element.$attrs = element.$attrs || {}) && (element.$attrs[key] = value);\n          element.setAttribute(key, value);\n        }\n      }\n    });\n  }\n}\n\n// src/dom/methods/createElement.ts\nfunction createElement(type, attributes = {}, ...children) {\n  if (type) {\n    const element = document.createElement(type);\n    setAttributes(element, attributes);\n    element.append(...children);\n    return element;\n  }\n  return void 0;\n}\n\n// src/dom/methods/createStyleAsString.ts\nfunction createStyleAsString(css, options = {}) {\n  return css ? `'<style ${Object.entries(options).reduce((s, [k, v]) => s + `${k}=\"${v}\"`, \" \")}>${css}</style>'` : \"\";\n}\n\n// src/dom/methods/createStyleTag.ts\nfunction createStyleTag(attributes = {}, container) {\n  let element = document.createElement(\"style\");\n  setAttributes(element, attributes);\n  if (!container) {\n    container = document.head;\n  }\n  container.appendChild(element);\n  return element;\n}\n\n// src/dom/methods/fadeIn.ts\nfunction fadeIn(element, duration) {\n  if (element) {\n    element.style.opacity = \"0\";\n    let last = + /* @__PURE__ */new Date();\n    let opacity = \"0\";\n    let tick = function () {\n      opacity = `${+element.style.opacity + ((/* @__PURE__ */new Date()).getTime() - last) / duration}`;\n      element.style.opacity = opacity;\n      last = + /* @__PURE__ */new Date();\n      if (+opacity < 1) {\n        !!window.requestAnimationFrame && requestAnimationFrame(tick) || setTimeout(tick, 16);\n      }\n    };\n    tick();\n  }\n}\n\n// src/dom/methods/fadeOut.ts\nfunction fadeOut(element, duration) {\n  if (element) {\n    let opacity = 1,\n      interval = 50,\n      gap = interval / duration;\n    let fading = setInterval(() => {\n      opacity -= gap;\n      if (opacity <= 0) {\n        opacity = 0;\n        clearInterval(fading);\n      }\n      element.style.opacity = opacity.toString();\n    }, interval);\n  }\n}\n\n// src/dom/methods/find.ts\nfunction find(element, selector) {\n  return isElement(element) ? Array.from(element.querySelectorAll(selector)) : [];\n}\n\n// src/dom/methods/findSingle.ts\nfunction findSingle(element, selector) {\n  return isElement(element) ? element.matches(selector) ? element : element.querySelector(selector) : null;\n}\n\n// src/dom/methods/focus.ts\nfunction focus(element, options) {\n  element && document.activeElement !== element && element.focus(options);\n}\n\n// src/dom/methods/getAttribute.ts\nfunction getAttribute(element, name) {\n  if (isElement(element)) {\n    const value = element.getAttribute(name);\n    if (!isNaN(value)) {\n      return +value;\n    }\n    if (value === \"true\" || value === \"false\") {\n      return value === \"true\";\n    }\n    return value;\n  }\n  return void 0;\n}\n\n// src/dom/methods/resolveUserAgent.ts\nfunction resolveUserAgent() {\n  let ua = navigator.userAgent.toLowerCase();\n  let match = /(chrome)[ ]([\\w.]+)/.exec(ua) || /(webkit)[ ]([\\w.]+)/.exec(ua) || /(opera)(?:.*version|)[ ]([\\w.]+)/.exec(ua) || /(msie) ([\\w.]+)/.exec(ua) || ua.indexOf(\"compatible\") < 0 && /(mozilla)(?:.*? rv:([\\w.]+)|)/.exec(ua) || [];\n  return {\n    browser: match[1] || \"\",\n    version: match[2] || \"0\"\n  };\n}\n\n// src/dom/methods/getBrowser.ts\nvar browser = null;\nfunction getBrowser() {\n  if (!browser) {\n    browser = {};\n    let matched = resolveUserAgent();\n    if (matched.browser) {\n      browser[matched.browser] = true;\n      browser[\"version\"] = matched.version;\n    }\n    if (browser[\"chrome\"]) {\n      browser[\"webkit\"] = true;\n    } else if (browser[\"webkit\"]) {\n      browser[\"safari\"] = true;\n    }\n  }\n  return browser;\n}\n\n// src/dom/methods/getBrowserLanguage.ts\nfunction getBrowserLanguage() {\n  return navigator.languages && navigator.languages.length && navigator.languages[0] || navigator.language || \"en\";\n}\n\n// src/dom/methods/getCSSProperty.ts\nfunction getCSSProperty(element, property, inline) {\n  var _a;\n  if (element && property) {\n    return inline ? (_a = element == null ? void 0 : element.style) == null ? void 0 : _a.getPropertyValue(property) : getComputedStyle(element).getPropertyValue(property);\n  }\n  return null;\n}\n\n// src/dom/methods/getCursorOffset.ts\nfunction getCursorOffset(element, prevText, nextText, currentText) {\n  if (element) {\n    let style = getComputedStyle(element);\n    let ghostDiv = document.createElement(\"div\");\n    ghostDiv.style.position = \"absolute\";\n    ghostDiv.style.top = \"0px\";\n    ghostDiv.style.left = \"0px\";\n    ghostDiv.style.visibility = \"hidden\";\n    ghostDiv.style.pointerEvents = \"none\";\n    ghostDiv.style.overflow = style.overflow;\n    ghostDiv.style.width = style.width;\n    ghostDiv.style.height = style.height;\n    ghostDiv.style.padding = style.padding;\n    ghostDiv.style.border = style.border;\n    ghostDiv.style.overflowWrap = style.overflowWrap;\n    ghostDiv.style.whiteSpace = style.whiteSpace;\n    ghostDiv.style.lineHeight = style.lineHeight;\n    ghostDiv.innerHTML = prevText.replace(/\\r\\n|\\r|\\n/g, \"<br />\");\n    let ghostSpan = document.createElement(\"span\");\n    ghostSpan.textContent = currentText;\n    ghostDiv.appendChild(ghostSpan);\n    let text = document.createTextNode(nextText);\n    ghostDiv.appendChild(text);\n    document.body.appendChild(ghostDiv);\n    const {\n      offsetLeft,\n      offsetTop,\n      clientHeight\n    } = ghostSpan;\n    document.body.removeChild(ghostDiv);\n    return {\n      left: Math.abs(offsetLeft - element.scrollLeft),\n      top: Math.abs(offsetTop - element.scrollTop) + clientHeight\n    };\n  }\n  return {\n    top: \"auto\",\n    left: \"auto\"\n  };\n}\n\n// src/dom/methods/getFocusableElements.ts\nfunction getFocusableElements(element, selector = \"\") {\n  let focusableElements = find(element, `button:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n            [href][clientHeight][clientWidth]:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n            input:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n            select:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n            textarea:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n            [tabIndex]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n            [contenteditable]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector}`);\n  let visibleFocusableElements = [];\n  for (let focusableElement of focusableElements) {\n    if (getComputedStyle(focusableElement).display != \"none\" && getComputedStyle(focusableElement).visibility != \"hidden\") visibleFocusableElements.push(focusableElement);\n  }\n  return visibleFocusableElements;\n}\n\n// src/dom/methods/getFirstFocusableElement.ts\nfunction getFirstFocusableElement(element, selector) {\n  const focusableElements = getFocusableElements(element, selector);\n  return focusableElements.length > 0 ? focusableElements[0] : null;\n}\n\n// src/dom/methods/getHeight.ts\nfunction getHeight(element) {\n  if (element) {\n    let height = element.offsetHeight;\n    let style = getComputedStyle(element);\n    height -= parseFloat(style.paddingTop) + parseFloat(style.paddingBottom) + parseFloat(style.borderTopWidth) + parseFloat(style.borderBottomWidth);\n    return height;\n  }\n  return 0;\n}\n\n// src/dom/methods/getHiddenElementOuterHeight.ts\nfunction getHiddenElementOuterHeight(element) {\n  if (element) {\n    element.style.visibility = \"hidden\";\n    element.style.display = \"block\";\n    let elementHeight = element.offsetHeight;\n    element.style.display = \"none\";\n    element.style.visibility = \"visible\";\n    return elementHeight;\n  }\n  return 0;\n}\n\n// src/dom/methods/getHiddenElementOuterWidth.ts\nfunction getHiddenElementOuterWidth(element) {\n  if (element) {\n    element.style.visibility = \"hidden\";\n    element.style.display = \"block\";\n    let elementWidth = element.offsetWidth;\n    element.style.display = \"none\";\n    element.style.visibility = \"visible\";\n    return elementWidth;\n  }\n  return 0;\n}\n\n// src/dom/methods/getParentNode.ts\nfunction getParentNode(element) {\n  if (element) {\n    let parent = element.parentNode;\n    if (parent && parent instanceof ShadowRoot && parent.host) {\n      parent = parent.host;\n    }\n    return parent;\n  }\n  return null;\n}\n\n// src/dom/methods/getIndex.ts\nfunction getIndex(element) {\n  var _a;\n  if (element) {\n    let children = (_a = getParentNode(element)) == null ? void 0 : _a.childNodes;\n    let num = 0;\n    if (children) {\n      for (let i = 0; i < children.length; i++) {\n        if (children[i] === element) return num;\n        if (children[i].nodeType === 1) num++;\n      }\n    }\n  }\n  return -1;\n}\n\n// src/dom/methods/getInnerWidth.ts\nfunction getInnerWidth(element) {\n  if (element) {\n    let width = element.offsetWidth;\n    let style = getComputedStyle(element);\n    width -= parseFloat(style.borderLeft) + parseFloat(style.borderRight);\n    return width;\n  }\n  return 0;\n}\n\n// src/dom/methods/getLastFocusableElement.ts\nfunction getLastFocusableElement(element, selector) {\n  const focusableElements = getFocusableElements(element, selector);\n  return focusableElements.length > 0 ? focusableElements[focusableElements.length - 1] : null;\n}\n\n// src/dom/methods/getNextElementSibling.ts\nfunction getNextElementSibling(element, selector) {\n  let nextElement = element.nextElementSibling;\n  while (nextElement) {\n    if (nextElement.matches(selector)) {\n      return nextElement;\n    } else {\n      nextElement = nextElement.nextElementSibling;\n    }\n  }\n  return null;\n}\n\n// src/dom/methods/getNextFocusableElement.ts\nfunction getNextFocusableElement(container, element, selector) {\n  const focusableElements = getFocusableElements(container, selector);\n  const index = focusableElements.length > 0 ? focusableElements.findIndex(el => el === element) : -1;\n  const nextIndex = index > -1 && focusableElements.length >= index + 1 ? index + 1 : -1;\n  return nextIndex > -1 ? focusableElements[nextIndex] : null;\n}\n\n// src/dom/methods/getOffset.ts\nfunction getOffset(element) {\n  if (element) {\n    let rect = element.getBoundingClientRect();\n    return {\n      top: rect.top + (window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0),\n      left: rect.left + (window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft || 0)\n    };\n  }\n  return {\n    top: \"auto\",\n    left: \"auto\"\n  };\n}\n\n// src/dom/methods/getOuterHeight.ts\nfunction getOuterHeight(element, margin) {\n  if (element) {\n    let height = element.offsetHeight;\n    if (margin) {\n      let style = getComputedStyle(element);\n      height += parseFloat(style.marginTop) + parseFloat(style.marginBottom);\n    }\n    return height;\n  }\n  return 0;\n}\n\n// src/dom/methods/getParents.ts\nfunction getParents(element, parents = []) {\n  const parent = getParentNode(element);\n  return parent === null ? parents : getParents(parent, parents.concat([parent]));\n}\n\n// src/dom/methods/getPreviousElementSibling.ts\nfunction getPreviousElementSibling(element, selector) {\n  let previousElement = element.previousElementSibling;\n  while (previousElement) {\n    if (previousElement.matches(selector)) {\n      return previousElement;\n    } else {\n      previousElement = previousElement.previousElementSibling;\n    }\n  }\n  return null;\n}\n\n// src/dom/methods/getScrollableParents.ts\nfunction getScrollableParents(element) {\n  let scrollableParents = [];\n  if (element) {\n    let parents = getParents(element);\n    const overflowRegex = /(auto|scroll)/;\n    const overflowCheck = node => {\n      try {\n        let styleDeclaration = window[\"getComputedStyle\"](node, null);\n        return overflowRegex.test(styleDeclaration.getPropertyValue(\"overflow\")) || overflowRegex.test(styleDeclaration.getPropertyValue(\"overflowX\")) || overflowRegex.test(styleDeclaration.getPropertyValue(\"overflowY\"));\n      } catch (err) {\n        return false;\n      }\n    };\n    for (let parent of parents) {\n      let scrollSelectors = parent.nodeType === 1 && parent.dataset[\"scrollselectors\"];\n      if (scrollSelectors) {\n        let selectors = scrollSelectors.split(\",\");\n        for (let selector of selectors) {\n          let el = findSingle(parent, selector);\n          if (el && overflowCheck(el)) {\n            scrollableParents.push(el);\n          }\n        }\n      }\n      if (parent.nodeType !== 9 && overflowCheck(parent)) {\n        scrollableParents.push(parent);\n      }\n    }\n  }\n  return scrollableParents;\n}\n\n// src/dom/methods/getSelection.ts\nfunction getSelection() {\n  if (window.getSelection) return window.getSelection().toString();else if (document.getSelection) return document.getSelection().toString();\n  return void 0;\n}\n\n// src/dom/methods/isExist.ts\nfunction isExist(element) {\n  return !!(element !== null && typeof element !== \"undefined\" && element.nodeName && getParentNode(element));\n}\n\n// src/dom/methods/getTargetElement.ts\nfunction getTargetElement(target, currentElement) {\n  var _a;\n  if (!target) return void 0;\n  switch (target) {\n    case \"document\":\n      return document;\n    case \"window\":\n      return window;\n    case \"body\":\n      return document.body;\n    case \"@next\":\n      return currentElement == null ? void 0 : currentElement.nextElementSibling;\n    case \"@prev\":\n      return currentElement == null ? void 0 : currentElement.previousElementSibling;\n    case \"@parent\":\n      return currentElement == null ? void 0 : currentElement.parentElement;\n    case \"@grandparent\":\n      return (_a = currentElement == null ? void 0 : currentElement.parentElement) == null ? void 0 : _a.parentElement;\n    default:\n      if (typeof target === \"string\") {\n        return document.querySelector(target);\n      }\n      const isFunction = obj => !!(obj && obj.constructor && obj.call && obj.apply);\n      const element = toElement(isFunction(target) ? target() : target);\n      return (element == null ? void 0 : element.nodeType) === 9 || isExist(element) ? element : void 0;\n  }\n}\n\n// src/dom/methods/getUserAgent.ts\nfunction getUserAgent() {\n  return navigator.userAgent;\n}\n\n// src/dom/methods/getWidth.ts\nfunction getWidth(element) {\n  if (element) {\n    let width = element.offsetWidth;\n    let style = getComputedStyle(element);\n    width -= parseFloat(style.paddingLeft) + parseFloat(style.paddingRight) + parseFloat(style.borderLeftWidth) + parseFloat(style.borderRightWidth);\n    return width;\n  }\n  return 0;\n}\n\n// src/dom/methods/hasCSSAnimation.ts\nfunction hasCSSAnimation(element) {\n  if (element) {\n    const style = getComputedStyle(element);\n    const animationDuration = parseFloat(style.getPropertyValue(\"animation-duration\") || \"0\");\n    return animationDuration > 0;\n  }\n  return false;\n}\n\n// src/dom/methods/hasCSSTransition.ts\nfunction hasCSSTransition(element) {\n  if (element) {\n    const style = getComputedStyle(element);\n    const transitionDuration = parseFloat(style.getPropertyValue(\"transition-duration\") || \"0\");\n    return transitionDuration > 0;\n  }\n  return false;\n}\n\n// src/dom/methods/invokeElementMethod.ts\nfunction invokeElementMethod(element, methodName, args) {\n  element[methodName].apply(element, args);\n}\n\n// src/dom/methods/isAndroid.ts\nfunction isAndroid() {\n  return /(android)/i.test(navigator.userAgent);\n}\n\n// src/dom/methods/isAttributeEquals.ts\nfunction isAttributeEquals(element, name, value) {\n  return isElement(element) ? getAttribute(element, name) === value : false;\n}\n\n// src/dom/methods/isAttributeNotEquals.ts\nfunction isAttributeNotEquals(element, name, value) {\n  return !isAttributeEquals(element, name, value);\n}\n\n// src/dom/methods/isClickable.ts\nfunction isClickable(element) {\n  if (element) {\n    const targetNode = element.nodeName;\n    const parentNode = element.parentElement && element.parentElement.nodeName;\n    return targetNode === \"INPUT\" || targetNode === \"TEXTAREA\" || targetNode === \"BUTTON\" || targetNode === \"A\" || parentNode === \"INPUT\" || parentNode === \"TEXTAREA\" || parentNode === \"BUTTON\" || parentNode === \"A\" || !!element.closest(\".p-button, .p-checkbox, .p-radiobutton\");\n  }\n  return false;\n}\n\n// src/dom/methods/isClient.ts\nfunction isClient() {\n  return !!(typeof window !== \"undefined\" && window.document && window.document.createElement);\n}\n\n// src/dom/methods/isFocusableElement.ts\nfunction isFocusableElement(element, selector = \"\") {\n  return isElement(element) ? element.matches(`button:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n            [href][clientHeight][clientWidth]:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n            input:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n            select:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n            textarea:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n            [tabIndex]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n            [contenteditable]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector}`) : false;\n}\n\n// src/dom/methods/isVisible.ts\nfunction isVisible(element) {\n  return !!(element && element.offsetParent != null);\n}\n\n// src/dom/methods/isHidden.ts\nfunction isHidden(element) {\n  return !isVisible(element);\n}\n\n// src/dom/methods/isIOS.ts\nfunction isIOS() {\n  return /iPad|iPhone|iPod/.test(navigator.userAgent) && !window[\"MSStream\"];\n}\n\n// src/dom/methods/isRTL.ts\nfunction isRTL(element) {\n  return element ? getComputedStyle(element).direction === \"rtl\" : false;\n}\n\n// src/dom/methods/isServer.ts\nfunction isServer() {\n  return !isClient();\n}\n\n// src/dom/methods/isTouchDevice.ts\nfunction isTouchDevice() {\n  return \"ontouchstart\" in window || navigator.maxTouchPoints > 0 || navigator.msMaxTouchPoints > 0;\n}\n\n// src/dom/methods/nestedPosition.ts\nfunction nestedPosition(element, level) {\n  var _a, _b;\n  if (element) {\n    const parentItem = element.parentElement;\n    const elementOffset = getOffset(parentItem);\n    const viewport = getViewport();\n    const sublistWidth = element.offsetParent ? element.offsetWidth : getHiddenElementOuterWidth(element);\n    const sublistHeight = element.offsetParent ? element.offsetHeight : getHiddenElementOuterHeight(element);\n    const itemOuterWidth = getOuterWidth((_a = parentItem == null ? void 0 : parentItem.children) == null ? void 0 : _a[0]);\n    const itemOuterHeight = getOuterHeight((_b = parentItem == null ? void 0 : parentItem.children) == null ? void 0 : _b[0]);\n    let left = \"\";\n    let top = \"\";\n    if (elementOffset.left + itemOuterWidth + sublistWidth > viewport.width - calculateScrollbarWidth()) {\n      if (elementOffset.left < sublistWidth) {\n        if (level % 2 === 1) {\n          left = elementOffset.left ? \"-\" + elementOffset.left + \"px\" : \"100%\";\n        } else if (level % 2 === 0) {\n          left = viewport.width - sublistWidth - calculateScrollbarWidth() + \"px\";\n        }\n      } else {\n        left = \"-100%\";\n      }\n    } else {\n      left = \"100%\";\n    }\n    if (element.getBoundingClientRect().top + itemOuterHeight + sublistHeight > viewport.height) {\n      top = `-${sublistHeight - itemOuterHeight}px`;\n    } else {\n      top = \"0px\";\n    }\n    element.style.top = top;\n    element.style.left = left;\n  }\n}\n\n// src/dom/methods/remove.ts\nfunction remove(element) {\n  var _a;\n  if (element) {\n    if (!(\"remove\" in Element.prototype)) (_a = element.parentNode) == null ? void 0 : _a.removeChild(element);else element.remove();\n  }\n}\n\n// src/dom/methods/removeChild.ts\nfunction removeChild(element, child) {\n  const target = toElement(element);\n  if (target) target.removeChild(child);else throw new Error(\"Cannot remove \" + child + \" from \" + element);\n}\n\n// src/dom/methods/removeStyleTag.ts\nfunction removeStyleTag(element) {\n  var _a;\n  if (isExist(element)) {\n    try {\n      (_a = element.parentNode) == null ? void 0 : _a.removeChild(element);\n    } catch (error) {}\n    return null;\n  }\n  return element;\n}\n\n// src/dom/methods/scrollInView.ts\nfunction scrollInView(container, item) {\n  let borderTopValue = getComputedStyle(container).getPropertyValue(\"borderTopWidth\");\n  let borderTop = borderTopValue ? parseFloat(borderTopValue) : 0;\n  let paddingTopValue = getComputedStyle(container).getPropertyValue(\"paddingTop\");\n  let paddingTop = paddingTopValue ? parseFloat(paddingTopValue) : 0;\n  let containerRect = container.getBoundingClientRect();\n  let itemRect = item.getBoundingClientRect();\n  let offset = itemRect.top + document.body.scrollTop - (containerRect.top + document.body.scrollTop) - borderTop - paddingTop;\n  let scroll = container.scrollTop;\n  let elementHeight = container.clientHeight;\n  let itemHeight = getOuterHeight(item);\n  if (offset < 0) {\n    container.scrollTop = scroll + offset;\n  } else if (offset + itemHeight > elementHeight) {\n    container.scrollTop = scroll + offset - elementHeight + itemHeight;\n  }\n}\n\n// src/dom/methods/setAttribute.ts\nfunction setAttribute(element, attribute = \"\", value) {\n  if (isElement(element) && value !== null && value !== void 0) {\n    element.setAttribute(attribute, value);\n  }\n}\n\n// src/dom/methods/setCSSProperty.ts\nfunction setCSSProperty(element, property, value = null, priority) {\n  var _a;\n  property && ((_a = element == null ? void 0 : element.style) == null ? void 0 : _a.setProperty(property, value, priority));\n}\nexport { absolutePosition, addClass, addStyle, alignOverlay, appendChild, blockBodyScroll, calculateBodyScrollbarWidth, calculateScrollbarHeight, calculateScrollbarWidth, clearSelection, createElement, createStyleAsString, createStyleTag, exportCSV, fadeIn, fadeOut, find, findSingle, focus, getAttribute, getBrowser, getBrowserLanguage, getCSSProperty, getCSSVariableByRegex, getCursorOffset, getFirstFocusableElement, getFocusableElements, getHeight, getHiddenElementDimensions, getHiddenElementOuterHeight, getHiddenElementOuterWidth, getIndex, getInnerWidth, getLastFocusableElement, getNextElementSibling, getNextFocusableElement, getOffset, getOuterHeight, getOuterWidth, getParentNode, getParents, getPreviousElementSibling, getScrollableParents, getSelection, getTargetElement, getUserAgent, getViewport, getWidth, getWindowScrollLeft, getWindowScrollTop, hasCSSAnimation, hasCSSTransition, hasClass, invokeElementMethod, isAndroid, isAttributeEquals, isAttributeNotEquals, isClickable, isClient, isElement, isExist, isFocusableElement, isHidden, isIOS, isRTL, isServer, isTouchDevice, isVisible, nestedPosition, relativePosition, remove, removeChild, removeClass, removeStyleTag, resolveUserAgent, saveAs, scrollInView, setAttribute, setAttributes, setCSSProperty, toElement, unblockBodyScroll };", "map": {"version": 3, "names": ["hasClass", "element", "className", "classList", "contains", "RegExp", "test", "addClass", "fn", "_className", "add", "flat", "filter", "Boolean", "for<PERSON>ach", "_classNames", "split", "calculateBodyScrollbarWidth", "window", "innerWidth", "document", "documentElement", "offsetWidth", "getCSSVariableByRegex", "variableRegex", "sheet", "styleSheets", "rule", "cssRules", "property", "style", "name", "value", "getPropertyValue", "trim", "e", "blockBodyScroll", "variableData", "body", "setProperty", "saveAs", "file", "link", "createElement", "download", "src", "setAttribute", "display", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "exportCSV", "csv", "filename", "blob", "Blob", "type", "navigator", "msSaveOrOpenBlob", "isDownloaded", "URL", "createObjectURL", "open", "encodeURI", "removeClass", "remove", "replace", "join", "unblockBodyScroll", "removeProperty", "getHiddenElementDimensions", "dimensions", "width", "height", "visibility", "offsetHeight", "getViewport", "win", "d", "g", "getElementsByTagName", "w", "clientWidth", "h", "innerHeight", "clientHeight", "getWindowScrollLeft", "doc", "pageXOffset", "scrollLeft", "clientLeft", "getWindowScrollTop", "pageYOffset", "scrollTop", "clientTop", "absolutePosition", "target", "gutter", "_a", "_b", "_c", "_d", "elementDimensions", "offsetParent", "elementOuterHeight", "elementOuterWidth", "targetOuterHeight", "targetOuterWidth", "targetOffset", "getBoundingClientRect", "windowScrollTop", "windowScrollLeft", "viewport", "top", "left", "origin", "Math", "max", "transform<PERSON><PERSON>in", "marginTop", "addStyle", "cssText", "Object", "entries", "key", "getOuterWidth", "margin", "HTMLElement", "getComputedStyle", "parseFloat", "marginLeft", "marginRight", "relativePosition", "targetHeight", "alignOverlay", "overlay", "appendTo", "calculateMinWidth", "min<PERSON><PERSON><PERSON>", "isElement", "nodeType", "nodeName", "toElement", "hasOwnProperty", "current", "el", "nativeElement", "child", "Error", "calculatedScrollbarHeight", "calculateScrollbarHeight", "borderTopWidth", "borderBottomWidth", "scrollDiv", "overflow", "position", "scrollbarHeight", "calculatedScrollbarWidth", "calculateScrollbarWidth", "borderLeftWidth", "borderRightWidth", "scrollbarWidth", "clearSelection", "getSelection", "selection", "empty", "removeAllRanges", "rangeCount", "getRangeAt", "getClientRects", "length", "setAttributes", "attributes", "computedStyles", "styles", "$attrs", "reduce", "cv", "v", "push", "_cv", "Array", "isArray", "map", "_k", "_v", "toLowerCase", "concat", "c", "matchedEvent", "match", "addEventListener", "Set", "children", "append", "createStyleAsString", "css", "options", "s", "k", "createStyleTag", "container", "head", "fadeIn", "duration", "opacity", "last", "Date", "tick", "getTime", "requestAnimationFrame", "setTimeout", "fadeOut", "interval", "gap", "fading", "setInterval", "clearInterval", "toString", "find", "selector", "from", "querySelectorAll", "findSingle", "matches", "querySelector", "focus", "activeElement", "getAttribute", "isNaN", "resolveUserAgent", "ua", "userAgent", "exec", "indexOf", "browser", "version", "<PERSON><PERSON><PERSON><PERSON>", "matched", "getBrowserLanguage", "languages", "language", "getCSSProperty", "inline", "getCursorOffset", "prevText", "nextText", "currentText", "ghostDiv", "pointerEvents", "padding", "border", "overflowWrap", "whiteSpace", "lineHeight", "innerHTML", "ghostSpan", "textContent", "text", "createTextNode", "offsetLeft", "offsetTop", "abs", "getFocusableElements", "focusableElements", "visibleFocusableElements", "focusableElement", "getFirstFocusableElement", "getHeight", "paddingTop", "paddingBottom", "getHiddenElementOuterHeight", "elementHeight", "getHiddenElementOuterWidth", "elementWidth", "getParentNode", "parent", "parentNode", "ShadowRoot", "host", "getIndex", "childNodes", "num", "i", "getInnerWidth", "borderLeft", "borderRight", "getLastFocusableElement", "getNextElementSibling", "nextElement", "nextElement<PERSON><PERSON>ling", "getNextFocusableElement", "index", "findIndex", "nextIndex", "getOffset", "rect", "getOuterHeight", "marginBottom", "getParents", "parents", "getPreviousElementSibling", "previousElement", "previousElementSibling", "getScrollableParents", "scrollableParents", "overflowRegex", "overflowCheck", "node", "styleDeclaration", "err", "scrollSelectors", "dataset", "selectors", "isExist", "getTargetElement", "currentElement", "parentElement", "isFunction", "obj", "constructor", "call", "apply", "getUserAgent", "getWidth", "paddingLeft", "paddingRight", "hasCSSAnimation", "animationDuration", "hasCSSTransition", "transitionDuration", "invokeElementMethod", "methodName", "args", "isAndroid", "isAttributeEquals", "isAttributeNotEquals", "isClickable", "targetNode", "closest", "isClient", "isFocusableElement", "isVisible", "isHidden", "isIOS", "isRTL", "direction", "isServer", "isTouchDevice", "maxTouchPoints", "msMaxTouchPoints", "nestedPosition", "level", "parentItem", "elementOffset", "sublist<PERSON><PERSON><PERSON>", "sublistHeight", "itemOuterWidth", "itemOuterHeight", "Element", "prototype", "removeStyleTag", "error", "scrollInView", "item", "borderTopValue", "borderTop", "paddingTopValue", "containerRect", "itemRect", "offset", "scroll", "itemHeight", "attribute", "setCSSProperty", "priority"], "sources": ["C:/projetos/scope-ai/frontend/node_modules/@primeuix/utils/dom/index.mjs"], "sourcesContent": ["// src/dom/methods/hasClass.ts\nfunction hasClass(element, className) {\n  if (element) {\n    if (element.classList) return element.classList.contains(className);\n    else return new RegExp(\"(^| )\" + className + \"( |$)\", \"gi\").test(element.className);\n  }\n  return false;\n}\n\n// src/dom/methods/addClass.ts\nfunction addClass(element, className) {\n  if (element && className) {\n    const fn = (_className) => {\n      if (!hasClass(element, _className)) {\n        if (element.classList) element.classList.add(_className);\n        else element.className += \" \" + _className;\n      }\n    };\n    [className].flat().filter(Boolean).forEach((_classNames) => _classNames.split(\" \").forEach(fn));\n  }\n}\n\n// src/dom/methods/calculateBodyScrollbarWidth.ts\nfunction calculateBodyScrollbarWidth() {\n  return window.innerWidth - document.documentElement.offsetWidth;\n}\n\n// src/dom/methods/getCSSVariableByRegex.ts\nfunction getCSSVariableByRegex(variableRegex) {\n  for (const sheet of document == null ? void 0 : document.styleSheets) {\n    try {\n      for (const rule of sheet == null ? void 0 : sheet.cssRules) {\n        for (const property of rule == null ? void 0 : rule.style) {\n          if (variableRegex.test(property)) {\n            return { name: property, value: rule.style.getPropertyValue(property).trim() };\n          }\n        }\n      }\n    } catch (e) {\n    }\n  }\n  return null;\n}\n\n// src/dom/helpers/blockBodyScroll.ts\nfunction blockBodyScroll(className = \"p-overflow-hidden\") {\n  const variableData = getCSSVariableByRegex(/-scrollbar-width$/);\n  (variableData == null ? void 0 : variableData.name) && document.body.style.setProperty(variableData.name, calculateBodyScrollbarWidth() + \"px\");\n  addClass(document.body, className);\n}\n\n// src/dom/helpers/saveAs.ts\nfunction saveAs(file) {\n  if (file) {\n    let link = document.createElement(\"a\");\n    if (link.download !== void 0) {\n      const { name, src } = file;\n      link.setAttribute(\"href\", src);\n      link.setAttribute(\"download\", name);\n      link.style.display = \"none\";\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      return true;\n    }\n  }\n  return false;\n}\n\n// src/dom/helpers/exportCSV.ts\nfunction exportCSV(csv, filename) {\n  let blob = new Blob([csv], {\n    type: \"application/csv;charset=utf-8;\"\n  });\n  if (window.navigator.msSaveOrOpenBlob) {\n    navigator.msSaveOrOpenBlob(blob, filename + \".csv\");\n  } else {\n    const isDownloaded = saveAs({ name: filename + \".csv\", src: URL.createObjectURL(blob) });\n    if (!isDownloaded) {\n      csv = \"data:text/csv;charset=utf-8,\" + csv;\n      window.open(encodeURI(csv));\n    }\n  }\n}\n\n// src/dom/methods/removeClass.ts\nfunction removeClass(element, className) {\n  if (element && className) {\n    const fn = (_className) => {\n      if (element.classList) element.classList.remove(_className);\n      else element.className = element.className.replace(new RegExp(\"(^|\\\\b)\" + _className.split(\" \").join(\"|\") + \"(\\\\b|$)\", \"gi\"), \" \");\n    };\n    [className].flat().filter(Boolean).forEach((_classNames) => _classNames.split(\" \").forEach(fn));\n  }\n}\n\n// src/dom/helpers/unblockBodyScroll.ts\nfunction unblockBodyScroll(className = \"p-overflow-hidden\") {\n  const variableData = getCSSVariableByRegex(/-scrollbar-width$/);\n  (variableData == null ? void 0 : variableData.name) && document.body.style.removeProperty(variableData.name);\n  removeClass(document.body, className);\n}\n\n// src/dom/methods/getHiddenElementDimensions.ts\nfunction getHiddenElementDimensions(element) {\n  let dimensions = { width: 0, height: 0 };\n  if (element) {\n    element.style.visibility = \"hidden\";\n    element.style.display = \"block\";\n    dimensions.width = element.offsetWidth;\n    dimensions.height = element.offsetHeight;\n    element.style.display = \"none\";\n    element.style.visibility = \"visible\";\n  }\n  return dimensions;\n}\n\n// src/dom/methods/getViewport.ts\nfunction getViewport() {\n  let win = window, d = document, e = d.documentElement, g = d.getElementsByTagName(\"body\")[0], w = win.innerWidth || e.clientWidth || g.clientWidth, h = win.innerHeight || e.clientHeight || g.clientHeight;\n  return { width: w, height: h };\n}\n\n// src/dom/methods/getWindowScrollLeft.ts\nfunction getWindowScrollLeft() {\n  let doc = document.documentElement;\n  return (window.pageXOffset || doc.scrollLeft) - (doc.clientLeft || 0);\n}\n\n// src/dom/methods/getWindowScrollTop.ts\nfunction getWindowScrollTop() {\n  let doc = document.documentElement;\n  return (window.pageYOffset || doc.scrollTop) - (doc.clientTop || 0);\n}\n\n// src/dom/methods/absolutePosition.ts\nfunction absolutePosition(element, target, gutter = true) {\n  var _a, _b, _c, _d;\n  if (element) {\n    const elementDimensions = element.offsetParent ? { width: element.offsetWidth, height: element.offsetHeight } : getHiddenElementDimensions(element);\n    const elementOuterHeight = elementDimensions.height;\n    const elementOuterWidth = elementDimensions.width;\n    const targetOuterHeight = target.offsetHeight;\n    const targetOuterWidth = target.offsetWidth;\n    const targetOffset = target.getBoundingClientRect();\n    const windowScrollTop = getWindowScrollTop();\n    const windowScrollLeft = getWindowScrollLeft();\n    const viewport = getViewport();\n    let top, left, origin = \"top\";\n    if (targetOffset.top + targetOuterHeight + elementOuterHeight > viewport.height) {\n      top = targetOffset.top + windowScrollTop - elementOuterHeight;\n      origin = \"bottom\";\n      if (top < 0) {\n        top = windowScrollTop;\n      }\n    } else {\n      top = targetOuterHeight + targetOffset.top + windowScrollTop;\n    }\n    if (targetOffset.left + elementOuterWidth > viewport.width) left = Math.max(0, targetOffset.left + windowScrollLeft + targetOuterWidth - elementOuterWidth);\n    else left = targetOffset.left + windowScrollLeft;\n    element.style.top = top + \"px\";\n    element.style.left = left + \"px\";\n    element.style.transformOrigin = origin;\n    gutter && (element.style.marginTop = origin === \"bottom\" ? `calc(${(_b = (_a = getCSSVariableByRegex(/-anchor-gutter$/)) == null ? void 0 : _a.value) != null ? _b : \"2px\"} * -1)` : (_d = (_c = getCSSVariableByRegex(/-anchor-gutter$/)) == null ? void 0 : _c.value) != null ? _d : \"\");\n  }\n}\n\n// src/dom/methods/addStyle.ts\nfunction addStyle(element, style) {\n  if (element) {\n    if (typeof style === \"string\") {\n      element.style.cssText = style;\n    } else {\n      Object.entries(style || {}).forEach(([key, value]) => element.style[key] = value);\n    }\n  }\n}\n\n// src/dom/methods/getOuterWidth.ts\nfunction getOuterWidth(element, margin) {\n  if (element instanceof HTMLElement) {\n    let width = element.offsetWidth;\n    if (margin) {\n      let style = getComputedStyle(element);\n      width += parseFloat(style.marginLeft) + parseFloat(style.marginRight);\n    }\n    return width;\n  }\n  return 0;\n}\n\n// src/dom/methods/relativePosition.ts\nfunction relativePosition(element, target, gutter = true) {\n  var _a, _b, _c, _d;\n  if (element) {\n    const elementDimensions = element.offsetParent ? { width: element.offsetWidth, height: element.offsetHeight } : getHiddenElementDimensions(element);\n    const targetHeight = target.offsetHeight;\n    const targetOffset = target.getBoundingClientRect();\n    const viewport = getViewport();\n    let top, left, origin = \"top\";\n    if (targetOffset.top + targetHeight + elementDimensions.height > viewport.height) {\n      top = -1 * elementDimensions.height;\n      origin = \"bottom\";\n      if (targetOffset.top + top < 0) {\n        top = -1 * targetOffset.top;\n      }\n    } else {\n      top = targetHeight;\n    }\n    if (elementDimensions.width > viewport.width) {\n      left = targetOffset.left * -1;\n    } else if (targetOffset.left + elementDimensions.width > viewport.width) {\n      left = (targetOffset.left + elementDimensions.width - viewport.width) * -1;\n    } else {\n      left = 0;\n    }\n    element.style.top = top + \"px\";\n    element.style.left = left + \"px\";\n    element.style.transformOrigin = origin;\n    gutter && (element.style.marginTop = origin === \"bottom\" ? `calc(${(_b = (_a = getCSSVariableByRegex(/-anchor-gutter$/)) == null ? void 0 : _a.value) != null ? _b : \"2px\"} * -1)` : (_d = (_c = getCSSVariableByRegex(/-anchor-gutter$/)) == null ? void 0 : _c.value) != null ? _d : \"\");\n  }\n}\n\n// src/dom/methods/alignOverlay.ts\nfunction alignOverlay(overlay, target, appendTo, calculateMinWidth = true) {\n  if (overlay && target) {\n    if (appendTo === \"self\") {\n      relativePosition(overlay, target);\n    } else {\n      calculateMinWidth && (overlay.style.minWidth = getOuterWidth(target) + \"px\");\n      absolutePosition(overlay, target);\n    }\n  }\n}\n\n// src/dom/methods/isElement.ts\nfunction isElement(element) {\n  return typeof HTMLElement === \"object\" ? element instanceof HTMLElement : element && typeof element === \"object\" && element !== null && element.nodeType === 1 && typeof element.nodeName === \"string\";\n}\n\n// src/dom/methods/toElement.ts\nfunction toElement(element) {\n  let target = element;\n  if (element && typeof element === \"object\") {\n    if (element.hasOwnProperty(\"current\")) {\n      target = element.current;\n    } else if (element.hasOwnProperty(\"el\")) {\n      if (element.el.hasOwnProperty(\"nativeElement\")) {\n        target = element.el.nativeElement;\n      } else {\n        target = element.el;\n      }\n    }\n  }\n  return isElement(target) ? target : void 0;\n}\n\n// src/dom/methods/appendChild.ts\nfunction appendChild(element, child) {\n  const target = toElement(element);\n  if (target) target.appendChild(child);\n  else throw new Error(\"Cannot append \" + child + \" to \" + element);\n}\n\n// src/dom/methods/calculateScrollbarHeight.ts\nvar calculatedScrollbarHeight = void 0;\nfunction calculateScrollbarHeight(element) {\n  if (element) {\n    let style = getComputedStyle(element);\n    return element.offsetHeight - element.clientHeight - parseFloat(style.borderTopWidth) - parseFloat(style.borderBottomWidth);\n  } else {\n    if (calculatedScrollbarHeight != null) return calculatedScrollbarHeight;\n    let scrollDiv = document.createElement(\"div\");\n    addStyle(scrollDiv, {\n      width: \"100px\",\n      height: \"100px\",\n      overflow: \"scroll\",\n      position: \"absolute\",\n      top: \"-9999px\"\n    });\n    document.body.appendChild(scrollDiv);\n    let scrollbarHeight = scrollDiv.offsetHeight - scrollDiv.clientHeight;\n    document.body.removeChild(scrollDiv);\n    calculatedScrollbarHeight = scrollbarHeight;\n    return scrollbarHeight;\n  }\n}\n\n// src/dom/methods/calculateScrollbarWidth.ts\nvar calculatedScrollbarWidth = void 0;\nfunction calculateScrollbarWidth(element) {\n  if (element) {\n    let style = getComputedStyle(element);\n    return element.offsetWidth - element.clientWidth - parseFloat(style.borderLeftWidth) - parseFloat(style.borderRightWidth);\n  } else {\n    if (calculatedScrollbarWidth != null) return calculatedScrollbarWidth;\n    let scrollDiv = document.createElement(\"div\");\n    addStyle(scrollDiv, {\n      width: \"100px\",\n      height: \"100px\",\n      overflow: \"scroll\",\n      position: \"absolute\",\n      top: \"-9999px\"\n    });\n    document.body.appendChild(scrollDiv);\n    let scrollbarWidth = scrollDiv.offsetWidth - scrollDiv.clientWidth;\n    document.body.removeChild(scrollDiv);\n    calculatedScrollbarWidth = scrollbarWidth;\n    return scrollbarWidth;\n  }\n}\n\n// src/dom/methods/clearSelection.ts\nfunction clearSelection() {\n  if (window.getSelection) {\n    const selection = window.getSelection() || {};\n    if (selection.empty) {\n      selection.empty();\n    } else if (selection.removeAllRanges && selection.rangeCount > 0 && selection.getRangeAt(0).getClientRects().length > 0) {\n      selection.removeAllRanges();\n    }\n  }\n}\n\n// src/dom/methods/setAttributes.ts\nfunction setAttributes(element, attributes = {}) {\n  if (isElement(element)) {\n    const computedStyles = (rule, value) => {\n      var _a, _b;\n      const styles = ((_a = element == null ? void 0 : element.$attrs) == null ? void 0 : _a[rule]) ? [(_b = element == null ? void 0 : element.$attrs) == null ? void 0 : _b[rule]] : [];\n      return [value].flat().reduce((cv, v) => {\n        if (v !== null && v !== void 0) {\n          const type = typeof v;\n          if (type === \"string\" || type === \"number\") {\n            cv.push(v);\n          } else if (type === \"object\") {\n            const _cv = Array.isArray(v) ? computedStyles(rule, v) : Object.entries(v).map(([_k, _v]) => rule === \"style\" && (!!_v || _v === 0) ? `${_k.replace(/([a-z])([A-Z])/g, \"$1-$2\").toLowerCase()}:${_v}` : !!_v ? _k : void 0);\n            cv = _cv.length ? cv.concat(_cv.filter((c) => !!c)) : cv;\n          }\n        }\n        return cv;\n      }, styles);\n    };\n    Object.entries(attributes).forEach(([key, value]) => {\n      if (value !== void 0 && value !== null) {\n        const matchedEvent = key.match(/^on(.+)/);\n        if (matchedEvent) {\n          element.addEventListener(matchedEvent[1].toLowerCase(), value);\n        } else if (key === \"p-bind\" || key === \"pBind\") {\n          setAttributes(element, value);\n        } else {\n          value = key === \"class\" ? [...new Set(computedStyles(\"class\", value))].join(\" \").trim() : key === \"style\" ? computedStyles(\"style\", value).join(\";\").trim() : value;\n          (element.$attrs = element.$attrs || {}) && (element.$attrs[key] = value);\n          element.setAttribute(key, value);\n        }\n      }\n    });\n  }\n}\n\n// src/dom/methods/createElement.ts\nfunction createElement(type, attributes = {}, ...children) {\n  if (type) {\n    const element = document.createElement(type);\n    setAttributes(element, attributes);\n    element.append(...children);\n    return element;\n  }\n  return void 0;\n}\n\n// src/dom/methods/createStyleAsString.ts\nfunction createStyleAsString(css, options = {}) {\n  return css ? `'<style ${Object.entries(options).reduce((s, [k, v]) => s + `${k}=\"${v}\"`, \" \")}>${css}</style>'` : \"\";\n}\n\n// src/dom/methods/createStyleTag.ts\nfunction createStyleTag(attributes = {}, container) {\n  let element = document.createElement(\"style\");\n  setAttributes(element, attributes);\n  if (!container) {\n    container = document.head;\n  }\n  container.appendChild(element);\n  return element;\n}\n\n// src/dom/methods/fadeIn.ts\nfunction fadeIn(element, duration) {\n  if (element) {\n    element.style.opacity = \"0\";\n    let last = +/* @__PURE__ */ new Date();\n    let opacity = \"0\";\n    let tick = function() {\n      opacity = `${+element.style.opacity + ((/* @__PURE__ */ new Date()).getTime() - last) / duration}`;\n      element.style.opacity = opacity;\n      last = +/* @__PURE__ */ new Date();\n      if (+opacity < 1) {\n        !!window.requestAnimationFrame && requestAnimationFrame(tick) || setTimeout(tick, 16);\n      }\n    };\n    tick();\n  }\n}\n\n// src/dom/methods/fadeOut.ts\nfunction fadeOut(element, duration) {\n  if (element) {\n    let opacity = 1, interval = 50, gap = interval / duration;\n    let fading = setInterval(() => {\n      opacity -= gap;\n      if (opacity <= 0) {\n        opacity = 0;\n        clearInterval(fading);\n      }\n      element.style.opacity = opacity.toString();\n    }, interval);\n  }\n}\n\n// src/dom/methods/find.ts\nfunction find(element, selector) {\n  return isElement(element) ? Array.from(element.querySelectorAll(selector)) : [];\n}\n\n// src/dom/methods/findSingle.ts\nfunction findSingle(element, selector) {\n  return isElement(element) ? element.matches(selector) ? element : element.querySelector(selector) : null;\n}\n\n// src/dom/methods/focus.ts\nfunction focus(element, options) {\n  element && document.activeElement !== element && element.focus(options);\n}\n\n// src/dom/methods/getAttribute.ts\nfunction getAttribute(element, name) {\n  if (isElement(element)) {\n    const value = element.getAttribute(name);\n    if (!isNaN(value)) {\n      return +value;\n    }\n    if (value === \"true\" || value === \"false\") {\n      return value === \"true\";\n    }\n    return value;\n  }\n  return void 0;\n}\n\n// src/dom/methods/resolveUserAgent.ts\nfunction resolveUserAgent() {\n  let ua = navigator.userAgent.toLowerCase();\n  let match = /(chrome)[ ]([\\w.]+)/.exec(ua) || /(webkit)[ ]([\\w.]+)/.exec(ua) || /(opera)(?:.*version|)[ ]([\\w.]+)/.exec(ua) || /(msie) ([\\w.]+)/.exec(ua) || ua.indexOf(\"compatible\") < 0 && /(mozilla)(?:.*? rv:([\\w.]+)|)/.exec(ua) || [];\n  return {\n    browser: match[1] || \"\",\n    version: match[2] || \"0\"\n  };\n}\n\n// src/dom/methods/getBrowser.ts\nvar browser = null;\nfunction getBrowser() {\n  if (!browser) {\n    browser = {};\n    let matched = resolveUserAgent();\n    if (matched.browser) {\n      browser[matched.browser] = true;\n      browser[\"version\"] = matched.version;\n    }\n    if (browser[\"chrome\"]) {\n      browser[\"webkit\"] = true;\n    } else if (browser[\"webkit\"]) {\n      browser[\"safari\"] = true;\n    }\n  }\n  return browser;\n}\n\n// src/dom/methods/getBrowserLanguage.ts\nfunction getBrowserLanguage() {\n  return navigator.languages && navigator.languages.length && navigator.languages[0] || navigator.language || \"en\";\n}\n\n// src/dom/methods/getCSSProperty.ts\nfunction getCSSProperty(element, property, inline) {\n  var _a;\n  if (element && property) {\n    return inline ? (_a = element == null ? void 0 : element.style) == null ? void 0 : _a.getPropertyValue(property) : getComputedStyle(element).getPropertyValue(property);\n  }\n  return null;\n}\n\n// src/dom/methods/getCursorOffset.ts\nfunction getCursorOffset(element, prevText, nextText, currentText) {\n  if (element) {\n    let style = getComputedStyle(element);\n    let ghostDiv = document.createElement(\"div\");\n    ghostDiv.style.position = \"absolute\";\n    ghostDiv.style.top = \"0px\";\n    ghostDiv.style.left = \"0px\";\n    ghostDiv.style.visibility = \"hidden\";\n    ghostDiv.style.pointerEvents = \"none\";\n    ghostDiv.style.overflow = style.overflow;\n    ghostDiv.style.width = style.width;\n    ghostDiv.style.height = style.height;\n    ghostDiv.style.padding = style.padding;\n    ghostDiv.style.border = style.border;\n    ghostDiv.style.overflowWrap = style.overflowWrap;\n    ghostDiv.style.whiteSpace = style.whiteSpace;\n    ghostDiv.style.lineHeight = style.lineHeight;\n    ghostDiv.innerHTML = prevText.replace(/\\r\\n|\\r|\\n/g, \"<br />\");\n    let ghostSpan = document.createElement(\"span\");\n    ghostSpan.textContent = currentText;\n    ghostDiv.appendChild(ghostSpan);\n    let text = document.createTextNode(nextText);\n    ghostDiv.appendChild(text);\n    document.body.appendChild(ghostDiv);\n    const { offsetLeft, offsetTop, clientHeight } = ghostSpan;\n    document.body.removeChild(ghostDiv);\n    return {\n      left: Math.abs(offsetLeft - element.scrollLeft),\n      top: Math.abs(offsetTop - element.scrollTop) + clientHeight\n    };\n  }\n  return {\n    top: \"auto\",\n    left: \"auto\"\n  };\n}\n\n// src/dom/methods/getFocusableElements.ts\nfunction getFocusableElements(element, selector = \"\") {\n  let focusableElements = find(\n    element,\n    `button:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n            [href][clientHeight][clientWidth]:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n            input:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n            select:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n            textarea:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n            [tabIndex]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n            [contenteditable]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector}`\n  );\n  let visibleFocusableElements = [];\n  for (let focusableElement of focusableElements) {\n    if (getComputedStyle(focusableElement).display != \"none\" && getComputedStyle(focusableElement).visibility != \"hidden\") visibleFocusableElements.push(focusableElement);\n  }\n  return visibleFocusableElements;\n}\n\n// src/dom/methods/getFirstFocusableElement.ts\nfunction getFirstFocusableElement(element, selector) {\n  const focusableElements = getFocusableElements(element, selector);\n  return focusableElements.length > 0 ? focusableElements[0] : null;\n}\n\n// src/dom/methods/getHeight.ts\nfunction getHeight(element) {\n  if (element) {\n    let height = element.offsetHeight;\n    let style = getComputedStyle(element);\n    height -= parseFloat(style.paddingTop) + parseFloat(style.paddingBottom) + parseFloat(style.borderTopWidth) + parseFloat(style.borderBottomWidth);\n    return height;\n  }\n  return 0;\n}\n\n// src/dom/methods/getHiddenElementOuterHeight.ts\nfunction getHiddenElementOuterHeight(element) {\n  if (element) {\n    element.style.visibility = \"hidden\";\n    element.style.display = \"block\";\n    let elementHeight = element.offsetHeight;\n    element.style.display = \"none\";\n    element.style.visibility = \"visible\";\n    return elementHeight;\n  }\n  return 0;\n}\n\n// src/dom/methods/getHiddenElementOuterWidth.ts\nfunction getHiddenElementOuterWidth(element) {\n  if (element) {\n    element.style.visibility = \"hidden\";\n    element.style.display = \"block\";\n    let elementWidth = element.offsetWidth;\n    element.style.display = \"none\";\n    element.style.visibility = \"visible\";\n    return elementWidth;\n  }\n  return 0;\n}\n\n// src/dom/methods/getParentNode.ts\nfunction getParentNode(element) {\n  if (element) {\n    let parent = element.parentNode;\n    if (parent && parent instanceof ShadowRoot && parent.host) {\n      parent = parent.host;\n    }\n    return parent;\n  }\n  return null;\n}\n\n// src/dom/methods/getIndex.ts\nfunction getIndex(element) {\n  var _a;\n  if (element) {\n    let children = (_a = getParentNode(element)) == null ? void 0 : _a.childNodes;\n    let num = 0;\n    if (children) {\n      for (let i = 0; i < children.length; i++) {\n        if (children[i] === element) return num;\n        if (children[i].nodeType === 1) num++;\n      }\n    }\n  }\n  return -1;\n}\n\n// src/dom/methods/getInnerWidth.ts\nfunction getInnerWidth(element) {\n  if (element) {\n    let width = element.offsetWidth;\n    let style = getComputedStyle(element);\n    width -= parseFloat(style.borderLeft) + parseFloat(style.borderRight);\n    return width;\n  }\n  return 0;\n}\n\n// src/dom/methods/getLastFocusableElement.ts\nfunction getLastFocusableElement(element, selector) {\n  const focusableElements = getFocusableElements(element, selector);\n  return focusableElements.length > 0 ? focusableElements[focusableElements.length - 1] : null;\n}\n\n// src/dom/methods/getNextElementSibling.ts\nfunction getNextElementSibling(element, selector) {\n  let nextElement = element.nextElementSibling;\n  while (nextElement) {\n    if (nextElement.matches(selector)) {\n      return nextElement;\n    } else {\n      nextElement = nextElement.nextElementSibling;\n    }\n  }\n  return null;\n}\n\n// src/dom/methods/getNextFocusableElement.ts\nfunction getNextFocusableElement(container, element, selector) {\n  const focusableElements = getFocusableElements(container, selector);\n  const index = focusableElements.length > 0 ? focusableElements.findIndex((el) => el === element) : -1;\n  const nextIndex = index > -1 && focusableElements.length >= index + 1 ? index + 1 : -1;\n  return nextIndex > -1 ? focusableElements[nextIndex] : null;\n}\n\n// src/dom/methods/getOffset.ts\nfunction getOffset(element) {\n  if (element) {\n    let rect = element.getBoundingClientRect();\n    return {\n      top: rect.top + (window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0),\n      left: rect.left + (window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft || 0)\n    };\n  }\n  return {\n    top: \"auto\",\n    left: \"auto\"\n  };\n}\n\n// src/dom/methods/getOuterHeight.ts\nfunction getOuterHeight(element, margin) {\n  if (element) {\n    let height = element.offsetHeight;\n    if (margin) {\n      let style = getComputedStyle(element);\n      height += parseFloat(style.marginTop) + parseFloat(style.marginBottom);\n    }\n    return height;\n  }\n  return 0;\n}\n\n// src/dom/methods/getParents.ts\nfunction getParents(element, parents = []) {\n  const parent = getParentNode(element);\n  return parent === null ? parents : getParents(parent, parents.concat([parent]));\n}\n\n// src/dom/methods/getPreviousElementSibling.ts\nfunction getPreviousElementSibling(element, selector) {\n  let previousElement = element.previousElementSibling;\n  while (previousElement) {\n    if (previousElement.matches(selector)) {\n      return previousElement;\n    } else {\n      previousElement = previousElement.previousElementSibling;\n    }\n  }\n  return null;\n}\n\n// src/dom/methods/getScrollableParents.ts\nfunction getScrollableParents(element) {\n  let scrollableParents = [];\n  if (element) {\n    let parents = getParents(element);\n    const overflowRegex = /(auto|scroll)/;\n    const overflowCheck = (node) => {\n      try {\n        let styleDeclaration = window[\"getComputedStyle\"](node, null);\n        return overflowRegex.test(styleDeclaration.getPropertyValue(\"overflow\")) || overflowRegex.test(styleDeclaration.getPropertyValue(\"overflowX\")) || overflowRegex.test(styleDeclaration.getPropertyValue(\"overflowY\"));\n      } catch (err) {\n        return false;\n      }\n    };\n    for (let parent of parents) {\n      let scrollSelectors = parent.nodeType === 1 && parent.dataset[\"scrollselectors\"];\n      if (scrollSelectors) {\n        let selectors = scrollSelectors.split(\",\");\n        for (let selector of selectors) {\n          let el = findSingle(parent, selector);\n          if (el && overflowCheck(el)) {\n            scrollableParents.push(el);\n          }\n        }\n      }\n      if (parent.nodeType !== 9 && overflowCheck(parent)) {\n        scrollableParents.push(parent);\n      }\n    }\n  }\n  return scrollableParents;\n}\n\n// src/dom/methods/getSelection.ts\nfunction getSelection() {\n  if (window.getSelection) return window.getSelection().toString();\n  else if (document.getSelection) return document.getSelection().toString();\n  return void 0;\n}\n\n// src/dom/methods/isExist.ts\nfunction isExist(element) {\n  return !!(element !== null && typeof element !== \"undefined\" && element.nodeName && getParentNode(element));\n}\n\n// src/dom/methods/getTargetElement.ts\nfunction getTargetElement(target, currentElement) {\n  var _a;\n  if (!target) return void 0;\n  switch (target) {\n    case \"document\":\n      return document;\n    case \"window\":\n      return window;\n    case \"body\":\n      return document.body;\n    case \"@next\":\n      return currentElement == null ? void 0 : currentElement.nextElementSibling;\n    case \"@prev\":\n      return currentElement == null ? void 0 : currentElement.previousElementSibling;\n    case \"@parent\":\n      return currentElement == null ? void 0 : currentElement.parentElement;\n    case \"@grandparent\":\n      return (_a = currentElement == null ? void 0 : currentElement.parentElement) == null ? void 0 : _a.parentElement;\n    default:\n      if (typeof target === \"string\") {\n        return document.querySelector(target);\n      }\n      const isFunction = (obj) => !!(obj && obj.constructor && obj.call && obj.apply);\n      const element = toElement(isFunction(target) ? target() : target);\n      return (element == null ? void 0 : element.nodeType) === 9 || isExist(element) ? element : void 0;\n  }\n}\n\n// src/dom/methods/getUserAgent.ts\nfunction getUserAgent() {\n  return navigator.userAgent;\n}\n\n// src/dom/methods/getWidth.ts\nfunction getWidth(element) {\n  if (element) {\n    let width = element.offsetWidth;\n    let style = getComputedStyle(element);\n    width -= parseFloat(style.paddingLeft) + parseFloat(style.paddingRight) + parseFloat(style.borderLeftWidth) + parseFloat(style.borderRightWidth);\n    return width;\n  }\n  return 0;\n}\n\n// src/dom/methods/hasCSSAnimation.ts\nfunction hasCSSAnimation(element) {\n  if (element) {\n    const style = getComputedStyle(element);\n    const animationDuration = parseFloat(style.getPropertyValue(\"animation-duration\") || \"0\");\n    return animationDuration > 0;\n  }\n  return false;\n}\n\n// src/dom/methods/hasCSSTransition.ts\nfunction hasCSSTransition(element) {\n  if (element) {\n    const style = getComputedStyle(element);\n    const transitionDuration = parseFloat(style.getPropertyValue(\"transition-duration\") || \"0\");\n    return transitionDuration > 0;\n  }\n  return false;\n}\n\n// src/dom/methods/invokeElementMethod.ts\nfunction invokeElementMethod(element, methodName, args) {\n  element[methodName].apply(element, args);\n}\n\n// src/dom/methods/isAndroid.ts\nfunction isAndroid() {\n  return /(android)/i.test(navigator.userAgent);\n}\n\n// src/dom/methods/isAttributeEquals.ts\nfunction isAttributeEquals(element, name, value) {\n  return isElement(element) ? getAttribute(element, name) === value : false;\n}\n\n// src/dom/methods/isAttributeNotEquals.ts\nfunction isAttributeNotEquals(element, name, value) {\n  return !isAttributeEquals(element, name, value);\n}\n\n// src/dom/methods/isClickable.ts\nfunction isClickable(element) {\n  if (element) {\n    const targetNode = element.nodeName;\n    const parentNode = element.parentElement && element.parentElement.nodeName;\n    return targetNode === \"INPUT\" || targetNode === \"TEXTAREA\" || targetNode === \"BUTTON\" || targetNode === \"A\" || parentNode === \"INPUT\" || parentNode === \"TEXTAREA\" || parentNode === \"BUTTON\" || parentNode === \"A\" || !!element.closest(\".p-button, .p-checkbox, .p-radiobutton\");\n  }\n  return false;\n}\n\n// src/dom/methods/isClient.ts\nfunction isClient() {\n  return !!(typeof window !== \"undefined\" && window.document && window.document.createElement);\n}\n\n// src/dom/methods/isFocusableElement.ts\nfunction isFocusableElement(element, selector = \"\") {\n  return isElement(element) ? element.matches(`button:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n            [href][clientHeight][clientWidth]:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n            input:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n            select:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n            textarea:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n            [tabIndex]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n            [contenteditable]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector}`) : false;\n}\n\n// src/dom/methods/isVisible.ts\nfunction isVisible(element) {\n  return !!(element && element.offsetParent != null);\n}\n\n// src/dom/methods/isHidden.ts\nfunction isHidden(element) {\n  return !isVisible(element);\n}\n\n// src/dom/methods/isIOS.ts\nfunction isIOS() {\n  return /iPad|iPhone|iPod/.test(navigator.userAgent) && !window[\"MSStream\"];\n}\n\n// src/dom/methods/isRTL.ts\nfunction isRTL(element) {\n  return element ? getComputedStyle(element).direction === \"rtl\" : false;\n}\n\n// src/dom/methods/isServer.ts\nfunction isServer() {\n  return !isClient();\n}\n\n// src/dom/methods/isTouchDevice.ts\nfunction isTouchDevice() {\n  return \"ontouchstart\" in window || navigator.maxTouchPoints > 0 || navigator.msMaxTouchPoints > 0;\n}\n\n// src/dom/methods/nestedPosition.ts\nfunction nestedPosition(element, level) {\n  var _a, _b;\n  if (element) {\n    const parentItem = element.parentElement;\n    const elementOffset = getOffset(parentItem);\n    const viewport = getViewport();\n    const sublistWidth = element.offsetParent ? element.offsetWidth : getHiddenElementOuterWidth(element);\n    const sublistHeight = element.offsetParent ? element.offsetHeight : getHiddenElementOuterHeight(element);\n    const itemOuterWidth = getOuterWidth((_a = parentItem == null ? void 0 : parentItem.children) == null ? void 0 : _a[0]);\n    const itemOuterHeight = getOuterHeight((_b = parentItem == null ? void 0 : parentItem.children) == null ? void 0 : _b[0]);\n    let left = \"\";\n    let top = \"\";\n    if (elementOffset.left + itemOuterWidth + sublistWidth > viewport.width - calculateScrollbarWidth()) {\n      if (elementOffset.left < sublistWidth) {\n        if (level % 2 === 1) {\n          left = elementOffset.left ? \"-\" + elementOffset.left + \"px\" : \"100%\";\n        } else if (level % 2 === 0) {\n          left = viewport.width - sublistWidth - calculateScrollbarWidth() + \"px\";\n        }\n      } else {\n        left = \"-100%\";\n      }\n    } else {\n      left = \"100%\";\n    }\n    if (element.getBoundingClientRect().top + itemOuterHeight + sublistHeight > viewport.height) {\n      top = `-${sublistHeight - itemOuterHeight}px`;\n    } else {\n      top = \"0px\";\n    }\n    element.style.top = top;\n    element.style.left = left;\n  }\n}\n\n// src/dom/methods/remove.ts\nfunction remove(element) {\n  var _a;\n  if (element) {\n    if (!(\"remove\" in Element.prototype)) (_a = element.parentNode) == null ? void 0 : _a.removeChild(element);\n    else element.remove();\n  }\n}\n\n// src/dom/methods/removeChild.ts\nfunction removeChild(element, child) {\n  const target = toElement(element);\n  if (target) target.removeChild(child);\n  else throw new Error(\"Cannot remove \" + child + \" from \" + element);\n}\n\n// src/dom/methods/removeStyleTag.ts\nfunction removeStyleTag(element) {\n  var _a;\n  if (isExist(element)) {\n    try {\n      (_a = element.parentNode) == null ? void 0 : _a.removeChild(element);\n    } catch (error) {\n    }\n    return null;\n  }\n  return element;\n}\n\n// src/dom/methods/scrollInView.ts\nfunction scrollInView(container, item) {\n  let borderTopValue = getComputedStyle(container).getPropertyValue(\"borderTopWidth\");\n  let borderTop = borderTopValue ? parseFloat(borderTopValue) : 0;\n  let paddingTopValue = getComputedStyle(container).getPropertyValue(\"paddingTop\");\n  let paddingTop = paddingTopValue ? parseFloat(paddingTopValue) : 0;\n  let containerRect = container.getBoundingClientRect();\n  let itemRect = item.getBoundingClientRect();\n  let offset = itemRect.top + document.body.scrollTop - (containerRect.top + document.body.scrollTop) - borderTop - paddingTop;\n  let scroll = container.scrollTop;\n  let elementHeight = container.clientHeight;\n  let itemHeight = getOuterHeight(item);\n  if (offset < 0) {\n    container.scrollTop = scroll + offset;\n  } else if (offset + itemHeight > elementHeight) {\n    container.scrollTop = scroll + offset - elementHeight + itemHeight;\n  }\n}\n\n// src/dom/methods/setAttribute.ts\nfunction setAttribute(element, attribute = \"\", value) {\n  if (isElement(element) && value !== null && value !== void 0) {\n    element.setAttribute(attribute, value);\n  }\n}\n\n// src/dom/methods/setCSSProperty.ts\nfunction setCSSProperty(element, property, value = null, priority) {\n  var _a;\n  property && ((_a = element == null ? void 0 : element.style) == null ? void 0 : _a.setProperty(property, value, priority));\n}\nexport {\n  absolutePosition,\n  addClass,\n  addStyle,\n  alignOverlay,\n  appendChild,\n  blockBodyScroll,\n  calculateBodyScrollbarWidth,\n  calculateScrollbarHeight,\n  calculateScrollbarWidth,\n  clearSelection,\n  createElement,\n  createStyleAsString,\n  createStyleTag,\n  exportCSV,\n  fadeIn,\n  fadeOut,\n  find,\n  findSingle,\n  focus,\n  getAttribute,\n  getBrowser,\n  getBrowserLanguage,\n  getCSSProperty,\n  getCSSVariableByRegex,\n  getCursorOffset,\n  getFirstFocusableElement,\n  getFocusableElements,\n  getHeight,\n  getHiddenElementDimensions,\n  getHiddenElementOuterHeight,\n  getHiddenElementOuterWidth,\n  getIndex,\n  getInnerWidth,\n  getLastFocusableElement,\n  getNextElementSibling,\n  getNextFocusableElement,\n  getOffset,\n  getOuterHeight,\n  getOuterWidth,\n  getParentNode,\n  getParents,\n  getPreviousElementSibling,\n  getScrollableParents,\n  getSelection,\n  getTargetElement,\n  getUserAgent,\n  getViewport,\n  getWidth,\n  getWindowScrollLeft,\n  getWindowScrollTop,\n  hasCSSAnimation,\n  hasCSSTransition,\n  hasClass,\n  invokeElementMethod,\n  isAndroid,\n  isAttributeEquals,\n  isAttributeNotEquals,\n  isClickable,\n  isClient,\n  isElement,\n  isExist,\n  isFocusableElement,\n  isHidden,\n  isIOS,\n  isRTL,\n  isServer,\n  isTouchDevice,\n  isVisible,\n  nestedPosition,\n  relativePosition,\n  remove,\n  removeChild,\n  removeClass,\n  removeStyleTag,\n  resolveUserAgent,\n  saveAs,\n  scrollInView,\n  setAttribute,\n  setAttributes,\n  setCSSProperty,\n  toElement,\n  unblockBodyScroll\n};\n"], "mappings": "AAAA;AACA,SAASA,QAAQA,CAACC,OAAO,EAAEC,SAAS,EAAE;EACpC,IAAID,OAAO,EAAE;IACX,IAAIA,OAAO,CAACE,SAAS,EAAE,OAAOF,OAAO,CAACE,SAAS,CAACC,QAAQ,CAACF,SAAS,CAAC,CAAC,KAC/D,OAAO,IAAIG,MAAM,CAAC,OAAO,GAAGH,SAAS,GAAG,OAAO,EAAE,IAAI,CAAC,CAACI,IAAI,CAACL,OAAO,CAACC,SAAS,CAAC;EACrF;EACA,OAAO,KAAK;AACd;;AAEA;AACA,SAASK,QAAQA,CAACN,OAAO,EAAEC,SAAS,EAAE;EACpC,IAAID,OAAO,IAAIC,SAAS,EAAE;IACxB,MAAMM,EAAE,GAAIC,UAAU,IAAK;MACzB,IAAI,CAACT,QAAQ,CAACC,OAAO,EAAEQ,UAAU,CAAC,EAAE;QAClC,IAAIR,OAAO,CAACE,SAAS,EAAEF,OAAO,CAACE,SAAS,CAACO,GAAG,CAACD,UAAU,CAAC,CAAC,KACpDR,OAAO,CAACC,SAAS,IAAI,GAAG,GAAGO,UAAU;MAC5C;IACF,CAAC;IACD,CAACP,SAAS,CAAC,CAACS,IAAI,CAAC,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,OAAO,CAAEC,WAAW,IAAKA,WAAW,CAACC,KAAK,CAAC,GAAG,CAAC,CAACF,OAAO,CAACN,EAAE,CAAC,CAAC;EACjG;AACF;;AAEA;AACA,SAASS,2BAA2BA,CAAA,EAAG;EACrC,OAAOC,MAAM,CAACC,UAAU,GAAGC,QAAQ,CAACC,eAAe,CAACC,WAAW;AACjE;;AAEA;AACA,SAASC,qBAAqBA,CAACC,aAAa,EAAE;EAC5C,KAAK,MAAMC,KAAK,IAAIL,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACM,WAAW,EAAE;IACpE,IAAI;MACF,KAAK,MAAMC,IAAI,IAAIF,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACG,QAAQ,EAAE;QAC1D,KAAK,MAAMC,QAAQ,IAAIF,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACG,KAAK,EAAE;UACzD,IAAIN,aAAa,CAAClB,IAAI,CAACuB,QAAQ,CAAC,EAAE;YAChC,OAAO;cAAEE,IAAI,EAAEF,QAAQ;cAAEG,KAAK,EAAEL,IAAI,CAACG,KAAK,CAACG,gBAAgB,CAACJ,QAAQ,CAAC,CAACK,IAAI,CAAC;YAAE,CAAC;UAChF;QACF;MACF;IACF,CAAC,CAAC,OAAOC,CAAC,EAAE,CACZ;EACF;EACA,OAAO,IAAI;AACb;;AAEA;AACA,SAASC,eAAeA,CAAClC,SAAS,GAAG,mBAAmB,EAAE;EACxD,MAAMmC,YAAY,GAAGd,qBAAqB,CAAC,mBAAmB,CAAC;EAC/D,CAACc,YAAY,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACN,IAAI,KAAKX,QAAQ,CAACkB,IAAI,CAACR,KAAK,CAACS,WAAW,CAACF,YAAY,CAACN,IAAI,EAAEd,2BAA2B,CAAC,CAAC,GAAG,IAAI,CAAC;EAC/IV,QAAQ,CAACa,QAAQ,CAACkB,IAAI,EAAEpC,SAAS,CAAC;AACpC;;AAEA;AACA,SAASsC,MAAMA,CAACC,IAAI,EAAE;EACpB,IAAIA,IAAI,EAAE;IACR,IAAIC,IAAI,GAAGtB,QAAQ,CAACuB,aAAa,CAAC,GAAG,CAAC;IACtC,IAAID,IAAI,CAACE,QAAQ,KAAK,KAAK,CAAC,EAAE;MAC5B,MAAM;QAAEb,IAAI;QAAEc;MAAI,CAAC,GAAGJ,IAAI;MAC1BC,IAAI,CAACI,YAAY,CAAC,MAAM,EAAED,GAAG,CAAC;MAC9BH,IAAI,CAACI,YAAY,CAAC,UAAU,EAAEf,IAAI,CAAC;MACnCW,IAAI,CAACZ,KAAK,CAACiB,OAAO,GAAG,MAAM;MAC3B3B,QAAQ,CAACkB,IAAI,CAACU,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;MACZ7B,QAAQ,CAACkB,IAAI,CAACY,WAAW,CAACR,IAAI,CAAC;MAC/B,OAAO,IAAI;IACb;EACF;EACA,OAAO,KAAK;AACd;;AAEA;AACA,SAASS,SAASA,CAACC,GAAG,EAAEC,QAAQ,EAAE;EAChC,IAAIC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACH,GAAG,CAAC,EAAE;IACzBI,IAAI,EAAE;EACR,CAAC,CAAC;EACF,IAAItC,MAAM,CAACuC,SAAS,CAACC,gBAAgB,EAAE;IACrCD,SAAS,CAACC,gBAAgB,CAACJ,IAAI,EAAED,QAAQ,GAAG,MAAM,CAAC;EACrD,CAAC,MAAM;IACL,MAAMM,YAAY,GAAGnB,MAAM,CAAC;MAAET,IAAI,EAAEsB,QAAQ,GAAG,MAAM;MAAER,GAAG,EAAEe,GAAG,CAACC,eAAe,CAACP,IAAI;IAAE,CAAC,CAAC;IACxF,IAAI,CAACK,YAAY,EAAE;MACjBP,GAAG,GAAG,8BAA8B,GAAGA,GAAG;MAC1ClC,MAAM,CAAC4C,IAAI,CAACC,SAAS,CAACX,GAAG,CAAC,CAAC;IAC7B;EACF;AACF;;AAEA;AACA,SAASY,WAAWA,CAAC/D,OAAO,EAAEC,SAAS,EAAE;EACvC,IAAID,OAAO,IAAIC,SAAS,EAAE;IACxB,MAAMM,EAAE,GAAIC,UAAU,IAAK;MACzB,IAAIR,OAAO,CAACE,SAAS,EAAEF,OAAO,CAACE,SAAS,CAAC8D,MAAM,CAACxD,UAAU,CAAC,CAAC,KACvDR,OAAO,CAACC,SAAS,GAAGD,OAAO,CAACC,SAAS,CAACgE,OAAO,CAAC,IAAI7D,MAAM,CAAC,SAAS,GAAGI,UAAU,CAACO,KAAK,CAAC,GAAG,CAAC,CAACmD,IAAI,CAAC,GAAG,CAAC,GAAG,SAAS,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC;IACpI,CAAC;IACD,CAACjE,SAAS,CAAC,CAACS,IAAI,CAAC,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,OAAO,CAAEC,WAAW,IAAKA,WAAW,CAACC,KAAK,CAAC,GAAG,CAAC,CAACF,OAAO,CAACN,EAAE,CAAC,CAAC;EACjG;AACF;;AAEA;AACA,SAAS4D,iBAAiBA,CAAClE,SAAS,GAAG,mBAAmB,EAAE;EAC1D,MAAMmC,YAAY,GAAGd,qBAAqB,CAAC,mBAAmB,CAAC;EAC/D,CAACc,YAAY,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACN,IAAI,KAAKX,QAAQ,CAACkB,IAAI,CAACR,KAAK,CAACuC,cAAc,CAAChC,YAAY,CAACN,IAAI,CAAC;EAC5GiC,WAAW,CAAC5C,QAAQ,CAACkB,IAAI,EAAEpC,SAAS,CAAC;AACvC;;AAEA;AACA,SAASoE,0BAA0BA,CAACrE,OAAO,EAAE;EAC3C,IAAIsE,UAAU,GAAG;IAAEC,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC;EACxC,IAAIxE,OAAO,EAAE;IACXA,OAAO,CAAC6B,KAAK,CAAC4C,UAAU,GAAG,QAAQ;IACnCzE,OAAO,CAAC6B,KAAK,CAACiB,OAAO,GAAG,OAAO;IAC/BwB,UAAU,CAACC,KAAK,GAAGvE,OAAO,CAACqB,WAAW;IACtCiD,UAAU,CAACE,MAAM,GAAGxE,OAAO,CAAC0E,YAAY;IACxC1E,OAAO,CAAC6B,KAAK,CAACiB,OAAO,GAAG,MAAM;IAC9B9C,OAAO,CAAC6B,KAAK,CAAC4C,UAAU,GAAG,SAAS;EACtC;EACA,OAAOH,UAAU;AACnB;;AAEA;AACA,SAASK,WAAWA,CAAA,EAAG;EACrB,IAAIC,GAAG,GAAG3D,MAAM;IAAE4D,CAAC,GAAG1D,QAAQ;IAAEe,CAAC,GAAG2C,CAAC,CAACzD,eAAe;IAAE0D,CAAC,GAAGD,CAAC,CAACE,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAAEC,CAAC,GAAGJ,GAAG,CAAC1D,UAAU,IAAIgB,CAAC,CAAC+C,WAAW,IAAIH,CAAC,CAACG,WAAW;IAAEC,CAAC,GAAGN,GAAG,CAACO,WAAW,IAAIjD,CAAC,CAACkD,YAAY,IAAIN,CAAC,CAACM,YAAY;EAC3M,OAAO;IAAEb,KAAK,EAAES,CAAC;IAAER,MAAM,EAAEU;EAAE,CAAC;AAChC;;AAEA;AACA,SAASG,mBAAmBA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAGnE,QAAQ,CAACC,eAAe;EAClC,OAAO,CAACH,MAAM,CAACsE,WAAW,IAAID,GAAG,CAACE,UAAU,KAAKF,GAAG,CAACG,UAAU,IAAI,CAAC,CAAC;AACvE;;AAEA;AACA,SAASC,kBAAkBA,CAAA,EAAG;EAC5B,IAAIJ,GAAG,GAAGnE,QAAQ,CAACC,eAAe;EAClC,OAAO,CAACH,MAAM,CAAC0E,WAAW,IAAIL,GAAG,CAACM,SAAS,KAAKN,GAAG,CAACO,SAAS,IAAI,CAAC,CAAC;AACrE;;AAEA;AACA,SAASC,gBAAgBA,CAAC9F,OAAO,EAAE+F,MAAM,EAAEC,MAAM,GAAG,IAAI,EAAE;EACxD,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;EAClB,IAAIpG,OAAO,EAAE;IACX,MAAMqG,iBAAiB,GAAGrG,OAAO,CAACsG,YAAY,GAAG;MAAE/B,KAAK,EAAEvE,OAAO,CAACqB,WAAW;MAAEmD,MAAM,EAAExE,OAAO,CAAC0E;IAAa,CAAC,GAAGL,0BAA0B,CAACrE,OAAO,CAAC;IACnJ,MAAMuG,kBAAkB,GAAGF,iBAAiB,CAAC7B,MAAM;IACnD,MAAMgC,iBAAiB,GAAGH,iBAAiB,CAAC9B,KAAK;IACjD,MAAMkC,iBAAiB,GAAGV,MAAM,CAACrB,YAAY;IAC7C,MAAMgC,gBAAgB,GAAGX,MAAM,CAAC1E,WAAW;IAC3C,MAAMsF,YAAY,GAAGZ,MAAM,CAACa,qBAAqB,CAAC,CAAC;IACnD,MAAMC,eAAe,GAAGnB,kBAAkB,CAAC,CAAC;IAC5C,MAAMoB,gBAAgB,GAAGzB,mBAAmB,CAAC,CAAC;IAC9C,MAAM0B,QAAQ,GAAGpC,WAAW,CAAC,CAAC;IAC9B,IAAIqC,GAAG;MAAEC,IAAI;MAAEC,MAAM,GAAG,KAAK;IAC7B,IAAIP,YAAY,CAACK,GAAG,GAAGP,iBAAiB,GAAGF,kBAAkB,GAAGQ,QAAQ,CAACvC,MAAM,EAAE;MAC/EwC,GAAG,GAAGL,YAAY,CAACK,GAAG,GAAGH,eAAe,GAAGN,kBAAkB;MAC7DW,MAAM,GAAG,QAAQ;MACjB,IAAIF,GAAG,GAAG,CAAC,EAAE;QACXA,GAAG,GAAGH,eAAe;MACvB;IACF,CAAC,MAAM;MACLG,GAAG,GAAGP,iBAAiB,GAAGE,YAAY,CAACK,GAAG,GAAGH,eAAe;IAC9D;IACA,IAAIF,YAAY,CAACM,IAAI,GAAGT,iBAAiB,GAAGO,QAAQ,CAACxC,KAAK,EAAE0C,IAAI,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAET,YAAY,CAACM,IAAI,GAAGH,gBAAgB,GAAGJ,gBAAgB,GAAGF,iBAAiB,CAAC,CAAC,KACvJS,IAAI,GAAGN,YAAY,CAACM,IAAI,GAAGH,gBAAgB;IAChD9G,OAAO,CAAC6B,KAAK,CAACmF,GAAG,GAAGA,GAAG,GAAG,IAAI;IAC9BhH,OAAO,CAAC6B,KAAK,CAACoF,IAAI,GAAGA,IAAI,GAAG,IAAI;IAChCjH,OAAO,CAAC6B,KAAK,CAACwF,eAAe,GAAGH,MAAM;IACtClB,MAAM,KAAKhG,OAAO,CAAC6B,KAAK,CAACyF,SAAS,GAAGJ,MAAM,KAAK,QAAQ,GAAG,QAAQ,CAAChB,EAAE,GAAG,CAACD,EAAE,GAAG3E,qBAAqB,CAAC,iBAAiB,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG2E,EAAE,CAAClE,KAAK,KAAK,IAAI,GAAGmE,EAAE,GAAG,KAAK,QAAQ,GAAG,CAACE,EAAE,GAAG,CAACD,EAAE,GAAG7E,qBAAqB,CAAC,iBAAiB,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG6E,EAAE,CAACpE,KAAK,KAAK,IAAI,GAAGqE,EAAE,GAAG,EAAE,CAAC;EAC5R;AACF;;AAEA;AACA,SAASmB,QAAQA,CAACvH,OAAO,EAAE6B,KAAK,EAAE;EAChC,IAAI7B,OAAO,EAAE;IACX,IAAI,OAAO6B,KAAK,KAAK,QAAQ,EAAE;MAC7B7B,OAAO,CAAC6B,KAAK,CAAC2F,OAAO,GAAG3F,KAAK;IAC/B,CAAC,MAAM;MACL4F,MAAM,CAACC,OAAO,CAAC7F,KAAK,IAAI,CAAC,CAAC,CAAC,CAAChB,OAAO,CAAC,CAAC,CAAC8G,GAAG,EAAE5F,KAAK,CAAC,KAAK/B,OAAO,CAAC6B,KAAK,CAAC8F,GAAG,CAAC,GAAG5F,KAAK,CAAC;IACnF;EACF;AACF;;AAEA;AACA,SAAS6F,aAAaA,CAAC5H,OAAO,EAAE6H,MAAM,EAAE;EACtC,IAAI7H,OAAO,YAAY8H,WAAW,EAAE;IAClC,IAAIvD,KAAK,GAAGvE,OAAO,CAACqB,WAAW;IAC/B,IAAIwG,MAAM,EAAE;MACV,IAAIhG,KAAK,GAAGkG,gBAAgB,CAAC/H,OAAO,CAAC;MACrCuE,KAAK,IAAIyD,UAAU,CAACnG,KAAK,CAACoG,UAAU,CAAC,GAAGD,UAAU,CAACnG,KAAK,CAACqG,WAAW,CAAC;IACvE;IACA,OAAO3D,KAAK;EACd;EACA,OAAO,CAAC;AACV;;AAEA;AACA,SAAS4D,gBAAgBA,CAACnI,OAAO,EAAE+F,MAAM,EAAEC,MAAM,GAAG,IAAI,EAAE;EACxD,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;EAClB,IAAIpG,OAAO,EAAE;IACX,MAAMqG,iBAAiB,GAAGrG,OAAO,CAACsG,YAAY,GAAG;MAAE/B,KAAK,EAAEvE,OAAO,CAACqB,WAAW;MAAEmD,MAAM,EAAExE,OAAO,CAAC0E;IAAa,CAAC,GAAGL,0BAA0B,CAACrE,OAAO,CAAC;IACnJ,MAAMoI,YAAY,GAAGrC,MAAM,CAACrB,YAAY;IACxC,MAAMiC,YAAY,GAAGZ,MAAM,CAACa,qBAAqB,CAAC,CAAC;IACnD,MAAMG,QAAQ,GAAGpC,WAAW,CAAC,CAAC;IAC9B,IAAIqC,GAAG;MAAEC,IAAI;MAAEC,MAAM,GAAG,KAAK;IAC7B,IAAIP,YAAY,CAACK,GAAG,GAAGoB,YAAY,GAAG/B,iBAAiB,CAAC7B,MAAM,GAAGuC,QAAQ,CAACvC,MAAM,EAAE;MAChFwC,GAAG,GAAG,CAAC,CAAC,GAAGX,iBAAiB,CAAC7B,MAAM;MACnC0C,MAAM,GAAG,QAAQ;MACjB,IAAIP,YAAY,CAACK,GAAG,GAAGA,GAAG,GAAG,CAAC,EAAE;QAC9BA,GAAG,GAAG,CAAC,CAAC,GAAGL,YAAY,CAACK,GAAG;MAC7B;IACF,CAAC,MAAM;MACLA,GAAG,GAAGoB,YAAY;IACpB;IACA,IAAI/B,iBAAiB,CAAC9B,KAAK,GAAGwC,QAAQ,CAACxC,KAAK,EAAE;MAC5C0C,IAAI,GAAGN,YAAY,CAACM,IAAI,GAAG,CAAC,CAAC;IAC/B,CAAC,MAAM,IAAIN,YAAY,CAACM,IAAI,GAAGZ,iBAAiB,CAAC9B,KAAK,GAAGwC,QAAQ,CAACxC,KAAK,EAAE;MACvE0C,IAAI,GAAG,CAACN,YAAY,CAACM,IAAI,GAAGZ,iBAAiB,CAAC9B,KAAK,GAAGwC,QAAQ,CAACxC,KAAK,IAAI,CAAC,CAAC;IAC5E,CAAC,MAAM;MACL0C,IAAI,GAAG,CAAC;IACV;IACAjH,OAAO,CAAC6B,KAAK,CAACmF,GAAG,GAAGA,GAAG,GAAG,IAAI;IAC9BhH,OAAO,CAAC6B,KAAK,CAACoF,IAAI,GAAGA,IAAI,GAAG,IAAI;IAChCjH,OAAO,CAAC6B,KAAK,CAACwF,eAAe,GAAGH,MAAM;IACtClB,MAAM,KAAKhG,OAAO,CAAC6B,KAAK,CAACyF,SAAS,GAAGJ,MAAM,KAAK,QAAQ,GAAG,QAAQ,CAAChB,EAAE,GAAG,CAACD,EAAE,GAAG3E,qBAAqB,CAAC,iBAAiB,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG2E,EAAE,CAAClE,KAAK,KAAK,IAAI,GAAGmE,EAAE,GAAG,KAAK,QAAQ,GAAG,CAACE,EAAE,GAAG,CAACD,EAAE,GAAG7E,qBAAqB,CAAC,iBAAiB,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG6E,EAAE,CAACpE,KAAK,KAAK,IAAI,GAAGqE,EAAE,GAAG,EAAE,CAAC;EAC5R;AACF;;AAEA;AACA,SAASiC,YAAYA,CAACC,OAAO,EAAEvC,MAAM,EAAEwC,QAAQ,EAAEC,iBAAiB,GAAG,IAAI,EAAE;EACzE,IAAIF,OAAO,IAAIvC,MAAM,EAAE;IACrB,IAAIwC,QAAQ,KAAK,MAAM,EAAE;MACvBJ,gBAAgB,CAACG,OAAO,EAAEvC,MAAM,CAAC;IACnC,CAAC,MAAM;MACLyC,iBAAiB,KAAKF,OAAO,CAACzG,KAAK,CAAC4G,QAAQ,GAAGb,aAAa,CAAC7B,MAAM,CAAC,GAAG,IAAI,CAAC;MAC5ED,gBAAgB,CAACwC,OAAO,EAAEvC,MAAM,CAAC;IACnC;EACF;AACF;;AAEA;AACA,SAAS2C,SAASA,CAAC1I,OAAO,EAAE;EAC1B,OAAO,OAAO8H,WAAW,KAAK,QAAQ,GAAG9H,OAAO,YAAY8H,WAAW,GAAG9H,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,IAAI,IAAIA,OAAO,CAAC2I,QAAQ,KAAK,CAAC,IAAI,OAAO3I,OAAO,CAAC4I,QAAQ,KAAK,QAAQ;AACxM;;AAEA;AACA,SAASC,SAASA,CAAC7I,OAAO,EAAE;EAC1B,IAAI+F,MAAM,GAAG/F,OAAO;EACpB,IAAIA,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;IAC1C,IAAIA,OAAO,CAAC8I,cAAc,CAAC,SAAS,CAAC,EAAE;MACrC/C,MAAM,GAAG/F,OAAO,CAAC+I,OAAO;IAC1B,CAAC,MAAM,IAAI/I,OAAO,CAAC8I,cAAc,CAAC,IAAI,CAAC,EAAE;MACvC,IAAI9I,OAAO,CAACgJ,EAAE,CAACF,cAAc,CAAC,eAAe,CAAC,EAAE;QAC9C/C,MAAM,GAAG/F,OAAO,CAACgJ,EAAE,CAACC,aAAa;MACnC,CAAC,MAAM;QACLlD,MAAM,GAAG/F,OAAO,CAACgJ,EAAE;MACrB;IACF;EACF;EACA,OAAON,SAAS,CAAC3C,MAAM,CAAC,GAAGA,MAAM,GAAG,KAAK,CAAC;AAC5C;;AAEA;AACA,SAAShD,WAAWA,CAAC/C,OAAO,EAAEkJ,KAAK,EAAE;EACnC,MAAMnD,MAAM,GAAG8C,SAAS,CAAC7I,OAAO,CAAC;EACjC,IAAI+F,MAAM,EAAEA,MAAM,CAAChD,WAAW,CAACmG,KAAK,CAAC,CAAC,KACjC,MAAM,IAAIC,KAAK,CAAC,gBAAgB,GAAGD,KAAK,GAAG,MAAM,GAAGlJ,OAAO,CAAC;AACnE;;AAEA;AACA,IAAIoJ,yBAAyB,GAAG,KAAK,CAAC;AACtC,SAASC,wBAAwBA,CAACrJ,OAAO,EAAE;EACzC,IAAIA,OAAO,EAAE;IACX,IAAI6B,KAAK,GAAGkG,gBAAgB,CAAC/H,OAAO,CAAC;IACrC,OAAOA,OAAO,CAAC0E,YAAY,GAAG1E,OAAO,CAACoF,YAAY,GAAG4C,UAAU,CAACnG,KAAK,CAACyH,cAAc,CAAC,GAAGtB,UAAU,CAACnG,KAAK,CAAC0H,iBAAiB,CAAC;EAC7H,CAAC,MAAM;IACL,IAAIH,yBAAyB,IAAI,IAAI,EAAE,OAAOA,yBAAyB;IACvE,IAAII,SAAS,GAAGrI,QAAQ,CAACuB,aAAa,CAAC,KAAK,CAAC;IAC7C6E,QAAQ,CAACiC,SAAS,EAAE;MAClBjF,KAAK,EAAE,OAAO;MACdC,MAAM,EAAE,OAAO;MACfiF,QAAQ,EAAE,QAAQ;MAClBC,QAAQ,EAAE,UAAU;MACpB1C,GAAG,EAAE;IACP,CAAC,CAAC;IACF7F,QAAQ,CAACkB,IAAI,CAACU,WAAW,CAACyG,SAAS,CAAC;IACpC,IAAIG,eAAe,GAAGH,SAAS,CAAC9E,YAAY,GAAG8E,SAAS,CAACpE,YAAY;IACrEjE,QAAQ,CAACkB,IAAI,CAACY,WAAW,CAACuG,SAAS,CAAC;IACpCJ,yBAAyB,GAAGO,eAAe;IAC3C,OAAOA,eAAe;EACxB;AACF;;AAEA;AACA,IAAIC,wBAAwB,GAAG,KAAK,CAAC;AACrC,SAASC,uBAAuBA,CAAC7J,OAAO,EAAE;EACxC,IAAIA,OAAO,EAAE;IACX,IAAI6B,KAAK,GAAGkG,gBAAgB,CAAC/H,OAAO,CAAC;IACrC,OAAOA,OAAO,CAACqB,WAAW,GAAGrB,OAAO,CAACiF,WAAW,GAAG+C,UAAU,CAACnG,KAAK,CAACiI,eAAe,CAAC,GAAG9B,UAAU,CAACnG,KAAK,CAACkI,gBAAgB,CAAC;EAC3H,CAAC,MAAM;IACL,IAAIH,wBAAwB,IAAI,IAAI,EAAE,OAAOA,wBAAwB;IACrE,IAAIJ,SAAS,GAAGrI,QAAQ,CAACuB,aAAa,CAAC,KAAK,CAAC;IAC7C6E,QAAQ,CAACiC,SAAS,EAAE;MAClBjF,KAAK,EAAE,OAAO;MACdC,MAAM,EAAE,OAAO;MACfiF,QAAQ,EAAE,QAAQ;MAClBC,QAAQ,EAAE,UAAU;MACpB1C,GAAG,EAAE;IACP,CAAC,CAAC;IACF7F,QAAQ,CAACkB,IAAI,CAACU,WAAW,CAACyG,SAAS,CAAC;IACpC,IAAIQ,cAAc,GAAGR,SAAS,CAACnI,WAAW,GAAGmI,SAAS,CAACvE,WAAW;IAClE9D,QAAQ,CAACkB,IAAI,CAACY,WAAW,CAACuG,SAAS,CAAC;IACpCI,wBAAwB,GAAGI,cAAc;IACzC,OAAOA,cAAc;EACvB;AACF;;AAEA;AACA,SAASC,cAAcA,CAAA,EAAG;EACxB,IAAIhJ,MAAM,CAACiJ,YAAY,EAAE;IACvB,MAAMC,SAAS,GAAGlJ,MAAM,CAACiJ,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC;IAC7C,IAAIC,SAAS,CAACC,KAAK,EAAE;MACnBD,SAAS,CAACC,KAAK,CAAC,CAAC;IACnB,CAAC,MAAM,IAAID,SAAS,CAACE,eAAe,IAAIF,SAAS,CAACG,UAAU,GAAG,CAAC,IAAIH,SAAS,CAACI,UAAU,CAAC,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC,CAACC,MAAM,GAAG,CAAC,EAAE;MACvHN,SAAS,CAACE,eAAe,CAAC,CAAC;IAC7B;EACF;AACF;;AAEA;AACA,SAASK,aAAaA,CAAC1K,OAAO,EAAE2K,UAAU,GAAG,CAAC,CAAC,EAAE;EAC/C,IAAIjC,SAAS,CAAC1I,OAAO,CAAC,EAAE;IACtB,MAAM4K,cAAc,GAAGA,CAAClJ,IAAI,EAAEK,KAAK,KAAK;MACtC,IAAIkE,EAAE,EAAEC,EAAE;MACV,MAAM2E,MAAM,GAAG,CAAC,CAAC5E,EAAE,GAAGjG,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC8K,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG7E,EAAE,CAACvE,IAAI,CAAC,IAAI,CAAC,CAACwE,EAAE,GAAGlG,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC8K,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG5E,EAAE,CAACxE,IAAI,CAAC,CAAC,GAAG,EAAE;MACnL,OAAO,CAACK,KAAK,CAAC,CAACrB,IAAI,CAAC,CAAC,CAACqK,MAAM,CAAC,CAACC,EAAE,EAAEC,CAAC,KAAK;QACtC,IAAIA,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,KAAK,CAAC,EAAE;UAC9B,MAAM1H,IAAI,GAAG,OAAO0H,CAAC;UACrB,IAAI1H,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,QAAQ,EAAE;YAC1CyH,EAAE,CAACE,IAAI,CAACD,CAAC,CAAC;UACZ,CAAC,MAAM,IAAI1H,IAAI,KAAK,QAAQ,EAAE;YAC5B,MAAM4H,GAAG,GAAGC,KAAK,CAACC,OAAO,CAACJ,CAAC,CAAC,GAAGL,cAAc,CAAClJ,IAAI,EAAEuJ,CAAC,CAAC,GAAGxD,MAAM,CAACC,OAAO,CAACuD,CAAC,CAAC,CAACK,GAAG,CAAC,CAAC,CAACC,EAAE,EAAEC,EAAE,CAAC,KAAK9J,IAAI,KAAK,OAAO,KAAK,CAAC,CAAC8J,EAAE,IAAIA,EAAE,KAAK,CAAC,CAAC,GAAG,GAAGD,EAAE,CAACtH,OAAO,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAACwH,WAAW,CAAC,CAAC,IAAID,EAAE,EAAE,GAAG,CAAC,CAACA,EAAE,GAAGD,EAAE,GAAG,KAAK,CAAC,CAAC;YAC3NP,EAAE,GAAGG,GAAG,CAACV,MAAM,GAAGO,EAAE,CAACU,MAAM,CAACP,GAAG,CAACxK,MAAM,CAAEgL,CAAC,IAAK,CAAC,CAACA,CAAC,CAAC,CAAC,GAAGX,EAAE;UAC1D;QACF;QACA,OAAOA,EAAE;MACX,CAAC,EAAEH,MAAM,CAAC;IACZ,CAAC;IACDpD,MAAM,CAACC,OAAO,CAACiD,UAAU,CAAC,CAAC9J,OAAO,CAAC,CAAC,CAAC8G,GAAG,EAAE5F,KAAK,CAAC,KAAK;MACnD,IAAIA,KAAK,KAAK,KAAK,CAAC,IAAIA,KAAK,KAAK,IAAI,EAAE;QACtC,MAAM6J,YAAY,GAAGjE,GAAG,CAACkE,KAAK,CAAC,SAAS,CAAC;QACzC,IAAID,YAAY,EAAE;UAChB5L,OAAO,CAAC8L,gBAAgB,CAACF,YAAY,CAAC,CAAC,CAAC,CAACH,WAAW,CAAC,CAAC,EAAE1J,KAAK,CAAC;QAChE,CAAC,MAAM,IAAI4F,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,OAAO,EAAE;UAC9C+C,aAAa,CAAC1K,OAAO,EAAE+B,KAAK,CAAC;QAC/B,CAAC,MAAM;UACLA,KAAK,GAAG4F,GAAG,KAAK,OAAO,GAAG,CAAC,GAAG,IAAIoE,GAAG,CAACnB,cAAc,CAAC,OAAO,EAAE7I,KAAK,CAAC,CAAC,CAAC,CAACmC,IAAI,CAAC,GAAG,CAAC,CAACjC,IAAI,CAAC,CAAC,GAAG0F,GAAG,KAAK,OAAO,GAAGiD,cAAc,CAAC,OAAO,EAAE7I,KAAK,CAAC,CAACmC,IAAI,CAAC,GAAG,CAAC,CAACjC,IAAI,CAAC,CAAC,GAAGF,KAAK;UACnK,CAAC/B,OAAO,CAAC8K,MAAM,GAAG9K,OAAO,CAAC8K,MAAM,IAAI,CAAC,CAAC,MAAM9K,OAAO,CAAC8K,MAAM,CAACnD,GAAG,CAAC,GAAG5F,KAAK,CAAC;UACxE/B,OAAO,CAAC6C,YAAY,CAAC8E,GAAG,EAAE5F,KAAK,CAAC;QAClC;MACF;IACF,CAAC,CAAC;EACJ;AACF;;AAEA;AACA,SAASW,aAAaA,CAACa,IAAI,EAAEoH,UAAU,GAAG,CAAC,CAAC,EAAE,GAAGqB,QAAQ,EAAE;EACzD,IAAIzI,IAAI,EAAE;IACR,MAAMvD,OAAO,GAAGmB,QAAQ,CAACuB,aAAa,CAACa,IAAI,CAAC;IAC5CmH,aAAa,CAAC1K,OAAO,EAAE2K,UAAU,CAAC;IAClC3K,OAAO,CAACiM,MAAM,CAAC,GAAGD,QAAQ,CAAC;IAC3B,OAAOhM,OAAO;EAChB;EACA,OAAO,KAAK,CAAC;AACf;;AAEA;AACA,SAASkM,mBAAmBA,CAACC,GAAG,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;EAC9C,OAAOD,GAAG,GAAG,WAAW1E,MAAM,CAACC,OAAO,CAAC0E,OAAO,CAAC,CAACrB,MAAM,CAAC,CAACsB,CAAC,EAAE,CAACC,CAAC,EAAErB,CAAC,CAAC,KAAKoB,CAAC,GAAG,GAAGC,CAAC,KAAKrB,CAAC,GAAG,EAAE,GAAG,CAAC,IAAIkB,GAAG,WAAW,GAAG,EAAE;AACtH;;AAEA;AACA,SAASI,cAAcA,CAAC5B,UAAU,GAAG,CAAC,CAAC,EAAE6B,SAAS,EAAE;EAClD,IAAIxM,OAAO,GAAGmB,QAAQ,CAACuB,aAAa,CAAC,OAAO,CAAC;EAC7CgI,aAAa,CAAC1K,OAAO,EAAE2K,UAAU,CAAC;EAClC,IAAI,CAAC6B,SAAS,EAAE;IACdA,SAAS,GAAGrL,QAAQ,CAACsL,IAAI;EAC3B;EACAD,SAAS,CAACzJ,WAAW,CAAC/C,OAAO,CAAC;EAC9B,OAAOA,OAAO;AAChB;;AAEA;AACA,SAAS0M,MAAMA,CAAC1M,OAAO,EAAE2M,QAAQ,EAAE;EACjC,IAAI3M,OAAO,EAAE;IACXA,OAAO,CAAC6B,KAAK,CAAC+K,OAAO,GAAG,GAAG;IAC3B,IAAIC,IAAI,GAAG,EAAC,eAAgB,IAAIC,IAAI,CAAC,CAAC;IACtC,IAAIF,OAAO,GAAG,GAAG;IACjB,IAAIG,IAAI,GAAG,SAAAA,CAAA,EAAW;MACpBH,OAAO,GAAG,GAAG,CAAC5M,OAAO,CAAC6B,KAAK,CAAC+K,OAAO,GAAG,CAAC,CAAC,eAAgB,IAAIE,IAAI,CAAC,CAAC,EAAEE,OAAO,CAAC,CAAC,GAAGH,IAAI,IAAIF,QAAQ,EAAE;MAClG3M,OAAO,CAAC6B,KAAK,CAAC+K,OAAO,GAAGA,OAAO;MAC/BC,IAAI,GAAG,EAAC,eAAgB,IAAIC,IAAI,CAAC,CAAC;MAClC,IAAI,CAACF,OAAO,GAAG,CAAC,EAAE;QAChB,CAAC,CAAC3L,MAAM,CAACgM,qBAAqB,IAAIA,qBAAqB,CAACF,IAAI,CAAC,IAAIG,UAAU,CAACH,IAAI,EAAE,EAAE,CAAC;MACvF;IACF,CAAC;IACDA,IAAI,CAAC,CAAC;EACR;AACF;;AAEA;AACA,SAASI,OAAOA,CAACnN,OAAO,EAAE2M,QAAQ,EAAE;EAClC,IAAI3M,OAAO,EAAE;IACX,IAAI4M,OAAO,GAAG,CAAC;MAAEQ,QAAQ,GAAG,EAAE;MAAEC,GAAG,GAAGD,QAAQ,GAAGT,QAAQ;IACzD,IAAIW,MAAM,GAAGC,WAAW,CAAC,MAAM;MAC7BX,OAAO,IAAIS,GAAG;MACd,IAAIT,OAAO,IAAI,CAAC,EAAE;QAChBA,OAAO,GAAG,CAAC;QACXY,aAAa,CAACF,MAAM,CAAC;MACvB;MACAtN,OAAO,CAAC6B,KAAK,CAAC+K,OAAO,GAAGA,OAAO,CAACa,QAAQ,CAAC,CAAC;IAC5C,CAAC,EAAEL,QAAQ,CAAC;EACd;AACF;;AAEA;AACA,SAASM,IAAIA,CAAC1N,OAAO,EAAE2N,QAAQ,EAAE;EAC/B,OAAOjF,SAAS,CAAC1I,OAAO,CAAC,GAAGoL,KAAK,CAACwC,IAAI,CAAC5N,OAAO,CAAC6N,gBAAgB,CAACF,QAAQ,CAAC,CAAC,GAAG,EAAE;AACjF;;AAEA;AACA,SAASG,UAAUA,CAAC9N,OAAO,EAAE2N,QAAQ,EAAE;EACrC,OAAOjF,SAAS,CAAC1I,OAAO,CAAC,GAAGA,OAAO,CAAC+N,OAAO,CAACJ,QAAQ,CAAC,GAAG3N,OAAO,GAAGA,OAAO,CAACgO,aAAa,CAACL,QAAQ,CAAC,GAAG,IAAI;AAC1G;;AAEA;AACA,SAASM,KAAKA,CAACjO,OAAO,EAAEoM,OAAO,EAAE;EAC/BpM,OAAO,IAAImB,QAAQ,CAAC+M,aAAa,KAAKlO,OAAO,IAAIA,OAAO,CAACiO,KAAK,CAAC7B,OAAO,CAAC;AACzE;;AAEA;AACA,SAAS+B,YAAYA,CAACnO,OAAO,EAAE8B,IAAI,EAAE;EACnC,IAAI4G,SAAS,CAAC1I,OAAO,CAAC,EAAE;IACtB,MAAM+B,KAAK,GAAG/B,OAAO,CAACmO,YAAY,CAACrM,IAAI,CAAC;IACxC,IAAI,CAACsM,KAAK,CAACrM,KAAK,CAAC,EAAE;MACjB,OAAO,CAACA,KAAK;IACf;IACA,IAAIA,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,OAAO,EAAE;MACzC,OAAOA,KAAK,KAAK,MAAM;IACzB;IACA,OAAOA,KAAK;EACd;EACA,OAAO,KAAK,CAAC;AACf;;AAEA;AACA,SAASsM,gBAAgBA,CAAA,EAAG;EAC1B,IAAIC,EAAE,GAAG9K,SAAS,CAAC+K,SAAS,CAAC9C,WAAW,CAAC,CAAC;EAC1C,IAAII,KAAK,GAAG,qBAAqB,CAAC2C,IAAI,CAACF,EAAE,CAAC,IAAI,qBAAqB,CAACE,IAAI,CAACF,EAAE,CAAC,IAAI,kCAAkC,CAACE,IAAI,CAACF,EAAE,CAAC,IAAI,iBAAiB,CAACE,IAAI,CAACF,EAAE,CAAC,IAAIA,EAAE,CAACG,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,+BAA+B,CAACD,IAAI,CAACF,EAAE,CAAC,IAAI,EAAE;EAC3O,OAAO;IACLI,OAAO,EAAE7C,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE;IACvB8C,OAAO,EAAE9C,KAAK,CAAC,CAAC,CAAC,IAAI;EACvB,CAAC;AACH;;AAEA;AACA,IAAI6C,OAAO,GAAG,IAAI;AAClB,SAASE,UAAUA,CAAA,EAAG;EACpB,IAAI,CAACF,OAAO,EAAE;IACZA,OAAO,GAAG,CAAC,CAAC;IACZ,IAAIG,OAAO,GAAGR,gBAAgB,CAAC,CAAC;IAChC,IAAIQ,OAAO,CAACH,OAAO,EAAE;MACnBA,OAAO,CAACG,OAAO,CAACH,OAAO,CAAC,GAAG,IAAI;MAC/BA,OAAO,CAAC,SAAS,CAAC,GAAGG,OAAO,CAACF,OAAO;IACtC;IACA,IAAID,OAAO,CAAC,QAAQ,CAAC,EAAE;MACrBA,OAAO,CAAC,QAAQ,CAAC,GAAG,IAAI;IAC1B,CAAC,MAAM,IAAIA,OAAO,CAAC,QAAQ,CAAC,EAAE;MAC5BA,OAAO,CAAC,QAAQ,CAAC,GAAG,IAAI;IAC1B;EACF;EACA,OAAOA,OAAO;AAChB;;AAEA;AACA,SAASI,kBAAkBA,CAAA,EAAG;EAC5B,OAAOtL,SAAS,CAACuL,SAAS,IAAIvL,SAAS,CAACuL,SAAS,CAACtE,MAAM,IAAIjH,SAAS,CAACuL,SAAS,CAAC,CAAC,CAAC,IAAIvL,SAAS,CAACwL,QAAQ,IAAI,IAAI;AAClH;;AAEA;AACA,SAASC,cAAcA,CAACjP,OAAO,EAAE4B,QAAQ,EAAEsN,MAAM,EAAE;EACjD,IAAIjJ,EAAE;EACN,IAAIjG,OAAO,IAAI4B,QAAQ,EAAE;IACvB,OAAOsN,MAAM,GAAG,CAACjJ,EAAE,GAAGjG,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC6B,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGoE,EAAE,CAACjE,gBAAgB,CAACJ,QAAQ,CAAC,GAAGmG,gBAAgB,CAAC/H,OAAO,CAAC,CAACgC,gBAAgB,CAACJ,QAAQ,CAAC;EACzK;EACA,OAAO,IAAI;AACb;;AAEA;AACA,SAASuN,eAAeA,CAACnP,OAAO,EAAEoP,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,EAAE;EACjE,IAAItP,OAAO,EAAE;IACX,IAAI6B,KAAK,GAAGkG,gBAAgB,CAAC/H,OAAO,CAAC;IACrC,IAAIuP,QAAQ,GAAGpO,QAAQ,CAACuB,aAAa,CAAC,KAAK,CAAC;IAC5C6M,QAAQ,CAAC1N,KAAK,CAAC6H,QAAQ,GAAG,UAAU;IACpC6F,QAAQ,CAAC1N,KAAK,CAACmF,GAAG,GAAG,KAAK;IAC1BuI,QAAQ,CAAC1N,KAAK,CAACoF,IAAI,GAAG,KAAK;IAC3BsI,QAAQ,CAAC1N,KAAK,CAAC4C,UAAU,GAAG,QAAQ;IACpC8K,QAAQ,CAAC1N,KAAK,CAAC2N,aAAa,GAAG,MAAM;IACrCD,QAAQ,CAAC1N,KAAK,CAAC4H,QAAQ,GAAG5H,KAAK,CAAC4H,QAAQ;IACxC8F,QAAQ,CAAC1N,KAAK,CAAC0C,KAAK,GAAG1C,KAAK,CAAC0C,KAAK;IAClCgL,QAAQ,CAAC1N,KAAK,CAAC2C,MAAM,GAAG3C,KAAK,CAAC2C,MAAM;IACpC+K,QAAQ,CAAC1N,KAAK,CAAC4N,OAAO,GAAG5N,KAAK,CAAC4N,OAAO;IACtCF,QAAQ,CAAC1N,KAAK,CAAC6N,MAAM,GAAG7N,KAAK,CAAC6N,MAAM;IACpCH,QAAQ,CAAC1N,KAAK,CAAC8N,YAAY,GAAG9N,KAAK,CAAC8N,YAAY;IAChDJ,QAAQ,CAAC1N,KAAK,CAAC+N,UAAU,GAAG/N,KAAK,CAAC+N,UAAU;IAC5CL,QAAQ,CAAC1N,KAAK,CAACgO,UAAU,GAAGhO,KAAK,CAACgO,UAAU;IAC5CN,QAAQ,CAACO,SAAS,GAAGV,QAAQ,CAACnL,OAAO,CAAC,aAAa,EAAE,QAAQ,CAAC;IAC9D,IAAI8L,SAAS,GAAG5O,QAAQ,CAACuB,aAAa,CAAC,MAAM,CAAC;IAC9CqN,SAAS,CAACC,WAAW,GAAGV,WAAW;IACnCC,QAAQ,CAACxM,WAAW,CAACgN,SAAS,CAAC;IAC/B,IAAIE,IAAI,GAAG9O,QAAQ,CAAC+O,cAAc,CAACb,QAAQ,CAAC;IAC5CE,QAAQ,CAACxM,WAAW,CAACkN,IAAI,CAAC;IAC1B9O,QAAQ,CAACkB,IAAI,CAACU,WAAW,CAACwM,QAAQ,CAAC;IACnC,MAAM;MAAEY,UAAU;MAAEC,SAAS;MAAEhL;IAAa,CAAC,GAAG2K,SAAS;IACzD5O,QAAQ,CAACkB,IAAI,CAACY,WAAW,CAACsM,QAAQ,CAAC;IACnC,OAAO;MACLtI,IAAI,EAAEE,IAAI,CAACkJ,GAAG,CAACF,UAAU,GAAGnQ,OAAO,CAACwF,UAAU,CAAC;MAC/CwB,GAAG,EAAEG,IAAI,CAACkJ,GAAG,CAACD,SAAS,GAAGpQ,OAAO,CAAC4F,SAAS,CAAC,GAAGR;IACjD,CAAC;EACH;EACA,OAAO;IACL4B,GAAG,EAAE,MAAM;IACXC,IAAI,EAAE;EACR,CAAC;AACH;;AAEA;AACA,SAASqJ,oBAAoBA,CAACtQ,OAAO,EAAE2N,QAAQ,GAAG,EAAE,EAAE;EACpD,IAAI4C,iBAAiB,GAAG7C,IAAI,CAC1B1N,OAAO,EACP,2FAA2F2N,QAAQ;AACvG,iIAAiIA,QAAQ;AACzI,qGAAqGA,QAAQ;AAC7G,sGAAsGA,QAAQ;AAC9G,wGAAwGA,QAAQ;AAChH,0GAA0GA,QAAQ;AAClH,iHAAiHA,QAAQ,EACvH,CAAC;EACD,IAAI6C,wBAAwB,GAAG,EAAE;EACjC,KAAK,IAAIC,gBAAgB,IAAIF,iBAAiB,EAAE;IAC9C,IAAIxI,gBAAgB,CAAC0I,gBAAgB,CAAC,CAAC3N,OAAO,IAAI,MAAM,IAAIiF,gBAAgB,CAAC0I,gBAAgB,CAAC,CAAChM,UAAU,IAAI,QAAQ,EAAE+L,wBAAwB,CAACtF,IAAI,CAACuF,gBAAgB,CAAC;EACxK;EACA,OAAOD,wBAAwB;AACjC;;AAEA;AACA,SAASE,wBAAwBA,CAAC1Q,OAAO,EAAE2N,QAAQ,EAAE;EACnD,MAAM4C,iBAAiB,GAAGD,oBAAoB,CAACtQ,OAAO,EAAE2N,QAAQ,CAAC;EACjE,OAAO4C,iBAAiB,CAAC9F,MAAM,GAAG,CAAC,GAAG8F,iBAAiB,CAAC,CAAC,CAAC,GAAG,IAAI;AACnE;;AAEA;AACA,SAASI,SAASA,CAAC3Q,OAAO,EAAE;EAC1B,IAAIA,OAAO,EAAE;IACX,IAAIwE,MAAM,GAAGxE,OAAO,CAAC0E,YAAY;IACjC,IAAI7C,KAAK,GAAGkG,gBAAgB,CAAC/H,OAAO,CAAC;IACrCwE,MAAM,IAAIwD,UAAU,CAACnG,KAAK,CAAC+O,UAAU,CAAC,GAAG5I,UAAU,CAACnG,KAAK,CAACgP,aAAa,CAAC,GAAG7I,UAAU,CAACnG,KAAK,CAACyH,cAAc,CAAC,GAAGtB,UAAU,CAACnG,KAAK,CAAC0H,iBAAiB,CAAC;IACjJ,OAAO/E,MAAM;EACf;EACA,OAAO,CAAC;AACV;;AAEA;AACA,SAASsM,2BAA2BA,CAAC9Q,OAAO,EAAE;EAC5C,IAAIA,OAAO,EAAE;IACXA,OAAO,CAAC6B,KAAK,CAAC4C,UAAU,GAAG,QAAQ;IACnCzE,OAAO,CAAC6B,KAAK,CAACiB,OAAO,GAAG,OAAO;IAC/B,IAAIiO,aAAa,GAAG/Q,OAAO,CAAC0E,YAAY;IACxC1E,OAAO,CAAC6B,KAAK,CAACiB,OAAO,GAAG,MAAM;IAC9B9C,OAAO,CAAC6B,KAAK,CAAC4C,UAAU,GAAG,SAAS;IACpC,OAAOsM,aAAa;EACtB;EACA,OAAO,CAAC;AACV;;AAEA;AACA,SAASC,0BAA0BA,CAAChR,OAAO,EAAE;EAC3C,IAAIA,OAAO,EAAE;IACXA,OAAO,CAAC6B,KAAK,CAAC4C,UAAU,GAAG,QAAQ;IACnCzE,OAAO,CAAC6B,KAAK,CAACiB,OAAO,GAAG,OAAO;IAC/B,IAAImO,YAAY,GAAGjR,OAAO,CAACqB,WAAW;IACtCrB,OAAO,CAAC6B,KAAK,CAACiB,OAAO,GAAG,MAAM;IAC9B9C,OAAO,CAAC6B,KAAK,CAAC4C,UAAU,GAAG,SAAS;IACpC,OAAOwM,YAAY;EACrB;EACA,OAAO,CAAC;AACV;;AAEA;AACA,SAASC,aAAaA,CAAClR,OAAO,EAAE;EAC9B,IAAIA,OAAO,EAAE;IACX,IAAImR,MAAM,GAAGnR,OAAO,CAACoR,UAAU;IAC/B,IAAID,MAAM,IAAIA,MAAM,YAAYE,UAAU,IAAIF,MAAM,CAACG,IAAI,EAAE;MACzDH,MAAM,GAAGA,MAAM,CAACG,IAAI;IACtB;IACA,OAAOH,MAAM;EACf;EACA,OAAO,IAAI;AACb;;AAEA;AACA,SAASI,QAAQA,CAACvR,OAAO,EAAE;EACzB,IAAIiG,EAAE;EACN,IAAIjG,OAAO,EAAE;IACX,IAAIgM,QAAQ,GAAG,CAAC/F,EAAE,GAAGiL,aAAa,CAAClR,OAAO,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGiG,EAAE,CAACuL,UAAU;IAC7E,IAAIC,GAAG,GAAG,CAAC;IACX,IAAIzF,QAAQ,EAAE;MACZ,KAAK,IAAI0F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1F,QAAQ,CAACvB,MAAM,EAAEiH,CAAC,EAAE,EAAE;QACxC,IAAI1F,QAAQ,CAAC0F,CAAC,CAAC,KAAK1R,OAAO,EAAE,OAAOyR,GAAG;QACvC,IAAIzF,QAAQ,CAAC0F,CAAC,CAAC,CAAC/I,QAAQ,KAAK,CAAC,EAAE8I,GAAG,EAAE;MACvC;IACF;EACF;EACA,OAAO,CAAC,CAAC;AACX;;AAEA;AACA,SAASE,aAAaA,CAAC3R,OAAO,EAAE;EAC9B,IAAIA,OAAO,EAAE;IACX,IAAIuE,KAAK,GAAGvE,OAAO,CAACqB,WAAW;IAC/B,IAAIQ,KAAK,GAAGkG,gBAAgB,CAAC/H,OAAO,CAAC;IACrCuE,KAAK,IAAIyD,UAAU,CAACnG,KAAK,CAAC+P,UAAU,CAAC,GAAG5J,UAAU,CAACnG,KAAK,CAACgQ,WAAW,CAAC;IACrE,OAAOtN,KAAK;EACd;EACA,OAAO,CAAC;AACV;;AAEA;AACA,SAASuN,uBAAuBA,CAAC9R,OAAO,EAAE2N,QAAQ,EAAE;EAClD,MAAM4C,iBAAiB,GAAGD,oBAAoB,CAACtQ,OAAO,EAAE2N,QAAQ,CAAC;EACjE,OAAO4C,iBAAiB,CAAC9F,MAAM,GAAG,CAAC,GAAG8F,iBAAiB,CAACA,iBAAiB,CAAC9F,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI;AAC9F;;AAEA;AACA,SAASsH,qBAAqBA,CAAC/R,OAAO,EAAE2N,QAAQ,EAAE;EAChD,IAAIqE,WAAW,GAAGhS,OAAO,CAACiS,kBAAkB;EAC5C,OAAOD,WAAW,EAAE;IAClB,IAAIA,WAAW,CAACjE,OAAO,CAACJ,QAAQ,CAAC,EAAE;MACjC,OAAOqE,WAAW;IACpB,CAAC,MAAM;MACLA,WAAW,GAAGA,WAAW,CAACC,kBAAkB;IAC9C;EACF;EACA,OAAO,IAAI;AACb;;AAEA;AACA,SAASC,uBAAuBA,CAAC1F,SAAS,EAAExM,OAAO,EAAE2N,QAAQ,EAAE;EAC7D,MAAM4C,iBAAiB,GAAGD,oBAAoB,CAAC9D,SAAS,EAAEmB,QAAQ,CAAC;EACnE,MAAMwE,KAAK,GAAG5B,iBAAiB,CAAC9F,MAAM,GAAG,CAAC,GAAG8F,iBAAiB,CAAC6B,SAAS,CAAEpJ,EAAE,IAAKA,EAAE,KAAKhJ,OAAO,CAAC,GAAG,CAAC,CAAC;EACrG,MAAMqS,SAAS,GAAGF,KAAK,GAAG,CAAC,CAAC,IAAI5B,iBAAiB,CAAC9F,MAAM,IAAI0H,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;EACtF,OAAOE,SAAS,GAAG,CAAC,CAAC,GAAG9B,iBAAiB,CAAC8B,SAAS,CAAC,GAAG,IAAI;AAC7D;;AAEA;AACA,SAASC,SAASA,CAACtS,OAAO,EAAE;EAC1B,IAAIA,OAAO,EAAE;IACX,IAAIuS,IAAI,GAAGvS,OAAO,CAAC4G,qBAAqB,CAAC,CAAC;IAC1C,OAAO;MACLI,GAAG,EAAEuL,IAAI,CAACvL,GAAG,IAAI/F,MAAM,CAAC0E,WAAW,IAAIxE,QAAQ,CAACC,eAAe,CAACwE,SAAS,IAAIzE,QAAQ,CAACkB,IAAI,CAACuD,SAAS,IAAI,CAAC,CAAC;MAC1GqB,IAAI,EAAEsL,IAAI,CAACtL,IAAI,IAAIhG,MAAM,CAACsE,WAAW,IAAIpE,QAAQ,CAACC,eAAe,CAACoE,UAAU,IAAIrE,QAAQ,CAACkB,IAAI,CAACmD,UAAU,IAAI,CAAC;IAC/G,CAAC;EACH;EACA,OAAO;IACLwB,GAAG,EAAE,MAAM;IACXC,IAAI,EAAE;EACR,CAAC;AACH;;AAEA;AACA,SAASuL,cAAcA,CAACxS,OAAO,EAAE6H,MAAM,EAAE;EACvC,IAAI7H,OAAO,EAAE;IACX,IAAIwE,MAAM,GAAGxE,OAAO,CAAC0E,YAAY;IACjC,IAAImD,MAAM,EAAE;MACV,IAAIhG,KAAK,GAAGkG,gBAAgB,CAAC/H,OAAO,CAAC;MACrCwE,MAAM,IAAIwD,UAAU,CAACnG,KAAK,CAACyF,SAAS,CAAC,GAAGU,UAAU,CAACnG,KAAK,CAAC4Q,YAAY,CAAC;IACxE;IACA,OAAOjO,MAAM;EACf;EACA,OAAO,CAAC;AACV;;AAEA;AACA,SAASkO,UAAUA,CAAC1S,OAAO,EAAE2S,OAAO,GAAG,EAAE,EAAE;EACzC,MAAMxB,MAAM,GAAGD,aAAa,CAAClR,OAAO,CAAC;EACrC,OAAOmR,MAAM,KAAK,IAAI,GAAGwB,OAAO,GAAGD,UAAU,CAACvB,MAAM,EAAEwB,OAAO,CAACjH,MAAM,CAAC,CAACyF,MAAM,CAAC,CAAC,CAAC;AACjF;;AAEA;AACA,SAASyB,yBAAyBA,CAAC5S,OAAO,EAAE2N,QAAQ,EAAE;EACpD,IAAIkF,eAAe,GAAG7S,OAAO,CAAC8S,sBAAsB;EACpD,OAAOD,eAAe,EAAE;IACtB,IAAIA,eAAe,CAAC9E,OAAO,CAACJ,QAAQ,CAAC,EAAE;MACrC,OAAOkF,eAAe;IACxB,CAAC,MAAM;MACLA,eAAe,GAAGA,eAAe,CAACC,sBAAsB;IAC1D;EACF;EACA,OAAO,IAAI;AACb;;AAEA;AACA,SAASC,oBAAoBA,CAAC/S,OAAO,EAAE;EACrC,IAAIgT,iBAAiB,GAAG,EAAE;EAC1B,IAAIhT,OAAO,EAAE;IACX,IAAI2S,OAAO,GAAGD,UAAU,CAAC1S,OAAO,CAAC;IACjC,MAAMiT,aAAa,GAAG,eAAe;IACrC,MAAMC,aAAa,GAAIC,IAAI,IAAK;MAC9B,IAAI;QACF,IAAIC,gBAAgB,GAAGnS,MAAM,CAAC,kBAAkB,CAAC,CAACkS,IAAI,EAAE,IAAI,CAAC;QAC7D,OAAOF,aAAa,CAAC5S,IAAI,CAAC+S,gBAAgB,CAACpR,gBAAgB,CAAC,UAAU,CAAC,CAAC,IAAIiR,aAAa,CAAC5S,IAAI,CAAC+S,gBAAgB,CAACpR,gBAAgB,CAAC,WAAW,CAAC,CAAC,IAAIiR,aAAa,CAAC5S,IAAI,CAAC+S,gBAAgB,CAACpR,gBAAgB,CAAC,WAAW,CAAC,CAAC;MACtN,CAAC,CAAC,OAAOqR,GAAG,EAAE;QACZ,OAAO,KAAK;MACd;IACF,CAAC;IACD,KAAK,IAAIlC,MAAM,IAAIwB,OAAO,EAAE;MAC1B,IAAIW,eAAe,GAAGnC,MAAM,CAACxI,QAAQ,KAAK,CAAC,IAAIwI,MAAM,CAACoC,OAAO,CAAC,iBAAiB,CAAC;MAChF,IAAID,eAAe,EAAE;QACnB,IAAIE,SAAS,GAAGF,eAAe,CAACvS,KAAK,CAAC,GAAG,CAAC;QAC1C,KAAK,IAAI4M,QAAQ,IAAI6F,SAAS,EAAE;UAC9B,IAAIxK,EAAE,GAAG8E,UAAU,CAACqD,MAAM,EAAExD,QAAQ,CAAC;UACrC,IAAI3E,EAAE,IAAIkK,aAAa,CAAClK,EAAE,CAAC,EAAE;YAC3BgK,iBAAiB,CAAC9H,IAAI,CAAClC,EAAE,CAAC;UAC5B;QACF;MACF;MACA,IAAImI,MAAM,CAACxI,QAAQ,KAAK,CAAC,IAAIuK,aAAa,CAAC/B,MAAM,CAAC,EAAE;QAClD6B,iBAAiB,CAAC9H,IAAI,CAACiG,MAAM,CAAC;MAChC;IACF;EACF;EACA,OAAO6B,iBAAiB;AAC1B;;AAEA;AACA,SAAS9I,YAAYA,CAAA,EAAG;EACtB,IAAIjJ,MAAM,CAACiJ,YAAY,EAAE,OAAOjJ,MAAM,CAACiJ,YAAY,CAAC,CAAC,CAACuD,QAAQ,CAAC,CAAC,CAAC,KAC5D,IAAItM,QAAQ,CAAC+I,YAAY,EAAE,OAAO/I,QAAQ,CAAC+I,YAAY,CAAC,CAAC,CAACuD,QAAQ,CAAC,CAAC;EACzE,OAAO,KAAK,CAAC;AACf;;AAEA;AACA,SAASgG,OAAOA,CAACzT,OAAO,EAAE;EACxB,OAAO,CAAC,EAAEA,OAAO,KAAK,IAAI,IAAI,OAAOA,OAAO,KAAK,WAAW,IAAIA,OAAO,CAAC4I,QAAQ,IAAIsI,aAAa,CAAClR,OAAO,CAAC,CAAC;AAC7G;;AAEA;AACA,SAAS0T,gBAAgBA,CAAC3N,MAAM,EAAE4N,cAAc,EAAE;EAChD,IAAI1N,EAAE;EACN,IAAI,CAACF,MAAM,EAAE,OAAO,KAAK,CAAC;EAC1B,QAAQA,MAAM;IACZ,KAAK,UAAU;MACb,OAAO5E,QAAQ;IACjB,KAAK,QAAQ;MACX,OAAOF,MAAM;IACf,KAAK,MAAM;MACT,OAAOE,QAAQ,CAACkB,IAAI;IACtB,KAAK,OAAO;MACV,OAAOsR,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,cAAc,CAAC1B,kBAAkB;IAC5E,KAAK,OAAO;MACV,OAAO0B,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACb,sBAAsB;IAChF,KAAK,SAAS;MACZ,OAAOa,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACC,aAAa;IACvE,KAAK,cAAc;MACjB,OAAO,CAAC3N,EAAE,GAAG0N,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACC,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG3N,EAAE,CAAC2N,aAAa;IAClH;MACE,IAAI,OAAO7N,MAAM,KAAK,QAAQ,EAAE;QAC9B,OAAO5E,QAAQ,CAAC6M,aAAa,CAACjI,MAAM,CAAC;MACvC;MACA,MAAM8N,UAAU,GAAIC,GAAG,IAAK,CAAC,EAAEA,GAAG,IAAIA,GAAG,CAACC,WAAW,IAAID,GAAG,CAACE,IAAI,IAAIF,GAAG,CAACG,KAAK,CAAC;MAC/E,MAAMjU,OAAO,GAAG6I,SAAS,CAACgL,UAAU,CAAC9N,MAAM,CAAC,GAAGA,MAAM,CAAC,CAAC,GAAGA,MAAM,CAAC;MACjE,OAAO,CAAC/F,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC2I,QAAQ,MAAM,CAAC,IAAI8K,OAAO,CAACzT,OAAO,CAAC,GAAGA,OAAO,GAAG,KAAK,CAAC;EACrG;AACF;;AAEA;AACA,SAASkU,YAAYA,CAAA,EAAG;EACtB,OAAO1Q,SAAS,CAAC+K,SAAS;AAC5B;;AAEA;AACA,SAAS4F,QAAQA,CAACnU,OAAO,EAAE;EACzB,IAAIA,OAAO,EAAE;IACX,IAAIuE,KAAK,GAAGvE,OAAO,CAACqB,WAAW;IAC/B,IAAIQ,KAAK,GAAGkG,gBAAgB,CAAC/H,OAAO,CAAC;IACrCuE,KAAK,IAAIyD,UAAU,CAACnG,KAAK,CAACuS,WAAW,CAAC,GAAGpM,UAAU,CAACnG,KAAK,CAACwS,YAAY,CAAC,GAAGrM,UAAU,CAACnG,KAAK,CAACiI,eAAe,CAAC,GAAG9B,UAAU,CAACnG,KAAK,CAACkI,gBAAgB,CAAC;IAChJ,OAAOxF,KAAK;EACd;EACA,OAAO,CAAC;AACV;;AAEA;AACA,SAAS+P,eAAeA,CAACtU,OAAO,EAAE;EAChC,IAAIA,OAAO,EAAE;IACX,MAAM6B,KAAK,GAAGkG,gBAAgB,CAAC/H,OAAO,CAAC;IACvC,MAAMuU,iBAAiB,GAAGvM,UAAU,CAACnG,KAAK,CAACG,gBAAgB,CAAC,oBAAoB,CAAC,IAAI,GAAG,CAAC;IACzF,OAAOuS,iBAAiB,GAAG,CAAC;EAC9B;EACA,OAAO,KAAK;AACd;;AAEA;AACA,SAASC,gBAAgBA,CAACxU,OAAO,EAAE;EACjC,IAAIA,OAAO,EAAE;IACX,MAAM6B,KAAK,GAAGkG,gBAAgB,CAAC/H,OAAO,CAAC;IACvC,MAAMyU,kBAAkB,GAAGzM,UAAU,CAACnG,KAAK,CAACG,gBAAgB,CAAC,qBAAqB,CAAC,IAAI,GAAG,CAAC;IAC3F,OAAOyS,kBAAkB,GAAG,CAAC;EAC/B;EACA,OAAO,KAAK;AACd;;AAEA;AACA,SAASC,mBAAmBA,CAAC1U,OAAO,EAAE2U,UAAU,EAAEC,IAAI,EAAE;EACtD5U,OAAO,CAAC2U,UAAU,CAAC,CAACV,KAAK,CAACjU,OAAO,EAAE4U,IAAI,CAAC;AAC1C;;AAEA;AACA,SAASC,SAASA,CAAA,EAAG;EACnB,OAAO,YAAY,CAACxU,IAAI,CAACmD,SAAS,CAAC+K,SAAS,CAAC;AAC/C;;AAEA;AACA,SAASuG,iBAAiBA,CAAC9U,OAAO,EAAE8B,IAAI,EAAEC,KAAK,EAAE;EAC/C,OAAO2G,SAAS,CAAC1I,OAAO,CAAC,GAAGmO,YAAY,CAACnO,OAAO,EAAE8B,IAAI,CAAC,KAAKC,KAAK,GAAG,KAAK;AAC3E;;AAEA;AACA,SAASgT,oBAAoBA,CAAC/U,OAAO,EAAE8B,IAAI,EAAEC,KAAK,EAAE;EAClD,OAAO,CAAC+S,iBAAiB,CAAC9U,OAAO,EAAE8B,IAAI,EAAEC,KAAK,CAAC;AACjD;;AAEA;AACA,SAASiT,WAAWA,CAAChV,OAAO,EAAE;EAC5B,IAAIA,OAAO,EAAE;IACX,MAAMiV,UAAU,GAAGjV,OAAO,CAAC4I,QAAQ;IACnC,MAAMwI,UAAU,GAAGpR,OAAO,CAAC4T,aAAa,IAAI5T,OAAO,CAAC4T,aAAa,CAAChL,QAAQ;IAC1E,OAAOqM,UAAU,KAAK,OAAO,IAAIA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,QAAQ,IAAIA,UAAU,KAAK,GAAG,IAAI7D,UAAU,KAAK,OAAO,IAAIA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,QAAQ,IAAIA,UAAU,KAAK,GAAG,IAAI,CAAC,CAACpR,OAAO,CAACkV,OAAO,CAAC,wCAAwC,CAAC;EACpR;EACA,OAAO,KAAK;AACd;;AAEA;AACA,SAASC,QAAQA,CAAA,EAAG;EAClB,OAAO,CAAC,EAAE,OAAOlU,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACE,QAAQ,IAAIF,MAAM,CAACE,QAAQ,CAACuB,aAAa,CAAC;AAC9F;;AAEA;AACA,SAAS0S,kBAAkBA,CAACpV,OAAO,EAAE2N,QAAQ,GAAG,EAAE,EAAE;EAClD,OAAOjF,SAAS,CAAC1I,OAAO,CAAC,GAAGA,OAAO,CAAC+N,OAAO,CAAC,2FAA2FJ,QAAQ;AACjJ,iIAAiIA,QAAQ;AACzI,qGAAqGA,QAAQ;AAC7G,sGAAsGA,QAAQ;AAC9G,wGAAwGA,QAAQ;AAChH,0GAA0GA,QAAQ;AAClH,iHAAiHA,QAAQ,EAAE,CAAC,GAAG,KAAK;AACpI;;AAEA;AACA,SAAS0H,SAASA,CAACrV,OAAO,EAAE;EAC1B,OAAO,CAAC,EAAEA,OAAO,IAAIA,OAAO,CAACsG,YAAY,IAAI,IAAI,CAAC;AACpD;;AAEA;AACA,SAASgP,QAAQA,CAACtV,OAAO,EAAE;EACzB,OAAO,CAACqV,SAAS,CAACrV,OAAO,CAAC;AAC5B;;AAEA;AACA,SAASuV,KAAKA,CAAA,EAAG;EACf,OAAO,kBAAkB,CAAClV,IAAI,CAACmD,SAAS,CAAC+K,SAAS,CAAC,IAAI,CAACtN,MAAM,CAAC,UAAU,CAAC;AAC5E;;AAEA;AACA,SAASuU,KAAKA,CAACxV,OAAO,EAAE;EACtB,OAAOA,OAAO,GAAG+H,gBAAgB,CAAC/H,OAAO,CAAC,CAACyV,SAAS,KAAK,KAAK,GAAG,KAAK;AACxE;;AAEA;AACA,SAASC,QAAQA,CAAA,EAAG;EAClB,OAAO,CAACP,QAAQ,CAAC,CAAC;AACpB;;AAEA;AACA,SAASQ,aAAaA,CAAA,EAAG;EACvB,OAAO,cAAc,IAAI1U,MAAM,IAAIuC,SAAS,CAACoS,cAAc,GAAG,CAAC,IAAIpS,SAAS,CAACqS,gBAAgB,GAAG,CAAC;AACnG;;AAEA;AACA,SAASC,cAAcA,CAAC9V,OAAO,EAAE+V,KAAK,EAAE;EACtC,IAAI9P,EAAE,EAAEC,EAAE;EACV,IAAIlG,OAAO,EAAE;IACX,MAAMgW,UAAU,GAAGhW,OAAO,CAAC4T,aAAa;IACxC,MAAMqC,aAAa,GAAG3D,SAAS,CAAC0D,UAAU,CAAC;IAC3C,MAAMjP,QAAQ,GAAGpC,WAAW,CAAC,CAAC;IAC9B,MAAMuR,YAAY,GAAGlW,OAAO,CAACsG,YAAY,GAAGtG,OAAO,CAACqB,WAAW,GAAG2P,0BAA0B,CAAChR,OAAO,CAAC;IACrG,MAAMmW,aAAa,GAAGnW,OAAO,CAACsG,YAAY,GAAGtG,OAAO,CAAC0E,YAAY,GAAGoM,2BAA2B,CAAC9Q,OAAO,CAAC;IACxG,MAAMoW,cAAc,GAAGxO,aAAa,CAAC,CAAC3B,EAAE,GAAG+P,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAChK,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG/F,EAAE,CAAC,CAAC,CAAC,CAAC;IACvH,MAAMoQ,eAAe,GAAG7D,cAAc,CAAC,CAACtM,EAAE,GAAG8P,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAChK,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG9F,EAAE,CAAC,CAAC,CAAC,CAAC;IACzH,IAAIe,IAAI,GAAG,EAAE;IACb,IAAID,GAAG,GAAG,EAAE;IACZ,IAAIiP,aAAa,CAAChP,IAAI,GAAGmP,cAAc,GAAGF,YAAY,GAAGnP,QAAQ,CAACxC,KAAK,GAAGsF,uBAAuB,CAAC,CAAC,EAAE;MACnG,IAAIoM,aAAa,CAAChP,IAAI,GAAGiP,YAAY,EAAE;QACrC,IAAIH,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE;UACnB9O,IAAI,GAAGgP,aAAa,CAAChP,IAAI,GAAG,GAAG,GAAGgP,aAAa,CAAChP,IAAI,GAAG,IAAI,GAAG,MAAM;QACtE,CAAC,MAAM,IAAI8O,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE;UAC1B9O,IAAI,GAAGF,QAAQ,CAACxC,KAAK,GAAG2R,YAAY,GAAGrM,uBAAuB,CAAC,CAAC,GAAG,IAAI;QACzE;MACF,CAAC,MAAM;QACL5C,IAAI,GAAG,OAAO;MAChB;IACF,CAAC,MAAM;MACLA,IAAI,GAAG,MAAM;IACf;IACA,IAAIjH,OAAO,CAAC4G,qBAAqB,CAAC,CAAC,CAACI,GAAG,GAAGqP,eAAe,GAAGF,aAAa,GAAGpP,QAAQ,CAACvC,MAAM,EAAE;MAC3FwC,GAAG,GAAG,IAAImP,aAAa,GAAGE,eAAe,IAAI;IAC/C,CAAC,MAAM;MACLrP,GAAG,GAAG,KAAK;IACb;IACAhH,OAAO,CAAC6B,KAAK,CAACmF,GAAG,GAAGA,GAAG;IACvBhH,OAAO,CAAC6B,KAAK,CAACoF,IAAI,GAAGA,IAAI;EAC3B;AACF;;AAEA;AACA,SAASjD,MAAMA,CAAChE,OAAO,EAAE;EACvB,IAAIiG,EAAE;EACN,IAAIjG,OAAO,EAAE;IACX,IAAI,EAAE,QAAQ,IAAIsW,OAAO,CAACC,SAAS,CAAC,EAAE,CAACtQ,EAAE,GAAGjG,OAAO,CAACoR,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGnL,EAAE,CAAChD,WAAW,CAACjD,OAAO,CAAC,CAAC,KACtGA,OAAO,CAACgE,MAAM,CAAC,CAAC;EACvB;AACF;;AAEA;AACA,SAASf,WAAWA,CAACjD,OAAO,EAAEkJ,KAAK,EAAE;EACnC,MAAMnD,MAAM,GAAG8C,SAAS,CAAC7I,OAAO,CAAC;EACjC,IAAI+F,MAAM,EAAEA,MAAM,CAAC9C,WAAW,CAACiG,KAAK,CAAC,CAAC,KACjC,MAAM,IAAIC,KAAK,CAAC,gBAAgB,GAAGD,KAAK,GAAG,QAAQ,GAAGlJ,OAAO,CAAC;AACrE;;AAEA;AACA,SAASwW,cAAcA,CAACxW,OAAO,EAAE;EAC/B,IAAIiG,EAAE;EACN,IAAIwN,OAAO,CAACzT,OAAO,CAAC,EAAE;IACpB,IAAI;MACF,CAACiG,EAAE,GAAGjG,OAAO,CAACoR,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGnL,EAAE,CAAChD,WAAW,CAACjD,OAAO,CAAC;IACtE,CAAC,CAAC,OAAOyW,KAAK,EAAE,CAChB;IACA,OAAO,IAAI;EACb;EACA,OAAOzW,OAAO;AAChB;;AAEA;AACA,SAAS0W,YAAYA,CAAClK,SAAS,EAAEmK,IAAI,EAAE;EACrC,IAAIC,cAAc,GAAG7O,gBAAgB,CAACyE,SAAS,CAAC,CAACxK,gBAAgB,CAAC,gBAAgB,CAAC;EACnF,IAAI6U,SAAS,GAAGD,cAAc,GAAG5O,UAAU,CAAC4O,cAAc,CAAC,GAAG,CAAC;EAC/D,IAAIE,eAAe,GAAG/O,gBAAgB,CAACyE,SAAS,CAAC,CAACxK,gBAAgB,CAAC,YAAY,CAAC;EAChF,IAAI4O,UAAU,GAAGkG,eAAe,GAAG9O,UAAU,CAAC8O,eAAe,CAAC,GAAG,CAAC;EAClE,IAAIC,aAAa,GAAGvK,SAAS,CAAC5F,qBAAqB,CAAC,CAAC;EACrD,IAAIoQ,QAAQ,GAAGL,IAAI,CAAC/P,qBAAqB,CAAC,CAAC;EAC3C,IAAIqQ,MAAM,GAAGD,QAAQ,CAAChQ,GAAG,GAAG7F,QAAQ,CAACkB,IAAI,CAACuD,SAAS,IAAImR,aAAa,CAAC/P,GAAG,GAAG7F,QAAQ,CAACkB,IAAI,CAACuD,SAAS,CAAC,GAAGiR,SAAS,GAAGjG,UAAU;EAC5H,IAAIsG,MAAM,GAAG1K,SAAS,CAAC5G,SAAS;EAChC,IAAImL,aAAa,GAAGvE,SAAS,CAACpH,YAAY;EAC1C,IAAI+R,UAAU,GAAG3E,cAAc,CAACmE,IAAI,CAAC;EACrC,IAAIM,MAAM,GAAG,CAAC,EAAE;IACdzK,SAAS,CAAC5G,SAAS,GAAGsR,MAAM,GAAGD,MAAM;EACvC,CAAC,MAAM,IAAIA,MAAM,GAAGE,UAAU,GAAGpG,aAAa,EAAE;IAC9CvE,SAAS,CAAC5G,SAAS,GAAGsR,MAAM,GAAGD,MAAM,GAAGlG,aAAa,GAAGoG,UAAU;EACpE;AACF;;AAEA;AACA,SAAStU,YAAYA,CAAC7C,OAAO,EAAEoX,SAAS,GAAG,EAAE,EAAErV,KAAK,EAAE;EACpD,IAAI2G,SAAS,CAAC1I,OAAO,CAAC,IAAI+B,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;IAC5D/B,OAAO,CAAC6C,YAAY,CAACuU,SAAS,EAAErV,KAAK,CAAC;EACxC;AACF;;AAEA;AACA,SAASsV,cAAcA,CAACrX,OAAO,EAAE4B,QAAQ,EAAEG,KAAK,GAAG,IAAI,EAAEuV,QAAQ,EAAE;EACjE,IAAIrR,EAAE;EACNrE,QAAQ,KAAK,CAACqE,EAAE,GAAGjG,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC6B,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGoE,EAAE,CAAC3D,WAAW,CAACV,QAAQ,EAAEG,KAAK,EAAEuV,QAAQ,CAAC,CAAC;AAC5H;AACA,SACExR,gBAAgB,EAChBxF,QAAQ,EACRiH,QAAQ,EACRc,YAAY,EACZtF,WAAW,EACXZ,eAAe,EACfnB,2BAA2B,EAC3BqI,wBAAwB,EACxBQ,uBAAuB,EACvBI,cAAc,EACdvH,aAAa,EACbwJ,mBAAmB,EACnBK,cAAc,EACdrJ,SAAS,EACTwJ,MAAM,EACNS,OAAO,EACPO,IAAI,EACJI,UAAU,EACVG,KAAK,EACLE,YAAY,EACZS,UAAU,EACVE,kBAAkB,EAClBG,cAAc,EACd3N,qBAAqB,EACrB6N,eAAe,EACfuB,wBAAwB,EACxBJ,oBAAoB,EACpBK,SAAS,EACTtM,0BAA0B,EAC1ByM,2BAA2B,EAC3BE,0BAA0B,EAC1BO,QAAQ,EACRI,aAAa,EACbG,uBAAuB,EACvBC,qBAAqB,EACrBG,uBAAuB,EACvBI,SAAS,EACTE,cAAc,EACd5K,aAAa,EACbsJ,aAAa,EACbwB,UAAU,EACVE,yBAAyB,EACzBG,oBAAoB,EACpB7I,YAAY,EACZwJ,gBAAgB,EAChBQ,YAAY,EACZvP,WAAW,EACXwP,QAAQ,EACR9O,mBAAmB,EACnBK,kBAAkB,EAClB4O,eAAe,EACfE,gBAAgB,EAChBzU,QAAQ,EACR2U,mBAAmB,EACnBG,SAAS,EACTC,iBAAiB,EACjBC,oBAAoB,EACpBC,WAAW,EACXG,QAAQ,EACRzM,SAAS,EACT+K,OAAO,EACP2B,kBAAkB,EAClBE,QAAQ,EACRC,KAAK,EACLC,KAAK,EACLE,QAAQ,EACRC,aAAa,EACbN,SAAS,EACTS,cAAc,EACd3N,gBAAgB,EAChBnE,MAAM,EACNf,WAAW,EACXc,WAAW,EACXyS,cAAc,EACdnI,gBAAgB,EAChB9L,MAAM,EACNmU,YAAY,EACZ7T,YAAY,EACZ6H,aAAa,EACb2M,cAAc,EACdxO,SAAS,EACT1E,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}