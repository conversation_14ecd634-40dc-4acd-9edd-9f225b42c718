/** @type {import('tailwindcss').Config} */
module.exports = {
	content: ['./src/**/*.{html,ts,scss}'],
	darkMode: 'class',
	theme: {
		extend: {
			backgroundImage: {},
			container: {
				center: true,
				padding: {
					'sm': '24px',
					'lg': '32px',
					'xl': '48px',
					'2xl': '0',
				},
			},
			fontSize: {
				11: '11px',
				12: '12px',
				13: '13px',
				14: '14px',
				15: '15px',
				16: '16px',
				17: '17px',
				18: '18px',
				19: '19px',
				20: '20px',
			},
			borderRadius: {
				none: '0',
				xs: '2px',
				sm: '4px',
				md: '6px',
				lg: '8px',
				xl: '12px',
			},
			screens: {
				'sm': '640px',
				'md': '768px',
				'lg': '1024px',
				'xl': '1280px',
				'2xl': '1440px',
			},
			colors: {},
			flex: {
				'1-0-auto': '1 0 auto',
			},
			fontFamily: {
				inter: ['Inter', 'sans-serif'],
				roboto: ['Roboto', 'sans-serif'],
			},
		},
	},
	plugins: [
		require('tailwindcss-primeui'),
		function ({ addBase, addComponents }) {
			addBase({
				':root': {},
				'.dark': {},
				'html, body': {
					// 'color': '#020617',
					backgroundColor: '#ECF1F5',
					fontFamily: 'Inter, sans-serif',
					fontSize: '14px',
					minHeight: '100%',
				},
			});

			addComponents({});
		},
	],
	variants: {
		extend: {},
	},
};
