"""
Módulo para captura de screenshots usando Playwright

Este módulo captura screenshots automatizados de websites em:
- Resolução desktop (1920x1080)
- Resolução mobile (375x667)
- Múltiplos browsers (Chromium, Firefox, Safari)
"""

import os
import re
from datetime import datetime, timezone
from typing import Dict, Any, Optional
from playwright.sync_api import sync_playwright
import uuid


class PlaywrightScreenshots:
    """
    Capturador de screenshots usando Playwright

    Captura screenshots automatizados de websites em diferentes resoluções
    e dispositivos para análise visual posterior.
    """

    def __init__(self, screenshots_dir: str = "screenshots"):
        """
        Inicializa o capturador de screenshots

        Args:
            screenshots_dir: Diretório para salvar screenshots
        """
        self.screenshots_dir = screenshots_dir
        self.desktop_viewport = {"width": 1920, "height": 1080}
        self.mobile_viewport = {"width": 375, "height": 667}

        # Criar diretório se não existir
        os.makedirs(self.screenshots_dir, exist_ok=True)

    def capture_screenshots(self, url: str, browser_type: str = "chromium") -> Dict[str, Any]:
        """
        Captura screenshots desktop e mobile de uma URL

        Args:
            url: URL do website para captura
            browser_type: Tipo de browser ("chromium", "firefox", "webkit")

        Returns:
            Dict contendo paths dos screenshots e metadata

        Raises:
            Exception: Se a URL for inválida ou houver erro na captura
        """
        if not self._is_valid_url(url):
            raise Exception(f"URL inválida: {url}")

        try:
            with sync_playwright() as p:
                # Selecionar browser
                browser_launcher = getattr(p, browser_type)
                browser = browser_launcher.launch(headless=True)

                try:
                    # Capturar screenshot desktop
                    desktop_path = self._capture_desktop_screenshot(
                        browser, url)

                    # Capturar screenshot mobile
                    mobile_path = self._capture_mobile_screenshot(browser, url)

                    # Gerar metadata
                    metadata = self._generate_metadata(url, browser_type)

                    return {
                        "desktop": desktop_path,
                        "mobile": mobile_path,
                        "metadata": metadata
                    }

                finally:
                    browser.close()

        except Exception as e:
            raise Exception(f"Erro na captura de screenshots: {str(e)}")

    def _is_valid_url(self, url: str) -> bool:
        """
        Valida se a URL está em formato correto

        Args:
            url: URL para validação

        Returns:
            True se válida, False caso contrário
        """
        if not url or not isinstance(url, str):
            return False

        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            # domain...
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)

        return bool(url_pattern.match(url))

    def _capture_desktop_screenshot(self, browser, url: str) -> str:
        """
        Captura screenshot desktop

        Args:
            browser: Instância do browser Playwright
            url: URL para captura

        Returns:
            Path do arquivo de screenshot desktop
        """
        try:
            page = browser.new_page()
            page.set_viewport_size(self.desktop_viewport)

            # Navegar para a URL
            page.goto(url, wait_until="networkidle", timeout=30000)

            # Aguardar carregamento adicional
            page.wait_for_timeout(2000)

            # Gerar nome único para o arquivo
            filename = f"desktop_{self._generate_filename(url)}"
            filepath = os.path.join(self.screenshots_dir, filename)

            # Capturar screenshot full page
            page.screenshot(
                path=filepath,
                full_page=True
            )

            page.close()
            return filepath

        except Exception as e:
            raise Exception(f"Erro na captura desktop: {str(e)}")

    def _capture_mobile_screenshot(self, browser, url: str) -> str:
        """
        Captura screenshot mobile

        Args:
            browser: Instância do browser Playwright
            url: URL para captura

        Returns:
            Path do arquivo de screenshot mobile
        """
        try:
            context = browser.new_context(
                viewport=self.mobile_viewport,
                user_agent="Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1"
            )

            page = context.new_page()

            # Navegar para a URL
            page.goto(url, wait_until="networkidle", timeout=30000)

            # Aguardar carregamento adicional
            page.wait_for_timeout(2000)

            # Gerar nome único para o arquivo
            filename = f"mobile_{self._generate_filename(url)}"
            filepath = os.path.join(self.screenshots_dir, filename)

            # Capturar screenshot mobile completo
            page.screenshot(
                path=filepath,
                full_page=True
            )

            context.close()
            return filepath

        except Exception as e:
            raise Exception(f"Erro na captura mobile: {str(e)}")

    def _generate_filename(self, url: str) -> str:
        """
        Gera nome único para arquivo de screenshot

        Args:
            url: URL base para o nome

        Returns:
            Nome de arquivo único
        """
        try:
            # Extrair domínio da URL
            domain = url.replace("https://", "").replace("http://", "")
            domain = domain.split("/")[0]
            domain = re.sub(r'[^\w\-.]', '_', domain)

            # Adicionar timestamp e UUID para unicidade
            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
            unique_id = str(uuid.uuid4())[:8]

            return f"{domain}_{timestamp}_{unique_id}.png"

        except Exception:
            # Fallback para nome genérico
            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
            unique_id = str(uuid.uuid4())[:8]
            return f"screenshot_{timestamp}_{unique_id}.png"

    def _generate_metadata(self, url: str, browser_type: str) -> Dict[str, Any]:
        """
        Gera metadata para os screenshots

        Args:
            url: URL capturada
            browser_type: Tipo de browser usado

        Returns:
            Dict com metadata dos screenshots
        """
        return {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "url": url,
            "browser_type": browser_type,
            "desktop_viewport": self.desktop_viewport,
            "mobile_viewport": self.mobile_viewport,
            "capture_settings": {
                "full_page": True,
                "quality": 85,
                "wait_until": "networkidle",
                "timeout": 30000,
                "additional_wait": 2000
            }
        }

    def capture_multiple_browsers(self, url: str) -> Dict[str, Any]:
        """
        Captura screenshots em múltiplos browsers

        Args:
            url: URL para captura

        Returns:
            Dict com screenshots de diferentes browsers
        """
        if not self._is_valid_url(url):
            raise Exception(f"URL inválida: {url}")

        browsers = ["chromium", "firefox", "webkit"]
        results = {}

        for browser_type in browsers:
            try:
                result = self.capture_screenshots(url, browser_type)
                results[browser_type] = result
            except Exception as e:
                results[browser_type] = {"error": str(e)}

        return results

    def cleanup_old_screenshots(self, days: int = 7):
        """
        Remove screenshots antigos

        Args:
            days: Número de dias para manter screenshots
        """
        try:
            import time
            current_time = time.time()
            cutoff_time = current_time - (days * 24 * 60 * 60)

            for filename in os.listdir(self.screenshots_dir):
                filepath = os.path.join(self.screenshots_dir, filename)
                if os.path.isfile(filepath):
                    file_time = os.path.getmtime(filepath)
                    if file_time < cutoff_time:
                        os.remove(filepath)

        except Exception:
            # Ignorar erros na limpeza
            pass
