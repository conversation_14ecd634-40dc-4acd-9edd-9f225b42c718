"""
Testes de Validação do TaskManager - ScopeAI MT-5
Testes específicos para o sistema de background tasks implementado no MT-4.
"""

import pytest
import asyncio
import time
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, UTC


class TestTaskManagerValidation:
    """Testes para validar o TaskManager implementado no MT-4"""

    @pytest.fixture
    def setup_task_data(self):
        """Setup para dados de teste de tasks"""
        return {
            "simple_task_data": "test_data",
            "client_id": "test_client_123",
            "client_name": "Test Client",
            "project_id": "test_project_456"
        }

    # ====================================================================
    # TESTES BÁSICOS DO TASK MANAGER
    # ====================================================================

    @pytest.mark.asyncio
    async def test_task_manager_import_and_creation(self, setup_task_data):
        """Testa importação e criação básica do TaskManager"""
        try:
            from shared.task_manager import (
                TaskManager, TaskPriority, TaskStatus, TaskInfo,
                create_background_task, get_task_status, task_manager
            )
            
            # Verificar classes e enums
            assert TaskPriority is not None
            assert TaskStatus is not None
            assert TaskInfo is not None
            assert task_manager is not None
            
            # Verificar valores dos enums
            assert hasattr(TaskPriority, 'CRITICAL')
            assert hasattr(TaskPriority, 'HIGH') 
            assert hasattr(TaskPriority, 'MEDIUM')
            assert hasattr(TaskPriority, 'LOW')
            
            assert hasattr(TaskStatus, 'PENDING')
            assert hasattr(TaskStatus, 'RUNNING')
            assert hasattr(TaskStatus, 'COMPLETED')
            assert hasattr(TaskStatus, 'FAILED')
            assert hasattr(TaskStatus, 'CANCELLED')
            
            print("✅ TaskManager importado e estruturas validadas")
            
        except ImportError as e:
            pytest.skip(f"TaskManager não disponível: {e}")

    @pytest.mark.asyncio
    async def test_create_simple_background_task(self, setup_task_data):
        """Testa criação de uma task simples"""
        try:
            from shared.task_manager import create_background_task, TaskPriority
            
            # Função de teste simples
            async def simple_test_task(data):
                await asyncio.sleep(0.1)
                return {"result": f"processed_{data}"}
            
            # Criar task
            task_id = await create_background_task(
                "simple_test",
                simple_test_task,
                TaskPriority.MEDIUM,
                setup_task_data["client_id"],
                setup_task_data["client_name"],
                1,
                setup_task_data["simple_task_data"]
            )
            
            assert task_id is not None
            assert isinstance(task_id, str)
            assert len(task_id) > 10  # UUID deve ter comprimento adequado
            
            print(f"✅ Task criada com ID: {task_id}")
            
        except ImportError:
            pytest.skip("TaskManager não disponível")

    @pytest.mark.asyncio
    async def test_task_status_tracking(self, setup_task_data):
        """Testa tracking de status das tasks"""
        try:
            from shared.task_manager import (
                create_background_task, get_task_status, 
                TaskPriority, TaskStatus
            )
            
            # Função que demora um pouco
            async def tracked_task(data):
                await asyncio.sleep(0.3)
                return {"tracked": data}
            
            # Criar task
            task_id = await create_background_task(
                "tracked_test",
                tracked_task,
                TaskPriority.HIGH,
                setup_task_data["client_id"],
                setup_task_data["client_name"],
                1,
                "tracking_data"
            )
            
            # Verificar status inicial
            task_info = await get_task_status(task_id)
            if task_info:
                assert task_info.task_id == task_id
                assert task_info.task_type == "tracked_test"
                assert task_info.client_id == setup_task_data["client_id"]
                assert task_info.client_name == setup_task_data["client_name"]
                
                # Status deve ser PENDING ou RUNNING
                assert task_info.status in [TaskStatus.PENDING, TaskStatus.RUNNING]
                
                print(f"✅ Status inicial: {task_info.status.value}")
                
                # Aguardar conclusão
                await asyncio.sleep(0.5)
                
                # Verificar status final
                final_task_info = await get_task_status(task_id)
                if final_task_info:
                    assert final_task_info.status in [TaskStatus.COMPLETED, TaskStatus.FAILED]
                    print(f"✅ Status final: {final_task_info.status.value}")
            
        except ImportError:
            pytest.skip("TaskManager não disponível")

    @pytest.mark.asyncio
    async def test_task_priorities(self, setup_task_data):
        """Testa sistema de prioridades das tasks"""
        try:
            from shared.task_manager import create_background_task, TaskPriority
            
            async def priority_task(priority_name):
                await asyncio.sleep(0.1)
                return {"priority": priority_name}
            
            # Criar tasks com diferentes prioridades
            priorities = [
                (TaskPriority.CRITICAL, "critical"),
                (TaskPriority.HIGH, "high"),
                (TaskPriority.MEDIUM, "medium"),
                (TaskPriority.LOW, "low")
            ]
            
            task_ids = []
            for priority, name in priorities:
                task_id = await create_background_task(
                    f"priority_test_{name}",
                    priority_task,
                    priority,
                    setup_task_data["client_id"],
                    setup_task_data["client_name"],
                    1,
                    name
                )
                task_ids.append((task_id, priority, name))
            
            assert len(task_ids) == 4
            print(f"✅ Criadas {len(task_ids)} tasks com diferentes prioridades")
            
            # Aguardar processamento
            await asyncio.sleep(0.5)
            
        except ImportError:
            pytest.skip("TaskManager não disponível")

    # ====================================================================
    # TESTES DE CONCORRÊNCIA
    # ====================================================================

    @pytest.mark.asyncio
    async def test_concurrent_task_execution(self, setup_task_data):
        """Testa execução concorrente de múltiplas tasks"""
        try:
            from shared.task_manager import create_background_task, TaskPriority
            
            async def concurrent_task(task_number):
                await asyncio.sleep(0.1)
                return {"task_number": task_number}
            
            # Criar múltiplas tasks simultâneas
            start_time = time.time()
            task_ids = []
            
            for i in range(5):
                task_id = await create_background_task(
                    f"concurrent_test_{i}",
                    concurrent_task,
                    TaskPriority.MEDIUM,
                    f"client_{i}",
                    f"Client {i}",
                    1,
                    i
                )
                task_ids.append(task_id)
            
            await asyncio.sleep(0.5)
            total_time = time.time() - start_time
            
            assert len(task_ids) == 5
            assert total_time < 2.0, f"Execução concorrente muito lenta: {total_time}s"
            
            print(f"✅ 5 tasks concorrentes executadas em {total_time:.3f}s")
            
        except ImportError:
            pytest.skip("TaskManager não disponível")

    @pytest.mark.asyncio
    async def test_resource_pool_utilization(self, setup_task_data):
        """Testa utilização dos pools de recursos"""
        try:
            from shared.task_manager import create_background_task, TaskPriority, task_manager
            
            async def resource_intensive_task(pool_type):
                # Simular diferentes tipos de operação
                if pool_type == "analysis":
                    await asyncio.sleep(0.15)  # Análise complexa
                elif pool_type == "report":
                    await asyncio.sleep(0.10)  # Geração de relatório
                elif pool_type == "pdf":
                    await asyncio.sleep(0.12)  # Processamento PDF
                else:
                    await asyncio.sleep(0.08)  # Operação geral
                
                return {"pool": pool_type, "completed": True}
            
            # Criar tasks para diferentes pools
            pool_tasks = [
                ("complete_report", "analysis"),
                ("markdown_report", "report"),
                ("pdf_generation", "pdf"),
                ("general_task", "general")
            ]
            
            task_ids = []
            for task_type, pool_type in pool_tasks:
                task_id = await create_background_task(
                    task_type,
                    resource_intensive_task,
                    TaskPriority.MEDIUM,
                    setup_task_data["client_id"],
                    setup_task_data["client_name"],
                    1,
                    pool_type
                )
                task_ids.append(task_id)
            
            # Aguardar processamento
            await asyncio.sleep(0.5)
            
            # Verificar estatísticas do sistema
            stats = task_manager.get_stats()
            assert "resource_usage" in stats
            
            print(f"✅ Resource pools utilizados: {stats.get('resource_usage', {})}")
            
        except ImportError:
            pytest.skip("TaskManager não disponível")

    # ====================================================================
    # TESTES DE ERROR HANDLING
    # ====================================================================

    @pytest.mark.asyncio
    async def test_task_error_handling(self, setup_task_data):
        """Testa tratamento de erros em tasks"""
        try:
            from shared.task_manager import (
                create_background_task, get_task_status, 
                TaskPriority, TaskStatus
            )
            
            async def failing_task(error_type):
                await asyncio.sleep(0.1)
                if error_type == "value_error":
                    raise ValueError("Erro de valor intencional")
                elif error_type == "type_error":
                    raise TypeError("Erro de tipo intencional")
                else:
                    raise Exception("Erro genérico intencional")
            
            # Criar task que vai falhar
            task_id = await create_background_task(
                "failing_test",
                failing_task,
                TaskPriority.MEDIUM,
                setup_task_data["client_id"],
                setup_task_data["client_name"],
                1,
                "value_error"
            )
            
            # Aguardar conclusão
            await asyncio.sleep(0.3)
            
            # Verificar que task falhou apropriadamente
            task_info = await get_task_status(task_id)
            if task_info:
                assert task_info.status == TaskStatus.FAILED
                assert task_info.error_message is not None
                assert "Erro de valor intencional" in task_info.error_message
                
                print(f"✅ Error handling: {task_info.error_message}")
            
        except ImportError:
            pytest.skip("TaskManager não disponível")

    @pytest.mark.asyncio
    async def test_task_cancellation(self, setup_task_data):
        """Testa cancelamento de tasks"""
        try:
            from shared.task_manager import (
                create_background_task, task_manager, 
                TaskPriority, TaskStatus
            )
            
            async def long_running_task(data):
                await asyncio.sleep(2.0)  # Task longa
                return {"completed": data}
            
            # Criar task longa
            task_id = await create_background_task(
                "long_test",
                long_running_task,
                TaskPriority.LOW,
                setup_task_data["client_id"],
                setup_task_data["client_name"],
                3,
                "long_data"
            )
            
            # Tentar cancelar (se task ainda estiver pendente)
            cancelled = await task_manager.cancel_task(task_id)
            
            if cancelled:
                task_info = await task_manager.get_task_info(task_id)
                if task_info:
                    assert task_info.status == TaskStatus.CANCELLED
                    print("✅ Task cancelada com sucesso")
            else:
                print("ℹ️ Task já estava em execução, não pode ser cancelada")
            
        except ImportError:
            pytest.skip("TaskManager não disponível")

    # ====================================================================
    # TESTES DE PERFORMANCE E ESTATÍSTICAS
    # ====================================================================

    @pytest.mark.asyncio
    async def test_system_statistics(self, setup_task_data):
        """Testa coleta de estatísticas do sistema"""
        try:
            from shared.task_manager import task_manager
            
            # Obter estatísticas
            stats = task_manager.get_stats()
            
            # Verificar estrutura das estatísticas
            expected_keys = [
                'total_tasks', 'completed_tasks', 'failed_tasks', 
                'cancelled_tasks', 'running_tasks', 'max_concurrent'
            ]
            
            for key in expected_keys:
                assert key in stats, f"Estatística '{key}' não encontrada"
                assert isinstance(stats[key], int), f"Estatística '{key}' deve ser int"
            
            # Verificar resource usage se disponível
            if 'resource_usage' in stats:
                assert isinstance(stats['resource_usage'], dict)
            
            print(f"✅ Estatísticas do sistema: {stats}")
            
        except ImportError:
            pytest.skip("TaskManager não disponível")

    @pytest.mark.asyncio
    async def test_performance_benchmarks(self, setup_task_data):
        """Testa benchmarks de performance do TaskManager"""
        try:
            from shared.task_manager import create_background_task, TaskPriority
            
            async def benchmark_task(data):
                # Simular operação típica
                await asyncio.sleep(0.05)
                result = []
                for i in range(10):
                    result.append({"item": i, "data": data})
                return result
            
            # Benchmark: criar muitas tasks rapidamente
            start_time = time.time()
            
            task_ids = []
            for i in range(20):
                task_id = await create_background_task(
                    f"benchmark_{i}",
                    benchmark_task,
                    TaskPriority.MEDIUM,
                    f"bench_client_{i}",
                    f"Bench Client {i}",
                    1,
                    f"data_{i}"
                )
                task_ids.append(task_id)
            
            creation_time = time.time() - start_time
            
            # Aguardar conclusão
            await asyncio.sleep(2.0)
            
            total_time = time.time() - start_time
            
            assert len(task_ids) == 20
            assert creation_time < 1.0, f"Criação de tasks muito lenta: {creation_time}s"
            assert total_time < 3.0, f"Execução total muito lenta: {total_time}s"
            
            print(f"📊 Benchmark: 20 tasks criadas em {creation_time:.3f}s, total: {total_time:.3f}s")
            
        except ImportError:
            pytest.skip("TaskManager não disponível")

    # ====================================================================
    # TESTES DE INTEGRAÇÃO COM SISTEMA EXISTENTE
    # ====================================================================

    @pytest.mark.asyncio
    async def test_integration_with_existing_endpoints(self, setup_task_data):
        """Testa integração com endpoints existentes (simulação)"""
        try:
            from shared.task_manager import create_background_task, TaskPriority
            
            # Simular endpoint que usa TaskManager
            async def simulate_complete_report_endpoint(client_id, client_name):
                # Simular função processar_relatorio_completo
                async def mock_processar_relatorio_completo(client_id, nome, site, cidade, estado):
                    await asyncio.sleep(0.2)
                    return {
                        "client_id": client_id,
                        "report_generated": True,
                        "timestamp": datetime.now(UTC).isoformat()
                    }
                
                # Criar task como o endpoint real faria
                task_id = await create_background_task(
                    "complete_report",
                    mock_processar_relatorio_completo,
                    TaskPriority.HIGH,
                    client_id,
                    client_name,
                    600,  # 10 minutos estimados
                    client_id,
                    client_name,
                    "https://test.com",
                    "São Paulo",
                    "SP"
                )
                
                return {
                    "message": "Relatório completo foi solicitado e está sendo processado",
                    "client_id": client_id,
                    "task_id": task_id,
                    "status": "processing_started"
                }
            
            # Testar simulação do endpoint
            result = await simulate_complete_report_endpoint(
                setup_task_data["client_id"],
                setup_task_data["client_name"]
            )
            
            assert result["status"] == "processing_started"
            assert "task_id" in result
            assert result["client_id"] == setup_task_data["client_id"]
            
            print(f"✅ Integração simulada: {result['task_id']}")
            
        except ImportError:
            pytest.skip("TaskManager não disponível")

    @pytest.mark.asyncio
    async def test_websocket_notifications_simulation(self, setup_task_data):
        """Testa simulação de notificações WebSocket"""
        try:
            from shared.task_manager import create_background_task, TaskPriority
            
            # Lista para capturar "notificações"
            notifications = []
            
            # Mock do WebSocket manager
            class MockWebSocketManager:
                async def broadcast(self, message):
                    notifications.append(message)
            
            async def notifying_task(data):
                await asyncio.sleep(0.1)
                return {"notification_test": data}
            
            # Criar task (o sistema real enviaria notificações)
            task_id = await create_background_task(
                "notification_test",
                notifying_task,
                TaskPriority.MEDIUM,
                setup_task_data["client_id"],
                setup_task_data["client_name"],
                1,
                "notification_data"
            )
            
            # Aguardar processamento
            await asyncio.sleep(0.3)
            
            # Em um sistema real, verificaríamos as notificações WebSocket
            # Aqui apenas validamos que a task foi criada
            assert task_id is not None
            print(f"✅ Task com notificações criada: {task_id}")
            
        except ImportError:
            pytest.skip("TaskManager não disponível")


# =====================================================================
# TESTES DE ROBUSTEZ E EDGE CASES
# =====================================================================

class TestTaskManagerRobustness:
    """Testes de robustez e edge cases do TaskManager"""

    @pytest.mark.asyncio
    async def test_high_load_handling(self):
        """Testa comportamento sob alta carga"""
        try:
            from shared.task_manager import create_background_task, TaskPriority
            
            async def quick_task(task_id):
                await asyncio.sleep(0.01)
                return f"quick_{task_id}"
            
            # Criar muitas tasks rapidamente
            task_count = 50
            start_time = time.time()
            
            task_ids = []
            for i in range(task_count):
                task_id = await create_background_task(
                    f"load_test_{i}",
                    quick_task,
                    TaskPriority.MEDIUM,
                    f"load_client_{i}",
                    f"Load Client {i}",
                    1,
                    i
                )
                task_ids.append(task_id)
            
            elapsed = time.time() - start_time
            
            assert len(task_ids) == task_count
            assert elapsed < 5.0, f"Criação de {task_count} tasks muito lenta: {elapsed}s"
            
            # Aguardar processamento
            await asyncio.sleep(2.0)
            
            print(f"📊 High load test: {task_count} tasks em {elapsed:.3f}s")
            
        except ImportError:
            pytest.skip("TaskManager não disponível")

    @pytest.mark.asyncio
    async def test_memory_efficiency_under_load(self):
        """Testa eficiência de memória sob carga"""
        try:
            import gc
            import tracemalloc
            
            from shared.task_manager import create_background_task, TaskPriority
            
            tracemalloc.start()
            gc.collect()
            
            async def memory_task(data):
                # Simular operação que usa memória
                temp_data = [i for i in range(100)]
                await asyncio.sleep(0.01)
                return {"processed": len(temp_data)}
            
            # Criar tasks intensivas em memória
            for i in range(30):
                await create_background_task(
                    f"memory_test_{i}",
                    memory_task,
                    TaskPriority.LOW,
                    f"mem_client_{i}",
                    f"Memory Client {i}",
                    1,
                    f"mem_data_{i}"
                )
            
            await asyncio.sleep(1.0)
            
            current, peak = tracemalloc.get_traced_memory()
            tracemalloc.stop()
            
            # Verificar que memória não explodiu (limite: 50MB)
            assert peak < 50 * 1024 * 1024, f"Uso de memória muito alto: {peak / 1024 / 1024:.2f} MB"
            
            print(f"📊 Memória pico: {peak / 1024 / 1024:.2f} MB")
            
        except ImportError:
            pytest.skip("TaskManager não disponível")


# =====================================================================
# CONFIGURAÇÃO E UTILITÁRIOS
# =====================================================================

def pytest_configure(config):
    """Configuração do pytest para testes do TaskManager"""
    config.addinivalue_line("markers", "asyncio: mark test as async")
    config.addinivalue_line("markers", "slow: mark test as slow")


if __name__ == "__main__":
    # Executar testes diretamente
    import sys
    sys.exit(pytest.main([__file__, "-v", "--tb=short"])) 