#!/usr/bin/env python3
"""
Update Prioritizer para Enriquecimento Contínuo

Implementa algoritmos de priorização inteligente para determinar
quais empresas e tipos de dados devem ser atualizados primeiro.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import math

# Configurar logging
logger = logging.getLogger(__name__)


class PriorityFactor(Enum):
    """Fatores que influenciam a priorização"""
    DATA_AGE = "data_age"                    # Idade dos dados
    DATA_QUALITY = "data_quality"            # Qualidade dos dados
    BUSINESS_IMPORTANCE = "business_importance"  # Importância do negócio
    UPDATE_FREQUENCY = "update_frequency"    # Frequência de atualização
    MARKET_ACTIVITY = "market_activity"      # Atividade no mercado
    COMPANY_SIZE = "company_size"            # Tamanho da empresa
    SECTOR_VOLATILITY = "sector_volatility"  # Volatilidade do setor


@dataclass
class PriorityScore:
    """Score de prioridade calculado"""
    company_id: str
    company_name: str
    total_score: float
    factor_scores: Dict[PriorityFactor, float]
    priority_rank: Optional[int] = None
    justification: str = ""


@dataclass
class PrioritizationConfig:
    """Configuração do algoritmo de priorização"""
    factor_weights: Dict[PriorityFactor, float]
    max_companies_per_batch: int = 10
    quality_threshold: float = 0.5
    age_threshold_days: int = 30
    enabled_factors: Optional[List[PriorityFactor]] = None

    def __post_init__(self):
        if self.enabled_factors is None:
            self.enabled_factors = list(PriorityFactor)


class UpdatePrioritizer:
    """
    Priorizador de Atualizações Inteligente

    Calcula scores de prioridade para empresas baseado em múltiplos
    fatores e algoritmos de machine learning simples.
    """

    def __init__(self, config: Optional[PrioritizationConfig] = None):
        """
        Inicializa o priorizador

        Args:
            config: Configuração personalizada
        """
        if config is None:
            config = self._get_default_config()

        self.config = config
        self.priority_history: List[PriorityScore] = []

        # Pesos padrão baseados em experiência prática
        self.default_factor_calculators = {
            PriorityFactor.DATA_AGE: self._calculate_age_score,
            PriorityFactor.DATA_QUALITY: self._calculate_quality_score,
            PriorityFactor.BUSINESS_IMPORTANCE: self._calculate_importance_score,
            PriorityFactor.UPDATE_FREQUENCY: self._calculate_frequency_score,
            PriorityFactor.MARKET_ACTIVITY: self._calculate_market_activity_score,
            PriorityFactor.COMPANY_SIZE: self._calculate_company_size_score,
            PriorityFactor.SECTOR_VOLATILITY: self._calculate_sector_volatility_score
        }

    def _get_default_config(self) -> PrioritizationConfig:
        """
        Retorna configuração padrão

        Returns:
            Configuração padrão do priorizador
        """
        return PrioritizationConfig(
            factor_weights={
                PriorityFactor.DATA_AGE: 0.3,
                PriorityFactor.DATA_QUALITY: 0.25,
                PriorityFactor.BUSINESS_IMPORTANCE: 0.2,
                PriorityFactor.UPDATE_FREQUENCY: 0.1,
                PriorityFactor.MARKET_ACTIVITY: 0.05,
                PriorityFactor.COMPANY_SIZE: 0.05,
                PriorityFactor.SECTOR_VOLATILITY: 0.05
            },
            max_companies_per_batch=10,
            quality_threshold=0.5,
            age_threshold_days=30
        )

    def calculate_priorities(self, companies_data: List[Dict[str, Any]]) -> List[PriorityScore]:
        """
        Calcula prioridades para lista de empresas

        Args:
            companies_data: Lista de dados das empresas

        Returns:
            Lista de scores de prioridade ordenada
        """
        logger.info(
            f"Calculando prioridades para {len(companies_data)} empresas")

        priority_scores = []

        for company_data in companies_data:
            try:
                score = self._calculate_company_priority(company_data)
                priority_scores.append(score)
            except Exception as e:
                logger.error(
                    f"Erro ao calcular prioridade para empresa {company_data.get('_id')}: {e}")
                continue

        # Ordenar por score total (maior primeiro)
        priority_scores.sort(key=lambda x: x.total_score, reverse=True)

        # Adicionar ranking
        for i, score in enumerate(priority_scores):
            score.priority_rank = i + 1

        # Armazenar histórico
        self.priority_history.extend(priority_scores)

        logger.info(
            f"Prioridades calculadas. Top score: {priority_scores[0].total_score:.3f}" if priority_scores else "Nenhuma prioridade calculada")
        return priority_scores

    def _calculate_company_priority(self, company_data: Dict[str, Any]) -> PriorityScore:
        """
        Calcula prioridade para uma empresa específica

        Args:
            company_data: Dados da empresa

        Returns:
            Score de prioridade calculado
        """
        company_id = str(company_data.get("_id", "unknown"))
        company_name = company_data.get("name", "Unknown")

        factor_scores = {}

        # Calcular score para cada fator habilitado
        enabled_factors = self.config.enabled_factors or list(PriorityFactor)
        for factor in enabled_factors:
            if factor in self.default_factor_calculators:
                calculator = self.default_factor_calculators[factor]
                factor_score = calculator(company_data)
                factor_scores[factor] = factor_score
            else:
                logger.warning(
                    f"Calculadora não encontrada para fator: {factor}")
                factor_scores[factor] = 0.0

        # Calcular score total ponderado
        total_score = 0.0
        for factor, score in factor_scores.items():
            weight = self.config.factor_weights.get(factor, 0.0)
            total_score += score * weight

        # Gerar justificativa
        justification = self._generate_justification(factor_scores)

        return PriorityScore(
            company_id=company_id,
            company_name=company_name,
            total_score=total_score,
            factor_scores=factor_scores,
            justification=justification
        )

    def _calculate_age_score(self, company_data: Dict[str, Any]) -> float:
        """
        Calcula score baseado na idade dos dados

        Args:
            company_data: Dados da empresa

        Returns:
            Score de idade (0-1, maior = mais antigo)
        """
        try:
            now = datetime.utcnow()
            reports = company_data.get("reports", [])

            if not reports:
                return 1.0  # Sem dados = prioridade máxima

            # Encontrar dados mais antigos
            oldest_update = now

            for report in reports:
                for data_type in ["dados_parcerias", "dados_modelo_negocio", "dados_pricing"]:
                    if data_type in report:
                        processed_at = report[data_type].get("processed_at")
                        if processed_at:
                            if isinstance(processed_at, str):
                                processed_at = datetime.fromisoformat(
                                    processed_at.replace('Z', '+00:00'))

                            if processed_at < oldest_update:
                                oldest_update = processed_at

            # Calcular score baseado na idade
            if oldest_update == now:
                return 1.0  # Sem dados válidos

            days_old = (now - oldest_update).days

            # Score aumenta com a idade dos dados
            # 0 dias = 0.0, 30 dias = 0.5, 90+ dias = 1.0
            age_score = min(1.0, days_old / 90.0)

            return age_score

        except Exception as e:
            logger.error(f"Erro ao calcular age score: {e}")
            return 0.5

    def _calculate_quality_score(self, company_data: Dict[str, Any]) -> float:
        """
        Calcula score baseado na qualidade dos dados

        Args:
            company_data: Dados da empresa

        Returns:
            Score de qualidade (0-1, maior = pior qualidade)
        """
        try:
            reports = company_data.get("reports", [])

            if not reports:
                return 1.0  # Sem dados = prioridade máxima

            quality_scores = []

            for report in reports:
                for data_type in ["dados_parcerias", "dados_modelo_negocio", "dados_pricing"]:
                    if data_type in report:
                        quality = report[data_type].get(
                            "data_quality_score", 0)
                        quality_scores.append(quality)

            if not quality_scores:
                return 1.0

            # Qualidade média
            avg_quality = sum(quality_scores) / len(quality_scores)

            # Inverter score (qualidade baixa = prioridade alta)
            quality_score = 1.0 - (avg_quality / 100.0)

            return max(0.0, min(1.0, quality_score))

        except Exception as e:
            logger.error(f"Erro ao calcular quality score: {e}")
            return 0.5

    def _calculate_importance_score(self, company_data: Dict[str, Any]) -> float:
        """
        Calcula score baseado na importância do negócio

        Args:
            company_data: Dados da empresa

        Returns:
            Score de importância (0-1)
        """
        try:
            # Fatores que indicam importância:
            # - Setor (tecnologia = mais importante)
            # - Funding (empresas com funding = mais importantes)
            # - Presença digital (alta = mais importante)

            importance_factors = []

            # Setor
            sector = company_data.get("sector", "").lower()
            if any(tech_keyword in sector for tech_keyword in ["technology", "software", "tech", "ai", "saas"]):
                importance_factors.append(0.8)
            elif any(high_keyword in sector for high_keyword in ["fintech", "healthtech", "edtech"]):
                importance_factors.append(0.7)
            else:
                importance_factors.append(0.5)

            # Verificar se tem dados de funding
            reports = company_data.get("reports", [])
            has_funding_data = False

            for report in reports:
                if "dados_funding" in report:
                    funding_data = report["dados_funding"]
                    status = funding_data.get(
                        "resumo_funding", {}).get("status_funding", "")
                    if status and status != "Não encontrado":
                        has_funding_data = True
                        break

            if has_funding_data:
                importance_factors.append(0.7)
            else:
                importance_factors.append(0.3)

            # Calcular score médio
            avg_importance = sum(importance_factors) / len(importance_factors)

            return avg_importance

        except Exception as e:
            logger.error(f"Erro ao calcular importance score: {e}")
            return 0.5

    def _calculate_frequency_score(self, company_data: Dict[str, Any]) -> float:
        """
        Calcula score baseado na frequência de atualização

        Args:
            company_data: Dados da empresa

        Returns:
            Score de frequência (0-1)
        """
        # Implementação simplificada
        # Empresas que nunca foram atualizadas têm score alto
        reports = company_data.get("reports", [])

        if not reports:
            return 1.0

        # Contar quantos tipos de dados existem
        data_types_count = 0
        for report in reports:
            for data_type in ["dados_parcerias", "dados_modelo_negocio", "dados_pricing"]:
                if data_type in report:
                    data_types_count += 1

        # Score inversamente proporcional à quantidade de dados
        # Menos dados = maior necessidade de atualização
        max_data_types = 3
        frequency_score = 1.0 - (data_types_count / max_data_types)

        return max(0.0, frequency_score)

    def _calculate_market_activity_score(self, company_data: Dict[str, Any]) -> float:
        """
        Calcula score baseado na atividade do mercado

        Args:
            company_data: Dados da empresa

        Returns:
            Score de atividade de mercado (0-1)
        """
        # Implementação placeholder
        # Poderia analisar notícias, mudanças de funding, etc.
        return 0.5

    def _calculate_company_size_score(self, company_data: Dict[str, Any]) -> float:
        """
        Calcula score baseado no tamanho da empresa

        Args:
            company_data: Dados da empresa

        Returns:
            Score de tamanho (0-1)
        """
        # Implementação placeholder
        # Empresas maiores podem ter prioridade maior
        return 0.5

    def _calculate_sector_volatility_score(self, company_data: Dict[str, Any]) -> float:
        """
        Calcula score baseado na volatilidade do setor

        Args:
            company_data: Dados da empresa

        Returns:
            Score de volatilidade do setor (0-1)
        """
        try:
            sector = company_data.get("sector", "").lower()

            # Setores com alta volatilidade (mudanças frequentes)
            if any(volatile_keyword in sector for volatile_keyword in ["crypto", "blockchain", "ai", "startup"]):
                return 0.8
            elif any(moderate_keyword in sector for moderate_keyword in ["technology", "software", "fintech"]):
                return 0.6
            else:
                return 0.3

        except Exception as e:
            logger.error(f"Erro ao calcular sector volatility score: {e}")
            return 0.5

    def _generate_justification(self, factor_scores: Dict[PriorityFactor, float]) -> str:
        """
        Gera justificativa para o score de prioridade

        Args:
            factor_scores: Scores por fator

        Returns:
            Justificativa textual
        """
        try:
            high_factors = []

            for factor, score in factor_scores.items():
                if score >= 0.7:
                    factor_name = factor.value.replace("_", " ").title()
                    high_factors.append(f"{factor_name} ({score:.2f})")

            if high_factors:
                return f"Prioridade alta devido a: {', '.join(high_factors)}"
            else:
                max_factor = max(factor_scores.items(), key=lambda x: x[1])
                factor_name = max_factor[0].value.replace("_", " ").title()
                return f"Prioridade baseada principalmente em: {factor_name} ({max_factor[1]:.2f})"

        except Exception as e:
            logger.error(f"Erro ao gerar justificativa: {e}")
            return "Prioridade calculada com base em múltiplos fatores"

    def get_top_priorities(self, companies_data: List[Dict[str, Any]],
                           limit: Optional[int] = None) -> List[PriorityScore]:
        """
        Retorna empresas com maior prioridade

        Args:
            companies_data: Lista de empresas
            limit: Número máximo de empresas a retornar

        Returns:
            Lista das empresas com maior prioridade
        """
        if limit is None:
            limit = self.config.max_companies_per_batch

        all_priorities = self.calculate_priorities(companies_data)
        return all_priorities[:limit]

    def get_prioritization_stats(self) -> Dict[str, Any]:
        """
        Retorna estatísticas do priorizador

        Returns:
            Estatísticas de priorização
        """
        if not self.priority_history:
            return {"total_calculations": 0, "average_score": 0, "factor_weights": self.config.factor_weights}

        total_calculations = len(self.priority_history)
        average_score = sum(
            p.total_score for p in self.priority_history) / total_calculations

        # Estatísticas por fator
        factor_stats = {}
        for factor in PriorityFactor:
            factor_scores = [p.factor_scores.get(
                factor, 0) for p in self.priority_history]
            if factor_scores:
                factor_stats[factor.value] = {
                    "average": sum(factor_scores) / len(factor_scores),
                    "max": max(factor_scores),
                    "min": min(factor_scores)
                }

        return {
            "total_calculations": total_calculations,
            "average_score": round(average_score, 3),
            "factor_weights": {f.value: w for f, w in self.config.factor_weights.items()},
            "factor_statistics": factor_stats,
            "config": {
                "max_companies_per_batch": self.config.max_companies_per_batch,
                "quality_threshold": self.config.quality_threshold,
                "age_threshold_days": self.config.age_threshold_days
            }
        }
