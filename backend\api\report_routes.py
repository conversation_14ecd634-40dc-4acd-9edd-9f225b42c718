# 📊 Report Routes - Innovation Scope AI
# Extracted from clients/routes.py following SRP principles

# Lazy import to avoid initialization issues
try:
    from clients.optimized_dossie_generator import optimized_generator
    OPTIMIZED_GENERATOR_AVAILABLE = True
except Exception as e:
    print(f"Optimized dossie generator not available: {e}")
    OPTIMIZED_GENERATOR_AVAILABLE = False
    optimized_generator = None
from clients.perplexity import gerar_dossie_perplexity
from clients.db import clients_collection, projetos_collection, db
from fastapi import APIRouter, HTTPException, status, BackgroundTasks
from fastapi.responses import Response, JSONResponse
from datetime import datetime, UTC
from bson import ObjectId
import asyncio
import logging
from typing import List, Dict, Any, Optional
import os
import sys
from pathlib import Path
from pymongo import MongoClient
from gridfs import GridFS

# Local imports
from shared.websocket_manager import websocket_manager

# Configure sys.path for correct imports
current_dir = Path(__file__).parent
backend_dir = current_dir.parent.parent
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))


router = APIRouter()
logger = logging.getLogger(__name__)

# ✅ NOVO: Imports para rotas de markdown (APÓS definir logger)
try:
    from tools.reports.markdown_generator import get_markdown_report_generator
    MARKDOWN_GENERATOR_AVAILABLE = True
except Exception as e:
    logger.warning(f"Markdown generator not available: {e}")
    MARKDOWN_GENERATOR_AVAILABLE = False
    get_markdown_report_generator = None

try:
    from tools.reports.markdown_to_pdf import MarkdownToPDFConverter
    MARKDOWN_TO_PDF_AVAILABLE = True
except Exception as e:
    logger.warning(f"Markdown to PDF converter not available: {e}")
    MARKDOWN_TO_PDF_AVAILABLE = False
    MarkdownToPDFConverter = None

# Enhanced PDF Generator Setup
ENHANCED_GENERATOR_AVAILABLE = False
ENHANCED_GENERATOR = None

try:
    from tools.reports.pdf_generator_enhanced import EnhancedPDFGenerator, AIReportGeneratorEnhanced
    ENHANCED_GENERATOR = AIReportGeneratorEnhanced
    ENHANCED_GENERATOR_AVAILABLE = True
    logger.info("✅ Enhanced PDF generator loaded successfully")
except Exception as e:
    logger.warning(f"⚠️ Enhanced PDF generator not available: {str(e)}")
    try:
        from tools.reports.pdf_generator_enhanced import AIReportGeneratorEnhanced
        ENHANCED_GENERATOR = AIReportGeneratorEnhanced
        ENHANCED_GENERATOR_AVAILABLE = True
        logger.info("✅ Enhanced PDF generator loaded (fallback)")
    except Exception as e2:
        logger.error(f"❌ Total enhanced generator failure: {str(e2)}")

logger.info("ℹ️ Report module initialized: using enhanced generator only")


# ===================================================================
# 📝 MARKDOWN REPORT ROUTES (MOVED FROM report_endpoints.py)
# ===================================================================

@router.post("/clients/{client_id}/generate-markdown")
async def generate_markdown_report(client_id: str):
    """
    Gera relatório executivo em Markdown usando processamento paralelo.

    Returns:
        JSON com o conteúdo Markdown e metadados
    """
    try:
        # Verificar se cliente existe
        try:
            client_obj_id = ObjectId(client_id)
        except:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="ID de cliente inválido"
            )

        client = clients_collection.find_one({"_id": client_obj_id})
        if not client:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Cliente não encontrado"
            )

        client_name = client.get("name", "Cliente")
        logger.info(f"📝 Gerando relatório Markdown para {client_name}")

        # Verificar se tem dados suficientes
        reports = client.get("reports", [])
        has_dossier = any(r.get("reportType") ==
                          "dossie_expandido" for r in reports)

        if not has_dossier and not reports:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="Cliente não possui dados suficientes. Execute a análise completa primeiro."
            )

        # Verificar se o gerador de markdown está disponível
        if not MARKDOWN_GENERATOR_AVAILABLE or not get_markdown_report_generator:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Gerador de markdown não está disponível no momento"
            )

        # Gerar relatório em Markdown
        generator = get_markdown_report_generator()
        result = await generator.generate_markdown_report(client_id)

        # Salvar markdown no banco (opcional)
        markdown_id = None
        if result.get("markdown_content"):
            fs = GridFS(db)
            markdown_id = fs.put(
                result["markdown_content"].encode('utf-8'),
                filename=f"relatorio_markdown_{client_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md",
                content_type="text/markdown",
                client_id=str(client_obj_id),
                generated_at=datetime.now(UTC),
                report_type="markdown_executive_parallel"
            )

            # Atualizar cliente com ID do markdown
            clients_collection.update_one(
                {"_id": client_obj_id},
                {
                    "$set": {
                        "markdown_report_id": markdown_id,
                        "markdown_generated_at": datetime.now(UTC),
                        "updated_at": datetime.now(UTC)
                    }
                }
            )

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "success": True,
                "client_id": client_id,
                "client_name": client_name,
                "markdown_id": str(markdown_id) if markdown_id else None,
                "markdown_content": result["markdown_content"],
                "metadata": result["metadata"],
                "message": f"Relatório Markdown (paralelo) gerado com sucesso para {client_name}"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Erro ao gerar relatório Markdown: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro ao gerar relatório: {str(e)}"
        )


@router.get("/clients/{client_id}/markdown")
async def get_markdown_report(client_id: str):
    """
    Busca relatório existente em Markdown

    Args:
        client_id: ID do cliente

    Returns:
        JSON com conteúdo Markdown e metadados
    """
    try:
        # Verificar se cliente existe
        try:
            client_obj_id = ObjectId(client_id)
        except:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="ID de cliente inválido"
            )

        client = clients_collection.find_one({"_id": client_obj_id})
        if not client:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Cliente não encontrado"
            )

        client_name = client.get("name", "Cliente")

        # Buscar markdown existente
        markdown_id = client.get("markdown_report_id")
        if not markdown_id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Nenhum relatório Markdown encontrado. Gere o relatório primeiro."
            )

        fs = GridFS(db)
        try:
            markdown_file = fs.get(markdown_id)
            markdown_content = markdown_file.read().decode('utf-8')

            # Metadados do arquivo
            file_metadata = {
                "filename": markdown_file.filename,
                "upload_date": markdown_file.upload_date.isoformat() if markdown_file.upload_date else None,
                "content_type": markdown_file.content_type,
                "length": markdown_file.length
            }

        except Exception as e:
            logger.error(f"Erro ao buscar Markdown do GridFS: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Relatório Markdown não encontrado no banco de dados"
            )

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "success": True,
                "client_id": client_id,
                "client_name": client_name,
                "markdown_content": markdown_content,
                "metadata": {
                    "generated_at": client.get("markdown_generated_at").isoformat() if client.get("markdown_generated_at") else None,
                    "file_info": file_metadata,
                    "report_type": "markdown_executive"
                },
                "message": f"Relatório Markdown encontrado para {client_name}"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Erro ao buscar relatório Markdown: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro ao buscar relatório: {str(e)}"
        )


@router.post("/clients/{client_id}/markdown-to-pdf")
async def convert_markdown_to_pdf(client_id: str, markdown_content: Optional[str] = None):
    """
    Converte relatório Markdown para PDF

    Args:
        client_id: ID do cliente
        markdown_content: Conteúdo Markdown (opcional, busca do banco se não fornecido)

    Returns:
        PDF file response
    """
    try:
        # Verificar se cliente existe
        try:
            client_obj_id = ObjectId(client_id)
        except:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="ID de cliente inválido"
            )

        client = clients_collection.find_one({"_id": client_obj_id})
        if not client:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Cliente não encontrado"
            )

        client_name = client.get("name", "Cliente")

        # Se não foi fornecido markdown_content, buscar do banco
        if not markdown_content:
            markdown_id = client.get("markdown_report_id")
            if not markdown_id:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Nenhum relatório Markdown encontrado. Gere o Markdown primeiro."
                )

            fs = GridFS(db)
            try:
                markdown_file = fs.get(markdown_id)
                markdown_content = markdown_file.read().decode('utf-8')
            except Exception as e:
                logger.error(f"Erro ao buscar Markdown do GridFS: {e}")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Relatório Markdown não encontrado no banco de dados"
                )

        logger.info(f"🔄 Convertendo Markdown para PDF - {client_name}")

        # Verificar se o conversor está disponível
        if not MARKDOWN_TO_PDF_AVAILABLE or not MarkdownToPDFConverter:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Conversor de Markdown para PDF não está disponível no momento"
            )

        # Converter para PDF usando MarkdownToPDFConverter
        converter = MarkdownToPDFConverter()
        metadata = {
            "client_id": client_id,
            "client_name": client_name,
            "generated_at": datetime.now(UTC).isoformat()
        }

        pdf_bytes = converter.convert_to_pdf(markdown_content, metadata)

        # Salvar PDF no GridFS
        fs = GridFS(db)
        pdf_id = fs.put(
            pdf_bytes,
            filename=f"relatorio_executivo_md_{client_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
            content_type="application/pdf",
            client_id=str(client_obj_id),
            generated_at=datetime.now(UTC),
            report_type="pdf_from_markdown"
        )

        # Atualizar cliente
        clients_collection.update_one(
            {"_id": client_obj_id},
            {
                "$set": {
                    "markdown_pdf_id": pdf_id,
                    "markdown_pdf_generated_at": datetime.now(UTC),
                    "pdf_report_available": True,
                    "updated_at": datetime.now(UTC)
                }
            }
        )

        # Retornar PDF
        return Response(
            content=pdf_bytes,
            media_type="application/pdf",
            headers={
                "Content-Disposition": f"attachment; filename=relatorio_executivo_{client_name.replace(' ', '_')}.pdf"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Erro ao converter Markdown para PDF: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro ao converter para PDF: {str(e)}"
        )


# ===================================================================
# 📊 ORIGINAL REPORT ROUTES (FROM clients/routes.py)
# ===================================================================

@router.post("/clients/{client_id}/complete-report")
async def request_complete_report(client_id: str):
    """
    FASE 2: Solicitar relatório completo em background

    - Inicia processamento do dossie_expandido completo
    - Retorna imediatamente com task_id para tracking
    - Notifica via WebSocket quando pronto
    """
    try:
        # Verificar se o cliente existe
        client = clients_collection.find_one({"_id": ObjectId(client_id)})
        if not client:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Cliente não encontrado"
            )

        # Verificar se já está processando
        if client.get("complete_report_requested", False):
            return {
                "message": "Relatório completo já foi solicitado",
                "client_id": client_id,
                "client_name": client.get("name"),
                "status": "already_processing"
            }

        # Marcar como solicitado
        clients_collection.update_one(
            {"_id": ObjectId(client_id)},
            {
                "$set": {
                    "complete_report_requested": True,
                    "report_status": "processing_complete",
                    "complete_report_started_at": datetime.now(UTC),
                    "updated_at": datetime.now(UTC)
                }
            }
        )

        # Importar TaskManager
        from shared.task_manager import create_background_task, TaskPriority

        # Criar task com tracking avançado
        task_id = await create_background_task(
            "complete_report",
            processar_relatorio_completo,
            TaskPriority.HIGH,
            client_id,
            client.get("name", ""),
            600,  # 10 minutos
            ObjectId(client_id),
            client.get("name") or "",
            client.get("site") or "",
            client.get("city") or "",
            client.get("state") or ""
        )

        logger.info(
            f"Relatório completo solicitado para: {client.get('name')} (ID: {client_id}, Task: {task_id})")

        return {
            "message": "Relatório completo foi solicitado e está sendo processado",
            "client_id": client_id,
            "client_name": client.get("name"),
            "task_id": task_id,
            "status": "processing_started",
            "estimated_time": "5-10 minutos",
            "notification_info": "Você será notificado quando estiver pronto",
            "tracking_endpoint": f"/tasks/{task_id}"
        }

    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="ID de cliente inválido"
        )

    except Exception as e:
        logger.error(f"Erro ao solicitar relatório completo: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        )


async def processar_relatorio_completo(client_id: ObjectId, nome: str, site: str, cidade: str, estado: str):
    """
    Processa relatório completo em background e notifica quando pronto
    NOVO: Inclui pesquisa automática do Tavily após gerar o dossiê
    """
    try:
        logger.info(
            f"Iniciando processamento do relatório completo para: {nome}")

        # ✅ NOVO: Atualizar status para "Coletando informações"
        clients_collection.update_one(
            {"_id": client_id},
            {
                "$set": {
                    "status": "Coletando informações",
                    "report_status": "processing_complete",
                    "updated_at": datetime.now(UTC)
                }
            }
        )

        # ✅ NOVO: Notificar frontend sobre início da coleta
        await websocket_manager.broadcast({
            "type": "collection_status_update",
            "clientId": str(client_id),
            "status": "Coletando informações",
            "message": f"Iniciando coleta de dados para {nome} . Tempo estimado: 10 minutos",
            "timestamp": datetime.now(UTC).isoformat(),
            "phase": "data_collection_started"
        })

        # Gerar dossie completo (função híbrida síncrona com parsers async internos)
        await asyncio.to_thread(
            gerar_dossie_perplexity,
            client_id, nome, site, cidade, estado
        )

        logger.info(
            f"✅ Dossiê completo gerado para {nome}. Iniciando pesquisa de mercado...")

        # Buscar dados do cliente para notificações
        client = clients_collection.find_one({"_id": client_id})
        sector = client.get("sector") or client.get(
            "setor") if client else None

        # ✅ ATUALIZAR: Status no banco para "Novo"
        clients_collection.update_one(
            {"_id": client_id},
            {
                "$set": {
                    "status": "Novo",  # ✅ Status deve ser "Novo", não "Ativo"
                    "report_status": "complete",
                    "complete_report_finished_at": datetime.now(UTC),
                    "complete_report_requested": False,  # ✅ Resetar flag de processamento
                    "updated_at": datetime.now(UTC)
                }
            }
        )

        # Mensagem de conclusão do dossiê
        ready_message = f"Dossiê concluído para {nome}. Deseja gerar projetos?"

        # ✅ NOVO: Notificar que TUDO está pronto para gerar projetos
        await websocket_manager.broadcast({
            "type": "ready_for_projects",
            "clientId": str(client_id),
            "clientName": nome,
            "status": "Novo",
            "message": ready_message,
            "timestamp": datetime.now(UTC).isoformat(),
            "action": "show_projects_alert",
            "phase": "ready_for_projects",
            "sector": sector
        })

        # 🚀 NOVO: Triggerar geração automática de PDF inteligente
        try:
            from clients.pdf_background_service import trigger_pdf_check
            pdf_generated = await trigger_pdf_check(str(client_id))
            if pdf_generated:
                logger.info(f"✅ PDF automático iniciado para {nome}")
            else:
                logger.info(
                    f"ℹ️ PDF automático não necessário para {nome} (requisitos não atendidos ou já existe)")
        except Exception as pdf_error:
            logger.warning(
                f"⚠️ Erro ao verificar PDF automático para {nome}: {pdf_error}")
            pdf_generated = False
            # Não falhar o processo principal por causa do PDF

        # Atualizar no banco se PDF foi gerado
        update_data = {
            "pdf_report_available": pdf_generated,
            "updated_at": datetime.now(UTC)
        }
        if pdf_generated:
            clients_collection.update_one(
                {"_id": client_id},
                {"$set": update_data}
            )

        # Notificar via WebSocket sobre conclusão do relatório
        await websocket_manager.broadcast({
            "type": "client_status_update",
            "clientId": str(client_id),
            "status": "Novo",
            "message": f"Relatório completo de {nome} foi finalizado",
            "timestamp": datetime.now(UTC).isoformat(),
            "action": "refresh_client",
            "pdf_available": pdf_generated
        })

        logger.info(
            f"✅ FLUXO COMPLETO CONCLUÍDO - Cliente {nome}: Dossiê ✅ | PDF {'✅' if pdf_generated else '⚠️'} | Frontend Notificado ✅")

    except Exception as e:
        logger.error(f"Erro no processamento do relatório completo: {str(e)}")

        # Marcar como erro no banco
        clients_collection.update_one(
            {"_id": client_id},
            {
                "$set": {
                    "status": "Erro",  # Atualizar status principal para erro
                    "report_status": "error",
                    "report_error": str(e),
                    "complete_report_requested": False,  # ✅ Resetar flag mesmo em erro
                    "updated_at": datetime.now(UTC)
                }
            }
        )


@router.get("/clients/{client_id}/report-status")
def get_report_status(client_id: str):
    """
    Verifica o status do relatório de um cliente
    """
    try:
        client = clients_collection.find_one({"_id": ObjectId(client_id)})
        if not client:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Cliente não encontrado"
            )

        report_status = client.get("report_status", "basic_only")

        status_info = {
            "client_id": client_id,
            "client_name": client.get("name"),
            "report_status": report_status,
            "complete_report_requested": client.get("complete_report_requested", False),
            "has_complete_report": report_status == "complete",
            "is_processing": report_status == "processing_complete",
            "has_error": report_status == "error"
        }

        # Informações adicionais baseadas no status
        if report_status == "processing_complete":
            started_at = client.get("complete_report_started_at")
            if started_at:
                status_info["started_at"] = started_at.isoformat()
                status_info["processing_duration"] = str(
                    datetime.now(UTC) - started_at)

        elif report_status == "complete":
            finished_at = client.get("complete_report_finished_at")
            if finished_at:
                status_info["finished_at"] = finished_at.isoformat()

        elif report_status == "error":
            error_message = client.get("report_error")
            if error_message:
                status_info["error_message"] = error_message

        return status_info

    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="ID de cliente inválido"
        )

    except Exception as e:
        logger.error(f"Erro ao verificar status do relatório: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        )


@router.get("/clients/{client_id}/team-agno-status")
def get_team_agno_status(client_id: str):
    """
    🚀 NOVO: Verifica o status da análise Team Agno
    """
    try:
        client = clients_collection.find_one({"_id": ObjectId(client_id)})
        if not client:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Cliente não encontrado"
            )

        report_status = client.get("report_status", "basic_only")

        # Verificar se tem análise Team Agno
        has_team_agno = any(
            report.get("report_type") == "team_agno_analysis"
            for report in client.get("reports", [])
        )

        status_info = {
            "client_id": client_id,
            "client_name": client.get("name"),
            "report_status": report_status,
            "team_agno_requested": client.get("team_agno_requested", False),
            "has_team_agno_analysis": has_team_agno,
            "is_processing_team_agno": report_status == "processing_team_agno",
            "team_agno_complete": report_status == "team_agno_complete",
            "has_team_agno_error": report_status == "team_agno_error",
            "agents_count": 10
        }

        # Informações adicionais baseadas no status
        if report_status == "processing_team_agno":
            started_at = client.get("team_agno_started_at")
            if started_at:
                status_info["started_at"] = started_at.isoformat()
                status_info["processing_duration"] = str(
                    datetime.now(UTC) - started_at)

        elif report_status == "team_agno_complete":
            finished_at = client.get("team_agno_finished_at")
            if finished_at:
                status_info["finished_at"] = finished_at.isoformat()

        elif report_status == "team_agno_error":
            error_message = client.get("team_agno_error")
            if error_message:
                status_info["error_message"] = error_message

        return status_info

    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="ID de cliente inválido"
        )

    except Exception as e:
        logger.error(f"Erro ao verificar status team agno: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        )


@router.get("/clients/{client_id}/enhanced-report")
def download_enhanced_report(client_id: str):
    """
    Download do relatório executivo aprimorado em PDF
    """
    try:
        # Verificar se o cliente existe
        client = clients_collection.find_one({"_id": ObjectId(client_id)})
        if not client:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Cliente não encontrado"
            )

        # Verificar se tem relatório aprimorado
        enhanced_pdf_id = client.get("enhanced_pdf_id")
        if not enhanced_pdf_id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Relatório aprimorado não encontrado. Execute a geração primeiro."
            )

        # Buscar PDF no GridFS
        fs = GridFS(db)
        try:
            pdf_file = fs.get(enhanced_pdf_id)
            pdf_data = pdf_file.read()

            client_name = client.get("nome", "Cliente")

            logger.info(f"✅ Servindo relatório aprimorado para {client_name}")

            return Response(
                content=pdf_data,
                media_type="application/pdf",
                headers={
                    "Content-Disposition": f"attachment; filename=relatorio_executivo_enhanced_{client_name.replace(' ', '_')}.pdf"
                }
            )

        except Exception as e:
            logger.error(f"Erro ao buscar PDF aprimorado no GridFS: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Arquivo PDF aprimorado não encontrado no sistema"
            )

    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="ID do cliente inválido"
        )
    except Exception as e:
        logger.error(f"Erro ao baixar relatório aprimorado: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        )


def _safe_datetime_age_days(pdf_generated_at):
    """Calcula idade em dias de forma segura lidando com timezone"""
    if not pdf_generated_at:
        return 999  # Força regeneração se não há data

    now_utc = datetime.now(UTC)

    # Se pdf_generated_at é naive (sem timezone), assumir UTC
    if isinstance(pdf_generated_at, datetime):
        if pdf_generated_at.tzinfo is None:
            pdf_generated_at = pdf_generated_at.replace(tzinfo=UTC)
        elif pdf_generated_at.tzinfo != UTC:
            pdf_generated_at = pdf_generated_at.astimezone(UTC)

    return (now_utc - pdf_generated_at).days


@router.get("/clients/{client_id}/report")
def download_client_report(client_id: str, regenerate: bool = False, enhanced: bool = True):
    """
    Download do relatório executivo AI em PDF do cliente
    🚀 NOVA VERSÃO: Usa sistema AI-powered com GridFS
    ✨ PADRÃO: Sempre gera PDF expandido (15+ seções)

    Parâmetros:
    - regenerate: Força regeneração do PDF (default: False)
    - enhanced: Usa o gerador aprimorado (default: True) - SEMPRE ATIVO

    Exemplos:
    - GET /clients/{id}/report - Baixa PDF expandido
    - GET /clients/{id}/report?regenerate=true - Regenera PDF expandido
    - GET /clients/{id}/report?enhanced=false - Força PDF básico (não recomendado)
    """
    try:
        # Verificar se o cliente existe
        client = clients_collection.find_one({"_id": ObjectId(client_id)})
        if not client:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Cliente não encontrado"
            )

        client_name = client.get("name", "Cliente")

        # 🔄 NOVA LÓGICA: Verificar se deve regenerar PDF
        should_regenerate = regenerate

        # Determinar qual gerador usar baseado no parâmetro enhanced
        if enhanced:
            pdf_file_id = client.get("enhanced_pdf_id")
            pdf_generated_at_field = "enhanced_pdf_generated_at"
            logger.info(f"📈 Usando gerador aprimorado para {client_name}")
        else:
            pdf_file_id = client.get("pdf_report_id")
            pdf_generated_at_field = "pdf_generated_at"

        # Verificar se PDF existe e se é muito antigo (7 dias)
        if not should_regenerate and pdf_file_id:
            pdf_generated_at = client.get(pdf_generated_at_field)
            if pdf_generated_at:
                age_days = _safe_datetime_age_days(pdf_generated_at)
                if age_days > 7:
                    should_regenerate = True
                    report_type = "aprimorado" if enhanced else "básico"
                    logger.info(
                        f"🔄 PDF {report_type} de {client_name} tem {age_days} dias. Regenerando...")

        # ✅ OPÇÃO 1: Gerar novo PDF se solicitado ou necessário
        if should_regenerate or not pdf_file_id:
            try:
                if enhanced and ENHANCED_GENERATOR_AVAILABLE and ENHANCED_GENERATOR:
                    # Usar gerador aprimorado
                    logger.info(
                        f"🔄 Gerando novo PDF aprimorado para {client_name}")

                    # Buscar dossiê expandido nos reports
                    expanded_dossier = None
                    for report in client.get("reports", []):
                        if report.get("reportType") == "dossie_expandido":
                            expanded_dossier = report.get("data", {})
                            break

                    if not expanded_dossier:
                        logger.warning(
                            f"⚠️ Cliente não possui dossiê expandido - criando baseado nos dados disponíveis")
                        expanded_dossier = _create_minimal_expanded_dossier(
                            client)

                    generator = ENHANCED_GENERATOR()
                    pdf_content = generator.generate_report(
                        expanded_dossier, client_name)

                    # Salvar no GridFS
                    fs = GridFS(db)
                    pdf_file_id_new = fs.put(
                        pdf_content,
                        filename=f"relatorio_executivo_enhanced_{client_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                        content_type="application/pdf",
                        client_id=str(client["_id"]),
                        generated_at=datetime.now(UTC),
                        report_type="enhanced_executive"
                    )

                    # Atualizar banco
                    clients_collection.update_one(
                        {"_id": client["_id"]},
                        {
                            "$set": {
                                "enhanced_pdf_id": pdf_file_id_new,
                                "enhanced_pdf_generated_at": datetime.now(UTC),
                                "updated_at": datetime.now(UTC)
                            }
                        }
                    )

                    # Baixar o PDF aprimorado
                    if pdf_file_id_new:
                        fs = GridFS(db)
                        pdf_file = fs.get(pdf_file_id_new)
                        pdf_content = pdf_file.read()

                        logger.info(
                            f"✅ Novo PDF aprimorado gerado e servido para {client_name}")
                        return Response(
                            content=pdf_content,
                            media_type="application/pdf",
                            headers={
                                "Content-Disposition": f"attachment; filename=relatorio_executivo_enhanced_{client_name.replace(' ', '_')}.pdf"
                            }
                        )

                logger.warning(
                    f"⚠️ Falha na geração de novo PDF para {client_name}. Tentando usar PDF existente...")

            except Exception as e:
                logger.error(
                    f"❌ Erro ao gerar novo PDF para {client_name}: {str(e)}")

        # ✅ OPÇÃO 2: Usar PDF existente do cache
        if pdf_file_id:
            if enhanced:
                # Usar PDF aprimorado do cache
                fs = GridFS(db)
                try:
                    pdf_file = fs.get(pdf_file_id)
                    pdf_data = pdf_file.read()

                    pdf_age = ""
                    if client.get(pdf_generated_at_field):
                        age_days = _safe_datetime_age_days(
                            client.get(pdf_generated_at_field))
                        pdf_age = f" ({age_days} dias)" if age_days > 0 else " (hoje)"

                    logger.info(
                        f"✅ Servindo PDF aprimorado em cache para {client_name}{pdf_age}")
                    return Response(
                        content=pdf_data,
                        media_type="application/pdf",
                        headers={
                            "Content-Disposition": f"attachment; filename=relatorio_executivo_enhanced_{client_name.replace(' ', '_')}.pdf"
                        }
                    )
                except Exception as e:
                    logger.warning(
                        f"PDF aprimorado com ID {pdf_file_id} não encontrado no GridFS: {e}")

        # ✅ PRIORIDADE 2: Verificar PDF do sistema antigo
        pdf_path = client.get("full_processing_pdf_path")
        if pdf_path and os.path.exists(pdf_path):
            from fastapi.responses import FileResponse
            return FileResponse(
                pdf_path,
                media_type="application/pdf",
                filename=f"relatorio_{client_name.replace(' ', '_')}.pdf"
            )

        # ✅ PRIORIDADE 3: Gerar PDF básico em tempo real se houver dados
        reports = client.get("reports", [])
        dossie_expandido = None

        for report in reports:
            if report.get("reportType") == "dossie_expandido":
                dossie_expandido = report.get("data", {})
                break

        if dossie_expandido:
            # Tentar gerar um relatório básico se não houver PDF AI
            from io import BytesIO
            from reportlab.pdfgen import canvas
            from fastapi.responses import StreamingResponse

            buffer = BytesIO()
            p = canvas.Canvas(buffer)

            # Título
            p.setFont("Helvetica-Bold", 16)
            p.drawString(100, 750, f"Relatório Básico - {client_name}")

            # Informações básicas
            p.setFont("Helvetica", 12)
            y_position = 700

            info_data = [
                f"Empresa: {client_name}",
                f"Setor: {client.get('sector', 'N/A')}",
                f"Cidade: {client.get('city', 'N/A')}",
                f"Estado: {client.get('state', 'N/A')}",
                f"Site: {client.get('site', 'N/A')}",
                "",
                "NOTA: Este é um relatório básico.",
                "Para o relatório executivo completo com IA,",
                "execute a análise completa do cliente."
            ]

            for info in info_data:
                p.drawString(100, y_position, info)
                y_position -= 25

            p.showPage()
            p.save()
            buffer.seek(0)

            return StreamingResponse(
                BytesIO(buffer.read()),
                media_type="application/pdf",
                headers={
                    "Content-Disposition": f"attachment; filename=relatorio_basico_{client_name.replace(' ', '_')}.pdf"
                }
            )

        # ✅ ERRO: Nenhum relatório disponível
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Nenhum relatório disponível para este cliente. Execute a análise completa primeiro."
        )

    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="ID do cliente inválido"
        )
    except Exception as e:
        logger.error(
            f"Erro ao baixar relatório para cliente {client_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        )


def _create_minimal_expanded_dossier(client_doc: dict) -> dict:
    """Cria um dossiê expandido mínimo baseado nos dados do cliente"""

    nome = client_doc.get("name", "Empresa")
    site = client_doc.get("site", "")
    cidade = client_doc.get("city", "")
    estado = client_doc.get("state", "")
    setor = client_doc.get("sector", client_doc.get("setor", "Indefinido"))

    # Buscar dados dos reports
    dossie_basico = {}

    for report in client_doc.get("reports", []):
        if report.get("reportType") == "dossie_basico":
            dossie_basico = report.get("data", {})

    return {
        "dossie_basico": {
            "nome": nome,
            "site": site,
            "setor": setor,
            "localizacao": f"{cidade}, {estado}" if cidade and estado else "Brasil",
            "descricao": dossie_basico.get("descricao", f"Empresa do setor {setor}"),
            "fundacao": dossie_basico.get("fundacao", "N/A"),
            "tamanho": dossie_basico.get("tamanho", "N/A"),
            **dossie_basico
        },
        "swot_expandida": {
            "forcas": dossie_basico.get("forcas", ["Empresa estabelecida"]),
            "fraquezas": dossie_basico.get("fraquezas", ["A identificar"]),
            "oportunidades": dossie_basico.get("oportunidades", ["Expansão digital"]),
            "ameacas": dossie_basico.get("ameacas", ["Concorrência"])
        },
        "stack_tecnologica": {"resumo": "A analisar", "detalhes": {}},
        "funding_investimentos": {"resumo": "Dados não disponíveis"},
        "presenca_digital": {"website": site, "status": "Análise pendente"},
        "parcerias_estrategicas": {"resumo": "A identificar"},
        "modelo_negocio": {"tipo": "A identificar", "setor": setor},
        "estrategia_pricing": {"resumo": "A analisar"},
        "canais_reviews": {"resumo": "A coletar"},
        "diagnostico_tecnico": {"resumo": "Pendente"},
        "pesquisa_mercado": {"resumo": "Dados incluídos no dossiê expandido", "setor": setor}
    }


@router.post("/clients/{client_id}/force-enhanced-report")
async def force_enhanced_report(client_id: str):
    """
    🚫 DEPRECATED: Este endpoint está obsoleto desde a implementação do sistema automático de PDFs.

    O sistema agora gera PDFs automaticamente quando:
    1. Dossiê expandido está completo
    2. Pesquisa de mercado está completa

    Use o fluxo normal: complete-report → aguarde conclusão → PDF é gerado automaticamente.

    Para regenerar um PDF, use: POST /clients/{client_id}/generate-enhanced-report
    """
    raise HTTPException(
        status_code=status.HTTP_410_GONE,
        detail={
            "error": "Endpoint obsoleto",
            "message": "PDFs são agora gerados automaticamente após conclusão do dossiê e pesquisa de mercado",
            "deprecated_since": "2024-01-10",
            "replacement": "Fluxo automático via /clients/{client_id}/complete-report",
            "manual_regeneration": "/clients/{client_id}/generate-enhanced-report",
            "new_workflow": {
                "step_1": "POST /clients/{client_id}/complete-report",
                "step_2": "Aguarde WebSocket notification: 'pdf_generation_completed'",
                "step_3": "GET /clients/{client_id}/report para download"
            }
        }
    )


@router.post("/clients/{client_id}/generate-enhanced-report")
async def generate_enhanced_report_manual(client_id: str, background_tasks: BackgroundTasks):
    """
    🧪 Endpoint manual para testar o novo gerador de PDF aprimorado
    """
    try:
        client_id_obj = ObjectId(client_id)

        # Buscar cliente
        client = clients_collection.find_one({"_id": client_id_obj})
        if not client:
            raise HTTPException(
                status_code=404, detail="Cliente não encontrado")

        # Verificar se tem expanded_dossier
        if not client.get("expanded_dossier"):
            raise HTTPException(
                status_code=400,
                detail="Cliente não possui dossiê expandido. Execute primeiro o relatório completo."
            )

        # Executar em background
        background_tasks.add_task(
            processar_enhanced_report,
            client_id_obj,
            client["nome"],
            client.get("site", ""),
            client.get("cidade", ""),
            client.get("estado", "")
        )

        return {
            "message": f"Geração de relatório aprimorado iniciada para {client['nome']}",
            "client_id": client_id,
            "status": "processing"
        }

    except Exception as e:
        logger.error(f"Erro ao iniciar geração de relatório aprimorado: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Erro interno: {str(e)}"
        )


async def processar_enhanced_report(client_id: ObjectId, nome: str, site: str, cidade: str, estado: str):
    """
    Processar geração do relatório executivo aprimorado
    """
    try:
        logger.info(
            f"🚀 Iniciando geração de relatório aprimorado para: {nome}")

        # Notificar início
        await websocket_manager.broadcast({
            "type": "enhanced_report_status",
            "clientId": str(client_id),
            "status": "processing",
            "message": f"🚀 Iniciando geração de relatório executivo aprimorado para {nome}",
            "timestamp": datetime.now(UTC).isoformat(),
            "phase": "enhanced_started"
        })

        # Atualizar status no banco
        clients_collection.update_one(
            {"_id": client_id},
            {
                "$set": {
                    "enhanced_report_status": "processing",
                    "enhanced_report_started_at": datetime.now(UTC),
                    "updated_at": datetime.now(UTC)
                }
            }
        )

        # Buscar dados do cliente
        client = clients_collection.find_one({"_id": client_id})
        if not client or not client.get("expanded_dossier"):
            raise Exception("Cliente ou dossiê expandido não encontrado")

        # Importar e usar o gerador aprimorado
        from tools.reports.pdf_generator_enhanced import AIReportGeneratorEnhanced

        # Notificar progresso
        await websocket_manager.broadcast({
            "type": "enhanced_report_status",
            "clientId": str(client_id),
            "status": "processing",
            "message": "🤖 IA gerando conteúdo executivo detalhado...",
            "timestamp": datetime.now(UTC).isoformat(),
            "phase": "ai_processing"
        })

        # Gerar relatório aprimorado
        generator = AIReportGeneratorEnhanced()
        pdf_content = await asyncio.to_thread(
            generator.generate_report,
            client["expanded_dossier"],
            nome
        )

        # Salvar PDF no GridFS
        fs = GridFS(db)
        pdf_id = fs.put(
            pdf_content,
            filename=f"relatorio_executivo_enhanced_{nome}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
            content_type="application/pdf",
            client_id=str(client_id),
            generated_at=datetime.now(UTC),
            report_type="enhanced_executive"
        )

        # Atualizar no banco
        clients_collection.update_one(
            {"_id": client_id},
            {
                "$set": {
                    "enhanced_report_status": "completed",
                    "enhanced_pdf_id": pdf_id,
                    "enhanced_pdf_generated_at": datetime.now(UTC),
                    "updated_at": datetime.now(UTC)
                }
            }
        )

        # Notificar sucesso
        await websocket_manager.broadcast({
            "type": "enhanced_report_completed",
            "clientId": str(client_id),
            "status": "completed",
            "message": f"✅ Relatório executivo aprimorado concluído para {nome}",
            "timestamp": datetime.now(UTC).isoformat(),
            "phase": "enhanced_completed",
            "pdf_id": str(pdf_id)
        })

        logger.info(f"✅ Relatório aprimorado concluído para {nome}")

    except Exception as e:
        logger.error(
            f"❌ Erro na geração do relatório aprimorado para {nome}: {str(e)}")

        # Atualizar erro no banco
        clients_collection.update_one(
            {"_id": client_id},
            {
                "$set": {
                    "enhanced_report_status": "error",
                    "enhanced_report_error": str(e),
                    "updated_at": datetime.now(UTC)
                }
            }
        )

        # Notificar erro
        await websocket_manager.broadcast({
            "type": "enhanced_report_error",
            "clientId": str(client_id),
            "status": "error",
            "message": f"❌ Erro na geração do relatório aprimorado: {str(e)}",
            "timestamp": datetime.now(UTC).isoformat(),
            "phase": "enhanced_error"
        })


@router.post("/clients/{client_id}/generate-optimized-dossie")
async def generate_optimized_dossie(client_id: str, background_tasks: BackgroundTasks):
    """
    🚀 Gera dossiê ultra-otimizado com paralelização e cache inteligente

    Melhorias de performance:
    - Redução de 18-25 minutos para 6-8 minutos (60-70% mais rápido)
    - Execução paralela de todas as pesquisas
    - Cache inteligente multi-camadas
    - Diagnósticos técnicos paralelos
    - Fallback automático para versão original

    Args:
        client_id: ID do cliente

    Returns:
        JSON com status da operação
    """
    try:
        # Verificar se cliente existe
        try:
            client_obj_id = ObjectId(client_id)
        except:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="ID de cliente inválido"
            )

        client = clients_collection.find_one({"_id": client_obj_id})
        if not client:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Cliente não encontrado"
            )

        nome = client.get("name", "")
        site = client.get("site", "")
        cidade = client.get("city", "")
        estado = client.get("state", "")

        # Verificar se já está processando
        if client.get("report_status") == "processing_optimized":
            return {
                "message": "Dossiê otimizado já está sendo processado",
                "status": "already_processing",
                "client_id": client_id
            }

        # Marcar como processando
        clients_collection.update_one(
            {"_id": client_obj_id},
            {
                "$set": {
                    "report_status": "processing_optimized",
                    "optimized_dossie_started_at": datetime.now(UTC),
                    "updated_at": datetime.now(UTC)
                }
            }
        )

        # Notificar início do processamento otimizado
        await websocket_manager.broadcast({
            "type": "optimized_dossie_started",
            "clientId": client_id,
            "status": "Processando (Otimizado)",
            "message": f"🚀 Iniciando geração ultra-otimizada do dossiê para {nome}",
            "timestamp": datetime.now(UTC).isoformat(),
            "phase": "optimized_started",
            "estimated_time": "6-8 minutos",
            "optimizations": [
                "Pesquisas paralelas",
                "Cache inteligente",
                "Diagnósticos paralelos",
                "Processamento assíncrono"
            ]
        })

        # Executar geração otimizada em background
        background_tasks.add_task(
            _processar_dossie_otimizado,
            client_obj_id,
            nome,
            site,
            cidade,
            estado
        )

        return {
            "message": f"Geração otimizada do dossiê iniciada para {nome}",
            "status": "processing",
            "client_id": client_id,
            "estimated_time": "6-8 minutos",
            "performance_improvement": "60-70% mais rápido que versão original"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Erro ao iniciar geração otimizada: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        )


async def _processar_dossie_otimizado(
    client_id: ObjectId,
    nome: str,
    site: str,
    cidade: str,
    estado: str
):
    """
    Processa dossiê otimizado em background
    """
    try:
        logger.info(f"🚀 Iniciando processamento otimizado para: {nome}")

        # Notificar progresso - Cache Check
        await websocket_manager.broadcast({
            "type": "optimized_dossie_progress",
            "clientId": str(client_id),
            "status": "Verificando cache",
            "message": "📋 Verificando cache inteligente...",
            "timestamp": datetime.now(UTC).isoformat(),
            "phase": "cache_check",
            "progress": 10
        })

        # Executar geração ultra-otimizada
        if not OPTIMIZED_GENERATOR_AVAILABLE or not optimized_generator:
            raise Exception("Optimized dossie generator not available")

        dossie_resultado = await optimized_generator.gerar_dossie_ultra_otimizado(
            str(client_id),
            nome,
            site if site else None,
            cidade if cidade else None,
            estado if estado else None
        )

        # Notificar progresso - Consolidação
        await websocket_manager.broadcast({
            "type": "optimized_dossie_progress",
            "clientId": str(client_id),
            "status": "Consolidando resultados",
            "message": "📊 Consolidando dados coletados...",
            "timestamp": datetime.now(UTC).isoformat(),
            "phase": "consolidation",
            "progress": 90
        })

        # Atualizar status final
        clients_collection.update_one(
            {"_id": client_id},
            {
                "$set": {
                    "report_status": "optimized_complete",
                    "optimized_dossie_finished_at": datetime.now(UTC),
                    "updated_at": datetime.now(UTC),
                    "optimized_dossie_data": dossie_resultado
                }
            }
        )

        # Calcular métricas de performance
        processing_time = dossie_resultado.get(
            "performance_metrics", {}).get("total_time", 0)
        efficiency_gain = dossie_resultado.get(
            "performance_metrics", {}).get("efficiency_gain", "N/A")

        # Notificar conclusão
        await websocket_manager.broadcast({
            "type": "optimized_dossie_completed",
            "clientId": str(client_id),
            "status": "Concluído (Otimizado)",
            "message": f"✅ Dossiê otimizado concluído para {nome}",
            "timestamp": datetime.now(UTC).isoformat(),
            "phase": "optimized_completed",
            "progress": 100,
            "performance_metrics": {
                "processing_time_seconds": processing_time,
                "efficiency_gain": efficiency_gain,
                "optimizations_used": dossie_resultado.get("metadata", {}).get("optimizations", [])
            }
        })

        logger.info(
            f"✅ Dossiê otimizado concluído para {nome} em {processing_time:.2f}s")

    except Exception as e:
        logger.error(
            f"❌ Erro no processamento otimizado para {nome}: {str(e)}")

        # Atualizar erro no banco
        clients_collection.update_one(
            {"_id": client_id},
            {
                "$set": {
                    "report_status": "optimized_error",
                    "optimized_dossie_error": str(e),
                    "updated_at": datetime.now(UTC)
                }
            }
        )

        # Notificar erro
        await websocket_manager.broadcast({
            "type": "optimized_dossie_error",
            "clientId": str(client_id),
            "status": "Erro (Otimizado)",
            "message": f"❌ Erro na geração otimizada: {str(e)}",
            "timestamp": datetime.now(UTC).isoformat(),
            "phase": "optimized_error"
        })


# ⚠️ FUNÇÃO DEPRECATED: processar_analise_team_agno()
# Esta função está obsoleta e não deveria ser usada.
# Use processar_estimativa_projeto() em project_generation_routes.py
async def processar_analise_team_agno(client_id: ObjectId, nome: str, site: str, cidade: str, estado: str, projeto_descricao: str = ""):
    """
    ⚠️ DEPRECATED: Esta função está obsoleta.

    USE INSTEAD: processar_estimativa_projeto() no project_generation_routes.py

    Esta função analisa clientes genéricos em vez de projetos específicos,
    o que não gera estimativas precisas.
    """
    try:
        logger.info(f"🚀 Iniciando análise Team Agno para: {nome}")

        # Atualizar status para Team Agno Analysis
        clients_collection.update_one(
            {"_id": client_id},
            {
                "$set": {
                    "status": "Análise Team Agno",
                    "report_status": "processing_team_agno",
                    "team_agno_started_at": datetime.now(UTC),
                    "updated_at": datetime.now(UTC)
                }
            }
        )

        # Notificar início da análise Team Agno
        await websocket_manager.broadcast({
            "type": "team_agno_status_update",
            "clientId": str(client_id),
            "status": "Análise Team Agno",
            "message": f"🚀 Iniciando análise multi-especializada para {nome}",
            "timestamp": datetime.now(UTC).isoformat(),
            "phase": "team_agno_started",
            "estimated_time": "2-3 minutos"
        })

        # Preparar input para Team Agno
        if not projeto_descricao:
            # Se não tem descrição específica, usar dados do cliente
            projeto_descricao = f"Projeto para {nome}"
            if site:
                projeto_descricao += f" - website: {site}"
            if cidade and estado:
                projeto_descricao += f" - localizada em {cidade}, {estado}"

        # 🤖 EXECUTAR TEAM AGNO
        import sys
        from pathlib import Path

        # Adicionar path se necessário
        current_dir = Path(__file__).parent
        backend_dir = current_dir.parent.parent
        if str(backend_dir) not in sys.path:
            sys.path.insert(0, str(backend_dir))

        # Importar o team configurado
        from teams.project_team import team

        # Notificar que os agentes estão trabalhando
        await websocket_manager.broadcast({
            "type": "team_agno_status_update",
            "clientId": str(client_id),
            "status": "Agentes trabalhando",
            "message": "10 especialistas analisando o projeto...",
            "timestamp": datetime.now(UTC).isoformat(),
            "phase": "agents_working"
        })

        # Executar análise Team Agno
        resultado_agno = await asyncio.to_thread(
            team.run,
            f"""
Cliente: {nome}
    Site: {site}
    Cidade: {cidade} - {estado}
    
    Necessidade do Projeto:
    {projeto_descricao}
    
    Objetivo: Obter estimativa detalhada com arquitetura, tecnologias, 
    cronograma, equipe necessária e custos estimados.
"""
        )

        # Processar resultado e salvar no banco
        team_agno_report = {
            "report_type": "team_agno_analysis",
            "timestamp": datetime.now(UTC).isoformat(),
            "projeto_analisado": projeto_descricao,
            "resultado_coordenador": resultado_agno.content if hasattr(resultado_agno, 'content') else str(resultado_agno),
            "agentes_executados": resultado_agno.formatted_tool_calls if hasattr(resultado_agno, 'formatted_tool_calls') else [],
            "status": "concluido"
        }

        # Salvar resultado no banco
        clients_collection.update_one(
            {"_id": client_id},
            {"$push": {"reports": team_agno_report}}
        )

        # Atualizar status final
        clients_collection.update_one(
            {"_id": client_id},
            {
                "$set": {
                    "status": "Novo",
                    "report_status": "team_agno_complete",
                    "team_agno_finished_at": datetime.now(UTC),
                    "team_agno_requested": False,
                    "updated_at": datetime.now(UTC)
                }
            }
        )

        # Notificar conclusão
        await websocket_manager.broadcast({
            "type": "team_agno_completed",
            "clientId": str(client_id),
            "clientName": nome,
            "status": "Novo",
            "message": f"✅ Análise Team Agno concluída para {nome}",
            "timestamp": datetime.now(UTC).isoformat(),
            "action": "refresh_client",
            "phase": "team_agno_completed"
        })

        logger.info(f"✅ Team Agno Analysis CONCLUÍDA para {nome}")

    except Exception as e:
        logger.error(f"❌ Erro na análise Team Agno para {nome}: {str(e)}")

        # Marcar como erro
        clients_collection.update_one(
            {"_id": client_id},
            {
                "$set": {
                    "status": "Erro Team Agno",
                    "report_status": "team_agno_error",
                    "team_agno_error": str(e),
                    "team_agno_requested": False,
                    "updated_at": datetime.now(UTC)
                }
            }
        )

        # Notificar erro
        await websocket_manager.broadcast({
            "type": "team_agno_error",
            "clientId": str(client_id),
            "message": f"❌ Erro na análise Team Agno: {str(e)}",
            "timestamp": datetime.now(UTC).isoformat()
        })


@router.post("/clients/{client_id}/team-agno-analysis")
def request_team_agno_analysis(client_id: str, background_tasks: BackgroundTasks, projeto_descricao: str = ""):
    """
    ⚠️ DEPRECATED: Este endpoint está obsoleto.

    USE INSTEAD: POST /projects/{project_id}/generate-estimate

    Esta rota analisa clientes genéricos em vez de projetos específicos,
    o que não gera estimativas precisas. Use a nova API de projetos.
    """
    raise HTTPException(
        status_code=status.HTTP_410_GONE,
        detail={
            "error": "Endpoint obsoleto",
            "message": "Use POST /projects/{project_id}/generate-estimate para gerar estimativas de projetos específicos",
            "deprecated_since": "2024-01-10",
            "replacement": "/projects/{project_id}/generate-estimate"
        }
    )
