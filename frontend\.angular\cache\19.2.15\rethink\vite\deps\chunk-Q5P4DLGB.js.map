{"version": 3, "sources": ["../../../../../../node_modules/@mermaid-js/parser/dist/mermaid-parser.core.mjs"], "sourcesContent": ["import { GitGraphModule, createGitGraphServices } from \"./chunks/mermaid-parser.core/chunk-2NYFTIL2.mjs\";\nimport { InfoModule, createInfoServices } from \"./chunks/mermaid-parser.core/chunk-EXZZNE6F.mjs\";\nimport { PacketModule, createPacketServices } from \"./chunks/mermaid-parser.core/chunk-V4Q32G6S.mjs\";\nimport { PieModule, createPieServices } from \"./chunks/mermaid-parser.core/chunk-ROXG7S4E.mjs\";\nimport { ArchitectureModule, createArchitectureServices } from \"./chunks/mermaid-parser.core/chunk-C4OEIS7N.mjs\";\nimport { RadarModule, createRadarServices } from \"./chunks/mermaid-parser.core/chunk-2O5ZK7RR.mjs\";\nimport { AbstractMermaidTokenBuilder, AbstractMermaidValueConverter, Architecture, ArchitectureGeneratedModule, Branch, Commit, CommonTokenBuilder, CommonValueConverter, GitGraph, GitGraphGeneratedModule, Info, InfoGeneratedModule, Merge, MermaidGeneratedSharedModule, Packet, PacketBlock, PacketGeneratedModule, Pie, PieGeneratedModule, PieSection, Radar, RadarGeneratedModule, Statement, __name, isArchitecture, isBranch, isCommit, isCommon, isGitGraph, isInfo, isMerge, isPacket, isPacketBlock, isPie, isPieSection } from \"./chunks/mermaid-parser.core/chunk-7PKI6E2E.mjs\";\n\n// src/parse.ts\nvar parsers = {};\nvar initializers = {\n  info: /* @__PURE__ */__name(async () => {\n    const {\n      createInfoServices: createInfoServices2\n    } = await import(\"./chunks/mermaid-parser.core/info-4N47QTOZ.mjs\");\n    const parser = createInfoServices2().Info.parser.LangiumParser;\n    parsers.info = parser;\n  }, \"info\"),\n  packet: /* @__PURE__ */__name(async () => {\n    const {\n      createPacketServices: createPacketServices2\n    } = await import(\"./chunks/mermaid-parser.core/packet-KVYON367.mjs\");\n    const parser = createPacketServices2().Packet.parser.LangiumParser;\n    parsers.packet = parser;\n  }, \"packet\"),\n  pie: /* @__PURE__ */__name(async () => {\n    const {\n      createPieServices: createPieServices2\n    } = await import(\"./chunks/mermaid-parser.core/pie-R6RNRRYF.mjs\");\n    const parser = createPieServices2().Pie.parser.LangiumParser;\n    parsers.pie = parser;\n  }, \"pie\"),\n  architecture: /* @__PURE__ */__name(async () => {\n    const {\n      createArchitectureServices: createArchitectureServices2\n    } = await import(\"./chunks/mermaid-parser.core/architecture-4AB2E3PP.mjs\");\n    const parser = createArchitectureServices2().Architecture.parser.LangiumParser;\n    parsers.architecture = parser;\n  }, \"architecture\"),\n  gitGraph: /* @__PURE__ */__name(async () => {\n    const {\n      createGitGraphServices: createGitGraphServices2\n    } = await import(\"./chunks/mermaid-parser.core/gitGraph-O2Q2CXLX.mjs\");\n    const parser = createGitGraphServices2().GitGraph.parser.LangiumParser;\n    parsers.gitGraph = parser;\n  }, \"gitGraph\"),\n  radar: /* @__PURE__ */__name(async () => {\n    const {\n      createRadarServices: createRadarServices2\n    } = await import(\"./chunks/mermaid-parser.core/radar-MK3ICKWK.mjs\");\n    const parser = createRadarServices2().Radar.parser.LangiumParser;\n    parsers.radar = parser;\n  }, \"radar\")\n};\nasync function parse(diagramType, text) {\n  const initializer = initializers[diagramType];\n  if (!initializer) {\n    throw new Error(`Unknown diagram type: ${diagramType}`);\n  }\n  if (!parsers[diagramType]) {\n    await initializer();\n  }\n  const parser = parsers[diagramType];\n  const result = parser.parse(text);\n  if (result.lexerErrors.length > 0 || result.parserErrors.length > 0) {\n    throw new MermaidParseError(result);\n  }\n  return result.value;\n}\n__name(parse, \"parse\");\nvar MermaidParseError = class extends Error {\n  constructor(result) {\n    const lexerErrors = result.lexerErrors.map(err => err.message).join(\"\\n\");\n    const parserErrors = result.parserErrors.map(err => err.message).join(\"\\n\");\n    super(`Parsing failed: ${lexerErrors} ${parserErrors}`);\n    this.result = result;\n  }\n  static {\n    __name(this, \"MermaidParseError\");\n  }\n};\nexport { AbstractMermaidTokenBuilder, AbstractMermaidValueConverter, Architecture, ArchitectureGeneratedModule, ArchitectureModule, Branch, Commit, CommonTokenBuilder, CommonValueConverter, GitGraph, GitGraphGeneratedModule, GitGraphModule, Info, InfoGeneratedModule, InfoModule, Merge, MermaidGeneratedSharedModule, MermaidParseError, Packet, PacketBlock, PacketGeneratedModule, PacketModule, Pie, PieGeneratedModule, PieModule, PieSection, Radar, RadarGeneratedModule, RadarModule, Statement, createArchitectureServices, createGitGraphServices, createInfoServices, createPacketServices, createPieServices, createRadarServices, isArchitecture, isBranch, isCommit, isCommon, isGitGraph, isInfo, isMerge, isPacket, isPacketBlock, isPie, isPieSection, parse };"], "mappings": ";;;;;;;;AASA,IAAI,UAAU,CAAC;AACf,IAAI,eAAe;AAAA,EACjB,MAAqB,OAAO,MAAY;AACtC,UAAM;AAAA,MACJ,oBAAoB;AAAA,IACtB,IAAI,MAAM,OAAO,6BAAgD;AACjE,UAAM,SAAS,oBAAoB,EAAE,KAAK,OAAO;AACjD,YAAQ,OAAO;AAAA,EACjB,IAAG,MAAM;AAAA,EACT,QAAuB,OAAO,MAAY;AACxC,UAAM;AAAA,MACJ,sBAAsB;AAAA,IACxB,IAAI,MAAM,OAAO,+BAAkD;AACnE,UAAM,SAAS,sBAAsB,EAAE,OAAO,OAAO;AACrD,YAAQ,SAAS;AAAA,EACnB,IAAG,QAAQ;AAAA,EACX,KAAoB,OAAO,MAAY;AACrC,UAAM;AAAA,MACJ,mBAAmB;AAAA,IACrB,IAAI,MAAM,OAAO,4BAA+C;AAChE,UAAM,SAAS,mBAAmB,EAAE,IAAI,OAAO;AAC/C,YAAQ,MAAM;AAAA,EAChB,IAAG,KAAK;AAAA,EACR,cAA6B,OAAO,MAAY;AAC9C,UAAM;AAAA,MACJ,4BAA4B;AAAA,IAC9B,IAAI,MAAM,OAAO,qCAAwD;AACzE,UAAM,SAAS,4BAA4B,EAAE,aAAa,OAAO;AACjE,YAAQ,eAAe;AAAA,EACzB,IAAG,cAAc;AAAA,EACjB,UAAyB,OAAO,MAAY;AAC1C,UAAM;AAAA,MACJ,wBAAwB;AAAA,IAC1B,IAAI,MAAM,OAAO,iCAAoD;AACrE,UAAM,SAAS,wBAAwB,EAAE,SAAS,OAAO;AACzD,YAAQ,WAAW;AAAA,EACrB,IAAG,UAAU;AAAA,EACb,OAAsB,OAAO,MAAY;AACvC,UAAM;AAAA,MACJ,qBAAqB;AAAA,IACvB,IAAI,MAAM,OAAO,8BAAiD;AAClE,UAAM,SAAS,qBAAqB,EAAE,MAAM,OAAO;AACnD,YAAQ,QAAQ;AAAA,EAClB,IAAG,OAAO;AACZ;AACA,SAAe,MAAM,aAAa,MAAM;AAAA;AACtC,UAAM,cAAc,aAAa,WAAW;AAC5C,QAAI,CAAC,aAAa;AAChB,YAAM,IAAI,MAAM,yBAAyB,WAAW,EAAE;AAAA,IACxD;AACA,QAAI,CAAC,QAAQ,WAAW,GAAG;AACzB,YAAM,YAAY;AAAA,IACpB;AACA,UAAM,SAAS,QAAQ,WAAW;AAClC,UAAM,SAAS,OAAO,MAAM,IAAI;AAChC,QAAI,OAAO,YAAY,SAAS,KAAK,OAAO,aAAa,SAAS,GAAG;AACnE,YAAM,IAAI,kBAAkB,MAAM;AAAA,IACpC;AACA,WAAO,OAAO;AAAA,EAChB;AAAA;AACA,OAAO,OAAO,OAAO;AACrB,IAAI,oBAAoB,cAAc,MAAM;AAAA,EAC1C,YAAY,QAAQ;AAClB,UAAM,cAAc,OAAO,YAAY,IAAI,SAAO,IAAI,OAAO,EAAE,KAAK,IAAI;AACxE,UAAM,eAAe,OAAO,aAAa,IAAI,SAAO,IAAI,OAAO,EAAE,KAAK,IAAI;AAC1E,UAAM,mBAAmB,WAAW,IAAI,YAAY,EAAE;AACtD,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,OAAO;AACL,WAAO,MAAM,mBAAmB;AAAA,EAClC;AACF;", "names": []}