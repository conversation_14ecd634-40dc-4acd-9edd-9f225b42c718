/* ========================================
   Rethink Template - Modern Professional Style
   Innovation Scope AI
   ======================================== */

/* Importar fontes modernas */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;600&display=swap');

/* ========================================
   Configuração da Página
   ======================================== */
@page {
	size: A4;
	margin: 2.5cm 2cm 3cm 2cm;

	@top-left {
		content: 'Innovation Scope AI';
		font-family: 'Inter', sans-serif;
		font-size: 8pt;
		font-weight: 600;
		color: #667eea;
	}

	@top-right {
		content: 'CONFIDENCIAL';
		font-family: 'Inter', sans-serif;
		font-size: 8pt;
		font-weight: 500;
		color: #e53e3e;
		text-transform: uppercase;
		letter-spacing: 0.5px;
	}

	@bottom-center {
		content: counter(page) ' de ' counter(pages);
		font-family: 'Inter', sans-serif;
		font-size: 9pt;
		color: #718096;
	}

	@bottom-right {
		content: '© ' attr(data-year) ' Innovation Scope AI';
		font-family: 'Inter', sans-serif;
		font-size: 8pt;
		color: #a0aec0;
	}
}

/* Configuração de primeira página */
@page :first {
	@top-left {
		content: none;
	}
	@top-right {
		content: none;
	}
}

/* ========================================
   Reset e Base
   ======================================== */
* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

html {
	font-size: 10pt;
}

body {
	font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
	font-size: 11pt;
	line-height: 1.7;
	color: #2d3748;
	background: white;
	font-weight: 400;
	text-rendering: optimizeLegibility;
	-webkit-font-smoothing: antialiased;
}

/* ========================================
   Tipografia - Títulos
   ======================================== */
h1 {
	font-size: 32pt;
	font-weight: 800;
	line-height: 1.2;
	margin: 0 0 20px 0;
	color: #1a202c;
	letter-spacing: -0.02em;
	page-break-after: avoid;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	-webkit-background-clip: text;
	background-clip: text;
	-webkit-text-fill-color: transparent;
}

h2 {
	font-size: 20pt;
	font-weight: 700;
	line-height: 1.3;
	margin: 40px 0 20px 0;
	color: #667eea;
	letter-spacing: -0.01em;
	page-break-after: avoid;
	border-bottom: 3px solid #e2e8f0;
	padding-bottom: 12px;
}

h3 {
	font-size: 14pt;
	font-weight: 600;
	line-height: 1.4;
	margin: 30px 0 15px 0;
	color: #4a5568;
	page-break-after: avoid;
}

h4 {
	font-size: 12pt;
	font-weight: 600;
	line-height: 1.5;
	margin: 20px 0 10px 0;
	color: #718096;
	page-break-after: avoid;
}

/* ========================================
   Texto e Parágrafos
   ======================================== */
p {
	margin: 0 0 15px 0;
	text-align: justify;
	hyphens: auto;
	orphans: 3;
	widows: 3;
}

/* Primeiro parágrafo após título */
h1 + p,
h2 + p,
h3 + p {
	margin-top: 10px;
}

/* Parágrafo de introdução */
.lead,
p:first-of-type {
	font-size: 12pt;
	line-height: 1.8;
	color: #4a5568;
	font-weight: 400;
}

/* ========================================
   Listas
   ======================================== */
ul,
ol {
	margin: 15px 0 20px 30px;
	padding: 0;
}

li {
	margin-bottom: 8px;
	line-height: 1.6;
}

/* Bullets customizados */
ul li::marker {
	color: #667eea;
	font-weight: 700;
}

/* Listas aninhadas */
ul ul,
ol ol,
ul ol,
ol ul {
	margin-top: 8px;
	margin-bottom: 8px;
}

/* ========================================
   Ênfase e Destaques
   ======================================== */
strong,
b {
	font-weight: 600;
	color: #1a202c;
}

em,
i {
	font-style: italic;
	color: #4a5568;
}

/* Destaque especial */
mark,
.highlight {
	background: linear-gradient(to right, rgba(102, 126, 234, 0.15), rgba(118, 75, 162, 0.15));
	padding: 2px 6px;
	border-radius: 4px;
	font-weight: 500;
}

/* ========================================
   Blockquotes
   ======================================== */
blockquote {
	margin: 25px 0;
	padding: 20px 25px;
	background: linear-gradient(to right, #f7fafc, #edf2f7);
	border-left: 5px solid #667eea;
	border-radius: 0 8px 8px 0;
	font-style: italic;
	color: #4a5568;
	page-break-inside: avoid;
}

blockquote p:last-child {
	margin-bottom: 0;
}

/* ========================================
   Tabelas
   ======================================== */
table {
	width: 100%;
	margin: 25px 0;
	border-collapse: separate;
	border-spacing: 0;
	border-radius: 12px;
	overflow: hidden;
	box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
	page-break-inside: avoid;
	font-size: 10pt;
}

thead {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

th {
	padding: 15px 20px;
	text-align: left;
	font-weight: 600;
	color: white;
	text-transform: uppercase;
	font-size: 9pt;
	letter-spacing: 0.05em;
}

td {
	padding: 12px 20px;
	border-bottom: 1px solid #e2e8f0;
}

tbody tr:last-child td {
	border-bottom: none;
}

tbody tr:nth-child(even) {
	background-color: #f7fafc;
}

tbody tr:hover {
	background-color: #edf2f7;
}

/* ========================================
   Código
   ======================================== */
code {
	font-family: 'JetBrains Mono', 'Consolas', 'Monaco', monospace;
	font-size: 9pt;
	background-color: #f7fafc;
	padding: 3px 6px;
	border-radius: 4px;
	color: #5a67d8;
	font-weight: 400;
}

pre {
	margin: 20px 0;
	padding: 20px;
	background-color: #1a202c;
	border-radius: 8px;
	overflow-x: auto;
	page-break-inside: avoid;
}

pre code {
	background-color: transparent;
	color: #e2e8f0;
	padding: 0;
	display: block;
	white-space: pre-wrap;
	word-wrap: break-word;
}

/* ========================================
   Separadores
   ======================================== */
hr {
	margin: 40px 0;
	border: none;
	height: 3px;
	background: linear-gradient(to right, #667eea, #764ba2, #667eea);
	border-radius: 2px;
	opacity: 0.3;
}

/* ========================================
   Links
   ======================================== */
a {
	color: #667eea;
	text-decoration: none;
	font-weight: 500;
	border-bottom: 1px solid transparent;
	transition: all 0.2s ease;
}

a:hover {
	color: #5a67d8;
	border-bottom-color: #5a67d8;
}

/* ========================================
   Caixas Especiais
   ======================================== */
.info-box,
.warning-box,
.success-box,
.metric-card {
	margin: 20px 0;
	padding: 20px 25px;
	border-radius: 12px;
	page-break-inside: avoid;
	position: relative;
	overflow: hidden;
}

.info-box {
	background: linear-gradient(135deg, #ebf4ff 0%, #e9d8fd 100%);
	border: 1px solid #c3dafe;
}

.warning-box {
	background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
	border: 1px solid #fcd34d;
}

.success-box {
	background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
	border: 1px solid #6ee7b7;
}

.metric-card {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	text-align: center;
	box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.metric-card .metric-value {
	font-size: 28pt;
	font-weight: 800;
	margin: 10px 0;
}

.metric-card .metric-label {
	font-size: 10pt;
	opacity: 0.9;
	text-transform: uppercase;
	letter-spacing: 0.05em;
}

/* ========================================
   Seções Específicas
   ======================================== */

/* Sumário Executivo */
#sumário-executivo {
	background: linear-gradient(to bottom, #f7fafc, white);
	padding: 30px;
	border-radius: 12px;
	margin-bottom: 40px;
}

/* SWOT */
.swot-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 20px;
	margin: 20px 0;
}

.swot-box {
	padding: 20px;
	border-radius: 8px;
}

.swot-box h4 {
	margin-top: 0;
	font-size: 11pt;
	text-transform: uppercase;
	letter-spacing: 0.05em;
}

/* Forças */
.swot-strengths {
	background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
	border: 1px solid #6ee7b7;
}

/* Fraquezas */
.swot-weaknesses {
	background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
	border: 1px solid #f87171;
}

/* Oportunidades */
.swot-opportunities {
	background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
	border: 1px solid #60a5fa;
}

/* Ameaças */
.swot-threats {
	background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
	border: 1px solid #fbbf24;
}

/* ========================================
   Rodapé e Metadados
   ======================================== */
.report-footer {
	margin-top: 60px;
	padding-top: 30px;
	border-top: 2px solid #e2e8f0;
	font-size: 9pt;
	color: #718096;
	text-align: center;
}

.confidential-notice {
	margin: 20px 0;
	padding: 15px;
	background: #fee2e2;
	border: 1px solid #fc8181;
	border-radius: 8px;
	text-align: center;
	font-weight: 600;
	color: #c53030;
	text-transform: uppercase;
	letter-spacing: 0.05em;
}

/* ========================================
   Utilitários
   ======================================== */
.text-center {
	text-align: center;
}
.text-right {
	text-align: right;
}
.text-left {
	text-align: left;
}

.mt-1 {
	margin-top: 10px;
}
.mt-2 {
	margin-top: 20px;
}
.mt-3 {
	margin-top: 30px;
}
.mt-4 {
	margin-top: 40px;
}

.mb-1 {
	margin-bottom: 10px;
}
.mb-2 {
	margin-bottom: 20px;
}
.mb-3 {
	margin-bottom: 30px;
}
.mb-4 {
	margin-bottom: 40px;
}

.page-break {
	page-break-after: always;
}
.no-break {
	page-break-inside: avoid;
}

/* ========================================
   Impressão e Mídia
   ======================================== */
@media print {
	body {
		font-size: 10pt;
		color: black;
	}

	a {
		color: #4a5568;
		text-decoration: none;
	}

	.no-print {
		display: none !important;
	}

	h1,
	h2,
	h3,
	h4 {
		page-break-after: avoid;
	}

	table,
	figure,
	blockquote {
		page-break-inside: avoid;
	}
}
