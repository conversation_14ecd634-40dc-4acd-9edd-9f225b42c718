// Declarações de tipos para extensões do Cytoscape.js

declare module 'cytoscape-dagre' {
	import { Ext } from 'cytoscape';

	interface DagreLayoutOptions {
		name: 'dagre';
		rankDir?: 'TB' | 'BT' | 'LR' | 'RL';
		align?: 'UL' | 'UR' | 'DL' | 'DR';
		rankSep?: number;
		nodeSep?: number;
		edgeSep?: number;
		marginX?: number;
		marginY?: number;
		acyclicer?: 'greedy' | undefined;
		ranker?: 'network-simplex' | 'tight-tree' | 'longest-path';
		minLen?: (edge: any) => number;
		edgeWeight?: (edge: any) => number;
		animate?: boolean;
		animationDuration?: number;
		animationEasing?: string;
		boundingBox?: any;
		transform?: (node: any, pos: any) => any;
		ready?: () => void;
		stop?: () => void;
	}

	const dagre: Ext;
	export = dagre;
}

declare module 'cytoscape-elk' {
	import { Ext } from 'cytoscape';

	interface ElkLayoutOptions {
		name: 'elk';
		elk?: {
			'algorithm'?: string;
			'elk.direction'?: 'UP' | 'DOWN' | 'LEFT' | 'RIGHT';
			'elk.spacing.nodeNode'?: string;
			'elk.layered.spacing.nodeNodeBetweenLayers'?: string;
			[key: string]: any;
		};
		priority?: (edge: any) => number;
		animate?: boolean;
		animationDuration?: number;
		animationEasing?: string;
		transform?: (node: any, pos: any) => any;
		ready?: () => void;
		stop?: () => void;
	}

	const elk: Ext;
	export = elk;
}
