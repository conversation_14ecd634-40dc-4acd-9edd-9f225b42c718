import { <PERSON><PERSON><PERSON>, Ng<PERSON><PERSON>, NgIf } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { MenuItem, MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { CheckboxModule } from 'primeng/checkbox';
import { ColorPickerModule } from 'primeng/colorpicker';
import { DialogModule } from 'primeng/dialog';
import { DividerModule } from 'primeng/divider';
import { InputSwitchModule } from 'primeng/inputswitch';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextarea } from 'primeng/inputtextarea';
import { PanelModule } from 'primeng/panel';
import { SelectModule } from 'primeng/select';
import { SelectButtonModule } from 'primeng/selectbutton';
import { TabsModule } from 'primeng/tabs';
import { TooltipModule } from 'primeng/tooltip';

import { INotificationSetting, ISettingItem, ISettingsSection, IUserProfile } from './setting.interface';

@Component({
	selector: 'app-settings-home',
	standalone: true,
	imports: [
		NgClass,
		NgFor,
		NgIf,
		FormsModule,
		ReactiveFormsModule,
		ButtonModule,
		CardModule,
		CheckboxModule,
		ColorPickerModule,
		DialogModule,
		DividerModule,
		InputSwitchModule,
		InputTextModule,
		InputTextarea,
		PanelModule,
		SelectButtonModule,
		SelectModule,
		TabsModule,
		TooltipModule,
	],
	templateUrl: './settings-home.component.html',
	styles: [
		`
			:host ::ng-deep .p-tabs .p-tabs-nav {
				background: #f8f9fa;
				border-radius: 0.5rem 0.5rem 0 0;
				padding: 0.5rem 1rem 0 1rem;
				border: none;
				border-bottom: 1px solid #e9ecef;
			}
			:host ::ng-deep .p-tabview .p-tabview-nav {
				background: #f8f9fa;
				border-radius: 0.5rem 0.5rem 0 0;
				padding: 0.5rem 1rem 0 1rem;
				border: none;
				border-bottom: 1px solid #e9ecef;
			}

			:host ::ng-deep .p-tabview .p-tabview-nav li .p-tabview-nav-link {
				border: none;
				color: #495057;
				padding: 1rem 1.5rem;
				font-weight: 500;
				border-radius: 0.5rem 0.5rem 0 0;
				transition: all 0.2s;
			}

			:host ::ng-deep .p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link {
				background: white;
				color: #3b82f6;
				border-bottom: 2px solid #3b82f6;
			}

			:host ::ng-deep .p-tabview .p-tabview-panels {
				background: white;
				border-radius: 0 0 0.5rem 0.5rem;
				padding: 2rem;
				border: none;
				box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
			}

			:host ::ng-deep .p-panel {
				margin-bottom: 1.5rem;
			}

			:host ::ng-deep .p-panel .p-panel-header {
				background: #f8f9fa;
				border: 1px solid #e9ecef;
				color: #495057;
				padding: 1rem 1.5rem;
				font-weight: 600;
			}

			:host ::ng-deep .p-panel .p-panel-content {
				background: white;
				border: 1px solid #e9ecef;
				border-top: 0;
				padding: 1.5rem;
			}

			.setting-item {
				margin-bottom: 1.5rem;
			}

			.setting-item-label {
				font-weight: 500;
				margin-bottom: 0.5rem;
			}

			.setting-item-description {
				font-size: 0.875rem;
				color: #6c757d;
				margin-bottom: 0.75rem;
			}

			.status-tag-sm {
				font-size: 0.8rem !important;
				padding: 0.5rem 0.7rem !important;
				height: 1.1rem !important;
				line-height: 0.9 !important;
				margin: 0 !important;
				display: inline-flex !important;
				align-items: center !important;
				font-weight: normal !important;
				border-radius: 0.8rem !important;
			}
		`,
	],
})
export class SettingsHomeComponent implements OnInit {
	public activeTabIndex = 0;
	public sections: Array<ISettingsSection> = [];
	public settings: Array<ISettingItem> = [];
	public navigationItems: Array<MenuItem> = [];
	public userProfile: IUserProfile | null = null;
	public notificationSettings: Array<INotificationSetting> = [];
	public isLoading = false;
	public isSaving = false;
	public showProfileDialog = false;

	// Opções para configurações de visualização
	public themeOptions = [
		{ label: 'Claro', value: 'light' },
		{ label: 'Escuro', value: 'dark' },
		{ label: 'Sistema', value: 'system' },
	];

	public dateFormatOptions = [
		{ label: 'DD/MM/YYYY', value: 'dd/MM/yyyy' },
		{ label: 'MM/DD/YYYY', value: 'MM/dd/yyyy' },
		{ label: 'YYYY-MM-DD', value: 'yyyy-MM-dd' },
	];

	public currencyOptions = [
		{ label: 'Real (R$)', value: 'BRL' },
		{ label: 'Dólar (US$)', value: 'USD' },
		{ label: 'Euro (€)', value: 'EUR' },
	];

	constructor(private messageService: MessageService) {}

	public ngOnInit(): void {
		this.loadSections();
		this.loadSettings();
		this.loadUserProfile();
		this.loadNotificationSettings();
		this.setupNavigation();
	}

	private loadSections(): void {
		this.isLoading = true;
		// Em produção, carregar de uma API
		this.sections = [
			{
				id: 'general',
				title: 'Configurações Gerais',
				icon: 'pi pi-cog',
				description: 'Configurações básicas do sistema',
				order: 1,
			},
			{
				id: 'appearance',
				title: 'Aparência',
				icon: 'pi pi-palette',
				description: 'Preferências de tema e exibição',
				order: 2,
			},
			{
				id: 'notifications',
				title: 'Notificações',
				icon: 'pi pi-bell',
				description: 'Configurar alertas e notificações',
				order: 3,
			},
			{
				id: 'security',
				title: 'Segurança',
				icon: 'pi pi-shield',
				description: 'Configurações de segurança e acesso',
				order: 4,
			},
			{
				id: 'integrations',
				title: 'Integrações',
				icon: 'pi pi-link',
				description: 'Conectar com outros serviços',
				order: 5,
			},
		];
		this.isLoading = false;
	}

	private loadSettings(): void {
		this.isLoading = true;
		// Em produção, carregar de uma API
		this.settings = [
			{
				id: '1',
				sectionId: 'general',
				label: 'Nome da Empresa',
				description: 'Nome da sua empresa ou organização',
				type: 'text',
				key: 'company_name',
				value: 'Minha Empresa Ltda.',
				required: true,
				order: 1,
			},
			{
				id: '2',
				sectionId: 'general',
				label: 'Email de Contato',
				description: 'Email principal para comunicações do sistema',
				type: 'email',
				key: 'contact_email',
				value: '<EMAIL>',
				required: true,
				validation: 'email',
				validationMessage: 'Por favor, insira um email válido',
				order: 2,
			},
			{
				id: '3',
				sectionId: 'appearance',
				label: 'Tema',
				description: 'Escolha o tema de interface preferido',
				type: 'select',
				key: 'theme',
				value: 'light',
				options: this.themeOptions,
				order: 1,
				required: false,
			},
			{
				id: '4',
				sectionId: 'appearance',
				label: 'Formato de Data',
				description: 'Formato para exibição de datas no sistema',
				type: 'select',
				key: 'date_format',
				value: 'dd/MM/yyyy',
				options: this.dateFormatOptions,
				order: 2,
				required: false,
			},
			{
				id: '5',
				sectionId: 'appearance',
				label: 'Moeda',
				description: 'Moeda padrão para valores monetários',
				type: 'select',
				key: 'currency',
				value: 'BRL',
				options: this.currencyOptions,
				order: 3,
				required: false,
			},
			{
				id: '6',
				sectionId: 'appearance',
				label: 'Cor Primária',
				description: 'Cor principal do tema (cabeçalhos, botões, etc)',
				type: 'color',
				key: 'primary_color',
				value: '#3B82F6',
				order: 4,
				required: false,
			},
			{
				id: '7',
				sectionId: 'security',
				label: 'Exigir autenticação em dois fatores',
				description: 'Aumenta a segurança exigindo um segundo método de verificação ao fazer login',
				type: 'boolean',
				key: 'require_2fa',
				value: false,
				order: 1,
				required: false,
			},
			{
				id: '8',
				sectionId: 'security',
				label: 'Tempo de Expiração da Sessão (minutos)',
				description: 'Tempo de inatividade antes do logout automático',
				type: 'number',
				key: 'session_timeout',
				value: 30,
				order: 2,
				required: false,
			},
			{
				id: '9',
				sectionId: 'integrations',
				label: 'API Key GitHub',
				description: 'Chave de API para integração com GitHub',
				type: 'text',
				key: 'github_api_key',
				value: '',
				placeholder: 'Insira sua API Key',
				order: 1,
				required: false,
			},
		];
		this.isLoading = false;
	}

	private loadUserProfile(): void {
		this.isLoading = true;
		// Em produção, carregar de uma API
		this.userProfile = {
			id: 1,
			name: 'João Silva',
			email: '<EMAIL>',
			avatar: 'https://cdn.pixabay.com/photo/2016/08/08/09/17/avatar-1577909_1280.png',
			role: 'Administrador',
			department: 'Tecnologia',
			position: 'Gerente de Projetos',
			phone: '+55 11 98765-4321',
			lastLogin: '2025-05-20T10:30:45',
			active: true,
			preferences: [
				{
					id: '1',
					userId: 1,
					settingKey: 'theme',
					settingValue: 'light',
					lastUpdated: '2025-04-15T08:20:30',
				},
				{
					id: '2',
					userId: 1,
					settingKey: 'date_format',
					settingValue: 'dd/MM/yyyy',
					lastUpdated: '2025-04-15T08:20:30',
				},
			],
		};
		this.isLoading = false;
	}

	private loadNotificationSettings(): void {
		this.isLoading = true;
		// Em produção, carregar de uma API
		this.notificationSettings = [
			{
				id: '1',
				type: 'project_created',
				description: 'Novos projetos criados',
				email: true,
				push: true,
				inApp: true,
			},
			{
				id: '2',
				type: 'project_updated',
				description: 'Atualizações em projetos',
				email: false,
				push: true,
				inApp: true,
			},
			{
				id: '3',
				type: 'project_deadline',
				description: 'Prazos de projetos próximos ao fim',
				email: true,
				push: true,
				inApp: true,
			},
			{
				id: '4',
				type: 'client_created',
				description: 'Novos clientes cadastrados',
				email: true,
				push: false,
				inApp: true,
			},
			{
				id: '5',
				type: 'task_assigned',
				description: 'Tarefas atribuídas a você',
				email: true,
				push: true,
				inApp: true,
			},
			{
				id: '6',
				type: 'task_updated',
				description: 'Atualizações em tarefas',
				email: false,
				push: true,
				inApp: true,
			},
			{
				id: '7',
				type: 'comment_mentioned',
				description: 'Menções em comentários',
				email: true,
				push: true,
				inApp: true,
			},
		];
		this.isLoading = false;
	}

	private setupNavigation(): void {
		this.navigationItems = this.sections.map(section => ({
			label: section.title,
			icon: section.icon,
			command: () => this.onSectionChange(section.id),
		}));
	}

	public onSectionChange(sectionId: string): void {
		const index = this.sections.findIndex(section => section.id === sectionId);
		if (index !== -1) {
			this.activeTabIndex = index;
		}
	}

	public getSettingsForSection(sectionId: string): Array<ISettingItem> {
		return this.settings.filter(setting => setting.sectionId === sectionId).sort((a, b) => a.order - b.order);
	}

	public saveSettings(): void {
		this.isSaving = true;

		// Em produção, esta seria uma chamada para uma API
		setTimeout(() => {
			this.isSaving = false;
			this.messageService.add({
				severity: 'success',
				summary: 'Configurações Salvas',
				detail: 'Suas configurações foram salvas com sucesso.',
			});
		}, 1000);
	}

	public updateNotification(setting: INotificationSetting, _type: 'email' | 'push' | 'inApp'): void {
		// Em produção, esta seria uma chamada para uma API para atualizar a configuração específica
		this.messageService.add({
			severity: 'info',
			summary: 'Preferência Atualizada',
			detail: `Configuração de "${setting.description}" foi atualizada.`,
		});
	}

	public saveUserProfile(): void {
		this.isSaving = true;

		// Em produção, esta seria uma chamada para uma API
		setTimeout(() => {
			this.isSaving = false;
			this.showProfileDialog = false;
			this.messageService.add({
				severity: 'success',
				summary: 'Perfil Atualizado',
				detail: 'Seu perfil foi atualizado com sucesso.',
			});
		}, 1000);
	}

	public openProfileDialog(): void {
		this.showProfileDialog = true;
	}

	public formatDate(dateString: string): string {
		const options: Intl.DateTimeFormatOptions = {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit',
		};
		return new Date(dateString).toLocaleDateString('pt-BR', options);
	}

	public resetSettings(): void {
		// Em produção, esta seria uma chamada para uma API para restaurar as configurações padrão
		this.messageService.add({
			severity: 'info',
			summary: 'Configurações Restauradas',
			detail: 'As configurações foram restauradas para os valores padrão.',
		});
	}

	public testNotifications(): void {
		// Em produção, esta seria uma chamada para uma API para enviar notificações de teste
		this.messageService.add({
			severity: 'info',
			summary: 'Teste Enviado',
			detail: 'Um teste de notificação foi enviado para seus canais configurados.',
		});
	}
}
