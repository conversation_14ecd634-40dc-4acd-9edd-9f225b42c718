"""Classe base para eventos de domínio."""

from abc import ABC
from dataclasses import dataclass, field
from datetime import datetime
from typing import Any, Dict
import uuid


@dataclass(frozen=True)
class DomainEvent(ABC):
    """Classe base abstrata para todos os eventos de domínio.

    Eventos são imutáveis e representam algo que aconteceu no passado.
    """

    # ID único do evento
    event_id: str = field(default_factory=lambda: str(uuid.uuid4()))

    # Quando o evento ocorreu
    occurred_at: datetime = field(default_factory=datetime.utcnow)

    # ID do agregado que gerou o evento
    aggregate_id: str = field(default="")

    # Versão do agregado quando o evento foi gerado
    aggregate_version: int = field(default=1)

    # Metadados adicionais do evento
    metadata: Dict[str, Any] = field(default_factory=dict)

    @property
    def event_name(self) -> str:
        """Retorna o nome do evento baseado no nome da classe."""
        return self.__class__.__name__

    @property
    def event_version(self) -> str:
        """Retorna a versão do evento para controle de schema."""
        return "1.0"

    def to_dict(self) -> Dict[str, Any]:
        """Converte o evento para dicionário.

        Returns:
            Representação do evento como dicionário
        """
        return {
            "event_id": self.event_id,
            "event_name": self.event_name,
            "event_version": self.event_version,
            "occurred_at": self.occurred_at.isoformat(),
            "aggregate_id": self.aggregate_id,
            "aggregate_version": self.aggregate_version,
            "data": self._get_event_data(),
            "metadata": self.metadata
        }

    def _get_event_data(self) -> Dict[str, Any]:
        """Obtém os dados específicos do evento.

        Deve ser sobrescrito pelas subclasses para incluir dados específicos.

        Returns:
            Dados do evento
        """
        # Remove campos base do dataclass
        data = {}
        for key, value in self.__dict__.items():
            if key not in {"event_id", "occurred_at", "aggregate_id",
                           "aggregate_version", "metadata"}:
                data[key] = value
        return data
