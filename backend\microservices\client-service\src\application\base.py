"""Base classes para a camada de aplicação."""

from typing import TypeVar, Generic, Optional, Union
from dataclasses import dataclass

T = TypeVar('T')
E = TypeVar('E')


@dataclass(frozen=True)
class Success(Generic[T]):
    """Representa um resultado de sucesso."""
    value: T


@dataclass(frozen=True)
class Failure(Generic[E]):
    """Representa um resultado de falha."""
    error: E


Result = Union[Success[T], Failure[E]]


def is_success(result: Result[T, E]) -> bool:
    """Verifica se o resultado é sucesso.

    Args:
        result: Resultado a verificar

    Returns:
        True se for Success
    """
    return isinstance(result, Success)


def is_failure(result: Result[T, E]) -> bool:
    """Verifica se o resultado é falha.

    Args:
        result: Resultado a verificar

    Returns:
        True se for Failure
    """
    return isinstance(result, Failure)


def unwrap(result: Result[T, E]) -> T:
    """Desembrulha um resultado de sucesso.

    Args:
        result: Resultado a desembrulhar

    Returns:
        Valor contido no Success

    Raises:
        ValueError: Se for Failure
    """
    if isinstance(result, Success):
        return result.value
    raise ValueError(f"Cannot unwrap failure: {result.error}")


def unwrap_error(result: Result[T, E]) -> E:
    """Desembrulha um resultado de erro.

    Args:
        result: Resultado a desembrulhar

    Returns:
        Erro contido no Failure

    Raises:
        ValueError: Se for Success
    """
    if isinstance(result, Failure):
        return result.error
    raise ValueError(f"Cannot unwrap error from success: {result.value}")
