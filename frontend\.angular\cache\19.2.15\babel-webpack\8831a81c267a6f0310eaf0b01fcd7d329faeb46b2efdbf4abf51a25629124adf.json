{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component } from '@angular/core';\nimport { BaseIcon } from 'primeng/icons/baseicon';\nclass CaretRightIcon extends BaseIcon {\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵCaretRightIcon_BaseFactory;\n    return function CaretRightIcon_Factory(__ngFactoryType__) {\n      return (ɵCaretRightIcon_BaseFactory || (ɵCaretRightIcon_BaseFactory = i0.ɵɵgetInheritedFactory(CaretRightIcon)))(__ngFactoryType__ || CaretRightIcon);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: CaretRightIcon,\n    selectors: [[\"CaretRightIcon\"]],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    decls: 2,\n    vars: 5,\n    consts: [[\"width\", \"14\", \"height\", \"14\", \"viewBox\", \"0 0 14 14\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"d\", \"M3.44433 13C3.34244 12.9987 3.24216 12.9744 3.15099 12.9289C3.03947 12.8742 2.94542 12.7895 2.87945 12.6843C2.81349 12.5791 2.77823 12.4575 2.77765 12.3333V1.66633C2.77823 1.54214 2.81349 1.42057 2.87945 1.31534C2.94542 1.21011 3.03947 1.1254 3.15099 1.07076C3.26082 1.01524 3.38401 0.991634 3.50658 1.00263C3.62914 1.01363 3.74617 1.05879 3.84435 1.13298L10.9557 6.46647C11.0385 6.52857 11.1057 6.6091 11.152 6.70167C11.1982 6.79424 11.2223 6.89632 11.2223 6.99982C11.2223 7.10332 11.1982 7.2054 11.152 7.29797C11.1057 7.39054 11.0385 7.47107 10.9557 7.53317L3.84435 12.8667C3.72925 12.9538 3.58869 13.0006 3.44433 13ZM4.11102 2.9997V10.9999L9.44451 6.99982L4.11102 2.9997Z\", \"fill\", \"currentColor\"]],\n    template: function CaretRightIcon_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(0, \"svg\", 0);\n        i0.ɵɵelement(1, \"path\", 1);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.getClassNames());\n        i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel)(\"aria-hidden\", ctx.ariaHidden)(\"role\", ctx.role);\n      }\n    },\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CaretRightIcon, [{\n    type: Component,\n    args: [{\n      selector: 'CaretRightIcon',\n      standalone: true,\n      template: `\n        <svg width=\"14\" height=\"14\" viewBox=\"0 0 14 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" [attr.aria-label]=\"ariaLabel\" [attr.aria-hidden]=\"ariaHidden\" [attr.role]=\"role\" [class]=\"getClassNames()\">\n            <path\n                d=\"M3.44433 13C3.34244 12.9987 3.24216 12.9744 3.15099 12.9289C3.03947 12.8742 2.94542 12.7895 2.87945 12.6843C2.81349 12.5791 2.77823 12.4575 2.77765 12.3333V1.66633C2.77823 1.54214 2.81349 1.42057 2.87945 1.31534C2.94542 1.21011 3.03947 1.1254 3.15099 1.07076C3.26082 1.01524 3.38401 0.991634 3.50658 1.00263C3.62914 1.01363 3.74617 1.05879 3.84435 1.13298L10.9557 6.46647C11.0385 6.52857 11.1057 6.6091 11.152 6.70167C11.1982 6.79424 11.2223 6.89632 11.2223 6.99982C11.2223 7.10332 11.1982 7.2054 11.152 7.29797C11.1057 7.39054 11.0385 7.47107 10.9557 7.53317L3.84435 12.8667C3.72925 12.9538 3.58869 13.0006 3.44433 13ZM4.11102 2.9997V10.9999L9.44451 6.99982L4.11102 2.9997Z\"\n                fill=\"currentColor\"\n            />\n        </svg>\n    `\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CaretRightIcon };", "map": {"version": 3, "names": ["i0", "Component", "BaseIcon", "CaretRightIcon", "ɵfac", "ɵCaretRightIcon_BaseFactory", "CaretRightIcon_Factory", "__ngFactoryType__", "ɵɵgetInheritedFactory", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "CaretRightIcon_Template", "rf", "ctx", "ɵɵnamespaceSVG", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵclassMap", "getClassNames", "ɵɵattribute", "aria<PERSON><PERSON><PERSON>", "ariaHidden", "role", "encapsulation", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "standalone"], "sources": ["C:/projetos/scope-ai/frontend/node_modules/primeng/fesm2022/primeng-icons-caretright.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component } from '@angular/core';\nimport { BaseIcon } from 'primeng/icons/baseicon';\n\nclass CaretRightIcon extends BaseIcon {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: CaretRightIcon, deps: null, target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.10\", type: CaretRightIcon, isStandalone: true, selector: \"CaretRightIcon\", usesInheritance: true, ngImport: i0, template: `\n        <svg width=\"14\" height=\"14\" viewBox=\"0 0 14 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" [attr.aria-label]=\"ariaLabel\" [attr.aria-hidden]=\"ariaHidden\" [attr.role]=\"role\" [class]=\"getClassNames()\">\n            <path\n                d=\"M3.44433 13C3.34244 12.9987 3.24216 12.9744 3.15099 12.9289C3.03947 12.8742 2.94542 12.7895 2.87945 12.6843C2.81349 12.5791 2.77823 12.4575 2.77765 12.3333V1.66633C2.77823 1.54214 2.81349 1.42057 2.87945 1.31534C2.94542 1.21011 3.03947 1.1254 3.15099 1.07076C3.26082 1.01524 3.38401 0.991634 3.50658 1.00263C3.62914 1.01363 3.74617 1.05879 3.84435 1.13298L10.9557 6.46647C11.0385 6.52857 11.1057 6.6091 11.152 6.70167C11.1982 6.79424 11.2223 6.89632 11.2223 6.99982C11.2223 7.10332 11.1982 7.2054 11.152 7.29797C11.1057 7.39054 11.0385 7.47107 10.9557 7.53317L3.84435 12.8667C3.72925 12.9538 3.58869 13.0006 3.44433 13ZM4.11102 2.9997V10.9999L9.44451 6.99982L4.11102 2.9997Z\"\n                fill=\"currentColor\"\n            />\n        </svg>\n    `, isInline: true });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: CaretRightIcon, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'CaretRightIcon',\n                    standalone: true,\n                    template: `\n        <svg width=\"14\" height=\"14\" viewBox=\"0 0 14 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" [attr.aria-label]=\"ariaLabel\" [attr.aria-hidden]=\"ariaHidden\" [attr.role]=\"role\" [class]=\"getClassNames()\">\n            <path\n                d=\"M3.44433 13C3.34244 12.9987 3.24216 12.9744 3.15099 12.9289C3.03947 12.8742 2.94542 12.7895 2.87945 12.6843C2.81349 12.5791 2.77823 12.4575 2.77765 12.3333V1.66633C2.77823 1.54214 2.81349 1.42057 2.87945 1.31534C2.94542 1.21011 3.03947 1.1254 3.15099 1.07076C3.26082 1.01524 3.38401 0.991634 3.50658 1.00263C3.62914 1.01363 3.74617 1.05879 3.84435 1.13298L10.9557 6.46647C11.0385 6.52857 11.1057 6.6091 11.152 6.70167C11.1982 6.79424 11.2223 6.89632 11.2223 6.99982C11.2223 7.10332 11.1982 7.2054 11.152 7.29797C11.1057 7.39054 11.0385 7.47107 10.9557 7.53317L3.84435 12.8667C3.72925 12.9538 3.58869 13.0006 3.44433 13ZM4.11102 2.9997V10.9999L9.44451 6.99982L4.11102 2.9997Z\"\n                fill=\"currentColor\"\n            />\n        </svg>\n    `\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CaretRightIcon };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,QAAQ,eAAe;AACzC,SAASC,QAAQ,QAAQ,wBAAwB;AAEjD,MAAMC,cAAc,SAASD,QAAQ,CAAC;EAClC,OAAOE,IAAI;IAAA,IAAAC,2BAAA;IAAA,gBAAAC,uBAAAC,iBAAA;MAAA,QAAAF,2BAAA,KAAAA,2BAAA,GAA+EL,EAAE,CAAAQ,qBAAA,CAAQL,cAAc,IAAAI,iBAAA,IAAdJ,cAAc;IAAA;EAAA;EAClH,OAAOM,IAAI,kBAD+ET,EAAE,CAAAU,iBAAA;IAAAC,IAAA,EACJR,cAAc;IAAAS,SAAA;IAAAC,QAAA,GADZb,EAAE,CAAAc,0BAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFpB,EAAE,CAAAsB,cAAA;QAAFtB,EAAE,CAAAuB,cAAA,YAEiH,CAAC;QAFpHvB,EAAE,CAAAwB,SAAA,aAMnF,CAAC;QANgFxB,EAAE,CAAAyB,YAAA,CAOnF,CAAC;MAAA;MAAA,IAAAL,EAAA;QAPgFpB,EAAE,CAAA0B,UAAA,CAAAL,GAAA,CAAAM,aAAA,EAEgH,CAAC;QAFnH3B,EAAE,CAAA4B,WAAA,eAAAP,GAAA,CAAAQ,SAAA,iBAAAR,GAAA,CAAAS,UAAA,UAAAT,GAAA,CAAAU,IAAA;MAAA;IAAA;IAAAC,aAAA;EAAA;AAShG;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAV8FjC,EAAE,CAAAkC,iBAAA,CAUJ/B,cAAc,EAAc,CAAC;IAC7GQ,IAAI,EAAEV,SAAS;IACfkC,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,gBAAgB;MAC1BC,UAAU,EAAE,IAAI;MAChBnB,QAAQ,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;IACgB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASf,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}