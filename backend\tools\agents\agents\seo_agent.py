"""
SEO Specialist Agent - Agente especializado em SEO completo e estratégico

Agente focado em análise completa de SEO:
- Análise técnica de SEO (meta tags, headings, estrutura)
- Core Web Vitals e performance para rankings
- Estratégia de keywords e análise de conteúdo
- Análise de backlinks e autoridade de domínio
- SEO local e mobile-first indexing
- Oportunidades de otimização e estratégias de longo prazo
"""

import logging
from typing import Dict, Any, Optional, List
from agno.agent import Agent
from ...shared.model_config import model_config
from agno.tools.reasoning import ReasoningTools

from ..tools import SEOAnalysisTools

logger = logging.getLogger(__name__)


class SEOSpecialistAgent:
    """Agente especializado em SEO completo usando Agno"""

    def __init__(self, model_name: Optional[str] = None):
        """
        Inicializa o SEO Specialist Agent usando modelos do settings.py

        Args:
            model_name: Modelo específico (opcional, usa automático se None)
        """
        self.seo_tools = SEOAnalysisTools()
        
        # Obter configuração de modelo baseada no settings.py
        agno_config = model_config.create_agno_config("analysis")
        
        if model_name:
            # Validar se modelo está disponível no settings
            available_models = [
                model_config.get_openai_model("analysis"),
                model_config.get_cohere_model("analysis"), 
                model_config.get_mistral_model("analysis")
            ]
            if model_name in available_models:
                self.model_name = model_name
            else:
                logger.warning(f"Modelo {model_name} não disponível no settings.py, usando automático")
                self.model_name = agno_config["model_id"]
        else:
            self.model_name = agno_config["model_id"]

        # Criar modelo baseado no provider
        if agno_config["provider"] == "openai":
            from agno.models.openai import OpenAI
            model_instance = OpenAI(id=self.model_name, api_key=agno_config["api_key"])
        elif agno_config["provider"] == "cohere":
            from agno.models.cohere import Cohere
            model_instance = Cohere(id=self.model_name, api_key=agno_config["api_key"])
        elif agno_config["provider"] == "mistral":
            from agno.models.mistral import Mistral
            model_instance = Mistral(id=self.model_name, api_key=agno_config["api_key"])
        else:
            from agno.models.openai import OpenAI
            model_instance = OpenAI(id="gpt-4o-mini", api_key=agno_config["api_key"])
            logger.warning(f"Provider {agno_config['provider']} não suportado, usando OpenAI fallback")

        self.agent = Agent(
            name="SEO Specialist",
            role="Expert in comprehensive SEO analysis, technical optimization, and search engine strategy",
            model=model_instance,
            tools=[ReasoningTools()],
            instructions=[
                "You are a comprehensive SEO expert specializing in all aspects of search engine optimization",
                "Analyze technical SEO, content strategy, keyword optimization, and backlink profiles",
                "Focus on Core Web Vitals impact on SEO rankings and user experience",
                "Provide specific, actionable recommendations for improving search visibility",
                "Consider E-A-T (Expertise, Authoritativeness, Trustworthiness) factors in your analysis",
                "Prioritize SEO improvements by potential ranking impact and implementation effort",
                "Balance technical optimization with content strategy and user experience",
                "Stay current with Google algorithm updates and SEO best practices"
            ],
            description="Specialist in comprehensive SEO strategy, technical optimization, content analysis, and search engine rankings",
            markdown=True,
            show_tool_calls=True
        )

        logger.info(f"SEO Specialist Agent inicializado com {agno_config['provider']}:{self.model_name}")

    def analyze_seo_strategy(self,
                             lighthouse_data: Dict[str, Any],
                             technical_data: Optional[Dict[str, Any]] = None,
                             keyword_data: Optional[Dict[str, Any]] = None,
                             backlink_data: Optional[Dict[str, Any]] = None,
                             local_data: Optional[Dict[str, Any]] = None,
                             company_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Analisa estratégia completa de SEO usando dados técnicos e estratégicos

        Args:
            lighthouse_data: Dados do Lighthouse (performance, SEO, accessibility)
            technical_data: Dados técnicos de SEO (meta tags, headings, etc.)
            keyword_data: Dados de keywords e análise de conteúdo
            backlink_data: Dados de backlinks e autoridade de domínio
            local_data: Dados de SEO local (opcional)
            company_context: Contexto da empresa para recomendações personalizadas

        Returns:
            Análise completa de SEO com estratégia e recomendações
        """
        try:
            logger.info(
                "Iniciando análise completa de SEO com SEO Specialist Agent")

            # Processar dados de SEO usando tools especializadas
            seo_insights = self.seo_tools.analyze_seo_data(
                lighthouse_data, technical_data, keyword_data, backlink_data, local_data)

            # Gerar estratégia de SEO baseada nos insights
            seo_strategy = self.seo_tools.create_seo_strategy(seo_insights)

            # Extrair informações da empresa
            company_info = company_context or {}
            company_name = company_info.get("name", "Site")
            sector = company_info.get("sector", "Geral")
            business_model = company_info.get(
                "business_model", "Não especificado")
            target_audience = company_info.get(
                "target_audience", "Público geral")

            # Extrair métricas principais
            overall_seo_score = seo_insights.overall_seo_score
            content_quality_score = seo_insights.content_quality_score
            user_experience_score = seo_insights.user_experience_score

            # Extrair análises específicas
            technical_analysis = seo_insights.technical_analysis
            keyword_analysis = seo_insights.keyword_analysis
            backlink_profile = seo_insights.backlink_profile
            core_web_vitals = seo_insights.core_web_vitals

            # Preparar contexto de dados disponíveis
            data_context = f"Fontes de dados: {', '.join(seo_insights.data_sources)}"

            # Contexto de SEO local se disponível
            local_context = ""
            if seo_insights.local_seo:
                local_seo = seo_insights.local_seo
                local_context = f"""
**ANÁLISE DE SEO LOCAL:**
- Google My Business Score: {local_seo.google_my_business_score}/100
- Citações Locais: {local_seo.local_citations_score}/100
- Consistência NAP: {local_seo.nap_consistency_score}/100
- Avaliações Locais: {local_seo.local_reviews_score}/100
- Keywords Locais: {local_seo.local_keywords_optimization}/100
"""

            # Construir prompt especializado para análise completa de SEO
            seo_analysis_prompt = f"""
Você é um SEO Specialist analisando o site da empresa {company_name} (setor: {sector}, modelo: {business_model}, público: {target_audience}).

**SCORES PRINCIPAIS DE SEO:**
- Score Geral de SEO: {overall_seo_score}/100
- Qualidade do Conteúdo: {content_quality_score}/100
- Experiência do Usuário: {user_experience_score}/100

**ANÁLISE TÉCNICA DE SEO:**
- Meta Tags: {technical_analysis.meta_tags_score}/100
- Estrutura de Headings: {technical_analysis.heading_structure_score}/100
- Linkagem Interna: {technical_analysis.internal_linking_score}/100
- Otimização de Imagens: {technical_analysis.image_optimization_score}/100
- Robots.txt: {technical_analysis.robots_txt_status}
- Sitemap XML: {technical_analysis.sitemap_status}
- Schema Markup: {technical_analysis.schema_markup_score}/100
- URLs Canônicas: {technical_analysis.canonical_urls_score}/100

**CORE WEB VITALS E PERFORMANCE:**
- LCP (Largest Contentful Paint): {core_web_vitals.lcp_score}/100
- FID (First Input Delay): {core_web_vitals.fid_score}/100
- CLS (Cumulative Layout Shift): {core_web_vitals.cls_score}/100
- Velocidade Mobile: {core_web_vitals.mobile_speed_score}/100
- Velocidade Desktop: {core_web_vitals.desktop_speed_score}/100
- Usabilidade Mobile: {core_web_vitals.mobile_usability_score}/100

**ESTRATÉGIA DE KEYWORDS:**
- Keywords Primárias: {', '.join(keyword_analysis.primary_keywords[:5]) if keyword_analysis.primary_keywords else 'Não identificadas'}
- Densidade de Keywords: {keyword_analysis.keyword_density_score}/100
- Alinhamento Intenção de Busca: {keyword_analysis.search_intent_alignment}/100
- Oportunidades Long Tail: {len(keyword_analysis.long_tail_opportunities)} identificadas
- Problemas Canibalização: {len(keyword_analysis.keyword_cannibalization_issues)} encontrados
- Gaps Competitivos: {len(keyword_analysis.competitive_keyword_gaps)} identificados

**PERFIL DE BACKLINKS:**
- Total de Backlinks: {backlink_profile.total_backlinks}
- Domínios Referenciadores: {backlink_profile.referring_domains}
- Autoridade Média dos Domínios: {backlink_profile.domain_authority_avg}/100
- Porcentagem de Links Tóxicos: {backlink_profile.toxic_links_percentage}%
- Diversidade de Anchor Text: {backlink_profile.anchor_text_diversity}/100
- Tendência de Crescimento: {backlink_profile.link_growth_trend}

{local_context}

**PROBLEMAS CRÍTICOS IDENTIFICADOS:**
{chr(10).join(f"- {issue}" for issue in seo_insights.critical_issues) if seo_insights.critical_issues else "- Nenhum problema crítico identificado"}

**OPORTUNIDADES DE VITÓRIAS RÁPIDAS:**
{chr(10).join(f"- {win}" for win in seo_insights.quick_wins) if seo_insights.quick_wins else "- Nenhuma vitória rápida identificada"}

**OPORTUNIDADES DE LONGO PRAZO:**
{chr(10).join(f"- {opp}" for opp in seo_insights.long_term_opportunities) if seo_insights.long_term_opportunities else "- Análise de oportunidades limitada"}

**VANTAGENS COMPETITIVAS:**
{chr(10).join(f"- {adv}" for adv in seo_insights.competitive_advantages) if seo_insights.competitive_advantages else "- Análise competitiva limitada"}

**GAPS COMPETITIVOS:**
{chr(10).join(f"- {gap}" for gap in seo_insights.competitive_gaps) if seo_insights.competitive_gaps else "- Nenhum gap crítico identificado"}

{data_context}

Forneça uma análise estratégica completa de SEO incluindo:

1. **DIAGNÓSTICO TÉCNICO DE SEO:**
   - Avaliação da saúde técnica geral do site
   - Principais problemas técnicos que impactam rankings
   - Status da indexação e crawlability
   - Análise de arquitetura de informação

2. **ANÁLISE DE CORE WEB VITALS E PERFORMANCE:**
   - Impacto das métricas de performance nos rankings
   - Problemas específicos de UX que afetam SEO
   - Recomendações para melhorar page experience
   - Estratégia mobile-first e usabilidade

3. **ESTRATÉGIA DE KEYWORDS E CONTEÚDO:**
   - Análise da estratégia atual de keywords
   - Oportunidades de otimização de conteúdo
   - Gaps de conteúdo vs. intenção de busca
   - Estratégia de long tail e semantic SEO
   - Implementação de E-A-T (Expertise, Authoritativeness, Trustworthiness)

4. **ANÁLISE DE BACKLINKS E AUTORIDADE:**
   - Avaliação da qualidade do perfil de backlinks
   - Oportunidades de link building estratégico
   - Riscos de penalizações por links tóxicos
   - Estratégias para aumentar autoridade de domínio

5. **OPORTUNIDADES DE OTIMIZAÇÃO:**
   - Vitórias rápidas (implementação 1-4 semanas)
   - Otimizações de médio prazo (1-6 meses)
   - Estratégias de longo prazo (6+ meses)
   - ROI esperado para cada categoria de otimização

6. **ESTRATÉGIA DE IMPLEMENTAÇÃO:**
   - Priorização de ações por impacto vs. esforço
   - Timeline detalhado de implementação
   - Recursos necessários para execução
   - Métricas de acompanhamento e KPIs
   - Pontos de revisão e otimização contínua

Foque em recomendações específicas, acionáveis e baseadas em dados que gerem resultados mensuráveis nos rankings e tráfego orgânico.
"""

            # Executar análise com o agente
            response = self.agent.run(seo_analysis_prompt)

            # Preparar resultado estruturado
            result = {
                "agent_type": "SEOSpecialistAgent",
                "seo_insights": seo_insights.model_dump() if hasattr(seo_insights, 'model_dump') else seo_insights,
                "seo_strategy": seo_strategy.model_dump() if hasattr(seo_strategy, 'model_dump') else seo_strategy,
                "strategic_analysis": response.content,
                "overall_seo_score": overall_seo_score,
                "content_quality_score": content_quality_score,
                "user_experience_score": user_experience_score,
                "seo_category": self._categorize_seo_health(overall_seo_score),
                "analysis_summary": {
                    "critical_issues": len(seo_insights.critical_issues),
                    "quick_wins": len(seo_insights.quick_wins),
                    "long_term_opportunities": len(seo_insights.long_term_opportunities),
                    "competitive_advantages": len(seo_insights.competitive_advantages),
                    "competitive_gaps": len(seo_insights.competitive_gaps),
                    "data_sources": seo_insights.data_sources,
                    "backlinks_total": backlink_profile.total_backlinks,
                    "referring_domains": backlink_profile.referring_domains,
                    "primary_keywords_count": len(keyword_analysis.primary_keywords)
                }
            }

            logger.info(
                f"Análise de SEO concluída - Score Geral: {overall_seo_score}/100")
            return result

        except Exception as e:
            logger.error(f"Erro na análise de SEO: {str(e)}")
            raise

    def _categorize_seo_health(self, seo_score: float) -> str:
        """Categoriza a saúde do SEO baseada no score geral"""
        if seo_score >= 90:
            return "Excelente - SEO otimizado e competitivo"
        elif seo_score >= 75:
            return "Bom - SEO sólido com oportunidades de melhoria"
        elif seo_score >= 60:
            return "Regular - Necessita otimizações importantes"
        elif seo_score >= 40:
            return "Problemático - Requer atenção urgente"
        else:
            return "Crítico - Necessita reconstrução completa da estratégia SEO"

    def analyze_keyword_opportunities(self,
                                      keyword_data: Dict[str, Any],
                                      content_data: Optional[Dict[str,
                                                                  Any]] = None,
                                      competitor_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Analisa oportunidades específicas de keywords e conteúdo

        Args:
            keyword_data: Dados de keywords atuais e potenciais
            content_data: Dados de análise de conteúdo
            competitor_data: Dados de keywords dos concorrentes

        Returns:
            Análise detalhada de oportunidades de keywords
        """
        try:
            logger.info("Analisando oportunidades de keywords")

            # Analisar keywords usando tools
            keyword_analysis = self.seo_tools._analyze_keywords(keyword_data)

            # Extrair dados principais
            primary_keywords = keyword_analysis.primary_keywords
            long_tail_opportunities = keyword_analysis.long_tail_opportunities
            competitive_gaps = keyword_analysis.competitive_keyword_gaps

            keyword_prompt = f"""
Analise as seguintes oportunidades de keywords para otimização de conteúdo:

**KEYWORDS ATUAIS:**
- Keywords Primárias: {', '.join(primary_keywords[:10]) if primary_keywords else 'Não identificadas'}
- Densidade Score: {keyword_analysis.keyword_density_score}/100
- Alinhamento Intenção: {keyword_analysis.search_intent_alignment}/100

**OPORTUNIDADES IDENTIFICADAS:**
Long Tail Keywords:
{chr(10).join(f"- {kw}" for kw in long_tail_opportunities[:8]) if long_tail_opportunities else "- Nenhuma oportunidade identificada"}

Gaps Competitivos:
{chr(10).join(f"- {gap}" for gap in competitive_gaps[:8]) if competitive_gaps else "- Análise competitiva limitada"}

Problemas de Canibalização:
{chr(10).join(f"- {issue}" for issue in keyword_analysis.keyword_cannibalization_issues[:5]) if keyword_analysis.keyword_cannibalization_issues else "- Nenhum problema identificado"}

Forneça análise incluindo:

1. **ESTRATÉGIA DE KEYWORDS PRIMÁRIAS:**
   - Otimização das keywords atuais
   - Novas keywords de alto valor a focar
   - Estratégia de distribuição por páginas

2. **OPORTUNIDADES LONG TAIL:**
   - Keywords long tail prioritárias
   - Conteúdo a criar para cada oportunidade
   - Estratégia de cluster de conteúdo

3. **ANÁLISE COMPETITIVA:**
   - Keywords onde concorrentes têm vantagem
   - Oportunidades de superação competitiva
   - Nichos não explorados

4. **OTIMIZAÇÃO DE CONTEÚDO:**
   - Melhorias para alinhamento com intenção
   - Estrutura de conteúdo recomendada
   - Semantic SEO e tópicos relacionados

5. **IMPLEMENTAÇÃO:**
   - Priorização por potencial de tráfego
   - Timeline de otimização
   - Métricas de acompanhamento
"""

            response = self.agent.run(keyword_prompt)

            return {
                "keyword_analysis": response.content,
                "keyword_insights": keyword_analysis.model_dump() if hasattr(keyword_analysis, 'model_dump') else keyword_analysis,
                "opportunity_score": self._calculate_keyword_opportunity_score(keyword_analysis),
                "priority_keywords": primary_keywords[:5],
                "long_tail_count": len(long_tail_opportunities),
                "competitive_gaps_count": len(competitive_gaps)
            }

        except Exception as e:
            logger.error(f"Erro ao analisar keywords: {str(e)}")
            return {"error": str(e)}

    def assess_technical_seo_health(self,
                                    lighthouse_data: Dict[str, Any],
                                    technical_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Avalia especificamente a saúde técnica do SEO

        Args:
            lighthouse_data: Dados do Lighthouse
            technical_data: Dados técnicos adicionais

        Returns:
            Análise detalhada de SEO técnico
        """
        try:
            logger.info("Avaliando saúde técnica do SEO")

            # Analisar aspectos técnicos
            technical_analysis = self.seo_tools._analyze_technical_seo(
                lighthouse_data, technical_data)
            core_web_vitals = self.seo_tools._analyze_core_web_vitals(
                lighthouse_data)

            technical_prompt = f"""
Analise a saúde técnica de SEO baseada nos seguintes dados:

**ANÁLISE TÉCNICA:**
- Meta Tags: {technical_analysis.meta_tags_score}/100
- Estrutura Headings: {technical_analysis.heading_structure_score}/100
- Linkagem Interna: {technical_analysis.internal_linking_score}/100
- Otimização Imagens: {technical_analysis.image_optimization_score}/100
- Robots.txt: {technical_analysis.robots_txt_status}
- Sitemap XML: {technical_analysis.sitemap_status}
- Schema Markup: {technical_analysis.schema_markup_score}/100
- URLs Canônicas: {technical_analysis.canonical_urls_score}/100

**CORE WEB VITALS:**
- LCP: {core_web_vitals.lcp_score}/100
- FID: {core_web_vitals.fid_score}/100
- CLS: {core_web_vitals.cls_score}/100
- Mobile Speed: {core_web_vitals.mobile_speed_score}/100
- Mobile Usability: {core_web_vitals.mobile_usability_score}/100

Forneça análise técnica detalhada incluindo:

1. **PROBLEMAS TÉCNICOS CRÍTICOS:**
   - Issues que impedem indexação adequada
   - Problemas de crawlability
   - Erros de estrutura que impactam rankings

2. **OTIMIZAÇÕES DE PERFORMANCE:**
   - Melhorias críticas em Core Web Vitals
   - Impacto da performance nos rankings
   - Estratégias de otimização técnica

3. **ESTRUTURA E ORGANIZAÇÃO:**
   - Arquitetura de informação
   - Estrutura de URLs e navegação
   - Internal linking strategy

4. **DADOS ESTRUTURADOS E MARKUP:**
   - Implementação de schema markup
   - Oportunidades de rich snippets
   - Structured data para melhor entendimento

5. **MOBILE-FIRST E USABILIDADE:**
   - Otimização para mobile-first indexing
   - Problemas de usabilidade mobile
   - Responsive design e performance

6. **PLANO DE CORREÇÃO:**
   - Priorização por impacto SEO
   - Timeline de implementação
   - Recursos técnicos necessários
"""

            response = self.agent.run(technical_prompt)

            return {
                "technical_analysis": response.content,
                "technical_insights": technical_analysis.model_dump() if hasattr(technical_analysis, 'model_dump') else technical_analysis,
                "core_web_vitals": core_web_vitals.model_dump() if hasattr(core_web_vitals, 'model_dump') else core_web_vitals,
                "technical_health_score": self._calculate_technical_health_score(technical_analysis, core_web_vitals),
                "critical_technical_issues": self._identify_critical_technical_issues(technical_analysis, core_web_vitals)
            }

        except Exception as e:
            logger.error(f"Erro ao avaliar SEO técnico: {str(e)}")
            return {"error": str(e)}

    def _calculate_keyword_opportunity_score(self, keyword_analysis: Any) -> float:
        """Calcula score de oportunidade de keywords"""
        try:
            score = 50.0

            # Score baseado em densidade
            if keyword_analysis.keyword_density_score > 80:
                score += 20
            elif keyword_analysis.keyword_density_score > 60:
                score += 10

            # Score baseado em alinhamento
            if keyword_analysis.search_intent_alignment > 80:
                score += 20
            elif keyword_analysis.search_intent_alignment > 60:
                score += 10

            # Score baseado em oportunidades
            long_tail_count = len(keyword_analysis.long_tail_opportunities)
            if long_tail_count > 10:
                score += 15
            elif long_tail_count > 5:
                score += 10

            # Penalização por problemas
            cannibalization_count = len(
                keyword_analysis.keyword_cannibalization_issues)
            if cannibalization_count > 3:
                score -= 15
            elif cannibalization_count > 0:
                score -= 5

            return min(max(score, 0), 100)

        except Exception:
            return 50.0

    def _calculate_technical_health_score(self, technical: Any, vitals: Any) -> float:
        """Calcula score de saúde técnica"""
        try:
            technical_score = (
                technical.meta_tags_score +
                technical.heading_structure_score +
                technical.internal_linking_score +
                technical.image_optimization_score +
                technical.schema_markup_score +
                technical.canonical_urls_score
            ) / 6

            vitals_score = (
                vitals.lcp_score +
                vitals.fid_score +
                vitals.cls_score +
                vitals.mobile_speed_score +
                vitals.mobile_usability_score
            ) / 5

            # Peso: 60% técnico, 40% vitals
            return (technical_score * 0.6) + (vitals_score * 0.4)

        except Exception:
            return 50.0

    def _identify_critical_technical_issues(self, technical: Any, vitals: Any) -> List[str]:
        """Identifica problemas técnicos críticos"""
        issues = []

        try:
            if technical.meta_tags_score < 50:
                issues.append("Meta tags críticas ausentes ou inadequadas")

            if technical.heading_structure_score < 40:
                issues.append("Estrutura de headings problemática")

            if technical.robots_txt_status == "Não encontrado":
                issues.append("Arquivo robots.txt não encontrado")

            if technical.sitemap_status == "Não encontrado":
                issues.append("Sitemap XML não encontrado")

            if vitals.lcp_score < 50:
                issues.append("LCP muito lento impactando rankings")

            if vitals.cls_score < 50:
                issues.append("CLS alto prejudicando experiência")

            if vitals.mobile_speed_score < 50:
                issues.append("Velocidade mobile crítica")

        except Exception:
            pass

        return issues

    def get_seo_recommendations_summary(self, analysis_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Extrai um resumo estruturado das recomendações de SEO

        Args:
            analysis_result: Resultado da análise de SEO

        Returns:
            Lista de recomendações estruturadas
        """
        try:
            seo_insights = analysis_result.get("seo_insights", {})
            analysis_summary = analysis_result.get("analysis_summary", {})

            recommendations = []

            # Recomendações baseadas em problemas críticos
            critical_issues = seo_insights.get("critical_issues", [])
            for issue in critical_issues[:3]:  # Top 3 críticos
                recommendations.append({
                    "category": "Critical Issue",
                    "recommendation": f"Corrigir urgentemente: {issue}",
                    "priority": "Critical",
                    "type": "Technical"
                })

            # Recomendações de vitórias rápidas
            quick_wins = seo_insights.get("quick_wins", [])
            for win in quick_wins[:3]:  # Top 3 quick wins
                recommendations.append({
                    "category": "Quick Win",
                    "recommendation": win,
                    "priority": "High",
                    "type": "Optimization"
                })

            # Recomendações baseadas em score geral
            overall_score = analysis_result.get("overall_seo_score", 0)
            if overall_score < 60:
                recommendations.append({
                    "category": "SEO Strategy",
                    "recommendation": f"Implementar estratégia abrangente de SEO (score atual: {overall_score}/100)",
                    "priority": "High",
                    "type": "Strategic"
                })

            # Recomendações de keywords
            keyword_analysis = seo_insights.get("keyword_analysis", {})
            if keyword_analysis.get("search_intent_alignment", 100) < 60:
                recommendations.append({
                    "category": "Content Strategy",
                    "recommendation": "Melhorar alinhamento do conteúdo com intenção de busca",
                    "priority": "Medium",
                    "type": "Content"
                })

            # Recomendações de backlinks
            backlink_profile = seo_insights.get("backlink_profile", {})
            if backlink_profile.get("total_backlinks", 0) < 50:
                recommendations.append({
                    "category": "Link Building",
                    "recommendation": "Implementar estratégia ativa de link building",
                    "priority": "Medium",
                    "type": "Strategic"
                })

            return recommendations

        except Exception as e:
            logger.error(f"Erro ao extrair recomendações de SEO: {str(e)}")
            return []
