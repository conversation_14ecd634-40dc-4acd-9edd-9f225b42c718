"""
Módulo para análise de performance usando Google Lighthouse

Este módulo executa análises automáticas de:
- Performance (Core Web Vitals)
- Acessibilidade
- SEO técnico
- Best practices

REQUISITO: Lighthouse deve estar instalado e funcional no sistema
"""

import json
import subprocess
from typing import Dict, Any, Optional
import re
import os


class LighthouseAnalyzer:
    """
    Analisador de performance usando Google Lighthouse

    Executa auditorias automatizadas de websites e retorna métricas estruturadas
    para performance, acessibilidade, SEO e best practices.

    IMPORTANTE: Este analisador requer Lighthouse instalado e funcional.
    """

    def __init__(self, chrome_flags: Optional[str] = None):
        """
        Inicializa o analisador Lighthouse

        Args:
            chrome_flags: Flags adicionais para o Chrome (opcional)

        Raises:
            Exception: Se Lighthouse não estiver disponível
        """
        self.chrome_flags = chrome_flags or "--headless --no-sandbox --disable-gpu --disable-dev-shm-usage --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-renderer-backgrounding"

        # Detectar Chrome automaticamente
        self.chrome_path = self._detect_chrome_path()

        # VERIFICAÇÃO OBRIGATÓRIA - Sistema não funciona sem Lighthouse
        if not self._check_lighthouse_availability():
            raise Exception(
                "❌ ERRO CRÍTICO: Lighthouse não está disponível! "
                "Instale com: npm install -g lighthouse"
            )

        print("✅ Lighthouse disponível e pronto para análises reais")
        if self.chrome_path:
            print(f"🔍 Chrome encontrado em: {self.chrome_path}")

    def _detect_chrome_path(self) -> Optional[str]:
        """
        Detecta automaticamente o caminho do Chrome/Chromium

        Returns:
            Caminho para o executável do Chrome ou None se não encontrado
        """
        # Verificar variáveis de ambiente primeiro
        chrome_env_vars = ['CHROME_PATH', 'CHROME_BIN',
                           'LIGHTHOUSE_CHROMIUM_PATH', 'PUPPETEER_EXECUTABLE_PATH']

        for env_var in chrome_env_vars:
            chrome_path = os.environ.get(env_var)
            if chrome_path and os.path.isfile(chrome_path):
                return chrome_path

        # Procurar em locais comuns
        common_paths = [
            '/usr/bin/google-chrome-stable',
            '/usr/bin/google-chrome',
            '/usr/bin/chromium-browser',
            '/usr/bin/chromium',
            '/opt/google/chrome/chrome',
            '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome'
        ]

        for path in common_paths:
            if os.path.isfile(path):
                return path

        return None

    def _check_lighthouse_availability(self) -> bool:
        """
        Verifica se Lighthouse está disponível no sistema

        Returns:
            True se Lighthouse está disponível, False caso contrário
        """
        try:
            result = subprocess.run(
                ["lighthouse", "--version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            if result.returncode == 0:
                version = result.stdout.strip()
                print(f"🔍 Lighthouse encontrado - Versão: {version}")
                return True
            return False
        except (subprocess.TimeoutExpired, FileNotFoundError, Exception) as e:
            print(f"❌ Lighthouse não encontrado: {str(e)}")
            return False

    def run_analysis(self, url: str) -> Dict[str, Any]:
        """
        Executa análise REAL do Lighthouse para uma URL

        Args:
            url: URL do website para análise

        Returns:
            Dict contendo dados REAIS de performance, acessibilidade, SEO e best practices

        Raises:
            Exception: Se a URL for inválida ou houver erro na execução do Lighthouse
        """
        if not self._is_valid_url(url):
            raise Exception(f"URL inválida: {url}")

        print(f"🚀 Iniciando análise REAL do Lighthouse para: {url}")

        try:
            # Comando Lighthouse com flags otimizadas para container
            cmd = [
                "lighthouse",
                url,
                "--output=json",
                "--only-categories=performance,accessibility,seo,best-practices",
                f"--chrome-flags={self.chrome_flags}",
                "--quiet",
                "--no-enable-error-reporting"
            ]

            # Adicionar caminho do Chrome se detectado
            if self.chrome_path:
                cmd.extend(["--chrome-path", self.chrome_path])

            print(f"📊 Executando: {' '.join(cmd[:3])}...")

            # Configurar variáveis de ambiente para o processo
            env = os.environ.copy()
            if self.chrome_path:
                env['CHROME_PATH'] = self.chrome_path
                env['PUPPETEER_EXECUTABLE_PATH'] = self.chrome_path

            # Executar Lighthouse via subprocess
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=180,  # Timeout de 3 minutos para análise completa
                env=env
            )

            if result.returncode != 0:
                error_msg = result.stderr or "Erro desconhecido"
                raise Exception(
                    f"Lighthouse falhou com código {result.returncode}: {error_msg}")

            print("✅ Análise Lighthouse concluída com sucesso!")

            # Parse do JSON retornado pelo Lighthouse
            try:
                lighthouse_data = json.loads(result.stdout)
            except json.JSONDecodeError as e:
                raise Exception(
                    f"JSON inválido retornado pelo Lighthouse: {str(e)}")

            # Extrair dados estruturados
            structured_data = self._extract_structured_data(lighthouse_data)
            structured_data["_real_data"] = True
            structured_data["_lighthouse_version"] = self._get_lighthouse_version()
            structured_data["_chrome_path"] = self.chrome_path

            print(
                f"📈 Análise processada - Score Performance: {structured_data['performance']['score']}%")
            return structured_data

        except subprocess.TimeoutExpired:
            raise Exception(
                f"Timeout na análise do Lighthouse para {url}. Tente novamente.")
        except Exception as e:
            raise Exception(f"Erro na análise Lighthouse: {str(e)}")

    def _get_lighthouse_version(self) -> str:
        """Obtém versão do Lighthouse instalado"""
        try:
            result = subprocess.run(
                ["lighthouse", "--version"],
                capture_output=True,
                text=True,
                timeout=5
            )
            return result.stdout.strip() if result.returncode == 0 else "unknown"
        except Exception:
            return "unknown"

    def _is_valid_url(self, url: str) -> bool:
        """
        Valida se a URL está em formato correto

        Args:
            url: URL para validação

        Returns:
            True se válida, False caso contrário
        """
        if not url or not isinstance(url, str):
            return False

        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            # domain...
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)

        return bool(url_pattern.match(url))

    def _extract_structured_data(self, lighthouse_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extrai dados estruturados do resultado Lighthouse

        Args:
            lighthouse_data: Dados brutos do Lighthouse

        Returns:
            Dados estruturados no formato esperado pelo sistema
        """
        try:
            # O Lighthouse pode retornar dados diretamente ou dentro de 'lhr'
            if "lhr" in lighthouse_data:
                data = lighthouse_data["lhr"]
            else:
                data = lighthouse_data

            categories = data.get("categories", {})
            audits = data.get("audits", {})

            # Extrair scores das categorias (0-1 -> 0-100)
            performance_score = int(categories.get(
                "performance", {}).get("score", 0) * 100)
            accessibility_score = int(categories.get(
                "accessibility", {}).get("score", 0) * 100)
            seo_score = int(categories.get("seo", {}).get("score", 0) * 100)
            best_practices_score = int(categories.get(
                "best-practices", {}).get("score", 0) * 100)

            # Debug dos scores extraídos
            print(
                f"📊 Scores extraídos: Performance={performance_score}%, Acessibilidade={accessibility_score}%, SEO={seo_score}%, Best Practices={best_practices_score}%")

            # Extrair Core Web Vitals
            core_metrics = self._extract_core_web_vitals(audits)

            # Extrair oportunidades e diagnósticos
            opportunities = self._extract_opportunities(audits)
            diagnostics = self._extract_diagnostics(audits)

            # Extrair issues de acessibilidade
            accessibility_issues = self._extract_accessibility_issues(audits)
            accessibility_recommendations = self._extract_accessibility_recommendations(
                audits)

            # Extrair dados de SEO
            seo_data = self._extract_seo_data(audits)

            # Extrair best practices
            best_practices_data = self._extract_best_practices_data(audits)

            return {
                "performance": {
                    "score": performance_score,
                    "metricas_core": core_metrics,
                    "oportunidades": opportunities,
                    "diagnosticos": diagnostics
                },
                "acessibilidade": {
                    "score": accessibility_score,
                    "issues": accessibility_issues,
                    "recomendacoes": accessibility_recommendations
                },
                "seo_tecnico": {
                    "score": seo_score,
                    "meta_tags": seo_data.get("meta_tags", "não analisado"),
                    "estrutura_html": seo_data.get("estrutura_html", "não analisado"),
                    "mobile_friendly": seo_data.get("mobile_friendly", "não analisado")
                },
                "best_practices": {
                    "score": best_practices_score,
                    "https": best_practices_data.get("https", False),
                    "vulnerabilidades": best_practices_data.get("vulnerabilidades", [])
                }
            }

        except Exception as e:
            print(f"❌ Erro ao extrair dados estruturados: {str(e)}")
            raise Exception(f"Erro ao extrair dados estruturados: {str(e)}")

    def _extract_core_web_vitals(self, audits: Dict[str, Any]) -> Dict[str, Any]:
        """Extrai métricas Core Web Vitals"""
        try:
            return {
                "fcp": int(audits.get("first-contentful-paint", {}).get("numericValue", 0)),
                "lcp": int(audits.get("largest-contentful-paint", {}).get("numericValue", 0)),
                "cls": round(audits.get("cumulative-layout-shift", {}).get("numericValue", 0), 3),
                "fid": int(audits.get("first-input-delay", {}).get("numericValue", 0)),
                "ttfb": int(audits.get("server-response-time", {}).get("numericValue", 0))
            }
        except Exception:
            return {"fcp": 0, "lcp": 0, "cls": 0, "fid": 0, "ttfb": 0}

    def _extract_opportunities(self, audits: Dict[str, Any]) -> list:
        """Extrai oportunidades de melhoria"""
        opportunities = []
        try:
            opportunity_audits = [
                "render-blocking-resources", "unused-css-rules", "unused-javascript",
                "modern-image-formats", "offscreen-images", "unminified-css"
            ]

            for audit_id in opportunity_audits:
                audit = audits.get(audit_id, {})
                if audit.get("score", 1) < 1:  # Score < 1 indica oportunidade
                    title = audit.get("title", audit_id)
                    opportunities.append(title)

            return opportunities
        except Exception:
            return []

    def _extract_diagnostics(self, audits: Dict[str, Any]) -> list:
        """Extrai diagnósticos de problemas"""
        diagnostics = []
        try:
            diagnostic_audits = [
                "mainthread-work-breakdown", "bootup-time", "uses-long-cache-ttl",
                "total-byte-weight", "dom-size"
            ]

            for audit_id in diagnostic_audits:
                audit = audits.get(audit_id, {})
                if audit.get("score", 1) < 0.9:  # Score baixo indica problema
                    title = audit.get("title", audit_id)
                    diagnostics.append(title)

            return diagnostics
        except Exception:
            return []

    def _extract_accessibility_issues(self, audits: Dict[str, Any]) -> list:
        """Extrai issues de acessibilidade"""
        issues = []
        try:
            accessibility_audits = [
                "color-contrast", "image-alt", "form-field-multiple-labels",
                "heading-order", "link-name", "button-name"
            ]

            for audit_id in accessibility_audits:
                audit = audits.get(audit_id, {})
                if audit.get("score", 1) < 1:
                    title = audit.get("title", audit_id)
                    issues.append(title)

            return issues
        except Exception:
            return []

    def _extract_accessibility_recommendations(self, audits: Dict[str, Any]) -> list:
        """Extrai recomendações de acessibilidade"""
        recommendations = []
        try:
            # Base nas issues encontradas, gerar recomendações genéricas
            if audits.get("color-contrast", {}).get("score", 1) < 1:
                recommendations.append("Melhorar contraste de cores")
            if audits.get("image-alt", {}).get("score", 1) < 1:
                recommendations.append(
                    "Adicionar textos alternativos em imagens")
            if audits.get("heading-order", {}).get("score", 1) < 1:
                recommendations.append("Corrigir hierarquia de cabeçalhos")

            return recommendations
        except Exception:
            return []

    def _extract_seo_data(self, audits: Dict[str, Any]) -> Dict[str, Any]:
        """Extrai dados de SEO"""
        try:
            meta_description = audits.get("meta-description", {})
            viewport = audits.get("viewport", {})

            return {
                "meta_tags": "adequado" if meta_description.get("score", 0) >= 1 else "necessita melhoria",
                "estrutura_html": "adequada" if audits.get("document-title", {}).get("score", 0) >= 1 else "necessita melhoria",
                "mobile_friendly": "sim" if viewport.get("score", 0) >= 1 else "necessita ajustes"
            }
        except Exception:
            return {
                "meta_tags": "não analisado",
                "estrutura_html": "não analisado",
                "mobile_friendly": "não analisado"
            }

    def _extract_best_practices_data(self, audits: Dict[str, Any]) -> Dict[str, Any]:
        """Extrai dados de best practices"""
        try:
            https_audit = audits.get("is-on-https", {})
            vulnerabilities = []

            # Verificar vulnerabilidades conhecidas
            if audits.get("no-vulnerable-libraries", {}).get("score", 1) < 1:
                vulnerabilities.append(
                    "Bibliotecas com vulnerabilidades detectadas")
            if audits.get("csp-xss", {}).get("score", 1) < 1:
                vulnerabilities.append("CSP inadequado para prevenção XSS")

            return {
                "https": https_audit.get("score", 0) >= 1,
                "vulnerabilidades": vulnerabilities
            }
        except Exception:
            return {
                "https": False,
                "vulnerabilidades": []
            }
