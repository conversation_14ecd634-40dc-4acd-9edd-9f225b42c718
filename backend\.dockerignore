# =========================================================
# .dockerignore - Otimização de Build Context
# =========================================================

# Arquivos Git
.git/
.gitignore
.gitattributes

# Cache Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Ambientes virtuais
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
.venv/

# Poetry
# poetry.lock deve ser incluído para garantir versões exatas
# Mantém pyproject.toml e poetry.lock para build

# Jupyter
.ipynb_checkpoints

# IDEs
.vscode/
.cursor/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# Logs
*.log
logs/
memory-bank/logs/

# Testes
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/

# Documentação
*.md
docs/
memory-bank/

# Dados de desenvolvimento
data/cache/
data/temp/
data/test/

# Docker
Dockerfile*
docker-compose*
.dockerignore

# CI/CD
.github/
.gitlab-ci.yml

# Backup files
*.bak
*.backup
*.old 