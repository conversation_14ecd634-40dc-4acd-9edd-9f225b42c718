"""
ReportDataCollector - Responsável pela Coleta de Dados de Relatórios

Módulo especializado em coletar e organizar dados necessários 
para diferentes tipos de relatórios, seguindo o Single Responsibility Principle.
"""

import logging
from typing import Dict, Any, Optional
from bson import ObjectId

from clients.db import clients_collection
from clients.async_db import motor_clients_collection
from tools.diagnostics import LighthouseAnalyzer, PlaywrightScreenshots, VisualAnalyzer, DiagnosticConsolidator

logger = logging.getLogger(__name__)


class ReportDataCollector:
    """
    Responsável por coletar dados de diferentes fontes para geração de relatórios.

    Única responsabilidade: Coleta e organização de dados raw para relatórios.
    """

    def __init__(self):
        """Inicializa o coletor de dados."""
        self.diagnostic_consolidator = DiagnosticConsolidator()
        # Note: Inicialização dos analisadores será feita quando necessário
        self.lighthouse_analyzer = None
        self.visual_analyzer = None
        self.screenshot_service = None

    async def get_client_data(self, client_id: str) -> Optional[Dict[str, Any]]:
        """
        Busca dados completos do cliente no MongoDB.

        Args:
            client_id: ID do cliente

        Returns:
            Dados do cliente ou None se não encontrado
        """
        try:
            client = await motor_clients_collection.find_one({"_id": ObjectId(client_id)})
            return client
        except Exception as e:
            logger.error(f"Erro ao buscar cliente {client_id}: {str(e)}")
            return None

    async def collect_report_data(
        self,
        client_data: Dict[str, Any],
        report_type: str
    ) -> Dict[str, Any]:
        """
        Coleta dados específicos necessários para o tipo de report.

        Args:
            client_data: Dados do cliente
            report_type: Tipo de report a ser gerado

        Returns:
            Dados coletados e organizados
        """
        try:
            # Extrair reports existentes do cliente
            existing_reports = client_data.get("reports", {})

            # Estrutura base de dados
            raw_data = {
                "client_basic_info": self._extract_basic_info(client_data),
                "existing_reports": existing_reports,
                "diagnostic_data": None,
                "competitive_data": None,
                "market_data": None,
                "technical_data": None,
                "financial_data": None,
                "collection_timestamp": "2025-06-18T16:45:36Z"
            }

            # Coletar dados específicos baseado no tipo de report
            if report_type in ["dossie_executivo", "analise_tecnica"]:
                raw_data["diagnostic_data"] = await self._collect_diagnostic_data(client_data)
                raw_data["technical_data"] = await self._collect_technical_data(client_data)

            if report_type in ["competitividade", "mercado_oportunidades"]:
                raw_data["competitive_data"] = await self._collect_competitive_data(client_data)
                raw_data["market_data"] = await self._collect_market_data(client_data)

            if report_type in ["funding_investimentos", "modelo_negocio"]:
                raw_data["financial_data"] = await self._collect_financial_data(client_data)

            if report_type == "crescimento_digital":
                raw_data["digital_presence"] = await self._collect_digital_presence_data(client_data)

            return raw_data

        except Exception as e:
            logger.error(
                f"Erro na coleta de dados para {report_type}: {str(e)}")
            raise Exception(f"Falha na coleta de dados: {str(e)}")

    def _extract_basic_info(self, client_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extrai informações básicas do cliente."""
        return {
            "name": client_data.get("name", ""),
            "company": client_data.get("company", ""),
            "sector": client_data.get("sector") or client_data.get("setor", "default"),
            "website": client_data.get("website", ""),
            "description": client_data.get("description", ""),
            "contact_info": client_data.get("contact", {}),
            "created_at": client_data.get("created_at"),
            "last_updated": client_data.get("updated_at")
        }

    async def _collect_diagnostic_data(self, client_data: Dict[str, Any]) -> Dict[str, Any]:
        """Coleta dados de diagnóstico técnico."""
        try:
            website = client_data.get("website")
            if not website:
                return {"error": "Website não informado"}

            # Consolidar diagnósticos existentes
            reports = client_data.get("reports", {})
            lighthouse_data = reports.get("lighthouse", {})
            visual_data = reports.get("visual_analysis", {})

            return {
                "lighthouse_analysis": lighthouse_data,
                "visual_analysis": visual_data,
                "performance_metrics": lighthouse_data.get("performance", {}),
                "accessibility_score": lighthouse_data.get("accessibility", {}),
                "seo_analysis": lighthouse_data.get("seo", {}),
                "best_practices": lighthouse_data.get("best_practices", {})
            }

        except Exception as e:
            logger.error(f"Erro ao coletar dados de diagnóstico: {str(e)}")
            return {"error": str(e)}

    async def _collect_technical_data(self, client_data: Dict[str, Any]) -> Dict[str, Any]:
        """Coleta dados técnicos da infraestrutura."""
        try:
            reports = client_data.get("reports", {})

            return {
                "infrastructure": self._extract_infrastructure_data(reports),
                "security": self._extract_security_data(reports),
                "scalability": reports.get("scalability_analysis", {}),
                "technical_debt": reports.get("technical_debt", {}),
                "technology_stack": reports.get("tech_stack", [])
            }

        except Exception as e:
            logger.error(f"Erro ao coletar dados técnicos: {str(e)}")
            return {"error": str(e)}

    async def _collect_competitive_data(self, client_data: Dict[str, Any]) -> Dict[str, Any]:
        """Coleta dados de análise competitiva."""
        try:
            reports = client_data.get("reports", {})

            return {
                "competitors": reports.get("competitors", []),
                "market_position": reports.get("market_position", {}),
                "competitive_advantages": reports.get("competitive_advantages", []),
                "market_share": reports.get("market_share", {}),
                "pricing_analysis": reports.get("pricing", {})
            }

        except Exception as e:
            logger.error(f"Erro ao coletar dados competitivos: {str(e)}")
            return {"error": str(e)}

    async def _collect_market_data(self, client_data: Dict[str, Any]) -> Dict[str, Any]:
        """Coleta dados de mercado e oportunidades."""
        try:
            reports = client_data.get("reports", {})

            return {
                "market_size": reports.get("market_size", {}),
                "growth_trends": reports.get("growth_trends", []),
                "opportunities": reports.get("opportunities", []),
                "market_segments": reports.get("market_segments", []),
                "target_audience": reports.get("target_audience", {})
            }

        except Exception as e:
            logger.error(f"Erro ao coletar dados de mercado: {str(e)}")
            return {"error": str(e)}

    async def _collect_financial_data(self, client_data: Dict[str, Any]) -> Dict[str, Any]:
        """Coleta dados financeiros e de investimento."""
        try:
            reports = client_data.get("reports", {})

            return {
                "funding_history": reports.get("funding", []),
                "revenue_model": reports.get("revenue_model", {}),
                "financial_metrics": reports.get("financial_metrics", {}),
                "investment_opportunities": reports.get("investment_opportunities", []),
                "valuation": reports.get("valuation", {})
            }

        except Exception as e:
            logger.error(f"Erro ao coletar dados financeiros: {str(e)}")
            return {"error": str(e)}

    async def _collect_digital_presence_data(self, client_data: Dict[str, Any]) -> Dict[str, Any]:
        """Coleta dados de presença digital."""
        try:
            reports = client_data.get("reports", {})

            return {
                "social_media": reports.get("social_media", {}),
                "digital_channels": reports.get("digital_channels", []),
                "online_reputation": reports.get("online_reputation", {}),
                "digital_marketing": reports.get("digital_marketing", {}),
                "conversion_metrics": reports.get("conversion_metrics", {})
            }

        except Exception as e:
            logger.error(
                f"Erro ao coletar dados de presença digital: {str(e)}")
            return {"error": str(e)}

    def _extract_security_data(self, reports: Dict[str, Any]) -> Dict[str, Any]:
        """Extrai dados de segurança dos reports."""
        return {
            "security_score": reports.get("security_analysis", {}).get("score", 0),
            "vulnerabilities": reports.get("security_analysis", {}).get("vulnerabilities", []),
            "ssl_status": reports.get("security_analysis", {}).get("ssl", {}),
            "privacy_policy": reports.get("security_analysis", {}).get("privacy", {}),
            "data_protection": reports.get("security_analysis", {}).get("data_protection", {})
        }

    def _extract_infrastructure_data(self, reports: Dict[str, Any]) -> Dict[str, Any]:
        """Extrai dados de infraestrutura dos reports."""
        return {
            "hosting": reports.get("infrastructure", {}).get("hosting", {}),
            "performance": reports.get("infrastructure", {}).get("performance", {}),
            "cdn": reports.get("infrastructure", {}).get("cdn", {}),
            "monitoring": reports.get("infrastructure", {}).get("monitoring", {}),
            "backup": reports.get("infrastructure", {}).get("backup", {})
        }
