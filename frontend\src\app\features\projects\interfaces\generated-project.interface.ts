/**
 * Interface para justificativa de agente individual
 */
export interface IAgenteJustificativa {
	nome_agente?: string;
	justificativa?: string;
	pontuacao?: number;
}

/**
 * Interface para estimativa detalhada gerada pelo Team Agno
 */
export interface IEstimativaDetalhada {
	// Novo formato de estimativa de escopo
	estimativa_escopo?: {
		projeto_nome: string;
		descricao_detalhada: string;
		complexidade_geral: number;
		risco_tecnico: 'Alto' | 'Médio' | 'Baixo';

		arquitetura: {
			tipo: string;
			justificativa: string;
			tecnologias_principais: string[];
			integracao_terceiros: string[];
			diagrama_mermaid: string;
		};

		cronograma: {
			duracao_meses: number;
			total_sprints: number;
			sprints_por_mes: number;
			marcos_principais: Array<{
				sprint: number;
				entrega: string;
			}>;
		};

		equipe_necessaria: {
			total_profissionais: number;
			formacao: Array<{
				funcao: string;
				quantidade: number;
				senioridade: string;
				custo_hora: number;
				horas_mes: number;
				custo_mensal: number;
				justificativa: string;
			}>;
			custo_mensal_total: number;
			custo_projeto_total: number;
		};

		stacks_tecnologicas: Array<{
			categoria: string;
			tecnologias: string[];
			justificativa: string;
			complexidade: number;
			horas_estimadas: number;
		}>;

		funcionalidades_principais: Array<{
			modulo: string;
			descricao: string;
			complexidade: number;
			horas_base: number;
			fator_risco: number;
			horas_final: number;
			status: string;
		}>;

		analise_riscos: {
			riscos_identificados: string[];
			fator_risco_total: number;
			plano_mitigacao: string;
		};

		investimento_total: {
			desenvolvimento: number;
			infraestrutura_mes: number;
			infraestrutura_setup: number;
			contingencia_20pct: number;
			total_projeto: number;
			valor_mensal_operacao: number;
		};

		entregaveis: string[];
		premissas: string[];
		recomendacoes: string[];
	};

	// Formato antigo para compatibilidade
	projeto?: {
		nome?: string;
		descricao?: string;
		requisitos?: string;
		prazo_de_entrega?: string;
		sprint_por_mes?: number;
		total_de_sprints?: number;
		total_de_horas_de_capacity?: number;
		tecnologias?: string[];
		prd?: string;
		diagrama?: string;
		stacks?: Array<{
			stack?: string;
			quantidade_de_desenvolvedores?: number;
			tecnologias?: string[];
			desenvolvedores?: Array<{
				quantidade?: string;
				senioridade?: string[];
				modalidade?: string;
				duracao?: string;
				motivo?: string;
			}>;
			diagrama?: string;
			motivo?: string;
		}>;
	};

	// Campos básicos para compatibilidade
	gerada_em?: string;
	tempo_processamento?: string;
	agentes_consultados?: number;
}

/**
 * Interface para projetos gerados automaticamente
 * Estrutura completa baseada nos dados retornados pelo backend (em português)
 */
export interface IGeneratedProject {
	// Campos básicos
	_id: string; // Mantém _id para clareza com MongoDB
	nome_projeto: string;
	cliente_nome: string;
	cliente_sector: string;
	resumo: string;
	tags: string[];
	progresso: number;
	equipe: string;
	status: string;

	// Campos de data
	created_at: string;
	updated_at: string;

	// Novos campos adicionados (em português)
	justificativa: string;
	agentes_justificativa: IAgenteJustificativa[]; // Array de justificativas dos agentes
	importancia: string;
	pontuacao: number;
	agente_responsavel: string;
	area_especialidade: string;

	// Campos adicionais (opcionais, em português)
	detalhamento?: string;
	beneficios: string;
	cliente_company?: string; // Pode manter em inglês se for nome de campo específico da API externa
	cliente_id?: string;
	
	// Campo para identificar origem do projeto
	ai_generated?: boolean; // true se foi gerado/refinado pela IA, false/undefined para sugestões automáticas

	// 🎯 NOVOS CAMPOS DE ESTIMATIVA
	estimativa_solicitada?: boolean;
	estimativa_status?: 'não_solicitada' | 'processando' | 'concluida' | 'erro';
	estimativa_detalhada?: IEstimativaDetalhada;
	estimativa_solicitada_em?: string;
	estimativa_concluida_em?: string;
	estimativa_erro?: string;

	// 🎯 NOVO: Campo "projeto" estruturado do Team Agno
	projeto?: IProjeto;
}

/**
 * 🎯 NOVA Interface para o campo "projeto" estruturado
 */
export interface IProjeto {
	nome?: string;
	descricao?: string;
	requisitos?: string;
	prazo_de_entrega?: string;
	sprint_por_mes?: number;
	total_de_sprints?: number;
	total_de_horas_de_capacity?: number;
	tecnologias?: string[];
	prd?: string;
	diagrama?: string;
	stacks?: IProjetoStack[];
	custos_estimados_r$?: IProjetoCustos;
	roadmap?: IProjetoRoadmap;
	analise_riscos?: IProjetoRiscos;
}

export interface IProjetoStack {
	stack?: string;
	quantidade_de_desenvolvedores?: number;
	tecnologias?: string[];
	desenvolvedores?: IProjetoDesenvolvedor[];
	diagrama?: string;
	motivo?: string;
}

export interface IProjetoDesenvolvedor {
	quantidade?: string;
	senioridade?: string[];
	modalidade?: string;
	duracao?: string;
	motivo?: string;
}

export interface IProjetoCustos {
	total_estimada?: number;
	detalhamento?: Record<string, number>;
}

export interface IProjetoRoadmap {
	fases?: IProjetoFase[];
}

export interface IProjetoFase {
	nome?: string;
	duracao_semanas?: number | null;
	atividades?: string[];
}

export interface IProjetoRiscos {
	riscos?: string[];
	mitigacoes?: string[];
}

/**
 * Interface para filtros de projetos gerados
 */
export interface IGeneratedProjectFilter {
	search?: string;
	cliente_sector?: string;
	equipe?: string;
	agente_responsavel?: string;
	area_especialidade?: string;
	importancia?: string;
	tags?: string[];
	sortBy?:
		| 'nome_projeto'
		| 'cliente_nome'
		| 'cliente_sector'
		| 'created_at'
		| 'updated_at'
		| 'pontuacao'
		| 'progresso'
		| 'equipe'
		| 'agente_responsavel'
		| 'area_especialidade'
		| 'importancia'
		| 'status';
	sortOrder?: 'asc' | 'desc';
}

/**
 * Interface para evento de clique no card
 */
export interface IProjectCardClickEvent {
	project: IGeneratedProject;
	action: 'view' | 'nextPhase' | 'viewEstimate';
}

/**
 * Interface para configuração de layout dos cards
 */
export interface ICardLayoutConfig {
	columns: number;
	gap: string;
	showSummary: boolean;
	showTags: boolean;
	showProgress: boolean;
}
