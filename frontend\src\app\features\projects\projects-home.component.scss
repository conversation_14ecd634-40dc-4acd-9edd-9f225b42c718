/* Estilos específicos para o componente projects-home */

.projects-home {
	&__card {
		transition: all 0.2s ease-in-out;

		&:hover {
			transform: translateY(-2px);
			box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
		}
	}

	&__progress-bar {
		.p-progressbar {
			height: 8px;
			border-radius: 4px;
			background-color: #f1f5f9;

			.p-progressbar-value {
				border-radius: 4px;
			}
		}
	}

	&__tag {
		font-size: 0.75rem;
		padding: 0.25rem 0.5rem;
	}

	&__empty-state {
		padding: 3rem;
		text-align: center;

		.empty-icon {
			font-size: 4rem;
			color: #cbd5e1;
			margin-bottom: 1rem;
		}
	}
}

/* Responsividade para cards */
@media (max-width: 768px) {
	.projects-home {
		&__card {
			margin-bottom: 1rem;
		}
	}
}

/* Customizações específicas do PrimeNG */
:host ::ng-deep {
	.p-card {
		border-radius: 12px;
		border: 1px solid #e2e8f0;

		.p-card-body {
			padding: 1.5rem;
		}
	}

	.p-progressbar {
		border-radius: 6px;

		.p-progressbar-value {
			border-radius: 6px;
		}
	}

	.p-chip {
		font-size: 0.75rem;
		padding: 0.25rem 0.75rem;
	}

	.p-tag {
		font-size: 0.75rem;
		font-weight: 500;
	}

	/* Project Details Drawer Styles */
	.project-details-drawer {
		.p-drawer {
			max-width: 75vw;

			@media (max-width: 768px) {
				max-width: 100vw;
				width: 100vw !important;
			}
		}

		.p-drawer-header {
			background: linear-gradient(135deg, #3b82f6, #6366f1);
			border: none;
			color: white;

			.p-drawer-header-title {
				color: white;
				font-weight: 600;
			}

			.p-drawer-close-button {
				color: white;

				&:hover {
					background: rgba(255, 255, 255, 0.1);
				}
			}
		}

		.p-drawer-content {
			background: #f8fafc;
			padding: 1.5rem;
		}
	}

	/* Estimativa Details Drawer Styles */
	.estimativa-details-drawer {
		.p-drawer {
			max-width: 85vw;

			@media (max-width: 768px) {
				max-width: 100vw;
				width: 100vw !important;
			}
		}

		.p-drawer-content {
			background: #f8fafc;
			padding: 1.5rem;

			/* Scrollbar personalizada */
			scrollbar-width: thin;
			scrollbar-color: #d1d5db #f9fafb;

			&::-webkit-scrollbar {
				width: 8px;
			}

			&::-webkit-scrollbar-track {
				background: #f9fafb;
				border-radius: 10px;
			}

			&::-webkit-scrollbar-thumb {
				background: #d1d5db;
				border-radius: 10px;
			}

			&::-webkit-scrollbar-thumb:hover {
				background: #9ca3af;
			}
		}
	}
}

/* Drawer Content Animations */
.drawer-header {
	animation: slideInFromTop 0.3s ease-out;
}

@keyframes slideInFromTop {
	from {
		opacity: 0;
		transform: translateY(-20px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

/* Card animations for drawer content */
.drawer-header + div,
.drawer-header ~ div {
	animation: fadeInUp 0.4s ease-out;
	animation-fill-mode: both;
}

@keyframes fadeInUp {
	from {
		opacity: 0;
		transform: translateY(20px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

/* Estilos para PRD Tab */
.prd-content {
	padding: 1rem;

	.prd-header {
		margin-bottom: 2rem;
		text-align: center;

		h4 {
			color: #1f2937;
			margin-bottom: 0.5rem;
		}

		p {
			color: #6b7280;
			font-size: 0.95rem;
		}
	}

	.prd-document {
		background: white;
		border-radius: 8px;
		box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
		overflow: hidden;
	}

	.prd-empty {
		text-align: center;
		padding: 3rem;
		color: #6b7280;

		i {
			font-size: 3rem;
			margin-bottom: 1rem;
			color: #d1d5db;
		}

		h5 {
			margin-bottom: 0.5rem;
			color: #374151;
		}
	}
}

/* Estilos para Stack Diagrams */
.stack-diagram {
	margin-top: 1rem;
	padding-top: 1rem;
	border-top: 1px solid #e5e7eb;

	h6 {
		color: #374151;
		margin-bottom: 0.75rem;
		font-size: 0.9rem;
		font-weight: 600;
	}
}

.stack-diagrams-overview {
	margin-top: 2rem;
	padding-top: 2rem;
	border-top: 2px solid #e5e7eb;

	h5 {
		color: #1f2937;
		margin-bottom: 1.5rem;
		font-weight: 600;
	}

	.stack-diagram-cards {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
		gap: 1.5rem;

		.diagram-card {
			background: white;
			border: 1px solid #e5e7eb;
			border-radius: 8px;
			padding: 1.5rem;
			box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

			h6 {
				color: #1f2937;
				margin-bottom: 1rem;
				font-weight: 600;
				text-align: center;
				padding-bottom: 0.5rem;
				border-bottom: 1px solid #f3f4f6;
			}
		}
	}
}

/* Estilos para Diagram Container */
.diagram-container {
	background: white;
	border-radius: 8px;
	padding: 1rem;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
	overflow-x: auto;
}

/* Responsividade para diagramas */
@media (max-width: 768px) {
	.stack-diagram-cards {
		grid-template-columns: 1fr;

		.diagram-card {
			padding: 1rem;
		}
	}

	.diagram-container {
		padding: 0.75rem;
	}
}

/* Utility classes */
.line-clamp-2 {
	display: -webkit-box;
	-webkit-line-clamp: 2;
	line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
	text-overflow: ellipsis;
}
