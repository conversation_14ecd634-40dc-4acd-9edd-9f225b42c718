.project-estimate-dialog {
	.p-dialog-content {
		padding: 0;
	}

	.p-tabview {
		.p-tabview-nav {
			background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
			border-radius: 8px 8px 0 0;

			li {
				.p-tabview-nav-link {
					color: white;
					font-weight: 500;

					&:hover {
						background: rgba(255, 255, 255, 0.1);
					}

					&.p-highlight {
						background: rgba(255, 255, 255, 0.2);
						border-color: rgba(255, 255, 255, 0.3);
					}
				}
			}
		}

		.p-tabview-panels {
			padding: 1.5rem;
			background: #f8fafc;
		}
	}
}

// Estilos para chips de tecnologia
.tech-chip-success {
	background: #dcfce7 !important;
	color: #166534 !important;
	border: 1px solid #bbf7d0;
}

.tech-chip-info {
	background: #dbeafe !important;
	color: #1e40af !important;
	border: 1px solid #bfdbfe;
}

.tech-chip-warning {
	background: #fef3c7 !important;
	color: #92400e !important;
	border: 1px solid #fde68a;
}

.tech-chip-help {
	background: #f3e8ff !important;
	color: #7c2d12 !important;
	border: 1px solid #e9d5ff;
}

.tech-chip-secondary {
	background: #f1f5f9 !important;
	color: #475569 !important;
	border: 1px solid #e2e8f0;
}

// Estilos para cards
p-card {
	.p-card {
		border: 1px solid #e2e8f0;
		border-radius: 12px;
		box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
		transition: all 0.3s ease;

		&:hover {
			box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
			transform: translateY(-2px);
		}

		.p-card-header {
			background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
			border-bottom: 1px solid #e2e8f0;
			font-weight: 600;
			color: #1e293b;
		}

		.p-card-content {
			padding: 1.25rem;
		}
	}
}

// Estilos para timeline
.p-timeline {
	.p-timeline-event-content {
		.p-card {
			border-left: 4px solid #3b82f6;

			.p-card-header {
				background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
				color: #1e40af;
			}
		}
	}
}

// Estilos para conteúdo PRD
.prd-content {
	max-height: 500px;
	overflow-y: auto;

	pre {
		line-height: 1.6;
		font-size: 0.875rem;
		color: #374151;
	}
}

// Estilos para container de diagramas
.mermaid-container {
	pre {
		background: #f8fafc;
		border: 1px solid #e2e8f0;
		border-radius: 8px;
		padding: 1rem;
		font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
		font-size: 0.8rem;
		line-height: 1.5;
		color: #1e293b;
		max-height: 400px;
		overflow: auto;
	}
}

// Animações
@keyframes fadeInUp {
	from {
		opacity: 0;
		transform: translateY(20px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.p-tabview-panel {
	animation: fadeInUp 0.3s ease-out;
}

// Responsividade
@media (max-width: 768px) {
	.project-estimate-dialog {
		.p-dialog {
			width: 95vw !important;
			height: 95vh !important;
			margin: 2.5vh auto;
		}

		.grid-cols-3 {
			grid-template-columns: 1fr;
		}

		.grid-cols-2 {
			grid-template-columns: 1fr;
		}
	}
}
