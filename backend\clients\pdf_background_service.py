#!/usr/bin/env python3
"""
Serviço de Background para Geração Automática de PDFs
Gera PDFs aprimorados automaticamente quando:
1. Dossiê expandido estiver pronto
2. Pesquisa de mercado estiver concluída
"""

import asyncio
import logging
from datetime import datetime, UTC
from typing import Optional
from bson import ObjectId
from gridfs import GridFS

from .db import clients_collection, db
from shared.websocket_manager import WebSocketManager

# Configurar logging
logger = logging.getLogger(__name__)


class PDFBackgroundService:
    """Serviço para geração automática de PDFs em background"""

    def __init__(self):
        self.fs = GridFS(db)
        self.websocket_manager = WebSocketManager()
        self._enhanced_generator = None
        self._load_generator()

    def _load_generator(self):
        """Carrega o gerador PDF aprimorado"""
        try:
            from tools.reports.pdf_generator_enhanced import AIReportGeneratorEnhanced
            self._enhanced_generator = AIReportGeneratorEnhanced()
            logger.info(
                "✅ Gerador PDF aprimorado carregado para background service")
        except Exception as e:
            logger.warning(f"⚠️ Gerador PDF aprimorado não disponível: {e}")

    async def check_and_generate_pdf(self, client_id: str) -> bool:
        """
        Verifica se cliente está pronto para PDF e gera se necessário

        Args:
            client_id: ID do cliente

        Returns:
            bool: True se PDF foi gerado, False caso contrário
        """
        try:
            # Buscar cliente
            client = clients_collection.find_one({"_id": ObjectId(client_id)})
            if not client:
                logger.warning(f"Cliente {client_id} não encontrado")
                return False

            # Verificar se já tem PDF aprimorado recente
            if self._has_recent_enhanced_pdf(client):
                logger.info(
                    f"Cliente {client.get('name')} já possui PDF aprimorado recente")
                return False

            # Verificar se tem os requisitos necessários
            dossie_expandido = self._get_expanded_dossier(client)
            pesquisa_mercado = self._get_market_research(client)

            if not dossie_expandido:
                logger.info(
                    f"Cliente {client.get('name')} não possui dossiê expandido pronto")
                return False

            if not pesquisa_mercado:
                logger.info(
                    f"Cliente {client.get('name')} não possui pesquisa de mercado pronta")
                return False

            # Ambos estão prontos - gerar PDF
            logger.info(
                f"🚀 Iniciando geração automática de PDF para {client.get('name')}")

            # Notificar via WebSocket
            await self.websocket_manager.broadcast({
                "type": "pdf_generation_started",
                "client_id": client_id,
                "client_name": client.get("name"),
                "message": "Gerando relatório executivo aprimorado..."
            })

            # Gerar PDF em background (sem bloquear)
            asyncio.create_task(self._generate_pdf_async(
                client_id, client, dossie_expandido))

            return True

        except Exception as e:
            logger.error(
                f"Erro ao verificar/gerar PDF para cliente {client_id}: {e}")
            return False

    async def _generate_pdf_async(self, client_id: str, client: dict, dossie_expandido: dict):
        """Gera PDF de forma assíncrona em background"""
        try:
            if not self._enhanced_generator:
                logger.error("Gerador PDF não disponível")
                return

            client_name = client.get("name", "Cliente")

            # Gerar PDF (operação longa)
            logger.info(f"📄 Gerando PDF aprimorado para {client_name}...")
            pdf_content = self._enhanced_generator.generate_report(
                dossie_expandido, client_name)

            # Salvar no GridFS
            pdf_file_id = self.fs.put(
                pdf_content,
                filename=f"relatorio_executivo_{client_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                content_type="application/pdf",
                client_id=client_id,
                generated_at=datetime.now(UTC),
                report_type="enhanced_executive_auto"  # Marca como gerado automaticamente
            )

            # Atualizar cliente
            clients_collection.update_one(
                {"_id": ObjectId(client_id)},
                {
                    "$set": {
                        "enhanced_pdf_id": pdf_file_id,
                        "enhanced_pdf_generated_at": datetime.now(UTC),
                        "pdf_report_available": True,
                        "pdf_report_type": "enhanced_auto",
                        "updated_at": datetime.now(UTC)
                    }
                }
            )

            # Notificar conclusão
            await self.websocket_manager.broadcast({
                "type": "pdf_generation_completed",
                "client_id": client_id,
                "client_name": client_name,
                "pdf_id": str(pdf_file_id),
                "file_size_kb": round(len(pdf_content) / 1024, 1),
                "message": f"Relatório executivo aprimorado pronto para {client_name}!"
            })

            logger.info(
                f"✅ PDF gerado automaticamente para {client_name}! ID: {pdf_file_id}")

        except Exception as e:
            logger.error(f"Erro na geração assíncrona de PDF: {e}")

            # Notificar erro
            await self.websocket_manager.broadcast({
                "type": "pdf_generation_error",
                "client_id": client_id,
                "client_name": client.get("name"),
                "error": str(e),
                "message": "Erro na geração do relatório executivo"
            })

    def _get_expanded_dossier(self, client: dict) -> Optional[dict]:
        """Busca dossiê expandido do cliente"""
        for report in client.get("reports", []):
            if (report.get("reportType") == "dossie_expandido" and
                    report.get("status") == "completed"):
                return report.get("data", {})
        return None

    def _get_market_research(self, client: dict) -> Optional[dict]:
        """Busca pesquisa de mercado do dossiê expandido"""
        # A pesquisa de mercado agora está incluída no dossiê expandido
        expanded_dossier = client.get("expanded_dossier", {})
        return expanded_dossier.get("dados_pesquisa_mercado", {})

    def _has_recent_enhanced_pdf(self, client: dict) -> bool:
        """Verifica se cliente já tem PDF aprimorado recente (últimas 24h)"""
        if not client.get("enhanced_pdf_generated_at"):
            return False

        # Verificar se foi gerado nas últimas 24 horas
        generated_at = client.get("enhanced_pdf_generated_at")
        if isinstance(generated_at, datetime):
            hours_ago = (datetime.now(UTC) -
                         generated_at).total_seconds() / 3600
            return hours_ago < 24  # PDF recente se gerado nas últimas 24h

        return False


# Instância global do serviço
pdf_service = PDFBackgroundService()


async def trigger_pdf_check(client_id: str):
    """
    Função helper para triggerar verificação de PDF
    Chamada quando dossiê ou pesquisa são concluídos
    """
    return await pdf_service.check_and_generate_pdf(client_id)
