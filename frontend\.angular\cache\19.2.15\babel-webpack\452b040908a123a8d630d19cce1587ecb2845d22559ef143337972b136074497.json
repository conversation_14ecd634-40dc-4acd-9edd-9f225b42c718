{"ast": null, "code": "// src/eventbus/index.ts\nfunction EventBus() {\n  const allHandlers = /* @__PURE__ */new Map();\n  return {\n    on(type, handler) {\n      let handlers = allHandlers.get(type);\n      if (!handlers) handlers = [handler];else handlers.push(handler);\n      allHandlers.set(type, handlers);\n      return this;\n    },\n    off(type, handler) {\n      let handlers = allHandlers.get(type);\n      if (handlers) {\n        handlers.splice(handlers.indexOf(handler) >>> 0, 1);\n      }\n      return this;\n    },\n    emit(type, evt) {\n      let handlers = allHandlers.get(type);\n      if (handlers) {\n        handlers.slice().map(handler => {\n          handler(evt);\n        });\n      }\n    },\n    clear() {\n      allHandlers.clear();\n    }\n  };\n}\nexport { EventBus };", "map": {"version": 3, "names": ["EventBus", "allHandlers", "Map", "on", "type", "handler", "handlers", "get", "push", "set", "off", "splice", "indexOf", "emit", "evt", "slice", "map", "clear"], "sources": ["C:/projetos/scope-ai/frontend/node_modules/@primeuix/utils/eventbus/index.mjs"], "sourcesContent": ["// src/eventbus/index.ts\nfunction EventBus() {\n  const allHandlers = /* @__PURE__ */ new Map();\n  return {\n    on(type, handler) {\n      let handlers = allHandlers.get(type);\n      if (!handlers) handlers = [handler];\n      else handlers.push(handler);\n      allHandlers.set(type, handlers);\n      return this;\n    },\n    off(type, handler) {\n      let handlers = allHandlers.get(type);\n      if (handlers) {\n        handlers.splice(handlers.indexOf(handler) >>> 0, 1);\n      }\n      return this;\n    },\n    emit(type, evt) {\n      let handlers = allHandlers.get(type);\n      if (handlers) {\n        handlers.slice().map((handler) => {\n          handler(evt);\n        });\n      }\n    },\n    clear() {\n      allHandlers.clear();\n    }\n  };\n}\nexport {\n  EventBus\n};\n"], "mappings": "AAAA;AACA,SAASA,QAAQA,CAAA,EAAG;EAClB,MAAMC,WAAW,GAAG,eAAgB,IAAIC,GAAG,CAAC,CAAC;EAC7C,OAAO;IACLC,EAAEA,CAACC,IAAI,EAAEC,OAAO,EAAE;MAChB,IAAIC,QAAQ,GAAGL,WAAW,CAACM,GAAG,CAACH,IAAI,CAAC;MACpC,IAAI,CAACE,QAAQ,EAAEA,QAAQ,GAAG,CAACD,OAAO,CAAC,CAAC,KAC/BC,QAAQ,CAACE,IAAI,CAACH,OAAO,CAAC;MAC3BJ,WAAW,CAACQ,GAAG,CAACL,IAAI,EAAEE,QAAQ,CAAC;MAC/B,OAAO,IAAI;IACb,CAAC;IACDI,GAAGA,CAACN,IAAI,EAAEC,OAAO,EAAE;MACjB,IAAIC,QAAQ,GAAGL,WAAW,CAACM,GAAG,CAACH,IAAI,CAAC;MACpC,IAAIE,QAAQ,EAAE;QACZA,QAAQ,CAACK,MAAM,CAACL,QAAQ,CAACM,OAAO,CAACP,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;MACrD;MACA,OAAO,IAAI;IACb,CAAC;IACDQ,IAAIA,CAACT,IAAI,EAAEU,GAAG,EAAE;MACd,IAAIR,QAAQ,GAAGL,WAAW,CAACM,GAAG,CAACH,IAAI,CAAC;MACpC,IAAIE,QAAQ,EAAE;QACZA,QAAQ,CAACS,KAAK,CAAC,CAAC,CAACC,GAAG,CAAEX,OAAO,IAAK;UAChCA,OAAO,CAACS,GAAG,CAAC;QACd,CAAC,CAAC;MACJ;IACF,CAAC;IACDG,KAAKA,CAAA,EAAG;MACNhB,WAAW,CAACgB,KAAK,CAAC,CAAC;IACrB;EACF,CAAC;AACH;AACA,SACEjB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}