# Tasks - Foco Atual

**Última Atualização**: 2025-01-29
**Sprint**: Refatoração Arquitetural
**Velocity**: 45 pontos

## 📌 Tarefa Ativa

### [TASK-001] Implementar DDD no Módulo Clients
**Status**: 🟡 EM PROGRESSO
**Prioridade**: P1
**Estimativa**: 40h
**Início**: 2025-01-28
**Responsável**: Team

#### Objetivo
Refatorar módulo Clients para Domain-Driven Design com Arquitetura Hexagonal, mantendo compatibilidade com sistema atual.

#### Subtasks
1. [x] Draft & review micro-task prompt ✅
2. [x] Reasoning Prelude (ToT + Sequential Thinking) ✅
3. [x] Repo scan – confirm files ✅
4. [x] Core domain implementation ✅
5. [ ] Infrastructure layer (MongoDB adapters)
6. [ ] Application services
7. [ ] API controllers refactor
8. [ ] Integration tests ≥ 80%
9. [ ] Documentation update
10. [ ] Self-score & log

#### Progresso Detalhado
- **MT-1**: Core domain entities criadas (Client, Project, Report) ✅
- **MT-2**: Value objects implementados (ClientId, Email, URL) ✅
- **MT-3**: Domain events estruturados ✅
- **MT-4**: Base repositories definidos ⏳
- **MT-5**: MongoDB adapters pendente

#### Arquivos Afetados
- `/backend/src/core/` - Base classes
- `/backend/src/modules/clients/` - Novo módulo DDD
- `/backend/clients/` - Módulo legado (será deprecado)

---

## 📋 Backlog Sprint

### [TASK-002] Corrigir Campo Progresso Inicial
**Status**: 📝 TODO
**Prioridade**: P1
**Estimativa**: 4h
**Blocked by**: None

**Goal**: Projetos em status "sugestão" devem ter progresso 0%, não 5-25%

### [TASK-003] Implementar SearchSociety
**Status**: 📝 TODO
**Prioridade**: P2
**Estimativa**: 16h
**Blocked by**: TASK-001

**Goal**: Substituir Perplexity API por múltiplas fontes gratuitas

### [TASK-004] Automated Validation Module
**Status**: 📝 TODO
**Prioridade**: P1
**Estimativa**: 24h
**Blocked by**: None

**Goal**: Validação automática com Playwright + evidências visuais

---

## 📊 Métricas do Sprint

### Burndown
- **Pontos Totais**: 45
- **Pontos Completos**: 12
- **Pontos em Progresso**: 15
- **Pontos Restantes**: 18

### Distribuição por Prioridade
- P1: 3 tarefas (75% do esforço)
- P2: 1 tarefa (25% do esforço)
- P3: 0 tarefas

### Health Metrics
- **Blockers**: 1 (SearchSociety aguarda DDD)
- **Risks**: Migração pode afetar performance
- **Dependencies**: MongoDB adapter crítico

---

## 🗂️ Arquivo de Tarefas

### Concluídas Recentemente
- ➜ [memory-bank/archive/2025/01/2025_01_27__14-30__Limpeza_Frontend_Angular.md](mdc:../archive/2025/01/2025_01_27__14-30__Limpeza_Frontend_Angular.md)
- ➜ [memory-bank/archive/2025/01/2025_01_26__09-15__Conversao_Assincrona_Backend.md](mdc:../archive/2025/01/2025_01_26__09-15__Conversao_Assincrona_Backend.md)

### Em Elaboração
- ➜ [memory-bank/roadmap/2025_01_30__10-00__GraphRAG_Implementation.md](mdc:./2025_01_30__10-00__GraphRAG_Implementation.md)
- ➜ [memory-bank/roadmap/2025_02_05__14-00__Kubernetes_Deploy.md](mdc:./2025_02_05__14-00__Kubernetes_Deploy.md)

---

## 🎯 Definition of Done

Para uma tarefa ser considerada completa:
- [ ] Código implementado e testado
- [ ] Cobertura de testes ≥ 80%
- [ ] Documentação atualizada
- [ ] Code review aprovado
- [ ] Zero bugs críticos
- [ ] Performance validada
- [ ] Arquivo movido para /archive/
- [ ] Score ≥ 27/30 (90%) 