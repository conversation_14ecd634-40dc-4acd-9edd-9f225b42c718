"""
Agentes especializados usando framework Agno

Agentes do Time Agno para análise arquitetural:
- PerformanceAgent: Especialista em performance web
- AccessibilityAgent: Especialista em acessibilidade
- SEOSpecialistAgent: Especialista em SEO completo e estratégico 
- SecurityAgent: Especialista em segurança
- UXUIDesignerAgent: Especialista em UX/UI design
- ProductOwnerAgent: Especialista em estratégia de produto e roadmap
- BenchmarkAgent: Especialista em análise competitiva e benchmarking
- TechnicalWriterAgent: Especialista em documentação técnica
"""

from .performance_agent import PerformanceAgent
from .accessibility_agent import AccessibilityAgent
from .seo_agent import SEOSpecialistAgent
from .security_agent import SecurityAgent
from .ux_ui_designer_agent import UXUIDesignerAgent
from .product_owner_agent import ProductOwnerAgent
from .benchmark_agent import BenchmarkAgent
from .technical_writer_agent import TechnicalWriterAgent

__all__ = [
    "PerformanceAgent",
    "AccessibilityAgent",
    "SEOSpecialistAgent",
    "SecurityAgent",
    "UXUIDesignerAgent",
    "ProductOwnerAgent",
    "BenchmarkAgent",
    "TechnicalWriterAgent"
]
