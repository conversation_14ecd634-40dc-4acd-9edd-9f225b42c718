import { CommonModule } from '@angular/common';
import {
	AfterViewInit,
	Component,
	ElementRef,
	Input,
	OnChanges,
	OnDestroy,
	OnInit,
	ViewChild,
	signal,
} from '@angular/core';
import cytoscape, { Core, ElementDefinition } from 'cytoscape';
import dagre from 'cytoscape-dagre';
import elk from 'cytoscape-elk';

// Registrar layouts
cytoscape.use(dagre);
cytoscape.use(elk);

interface DiagramNode {
	id: string;
	label: string;
	type?: 'entity' | 'process' | 'decision' | 'start' | 'end';
	group?: string;
}

interface DiagramEdge {
	source: string;
	target: string;
	label?: string;
	type?: 'relationship' | 'flow' | 'dependency';
}

@Component({
	selector: 'app-cytoscape-diagram',
	standalone: true,
	imports: [CommonModule],
	template: `
		<div class="cytoscape-container">
			<div class="diagram-header">
				<h6>📊 Diagrama de Arquitetura</h6>
				<div class="header-actions">
					<button type="button" class="action-btn" (click)="toggleView()" title="Alternar visualização">
						<i class="pi" [ngClass]="showRendered() ? 'pi-code' : 'pi-eye'"></i>
						{{ showRendered() ? 'Código' : 'Diagrama' }}
					</button>
					<button type="button" class="action-btn" (click)="exportDiagram()" title="Exportar como PNG">
						<i class="pi pi-download"></i>
						Exportar
					</button>
					<button type="button" class="action-btn" (click)="resetZoom()" title="Resetar zoom">
						<i class="pi pi-refresh"></i>
						Reset
					</button>
				</div>
			</div>

			<!-- Diagrama Renderizado -->
			<div class="diagram-content" [ngClass]="{ 'show-rendered': showRendered(), 'show-code': !showRendered() }">
				<div class="rendered-diagram" #cytoscapeContainer></div>

				<!-- Fallback: Mostrar código original -->
				<div class="diagram-fallback">
					<div class="diagram-notice">
						<i class="pi pi-info-circle"></i>
						<span>Código Original - Copie para usar em outros editores</span>
						<a href="https://mermaid.live/" target="_blank" class="mermaid-link"> Abrir no Mermaid Live </a>
					</div>

					<pre class="diagram-code"><code>{{ originalCode() }}</code></pre>
				</div>
			</div>

			@if (errorMessage()) {
				<div class="error-message">
					<i class="pi pi-exclamation-triangle"></i>
					<p>{{ errorMessage() }}</p>
					<small>Mostrando código original como fallback</small>
				</div>
			}
		</div>
	`,
	styles: [
		`
			.cytoscape-container {
				width: 100%;
				background: #f8f9fa;
				border: 1px solid #e0e0e0;
				border-radius: 8px;
				overflow: hidden;
			}

			.diagram-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 0.75rem 1rem;
				background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
				color: white;
				border-bottom: 1px solid #e0e0e0;
			}

			.diagram-header h6 {
				margin: 0;
				font-size: 1rem;
				font-weight: 600;
			}

			.header-actions {
				display: flex;
				gap: 0.5rem;
			}

			.action-btn {
				display: flex;
				align-items: center;
				gap: 0.25rem;
				padding: 0.375rem 0.75rem;
				background: rgba(255, 255, 255, 0.2);
				color: white;
				border: 1px solid rgba(255, 255, 255, 0.3);
				border-radius: 4px;
				font-size: 0.875rem;
				cursor: pointer;
				transition: all 0.2s ease;
			}

			.action-btn:hover {
				background: rgba(255, 255, 255, 0.3);
				border-color: rgba(255, 255, 255, 0.5);
			}

			.diagram-content {
				position: relative;
				min-height: 400px;
			}

			.rendered-diagram {
				width: 100%;
				height: 400px;
				background: white;
			}

			.show-rendered .rendered-diagram {
				display: block;
			}

			.show-rendered .diagram-fallback {
				display: none;
			}

			.show-code .rendered-diagram {
				display: none;
			}

			.show-code .diagram-fallback {
				display: block;
			}

			.diagram-fallback {
				padding: 1rem;
			}

			.diagram-notice {
				display: flex;
				align-items: center;
				gap: 0.5rem;
				margin-bottom: 1rem;
				padding: 0.75rem;
				background: #e3f2fd;
				border: 1px solid #bbdefb;
				border-radius: 4px;
				font-size: 0.875rem;
				color: #1565c0;
			}

			.mermaid-link {
				color: #1976d2;
				text-decoration: none;
				font-weight: 500;
				margin-left: auto;
			}

			.mermaid-link:hover {
				text-decoration: underline;
			}

			.diagram-code {
				background: #f5f5f5;
				border: 1px solid #ddd;
				border-radius: 4px;
				padding: 1rem;
				font-family: 'Courier New', monospace;
				font-size: 0.875rem;
				line-height: 1.4;
				overflow-x: auto;
				white-space: pre-wrap;
				word-wrap: break-word;
			}

			.error-message {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				padding: 1rem;
				color: #dc3545;
				text-align: center;
				background: #fff5f5;
				border-top: 1px solid #fed7d7;
			}

			.error-message i {
				font-size: 1.5rem;
				margin-bottom: 0.5rem;
			}

			.error-message p {
				margin: 0.25rem 0;
				font-weight: 600;
			}

			.error-message small {
				color: #6c757d;
			}
		`,
	],
})
export class CytoscapeDiagramComponent implements OnInit, AfterViewInit, OnChanges, OnDestroy {
	@Input() diagram: string = '';
	@Input() theme: 'light' | 'dark' = 'light';
	@Input() layout: 'dagre' | 'elk' | 'grid' | 'circle' = 'dagre';
	@ViewChild('cytoscapeContainer', { static: false }) cytoscapeContainer!: ElementRef;

	// Signals para estado reativo
	public originalCode = signal<string>('');
	public showRendered = signal<boolean>(true);
	public errorMessage = signal<string>('');

	private cy: Core | null = null;

	ngOnInit(): void {
		this.processDiagram();
	}

	ngAfterViewInit(): void {
		if (this.showRendered() && this.originalCode()) {
			this.renderDiagram();
		}
	}

	ngOnChanges(): void {
		this.processDiagram();
		if (this.cytoscapeContainer && this.showRendered()) {
			this.renderDiagram();
		}
	}

	ngOnDestroy(): void {
		if (this.cy) {
			this.cy.destroy();
		}
	}

	public toggleView(): void {
		this.showRendered.set(!this.showRendered());
		if (this.showRendered() && this.originalCode()) {
			setTimeout(() => this.renderDiagram(), 100);
		}
	}

	public resetZoom(): void {
		if (this.cy) {
			this.cy.fit();
			this.cy.center();
		}
	}

	public exportDiagram(): void {
		if (this.cy) {
			const png = this.cy.png({ scale: 2, full: true });
			const link = document.createElement('a');
			link.download = 'diagram.png';
			link.href = png;
			link.click();
		}
	}

	private processDiagram(): void {
		if (!this.diagram) {
			this.originalCode.set('Nenhum diagrama disponível');
			return;
		}

		// DEBUG: Log do código recebido
		console.log('🔍 Código Mermaid recebido:', this.diagram);

		// Limpar código (remover prefixo "mermaid" se existir)
		const cleanCode = this.diagram.replace(/^mermaid\s*\n?/, '').trim();
		this.originalCode.set(cleanCode || 'Diagrama vazio');

		// DEBUG: Log do código limpo
		console.log('🧹 Código limpo:', cleanCode);
	}

	private async renderDiagram(): Promise<void> {
		if (!this.cytoscapeContainer || !this.originalCode()) {
			return;
		}

		try {
			// Limpar container anterior
			if (this.cy) {
				this.cy.destroy();
			}

			// Converter Mermaid para Cytoscape
			const { nodes, edges } = this.parseMermaidToCytoscape(this.originalCode());

			// Criar elementos Cytoscape
			const elements: ElementDefinition[] = [
				...nodes.map(node => ({
					data: { id: node.id, label: node.label, type: node.type, group: node.group },
				})),
				...edges.map(edge => ({
					data: {
						id: `${edge.source}-${edge.target}`,
						source: edge.source,
						target: edge.target,
						label: edge.label,
						type: edge.type,
					},
				})),
			];

			// Inicializar Cytoscape
			this.cy = cytoscape({
				container: this.cytoscapeContainer.nativeElement,
				elements: elements,
				style: this.getCytoscapeStyle(),
				layout: this.getLayoutConfig(),
				userZoomingEnabled: true,
				userPanningEnabled: true,
				boxSelectionEnabled: false,
				autoungrabify: false,
			});

			this.errorMessage.set('');
		} catch (error) {
			console.error('❌ Erro ao renderizar diagrama Cytoscape:', error);
			this.errorMessage.set('Erro ao processar diagrama. Verifique a sintaxe.');
			this.showRendered.set(false); // Mostrar código como fallback
		}
	}

	private parseMermaidToCytoscape(mermaidCode: string): { nodes: DiagramNode[]; edges: DiagramEdge[] } {
		const nodes: DiagramNode[] = [];
		const edges: DiagramEdge[] = [];
		const lines = mermaidCode
			.split('\n')
			.map(line => line.trim())
			.filter(line => line);

		// DEBUG: Log das linhas processadas
		console.log('📝 Linhas do diagrama:', lines);

		let diagramType = 'flowchart';

		// Detectar tipo de diagrama
		if (lines[0]?.includes('erDiagram')) {
			diagramType = 'er';
		} else if (lines[0]?.includes('sequenceDiagram')) {
			diagramType = 'sequence';
		} else if (lines[0]?.includes('graph') || lines[0]?.includes('flowchart')) {
			diagramType = 'flowchart';
		}

		console.log('🎯 Tipo de diagrama detectado:', diagramType);

		switch (diagramType) {
			case 'er':
				this.parseERDiagram(lines, nodes, edges);
				break;
			case 'sequence':
				this.parseSequenceDiagram(lines, nodes, edges);
				break;
			case 'flowchart':
			default:
				this.parseFlowchartDiagram(lines, nodes, edges);
				break;
		}

		console.log('🔗 Nós encontrados:', nodes);
		console.log('🔗 Arestas encontradas:', edges);

		return { nodes, edges };
	}

	private parseERDiagram(lines: string[], nodes: DiagramNode[], edges: DiagramEdge[]): void {
		for (const line of lines) {
			if (line.includes('||--') || line.includes('}--') || line.includes('||..')) {
				// Relacionamento ER: Entity1 ||--o{ Entity2 : relationship
				const match = line.match(/(\w+)\s*(\|\|--|\}--|\|\|\.\.)[o{]*\s*(\w+)\s*:\s*(.+)/);
				if (match) {
					const [, entity1, , entity2, relationship] = match;

					// Adicionar entidades se não existirem
					if (!nodes.find(n => n.id === entity1)) {
						nodes.push({ id: entity1, label: entity1, type: 'entity' });
					}
					if (!nodes.find(n => n.id === entity2)) {
						nodes.push({ id: entity2, label: entity2, type: 'entity' });
					}

					// Adicionar relacionamento
					edges.push({
						source: entity1,
						target: entity2,
						label: relationship.trim(),
						type: 'relationship',
					});
				}
			}
		}
	}

	private parseSequenceDiagram(lines: string[], nodes: DiagramNode[], edges: DiagramEdge[]): void {
		for (const line of lines) {
			if (line.includes('participant')) {
				// Participante: participant A as Alice
				const match = line.match(/participant\s+(\w+)(?:\s+as\s+(.+))?/);
				if (match) {
					const [, id, label] = match;
					nodes.push({ id, label: label || id, type: 'process' });
				}
			} else if (line.includes('->>') || line.includes('->')) {
				// Mensagem: A->>B: Hello
				const match = line.match(/(\w+)\s*-[>]{1,2}\s*(\w+)\s*:\s*(.+)/);
				if (match) {
					const [, source, target, message] = match;

					// Adicionar participantes se não existirem
					if (!nodes.find(n => n.id === source)) {
						nodes.push({ id: source, label: source, type: 'process' });
					}
					if (!nodes.find(n => n.id === target)) {
						nodes.push({ id: target, label: target, type: 'process' });
					}

					edges.push({
						source,
						target,
						label: message.trim(),
						type: 'flow',
					});
				}
			}
		}
	}

	private parseFlowchartDiagram(lines: string[], nodes: DiagramNode[], edges: DiagramEdge[]): void {
		for (const line of lines) {
			if (line.includes('-->') || line.includes('->')) {
				// Conexão: A --> B ou A[Label] --> B[Label]
				const match = line.match(/(\w+)(?:\[([^\]]+)\])?\s*--?>\s*(\w+)(?:\[([^\]]+)\])?/);
				if (match) {
					const [, sourceId, sourceLabel, targetId, targetLabel] = match;

					// Adicionar nós se não existirem
					if (!nodes.find(n => n.id === sourceId)) {
						nodes.push({
							id: sourceId,
							label: sourceLabel || sourceId,
							type: 'process',
						});
					}
					if (!nodes.find(n => n.id === targetId)) {
						nodes.push({
							id: targetId,
							label: targetLabel || targetId,
							type: 'process',
						});
					}

					edges.push({
						source: sourceId,
						target: targetId,
						type: 'flow',
					});
				}
			} else if (line.includes('[') && line.includes(']')) {
				// Nó isolado: A[Label]
				const match = line.match(/(\w+)\[([^\]]+)\]/);
				if (match) {
					const [, id, label] = match;
					if (!nodes.find(n => n.id === id)) {
						nodes.push({ id, label, type: 'process' });
					}
				}
			}
		}
	}

	private getCytoscapeStyle(): any[] {
		const baseStyle = [
			{
				selector: 'node',
				style: {
					'background-color': this.theme === 'dark' ? '#4a5568' : '#e2e8f0',
					'label': 'data(label)',
					'text-valign': 'center',
					'text-halign': 'center',
					'color': this.theme === 'dark' ? '#ffffff' : '#2d3748',
					'font-size': '12px',
					'font-weight': 'bold',
					'border-width': 2,
					'border-color': this.theme === 'dark' ? '#718096' : '#cbd5e0',
					'width': 'label',
					'height': 'label',
					'padding': '8px',
					'shape': 'roundrectangle',
				},
			},
			{
				selector: 'node[type="entity"]',
				style: {
					'background-color': '#4299e1',
					'border-color': '#3182ce',
					'color': '#ffffff',
				},
			},
			{
				selector: 'node[type="process"]',
				style: {
					'background-color': '#48bb78',
					'border-color': '#38a169',
					'color': '#ffffff',
				},
			},
			{
				selector: 'edge',
				style: {
					'width': 2,
					'line-color': this.theme === 'dark' ? '#718096' : '#a0aec0',
					'target-arrow-color': this.theme === 'dark' ? '#718096' : '#a0aec0',
					'target-arrow-shape': 'triangle',
					'curve-style': 'bezier',
					'label': 'data(label)',
					'font-size': '10px',
					'color': this.theme === 'dark' ? '#e2e8f0' : '#4a5568',
					'text-rotation': 'autorotate',
					'text-margin-y': -10,
				},
			},
			{
				selector: 'edge[type="relationship"]',
				style: {
					'line-color': '#ed8936',
					'target-arrow-color': '#ed8936',
					'line-style': 'dashed',
				},
			},
		];

		return baseStyle;
	}

	private getLayoutConfig(): any {
		const layouts = {
			dagre: {
				name: 'dagre',
				rankDir: 'TB',
				align: 'UL',
				rankSep: 50,
				nodeSep: 50,
				edgeSep: 10,
				marginX: 20,
				marginY: 20,
			},
			elk: {
				name: 'elk',
				elk: {
					'algorithm': 'layered',
					'elk.direction': 'DOWN',
					'elk.spacing.nodeNode': '50',
					'elk.layered.spacing.nodeNodeBetweenLayers': '50',
				},
			},
			grid: {
				name: 'grid',
				rows: undefined,
				cols: undefined,
				position: function (node: any) {
					return { row: node.data('row'), col: node.data('col') };
				},
			},
			circle: {
				name: 'circle',
				radius: 100,
				startAngle: -Math.PI / 2,
				sweep: Math.PI * 2,
				clockwise: true,
			},
		};

		return layouts[this.layout] || layouts.dagre;
	}
}
