import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { PodcastModalComponent, PodcastModalData } from './podcast-modal.component';

describe('PodcastModalComponent', () => {
	let component: PodcastModalComponent;
	let fixture: ComponentFixture<PodcastModalComponent>;
	let messageService: jasmine.SpyObj<MessageService>;

	const mockData: PodcastModalData = {
		visible: true,
		generating: false,
		url: 'http://localhost:8040/podcasts/123/audio',
		title: 'Projeto Teste',
		projectId: 'proj123',
	};

	beforeEach(async () => {
		const messageServiceSpy = jasmine.createSpyObj('MessageService', ['add']);

		await TestBed.configureTestingModule({
			imports: [
				PodcastModalComponent,
				HttpClientTestingModule,
				DialogModule,
				ButtonModule,
				BrowserAnimationsModule,
			],
			providers: [{ provide: MessageService, useValue: messageServiceSpy }],
		}).compileComponents();

		messageService = TestBed.inject(MessageService) as jasmine.SpyObj<MessageService>;
		fixture = TestBed.createComponent(PodcastModalComponent);
		component = fixture.componentInstance;
	});

	it('deve criar o componente', () => {
		expect(component).toBeTruthy();
	});

	describe('Abertura e Fechamento', () => {
		it('deve exibir modal quando visible é true', () => {
			component.data = { ...mockData, visible: true };
			fixture.detectChanges();

			const dialog = fixture.debugElement.query(By.css('p-dialog'));
			expect(dialog).toBeTruthy();
			expect(dialog.componentInstance.visible).toBe(true);
		});

		it('deve ocultar modal quando visible é false', () => {
			component.data = { ...mockData, visible: false };
			fixture.detectChanges();

			const dialog = fixture.debugElement.query(By.css('p-dialog'));
			expect(dialog.componentInstance.visible).toBe(false);
		});

		it('deve emitir evento close ao fechar', () => {
			spyOn(component.close, 'emit');
			component.onClose();

			expect(component.close.emit).toHaveBeenCalled();
		});

		it('deve chamar onClose quando dialog emite onHide', () => {
			spyOn(component, 'onClose');
			component.data = { ...mockData, visible: true };
			fixture.detectChanges();

			const dialog = fixture.debugElement.query(By.css('p-dialog'));
			dialog.triggerEventHandler('onHide', null);

			expect(component.onClose).toHaveBeenCalled();
		});
	});

	describe('Estados Visuais', () => {
		it('deve mostrar spinner quando generating é true', () => {
			component.data = { ...mockData, generating: true, url: '' };
			fixture.detectChanges();

			const spinner = fixture.debugElement.query(By.css('.pi-spinner'));
			const loadingText = fixture.nativeElement.querySelector('p.font-semibold');

			expect(spinner).toBeTruthy();
			expect(loadingText?.textContent).toContain('Gerando Podcast...');
		});

		it('deve mostrar player quando url existe e não está gerando', () => {
			component.data = { ...mockData, generating: false };
			fixture.detectChanges();

			const audioElement = fixture.debugElement.query(By.css('audio'));
			expect(audioElement).toBeTruthy();
			expect(audioElement.nativeElement.src).toBe(mockData.url);
		});

		it('deve mostrar estado vazio quando sem url e sem generating', () => {
			component.data = { ...mockData, url: '', generating: false };
			fixture.detectChanges();

			const emptyState = fixture.nativeElement.querySelector('.pi-microphone.text-gray-400');
			const emptyTextElement = fixture.nativeElement.querySelector(
				'.bg-white.rounded-xl .text-center p.text-gray-600.mb-4',
			);

			expect(emptyState).toBeTruthy();
			expect(emptyTextElement?.textContent).toContain('Nenhum podcast disponível');
		});

		it('deve mostrar botão gerar quando projectId existe e sem url', () => {
			component.data = { ...mockData, url: '', projectId: 'proj123' };
			fixture.detectChanges();

			const generateButton = fixture.debugElement.query(By.css('button[label="Gerar Podcast"]'));
			expect(generateButton).toBeTruthy();
		});

		it('não deve mostrar botão gerar quando projectId não existe', () => {
			component.data = { ...mockData, url: '', projectId: undefined };
			fixture.detectChanges();

			const generateButton = fixture.debugElement.query(By.css('button[label="Gerar Podcast"]'));
			expect(generateButton).toBeFalsy();
		});
	});

	describe('Integração com Player', () => {
		it('deve configurar autoplay no audio element', () => {
			component.data = mockData;
			fixture.detectChanges();

			const audioElement: HTMLAudioElement = fixture.nativeElement.querySelector('audio');
			expect(audioElement.autoplay).toBe(true);
		});

		it('deve usar URL fornecida como source', () => {
			component.data = mockData;
			fixture.detectChanges();

			const audioElement: HTMLAudioElement = fixture.nativeElement.querySelector('audio');
			const sourceElement: HTMLSourceElement = audioElement.querySelector('source')!;

			expect(audioElement.src).toContain(mockData.url);
			expect(sourceElement.src).toContain(mockData.url);
			expect(sourceElement.type).toBe('audio/mpeg');
		});

		it('deve ter atributo preload configurado como auto', () => {
			component.data = mockData;
			fixture.detectChanges();

			const audioElement: HTMLAudioElement = fixture.nativeElement.querySelector('audio');
			expect(audioElement.preload).toBe('auto');
		});

		it('deve exibir mensagem de erro para navegadores incompatíveis', () => {
			component.data = mockData;
			fixture.detectChanges();

			const fallbackText = fixture.nativeElement.querySelector('audio p.text-red-600');
			expect(fallbackText?.textContent).toContain('Seu navegador não suporta o elemento de áudio.');
		});
	});

	describe('Eventos', () => {
		it('deve emitir generate com projectId correto', () => {
			spyOn(component.generate, 'emit');
			component.data = { ...mockData, projectId: 'proj456' };

			component.generatePodcast();

			expect(component.generate.emit).toHaveBeenCalledWith('proj456');
		});

		it('não deve emitir generate se projectId não existir', () => {
			spyOn(component.generate, 'emit');
			component.data = { ...mockData, projectId: undefined };

			component.generatePodcast();

			expect(component.generate.emit).not.toHaveBeenCalled();
		});

		it('deve chamar generatePodcast quando botão gerar é clicado', () => {
			spyOn(component, 'generatePodcast');
			component.data = { ...mockData, url: '', projectId: 'proj123' };
			fixture.detectChanges();

			const generateButton = fixture.debugElement.query(By.css('button[label="Gerar Podcast"]'));
			generateButton.nativeElement.click();

			expect(component.generatePodcast).toHaveBeenCalled();
		});
	});

	describe('Header e Título', () => {
		it('deve exibir título do projeto no header', () => {
			component.data = { ...mockData, title: 'Meu Projeto Especial' };
			fixture.detectChanges();

			const titleElement = fixture.nativeElement.querySelector('.text-sm.text-gray-600.m-0.mt-1');
			expect(titleElement?.textContent).toBe('Meu Projeto Especial');
		});

		it('deve sanitizar título para evitar XSS', () => {
			const maliciousTitle = '<script>alert("xss")</script>Projeto';
			component.data = { ...mockData, title: maliciousTitle };
			fixture.detectChanges();

			const titleElement = fixture.nativeElement.querySelector('.text-sm.text-gray-600.m-0.mt-1');
			// Angular sanitiza automaticamente, então o script não deve ser executado
			expect(titleElement?.innerHTML).not.toContain('<script>');
			expect(titleElement?.textContent).toContain('Projeto');
		});

		it('deve exibir ícone de microfone no header', () => {
			component.data = mockData;
			fixture.detectChanges();

			const icon = fixture.nativeElement.querySelector('.pi-microphone.text-purple-600');
			expect(icon).toBeTruthy();
		});
	});

	describe('Responsividade', () => {
		it('deve aplicar classes responsivas ao modal', () => {
			component.data = mockData;
			fixture.detectChanges();

			const dialog = fixture.debugElement.query(By.css('p-dialog'));
			expect(dialog.componentInstance.style).toEqual({
				width: '90vw',
				maxWidth: '600px',
			});
		});

		it('deve aplicar padding responsivo no container', () => {
			component.data = mockData;
			fixture.detectChanges();

			// Verifica se o container tem padding aplicado
			const container = fixture.nativeElement.querySelector('.podcast-player-container');
			expect(container).toBeTruthy();
			// CSS responsivo é aplicado via classes do componente
		});
	});

	describe('Limpeza e Destruição', () => {
		it('deve implementar OnDestroy', () => {
			expect(component.ngOnDestroy).toBeDefined();
		});

		it('deve chamar ngOnDestroy sem erros', () => {
			expect(() => component.ngOnDestroy()).not.toThrow();
		});

		it('deve limpar audioError ao destruir', () => {
			component.audioError = 'Erro teste';
			component.ngOnDestroy();
			expect(component.audioError).toBeNull();
		});
	});

	describe('Botão Fechar', () => {
		it('deve exibir botão fechar com ícone e label corretos', () => {
			component.data = mockData;
			fixture.detectChanges();

			const closeButton = fixture.debugElement.query(By.css('button[label="Fechar"]'));
			expect(closeButton).toBeTruthy();
			expect(closeButton.nativeElement.classList).toContain('p-button-outlined');
			expect(closeButton.nativeElement.classList).toContain('p-button-secondary');
		});

		it('deve chamar onClose quando botão fechar é clicado', () => {
			spyOn(component, 'onClose');
			component.data = mockData;
			fixture.detectChanges();

			const closeButton = fixture.debugElement.query(By.css('button[label="Fechar"]'));
			closeButton.nativeElement.click();

			expect(component.onClose).toHaveBeenCalled();
		});
	});

	describe('Tratamento de Erros', () => {
		it('deve tratar erro de carregamento de áudio', () => {
			const errorEvent = new Event('error');
			spyOn(console, 'error');

			component.onAudioError(errorEvent);

			expect(console.error).toHaveBeenCalledWith('Erro ao carregar áudio:', errorEvent);
			expect(component.audioError).toBe('Erro ao carregar o áudio. Por favor, tente novamente.');
		});

		it('deve limpar erro ao fechar modal', () => {
			component.audioError = 'Erro teste';
			component.onClose();

			expect(component.audioError).toBeNull();
		});
	});
});
