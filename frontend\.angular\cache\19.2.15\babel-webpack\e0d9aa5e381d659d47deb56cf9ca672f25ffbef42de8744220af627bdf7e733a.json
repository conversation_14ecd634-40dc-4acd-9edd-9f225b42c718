{"ast": null, "code": "import { mergeMap } from './mergeMap';\nexport const flatMap = mergeMap;", "map": {"version": 3, "names": ["mergeMap", "flatMap"], "sources": ["C:/projetos/scope-ai/frontend/node_modules/rxjs/dist/esm/internal/operators/flatMap.js"], "sourcesContent": ["import { mergeMap } from './mergeMap';\nexport const flatMap = mergeMap;\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,YAAY;AACrC,OAAO,MAAMC,OAAO,GAAGD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}