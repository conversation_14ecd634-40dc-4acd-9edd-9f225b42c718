"""
Endpoints para gerenciamento de projetos via Boardroom Advisor
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from typing import Dict, Any
from bson import ObjectId
from datetime import datetime, UTC
import logging
from clients.db import clients_collection, projetos_collection
from teams.boardroom_advisor_agents import boardroom_advisor_agents_suggestions
from shared.websocket_manager import websocket_manager

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/boardroom", tags=["Boardroom"])


async def recriar_projetos_task(client_id: ObjectId, client_name: str):
    """
    Task assíncrona para recriar projetos de um cliente
    """
    try:
        logger.info(f"🔄 Iniciando recriação de projetos para {client_name}")
        
        # Notificar início
        await websocket_manager.broadcast({
            "type": "projects_recreation_started",
            "clientId": str(client_id),
            "clientName": client_name,
            "message": f"Recriando projetos para {client_name}...",
            "timestamp": datetime.now(UTC).isoformat()
        })
        
        # Remover projetos antigos
        delete_result = projetos_collection.delete_many({"cliente_id": client_id})
        logger.info(f"🗑️ {delete_result.deleted_count} projetos antigos removidos")
        
        # Buscar dados do cliente para análise
        client = clients_collection.find_one({"_id": client_id})
        if not client:
            raise Exception("Cliente não encontrado")
            
        # Preparar documentos para análise
        documents = []
        
        # Adicionar dossiê expandido se existir
        if client.get("optimized_dossie_data"):
            documents.append({
                "tipo": "dossie_expandido",
                "dados": client["optimized_dossie_data"]
            })
        
        # Adicionar reports se existirem
        for report in client.get("reports", []):
            documents.append({
                "tipo": report.get("report_type", "report"),
                "dados": report
            })
            
        if not documents:
            raise Exception("Nenhum documento encontrado para análise")
            
        # Gerar novos projetos
        logger.info(f"📊 Analisando {len(documents)} documentos com time da diretoria")
        projetos_sugeridos = boardroom_advisor_agents_suggestions(documents)
        
        logger.info(f"💡 {len(projetos_sugeridos)} novos projetos sugeridos")
        
        # Salvar novos projetos
        projetos_salvos = 0
        for projeto in projetos_sugeridos:
            try:
                # Determinar equipe baseada na área
                area = projeto.get("area_especialidade", "")
                equipe_map = {
                    "Setor de Dados": "Data Analytics",
                    "Setor de Produto": "Product",
                    "Setor de Design": "Design",
                    "Setor de Engenharia": "Engineering",
                    "Setor de arquitetura": "Architecture"
                }
                equipe = equipe_map.get(area, "Strategy")
                
                projeto_doc = {
                    "cliente_id": client_id,
                    "nome_projeto": projeto.get("nome_projeto", "Projeto sem nome"),
                    "tags": projeto.get("tags", ["Estratégia"])[:3],
                    "status": "Sugestão",
                    "resumo": projeto.get("resumo", "")[:100],
                    "progresso": 0,
                    "equipe": equipe,
                    "justificativa": projeto.get("justificativa", ""),
                    "importancia": projeto.get("importancia", ""),
                    "pontuacao": projeto.get("media_pontuacao_geral", projeto.get("pontuacao", 70)),
                    "detalhamento": projeto.get("detalhamento", ""),
                    "beneficios": projeto.get("beneficios", ""),
                    "agentes_justificativa": projeto.get("agentes_justificativa", []),
                    "agente_responsavel": projeto.get("agente_responsavel", ""),
                    "area_especialidade": projeto.get("area_especialidade", ""),
                    "created_at": datetime.now(UTC),
                    "updated_at": datetime.now(UTC)
                }
                
                projetos_collection.insert_one(projeto_doc)
                projetos_salvos += 1
                logger.info(f"✅ Projeto '{projeto_doc['nome_projeto']}' salvo")
                
            except Exception as e:
                logger.error(f"Erro ao salvar projeto: {e}")
                continue
                
        # Atualizar contador de projetos no cliente
        clients_collection.update_one(
            {"_id": client_id},
            {
                "$set": {
                    "projetos_count": projetos_salvos,
                    "updated_at": datetime.now(UTC)
                }
            }
        )
        
        # Notificar conclusão
        await websocket_manager.broadcast({
            "type": "projects_recreation_completed",
            "clientId": str(client_id),
            "clientName": client_name,
            "message": f"✅ {projetos_salvos} projetos recriados com sucesso para {client_name}!",
            "projects_count": projetos_salvos,
            "timestamp": datetime.now(UTC).isoformat()
        })
        
        logger.info(f"🎉 Recriação de projetos concluída para {client_name}: {projetos_salvos} projetos")
        
    except Exception as e:
        logger.error(f"Erro ao recriar projetos para {client_name}: {e}")
        
        # Notificar erro
        await websocket_manager.broadcast({
            "type": "projects_recreation_error",
            "clientId": str(client_id),
            "clientName": client_name,
            "message": f"❌ Erro ao recriar projetos: {str(e)}",
            "timestamp": datetime.now(UTC).isoformat()
        })


@router.post("/recreate-projects/{client_id}")
async def recreate_projects(client_id: str, background_tasks: BackgroundTasks):
    """
    Recria todos os projetos de um cliente
    
    Este endpoint:
    1. Remove todos os projetos existentes do cliente
    2. Reanalisa os dados do cliente com os agentes do boardroom
    3. Gera novos projetos baseados na análise atualizada
    """
    try:
        # Validar ObjectId
        try:
            client_obj_id = ObjectId(client_id)
        except:
            raise HTTPException(
                status_code=400,
                detail="ID de cliente inválido"
            )
            
        # Verificar se cliente existe
        client = clients_collection.find_one({"_id": client_obj_id})
        if not client:
            raise HTTPException(
                status_code=404,
                detail="Cliente não encontrado"
            )
            
        client_name = client.get("name", "Cliente")
        
        # Verificar se cliente tem dados suficientes
        has_data = (
            client.get("optimized_dossie_data") or 
            client.get("reports") or 
            client.get("funding_data") or
            client.get("presenca_digital_data")
        )
        
        if not has_data:
            raise HTTPException(
                status_code=400,
                detail="Cliente não possui dados suficientes para gerar projetos. Execute primeiro a análise completa."
            )
            
        # Adicionar task em background
        background_tasks.add_task(
            recriar_projetos_task,
            client_obj_id,
            client_name
        )
        
        return {
            "success": True,
            "message": f"Recriação de projetos iniciada para {client_name}",
            "client_id": client_id,
            "status": "processing"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao iniciar recriação de projetos: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Erro interno ao recriar projetos: {str(e)}"
        )


@router.get("/projects/{client_id}")
async def get_client_projects(client_id: str):
    """
    Retorna todos os projetos de um cliente
    """
    try:
        # Validar ObjectId
        try:
            client_obj_id = ObjectId(client_id)
        except:
            raise HTTPException(
                status_code=400,
                detail="ID de cliente inválido"
            )
            
        # Buscar projetos
        projects = list(projetos_collection.find({"cliente_id": client_obj_id}))
        
        # Converter ObjectId para string
        for project in projects:
            project["_id"] = str(project["_id"])
            project["cliente_id"] = str(project["cliente_id"])
            
        return {
            "success": True,
            "client_id": client_id,
            "projects": projects,
            "total": len(projects)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao buscar projetos: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao buscar projetos: {str(e)}"
        )