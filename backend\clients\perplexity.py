from config.settings import env
import requests
from .db import clients_collection
import json
import os
import sys
import asyncio
import re
import subprocess
from typing import Dict, Any, Optional
from datetime import datetime
import logging
from bson import ObjectId
from shared.websocket_manager import WebSocketManager
from clients.parsers import (
    parse_dados_funding, parse_dados_presenca_digital,
    parse_dados_canais_reviews, parse_dados_pesquisa_mercado
)

# Configurar o logger
logger = logging.getLogger(__name__)


def gerar_dossie_perplexity(client_id, nome, site=None, cidade=None, estado=None):
    """
    Gera dossiê expandido da empresa incluindo Análise SWOT detalhada e Stack Técnico
    HÍBRIDO: Função síncrona que executa parsers assíncronos internamente
    """
    api_key = env.PERPLEXITY_API_KEY
    empresa = nome or 'Dado não encontrado'
    cidade_str = cidade if cidade else 'Dado não encontrado'
    estado_str = estado if estado else 'Dado não encontrado'
    site_str = site if site else 'Dado não encontrado'

    # Gerar dossiê básico primeiro
    dossie_basico = _gerar_dossie_basico(
        api_key, empresa, cidade_str, estado_str, site_str)

    # Gerar análise SWOT expandida
    analise_swot_expandida = _gerar_analise_swot_expandida(
        api_key, empresa, dossie_basico)

    # Gerar análise de stack técnico
    stack_tecnico = _gerar_stack_tecnico(api_key, empresa, site_str)

    # Gerar análise detalhada do histórico de funding
    dados_funding_brutos = _gerar_dados_funding(api_key, empresa, site_str)

    # Processar e validar dados de funding usando parser ASYNC
    dados_funding = asyncio.run(parse_dados_funding(dados_funding_brutos))

    # Gerar análise detalhada da presença digital
    dados_presenca_digital_brutos = _gerar_dados_presenca_digital(
        api_key, empresa, site_str)

    # Processar e validar dados de presença digital usando parser ASYNC
    dados_presenca_digital = asyncio.run(parse_dados_presenca_digital(
        dados_presenca_digital_brutos))

    # Gerar análise de parcerias comerciais e tecnológicas
    dados_parcerias_brutos = _gerar_dados_parcerias(api_key, empresa, site_str)

    # Gerar análise de modelo de negócio
    dados_modelo_negocio_brutos = _gerar_dados_modelo_negocio(
        api_key, empresa, site_str)

    # Gerar análise de pricing
    dados_pricing_brutos = _gerar_dados_pricing(api_key, empresa, site_str)

    # Gerar análise de canais de distribuição, reviews e certificações
    dados_canais_reviews_brutos = _gerar_dados_canais_reviews(
        api_key, empresa, site_str)

    # Processar e validar dados de canais/reviews usando parser ASYNC
    dados_canais_reviews = asyncio.run(parse_dados_canais_reviews(
        dados_canais_reviews_brutos))

    # Gerar pesquisa de mercado e análise de produtos/serviços
    dados_pesquisa_mercado_brutos = _gerar_dados_pesquisa_mercado(
        api_key, empresa, site_str)

    # Processar e validar dados de pesquisa de mercado usando parser ASYNC
    dados_pesquisa_mercado = asyncio.run(parse_dados_pesquisa_mercado(
        dados_pesquisa_mercado_brutos))

    # Gerar diagnóstico técnico completo (Lighthouse + Screenshots + Análise Visual IA)
    logger.info(
        f"Gerando diagnóstico técnico para: {empresa} . Tempo estimado: 10 minutos")

    # Verificar se api_key está disponível
    if api_key:
        diagnostico_tecnico = _gerar_diagnostico_tecnico(
            api_key=api_key,
            empresa=empresa,
            site_str=site_str,
            dados_setor=classificar_setor_simples(
                nome=empresa, site=site_str, cidade=cidade_str)
        )
    else:
        logger.warning("API key não disponível - pulando diagnóstico técnico")
        diagnostico_tecnico = {
            "erro": "API key não disponível para diagnóstico técnico",
            "status": "skipped"
        }

    # Consolidar todos os dados
    dossie_completo = {
        **dossie_basico,
        "analise_swot_expandida": analise_swot_expandida,
        "stack_tecnico": stack_tecnico,
        "dados_funding": dados_funding,
        "dados_presenca_digital": dados_presenca_digital,
        "dados_parcerias": dados_parcerias_brutos,
        "dados_modelo_negocio": dados_modelo_negocio_brutos,
        "dados_pricing": dados_pricing_brutos,
        "dados_canais_reviews": dados_canais_reviews,
        "dados_pesquisa_mercado": dados_pesquisa_mercado,
        # NOVO: Diagnósticos técnicos completos
        "diagnostico_tecnico": diagnostico_tecnico,
        "report_type": "dossie_expandido",
        "version": "2.3"  # Incrementar versão para incluir diagnósticos técnicos
    }

    # Salvar no MongoDB
    clients_collection.update_one(
        {"_id": client_id},
        {"$push": {"reports": dossie_completo}}
    )

    # Acionar classificação de setor via LLM
    try:
        setor_llm = classificar_setor_por_llm(dossie_completo)
        if setor_llm:  # Só atualiza se conseguiu classificar
            clients_collection.update_one(
                {"_id": client_id}, {"$set": {"sector": setor_llm}})
            # Notificar frontend via WebSocket
            _notificar_websocket(client_id, setor_llm)
        else:
            # Se classificação LLM falhou, preserva o setor existente
            client_atual = clients_collection.find_one({"_id": client_id})
            if client_atual:
                setor_preservado = client_atual.get(
                    "sector") or client_atual.get("setor", "Tecnologia")
                # Garantir que ambos os campos estejam sincronizados
                clients_collection.update_one(
                    {"_id": client_id},
                    {"$set": {"sector": setor_preservado, "setor": setor_preservado}}
                )
                print(
                    f"Classificação LLM falhou, preservando setor: {setor_preservado}", file=sys.stderr)
                # Notificar com o setor preservado
                _notificar_websocket(client_id, setor_preservado)
            else:
                print(
                    f"Cliente {client_id} não encontrado para preservar setor", file=sys.stderr)

    except Exception as e:
        print(f"Erro ao classificar setor: {e}", file=sys.stderr)
        # Em caso de erro, preservar setor existente
        try:
            client_atual = clients_collection.find_one({"_id": client_id})
            if client_atual:
                setor_preservado = client_atual.get(
                    "sector") or client_atual.get("setor", "Tecnologia")
                # Garantir que ambos os campos estejam sincronizados
                clients_collection.update_one(
                    {"_id": client_id},
                    {"$set": {"sector": setor_preservado, "setor": setor_preservado}}
                )
                print(
                    f"Preservando setor após erro: {setor_preservado}", file=sys.stderr)
                _notificar_websocket(client_id, setor_preservado)
            else:
                print(
                    f"Cliente {client_id} não encontrado para preservar setor após erro", file=sys.stderr)
        except Exception as e2:
            print(f"Erro ao preservar setor: {e2}", file=sys.stderr)


def _gerar_dossie_basico(api_key, empresa, cidade_str, estado_str, site_str):
    """Gera o dossiê básico original"""
    system_content = (
        "Você é um analista sênior de inteligência de mercado especializado em análise competitiva. "
        "Sua missão é gerar dossiês completos e detalhados sobre empresas, sempre respondendo APENAS em JSON válido, sem comentários ou texto fora do objeto JSON. "
        "OBRIGATÓRIO: Inclua sempre uma seção detalhada de concorrentes principais com nomes reais e específicos. "
        "Seja descritivo, profissional e use dados atualizados, exemplos concretos, nomes, números e fatos verificáveis. "
        "Se algum dado não for encontrado, use 'Dado não encontrado'. "
        "O texto deve estar em português do Brasil, tom profissional, pronto para uso em relatório corporativo."
    )

    user_content = (
        f"Elabore um dossiê completo e analítico sobre a empresa {empresa}, sediada em {cidade_str}, {estado_str}, Brasil, site {site_str}. "
        "IMPORTANTE: Inclua OBRIGATORIAMENTE uma análise detalhada dos principais concorrentes diretos e indiretos, com nomes específicos de empresas. "
        "O dossiê deve seguir EXATAMENTE esta estrutura JSON:\n"
        "{\n"
        "  \"informacoes_gerais\": {\n"
        "    \"nome\": \"Nome da empresa\",\n"
        "    \"descricao\": \"Descrição detalhada\",\n"
        "    \"executivos\": [\"Nome 1 - Cargo\", \"Nome 2 - Cargo\"]\n"
        "  },\n"
        "  \"historico\": \"Histórico detalhado da empresa\",\n"
        "  \"produtos_servicos\": [\"Produto/Serviço 1\", \"Produto/Serviço 2\"],\n"
        "  \"concorrentes\": {\n"
        "    \"principais\": [\"Nome Concorrente 1\", \"Nome Concorrente 2\", \"Nome Concorrente 3\"],\n"
        "    \"diferenciais_concorrentes\": \"O que os concorrentes fazem melhor\"\n"
        "  },\n"
        "  \"cases_sucesso\": {\n"
        "    \"case_1\": \"Descrição detalhada do case 1\",\n"
        "    \"case_2\": \"Descrição detalhada do case 2\"\n"
        "  },\n"
        "  \"saude_financeira\": {\n"
        "    \"faturamento_historico\": \"Dados financeiros históricos\",\n"
        "    \"projecao_2023\": \"Projeção para 2023\",\n"
        "    \"crescimento\": \"Análise de crescimento\"\n"
        "  },\n"
        "  \"analise_swot\": {\n"
        "    \"forcas\": [\"Força 1\", \"Força 2\"],\n"
        "    \"fraquezas\": [\"Fraqueza 1\", \"Fraqueza 2\"],\n"
        "    \"oportunidades\": [\"Oportunidade 1\", \"Oportunidade 2\"],\n"
        "    \"ameacas\": [\"Ameaça 1\", \"Ameaça 2\"]\n"
        "  },\n"
        "  \"oportunidades_novos_produtos\": [\"Produto 1\", \"Produto 2\"],\n"
        "  \"melhorias_futuras\": [\"Melhoria 1\", \"Melhoria 2\"]\n"
        "}\n\n"
        "CRÍTICO: A seção 'concorrentes' deve conter nomes reais e específicos de empresas que competem no mesmo mercado. "
        "Evite respostas genéricas. Pesquise e identifique competidores diretos e indiretos da empresa. "
        "Seja detalhado em todas as seções, especialmente em concorrentes, cases de sucesso e análise financeira."
    )

    return _fazer_requisicao_perplexity(api_key, system_content, user_content)


def _gerar_analise_swot_expandida(api_key, empresa, dossie_basico):
    """Gera análise SWOT expandida e detalhada"""
    system_content = (
        "Você é um consultor estratégico sênior especializado em análise SWOT empresarial. "
        "Sua missão é criar análises SWOT detalhadas e acionáveis, sempre respondendo APENAS em JSON válido. "
        "Seja específico, use dados concretos e forneça insights estratégicos profundos. "
        "O texto deve estar em português do Brasil, tom consultivo e profissional."
    )

    contexto_empresa = json.dumps(dossie_basico, ensure_ascii=False, indent=2)

    user_content = (
        f"Com base no seguinte contexto sobre a empresa {empresa}, elabore uma análise SWOT expandida e estratégica. "
        f"CONTEXTO DA EMPRESA:\n{contexto_empresa}\n\n"
        "Estruture a resposta EXATAMENTE neste formato JSON:\n"
        "{\n"
        "  \"forcas_detalhadas\": {\n"
        "    \"recursos_financeiros\": \"Análise detalhada dos recursos financeiros\",\n"
        "    \"capacidades_tecnicas\": \"Análise das capacidades técnicas\",\n"
        "    \"posicionamento_mercado\": \"Análise do posicionamento no mercado\",\n"
        "    \"recursos_humanos\": \"Análise dos recursos humanos\",\n"
        "    \"inovacao_tecnologia\": \"Análise de inovação e tecnologia\"\n"
        "  },\n"
        "  \"fraquezas_detalhadas\": {\n"
        "    \"limitacoes_financeiras\": \"Análise das limitações financeiras\",\n"
        "    \"gaps_tecnologicos\": \"Análise dos gaps tecnológicos\",\n"
        "    \"fraquezas_operacionais\": \"Análise das fraquezas operacionais\",\n"
        "    \"limitacoes_mercado\": \"Análise das limitações de mercado\",\n"
        "    \"dependencias_criticas\": \"Análise das dependências críticas\"\n"
        "  },\n"
        "  \"oportunidades_detalhadas\": {\n"
        "    \"tendencias_mercado\": \"Análise das tendências de mercado\",\n"
        "    \"expansao_geografica\": \"Análise de oportunidades de expansão\",\n"
        "    \"novos_segmentos\": \"Análise de novos segmentos\",\n"
        "    \"parcerias_estrategicas\": \"Análise de parcerias estratégicas\",\n"
        "    \"inovacoes_emergentes\": \"Análise de inovações emergentes\"\n"
        "  },\n"
        "  \"ameacas_detalhadas\": {\n"
        "    \"concorrencia_direta\": \"Análise da concorrência direta\",\n"
        "    \"mudancas_regulatorias\": \"Análise de mudanças regulatórias\",\n"
        "    \"disrupcoes_tecnologicas\": \"Análise de disrupções tecnológicas\",\n"
        "    \"riscos_economicos\": \"Análise dos riscos econômicos\",\n"
        "    \"mudancas_comportamento_consumidor\": \"Análise de mudanças no comportamento do consumidor\"\n"
        "  },\n"
        "  \"estrategias_recomendadas\": {\n"
        "    \"forcas_oportunidades\": [\"Estratégia 1\", \"Estratégia 2\"],\n"
        "    \"forcas_ameacas\": [\"Estratégia 1\", \"Estratégia 2\"],\n"
        "    \"fraquezas_oportunidades\": [\"Estratégia 1\", \"Estratégia 2\"],\n"
        "    \"fraquezas_ameacas\": [\"Estratégia 1\", \"Estratégia 2\"]\n"
        "  },\n"
        "  \"prioridades_acao\": {\n"
        "    \"curto_prazo_3_meses\": [\"Ação 1\", \"Ação 2\"],\n"
        "    \"medio_prazo_6_12_meses\": [\"Ação 1\", \"Ação 2\"],\n"
        "    \"longo_prazo_1_2_anos\": [\"Ação 1\", \"Ação 2\"]\n"
        "  }\n"
        "}\n\n"
        "IMPORTANTE: Seja específico e acionável em todas as análises. "
        "Use dados concretos quando disponíveis e forneça insights estratégicos profundos."
    )

    return _fazer_requisicao_perplexity(api_key, system_content, user_content)


def _gerar_stack_tecnico(api_key, empresa, site_str):
    """Gera análise detalhada do stack tecnológico da empresa"""
    system_content = (
        "Você é um arquiteto de software sênior e especialista em análise de stack tecnológico. "
        "Sua missão é identificar e analisar as tecnologias utilizadas por empresas, sempre respondendo APENAS em JSON válido. "
        "Seja técnico, preciso e use dados verificáveis quando disponíveis. "
        "O texto deve estar em português do Brasil, tom técnico e profissional."
    )

    user_content = (
        f"Analise o stack tecnológico da empresa {empresa} (site: {site_str}). "
        "Identifique todas as tecnologias utilizadas pela empresa e estruture a resposta EXATAMENTE neste formato JSON:\n"
        "{\n"
        "  \"frontend\": {\n"
        "    \"linguagens\": [\"JavaScript\", \"TypeScript\"],\n"
        "    \"frameworks\": [\"React\", \"Angular\", \"Vue.js\"],\n"
        "    \"bibliotecas\": [\"jQuery\", \"Bootstrap\"],\n"
        "    \"build_tools\": [\"Webpack\", \"Vite\"]\n"
        "  },\n"
        "  \"backend\": {\n"
        "    \"linguagens\": [\"Python\", \"Node.js\", \"Java\"],\n"
        "    \"frameworks\": [\"Django\", \"FastAPI\", \"Express\"],\n"
        "    \"apis\": [\"REST\", \"GraphQL\"]\n"
        "  },\n"
        "  \"banco_dados\": {\n"
        "    \"relacionais\": [\"PostgreSQL\", \"MySQL\"],\n"
        "    \"nosql\": [\"MongoDB\", \"Redis\"],\n"
        "    \"cache\": [\"Redis\", \"Memcached\"]\n"
        "  },\n"
        "  \"infraestrutura\": {\n"
        "    \"cloud_providers\": [\"AWS\", \"Google Cloud\", \"Azure\"],\n"
        "    \"containerizacao\": [\"Docker\", \"Kubernetes\"],\n"
        "    \"ci_cd\": [\"GitHub Actions\", \"Jenkins\"]\n"
        "  },\n"
        "  \"monitoramento\": {\n"
        "    \"apm\": [\"New Relic\", \"Datadog\"],\n"
        "    \"logs\": [\"ELK Stack\", \"Splunk\"],\n"
        "    \"metricas\": [\"Prometheus\", \"Grafana\"]\n"
        "  },\n"
        "  \"seguranca\": {\n"
        "    \"ssl_tls\": \"Análise dos certificados SSL/TLS\",\n"
        "    \"headers_seguranca\": [\"HSTS\", \"CSP\"],\n"
        "    \"ferramentas\": [\"WAF\", \"Cloudflare\"]\n"
        "  },\n"
        "  \"analytics_marketing\": {\n"
        "    \"analytics\": [\"Google Analytics\", \"Adobe Analytics\"],\n"
        "    \"tag_managers\": [\"Google Tag Manager\"],\n"
        "    \"marketing\": [\"Facebook Pixel\", \"Google Ads\"]\n"
        "  },\n"
        "  \"terceiros_integracoes\": {\n"
        "    \"pagamentos\": [\"Stripe\", \"PayPal\"],\n"
        "    \"comunicacao\": [\"Twilio\", \"SendGrid\"],\n"
        "    \"crm\": [\"Salesforce\", \"HubSpot\"]\n"
        "  },\n"
        "  \"analise_tecnica\": {\n"
        "    \"nivel_modernidade\": \"Alto/Médio/Baixo - Justificativa\",\n"
        "    \"escalabilidade\": \"Análise da capacidade de escalar\",\n"
        "    \"performance\": \"Análise de performance técnica\",\n"
        "    \"manutencao\": \"Análise da facilidade de manutenção\",\n"
        "    \"gaps_identificados\": [\"Gap 1\", \"Gap 2\"],\n"
        "    \"recomendacoes\": [\"Recomendação 1\", \"Recomendação 2\"]\n"
        "  },\n"
        "  \"metodologia_deteccao\": {\n"
        "    \"fontes_utilizadas\": [\"Análise de headers HTTP\", \"Wappalyzer\", \"BuiltWith\"],\n"
        "    \"confiabilidade\": \"Alta/Média/Baixa\",\n"
        "    \"observacoes\": \"Observações sobre a detecção\"\n"
        "  }\n"
        "}\n\n"
        "INSTRUÇÕES ESPECÍFICAS:\n"
        "1. Se o site estiver acessível, analise-o tecnicamente\n"
        "2. Use ferramentas como Wappalyzer, BuiltWith ou análise de headers\n"
        "3. Se não conseguir detectar algo, use 'Não detectado'\n"
        "4. Seja específico nas versões quando possível\n"
        "5. Inclua análise crítica do stack na seção 'analise_tecnica'\n"
        "6. Documente a metodologia utilizada para detecção"
    )

    return _fazer_requisicao_perplexity(api_key, system_content, user_content)


def _gerar_dados_funding(api_key, empresa, site_str=None):
    """Gera análise detalhada do histórico de funding da empresa"""
    system_content = (
        "Você é um analista financeiro sênior especializado em análise de investimentos e funding corporativo. "
        "Sua missão é identificar e analisar o histórico de investimentos de empresas, sempre respondendo APENAS em JSON válido. "
        "Seja preciso, use dados verificáveis e inclua datas, valores e nomes específicos quando disponíveis. "
        "O texto deve estar em português do Brasil, tom profissional e analítico."
    )

    site_info = f" (site: {site_str})" if site_str and site_str != "Dado não encontrado" else ""

    user_content = (
        f"Analise o histórico de funding e investimentos da empresa {empresa}{site_info}. "
        "Pesquise todas as rodadas de investimento, valores captados, investidores e marcos financeiros. "
        "Estruture a resposta EXATAMENTE neste formato JSON:\n"
        "{\n"
        "  \"resumo_funding\": {\n"
        "    \"total_captado\": \"Valor total captado em todas as rodadas (ex: $50M)\",\n"
        "    \"numero_rodadas\": \"Número total de rodadas de investimento\",\n"
        "    \"ultimo_valuation\": \"Último valuation conhecido da empresa\",\n"
        "    \"status_funding\": \"Status atual (ex: Série A, IPO, Privada)\"\n"
        "  },\n"
        "  \"rodadas_investimento\": [\n"
        "    {\n"
        "      \"rodada\": \"Nome da rodada (ex: Seed, Série A, Série B)\",\n"
        "      \"data\": \"Data da rodada (MM/AAAA)\",\n"
        "      \"valor\": \"Valor captado na rodada\",\n"
        "      \"valuation_pre\": \"Valuation pré-investimento\",\n"
        "      \"valuation_pos\": \"Valuation pós-investimento\",\n"
        "      \"lead_investor\": \"Investidor principal da rodada\",\n"
        "      \"outros_investidores\": [\"Investidor 1\", \"Investidor 2\"],\n"
        "      \"uso_capital\": \"Destino declarado do capital\"\n"
        "    }\n"
        "  ],\n"
        "  \"investidores_principais\": {\n"
        "    \"venture_capital\": [\"VC 1\", \"VC 2\"],\n"
        "    \"private_equity\": [\"PE 1\", \"PE 2\"],\n"
        "    \"anjos_investidores\": [\"Anjo 1\", \"Anjo 2\"],\n"
        "    \"investidores_corporativos\": [\"Corp 1\", \"Corp 2\"],\n"
        "    \"fundos_governo\": [\"Fundo Gov 1\", \"Fundo Gov 2\"]\n"
        "  },\n"
        "  \"metricas_investimento\": {\n"
        "    \"ticket_medio_rodada\": \"Valor médio por rodada\",\n"
        "    \"tempo_medio_entre_rodadas\": \"Tempo médio entre rodadas (meses)\",\n"
        "    \"crescimento_valuation\": \"Taxa de crescimento do valuation\",\n"
        "    \"burn_rate_estimado\": \"Taxa de queima estimada (se disponível)\",\n"
        "    \"runway_estimado\": \"Runway estimado com base no último funding\"\n"
        "  },\n"
        "  \"analise_competitiva_funding\": {\n"
        "    \"comparacao_setor\": \"Como o funding se compara ao setor\",\n"
        "    \"benchmark_concorrentes\": \"Comparação com concorrentes\",\n"
        "    \"posicionamento_mercado\": \"Posicionamento no mercado de investimentos\"\n"
        "  },\n"
        "  \"perspectivas_futuras\": {\n"
        "    \"proxima_rodada_estimada\": \"Estimativa da próxima rodada\",\n"
        "    \"necessidades_capital\": \"Necessidades de capital identificadas\",\n"
        "    \"potencial_ipo_aquisicao\": \"Potencial para IPO ou aquisição\",\n"
        "    \"riscos_funding\": [\"Risco 1\", \"Risco 2\"]\n"
        "  },\n"
        "  \"fontes_informacao\": {\n"
        "    \"fontes_utilizadas\": [\"Crunchbase\", \"PitchBook\", \"Comunicados oficiais\"],\n"
        "    \"confiabilidade_dados\": \"Alta/Média/Baixa\",\n"
        "    \"data_ultima_atualizacao\": \"Data da última informação disponível\",\n"
        "    \"observacoes\": \"Observações sobre a qualidade dos dados\"\n"
        "  }\n"
        "}\n\n"
        "INSTRUÇÕES ESPECÍFICAS:\n"
        "1. Pesquise em fontes confiáveis como Crunchbase, PitchBook, comunicados oficiais\n"
        "2. Se não encontrar dados específicos, use 'Não disponível'\n"
        "3. Inclua datas no formato MM/AAAA quando disponível\n"
        "4. Valores monetários em USD quando possível, especificando a moeda\n"
        "5. Seja específico com nomes de investidores e fundos\n"
        "6. Inclua análise crítica do histórico de funding\n"
        "7. Documente a confiabilidade das fontes utilizadas\n"
        "8. Para empresas brasileiras, inclua também investidores locais"
    )

    return _fazer_requisicao_perplexity(api_key, system_content, user_content)


def _gerar_dados_presenca_digital(api_key, empresa, site_str=None):
    """Gera análise detalhada da presença digital da empresa"""
    system_content = (
        "Você é um especialista sênior em marketing digital e SEO com expertise em análise de presença digital corporativa. "
        "Sua missão é identificar e analisar a presença digital de empresas, sempre respondendo APENAS em JSON válido. "
        "Seja preciso, use dados verificáveis e métricas reais quando disponíveis. "
        "O texto deve estar em português do Brasil, tom profissional e analítico."
    )

    site_info = f" (site: {site_str})" if site_str and site_str != "Dado não encontrado" else ""

    user_content = (
        f"Analise a presença digital da empresa {empresa}{site_info}. "
        "Pesquise métricas de SEO, redes sociais, domain authority e tráfego digital. "
        "Estruture a resposta EXATAMENTE neste formato JSON:\n"
        "{\n"
        "  \"resumo_presenca_digital\": {\n"
        "    \"domain_authority\": \"Score DA estimado (0-100)\",\n"
        "    \"ranking_seo_geral\": \"Posicionamento SEO geral\",\n"
        "    \"trafego_mensal_estimado\": \"Tráfego orgânico mensal estimado\",\n"
        "    \"score_presenca_digital\": \"Score geral da presença digital (0-100)\"\n"
        "  },\n"
        "  \"metricas_seo\": {\n"
        "    \"domain_authority\": \"Score DA (Moz/Ahrefs)\",\n"
        "    \"page_authority\": \"Score PA médio das páginas principais\",\n"
        "    \"backlinks_totais\": \"Número total de backlinks\",\n"
        "    \"dominios_referencia\": \"Número de domínios de referência\",\n"
        "    \"keywords_organicas\": \"Número de keywords rankeando\",\n"
        "    \"keywords_top_10\": \"Keywords posicionadas no top 10\",\n"
        "    \"trafego_organico_mensal\": \"Tráfego orgânico estimado/mês\",\n"
        "    \"valor_trafego_organico\": \"Valor estimado do tráfego orgânico/mês\"\n"
        "  },\n"
        "  \"principais_keywords\": [\n"
        "    {\n"
        "      \"keyword\": \"Palavra-chave principal\",\n"
        "      \"posicao\": \"Posição no Google\",\n"
        "      \"volume_busca\": \"Volume de busca mensal\",\n"
        "      \"dificuldade\": \"Dificuldade da keyword (0-100)\"\n"
        "    }\n"
        "  ],\n"
        "  \"redes_sociais\": {\n"
        "    \"linkedin\": {\n"
        "      \"seguidores\": \"Número de seguidores\",\n"
        "      \"engajamento_medio\": \"Taxa de engajamento média\",\n"
        "      \"frequencia_posts\": \"Frequência de postagem\",\n"
        "      \"qualidade_conteudo\": \"Avaliação da qualidade do conteúdo\"\n"
        "    },\n"
        "    \"instagram\": {\n"
        "      \"seguidores\": \"Número de seguidores\",\n"
        "      \"engajamento_medio\": \"Taxa de engajamento média\",\n"
        "      \"frequencia_posts\": \"Frequência de postagem\",\n"
        "      \"qualidade_conteudo\": \"Avaliação da qualidade do conteúdo\"\n"
        "    },\n"
        "    \"twitter\": {\n"
        "      \"seguidores\": \"Número de seguidores\",\n"
        "      \"engajamento_medio\": \"Taxa de engajamento média\",\n"
        "      \"frequencia_posts\": \"Frequência de postagem\",\n"
        "      \"qualidade_conteudo\": \"Avaliação da qualidade do conteúdo\"\n"
        "    },\n"
        "    \"facebook\": {\n"
        "      \"seguidores\": \"Número de seguidores/curtidas\",\n"
        "      \"engajamento_medio\": \"Taxa de engajamento média\",\n"
        "      \"frequencia_posts\": \"Frequência de postagem\",\n"
        "      \"qualidade_conteudo\": \"Avaliação da qualidade do conteúdo\"\n"
        "    },\n"
        "    \"youtube\": {\n"
        "      \"inscritos\": \"Número de inscritos\",\n"
        "      \"visualizacoes_totais\": \"Total de visualizações\",\n"
        "      \"frequencia_videos\": \"Frequência de upload\",\n"
        "      \"qualidade_conteudo\": \"Avaliação da qualidade do conteúdo\"\n"
        "    }\n"
        "  },\n"
        "  \"analise_site\": {\n"
        "    \"velocidade_carregamento\": \"Velocidade de carregamento (PageSpeed)\",\n"
        "    \"mobile_friendly\": \"Compatibilidade mobile (Sim/Não)\",\n"
        "    \"certificado_ssl\": \"Certificado SSL ativo (Sim/Não)\",\n"
        "    \"estrutura_urls\": \"Qualidade da estrutura de URLs\",\n"
        "    \"meta_tags\": \"Qualidade das meta tags\",\n"
        "    \"schema_markup\": \"Implementação de schema markup\",\n"
        "    \"analytics_implementado\": \"Google Analytics implementado\"\n"
        "  },\n"
        "  \"conteudo_digital\": {\n"
        "    \"blog_ativo\": \"Blog ativo (Sim/Não)\",\n"
        "    \"frequencia_blog\": \"Frequência de postagem no blog\",\n"
        "    \"qualidade_conteudo_blog\": \"Avaliação da qualidade do conteúdo\",\n"
        "    \"materiais_educativos\": \"Materiais educativos disponíveis\",\n"
        "    \"webinars_eventos\": \"Webinars e eventos online\",\n"
        "    \"podcasts\": \"Presença em podcasts\"\n"
        "  },\n"
        "  \"reputacao_online\": {\n"
        "    \"google_reviews\": {\n"
        "      \"nota_media\": \"Nota média no Google\",\n"
        "      \"numero_avaliacoes\": \"Número de avaliações\",\n"
        "      \"tendencia_avaliacoes\": \"Tendência das avaliações\"\n"
        "    },\n"
        "    \"mencoes_imprensa\": \"Menções na imprensa digital\",\n"
        "    \"premios_reconhecimentos\": \"Prêmios e reconhecimentos digitais\",\n"
        "    \"sentiment_geral\": \"Sentiment geral online (Positivo/Neutro/Negativo)\"\n"
        "  },\n"
        "  \"competitividade_digital\": {\n"
        "    \"ranking_vs_concorrentes\": \"Posicionamento vs principais concorrentes\",\n"
        "    \"gaps_identificados\": [\"Gap 1\", \"Gap 2\"],\n"
        "    \"oportunidades_melhoria\": [\"Oportunidade 1\", \"Oportunidade 2\"],\n"
        "    \"benchmark_setor\": \"Como se compara ao setor\"\n"
        "  },\n"
        "  \"recomendacoes_estrategicas\": {\n"
        "    \"prioridade_alta\": [\"Recomendação 1\", \"Recomendação 2\"],\n"
        "    \"prioridade_media\": [\"Recomendação 1\", \"Recomendação 2\"],\n"
        "    \"prioridade_baixa\": [\"Recomendação 1\", \"Recomendação 2\"],\n"
        "    \"investimento_estimado\": \"Investimento estimado para melhorias\",\n"
        "    \"roi_esperado\": \"ROI esperado das melhorias\"\n"
        "  },\n"
        "  \"fontes_analise\": {\n"
        "    \"ferramentas_utilizadas\": [\"SEMrush\", \"Ahrefs\", \"Moz\", \"SimilarWeb\"],\n"
        "    \"confiabilidade_dados\": \"Alta/Média/Baixa\",\n"
        "    \"data_analise\": \"Data da análise\",\n"
        "    \"limitacoes\": \"Limitações da análise\",\n"
        "    \"observacoes\": \"Observações importantes\"\n"
        "  }\n"
        "}\n\n"
        "INSTRUÇÕES ESPECÍFICAS:\n"
        "1. Use ferramentas como SEMrush, Ahrefs, Moz, SimilarWeb para dados\n"
        "2. Se não encontrar dados específicos, use 'Não disponível'\n"
        "3. Inclua métricas numéricas sempre que possível\n"
        "4. Avalie a qualidade do conteúdo de forma objetiva\n"
        "5. Compare com padrões do setor quando relevante\n"
        "6. Identifique oportunidades concretas de melhoria\n"
        "7. Documente a metodologia e fontes utilizadas\n"
        "8. Para empresas brasileiras, considere o contexto local\n"
        "9. Inclua análise de presença em plataformas relevantes ao setor"
    )

    return _fazer_requisicao_perplexity(api_key, system_content, user_content)


def _gerar_dados_parcerias(api_key, empresa, site_str=None):
    """Gera análise detalhada de parcerias comerciais e tecnológicas da empresa"""
    system_content = (
        "Você é um especialista sênior em desenvolvimento de negócios e parcerias estratégicas corporativas. "
        "Sua missão é identificar e analisar o ecossistema de parcerias de empresas, sempre respondendo APENAS em JSON válido. "
        "Seja preciso, use dados verificáveis e inclua nomes específicos de parceiros quando disponíveis. "
        "O texto deve estar em português do Brasil, tom profissional e estratégico."
    )

    site_info = f" (site: {site_str})" if site_str and site_str != "Dado não encontrado" else ""

    user_content = (
        f"Analise o ecossistema de parcerias da empresa {empresa}{site_info}. "
        "Pesquise parcerias comerciais, tecnológicas, integrações e programas de parceiros. "
        "Estruture a resposta EXATAMENTE neste formato JSON:\n"
        "{\n"
        "  \"resumo_parcerias\": {\n"
        "    \"total_parceiros_identificados\": \"Número total de parceiros identificados\",\n"
        "    \"nivel_maturidade_parcerias\": \"Iniciante/Intermediário/Avançado\",\n"
        "    \"estrategia_parcerias\": \"Estratégia principal de parcerias\",\n"
        "    \"foco_geografico\": \"Foco geográfico das parcerias\"\n"
        "  },\n"
        "  \"parcerias_comerciais\": {\n"
        "    \"revendedores\": [\"Revendedor 1\", \"Revendedor 2\"],\n"
        "    \"distribuidores\": [\"Distribuidor 1\", \"Distribuidor 2\"],\n"
        "    \"canais_parceiros\": [\"Canal 1\", \"Canal 2\"],\n"
        "    \"consultores_implementadores\": [\"Consultor 1\", \"Consultor 2\"],\n"
        "    \"parceiros_go_to_market\": [\"Parceiro GTM 1\", \"Parceiro GTM 2\"]\n"
        "  },\n"
        "  \"parcerias_tecnologicas\": {\n"
        "    \"integracoes_nativas\": [\"Integração 1\", \"Integração 2\"],\n"
        "    \"apis_conectadas\": [\"API 1\", \"API 2\"],\n"
        "    \"plataformas_marketplace\": [\"Marketplace 1\", \"Marketplace 2\"],\n"
        "    \"parceiros_tecnicos\": [\"Parceiro Técnico 1\", \"Parceiro Técnico 2\"],\n"
        "    \"fornecedores_infraestrutura\": [\"Fornecedor 1\", \"Fornecedor 2\"]\n"
        "  },\n"
        "  \"parcerias_estrategicas\": {\n"
        "    \"joint_ventures\": [\"JV 1\", \"JV 2\"],\n"
        "    \"acordos_desenvolvimento\": [\"Acordo 1\", \"Acordo 2\"],\n"
        "    \"parceiros_investidores\": [\"Parceiro Inv 1\", \"Parceiro Inv 2\"],\n"
        "    \"aliancas_setoriais\": [\"Aliança 1\", \"Aliança 2\"],\n"
        "    \"parcerias_inovacao\": [\"Parceria Inov 1\", \"Parceria Inov 2\"]\n"
        "  },\n"
        "  \"programas_parceiros\": {\n"
        "    \"possui_programa_formal\": \"Sim/Não\",\n"
        "    \"tiers_programa\": [\"Tier 1\", \"Tier 2\", \"Tier 3\"],\n"
        "    \"beneficios_oferecidos\": [\"Benefício 1\", \"Benefício 2\"],\n"
        "    \"requisitos_participacao\": [\"Requisito 1\", \"Requisito 2\"],\n"
        "    \"suporte_parceiros\": \"Descrição do suporte oferecido\",\n"
        "    \"treinamento_certificacao\": \"Programas de treinamento disponíveis\"\n"
        "  },\n"
        "  \"cases_sucesso_parcerias\": [\n"
        "    {\n"
        "      \"parceiro\": \"Nome do parceiro\",\n"
        "      \"tipo_parceria\": \"Tipo da parceria\",\n"
        "      \"resultado_obtido\": \"Resultado específico da parceria\",\n"
        "      \"valor_gerado\": \"Valor ou benefício gerado\"\n"
        "    }\n"
        "  ],\n"
        "  \"analise_competitiva_parcerias\": {\n"
        "    \"benchmark_concorrentes\": \"Como as parcerias se comparam aos concorrentes\",\n"
        "    \"gaps_identificados\": [\"Gap 1\", \"Gap 2\"],\n"
        "    \"oportunidades_parcerias\": [\"Oportunidade 1\", \"Oportunidade 2\"],\n"
        "    \"ameacas_parcerias\": [\"Ameaça 1\", \"Ameaça 2\"]\n"
        "  },\n"
        "  \"metricas_parcerias\": {\n"
        "    \"receita_via_parceiros_estimada\": \"% da receita via parceiros\",\n"
        "    \"numero_parceiros_ativos\": \"Número de parceiros ativos\",\n"
        "    \"crescimento_parcerias\": \"Taxa de crescimento de parcerias\",\n"
        "    \"roi_programa_parceiros\": \"ROI estimado do programa de parceiros\"\n"
        "  },\n"
        "  \"fontes_informacao\": {\n"
        "    \"fontes_utilizadas\": [\"Site oficial\", \"Comunicados\", \"Parcerias anunciadas\"],\n"
        "    \"confiabilidade_dados\": \"Alta/Média/Baixa\",\n"
        "    \"data_ultima_atualizacao\": \"Data da última informação disponível\",\n"
        "    \"observacoes\": \"Observações sobre a qualidade dos dados\"\n"
        "  }\n"
        "}\n\n"
        "INSTRUÇÕES ESPECÍFICAS:\n"
        "1. Pesquise no site oficial, seção de parceiros, press releases\n"
        "2. Identifique integrações listadas em marketplaces (Salesforce AppExchange, etc.)\n"
        "3. Se não encontrar dados específicos, use 'Não identificado'\n"
        "4. Seja específico com nomes de empresas parceiras\n"
        "5. Inclua análise da maturidade do programa de parcerias\n"
        "6. Documente a metodologia utilizada para identificação\n"
        "7. Foque em parcerias que geram valor de negócio\n"
        "8. Identifique tanto parcerias formais quanto informais"
    )

    return _fazer_requisicao_perplexity(api_key, system_content, user_content)


def _gerar_dados_modelo_negocio(api_key, empresa, site_str=None):
    """Gera análise detalhada do modelo de negócio da empresa com classificações granulares"""

    # Primeiro, identifica o setor para contextualizar a análise
    try:
        setor_info = _detectar_setor_empresa(api_key, empresa, site_str)
    except Exception:
        # Se a detecção de setor falhar, continua sem contexto de setor
        setor_info = None

    system_content = (
        "Você é um consultor estratégico sênior especializado em análise de modelos de negócio e estratégia corporativa. "
        "Sua missão é identificar e analisar modelos de negócio empresariais com classificações granulares e específicas por setor, sempre respondendo APENAS em JSON válido. "
        "Use frameworks reconhecidos como Business Model Canvas, Value Stream Mapping e Unit Economics. "
        "Seja preciso, use dados verificáveis e adapte a análise ao setor específico da empresa. "
        "O texto deve estar em português do Brasil, tom consultivo e estratégico."
    )

    site_info = f" (site: {site_str})" if site_str and site_str != "Dado não encontrado" else ""
    setor_contexto = f"\nSETOR IDENTIFICADO: {setor_info.get('setor', 'Geral')}\nCARACTERÍSTICAS DO SETOR: {setor_info.get('caracteristicas', 'Análise geral')}\n" if setor_info else ""

    user_content = (
        f"Analise o modelo de negócio da empresa {empresa}{site_info} com foco em classificações granulares e métricas específicas.{setor_contexto}"
        "Use frameworks como Business Model Canvas, Unit Economics e Value Stream para estruturar a análise. "
        "Estruture a resposta EXATAMENTE neste formato JSON expandido:\n"
        "{\n"
        "  \"classificacao_modelo\": {\n"
        "    \"tipo_principal\": \"B2B/B2C/B2B2C/C2C/Marketplace/Platform/Agregador/Orchestrator\",\n"
        "    \"subtipos_detalhados\": {\n"
        "      \"categoria_primaria\": \"SaaS/E-commerce/Marketplace/Consultoria/Manufatura/Fintech/Healthtech/Edtech/Proptech/Insurtech/Logistics/Media\",\n"
        "      \"modelo_distribuicao\": \"Direct-to-Consumer/Channel/Hybrid/White-label/API-first\",\n"
        "      \"modelo_receita\": \"Subscription/Transactional/Commission/Licensing/Advertising/Freemium/Usage-based/Asset-light/Asset-heavy\",\n"
        "      \"arquitetura_valor\": \"Linear/Platform/Ecosystem/Network-effects/Multi-sided\"\n"
        "    },\n"
        "    \"modelo_hibrido\": {\n"
        "      \"e_hibrido\": \"Sim/Não\",\n"
        "      \"combinacoes\": [\"Combinação 1\", \"Combinação 2\"],\n"
        "      \"razao_hibridismo\": \"Justificativa para modelo híbrido\",\n"
        "      \"evolucao_hibrida\": \"Como o modelo evoluiu para híbrido\"\n"
        "    },\n"
        "    \"complexidade_modelo\": {\n"
        "      \"nivel\": \"Simples/Moderada/Complexa/Altamente-Complexa\",\n"
        "      \"fatores_complexidade\": [\"Fator 1\", \"Fator 2\"],\n"
        "      \"score_complexidade\": \"Score de 1-10\"\n"
        "    },\n"
        "    \"maturidade_digital\": \"Tradicional/Digital-first/Native-digital/Platform-native\"\n"
        "  },\n"
        "  \"proposta_valor\": {\n"
        "    \"valor_principal\": \"Proposta de valor principal da empresa\",\n"
        "    \"problemas_resolvidos\": {\n"
        "      \"primarios\": [\"Problema principal 1\", \"Problema principal 2\"],\n"
        "      \"secundarios\": [\"Problema secundário 1\", \"Problema secundário 2\"],\n"
        "      \"criticidade\": \"Alta/Média/Baixa - Para cada problema\"\n"
        "    },\n"
        "    \"beneficios_entregues\": {\n"
        "      \"tangíveis\": [\"Benefício mensurável 1\", \"Benefício mensurável 2\"],\n"
        "      \"intangíveis\": [\"Benefício intangível 1\", \"Benefício intangível 2\"],\n"
        "      \"quantificacao\": \"Quantificação dos benefícios quando possível\"\n"
        "    },\n"
        "    \"diferencial_competitivo\": {\n"
        "      \"principal\": \"Principal diferencial competitivo\",\n"
        "      \"secundarios\": [\"Diferencial 2\", \"Diferencial 3\"],\n"
        "      \"sustentabilidade\": \"Análise da sustentabilidade do diferencial\",\n"
        "      \"barreiras_entrada\": [\"Barreira 1\", \"Barreira 2\"]\n"
        "    },\n"
        "    \"valor_quantificado\": {\n"
        "      \"roi_cliente\": \"ROI típico para clientes\",\n"
        "      \"payback_period\": \"Período de payback para clientes\",\n"
        "      \"metricas_valor\": \"Métricas específicas de valor entregue\"\n"
        "    }\n"
        "  },\n"
        "  \"segmentos_clientes\": {\n"
        "    \"segmentacao_principal\": {\n"
        "      \"segmento_primario\": \"Segmento principal de clientes\",\n"
        "      \"percentual_receita\": \"% da receita deste segmento\",\n"
        "      \"caracteristicas\": [\"Característica 1\", \"Característica 2\"]\n"
        "    },\n"
        "    \"segmentos_secundarios\": [\n"
        "      {\n"
        "        \"segmento\": \"Nome do segmento\",\n"
        "        \"percentual_receita\": \"% da receita\",\n"
        "        \"potencial_crescimento\": \"Alto/Médio/Baixo\"\n"
        "      }\n"
        "    ],\n"
        "    \"perfil_clientes\": {\n"
        "      \"tamanho_empresas\": \"Startups/PME/Enterprise/Global/Misto\",\n"
        "      \"ticket_medio\": \"Faixa de ticket médio por segmento\",\n"
        "      \"ciclo_vendas\": \"Duração típica do ciclo de vendas\",\n"
        "      \"decisor_compra\": \"Perfil típico do decisor\"\n"
        "    },\n"
        "    \"setores_atendidos\": {\n"
        "      \"verticais_principais\": [\"Vertical 1\", \"Vertical 2\"],\n"
        "      \"especializacao_vertical\": \"Sim/Não - Descrição\",\n"
        "      \"requisitos_regulatorios\": [\"Regulação 1\", \"Regulação 2\"]\n"
        "    },\n"
        "    \"geografias_atendidas\": {\n"
        "      \"mercados_principais\": [\"País/Região 1\", \"País/Região 2\"],\n"
        "      \"estrategia_expansao\": \"Estratégia de expansão geográfica\",\n"
        "      \"adaptacoes_locais\": \"Adaptações necessárias por mercado\"\n"
        "    }\n"
        "  },\n"
        "  \"canais_distribuicao\": {\n"
        "    \"mix_canais\": {\n"
        "      \"canais_principais\": [\"Canal 1 - % receita\", \"Canal 2 - % receita\"],\n"
        "      \"estrategia_omnichannel\": \"Sim/Não - Descrição\",\n"
        "      \"integracao_canais\": \"Nível de integração entre canais\"\n"
        "    },\n"
        "    \"estrategia_go_to_market\": {\n"
        "      \"abordagem_principal\": \"Product-led/Sales-led/Marketing-led/Partner-led\",\n"
        "      \"funil_aquisicao\": \"Descrição do funil de aquisição\",\n"
        "      \"investimento_gtm\": \"Investimento relativo em GTM\"\n"
        "    },\n"
        "    \"canais_especificos\": {\n"
        "      \"vendas\": [\"Venda direta\", \"Parceiros\", \"Online\", \"Inside sales\"],\n"
        "      \"marketing\": [\"Digital\", \"Eventos\", \"Referência\", \"Inbound\", \"Account-based\"],\n"
        "      \"suporte\": [\"Chat\", \"Email\", \"Telefone\", \"Self-service\", \"Community\"]\n"
        "    }\n"
        "  },\n"
        "  \"relacionamento_clientes\": {\n"
        "    \"estrategia_relacionamento\": {\n"
        "      \"tipo_relacionamento\": \"Transacional/Consultivo/Self-service/Community-driven/High-touch\",\n"
        "      \"personalizacao\": \"Nível de personalização oferecido\",\n"
        "      \"frequencia_interacao\": \"Frequência típica de interação\"\n"
        "    },\n"
        "    \"estrategia_retencao\": {\n"
        "      \"taticas_principais\": [\"Tática 1\", \"Tática 2\"],\n"
        "      \"indicadores_satisfacao\": [\"NPS\", \"CSAT\", \"Churn rate\"],\n"
        "      \"programas_sucesso\": \"Programas de customer success\"\n"
        "    },\n"
        "    \"programas_engajamento\": {\n"
        "      \"fidelidade\": \"Programas de fidelidade existentes\",\n"
        "      \"comunidade\": \"Estratégias de building community\",\n"
        "      \"advocacy\": \"Programas de customer advocacy\"\n"
        "    },\n"
        "    \"crescimento_conta\": {\n"
        "      \"estrategia_upsell\": \"Estratégia de upsell detalhada\",\n"
        "      \"cross_sell\": \"Oportunidades de cross-sell\",\n"
        "      \"expansion_revenue\": \"% da receita vinda de expansão\"\n"
        "    }\n"
        "  },\n"
        "  \"recursos_principais\": {\n"
        "    \"recursos_fisicos\": {\n"
        "      \"ativos_principais\": [\"Ativo físico 1\", \"Ativo físico 2\"],\n"
        "      \"intensidade_capital\": \"Alta/Média/Baixa\",\n"
        "      \"necessidade_investimento\": \"Necessidades de investimento em ativos\"\n"
        "    },\n"
        "    \"recursos_intelectuais\": {\n"
        "      \"propriedade_intelectual\": [\"IP 1\", \"IP 2\"],\n"
        "      \"dados_proprietarios\": \"Dados proprietários como vantagem\",\n"
        "      \"algoritmos_ia\": \"Uso de algoritmos e IA como diferencial\",\n"
        "      \"marca_reputacao\": \"Força da marca como recurso\"\n"
        "    },\n"
        "    \"recursos_humanos\": {\n"
        "      \"talento_critico\": [\"Tipo de talento 1\", \"Tipo de talento 2\"],\n"
        "      \"competencias_core\": [\"Competência 1\", \"Competência 2\"],\n"
        "      \"cultura_organizacional\": \"Aspectos culturais como recurso\"\n"
        "    },\n"
        "    \"recursos_financeiros\": {\n"
        "      \"situacao_financeira\": \"Situação dos recursos financeiros\",\n"
        "      \"acesso_capital\": \"Facilidade de acesso a capital\",\n"
        "      \"estrutura_custos\": \"Características da estrutura de custos\"\n"
        "    },\n"
        "    \"recursos_tecnologicos\": {\n"
        "      \"plataforma_tech\": [\"Tecnologia core 1\", \"Tecnologia core 2\"],\n"
        "      \"infraestrutura\": \"Características da infraestrutura\",\n"
        "      \"capacidade_inovacao\": \"Capacidade de inovação tecnológica\"\n"
        "    }\n"
        "  },\n"
        "  \"metricas_modelo_negocio\": {\n"
        "    \"unit_economics\": {\n"
        "      \"cac\": \"Customer Acquisition Cost estimado\",\n"
        "      \"ltv\": \"Lifetime Value estimado\",\n"
        "      \"ltv_cac_ratio\": \"Razão LTV/CAC\",\n"
        "      \"payback_period\": \"Período de payback do CAC\",\n"
        "      \"gross_margin\": \"Margem bruta estimada\"\n"
        "    },\n"
        "    \"crescimento\": {\n"
        "      \"growth_rate\": \"Taxa de crescimento anual\",\n"
        "      \"retention_rate\": \"Taxa de retenção\",\n"
        "      \"expansion_rate\": \"Taxa de expansão (NRR)\",\n"
        "      \"viral_coefficient\": \"Coeficiente viral (se aplicável)\"\n"
        "    },\n"
        "    \"operacionais\": {\n"
        "      \"capital_efficiency\": \"Eficiência de capital\",\n"
        "      \"working_capital\": \"Necessidades de capital de giro\",\n"
        "      \"asset_turnover\": \"Giro de ativos\",\n"
        "      \"operating_leverage\": \"Alavancagem operacional\"\n"
        "    },\n"
        "    \"mercado\": {\n"
        "      \"market_share\": \"Market share estimado\",\n"
        "      \"tam_sam_som\": \"Análise TAM/SAM/SOM\",\n"
        "      \"penetracao_mercado\": \"Taxa de penetração no mercado\"\n"
        "    }\n"
        "  },\n"
        "  \"escalabilidade_modelo\": {\n"
        "    \"analise_escalabilidade\": {\n"
        "      \"nivel_escalabilidade\": \"Muito Alto/Alto/Médio/Baixo/Muito Baixo\",\n"
        "      \"score_escalabilidade\": \"Score de 1-10\",\n"
        "      \"justificativa\": \"Justificativa para o score\"\n"
        "    },\n"
        "    \"fatores_limitantes\": {\n"
        "      \"limitantes_internos\": [\"Limitante interno 1\", \"Limitante interno 2\"],\n"
        "      \"limitantes_externos\": [\"Limitante externo 1\", \"Limitante externo 2\"],\n"
        "      \"gargalos_principais\": [\"Gargalo 1\", \"Gargalo 2\"]\n"
        "    },\n"
        "    \"alavancas_crescimento\": {\n"
        "      \"alavancas_primarias\": [\"Alavanca principal 1\", \"Alavanca principal 2\"],\n"
        "      \"alavancas_secundarias\": [\"Alavanca secundária 1\", \"Alavanca secundária 2\"],\n"
        "      \"network_effects\": \"Presença de efeitos de rede - Sim/Não\",\n"
        "      \"economies_scale\": \"Economias de escala identificadas\"\n"
        "    },\n"
        "    \"requisitos_escala\": {\n"
        "      \"investimentos_necessarios\": \"Investimentos para escalar\",\n"
        "      \"recursos_criticos\": \"Recursos críticos para escalabilidade\",\n"
        "      \"timeline_escala\": \"Timeline estimado para alcançar escala\"\n"
        "    }\n"
        "  },\n"
        "  \"evolucao_modelo\": {\n"
        "    \"historico_evolucao\": {\n"
        "      \"modelo_original\": \"Modelo de negócio original\",\n"
        "      \"principais_pivots\": [\n"
        "        {\n"
        "          \"data_aproximada\": \"Período do pivot\",\n"
        "          \"tipo_pivot\": \"Tipo de pivot realizado\",\n"
        "          \"razao\": \"Razão para o pivot\",\n"
        "          \"resultado\": \"Resultado do pivot\"\n"
        "        }\n"
        "      ],\n"
        "      \"marcos_evolucao\": [\"Marco 1\", \"Marco 2\"]\n"
        "    },\n"
        "    \"tendencias_futuras\": {\n"
        "      \"tendencias_evolucao\": [\"Tendência 1\", \"Tendência 2\"],\n"
        "      \"futuro_modelo\": \"Direção futura do modelo\",\n"
        "      \"inovacoes_esperadas\": [\"Inovação 1\", \"Inovação 2\"],\n"
        "      \"adaptacoes_mercado\": \"Adaptações esperadas do mercado\"\n"
        "    }\n"
        "  },\n"
        "  \"riscos_modelo\": {\n"
        "    \"riscos_estrategicos\": {\n"
        "      \"riscos_principais\": [\"Risco estratégico 1\", \"Risco estratégico 2\"],\n"
        "      \"probabilidade\": \"Alta/Média/Baixa para cada risco\",\n"
        "      \"impacto\": \"Alto/Médio/Baixo para cada risco\"\n"
        "    },\n"
        "    \"vulnerabilidades\": {\n"
        "      \"vulnerabilidades_tecnicas\": [\"Vulnerabilidade técnica 1\", \"Vulnerabilidade técnica 2\"],\n"
        "      \"vulnerabilidades_mercado\": [\"Vulnerabilidade de mercado 1\", \"Vulnerabilidade de mercado 2\"],\n"
        "      \"vulnerabilidades_competitivas\": [\"Vulnerabilidade competitiva 1\", \"Vulnerabilidade competitiva 2\"]\n"
        "    },\n"
        "    \"dependencias_criticas\": {\n"
        "      \"fornecedores_chave\": [\"Dependência fornecedor 1\", \"Dependência fornecedor 2\"],\n"
        "      \"tecnologias_criticas\": [\"Dependência tecnológica 1\", \"Dependência tecnológica 2\"],\n"
        "      \"regulatorias\": [\"Dependência regulatória 1\", \"Dependência regulatória 2\"]\n"
        "    },\n"
        "    \"planos_contingencia\": {\n"
        "      \"estrategias_mitigacao\": [\"Estratégia 1\", \"Estratégia 2\"],\n"
        "      \"planos_backup\": \"Planos de contingência identificados\",\n"
        "      \"monitoramento\": \"Sistemas de monitoramento de riscos\"\n"
        "    }\n"
        "  },\n"
        "  \"benchmarking_setor\": {\n"
        "    \"comparacao_modelos\": \"Como o modelo se compara aos padrões do setor\",\n"
        "    \"melhores_praticas\": [\"Melhor prática 1\", \"Melhor prática 2\"],\n"
        "    \"gaps_mercado\": [\"Gap identificado 1\", \"Gap identificado 2\"],\n"
        "    \"inovacoes_setor\": [\"Inovação setorial 1\", \"Inovação setorial 2\"]\n"
        "  }\n"
        "}\n\n"
        "INSTRUÇÕES ESPECÍFICAS EXPANDIDAS:\n"
        "1. Use frameworks reconhecidos: Business Model Canvas, Unit Economics, Platform Canvas\n"
        "2. Seja MUITO específico na classificação granular do modelo\n"
        "3. Para modelos híbridos, detalhe EXATAMENTE quais elementos são combinados\n"
        "4. Inclua métricas específicas do setor (SaaS: ARR, MRR; E-commerce: AOV, Conv Rate; etc.)\n"
        "5. Se não encontrar dados específicos, use 'Não identificado' mas mantenha a estrutura\n"
        "6. Analise a evolução histórica do modelo quando possível\n"
        "7. Inclua análise profunda de escalabilidade com score numérico\n"
        "8. Para empresas brasileiras, considere aspectos regulatórios locais\n"
        "9. Documente fontes utilizadas para a análise\n"
        "10. Foque em aspectos únicos e diferenciadores do modelo específico\n"
        "11. Considere tanto aspectos operacionais quanto estratégicos\n"
        "12. Adapte a profundidade da análise ao setor identificado\n"
        "13. Inclua comparação com benchmarks setoriais quando relevante"
    )

    return _fazer_requisicao_perplexity(api_key, system_content, user_content)


def _detectar_setor_empresa(api_key, empresa, site_str=None):
    """Detecta o setor da empresa para contextualizar a análise de modelo de negócio"""
    system_content = (
        "Você é um especialista em classificação setorial e análise de mercado. "
        "Sua missão é identificar o setor principal de uma empresa e suas características, sempre respondendo APENAS em JSON válido. "
        "Seja preciso e use dados verificáveis quando disponíveis."
    )

    site_info = f" (site: {site_str})" if site_str and site_str != "Dado não encontrado" else ""

    user_content = (
        f"Identifique o setor principal da empresa {empresa}{site_info} e suas características. "
        "Estruture a resposta EXATAMENTE neste formato JSON:\n"
        "{\n"
        "  \"setor\": \"Nome do setor principal\",\n"
        "  \"subsetor\": \"Subsetor específico\",\n"
        "  \"caracteristicas\": \"Características principais do setor que impactam modelos de negócio\",\n"
        "  \"modelos_tipicos\": [\"Modelo típico 1\", \"Modelo típico 2\"],\n"
        "  \"metricas_chave\": [\"Métrica 1\", \"Métrica 2\"],\n"
        "  \"fatores_criticos\": [\"Fator crítico 1\", \"Fator crítico 2\"]\n"
        "}\n\n"
        "Seja específico e foque nas características que impactam a análise de modelo de negócio."
    )

    try:
        return _fazer_requisicao_perplexity(api_key, system_content, user_content)
    except Exception:
        return None


def _gerar_dados_pricing(api_key, empresa, site_str=None):
    """Gera análise detalhada de pricing da empresa com insights estratégicos avançados"""

    # Primeiro, identifica o setor para contextualizar a análise
    try:
        setor_info = _detectar_setor_empresa(api_key, empresa, site_str)
    except Exception:
        # Se a detecção de setor falhar, continua sem contexto de setor
        setor_info = None

    system_content = (
        "Você é um especialista sênior em pricing strategy, revenue management e psicologia de preços com experiência em múltiplos setores. "
        "Sua missão é identificar e analisar estratégias de pricing empresariais com foco em insights estratégicos e competitivos, sempre respondendo APENAS em JSON válido. "
        "Use frameworks reconhecidos como Value-Based Pricing, Price Elasticity Analysis, Competitive Positioning Maps e Revenue Optimization. "
        "Seja preciso, use dados verificáveis, identifique padrões psicológicos de pricing e analise o contexto competitivo. "
        "O texto deve estar em português do Brasil, tom analítico e estratégico com insights acionáveis."
    )

    site_info = f" (site: {site_str})" if site_str and site_str != "Dado não encontrado" else ""
    setor_contexto = f"\nSETOR IDENTIFICADO: {setor_info.get('setor', 'Geral')}\nCARACTERÍSTICAS DO SETOR: {setor_info.get('caracteristicas', 'Análise geral')}\nMÉTRICAS TÍPICAS DO SETOR: {', '.join(setor_info.get('metricas_chave', []))}\n" if setor_info else ""

    user_content = (
        f"Analise a estratégia de pricing da empresa {empresa}{site_info} com foco em insights estratégicos e competitivos.{setor_contexto}"
        "Pesquise estruturas de preços, tiers, políticas de desconto, estratégias psicológicas e benchmarking competitivo. "
        "Use frameworks de pricing strategy e revenue optimization para estruturar a análise. "
        "Estruture a resposta EXATAMENTE neste formato JSON expandido:\n"
        "{\n"
        "  \"resumo_pricing\": {\n"
        "    \"estrategia_principal\": \"Value-based/Cost-plus/Competition-based/Penetration/Skimming/Dynamic/Psychological\",\n"
        "    \"modelo_pricing\": \"Subscription/One-time/Usage-based/Freemium/Tiered/Hybrid/Performance-based\",\n"
        "    \"transparencia_precos\": \"Alta/Média/Baixa\",\n"
        "    \"complexidade_estrutura\": \"Simples/Moderada/Complexa/Altamente-Complexa\",\n"
        "    \"framework_pricing\": \"Framework principal identificado (SaaS, Marketplace, etc.)\",\n"
        "    \"maturidade_pricing\": \"Iniciante/Intermediário/Avançado/Sofisticado\"\n"
        "  },\n"
        "  \"estrutura_precos\": {\n"
        "    \"tipo_cobranca\": \"Mensal/Anual/Por uso/Por projeto/Híbrido/Baseado em valor\",\n"
        "    \"moeda_principal\": \"BRL/USD/EUR/Múltiplas\",\n"
        "    \"inclui_setup_fee\": \"Sim/Não - Valor se aplicável\",\n"
        "    \"periodo_trial\": \"Duração do período de teste gratuito\",\n"
        "    \"politica_cancelamento\": \"Política de cancelamento detalhada\",\n"
        "    \"contratos_minimos\": \"Contratos mínimos obrigatórios\",\n"
        "    \"flexibilidade_pagamento\": \"Opções de flexibilidade de pagamento\",\n"
        "    \"moedas_aceitas\": [\"Moeda 1\", \"Moeda 2\"],\n"
        "    \"metodos_pagamento\": [\"Cartão\", \"Boleto\", \"PIX\", \"Transferência\"]\n"
        "  },\n"
        "  \"tiers_precos_detalhados\": [\n"
        "    {\n"
        "      \"nome_tier\": \"Nome do tier (ex: Básico, Pro, Enterprise)\",\n"
        "      \"posicionamento_tier\": \"Entry-level/Mid-market/Enterprise/Premium\",\n"
        "      \"preco_mensal\": \"Preço mensal em moeda local\",\n"
        "      \"preco_anual\": \"Preço anual (se aplicável)\",\n"
        "      \"desconto_anual\": \"% de desconto para pagamento anual\",\n"
        "      \"principais_features\": [\"Feature 1\", \"Feature 2\"],\n"
        "      \"limites_uso\": \"Limites de uso do tier\",\n"
        "      \"target_audience\": \"Público-alvo do tier\"\n"
        "    }\n"
        "  ],\n"
        "  \"politicas_desconto\": {\n"
        "    \"desconto_volume\": \"Descontos por volume de compra\",\n"
        "    \"desconto_anual\": \"Desconto para pagamento anual\",\n"
        "    \"desconto_estudante\": \"Desconto para estudantes/ONGs\",\n"
        "    \"desconto_startup\": \"Programas para startups\",\n"
        "    \"negociacao_enterprise\": \"Possibilidade de negociação para enterprise\"\n"
        "  },\n"
        "  \"add_ons_extras\": {\n"
        "    \"servicos_adicionais\": [\"Serviço adicional 1\", \"Serviço adicional 2\"],\n"
        "    \"integracao_premium\": \"Integrações pagas\",\n"
        "    \"suporte_premium\": \"Níveis de suporte pagos\",\n"
        "    \"consultoria_implementacao\": \"Serviços de consultoria\",\n"
        "    \"treinamento_certificacao\": \"Programas de treinamento pagos\"\n"
        "  },\n"
        "  \"metricas_pricing\": {\n"
        "    \"ticket_medio\": \"Ticket médio estimado\",\n"
        "    \"ltv_estimado\": \"Lifetime Value estimado\",\n"
        "    \"churn_rate\": \"Taxa de churn estimada\",\n"
        "    \"arpu\": \"Average Revenue Per User\",\n"
        "    \"conversion_rate\": \"Taxa de conversão trial para pago\"\n"
        "  },\n"
        "  \"analise_competitiva\": {\n"
        "    \"posicionamento_mercado\": \"Premium/Médio/Econômico\",\n"
        "    \"comparacao_concorrentes\": [\n"
        "      {\n"
        "        \"concorrente\": \"Nome do concorrente\",\n"
        "        \"preco_similar\": \"Preço do tier similar\",\n"
        "        \"diferencial_pricing\": \"Diferencial no pricing\"\n"
        "      }\n"
        "    ],\n"
        "    \"vantagem_competitiva\": \"Vantagem competitiva no pricing\",\n"
        "    \"desvantagem_pricing\": \"Possíveis desvantagens no pricing\"\n"
        "  },\n"
        "  \"estrategias_monetizacao\": {\n"
        "    \"receita_principal\": \"Fonte principal de receita\",\n"
        "    \"receitas_secundarias\": [\"Fonte secundária 1\", \"Fonte secundária 2\"],\n"
        "    \"modelo_crescimento\": \"Land and expand/New logo acquisition\",\n"
        "    \"upsell_opportunities\": [\"Oportunidade 1\", \"Oportunidade 2\"],\n"
        "    \"cross_sell_products\": [\"Produto cross-sell 1\", \"Produto cross-sell 2\"]\n"
        "  },\n"
        "  \"elasticidade_preco\": {\n"
        "    \"sensibilidade_preco\": \"Alta/Média/Baixa\",\n"
        "    \"mudancas_historicas\": \"Mudanças históricas de preço identificadas\",\n"
        "    \"resposta_mercado\": \"Resposta do mercado às mudanças\",\n"
        "    \"teste_pricing\": \"Evidências de testes de pricing\"\n"
        "  },\n"
        "  \"tendencias_pricing\": {\n"
        "    \"evolucao_precos\": \"Evolução dos preços nos últimos anos\",\n"
        "    \"tendencia_setor\": \"Tendências de pricing no setor\",\n"
        "    \"impacto_concorrencia\": \"Impacto da concorrência no pricing\",\n"
        "    \"futuro_pricing\": \"Tendências futuras esperadas\"\n"
        "  },\n"
        "  \"fontes_informacao\": {\n"
        "    \"fontes_utilizadas\": [\"Página de preços\", \"Propostas públicas\", \"Reviews\"],\n"
        "    \"confiabilidade_dados\": \"Alta/Média/Baixa\",\n"
        "    \"data_ultima_atualizacao\": \"Data da última atualização de preços\",\n"
        "    \"observacoes\": \"Observações sobre a coleta de dados\"\n"
        "  }\n"
        "}\n\n"
        "INSTRUÇÕES ESPECÍFICAS:\n"
        "1. Pesquise páginas de pricing, propostas comerciais públicas\n"
        "2. Compare com 3-5 concorrentes principais quando possível\n"
        "3. Se não encontrar preços específicos, use 'Sob consulta'\n"
        "4. Inclua moeda e contextualize valores geográficos\n"
        "5. Analise a estratégia por trás dos preços, não apenas valores\n"
        "6. Documente a metodologia de coleta dos dados\n"
        "7. Identifique padrões e tendências no pricing\n"
        "8. Foque em insights estratégicos sobre a abordagem de pricing"
    )

    return _fazer_requisicao_perplexity(api_key, system_content, user_content)


def _fazer_requisicao_perplexity(api_key, system_content, user_content):
    """Faz requisição para a API do Perplexity e processa a resposta"""
    try:
        response = requests.post(
            "https://api.perplexity.ai/chat/completions",
            json={
                "model": "sonar-pro",
                "messages": [
                    {"role": "system", "content": system_content},
                    {"role": "user", "content": user_content}
                ],
                "return_images": True,
                "web_search_options": {
                    "search_context_size": "high",
                    "user_location": {
                        "country": "BR"
                    }
                }
            },
            headers={
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
        )

        if response.status_code == 200:
            content = response.json()["choices"][0]["message"]["content"]
            # Extrair apenas o JSON da resposta
            start = content.find('{')
            end = content.rfind('}') + 1
            json_str = content[start:end]
            return json.loads(json_str)
        else:
            return {"erro": f"Erro na API Perplexity: {response.status_code}", "raw": response.text}

    except Exception as e:
        return {"erro": f"Erro ao processar requisição: {str(e)}"}


def _notificar_websocket(client_id, setor):
    """Notifica o frontend via WebSocket"""
    try:
        # ✅ CORREÇÃO: Usar o websocket_manager global em vez do manager do main.py
        from shared.websocket_manager import websocket_manager
        import asyncio

        loop = None
        try:
            loop = asyncio.get_running_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        coro = websocket_manager.broadcast({
            "type": "client_update",
            "clientId": str(client_id),
            "sector": setor
        })

        if loop.is_running():
            asyncio.run_coroutine_threadsafe(coro, loop)
        else:
            loop.run_until_complete(coro)
    except Exception as e:
        print(f"Erro ao notificar via WebSocket: {e}", file=sys.stderr)


def classificar_setor_por_llm(dossie):
    """
    Usa uma LLM (Perplexity) para classificar o setor do cliente a partir do dossiê.
    Retorna o setor como string (apenas uma palavra).
    """
    # Extrair texto relevante do dossiê
    texto = json.dumps(dossie, ensure_ascii=False)

    # Prompt livre para classificação
    prompt = (
        "Analise o seguinte dossiê de empresa e classifique, de forma sucinta e objetiva, o setor principal de atuação da empresa. "
        "Responda apenas com o nome do setor principal (Exemplo: Tecnologia, Saúde, Transporte, Bancário, etc.), sem explicações adicionais.\n\nDossiê:\n" +
        texto + "\n\nSetor principal:"
    )

    # Usar Perplexity ao invés de OpenAI
    perplexity_api_key = os.getenv("PERPLEXITY_API_KEY")
    if perplexity_api_key:
        import requests
        url = "https://api.perplexity.ai/chat/completions"
        headers = {
            "Authorization": f"Bearer {perplexity_api_key}",
            "Content-Type": "application/json"
        }
        payload = {
            "model": "llama-3.1-sonar-small-128k-online",
            "messages": [
                {"role": "system",
                    "content": "Você é um classificador de setores de empresas. Sempre responda apenas com UMA palavra indicando o setor."},
                {"role": "user", "content": prompt}
            ],
            "max_tokens": 10,
            "temperature": 0.2
        }

        try:
            resp = requests.post(
                url, json=payload, headers=headers, timeout=30)
            if resp.status_code == 200:
                try:
                    content = resp.json()["choices"][0]["message"]["content"]
                    # Garante apenas uma palavra
                    setor = content.strip().split()[0]
                    print(
                        f"✅ Setor classificado com sucesso via Perplexity: {setor}")
                    return setor
                except Exception as e:
                    print(
                        f"❌ Erro ao processar resposta Perplexity: {e}", file=sys.stderr)
                    return None
            else:
                print(
                    f"❌ Erro na API Perplexity: {resp.status_code} - {resp.text}", file=sys.stderr)
                return None
        except Exception as e:
            print(f"❌ Erro na requisição Perplexity: {e}", file=sys.stderr)
            return None
    else:
        print("❌ PERPLEXITY_API_KEY não configurada", file=sys.stderr)
        return None


def _gerar_dados_canais_reviews(api_key, empresa, site_str=None):
    """Gera análise detalhada de canais de distribuição, reviews e certificações da empresa"""

    # Primeiro, identifica o setor para contextualizar a análise
    try:
        setor_info = _detectar_setor_empresa(api_key, empresa, site_str)
    except Exception:
        # Se a detecção de setor falhar, continua sem contexto de setor
        setor_info = None

    system_content = (
        "Você é um especialista sênior em canais de distribuição, análise de reputação e certificações empresariais com experiência em múltiplos setores. "
        "Sua missão é identificar e analisar estratégias de go-to-market, feedback de clientes, e credibilidade empresarial, sempre respondendo APENAS em JSON válido. "
        "Use frameworks reconhecidos como Channel Strategy, Customer Journey Mapping, Net Promoter Score (NPS), e Reputation Management. "
        "Seja preciso, use dados verificáveis, identifique padrões de satisfação e analise o contexto competitivo de distribuição. "
        "O texto deve estar em português do Brasil, tom analítico e estratégico com insights acionáveis."
    )

    site_info = f" (site: {site_str})" if site_str and site_str != "Dado não encontrado" else ""
    setor_contexto = f"\nSETOR IDENTIFICADO: {setor_info.get('setor', 'Geral')}\nCARACTERÍSTICAS DO SETOR: {setor_info.get('caracteristicas', 'Análise geral')}\nMÉTRICAS TÍPICAS DO SETOR: {', '.join(setor_info.get('metricas_chave', []))}\n" if setor_info else ""

    user_content = (
        f"Analise a estratégia de canais de distribuição, reviews de clientes e certificações da empresa {empresa}{site_info} com foco em insights estratégicos e reputacionais.{setor_contexto}"
        "Pesquise canais de vendas, feedback de clientes, certificações técnicas, premiações e reconhecimentos. "
        "Use frameworks de channel strategy, customer experience e reputation management para estruturar a análise. "
        "Estruture a resposta EXATAMENTE neste formato JSON expandido:\n"
        "{\n"
        "  \"dados_canais_distribuicao\": {\n"
        "    \"resumo_canais\": {\n"
        "      \"estrategia_principal\": \"Direct-to-consumer/B2B-sales/Marketplace/Omnichannel/Partner-network/Hybrid\",\n"
        "      \"modelo_distribuicao\": \"Direto/Indireto/Misto/Franquia/Licenciamento/Dropshipping\",\n"
        "      \"cobertura_geografica\": \"Local/Regional/Nacional/Internacional/Global\",\n"
        "      \"maturidade_canais\": \"Iniciante/Intermediário/Avançado/Sofisticado\",\n"
        "      \"complexidade_rede\": \"Simples/Moderada/Complexa/Altamente-Complexa\",\n"
        "      \"integracoes_sistemas\": \"Manual/Semi-automatizada/Automatizada/AI-powered\"\n"
        "    },\n"
        "    \"canais_principais\": [\n"
        "      {\n"
        "        \"nome_canal\": \"Nome do canal (ex: Site próprio, Amazon, Representantes)\",\n"
        "        \"tipo_canal\": \"Online/Offline/Híbrido\",\n"
        "        \"importancia_estrategica\": \"Primário/Secundário/Suporte/Experimental\",\n"
        "        \"percentual_vendas\": \"% estimado das vendas totais\",\n"
        "        \"target_audience\": \"Público-alvo principal do canal\",\n"
        "        \"vantagens_canal\": \"Principais vantagens do canal\",\n"
        "        \"limitacoes_canal\": \"Limitações identificadas\"\n"
        "      }\n"
        "    ],\n"
        "    \"estrategia_omnichannel\": {\n"
        "      \"integracao_canais\": \"Nível de integração entre canais\",\n"
        "      \"experiencia_unificada\": \"Consistência da experiência do cliente\",\n"
        "      \"dados_compartilhados\": \"Compartilhamento de dados entre canais\",\n"
        "      \"inventario_centralizado\": \"Gestão centralizada de estoque\",\n"
        "      \"politicas_unificadas\": \"Políticas uniformes entre canais\"\n"
        "    },\n"
        "    \"parceiros_distribuicao\": {\n"
        "      \"revendedores_autorizados\": [\"Parceiro 1\", \"Parceiro 2\"],\n"
        "      \"distribuidores_regionais\": [\"Distribuidor 1\", \"Distribuidor 2\"],\n"
        "      \"marketplaces_principais\": [\"Marketplace 1\", \"Marketplace 2\"],\n"
        "      \"canais_especialistas\": [\"Canal especializado 1\", \"Canal especializado 2\"],\n"
        "      \"programas_parceiros\": \"Descrição dos programas de parceiros\"\n"
        "    },\n"
        "    \"performance_canais\": {\n"
        "      \"canal_mais_eficiente\": \"Canal com melhor ROI\",\n"
        "      \"crescimento_por_canal\": \"Tendências de crescimento por canal\",\n"
        "      \"custos_aquisicao\": \"CAC por canal quando disponível\",\n"
        "      \"tempo_ciclo_vendas\": \"Tempo médio de ciclo por canal\",\n"
        "      \"satisfacao_parceiros\": \"Satisfação dos parceiros de canal\"\n"
        "    },\n"
        "    \"go_to_market\": {\n"
        "      \"estrategia_lancamento\": \"Estratégia para novos produtos/mercados\",\n"
        "      \"segmentacao_canais\": \"Segmentação por tipo de cliente\",\n"
        "      \"adaptacao_local\": \"Adaptação para mercados locais\",\n"
        "      \"suporte_vendas\": \"Estrutura de suporte às vendas\",\n"
        "      \"treinamento_canais\": \"Programas de capacitação de canais\"\n"
        "    }\n"
        "  },\n"
        "  \"dados_reviews_feedback\": {\n"
        "    \"resumo_reputacao\": {\n"
        "      \"score_reputacao_geral\": \"Score médio geral (1-5 ou 1-10)\",\n"
        "      \"numero_total_reviews\": \"Número total de avaliações encontradas\",\n"
        "      \"tendencia_satisfacao\": \"Crescente/Estável/Decrescente\",\n"
        "      \"confiabilidade_dados\": \"Alta/Média/Baixa\",\n"
        "      \"fonte_principal_reviews\": \"Plataforma principal de reviews\",\n"
        "      \"periodo_analise\": \"Período das avaliações analisadas\"\n"
        "    },\n"
        "    \"plataformas_review\": {\n"
        "      \"google_reviews\": {\n"
        "        \"rating_medio\": \"Rating médio no Google\",\n"
        "        \"numero_avaliacoes\": \"Número de avaliações\",\n"
        "        \"comentarios_recentes\": \"Análise dos comentários recentes\"\n"
        "      },\n"
        "      \"reclame_aqui\": {\n"
        "        \"nota_reclame_aqui\": \"Nota no Reclame Aqui\",\n"
        "        \"tempo_resposta\": \"Tempo médio de resposta\",\n"
        "        \"indice_solucao\": \"Índice de solução de problemas\"\n"
        "      },\n"
        "      \"trustpilot\": {\n"
        "        \"rating_trustpilot\": \"Rating no Trustpilot\",\n"
        "        \"numero_reviews\": \"Número de reviews\",\n"
        "        \"classificacao_empresa\": \"Classificação da empresa\"\n"
        "      },\n"
        "      \"app_stores\": {\n"
        "        \"google_play_rating\": \"Rating no Google Play\",\n"
        "        \"app_store_rating\": \"Rating na App Store\",\n"
        "        \"downloads_estimados\": \"Downloads estimados\"\n"
        "      },\n"
        "      \"reviews_especializados\": {\n"
        "        \"sites_especializados\": [\"Site especializado 1\", \"Site especializado 2\"],\n"
        "        \"ratings_especializados\": \"Ratings em sites especializados\",\n"
        "        \"premios_mercado\": [\"Prêmio 1\", \"Prêmio 2\"]\n"
        "      }\n"
        "    },\n"
        "    \"sentiment_geral\": {\n"
        "      \"sentiment_positivo\": \"% de comentários positivos\",\n"
        "      \"sentiment_neutro\": \"% de comentários neutros\",\n"
        "      \"sentiment_negativo\": \"% de comentários negativos\",\n"
        "      \"palavras_chave_positivas\": [\"Palavra positiva 1\", \"Palavra positiva 2\"],\n"
        "      \"palavras_chave_negativas\": [\"Palavra negativa 1\", \"Palavra negativa 2\"]\n"
        "    },\n"
        "    \"pontos_fortes_fracos\": {\n"
        "      \"principais_elogios\": [\"Elogio 1\", \"Elogio 2\", \"Elogio 3\"],\n"
        "      \"principais_criticas\": [\"Crítica 1\", \"Crítica 2\", \"Crítica 3\"],\n"
        "      \"aspectos_mais_valorizados\": [\"Aspecto 1\", \"Aspecto 2\"],\n"
        "      \"areas_melhoria\": [\"Área 1\", \"Área 2\"],\n"
        "      \"comparacao_concorrentes\": \"Como se compara aos concorrentes\"\n"
        "    },\n"
        "    \"tendencias_satisfacao\": {\n"
        "      \"evolucao_ratings\": \"Evolução dos ratings ao longo do tempo\",\n"
        "      \"sazonalidade_reviews\": \"Padrões sazonais nas avaliações\",\n"
        "      \"impacto_melhorias\": \"Impacto de melhorias nas avaliações\",\n"
        "      \"nps_estimado\": \"Net Promoter Score estimado\",\n"
        "      \"taxa_recomendacao\": \"% de clientes que recomendariam\"\n"
        "    },\n"
        "    \"gestao_feedback\": {\n"
        "      \"resposta_reviews\": \"Empresa responde aos reviews\",\n"
        "      \"tempo_resposta_medio\": \"Tempo médio de resposta\",\n"
        "      \"qualidade_respostas\": \"Qualidade das respostas da empresa\",\n"
        "      \"resolucao_problemas\": \"Taxa de resolução de problemas\",\n"
        "      \"proatividade_melhorias\": \"Proatividade em implementar melhorias\"\n"
        "    }\n"
        "  },\n"
        "  \"dados_certificacoes\": {\n"
        "    \"resumo_certificacoes\": {\n"
        "      \"total_certificacoes\": \"Número total de certificações identificadas\",\n"
        "      \"nivel_certificacao\": \"Básico/Intermediário/Avançado/Premium\",\n"
        "      \"areas_cobertas\": [\"Área 1\", \"Área 2\", \"Área 3\"],\n"
        "      \"validade_media\": \"Validade média das certificações\",\n"
        "      \"investimento_estimado\": \"Investimento estimado em certificações\",\n"
        "      \"impacto_mercado\": \"Impacto no posicionamento de mercado\"\n"
        "    },\n"
        "    \"certificacoes_tecnicas\": {\n"
        "      \"iso_quality\": [\"ISO 9001\", \"ISO 27001\", \"Outras ISOs\"],\n"
        "      \"certificacoes_setor\": [\"Certificação específica 1\", \"Certificação específica 2\"],\n"
        "      \"cloud_providers\": [\"AWS Partner\", \"Azure Certified\", \"Google Cloud\"],\n"
        "      \"seguranca_dados\": [\"SOC 2\", \"GDPR Compliance\", \"LGPD\"],\n"
        "      \"metodologias_dev\": [\"Agile\", \"DevOps\", \"Outras metodologias\"]\n"
        "    },\n"
        "    \"premiacao_industria\": {\n"
        "      \"premios_nacionais\": [\"Prêmio nacional 1\", \"Prêmio nacional 2\"],\n"
        "      \"premios_internacionais\": [\"Prêmio internacional 1\", \"Prêmio internacional 2\"],\n"
        "      \"rankings_mercado\": [\"Ranking 1\", \"Ranking 2\"],\n"
        "      \"reconhecimentos_midia\": [\"Reconhecimento 1\", \"Reconhecimento 2\"],\n"
        "      \"cases_premiados\": [\"Case premiado 1\", \"Case premiado 2\"]\n"
        "    },\n"
        "    \"compliance_regulatorio\": {\n"
        "      \"licencas_operacao\": [\"Licença 1\", \"Licença 2\"],\n"
        "      \"conformidade_legal\": [\"Regulamentação 1\", \"Regulamentação 2\"],\n"
        "      \"auditorias_externas\": \"Frequência e resultados de auditorias\",\n"
        "      \"compliance_internacional\": [\"GDPR\", \"SOX\", \"Outras\"],\n"
        "      \"certificacoes_ambientais\": [\"Certificação ambiental 1\", \"Certificação ambiental 2\"]\n"
        "    },\n"
        "    \"reconhecimentos\": {\n"
        "      \"great_place_work\": \"Certificação Great Place to Work\",\n"
        "      \"b_corp\": \"Certificação B Corp\",\n"
        "      \"startup_awards\": [\"Award 1\", \"Award 2\"],\n"
        "      \"lideranca_mercado\": [\"Reconhecimento liderança 1\", \"Reconhecimento liderança 2\"],\n"
        "      \"inovacao_premios\": [\"Prêmio inovação 1\", \"Prêmio inovação 2\"]\n"
        "    },\n"
        "    \"impacto_certificacoes\": {\n"
        "      \"credibilidade_mercado\": \"Impacto na credibilidade\",\n"
        "      \"diferenciacao_competitiva\": \"Diferenciação dos concorrentes\",\n"
        "      \"acesso_novos_mercados\": \"Acesso facilitado a novos mercados\",\n"
        "      \"premium_pricing\": \"Justificativa para preços premium\",\n"
        "      \"parcerias_estrategicas\": \"Facilitação de parcerias\"\n"
        "    }\n"
        "  },\n"
        "  \"analise_reputacional\": {\n"
        "    \"posicionamento_mercado\": \"Líder/Challenger/Seguidor/Nicho\",\n"
        "    \"confianca_marca\": \"Nível de confiança na marca\",\n"
        "    \"awareness_mercado\": \"Reconhecimento no mercado\",\n"
        "    \"recomendacao_espontanea\": \"Taxa de recomendação espontânea\",\n"
        "    \"vulnerabilidades_reputacao\": [\"Vulnerabilidade 1\", \"Vulnerabilidade 2\"],\n"
        "    \"oportunidades_melhoria\": [\"Oportunidade 1\", \"Oportunidade 2\"]\n"
        "  },\n"
        "  \"competitividade_canais\": {\n"
        "    \"benchmark_concorrentes\": [\n"
        "      {\n"
        "        \"concorrente\": \"Nome do concorrente\",\n"
        "        \"canais_principais\": [\"Canal 1\", \"Canal 2\"],\n"
        "        \"vantagem_competitiva\": \"Vantagem nos canais\"\n"
        "      }\n"
        "    ],\n"
        "    \"gaps_distribuicao\": [\"Gap 1\", \"Gap 2\"],\n"
        "    \"oportunidades_expansao\": [\"Oportunidade 1\", \"Oportunidade 2\"],\n"
        "    \"ameacas_canais\": [\"Ameaça 1\", \"Ameaça 2\"]\n"
        "  },\n"
        "  \"fontes_informacao\": {\n"
        "    \"fontes_utilizadas\": [\"Reviews Google\", \"Reclame Aqui\", \"Site da empresa\"],\n"
        "    \"confiabilidade_dados\": \"Alta/Média/Baixa\",\n"
        "    \"data_ultima_atualizacao\": \"Data da última atualização dos dados\",\n"
        "    \"observacoes\": \"Observações sobre a coleta de dados\",\n"
        "    \"limitacoes_analise\": \"Limitações identificadas na análise\"\n"
        "  }\n"
        "}\n\n"
        "INSTRUÇÕES ESPECÍFICAS:\n"
        "1. Pesquise canais de vendas, plataformas de review e certificações oficiais\n"
        "2. Compare com 3-5 concorrentes principais quando possível\n"
        "3. Se não encontrar dados específicos, use 'Não disponível'\n"
        "4. Foque em insights estratégicos sobre distribuição e reputação\n"
        "5. Documente a metodologia de coleta dos dados\n"
        "6. Identifique padrões e tendências na satisfação do cliente\n"
        "7. Analise o impacto das certificações no posicionamento\n"
        "8. Inclua métricas de performance quando disponíveis"
    )

    return _fazer_requisicao_perplexity(api_key, system_content, user_content)


def _gerar_dados_pesquisa_mercado(api_key, empresa, site_str=None):
    """Gera análise detalhada de pesquisa de mercado e produtos/serviços da empresa"""

    # Validação de parâmetros
    if not api_key or api_key.strip() == "":
        raise ValueError("API key não pode estar vazio")

    if not empresa or empresa.strip() == "":
        raise ValueError("Nome da empresa não pode estar vazio")

    # Primeiro, identifica o setor para contextualizar a análise
    try:
        setor_info = _detectar_setor_empresa(api_key, empresa, site_str)
    except Exception:
        # Se a detecção de setor falhar, continua sem contexto de setor
        setor_info = None

    system_content = (
        "Você é um especialista sênior em inteligência de mercado e análise competitiva com experiência em múltiplos setores. "
        "Sua missão é conduzir pesquisas paralelas detalhadas sobre mercado, tendências setoriais e análise de produtos/serviços concorrentes, sempre respondendo APENAS em JSON válido. "
        "Use frameworks reconhecidos como TAM/SAM/SOM, Porter's Five Forces, PESTEL Analysis, e Competitive Intelligence. "
        "Seja analítico, use dados verificáveis de fontes confiáveis como Gartner, IDC, McKinsey, e relatórios setoriais oficiais. "
        "Identifique oportunidades de mercado, gaps competitivos e insights estratégicos acionáveis. "
        "O texto deve estar em português do Brasil, tom estratégico e consultivo com insights de alto valor."
    )

    site_info = f" (site: {site_str})" if site_str and site_str != "Dado não encontrado" else ""
    setor_contexto = f"\nSETOR IDENTIFICADO: {setor_info.get('setor', 'Geral')}\nCARACTERÍSTICAS DO SETOR: {setor_info.get('caracteristicas', 'Análise geral')}\nMÉTRICAS TÍPICAS DO SETOR: {', '.join(setor_info.get('metricas_chave', []))}\n" if setor_info else ""

    user_content = (
        f"Conduza uma pesquisa paralela abrangente do mercado onde atua a empresa {empresa}{site_info} com foco em oportunidades estratégicas e intelligence competitiva.{setor_contexto}"
        "Pesquise tamanho de mercado (TAM/SAM/SOM), tendências setoriais, análise de concorrentes, gaps de mercado e oportunidades de produtos. "
        "Use frameworks de market intelligence, competitive analysis e strategic foresight para estruturar insights acionáveis. "
        "Estruture a resposta EXATAMENTE neste formato JSON expandido:\n"
        "{\n"
        "  \"pesquisa_mercado\": {\n"
        "    \"tamanho_mercado\": {\n"
        "      \"tam_global\": \"Total Addressable Market - valor global do mercado\",\n"
        "      \"sam_serviceable\": \"Serviceable Addressable Market - porção que a empresa pode servir\",\n"
        "      \"som_obtainable\": \"Serviceable Obtainable Market - porção realisticamente obtível\",\n"
        "      \"crescimento_cagr\": \"Taxa de crescimento anual composta projetada\",\n"
        "      \"geografia_mercados\": [\"Brasil\", \"Argentina\", \"México\", \"Chile\", \"Global\"]\n"
        "    },\n"
        "    \"tendencias_setor\": [\n"
        "      {\n"
        "        \"tendencia\": \"Nome da tendência (ex: AI generativa, Economia circular)\",\n"
        "        \"impacto\": \"alto/médio/baixo\",\n"
        "        \"horizonte_tempo\": \"curto prazo/médio prazo/longo prazo\",\n"
        "        \"evidencias\": [\"Dados que suportam a tendência\", \"Investimentos reportados\", \"Casos de uso\"]\n"
        "      }\n"
        "    ],\n"
        "    \"drivers_crescimento\": [\n"
        "      \"Fator de crescimento 1 (ex: Digitalização acelerada)\",\n"
        "      \"Fator de crescimento 2 (ex: Regulamentações governamentais)\",\n"
        "      \"Fator de crescimento 3 (ex: Mudanças comportamentais)\"\n"
        "    ],\n"
        "    \"barreiras_entrada\": [\n"
        "      \"Barreira 1 (ex: Alto investimento inicial)\",\n"
        "      \"Barreira 2 (ex: Regulamentações complexas)\",\n"
        "      \"Barreira 3 (ex: Necessidade de expertise técnica)\"\n"
        "    ]\n"
        "  },\n"
        "  \"analise_produtos_servicos\": {\n"
        "    \"portfolio_concorrentes\": [\n"
        "      {\n"
        "        \"concorrente\": \"Nome do concorrente principal\",\n"
        "        \"produtos_principais\": [\"Produto 1\", \"Produto 2\", \"Produto 3\"],\n"
        "        \"posicionamento\": \"premium/mid-market/entry-level/niche\",\n"
        "        \"diferenciais\": [\"Principal diferencial 1\", \"Principal diferencial 2\"]\n"
        "      }\n"
        "    ],\n"
        "    \"gaps_mercado\": [\n"
        "      \"Gap não atendido 1 (ex: Soluções para PMEs)\",\n"
        "      \"Gap não atendido 2 (ex: Integração com ERPs brasileiros)\",\n"
        "      \"Gap não atendido 3 (ex: Suporte em português)\"\n"
        "    ],\n"
        "    \"inovacoes_recentes\": [\n"
        "      \"Inovação 1 (ex: AI conversacional)\",\n"
        "      \"Inovação 2 (ex: Análise preditiva em tempo real)\",\n"
        "      \"Inovação 3 (ex: Automação inteligente)\"\n"
        "    ],\n"
        "    \"tendencias_produto\": [\n"
        "      \"Tendência de produto 1 (ex: No-code platforms)\",\n"
        "      \"Tendência de produto 2 (ex: AI-powered insights)\",\n"
        "      \"Tendência de produto 3 (ex: Real-time personalization)\"\n"
        "    ],\n"
        "    \"oportunidades_produto\": [\n"
        "      \"Oportunidade 1 (ex: Vertical específico para e-commerce)\",\n"
        "      \"Oportunidade 2 (ex: Módulo de compliance automatizado)\",\n"
        "      \"Oportunidade 3 (ex: Dashboard executivo mobile)\"\n"
        "    ]\n"
        "  },\n"
        "  \"insights_competitivos\": {\n"
        "    \"posicionamento_relativo\": \"Líder/Challenger/Seguidor/Nicho - posição da empresa no mercado\",\n"
        "    \"vantagens_competitivas\": [\n"
        "      \"Vantagem competitiva 1 (ex: Deep knowledge mercado local)\",\n"
        "      \"Vantagem competitiva 2 (ex: Agilidade para implementar features)\",\n"
        "      \"Vantagem competitiva 3 (ex: Pricing competitivo)\"\n"
        "    ],\n"
        "    \"vulnerabilidades\": [\n"
        "      \"Vulnerabilidade 1 (ex: Menor brand awareness)\",\n"
        "      \"Vulnerabilidade 2 (ex: Limitações de budget para marketing)\",\n"
        "      \"Vulnerabilidade 3 (ex: Dependência de poucos clientes)\"\n"
        "    ],\n"
        "    \"recomendacoes_estrategicas\": [\n"
        "      \"Recomendação estratégica 1 (ex: Focar em nichos específicos)\",\n"
        "      \"Recomendação estratégica 2 (ex: Desenvolver parcerias)\",\n"
        "      \"Recomendação estratégica 3 (ex: Investir em content marketing)\"\n"
        "    ]\n"
        "  },\n"
        "  \"fontes_informacao\": {\n"
        "    \"fontes_utilizadas\": [\n"
        "      \"Gartner Magic Quadrant\",\n"
        "      \"IDC Market Research\",\n"
        "      \"Crunchbase funding data\",\n"
        "      \"G2 product reviews\",\n"
        "      \"Relatórios setoriais específicos\"\n"
        "    ],\n"
        "    \"confiabilidade_dados\": \"Alta/Média/Baixa\",\n"
        "    \"data_ultima_atualizacao\": \"Data da última atualização dos dados\",\n"
        "    \"observacoes\": \"Observações sobre a metodologia de pesquisa\",\n"
        "    \"limitacoes_analise\": \"Limitações identificadas na análise de mercado\"\n"
        "  }\n"
        "}\n\n"
        "INSTRUÇÕES ESPECÍFICAS:\n"
        "1. Use dados reais de fontes confiáveis como Gartner, IDC, McKinsey\n"
        "2. Pesquise 5-8 concorrentes principais com dados específicos\n"
        "3. Calcule TAM/SAM/SOM com metodologia clara\n"
        "4. Identifique 3-5 tendências setoriais com evidências\n"
        "5. Se não encontrar dados específicos, use 'Dados não disponíveis'\n"
        "6. Foque em insights acionáveis e oportunidades estratégicas\n"
        "7. Documente metodologia e limitações da análise\n"
        "8. Inclua projeções de crescimento com horizonte temporal\n"
        "9. Analise gaps competitivos específicos do mercado brasileiro\n"
        "10. Identifique oportunidades de inovação e diferenciação"
    )

    return _fazer_requisicao_perplexity(api_key, system_content, user_content)


def _gerar_diagnostico_tecnico(api_key: str, empresa: str, site_str: str, dados_setor: Optional[str] = None) -> Dict[str, Any]:
    """
    Gera diagnóstico técnico abrangente usando Lighthouse, Playwright e análise visual por IA

    Esta função orquestra múltiplas ferramentas para criar um diagnóstico técnico completo:
    - Análise de performance com Google Lighthouse
    - Captura de screenshots com Playwright  
    - Análise visual por IA dos screenshots
    - Consolidação de resultados em relatório final

    Args:
        api_key: Chave da API Perplexity para análise por IA
        empresa: Nome da empresa analisada
        site_str: URL do site para análise
        dados_setor: Setor da empresa para contextualização (opcional)

    Returns:
        Dict com diagnóstico técnico completo ou erro
    """
    try:
        from tools.diagnostics import (
            LighthouseAnalyzer,
            PlaywrightScreenshots,
            VisualAnalyzer,
            DiagnosticConsolidator
        )

        # Detectar setor automaticamente se não fornecido
        if not dados_setor:
            setor_result = _detectar_setor_empresa(api_key, empresa, site_str)
            # Extrair setor do resultado (pode ser dict ou string)
            if isinstance(setor_result, dict) and 'setor' in setor_result:
                dados_setor = setor_result['setor']
            elif isinstance(setor_result, str):
                dados_setor = setor_result
            else:
                dados_setor = "Tecnologia"  # fallback padrão

                # Configurar analisadores
        lighthouse_analyzer = LighthouseAnalyzer()
        screenshot_tool = PlaywrightScreenshots()

        # VisualAnalyzer precisa da OPENAI_API_KEY, não da PERPLEXITY_API_KEY
        openai_api_key = env.OPENAI_API_KEY or "fallback-key"
        visual_analyzer = VisualAnalyzer(openai_api_key)

        consolidator = DiagnosticConsolidator()

        # ETAPA 1: Análise Lighthouse
        logger.info(f"Iniciando análise Lighthouse para {site_str}")
        lighthouse_data = lighthouse_analyzer.run_analysis(site_str)

        if not lighthouse_data or "erro" in lighthouse_data:
            logger.warning(f"Falha na análise Lighthouse: {lighthouse_data}")
            # Continuar com dados vazios para não bloquear análise visual
            lighthouse_data = _get_default_lighthouse_data()

        # ETAPA 2: Captura de Screenshots
        logger.info(f"Capturando screenshots para {site_str}")
        screenshots_data = screenshot_tool.capture_screenshots(site_str)

        if not screenshots_data or "erro" in screenshots_data:
            logger.warning(
                f"Falha na captura de screenshots: {screenshots_data}")
            screenshots_data = _get_default_screenshots_data()

        # ETAPA 3: Análise Visual por IA
        logger.info(f"Analisando screenshots por IA para {empresa}")

        # Contexto para análise visual COM DADOS DO LIGHTHOUSE
        context_data = {
            "empresa": empresa,
            "setor": dados_setor,
            "site": site_str,
            "lighthouse_data": lighthouse_data,  # Passar dados reais do Lighthouse
            "prompt_adicional": f"""
            Analise visualmente o website da empresa {empresa} (setor: {dados_setor}).
            
            Foque em:
            1. Qualidade do layout e hierarquia visual
            2. Experiência do usuário (UX) geral
            3. Design responsivo mobile vs desktop
            4. Elementos de confiança e profissionalismo
            5. Clareza da proposta de valor
            6. Facilidade de navegação
            
            Forneça insights específicos e acionáveis para melhorias.
            """
        }

        visual_analysis = visual_analyzer.analyze_screenshots(
            screenshots_data, context_data
        )

        if not visual_analysis or "erro" in visual_analysis:
            logger.warning(f"Falha na análise visual: {visual_analysis}")
            visual_analysis = _get_default_visual_analysis()

        # ETAPA 4: Consolidação do Relatório
        logger.info(f"Consolidando relatório técnico para {empresa}")

        relatorio_consolidado = consolidator.build_final_report(
            lighthouse_data=lighthouse_data,
            visual_analysis=visual_analysis,
            screenshots=screenshots_data
        )

        # ETAPA 5: Estruturar resultado final
        resultado = {
            "diagnostico_lighthouse": lighthouse_data,
            "analise_visual": {
                "screenshots": screenshots_data.get("screenshots", {}),
                "analise_ui_ux": visual_analysis.get("analise_ui_ux", {}),
                "recomendacoes_design": visual_analysis.get("recomendacoes_design", [])
            },
            "relatorio_consolidado": relatorio_consolidado,
            "metadata": {
                "empresa": empresa,
                "setor": dados_setor,
                "site_analisado": site_str,
                "data_analise": datetime.utcnow().isoformat(),
                "ferramentas_utilizadas": ["lighthouse", "playwright", "ia_visual"],
                "versao_diagnostico": "1.0"
            }
        }

        logger.info(f"Diagnóstico técnico concluído para {empresa}")

        # ETAPA 6: Notificar frontend via WebSocket sobre conclusão do diagnóstico
        try:
            # ✅ CORREÇÃO: Usar o websocket_manager global em vez do manager do main.py
            from shared.websocket_manager import websocket_manager
            import asyncio

            loop = None
            try:
                loop = asyncio.get_running_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            # Extrair score principal para notificação
            performance_score = lighthouse_data.get(
                "performance", {}).get("score", 0)

            notification_data = {
                "type": "lighthouse_analysis_complete",
                "empresa": empresa,
                "site": site_str,
                "performance_score": performance_score,
                "timestamp": datetime.utcnow().isoformat(),
                "message": f"Análise técnica concluída para {empresa} - Performance: {performance_score}%"
            }

            coro = websocket_manager.broadcast(notification_data)
            if loop.is_running():
                asyncio.run_coroutine_threadsafe(coro, loop)
            else:
                loop.run_until_complete(coro)

            print(
                f"✅ Frontend notificado via WebSocket sobre conclusão da análise de {empresa}")
        except Exception as e:
            print(
                f"⚠️ Erro ao notificar frontend via WebSocket: {e}", file=sys.stderr)

        return resultado

    except ImportError as e:
        erro_msg = f"Erro ao importar ferramentas de diagnóstico: {str(e)}"
        logger.error(erro_msg)
        return {
            "erro": erro_msg,
            "details": "Verifique se os módulos tools.diagnostics estão instalados corretamente"
        }

    except Exception as e:
        erro_msg = f"Erro ao gerar diagnóstico técnico para {empresa}: {str(e)}"
        logger.error(erro_msg)
        return {
            "erro": erro_msg,
            "empresa": empresa,
            "site": site_str
        }


def _get_default_lighthouse_data() -> Dict[str, Any]:
    """Retorna dados padrão para Lighthouse em caso de falha"""
    from ..shared.data_quality import DataQuality, wrap_with_quality
    
    fallback_lighthouse = {
        "performance": {
            "score": 0,
            "metricas_core": {"fcp": 0, "lcp": 0, "cls": 0, "fid": 0, "ttfb": 0},
            "oportunidades": ["Análise de performance não disponível - Lighthouse falhou"],
            "diagnosticos": ["Lighthouse não pôde ser executado"]
        },
        "acessibilidade": {
            "score": 0,
            "issues": ["Análise de acessibilidade não disponível - Lighthouse falhou"],
            "recomendacoes": ["Execute análise manual de acessibilidade"]
        },
        "seo_tecnico": {
            "score": 0,
            "meta_tags": "não analisado - Lighthouse falhou",
            "estrutura_html": "não analisado - Lighthouse falhou",
            "mobile_friendly": "não analisado - Lighthouse falhou"
        },
        "best_practices": {
            "score": 0,
            "https": False,
            "vulnerabilidades": ["Análise de segurança não disponível - Lighthouse falhou"]
        }
    }
    
    return wrap_with_quality(
        data=fallback_lighthouse,
        quality=DataQuality.ERROR_FALLBACK,
        source="lighthouse_default_fallback",
        confidence_score=0.0,
        fallback_reason="Lighthouse não pôde ser executado"
    )


def _get_default_screenshots_data() -> Dict[str, Any]:
    """Retorna dados padrão para screenshots em caso de falha"""
    from ..shared.data_quality import DataQuality, wrap_with_quality
    
    fallback_screenshots = {
        "screenshots": {
            "desktop": "",
            "mobile": "",
            "metadata": {
                "erro": "Screenshots não puderam ser capturados",
                "timestamp": datetime.utcnow().isoformat()
            }
        }
    }
    
    return wrap_with_quality(
        data=fallback_screenshots,
        quality=DataQuality.ERROR_FALLBACK,
        source="screenshots_default_fallback",
        confidence_score=0.0,
        fallback_reason="Screenshots não puderam ser capturados"
    )


def _get_default_visual_analysis() -> Dict[str, Any]:
    """Retorna dados padrão para análise visual em caso de falha"""
    from ..shared.data_quality import DataQuality, wrap_with_quality
    
    fallback_visual = {
        "analise_ui_ux": {
            "layout_quality": "não analisado - análise visual falhou",
            "user_experience": "não analisado - análise visual falhou",
            "visual_hierarchy": "não analisado - análise visual falhou",
            "responsive_design": "não analisado - análise visual falhou"
        },
        "recomendacoes_design": [
            "Análise visual manual recomendada - falha na análise automática",
            "Verificar responsividade em diferentes dispositivos",
            "Avaliar hierarquia visual e usabilidade"
        ]
    }
    
    return wrap_with_quality(
        data=fallback_visual,
        quality=DataQuality.ERROR_FALLBACK,
        source="visual_analysis_default_fallback",
        confidence_score=0.0,
        fallback_reason="Análise visual automática falhou"
    )


def gerar_relatorio_basico(client_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Gera relatório básico sem uso de IA pesada (para FASE 1 - cadastro rápido)

    Args:
        client_data: Dados básicos do cliente

    Returns:
        Relatório básico simples para inclusão imediata
    """
    try:
        now = datetime.now()

        # Extrair informações básicas
        nome = client_data.get("name", "")
        site = client_data.get("site", "")
        cidade = client_data.get("city", "")
        estado = client_data.get("state", "")
        setor = client_data.get("setor", "")

        # Gerar relatório básico estruturado
        relatorio_basico = {
            "informacoes_gerais": {
                "nome": nome,
                "descricao": f"Empresa {nome} localizada em {cidade}, {estado}.",
                "executivos": [
                    f"{client_data.get('responsible', 'Não informado')} - {client_data.get('responsibleRole', 'Cargo não informado')}"
                ] if client_data.get('responsible') else []
            },
            "historico": f"Empresa {nome} cadastrada no sistema em {now.strftime('%d/%m/%Y')}. Localizada em {cidade}, {estado}.",
            "produtos_servicos": [
                "Informações detalhadas serão obtidas no relatório completo"
            ],
            "concorrentes": {
                "principais": ["Análise competitiva será incluída no relatório completo"],
                "diferenciais_concorrentes": "Análise será incluída no relatório completo"
            },
            "localização": {
                "cidade": cidade,
                "estado": estado,
                "site": site if site else "Não informado"
            },
            "contatos": client_data.get("contacts", []),
            "setor_classificado": setor,
            "status_relatorio": "básico",
            "report_type": "basico_rapido",
            "version": "1.0",
            "generated_at": now.isoformat(),
            "nota": "Este é um relatório básico gerado automaticamente. Para análise completa e detalhada, solicite o relatório expandido."
        }

        logger.info(f"Relatório básico gerado para: {nome}")
        return relatorio_basico

    except Exception as e:
        logger.error(f"Erro ao gerar relatório básico: {str(e)}")
        return {
            "error": "Erro ao gerar relatório básico",
            "report_type": "basico_rapido",
            "generated_at": datetime.now().isoformat()
        }


def classificar_setor_simples(nome: str = "", site: str = "", cidade: str = "") -> str:
    """
    Classifica setor de forma simples e rápida sem uso de IA pesada (para FASE 1)

    Args:
        nome: Nome da empresa
        site: Site da empresa  
        cidade: Cidade da empresa

    Returns:
        Setor classificado de forma básica
    """
    try:
        # Classificação baseada em palavras-chave simples
        nome_lower = nome.lower() if nome else ""
        site_lower = site.lower() if site else ""

        # Dicionário de palavras-chave para setores
        setores_keywords = {
            "Tecnologia": [
                "software", "tech", "tecnologia", "sistema", "app", "digital", "dev",
                "data", "analytics", "ia", "artificial", "machine", "learning", "cloud",
                "cyber", "security", "fintech", "edtech", "saas", "api", "web"
            ],
            "Consultoria": [
                "consultoria", "consulting", "advisory", "estrategia", "gestao",
                "business", "management", "rh", "recursos", "humanos"
            ],
            "Marketing": [
                "marketing", "publicidade", "propaganda", "branding", "design",
                "grafico", "comunicacao", "midia", "social", "agencia"
            ],
            "Educação": [
                "educacao", "ensino", "escola", "universidade", "curso", "treinamento",
                "capacitacao", "learning", "education", "academy"
            ],
            "Saúde": [
                "saude", "medico", "clinica", "hospital", "medicina", "farmacia",
                "health", "medical", "wellness", "terapia"
            ],
            "Finanças": [
                "financeiro", "banco", "investment", "investimento", "seguro", "credito",
                "contabilidade", "fiscal", "finance", "capital"
            ],
            "E-commerce": [
                "ecommerce", "loja", "varejo", "marketplace", "vendas", "comercio",
                "retail", "shopping", "store"
            ],
            "Indústria": [
                "industria", "industrial", "manufatura", "fabrica", "producao",
                "manufacturing", "factory", "engenharia"
            ],
            "Serviços": [
                "servicos", "services", "atendimento", "suporte", "manutencao",
                "support", "assistance"
            ],
            "Logística": [
                "logistica", "transporte", "entrega", "delivery", "logistics",
                "transport", "distribuicao"
            ]
        }

        # Buscar correspondências
        texto_busca = f"{nome_lower} {site_lower}".strip()

        # Nova lógica com pontuação por especificidade
        matches = []

        for setor, keywords in setores_keywords.items():
            for keyword in keywords:
                if keyword in texto_busca:
                    # Calcular pontuação baseada em especificidade
                    # Palavras mais longas = mais específicas
                    score = len(keyword)
                    if keyword in nome_lower:  # Nome da empresa tem prioridade
                        score += 10

                    matches.append((setor, keyword, score))

        # Escolher melhor match baseado na pontuação
        if matches:
            best_match = max(matches, key=lambda x: x[2])
            setor, keyword, score = best_match
            logger.info(
                f"Setor '{setor}' classificado para {nome} baseado na palavra-chave '{keyword}' (score: {score})")
            return setor

        # Classificação por TLD do site
        if site_lower:
            if ".edu" in site_lower or ".org" in site_lower:
                return "Educação"
            elif ".gov" in site_lower:
                return "Governo"

        # Classificação por localização (heurísticas básicas)
        if cidade.lower() in ["são paulo", "rio de janeiro", "belo horizonte"]:
            # Grandes centros têm mais probabilidade de ser tecnologia/serviços
            return "Serviços"

        # Fallback para setor genérico
        logger.info(f"Setor não identificado para {nome}, usando 'Geral'")
        return "Geral"

    except Exception as e:
        logger.error(f"Erro ao classificar setor simples: {str(e)}")
        return "Não classificado"
