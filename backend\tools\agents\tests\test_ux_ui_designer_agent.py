"""
Testes para UX/UI Designer Agent

Testes abrangentes cobrindo:
- Análise de dados visuais
- Identificação de problemas UX/UI
- Geração de recomendações
- Integração com VisualAnalysisTools
- Diferentes cenários de qualidade visual
"""

import pytest
import logging
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, Any

from ..agents.ux_ui_designer_agent import UXUIDesignerAgent
from ..tools.visual_analysis_tools import VisualInsights


class TestUXUIDesignerAgent:
    """Testes para o agente UX/UI Designer"""

    @pytest.fixture
    def agent(self):
        """Fixture para criar instância do agente"""
        with patch('backend.tools.agents.agents.ux_ui_designer_agent.Agent'):
            with patch('backend.tools.agents.agents.ux_ui_designer_agent.VisualAnalysisTools') as mock_tools:
                agent = UXUIDesignerAgent()
                agent.visual_tools = mock_tools.return_value
                return agent

    @pytest.fixture
    def sample_visual_analysis(self):
        """Dados de exemplo para análise visual"""
        return {
            "analise_ui_ux": {
                "layout_quality": "bom",
                "user_experience": "regular",
                "responsive_design": "ruim",
                "visual_hierarchy": "médio"
            },
            "screenshots_urls": [
                "https://example.com/screenshot1.png",
                "https://example.com/screenshot2.png"
            ],
            "recomendacoes_design": [
                "Melhorar contraste de cores",
                "Otimizar layout para mobile"
            ],
            "issues": [
                "Layout inconsistente",
                "Navegação confusa"
            ]
        }

    @pytest.fixture
    def sample_lighthouse_data(self):
        """Dados de exemplo do Lighthouse"""
        return {
            "performance": {"score": 0.75},
            "accessibility": {"score": 0.85}
        }

    @pytest.fixture
    def sample_company_context(self):
        """Contexto de exemplo da empresa"""
        return {
            "name": "Empresa Teste",
            "sector": "E-commerce",
            "website": "https://exemplo.com"
        }

    @pytest.fixture
    def mock_visual_insights(self):
        """Mock para VisualInsights"""
        return VisualInsights(
            layout_quality="bom",
            user_experience="regular",
            responsive_design="ruim",
            visual_hierarchy="médio",
            ui_issues=["Layout inconsistente", "Cores inadequadas"],
            ux_issues=["Navegação confusa", "Fluxo interrompido"],
            design_recommendations=[
                "Melhorar contraste", "Simplificar navegação"],
            overall_visual_score=65.0,
            accessibility_visual_score=70.0
        )

    def test_agent_initialization(self, agent):
        """Testa inicialização do agente"""
        assert agent is not None
        assert hasattr(agent, 'visual_tools')
        assert hasattr(agent, 'agent')

    @patch('backend.tools.agents.agents.ux_ui_designer_agent.VisualAnalysisTools')
    def test_analyze_ux_ui_design_basic(self, mock_visual_tools, agent, sample_visual_analysis):
        """Testa análise básica de UX/UI"""
        # Mock das ferramentas visuais
        mock_visual_insights = Mock()
        mock_visual_insights.layout_quality = "bom"
        mock_visual_insights.user_experience = "regular"
        mock_visual_insights.responsive_design = "ruim"
        mock_visual_insights.visual_hierarchy = "médio"
        mock_visual_insights.ui_issues = ["Layout inconsistente"]
        mock_visual_insights.ux_issues = ["Navegação confusa"]
        mock_visual_insights.overall_visual_score = 65.0
        mock_visual_insights.accessibility_visual_score = 70.0
        mock_visual_insights.model_dump.return_value = {
            "layout_quality": "bom",
            "user_experience": "regular",
            "responsive_design": "ruim",
            "overall_visual_score": 65.0
        }

        agent.visual_tools.analyze_visual_data.return_value = mock_visual_insights
        agent.visual_tools.get_visual_priorities.return_value = [
            "Melhorar responsividade"]
        agent.visual_tools.assess_mobile_readiness.return_value = {
            "assessment": "Necessita melhorias"}

        # Mock do agente Agno
        mock_response = Mock()
        mock_response.content = "Análise detalhada de UX/UI com recomendações específicas"
        agent.agent.run.return_value = mock_response

        # Executar análise
        result = agent.analyze_ux_ui_design(sample_visual_analysis)

        # Verificações
        assert result is not None
        assert result["agent_type"] == "UXUIDesignerAgent"
        assert result["overall_visual_score"] == 65.0
        assert result["ui_issues_count"] == 1
        assert result["ux_issues_count"] == 1
        assert "design_category" in result
        assert "analysis_summary" in result

    @patch('backend.tools.agents.agents.ux_ui_designer_agent.VisualAnalysisTools')
    def test_analyze_with_lighthouse_context(self, mock_visual_tools, agent, sample_visual_analysis, sample_lighthouse_data, sample_company_context):
        """Testa análise com contexto do Lighthouse"""
        # Setup mocks
        mock_visual_insights = Mock()
        mock_visual_insights.layout_quality = "excelente"
        mock_visual_insights.user_experience = "bom"
        mock_visual_insights.responsive_design = "bom"
        mock_visual_insights.visual_hierarchy = "excelente"
        mock_visual_insights.ui_issues = []
        mock_visual_insights.ux_issues = []
        mock_visual_insights.overall_visual_score = 90.0
        mock_visual_insights.accessibility_visual_score = 85.0
        mock_visual_insights.model_dump.return_value = {
            "overall_visual_score": 90.0}

        agent.visual_tools.analyze_visual_data.return_value = mock_visual_insights
        agent.visual_tools.get_visual_priorities.return_value = []
        agent.visual_tools.assess_mobile_readiness.return_value = {
            "assessment": "Excelente"}

        mock_response = Mock()
        mock_response.content = "Análise com contexto Lighthouse"
        agent.agent.run.return_value = mock_response

        # Executar análise
        result = agent.analyze_ux_ui_design(
            sample_visual_analysis,
            lighthouse_data=sample_lighthouse_data,
            company_context=sample_company_context
        )

        # Verificações
        assert result["overall_visual_score"] == 90.0
        assert result["design_category"] == "Excelente - Design maduro"

        # Verificar se o prompt incluiu dados do Lighthouse
        call_args = agent.agent.run.call_args[0][0]
        assert "Performance Score: 75/100" in call_args
        assert "Accessibility Score: 85/100" in call_args
        assert "Empresa Teste" in call_args
        assert "E-commerce" in call_args

    def test_categorize_design_maturity(self, agent):
        """Testa categorização da maturidade do design"""
        assert agent._categorize_design_maturity(
            95.0) == "Excelente - Design maduro"
        assert agent._categorize_design_maturity(
            80.0) == "Bom - Design sólido com oportunidades"
        assert agent._categorize_design_maturity(
            65.0) == "Regular - Necessita melhorias"
        assert agent._categorize_design_maturity(
            45.0) == "Ruim - Problemas significativos"
        assert agent._categorize_design_maturity(
            25.0) == "Crítico - Redesign necessário"

    def test_get_design_recommendations_summary(self, agent):
        """Testa extração de resumo de recomendações"""
        analysis_result = {
            "visual_insights": {
                "ui_issues": ["Layout de baixa qualidade", "Cores inadequadas"],
                "ux_issues": ["Experiência do usuário prejudicada", "Navegação confusa"],
                "overall_visual_score": 45.0
            }
        }

        recommendations = agent.get_design_recommendations_summary(
            analysis_result)

        assert len(recommendations) == 5  # 2 UI + 2 UX + 1 Strategic

        # Verificar tipos de recomendações
        types = [rec["type"] for rec in recommendations]
        assert "UI_Issue" in types
        assert "UX_Issue" in types
        assert "Strategic" in types

        # Verificar categorias
        categories = [rec["category"] for rec in recommendations]
        assert "Interface Design" in categories
        assert "User Experience" in categories
        assert "Design Strategy" in categories

    @patch('backend.tools.agents.agents.ux_ui_designer_agent.VisualAnalysisTools')
    def test_error_handling(self, mock_visual_tools, agent, sample_visual_analysis):
        """Testa tratamento de erros"""
        # Simular erro nas ferramentas visuais
        agent.visual_tools.analyze_visual_data.side_effect = Exception(
            "Erro de análise")

        # Verificar se a exceção é propagada
        with pytest.raises(Exception, match="Erro de análise"):
            agent.analyze_ux_ui_design(sample_visual_analysis)

    @patch('backend.tools.agents.agents.ux_ui_designer_agent.VisualAnalysisTools')
    def test_empty_visual_data(self, mock_visual_tools, agent):
        """Testa análise com dados visuais vazios"""
        empty_data = {}

        mock_visual_insights = Mock()
        mock_visual_insights.layout_quality = "Não avaliado"
        mock_visual_insights.user_experience = "Não avaliado"
        mock_visual_insights.responsive_design = "Não avaliado"
        mock_visual_insights.visual_hierarchy = None
        mock_visual_insights.ui_issues = []
        mock_visual_insights.ux_issues = []
        mock_visual_insights.overall_visual_score = 50.0
        mock_visual_insights.accessibility_visual_score = 50.0
        mock_visual_insights.model_dump.return_value = {
            "overall_visual_score": 50.0}

        agent.visual_tools.analyze_visual_data.return_value = mock_visual_insights
        agent.visual_tools.get_visual_priorities.return_value = []
        agent.visual_tools.assess_mobile_readiness.return_value = {
            "assessment": "Não avaliado"}

        mock_response = Mock()
        mock_response.content = "Análise com dados limitados"
        agent.agent.run.return_value = mock_response

        result = agent.analyze_ux_ui_design(empty_data)

        assert result["overall_visual_score"] == 50.0
        assert result["ui_issues_count"] == 0
        assert result["ux_issues_count"] == 0

    @patch('backend.tools.agents.agents.ux_ui_designer_agent.VisualAnalysisTools')
    def test_high_quality_design_analysis(self, mock_visual_tools, agent, sample_visual_analysis):
        """Testa análise de design de alta qualidade"""
        mock_visual_insights = Mock()
        mock_visual_insights.layout_quality = "excelente"
        mock_visual_insights.user_experience = "excelente"
        mock_visual_insights.responsive_design = "excelente"
        mock_visual_insights.visual_hierarchy = "excelente"
        mock_visual_insights.ui_issues = []
        mock_visual_insights.ux_issues = []
        mock_visual_insights.overall_visual_score = 95.0
        mock_visual_insights.accessibility_visual_score = 90.0
        mock_visual_insights.model_dump.return_value = {
            "overall_visual_score": 95.0}

        agent.visual_tools.analyze_visual_data.return_value = mock_visual_insights
        agent.visual_tools.get_visual_priorities.return_value = []
        agent.visual_tools.assess_mobile_readiness.return_value = {
            "assessment": "Excelente"}

        mock_response = Mock()
        mock_response.content = "Design de alta qualidade identificado"
        agent.agent.run.return_value = mock_response

        result = agent.analyze_ux_ui_design(sample_visual_analysis)

        assert result["overall_visual_score"] == 95.0
        assert result["design_category"] == "Excelente - Design maduro"
        assert result["ui_issues_count"] == 0
        assert result["ux_issues_count"] == 0

    def test_get_design_recommendations_summary_error_handling(self, agent):
        """Testa tratamento de erro na extração de recomendações"""
        # Dados vazios - o método deve processar o que conseguir
        empty_result = {}

        recommendations = agent.get_design_recommendations_summary(
            empty_result)

        # Com dados vazios, deve retornar apenas recomendação estratégica baseada no score 0
        assert len(recommendations) == 1
        assert recommendations[0]["type"] == "Strategic"
        assert recommendations[0]["category"] == "Design Strategy"

    @patch('backend.tools.agents.agents.ux_ui_designer_agent.VisualAnalysisTools')
    def test_mobile_readiness_integration(self, mock_visual_tools, agent, sample_visual_analysis):
        """Testa integração com avaliação de mobile readiness"""
        mock_visual_insights = Mock()
        mock_visual_insights.layout_quality = "bom"
        mock_visual_insights.user_experience = "bom"
        mock_visual_insights.responsive_design = "ruim"
        mock_visual_insights.visual_hierarchy = "bom"
        mock_visual_insights.ui_issues = ["Design não responsivo"]
        mock_visual_insights.ux_issues = []
        mock_visual_insights.overall_visual_score = 70.0
        mock_visual_insights.accessibility_visual_score = 75.0
        mock_visual_insights.model_dump.return_value = {
            "overall_visual_score": 70.0}

        agent.visual_tools.analyze_visual_data.return_value = mock_visual_insights
        agent.visual_tools.get_visual_priorities.return_value = [
            "Melhorar responsividade"]
        agent.visual_tools.assess_mobile_readiness.return_value = {
            "assessment": "Necessita melhorias significativas para mobile"
        }

        mock_response = Mock()
        mock_response.content = "Análise focada em mobile readiness"
        agent.agent.run.return_value = mock_response

        result = agent.analyze_ux_ui_design(sample_visual_analysis)

        # Verificar se mobile readiness foi incluído
        assert "mobile_readiness" in result
        assert result["mobile_readiness"]["assessment"] == "Necessita melhorias significativas para mobile"

        # Verificar se o prompt incluiu informações de mobile
        call_args = agent.agent.run.call_args[0][0]
        assert "MOBILE READINESS:" in call_args
        assert "Necessita melhorias significativas para mobile" in call_args
