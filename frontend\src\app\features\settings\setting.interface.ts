export interface ISettingsSection {
	id: string;
	title: string;
	icon: string;
	description: string;
	order: number;
}

export interface ISettingItem {
	id: string;
	sectionId: string;
	label: string;
	description?: string;
	type: 'text' | 'number' | 'boolean' | 'select' | 'color' | 'date' | 'email';
	key: string;
	value: string | number | boolean | null;
	options?: Array<ISettingOption>;
	required: boolean;
	validation?: string;
	validationMessage?: string;
	placeholder?: string;
	info?: string;
	order: number;
}

export interface ISettingOption {
	label: string;
	value: string | number | boolean;
	description?: string;
}

export interface IUserPreference {
	id: string;
	userId: number;
	settingKey: string;
	settingValue: string;
	lastUpdated: string;
}

export interface IUserProfile {
	id: number;
	name: string;
	email: string;
	avatar?: string;
	role: string;
	department?: string;
	position?: string;
	phone?: string;
	lastLogin: string;
	active: boolean;
	preferences: Array<IUserPreference>;
}

export interface ISortOption {
	label: string;
	value: string;
}

export interface IPageChangeEvent {
	first?: number;
	rows?: number;
	page?: number;
	pageCount?: number;
}

export interface INotificationSetting {
	id: string;
	type: string;
	description: string;
	email: boolean;
	push: boolean;
	inApp: boolean;
}
