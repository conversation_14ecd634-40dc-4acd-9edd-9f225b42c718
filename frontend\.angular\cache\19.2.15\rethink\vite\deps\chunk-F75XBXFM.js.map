{"version": 3, "sources": ["../../../../../../node_modules/mermaid/dist/chunks/mermaid.core/chunk-RZ5BOZE2.mjs"], "sourcesContent": ["import { __name, configureSvgSize, log } from \"./chunk-YTJNT7DU.mjs\";\n\n// src/rendering-util/insertElementsForSize.js\nimport { select } from \"d3\";\nvar getDiagramElement = /* @__PURE__ */__name((id, securityLevel) => {\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = select(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? select(sandboxElement.nodes()[0].contentDocument.body) : select(\"body\");\n  const svg = root.select(`[id=\"${id}\"]`);\n  return svg;\n}, \"getDiagramElement\");\n\n// src/rendering-util/setupViewPortForSVG.ts\nvar setupViewPortForSVG = /* @__PURE__ */__name((svg, padding, cssDiagram, useMaxWidth) => {\n  svg.attr(\"class\", cssDiagram);\n  const {\n    width,\n    height,\n    x,\n    y\n  } = calculateDimensionsWithPadding(svg, padding);\n  configureSvgSize(svg, height, width, useMaxWidth);\n  const viewBox = createViewBox(x, y, width, height, padding);\n  svg.attr(\"viewBox\", viewBox);\n  log.debug(`viewBox configured: ${viewBox} with padding: ${padding}`);\n}, \"setupViewPortForSVG\");\nvar calculateDimensionsWithPadding = /* @__PURE__ */__name((svg, padding) => {\n  const bounds = svg.node()?.getBBox() || {\n    width: 0,\n    height: 0,\n    x: 0,\n    y: 0\n  };\n  return {\n    width: bounds.width + padding * 2,\n    height: bounds.height + padding * 2,\n    x: bounds.x,\n    y: bounds.y\n  };\n}, \"calculateDimensionsWithPadding\");\nvar createViewBox = /* @__PURE__ */__name((x, y, width, height, padding) => {\n  return `${x - padding} ${y - padding} ${width} ${height}`;\n}, \"createViewBox\");\nexport { getDiagramElement, setupViewPortForSVG };"], "mappings": ";;;;;;;;AAIA,IAAI,oBAAmC,OAAO,CAAC,IAAI,kBAAkB;AACnE,MAAI;AACJ,MAAI,kBAAkB,WAAW;AAC/B,qBAAiB,eAAO,OAAO,EAAE;AAAA,EACnC;AACA,QAAM,OAAO,kBAAkB,YAAY,eAAO,eAAe,MAAM,EAAE,CAAC,EAAE,gBAAgB,IAAI,IAAI,eAAO,MAAM;AACjH,QAAM,MAAM,KAAK,OAAO,QAAQ,EAAE,IAAI;AACtC,SAAO;AACT,GAAG,mBAAmB;AAGtB,IAAI,sBAAqC,OAAO,CAAC,KAAK,SAAS,YAAY,gBAAgB;AACzF,MAAI,KAAK,SAAS,UAAU;AAC5B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,+BAA+B,KAAK,OAAO;AAC/C,mBAAiB,KAAK,QAAQ,OAAO,WAAW;AAChD,QAAM,UAAU,cAAc,GAAG,GAAG,OAAO,QAAQ,OAAO;AAC1D,MAAI,KAAK,WAAW,OAAO;AAC3B,MAAI,MAAM,uBAAuB,OAAO,kBAAkB,OAAO,EAAE;AACrE,GAAG,qBAAqB;AACxB,IAAI,iCAAgD,OAAO,CAAC,KAAK,YAAY;AAC3E,QAAM,SAAS,IAAI,KAAK,GAAG,QAAQ,KAAK;AAAA,IACtC,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACA,SAAO;AAAA,IACL,OAAO,OAAO,QAAQ,UAAU;AAAA,IAChC,QAAQ,OAAO,SAAS,UAAU;AAAA,IAClC,GAAG,OAAO;AAAA,IACV,GAAG,OAAO;AAAA,EACZ;AACF,GAAG,gCAAgC;AACnC,IAAI,gBAA+B,OAAO,CAAC,GAAG,GAAG,OAAO,QAAQ,YAAY;AAC1E,SAAO,GAAG,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,KAAK,IAAI,MAAM;AACzD,GAAG,eAAe;", "names": []}