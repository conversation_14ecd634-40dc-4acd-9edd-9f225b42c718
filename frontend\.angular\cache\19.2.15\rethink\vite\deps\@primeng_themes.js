import {
  $dt,
  $t,
  config_default,
  css,
  definePreset,
  dt,
  dtwt,
  getComputedValue,
  getRule,
  getVariableName,
  getVariableValue,
  hasOddBraces,
  merge,
  mix_default,
  palette_default,
  service_default,
  setProperty,
  shade_default,
  themeUtils_default,
  tint_default,
  toNormalizePrefix,
  toNormalizeVariable,
  toTokenKey,
  toUnit,
  toValue,
  toVariables_default,
  updatePreset,
  updatePrimaryPalette,
  updateSurfacePalette,
  usePreset,
  useTheme
} from "./chunk-CWYBPNCF.js";
import "./chunk-NJ25EVEJ.js";
import "./chunk-SERTD5K6.js";
export {
  $dt,
  $t,
  config_default as Theme,
  service_default as ThemeService,
  themeUtils_default as ThemeUtils,
  css,
  definePreset,
  dt,
  dtwt,
  getComputedValue,
  getRule,
  getVariableName,
  getVariableValue,
  hasOddBraces,
  merge,
  mix_default as mix,
  palette_default as palette,
  setProperty,
  shade_default as shade,
  tint_default as tint,
  toNormalizePrefix,
  toNormalizeVariable,
  toTokenKey,
  toUnit,
  toValue,
  toVariables_default as toVariables,
  updatePreset,
  updatePrimaryPalette,
  updateSurfacePalette,
  usePreset,
  useTheme
};
