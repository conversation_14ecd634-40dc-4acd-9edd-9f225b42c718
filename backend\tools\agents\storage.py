"""
Agent Storage - Persistência MongoDB para Análises de Agentes

Sistema de armazenamento para resultados de análises do Time Agno:
- Salvar resultados de análises arquiteturais
- Buscar análises por empresa/projeto
- Histórico de análises e evolução
- Métricas de performance do sistema
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Union, TYPE_CHECKING
from datetime import datetime, timezone
from bson import ObjectId
import pymongo

from config.settings import env

# Type checking imports - apenas para tipos, não runtime
if TYPE_CHECKING:
    try:
        from .schemas import AnalysisResult, ArchitecturalRecommendation, TechnicalRecommendation
    except ImportError:
        # Fallback types para TYPE_CHECKING quando schemas não disponíveis
        AnalysisResult = Dict[str, Any]
        ArchitecturalRecommendation = Dict[str, Any]
        TechnicalRecommendation = Dict[str, Any]
else:
    # Para runtime, usar Any como alias de tipo
    AnalysisResult = Any
    ArchitecturalRecommendation = Any
    TechnicalRecommendation = Any

logger = logging.getLogger(__name__)

# Aliases de tipos para uso consistente
AnalysisResultType = Union[Dict[str, Any], Any]
ArchitecturalRecommendationType = Union[Dict[str, Any], Any]
TechnicalRecommendationType = Union[Dict[str, Any], Any]

# Definir classes de exceção base


class StorageError(Exception):
    """Exceção base para erros de storage"""
    pass


class StorageConnectionError(StorageError):
    """Erro de conexão com storage"""
    pass


class StorageDuplicateError(StorageError):
    """Erro de chave duplicada"""
    pass


# Verificar disponibilidade do MongoDB
MONGODB_AVAILABLE = False
MongoClient = None
AsyncIOMotorClient = None

try:
    from pymongo import MongoClient
    from motor.motor_asyncio import AsyncIOMotorClient
    import pymongo.errors
    MONGODB_AVAILABLE = True

    # Usar as exceções reais quando disponível
    _PyMongoError = pymongo.errors.PyMongoError
    _ConnectionFailure = pymongo.errors.ConnectionFailure
    _DuplicateKeyError = pymongo.errors.DuplicateKeyError

except ImportError:
    logger.warning("Dependências MongoDB não disponíveis - modo simulação")

    # Usar as stubs quando MongoDB não disponível
    _PyMongoError = StorageError
    _ConnectionFailure = StorageConnectionError
    _DuplicateKeyError = StorageDuplicateError

# Importar schemas em runtime com fallback para Any
_AnalysisResultClass: Any = None
_ArchitecturalRecommendationClass: Any = None
_TechnicalRecommendationClass: Any = None

try:
    from .schemas import AnalysisResult as _AnalysisResultClass
    from .schemas import ArchitecturalRecommendation as _ArchitecturalRecommendationClass
    from .schemas import TechnicalRecommendation as _TechnicalRecommendationClass
    SCHEMAS_AVAILABLE = True
except ImportError as e:
    logger.error(f"Erro ao importar schemas: {e}")
    # Usar Dict como fallback em runtime
    _AnalysisResultClass = dict
    _ArchitecturalRecommendationClass = dict
    _TechnicalRecommendationClass = dict
    SCHEMAS_AVAILABLE = False


class AgentStorage:
    """
    Gerenciador de Storage MongoDB para análises de agentes

    Responsabilidades:
    - Persistir resultados de análises arquiteturais
    - Gerenciar coleções de agentes e análises
    - Fornecer interface assíncrona para consultas
    - Manter histórico de análises por empresa
    """

    def __init__(self):
        """Inicializa conexão com MongoDB"""
        if not MONGODB_AVAILABLE:
            logger.warning("MongoDB não disponível. Usando modo de simulação.")
            self.mongodb_available = False
            self.connection_uri: Optional[str] = None
            self.database_name: Optional[str] = None
            self.client: Optional[Any] = None
            self.db: Optional[Any] = None
            return

        self.mongodb_available = True

        # Configurações do MongoDB
        mongodb_uri = getattr(env, 'MONGODB_ATLAS_CONNECTION_URI', None)
        if not mongodb_uri:
            logger.warning(
                "MONGODB_ATLAS_CONNECTION_URI não configurado - usando modo simulação")
            self.mongodb_available = False
            self.connection_uri = None
            self.database_name = None
            self.client = None
            self.db = None
            return

        self.connection_uri = mongodb_uri
        self.database_name = getattr(env, 'MONGODB_DATABASE_NAME', 'scope_ai')
        self.client: Optional[Any] = None
        self.db: Optional[Any] = None

        # Coleções do sistema de agentes
        self.collections = {
            "analyses": "agent_analyses",
            "recommendations": "agent_recommendations",
            "performance_metrics": "agent_performance",
            "companies": "analyzed_companies"
        }

        logger.info("Agent Storage inicializado")

    async def connect(self) -> None:
        """Estabelece conexão com MongoDB"""
        if not self.mongodb_available:
            logger.info("Simulando conexão MongoDB")
            return

        if not self.connection_uri:
            logger.error("URI de conexão MongoDB não disponível")
            return

        try:
            # Criar cliente com tipo correto
            if AsyncIOMotorClient is not None:
                self.client = AsyncIOMotorClient(self.connection_uri)
            else:
                logger.error("AsyncIOMotorClient não disponível")
                return

            # Testar conexão
            if self.client is not None:
                await self.client.admin.command('ping')

                # Definir database
                if self.database_name:
                    self.db = self.client[self.database_name]

                # Configurar índices
                await self._setup_indexes()

                logger.info(f"Conectado ao MongoDB: {self.database_name}")
            else:
                logger.error("Cliente MongoDB não foi inicializado")

        except Exception as e:
            logger.error(f"Erro ao conectar com MongoDB: {str(e)}")
            self.client = None
            self.db = None
            raise Exception(f"Falha na conexão MongoDB: {str(e)}")

    async def disconnect(self) -> None:
        """Fecha conexão com MongoDB"""
        if self.client and self.mongodb_available:
            self.client.close()
            logger.info("Conexão MongoDB fechada")

    async def _setup_indexes(self) -> None:
        """Configura índices para otimização de consultas"""
        if not self.mongodb_available or self.db is None:
            return

        try:
            # Índices para análises
            analyses_collection = self.db[self.collections["analyses"]]
            await analyses_collection.create_index([
                ("analysis_id", pymongo.ASCENDING)
            ], unique=True)
            await analyses_collection.create_index([
                ("company_info.name", pymongo.ASCENDING),
                ("created_at", pymongo.DESCENDING)
            ])
            await analyses_collection.create_index([
                ("recommendations.current_overall_score", pymongo.DESCENDING)
            ])

            # Índices para empresas
            companies_collection = self.db[self.collections["companies"]]
            await companies_collection.create_index([
                ("name", pymongo.ASCENDING),
                ("website", pymongo.ASCENDING)
            ], unique=True)

            # Índices para métricas de performance
            metrics_collection = self.db[self.collections["performance_metrics"]]
            await metrics_collection.create_index([
                ("timestamp", pymongo.DESCENDING)
            ])

            logger.info("Índices MongoDB configurados")

        except Exception as e:
            logger.error(f"Erro ao configurar índices: {str(e)}")

    async def save_analysis_result(self, analysis_result: AnalysisResultType) -> str:
        """
        Salva resultado de análise no MongoDB

        Args:
            analysis_result: Resultado da análise para salvar

        Returns:
            ID do documento salvo no MongoDB
        """
        if not self.mongodb_available:
            logger.info(
                f"Simulando salvamento da análise: {getattr(analysis_result, 'analysis_id', 'unknown')}")
            return f"simulated_{getattr(analysis_result, 'analysis_id', 'unknown')}"

        if self.db is None:
            logger.error("Database não inicializado")
            return f"error_{getattr(analysis_result, 'analysis_id', 'unknown')}"

        try:
            # Preparar documento para MongoDB
            document = self._prepare_analysis_document(analysis_result)

            # Salvar na coleção de análises
            collection = self.db[self.collections["analyses"]]
            result = await collection.insert_one(document)

            # Atualizar informações da empresa
            company_info = getattr(analysis_result, 'company_info', {})
            await self._update_company_info(company_info)

            # Registrar métricas de performance
            await self._record_performance_metrics(analysis_result)

            logger.info(
                f"Análise salva: {getattr(analysis_result, 'analysis_id', 'unknown')} -> {result.inserted_id}")
            return str(result.inserted_id)

        except Exception as e:
            analysis_id = getattr(analysis_result, 'analysis_id', 'unknown')
            if "duplicate key" in str(e).lower() or "duplicatekey" in str(e).lower():
                logger.warning(
                    f"Análise {analysis_id} já existe")
                raise ValueError(
                    f"Análise com ID {analysis_id} já existe")

            logger.error(f"Erro ao salvar análise: {str(e)}")
            raise Exception(f"Falha ao salvar análise no MongoDB: {str(e)}")

    def _prepare_analysis_document(self, analysis_result: AnalysisResultType) -> Dict[str, Any]:
        """
        Prepara documento para inserção no MongoDB

        Args:
            analysis_result: Resultado da análise

        Returns:
            Documento formatado para MongoDB
        """
        # Converter resultado para dicionário
        if SCHEMAS_AVAILABLE and hasattr(analysis_result, 'model_dump'):
            # Type guard: se tem model_dump, assumir que é um modelo Pydantic
            doc = analysis_result.model_dump()  # type: ignore
        else:
            # Fallback para dict comum
            doc = dict(analysis_result) if hasattr(
                analysis_result, 'items') else {}

        # Adicionar metadados MongoDB
        doc["_created_at"] = datetime.now(timezone.utc)
        doc["_document_version"] = "1.0"
        doc["_agent_system"] = "agno"

        # Processar recomendações técnicas para busca otimizada
        recommendations = getattr(analysis_result, 'recommendations', None)
        if recommendations and hasattr(recommendations, 'technical_recommendations'):
            tech_recs = getattr(
                recommendations, 'technical_recommendations', [])
            if tech_recs:
                doc["_search_categories"] = list(set([
                    getattr(rec, 'category', {}).get(
                        'value', str(getattr(rec, 'category', '')))
                    for rec in tech_recs
                ]))
                doc["_priority_levels"] = list(set([
                    getattr(rec, 'priority', {}).get(
                        'value', str(getattr(rec, 'priority', '')))
                    for rec in tech_recs
                ]))
                doc["_total_recommendations"] = len(tech_recs)

        return doc

    async def _update_company_info(self, company_info: Dict[str, Any]) -> None:
        """Atualiza informações da empresa na coleção de empresas"""
        if not self.mongodb_available or self.db is None:
            return

        try:
            collection = self.db[self.collections["companies"]]

            # Buscar empresa existente
            query = {"name": company_info.get("name", "")}
            existing = await collection.find_one(query)

            update_doc = {
                "$set": {
                    "last_analysis": datetime.now(timezone.utc),
                    "sector": company_info.get("sector"),
                    "website": company_info.get("website"),
                    "size": company_info.get("size")
                },
                "$inc": {"analysis_count": 1}
            }

            if existing:
                await collection.update_one(query, update_doc)
            else:
                # Criar nova entrada
                new_company = {
                    "name": company_info.get("name", ""),
                    "sector": company_info.get("sector"),
                    "website": company_info.get("website"),
                    "size": company_info.get("size"),
                    "first_analysis": datetime.now(timezone.utc),
                    "last_analysis": datetime.now(timezone.utc),
                    "analysis_count": 1
                }
                await collection.insert_one(new_company)

        except Exception as e:
            logger.error(f"Erro ao atualizar empresa: {str(e)}")

    async def _record_performance_metrics(self, analysis_result: AnalysisResultType) -> None:
        """Registra métricas de performance da análise"""
        if not self.mongodb_available or self.db is None:
            return

        try:
            collection = self.db[self.collections["performance_metrics"]]

            # Extrair dados com fallback seguro
            analysis_id = getattr(analysis_result, 'analysis_id', 'unknown')
            confidence_score = getattr(
                analysis_result, 'confidence_score', 0.0)
            agent_version = getattr(
                analysis_result, 'agent_version', 'unknown')

            recommendations = getattr(analysis_result, 'recommendations', None)
            overall_score = getattr(
                recommendations, 'current_overall_score', 0.0) if recommendations else 0.0
            tech_recs = getattr(recommendations, 'technical_recommendations', [
            ]) if recommendations else []

            company_info = getattr(analysis_result, 'company_info', {})
            company_sector = company_info.get(
                'sector') if isinstance(company_info, dict) else None

            analysis_metadata = getattr(
                analysis_result, 'analysis_metadata', {})

            metrics_doc = {
                "analysis_id": analysis_id,
                "timestamp": datetime.now(timezone.utc),
                "confidence_score": confidence_score,
                "agent_version": agent_version,
                "overall_score": overall_score,
                "recommendations_count": len(tech_recs),
                "company_sector": company_sector,
                "processing_metadata": analysis_metadata
            }

            await collection.insert_one(metrics_doc)

        except Exception as e:
            logger.error(f"Erro ao registrar métricas: {str(e)}")

    async def get_analysis_by_id(self, analysis_id: str) -> Optional[AnalysisResultType]:
        """
        Busca análise por ID

        Args:
            analysis_id: ID da análise

        Returns:
            Resultado da análise ou None se não encontrada
        """
        if not self.mongodb_available:
            logger.info(f"Simulando busca da análise: {analysis_id}")
            return None

        if self.db is None:
            logger.error("Database não inicializado")
            return None

        try:
            collection = self.db[self.collections["analyses"]]
            document = await collection.find_one({"analysis_id": analysis_id})

            if document:
                # Remover campos internos MongoDB
                document.pop("_id", None)
                document.pop("_created_at", None)
                document.pop("_document_version", None)
                document.pop("_agent_system", None)
                document.pop("_search_categories", None)
                document.pop("_priority_levels", None)
                document.pop("_total_recommendations", None)

                # Criar objeto usando a classe disponível
                if SCHEMAS_AVAILABLE and _AnalysisResultClass:
                    return _AnalysisResultClass(**document)
                else:
                    # Retornar como dict quando schemas não disponíveis
                    return document  # type: ignore

            return None

        except Exception as e:
            logger.error(f"Erro ao buscar análise {analysis_id}: {str(e)}")
            raise

    async def list_analyses_by_company(self, company_name: str, limit: int = 10) -> List[AnalysisResultType]:
        """
        Lista análises de uma empresa

        Args:
            company_name: Nome da empresa
            limit: Máximo de resultados

        Returns:
            Lista de análises da empresa
        """
        if not self.mongodb_available:
            logger.info(f"Simulando busca de análises para: {company_name}")
            return []

        if self.db is None:
            logger.error("Database não inicializado")
            return []

        try:
            collection = self.db[self.collections["analyses"]]
            cursor = collection.find(
                {"company_info.name": company_name}
            ).sort("created_at", -1).limit(limit)

            analyses = []
            async for document in cursor:
                # Limpar campos MongoDB internos
                document.pop("_id", None)
                document.pop("_created_at", None)
                document.pop("_document_version", None)
                document.pop("_agent_system", None)
                document.pop("_search_categories", None)
                document.pop("_priority_levels", None)
                document.pop("_total_recommendations", None)

                # Criar objeto usando a classe disponível
                if SCHEMAS_AVAILABLE and _AnalysisResultClass:
                    analyses.append(_AnalysisResultClass(**document))
                else:
                    # Retornar como dict quando schemas não disponíveis
                    analyses.append(document)  # type: ignore

            return analyses

        except Exception as e:
            logger.error(
                f"Erro ao listar análises para {company_name}: {str(e)}")
            return []

    async def get_companies_by_sector(self, sector: str, limit: int = 20) -> List[Dict[str, Any]]:
        """
        Busca empresas por setor

        Args:
            sector: Setor da empresa
            limit: Máximo de resultados

        Returns:
            Lista de empresas do setor
        """
        if not self.mongodb_available:
            logger.info(f"Simulando busca de empresas do setor: {sector}")
            return []

        if self.db is None:
            logger.error("Database não inicializado")
            return []

        try:
            collection = self.db[self.collections["companies"]]
            cursor = collection.find(
                {"sector": sector}
            ).sort("analysis_count", -1).limit(limit)

            companies = []
            async for doc in cursor:
                doc.pop("_id", None)
                companies.append(doc)

            return companies

        except Exception as e:
            logger.error(
                f"Erro ao buscar empresas do setor {sector}: {str(e)}")
            return []

    async def get_analysis_trends(self, days: int = 30) -> Dict[str, Any]:
        """
        Analisa tendências de análises dos últimos N dias

        Args:
            days: Número de dias para análise

        Returns:
            Dados de tendências
        """
        if not self.mongodb_available:
            logger.info(f"Simulando análise de tendências para {days} dias")
            return {"trend": "simulated", "days": days}

        if self.db is None:
            logger.error("Database não inicializado")
            return {"error": "Database não inicializado"}

        try:
            from datetime import timedelta

            start_date = datetime.now(timezone.utc) - timedelta(days=days)

            collection = self.db[self.collections["analyses"]]

            # Agregação para estatísticas
            pipeline = [
                {"$match": {"created_at": {"$gte": start_date}}},
                {"$group": {
                    "_id": {
                        "year": {"$year": "$created_at"},
                        "month": {"$month": "$created_at"},
                        "day": {"$dayOfMonth": "$created_at"}
                    },
                    "count": {"$sum": 1},
                    "avg_score": {"$avg": "$recommendations.current_overall_score"},
                    "avg_confidence": {"$avg": "$confidence_score"}
                }},
                {"$sort": {"_id": 1}}
            ]

            cursor = collection.aggregate(pipeline)
            trends = []
            async for doc in cursor:
                trends.append(doc)

            return {
                "period_days": days,
                "total_analyses": len(trends),
                "daily_trends": trends,
                "generated_at": datetime.now(timezone.utc)
            }

        except Exception as e:
            logger.error(f"Erro ao calcular tendências: {str(e)}")
            return {}

    def get_storage_stats(self) -> Dict[str, Any]:
        """
        Retorna estatísticas do storage (versão síncrona)

        Returns:
            Estatísticas de uso do storage
        """
        if not self.mongodb_available:
            return {
                "status": "simulated",
                "mongodb_available": False,
                "collections": self.collections
            }

        if self.db is None:
            return {
                "status": "disconnected",
                "mongodb_available": True,
                "collections": self.collections
            }

        return {
            "status": "connected",
            "mongodb_available": True,
            "database": self.database_name,
            "collections": self.collections
        }

    async def get_storage_stats_async(self) -> Dict[str, Any]:
        """
        Retorna estatísticas do storage (versão assíncrona)

        Returns:
            Estatísticas de uso do storage
        """
        if not self.mongodb_available:
            return {
                "status": "simulated",
                "mongodb_available": False,
                "collections": self.collections
            }

        if self.db is None:
            return {
                "status": "disconnected",
                "mongodb_available": True,
                "collections": self.collections
            }

        try:
            stats = {
                "mongodb_available": True,
                "database": self.database_name,
                "collections": {}
            }

            for name, collection_name in self.collections.items():
                collection = self.db[collection_name]
                count = await collection.count_documents({})
                stats["collections"][name] = {
                    "name": collection_name,
                    "document_count": count
                }

            return stats

        except Exception as e:
            logger.error(f"Erro ao obter estatísticas: {str(e)}")
            return {"error": str(e)}

    async def delete_analysis(self, analysis_id: str) -> bool:
        """
        Remove análise do storage

        Args:
            analysis_id: ID da análise para remover

        Returns:
            True se removida com sucesso
        """
        if not self.mongodb_available:
            logger.info(f"Simulando remoção da análise: {analysis_id}")
            return True

        if self.db is None:
            logger.error("Database não inicializado")
            return False

        try:
            collection = self.db[self.collections["analyses"]]
            result = await collection.delete_one({"analysis_id": analysis_id})

            if result.deleted_count > 0:
                logger.info(f"Análise {analysis_id} removida")
                return True

            return False

        except Exception as e:
            logger.error(f"Erro ao remover análise {analysis_id}: {str(e)}")
            return False

    async def health_check(self) -> Dict[str, Any]:
        """
        Verifica saúde da conexão MongoDB

        Returns:
            Status de saúde do sistema
        """
        if not self.mongodb_available:
            return {
                "status": "unavailable",
                "mongodb_available": False,
                "message": "MongoDB dependencies not installed"
            }

        try:
            if not self.client:
                return {
                    "status": "disconnected",
                    "mongodb_available": True,
                    "message": "Not connected to MongoDB"
                }

            # Testar conexão
            await self.client.admin.command('ping')

            return {
                "status": "healthy",
                "mongodb_available": True,
                "database": self.database_name,
                "collections": list(self.collections.values()),
                "timestamp": datetime.now(timezone.utc)
            }

        except Exception as e:
            return {
                "status": "error",
                "mongodb_available": True,
                "error": str(e),
                "timestamp": datetime.now(timezone.utc)
            }


# ============================================================================
# CONTEXT MANAGER E HELPERS
# ============================================================================

class AgentStorageManager:
    """Context manager para AgentStorage com gerenciamento automático de conexão"""

    def __init__(self):
        self.storage = AgentStorage()

    async def __aenter__(self) -> AgentStorage:
        await self.storage.connect()
        return self.storage

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.storage.disconnect()


async def save_analysis_async(analysis_result: AnalysisResultType) -> str:
    """
    Helper para salvar análise de forma assíncrona

    Args:
        analysis_result: Resultado da análise

    Returns:
        ID do documento MongoDB
    """
    async with AgentStorageManager() as storage:
        return await storage.save_analysis_result(analysis_result)


async def get_analysis_async(analysis_id: str) -> Optional[AnalysisResultType]:
    """
    Helper para buscar análise de forma assíncrona

    Args:
        analysis_id: ID da análise

    Returns:
        Resultado da análise ou None
    """
    async with AgentStorageManager() as storage:
        return await storage.get_analysis_by_id(analysis_id)


# ============================================================================
# HELPERS SÍNCRONOS PARA INTEGRAÇÃO
# ============================================================================

def save_analysis_sync(analysis_result: AnalysisResultType) -> str:
    """
    Versão síncrona para salvar análise

    Args:
        analysis_result: Resultado da análise

    Returns:
        ID do documento MongoDB
    """
    return asyncio.run(save_analysis_async(analysis_result))


def get_analysis_sync(analysis_id: str) -> Optional[AnalysisResultType]:
    """
    Versão síncrona para buscar análise

    Args:
        analysis_id: ID da análise

    Returns:
        Resultado da análise ou None
    """
    return asyncio.run(get_analysis_async(analysis_id))


if __name__ == "__main__":
    # Teste básico
    async def test_storage():
        async with AgentStorageManager() as storage:
            health = await storage.health_check()
            print(f"Storage Health: {health}")

            stats = await storage.get_storage_stats_async()
            print(f"Storage Stats: {stats}")

    asyncio.run(test_storage())
