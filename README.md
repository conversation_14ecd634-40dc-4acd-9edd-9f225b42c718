# ScopeAI - Innovation Scope AI

## 📋 Sobre o Projeto

**ScopeAI** é uma plataforma inteligente que transforma qualquer URL de projeto web em **análises técnicas completas** e **relatórios profissionais**. 

### O que ele faz?

Imagine que você tem um site e quer saber:
- 🚀 **Performance**: Quão rápido seu site carrega?
- 🔒 **Segurança**: Há vulnerabilidades no seu código?
- 📱 **UX/UI**: A experiência do usuário está boa?
- 🔍 **SEO**: Está otimizado para Google?
- ♿ **Acessibilidade**: Pessoas com deficiência conseguem usar?

Em vez de contratar 7 especialistas diferentes, você **insere uma URL** e o ScopeAI usa **inteligência artificial** para analisar tudo e gerar:
- 📄 **Relatório HTML** interativo
- 📋 **PDF profissional** para apresentações  
- 🎧 **Podcast em áudio** explicando os resultados

## 🏗️ Arquitetura (Como funciona por dentro)

```
┌─────────────────┐    ┌────────────────────┐    ┌──────────────────┐
│   FRONTEND      │────│     BACKEND        │────│   DATABASES      │
│   Angular 19    │    │    FastAPI         │    │   MongoDB Atlas  │
│   (Interface)   │    │   (7 Agentes IA)   │    │   Redis Cache    │
│   Porta: 4200   │    │    Porta: 8040     │    │   (Nuvem)        │
└─────────────────┘    └────────────────────┘    └──────────────────┘
```

**Frontend**: Interface web onde você interage (feita em Angular)  
**Backend**: "Cérebro" que coordena os agentes de IA (feito em Python/FastAPI)  
**Databases**: Onde ficam salvos os relatórios e dados (MongoDB + Redis)

## ⚡ Pré-requisitos

Antes de começar, você precisa ter instalado:

### 📱 Para qualquer pessoa (Opção Docker):
- **Git**: Para baixar o código ([Download aqui](https://git-scm.com/))
- **Docker Desktop**: Para rodar containers ([Download aqui](https://www.docker.com/products/docker-desktop/))

### 🛠️ Para desenvolvedores (Opção Local):
- **Git** ([Download](https://git-scm.com/))
- **Node.js** versão 18 ou 20 ([Download](https://nodejs.org/))
- **Python** versão 3.11, 3.12 ou 3.13 ([Download](https://python.org/))

---

## 🚀 OPÇÃO 1: Docker (RECOMENDADO para começar)

### Por que Docker?
Docker é como uma "máquina virtual leve" que roda seu projeto de forma isolada. **Vantagens**:
- ✅ Não precisa instalar Python, Node.js nem configurar nada
- ✅ Funciona igual em Windows, Mac e Linux  
- ✅ Não "bagunça" seu computador com dependências

### Passo 1: Baixar o código
```bash
# 1. Abra o terminal (Command Prompt no Windows, Terminal no Mac/Linux)
# 2. Navegue até onde quer baixar o projeto (ex: Desktop)
cd Desktop

# 3. Clone (baixe) o repositório
git clone [URL_DO_REPOSITORIO]
cd scope-ai
```

### Passo 2: Rodar com Docker
```bash
# Este comando baixa, instala e roda TUDO automaticamente
docker-compose up

# 💡 O que está acontecendo:
# - Baixando imagem do Python + dependências do backend
# - Instalando 40+ bibliotecas Python (FastAPI, IA, relatórios, etc.)
# - Configurando Chrome/Playwright para análises web
# - Conectando com MongoDB Atlas e Redis na nuvem
# - Subindo o backend na porta 8040
```

**⏱️ Primeira execução**: 5-10 minutos (baixa tudo)  
**Próximas execuções**: 30 segundos (só inicia)

### Passo 3: Rodar o Frontend (em outro terminal)
```bash
# Abra um NOVO terminal na mesma pasta do projeto
cd frontend

# Instale as dependências do Angular (primeira vez apenas)
npm install

# Rode o frontend
npm start

# 💡 O que está acontecendo:
# - Instalando Angular 19 + PrimeNG + TailwindCSS
# - Compilando TypeScript para JavaScript
# - Iniciando servidor de desenvolvimento
# - Interface disponível em http://localhost:4200
```

### Passo 4: Verificar se funcionou
1. **Backend**: Abra http://localhost:8040/docs
   - Deve mostrar a documentação da API (Swagger)
   
2. **Frontend**: Abra http://localhost:4200
   - Deve mostrar a interface do ScopeAI

3. **Teste completo**: 
   - Na interface, cadastre um cliente de teste
   - Insira uma URL (ex: https://google.com)
   - Aguarde a análise (~3-5 minutos)
   - Veja o relatório gerado

---

## 🛠️ OPÇÃO 2: Desenvolvimento Local

### Quando usar?
- Você quer **modificar o código**
- Quer **entender como funciona** por dentro
- Vai **contribuir** com o projeto

### Passo 1: Baixar e configurar o Backend
```bash
# 1. Clone o projeto (se ainda não fez)
git clone [URL_DO_REPOSITORIO]
cd scope-ai/backend

# 2. Verifique se tem Python correto
python --version
# Deve mostrar Python 3.11.x, 3.12.x ou 3.13.x

# 3. Instale Poetry (gerenciador de dependências Python)
curl -sSL https://install.python-poetry.org | python3 -
# ou no Windows: 
# Invoke-WebRequest -Uri https://install.python-poetry.org -UseBasicParsing | Invoke-Expression

# 4. Instale as dependências (40+ bibliotecas)
poetry install

# 💡 O que está sendo instalado:
# - FastAPI: Framework web moderno e rápido  
# - LangChain: Para orquestrar modelos de IA
# - OpenAI, Groq: Integrações com modelos GPT
# - Playwright: Automação de navegadores
# - Motor: Driver assíncrono para MongoDB
# - Pandas, NumPy: Análise de dados
# - WeasyPrint: Geração de PDFs
# - Edge-TTS: Síntese de voz para podcasts
```

### Passo 2: Configurar variáveis de ambiente
```bash
# O arquivo .env já está configurado com:
# - Chaves de API (OpenAI, Cohere, Perplexity)
# - Conexões MongoDB Atlas e Redis Cloud
# - Configurações do servidor

# Verifique se está tudo certo:
cat .env

# 💡 As chaves de API já estão configuradas para desenvolvimento
# Em produção, você precisaria das suas próprias chaves
```

### Passo 3: Rodar o Backend
```bash
# Dentro da pasta backend/, rode:
poetry run uvicorn main:app --reload --host 0.0.0.0 --port 8040

# 💡 O que cada parâmetro faz:
# - uvicorn: Servidor ASGI para Python assíncrono
# - main:app: Arquivo main.py, variável app
# - --reload: Reinicia automaticamente se mudar código
# - --host 0.0.0.0: Aceita conexões de qualquer IP
# - --port 8040: Porta onde o backend vai rodar
```

### Passo 4: Configurar e rodar o Frontend
```bash
# Abra um NOVO terminal
cd scope-ai/frontend

# Verifique se tem Node.js correto
node --version
# Deve mostrar v18.x.x ou v20.x.x

# Instale Angular CLI globalmente (uma vez só)
npm install -g @angular/cli

# Instale dependências do projeto
npm install

# 💡 O que está sendo instalado:
# - Angular 19: Framework principal
# - PrimeNG: Biblioteca de componentes UI
# - TailwindCSS: Framework CSS utilitário  
# - TypeScript: Linguagem tipada
# - Mermaid: Renderização de diagramas
# - Chart.js: Gráficos e visualizações

# Rode o frontend
npm start
# ou
ng serve

# Interface disponível em: http://localhost:4200
```

### Passo 5: Instalar browsers para análises (opcional)
```bash
# Se for usar análises com Playwright (screenshots, performance)
cd backend
poetry run playwright install chromium

# 💡 Isso instala uma versão do Chrome específica para automação
```

---

## ✅ Verificação e Testes

### 1. Verificar se os serviços estão funcionando
```bash
# Backend health check
curl http://localhost:8040/docs
# Deve retornar a página de documentação da API

# Frontend
# Abrir http://localhost:4200 no navegador
```

### 2. Teste básico completo
1. **Acesse**: http://localhost:4200
2. **Cadastre um cliente** (qualquer nome e dados)
3. **Insira uma URL** para análise (ex: https://github.com)
4. **Aguarde** a análise (~3-5 minutos)
5. **Veja o relatório** gerado

### 3. Teste avançado (APIs)
```bash
# Teste direto na API usando curl
curl -X POST "http://localhost:8040/clients" \
  -H "Content-Type: application/json" \
  -d '{"nome": "Teste", "url": "https://example.com"}'

# Ou use a interface Swagger em:
# http://localhost:8040/docs
```

---

## 🔧 Troubleshooting (Problemas Comuns)

### ❌ "Porta já em uso"
```bash
# Se a porta 4200 ou 8040 estiver ocupada:

# Verificar quem está usando a porta
netstat -ano | findstr :4200  # Windows
lsof -i :4200                 # Mac/Linux

# Matar o processo ou usar outra porta:
ng serve --port 4201
uvicorn main:app --port 8041
```

### ❌ "Docker não found"
- Instale Docker Desktop: https://www.docker.com/products/docker-desktop/
- Reinicie o computador após instalar
- Verifique: `docker --version`

### ❌ "Python version not supported"  
```bash
# Verifique sua versão
python --version

# Se for menor que 3.11 ou maior que 3.13:
# - Baixe Python correto em https://python.org/
# - Ou use pyenv para gerenciar versões
```

### ❌ "npm ERR! permission denied"
```bash
# Mac/Linux - usar sudo:
sudo npm install -g @angular/cli

# Windows - executar terminal como Administrador
```

### ❌ "ModuleNotFoundError" no Python
```bash
# Certifique-se de estar no ambiente virtual correto:
cd backend
poetry shell
poetry install
```

### ❌ Frontend não conecta com Backend
1. **Verifique se o backend está rodando**: http://localhost:8040/docs
2. **Verifique CORS**: Backend já está configurado para aceitar todas origens
3. **Verifique firewall**: Pode estar bloqueando a porta 8040

---

## 🎯 Funcionalidades Principais

### 🤖 7 Agentes Especializados
- **Performance Agent**: Analisa velocidade, Core Web Vitals
- **SEO Agent**: Otimização para buscadores
- **Security Agent**: Vulnerabilidades e segurança
- **UX/UI Designer**: Experiência e interface
- **Accessibility Agent**: Conformidade WCAG
- **Product Owner**: Análise de funcionalidades
- **Technical Writer**: Qualidade da documentação

### 📊 Relatórios Multi-formato
- **HTML Interativo**: Visualização rica com gráficos
- **PDF Profissional**: Para apresentações e compartilhamento
- **Podcast de Áudio**: Explicação em português falado

### 🚀 Tecnologias Modernas
- **Backend**: FastAPI (Python) - Performance assíncrona
- **Frontend**: Angular 19 - Componentes standalone
- **IA**: OpenAI, Groq, Ollama - Múltiplos providers
- **Database**: MongoDB Atlas - Escalável na nuvem
- **Cache**: Redis - Performance otimizada

---

## 📈 Status do Projeto

**Progresso**: 96% completo 🟩

### ✅ Implementado
- Sistema de agentes funcionando
- Geração de relatórios em 3 formatos
- Interface moderna e responsiva
- Integração com múltiplos LLMs
- Sistema assíncrono otimizado
- Arquitetura limpa (3.047 linhas de código morto removidas)

### 🔄 Em desenvolvimento
- Módulo de validação automatizada
- Knowledge management com IA
- Otimizações de performance

---

## 🤝 Como Contribuir

### Para desenvolvedores:
1. **Use a Opção 2** (desenvolvimento local)
2. **Leia o código**: Comece por `backend/main.py` e `frontend/src/app/`
3. **Veja a arquitetura**: `memory-bank/systemPatterns.md`
4. **Entenda o memory-bank**: Toda documentação técnica está lá

### Para testadores:
1. **Use a Opção 1** (Docker)
2. **Teste diferentes URLs**: Sites grandes, pequenos, com problemas
3. **Reporte bugs**: Abra issues com URLs que falharam

### Para product managers:
1. **Teste o fluxo completo** de cadastro → análise → relatório
2. **Avalie os insights** gerados pelos agentes
3. **Sugira melhorias** na UX/UI

---

## 📞 Suporte

- **Issues**: Abra issues no repositório GitHub
- **Documentação técnica**: Pasta `memory-bank/`
- **API Docs**: http://localhost:8040/docs (quando rodando)

---

**Desenvolvido com ❤️ usando Angular 19, FastAPI e Inteligência Artificial**

*Última atualização: 2025-06-24* 