import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, catchError, combineLatest, map, Observable, throwError } from 'rxjs';

import {
	IBackendProject,
	IEstimateStatusWithDetails,
	IProjectsApiResponse,
	IProjectWithDetailedEstimate,
} from '../interfaces/backend-project.interface';
import {
	IEstimativaDetalhada,
	IGeneratedProject,
	IGeneratedProjectFilter,
} from '../interfaces/generated-project.interface';

@Injectable({
	providedIn: 'root',
})
export class GeneratedProjectsService {
	private apiUrl = 'http://localhost:8040'; // Base URL do backend
	private projectsSubject = new BehaviorSubject<Array<IGeneratedProject>>([]);
	private filterSubject = new BehaviorSubject<IGeneratedProjectFilter>({
		sortBy: 'created_at',
		sortOrder: 'desc', // Padrão: mais recentes primeiro
	});
	private loadingSubject = new BehaviorSubject<boolean>(false);

	public projects$ = this.projectsSubject.asObservable();
	public filter$ = this.filterSubject.asObservable();
	public loading$ = this.loadingSubject.asObservable();
	public totalRecords$ = this.projects$.pipe(map(projects => projects.length));

	/**
	 * Observable com projetos filtrados e ordenados
	 */
	public filteredProjects$: Observable<Array<IGeneratedProject>> = combineLatest([this.projects$, this.filter$]).pipe(
		map(([projects, filter]) => {
			let filtered = [...projects];

			// Aplicar filtro de busca
			if (filter.search?.trim()) {
				const searchTerm = filter.search.toLowerCase().trim();
				filtered = filtered.filter(
					project =>
						project.nome_projeto.toLowerCase().includes(searchTerm) ||
						project.cliente_nome.toLowerCase().includes(searchTerm) ||
						project.cliente_sector.toLowerCase().includes(searchTerm) ||
						project.resumo.toLowerCase().includes(searchTerm) ||
						project.tags.some(tag => tag.toLowerCase().includes(searchTerm)) ||
						(project.agente_responsavel && project.agente_responsavel.toLowerCase().includes(searchTerm)) ||
						(project.equipe && project.equipe.toLowerCase().includes(searchTerm)) ||
						(project.area_especialidade && project.area_especialidade.toLowerCase().includes(searchTerm)),
				);
			}

			// Aplicar filtro por setor (cliente_sector)
			if (filter.cliente_sector?.trim()) {
				filtered = filtered.filter(project => project.cliente_sector === filter.cliente_sector);
			}

			// Aplicar filtro por equipe
			if (filter.equipe?.trim()) {
				filtered = filtered.filter(project => project.equipe === filter.equipe);
			}

			// Aplicar filtro por agente_responsavel
			if (filter.agente_responsavel?.trim()) {
				filtered = filtered.filter(project => project.agente_responsavel === filter.agente_responsavel);
			}

			// Aplicar filtro por area_especialidade
			if (filter.area_especialidade?.trim()) {
				filtered = filtered.filter(project => project.area_especialidade === filter.area_especialidade);
			}

			// Aplicar filtro por importancia
			if (filter.importancia?.trim()) {
				filtered = filtered.filter(project => project.importancia === filter.importancia);
			}

			// Aplicar filtro por tags
			if (filter.tags?.length) {
				filtered = filtered.filter(project => filter.tags!.some(tag => project.tags.includes(tag)));
			}

			// Aplicar ordenação
			if (filter.sortBy) {
				filtered = this.sortProjects(filtered, filter.sortBy, filter.sortOrder || 'desc');
			}

			return filtered;
		}),
	);

	constructor(private http: HttpClient) {
		this.loadProjectsFromBackend();
	}

	/**
	 * Carrega projetos do backend
	 */
	private loadProjectsFromBackend(): void {
		// Don't set loading to true if we already have data
		// This prevents blocking the UI on refresh
		if (this.projectsSubject.value.length === 0) {
			this.setLoading(true);
		}

		this.http.get<IProjectsApiResponse | Array<IBackendProject>>(`${this.apiUrl}/projects`).subscribe({
			next: response => {
				let rawProjects: Array<IBackendProject> = [];

				// Extrair array de projetos da resposta
				if (Array.isArray(response)) {
					rawProjects = response;
				} else if (response && Array.isArray(response.projects)) {
					rawProjects = response.projects;
				} else {
					console.warn('⚠️ Formato inesperado da resposta do backend /projects:', response);
				}

				// Transformar dados do backend para interface frontend
				const projects: Array<IGeneratedProject> = rawProjects.map(project =>
					this.transformBackendProject(project),
				);

				// Ordenação composta: data (dia/mês/ano) + pontuacao (maior nota)
				const sortedProjects = projects.sort((a, b) => {
					// Normalizar datas para comparar apenas dia/mês/ano (ignorar horário)
					const dateA = new Date(a.updated_at || a.created_at);
					const dateB = new Date(b.updated_at || b.created_at);

					// Criar datas normalizadas (00:00:00 do dia)
					const normalizedDateA = new Date(dateA.getFullYear(), dateA.getMonth(), dateA.getDate());
					const normalizedDateB = new Date(dateB.getFullYear(), dateB.getMonth(), dateB.getDate());

					// Critério 1: data normalizada (mais recente primeiro)
					const dateComparison = normalizedDateB.getTime() - normalizedDateA.getTime();

					// Se as datas (dia/mês/ano) forem iguais, usa pontuacao como critério
					if (dateComparison === 0) {
						// Critério 2: pontuacao (maior nota primeiro)
						return (b.pontuacao || 0) - (a.pontuacao || 0);
					}

					return dateComparison;
				});

				this.projectsSubject.next(sortedProjects);
				this.setLoading(false);
			},
			error: error => {
				console.error('❌ Erro ao carregar projetos:', error);
				// Don't clear existing data on error
				// Keep showing what we have
				this.setLoading(false);
			},
		});
	}

	/**
	 * 🔄 Transforma dados do backend para interface frontend
	 */
	private transformBackendProject(backendProject: IBackendProject): IGeneratedProject {
		// Mapear status da estimativa - usar os novos campos padronizados primeiro
		const estimateStatus = backendProject.estimate_status || backendProject.estimativa_status;
		const estimateRequested = backendProject.estimate_requested || backendProject.estimativa_solicitada;

		// 🎯 Verificar se tem estimativa processada (novo campo do backend)
		const hasProcessedEstimate = !!(backendProject as IProjectWithDetailedEstimate).estimativa_processada;

		// Mapear status legível
		let mappedEstimateStatus: IGeneratedProject['estimativa_status'] = 'não_solicitada';
		if (estimateStatus === 'processing' || estimateStatus === 'processando') {
			mappedEstimateStatus = 'processando';
		} else if (estimateStatus === 'completed' || estimateStatus === 'concluida') {
			mappedEstimateStatus = 'concluida';
		} else if (estimateStatus === 'error' || estimateStatus === 'erro') {
			mappedEstimateStatus = 'erro';
		}

		// Para projetos com estimativa processada, sempre considerar como detalhada
		const estimateResult = hasProcessedEstimate
			? ({ processada: true } as IEstimativaDetalhada)
			: backendProject.estimate_resultado || backendProject.estimativa_detalhada;

		return {
			_id: backendProject._id || '',
			nome_projeto: backendProject.nome_projeto || '',
			cliente_nome: backendProject.cliente_nome || '',
			cliente_sector: backendProject.cliente_sector || '',
			resumo: backendProject.resumo || '',
			tags: Array.isArray(backendProject.tags) ? backendProject.tags : [],
			agentes_justificativa: Array.isArray(backendProject.agentes_justificativa)
				? backendProject.agentes_justificativa
				: [],
			progresso: typeof backendProject.progresso === 'number' ? backendProject.progresso : 0,
			equipe: backendProject.equipe || 'Não definida',
			status: backendProject.status || 'Novo',
			created_at: backendProject.created_at || new Date().toISOString(),
			updated_at: backendProject.updated_at || new Date().toISOString(),
			justificativa: backendProject.justificativa || '',
			importancia: backendProject.importancia || '',
			pontuacao: typeof backendProject.pontuacao === 'number' ? backendProject.pontuacao : 0,
			agente_responsavel: backendProject.agente_responsavel || '',
			area_especialidade: backendProject.area_especialidade || '',
			detalhamento: backendProject.detalhamento || undefined,
			beneficios: typeof backendProject.beneficios === 'string' ? backendProject.beneficios : '',
			cliente_id: backendProject.cliente_id || undefined,

			// 🎯 Campos de estimativa usando campos padronizados do backend
			estimativa_status: mappedEstimateStatus,
			estimativa_detalhada: estimateResult || undefined,
			estimativa_solicitada: estimateRequested || false,
			estimativa_solicitada_em:
				backendProject.estimate_solicitada_em || backendProject.estimativa_solicitada_em || undefined,
			estimativa_concluida_em:
				backendProject.estimate_atualizada_em || backendProject.estimativa_concluida_em || undefined,
			estimativa_erro: backendProject.estimate_error || backendProject.estimativa_erro || undefined,

			// 🎯 NOVO: Campo "projeto" estruturado do Team Agno
			projeto: backendProject.projeto || undefined,
			
			// Campo para identificar origem do projeto
			ai_generated: backendProject.ai_generated || false,
		};
	}

	/**
	 * Recarrega projetos do backend
	 */
	public refreshProjects(): void {
		this.loadProjectsFromBackend();
	}

	/**
	 * Busca todos os projetos
	 */
	public getProjects(): Observable<Array<IGeneratedProject>> {
		return this.projects$;
	}

	/**
	 * Busca projeto por ID
	 */
	public getProjectById(id: string): Observable<IGeneratedProject | undefined> {
		return this.projects$.pipe(map(projects => projects.find(p => p._id === id)));
	}

	/**
	 * Adiciona novo projeto
	 */
	public addProject(project: Omit<IGeneratedProject, '_id' | 'created_at' | 'updated_at'>): void {
		const currentProjects = this.projectsSubject.value;
		const newProject: IGeneratedProject = {
			...project,
			_id: `temp_${Date.now()}`, // Usar timestamp para ID temporário
			nome_projeto: project.nome_projeto || '',
			cliente_nome: project.cliente_nome || '',
			cliente_sector: project.cliente_sector || '',
			resumo: project.resumo || '',
			tags: project.tags || [],
			progresso: project.progresso ?? 0,
			equipe: project.equipe || 'Não definida',
			status: project.status || 'Novo',
			created_at: new Date().toISOString(),
			updated_at: new Date().toISOString(),
			justificativa: project.justificativa || '',
			agentes_justificativa: project.agentes_justificativa || [],
			importancia: project.importancia || '',
			pontuacao: project.pontuacao ?? 0,
			agente_responsavel: project.agente_responsavel || '',
			area_especialidade: project.area_especialidade || '',
		};
		this.projectsSubject.next([...currentProjects, newProject]);
	}

	/**
	 * Atualiza filtros
	 */
	public updateFilter(filter: Partial<IGeneratedProjectFilter>): void {
		const currentFilter = this.filterSubject.value;
		this.filterSubject.next({ ...currentFilter, ...filter });
	}

	/**
	 * Limpa filtros (mantém ordenação padrão)
	 */
	public clearFilters(): void {
		this.filterSubject.next({
			sortBy: 'created_at',
			sortOrder: 'desc',
		});
	}

	/**
	 * Busca setores únicos
	 */
	public getUniqueSectors(): Observable<Array<string>> {
		return this.projects$.pipe(
			map(projects => {
				const sectors = projects.map(p => p.cliente_sector);
				return [...new Set(sectors)].sort();
			}),
		);
	}

	/**
	 * Busca equipes únicas
	 */
	public getUniqueTeams(): Observable<Array<string>> {
		return this.projects$.pipe(
			map(projects => {
				const teams = projects.map(p => p.equipe).filter(team => team && team !== 'Não definida');
				return [...new Set(teams)].sort();
			}),
		);
	}

	/**
	 * Busca agentes responsáveis únicos
	 */
	public getUniqueResponsibleAgents(): Observable<Array<string>> {
		return this.projects$.pipe(
			map(projects => {
				const agents = projects.map(p => p.agente_responsavel).filter(agent => !!agent);
				return [...new Set(agents)].sort();
			}),
		);
	}

	/**
	 * Busca áreas de especialidade únicas
	 */
	public getUniqueExpertiseAreas(): Observable<Array<string>> {
		return this.projects$.pipe(
			map(projects => {
				const areas = projects.map(p => p.area_especialidade).filter(area => !!area);
				return [...new Set(areas)].sort();
			}),
		);
	}

	/**
	 * Busca níveis de importância únicos
	 */
	public getUniqueImportanceLevels(): Observable<Array<string>> {
		return this.projects$.pipe(
			map(projects => {
				const levels = projects.map(p => p.importancia).filter(level => !!level);
				return [...new Set(levels)].sort();
			}),
		);
	}

	/**
	 * Busca tags únicas
	 */
	public getUniqueTags(): Observable<Array<string>> {
		return this.projects$.pipe(
			map(projects => {
				const allTags = projects.flatMap(p => p.tags);
				return [...new Set(allTags)].sort();
			}),
		);
	}

	/**
	 * Ordena projetos
	 */
	private sortProjects(
		projects: Array<IGeneratedProject>,
		sortBy: IGeneratedProjectFilter['sortBy'],
		sortOrder: 'asc' | 'desc',
	): Array<IGeneratedProject> {
		if (!sortBy) {
			return projects;
		}

		return projects.sort((a, b) => {
			let comparison = 0;

			const valA = a[sortBy!];
			const valB = b[sortBy!];

			switch (sortBy) {
				case 'nome_projeto':
				case 'cliente_nome':
				case 'cliente_sector':
				case 'equipe':
				case 'agente_responsavel':
				case 'area_especialidade':
				case 'importancia':
				case 'status':
					comparison = String(valA ?? '')
						.toLowerCase()
						.localeCompare(String(valB ?? '').toLowerCase());
					break;
				case 'progresso':
				case 'pontuacao':
					comparison = Number(valA ?? 0) - Number(valB ?? 0);
					break;
				case 'created_at':
				case 'updated_at':
					comparison =
						new Date(a.updated_at || a.created_at).getTime() -
						new Date(b.updated_at || b.created_at).getTime();
					break;
				default:
					const _exhaustiveCheck: never = sortBy;
					comparison = 0;
			}
			return sortOrder === 'desc' ? -comparison : comparison;
		});
	}

	/**
	 * Define estado de loading
	 */
	public setLoading(loading: boolean): void {
		this.loadingSubject.next(loading);
	}

	/**
	 * 🎯 Gera estimativa detalhada para um projeto específico usando Team Agno
	 */
	public generateProjectEstimate(projectId: string): Observable<{
		message: string;
		project_id: string;
		status: string;
		estimated_time: string;
	}> {
		return this.http
			.post<{
				message: string;
				project_id: string;
				status: string;
				estimated_time: string;
			}>(`${this.apiUrl}/projects/${projectId}/generate-estimate`, {})
			.pipe(
				catchError(error => {
					console.error('❌ Erro ao gerar estimativa:', error);
					return throwError(() => error);
				}),
			);
	}

	/**
	 * 📊 Verifica status da estimativa de um projeto
	 */
	public getProjectEstimateStatus(projectId: string): Observable<{
		project_id: string;
		estimativa_status: string;
		has_estimativa: boolean;
	}> {
		return this.http
			.get<{
				project_id: string;
				estimativa_status: string;
				has_estimativa: boolean;
			}>(`${this.apiUrl}/projects/${projectId}/estimate-status`)
			.pipe(
				catchError(error => {
					console.error('❌ Erro ao verificar status da estimativa:', error);
					return throwError(() => error);
				}),
			);
	}

	/**
	 * ✅ Verifica se um projeto possui estimativa concluída
	 */
	public projectHasEstimate(projectId: string): boolean {
		if (!projectId) return false;

		const currentProjects = this.projectsSubject.value;
		const project = currentProjects.find(p => p._id === projectId);

		if (!project) return false;

		// Verificar pelos campos de status de estimativa
		const hasCompletedStatus = project.estimativa_status === 'concluida';

		// Verificar se tem estimativa detalhada
		const hasEstimateData = !!project.estimativa_detalhada;

		return hasCompletedStatus && hasEstimateData;
	}

	/**
	 * 🔄 Atualiza dados de estimativa de um projeto no estado local
	 */
	public updateProjectEstimate(
		projectId: string,
		estimateData: Partial<{
			estimativa_status: IGeneratedProject['estimativa_status'];
			estimativa_detalhada: IEstimativaDetalhada;
			estimativa_solicitada: boolean;
			estimativa_solicitada_em: string;
			estimativa_concluida_em: string;
			estimativa_erro: string;
		}>,
	): void {
		if (!projectId) return;

		const currentProjects = this.projectsSubject.value;
		const updatedProjects = currentProjects.map(project => {
			if (project._id === projectId) {
				return {
					...project,
					...estimateData,
					updated_at: new Date().toISOString(),
				};
			}
			return project;
		});

		this.projectsSubject.next(updatedProjects);
	}

	/**
	 * 🎯 Solicita geração de estimativa e atualiza estado local
	 */
	public async requestProjectEstimate(projectId: string): Promise<{
		message: string;
		project_id: string;
		status: string;
		estimated_time: string;
	}> {
		try {
			// Atualizar estado local para "processando"
			this.updateProjectEstimate(projectId, {
				estimativa_solicitada: true,
				estimativa_status: 'processando',
				estimativa_solicitada_em: new Date().toISOString(),
			});

			// Fazer chamada para o backend
			const response = await this.generateProjectEstimate(projectId).toPromise();

			if (!response) {
				throw new Error('Resposta vazia do servidor');
			}

			return response;
		} catch (error) {
			// Em caso de erro, atualizar estado local
			this.updateProjectEstimate(projectId, {
				estimativa_status: 'erro',
				estimativa_erro: 'Erro ao solicitar estimativa',
			});

			throw error;
		}
	}

	/**
	 * 📋 Busca estimativa detalhada de um projeto
	 */
	public getProjectEstimate(projectId: string): IEstimativaDetalhada | undefined {
		if (!projectId) return undefined;

		const currentProjects = this.projectsSubject.value;
		const project = currentProjects.find(p => p._id === projectId);

		return project?.estimativa_detalhada;
	}

	/**
	 * 🔍 Busca status atual da estimativa de um projeto
	 */
	public getProjectEstimateCurrentStatus(projectId: string): IGeneratedProject['estimativa_status'] {
		if (!projectId) return 'não_solicitada';

		const currentProjects = this.projectsSubject.value;
		const project = currentProjects.find(p => p._id === projectId);

		return project?.estimativa_status || 'não_solicitada';
	}

	/**
	 * 🔄 Recarrega dados de um projeto específico do backend
	 */
	public async reloadSpecificProject(projectId: string): Promise<IGeneratedProject | null> {
		try {
			// Buscar dados atualizados do projeto no backend
			const response = await this.http.get<IBackendProject>(`${this.apiUrl}/projects/${projectId}`).toPromise();

			if (!response) {
				console.warn(`⚠️ Projeto ${projectId} não encontrado no backend`);
				return null;
			}

			// Transformar dados do backend
			const transformedProject = this.transformBackendProject(response);

			// Atualizar no estado local
			const currentProjects = this.projectsSubject.value;
			const updatedProjects = currentProjects.map(project => {
				if (project._id === projectId) {
					return transformedProject;
				}
				return project;
			});

			this.projectsSubject.next(updatedProjects);

			return transformedProject;
		} catch (error) {
			console.error(`❌ Erro ao recarregar projeto ${projectId}:`, error);
			return null;
		}
	}

	/**
	 * 🎯 Buscar dados detalhados de projeto com estimativa processada
	 */
	public getProjectWithDetailedEstimate(projectId: string): Observable<IProjectWithDetailedEstimate> {
		return this.http.get<IProjectWithDetailedEstimate>(`${this.apiUrl}/projects/${projectId}`).pipe(
			map(response => {
				return response;
			}),
			catchError(error => {
				console.error(`❌ Erro ao buscar detalhes do projeto ${projectId}:`, error);
				return throwError(() => error);
			}),
		);
	}

	/**
	 * 🔍 Buscar status da estimativa com dados processados
	 */
	public getEstimateStatusWithDetails(projectId: string): Observable<IEstimateStatusWithDetails> {
		return this.http.get<IEstimateStatusWithDetails>(`${this.apiUrl}/projects/${projectId}/estimate-status`).pipe(
			map(response => {
				return response;
			}),
			catchError(error => {
				console.error(`❌ Erro ao buscar status da estimativa ${projectId}:`, error);
				return throwError(() => error);
			}),
		);
	}
}
