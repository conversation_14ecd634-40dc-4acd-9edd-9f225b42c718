"""
Schemas para Sistema de Orquestração de Agentes

Define todas as estruturas de dados usadas pelo sistema de orquestração,
incluindo configurações, resultados e status de execução.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any, Literal
from enum import Enum
from pydantic import BaseModel, Field
import uuid


class OrchestrationStatus(str, Enum):
    """Status da orquestração completa"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    PARTIAL_SUCCESS = "partial_success"
    CANCELLED = "cancelled"


class AgentExecutionStatus(str, Enum):
    """Status de execução de um agente individual"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"
    RETRYING = "retrying"


class ExecutionPhase(BaseModel):
    """Fase de execução com agentes específicos"""
    phase_id: int = Field(..., description="ID único da fase")
    name: str = Field(..., description="Nome descritivo da fase")
    agents: List[str] = Field(...,
                              description="Lista de agentes a executar nesta fase")
    dependencies: List[str] = Field(
        default_factory=list, description="Fases que devem ser completadas antes")
    parallel: bool = Field(
        default=True, description="Se agentes da fase podem executar em paralelo")
    timeout_seconds: int = Field(
        default=300, description="Timeout para a fase completa")


class OrchestrationConfig(BaseModel):
    """Configuração da orquestração"""
    phases: List[ExecutionPhase] = Field(
        default_factory=list, description="Fases de execução")
    max_retries_per_agent: int = Field(
        default=3, description="Máximo de retries por agente")
    timeout_per_agent_seconds: int = Field(
        default=120, description="Timeout por agente individual")
    continue_on_failure: bool = Field(
        default=True, description="Continuar execução mesmo com falhas")
    enable_parallel_execution: bool = Field(
        default=True, description="Habilitar execução paralela")
    save_intermediate_results: bool = Field(
        default=True, description="Salvar resultados intermediários")

    @classmethod
    def get_default_config(cls) -> "OrchestrationConfig":
        """Retorna configuração padrão com fases otimizadas"""
        default_phases = [
            ExecutionPhase(
                phase_id=1,
                name="Análise Base",
                agents=["technical_architect", "uxui_designer"],
                dependencies=[],
                parallel=True,
                timeout_seconds=300
            ),
            ExecutionPhase(
                phase_id=2,
                name="Análise Estratégica",
                agents=["product_owner"],
                dependencies=["1"],
                parallel=False,
                timeout_seconds=200
            ),
            ExecutionPhase(
                phase_id=3,
                name="Análise Especializada",
                agents=["seo_specialist", "benchmark_agent"],
                dependencies=["1"],
                parallel=True,
                timeout_seconds=300
            ),
            ExecutionPhase(
                phase_id=4,
                name="Consolidação Final",
                agents=["technical_writer"],
                dependencies=["2", "3"],
                parallel=False,
                timeout_seconds=180
            )
        ]

        return cls(phases=default_phases)


class AgentExecutionResult(BaseModel):
    """Resultado da execução de um agente individual"""
    agent_name: str = Field(..., description="Nome do agente executado")
    status: AgentExecutionStatus = Field(...,
                                         description="Status final da execução")
    start_time: datetime = Field(...,
                                 description="Horário de início da execução")
    end_time: Optional[datetime] = Field(
        None, description="Horário de término da execução")
    execution_time_ms: Optional[int] = Field(
        None, description="Tempo de execução em milissegundos")

    # Resultados
    result_data: Optional[Dict[str, Any]] = Field(
        None, description="Dados do resultado da análise")
    analysis_id: Optional[str] = Field(
        None, description="ID do resultado salvo no storage")

    # Informações de execução
    phase_id: int = Field(..., description="ID da fase em que foi executado")
    retry_count: int = Field(
        default=0, description="Número de tentativas realizadas")
    error_message: Optional[str] = Field(
        None, description="Mensagem de erro se falhou")

    # Métricas
    input_data_size_kb: Optional[float] = Field(
        None, description="Tamanho dos dados de entrada em KB")
    output_data_size_kb: Optional[float] = Field(
        None, description="Tamanho dos dados de saída em KB")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class OrchestrationResult(BaseModel):
    """Resultado completo da orquestração"""
    orchestration_id: str = Field(default_factory=lambda: str(
        uuid.uuid4()), description="ID único da orquestração")
    client_id: str = Field(..., description="ID do cliente sendo analisado")

    # Timestamps
    start_time: datetime = Field(
        default_factory=datetime.now, description="Horário de início")
    end_time: Optional[datetime] = Field(
        None, description="Horário de término")
    last_update: datetime = Field(
        default_factory=datetime.now, description="Última atualização")

    # Status e configuração
    status: OrchestrationStatus = Field(
        default=OrchestrationStatus.PENDING, description="Status atual")
    config: OrchestrationConfig = Field(..., description="Configuração usada")

    # Resultados por agente
    agent_results: Dict[str, AgentExecutionResult] = Field(
        default_factory=dict, description="Resultados por agente")

    # Métricas gerais
    total_execution_time_ms: Optional[int] = Field(
        None, description="Tempo total de execução")
    successful_agents: int = Field(
        default=0, description="Número de agentes executados com sucesso")
    failed_agents: int = Field(
        default=0, description="Número de agentes que falharam")

    # Informações adicionais
    error_summary: Optional[str] = Field(
        None, description="Resumo dos erros se houver falhas")
    final_report_id: Optional[str] = Field(
        None, description="ID do relatório final consolidado")

    # Metadados
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Metadados adicionais")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

    def get_execution_progress(self) -> Dict[str, Any]:
        """Retorna progresso atual da execução"""
        total_agents = sum(len(phase.agents) for phase in self.config.phases)
        completed_agents = len([r for r in self.agent_results.values()
                                if r.status == AgentExecutionStatus.COMPLETED])
        failed_agents = len([r for r in self.agent_results.values()
                             if r.status == AgentExecutionStatus.FAILED])
        running_agents = len([r for r in self.agent_results.values()
                              if r.status == AgentExecutionStatus.RUNNING])

        progress_percentage = (
            completed_agents / total_agents * 100) if total_agents > 0 else 0

        return {
            "total_agents": total_agents,
            "completed_agents": completed_agents,
            "failed_agents": failed_agents,
            "running_agents": running_agents,
            "progress_percentage": round(progress_percentage, 2),
            "current_status": self.status.value
        }

    def add_agent_result(self, result: AgentExecutionResult):
        """Adiciona resultado de um agente e atualiza métricas"""
        self.agent_results[result.agent_name] = result
        self.last_update = datetime.now()

        # Atualizar contadores
        self.successful_agents = len([r for r in self.agent_results.values()
                                      if r.status == AgentExecutionStatus.COMPLETED])
        self.failed_agents = len([r for r in self.agent_results.values()
                                  if r.status == AgentExecutionStatus.FAILED])

        # Atualizar status geral se necessário
        if self.status == OrchestrationStatus.RUNNING:
            total_agents = sum(len(phase.agents)
                               for phase in self.config.phases)
            if self.successful_agents + self.failed_agents == total_agents:
                if self.failed_agents == 0:
                    self.status = OrchestrationStatus.COMPLETED
                elif self.successful_agents > 0:
                    self.status = OrchestrationStatus.PARTIAL_SUCCESS
                else:
                    self.status = OrchestrationStatus.FAILED

    def mark_completed(self):
        """Marca orquestração como completa e calcula métricas finais"""
        self.end_time = datetime.now()

        if self.start_time and self.end_time:
            self.total_execution_time_ms = int(
                (self.end_time - self.start_time).total_seconds() * 1000
            )

        # Determinar status final
        if self.failed_agents == 0:
            self.status = OrchestrationStatus.COMPLETED
        elif self.successful_agents > 0:
            self.status = OrchestrationStatus.PARTIAL_SUCCESS
        else:
            self.status = OrchestrationStatus.FAILED


class OrchestrationStatusResponse(BaseModel):
    """Response para consulta de status da orquestração"""
    orchestration_id: str
    client_id: str
    status: OrchestrationStatus
    progress: Dict[str, Any]
    agent_status: Dict[str, AgentExecutionStatus]
    start_time: datetime
    estimated_completion: Optional[datetime] = None
    error_summary: Optional[str] = None

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
