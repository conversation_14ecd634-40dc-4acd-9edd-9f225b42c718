"""
Product Owner Agent - Agente especializado em estratégia de produto e roadmap

Agente focado em análise estratégica de produtos:
- Análise de produto e mercado
- Identificação de oportunidades estratégicas
- Definição de roadmap baseado em dados
- Priorização de funcionalidades
- Recomendações de negócio
"""

import logging
from typing import Dict, Any, Optional, List
from agno.agent import Agent
from ...shared.model_config import model_config
from agno.tools.reasoning import ReasoningTools

from ..tools import ProductAnalysisTools

logger = logging.getLogger(__name__)


class ProductOwnerAgent:
    """Agente especializado em Product Ownership usando Agno"""

    def __init__(self, model_name: Optional[str] = None):
        """
        Inicializa o Product Owner Agent usando modelos do settings.py

        Args:
            model_name: Modelo específico (opcional, usa automático se None)
        """
        self.product_tools = ProductAnalysisTools()
        
        # Obter configuração de modelo baseada no settings.py
        agno_config = model_config.create_agno_config("analysis")
        
        if model_name:
            # Validar se modelo está disponível no settings
            available_models = [
                model_config.get_openai_model("analysis"),
                model_config.get_cohere_model("analysis"), 
                model_config.get_mistral_model("analysis")
            ]
            if model_name in available_models:
                self.model_name = model_name
            else:
                logger.warning(f"Modelo {model_name} não disponível no settings.py, usando automático")
                self.model_name = agno_config["model_id"]
        else:
            self.model_name = agno_config["model_id"]

        # Criar modelo baseado no provider
        if agno_config["provider"] == "openai":
            from agno.models.openai import OpenAI
            model_instance = OpenAI(id=self.model_name, api_key=agno_config["api_key"])
        elif agno_config["provider"] == "cohere":
            from agno.models.cohere import Cohere
            model_instance = Cohere(id=self.model_name, api_key=agno_config["api_key"])
        elif agno_config["provider"] == "mistral":
            from agno.models.mistral import Mistral
            model_instance = Mistral(id=self.model_name, api_key=agno_config["api_key"])
        else:
            from agno.models.openai import OpenAI
            model_instance = OpenAI(id="gpt-4o-mini", api_key=agno_config["api_key"])
            logger.warning(f"Provider {agno_config['provider']} não suportado, usando OpenAI fallback")

        self.agent = Agent(
            name="Product Owner Specialist",
            role="Expert in product strategy, roadmap planning, and market analysis",
            model=model_instance,
            tools=[ReasoningTools()],
            instructions=[
                "You are a Product Owner expert specializing in strategic product analysis and roadmap planning",
                "Analyze product data, market trends, and competitive landscape to provide strategic recommendations",
                "Focus on business value, user impact, and market opportunities in your recommendations",
                "Prioritize features based on ROI, user needs, and strategic business objectives",
                "Provide actionable roadmaps with clear timelines and success metrics",
                "Consider both short-term wins and long-term strategic positioning",
                "Base your recommendations on data-driven insights and market intelligence"
            ],
            description="Specialist in product strategy, roadmap planning, feature prioritization, and market opportunity analysis",
            markdown=True,
            show_tool_calls=True
        )

        logger.info(f"Product Owner Agent inicializado com {agno_config['provider']}:{self.model_name}")

    def analyze_product_strategy(self,
                                 product_data: Dict[str, Any],
                                 market_data: Optional[Dict[str, Any]] = None,
                                 competitor_data: Optional[Dict[str, Any]] = None,
                                 company_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Analisa estratégia de produto usando dados de mercado e concorrência

        Args:
            product_data: Dados do produto atual
            market_data: Dados de mercado (opcional)
            competitor_data: Dados dos concorrentes (opcional)
            company_context: Contexto da empresa para recomendações personalizadas

        Returns:
            Análise completa de estratégia de produto com roadmap
        """
        try:
            logger.info(
                "Iniciando análise estratégica de produto com Product Owner Agent")

            # Processar dados de produto usando tools especializadas
            product_insights = self.product_tools.analyze_product_data(
                product_data, market_data, competitor_data)

            # Analisar trends de mercado se dados disponíveis
            market_trends = {}
            if market_data:
                company_info = company_context or {}
                industry = company_info.get("sector", "technology")
                market_trends = self.product_tools.analyze_market_trends(
                    market_data, industry)

            # Extrair informações da empresa
            company_info = company_context or {}
            company_name = company_info.get("name", "Empresa")
            sector = company_info.get("sector", "Tecnologia")
            business_model = company_info.get(
                "business_model", "Não especificado")

            # Extrair dados do produto
            features = product_data.get("features", [])
            business_metrics = product_data.get("business_metrics", {})
            user_feedback = product_data.get("user_feedback", [])
            roadmap_requests = product_data.get("roadmap_requests", [])

            # Preparar contexto de mercado
            market_context = ""
            if market_trends:
                market_potential = market_trends.get(
                    "market_potential", "Não avaliado")
                growth_rate = market_trends.get("growth_rate", 0)
                high_impact_trends = market_trends.get(
                    "high_impact_trends", [])
                market_context = f"""
**CONTEXTO DE MERCADO:**
- Potencial de Mercado: {market_potential}
- Taxa de Crescimento: {growth_rate}%
- Tendências de Alto Impacto: {', '.join(high_impact_trends[:3]) if high_impact_trends else 'Não identificadas'}
"""

            # Construir prompt especializado para análise estratégica
            strategy_prompt = f"""
Você é um Product Owner estratégico analisando a empresa {company_name} (setor: {sector}, modelo: {business_model}). 

**INSIGHTS DE PRODUTO PROCESSADOS:**
- Maturidade do Produto: {product_insights.product_maturity}
- Posicionamento: {product_insights.market_positioning}
- Vantagem Competitiva: {product_insights.competitive_advantage}
- Completude de Features: {product_insights.feature_completeness}
- Product-Market Fit Score: {product_insights.product_market_fit_score}/100
- Score de Inovação: {product_insights.innovation_score}/100
- Score de Valor para Usuário: {product_insights.user_value_score}/100

**GAPS E OPORTUNIDADES IDENTIFICADOS:**
Gaps de Funcionalidades:
{chr(10).join(f"- {gap}" for gap in product_insights.feature_gaps) if product_insights.feature_gaps else "- Nenhum gap crítico identificado"}

Oportunidades de Mercado:
{chr(10).join(f"- {opp}" for opp in product_insights.market_opportunities) if product_insights.market_opportunities else "- Análise de mercado limitada"}

Ameaças Competitivas:
{chr(10).join(f"- {threat}" for threat in product_insights.competitive_threats) if product_insights.competitive_threats else "- Análise competitiva limitada"}

{market_context}

**DADOS ADICIONAIS:**
- Features Atuais: {len(features)} funcionalidades
- Feedback de Usuários: {len(user_feedback)} feedbacks coletados
- Solicitações de Roadmap: {len(roadmap_requests)} solicitações pendentes

Forneça uma análise estratégica completa incluindo:

1. **DIAGNÓSTICO ESTRATÉGICO:**
   - Avaliação da posição atual no mercado
   - Principais forças e fraquezas do produto
   - Análise SWOT resumida (Strengths, Weaknesses, Opportunities, Threats)

2. **OPORTUNIDADES ESTRATÉGICAS PRIORIZADAS:**
   - Top 3 oportunidades de curto prazo (3-6 meses)
   - Top 2 oportunidades de médio prazo (6-12 meses)
   - 1 oportunidade de longo prazo (12+ meses)
   - Para cada oportunidade: impacto esperado, esforço necessário, ROI estimado

3. **ROADMAP ESTRATÉGICO RECOMENDADO:**
   - Trimestre 1: Iniciativas prioritárias e quick wins
   - Trimestre 2-3: Desenvolvimentos de médio prazo
   - Trimestre 4+: Investimentos estratégicos de longo prazo
   - Para cada fase: objetivos, métricas de sucesso, recursos necessários

4. **PRIORIZAÇÃO DE FEATURES:**
   - Framework de priorização utilizado (ex: RICE, Value vs Effort)
   - Top 5 features a desenvolver com justificativa
   - Features a descontinuar ou adiar com razões

5. **MÉTRICAS DE SUCESSO:**
   - KPIs primários para cada iniciativa
   - Metas específicas e mensuráveis
   - Cronograma de avaliação e revisão

6. **RECOMENDAÇÕES DE EXECUÇÃO:**
   - Estrutura de equipe necessária
   - Riscos e mitigation strategies
   - Pontos de decisão críticos

Foque em recomendações práticas, baseadas em dados, e que gerem valor tangível para o negócio e usuários.
"""

            # Executar análise com o agente
            response = self.agent.run(strategy_prompt)

            # Preparar resultado estruturado
            result = {
                "agent_type": "ProductOwnerAgent",
                "product_insights": product_insights.model_dump(),
                "market_trends": market_trends,
                "strategic_analysis": response.content,
                "product_maturity": product_insights.product_maturity,
                "product_market_fit_score": product_insights.product_market_fit_score,
                "innovation_score": product_insights.innovation_score,
                "user_value_score": product_insights.user_value_score,
                "strategy_category": self._categorize_product_strategy(product_insights),
                "analysis_summary": {
                    "total_gaps": len(product_insights.feature_gaps),
                    "total_opportunities": len(product_insights.market_opportunities),
                    "total_threats": len(product_insights.competitive_threats),
                    "overall_score": round((product_insights.product_market_fit_score +
                                            product_insights.innovation_score +
                                            product_insights.user_value_score) / 3, 1)
                }
            }

            logger.info(
                f"Análise estratégica concluída - Score Geral: {result['analysis_summary']['overall_score']}/100")
            return result

        except Exception as e:
            logger.error(f"Erro na análise estratégica: {str(e)}")
            raise

    def _categorize_product_strategy(self, insights: Any) -> str:
        """Categoriza a estratégia de produto baseada nos insights"""
        pmf_score = insights.product_market_fit_score
        innovation_score = insights.innovation_score

        if pmf_score >= 80 and innovation_score >= 70:
            return "Líder de Mercado - Manter posição e inovar"
        elif pmf_score >= 70 and innovation_score >= 60:
            return "Posição Forte - Expandir e otimizar"
        elif pmf_score >= 60:
            return "Crescimento - Focar em product-market fit"
        elif innovation_score >= 70:
            return "Inovador - Buscar encaixe no mercado"
        else:
            return "Desenvolvimento - Melhorar produto e posicionamento"

    def create_feature_roadmap(self, features_data: List[Dict[str, Any]],
                               strategic_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Cria roadmap de features baseado em priorização estratégica

        Args:
            features_data: Lista de features com dados de priorização
            strategic_context: Contexto estratégico para priorização

        Returns:
            Roadmap estruturado de features
        """
        try:
            logger.info("Criando roadmap de features")

            # Priorizar features usando tools
            prioritized_features = self.product_tools.prioritize_roadmap_features(
                features_data)

            # Construir prompt para roadmap
            roadmap_prompt = f"""
Com base nas seguintes features priorizadas, crie um roadmap estruturado:

**FEATURES PRIORIZADAS:**
{chr(10).join([
                f"- {feature.feature} (Score: {feature.priority_score}, Valor: {feature.business_value}/10, Esforço: {feature.effort}/10, Impacto: {feature.user_impact}/10)"
                for feature in prioritized_features[:10]
            ])}

**CONTEXTO ESTRATÉGICO:**
{strategic_context.get('strategic_focus', 'Crescimento e inovação') if strategic_context else 'Crescimento e inovação'}

Organize em:
1. **Sprint 1-2 (0-6 semanas):** Quick wins e foundations
2. **Sprint 3-4 (6-12 semanas):** Features de impacto médio 
3. **Sprint 5-6 (12-18 semanas):** Iniciativas estratégicas
4. **Backlog (18+ semanas):** Features futuras

Para cada período, inclua:
- Objetivos do período
- Features incluídas com justificativa
- Recursos estimados
- Métricas de sucesso
"""

            response = self.agent.run(roadmap_prompt)

            return {
                "roadmap_analysis": response.content,
                "prioritized_features": [feature.model_dump() for feature in prioritized_features],
                "total_features": len(prioritized_features),
                "high_priority_count": len([f for f in prioritized_features if f.priority_score >= 7]),
                "roadmap_horizon": "18+ semanas"
            }

        except Exception as e:
            logger.error(f"Erro ao criar roadmap: {str(e)}")
            return {"error": str(e)}

    def assess_market_opportunities(self, market_data: Dict[str, Any],
                                    current_product: Dict[str, Any],
                                    company_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Avalia oportunidades de mercado para expansão estratégica

        Args:
            market_data: Dados de mercado e trends
            current_product: Dados do produto atual
            company_context: Contexto da empresa

        Returns:
            Análise de oportunidades de mercado
        """
        try:
            logger.info("Avaliando oportunidades de mercado")

            # Analisar trends usando tools
            company_info = company_context or {}
            industry = company_info.get("sector", "technology")
            market_analysis = self.product_tools.analyze_market_trends(
                market_data, industry)

            # Extrair contexto atual
            current_segments = current_product.get("current_segments", [])
            current_features = current_product.get("features", [])

            opportunity_prompt = f"""
Analise as seguintes oportunidades de mercado para expansão estratégica:

**ANÁLISE DE MERCADO ATUAL:**
- Potencial: {market_analysis.get('market_potential', 'Não avaliado')}
- Taxa de Crescimento: {market_analysis.get('growth_rate', 0)}%
- Trends de Alto Impacto: {', '.join(market_analysis.get('high_impact_trends', []))}
- Trends de Médio Impacto: {', '.join(market_analysis.get('medium_impact_trends', []))}

**POSIÇÃO ATUAL:**
- Segmentos Atendidos: {', '.join(current_segments) if current_segments else 'Não especificado'}
- Features Atuais: {len(current_features)} funcionalidades

Forneça análise incluindo:

1. **OPORTUNIDADES DE EXPANSÃO:**
   - Novos segmentos de mercado (com potencial de receita)
   - Novas geografias ou regiões
   - Novos casos de uso ou aplicações

2. **OPORTUNIDADES DE PRODUTO:**
   - Gaps de features baseados em trends
   - Integrações estratégicas necessárias
   - Inovações tecnológicas a explorar

3. **PARCERIAS ESTRATÉGICAS:**
   - Tipos de parceiros a buscar
   - Canais de distribuição alternativos
   - Ecossistemas para integração

4. **TIMELINE E PRIORIZAÇÃO:**
   - Oportunidades de curto prazo (0-6 meses)
   - Oportunidades de médio prazo (6-18 meses)
   - Oportunidades de longo prazo (18+ meses)

5. **ANÁLISE DE RISCO/RETORNO:**
   - Investment required vs expected return
   - Competitive risks
   - Market timing considerations

Foque em oportunidades concretas e acionáveis.
"""

            response = self.agent.run(opportunity_prompt)

            return {
                "market_opportunity_analysis": response.content,
                "market_analysis": market_analysis,
                "opportunity_score": self._calculate_opportunity_score(market_analysis),
                "current_positioning": {
                    "segments": current_segments,
                    "feature_count": len(current_features)
                }
            }

        except Exception as e:
            logger.error(f"Erro ao avaliar oportunidades: {str(e)}")
            return {"error": str(e)}

    def _calculate_opportunity_score(self, market_analysis: Dict[str, Any]) -> float:
        """Calcula score de oportunidade baseado na análise de mercado"""
        try:
            score = 50.0  # Score base

            growth_rate = market_analysis.get("growth_rate", 0)
            market_potential = market_analysis.get(
                "market_potential", "").lower()
            high_impact_trends = len(
                market_analysis.get("high_impact_trends", []))

            # Score baseado em crescimento
            if growth_rate > 20:
                score += 30
            elif growth_rate > 10:
                score += 20
            elif growth_rate > 5:
                score += 10

            # Score baseado em potencial
            if market_potential == "alto":
                score += 20
            elif market_potential == "médio":
                score += 10

            # Score baseado em trends
            score += min(high_impact_trends * 5, 20)

            return min(score, 100.0)

        except Exception:
            return 50.0

    def get_strategic_recommendations_summary(self, analysis_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Extrai um resumo estruturado das recomendações estratégicas

        Args:
            analysis_result: Resultado da análise estratégica

        Returns:
            Lista de recomendações estruturadas
        """
        try:
            product_insights = analysis_result.get("product_insights", {})
            analysis_summary = analysis_result.get("analysis_summary", {})

            recommendations = []

            # Recomendações baseadas em product-market fit
            pmf_score = product_insights.get("product_market_fit_score", 0)
            if pmf_score < 70:
                recommendations.append({
                    "category": "Product-Market Fit",
                    "recommendation": f"Melhorar product-market fit (atual: {pmf_score}/100)",
                    "priority": "High",
                    "type": "Strategic"
                })

            # Recomendações baseadas em inovação
            innovation_score = product_insights.get("innovation_score", 0)
            if innovation_score < 60:
                recommendations.append({
                    "category": "Innovation",
                    "recommendation": f"Aumentar capacidade de inovação (atual: {innovation_score}/100)",
                    "priority": "Medium",
                    "type": "Strategic"
                })

            # Recomendações baseadas em gaps
            feature_gaps = product_insights.get("feature_gaps", [])
            if feature_gaps:
                recommendations.append({
                    "category": "Feature Development",
                    "recommendation": f"Implementar {len(feature_gaps)} funcionalidades identificadas como gaps",
                    "priority": "High" if len(feature_gaps) > 3 else "Medium",
                    "type": "Tactical"
                })

            # Recomendações baseadas em oportunidades
            market_opportunities = product_insights.get(
                "market_opportunities", [])
            if market_opportunities:
                recommendations.append({
                    "category": "Market Expansion",
                    "recommendation": f"Explorar {len(market_opportunities)} oportunidades de mercado identificadas",
                    "priority": "Medium",
                    "type": "Strategic"
                })

            return recommendations

        except Exception as e:
            logger.error(f"Erro ao extrair recomendações: {str(e)}")
            return []
