"""
Agent Runner - Executor Individual de Agentes

Responsável por executar agentes individuais com lógica de retry,
timeout e tratamento de erros.
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, Optional, Type, List
from .schemas import AgentExecutionR<PERSON>ult, AgentExecutionStatus, OrchestrationConfig
from .context import OrchestrationContext, ContextualData

# Imports dos agentes existentes
# TODO: Corrigir imports quando sistema estiver integrado
try:
    # Tentar imports relativos primeiro (contexto de produção)
    from ..teams.architect_team import ArchitectTeam
    from ..agents.ux_ui_designer_agent import UXUIDesignerAgent
    from ..agents.product_owner_agent import ProductOwnerAgent
    from ..agents.seo_agent import SEOSpecialistAgent
    from ..agents.benchmark_agent import BenchmarkAgent
    from ..agents.technical_writer_agent import TechnicalWriterAgent
except ImportError as e:
    # Erro crítico - imports obrigatórios não encontrados
    logger = logging.getLogger(__name__)
    logger.critical(f"Imports de agentes obrigatórios falharam: {e}")
    logger.critical("Sistema não pode funcionar sem os agentes reais")
    raise ImportError(f"Agentes obrigatórios não encontrados: {e}") from e

logger = logging.getLogger(__name__)


class AgentRunner:
    """
    Executor individual de agentes com robustez e monitoramento.

    Responsável por:
    - Instanciar e executar agentes específicos
    - Gerenciar timeouts e retries
    - Capturar métricas de execução
    - Tratar erros e fallbacks
    """

    # Mapeamento de nomes para classes de agentes
    AGENT_CLASSES = {
        "technical_architect": ArchitectTeam,
        "uxui_designer": UXUIDesignerAgent,
        "product_owner": ProductOwnerAgent,
        "seo_specialist": SEOSpecialistAgent,
        "benchmark_agent": BenchmarkAgent,
        "technical_writer": TechnicalWriterAgent
    }

    # Mapeamento de métodos de análise por agente
    AGENT_METHODS = {
        "technical_architect": "analyze_complete",
        "uxui_designer": "analyze_ux_ui_design",
        "product_owner": "analyze_product_strategy",
        "seo_specialist": "analyze_seo_strategy",
        "benchmark_agent": "analyze_competitive_landscape",
        "technical_writer": "consolidate_insights"
    }

    def __init__(self, config: OrchestrationConfig):
        self.config = config
        self.agent_instances: Dict[str, Any] = {}
        self._initialize_agents()

    def _initialize_agents(self):
        """Inicializa instâncias dos agentes"""
        try:
            for agent_name, agent_class in self.AGENT_CLASSES.items():
                self.agent_instances[agent_name] = agent_class()
                logger.debug(f"Agente {agent_name} inicializado")

            logger.info(
                f"AgentRunner inicializado com {len(self.agent_instances)} agentes")

        except Exception as e:
            logger.error(f"Erro ao inicializar agentes: {e}")
            raise

    async def run_agent(
        self,
        agent_name: str,
        context: OrchestrationContext,
        phase_id: int
    ) -> AgentExecutionResult:
        """
        Executa um agente específico com robustez

        Args:
            agent_name: Nome do agente a executar
            context: Contexto da orquestração
            phase_id: ID da fase atual

        Returns:
            Resultado da execução do agente
        """
        start_time = datetime.now()

        # Criar resultado inicial com valores padrão
        result = AgentExecutionResult(
            agent_name=agent_name,
            status=AgentExecutionStatus.PENDING,
            start_time=start_time,
            phase_id=phase_id,
            end_time=None,
            execution_time_ms=0,
            result_data={},
            analysis_id="",
            error_message=None,
            input_data_size_kb=0.0,
            output_data_size_kb=0.0
        )

        logger.info(f"Iniciando execução do agente {agent_name}")

        try:
            # Validar se agente existe
            if agent_name not in self.agent_instances:
                raise ValueError(f"Agente '{agent_name}' não encontrado")

            result.status = AgentExecutionStatus.RUNNING

            # Executar com retry
            success = False
            last_error = None

            for attempt in range(self.config.max_retries_per_agent + 1):
                try:
                    result.retry_count = attempt

                    if attempt > 0:
                        result.status = AgentExecutionStatus.RETRYING
                        logger.info(
                            f"Tentativa {attempt + 1} para agente {agent_name}")

                    # Executar agente com timeout
                    execution_result = await self._execute_agent_with_timeout(
                        agent_name, context, phase_id
                    )

                    # Sucesso
                    result.result_data = execution_result
                    result.status = AgentExecutionStatus.COMPLETED
                    success = True
                    break

                except asyncio.TimeoutError as e:
                    last_error = f"Timeout na execução (tentativa {attempt + 1})"
                    logger.warning(
                        f"Timeout no agente {agent_name} - tentativa {attempt + 1}")

                    if attempt < self.config.max_retries_per_agent:
                        # Backoff exponencial
                        await asyncio.sleep(2 ** attempt)

                except Exception as e:
                    last_error = f"Erro na execução: {str(e)}"
                    logger.error(
                        f"Erro no agente {agent_name} - tentativa {attempt + 1}: {e}")

                    if attempt < self.config.max_retries_per_agent:
                        # Backoff exponencial
                        await asyncio.sleep(2 ** attempt)

            # Se não teve sucesso após todos os retries
            if not success:
                result.status = AgentExecutionStatus.FAILED
                result.error_message = last_error or "Falha após todos os retries"
                logger.error(
                    f"Agente {agent_name} falhou após {self.config.max_retries_per_agent + 1} tentativas")

        except Exception as e:
            result.status = AgentExecutionStatus.FAILED
            result.error_message = f"Erro crítico: {str(e)}"
            logger.error(f"Erro crítico no agente {agent_name}: {e}")

        finally:
            # Finalizar resultado
            result.end_time = datetime.now()
            if result.start_time and result.end_time:
                execution_time = (result.end_time -
                                  result.start_time).total_seconds() * 1000
                result.execution_time_ms = int(execution_time)

            logger.info(
                f"Agente {agent_name} finalizado - Status: {result.status.value} "
                f"- Tempo: {result.execution_time_ms}ms"
            )

        return result

    async def _execute_agent_with_timeout(
        self,
        agent_name: str,
        context: OrchestrationContext,
        phase_id: int
    ) -> Dict[str, Any]:
        """
        Executa agente com timeout configurado

        Args:
            agent_name: Nome do agente
            context: Contexto da orquestração
            phase_id: ID da fase

        Returns:
            Resultado da execução do agente
        """
        # Preparar dados de entrada específicos para o agente
        agent_input = self._prepare_agent_input(agent_name, context, phase_id)

        # Obter instância e método do agente
        agent_instance = self.agent_instances[agent_name]
        method_name = self.AGENT_METHODS[agent_name]
        agent_method = getattr(agent_instance, method_name)

        # Executar com timeout
        timeout = self.config.timeout_per_agent_seconds
        try:
            result = await asyncio.wait_for(
                agent_method(agent_input),
                timeout=timeout
            )

            logger.debug(f"Agente {agent_name} executado com sucesso")
            return result

        except asyncio.TimeoutError:
            logger.error(f"Timeout de {timeout}s no agente {agent_name}")
            raise

        except Exception as e:
            logger.error(f"Erro na execução do agente {agent_name}: {e}")
            raise

    def _prepare_agent_input(
        self,
        agent_name: str,
        context: OrchestrationContext,
        phase_id: int
    ) -> Dict[str, Any]:
        """
        Prepara dados de entrada específicos para cada agente

        Args:
            agent_name: Nome do agente
            context: Contexto da orquestração
            phase_id: ID da fase

        Returns:
            Dados de entrada contextualizados para o agente
        """
        try:
            # Preparar dados básicos
            base_input = context.prepare_agent_input(agent_name, [])

            # Adicionar dados contextuais específicos
            if agent_name == "technical_architect":
                return ContextualData.for_technical_architect(context)
            elif agent_name == "uxui_designer":
                return ContextualData.for_uxui_designer(context)
            elif agent_name == "product_owner":
                return ContextualData.for_product_owner(context)
            elif agent_name == "seo_specialist":
                return ContextualData.for_seo_specialist(context)
            elif agent_name == "benchmark_agent":
                return ContextualData.for_benchmark_agent(context)
            elif agent_name == "technical_writer":
                return ContextualData.for_technical_writer(context)
            else:
                return base_input

        except Exception as e:
            logger.error(f"Erro ao preparar input para {agent_name}: {e}")
            return {}

    def _calculate_data_size(self, data: Any) -> float:
        """
        Calcula tamanho estimado dos dados em KB

        Args:
            data: Dados para calcular o tamanho

        Returns:
            Tamanho estimado em KB
        """
        try:
            import sys
            size_bytes = sys.getsizeof(str(data))
            return round(size_bytes / 1024, 2)
        except:
            return 0.0

    def get_agent_health(self) -> Dict[str, Any]:
        """
        Retorna status de saúde dos agentes

        Returns:
            Mapa de status de saúde por agente
        """
        health_status = {}

        for agent_name in self.AGENT_CLASSES.keys():
            try:
                # Verificar se agente está inicializado
                agent_instance = self.agent_instances.get(agent_name)
                if agent_instance is None:
                    health_status[agent_name] = {
                        "status": "ERROR",
                        "message": "Agente não inicializado"
                    }
                    continue

                # Verificar se métodos existem
                method_name = self.AGENT_METHODS.get(agent_name)
                if method_name is None or not hasattr(agent_instance, method_name):
                    health_status[agent_name] = {
                        "status": "ERROR",
                        "message": f"Método '{method_name}' não encontrado"
                    }
                    continue

                health_status[agent_name] = {
                    "status": "HEALTHY",
                    "message": "Agente operacional"
                }

            except Exception as e:
                health_status[agent_name] = {
                    "status": "ERROR",
                    "message": f"Erro de saúde: {str(e)}"
                }

        return health_status

    async def validate_agent(self, agent_name: str) -> bool:
        """
        Valida se um agente específico está operacional

        Args:
            agent_name: Nome do agente a validar

        Returns:
            True se agente está operacional
        """
        try:
            if agent_name not in self.AGENT_CLASSES:
                logger.error(f"Agente '{agent_name}' não está registrado")
                return False

            if agent_name not in self.agent_instances:
                logger.error(f"Agente '{agent_name}' não foi inicializado")
                return False

            agent_instance = self.agent_instances[agent_name]
            method_name = self.AGENT_METHODS[agent_name]

            if not hasattr(agent_instance, method_name):
                logger.error(
                    f"Agente '{agent_name}' não possui método '{method_name}'")
                return False

            logger.debug(f"Agente '{agent_name}' validado com sucesso")
            return True

        except Exception as e:
            logger.error(f"Erro na validação do agente '{agent_name}': {e}")
            return False

    async def test_agent_connectivity(self, agent_name: str) -> Dict[str, Any]:
        """
        Testa conectividade básica de um agente

        Args:
            agent_name: Nome do agente a testar

        Returns:
            Resultado do teste de conectividade
        """
        start_time = datetime.now()

        try:
            # Validar se agente existe
            if not await self.validate_agent(agent_name):
                return {
                    "agent_name": agent_name,
                    "status": "FAILED",
                    "message": "Agente inválido",
                    "test_duration_ms": 0
                }

            # Criar contexto de teste mínimo
            test_context = OrchestrationContext(
                client_id="test",
                client_data={},
                config=self.config
            )

            # Tentar executar com timeout curto
            try:
                await asyncio.wait_for(
                    self._execute_agent_with_timeout(
                        agent_name, test_context, 0),
                    timeout=5.0  # 5 segundos de timeout para teste
                )

                end_time = datetime.now()
                duration_ms = int(
                    (end_time - start_time).total_seconds() * 1000)

                return {
                    "agent_name": agent_name,
                    "status": "HEALTHY",
                    "message": "Teste de conectividade bem-sucedido",
                    "test_duration_ms": duration_ms
                }

            except asyncio.TimeoutError:
                return {
                    "agent_name": agent_name,
                    "status": "TIMEOUT",
                    "message": "Timeout no teste de conectividade",
                    "test_duration_ms": 5000
                }

        except Exception as e:
            end_time = datetime.now()
            duration_ms = int((end_time - start_time).total_seconds() * 1000)

            return {
                "agent_name": agent_name,
                "status": "ERROR",
                "message": f"Erro no teste: {str(e)}",
                "test_duration_ms": duration_ms
            }

    def cleanup(self):
        """Limpa recursos dos agentes"""
        logger.info("Iniciando cleanup do AgentRunner")
        self.agent_instances.clear()
        logger.info("AgentRunner cleanup concluído")


class AgentExecutionMetrics:
    """Coletor de métricas de execução dos agentes"""

    def __init__(self):
        self.execution_count = 0
        self.total_execution_time_ms = 0
        self.success_count = 0
        self.failure_count = 0
        self.retry_count = 0
        self.timeout_count = 0
        self.agent_performance: Dict[str, List[int]] = {}

    def record_execution(self, result: AgentExecutionResult):
        """Registra resultado de uma execução"""
        self.execution_count += 1

        # Verificar se execution_time_ms não é None
        execution_time = result.execution_time_ms or 0
        self.total_execution_time_ms += execution_time

        if result.status == AgentExecutionStatus.COMPLETED:
            self.success_count += 1
        elif result.status == AgentExecutionStatus.FAILED:
            self.failure_count += 1

        self.retry_count += result.retry_count

        # Registrar performance por agente
        if result.agent_name not in self.agent_performance:
            self.agent_performance[result.agent_name] = []

        self.agent_performance[result.agent_name].append(execution_time)

        self._update_performance_stats(result)

    def _update_performance_stats(self, result: AgentExecutionResult):
        """Atualiza estatísticas de performance"""
        # Detectar timeouts (execução > 30s geralmente indica timeout)
        execution_time = result.execution_time_ms or 0
        if execution_time > 30000:
            self.timeout_count += 1

    def get_performance_report(self) -> Dict[str, Any]:
        """Gera relatório de performance consolidado"""
        if self.execution_count == 0:
            return {"status": "No executions recorded"}

        success_rate = (self.success_count / self.execution_count) * 100
        avg_execution_time = self.total_execution_time_ms / self.execution_count

        agent_stats = {}
        for agent_name, times in self.agent_performance.items():
            if times:
                agent_stats[agent_name] = {
                    "executions": len(times),
                    "avg_time_ms": sum(times) / len(times),
                    "min_time_ms": min(times),
                    "max_time_ms": max(times)
                }

        return {
            "total_executions": self.execution_count,
            "success_rate": round(success_rate, 2),
            "failure_count": self.failure_count,
            "retry_count": self.retry_count,
            "timeout_count": self.timeout_count,
            "avg_execution_time_ms": round(avg_execution_time, 2),
            "agent_performance": agent_stats
        }
