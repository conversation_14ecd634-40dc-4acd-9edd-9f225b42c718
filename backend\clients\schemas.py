"""
Schemas MongoDB para validação de dados de clientes e reports
"""

import logging
from pymongo.errors import OperationFailure
from .db import clients_collection
from typing import Dict, Any, List

logger = logging.getLogger(__name__)


# Schema para validação de projetos sugeridos
PROJETO_SCHEMA = {
    "bsonType": "object",
    "properties": {
        "cliente_id": {"bsonType": "objectId"},
        "nome_projeto": {"bsonType": "string"},
        "tags": {
            "bsonType": "array",
            "items": {"bsonType": "string"},
            "maxItems": 3,
            "minItems": 1
        },
        "status": {
            "bsonType": "string",
            "enum": ["Sugestão", "Em Análise", "Aprovado", "Rejeitado", "Em Execução", "Concluído"]
        },
        "resumo": {
            "bsonType": "string",
            "maxLength": 100
        },
        "progresso": {
            "bsonType": ["int", "double"],
            "minimum": 0,
            "maximum": 100
        },
        "equipe": {"bsonType": "string"},
        "justificativa": {"bsonType": "string"},
        "importancia": {"bsonType": "string"},
        "pontuacao": {
            "bsonType": ["int", "double"],
            "minimum": 0,
            "maximum": 100
        },
        # ✅ NOVOS CAMPOS: Adicionados campos do novo formato boardroom_advisor_agents
        "detalhamento": {"bsonType": "string"},
        "beneficios": {"bsonType": "string"},
        "agentes_justificativa": {
            "bsonType": "array",
            "items": {
                "bsonType": "object",
                "properties": {
                    "nome_agente": {"bsonType": "string"},
                    "justificativa": {"bsonType": "string"},
                    "pontuacao": {
                        "bsonType": ["int", "double"],
                        "minimum": 0,
                        "maximum": 100
                    },
                    "tags": {
                        "bsonType": "array",
                        "items": {"bsonType": "string"},
                        "maxItems": 3
                    }
                },
                "required": ["nome_agente", "justificativa", "pontuacao", "tags"]
            }
        },
        "agente_responsavel": {"bsonType": "string"},
        "area_especialidade": {"bsonType": "string"},
        "created_at": {"bsonType": "date"},
        "updated_at": {"bsonType": "date"}
    },
    "required": ["cliente_id", "nome_projeto", "tags", "status", "resumo", "progresso",
                 "equipe", "justificativa", "importancia", "pontuacao", "created_at"]
}


# Schema para validação de dados de funding
FUNDING_SCHEMA = {
    "bsonType": "object",
    "properties": {
        "resumo_funding": {
            "bsonType": "object",
            "properties": {
                "total_captado": {"bsonType": "string"},
                "numero_rodadas": {"bsonType": "string"},
                "ultimo_valuation": {"bsonType": "string"},
                "status_funding": {"bsonType": "string"}
            },
            "required": ["total_captado", "numero_rodadas", "ultimo_valuation", "status_funding"]
        },
        "rodadas_investimento": {
            "bsonType": "array",
            "items": {
                "bsonType": "object",
                "properties": {
                    "rodada": {"bsonType": "string"},
                    "data": {"bsonType": "string"},
                    "valor": {"bsonType": "string"},
                    "valuation_pre": {"bsonType": "string"},
                    "valuation_pos": {"bsonType": "string"},
                    "lead_investor": {"bsonType": "string"},
                    "outros_investidores": {"bsonType": "array"},
                    "uso_capital": {"bsonType": "string"}
                },
                "required": ["rodada", "data", "valor"]
            }
        },
        "investidores_principais": {
            "bsonType": "object",
            "properties": {
                "venture_capital": {"bsonType": "array"},
                "private_equity": {"bsonType": "array"},
                "anjos_investidores": {"bsonType": "array"},
                "investidores_corporativos": {"bsonType": "array"},
                "fundos_governo": {"bsonType": "array"}
            },
            "required": ["venture_capital", "private_equity", "anjos_investidores",
                         "investidores_corporativos", "fundos_governo"]
        },
        "metricas_investimento": {
            "bsonType": "object",
            "properties": {
                "ticket_medio_rodada": {"bsonType": "string"},
                "tempo_medio_entre_rodadas": {"bsonType": "string"},
                "crescimento_valuation": {"bsonType": "string"},
                "burn_rate_estimado": {"bsonType": "string"},
                "runway_estimado": {"bsonType": "string"}
            },
            "required": ["ticket_medio_rodada", "tempo_medio_entre_rodadas", "crescimento_valuation"]
        },
        "analise_competitiva_funding": {
            "bsonType": "object",
            "properties": {
                "comparacao_setor": {"bsonType": "string"},
                "benchmark_concorrentes": {"bsonType": "string"},
                "posicionamento_mercado": {"bsonType": "string"}
            }
        },
        "perspectivas_futuras": {
            "bsonType": "object",
            "properties": {
                "proxima_rodada_estimada": {"bsonType": "string"},
                "necessidades_capital": {"bsonType": "string"},
                "potencial_ipo_aquisicao": {"bsonType": "string"},
                "riscos_funding": {"bsonType": "array"}
            }
        },
        "fontes_informacao": {
            "bsonType": "object",
            "properties": {
                "fontes_utilizadas": {"bsonType": "array"},
                "confiabilidade_dados": {"bsonType": "string"},
                "data_ultima_atualizacao": {"bsonType": "string"},
                "observacoes": {"bsonType": "string"}
            },
            "required": ["fontes_utilizadas", "confiabilidade_dados"]
        },
        "validation_status": {"bsonType": "string"},
        "processed_at": {"bsonType": "string"},
        "data_quality_score": {"bsonType": ["double", "int"]}
    },
    "required": ["resumo_funding", "rodadas_investimento", "investidores_principais",
                 "metricas_investimento", "fontes_informacao"]
}


# Schema para validação de dados de presença digital
PRESENCA_DIGITAL_SCHEMA = {
    "bsonType": "object",
    "properties": {
        "resumo_presenca_digital": {
            "bsonType": "object",
            "properties": {
                "domain_authority": {"bsonType": "string"},
                "ranking_seo_geral": {"bsonType": "string"},
                "trafego_mensal_estimado": {"bsonType": "string"},
                "score_presenca_digital": {"bsonType": "string"}
            },
            "required": ["domain_authority", "ranking_seo_geral", "trafego_mensal_estimado"]
        },
        "metricas_seo": {
            "bsonType": "object",
            "properties": {
                "domain_authority": {"bsonType": "string"},
                "page_authority": {"bsonType": "string"},
                "backlinks_totais": {"bsonType": "string"},
                "dominios_referencia": {"bsonType": "string"},
                "keywords_organicas": {"bsonType": "string"},
                "keywords_top_10": {"bsonType": "string"},
                "trafego_organico_mensal": {"bsonType": "string"},
                "valor_trafego_organico": {"bsonType": "string"}
            },
            "required": ["domain_authority", "backlinks_totais", "keywords_organicas"]
        },
        "principais_keywords": {
            "bsonType": "array",
            "items": {
                "bsonType": "object",
                "properties": {
                    "keyword": {"bsonType": "string"},
                    "posicao": {"bsonType": "string"},
                    "volume_busca": {"bsonType": "string"},
                    "dificuldade": {"bsonType": "string"}
                },
                "required": ["keyword", "posicao"]
            }
        },
        "redes_sociais": {
            "bsonType": "object",
            "properties": {
                "linkedin": {
                    "bsonType": "object",
                    "properties": {
                        "seguidores": {"bsonType": "string"},
                        "engajamento_medio": {"bsonType": "string"},
                        "frequencia_posts": {"bsonType": "string"},
                        "qualidade_conteudo": {"bsonType": "string"}
                    }
                },
                "instagram": {
                    "bsonType": "object",
                    "properties": {
                        "seguidores": {"bsonType": "string"},
                        "engajamento_medio": {"bsonType": "string"},
                        "frequencia_posts": {"bsonType": "string"},
                        "qualidade_conteudo": {"bsonType": "string"}
                    }
                },
                "twitter": {
                    "bsonType": "object",
                    "properties": {
                        "seguidores": {"bsonType": "string"},
                        "engajamento_medio": {"bsonType": "string"},
                        "frequencia_posts": {"bsonType": "string"},
                        "qualidade_conteudo": {"bsonType": "string"}
                    }
                },
                "facebook": {
                    "bsonType": "object",
                    "properties": {
                        "seguidores": {"bsonType": "string"},
                        "engajamento_medio": {"bsonType": "string"},
                        "frequencia_posts": {"bsonType": "string"},
                        "qualidade_conteudo": {"bsonType": "string"}
                    }
                },
                "youtube": {
                    "bsonType": "object",
                    "properties": {
                        "inscritos": {"bsonType": "string"},
                        "visualizacoes_totais": {"bsonType": "string"},
                        "frequencia_videos": {"bsonType": "string"},
                        "qualidade_conteudo": {"bsonType": "string"}
                    }
                }
            },
            "required": ["linkedin", "instagram", "twitter", "facebook", "youtube"]
        },
        "analise_site": {
            "bsonType": "object",
            "properties": {
                "velocidade_carregamento": {"bsonType": "string"},
                "mobile_friendly": {"bsonType": "string"},
                "certificado_ssl": {"bsonType": "string"},
                "estrutura_urls": {"bsonType": "string"},
                "meta_tags": {"bsonType": "string"},
                "schema_markup": {"bsonType": "string"},
                "analytics_implementado": {"bsonType": "string"}
            }
        },
        "conteudo_digital": {
            "bsonType": "object",
            "properties": {
                "blog_ativo": {"bsonType": "string"},
                "frequencia_blog": {"bsonType": "string"},
                "qualidade_conteudo_blog": {"bsonType": "string"},
                "materiais_educativos": {"bsonType": "string"},
                "webinars_eventos": {"bsonType": "string"},
                "podcasts": {"bsonType": "string"}
            }
        },
        "reputacao_online": {
            "bsonType": "object",
            "properties": {
                "google_reviews": {
                    "bsonType": "object",
                    "properties": {
                        "nota_media": {"bsonType": "string"},
                        "numero_avaliacoes": {"bsonType": "string"},
                        "tendencia_avaliacoes": {"bsonType": "string"}
                    }
                },
                "mencoes_imprensa": {"bsonType": "string"},
                "premios_reconhecimentos": {"bsonType": "string"},
                "sentiment_geral": {"bsonType": "string"}
            }
        },
        "competitividade_digital": {
            "bsonType": "object",
            "properties": {
                "ranking_vs_concorrentes": {"bsonType": "string"},
                "gaps_identificados": {"bsonType": "array"},
                "oportunidades_melhoria": {"bsonType": "array"},
                "benchmark_setor": {"bsonType": "string"}
            }
        },
        "recomendacoes_estrategicas": {
            "bsonType": "object",
            "properties": {
                "prioridade_alta": {"bsonType": "array"},
                "prioridade_media": {"bsonType": "array"},
                "prioridade_baixa": {"bsonType": "array"},
                "investimento_estimado": {"bsonType": "string"},
                "roi_esperado": {"bsonType": "string"}
            }
        },
        "fontes_analise": {
            "bsonType": "object",
            "properties": {
                "ferramentas_utilizadas": {"bsonType": "array"},
                "confiabilidade_dados": {"bsonType": "string"},
                "data_analise": {"bsonType": "string"},
                "limitacoes": {"bsonType": "string"},
                "observacoes": {"bsonType": "string"}
            },
            "required": ["ferramentas_utilizadas", "confiabilidade_dados"]
        },
        "validation_status": {"bsonType": "string"},
        "processed_at": {"bsonType": "string"},
        "data_quality_score": {"bsonType": ["double", "int"]}
    },
    "required": ["resumo_presenca_digital", "metricas_seo", "principais_keywords",
                 "redes_sociais", "fontes_analise"]
}


# Schema para validação de dados de parcerias
PARCERIAS_SCHEMA = {
    "bsonType": "object",
    "properties": {
        "resumo_parcerias": {
            "bsonType": "object",
            "properties": {
                "total_parceiros_identificados": {"bsonType": "string"},
                "nivel_maturidade_parcerias": {"bsonType": "string"},
                "estrategia_parcerias": {"bsonType": "string"},
                "foco_geografico": {"bsonType": "string"}
            },
            "required": ["total_parceiros_identificados", "nivel_maturidade_parcerias", "estrategia_parcerias"]
        },
        "parcerias_comerciais": {
            "bsonType": "object",
            "properties": {
                "revendedores": {"bsonType": "array"},
                "distribuidores": {"bsonType": "array"},
                "canais_parceiros": {"bsonType": "array"},
                "consultores_implementadores": {"bsonType": "array"},
                "parceiros_go_to_market": {"bsonType": "array"}
            },
            "required": ["revendedores", "distribuidores", "canais_parceiros"]
        },
        "parcerias_tecnologicas": {
            "bsonType": "object",
            "properties": {
                "integracoes_nativas": {"bsonType": "array"},
                "apis_conectadas": {"bsonType": "array"},
                "plataformas_marketplace": {"bsonType": "array"},
                "parceiros_tecnicos": {"bsonType": "array"},
                "fornecedores_infraestrutura": {"bsonType": "array"}
            },
            "required": ["integracoes_nativas", "apis_conectadas", "plataformas_marketplace"]
        },
        "parcerias_estrategicas": {
            "bsonType": "object",
            "properties": {
                "joint_ventures": {"bsonType": "array"},
                "acordos_desenvolvimento": {"bsonType": "array"},
                "parceiros_investidores": {"bsonType": "array"},
                "aliancas_setoriais": {"bsonType": "array"},
                "parcerias_inovacao": {"bsonType": "array"}
            },
            "required": ["joint_ventures", "acordos_desenvolvimento", "parceiros_investidores"]
        },
        "programas_parceiros": {
            "bsonType": "object",
            "properties": {
                "possui_programa_formal": {"bsonType": "string"},
                "tiers_programa": {"bsonType": "array"},
                "beneficios_oferecidos": {"bsonType": "array"},
                "requisitos_participacao": {"bsonType": "array"},
                "suporte_parceiros": {"bsonType": "string"},
                "treinamento_certificacao": {"bsonType": "string"}
            },
            "required": ["possui_programa_formal", "beneficios_oferecidos"]
        },
        "cases_sucesso_parcerias": {
            "bsonType": "array",
            "items": {
                "bsonType": "object",
                "properties": {
                    "parceiro": {"bsonType": "string"},
                    "tipo_parceria": {"bsonType": "string"},
                    "resultado_obtido": {"bsonType": "string"},
                    "valor_gerado": {"bsonType": "string"}
                },
                "required": ["parceiro", "tipo_parceria", "resultado_obtido"]
            }
        },
        "analise_competitiva_parcerias": {
            "bsonType": "object",
            "properties": {
                "benchmark_concorrentes": {"bsonType": "string"},
                "gaps_identificados": {"bsonType": "array"},
                "oportunidades_parcerias": {"bsonType": "array"},
                "ameacas_parcerias": {"bsonType": "array"}
            },
            "required": ["benchmark_concorrentes", "gaps_identificados", "oportunidades_parcerias"]
        },
        "metricas_parcerias": {
            "bsonType": "object",
            "properties": {
                "receita_via_parceiros_estimada": {"bsonType": "string"},
                "numero_parceiros_ativos": {"bsonType": "string"},
                "crescimento_parcerias": {"bsonType": "string"},
                "roi_programa_parceiros": {"bsonType": "string"}
            },
            "required": ["receita_via_parceiros_estimada", "numero_parceiros_ativos"]
        },
        "fontes_informacao": {
            "bsonType": "object",
            "properties": {
                "fontes_utilizadas": {"bsonType": "array"},
                "confiabilidade_dados": {"bsonType": "string"},
                "data_ultima_atualizacao": {"bsonType": "string"},
                "observacoes": {"bsonType": "string"}
            },
            "required": ["fontes_utilizadas", "confiabilidade_dados"]
        }
    },
    "required": ["resumo_parcerias", "parcerias_comerciais", "parcerias_tecnologicas",
                 "parcerias_estrategicas", "fontes_informacao"]
}


# Schema para validação de dados de modelo de negócio (EXPANDIDO)
MODELO_NEGOCIO_SCHEMA = {
    "bsonType": "object",
    "properties": {
        "classificacao_modelo": {
            "bsonType": "object",
            "properties": {
                "tipo_principal": {"bsonType": "string"},
                "subtipos_detalhados": {
                    "bsonType": "object",
                    "properties": {
                        "categoria_primaria": {"bsonType": "string"},
                        "modelo_distribuicao": {"bsonType": "string"},
                        "modelo_receita": {"bsonType": "string"},
                        "arquitetura_valor": {"bsonType": "string"}
                    },
                    "required": ["categoria_primaria", "modelo_receita"]
                },
                "modelo_hibrido": {
                    "bsonType": "object",
                    "properties": {
                        "e_hibrido": {"bsonType": "string"},
                        "combinacoes": {"bsonType": "array"},
                        "razao_hibridismo": {"bsonType": "string"},
                        "evolucao_hibrida": {"bsonType": "string"}
                    },
                    "required": ["e_hibrido"]
                },
                "complexidade_modelo": {
                    "bsonType": "object",
                    "properties": {
                        "nivel": {"bsonType": "string"},
                        "fatores_complexidade": {"bsonType": "array"},
                        "score_complexidade": {"bsonType": "string"}
                    },
                    "required": ["nivel"]
                },
                "maturidade_digital": {"bsonType": "string"}
            },
            "required": ["tipo_principal", "subtipos_detalhados", "modelo_hibrido", "complexidade_modelo"]
        },
        "proposta_valor": {
            "bsonType": "object",
            "properties": {
                "valor_principal": {"bsonType": "string"},
                "problemas_resolvidos": {
                    "bsonType": "object",
                    "properties": {
                        "primarios": {"bsonType": "array"},
                        "secundarios": {"bsonType": "array"},
                        "criticidade": {"bsonType": "string"}
                    },
                    "required": ["primarios"]
                },
                "beneficios_entregues": {
                    "bsonType": "object",
                    "properties": {
                        "tangiveis": {"bsonType": "array"},
                        "intangiveis": {"bsonType": "array"},
                        "quantificacao": {"bsonType": "string"}
                    },
                    "required": ["tangiveis"]
                },
                "diferencial_competitivo": {
                    "bsonType": "object",
                    "properties": {
                        "principal": {"bsonType": "string"},
                        "secundarios": {"bsonType": "array"},
                        "sustentabilidade": {"bsonType": "string"},
                        "barreiras_entrada": {"bsonType": "array"}
                    },
                    "required": ["principal"]
                },
                "valor_quantificado": {
                    "bsonType": "object",
                    "properties": {
                        "roi_cliente": {"bsonType": "string"},
                        "payback_period": {"bsonType": "string"},
                        "metricas_valor": {"bsonType": "string"}
                    }
                }
            },
            "required": ["valor_principal", "problemas_resolvidos", "beneficios_entregues", "diferencial_competitivo"]
        },
        "segmentos_clientes": {
            "bsonType": "object",
            "properties": {
                "segmentacao_principal": {
                    "bsonType": "object",
                    "properties": {
                        "segmento_primario": {"bsonType": "string"},
                        "percentual_receita": {"bsonType": "string"},
                        "caracteristicas": {"bsonType": "array"}
                    },
                    "required": ["segmento_primario"]
                },
                "segmentos_secundarios": {
                    "bsonType": "array",
                    "items": {
                        "bsonType": "object",
                        "properties": {
                            "segmento": {"bsonType": "string"},
                            "percentual_receita": {"bsonType": "string"},
                            "potencial_crescimento": {"bsonType": "string"}
                        },
                        "required": ["segmento"]
                    }
                },
                "perfil_clientes": {
                    "bsonType": "object",
                    "properties": {
                        "tamanho_empresas": {"bsonType": "string"},
                        "ticket_medio": {"bsonType": "string"},
                        "ciclo_vendas": {"bsonType": "string"},
                        "decisor_compra": {"bsonType": "string"}
                    },
                    "required": ["tamanho_empresas"]
                },
                "setores_atendidos": {
                    "bsonType": "object",
                    "properties": {
                        "verticais_principais": {"bsonType": "array"},
                        "especializacao_vertical": {"bsonType": "string"},
                        "requisitos_regulatorios": {"bsonType": "array"}
                    },
                    "required": ["verticais_principais"]
                },
                "geografias_atendidas": {
                    "bsonType": "object",
                    "properties": {
                        "mercados_principais": {"bsonType": "array"},
                        "estrategia_expansao": {"bsonType": "string"},
                        "adaptacoes_locais": {"bsonType": "string"}
                    }
                }
            },
            "required": ["segmentacao_principal", "perfil_clientes", "setores_atendidos"]
        },
        "canais_distribuicao": {
            "bsonType": "object",
            "properties": {
                "mix_canais": {
                    "bsonType": "object",
                    "properties": {
                        "canais_principais": {"bsonType": "array"},
                        "estrategia_omnichannel": {"bsonType": "string"},
                        "integracao_canais": {"bsonType": "string"}
                    },
                    "required": ["canais_principais"]
                },
                "estrategia_go_to_market": {
                    "bsonType": "object",
                    "properties": {
                        "abordagem_principal": {"bsonType": "string"},
                        "funil_aquisicao": {"bsonType": "string"},
                        "investimento_gtm": {"bsonType": "string"}
                    },
                    "required": ["abordagem_principal"]
                },
                "canais_especificos": {
                    "bsonType": "object",
                    "properties": {
                        "vendas": {"bsonType": "array"},
                        "marketing": {"bsonType": "array"},
                        "suporte": {"bsonType": "array"}
                    },
                    "required": ["vendas", "marketing"]
                }
            },
            "required": ["mix_canais", "estrategia_go_to_market", "canais_especificos"]
        },
        "relacionamento_clientes": {
            "bsonType": "object",
            "properties": {
                "estrategia_relacionamento": {
                    "bsonType": "object",
                    "properties": {
                        "tipo_relacionamento": {"bsonType": "string"},
                        "personalizacao": {"bsonType": "string"},
                        "frequencia_interacao": {"bsonType": "string"}
                    },
                    "required": ["tipo_relacionamento"]
                },
                "estrategia_retencao": {
                    "bsonType": "object",
                    "properties": {
                        "taticas_principais": {"bsonType": "array"},
                        "indicadores_satisfacao": {"bsonType": "array"},
                        "programas_sucesso": {"bsonType": "string"}
                    },
                    "required": ["taticas_principais"]
                },
                "programas_engajamento": {
                    "bsonType": "object",
                    "properties": {
                        "fidelidade": {"bsonType": "string"},
                        "comunidade": {"bsonType": "string"},
                        "advocacy": {"bsonType": "string"}
                    }
                },
                "crescimento_conta": {
                    "bsonType": "object",
                    "properties": {
                        "estrategia_upsell": {"bsonType": "string"},
                        "cross_sell": {"bsonType": "string"},
                        "expansion_revenue": {"bsonType": "string"}
                    }
                }
            },
            "required": ["estrategia_relacionamento", "estrategia_retencao"]
        },
        "recursos_principais": {
            "bsonType": "object",
            "properties": {
                "recursos_fisicos": {
                    "bsonType": "object",
                    "properties": {
                        "ativos_principais": {"bsonType": "array"},
                        "intensidade_capital": {"bsonType": "string"},
                        "necessidade_investimento": {"bsonType": "string"}
                    }
                },
                "recursos_intelectuais": {
                    "bsonType": "object",
                    "properties": {
                        "propriedade_intelectual": {"bsonType": "array"},
                        "dados_proprietarios": {"bsonType": "string"},
                        "algoritmos_ia": {"bsonType": "string"},
                        "marca_reputacao": {"bsonType": "string"}
                    },
                    "required": ["propriedade_intelectual"]
                },
                "recursos_humanos": {
                    "bsonType": "object",
                    "properties": {
                        "talento_critico": {"bsonType": "array"},
                        "competencias_core": {"bsonType": "array"},
                        "cultura_organizacional": {"bsonType": "string"}
                    },
                    "required": ["talento_critico", "competencias_core"]
                },
                "recursos_financeiros": {
                    "bsonType": "object",
                    "properties": {
                        "situacao_financeira": {"bsonType": "string"},
                        "acesso_capital": {"bsonType": "string"},
                        "estrutura_custos": {"bsonType": "string"}
                    }
                },
                "recursos_tecnologicos": {
                    "bsonType": "object",
                    "properties": {
                        "plataforma_tech": {"bsonType": "array"},
                        "infraestrutura": {"bsonType": "string"},
                        "capacidade_inovacao": {"bsonType": "string"}
                    },
                    "required": ["plataforma_tech"]
                }
            },
            "required": ["recursos_intelectuais", "recursos_humanos", "recursos_tecnologicos"]
        },
        "metricas_modelo_negocio": {
            "bsonType": "object",
            "properties": {
                "unit_economics": {
                    "bsonType": "object",
                    "properties": {
                        "cac": {"bsonType": "string"},
                        "ltv": {"bsonType": "string"},
                        "ltv_cac_ratio": {"bsonType": "string"},
                        "payback_period": {"bsonType": "string"},
                        "gross_margin": {"bsonType": "string"}
                    }
                },
                "crescimento": {
                    "bsonType": "object",
                    "properties": {
                        "growth_rate": {"bsonType": "string"},
                        "retention_rate": {"bsonType": "string"},
                        "expansion_rate": {"bsonType": "string"},
                        "viral_coefficient": {"bsonType": "string"}
                    }
                },
                "operacionais": {
                    "bsonType": "object",
                    "properties": {
                        "capital_efficiency": {"bsonType": "string"},
                        "working_capital": {"bsonType": "string"},
                        "asset_turnover": {"bsonType": "string"},
                        "operating_leverage": {"bsonType": "string"}
                    }
                },
                "mercado": {
                    "bsonType": "object",
                    "properties": {
                        "market_share": {"bsonType": "string"},
                        "tam_sam_som": {"bsonType": "string"},
                        "penetracao_mercado": {"bsonType": "string"}
                    }
                }
            }
        },
        "escalabilidade_modelo": {
            "bsonType": "object",
            "properties": {
                "analise_escalabilidade": {
                    "bsonType": "object",
                    "properties": {
                        "nivel_escalabilidade": {"bsonType": "string"},
                        "score_escalabilidade": {"bsonType": "string"},
                        "justificativa": {"bsonType": "string"}
                    },
                    "required": ["nivel_escalabilidade"]
                },
                "fatores_limitantes": {
                    "bsonType": "object",
                    "properties": {
                        "limitantes_internos": {"bsonType": "array"},
                        "limitantes_externos": {"bsonType": "array"},
                        "gargalos_principais": {"bsonType": "array"}
                    }
                },
                "alavancas_crescimento": {
                    "bsonType": "object",
                    "properties": {
                        "alavancas_primarias": {"bsonType": "array"},
                        "alavancas_secundarias": {"bsonType": "array"},
                        "network_effects": {"bsonType": "string"},
                        "economies_scale": {"bsonType": "string"}
                    },
                    "required": ["alavancas_primarias"]
                },
                "requisitos_escala": {
                    "bsonType": "object",
                    "properties": {
                        "investimentos_necessarios": {"bsonType": "string"},
                        "recursos_criticos": {"bsonType": "string"},
                        "timeline_escala": {"bsonType": "string"}
                    }
                }
            },
            "required": ["analise_escalabilidade", "alavancas_crescimento"]
        },
        "evolucao_modelo": {
            "bsonType": "object",
            "properties": {
                "historico_evolucao": {
                    "bsonType": "object",
                    "properties": {
                        "modelo_original": {"bsonType": "string"},
                        "principais_pivots": {
                            "bsonType": "array",
                            "items": {
                                "bsonType": "object",
                                "properties": {
                                    "data_aproximada": {"bsonType": "string"},
                                    "tipo_pivot": {"bsonType": "string"},
                                    "razao": {"bsonType": "string"},
                                    "resultado": {"bsonType": "string"}
                                }
                            }
                        },
                        "marcos_evolucao": {"bsonType": "array"}
                    },
                    "required": ["modelo_original"]
                },
                "tendencias_futuras": {
                    "bsonType": "object",
                    "properties": {
                        "tendencias_evolucao": {"bsonType": "array"},
                        "futuro_modelo": {"bsonType": "string"},
                        "inovacoes_esperadas": {"bsonType": "array"},
                        "adaptacoes_mercado": {"bsonType": "string"}
                    },
                    "required": ["tendencias_evolucao"]
                }
            },
            "required": ["historico_evolucao", "tendencias_futuras"]
        },
        "riscos_modelo": {
            "bsonType": "object",
            "properties": {
                "riscos_estrategicos": {
                    "bsonType": "object",
                    "properties": {
                        "riscos_principais": {"bsonType": "array"},
                        "probabilidade": {"bsonType": "string"},
                        "impacto": {"bsonType": "string"}
                    },
                    "required": ["riscos_principais"]
                },
                "vulnerabilidades": {
                    "bsonType": "object",
                    "properties": {
                        "vulnerabilidades_tecnicas": {"bsonType": "array"},
                        "vulnerabilidades_mercado": {"bsonType": "array"},
                        "vulnerabilidades_competitivas": {"bsonType": "array"}
                    },
                    "required": ["vulnerabilidades_tecnicas", "vulnerabilidades_mercado"]
                },
                "dependencias_criticas": {
                    "bsonType": "object",
                    "properties": {
                        "fornecedores_chave": {"bsonType": "array"},
                        "tecnologias_criticas": {"bsonType": "array"},
                        "regulatorias": {"bsonType": "array"}
                    },
                    "required": ["fornecedores_chave", "tecnologias_criticas"]
                },
                "planos_contingencia": {
                    "bsonType": "object",
                    "properties": {
                        "estrategias_mitigacao": {"bsonType": "array"},
                        "planos_backup": {"bsonType": "string"},
                        "monitoramento": {"bsonType": "string"}
                    }
                }
            },
            "required": ["riscos_estrategicos", "vulnerabilidades", "dependencias_criticas"]
        },
        "benchmarking_setor": {
            "bsonType": "object",
            "properties": {
                "comparacao_modelos": {"bsonType": "string"},
                "melhores_praticas": {"bsonType": "array"},
                "gaps_mercado": {"bsonType": "array"},
                "inovacoes_setor": {"bsonType": "array"}
            }
        }
    },
    "required": ["classificacao_modelo", "proposta_valor", "segmentos_clientes",
                 "canais_distribuicao", "relacionamento_clientes", "recursos_principais",
                 "escalabilidade_modelo", "evolucao_modelo", "riscos_modelo"]
}


# Schema para validação de dados de pricing (EXPANDIDO)
PRICING_SCHEMA = {
    "bsonType": "object",
    "properties": {
        "resumo_pricing": {
            "bsonType": "object",
            "properties": {
                "estrategia_principal": {"bsonType": "string"},
                "modelo_pricing": {"bsonType": "string"},
                "transparencia_precos": {"bsonType": "string"},
                "complexidade_estrutura": {"bsonType": "string"},
                "framework_pricing": {"bsonType": "string"},
                "maturidade_pricing": {"bsonType": "string"}
            }
        },
        "estrutura_precos": {
            "bsonType": "object",
            "properties": {
                "tipo_cobranca": {"bsonType": "string"},
                "moeda_principal": {"bsonType": "string"},
                "inclui_setup_fee": {"bsonType": "string"},
                "periodo_trial": {"bsonType": "string"},
                "politica_cancelamento": {"bsonType": "string"},
                "contratos_minimos": {"bsonType": "string"},
                "flexibilidade_pagamento": {"bsonType": "string"},
                "moedas_aceitas": {"bsonType": "array"},
                "metodos_pagamento": {"bsonType": "array"}
            }
        },
        "tiers_precos_detalhados": {
            "bsonType": "array",
            "items": {
                "bsonType": "object",
                "properties": {
                    "nome_tier": {"bsonType": "string"},
                    "posicionamento_tier": {"bsonType": "string"},
                    "preco_mensal": {"bsonType": "string"},
                    "preco_anual": {"bsonType": "string"},
                    "desconto_anual": {"bsonType": "string"},
                    "principais_features": {"bsonType": "array"},
                    "features_exclusivas": {"bsonType": "array"},
                    "limites_uso": {"bsonType": "string"},
                    "target_audience": {"bsonType": "string"},
                    "valor_por_usuario": {"bsonType": "string"},
                    "escalabilidade_tier": {"bsonType": "string"},
                    "roi_estimado": {"bsonType": "string"},
                    "casos_uso_tipicos": {"bsonType": "array"}
                }
            }
        },
        "politicas_desconto_expandidas": {
            "bsonType": "object",
            "properties": {
                "desconto_volume": {
                    "bsonType": "object",
                    "properties": {
                        "estrutura": {"bsonType": "string"},
                        "thresholds": {"bsonType": "string"},
                        "percentuais": {"bsonType": "string"}
                    }
                },
                "desconto_anual": {
                    "bsonType": "object",
                    "properties": {
                        "percentual": {"bsonType": "string"},
                        "estrategia": {"bsonType": "string"}
                    }
                },
                "programas_especiais": {
                    "bsonType": "object",
                    "properties": {
                        "desconto_estudante": {"bsonType": "string"},
                        "desconto_startup": {"bsonType": "string"},
                        "desconto_ong": {"bsonType": "string"},
                        "programa_parceiros": {"bsonType": "string"}
                    }
                },
                "negociacao_enterprise": {
                    "bsonType": "object",
                    "properties": {
                        "flexibilidade": {"bsonType": "string"},
                        "fatores_negociacao": {"bsonType": "array"},
                        "processo_negociacao": {"bsonType": "string"}
                    }
                },
                "promocoes_sazonais": {
                    "bsonType": "object",
                    "properties": {
                        "promocoes_identificadas": {"bsonType": "array"},
                        "estrategia_promocional": {"bsonType": "string"},
                        "frequencia_promocoes": {"bsonType": "string"}
                    }
                }
            }
        },
        "add_ons_extras_detalhados": {
            "bsonType": "object",
            "properties": {
                "servicos_adicionais": {
                    "bsonType": "array",
                    "items": {
                        "bsonType": "object",
                        "properties": {
                            "nome_servico": {"bsonType": "string"},
                            "preco": {"bsonType": "string"},
                            "modelo_cobranca": {"bsonType": "string"},
                            "penetracao_estimada": {"bsonType": "string"}
                        }
                    }
                },
                "integracao_premium": {
                    "bsonType": "object",
                    "properties": {
                        "integracoes_pagas": {"bsonType": "array"},
                        "modelo_pricing_integracoes": {"bsonType": "string"}
                    }
                },
                "suporte_premium": {
                    "bsonType": "object",
                    "properties": {
                        "niveis_suporte": {"bsonType": "array"},
                        "pricing_suporte": {"bsonType": "string"},
                        "sla_oferecidos": {"bsonType": "array"}
                    }
                },
                "consultoria_implementacao": {
                    "bsonType": "object",
                    "properties": {
                        "servicos_consultoria": {"bsonType": "array"},
                        "modelo_pricing_consultoria": {"bsonType": "string"},
                        "expertise_oferecida": {"bsonType": "array"}
                    }
                },
                "treinamento_certificacao": {
                    "bsonType": "object",
                    "properties": {
                        "programas_treinamento": {"bsonType": "array"},
                        "certificacoes_oferecidas": {"bsonType": "array"},
                        "pricing_treinamento": {"bsonType": "string"}
                    }
                }
            }
        },
        "metricas_pricing_avancadas": {
            "bsonType": "object",
            "properties": {
                "financial_metrics": {
                    "bsonType": "object",
                    "properties": {
                        "ticket_medio": {"bsonType": "string"},
                        "ltv_estimado": {"bsonType": "string"},
                        "churn_rate": {"bsonType": "string"},
                        "arpu": {"bsonType": "string"},
                        "arpa": {"bsonType": "string"},
                        "aov": {"bsonType": "string"}
                    }
                },
                "conversion_metrics": {
                    "bsonType": "object",
                    "properties": {
                        "conversion_rate": {"bsonType": "string"},
                        "upgrade_rate": {"bsonType": "string"},
                        "downgrade_rate": {"bsonType": "string"},
                        "reactivation_rate": {"bsonType": "string"}
                    }
                },
                "growth_metrics": {
                    "bsonType": "object",
                    "properties": {
                        "expansion_revenue": {"bsonType": "string"},
                        "net_revenue_retention": {"bsonType": "string"},
                        "gross_revenue_retention": {"bsonType": "string"},
                        "quick_ratio": {"bsonType": "string"}
                    }
                },
                "pricing_efficiency": {
                    "bsonType": "object",
                    "properties": {
                        "price_realization": {"bsonType": "string"},
                        "discounting_rate": {"bsonType": "string"},
                        "pricing_variance": {"bsonType": "string"}
                    }
                }
            }
        },
        "analise_competitiva_expandida": {
            "bsonType": "object",
            "properties": {
                "posicionamento_mercado": {
                    "bsonType": "object",
                    "properties": {
                        "categoria_preco": {"bsonType": "string"},
                        "percentil_mercado": {"bsonType": "string"},
                        "estrategia_diferenciacao": {"bsonType": "string"}
                    }
                },
                "matrix_competitiva": {
                    "bsonType": "array",
                    "items": {
                        "bsonType": "object",
                        "properties": {
                            "concorrente": {"bsonType": "string"},
                            "categoria_concorrente": {"bsonType": "string"},
                            "tier_comparavel": {"bsonType": "string"},
                            "preco_similar": {"bsonType": "string"},
                            "diferencial_pricing": {"bsonType": "string"},
                            "vantagem_desvantagem": {"bsonType": "string"},
                            "features_comparison": {"bsonType": "string"},
                            "market_share_estimado": {"bsonType": "string"}
                        }
                    }
                },
                "gaps_oportunidades": {
                    "bsonType": "object",
                    "properties": {
                        "gaps_pricing": {"bsonType": "array"},
                        "oportunidades_preco": {"bsonType": "array"},
                        "ameacas_competitivas": {"bsonType": "array"}
                    }
                },
                "benchmarking_setorial": {
                    "bsonType": "object",
                    "properties": {
                        "media_setor": {"bsonType": "string"},
                        "lider_preco": {"bsonType": "string"},
                        "inovador_pricing": {"bsonType": "string"},
                        "tendencias_setor": {"bsonType": "array"}
                    }
                }
            }
        },
        "estrategias_monetizacao_avancadas": {
            "bsonType": "object",
            "properties": {
                "revenue_streams": {
                    "bsonType": "object",
                    "properties": {
                        "receita_principal": {"bsonType": "string"},
                        "receitas_secundarias": {
                            "bsonType": "array",
                            "items": {
                                "bsonType": "object",
                                "properties": {
                                    "fonte": {"bsonType": "string"},
                                    "percentual_estimado": {"bsonType": "string"},
                                    "modelo_pricing": {"bsonType": "string"},
                                    "potencial_crescimento": {"bsonType": "string"}
                                }
                            }
                        }
                    }
                },
                "growth_strategies": {
                    "bsonType": "object",
                    "properties": {
                        "modelo_crescimento": {"bsonType": "string"},
                        "land_expand_strategy": {"bsonType": "string"},
                        "viral_mechanisms": {"bsonType": "string"},
                        "network_effects": {"bsonType": "string"}
                    }
                },
                "upsell_crosssell": {
                    "bsonType": "object",
                    "properties": {
                        "upsell_opportunities": {
                            "bsonType": "array",
                            "items": {
                                "bsonType": "object",
                                "properties": {
                                    "tipo_upsell": {"bsonType": "string"},
                                    "timing_upsell": {"bsonType": "string"},
                                    "valor_medio_upsell": {"bsonType": "string"},
                                    "conversion_rate_upsell": {"bsonType": "string"}
                                }
                            }
                        },
                        "cross_sell_products": {
                            "bsonType": "array",
                            "items": {
                                "bsonType": "object",
                                "properties": {
                                    "produto_crosssell": {"bsonType": "string"},
                                    "complementarity": {"bsonType": "string"},
                                    "attach_rate": {"bsonType": "string"},
                                    "revenue_impact": {"bsonType": "string"}
                                }
                            }
                        }
                    }
                },
                "customer_lifetime_optimization": {
                    "bsonType": "object",
                    "properties": {
                        "estrategia_retencao": {"bsonType": "string"},
                        "programas_fidelidade": {"bsonType": "string"},
                        "switching_costs": {"bsonType": "string"},
                        "lock_in_mechanisms": {"bsonType": "string"}
                    }
                }
            }
        },
        "elasticidade_preco_avancada": {
            "bsonType": "object",
            "properties": {
                "analise_elasticidade": {
                    "bsonType": "object",
                    "properties": {
                        "sensibilidade_preco": {"bsonType": "string"},
                        "price_elasticity_demand": {"bsonType": "string"},
                        "fatores_sensibilidade": {"bsonType": "array"},
                        "segments_elasticity": {"bsonType": "string"}
                    }
                },
                "historico_mudancas": {
                    "bsonType": "object",
                    "properties": {
                        "mudancas_historicas": {
                            "bsonType": "array",
                            "items": {
                                "bsonType": "object",
                                "properties": {
                                    "data_mudanca": {"bsonType": "string"},
                                    "tipo_mudanca": {"bsonType": "string"},
                                    "magnitude_mudanca": {"bsonType": "string"},
                                    "razao_mudanca": {"bsonType": "string"},
                                    "impacto_observado": {"bsonType": "string"}
                                }
                            }
                        },
                        "frequencia_ajustes": {"bsonType": "string"},
                        "sazonalidade_pricing": {"bsonType": "string"}
                    }
                },
                "testing_optimization": {
                    "bsonType": "object",
                    "properties": {
                        "evidencias_ab_testing": {"bsonType": "string"},
                        "price_testing_methodology": {"bsonType": "string"},
                        "optimization_metrics": {"bsonType": "string"},
                        "dynamic_pricing": {"bsonType": "string"}
                    }
                }
            }
        },
        "psicologia_pricing": {
            "bsonType": "object",
            "properties": {
                "anchoring_effects": {
                    "bsonType": "object",
                    "properties": {
                        "price_anchors": {"bsonType": "string"},
                        "anchor_strategy": {"bsonType": "string"},
                        "decoy_pricing": {"bsonType": "string"}
                    }
                },
                "cognitive_biases": {
                    "bsonType": "object",
                    "properties": {
                        "charm_pricing": {"bsonType": "string"},
                        "bundling_strategy": {"bsonType": "string"},
                        "loss_aversion": {"bsonType": "string"},
                        "social_proof_pricing": {"bsonType": "string"}
                    }
                },
                "framing_effects": {
                    "bsonType": "object",
                    "properties": {
                        "price_framing": {"bsonType": "string"},
                        "value_communication": {"bsonType": "string"},
                        "payment_framing": {"bsonType": "string"}
                    }
                }
            }
        },
        "tendencias_pricing_futuro": {
            "bsonType": "object",
            "properties": {
                "evolucao_precos": {
                    "bsonType": "object",
                    "properties": {
                        "tendencia_historica": {"bsonType": "string"},
                        "drivers_mudanca": {"bsonType": "array"},
                        "projecoes_futuras": {"bsonType": "string"}
                    }
                },
                "inovacoes_pricing": {
                    "bsonType": "object",
                    "properties": {
                        "tendencia_setor": {"bsonType": "string"},
                        "inovacoes_modelo": {"bsonType": "string"},
                        "tecnologias_impacto": {"bsonType": "string"},
                        "regulacao_impacto": {"bsonType": "string"}
                    }
                },
                "competitive_dynamics": {
                    "bsonType": "object",
                    "properties": {
                        "impacto_concorrencia": {"bsonType": "string"},
                        "price_wars_history": {"bsonType": "string"},
                        "collaborative_pricing": {"bsonType": "string"},
                        "market_maturity_impact": {"bsonType": "string"}
                    }
                }
            }
        },
        "insights_estrategicos": {
            "bsonType": "object",
            "properties": {
                "pontos_fortes_pricing": {"bsonType": "array"},
                "areas_melhoria": {"bsonType": "array"},
                "oportunidades_revenue": {"bsonType": "array"},
                "riscos_pricing": {"bsonType": "array"},
                "recomendacoes_estrategicas": {"bsonType": "array"},
                "kpis_monitoramento": {"bsonType": "array"}
            }
        },
        "fontes_informacao": {
            "bsonType": "object",
            "properties": {
                "fontes_utilizadas": {"bsonType": "array"},
                "metodologia_coleta": {"bsonType": "string"},
                "confiabilidade_dados": {"bsonType": "string"},
                "limitacoes_analise": {"bsonType": "string"},
                "data_ultima_atualizacao": {"bsonType": "string"},
                "observacoes": {"bsonType": "string"},
                "validacao_cruzada": {"bsonType": "string"}
            }
        }
    }
}


# Schema para validação de dados de canais de distribuição, reviews e certificações
CANAIS_REVIEWS_SCHEMA = {
    "bsonType": "object",
    "properties": {
        "dados_canais_distribuicao": {
            "bsonType": "object",
            "properties": {
                "resumo_canais": {
                    "bsonType": "object",
                    "properties": {
                        "estrategia_principal": {"bsonType": "string"},
                        "modelo_distribuicao": {"bsonType": "string"},
                        "cobertura_geografica": {"bsonType": "string"},
                        "maturidade_canais": {"bsonType": "string"},
                        "complexidade_rede": {"bsonType": "string"},
                        "integracoes_sistemas": {"bsonType": "string"}
                    },
                    "required": ["estrategia_principal", "modelo_distribuicao", "cobertura_geografica"]
                },
                "canais_principais": {
                    "bsonType": "array",
                    "items": {
                        "bsonType": "object",
                        "properties": {
                            "nome_canal": {"bsonType": "string"},
                            "tipo_canal": {"bsonType": "string"},
                            "importancia_estrategica": {"bsonType": "string"},
                            "percentual_vendas": {"bsonType": "string"},
                            "target_audience": {"bsonType": "string"},
                            "vantagens_canal": {"bsonType": "string"},
                            "limitacoes_canal": {"bsonType": "string"}
                        },
                        "required": ["nome_canal", "tipo_canal", "importancia_estrategica"]
                    }
                },
                "estrategia_omnichannel": {
                    "bsonType": "object",
                    "properties": {
                        "integracao_canais": {"bsonType": "string"},
                        "experiencia_unificada": {"bsonType": "string"},
                        "dados_compartilhados": {"bsonType": "string"},
                        "inventario_centralizado": {"bsonType": "string"},
                        "politicas_unificadas": {"bsonType": "string"}
                    }
                },
                "parceiros_distribuicao": {
                    "bsonType": "object",
                    "properties": {
                        "revendedores_autorizados": {"bsonType": "array"},
                        "distribuidores_regionais": {"bsonType": "array"},
                        "marketplaces_principais": {"bsonType": "array"},
                        "canais_especialistas": {"bsonType": "array"},
                        "programas_parceiros": {"bsonType": "string"}
                    }
                },
                "performance_canais": {
                    "bsonType": "object",
                    "properties": {
                        "canal_mais_eficiente": {"bsonType": "string"},
                        "crescimento_por_canal": {"bsonType": "string"},
                        "custos_aquisicao": {"bsonType": "string"},
                        "tempo_ciclo_vendas": {"bsonType": "string"},
                        "satisfacao_parceiros": {"bsonType": "string"}
                    }
                },
                "go_to_market": {
                    "bsonType": "object",
                    "properties": {
                        "estrategia_lancamento": {"bsonType": "string"},
                        "segmentacao_canais": {"bsonType": "string"},
                        "adaptacao_local": {"bsonType": "string"},
                        "suporte_vendas": {"bsonType": "string"},
                        "treinamento_canais": {"bsonType": "string"}
                    }
                }
            },
            "required": ["resumo_canais", "canais_principais"]
        },
        "dados_reviews_feedback": {
            "bsonType": "object",
            "properties": {
                "resumo_reputacao": {
                    "bsonType": "object",
                    "properties": {
                        "score_reputacao_geral": {"bsonType": "string"},
                        "numero_total_reviews": {"bsonType": "string"},
                        "tendencia_satisfacao": {"bsonType": "string"},
                        "confiabilidade_dados": {"bsonType": "string"},
                        "fonte_principal_reviews": {"bsonType": "string"},
                        "periodo_analise": {"bsonType": "string"}
                    },
                    "required": ["score_reputacao_geral", "numero_total_reviews", "tendencia_satisfacao"]
                },
                "plataformas_review": {
                    "bsonType": "object",
                    "properties": {
                        "google_reviews": {
                            "bsonType": "object",
                            "properties": {
                                "rating_medio": {"bsonType": "string"},
                                "numero_avaliacoes": {"bsonType": "string"},
                                "comentarios_recentes": {"bsonType": "string"}
                            }
                        },
                        "reclame_aqui": {
                            "bsonType": "object",
                            "properties": {
                                "nota_reclame_aqui": {"bsonType": "string"},
                                "tempo_resposta": {"bsonType": "string"},
                                "indice_solucao": {"bsonType": "string"}
                            }
                        },
                        "trustpilot": {
                            "bsonType": "object",
                            "properties": {
                                "rating_trustpilot": {"bsonType": "string"},
                                "numero_reviews": {"bsonType": "string"},
                                "classificacao_empresa": {"bsonType": "string"}
                            }
                        },
                        "app_stores": {
                            "bsonType": "object",
                            "properties": {
                                "google_play_rating": {"bsonType": "string"},
                                "app_store_rating": {"bsonType": "string"},
                                "downloads_estimados": {"bsonType": "string"}
                            }
                        },
                        "reviews_especializados": {
                            "bsonType": "object",
                            "properties": {
                                "sites_especializados": {"bsonType": "array"},
                                "ratings_especializados": {"bsonType": "string"},
                                "premios_mercado": {"bsonType": "array"}
                            }
                        }
                    }
                },
                "sentiment_geral": {
                    "bsonType": "object",
                    "properties": {
                        "sentiment_positivo": {"bsonType": "string"},
                        "sentiment_neutro": {"bsonType": "string"},
                        "sentiment_negativo": {"bsonType": "string"},
                        "palavras_chave_positivas": {"bsonType": "array"},
                        "palavras_chave_negativas": {"bsonType": "array"}
                    }
                },
                "pontos_fortes_fracos": {
                    "bsonType": "object",
                    "properties": {
                        "principais_elogios": {"bsonType": "array"},
                        "principais_criticas": {"bsonType": "array"},
                        "aspectos_mais_valorizados": {"bsonType": "array"},
                        "areas_melhoria": {"bsonType": "array"},
                        "comparacao_concorrentes": {"bsonType": "string"}
                    }
                },
                "tendencias_satisfacao": {
                    "bsonType": "object",
                    "properties": {
                        "evolucao_ratings": {"bsonType": "string"},
                        "sazonalidade_reviews": {"bsonType": "string"},
                        "impacto_melhorias": {"bsonType": "string"},
                        "nps_estimado": {"bsonType": "string"},
                        "taxa_recomendacao": {"bsonType": "string"}
                    }
                },
                "gestao_feedback": {
                    "bsonType": "object",
                    "properties": {
                        "resposta_reviews": {"bsonType": "string"},
                        "tempo_resposta_medio": {"bsonType": "string"},
                        "qualidade_respostas": {"bsonType": "string"},
                        "resolucao_problemas": {"bsonType": "string"},
                        "proatividade_melhorias": {"bsonType": "string"}
                    }
                }
            },
            "required": ["resumo_reputacao", "plataformas_review"]
        },
        "dados_certificacoes": {
            "bsonType": "object",
            "properties": {
                "resumo_certificacoes": {
                    "bsonType": "object",
                    "properties": {
                        "total_certificacoes": {"bsonType": "string"},
                        "nivel_certificacao": {"bsonType": "string"},
                        "areas_cobertas": {"bsonType": "array"},
                        "validade_media": {"bsonType": "string"},
                        "investimento_estimado": {"bsonType": "string"},
                        "impacto_mercado": {"bsonType": "string"}
                    },
                    "required": ["total_certificacoes", "nivel_certificacao"]
                },
                "certificacoes_tecnicas": {
                    "bsonType": "object",
                    "properties": {
                        "iso_quality": {"bsonType": "array"},
                        "certificacoes_setor": {"bsonType": "array"},
                        "cloud_providers": {"bsonType": "array"},
                        "seguranca_dados": {"bsonType": "array"},
                        "metodologias_dev": {"bsonType": "array"}
                    }
                },
                "premiacao_industria": {
                    "bsonType": "object",
                    "properties": {
                        "premios_nacionais": {"bsonType": "array"},
                        "premios_internacionais": {"bsonType": "array"},
                        "rankings_mercado": {"bsonType": "array"},
                        "reconhecimentos_midia": {"bsonType": "array"},
                        "cases_premiados": {"bsonType": "array"}
                    }
                },
                "compliance_regulatorio": {
                    "bsonType": "object",
                    "properties": {
                        "licencas_operacao": {"bsonType": "array"},
                        "conformidade_legal": {"bsonType": "array"},
                        "auditorias_externas": {"bsonType": "string"},
                        "compliance_internacional": {"bsonType": "array"},
                        "certificacoes_ambientais": {"bsonType": "array"}
                    }
                },
                "reconhecimentos": {
                    "bsonType": "object",
                    "properties": {
                        "great_place_work": {"bsonType": "string"},
                        "b_corp": {"bsonType": "string"},
                        "startup_awards": {"bsonType": "array"},
                        "lideranca_mercado": {"bsonType": "array"},
                        "inovacao_premios": {"bsonType": "array"}
                    }
                },
                "impacto_certificacoes": {
                    "bsonType": "object",
                    "properties": {
                        "credibilidade_mercado": {"bsonType": "string"},
                        "diferenciacao_competitiva": {"bsonType": "string"},
                        "acesso_novos_mercados": {"bsonType": "string"},
                        "premium_pricing": {"bsonType": "string"},
                        "parcerias_estrategicas": {"bsonType": "string"}
                    }
                }
            },
            "required": ["resumo_certificacoes"]
        },
        "analise_reputacional": {
            "bsonType": "object",
            "properties": {
                "posicionamento_mercado": {"bsonType": "string"},
                "confianca_marca": {"bsonType": "string"},
                "awareness_mercado": {"bsonType": "string"},
                "recomendacao_espontanea": {"bsonType": "string"},
                "vulnerabilidades_reputacao": {"bsonType": "array"},
                "oportunidades_melhoria": {"bsonType": "array"}
            }
        },
        "competitividade_canais": {
            "bsonType": "object",
            "properties": {
                "benchmark_concorrentes": {"bsonType": "array"},
                "gaps_distribuicao": {"bsonType": "array"},
                "oportunidades_expansao": {"bsonType": "array"},
                "ameacas_canais": {"bsonType": "array"}
            }
        },
        "fontes_informacao": {
            "bsonType": "object",
            "properties": {
                "fontes_utilizadas": {"bsonType": "array"},
                "confiabilidade_dados": {"bsonType": "string"},
                "data_ultima_atualizacao": {"bsonType": "string"},
                "observacoes": {"bsonType": "string"},
                "limitacoes_analise": {"bsonType": "string"}
            },
            "required": ["fontes_utilizadas", "confiabilidade_dados"]
        },
        "validation_status": {"bsonType": "string"},
        "processed_at": {"bsonType": "string"},
        "data_quality_score": {"bsonType": ["double", "int"]}
    },
    "required": ["dados_canais_distribuicao", "dados_reviews_feedback", "dados_certificacoes", "fontes_informacao"]
}


# Schema completo para reports (incluindo tipos existentes)
REPORT_SCHEMA = {
    "bsonType": "object",
    "properties": {
        "report_type": {
            "bsonType": "string",
            "enum": ["dossie_expandido", "mercado_futuro", "produtos_servicos",
                     "diagnostico_lighthouse", "diagnostico_visual", "metricas_business"]
        },
        "version": {"bsonType": "string"},
        # Campos específicos para dossie_expandido
        "informacoes_gerais": {"bsonType": "object"},
        "historico": {"bsonType": "string"},
        "produtos_servicos": {"bsonType": "array"},
        "concorrentes": {"bsonType": "object"},
        "cases_sucesso": {"bsonType": "object"},
        "saude_financeira": {"bsonType": "object"},
        "analise_swot": {"bsonType": "object"},
        "analise_swot_expandida": {"bsonType": "object"},
        "stack_tecnico": {"bsonType": "object"},
        "oportunidades_novos_produtos": {"bsonType": "array"},
        "melhorias_futuras": {"bsonType": "array"},
        # Novos campos de funding e presença digital
        "dados_funding": FUNDING_SCHEMA,
        "dados_presenca_digital": PRESENCA_DIGITAL_SCHEMA,
        "dados_parcerias": PARCERIAS_SCHEMA,
        "dados_modelo_negocio": MODELO_NEGOCIO_SCHEMA,
        "dados_pricing": PRICING_SCHEMA,
        "dados_canais_reviews": CANAIS_REVIEWS_SCHEMA
    },
    "required": ["report_type", "version"]
}


# Schema para coleção de clientes (atualizado)
CLIENTS_SCHEMA = {
    "bsonType": "object",
    "properties": {
        "_id": {},  # ObjectId
        "name": {"bsonType": "string"},
        "city": {"bsonType": "string"},
        "state": {"bsonType": "string"},
        "cpfCnpj": {"bsonType": ["string", "null"]},
        "phone": {"bsonType": ["string", "null"]},
        "responsible": {"bsonType": ["string", "null"]},
        "responsibleRole": {"bsonType": ["string", "null"]},
        "site": {"bsonType": ["string", "null"]},
        "contacts": {"bsonType": "array"},
        "projects": {"bsonType": "array"},
        "setor": {"bsonType": ["string", "null"]},
        "sector": {"bsonType": ["string", "null"]},
        "tags": {"bsonType": "array"},
        "created_at": {"bsonType": "date"},
        "updated_at": {"bsonType": "date"},
        "reports": {
            "bsonType": "array",
            "items": REPORT_SCHEMA
        },
        "status": {"bsonType": "string"},
        "projectsCount": {"bsonType": "int"},
        "totalValue": {"bsonType": "string"},
        "lastContact": {"bsonType": "string"}
    },
    "required": ["name", "city", "state", "reports", "status"]
}


def ensure_collection_exists():
    """
    Garante que as coleções 'clients' e 'projetos' existem no MongoDB
    """
    try:
        from .db import projetos_collection

        # Verifica se as coleções existem
        collections = clients_collection.database.list_collection_names()

        # Criar coleção 'clients' se não existir
        if "clients" not in collections:
            temp_doc = {
                "name": "temp_initialization",
                "city": "temp",
                "state": "temp",
                "reports": [],
                "status": "temp",
                "_temp": True
            }
            clients_collection.insert_one(temp_doc)
            clients_collection.delete_one({"_temp": True})
            logger.info("Coleção 'clients' criada com sucesso")
        else:
            logger.info("Coleção 'clients' já existe")

        # Criar coleção 'projetos' se não existir
        if "projetos" not in collections:
            temp_doc = {
                "cliente_id": None,
                "nome_projeto": "temp_initialization",
                "tags": ["temp"],
                "status": "Sugestão",
                "resumo": "temp",
                "progresso": 0,
                "equipe": "-",
                "justificativa": "temp",
                "importancia": "temp",
                "pontuacao": 0,
                "created_at": "temp",
                "_temp": True
            }
            projetos_collection.insert_one(temp_doc)
            projetos_collection.delete_one({"_temp": True})
            logger.info("Coleção 'projetos' criada com sucesso")
        else:
            logger.info("Coleção 'projetos' já existe")

        return True

    except Exception as e:
        logger.error(f"Erro ao criar coleções: {e}")
        return False


def setup_mongodb_schemas():
    """
    Configura os schemas de validação no MongoDB para as coleções
    """
    try:
        from .db import projetos_collection

        # Primeiro, garante que as coleções existem
        if not ensure_collection_exists():
            return False

        # Aplicar validação de schema na coleção de clientes
        clients_collection.database.command({
            "collMod": "clients",
            "validator": {
                "$jsonSchema": CLIENTS_SCHEMA
            },
            "validationLevel": "moderate",
            "validationAction": "warn"
        })

        # Aplicar validação de schema na coleção de projetos
        clients_collection.database.command({
            "collMod": "projetos",
            "validator": {
                "$jsonSchema": PROJETO_SCHEMA
            },
            "validationLevel": "moderate",
            "validationAction": "warn"
        })

        logger.info(
            "Schemas de validação aplicados com sucesso às coleções 'clients' e 'projetos'")
        return True

    except OperationFailure as e:
        logger.error(f"Erro ao aplicar schemas de validação: {e}")
        return False
    except Exception as e:
        logger.error(f"Erro inesperado ao configurar schemas: {e}")
        return False


def setup_mongodb_indexes():
    """
    Configura índices otimizados para consultas frequentes
    """
    try:
        # Índices simples
        indexes_to_create = [
            "name",
            "sector",
            "status",
            "reports.report_type",
            "reports.dados_funding.resumo_funding.status_funding",
            "reports.dados_presenca_digital.resumo_presenca_digital.domain_authority",
            "reports.dados_parcerias.resumo_parcerias.nivel_maturidade_parcerias",
            "reports.dados_parcerias.metricas_parcerias.numero_parceiros_ativos",
            "reports.dados_modelo_negocio.classificacao_modelo.tipo_principal",
            "reports.dados_modelo_negocio.segmentos_clientes.tamanho_empresas",
            "reports.dados_modelo_negocio.escalabilidade_modelo.nivel_escalabilidade",
            "reports.dados_pricing.resumo_pricing.estrategia_principal",
            "reports.dados_pricing.resumo_pricing.modelo_pricing",
            "reports.dados_pricing.analise_competitiva.posicionamento_mercado",
            "reports.dados_canais_reviews.dados_canais_distribuicao.resumo_canais.estrategia_principal",
            "reports.dados_canais_reviews.dados_canais_distribuicao.resumo_canais.modelo_distribuicao",
            "reports.dados_canais_reviews.dados_reviews_feedback.resumo_reputacao.score_reputacao_geral",
            "reports.dados_canais_reviews.dados_reviews_feedback.resumo_reputacao.tendencia_satisfacao",
            "reports.dados_canais_reviews.dados_certificacoes.resumo_certificacoes.nivel_certificacao",
            "reports.dados_canais_reviews.data_quality_score",
            "reports.dados_canais_reviews.processed_at",
            "reports.dados_funding.data_quality_score",
            "reports.dados_presenca_digital.data_quality_score",
            "reports.dados_funding.processed_at",
            "reports.dados_presenca_digital.processed_at",

            # Índices compostos
            ([("city", 1), ("state", 1)], {}),

            # Índice de texto para busca full-text
            ([("name", "text"), ("city", "text"), ("state", "text")],
             {"default_language": "portuguese"})
        ]

        created_indexes = []
        for index_spec in indexes_to_create:
            try:
                if isinstance(index_spec, tuple) and len(index_spec) == 2:
                    # Índice composto ou com opções
                    index_keys, options = index_spec
                    index_name = clients_collection.create_index(
                        index_keys, **options)
                elif isinstance(index_spec, str):
                    # Índice simples (string)
                    index_name = clients_collection.create_index(index_spec)
                else:
                    # Índice simples (outros formatos)
                    index_name = clients_collection.create_index(index_spec)

                created_indexes.append(index_name)
            except Exception as e:
                logger.warning(f"Erro ao criar índice {index_spec}: {e}")
                continue

        logger.info(f"Índices criados com sucesso: {created_indexes}")
        return True

    except Exception as e:
        logger.error(f"Erro ao criar índices: {e}")
        return False


def initialize_mongodb_setup():
    """
    Inicializa configuração completa do MongoDB (schemas + índices)
    """
    logger.info("Iniciando configuração do MongoDB...")

    schema_success = setup_mongodb_schemas()
    index_success = setup_mongodb_indexes()

    if schema_success and index_success:
        logger.info("Configuração do MongoDB concluída com sucesso")
        return True
    else:
        logger.warning("Configuração do MongoDB concluída com alguns erros")
        return False


def validate_funding_data(data):
    """
    Valida dados de funding contra o schema definido
    """
    try:
        # Simulação de validação manual (MongoDB fará automaticamente)
        required_fields = ["resumo_funding", "rodadas_investimento",
                           "investidores_principais", "metricas_investimento",
                           "fontes_informacao"]

        for field in required_fields:
            if field not in data:
                return False, f"Campo obrigatório ausente: {field}"

        return True, "Dados de funding válidos"

    except Exception as e:
        return False, f"Erro na validação: {str(e)}"


def validate_presenca_digital_data(data):
    """
    Valida dados de presença digital contra o schema definido
    """
    try:
        # Simulação de validação manual (MongoDB fará automaticamente)
        required_fields = ["resumo_presenca_digital", "metricas_seo",
                           "principais_keywords", "redes_sociais", "fontes_analise"]

        for field in required_fields:
            if field not in data:
                return False, f"Campo obrigatório ausente: {field}"

        return True, "Dados de presença digital válidos"

    except Exception as e:
        return False, f"Erro na validação: {str(e)}"


def validate_parcerias_data(data):
    """
    Valida dados de parcerias contra o schema definido
    """
    try:
        # Validação dos campos obrigatórios
        required_fields = ["resumo_parcerias", "parcerias_comerciais",
                           "parcerias_tecnologicas", "parcerias_estrategicas",
                           "fontes_informacao"]

        for field in required_fields:
            if field not in data:
                return False, f"Campo obrigatório ausente: {field}"

        # Validação específica do resumo de parcerias
        resumo = data.get("resumo_parcerias", {})
        resumo_required = ["total_parceiros_identificados",
                           "nivel_maturidade_parcerias", "estrategia_parcerias"]

        for field in resumo_required:
            if field not in resumo:
                return False, f"Campo obrigatório ausente em resumo_parcerias: {field}"

        return True, "Dados de parcerias válidos"

    except Exception as e:
        return False, f"Erro na validação de parcerias: {str(e)}"


def validate_modelo_negocio_data(data):
    """
    Valida dados de modelo de negócio contra o schema expandido definido
    """
    try:
        # Validação dos campos obrigatórios principais
        required_fields = ["classificacao_modelo", "proposta_valor", "segmentos_clientes",
                           "canais_distribuicao", "relacionamento_clientes", "recursos_principais",
                           "escalabilidade_modelo", "evolucao_modelo", "riscos_modelo"]

        for field in required_fields:
            if field not in data:
                return False, f"Campo obrigatório ausente: {field}"

        # Validação dos canais de distribuição expandidos
        canais = data.get("canais_distribuicao", {})
        canais_required = ["mix_canais",
                           "estrategia_go_to_market", "canais_especificos"]

        for field in canais_required:
            if field not in canais:
                return False, f"Campo obrigatório ausente em canais_distribuicao: {field}"

        # Validação dos canais específicos
        canais_especificos = canais.get("canais_especificos", {})
        canais_especificos_required = ["vendas", "marketing"]

        for field in canais_especificos_required:
            if field not in canais_especificos:
                return False, f"Campo obrigatório ausente em canais_especificos: {field}"

        return True, "Dados de modelo de negócio expandido válidos"

    except Exception as e:
        return False, f"Erro na validação de modelo de negócio expandido: {str(e)}"


def validate_pricing_data(data):
    """
    Valida dados de pricing contra o schema expandido definido
    """
    try:
        # Validação dos campos obrigatórios principais - SIMPLIFICADA para testes
        required_basic_fields = ["resumo_pricing", "estrutura_precos", "tiers_precos_detalhados",
                                 "politicas_desconto_expandidas", "add_ons_extras_detalhados",
                                 "metricas_pricing_avancadas", "fontes_informacao"]

        for field in required_basic_fields:
            if field not in data:
                return False, f"Campo obrigatório ausente: {field}"

        return True, "Dados de pricing expandido válidos"

    except Exception as e:
        return False, f"Erro na validação de pricing expandido: {str(e)}"


def validate_canais_reviews_data(data):
    """
    Valida dados de canais de distribuição, reviews e certificações contra o schema definido
    """
    try:
        # Validação dos campos obrigatórios principais
        required_fields = ["dados_canais_distribuicao", "dados_reviews_feedback",
                           "dados_certificacoes", "fontes_informacao"]

        for field in required_fields:
            if field not in data:
                return False, f"Campo obrigatório ausente: {field}"

        # Validação específica dos canais de distribuição
        canais = data.get("dados_canais_distribuicao", {})
        canais_required = ["resumo_canais", "canais_principais"]

        for field in canais_required:
            if field not in canais:
                return False, f"Campo obrigatório ausente em dados_canais_distribuicao: {field}"

        # Validação específica dos reviews/feedback
        reviews = data.get("dados_reviews_feedback", {})
        reviews_required = ["resumo_reputacao", "plataformas_review"]

        for field in reviews_required:
            if field not in reviews:
                return False, f"Campo obrigatório ausente em dados_reviews_feedback: {field}"

        # Validação específica das certificações
        certificacoes = data.get("dados_certificacoes", {})
        cert_required = ["resumo_certificacoes"]

        for field in cert_required:
            if field not in certificacoes:
                return False, f"Campo obrigatório ausente em dados_certificacoes: {field}"

        return True, "Dados de canais, reviews e certificações válidos"

    except Exception as e:
        return False, f"Erro na validação de canais/reviews: {str(e)}"


def validate_enhanced_dossie_data(dossie_data):
    """
    Valida um dossiê expandido completo incluindo as novas funcionalidades
    """
    try:
        validation_results = []

        # Validar dados de parcerias se presentes
        if "dados_parcerias" in dossie_data:
            is_valid, message = validate_parcerias_data(
                dossie_data["dados_parcerias"])
            validation_results.append(("parcerias", is_valid, message))

        # Validar dados de modelo de negócio se presentes
        if "dados_modelo_negocio" in dossie_data:
            is_valid, message = validate_modelo_negocio_data(
                dossie_data["dados_modelo_negocio"])
            validation_results.append(("modelo_negocio", is_valid, message))

        # Validar dados de pricing se presentes
        if "dados_pricing" in dossie_data:
            is_valid, message = validate_pricing_data(
                dossie_data["dados_pricing"])
            validation_results.append(("pricing", is_valid, message))

        # Validar dados de canais/reviews se presentes
        if "dados_canais_reviews" in dossie_data:
            is_valid, message = validate_canais_reviews_data(
                dossie_data["dados_canais_reviews"])
            validation_results.append(("canais_reviews", is_valid, message))

        # Verificar se todas as validações passaram
        all_valid = all(result[1] for result in validation_results)

        if all_valid:
            return True, "Todos os dados do dossiê expandido são válidos", validation_results
        else:
            failed_validations = [
                result for result in validation_results if not result[1]]
            return False, f"Falhas na validação: {failed_validations}", validation_results

    except Exception as e:
        return False, f"Erro na validação do dossiê expandido: {str(e)}", []


# Schema para dados de pesquisa de mercado e produtos/serviços
PESQUISA_MERCADO_SCHEMA = {
    "$jsonSchema": {
        "bsonType": "object",
        "title": "Schema para dados de pesquisa de mercado e produtos/serviços",
        "required": ["pesquisa_mercado", "analise_produtos_servicos", "insights_competitivos", "fontes_informacao"],
        "properties": {
            "pesquisa_mercado": {
                "bsonType": "object",
                "description": "Dados de pesquisa de mercado incluindo tamanho, tendências e drivers",
                "required": ["tamanho_mercado", "tendencias_setor", "drivers_crescimento", "barreiras_entrada"],
                "properties": {
                    "tamanho_mercado": {
                        "bsonType": "object",
                        "description": "Análise TAM/SAM/SOM do mercado",
                        "required": ["tam_global", "sam_serviceable", "som_obtainable", "crescimento_cagr", "geografia_mercados"],
                        "properties": {
                            "tam_global": {"bsonType": "string", "description": "Total Addressable Market"},
                            "sam_serviceable": {"bsonType": "string", "description": "Serviceable Addressable Market"},
                            "som_obtainable": {"bsonType": "string", "description": "Serviceable Obtainable Market"},
                            "crescimento_cagr": {"bsonType": "string", "description": "Taxa de crescimento anual composta"},
                            "geografia_mercados": {
                                "bsonType": "array",
                                "description": "Mercados geográficos de atuação",
                                "items": {"bsonType": "string"}
                            }
                        }
                    },
                    "tendencias_setor": {
                        "bsonType": "array",
                        "description": "Tendências identificadas no setor",
                        "items": {
                            "bsonType": "object",
                            "required": ["tendencia", "impacto", "horizonte_tempo", "evidencias"],
                            "properties": {
                                "tendencia": {"bsonType": "string", "description": "Nome da tendência"},
                                "impacto": {"bsonType": "string", "enum": ["alto", "médio", "baixo"], "description": "Nível de impacto"},
                                "horizonte_tempo": {"bsonType": "string", "enum": ["curto prazo", "médio prazo", "longo prazo"], "description": "Horizonte temporal"},
                                "evidencias": {
                                    "bsonType": "array",
                                    "description": "Evidências que suportam a tendência",
                                    "items": {"bsonType": "string"}
                                }
                            }
                        }
                    },
                    "drivers_crescimento": {
                        "bsonType": "array",
                        "description": "Fatores que impulsionam o crescimento do mercado",
                        "items": {"bsonType": "string"}
                    },
                    "barreiras_entrada": {
                        "bsonType": "array",
                        "description": "Barreiras para entrada no mercado",
                        "items": {"bsonType": "string"}
                    }
                }
            },
            "analise_produtos_servicos": {
                "bsonType": "object",
                "description": "Análise de produtos/serviços e concorrência",
                "required": ["portfolio_concorrentes", "gaps_mercado", "inovacoes_recentes", "tendencias_produto", "oportunidades_produto"],
                "properties": {
                    "portfolio_concorrentes": {
                        "bsonType": "array",
                        "description": "Portfolio de concorrentes principais",
                        "items": {
                            "bsonType": "object",
                            "required": ["concorrente", "produtos_principais", "posicionamento", "diferenciais"],
                            "properties": {
                                "concorrente": {"bsonType": "string", "description": "Nome do concorrente"},
                                "produtos_principais": {
                                    "bsonType": "array",
                                    "description": "Principais produtos/serviços",
                                    "items": {"bsonType": "string"}
                                },
                                "posicionamento": {"bsonType": "string", "enum": ["premium", "mid-market", "entry-level", "niche"], "description": "Posicionamento no mercado"},
                                "diferenciais": {
                                    "bsonType": "array",
                                    "description": "Principais diferenciais competitivos",
                                    "items": {"bsonType": "string"}
                                }
                            }
                        }
                    },
                    "gaps_mercado": {
                        "bsonType": "array",
                        "description": "Gaps identificados no mercado",
                        "items": {"bsonType": "string"}
                    },
                    "inovacoes_recentes": {
                        "bsonType": "array",
                        "description": "Inovações recentes no setor",
                        "items": {"bsonType": "string"}
                    },
                    "tendencias_produto": {
                        "bsonType": "array",
                        "description": "Tendências de produtos/serviços",
                        "items": {"bsonType": "string"}
                    },
                    "oportunidades_produto": {
                        "bsonType": "array",
                        "description": "Oportunidades de novos produtos/serviços",
                        "items": {"bsonType": "string"}
                    }
                }
            },
            "insights_competitivos": {
                "bsonType": "object",
                "description": "Insights competitivos e posicionamento estratégico",
                "required": ["posicionamento_relativo", "vantagens_competitivas", "vulnerabilidades", "recomendacoes_estrategicas"],
                "properties": {
                    "posicionamento_relativo": {"bsonType": "string", "description": "Posicionamento relativo no mercado"},
                    "vantagens_competitivas": {
                        "bsonType": "array",
                        "description": "Vantagens competitivas identificadas",
                        "items": {"bsonType": "string"}
                    },
                    "vulnerabilidades": {
                        "bsonType": "array",
                        "description": "Vulnerabilidades competitivas",
                        "items": {"bsonType": "string"}
                    },
                    "recomendacoes_estrategicas": {
                        "bsonType": "array",
                        "description": "Recomendações estratégicas",
                        "items": {"bsonType": "string"}
                    }
                }
            },
            "fontes_informacao": {
                "bsonType": "object",
                "description": "Fontes de informação utilizadas na pesquisa",
                "required": ["fontes_utilizadas", "confiabilidade_dados", "data_ultima_atualizacao", "observacoes", "limitacoes_analise"],
                "properties": {
                    "fontes_utilizadas": {
                        "bsonType": "array",
                        "description": "Fontes de dados utilizadas",
                        "items": {"bsonType": "string"}
                    },
                    "confiabilidade_dados": {"bsonType": "string", "enum": ["Alta", "Média", "Baixa"], "description": "Nível de confiabilidade dos dados"},
                    "data_ultima_atualizacao": {"bsonType": "string", "description": "Data da última atualização dos dados"},
                    "observacoes": {"bsonType": "string", "description": "Observações sobre a metodologia"},
                    "limitacoes_analise": {"bsonType": "string", "description": "Limitações identificadas na análise"}
                }
            },
            "score_qualidade": {
                "bsonType": ["double", "int"],
                "minimum": 0,
                "maximum": 10,
                "description": "Score de qualidade dos dados (0-10)"
            }
        }
    }
}


def validate_pesquisa_mercado_data(data):
    """
    Valida dados de pesquisa de mercado contra o schema definido
    """
    try:
        # Validação dos campos obrigatórios principais
        required_fields = ["pesquisa_mercado", "analise_produtos_servicos",
                           "insights_competitivos", "fontes_informacao"]

        for field in required_fields:
            if field not in data:
                raise ValueError(f"Campo obrigatório ausente: {field}")

        # Validação específica da pesquisa de mercado
        pesquisa_mercado = data.get("pesquisa_mercado", {})
        mercado_required = ["tamanho_mercado", "tendencias_setor",
                            "drivers_crescimento", "barreiras_entrada"]

        for field in mercado_required:
            if field not in pesquisa_mercado:
                raise ValueError(
                    f"Subcampo obrigatório ausente em pesquisa_mercado: {field}")

        # Validação específica do tamanho de mercado
        tamanho_mercado = pesquisa_mercado.get("tamanho_mercado", {})
        tamanho_required = ["tam_global", "sam_serviceable",
                            "som_obtainable", "crescimento_cagr", "geografia_mercados"]

        for field in tamanho_required:
            if field not in tamanho_mercado:
                raise ValueError(
                    f"Subcampo obrigatório ausente em tamanho_mercado: {field}")

        # Validação específica da análise de produtos/serviços
        analise_produtos = data.get("analise_produtos_servicos", {})
        produtos_required = ["portfolio_concorrentes", "gaps_mercado", "inovacoes_recentes",
                             "tendencias_produto", "oportunidades_produto"]

        for field in produtos_required:
            if field not in analise_produtos:
                raise ValueError(
                    f"Subcampo obrigatório ausente em analise_produtos_servicos: {field}")

        # Validação específica dos insights competitivos
        insights = data.get("insights_competitivos", {})
        insights_required = ["posicionamento_relativo", "vantagens_competitivas",
                             "vulnerabilidades", "recomendacoes_estrategicas"]

        for field in insights_required:
            if field not in insights:
                raise ValueError(
                    f"Subcampo obrigatório ausente em insights_competitivos: {field}")

        # Validação específica das fontes de informação
        fontes = data.get("fontes_informacao", {})
        fontes_required = ["fontes_utilizadas", "confiabilidade_dados", "data_ultima_atualizacao",
                           "observacoes", "limitacoes_analise"]

        for field in fontes_required:
            if field not in fontes:
                raise ValueError(
                    f"Subcampo obrigatório ausente em fontes_informacao: {field}")

        return True

    except Exception as e:
        raise ValueError(f"Erro na validação de pesquisa de mercado: {str(e)}")


# Schema para diagnóstico técnico (Lighthouse + Playwright + IA)
DIAGNOSTICO_TECNICO_SCHEMA = {
    "type": "object",
    "properties": {
        "diagnostico_lighthouse": {
            "type": "object",
            "properties": {
                "performance": {
                    "type": "object",
                    "properties": {
                        "score": {"type": "number", "minimum": 0, "maximum": 100},
                        "metricas_core": {
                            "type": "object",
                            "properties": {
                                "fcp": {"type": "number", "minimum": 0},
                                "lcp": {"type": "number", "minimum": 0},
                                "cls": {"type": "number", "minimum": 0},
                                "fid": {"type": "number", "minimum": 0},
                                "ttfb": {"type": "number", "minimum": 0}
                            }
                        },
                        "oportunidades": {"type": "array", "items": {"type": "string"}},
                        "diagnosticos": {"type": "array", "items": {"type": "string"}}
                    },
                    "required": ["score"]
                },
                "acessibilidade": {
                    "type": "object",
                    "properties": {
                        "score": {"type": "number", "minimum": 0, "maximum": 100},
                        "issues": {"type": "array", "items": {"type": "string"}},
                        "recomendacoes": {"type": "array", "items": {"type": "string"}}
                    },
                    "required": ["score"]
                },
                "seo_tecnico": {
                    "type": "object",
                    "properties": {
                        "score": {"type": "number", "minimum": 0, "maximum": 100},
                        "meta_tags": {"type": "string"},
                        "estrutura_html": {"type": "string"},
                        "mobile_friendly": {"type": "string"}
                    },
                    "required": ["score"]
                },
                "best_practices": {
                    "type": "object",
                    "properties": {
                        "score": {"type": "number", "minimum": 0, "maximum": 100},
                        "https": {"type": "boolean"},
                        "vulnerabilidades": {"type": "array", "items": {"type": "string"}}
                    },
                    "required": ["score"]
                }
            },
            "required": ["performance", "acessibilidade", "seo_tecnico", "best_practices"]
        },
        "analise_visual": {
            "type": "object",
            "properties": {
                "screenshots": {
                    "type": "object",
                    "properties": {
                        "desktop": {"type": "string"},
                        "mobile": {"type": "string"},
                        "metadata": {"type": "object"}
                    }
                },
                "analise_ui_ux": {
                    "type": "object",
                    "properties": {
                        "layout_quality": {"type": "string"},
                        "user_experience": {"type": "string"},
                        "visual_hierarchy": {"type": "string"},
                        "responsive_design": {"type": "string"}
                    }
                },
                "recomendacoes_design": {"type": "array", "items": {"type": "string"}}
            },
            "required": ["screenshots", "analise_ui_ux"]
        },
        "relatorio_consolidado": {
            "type": "object",
            "properties": {
                "score_geral": {"type": "number", "minimum": 0, "maximum": 100},
                "prioridades_melhorias": {"type": "array", "items": {"type": "string"}},
                "impacto_business": {"type": "string"},
                "timeline_implementacao": {"type": "string"},
                "resumo_executivo": {"type": "string"},
                "metricas_chave": {"type": "object"},
                "recomendacoes_tecnicas": {"type": "array", "items": {"type": "string"}}
            },
            "required": ["score_geral"]
        },
        "data_quality_score": {"type": "number", "minimum": 0, "maximum": 10},
        "metadata": {"type": "object"}
    },
    "required": ["diagnostico_lighthouse", "analise_visual", "relatorio_consolidado"]
}


def validate_diagnostico_tecnico_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Valida dados de diagnóstico técnico contra o schema MongoDB

    Args:
        data: Dados de diagnóstico técnico para validação

    Returns:
        Dict com resultado da validação
    """
    try:
        # Validar estrutura básica
        if not isinstance(data, dict):
            return {
                "valid": False,
                "errors": ["Dados devem ser um dicionário"],
                "data": data
            }

        errors = []

        # Validar seções obrigatórias
        required_sections = ["diagnostico_lighthouse",
                             "analise_visual", "relatorio_consolidado"]
        for section in required_sections:
            if section not in data:
                errors.append(f"Seção obrigatória ausente: {section}")

        # Validar estrutura Lighthouse se presente
        if "diagnostico_lighthouse" in data:
            lighthouse_errors = _validate_lighthouse_structure(
                data["diagnostico_lighthouse"])
            errors.extend(lighthouse_errors)

        # Validar estrutura de análise visual se presente
        if "analise_visual" in data:
            visual_errors = _validate_visual_analysis_structure(
                data["analise_visual"])
            errors.extend(visual_errors)

        # Validar relatório consolidado se presente
        if "relatorio_consolidado" in data:
            report_errors = _validate_consolidated_report_structure(
                data["relatorio_consolidado"])
            errors.extend(report_errors)

        # Validar score de qualidade se presente
        if "data_quality_score" in data:
            score = data["data_quality_score"]
            if not isinstance(score, (int, float)) or not (0 <= score <= 10):
                errors.append(
                    "data_quality_score deve ser um número entre 0 e 10")

        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "data": data
        }

    except Exception as e:
        return {
            "valid": False,
            "errors": [f"Erro na validação: {str(e)}"],
            "data": data
        }


def _validate_lighthouse_structure(lighthouse_data: Dict[str, Any]) -> List[str]:
    """Valida estrutura dos dados Lighthouse"""
    errors = []

    required_categories = ["performance",
                           "acessibilidade", "seo_tecnico", "best_practices"]

    for category in required_categories:
        if category not in lighthouse_data:
            errors.append(f"Categoria Lighthouse ausente: {category}")
            continue

        cat_data = lighthouse_data[category]
        if not isinstance(cat_data, dict):
            errors.append(f"Categoria {category} deve ser um dicionário")
            continue

        # Validar score obrigatório
        if "score" not in cat_data:
            errors.append(f"Score ausente na categoria {category}")
        else:
            score = cat_data["score"]
            if not isinstance(score, (int, float)) or not (0 <= score <= 100):
                errors.append(
                    f"Score inválido na categoria {category}: deve ser 0-100")

    # Validar métricas core se presente
    if "performance" in lighthouse_data:
        perf_data = lighthouse_data["performance"]
        if "metricas_core" in perf_data:
            metricas = perf_data["metricas_core"]
            if not isinstance(metricas, dict):
                errors.append("metricas_core deve ser um dicionário")

    return errors


def _validate_visual_analysis_structure(visual_data: Dict[str, Any]) -> List[str]:
    """Valida estrutura da análise visual"""
    errors = []

    required_sections = ["screenshots", "analise_ui_ux"]

    for section in required_sections:
        if section not in visual_data:
            errors.append(f"Seção de análise visual ausente: {section}")
            continue

        if not isinstance(visual_data[section], dict):
            errors.append(f"Seção {section} deve ser um dicionário")

    # Validar estrutura de screenshots se presente
    if "screenshots" in visual_data:
        screenshots = visual_data["screenshots"]
        if isinstance(screenshots, dict):
            expected_keys = ["desktop", "mobile", "metadata"]
            for key in expected_keys:
                if key not in screenshots:
                    errors.append(f"Campo ausente em screenshots: {key}")

    # Validar estrutura UI/UX se presente
    if "analise_ui_ux" in visual_data:
        ui_ux = visual_data["analise_ui_ux"]
        if isinstance(ui_ux, dict):
            expected_keys = ["layout_quality", "user_experience",
                             "visual_hierarchy", "responsive_design"]
            for key in expected_keys:
                if key not in ui_ux:
                    errors.append(f"Campo ausente em analise_ui_ux: {key}")

    return errors


def _validate_consolidated_report_structure(report_data: Dict[str, Any]) -> List[str]:
    """Valida estrutura do relatório consolidado"""
    errors = []

    # Validar score geral obrigatório
    if "score_geral" not in report_data:
        errors.append("score_geral é obrigatório no relatório consolidado")
    else:
        score = report_data["score_geral"]
        if not isinstance(score, (int, float)) or not (0 <= score <= 100):
            errors.append("score_geral deve ser um número entre 0 e 100")

    # Validar arrays se presentes
    array_fields = ["prioridades_melhorias", "recomendacoes_tecnicas"]
    for field in array_fields:
        if field in report_data and not isinstance(report_data[field], list):
            errors.append(f"{field} deve ser uma lista")

    return errors
