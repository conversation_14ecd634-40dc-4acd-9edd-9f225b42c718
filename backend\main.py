from clients.routes import router as clients_router
from fastapi.middleware.cors import CORSMiddleware
from fastapi import FastAPI
import warnings
warnings.filterwarnings("ignore", category=SyntaxWarning, module="pydub")
warnings.filterwarnings("ignore", message=".*invalid escape sequence.*")


try:
    from api import APIRouter, AuthMiddleware, ValidationMiddleware
    from api.error_handler import error_handler
    API_COMPONENTS_AVAILABLE = True
except ImportError:
    API_COMPONENTS_AVAILABLE = False
    APIRouter = None
    AuthMiddleware = None
    ValidationMiddleware = None
    error_handler = None

# Importar endpoints de processamento completo
try:
    from api.full_processing_endpoints import router as full_processing_router
    FULL_PROCESSING_AVAILABLE = True
except ImportError:
    FULL_PROCESSING_AVAILABLE = False
    full_processing_router = None

# Importar endpoints de relatórios
try:
    from api.report_routes import router as report_router
    REPORT_ENDPOINTS_AVAILABLE = True
except ImportError:
    try:
        from api.report_endpoints import router as report_router
        REPORT_ENDPOINTS_AVAILABLE = True
    except ImportError:
        REPORT_ENDPOINTS_AVAILABLE = False
        report_router = None

# Importar endpoints de IA
try:
    from api.ai_endpoints import router as ai_router
    AI_ENDPOINTS_AVAILABLE = True
except ImportError:
    AI_ENDPOINTS_AVAILABLE = False
    ai_router = None


try:
    from api.podcast_endpoints import router as podcast_router
    PODCAST_ENDPOINTS_AVAILABLE = True
except ImportError:
    PODCAST_ENDPOINTS_AVAILABLE = False
    podcast_router = None


try:
    from api.boardroom_endpoints import router as boardroom_router
    BOARDROOM_ENDPOINTS_AVAILABLE = True
except ImportError:
    BOARDROOM_ENDPOINTS_AVAILABLE = False
    boardroom_router = None


app = FastAPI(
    title="ScopeAI Backend",
    description="API para gerenciamento de projetos, clientes e integrações ScopeAI.",
    version="0.1.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS totalmente aberto (inclusive para WebSocket)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


if API_COMPONENTS_AVAILABLE and AuthMiddleware and ValidationMiddleware:
    app.add_middleware(AuthMiddleware)
    app.add_middleware(ValidationMiddleware)

app.include_router(clients_router)


if FULL_PROCESSING_AVAILABLE and full_processing_router:
    app.include_router(full_processing_router)


if REPORT_ENDPOINTS_AVAILABLE and report_router:
    app.include_router(report_router)


if AI_ENDPOINTS_AVAILABLE and ai_router:
    app.include_router(ai_router)


if PODCAST_ENDPOINTS_AVAILABLE and podcast_router:
    app.include_router(podcast_router, prefix="/podcasts", tags=["Podcasts"])


if BOARDROOM_ENDPOINTS_AVAILABLE and boardroom_router:
    app.include_router(boardroom_router)


try:
    from api.project_pdf_endpoints import router as project_pdf_router
    app.include_router(project_pdf_router)
    print("✅ Project PDF endpoints carregados")
except ImportError:
    print("⚠️ Project PDF endpoints não disponíveis")

# Novo sistema de API padronizada (se disponível)
if API_COMPONENTS_AVAILABLE and APIRouter and error_handler:
    api_router = APIRouter()
    app.include_router(api_router.get_main_router())
    app.add_exception_handler(Exception, error_handler.exception_handler)



try:
    from api.basic_endpoints import router as basic_router
    app.include_router(basic_router)
    print("✅ Basic endpoints carregados")
except ImportError:
    print("⚠️ Basic endpoints não disponíveis")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8040)
