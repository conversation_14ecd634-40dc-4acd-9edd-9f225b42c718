"""
ReportNotificationService - Responsável pelas Notificações de Relatórios

Módulo especializado em gerenciar notificações, alertas e comunicações
relacionadas aos relatórios, seguindo o Single Responsibility Principle.
"""

import logging
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional
from enum import Enum

logger = logging.getLogger(__name__)


class NotificationType(Enum):
    """Tipos de notificação disponíveis."""
    REPORT_GENERATED = "report_generated"
    REPORT_UPDATED = "report_updated"
    REPORT_ERROR = "report_error"
    QUALITY_ALERT = "quality_alert"
    VERSION_ALERT = "version_alert"


class NotificationChannel(Enum):
    """Canais de notificação disponíveis."""
    EMAIL = "email"
    WEBHOOK = "webhook"
    SMS = "sms"
    PUSH = "push"
    SLACK = "slack"


class ReportNotificationService:
    """
    Responsável por enviar notificações sobre relatórios.

    Única responsabilidade: Gerenciar notificações e alertas de relatórios.
    """

    def __init__(self):
        """Inicializa o serviço de notificações."""
        self.enabled_channels = [
            NotificationChannel.EMAIL, NotificationChannel.WEBHOOK]
        self.notification_templates = self._load_notification_templates()

    async def send_report_notifications(
        self,
        client_id: str,
        report_type: str,
        report: Dict[str, Any],
        notification_type: NotificationType = NotificationType.REPORT_GENERATED
    ) -> Dict[str, Any]:
        """
        Envia notificações sobre um relatório específico.

        Args:
            client_id: ID do cliente
            report_type: Tipo do relatório
            report: Dados do relatório
            notification_type: Tipo de notificação

        Returns:
            Status das notificações enviadas
        """
        try:
            logger.info(
                f"Enviando notificações {notification_type.value} para cliente {client_id}")

            # Preparar contexto da notificação
            notification_context = self._prepare_notification_context(
                client_id, report_type, report, notification_type
            )

            # Resultados de envio por canal
            results = {}

            # Enviar por cada canal habilitado
            for channel in self.enabled_channels:
                try:
                    result = await self._send_notification_by_channel(
                        channel, notification_context
                    )
                    results[channel.value] = result
                except Exception as e:
                    logger.error(
                        f"Erro no envio via {channel.value}: {str(e)}")
                    results[channel.value] = {
                        "status": "error", "error": str(e)}

            # Calcular status geral
            successful_sends = sum(
                1 for result in results.values() if result.get("status") == "success")

            return {
                "notification_type": notification_type.value,
                "client_id": client_id,
                "report_type": report_type,
                "channels_attempted": len(self.enabled_channels),
                "successful_sends": successful_sends,
                "results": results,
                "sent_at": datetime.now(timezone.utc).isoformat(),
                "overall_status": "success" if successful_sends > 0 else "failed"
            }

        except Exception as e:
            logger.error(f"Erro no envio de notificações: {str(e)}")
            return {
                "notification_type": notification_type.value,
                "overall_status": "error",
                "error": str(e)
            }

    async def send_quality_alert(
        self,
        client_id: str,
        report_type: str,
        quality_score: float,
        threshold: float = 70.0
    ) -> Dict[str, Any]:
        """
        Envia alerta de qualidade de dados.

        Args:
            client_id: ID do cliente
            report_type: Tipo do relatório
            quality_score: Score de qualidade atual
            threshold: Limiar mínimo de qualidade

        Returns:
            Status do envio do alerta
        """
        try:
            if quality_score >= threshold:
                return {"status": "skipped", "reason": "Quality above threshold"}

            # Preparar contexto específico para alerta de qualidade
            context = {
                "client_id": client_id,
                "report_type": report_type,
                "quality_score": quality_score,
                "threshold": threshold,
                "severity": self._determine_alert_severity(quality_score),
                "message": f"Quality score ({quality_score:.1f}%) is below threshold ({threshold:.1f}%)",
                "action_required": True
            }

            return await self.send_report_notifications(
                client_id, report_type, context, NotificationType.QUALITY_ALERT
            )

        except Exception as e:
            logger.error(f"Erro no envio de alerta de qualidade: {str(e)}")
            return {"status": "error", "error": str(e)}

    async def send_version_alert(
        self,
        client_id: str,
        report_type: str,
        version_info: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Envia alerta de nova versão de relatório.

        Args:
            client_id: ID do cliente
            report_type: Tipo do relatório
            version_info: Informações da nova versão

        Returns:
            Status do envio do alerta
        """
        try:
            if not version_info.get("is_major_change", False):
                return {"status": "skipped", "reason": "Minor version change"}

            context = {
                "client_id": client_id,
                "report_type": report_type,
                "new_version": version_info.get("version"),
                "previous_version": version_info.get("previous_version"),
                "changes": version_info.get("changes_detected", []),
                "is_major_change": True
            }

            return await self.send_report_notifications(
                client_id, report_type, context, NotificationType.VERSION_ALERT
            )

        except Exception as e:
            logger.error(f"Erro no envio de alerta de versão: {str(e)}")
            return {"status": "error", "error": str(e)}

    def _prepare_notification_context(
        self,
        client_id: str,
        report_type: str,
        report: Dict[str, Any],
        notification_type: NotificationType
    ) -> Dict[str, Any]:
        """Prepara contexto da notificação com dados relevantes."""
        try:
            base_context = {
                "client_id": client_id,
                "report_type": report_type,
                "notification_type": notification_type.value,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "report_id": report.get("_id") or report.get("report_id"),
                "report_url": self._generate_report_url(report.get("_id") or report.get("report_id")),
            }

            # Adicionar dados específicos do relatório
            if "content" in report:
                content = report["content"]
                base_context.update({
                    "quality_score": content.get("data_quality_score", 0),
                    "insights_count": len(content.get("key_insights", [])),
                    "recommendations_count": len(content.get("recommendations", [])),
                    "executive_summary": content.get("executive_summary", "")[:200] + "..." if content.get("executive_summary", "") else ""
                })

            # Adicionar metadados se disponíveis
            if "metadata" in report:
                metadata = report["metadata"]
                base_context.update({
                    "processing_time": metadata.get("processing_time", 0),
                    "template_used": metadata.get("template_used", ""),
                    "file_size": report.get("file_size", 0)
                })

            return base_context

        except Exception as e:
            logger.error(f"Erro na preparação do contexto: {str(e)}")
            return {"client_id": client_id, "report_type": report_type, "error": str(e)}

    async def _send_notification_by_channel(
        self,
        channel: NotificationChannel,
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Envia notificação por um canal específico."""
        try:
            if channel == NotificationChannel.EMAIL:
                return await self._send_email_notification(context)
            elif channel == NotificationChannel.WEBHOOK:
                return await self._send_webhook_notification(context)
            elif channel == NotificationChannel.SMS:
                return await self._send_sms_notification(context)
            elif channel == NotificationChannel.PUSH:
                return await self._send_push_notification(context)
            elif channel == NotificationChannel.SLACK:
                return await self._send_slack_notification(context)
            else:
                return {"status": "error", "error": f"Channel {channel.value} not supported"}

        except Exception as e:
            logger.error(f"Erro no canal {channel.value}: {str(e)}")
            return {"status": "error", "error": str(e)}

    async def _send_email_notification(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Envia notificação por email."""
        try:
            # Placeholder para integração com serviço de email
            logger.info(
                f"Email notification sent for report {context.get('report_id')}")
            return {
                "status": "success",
                "channel": "email",
                "recipient": "<EMAIL>",  # Seria buscado dos dados do cliente
                "sent_at": datetime.now(timezone.utc).isoformat()
            }
        except Exception as e:
            return {"status": "error", "error": str(e)}

    async def _send_webhook_notification(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Envia notificação via webhook."""
        try:
            # Placeholder para integração com webhook
            logger.info(
                f"Webhook notification sent for report {context.get('report_id')}")
            return {
                "status": "success",
                "channel": "webhook",
                "webhook_url": "https://client-webhook.com/notifications",  # Configurável
                "sent_at": datetime.now(timezone.utc).isoformat()
            }
        except Exception as e:
            return {"status": "error", "error": str(e)}

    async def _send_sms_notification(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Envia notificação por SMS."""
        try:
            # Placeholder para integração com serviço de SMS
            logger.info(
                f"SMS notification sent for report {context.get('report_id')}")
            return {
                "status": "success",
                "channel": "sms",
                "phone": "+5511999999999",  # Seria buscado dos dados do cliente
                "sent_at": datetime.now(timezone.utc).isoformat()
            }
        except Exception as e:
            return {"status": "error", "error": str(e)}

    async def _send_push_notification(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Envia notificação push."""
        try:
            # Placeholder para integração com serviço de push
            logger.info(
                f"Push notification sent for report {context.get('report_id')}")
            return {
                "status": "success",
                "channel": "push",
                "device_token": "device_token_here",
                "sent_at": datetime.now(timezone.utc).isoformat()
            }
        except Exception as e:
            return {"status": "error", "error": str(e)}

    async def _send_slack_notification(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Envia notificação via Slack."""
        try:
            # Placeholder para integração com Slack
            logger.info(
                f"Slack notification sent for report {context.get('report_id')}")
            return {
                "status": "success",
                "channel": "slack",
                "slack_channel": "#reports",
                "sent_at": datetime.now(timezone.utc).isoformat()
            }
        except Exception as e:
            return {"status": "error", "error": str(e)}

    def _determine_alert_severity(self, quality_score: float) -> str:
        """Determina severidade do alerta baseado no score de qualidade."""
        if quality_score < 30:
            return "critical"
        elif quality_score < 50:
            return "high"
        elif quality_score < 70:
            return "medium"
        else:
            return "low"

    def _generate_report_url(self, report_id: Optional[str]) -> str:
        """Gera URL para visualização do relatório."""
        if not report_id:
            return ""
        return f"https://dashboard.innovation-scope.ai/reports/{report_id}"

    def _load_notification_templates(self) -> Dict[str, Dict[str, str]]:
        """Carrega templates de notificação."""
        return {
            "report_generated": {
                "subject": "Novo relatório {report_type} disponível",
                "body": "O relatório {report_type} foi gerado com sucesso. Score de qualidade: {quality_score}%",
                "slack_text": "📊 Relatório {report_type} gerado para cliente {client_id}"
            },
            "quality_alert": {
                "subject": "⚠️ Alerta de qualidade - {report_type}",
                "body": "Score de qualidade abaixo do esperado: {quality_score}%",
                "slack_text": "⚠️ Score de qualidade baixo ({quality_score}%) para {report_type}"
            },
            "version_alert": {
                "subject": "🔄 Nova versão disponível - {report_type}",
                "body": "Nova versão {new_version} do relatório {report_type}",
                "slack_text": "🔄 Nova versão {new_version} de {report_type} disponível"
            }
        }

    async def get_notification_preferences(self, client_id: str) -> Dict[str, Any]:
        """Recupera preferências de notificação do cliente."""
        # Placeholder para buscar preferências no banco de dados
        return {
            "email_enabled": True,
            "webhook_enabled": True,
            "sms_enabled": False,
            "quality_threshold": 70.0,
            "notify_major_versions": True,
            "notify_minor_versions": False
        }

    async def update_notification_preferences(
        self,
        client_id: str,
        preferences: Dict[str, Any]
    ) -> bool:
        """Atualiza preferências de notificação do cliente."""
        try:
            # Placeholder para salvar preferências no banco de dados
            logger.info(
                f"Preferências de notificação atualizadas para cliente {client_id}")
            return True
        except Exception as e:
            logger.error(f"Erro ao atualizar preferências: {str(e)}")
            return False
