{"ast": null, "code": "import { DOCUMENT, isPlatform<PERSON>rowser } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, PLATFORM_ID, ElementRef, booleanAttribute, Input, Directive, NgModule } from '@angular/core';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { DomHandler } from 'primeng/dom';\n\n/**\n * AutoFocus manages focus on focusable element on load.\n * @group Components\n */\nclass AutoFocus extends BaseComponent {\n  /**\n   * When present, it specifies that the component should automatically get focus on load.\n   * @deprecated use [pAutoFocus]=\"true\"\n   * @group Props\n   */\n  autofocus = false;\n  /**\n   * When present, it specifies that the component should automatically get focus on load.\n   * @group Props\n   */\n  _autofocus = false;\n  focused = false;\n  platformId = inject(PLATFORM_ID);\n  document = inject(DOCUMENT);\n  host = inject(ElementRef);\n  ngAfterContentChecked() {\n    // This sets the `attr.autofocus` which is different than the Input `autofocus` attribute.\n    if (this.autofocus === false) {\n      this.host.nativeElement.removeAttribute('autofocus');\n    } else {\n      this.host.nativeElement.setAttribute('autofocus', true);\n    }\n    if (!this.focused) {\n      this.autoFocus();\n    }\n  }\n  ngAfterViewChecked() {\n    if (!this.focused) {\n      this.autoFocus();\n    }\n  }\n  autoFocus() {\n    if (isPlatformBrowser(this.platformId) && this._autofocus) {\n      setTimeout(() => {\n        const focusableElements = DomHandler.getFocusableElements(this.host?.nativeElement);\n        if (focusableElements.length === 0) {\n          this.host.nativeElement.focus();\n        }\n        if (focusableElements.length > 0) {\n          focusableElements[0].focus();\n        }\n        this.focused = true;\n      });\n    }\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵAutoFocus_BaseFactory;\n    return function AutoFocus_Factory(__ngFactoryType__) {\n      return (ɵAutoFocus_BaseFactory || (ɵAutoFocus_BaseFactory = i0.ɵɵgetInheritedFactory(AutoFocus)))(__ngFactoryType__ || AutoFocus);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: AutoFocus,\n    selectors: [[\"\", \"pAutoFocus\", \"\"]],\n    inputs: {\n      autofocus: [2, \"autofocus\", \"autofocus\", booleanAttribute],\n      _autofocus: [0, \"pAutoFocus\", \"_autofocus\"]\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AutoFocus, [{\n    type: Directive,\n    args: [{\n      selector: '[pAutoFocus]',\n      standalone: true\n    }]\n  }], null, {\n    autofocus: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    _autofocus: [{\n      type: Input,\n      args: ['pAutoFocus']\n    }]\n  });\n})();\nclass AutoFocusModule {\n  static ɵfac = function AutoFocusModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AutoFocusModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: AutoFocusModule,\n    imports: [AutoFocus],\n    exports: [AutoFocus]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AutoFocusModule, [{\n    type: NgModule,\n    args: [{\n      imports: [AutoFocus],\n      exports: [AutoFocus]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AutoFocus, AutoFocusModule };", "map": {"version": 3, "names": ["DOCUMENT", "isPlatformBrowser", "i0", "inject", "PLATFORM_ID", "ElementRef", "booleanAttribute", "Input", "Directive", "NgModule", "BaseComponent", "<PERSON><PERSON><PERSON><PERSON>", "AutoFocus", "autofocus", "_autofocus", "focused", "platformId", "document", "host", "ngAfterContentChecked", "nativeElement", "removeAttribute", "setAttribute", "autoFocus", "ngAfterViewChecked", "setTimeout", "focusableElements", "getFocusableElements", "length", "focus", "ɵfac", "ɵAutoFocus_BaseFactory", "AutoFocus_Factory", "__ngFactoryType__", "ɵɵgetInheritedFactory", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "inputs", "features", "ɵɵInheritDefinitionFeature", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "standalone", "transform", "AutoFocusModule", "AutoFocusModule_Factory", "ɵmod", "ɵɵdefineNgModule", "imports", "exports", "ɵinj", "ɵɵdefineInjector"], "sources": ["C:/projetos/scope-ai/frontend/node_modules/primeng/fesm2022/primeng-autofocus.mjs"], "sourcesContent": ["import { DOCUMENT, isPlatform<PERSON>rowser } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, PLATFORM_ID, ElementRef, booleanAttribute, Input, Directive, NgModule } from '@angular/core';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { DomHandler } from 'primeng/dom';\n\n/**\n * AutoFocus manages focus on focusable element on load.\n * @group Components\n */\nclass AutoFocus extends BaseComponent {\n    /**\n     * When present, it specifies that the component should automatically get focus on load.\n     * @deprecated use [pAutoFocus]=\"true\"\n     * @group Props\n     */\n    autofocus = false;\n    /**\n     * When present, it specifies that the component should automatically get focus on load.\n     * @group Props\n     */\n    _autofocus = false;\n    focused = false;\n    platformId = inject(PLATFORM_ID);\n    document = inject(DOCUMENT);\n    host = inject(ElementRef);\n    ngAfterContentChecked() {\n        // This sets the `attr.autofocus` which is different than the Input `autofocus` attribute.\n        if (this.autofocus === false) {\n            this.host.nativeElement.removeAttribute('autofocus');\n        }\n        else {\n            this.host.nativeElement.setAttribute('autofocus', true);\n        }\n        if (!this.focused) {\n            this.autoFocus();\n        }\n    }\n    ngAfterViewChecked() {\n        if (!this.focused) {\n            this.autoFocus();\n        }\n    }\n    autoFocus() {\n        if (isPlatformBrowser(this.platformId) && this._autofocus) {\n            setTimeout(() => {\n                const focusableElements = DomHandler.getFocusableElements(this.host?.nativeElement);\n                if (focusableElements.length === 0) {\n                    this.host.nativeElement.focus();\n                }\n                if (focusableElements.length > 0) {\n                    focusableElements[0].focus();\n                }\n                this.focused = true;\n            });\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: AutoFocus, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"19.2.10\", type: AutoFocus, isStandalone: true, selector: \"[pAutoFocus]\", inputs: { autofocus: [\"autofocus\", \"autofocus\", booleanAttribute], _autofocus: [\"pAutoFocus\", \"_autofocus\"] }, usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: AutoFocus, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[pAutoFocus]',\n                    standalone: true\n                }]\n        }], propDecorators: { autofocus: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], _autofocus: [{\n                type: Input,\n                args: ['pAutoFocus']\n            }] } });\nclass AutoFocusModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: AutoFocusModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.10\", ngImport: i0, type: AutoFocusModule, imports: [AutoFocus], exports: [AutoFocus] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: AutoFocusModule });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: AutoFocusModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [AutoFocus],\n                    exports: [AutoFocus]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AutoFocus, AutoFocusModule };\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,iBAAiB,QAAQ,iBAAiB;AAC7D,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,WAAW,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AAC7G,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,UAAU,QAAQ,aAAa;;AAExC;AACA;AACA;AACA;AACA,MAAMC,SAAS,SAASF,aAAa,CAAC;EAClC;AACJ;AACA;AACA;AACA;EACIG,SAAS,GAAG,KAAK;EACjB;AACJ;AACA;AACA;EACIC,UAAU,GAAG,KAAK;EAClBC,OAAO,GAAG,KAAK;EACfC,UAAU,GAAGb,MAAM,CAACC,WAAW,CAAC;EAChCa,QAAQ,GAAGd,MAAM,CAACH,QAAQ,CAAC;EAC3BkB,IAAI,GAAGf,MAAM,CAACE,UAAU,CAAC;EACzBc,qBAAqBA,CAAA,EAAG;IACpB;IACA,IAAI,IAAI,CAACN,SAAS,KAAK,KAAK,EAAE;MAC1B,IAAI,CAACK,IAAI,CAACE,aAAa,CAACC,eAAe,CAAC,WAAW,CAAC;IACxD,CAAC,MACI;MACD,IAAI,CAACH,IAAI,CAACE,aAAa,CAACE,YAAY,CAAC,WAAW,EAAE,IAAI,CAAC;IAC3D;IACA,IAAI,CAAC,IAAI,CAACP,OAAO,EAAE;MACf,IAAI,CAACQ,SAAS,CAAC,CAAC;IACpB;EACJ;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACT,OAAO,EAAE;MACf,IAAI,CAACQ,SAAS,CAAC,CAAC;IACpB;EACJ;EACAA,SAASA,CAAA,EAAG;IACR,IAAItB,iBAAiB,CAAC,IAAI,CAACe,UAAU,CAAC,IAAI,IAAI,CAACF,UAAU,EAAE;MACvDW,UAAU,CAAC,MAAM;QACb,MAAMC,iBAAiB,GAAGf,UAAU,CAACgB,oBAAoB,CAAC,IAAI,CAACT,IAAI,EAAEE,aAAa,CAAC;QACnF,IAAIM,iBAAiB,CAACE,MAAM,KAAK,CAAC,EAAE;UAChC,IAAI,CAACV,IAAI,CAACE,aAAa,CAACS,KAAK,CAAC,CAAC;QACnC;QACA,IAAIH,iBAAiB,CAACE,MAAM,GAAG,CAAC,EAAE;UAC9BF,iBAAiB,CAAC,CAAC,CAAC,CAACG,KAAK,CAAC,CAAC;QAChC;QACA,IAAI,CAACd,OAAO,GAAG,IAAI;MACvB,CAAC,CAAC;IACN;EACJ;EACA,OAAOe,IAAI;IAAA,IAAAC,sBAAA;IAAA,gBAAAC,kBAAAC,iBAAA;MAAA,QAAAF,sBAAA,KAAAA,sBAAA,GAA+E7B,EAAE,CAAAgC,qBAAA,CAAQtB,SAAS,IAAAqB,iBAAA,IAATrB,SAAS;IAAA;EAAA;EAC7G,OAAOuB,IAAI,kBAD+EjC,EAAE,CAAAkC,iBAAA;IAAAC,IAAA,EACJzB,SAAS;IAAA0B,SAAA;IAAAC,MAAA;MAAA1B,SAAA,gCAAgGP,gBAAgB;MAAAQ,UAAA;IAAA;IAAA0B,QAAA,GADvHtC,EAAE,CAAAuC,0BAAA;EAAA;AAEhG;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH8FxC,EAAE,CAAAyC,iBAAA,CAGJ/B,SAAS,EAAc,CAAC;IACxGyB,IAAI,EAAE7B,SAAS;IACfoC,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,cAAc;MACxBC,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEjC,SAAS,EAAE,CAAC;MAC1BwB,IAAI,EAAE9B,KAAK;MACXqC,IAAI,EAAE,CAAC;QAAEG,SAAS,EAAEzC;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEQ,UAAU,EAAE,CAAC;MACbuB,IAAI,EAAE9B,KAAK;MACXqC,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMI,eAAe,CAAC;EAClB,OAAOlB,IAAI,YAAAmB,wBAAAhB,iBAAA;IAAA,YAAAA,iBAAA,IAAyFe,eAAe;EAAA;EACnH,OAAOE,IAAI,kBAlB+EhD,EAAE,CAAAiD,gBAAA;IAAAd,IAAA,EAkBSW,eAAe;IAAAI,OAAA,GAAYxC,SAAS;IAAAyC,OAAA,GAAazC,SAAS;EAAA;EAC/J,OAAO0C,IAAI,kBAnB+EpD,EAAE,CAAAqD,gBAAA;AAoBhG;AACA;EAAA,QAAAb,SAAA,oBAAAA,SAAA,KArB8FxC,EAAE,CAAAyC,iBAAA,CAqBJK,eAAe,EAAc,CAAC;IAC9GX,IAAI,EAAE5B,QAAQ;IACdmC,IAAI,EAAE,CAAC;MACCQ,OAAO,EAAE,CAACxC,SAAS,CAAC;MACpByC,OAAO,EAAE,CAACzC,SAAS;IACvB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASA,SAAS,EAAEoC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}