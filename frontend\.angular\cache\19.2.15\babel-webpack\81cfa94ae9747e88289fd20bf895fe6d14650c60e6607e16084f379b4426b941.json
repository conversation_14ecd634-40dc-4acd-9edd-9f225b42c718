{"ast": null, "code": "import { animation, style, animate, trigger, transition, useAnimation } from '@angular/animations';\nimport * as i1 from '@angular/common';\nimport { isPlatformBrowser, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, inject, NgZone, booleanAttribute, numberAttribute, ContentChildren, ContentChild, Input, ViewChild, Output, ViewEncapsulation, ChangeDetectionStrategy, Component, NgModule } from '@angular/core';\nimport { uuid, blockBodyScroll, unblockBodyScroll, setAttribute, hasClass, addClass, getOuterWidth, getOuterHeight, getViewport, removeClass, appendChild } from '@primeuix/utils';\nimport { TranslationKeys, SharedModule, PrimeTemplate } from 'primeng/api';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { Button } from 'primeng/button';\nimport { <PERSON><PERSON>and<PERSON> } from 'primeng/dom';\nimport { FocusTrap } from 'primeng/focustrap';\nimport { TimesIcon, WindowMaximizeIcon, WindowMinimizeIcon } from 'primeng/icons';\nimport { ZIndexUtils } from 'primeng/utils';\nimport { BaseStyle } from 'primeng/base';\nconst _c0 = [\"header\"];\nconst _c1 = [\"content\"];\nconst _c2 = [\"footer\"];\nconst _c3 = [\"closeicon\"];\nconst _c4 = [\"maximizeicon\"];\nconst _c5 = [\"minimizeicon\"];\nconst _c6 = [\"headless\"];\nconst _c7 = [\"titlebar\"];\nconst _c8 = [\"*\", [[\"p-footer\"]]];\nconst _c9 = [\"*\", \"p-footer\"];\nconst _c10 = (a0, a1, a2) => ({\n  position: \"fixed\",\n  height: \"100%\",\n  width: \"100%\",\n  left: 0,\n  top: 0,\n  display: \"flex\",\n  \"justify-content\": a0,\n  \"align-items\": a1,\n  \"pointer-events\": a2\n});\nconst _c11 = a0 => ({\n  \"p-dialog p-component\": true,\n  \"p-dialog-maximized\": a0\n});\nconst _c12 = () => ({\n  display: \"flex\",\n  \"flex-direction\": \"column\",\n  \"pointer-events\": \"auto\"\n});\nconst _c13 = (a0, a1) => ({\n  transform: a0,\n  transition: a1\n});\nconst _c14 = a0 => ({\n  value: \"visible\",\n  params: a0\n});\nfunction Dialog_div_0_div_1_ng_container_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Dialog_div_0_div_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Dialog_div_0_div_1_ng_container_2_ng_container_1_Template, 1, 0, \"ng-container\", 11);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1._headlessTemplate || ctx_r1.headlessTemplate || ctx_r1.headlessT);\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵlistener(\"mousedown\", function Dialog_div_0_div_1_ng_template_3_div_0_Template_div_mousedown_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.initResize($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.cx(\"resizeHandle\"));\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵproperty(\"id\", ctx_r1.ariaLabelledBy)(\"ngClass\", ctx_r1.cx(\"title\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.header);\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_p_button_5_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 18);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(6);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.maximized ? ctx_r1.minimizeIcon : ctx_r1.maximizeIcon);\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_p_button_5_ng_container_2_WindowMaximizeIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"WindowMaximizeIcon\");\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_p_button_5_ng_container_2_WindowMinimizeIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"WindowMinimizeIcon\");\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_p_button_5_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Dialog_div_0_div_1_ng_template_3_div_1_p_button_5_ng_container_2_WindowMaximizeIcon_1_Template, 1, 0, \"WindowMaximizeIcon\", 23)(2, Dialog_div_0_div_1_ng_template_3_div_1_p_button_5_ng_container_2_WindowMinimizeIcon_2_Template, 1, 0, \"WindowMinimizeIcon\", 23);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.maximized && !ctx_r1._maximizeiconTemplate && !ctx_r1.maximizeIconTemplate && !ctx_r1.maximizeIconT);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.maximized && !ctx_r1._minimizeiconTemplate && !ctx_r1.minimizeIconTemplate && !ctx_r1.minimizeIconT);\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_p_button_5_ng_container_3_1_ng_template_0_Template(rf, ctx) {}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_p_button_5_ng_container_3_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Dialog_div_0_div_1_ng_template_3_div_1_p_button_5_ng_container_3_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_p_button_5_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Dialog_div_0_div_1_ng_template_3_div_1_p_button_5_ng_container_3_1_Template, 1, 0, null, 11);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1._maximizeiconTemplate || ctx_r1.maximizeIconTemplate || ctx_r1.maximizeIconT);\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_p_button_5_ng_container_4_1_ng_template_0_Template(rf, ctx) {}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_p_button_5_ng_container_4_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Dialog_div_0_div_1_ng_template_3_div_1_p_button_5_ng_container_4_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_p_button_5_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Dialog_div_0_div_1_ng_template_3_div_1_p_button_5_ng_container_4_1_Template, 1, 0, null, 11);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1._minimizeiconTemplate || ctx_r1.minimizeIconTemplate || ctx_r1.minimizeIconT);\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_p_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 22);\n    i0.ɵɵlistener(\"onClick\", function Dialog_div_0_div_1_ng_template_3_div_1_p_button_5_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r1.maximize());\n    })(\"keydown.enter\", function Dialog_div_0_div_1_ng_template_3_div_1_p_button_5_Template_p_button_keydown_enter_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r1.maximize());\n    });\n    i0.ɵɵtemplate(1, Dialog_div_0_div_1_ng_template_3_div_1_p_button_5_span_1_Template, 1, 1, \"span\", 14)(2, Dialog_div_0_div_1_ng_template_3_div_1_p_button_5_ng_container_2_Template, 3, 2, \"ng-container\", 23)(3, Dialog_div_0_div_1_ng_template_3_div_1_p_button_5_ng_container_3_Template, 2, 1, \"ng-container\", 23)(4, Dialog_div_0_div_1_ng_template_3_div_1_p_button_5_ng_container_4_Template, 2, 1, \"ng-container\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵproperty(\"styleClass\", ctx_r1.cx(\"pcMaximizeButton\"))(\"tabindex\", ctx_r1.maximizable ? \"0\" : \"-1\")(\"ariaLabel\", ctx_r1.maximizeLabel)(\"buttonProps\", ctx_r1.maximizeButtonProps);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.maximizeIcon && !ctx_r1._maximizeiconTemplate && !ctx_r1._minimizeiconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.maximizeIcon && !(ctx_r1.maximizeButtonProps == null ? null : ctx_r1.maximizeButtonProps.icon));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.maximized);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.maximized);\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_p_button_6_ng_template_1_ng_container_0_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 18);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(8);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.closeIcon);\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_p_button_6_ng_template_1_ng_container_0_TimesIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"TimesIcon\");\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_p_button_6_ng_template_1_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Dialog_div_0_div_1_ng_template_3_div_1_p_button_6_ng_template_1_ng_container_0_span_1_Template, 1, 1, \"span\", 14)(2, Dialog_div_0_div_1_ng_template_3_div_1_p_button_6_ng_template_1_ng_container_0_TimesIcon_2_Template, 1, 0, \"TimesIcon\", 23);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(7);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.closeIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.closeIcon);\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_p_button_6_ng_template_1_span_1_1_ng_template_0_Template(rf, ctx) {}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_p_button_6_ng_template_1_span_1_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Dialog_div_0_div_1_ng_template_3_div_1_p_button_6_ng_template_1_span_1_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_p_button_6_ng_template_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtemplate(1, Dialog_div_0_div_1_ng_template_3_div_1_p_button_6_ng_template_1_span_1_1_Template, 1, 0, null, 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(7);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1._closeiconTemplate || ctx_r1.closeIconTemplate || ctx_r1.closeIconT);\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_p_button_6_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Dialog_div_0_div_1_ng_template_3_div_1_p_button_6_ng_template_1_ng_container_0_Template, 3, 2, \"ng-container\", 23)(1, Dialog_div_0_div_1_ng_template_3_div_1_p_button_6_ng_template_1_span_1_Template, 2, 1, \"span\", 23);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(6);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1._closeiconTemplate && !ctx_r1.closeIconTemplate && !ctx_r1.closeIconT && !(ctx_r1.closeButtonProps == null ? null : ctx_r1.closeButtonProps.icon));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1._closeiconTemplate || ctx_r1.closeIconTemplate || ctx_r1.closeIconT);\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_p_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 24);\n    i0.ɵɵlistener(\"onClick\", function Dialog_div_0_div_1_ng_template_3_div_1_p_button_6_Template_p_button_onClick_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r1.close($event));\n    })(\"keydown.enter\", function Dialog_div_0_div_1_ng_template_3_div_1_p_button_6_Template_p_button_keydown_enter_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r1.close($event));\n    });\n    i0.ɵɵtemplate(1, Dialog_div_0_div_1_ng_template_3_div_1_p_button_6_ng_template_1_Template, 2, 2, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵproperty(\"styleClass\", ctx_r1.cx(\"pcCloseButton\"))(\"ariaLabel\", ctx_r1.closeAriaLabel)(\"tabindex\", ctx_r1.closeTabindex)(\"buttonProps\", ctx_r1.closeButtonProps);\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16, 3);\n    i0.ɵɵlistener(\"mousedown\", function Dialog_div_0_div_1_ng_template_3_div_1_Template_div_mousedown_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.initDrag($event));\n    });\n    i0.ɵɵtemplate(2, Dialog_div_0_div_1_ng_template_3_div_1_span_2_Template, 2, 3, \"span\", 17)(3, Dialog_div_0_div_1_ng_template_3_div_1_ng_container_3_Template, 1, 0, \"ng-container\", 11);\n    i0.ɵɵelementStart(4, \"div\", 18);\n    i0.ɵɵtemplate(5, Dialog_div_0_div_1_ng_template_3_div_1_p_button_5_Template, 5, 8, \"p-button\", 19)(6, Dialog_div_0_div_1_ng_template_3_div_1_p_button_6_Template, 3, 4, \"p-button\", 20);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.cx(\"header\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1._headerTemplate && !ctx_r1.headerTemplate && !ctx_r1.headerT);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1._headerTemplate || ctx_r1.headerTemplate || ctx_r1.headerT);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.cx(\"headerActions\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.maximizable);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.closable);\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_6_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18, 5);\n    i0.ɵɵprojection(2, 1);\n    i0.ɵɵtemplate(3, Dialog_div_0_div_1_ng_template_3_div_6_ng_container_3_Template, 1, 0, \"ng-container\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.cx(\"footer\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1._footerTemplate || ctx_r1.footerTemplate || ctx_r1.footerT);\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Dialog_div_0_div_1_ng_template_3_div_0_Template, 1, 1, \"div\", 12)(1, Dialog_div_0_div_1_ng_template_3_div_1_Template, 7, 6, \"div\", 13);\n    i0.ɵɵelementStart(2, \"div\", 7, 2);\n    i0.ɵɵprojection(4);\n    i0.ɵɵtemplate(5, Dialog_div_0_div_1_ng_template_3_ng_container_5_Template, 1, 0, \"ng-container\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, Dialog_div_0_div_1_ng_template_3_div_6_Template, 4, 2, \"div\", 14);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.resizable);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showHeader);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r1.contentStyleClass);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.cx(\"content\"))(\"ngStyle\", ctx_r1.contentStyle);\n    i0.ɵɵattribute(\"data-pc-section\", \"content\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1._contentTemplate || ctx_r1.contentTemplate || ctx_r1.contentT);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1._footerTemplate || ctx_r1.footerTemplate || ctx_r1.footerT);\n  }\n}\nfunction Dialog_div_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9, 0);\n    i0.ɵɵlistener(\"@animation.start\", function Dialog_div_0_div_1_Template_div_animation_animation_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAnimationStart($event));\n    })(\"@animation.done\", function Dialog_div_0_div_1_Template_div_animation_animation_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAnimationEnd($event));\n    });\n    i0.ɵɵtemplate(2, Dialog_div_0_div_1_ng_container_2_Template, 2, 1, \"ng-container\", 10)(3, Dialog_div_0_div_1_ng_template_3_Template, 7, 9, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const notHeadless_r7 = i0.ɵɵreference(4);\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleMap(ctx_r1.style);\n    i0.ɵɵclassMap(ctx_r1.styleClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(13, _c11, ctx_r1.maximizable && ctx_r1.maximized))(\"ngStyle\", i0.ɵɵpureFunction0(15, _c12))(\"pFocusTrapDisabled\", ctx_r1.focusTrap === false)(\"@animation\", i0.ɵɵpureFunction1(19, _c14, i0.ɵɵpureFunction2(16, _c13, ctx_r1.transformOptions, ctx_r1.transitionOptions)));\n    i0.ɵɵattribute(\"role\", ctx_r1.role)(\"aria-labelledby\", ctx_r1.ariaLabelledBy)(\"aria-modal\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1._headlessTemplate || ctx_r1.headlessTemplate || ctx_r1.headlessT)(\"ngIfElse\", notHeadless_r7);\n  }\n}\nfunction Dialog_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵtemplate(1, Dialog_div_0_div_1_Template, 5, 21, \"div\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(ctx_r1.maskStyle);\n    i0.ɵɵclassMap(ctx_r1.maskStyleClass);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.maskClass)(\"ngStyle\", i0.ɵɵpureFunction3(7, _c10, ctx_r1.position === \"left\" || ctx_r1.position === \"topleft\" || ctx_r1.position === \"bottomleft\" ? \"flex-start\" : ctx_r1.position === \"right\" || ctx_r1.position === \"topright\" || ctx_r1.position === \"bottomright\" ? \"flex-end\" : \"center\", ctx_r1.position === \"top\" || ctx_r1.position === \"topleft\" || ctx_r1.position === \"topright\" ? \"flex-start\" : ctx_r1.position === \"bottom\" || ctx_r1.position === \"bottomleft\" || ctx_r1.position === \"bottomright\" ? \"flex-end\" : \"center\", ctx_r1.modal ? \"auto\" : \"none\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.visible);\n  }\n}\nconst theme = ({\n  dt\n}) => `\n.p-dialog {\n    max-height: 90%;\n    transform: scale(1);\n    border-radius: ${dt('dialog.border.radius')};\n    box-shadow: ${dt('dialog.shadow')};\n    background: ${dt('dialog.background')};\n    border: 1px solid ${dt('dialog.border.color')};\n    color: ${dt('dialog.color')};\n    display: flex;\n    flex-direction: column;\n    pointer-events: auto\n}\n\n.p-dialog-content {\n    overflow-y: auto;\n    padding: ${dt('dialog.content.padding')};\n    flex-grow: 1;\n}\n\n.p-dialog-header {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    flex-shrink: 0;\n    padding: ${dt('dialog.header.padding')};\n}\n\n.p-dialog-title {\n    font-weight: ${dt('dialog.title.font.weight')};\n    font-size: ${dt('dialog.title.font.size')};\n}\n\n.p-dialog-footer {\n    flex-shrink: 0;\n    padding: ${dt('dialog.footer.padding')};\n    display: flex;\n    justify-content: flex-end;\n    gap: ${dt('dialog.footer.gap')};\n}\n\n.p-dialog-header-actions {\n    display: flex;\n    align-items: center;\n    gap: ${dt('dialog.header.gap')};\n}\n\n.p-dialog-enter-active {\n    transition: all 150ms cubic-bezier(0, 0, 0.2, 1);\n}\n\n.p-dialog-leave-active {\n    transition: all 150ms cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.p-dialog-enter-from,\n.p-dialog-leave-to {\n    opacity: 0;\n    transform: scale(0.7);\n}\n\n.p-dialog-top .p-dialog,\n.p-dialog-bottom .p-dialog,\n.p-dialog-left .p-dialog,\n.p-dialog-right .p-dialog,\n.p-dialog-topleft .p-dialog,\n.p-dialog-topright .p-dialog,\n.p-dialog-bottomleft .p-dialog,\n.p-dialog-bottomright .p-dialog {\n    margin: 0.75rem;\n    transform: translate3d(0px, 0px, 0px);\n}\n\n.p-dialog-top .p-dialog-enter-active,\n.p-dialog-top .p-dialog-leave-active,\n.p-dialog-bottom .p-dialog-enter-active,\n.p-dialog-bottom .p-dialog-leave-active,\n.p-dialog-left .p-dialog-enter-active,\n.p-dialog-left .p-dialog-leave-active,\n.p-dialog-right .p-dialog-enter-active,\n.p-dialog-right .p-dialog-leave-active,\n.p-dialog-topleft .p-dialog-enter-active,\n.p-dialog-topleft .p-dialog-leave-active,\n.p-dialog-topright .p-dialog-enter-active,\n.p-dialog-topright .p-dialog-leave-active,\n.p-dialog-bottomleft .p-dialog-enter-active,\n.p-dialog-bottomleft .p-dialog-leave-active,\n.p-dialog-bottomright .p-dialog-enter-active,\n.p-dialog-bottomright .p-dialog-leave-active {\n    transition: all 0.3s ease-out;\n}\n\n.p-dialog-top .p-dialog-enter-from,\n.p-dialog-top .p-dialog-leave-to {\n    transform: translate3d(0px, -100%, 0px);\n}\n\n.p-dialog-bottom .p-dialog-enter-from,\n.p-dialog-bottom .p-dialog-leave-to {\n    transform: translate3d(0px, 100%, 0px);\n}\n\n.p-dialog-left .p-dialog-enter-from,\n.p-dialog-left .p-dialog-leave-to,\n.p-dialog-topleft .p-dialog-enter-from,\n.p-dialog-topleft .p-dialog-leave-to,\n.p-dialog-bottomleft .p-dialog-enter-from,\n.p-dialog-bottomleft .p-dialog-leave-to {\n    transform: translate3d(-100%, 0px, 0px);\n}\n\n.p-dialog-right .p-dialog-enter-from,\n.p-dialog-right .p-dialog-leave-to,\n.p-dialog-topright .p-dialog-enter-from,\n.p-dialog-topright .p-dialog-leave-to,\n.p-dialog-bottomright .p-dialog-enter-from,\n.p-dialog-bottomright .p-dialog-leave-to {\n    transform: translate3d(100%, 0px, 0px);\n}\n\n.p-dialog-left:dir(rtl) .p-dialog-enter-from,\n.p-dialog-left:dir(rtl) .p-dialog-leave-to,\n.p-dialog-topleft:dir(rtl) .p-dialog-enter-from,\n.p-dialog-topleft:dir(rtl) .p-dialog-leave-to,\n.p-dialog-bottomleft:dir(rtl) .p-dialog-enter-from,\n.p-dialog-bottomleft:dir(rtl) .p-dialog-leave-to {\n    transform: translate3d(100%, 0px, 0px);\n}\n\n.p-dialog-right:dir(rtl) .p-dialog-enter-from,\n.p-dialog-right:dir(rtl) .p-dialog-leave-to,\n.p-dialog-topright:dir(rtl) .p-dialog-enter-from,\n.p-dialog-topright:dir(rtl) .p-dialog-leave-to,\n.p-dialog-bottomright:dir(rtl) .p-dialog-enter-from,\n.p-dialog-bottomright:dir(rtl) .p-dialog-leave-to {\n    transform: translate3d(-100%, 0px, 0px);\n}\n\n.p-dialog-maximized {\n    width: 100vw !important;\n    height: 100vh !important;\n    top: 0px !important;\n    left: 0px !important;\n    max-height: 100%;\n    height: 100%;\n    border-radius: 0;\n}\n\n.p-dialog-maximized .p-dialog-content {\n    flex-grow: 1;\n}\n\n.p-overlay-mask:dir(rtl) {\n    flex-direction: row-reverse;\n}\n\n/* For PrimeNG */\n\n.p-dialog .p-resizable-handle {\n    position: absolute;\n    font-size: 0.1px;\n    display: block;\n    cursor: se-resize;\n    width: 12px;\n    height: 12px;\n    right: 1px;\n    bottom: 1px;\n}\n\n.p-confirm-dialog .p-dialog-content {\n    display: flex;\n    align-items: center;\n}\n`;\n/* Position */\nconst inlineStyles = {\n  mask: ({\n    instance\n  }) => ({\n    position: 'fixed',\n    height: '100%',\n    width: '100%',\n    left: 0,\n    top: 0,\n    display: 'flex',\n    justifyContent: instance.position === 'left' || instance.position === 'topleft' || instance.position === 'bottomleft' ? 'flex-start' : instance.position === 'right' || instance.position === 'topright' || instance.position === 'bottomright' ? 'flex-end' : 'center',\n    alignItems: instance.position === 'top' || instance.position === 'topleft' || instance.position === 'topright' ? 'flex-start' : instance.position === 'bottom' || instance.position === 'bottomleft' || instance.position === 'bottomright' ? 'flex-end' : 'center',\n    pointerEvents: instance.modal ? 'auto' : 'none'\n  }),\n  root: {\n    display: 'flex',\n    flexDirection: 'column',\n    pointerEvents: 'auto'\n  }\n};\nconst classes = {\n  mask: ({\n    instance\n  }) => {\n    const positions = ['left', 'right', 'top', 'topleft', 'topright', 'bottom', 'bottomleft', 'bottomright'];\n    const pos = positions.find(item => item === instance.position);\n    return {\n      'p-dialog-mask': true,\n      'p-overlay-mask p-overlay-mask-enter': instance.modal,\n      [`p-dialog-${pos}`]: pos\n    };\n  },\n  root: ({\n    instance\n  }) => ({\n    'p-dialog p-component': true,\n    'p-dialog-maximized': instance.maximizable && instance.maximized\n  }),\n  header: 'p-dialog-header',\n  title: 'p-dialog-title',\n  resizeHandle: 'p-resizable-handle',\n  headerActions: 'p-dialog-header-actions',\n  pcMaximizeButton: 'p-dialog-maximize-button',\n  pcCloseButton: 'p-dialog-close-button',\n  content: 'p-dialog-content',\n  footer: 'p-dialog-footer'\n};\nclass DialogStyle extends BaseStyle {\n  name = 'dialog';\n  theme = theme;\n  classes = classes;\n  inlineStyles = inlineStyles;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵDialogStyle_BaseFactory;\n    return function DialogStyle_Factory(__ngFactoryType__) {\n      return (ɵDialogStyle_BaseFactory || (ɵDialogStyle_BaseFactory = i0.ɵɵgetInheritedFactory(DialogStyle)))(__ngFactoryType__ || DialogStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DialogStyle,\n    factory: DialogStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DialogStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n *\n * Dialog is a container to display content in an overlay window.\n *\n * [Live Demo](https://www.primeng.org/dialog)\n *\n * @module dialogstyle\n *\n */\nvar DialogClasses;\n(function (DialogClasses) {\n  /**\n   * Class name of the mask element\n   */\n  DialogClasses[\"mask\"] = \"p-dialog-mask\";\n  /**\n   * Class name of the root element\n   */\n  DialogClasses[\"root\"] = \"p-dialog\";\n  /**\n   * Class name of the header element\n   */\n  DialogClasses[\"header\"] = \"p-dialog-header\";\n  /**\n   * Class name of the title element\n   */\n  DialogClasses[\"title\"] = \"p-dialog-title\";\n  /**\n   * Class name of the header actions element\n   */\n  DialogClasses[\"headerActions\"] = \"p-dialog-header-actions\";\n  /**\n   * Class name of the maximize button element\n   */\n  DialogClasses[\"pcMaximizeButton\"] = \"p-dialog-maximize-button\";\n  /**\n   * Class name of the close button element\n   */\n  DialogClasses[\"pcCloseButton\"] = \"p-dialog-close-button\";\n  /**\n   * Class name of the content element\n   */\n  DialogClasses[\"content\"] = \"p-dialog-content\";\n  /**\n   * Class name of the footer element\n   */\n  DialogClasses[\"footer\"] = \"p-dialog-footer\";\n})(DialogClasses || (DialogClasses = {}));\nconst showAnimation = animation([style({\n  transform: '{{transform}}',\n  opacity: 0\n}), animate('{{transition}}')]);\nconst hideAnimation = animation([animate('{{transition}}', style({\n  transform: '{{transform}}',\n  opacity: 0\n}))]);\n/**\n * Dialog is a container to display content in an overlay window.\n * @group Components\n */\nclass Dialog extends BaseComponent {\n  /**\n   * Title text of the dialog.\n   * @group Props\n   */\n  header;\n  /**\n   * Enables dragging to change the position using header.\n   * @group Props\n   */\n  draggable = true;\n  /**\n   * Enables resizing of the content.\n   * @group Props\n   */\n  resizable = true;\n  /**\n   * Defines the left offset of dialog.\n   * @group Props\n   * @deprecated positionLeft property is deprecated.\n   */\n  get positionLeft() {\n    return 0;\n  }\n  set positionLeft(_positionLeft) {\n    console.log('positionLeft property is deprecated.');\n  }\n  /**\n   * Defines the top offset of dialog.\n   * @group Props\n   * @deprecated positionTop property is deprecated.\n   */\n  get positionTop() {\n    return 0;\n  }\n  set positionTop(_positionTop) {\n    console.log('positionTop property is deprecated.');\n  }\n  /**\n   * Style of the content section.\n   * @group Props\n   */\n  contentStyle;\n  /**\n   * Style class of the content.\n   * @group Props\n   */\n  contentStyleClass;\n  /**\n   * Defines if background should be blocked when dialog is displayed.\n   * @group Props\n   */\n  modal = false;\n  /**\n   * Specifies if pressing escape key should hide the dialog.\n   * @group Props\n   */\n  closeOnEscape = true;\n  /**\n   * Specifies if clicking the modal background should hide the dialog.\n   * @group Props\n   */\n  dismissableMask = false;\n  /**\n   * When enabled dialog is displayed in RTL direction.\n   * @group Props\n   */\n  rtl = false;\n  /**\n   * Adds a close icon to the header to hide the dialog.\n   * @group Props\n   */\n  closable = true;\n  /**\n   * Defines if the component is responsive.\n   * @group Props\n   * @deprecated Responsive property is deprecated.\n   */\n  get responsive() {\n    return false;\n  }\n  set responsive(_responsive) {\n    console.log('Responsive property is deprecated.');\n  }\n  /**\n   * Target element to attach the dialog, valid values are \"body\" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n   * @group Props\n   */\n  appendTo;\n  /**\n   * Object literal to define widths per screen size.\n   * @group Props\n   */\n  breakpoints;\n  /**\n   * Style class of the component.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Style class of the mask.\n   * @group Props\n   */\n  maskStyleClass;\n  /**\n   * Style of the mask.\n   * @group Props\n   */\n  maskStyle;\n  /**\n   * Whether to show the header or not.\n   * @group Props\n   */\n  showHeader = true;\n  /**\n   * Defines the breakpoint of the component responsive.\n   * @group Props\n   * @deprecated Breakpoint property is not utilized and deprecated. Use breakpoints or CSS media queries instead.\n   */\n  get breakpoint() {\n    return 649;\n  }\n  set breakpoint(_breakpoint) {\n    console.log('Breakpoint property is not utilized and deprecated, use breakpoints or CSS media queries instead.');\n  }\n  /**\n   * Whether background scroll should be blocked when dialog is visible.\n   * @group Props\n   */\n  blockScroll = false;\n  /**\n   * Whether to automatically manage layering.\n   * @group Props\n   */\n  autoZIndex = true;\n  /**\n   * Base zIndex value to use in layering.\n   * @group Props\n   */\n  baseZIndex = 0;\n  /**\n   * Minimum value for the left coordinate of dialog in dragging.\n   * @group Props\n   */\n  minX = 0;\n  /**\n   * Minimum value for the top coordinate of dialog in dragging.\n   * @group Props\n   */\n  minY = 0;\n  /**\n   * When enabled, first focusable element receives focus on show.\n   * @group Props\n   */\n  focusOnShow = true;\n  /**\n   * Whether the dialog can be displayed full screen.\n   * @group Props\n   */\n  maximizable = false;\n  /**\n   * Keeps dialog in the viewport.\n   * @group Props\n   */\n  keepInViewport = true;\n  /**\n   * When enabled, can only focus on elements inside the dialog.\n   * @group Props\n   */\n  focusTrap = true;\n  /**\n   * Transition options of the animation.\n   * @group Props\n   */\n  transitionOptions = '150ms cubic-bezier(0, 0, 0.2, 1)';\n  /**\n   * Name of the close icon.\n   * @group Props\n   */\n  closeIcon;\n  /**\n   * Defines a string that labels the close button for accessibility.\n   * @group Props\n   */\n  closeAriaLabel;\n  /**\n   * Index of the close button in tabbing order.\n   * @group Props\n   */\n  closeTabindex = '0';\n  /**\n   * Name of the minimize icon.\n   * @group Props\n   */\n  minimizeIcon;\n  /**\n   * Name of the maximize icon.\n   * @group Props\n   */\n  maximizeIcon;\n  /**\n   * Used to pass all properties of the ButtonProps to the Button component.\n   * @group Props\n   */\n  closeButtonProps = {\n    severity: 'secondary',\n    text: true,\n    rounded: true\n  };\n  /**\n   * Used to pass all properties of the ButtonProps to the Button component.\n   * @group Props\n   */\n  maximizeButtonProps = {\n    severity: 'secondary',\n    text: true,\n    rounded: true\n  };\n  /**\n   * Specifies the visibility of the dialog.\n   * @group Props\n   */\n  get visible() {\n    return this._visible;\n  }\n  set visible(value) {\n    this._visible = value;\n    if (this._visible && !this.maskVisible) {\n      this.maskVisible = true;\n    }\n  }\n  /**\n   * Inline style of the component.\n   * @group Props\n   */\n  get style() {\n    return this._style;\n  }\n  set style(value) {\n    if (value) {\n      this._style = {\n        ...value\n      };\n      this.originalStyle = value;\n    }\n  }\n  /**\n   * Position of the dialog.\n   * @group Props\n   */\n  get position() {\n    return this._position;\n  }\n  set position(value) {\n    this._position = value;\n    switch (value) {\n      case 'topleft':\n      case 'bottomleft':\n      case 'left':\n        this.transformOptions = 'translate3d(-100%, 0px, 0px)';\n        break;\n      case 'topright':\n      case 'bottomright':\n      case 'right':\n        this.transformOptions = 'translate3d(100%, 0px, 0px)';\n        break;\n      case 'bottom':\n        this.transformOptions = 'translate3d(0px, 100%, 0px)';\n        break;\n      case 'top':\n        this.transformOptions = 'translate3d(0px, -100%, 0px)';\n        break;\n      default:\n        this.transformOptions = 'scale(0.7)';\n        break;\n    }\n  }\n  /**\n   * Role attribute of html element.\n   * @group Emits\n   */\n  role = 'dialog';\n  /**\n   * Callback to invoke when dialog is shown.\n   * @group Emits\n   */\n  onShow = new EventEmitter();\n  /**\n   * Callback to invoke when dialog is hidden.\n   * @group Emits\n   */\n  onHide = new EventEmitter();\n  /**\n   * This EventEmitter is used to notify changes in the visibility state of a component.\n   * @param {boolean} value - New value.\n   * @group Emits\n   */\n  visibleChange = new EventEmitter();\n  /**\n   * Callback to invoke when dialog resizing is initiated.\n   * @param {MouseEvent} event - Mouse event.\n   * @group Emits\n   */\n  onResizeInit = new EventEmitter();\n  /**\n   * Callback to invoke when dialog resizing is completed.\n   * @param {MouseEvent} event - Mouse event.\n   * @group Emits\n   */\n  onResizeEnd = new EventEmitter();\n  /**\n   * Callback to invoke when dialog dragging is completed.\n   * @param {DragEvent} event - Drag event.\n   * @group Emits\n   */\n  onDragEnd = new EventEmitter();\n  /**\n   * Callback to invoke when dialog maximized or unmaximized.\n   * @group Emits\n   */\n  onMaximize = new EventEmitter();\n  headerViewChild;\n  contentViewChild;\n  footerViewChild;\n  /**\n   * Header template.\n   * @group Props\n   */\n  headerTemplate;\n  /**\n   * Content template.\n   * @group Props\n   */\n  contentTemplate;\n  /**\n   * Footer template.\n   * @group Props\n   */\n  footerTemplate;\n  /**\n   * Close icon template.\n   * @group Props\n   */\n  closeIconTemplate;\n  /**\n   * Maximize icon template.\n   * @group Props\n   */\n  maximizeIconTemplate;\n  /**\n   * Minimize icon template.\n   * @group Props\n   */\n  minimizeIconTemplate;\n  /**\n   * Headless template.\n   * @group Props\n   */\n  headlessTemplate;\n  _headerTemplate;\n  _contentTemplate;\n  _footerTemplate;\n  _closeiconTemplate;\n  _maximizeiconTemplate;\n  _minimizeiconTemplate;\n  _headlessTemplate;\n  _visible = false;\n  maskVisible;\n  container;\n  wrapper;\n  dragging;\n  ariaLabelledBy = this.getAriaLabelledBy();\n  documentDragListener;\n  documentDragEndListener;\n  resizing;\n  documentResizeListener;\n  documentResizeEndListener;\n  documentEscapeListener;\n  maskClickListener;\n  lastPageX;\n  lastPageY;\n  preventVisibleChangePropagation;\n  maximized;\n  preMaximizeContentHeight;\n  preMaximizeContainerWidth;\n  preMaximizeContainerHeight;\n  preMaximizePageX;\n  preMaximizePageY;\n  id = uuid('pn_id_');\n  _style = {};\n  _position = 'center';\n  originalStyle;\n  transformOptions = 'scale(0.7)';\n  styleElement;\n  window;\n  _componentStyle = inject(DialogStyle);\n  headerT;\n  contentT;\n  footerT;\n  closeIconT;\n  maximizeIconT;\n  minimizeIconT;\n  headlessT;\n  get maximizeLabel() {\n    return this.config.getTranslation(TranslationKeys.ARIA)['maximizeLabel'];\n  }\n  zone = inject(NgZone);\n  get maskClass() {\n    const positions = ['left', 'right', 'top', 'topleft', 'topright', 'bottom', 'bottomleft', 'bottomright'];\n    const pos = positions.find(item => item === this.position);\n    return {\n      'p-dialog-mask': true,\n      'p-overlay-mask p-overlay-mask-enter': this.modal || this.dismissableMask,\n      [`p-dialog-${pos}`]: pos\n    };\n  }\n  ngOnInit() {\n    super.ngOnInit();\n    if (this.breakpoints) {\n      this.createStyle();\n    }\n  }\n  templates;\n  ngAfterContentInit() {\n    this.templates?.forEach(item => {\n      switch (item.getType()) {\n        case 'header':\n          this.headerT = item.template;\n          break;\n        case 'content':\n          this.contentT = item.template;\n          break;\n        case 'footer':\n          this.footerT = item.template;\n          break;\n        case 'closeicon':\n          this.closeIconT = item.template;\n          break;\n        case 'maximizeicon':\n          this.maximizeIconT = item.template;\n          break;\n        case 'minimizeicon':\n          this.minimizeIconT = item.template;\n          break;\n        case 'headless':\n          this.headlessT = item.template;\n          break;\n        default:\n          this.contentT = item.template;\n          break;\n      }\n    });\n  }\n  getAriaLabelledBy() {\n    return this.header !== null ? uuid('pn_id_') + '_header' : null;\n  }\n  parseDurationToMilliseconds(durationString) {\n    const transitionTimeRegex = /([\\d\\.]+)(ms|s)\\b/g;\n    let totalMilliseconds = 0;\n    let match;\n    while ((match = transitionTimeRegex.exec(durationString)) !== null) {\n      const value = parseFloat(match[1]);\n      const unit = match[2];\n      if (unit === 'ms') {\n        totalMilliseconds += value;\n      } else if (unit === 's') {\n        totalMilliseconds += value * 1000;\n      }\n    }\n    if (totalMilliseconds === 0) {\n      return undefined;\n    }\n    return totalMilliseconds;\n  }\n  _focus(focusParentElement) {\n    if (focusParentElement) {\n      const timeoutDuration = this.parseDurationToMilliseconds(this.transitionOptions);\n      let _focusableElements = DomHandler.getFocusableElements(focusParentElement);\n      if (_focusableElements && _focusableElements.length > 0) {\n        this.zone.runOutsideAngular(() => {\n          setTimeout(() => _focusableElements[0].focus(), timeoutDuration || 5);\n        });\n        return true;\n      }\n    }\n    return false;\n  }\n  focus(focusParentElement) {\n    let focused = this._focus(focusParentElement);\n    if (!focused) {\n      focused = this._focus(this.footerViewChild?.nativeElement);\n      if (!focused) {\n        focused = this._focus(this.headerViewChild?.nativeElement);\n        if (!focused) {\n          this._focus(this.contentViewChild?.nativeElement);\n        }\n      }\n    }\n  }\n  close(event) {\n    this.visibleChange.emit(false);\n    event.preventDefault();\n  }\n  enableModality() {\n    if (this.closable && this.dismissableMask) {\n      this.maskClickListener = this.renderer.listen(this.wrapper, 'mousedown', event => {\n        if (this.wrapper && this.wrapper.isSameNode(event.target)) {\n          this.close(event);\n        }\n      });\n    }\n    if (this.modal) {\n      blockBodyScroll();\n    }\n  }\n  disableModality() {\n    if (this.wrapper) {\n      if (this.dismissableMask) {\n        this.unbindMaskClickListener();\n      }\n      // for nested dialogs w/modal\n      const scrollBlockers = document.querySelectorAll('.p-dialog-mask-scrollblocker');\n      if (this.modal && scrollBlockers && scrollBlockers.length == 1) {\n        unblockBodyScroll();\n      }\n      if (!this.cd.destroyed) {\n        this.cd.detectChanges();\n      }\n    }\n  }\n  maximize() {\n    this.maximized = !this.maximized;\n    if (!this.modal && !this.blockScroll) {\n      if (this.maximized) {\n        blockBodyScroll();\n      } else {\n        unblockBodyScroll();\n      }\n    }\n    this.onMaximize.emit({\n      maximized: this.maximized\n    });\n  }\n  unbindMaskClickListener() {\n    if (this.maskClickListener) {\n      this.maskClickListener();\n      this.maskClickListener = null;\n    }\n  }\n  moveOnTop() {\n    if (this.autoZIndex) {\n      ZIndexUtils.set('modal', this.container, this.baseZIndex + this.config.zIndex.modal);\n      this.wrapper.style.zIndex = String(parseInt(this.container.style.zIndex, 10) - 1);\n    }\n  }\n  createStyle() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (!this.styleElement) {\n        this.styleElement = this.renderer.createElement('style');\n        this.styleElement.type = 'text/css';\n        this.renderer.appendChild(this.document.head, this.styleElement);\n        let innerHTML = '';\n        for (let breakpoint in this.breakpoints) {\n          innerHTML += `\n                        @media screen and (max-width: ${breakpoint}) {\n                            .p-dialog[${this.id}]:not(.p-dialog-maximized) {\n                                width: ${this.breakpoints[breakpoint]} !important;\n                            }\n                        }\n                    `;\n        }\n        this.renderer.setProperty(this.styleElement, 'innerHTML', innerHTML);\n        setAttribute(this.styleElement, 'nonce', this.config?.csp()?.nonce);\n      }\n    }\n  }\n  initDrag(event) {\n    if (hasClass(event.target, 'p-dialog-maximize-icon') || hasClass(event.target, 'p-dialog-header-close-icon') || hasClass(event.target.parentElement, 'p-dialog-header-icon')) {\n      return;\n    }\n    if (this.draggable) {\n      this.dragging = true;\n      this.lastPageX = event.pageX;\n      this.lastPageY = event.pageY;\n      this.container.style.margin = '0';\n      addClass(this.document.body, 'p-unselectable-text');\n    }\n  }\n  onDrag(event) {\n    if (this.dragging) {\n      const containerWidth = getOuterWidth(this.container);\n      const containerHeight = getOuterHeight(this.container);\n      const deltaX = event.pageX - this.lastPageX;\n      const deltaY = event.pageY - this.lastPageY;\n      const offset = this.container.getBoundingClientRect();\n      const containerComputedStyle = getComputedStyle(this.container);\n      const leftMargin = parseFloat(containerComputedStyle.marginLeft);\n      const topMargin = parseFloat(containerComputedStyle.marginTop);\n      const leftPos = offset.left + deltaX - leftMargin;\n      const topPos = offset.top + deltaY - topMargin;\n      const viewport = getViewport();\n      this.container.style.position = 'fixed';\n      if (this.keepInViewport) {\n        if (leftPos >= this.minX && leftPos + containerWidth < viewport.width) {\n          this._style.left = `${leftPos}px`;\n          this.lastPageX = event.pageX;\n          this.container.style.left = `${leftPos}px`;\n        }\n        if (topPos >= this.minY && topPos + containerHeight < viewport.height) {\n          this._style.top = `${topPos}px`;\n          this.lastPageY = event.pageY;\n          this.container.style.top = `${topPos}px`;\n        }\n      } else {\n        this.lastPageX = event.pageX;\n        this.container.style.left = `${leftPos}px`;\n        this.lastPageY = event.pageY;\n        this.container.style.top = `${topPos}px`;\n      }\n    }\n  }\n  endDrag(event) {\n    if (this.dragging) {\n      this.dragging = false;\n      removeClass(this.document.body, 'p-unselectable-text');\n      this.cd.detectChanges();\n      this.onDragEnd.emit(event);\n    }\n  }\n  resetPosition() {\n    this.container.style.position = '';\n    this.container.style.left = '';\n    this.container.style.top = '';\n    this.container.style.margin = '';\n  }\n  //backward compatibility\n  center() {\n    this.resetPosition();\n  }\n  initResize(event) {\n    if (this.resizable) {\n      this.resizing = true;\n      this.lastPageX = event.pageX;\n      this.lastPageY = event.pageY;\n      addClass(this.document.body, 'p-unselectable-text');\n      this.onResizeInit.emit(event);\n    }\n  }\n  onResize(event) {\n    if (this.resizing) {\n      let deltaX = event.pageX - this.lastPageX;\n      let deltaY = event.pageY - this.lastPageY;\n      let containerWidth = getOuterWidth(this.container);\n      let containerHeight = getOuterHeight(this.container);\n      let contentHeight = getOuterHeight(this.contentViewChild?.nativeElement);\n      let newWidth = containerWidth + deltaX;\n      let newHeight = containerHeight + deltaY;\n      let minWidth = this.container.style.minWidth;\n      let minHeight = this.container.style.minHeight;\n      let offset = this.container.getBoundingClientRect();\n      let viewport = getViewport();\n      let hasBeenDragged = !parseInt(this.container.style.top) || !parseInt(this.container.style.left);\n      if (hasBeenDragged) {\n        newWidth += deltaX;\n        newHeight += deltaY;\n      }\n      if ((!minWidth || newWidth > parseInt(minWidth)) && offset.left + newWidth < viewport.width) {\n        this._style.width = newWidth + 'px';\n        this.container.style.width = this._style.width;\n      }\n      if ((!minHeight || newHeight > parseInt(minHeight)) && offset.top + newHeight < viewport.height) {\n        this.contentViewChild.nativeElement.style.height = contentHeight + newHeight - containerHeight + 'px';\n        if (this._style.height) {\n          this._style.height = newHeight + 'px';\n          this.container.style.height = this._style.height;\n        }\n      }\n      this.lastPageX = event.pageX;\n      this.lastPageY = event.pageY;\n    }\n  }\n  resizeEnd(event) {\n    if (this.resizing) {\n      this.resizing = false;\n      removeClass(this.document.body, 'p-unselectable-text');\n      this.onResizeEnd.emit(event);\n    }\n  }\n  bindGlobalListeners() {\n    if (this.draggable) {\n      this.bindDocumentDragListener();\n      this.bindDocumentDragEndListener();\n    }\n    if (this.resizable) {\n      this.bindDocumentResizeListeners();\n    }\n    if (this.closeOnEscape && this.closable) {\n      this.bindDocumentEscapeListener();\n    }\n  }\n  unbindGlobalListeners() {\n    this.unbindDocumentDragListener();\n    this.unbindDocumentDragEndListener();\n    this.unbindDocumentResizeListeners();\n    this.unbindDocumentEscapeListener();\n  }\n  bindDocumentDragListener() {\n    if (!this.documentDragListener) {\n      this.zone.runOutsideAngular(() => {\n        this.documentDragListener = this.renderer.listen(this.document.defaultView, 'mousemove', this.onDrag.bind(this));\n      });\n    }\n  }\n  unbindDocumentDragListener() {\n    if (this.documentDragListener) {\n      this.documentDragListener();\n      this.documentDragListener = null;\n    }\n  }\n  bindDocumentDragEndListener() {\n    if (!this.documentDragEndListener) {\n      this.zone.runOutsideAngular(() => {\n        this.documentDragEndListener = this.renderer.listen(this.document.defaultView, 'mouseup', this.endDrag.bind(this));\n      });\n    }\n  }\n  unbindDocumentDragEndListener() {\n    if (this.documentDragEndListener) {\n      this.documentDragEndListener();\n      this.documentDragEndListener = null;\n    }\n  }\n  bindDocumentResizeListeners() {\n    if (!this.documentResizeListener && !this.documentResizeEndListener) {\n      this.zone.runOutsideAngular(() => {\n        this.documentResizeListener = this.renderer.listen(this.document.defaultView, 'mousemove', this.onResize.bind(this));\n        this.documentResizeEndListener = this.renderer.listen(this.document.defaultView, 'mouseup', this.resizeEnd.bind(this));\n      });\n    }\n  }\n  unbindDocumentResizeListeners() {\n    if (this.documentResizeListener && this.documentResizeEndListener) {\n      this.documentResizeListener();\n      this.documentResizeEndListener();\n      this.documentResizeListener = null;\n      this.documentResizeEndListener = null;\n    }\n  }\n  bindDocumentEscapeListener() {\n    const documentTarget = this.el ? this.el.nativeElement.ownerDocument : 'document';\n    this.documentEscapeListener = this.renderer.listen(documentTarget, 'keydown', event => {\n      if (event.key == 'Escape') {\n        this.close(event);\n      }\n    });\n  }\n  unbindDocumentEscapeListener() {\n    if (this.documentEscapeListener) {\n      this.documentEscapeListener();\n      this.documentEscapeListener = null;\n    }\n  }\n  appendContainer() {\n    if (this.appendTo) {\n      if (this.appendTo === 'body') this.renderer.appendChild(this.document.body, this.wrapper);else appendChild(this.appendTo, this.wrapper);\n    }\n  }\n  restoreAppend() {\n    if (this.container && this.appendTo) {\n      this.renderer.appendChild(this.el.nativeElement, this.wrapper);\n    }\n  }\n  onAnimationStart(event) {\n    switch (event.toState) {\n      case 'visible':\n        this.container = event.element;\n        this.wrapper = this.container?.parentElement;\n        this.appendContainer();\n        this.moveOnTop();\n        this.bindGlobalListeners();\n        this.container?.setAttribute(this.id, '');\n        if (this.modal) {\n          this.enableModality();\n        }\n        // if (!this.modal && this.blockScroll) {\n        //     addClass(this.document.body, 'p-overflow-hidden');\n        // }\n        if (this.focusOnShow) {\n          this.focus();\n        }\n        break;\n      case 'void':\n        if (this.wrapper && this.modal) {\n          addClass(this.wrapper, 'p-overlay-mask-leave');\n        }\n        break;\n    }\n  }\n  onAnimationEnd(event) {\n    switch (event.toState) {\n      case 'void':\n        this.onContainerDestroy();\n        this.onHide.emit({});\n        this.cd.markForCheck();\n        if (this.maskVisible !== this.visible) {\n          this.maskVisible = this.visible;\n        }\n        break;\n      case 'visible':\n        this.onShow.emit({});\n        break;\n    }\n  }\n  onContainerDestroy() {\n    this.unbindGlobalListeners();\n    this.dragging = false;\n    this.maskVisible = false;\n    if (this.maximized) {\n      // removeClass(this.document.body, 'p-overflow-hidden')\n      this.document.body.style.removeProperty('--scrollbar;-width');\n      this.maximized = false;\n    }\n    if (this.modal) {\n      this.disableModality();\n    }\n    // if (this.blockScroll) {\n    //      removeClass(this.document.body, 'p-overflow-hidden');\n    // }\n    if (hasClass(this.document.body, 'p-overflow-hidden')) {\n      removeClass(this.document.body, 'p-overflow-hidden');\n    }\n    if (this.container && this.autoZIndex) {\n      ZIndexUtils.clear(this.container);\n    }\n    this.container = null;\n    this.wrapper = null;\n    this._style = this.originalStyle ? {\n      ...this.originalStyle\n    } : {};\n  }\n  destroyStyle() {\n    if (this.styleElement) {\n      this.renderer.removeChild(this.document.head, this.styleElement);\n      this.styleElement = null;\n    }\n  }\n  ngOnDestroy() {\n    if (this.container) {\n      this.restoreAppend();\n      this.onContainerDestroy();\n    }\n    this.destroyStyle();\n    super.ngOnDestroy();\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵDialog_BaseFactory;\n    return function Dialog_Factory(__ngFactoryType__) {\n      return (ɵDialog_BaseFactory || (ɵDialog_BaseFactory = i0.ɵɵgetInheritedFactory(Dialog)))(__ngFactoryType__ || Dialog);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Dialog,\n    selectors: [[\"p-dialog\"]],\n    contentQueries: function Dialog_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, _c0, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c1, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c2, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c3, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c4, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c5, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c6, 4);\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._headerTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._contentTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._footerTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._closeiconTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._maximizeiconTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._minimizeiconTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._headlessTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    viewQuery: function Dialog_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c7, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n        i0.ɵɵviewQuery(_c2, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerViewChild = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.contentViewChild = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.footerViewChild = _t.first);\n      }\n    },\n    inputs: {\n      header: \"header\",\n      draggable: [2, \"draggable\", \"draggable\", booleanAttribute],\n      resizable: [2, \"resizable\", \"resizable\", booleanAttribute],\n      positionLeft: \"positionLeft\",\n      positionTop: \"positionTop\",\n      contentStyle: \"contentStyle\",\n      contentStyleClass: \"contentStyleClass\",\n      modal: [2, \"modal\", \"modal\", booleanAttribute],\n      closeOnEscape: [2, \"closeOnEscape\", \"closeOnEscape\", booleanAttribute],\n      dismissableMask: [2, \"dismissableMask\", \"dismissableMask\", booleanAttribute],\n      rtl: [2, \"rtl\", \"rtl\", booleanAttribute],\n      closable: [2, \"closable\", \"closable\", booleanAttribute],\n      responsive: \"responsive\",\n      appendTo: \"appendTo\",\n      breakpoints: \"breakpoints\",\n      styleClass: \"styleClass\",\n      maskStyleClass: \"maskStyleClass\",\n      maskStyle: \"maskStyle\",\n      showHeader: [2, \"showHeader\", \"showHeader\", booleanAttribute],\n      breakpoint: \"breakpoint\",\n      blockScroll: [2, \"blockScroll\", \"blockScroll\", booleanAttribute],\n      autoZIndex: [2, \"autoZIndex\", \"autoZIndex\", booleanAttribute],\n      baseZIndex: [2, \"baseZIndex\", \"baseZIndex\", numberAttribute],\n      minX: [2, \"minX\", \"minX\", numberAttribute],\n      minY: [2, \"minY\", \"minY\", numberAttribute],\n      focusOnShow: [2, \"focusOnShow\", \"focusOnShow\", booleanAttribute],\n      maximizable: [2, \"maximizable\", \"maximizable\", booleanAttribute],\n      keepInViewport: [2, \"keepInViewport\", \"keepInViewport\", booleanAttribute],\n      focusTrap: [2, \"focusTrap\", \"focusTrap\", booleanAttribute],\n      transitionOptions: \"transitionOptions\",\n      closeIcon: \"closeIcon\",\n      closeAriaLabel: \"closeAriaLabel\",\n      closeTabindex: \"closeTabindex\",\n      minimizeIcon: \"minimizeIcon\",\n      maximizeIcon: \"maximizeIcon\",\n      closeButtonProps: \"closeButtonProps\",\n      maximizeButtonProps: \"maximizeButtonProps\",\n      visible: \"visible\",\n      style: \"style\",\n      position: \"position\",\n      role: \"role\",\n      headerTemplate: [0, \"content\", \"headerTemplate\"],\n      contentTemplate: \"contentTemplate\",\n      footerTemplate: \"footerTemplate\",\n      closeIconTemplate: \"closeIconTemplate\",\n      maximizeIconTemplate: \"maximizeIconTemplate\",\n      minimizeIconTemplate: \"minimizeIconTemplate\",\n      headlessTemplate: \"headlessTemplate\"\n    },\n    outputs: {\n      onShow: \"onShow\",\n      onHide: \"onHide\",\n      visibleChange: \"visibleChange\",\n      onResizeInit: \"onResizeInit\",\n      onResizeEnd: \"onResizeEnd\",\n      onDragEnd: \"onDragEnd\",\n      onMaximize: \"onMaximize\"\n    },\n    features: [i0.ɵɵProvidersFeature([DialogStyle]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c9,\n    decls: 1,\n    vars: 1,\n    consts: [[\"container\", \"\"], [\"notHeadless\", \"\"], [\"content\", \"\"], [\"titlebar\", \"\"], [\"icon\", \"\"], [\"footer\", \"\"], [3, \"ngClass\", \"class\", \"ngStyle\", \"style\", 4, \"ngIf\"], [3, \"ngClass\", \"ngStyle\"], [\"pFocusTrap\", \"\", 3, \"class\", \"ngClass\", \"ngStyle\", \"style\", \"pFocusTrapDisabled\", 4, \"ngIf\"], [\"pFocusTrap\", \"\", 3, \"ngClass\", \"ngStyle\", \"pFocusTrapDisabled\"], [4, \"ngIf\", \"ngIfElse\"], [4, \"ngTemplateOutlet\"], [\"style\", \"z-index: 90;\", 3, \"ngClass\", \"mousedown\", 4, \"ngIf\"], [3, \"ngClass\", \"mousedown\", 4, \"ngIf\"], [3, \"ngClass\", 4, \"ngIf\"], [2, \"z-index\", \"90\", 3, \"mousedown\", \"ngClass\"], [3, \"mousedown\", \"ngClass\"], [3, \"id\", \"ngClass\", 4, \"ngIf\"], [3, \"ngClass\"], [3, \"styleClass\", \"tabindex\", \"ariaLabel\", \"buttonProps\", \"onClick\", \"keydown.enter\", 4, \"ngIf\"], [3, \"styleClass\", \"ariaLabel\", \"tabindex\", \"buttonProps\", \"onClick\", \"keydown.enter\", 4, \"ngIf\"], [3, \"id\", \"ngClass\"], [3, \"onClick\", \"keydown.enter\", \"styleClass\", \"tabindex\", \"ariaLabel\", \"buttonProps\"], [4, \"ngIf\"], [3, \"onClick\", \"keydown.enter\", \"styleClass\", \"ariaLabel\", \"tabindex\", \"buttonProps\"]],\n    template: function Dialog_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c8);\n        i0.ɵɵtemplate(0, Dialog_div_0_Template, 2, 11, \"div\", 6);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.maskVisible);\n      }\n    },\n    dependencies: [CommonModule, i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, Button, FocusTrap, TimesIcon, WindowMaximizeIcon, WindowMinimizeIcon, SharedModule],\n    encapsulation: 2,\n    data: {\n      animation: [trigger('animation', [transition('void => visible', [useAnimation(showAnimation)]), transition('visible => void', [useAnimation(hideAnimation)])])]\n    },\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Dialog, [{\n    type: Component,\n    args: [{\n      selector: 'p-dialog',\n      standalone: true,\n      imports: [CommonModule, Button, FocusTrap, TimesIcon, WindowMaximizeIcon, WindowMinimizeIcon, SharedModule],\n      template: `\n        <div\n            *ngIf=\"maskVisible\"\n            [ngClass]=\"maskClass\"\n            [class]=\"maskStyleClass\"\n            [ngStyle]=\"{\n                position: 'fixed',\n                height: '100%',\n                width: '100%',\n                left: 0,\n                top: 0,\n                display: 'flex',\n                'justify-content': position === 'left' || position === 'topleft' || position === 'bottomleft' ? 'flex-start' : position === 'right' || position === 'topright' || position === 'bottomright' ? 'flex-end' : 'center',\n                'align-items': position === 'top' || position === 'topleft' || position === 'topright' ? 'flex-start' : position === 'bottom' || position === 'bottomleft' || position === 'bottomright' ? 'flex-end' : 'center',\n                'pointer-events': modal ? 'auto' : 'none'\n            }\"\n            [style]=\"maskStyle\"\n        >\n            <div\n                *ngIf=\"visible\"\n                #container\n                [class]=\"styleClass\"\n                [ngClass]=\"{ 'p-dialog p-component': true, 'p-dialog-maximized': maximizable && maximized }\"\n                [ngStyle]=\"{ display: 'flex', 'flex-direction': 'column', 'pointer-events': 'auto' }\"\n                [style]=\"style\"\n                pFocusTrap\n                [pFocusTrapDisabled]=\"focusTrap === false\"\n                [@animation]=\"{\n                    value: 'visible',\n                    params: { transform: transformOptions, transition: transitionOptions }\n                }\"\n                (@animation.start)=\"onAnimationStart($event)\"\n                (@animation.done)=\"onAnimationEnd($event)\"\n                [attr.role]=\"role\"\n                [attr.aria-labelledby]=\"ariaLabelledBy\"\n                [attr.aria-modal]=\"true\"\n            >\n                <ng-container *ngIf=\"_headlessTemplate || headlessTemplate || headlessT; else notHeadless\">\n                    <ng-container *ngTemplateOutlet=\"_headlessTemplate || headlessTemplate || headlessT\"></ng-container>\n                </ng-container>\n\n                <ng-template #notHeadless>\n                    <div *ngIf=\"resizable\" [ngClass]=\"cx('resizeHandle')\" style=\"z-index: 90;\" (mousedown)=\"initResize($event)\"></div>\n                    <div #titlebar [ngClass]=\"cx('header')\" (mousedown)=\"initDrag($event)\" *ngIf=\"showHeader\">\n                        <span [id]=\"ariaLabelledBy\" [ngClass]=\"cx('title')\" *ngIf=\"!_headerTemplate && !headerTemplate && !headerT\">{{ header }}</span>\n                        <ng-container *ngTemplateOutlet=\"_headerTemplate || headerTemplate || headerT\"></ng-container>\n                        <div [ngClass]=\"cx('headerActions')\">\n                            <p-button *ngIf=\"maximizable\" [styleClass]=\"cx('pcMaximizeButton')\" (onClick)=\"maximize()\" (keydown.enter)=\"maximize()\" [tabindex]=\"maximizable ? '0' : '-1'\" [ariaLabel]=\"maximizeLabel\" [buttonProps]=\"maximizeButtonProps\">\n                                <span *ngIf=\"maximizeIcon && !_maximizeiconTemplate && !_minimizeiconTemplate\" [ngClass]=\"maximized ? minimizeIcon : maximizeIcon\"></span>\n                                <ng-container *ngIf=\"!maximizeIcon && !maximizeButtonProps?.icon\">\n                                    <WindowMaximizeIcon *ngIf=\"!maximized && !_maximizeiconTemplate && !maximizeIconTemplate && !maximizeIconT\" />\n                                    <WindowMinimizeIcon *ngIf=\"maximized && !_minimizeiconTemplate && !minimizeIconTemplate && !minimizeIconT\" />\n                                </ng-container>\n                                <ng-container *ngIf=\"!maximized\">\n                                    <ng-template *ngTemplateOutlet=\"_maximizeiconTemplate || maximizeIconTemplate || maximizeIconT\"></ng-template>\n                                </ng-container>\n                                <ng-container *ngIf=\"maximized\">\n                                    <ng-template *ngTemplateOutlet=\"_minimizeiconTemplate || minimizeIconTemplate || minimizeIconT\"></ng-template>\n                                </ng-container>\n                            </p-button>\n                            <p-button *ngIf=\"closable\" [styleClass]=\"cx('pcCloseButton')\" [ariaLabel]=\"closeAriaLabel\" (onClick)=\"close($event)\" (keydown.enter)=\"close($event)\" [tabindex]=\"closeTabindex\" [buttonProps]=\"closeButtonProps\">\n                                <ng-template #icon>\n                                    <ng-container *ngIf=\"!_closeiconTemplate && !closeIconTemplate && !closeIconT && !closeButtonProps?.icon\">\n                                        <span *ngIf=\"closeIcon\" [ngClass]=\"closeIcon\"></span>\n                                        <TimesIcon *ngIf=\"!closeIcon\" />\n                                    </ng-container>\n                                    <span *ngIf=\"_closeiconTemplate || closeIconTemplate || closeIconT\">\n                                        <ng-template *ngTemplateOutlet=\"_closeiconTemplate || closeIconTemplate || closeIconT\"></ng-template>\n                                    </span>\n                                </ng-template>\n                            </p-button>\n                        </div>\n                    </div>\n                    <div #content [ngClass]=\"cx('content')\" [class]=\"contentStyleClass\" [ngStyle]=\"contentStyle\" [attr.data-pc-section]=\"'content'\">\n                        <ng-content></ng-content>\n                        <ng-container *ngTemplateOutlet=\"_contentTemplate || contentTemplate || contentT\"></ng-container>\n                    </div>\n                    <div #footer [ngClass]=\"cx('footer')\" *ngIf=\"_footerTemplate || footerTemplate || footerT\">\n                        <ng-content select=\"p-footer\"></ng-content>\n                        <ng-container *ngTemplateOutlet=\"_footerTemplate || footerTemplate || footerT\"></ng-container>\n                    </div>\n                </ng-template>\n            </div>\n        </div>\n    `,\n      animations: [trigger('animation', [transition('void => visible', [useAnimation(showAnimation)]), transition('visible => void', [useAnimation(hideAnimation)])])],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [DialogStyle]\n    }]\n  }], null, {\n    header: [{\n      type: Input\n    }],\n    draggable: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    resizable: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    positionLeft: [{\n      type: Input\n    }],\n    positionTop: [{\n      type: Input\n    }],\n    contentStyle: [{\n      type: Input\n    }],\n    contentStyleClass: [{\n      type: Input\n    }],\n    modal: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    closeOnEscape: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    dismissableMask: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    rtl: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    closable: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    responsive: [{\n      type: Input\n    }],\n    appendTo: [{\n      type: Input\n    }],\n    breakpoints: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    maskStyleClass: [{\n      type: Input\n    }],\n    maskStyle: [{\n      type: Input\n    }],\n    showHeader: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    breakpoint: [{\n      type: Input\n    }],\n    blockScroll: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    autoZIndex: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    baseZIndex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    minX: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    minY: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    focusOnShow: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    maximizable: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    keepInViewport: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    focusTrap: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    transitionOptions: [{\n      type: Input\n    }],\n    closeIcon: [{\n      type: Input\n    }],\n    closeAriaLabel: [{\n      type: Input\n    }],\n    closeTabindex: [{\n      type: Input\n    }],\n    minimizeIcon: [{\n      type: Input\n    }],\n    maximizeIcon: [{\n      type: Input\n    }],\n    closeButtonProps: [{\n      type: Input\n    }],\n    maximizeButtonProps: [{\n      type: Input\n    }],\n    visible: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    position: [{\n      type: Input\n    }],\n    role: [{\n      type: Input\n    }],\n    onShow: [{\n      type: Output\n    }],\n    onHide: [{\n      type: Output\n    }],\n    visibleChange: [{\n      type: Output\n    }],\n    onResizeInit: [{\n      type: Output\n    }],\n    onResizeEnd: [{\n      type: Output\n    }],\n    onDragEnd: [{\n      type: Output\n    }],\n    onMaximize: [{\n      type: Output\n    }],\n    headerViewChild: [{\n      type: ViewChild,\n      args: ['titlebar']\n    }],\n    contentViewChild: [{\n      type: ViewChild,\n      args: ['content']\n    }],\n    footerViewChild: [{\n      type: ViewChild,\n      args: ['footer']\n    }],\n    headerTemplate: [{\n      type: Input,\n      args: ['content']\n    }],\n    contentTemplate: [{\n      type: Input\n    }],\n    footerTemplate: [{\n      type: Input\n    }],\n    closeIconTemplate: [{\n      type: Input\n    }],\n    maximizeIconTemplate: [{\n      type: Input\n    }],\n    minimizeIconTemplate: [{\n      type: Input\n    }],\n    headlessTemplate: [{\n      type: Input\n    }],\n    _headerTemplate: [{\n      type: ContentChild,\n      args: ['header', {\n        descendants: false\n      }]\n    }],\n    _contentTemplate: [{\n      type: ContentChild,\n      args: ['content', {\n        descendants: false\n      }]\n    }],\n    _footerTemplate: [{\n      type: ContentChild,\n      args: ['footer', {\n        descendants: false\n      }]\n    }],\n    _closeiconTemplate: [{\n      type: ContentChild,\n      args: ['closeicon', {\n        descendants: false\n      }]\n    }],\n    _maximizeiconTemplate: [{\n      type: ContentChild,\n      args: ['maximizeicon', {\n        descendants: false\n      }]\n    }],\n    _minimizeiconTemplate: [{\n      type: ContentChild,\n      args: ['minimizeicon', {\n        descendants: false\n      }]\n    }],\n    _headlessTemplate: [{\n      type: ContentChild,\n      args: ['headless', {\n        descendants: false\n      }]\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }]\n  });\n})();\nclass DialogModule {\n  static ɵfac = function DialogModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DialogModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: DialogModule,\n    imports: [Dialog, SharedModule],\n    exports: [Dialog, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [Dialog, SharedModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DialogModule, [{\n    type: NgModule,\n    args: [{\n      imports: [Dialog, SharedModule],\n      exports: [Dialog, SharedModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Dialog, DialogClasses, DialogModule, DialogStyle };", "map": {"version": 3, "names": ["animation", "style", "animate", "trigger", "transition", "useAnimation", "i1", "isPlatformBrowser", "CommonModule", "i0", "Injectable", "EventEmitter", "inject", "NgZone", "booleanAttribute", "numberAttribute", "ContentChildren", "ContentChild", "Input", "ViewChild", "Output", "ViewEncapsulation", "ChangeDetectionStrategy", "Component", "NgModule", "uuid", "blockBodyScroll", "unblockBodyScroll", "setAttribute", "hasClass", "addClass", "getOuterWidth", "getOuterHeight", "getViewport", "removeClass", "append<PERSON><PERSON><PERSON>", "Translation<PERSON>eys", "SharedModule", "PrimeTemplate", "BaseComponent", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "FocusTrap", "TimesIcon", "WindowMaximizeIcon", "WindowMinimizeIcon", "ZIndexUtils", "BaseStyle", "_c0", "_c1", "_c2", "_c3", "_c4", "_c5", "_c6", "_c7", "_c8", "_c9", "_c10", "a0", "a1", "a2", "position", "height", "width", "left", "top", "display", "_c11", "_c12", "_c13", "transform", "_c14", "value", "params", "Dialog_div_0_div_1_ng_container_2_ng_container_1_Template", "rf", "ctx", "ɵɵelementContainer", "Dialog_div_0_div_1_ng_container_2_Template", "ɵɵelementContainerStart", "ɵɵtemplate", "ɵɵelementContainerEnd", "ctx_r1", "ɵɵnextContext", "ɵɵadvance", "ɵɵproperty", "_headlessTemplate", "headlessTemplate", "headlessT", "Dialog_div_0_div_1_ng_template_3_div_0_Template", "_r3", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "Dialog_div_0_div_1_ng_template_3_div_0_Template_div_mousedown_0_listener", "$event", "ɵɵrestoreView", "ɵɵresetView", "initResize", "ɵɵelementEnd", "cx", "Dialog_div_0_div_1_ng_template_3_div_1_span_2_Template", "ɵɵtext", "ariaLabelledBy", "ɵɵtextInterpolate", "header", "Dialog_div_0_div_1_ng_template_3_div_1_ng_container_3_Template", "Dialog_div_0_div_1_ng_template_3_div_1_p_button_5_span_1_Template", "ɵɵelement", "maximized", "minimizeIcon", "maximizeIcon", "Dialog_div_0_div_1_ng_template_3_div_1_p_button_5_ng_container_2_WindowMaximizeIcon_1_Template", "Dialog_div_0_div_1_ng_template_3_div_1_p_button_5_ng_container_2_WindowMinimizeIcon_2_Template", "Dialog_div_0_div_1_ng_template_3_div_1_p_button_5_ng_container_2_Template", "_maximizeiconTemplate", "maximizeIconTemplate", "maximizeIconT", "_minimizeiconTemplate", "minimizeIconTemplate", "minimizeIconT", "Dialog_div_0_div_1_ng_template_3_div_1_p_button_5_ng_container_3_1_ng_template_0_Template", "Dialog_div_0_div_1_ng_template_3_div_1_p_button_5_ng_container_3_1_Template", "Dialog_div_0_div_1_ng_template_3_div_1_p_button_5_ng_container_3_Template", "Dialog_div_0_div_1_ng_template_3_div_1_p_button_5_ng_container_4_1_ng_template_0_Template", "Dialog_div_0_div_1_ng_template_3_div_1_p_button_5_ng_container_4_1_Template", "Dialog_div_0_div_1_ng_template_3_div_1_p_button_5_ng_container_4_Template", "Dialog_div_0_div_1_ng_template_3_div_1_p_button_5_Template", "_r5", "Dialog_div_0_div_1_ng_template_3_div_1_p_button_5_Template_p_button_onClick_0_listener", "maximize", "Dialog_div_0_div_1_ng_template_3_div_1_p_button_5_Template_p_button_keydown_enter_0_listener", "maximizable", "maximizeLabel", "maximizeButtonProps", "icon", "Dialog_div_0_div_1_ng_template_3_div_1_p_button_6_ng_template_1_ng_container_0_span_1_Template", "closeIcon", "Dialog_div_0_div_1_ng_template_3_div_1_p_button_6_ng_template_1_ng_container_0_TimesIcon_2_Template", "Dialog_div_0_div_1_ng_template_3_div_1_p_button_6_ng_template_1_ng_container_0_Template", "Dialog_div_0_div_1_ng_template_3_div_1_p_button_6_ng_template_1_span_1_1_ng_template_0_Template", "Dialog_div_0_div_1_ng_template_3_div_1_p_button_6_ng_template_1_span_1_1_Template", "Dialog_div_0_div_1_ng_template_3_div_1_p_button_6_ng_template_1_span_1_Template", "_closeiconTemplate", "closeIconTemplate", "closeIconT", "Dialog_div_0_div_1_ng_template_3_div_1_p_button_6_ng_template_1_Template", "closeButtonProps", "Dialog_div_0_div_1_ng_template_3_div_1_p_button_6_Template", "_r6", "Dialog_div_0_div_1_ng_template_3_div_1_p_button_6_Template_p_button_onClick_0_listener", "close", "Dialog_div_0_div_1_ng_template_3_div_1_p_button_6_Template_p_button_keydown_enter_0_listener", "ɵɵtemplateRefExtractor", "closeAriaLabel", "closeTabindex", "Dialog_div_0_div_1_ng_template_3_div_1_Template", "_r4", "Dialog_div_0_div_1_ng_template_3_div_1_Template_div_mousedown_0_listener", "initDrag", "_headerTemplate", "headerTemplate", "headerT", "closable", "Dialog_div_0_div_1_ng_template_3_ng_container_5_Template", "Dialog_div_0_div_1_ng_template_3_div_6_ng_container_3_Template", "Dialog_div_0_div_1_ng_template_3_div_6_Template", "ɵɵprojection", "_footerTemplate", "footerTemplate", "footerT", "Dialog_div_0_div_1_ng_template_3_Template", "resizable", "showHeader", "ɵɵclassMap", "contentStyleClass", "contentStyle", "ɵɵattribute", "_contentTemplate", "contentTemplate", "contentT", "Dialog_div_0_div_1_Template", "_r1", "Dialog_div_0_div_1_Template_div_animation_animation_start_0_listener", "onAnimationStart", "Dialog_div_0_div_1_Template_div_animation_animation_done_0_listener", "onAnimationEnd", "notHeadless_r7", "ɵɵreference", "ɵɵstyleMap", "styleClass", "ɵɵpureFunction1", "ɵɵpureFunction0", "focusTrap", "ɵɵpureFunction2", "transformOptions", "transitionOptions", "role", "Dialog_div_0_Template", "maskStyle", "maskStyleClass", "maskClass", "ɵɵpureFunction3", "modal", "visible", "theme", "dt", "inlineStyles", "mask", "instance", "justifyContent", "alignItems", "pointerEvents", "root", "flexDirection", "classes", "positions", "pos", "find", "item", "title", "resizeHandle", "headerActions", "pcMaximizeButton", "pc<PERSON>lose<PERSON><PERSON>on", "content", "footer", "DialogStyle", "name", "ɵfac", "ɵDialogStyle_BaseFactory", "DialogStyle_Factory", "__ngFactoryType__", "ɵɵgetInheritedFactory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "DialogClasses", "showAnimation", "opacity", "hideAnimation", "Dialog", "draggable", "positionLeft", "_positionLeft", "console", "log", "positionTop", "_positionTop", "closeOnEscape", "dismissableMask", "rtl", "responsive", "_responsive", "appendTo", "breakpoints", "breakpoint", "_breakpoint", "blockScroll", "autoZIndex", "baseZIndex", "minX", "minY", "focusOnShow", "keepInViewport", "severity", "text", "rounded", "_visible", "maskVisible", "_style", "originalStyle", "_position", "onShow", "onHide", "visibleChange", "onResizeInit", "onResizeEnd", "onDragEnd", "onMaximize", "headerViewChild", "contentViewChild", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "container", "wrapper", "dragging", "getAriaLabelledBy", "documentDragListener", "documentDragEndListener", "resizing", "documentResizeListener", "documentResizeEndListener", "documentEscapeListener", "maskClickListener", "lastPageX", "lastPageY", "preventVisibleChangePropagation", "preMaximizeContentHeight", "preMaximizeContainerWidth", "preMaximizeContainerHeight", "preMaximizePageX", "preMaximizePageY", "id", "styleElement", "window", "_componentStyle", "config", "getTranslation", "ARIA", "zone", "ngOnInit", "createStyle", "templates", "ngAfterContentInit", "for<PERSON>ach", "getType", "template", "parseDurationToMilliseconds", "durationString", "transitionTimeRegex", "totalMilliseconds", "match", "exec", "parseFloat", "unit", "undefined", "_focus", "focusParentElement", "timeoutDuration", "_focusableElements", "getFocusableElements", "length", "runOutsideAngular", "setTimeout", "focus", "focused", "nativeElement", "event", "emit", "preventDefault", "enableModality", "renderer", "listen", "isSameNode", "target", "disableModality", "unbindMaskClickListener", "scrollBlockers", "document", "querySelectorAll", "cd", "destroyed", "detectChanges", "moveOnTop", "set", "zIndex", "String", "parseInt", "platformId", "createElement", "head", "innerHTML", "setProperty", "csp", "nonce", "parentElement", "pageX", "pageY", "margin", "body", "onDrag", "containerWidth", "containerHeight", "deltaX", "deltaY", "offset", "getBoundingClientRect", "containerComputedStyle", "getComputedStyle", "leftMargin", "marginLeft", "<PERSON><PERSON><PERSON><PERSON>", "marginTop", "leftPos", "topPos", "viewport", "endDrag", "resetPosition", "center", "onResize", "contentHeight", "newWidth", "newHeight", "min<PERSON><PERSON><PERSON>", "minHeight", "hasBeenDragged", "resizeEnd", "bindGlobalListeners", "bindDocumentDragListener", "bindDocumentDragEndListener", "bindDocumentResizeListeners", "bindDocumentEscapeListener", "unbindGlobalListeners", "unbindDocumentDragListener", "unbindDocumentDragEndListener", "unbindDocumentResizeListeners", "unbindDocumentEscapeListener", "defaultView", "bind", "documentTarget", "el", "ownerDocument", "key", "append<PERSON><PERSON><PERSON>", "restoreAppend", "toState", "element", "onContainerDestroy", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "removeProperty", "clear", "destroyStyle", "<PERSON><PERSON><PERSON><PERSON>", "ngOnDestroy", "ɵDialog_BaseFactory", "Dialog_Factory", "ɵcmp", "ɵɵdefineComponent", "selectors", "contentQueries", "Dialog_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "viewQuery", "Dialog_Query", "ɵɵviewQuery", "inputs", "outputs", "features", "ɵɵProvidersFeature", "ɵɵInheritDefinitionFeature", "ngContentSelectors", "decls", "vars", "consts", "Dialog_Template", "ɵɵprojectionDef", "dependencies", "Ng<PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "NgStyle", "encapsulation", "data", "changeDetection", "args", "selector", "standalone", "imports", "animations", "OnPush", "None", "providers", "descendants", "DialogModule", "DialogModule_Factory", "ɵmod", "ɵɵdefineNgModule", "exports", "ɵinj", "ɵɵdefineInjector"], "sources": ["C:/projetos/scope-ai/frontend/node_modules/primeng/fesm2022/primeng-dialog.mjs"], "sourcesContent": ["import { animation, style, animate, trigger, transition, useAnimation } from '@angular/animations';\nimport * as i1 from '@angular/common';\nimport { isPlatformBrowser, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, inject, NgZone, booleanAttribute, numberAttribute, ContentChildren, ContentChild, Input, ViewChild, Output, ViewEncapsulation, ChangeDetectionStrategy, Component, NgModule } from '@angular/core';\nimport { uuid, blockBodyScroll, unblockBodyScroll, setAttribute, hasClass, addClass, getOuterWidth, getOuterHeight, getViewport, removeClass, appendChild } from '@primeuix/utils';\nimport { TranslationKeys, SharedModule, PrimeTemplate } from 'primeng/api';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { Button } from 'primeng/button';\nimport { <PERSON><PERSON><PERSON><PERSON> } from 'primeng/dom';\nimport { FocusTrap } from 'primeng/focustrap';\nimport { TimesIcon, WindowMaximizeIcon, WindowMinimizeIcon } from 'primeng/icons';\nimport { ZIndexUtils } from 'primeng/utils';\nimport { BaseStyle } from 'primeng/base';\n\nconst theme = ({ dt }) => `\n.p-dialog {\n    max-height: 90%;\n    transform: scale(1);\n    border-radius: ${dt('dialog.border.radius')};\n    box-shadow: ${dt('dialog.shadow')};\n    background: ${dt('dialog.background')};\n    border: 1px solid ${dt('dialog.border.color')};\n    color: ${dt('dialog.color')};\n    display: flex;\n    flex-direction: column;\n    pointer-events: auto\n}\n\n.p-dialog-content {\n    overflow-y: auto;\n    padding: ${dt('dialog.content.padding')};\n    flex-grow: 1;\n}\n\n.p-dialog-header {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    flex-shrink: 0;\n    padding: ${dt('dialog.header.padding')};\n}\n\n.p-dialog-title {\n    font-weight: ${dt('dialog.title.font.weight')};\n    font-size: ${dt('dialog.title.font.size')};\n}\n\n.p-dialog-footer {\n    flex-shrink: 0;\n    padding: ${dt('dialog.footer.padding')};\n    display: flex;\n    justify-content: flex-end;\n    gap: ${dt('dialog.footer.gap')};\n}\n\n.p-dialog-header-actions {\n    display: flex;\n    align-items: center;\n    gap: ${dt('dialog.header.gap')};\n}\n\n.p-dialog-enter-active {\n    transition: all 150ms cubic-bezier(0, 0, 0.2, 1);\n}\n\n.p-dialog-leave-active {\n    transition: all 150ms cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.p-dialog-enter-from,\n.p-dialog-leave-to {\n    opacity: 0;\n    transform: scale(0.7);\n}\n\n.p-dialog-top .p-dialog,\n.p-dialog-bottom .p-dialog,\n.p-dialog-left .p-dialog,\n.p-dialog-right .p-dialog,\n.p-dialog-topleft .p-dialog,\n.p-dialog-topright .p-dialog,\n.p-dialog-bottomleft .p-dialog,\n.p-dialog-bottomright .p-dialog {\n    margin: 0.75rem;\n    transform: translate3d(0px, 0px, 0px);\n}\n\n.p-dialog-top .p-dialog-enter-active,\n.p-dialog-top .p-dialog-leave-active,\n.p-dialog-bottom .p-dialog-enter-active,\n.p-dialog-bottom .p-dialog-leave-active,\n.p-dialog-left .p-dialog-enter-active,\n.p-dialog-left .p-dialog-leave-active,\n.p-dialog-right .p-dialog-enter-active,\n.p-dialog-right .p-dialog-leave-active,\n.p-dialog-topleft .p-dialog-enter-active,\n.p-dialog-topleft .p-dialog-leave-active,\n.p-dialog-topright .p-dialog-enter-active,\n.p-dialog-topright .p-dialog-leave-active,\n.p-dialog-bottomleft .p-dialog-enter-active,\n.p-dialog-bottomleft .p-dialog-leave-active,\n.p-dialog-bottomright .p-dialog-enter-active,\n.p-dialog-bottomright .p-dialog-leave-active {\n    transition: all 0.3s ease-out;\n}\n\n.p-dialog-top .p-dialog-enter-from,\n.p-dialog-top .p-dialog-leave-to {\n    transform: translate3d(0px, -100%, 0px);\n}\n\n.p-dialog-bottom .p-dialog-enter-from,\n.p-dialog-bottom .p-dialog-leave-to {\n    transform: translate3d(0px, 100%, 0px);\n}\n\n.p-dialog-left .p-dialog-enter-from,\n.p-dialog-left .p-dialog-leave-to,\n.p-dialog-topleft .p-dialog-enter-from,\n.p-dialog-topleft .p-dialog-leave-to,\n.p-dialog-bottomleft .p-dialog-enter-from,\n.p-dialog-bottomleft .p-dialog-leave-to {\n    transform: translate3d(-100%, 0px, 0px);\n}\n\n.p-dialog-right .p-dialog-enter-from,\n.p-dialog-right .p-dialog-leave-to,\n.p-dialog-topright .p-dialog-enter-from,\n.p-dialog-topright .p-dialog-leave-to,\n.p-dialog-bottomright .p-dialog-enter-from,\n.p-dialog-bottomright .p-dialog-leave-to {\n    transform: translate3d(100%, 0px, 0px);\n}\n\n.p-dialog-left:dir(rtl) .p-dialog-enter-from,\n.p-dialog-left:dir(rtl) .p-dialog-leave-to,\n.p-dialog-topleft:dir(rtl) .p-dialog-enter-from,\n.p-dialog-topleft:dir(rtl) .p-dialog-leave-to,\n.p-dialog-bottomleft:dir(rtl) .p-dialog-enter-from,\n.p-dialog-bottomleft:dir(rtl) .p-dialog-leave-to {\n    transform: translate3d(100%, 0px, 0px);\n}\n\n.p-dialog-right:dir(rtl) .p-dialog-enter-from,\n.p-dialog-right:dir(rtl) .p-dialog-leave-to,\n.p-dialog-topright:dir(rtl) .p-dialog-enter-from,\n.p-dialog-topright:dir(rtl) .p-dialog-leave-to,\n.p-dialog-bottomright:dir(rtl) .p-dialog-enter-from,\n.p-dialog-bottomright:dir(rtl) .p-dialog-leave-to {\n    transform: translate3d(-100%, 0px, 0px);\n}\n\n.p-dialog-maximized {\n    width: 100vw !important;\n    height: 100vh !important;\n    top: 0px !important;\n    left: 0px !important;\n    max-height: 100%;\n    height: 100%;\n    border-radius: 0;\n}\n\n.p-dialog-maximized .p-dialog-content {\n    flex-grow: 1;\n}\n\n.p-overlay-mask:dir(rtl) {\n    flex-direction: row-reverse;\n}\n\n/* For PrimeNG */\n\n.p-dialog .p-resizable-handle {\n    position: absolute;\n    font-size: 0.1px;\n    display: block;\n    cursor: se-resize;\n    width: 12px;\n    height: 12px;\n    right: 1px;\n    bottom: 1px;\n}\n\n.p-confirm-dialog .p-dialog-content {\n    display: flex;\n    align-items: center;\n}\n`;\n/* Position */\nconst inlineStyles = {\n    mask: ({ instance }) => ({\n        position: 'fixed',\n        height: '100%',\n        width: '100%',\n        left: 0,\n        top: 0,\n        display: 'flex',\n        justifyContent: instance.position === 'left' || instance.position === 'topleft' || instance.position === 'bottomleft'\n            ? 'flex-start'\n            : instance.position === 'right' || instance.position === 'topright' || instance.position === 'bottomright'\n                ? 'flex-end'\n                : 'center',\n        alignItems: instance.position === 'top' || instance.position === 'topleft' || instance.position === 'topright'\n            ? 'flex-start'\n            : instance.position === 'bottom' || instance.position === 'bottomleft' || instance.position === 'bottomright'\n                ? 'flex-end'\n                : 'center',\n        pointerEvents: instance.modal ? 'auto' : 'none'\n    }),\n    root: {\n        display: 'flex',\n        flexDirection: 'column',\n        pointerEvents: 'auto'\n    }\n};\nconst classes = {\n    mask: ({ instance }) => {\n        const positions = ['left', 'right', 'top', 'topleft', 'topright', 'bottom', 'bottomleft', 'bottomright'];\n        const pos = positions.find((item) => item === instance.position);\n        return {\n            'p-dialog-mask': true,\n            'p-overlay-mask p-overlay-mask-enter': instance.modal,\n            [`p-dialog-${pos}`]: pos\n        };\n    },\n    root: ({ instance }) => ({ 'p-dialog p-component': true, 'p-dialog-maximized': instance.maximizable && instance.maximized }),\n    header: 'p-dialog-header',\n    title: 'p-dialog-title',\n    resizeHandle: 'p-resizable-handle',\n    headerActions: 'p-dialog-header-actions',\n    pcMaximizeButton: 'p-dialog-maximize-button',\n    pcCloseButton: 'p-dialog-close-button',\n    content: 'p-dialog-content',\n    footer: 'p-dialog-footer'\n};\nclass DialogStyle extends BaseStyle {\n    name = 'dialog';\n    theme = theme;\n    classes = classes;\n    inlineStyles = inlineStyles;\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: DialogStyle, deps: null, target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: DialogStyle });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: DialogStyle, decorators: [{\n            type: Injectable\n        }] });\n/**\n *\n * Dialog is a container to display content in an overlay window.\n *\n * [Live Demo](https://www.primeng.org/dialog)\n *\n * @module dialogstyle\n *\n */\nvar DialogClasses;\n(function (DialogClasses) {\n    /**\n     * Class name of the mask element\n     */\n    DialogClasses[\"mask\"] = \"p-dialog-mask\";\n    /**\n     * Class name of the root element\n     */\n    DialogClasses[\"root\"] = \"p-dialog\";\n    /**\n     * Class name of the header element\n     */\n    DialogClasses[\"header\"] = \"p-dialog-header\";\n    /**\n     * Class name of the title element\n     */\n    DialogClasses[\"title\"] = \"p-dialog-title\";\n    /**\n     * Class name of the header actions element\n     */\n    DialogClasses[\"headerActions\"] = \"p-dialog-header-actions\";\n    /**\n     * Class name of the maximize button element\n     */\n    DialogClasses[\"pcMaximizeButton\"] = \"p-dialog-maximize-button\";\n    /**\n     * Class name of the close button element\n     */\n    DialogClasses[\"pcCloseButton\"] = \"p-dialog-close-button\";\n    /**\n     * Class name of the content element\n     */\n    DialogClasses[\"content\"] = \"p-dialog-content\";\n    /**\n     * Class name of the footer element\n     */\n    DialogClasses[\"footer\"] = \"p-dialog-footer\";\n})(DialogClasses || (DialogClasses = {}));\n\nconst showAnimation = animation([style({ transform: '{{transform}}', opacity: 0 }), animate('{{transition}}')]);\nconst hideAnimation = animation([animate('{{transition}}', style({ transform: '{{transform}}', opacity: 0 }))]);\n/**\n * Dialog is a container to display content in an overlay window.\n * @group Components\n */\nclass Dialog extends BaseComponent {\n    /**\n     * Title text of the dialog.\n     * @group Props\n     */\n    header;\n    /**\n     * Enables dragging to change the position using header.\n     * @group Props\n     */\n    draggable = true;\n    /**\n     * Enables resizing of the content.\n     * @group Props\n     */\n    resizable = true;\n    /**\n     * Defines the left offset of dialog.\n     * @group Props\n     * @deprecated positionLeft property is deprecated.\n     */\n    get positionLeft() {\n        return 0;\n    }\n    set positionLeft(_positionLeft) {\n        console.log('positionLeft property is deprecated.');\n    }\n    /**\n     * Defines the top offset of dialog.\n     * @group Props\n     * @deprecated positionTop property is deprecated.\n     */\n    get positionTop() {\n        return 0;\n    }\n    set positionTop(_positionTop) {\n        console.log('positionTop property is deprecated.');\n    }\n    /**\n     * Style of the content section.\n     * @group Props\n     */\n    contentStyle;\n    /**\n     * Style class of the content.\n     * @group Props\n     */\n    contentStyleClass;\n    /**\n     * Defines if background should be blocked when dialog is displayed.\n     * @group Props\n     */\n    modal = false;\n    /**\n     * Specifies if pressing escape key should hide the dialog.\n     * @group Props\n     */\n    closeOnEscape = true;\n    /**\n     * Specifies if clicking the modal background should hide the dialog.\n     * @group Props\n     */\n    dismissableMask = false;\n    /**\n     * When enabled dialog is displayed in RTL direction.\n     * @group Props\n     */\n    rtl = false;\n    /**\n     * Adds a close icon to the header to hide the dialog.\n     * @group Props\n     */\n    closable = true;\n    /**\n     * Defines if the component is responsive.\n     * @group Props\n     * @deprecated Responsive property is deprecated.\n     */\n    get responsive() {\n        return false;\n    }\n    set responsive(_responsive) {\n        console.log('Responsive property is deprecated.');\n    }\n    /**\n     * Target element to attach the dialog, valid values are \"body\" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n     * @group Props\n     */\n    appendTo;\n    /**\n     * Object literal to define widths per screen size.\n     * @group Props\n     */\n    breakpoints;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Style class of the mask.\n     * @group Props\n     */\n    maskStyleClass;\n    /**\n     * Style of the mask.\n     * @group Props\n     */\n    maskStyle;\n    /**\n     * Whether to show the header or not.\n     * @group Props\n     */\n    showHeader = true;\n    /**\n     * Defines the breakpoint of the component responsive.\n     * @group Props\n     * @deprecated Breakpoint property is not utilized and deprecated. Use breakpoints or CSS media queries instead.\n     */\n    get breakpoint() {\n        return 649;\n    }\n    set breakpoint(_breakpoint) {\n        console.log('Breakpoint property is not utilized and deprecated, use breakpoints or CSS media queries instead.');\n    }\n    /**\n     * Whether background scroll should be blocked when dialog is visible.\n     * @group Props\n     */\n    blockScroll = false;\n    /**\n     * Whether to automatically manage layering.\n     * @group Props\n     */\n    autoZIndex = true;\n    /**\n     * Base zIndex value to use in layering.\n     * @group Props\n     */\n    baseZIndex = 0;\n    /**\n     * Minimum value for the left coordinate of dialog in dragging.\n     * @group Props\n     */\n    minX = 0;\n    /**\n     * Minimum value for the top coordinate of dialog in dragging.\n     * @group Props\n     */\n    minY = 0;\n    /**\n     * When enabled, first focusable element receives focus on show.\n     * @group Props\n     */\n    focusOnShow = true;\n    /**\n     * Whether the dialog can be displayed full screen.\n     * @group Props\n     */\n    maximizable = false;\n    /**\n     * Keeps dialog in the viewport.\n     * @group Props\n     */\n    keepInViewport = true;\n    /**\n     * When enabled, can only focus on elements inside the dialog.\n     * @group Props\n     */\n    focusTrap = true;\n    /**\n     * Transition options of the animation.\n     * @group Props\n     */\n    transitionOptions = '150ms cubic-bezier(0, 0, 0.2, 1)';\n    /**\n     * Name of the close icon.\n     * @group Props\n     */\n    closeIcon;\n    /**\n     * Defines a string that labels the close button for accessibility.\n     * @group Props\n     */\n    closeAriaLabel;\n    /**\n     * Index of the close button in tabbing order.\n     * @group Props\n     */\n    closeTabindex = '0';\n    /**\n     * Name of the minimize icon.\n     * @group Props\n     */\n    minimizeIcon;\n    /**\n     * Name of the maximize icon.\n     * @group Props\n     */\n    maximizeIcon;\n    /**\n     * Used to pass all properties of the ButtonProps to the Button component.\n     * @group Props\n     */\n    closeButtonProps = {\n        severity: 'secondary',\n        text: true,\n        rounded: true\n    };\n    /**\n     * Used to pass all properties of the ButtonProps to the Button component.\n     * @group Props\n     */\n    maximizeButtonProps = {\n        severity: 'secondary',\n        text: true,\n        rounded: true\n    };\n    /**\n     * Specifies the visibility of the dialog.\n     * @group Props\n     */\n    get visible() {\n        return this._visible;\n    }\n    set visible(value) {\n        this._visible = value;\n        if (this._visible && !this.maskVisible) {\n            this.maskVisible = true;\n        }\n    }\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    get style() {\n        return this._style;\n    }\n    set style(value) {\n        if (value) {\n            this._style = { ...value };\n            this.originalStyle = value;\n        }\n    }\n    /**\n     * Position of the dialog.\n     * @group Props\n     */\n    get position() {\n        return this._position;\n    }\n    set position(value) {\n        this._position = value;\n        switch (value) {\n            case 'topleft':\n            case 'bottomleft':\n            case 'left':\n                this.transformOptions = 'translate3d(-100%, 0px, 0px)';\n                break;\n            case 'topright':\n            case 'bottomright':\n            case 'right':\n                this.transformOptions = 'translate3d(100%, 0px, 0px)';\n                break;\n            case 'bottom':\n                this.transformOptions = 'translate3d(0px, 100%, 0px)';\n                break;\n            case 'top':\n                this.transformOptions = 'translate3d(0px, -100%, 0px)';\n                break;\n            default:\n                this.transformOptions = 'scale(0.7)';\n                break;\n        }\n    }\n    /**\n     * Role attribute of html element.\n     * @group Emits\n     */\n    role = 'dialog';\n    /**\n     * Callback to invoke when dialog is shown.\n     * @group Emits\n     */\n    onShow = new EventEmitter();\n    /**\n     * Callback to invoke when dialog is hidden.\n     * @group Emits\n     */\n    onHide = new EventEmitter();\n    /**\n     * This EventEmitter is used to notify changes in the visibility state of a component.\n     * @param {boolean} value - New value.\n     * @group Emits\n     */\n    visibleChange = new EventEmitter();\n    /**\n     * Callback to invoke when dialog resizing is initiated.\n     * @param {MouseEvent} event - Mouse event.\n     * @group Emits\n     */\n    onResizeInit = new EventEmitter();\n    /**\n     * Callback to invoke when dialog resizing is completed.\n     * @param {MouseEvent} event - Mouse event.\n     * @group Emits\n     */\n    onResizeEnd = new EventEmitter();\n    /**\n     * Callback to invoke when dialog dragging is completed.\n     * @param {DragEvent} event - Drag event.\n     * @group Emits\n     */\n    onDragEnd = new EventEmitter();\n    /**\n     * Callback to invoke when dialog maximized or unmaximized.\n     * @group Emits\n     */\n    onMaximize = new EventEmitter();\n    headerViewChild;\n    contentViewChild;\n    footerViewChild;\n    /**\n     * Header template.\n     * @group Props\n     */\n    headerTemplate;\n    /**\n     * Content template.\n     * @group Props\n     */\n    contentTemplate;\n    /**\n     * Footer template.\n     * @group Props\n     */\n    footerTemplate;\n    /**\n     * Close icon template.\n     * @group Props\n     */\n    closeIconTemplate;\n    /**\n     * Maximize icon template.\n     * @group Props\n     */\n    maximizeIconTemplate;\n    /**\n     * Minimize icon template.\n     * @group Props\n     */\n    minimizeIconTemplate;\n    /**\n     * Headless template.\n     * @group Props\n     */\n    headlessTemplate;\n    _headerTemplate;\n    _contentTemplate;\n    _footerTemplate;\n    _closeiconTemplate;\n    _maximizeiconTemplate;\n    _minimizeiconTemplate;\n    _headlessTemplate;\n    _visible = false;\n    maskVisible;\n    container;\n    wrapper;\n    dragging;\n    ariaLabelledBy = this.getAriaLabelledBy();\n    documentDragListener;\n    documentDragEndListener;\n    resizing;\n    documentResizeListener;\n    documentResizeEndListener;\n    documentEscapeListener;\n    maskClickListener;\n    lastPageX;\n    lastPageY;\n    preventVisibleChangePropagation;\n    maximized;\n    preMaximizeContentHeight;\n    preMaximizeContainerWidth;\n    preMaximizeContainerHeight;\n    preMaximizePageX;\n    preMaximizePageY;\n    id = uuid('pn_id_');\n    _style = {};\n    _position = 'center';\n    originalStyle;\n    transformOptions = 'scale(0.7)';\n    styleElement;\n    window;\n    _componentStyle = inject(DialogStyle);\n    headerT;\n    contentT;\n    footerT;\n    closeIconT;\n    maximizeIconT;\n    minimizeIconT;\n    headlessT;\n    get maximizeLabel() {\n        return this.config.getTranslation(TranslationKeys.ARIA)['maximizeLabel'];\n    }\n    zone = inject(NgZone);\n    get maskClass() {\n        const positions = ['left', 'right', 'top', 'topleft', 'topright', 'bottom', 'bottomleft', 'bottomright'];\n        const pos = positions.find((item) => item === this.position);\n        return {\n            'p-dialog-mask': true,\n            'p-overlay-mask p-overlay-mask-enter': this.modal || this.dismissableMask,\n            [`p-dialog-${pos}`]: pos\n        };\n    }\n    ngOnInit() {\n        super.ngOnInit();\n        if (this.breakpoints) {\n            this.createStyle();\n        }\n    }\n    templates;\n    ngAfterContentInit() {\n        this.templates?.forEach((item) => {\n            switch (item.getType()) {\n                case 'header':\n                    this.headerT = item.template;\n                    break;\n                case 'content':\n                    this.contentT = item.template;\n                    break;\n                case 'footer':\n                    this.footerT = item.template;\n                    break;\n                case 'closeicon':\n                    this.closeIconT = item.template;\n                    break;\n                case 'maximizeicon':\n                    this.maximizeIconT = item.template;\n                    break;\n                case 'minimizeicon':\n                    this.minimizeIconT = item.template;\n                    break;\n                case 'headless':\n                    this.headlessT = item.template;\n                    break;\n                default:\n                    this.contentT = item.template;\n                    break;\n            }\n        });\n    }\n    getAriaLabelledBy() {\n        return this.header !== null ? uuid('pn_id_') + '_header' : null;\n    }\n    parseDurationToMilliseconds(durationString) {\n        const transitionTimeRegex = /([\\d\\.]+)(ms|s)\\b/g;\n        let totalMilliseconds = 0;\n        let match;\n        while ((match = transitionTimeRegex.exec(durationString)) !== null) {\n            const value = parseFloat(match[1]);\n            const unit = match[2];\n            if (unit === 'ms') {\n                totalMilliseconds += value;\n            }\n            else if (unit === 's') {\n                totalMilliseconds += value * 1000;\n            }\n        }\n        if (totalMilliseconds === 0) {\n            return undefined;\n        }\n        return totalMilliseconds;\n    }\n    _focus(focusParentElement) {\n        if (focusParentElement) {\n            const timeoutDuration = this.parseDurationToMilliseconds(this.transitionOptions);\n            let _focusableElements = DomHandler.getFocusableElements(focusParentElement);\n            if (_focusableElements && _focusableElements.length > 0) {\n                this.zone.runOutsideAngular(() => {\n                    setTimeout(() => _focusableElements[0].focus(), timeoutDuration || 5);\n                });\n                return true;\n            }\n        }\n        return false;\n    }\n    focus(focusParentElement) {\n        let focused = this._focus(focusParentElement);\n        if (!focused) {\n            focused = this._focus(this.footerViewChild?.nativeElement);\n            if (!focused) {\n                focused = this._focus(this.headerViewChild?.nativeElement);\n                if (!focused) {\n                    this._focus(this.contentViewChild?.nativeElement);\n                }\n            }\n        }\n    }\n    close(event) {\n        this.visibleChange.emit(false);\n        event.preventDefault();\n    }\n    enableModality() {\n        if (this.closable && this.dismissableMask) {\n            this.maskClickListener = this.renderer.listen(this.wrapper, 'mousedown', (event) => {\n                if (this.wrapper && this.wrapper.isSameNode(event.target)) {\n                    this.close(event);\n                }\n            });\n        }\n        if (this.modal) {\n            blockBodyScroll();\n        }\n    }\n    disableModality() {\n        if (this.wrapper) {\n            if (this.dismissableMask) {\n                this.unbindMaskClickListener();\n            }\n            // for nested dialogs w/modal\n            const scrollBlockers = document.querySelectorAll('.p-dialog-mask-scrollblocker');\n            if (this.modal && scrollBlockers && scrollBlockers.length == 1) {\n                unblockBodyScroll();\n            }\n            if (!this.cd.destroyed) {\n                this.cd.detectChanges();\n            }\n        }\n    }\n    maximize() {\n        this.maximized = !this.maximized;\n        if (!this.modal && !this.blockScroll) {\n            if (this.maximized) {\n                blockBodyScroll();\n            }\n            else {\n                unblockBodyScroll();\n            }\n        }\n        this.onMaximize.emit({ maximized: this.maximized });\n    }\n    unbindMaskClickListener() {\n        if (this.maskClickListener) {\n            this.maskClickListener();\n            this.maskClickListener = null;\n        }\n    }\n    moveOnTop() {\n        if (this.autoZIndex) {\n            ZIndexUtils.set('modal', this.container, this.baseZIndex + this.config.zIndex.modal);\n            this.wrapper.style.zIndex = String(parseInt(this.container.style.zIndex, 10) - 1);\n        }\n    }\n    createStyle() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (!this.styleElement) {\n                this.styleElement = this.renderer.createElement('style');\n                this.styleElement.type = 'text/css';\n                this.renderer.appendChild(this.document.head, this.styleElement);\n                let innerHTML = '';\n                for (let breakpoint in this.breakpoints) {\n                    innerHTML += `\n                        @media screen and (max-width: ${breakpoint}) {\n                            .p-dialog[${this.id}]:not(.p-dialog-maximized) {\n                                width: ${this.breakpoints[breakpoint]} !important;\n                            }\n                        }\n                    `;\n                }\n                this.renderer.setProperty(this.styleElement, 'innerHTML', innerHTML);\n                setAttribute(this.styleElement, 'nonce', this.config?.csp()?.nonce);\n            }\n        }\n    }\n    initDrag(event) {\n        if (hasClass(event.target, 'p-dialog-maximize-icon') || hasClass(event.target, 'p-dialog-header-close-icon') || hasClass(event.target.parentElement, 'p-dialog-header-icon')) {\n            return;\n        }\n        if (this.draggable) {\n            this.dragging = true;\n            this.lastPageX = event.pageX;\n            this.lastPageY = event.pageY;\n            this.container.style.margin = '0';\n            addClass(this.document.body, 'p-unselectable-text');\n        }\n    }\n    onDrag(event) {\n        if (this.dragging) {\n            const containerWidth = getOuterWidth(this.container);\n            const containerHeight = getOuterHeight(this.container);\n            const deltaX = event.pageX - this.lastPageX;\n            const deltaY = event.pageY - this.lastPageY;\n            const offset = this.container.getBoundingClientRect();\n            const containerComputedStyle = getComputedStyle(this.container);\n            const leftMargin = parseFloat(containerComputedStyle.marginLeft);\n            const topMargin = parseFloat(containerComputedStyle.marginTop);\n            const leftPos = offset.left + deltaX - leftMargin;\n            const topPos = offset.top + deltaY - topMargin;\n            const viewport = getViewport();\n            this.container.style.position = 'fixed';\n            if (this.keepInViewport) {\n                if (leftPos >= this.minX && leftPos + containerWidth < viewport.width) {\n                    this._style.left = `${leftPos}px`;\n                    this.lastPageX = event.pageX;\n                    this.container.style.left = `${leftPos}px`;\n                }\n                if (topPos >= this.minY && topPos + containerHeight < viewport.height) {\n                    this._style.top = `${topPos}px`;\n                    this.lastPageY = event.pageY;\n                    this.container.style.top = `${topPos}px`;\n                }\n            }\n            else {\n                this.lastPageX = event.pageX;\n                this.container.style.left = `${leftPos}px`;\n                this.lastPageY = event.pageY;\n                this.container.style.top = `${topPos}px`;\n            }\n        }\n    }\n    endDrag(event) {\n        if (this.dragging) {\n            this.dragging = false;\n            removeClass(this.document.body, 'p-unselectable-text');\n            this.cd.detectChanges();\n            this.onDragEnd.emit(event);\n        }\n    }\n    resetPosition() {\n        this.container.style.position = '';\n        this.container.style.left = '';\n        this.container.style.top = '';\n        this.container.style.margin = '';\n    }\n    //backward compatibility\n    center() {\n        this.resetPosition();\n    }\n    initResize(event) {\n        if (this.resizable) {\n            this.resizing = true;\n            this.lastPageX = event.pageX;\n            this.lastPageY = event.pageY;\n            addClass(this.document.body, 'p-unselectable-text');\n            this.onResizeInit.emit(event);\n        }\n    }\n    onResize(event) {\n        if (this.resizing) {\n            let deltaX = event.pageX - this.lastPageX;\n            let deltaY = event.pageY - this.lastPageY;\n            let containerWidth = getOuterWidth(this.container);\n            let containerHeight = getOuterHeight(this.container);\n            let contentHeight = getOuterHeight(this.contentViewChild?.nativeElement);\n            let newWidth = containerWidth + deltaX;\n            let newHeight = containerHeight + deltaY;\n            let minWidth = this.container.style.minWidth;\n            let minHeight = this.container.style.minHeight;\n            let offset = this.container.getBoundingClientRect();\n            let viewport = getViewport();\n            let hasBeenDragged = !parseInt(this.container.style.top) || !parseInt(this.container.style.left);\n            if (hasBeenDragged) {\n                newWidth += deltaX;\n                newHeight += deltaY;\n            }\n            if ((!minWidth || newWidth > parseInt(minWidth)) && offset.left + newWidth < viewport.width) {\n                this._style.width = newWidth + 'px';\n                this.container.style.width = this._style.width;\n            }\n            if ((!minHeight || newHeight > parseInt(minHeight)) && offset.top + newHeight < viewport.height) {\n                this.contentViewChild.nativeElement.style.height = contentHeight + newHeight - containerHeight + 'px';\n                if (this._style.height) {\n                    this._style.height = newHeight + 'px';\n                    this.container.style.height = this._style.height;\n                }\n            }\n            this.lastPageX = event.pageX;\n            this.lastPageY = event.pageY;\n        }\n    }\n    resizeEnd(event) {\n        if (this.resizing) {\n            this.resizing = false;\n            removeClass(this.document.body, 'p-unselectable-text');\n            this.onResizeEnd.emit(event);\n        }\n    }\n    bindGlobalListeners() {\n        if (this.draggable) {\n            this.bindDocumentDragListener();\n            this.bindDocumentDragEndListener();\n        }\n        if (this.resizable) {\n            this.bindDocumentResizeListeners();\n        }\n        if (this.closeOnEscape && this.closable) {\n            this.bindDocumentEscapeListener();\n        }\n    }\n    unbindGlobalListeners() {\n        this.unbindDocumentDragListener();\n        this.unbindDocumentDragEndListener();\n        this.unbindDocumentResizeListeners();\n        this.unbindDocumentEscapeListener();\n    }\n    bindDocumentDragListener() {\n        if (!this.documentDragListener) {\n            this.zone.runOutsideAngular(() => {\n                this.documentDragListener = this.renderer.listen(this.document.defaultView, 'mousemove', this.onDrag.bind(this));\n            });\n        }\n    }\n    unbindDocumentDragListener() {\n        if (this.documentDragListener) {\n            this.documentDragListener();\n            this.documentDragListener = null;\n        }\n    }\n    bindDocumentDragEndListener() {\n        if (!this.documentDragEndListener) {\n            this.zone.runOutsideAngular(() => {\n                this.documentDragEndListener = this.renderer.listen(this.document.defaultView, 'mouseup', this.endDrag.bind(this));\n            });\n        }\n    }\n    unbindDocumentDragEndListener() {\n        if (this.documentDragEndListener) {\n            this.documentDragEndListener();\n            this.documentDragEndListener = null;\n        }\n    }\n    bindDocumentResizeListeners() {\n        if (!this.documentResizeListener && !this.documentResizeEndListener) {\n            this.zone.runOutsideAngular(() => {\n                this.documentResizeListener = this.renderer.listen(this.document.defaultView, 'mousemove', this.onResize.bind(this));\n                this.documentResizeEndListener = this.renderer.listen(this.document.defaultView, 'mouseup', this.resizeEnd.bind(this));\n            });\n        }\n    }\n    unbindDocumentResizeListeners() {\n        if (this.documentResizeListener && this.documentResizeEndListener) {\n            this.documentResizeListener();\n            this.documentResizeEndListener();\n            this.documentResizeListener = null;\n            this.documentResizeEndListener = null;\n        }\n    }\n    bindDocumentEscapeListener() {\n        const documentTarget = this.el ? this.el.nativeElement.ownerDocument : 'document';\n        this.documentEscapeListener = this.renderer.listen(documentTarget, 'keydown', (event) => {\n            if (event.key == 'Escape') {\n                this.close(event);\n            }\n        });\n    }\n    unbindDocumentEscapeListener() {\n        if (this.documentEscapeListener) {\n            this.documentEscapeListener();\n            this.documentEscapeListener = null;\n        }\n    }\n    appendContainer() {\n        if (this.appendTo) {\n            if (this.appendTo === 'body')\n                this.renderer.appendChild(this.document.body, this.wrapper);\n            else\n                appendChild(this.appendTo, this.wrapper);\n        }\n    }\n    restoreAppend() {\n        if (this.container && this.appendTo) {\n            this.renderer.appendChild(this.el.nativeElement, this.wrapper);\n        }\n    }\n    onAnimationStart(event) {\n        switch (event.toState) {\n            case 'visible':\n                this.container = event.element;\n                this.wrapper = this.container?.parentElement;\n                this.appendContainer();\n                this.moveOnTop();\n                this.bindGlobalListeners();\n                this.container?.setAttribute(this.id, '');\n                if (this.modal) {\n                    this.enableModality();\n                }\n                // if (!this.modal && this.blockScroll) {\n                //     addClass(this.document.body, 'p-overflow-hidden');\n                // }\n                if (this.focusOnShow) {\n                    this.focus();\n                }\n                break;\n            case 'void':\n                if (this.wrapper && this.modal) {\n                    addClass(this.wrapper, 'p-overlay-mask-leave');\n                }\n                break;\n        }\n    }\n    onAnimationEnd(event) {\n        switch (event.toState) {\n            case 'void':\n                this.onContainerDestroy();\n                this.onHide.emit({});\n                this.cd.markForCheck();\n                if (this.maskVisible !== this.visible) {\n                    this.maskVisible = this.visible;\n                }\n                break;\n            case 'visible':\n                this.onShow.emit({});\n                break;\n        }\n    }\n    onContainerDestroy() {\n        this.unbindGlobalListeners();\n        this.dragging = false;\n        this.maskVisible = false;\n        if (this.maximized) {\n            // removeClass(this.document.body, 'p-overflow-hidden')\n            this.document.body.style.removeProperty('--scrollbar;-width');\n            this.maximized = false;\n        }\n        if (this.modal) {\n            this.disableModality();\n        }\n        // if (this.blockScroll) {\n        //      removeClass(this.document.body, 'p-overflow-hidden');\n        // }\n        if (hasClass(this.document.body, 'p-overflow-hidden')) {\n            removeClass(this.document.body, 'p-overflow-hidden');\n        }\n        if (this.container && this.autoZIndex) {\n            ZIndexUtils.clear(this.container);\n        }\n        this.container = null;\n        this.wrapper = null;\n        this._style = this.originalStyle ? { ...this.originalStyle } : {};\n    }\n    destroyStyle() {\n        if (this.styleElement) {\n            this.renderer.removeChild(this.document.head, this.styleElement);\n            this.styleElement = null;\n        }\n    }\n    ngOnDestroy() {\n        if (this.container) {\n            this.restoreAppend();\n            this.onContainerDestroy();\n        }\n        this.destroyStyle();\n        super.ngOnDestroy();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: Dialog, deps: null, target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"19.2.10\", type: Dialog, isStandalone: true, selector: \"p-dialog\", inputs: { header: \"header\", draggable: [\"draggable\", \"draggable\", booleanAttribute], resizable: [\"resizable\", \"resizable\", booleanAttribute], positionLeft: \"positionLeft\", positionTop: \"positionTop\", contentStyle: \"contentStyle\", contentStyleClass: \"contentStyleClass\", modal: [\"modal\", \"modal\", booleanAttribute], closeOnEscape: [\"closeOnEscape\", \"closeOnEscape\", booleanAttribute], dismissableMask: [\"dismissableMask\", \"dismissableMask\", booleanAttribute], rtl: [\"rtl\", \"rtl\", booleanAttribute], closable: [\"closable\", \"closable\", booleanAttribute], responsive: \"responsive\", appendTo: \"appendTo\", breakpoints: \"breakpoints\", styleClass: \"styleClass\", maskStyleClass: \"maskStyleClass\", maskStyle: \"maskStyle\", showHeader: [\"showHeader\", \"showHeader\", booleanAttribute], breakpoint: \"breakpoint\", blockScroll: [\"blockScroll\", \"blockScroll\", booleanAttribute], autoZIndex: [\"autoZIndex\", \"autoZIndex\", booleanAttribute], baseZIndex: [\"baseZIndex\", \"baseZIndex\", numberAttribute], minX: [\"minX\", \"minX\", numberAttribute], minY: [\"minY\", \"minY\", numberAttribute], focusOnShow: [\"focusOnShow\", \"focusOnShow\", booleanAttribute], maximizable: [\"maximizable\", \"maximizable\", booleanAttribute], keepInViewport: [\"keepInViewport\", \"keepInViewport\", booleanAttribute], focusTrap: [\"focusTrap\", \"focusTrap\", booleanAttribute], transitionOptions: \"transitionOptions\", closeIcon: \"closeIcon\", closeAriaLabel: \"closeAriaLabel\", closeTabindex: \"closeTabindex\", minimizeIcon: \"minimizeIcon\", maximizeIcon: \"maximizeIcon\", closeButtonProps: \"closeButtonProps\", maximizeButtonProps: \"maximizeButtonProps\", visible: \"visible\", style: \"style\", position: \"position\", role: \"role\", headerTemplate: [\"content\", \"headerTemplate\"], contentTemplate: \"contentTemplate\", footerTemplate: \"footerTemplate\", closeIconTemplate: \"closeIconTemplate\", maximizeIconTemplate: \"maximizeIconTemplate\", minimizeIconTemplate: \"minimizeIconTemplate\", headlessTemplate: \"headlessTemplate\" }, outputs: { onShow: \"onShow\", onHide: \"onHide\", visibleChange: \"visibleChange\", onResizeInit: \"onResizeInit\", onResizeEnd: \"onResizeEnd\", onDragEnd: \"onDragEnd\", onMaximize: \"onMaximize\" }, providers: [DialogStyle], queries: [{ propertyName: \"_headerTemplate\", first: true, predicate: [\"header\"] }, { propertyName: \"_contentTemplate\", first: true, predicate: [\"content\"] }, { propertyName: \"_footerTemplate\", first: true, predicate: [\"footer\"] }, { propertyName: \"_closeiconTemplate\", first: true, predicate: [\"closeicon\"] }, { propertyName: \"_maximizeiconTemplate\", first: true, predicate: [\"maximizeicon\"] }, { propertyName: \"_minimizeiconTemplate\", first: true, predicate: [\"minimizeicon\"] }, { propertyName: \"_headlessTemplate\", first: true, predicate: [\"headless\"] }, { propertyName: \"templates\", predicate: PrimeTemplate }], viewQueries: [{ propertyName: \"headerViewChild\", first: true, predicate: [\"titlebar\"], descendants: true }, { propertyName: \"contentViewChild\", first: true, predicate: [\"content\"], descendants: true }, { propertyName: \"footerViewChild\", first: true, predicate: [\"footer\"], descendants: true }], usesInheritance: true, ngImport: i0, template: `\n        <div\n            *ngIf=\"maskVisible\"\n            [ngClass]=\"maskClass\"\n            [class]=\"maskStyleClass\"\n            [ngStyle]=\"{\n                position: 'fixed',\n                height: '100%',\n                width: '100%',\n                left: 0,\n                top: 0,\n                display: 'flex',\n                'justify-content': position === 'left' || position === 'topleft' || position === 'bottomleft' ? 'flex-start' : position === 'right' || position === 'topright' || position === 'bottomright' ? 'flex-end' : 'center',\n                'align-items': position === 'top' || position === 'topleft' || position === 'topright' ? 'flex-start' : position === 'bottom' || position === 'bottomleft' || position === 'bottomright' ? 'flex-end' : 'center',\n                'pointer-events': modal ? 'auto' : 'none'\n            }\"\n            [style]=\"maskStyle\"\n        >\n            <div\n                *ngIf=\"visible\"\n                #container\n                [class]=\"styleClass\"\n                [ngClass]=\"{ 'p-dialog p-component': true, 'p-dialog-maximized': maximizable && maximized }\"\n                [ngStyle]=\"{ display: 'flex', 'flex-direction': 'column', 'pointer-events': 'auto' }\"\n                [style]=\"style\"\n                pFocusTrap\n                [pFocusTrapDisabled]=\"focusTrap === false\"\n                [@animation]=\"{\n                    value: 'visible',\n                    params: { transform: transformOptions, transition: transitionOptions }\n                }\"\n                (@animation.start)=\"onAnimationStart($event)\"\n                (@animation.done)=\"onAnimationEnd($event)\"\n                [attr.role]=\"role\"\n                [attr.aria-labelledby]=\"ariaLabelledBy\"\n                [attr.aria-modal]=\"true\"\n            >\n                <ng-container *ngIf=\"_headlessTemplate || headlessTemplate || headlessT; else notHeadless\">\n                    <ng-container *ngTemplateOutlet=\"_headlessTemplate || headlessTemplate || headlessT\"></ng-container>\n                </ng-container>\n\n                <ng-template #notHeadless>\n                    <div *ngIf=\"resizable\" [ngClass]=\"cx('resizeHandle')\" style=\"z-index: 90;\" (mousedown)=\"initResize($event)\"></div>\n                    <div #titlebar [ngClass]=\"cx('header')\" (mousedown)=\"initDrag($event)\" *ngIf=\"showHeader\">\n                        <span [id]=\"ariaLabelledBy\" [ngClass]=\"cx('title')\" *ngIf=\"!_headerTemplate && !headerTemplate && !headerT\">{{ header }}</span>\n                        <ng-container *ngTemplateOutlet=\"_headerTemplate || headerTemplate || headerT\"></ng-container>\n                        <div [ngClass]=\"cx('headerActions')\">\n                            <p-button *ngIf=\"maximizable\" [styleClass]=\"cx('pcMaximizeButton')\" (onClick)=\"maximize()\" (keydown.enter)=\"maximize()\" [tabindex]=\"maximizable ? '0' : '-1'\" [ariaLabel]=\"maximizeLabel\" [buttonProps]=\"maximizeButtonProps\">\n                                <span *ngIf=\"maximizeIcon && !_maximizeiconTemplate && !_minimizeiconTemplate\" [ngClass]=\"maximized ? minimizeIcon : maximizeIcon\"></span>\n                                <ng-container *ngIf=\"!maximizeIcon && !maximizeButtonProps?.icon\">\n                                    <WindowMaximizeIcon *ngIf=\"!maximized && !_maximizeiconTemplate && !maximizeIconTemplate && !maximizeIconT\" />\n                                    <WindowMinimizeIcon *ngIf=\"maximized && !_minimizeiconTemplate && !minimizeIconTemplate && !minimizeIconT\" />\n                                </ng-container>\n                                <ng-container *ngIf=\"!maximized\">\n                                    <ng-template *ngTemplateOutlet=\"_maximizeiconTemplate || maximizeIconTemplate || maximizeIconT\"></ng-template>\n                                </ng-container>\n                                <ng-container *ngIf=\"maximized\">\n                                    <ng-template *ngTemplateOutlet=\"_minimizeiconTemplate || minimizeIconTemplate || minimizeIconT\"></ng-template>\n                                </ng-container>\n                            </p-button>\n                            <p-button *ngIf=\"closable\" [styleClass]=\"cx('pcCloseButton')\" [ariaLabel]=\"closeAriaLabel\" (onClick)=\"close($event)\" (keydown.enter)=\"close($event)\" [tabindex]=\"closeTabindex\" [buttonProps]=\"closeButtonProps\">\n                                <ng-template #icon>\n                                    <ng-container *ngIf=\"!_closeiconTemplate && !closeIconTemplate && !closeIconT && !closeButtonProps?.icon\">\n                                        <span *ngIf=\"closeIcon\" [ngClass]=\"closeIcon\"></span>\n                                        <TimesIcon *ngIf=\"!closeIcon\" />\n                                    </ng-container>\n                                    <span *ngIf=\"_closeiconTemplate || closeIconTemplate || closeIconT\">\n                                        <ng-template *ngTemplateOutlet=\"_closeiconTemplate || closeIconTemplate || closeIconT\"></ng-template>\n                                    </span>\n                                </ng-template>\n                            </p-button>\n                        </div>\n                    </div>\n                    <div #content [ngClass]=\"cx('content')\" [class]=\"contentStyleClass\" [ngStyle]=\"contentStyle\" [attr.data-pc-section]=\"'content'\">\n                        <ng-content></ng-content>\n                        <ng-container *ngTemplateOutlet=\"_contentTemplate || contentTemplate || contentT\"></ng-container>\n                    </div>\n                    <div #footer [ngClass]=\"cx('footer')\" *ngIf=\"_footerTemplate || footerTemplate || footerT\">\n                        <ng-content select=\"p-footer\"></ng-content>\n                        <ng-container *ngTemplateOutlet=\"_footerTemplate || footerTemplate || footerT\"></ng-container>\n                    </div>\n                </ng-template>\n            </div>\n        </div>\n    `, isInline: true, dependencies: [{ kind: \"ngmodule\", type: CommonModule }, { kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i1.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"component\", type: Button, selector: \"p-button\", inputs: [\"type\", \"iconPos\", \"icon\", \"badge\", \"label\", \"disabled\", \"loading\", \"loadingIcon\", \"raised\", \"rounded\", \"text\", \"plain\", \"severity\", \"outlined\", \"link\", \"tabindex\", \"size\", \"variant\", \"style\", \"styleClass\", \"badgeClass\", \"badgeSeverity\", \"ariaLabel\", \"autofocus\", \"fluid\", \"buttonProps\"], outputs: [\"onClick\", \"onFocus\", \"onBlur\"] }, { kind: \"directive\", type: FocusTrap, selector: \"[pFocusTrap]\", inputs: [\"pFocusTrapDisabled\"] }, { kind: \"component\", type: TimesIcon, selector: \"TimesIcon\" }, { kind: \"component\", type: WindowMaximizeIcon, selector: \"WindowMaximizeIcon\" }, { kind: \"component\", type: WindowMinimizeIcon, selector: \"WindowMinimizeIcon\" }, { kind: \"ngmodule\", type: SharedModule }], animations: [trigger('animation', [transition('void => visible', [useAnimation(showAnimation)]), transition('visible => void', [useAnimation(hideAnimation)])])], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: Dialog, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'p-dialog',\n                    standalone: true,\n                    imports: [CommonModule, Button, FocusTrap, TimesIcon, WindowMaximizeIcon, WindowMinimizeIcon, SharedModule],\n                    template: `\n        <div\n            *ngIf=\"maskVisible\"\n            [ngClass]=\"maskClass\"\n            [class]=\"maskStyleClass\"\n            [ngStyle]=\"{\n                position: 'fixed',\n                height: '100%',\n                width: '100%',\n                left: 0,\n                top: 0,\n                display: 'flex',\n                'justify-content': position === 'left' || position === 'topleft' || position === 'bottomleft' ? 'flex-start' : position === 'right' || position === 'topright' || position === 'bottomright' ? 'flex-end' : 'center',\n                'align-items': position === 'top' || position === 'topleft' || position === 'topright' ? 'flex-start' : position === 'bottom' || position === 'bottomleft' || position === 'bottomright' ? 'flex-end' : 'center',\n                'pointer-events': modal ? 'auto' : 'none'\n            }\"\n            [style]=\"maskStyle\"\n        >\n            <div\n                *ngIf=\"visible\"\n                #container\n                [class]=\"styleClass\"\n                [ngClass]=\"{ 'p-dialog p-component': true, 'p-dialog-maximized': maximizable && maximized }\"\n                [ngStyle]=\"{ display: 'flex', 'flex-direction': 'column', 'pointer-events': 'auto' }\"\n                [style]=\"style\"\n                pFocusTrap\n                [pFocusTrapDisabled]=\"focusTrap === false\"\n                [@animation]=\"{\n                    value: 'visible',\n                    params: { transform: transformOptions, transition: transitionOptions }\n                }\"\n                (@animation.start)=\"onAnimationStart($event)\"\n                (@animation.done)=\"onAnimationEnd($event)\"\n                [attr.role]=\"role\"\n                [attr.aria-labelledby]=\"ariaLabelledBy\"\n                [attr.aria-modal]=\"true\"\n            >\n                <ng-container *ngIf=\"_headlessTemplate || headlessTemplate || headlessT; else notHeadless\">\n                    <ng-container *ngTemplateOutlet=\"_headlessTemplate || headlessTemplate || headlessT\"></ng-container>\n                </ng-container>\n\n                <ng-template #notHeadless>\n                    <div *ngIf=\"resizable\" [ngClass]=\"cx('resizeHandle')\" style=\"z-index: 90;\" (mousedown)=\"initResize($event)\"></div>\n                    <div #titlebar [ngClass]=\"cx('header')\" (mousedown)=\"initDrag($event)\" *ngIf=\"showHeader\">\n                        <span [id]=\"ariaLabelledBy\" [ngClass]=\"cx('title')\" *ngIf=\"!_headerTemplate && !headerTemplate && !headerT\">{{ header }}</span>\n                        <ng-container *ngTemplateOutlet=\"_headerTemplate || headerTemplate || headerT\"></ng-container>\n                        <div [ngClass]=\"cx('headerActions')\">\n                            <p-button *ngIf=\"maximizable\" [styleClass]=\"cx('pcMaximizeButton')\" (onClick)=\"maximize()\" (keydown.enter)=\"maximize()\" [tabindex]=\"maximizable ? '0' : '-1'\" [ariaLabel]=\"maximizeLabel\" [buttonProps]=\"maximizeButtonProps\">\n                                <span *ngIf=\"maximizeIcon && !_maximizeiconTemplate && !_minimizeiconTemplate\" [ngClass]=\"maximized ? minimizeIcon : maximizeIcon\"></span>\n                                <ng-container *ngIf=\"!maximizeIcon && !maximizeButtonProps?.icon\">\n                                    <WindowMaximizeIcon *ngIf=\"!maximized && !_maximizeiconTemplate && !maximizeIconTemplate && !maximizeIconT\" />\n                                    <WindowMinimizeIcon *ngIf=\"maximized && !_minimizeiconTemplate && !minimizeIconTemplate && !minimizeIconT\" />\n                                </ng-container>\n                                <ng-container *ngIf=\"!maximized\">\n                                    <ng-template *ngTemplateOutlet=\"_maximizeiconTemplate || maximizeIconTemplate || maximizeIconT\"></ng-template>\n                                </ng-container>\n                                <ng-container *ngIf=\"maximized\">\n                                    <ng-template *ngTemplateOutlet=\"_minimizeiconTemplate || minimizeIconTemplate || minimizeIconT\"></ng-template>\n                                </ng-container>\n                            </p-button>\n                            <p-button *ngIf=\"closable\" [styleClass]=\"cx('pcCloseButton')\" [ariaLabel]=\"closeAriaLabel\" (onClick)=\"close($event)\" (keydown.enter)=\"close($event)\" [tabindex]=\"closeTabindex\" [buttonProps]=\"closeButtonProps\">\n                                <ng-template #icon>\n                                    <ng-container *ngIf=\"!_closeiconTemplate && !closeIconTemplate && !closeIconT && !closeButtonProps?.icon\">\n                                        <span *ngIf=\"closeIcon\" [ngClass]=\"closeIcon\"></span>\n                                        <TimesIcon *ngIf=\"!closeIcon\" />\n                                    </ng-container>\n                                    <span *ngIf=\"_closeiconTemplate || closeIconTemplate || closeIconT\">\n                                        <ng-template *ngTemplateOutlet=\"_closeiconTemplate || closeIconTemplate || closeIconT\"></ng-template>\n                                    </span>\n                                </ng-template>\n                            </p-button>\n                        </div>\n                    </div>\n                    <div #content [ngClass]=\"cx('content')\" [class]=\"contentStyleClass\" [ngStyle]=\"contentStyle\" [attr.data-pc-section]=\"'content'\">\n                        <ng-content></ng-content>\n                        <ng-container *ngTemplateOutlet=\"_contentTemplate || contentTemplate || contentT\"></ng-container>\n                    </div>\n                    <div #footer [ngClass]=\"cx('footer')\" *ngIf=\"_footerTemplate || footerTemplate || footerT\">\n                        <ng-content select=\"p-footer\"></ng-content>\n                        <ng-container *ngTemplateOutlet=\"_footerTemplate || footerTemplate || footerT\"></ng-container>\n                    </div>\n                </ng-template>\n            </div>\n        </div>\n    `,\n                    animations: [trigger('animation', [transition('void => visible', [useAnimation(showAnimation)]), transition('visible => void', [useAnimation(hideAnimation)])])],\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    providers: [DialogStyle]\n                }]\n        }], propDecorators: { header: [{\n                type: Input\n            }], draggable: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], resizable: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], positionLeft: [{\n                type: Input\n            }], positionTop: [{\n                type: Input\n            }], contentStyle: [{\n                type: Input\n            }], contentStyleClass: [{\n                type: Input\n            }], modal: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], closeOnEscape: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], dismissableMask: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], rtl: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], closable: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], responsive: [{\n                type: Input\n            }], appendTo: [{\n                type: Input\n            }], breakpoints: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], maskStyleClass: [{\n                type: Input\n            }], maskStyle: [{\n                type: Input\n            }], showHeader: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], breakpoint: [{\n                type: Input\n            }], blockScroll: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], autoZIndex: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], baseZIndex: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], minX: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], minY: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], focusOnShow: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], maximizable: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], keepInViewport: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], focusTrap: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], transitionOptions: [{\n                type: Input\n            }], closeIcon: [{\n                type: Input\n            }], closeAriaLabel: [{\n                type: Input\n            }], closeTabindex: [{\n                type: Input\n            }], minimizeIcon: [{\n                type: Input\n            }], maximizeIcon: [{\n                type: Input\n            }], closeButtonProps: [{\n                type: Input\n            }], maximizeButtonProps: [{\n                type: Input\n            }], visible: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], position: [{\n                type: Input\n            }], role: [{\n                type: Input\n            }], onShow: [{\n                type: Output\n            }], onHide: [{\n                type: Output\n            }], visibleChange: [{\n                type: Output\n            }], onResizeInit: [{\n                type: Output\n            }], onResizeEnd: [{\n                type: Output\n            }], onDragEnd: [{\n                type: Output\n            }], onMaximize: [{\n                type: Output\n            }], headerViewChild: [{\n                type: ViewChild,\n                args: ['titlebar']\n            }], contentViewChild: [{\n                type: ViewChild,\n                args: ['content']\n            }], footerViewChild: [{\n                type: ViewChild,\n                args: ['footer']\n            }], headerTemplate: [{\n                type: Input,\n                args: ['content']\n            }], contentTemplate: [{\n                type: Input\n            }], footerTemplate: [{\n                type: Input\n            }], closeIconTemplate: [{\n                type: Input\n            }], maximizeIconTemplate: [{\n                type: Input\n            }], minimizeIconTemplate: [{\n                type: Input\n            }], headlessTemplate: [{\n                type: Input\n            }], _headerTemplate: [{\n                type: ContentChild,\n                args: ['header', { descendants: false }]\n            }], _contentTemplate: [{\n                type: ContentChild,\n                args: ['content', { descendants: false }]\n            }], _footerTemplate: [{\n                type: ContentChild,\n                args: ['footer', { descendants: false }]\n            }], _closeiconTemplate: [{\n                type: ContentChild,\n                args: ['closeicon', { descendants: false }]\n            }], _maximizeiconTemplate: [{\n                type: ContentChild,\n                args: ['maximizeicon', { descendants: false }]\n            }], _minimizeiconTemplate: [{\n                type: ContentChild,\n                args: ['minimizeicon', { descendants: false }]\n            }], _headlessTemplate: [{\n                type: ContentChild,\n                args: ['headless', { descendants: false }]\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }] } });\nclass DialogModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: DialogModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.10\", ngImport: i0, type: DialogModule, imports: [Dialog, SharedModule], exports: [Dialog, SharedModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: DialogModule, imports: [Dialog, SharedModule, SharedModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: DialogModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [Dialog, SharedModule],\n                    exports: [Dialog, SharedModule]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Dialog, DialogClasses, DialogModule, DialogStyle };\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAEC,UAAU,EAAEC,YAAY,QAAQ,qBAAqB;AAClG,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,iBAAiB,EAAEC,YAAY,QAAQ,iBAAiB;AACjE,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,YAAY,EAAEC,MAAM,EAAEC,MAAM,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,eAAe,EAAEC,YAAY,EAAEC,KAAK,EAAEC,SAAS,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AACrO,SAASC,IAAI,EAAEC,eAAe,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,cAAc,EAAEC,WAAW,EAAEC,WAAW,EAAEC,WAAW,QAAQ,iBAAiB;AAClL,SAASC,eAAe,EAAEC,YAAY,EAAEC,aAAa,QAAQ,aAAa;AAC1E,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,UAAU,QAAQ,aAAa;AACxC,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,SAASC,SAAS,EAAEC,kBAAkB,EAAEC,kBAAkB,QAAQ,eAAe;AACjF,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,SAAS,QAAQ,cAAc;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,IAAA,GAAAA,CAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA;EAAAC,QAAA;EAAAC,MAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,GAAA;EAAAC,OAAA;EAAA,mBAAAR,EAAA;EAAA,eAAAC,EAAA;EAAA,kBAAAC;AAAA;AAAA,MAAAO,IAAA,GAAAT,EAAA;EAAA;EAAA,sBAAAA;AAAA;AAAA,MAAAU,IAAA,GAAAA,CAAA;EAAAF,OAAA;EAAA;EAAA;AAAA;AAAA,MAAAG,IAAA,GAAAA,CAAAX,EAAA,EAAAC,EAAA;EAAAW,SAAA,EAAAZ,EAAA;EAAAvD,UAAA,EAAAwD;AAAA;AAAA,MAAAY,IAAA,GAAAb,EAAA;EAAAc,KAAA;EAAAC,MAAA,EAAAf;AAAA;AAAA,SAAAgB,0DAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAoOqDnE,EAAE,CAAAqE,kBAAA,EA07BuB,CAAC;EAAA;AAAA;AAAA,SAAAC,2CAAAH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA17B1BnE,EAAE,CAAAuE,uBAAA,EAy7BU,CAAC;IAz7BbvE,EAAE,CAAAwE,UAAA,IAAAN,yDAAA,0BA07BQ,CAAC;IA17BXlE,EAAE,CAAAyE,qBAAA;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAO,MAAA,GAAF1E,EAAE,CAAA2E,aAAA;IAAF3E,EAAE,CAAA4E,SAAA,CA07BM,CAAC;IA17BT5E,EAAE,CAAA6E,UAAA,qBAAAH,MAAA,CAAAI,iBAAA,IAAAJ,MAAA,CAAAK,gBAAA,IAAAL,MAAA,CAAAM,SA07BM,CAAC;EAAA;AAAA;AAAA,SAAAC,gDAAAd,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAe,GAAA,GA17BTlF,EAAE,CAAAmF,gBAAA;IAAFnF,EAAE,CAAAoF,cAAA,aA87B+B,CAAC;IA97BlCpF,EAAE,CAAAqF,UAAA,uBAAAC,yEAAAC,MAAA;MAAFvF,EAAE,CAAAwF,aAAA,CAAAN,GAAA;MAAA,MAAAR,MAAA,GAAF1E,EAAE,CAAA2E,aAAA;MAAA,OAAF3E,EAAE,CAAAyF,WAAA,CA87BYf,MAAA,CAAAgB,UAAA,CAAAH,MAAiB,CAAC;IAAA,EAAC;IA97BjCvF,EAAE,CAAA2F,YAAA,CA87BqC,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAO,MAAA,GA97BxC1E,EAAE,CAAA2E,aAAA;IAAF3E,EAAE,CAAA6E,UAAA,YAAAH,MAAA,CAAAkB,EAAA,gBA87BxB,CAAC;EAAA;AAAA;AAAA,SAAAC,uDAAA1B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA97BqBnE,EAAE,CAAAoF,cAAA,cAg8BmC,CAAC;IAh8BtCpF,EAAE,CAAA8F,MAAA,EAg8B+C,CAAC;IAh8BlD9F,EAAE,CAAA2F,YAAA,CAg8BsD,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAO,MAAA,GAh8BzD1E,EAAE,CAAA2E,aAAA;IAAF3E,EAAE,CAAA6E,UAAA,OAAAH,MAAA,CAAAqB,cAg8B9C,CAAC,YAAArB,MAAA,CAAAkB,EAAA,SAAuB,CAAC;IAh8BmB5F,EAAE,CAAA4E,SAAA,CAg8B+C,CAAC;IAh8BlD5E,EAAE,CAAAgG,iBAAA,CAAAtB,MAAA,CAAAuB,MAg8B+C,CAAC;EAAA;AAAA;AAAA,SAAAC,+DAAA/B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAh8BlDnE,EAAE,CAAAqE,kBAAA,EAi8BqB,CAAC;EAAA;AAAA;AAAA,SAAA8B,kEAAAhC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAj8BxBnE,EAAE,CAAAoG,SAAA,cAo8ByE,CAAC;EAAA;EAAA,IAAAjC,EAAA;IAAA,MAAAO,MAAA,GAp8B5E1E,EAAE,CAAA2E,aAAA;IAAF3E,EAAE,CAAA6E,UAAA,YAAAH,MAAA,CAAA2B,SAAA,GAAA3B,MAAA,CAAA4B,YAAA,GAAA5B,MAAA,CAAA6B,YAo8BiE,CAAC;EAAA;AAAA;AAAA,SAAAC,+FAAArC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAp8BpEnE,EAAE,CAAAoG,SAAA,wBAs8BiD,CAAC;EAAA;AAAA;AAAA,SAAAK,+FAAAtC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAt8BpDnE,EAAE,CAAAoG,SAAA,wBAu8BgD,CAAC;EAAA;AAAA;AAAA,SAAAM,0EAAAvC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAv8BnDnE,EAAE,CAAAuE,uBAAA,EAq8BC,CAAC;IAr8BJvE,EAAE,CAAAwE,UAAA,IAAAgC,8FAAA,gCAs8BiD,CAAC,IAAAC,8FAAA,gCACF,CAAC;IAv8BnDzG,EAAE,CAAAyE,qBAAA;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAO,MAAA,GAAF1E,EAAE,CAAA2E,aAAA;IAAF3E,EAAE,CAAA4E,SAAA,CAs8B6C,CAAC;IAt8BhD5E,EAAE,CAAA6E,UAAA,UAAAH,MAAA,CAAA2B,SAAA,KAAA3B,MAAA,CAAAiC,qBAAA,KAAAjC,MAAA,CAAAkC,oBAAA,KAAAlC,MAAA,CAAAmC,aAs8B6C,CAAC;IAt8BhD7G,EAAE,CAAA4E,SAAA,CAu8B4C,CAAC;IAv8B/C5E,EAAE,CAAA6E,UAAA,SAAAH,MAAA,CAAA2B,SAAA,KAAA3B,MAAA,CAAAoC,qBAAA,KAAApC,MAAA,CAAAqC,oBAAA,KAAArC,MAAA,CAAAsC,aAu8B4C,CAAC;EAAA;AAAA;AAAA,SAAAC,0FAAA9C,EAAA,EAAAC,GAAA;AAAA,SAAA8C,4EAAA/C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAv8B/CnE,EAAE,CAAAwE,UAAA,IAAAyC,yFAAA,qBA08BmC,CAAC;EAAA;AAAA;AAAA,SAAAE,0EAAAhD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA18BtCnE,EAAE,CAAAuE,uBAAA,EAy8BhC,CAAC;IAz8B6BvE,EAAE,CAAAwE,UAAA,IAAA0C,2EAAA,gBA08BmC,CAAC;IA18BtClH,EAAE,CAAAyE,qBAAA;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAO,MAAA,GAAF1E,EAAE,CAAA2E,aAAA;IAAF3E,EAAE,CAAA4E,SAAA,CA08BiC,CAAC;IA18BpC5E,EAAE,CAAA6E,UAAA,qBAAAH,MAAA,CAAAiC,qBAAA,IAAAjC,MAAA,CAAAkC,oBAAA,IAAAlC,MAAA,CAAAmC,aA08BiC,CAAC;EAAA;AAAA;AAAA,SAAAO,0FAAAjD,EAAA,EAAAC,GAAA;AAAA,SAAAiD,4EAAAlD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA18BpCnE,EAAE,CAAAwE,UAAA,IAAA4C,yFAAA,qBA68BmC,CAAC;EAAA;AAAA;AAAA,SAAAE,0EAAAnD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA78BtCnE,EAAE,CAAAuE,uBAAA,EA48BjC,CAAC;IA58B8BvE,EAAE,CAAAwE,UAAA,IAAA6C,2EAAA,gBA68BmC,CAAC;IA78BtCrH,EAAE,CAAAyE,qBAAA;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAO,MAAA,GAAF1E,EAAE,CAAA2E,aAAA;IAAF3E,EAAE,CAAA4E,SAAA,CA68BiC,CAAC;IA78BpC5E,EAAE,CAAA6E,UAAA,qBAAAH,MAAA,CAAAoC,qBAAA,IAAApC,MAAA,CAAAqC,oBAAA,IAAArC,MAAA,CAAAsC,aA68BiC,CAAC;EAAA;AAAA;AAAA,SAAAO,2DAAApD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAqD,GAAA,GA78BpCxH,EAAE,CAAAmF,gBAAA;IAAFnF,EAAE,CAAAoF,cAAA,kBAm8ByJ,CAAC;IAn8B5JpF,EAAE,CAAAqF,UAAA,qBAAAoC,uFAAA;MAAFzH,EAAE,CAAAwF,aAAA,CAAAgC,GAAA;MAAA,MAAA9C,MAAA,GAAF1E,EAAE,CAAA2E,aAAA;MAAA,OAAF3E,EAAE,CAAAyF,WAAA,CAm8BWf,MAAA,CAAAgD,QAAA,CAAS,CAAC;IAAA,EAAC,2BAAAC,6FAAA;MAn8BxB3H,EAAE,CAAAwF,aAAA,CAAAgC,GAAA;MAAA,MAAA9C,MAAA,GAAF1E,EAAE,CAAA2E,aAAA;MAAA,OAAF3E,EAAE,CAAAyF,WAAA,CAm8BwCf,MAAA,CAAAgD,QAAA,CAAS,CAAC;IAAA,EAAC;IAn8BrD1H,EAAE,CAAAwE,UAAA,IAAA2B,iEAAA,kBAo8BkE,CAAC,IAAAO,yEAAA,0BAClE,CAAC,IAAAS,yEAAA,0BAIlC,CAAC,IAAAG,yEAAA,0BAGF,CAAC;IA58B8BtH,EAAE,CAAA2F,YAAA,CA+8B1D,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAO,MAAA,GA/8BuD1E,EAAE,CAAA2E,aAAA;IAAF3E,EAAE,CAAA6E,UAAA,eAAAH,MAAA,CAAAkB,EAAA,oBAm8BF,CAAC,aAAAlB,MAAA,CAAAkD,WAAA,aAAyF,CAAC,cAAAlD,MAAA,CAAAmD,aAA2B,CAAC,gBAAAnD,MAAA,CAAAoD,mBAAmC,CAAC;IAn8B3J9H,EAAE,CAAA4E,SAAA,CAo8BY,CAAC;IAp8Bf5E,EAAE,CAAA6E,UAAA,SAAAH,MAAA,CAAA6B,YAAA,KAAA7B,MAAA,CAAAiC,qBAAA,KAAAjC,MAAA,CAAAoC,qBAo8BY,CAAC;IAp8Bf9G,EAAE,CAAA4E,SAAA,CAq8BD,CAAC;IAr8BF5E,EAAE,CAAA6E,UAAA,UAAAH,MAAA,CAAA6B,YAAA,MAAA7B,MAAA,CAAAoD,mBAAA,kBAAApD,MAAA,CAAAoD,mBAAA,CAAAC,IAAA,CAq8BD,CAAC;IAr8BF/H,EAAE,CAAA4E,SAAA,CAy8BlC,CAAC;IAz8B+B5E,EAAE,CAAA6E,UAAA,UAAAH,MAAA,CAAA2B,SAy8BlC,CAAC;IAz8B+BrG,EAAE,CAAA4E,SAAA,CA48BnC,CAAC;IA58BgC5E,EAAE,CAAA6E,UAAA,SAAAH,MAAA,CAAA2B,SA48BnC,CAAC;EAAA;AAAA;AAAA,SAAA2B,+FAAA7D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA58BgCnE,EAAE,CAAAoG,SAAA,cAm9BJ,CAAC;EAAA;EAAA,IAAAjC,EAAA;IAAA,MAAAO,MAAA,GAn9BC1E,EAAE,CAAA2E,aAAA;IAAF3E,EAAE,CAAA6E,UAAA,YAAAH,MAAA,CAAAuD,SAm9BZ,CAAC;EAAA;AAAA;AAAA,SAAAC,oGAAA/D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAn9BSnE,EAAE,CAAAoG,SAAA,eAo9BzB,CAAC;EAAA;AAAA;AAAA,SAAA+B,wFAAAhE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAp9BsBnE,EAAE,CAAAuE,uBAAA,EAk9B6C,CAAC;IAl9BhDvE,EAAE,CAAAwE,UAAA,IAAAwD,8FAAA,kBAm9BX,CAAC,IAAAE,mGAAA,uBACf,CAAC;IAp9BsBlI,EAAE,CAAAyE,qBAAA;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAO,MAAA,GAAF1E,EAAE,CAAA2E,aAAA;IAAF3E,EAAE,CAAA4E,SAAA,CAm9BnC,CAAC;IAn9BgC5E,EAAE,CAAA6E,UAAA,SAAAH,MAAA,CAAAuD,SAm9BnC,CAAC;IAn9BgCjI,EAAE,CAAA4E,SAAA,CAo9B7B,CAAC;IAp9B0B5E,EAAE,CAAA6E,UAAA,UAAAH,MAAA,CAAAuD,SAo9B7B,CAAC;EAAA;AAAA;AAAA,SAAAG,gGAAAjE,EAAA,EAAAC,GAAA;AAAA,SAAAiE,kFAAAlE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAp9B0BnE,EAAE,CAAAwE,UAAA,IAAA4D,+FAAA,qBAu9B8B,CAAC;EAAA;AAAA;AAAA,SAAAE,gFAAAnE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAv9BjCnE,EAAE,CAAAoF,cAAA,UAs9BO,CAAC;IAt9BVpF,EAAE,CAAAwE,UAAA,IAAA6D,iFAAA,gBAu9B8B,CAAC;IAv9BjCrI,EAAE,CAAA2F,YAAA,CAw9BtD,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAO,MAAA,GAx9BmD1E,EAAE,CAAA2E,aAAA;IAAF3E,EAAE,CAAA4E,SAAA,CAu9B4B,CAAC;IAv9B/B5E,EAAE,CAAA6E,UAAA,qBAAAH,MAAA,CAAA6D,kBAAA,IAAA7D,MAAA,CAAA8D,iBAAA,IAAA9D,MAAA,CAAA+D,UAu9B4B,CAAC;EAAA;AAAA;AAAA,SAAAC,yEAAAvE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAv9B/BnE,EAAE,CAAAwE,UAAA,IAAA2D,uFAAA,0BAk9B6C,CAAC,IAAAG,+EAAA,kBAIvC,CAAC;EAAA;EAAA,IAAAnE,EAAA;IAAA,MAAAO,MAAA,GAt9BV1E,EAAE,CAAA2E,aAAA;IAAF3E,EAAE,CAAA6E,UAAA,UAAAH,MAAA,CAAA6D,kBAAA,KAAA7D,MAAA,CAAA8D,iBAAA,KAAA9D,MAAA,CAAA+D,UAAA,MAAA/D,MAAA,CAAAiE,gBAAA,kBAAAjE,MAAA,CAAAiE,gBAAA,CAAAZ,IAAA,CAk9B2C,CAAC;IAl9B9C/H,EAAE,CAAA4E,SAAA,CAs9BK,CAAC;IAt9BR5E,EAAE,CAAA6E,UAAA,SAAAH,MAAA,CAAA6D,kBAAA,IAAA7D,MAAA,CAAA8D,iBAAA,IAAA9D,MAAA,CAAA+D,UAs9BK,CAAC;EAAA;AAAA;AAAA,SAAAG,2DAAAzE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA0E,GAAA,GAt9BR7I,EAAE,CAAAmF,gBAAA;IAAFnF,EAAE,CAAAoF,cAAA,kBAg9B4I,CAAC;IAh9B/IpF,EAAE,CAAAqF,UAAA,qBAAAyD,uFAAAvD,MAAA;MAAFvF,EAAE,CAAAwF,aAAA,CAAAqD,GAAA;MAAA,MAAAnE,MAAA,GAAF1E,EAAE,CAAA2E,aAAA;MAAA,OAAF3E,EAAE,CAAAyF,WAAA,CAg9BkCf,MAAA,CAAAqE,KAAA,CAAAxD,MAAY,CAAC;IAAA,EAAC,2BAAAyD,6FAAAzD,MAAA;MAh9BlDvF,EAAE,CAAAwF,aAAA,CAAAqD,GAAA;MAAA,MAAAnE,MAAA,GAAF1E,EAAE,CAAA2E,aAAA;MAAA,OAAF3E,EAAE,CAAAyF,WAAA,CAg9BkEf,MAAA,CAAAqE,KAAA,CAAAxD,MAAY,CAAC;IAAA,EAAC;IAh9BlFvF,EAAE,CAAAwE,UAAA,IAAAkE,wEAAA,gCAAF1I,EAAE,CAAAiJ,sBAi9B9C,CAAC;IAj9B2CjJ,EAAE,CAAA2F,YAAA,CA09B1D,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAO,MAAA,GA19BuD1E,EAAE,CAAA2E,aAAA;IAAF3E,EAAE,CAAA6E,UAAA,eAAAH,MAAA,CAAAkB,EAAA,iBAg9BR,CAAC,cAAAlB,MAAA,CAAAwE,cAA4B,CAAC,aAAAxE,MAAA,CAAAyE,aAAoF,CAAC,gBAAAzE,MAAA,CAAAiE,gBAAgC,CAAC;EAAA;AAAA;AAAA,SAAAS,gDAAAjF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAkF,GAAA,GAh9B9IrJ,EAAE,CAAAmF,gBAAA;IAAFnF,EAAE,CAAAoF,cAAA,gBA+7Ba,CAAC;IA/7BhBpF,EAAE,CAAAqF,UAAA,uBAAAiE,yEAAA/D,MAAA;MAAFvF,EAAE,CAAAwF,aAAA,CAAA6D,GAAA;MAAA,MAAA3E,MAAA,GAAF1E,EAAE,CAAA2E,aAAA;MAAA,OAAF3E,EAAE,CAAAyF,WAAA,CA+7BvBf,MAAA,CAAA6E,QAAA,CAAAhE,MAAe,CAAC;IAAA,EAAC;IA/7BIvF,EAAE,CAAAwE,UAAA,IAAAqB,sDAAA,kBAg8BmC,CAAC,IAAAK,8DAAA,0BAC9B,CAAC;IAj8BTlG,EAAE,CAAAoF,cAAA,aAk8BpC,CAAC;IAl8BiCpF,EAAE,CAAAwE,UAAA,IAAA+C,0DAAA,sBAm8ByJ,CAAC,IAAAqB,0DAAA,sBAad,CAAC;IAh9B/I5I,EAAE,CAAA2F,YAAA,CA29BnE,CAAC,CACL,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAO,MAAA,GA59BoE1E,EAAE,CAAA2E,aAAA;IAAF3E,EAAE,CAAA6E,UAAA,YAAAH,MAAA,CAAAkB,EAAA,UA+7BtC,CAAC;IA/7BmC5F,EAAE,CAAA4E,SAAA,EAg8BiC,CAAC;IAh8BpC5E,EAAE,CAAA6E,UAAA,UAAAH,MAAA,CAAA8E,eAAA,KAAA9E,MAAA,CAAA+E,cAAA,KAAA/E,MAAA,CAAAgF,OAg8BiC,CAAC;IAh8BpC1J,EAAE,CAAA4E,SAAA,CAi8BI,CAAC;IAj8BP5E,EAAE,CAAA6E,UAAA,qBAAAH,MAAA,CAAA8E,eAAA,IAAA9E,MAAA,CAAA+E,cAAA,IAAA/E,MAAA,CAAAgF,OAi8BI,CAAC;IAj8BP1J,EAAE,CAAA4E,SAAA,CAk8BrC,CAAC;IAl8BkC5E,EAAE,CAAA6E,UAAA,YAAAH,MAAA,CAAAkB,EAAA,iBAk8BrC,CAAC;IAl8BkC5F,EAAE,CAAA4E,SAAA,CAm8BzC,CAAC;IAn8BsC5E,EAAE,CAAA6E,UAAA,SAAAH,MAAA,CAAAkD,WAm8BzC,CAAC;IAn8BsC5H,EAAE,CAAA4E,SAAA,CAg9B5C,CAAC;IAh9ByC5E,EAAE,CAAA6E,UAAA,SAAAH,MAAA,CAAAiF,QAg9B5C,CAAC;EAAA;AAAA;AAAA,SAAAC,yDAAAzF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAh9ByCnE,EAAE,CAAAqE,kBAAA,EA+9BwB,CAAC;EAAA;AAAA;AAAA,SAAAwF,+DAAA1F,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/9B3BnE,EAAE,CAAAqE,kBAAA,EAm+BqB,CAAC;EAAA;AAAA;AAAA,SAAAyF,gDAAA3F,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAn+BxBnE,EAAE,CAAAoF,cAAA,gBAi+Bc,CAAC;IAj+BjBpF,EAAE,CAAA+J,YAAA,KAk+B9B,CAAC;IAl+B2B/J,EAAE,CAAAwE,UAAA,IAAAqF,8DAAA,0BAm+BM,CAAC;IAn+BT7J,EAAE,CAAA2F,YAAA,CAo+BvE,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAO,MAAA,GAp+BoE1E,EAAE,CAAA2E,aAAA;IAAF3E,EAAE,CAAA6E,UAAA,YAAAH,MAAA,CAAAkB,EAAA,UAi+BxC,CAAC;IAj+BqC5F,EAAE,CAAA4E,SAAA,EAm+BI,CAAC;IAn+BP5E,EAAE,CAAA6E,UAAA,qBAAAH,MAAA,CAAAsF,eAAA,IAAAtF,MAAA,CAAAuF,cAAA,IAAAvF,MAAA,CAAAwF,OAm+BI,CAAC;EAAA;AAAA;AAAA,SAAAC,0CAAAhG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAn+BPnE,EAAE,CAAAwE,UAAA,IAAAS,+CAAA,iBA87B+B,CAAC,IAAAmE,+CAAA,iBACnB,CAAC;IA/7BhBpJ,EAAE,CAAAoF,cAAA,eA69BmD,CAAC;IA79BtDpF,EAAE,CAAA+J,YAAA,EA89BhD,CAAC;IA99B6C/J,EAAE,CAAAwE,UAAA,IAAAoF,wDAAA,0BA+9BS,CAAC;IA/9BZ5J,EAAE,CAAA2F,YAAA,CAg+BvE,CAAC;IAh+BoE3F,EAAE,CAAAwE,UAAA,IAAAsF,+CAAA,iBAi+Bc,CAAC;EAAA;EAAA,IAAA3F,EAAA;IAAA,MAAAO,MAAA,GAj+BjB1E,EAAE,CAAA2E,aAAA;IAAF3E,EAAE,CAAA6E,UAAA,SAAAH,MAAA,CAAA0F,SA87BxD,CAAC;IA97BqDpK,EAAE,CAAA4E,SAAA,CA+7BW,CAAC;IA/7Bd5E,EAAE,CAAA6E,UAAA,SAAAH,MAAA,CAAA2F,UA+7BW,CAAC;IA/7BdrK,EAAE,CAAA4E,SAAA,CA69BV,CAAC;IA79BO5E,EAAE,CAAAsK,UAAA,CAAA5F,MAAA,CAAA6F,iBA69BV,CAAC;IA79BOvK,EAAE,CAAA6E,UAAA,YAAAH,MAAA,CAAAkB,EAAA,WA69BtC,CAAC,YAAAlB,MAAA,CAAA8F,YAAoD,CAAC;IA79BlBxK,EAAE,CAAAyK,WAAA;IAAFzK,EAAE,CAAA4E,SAAA,EA+9BO,CAAC;IA/9BV5E,EAAE,CAAA6E,UAAA,qBAAAH,MAAA,CAAAgG,gBAAA,IAAAhG,MAAA,CAAAiG,eAAA,IAAAjG,MAAA,CAAAkG,QA+9BO,CAAC;IA/9BV5K,EAAE,CAAA4E,SAAA,CAi+BY,CAAC;IAj+Bf5E,EAAE,CAAA6E,UAAA,SAAAH,MAAA,CAAAsF,eAAA,IAAAtF,MAAA,CAAAuF,cAAA,IAAAvF,MAAA,CAAAwF,OAi+BY,CAAC;EAAA;AAAA;AAAA,SAAAW,4BAAA1G,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA2G,GAAA,GAj+Bf9K,EAAE,CAAAmF,gBAAA;IAAFnF,EAAE,CAAAoF,cAAA,eAw7BpF,CAAC;IAx7BiFpF,EAAE,CAAAqF,UAAA,8BAAA0F,qEAAAxF,MAAA;MAAFvF,EAAE,CAAAwF,aAAA,CAAAsF,GAAA;MAAA,MAAApG,MAAA,GAAF1E,EAAE,CAAA2E,aAAA;MAAA,OAAF3E,EAAE,CAAAyF,WAAA,CAm7B5Df,MAAA,CAAAsG,gBAAA,CAAAzF,MAAuB,CAAC;IAAA,EAAC,6BAAA0F,oEAAA1F,MAAA;MAn7BiCvF,EAAE,CAAAwF,aAAA,CAAAsF,GAAA;MAAA,MAAApG,MAAA,GAAF1E,EAAE,CAAA2E,aAAA;MAAA,OAAF3E,EAAE,CAAAyF,WAAA,CAo7B7Df,MAAA,CAAAwG,cAAA,CAAA3F,MAAqB,CAAC;IAAA,EAAC;IAp7BoCvF,EAAE,CAAAwE,UAAA,IAAAF,0CAAA,0BAy7BU,CAAC,IAAA6F,yCAAA,gCAz7BbnK,EAAE,CAAAiJ,sBA67BvD,CAAC;IA77BoDjJ,EAAE,CAAA2F,YAAA,CAs+B/E,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAgH,cAAA,GAt+B4EnL,EAAE,CAAAoL,WAAA;IAAA,MAAA1G,MAAA,GAAF1E,EAAE,CAAA2E,aAAA;IAAF3E,EAAE,CAAAqL,UAAA,CAAA3G,MAAA,CAAAlF,KA46BlE,CAAC;IA56B+DQ,EAAE,CAAAsK,UAAA,CAAA5F,MAAA,CAAA4G,UAy6B7D,CAAC;IAz6B0DtL,EAAE,CAAA6E,UAAA,YAAF7E,EAAE,CAAAuL,eAAA,KAAA5H,IAAA,EAAAe,MAAA,CAAAkD,WAAA,IAAAlD,MAAA,CAAA2B,SAAA,CA06BW,CAAC,YA16BdrG,EAAE,CAAAwL,eAAA,KAAA5H,IAAA,CA26BI,CAAC,uBAAAc,MAAA,CAAA+G,SAAA,UAG5C,CAAC,eA96BoCzL,EAAE,CAAAuL,eAAA,KAAAxH,IAAA,EAAF/D,EAAE,CAAA0L,eAAA,KAAA7H,IAAA,EAAAa,MAAA,CAAAiH,gBAAA,EAAAjH,MAAA,CAAAkH,iBAAA,EAk7B/E,CAAC;IAl7B4E5L,EAAE,CAAAyK,WAAA,SAAA/F,MAAA,CAAAmH,IAAA,qBAAAnH,MAAA,CAAAqB,cAAA;IAAF/F,EAAE,CAAA4E,SAAA,EAy7BR,CAAC;IAz7BK5E,EAAE,CAAA6E,UAAA,SAAAH,MAAA,CAAAI,iBAAA,IAAAJ,MAAA,CAAAK,gBAAA,IAAAL,MAAA,CAAAM,SAy7BR,CAAC,aAAAmG,cAAe,CAAC;EAAA;AAAA;AAAA,SAAAW,sBAAA3H,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAz7BXnE,EAAE,CAAAoF,cAAA,YAq6BxF,CAAC;IAr6BqFpF,EAAE,CAAAwE,UAAA,IAAAqG,2BAAA,iBAw7BpF,CAAC;IAx7BiF7K,EAAE,CAAA2F,YAAA,CAu+BnF,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAO,MAAA,GAv+BgF1E,EAAE,CAAA2E,aAAA;IAAF3E,EAAE,CAAAqL,UAAA,CAAA3G,MAAA,CAAAqH,SAo6BlE,CAAC;IAp6B+D/L,EAAE,CAAAsK,UAAA,CAAA5F,MAAA,CAAAsH,cAw5B7D,CAAC;IAx5B0DhM,EAAE,CAAA6E,UAAA,YAAAH,MAAA,CAAAuH,SAu5BhE,CAAC,YAv5B6DjM,EAAE,CAAAkM,eAAA,IAAAjJ,IAAA,EAAAyB,MAAA,CAAArB,QAAA,eAAAqB,MAAA,CAAArB,QAAA,kBAAAqB,MAAA,CAAArB,QAAA,mCAAAqB,MAAA,CAAArB,QAAA,gBAAAqB,MAAA,CAAArB,QAAA,mBAAAqB,MAAA,CAAArB,QAAA,4CAAAqB,MAAA,CAAArB,QAAA,cAAAqB,MAAA,CAAArB,QAAA,kBAAAqB,MAAA,CAAArB,QAAA,iCAAAqB,MAAA,CAAArB,QAAA,iBAAAqB,MAAA,CAAArB,QAAA,qBAAAqB,MAAA,CAAArB,QAAA,4CAAAqB,MAAA,CAAAyH,KAAA,mBAm6BnF,CAAC;IAn6BgFnM,EAAE,CAAA4E,SAAA,CAu6BnE,CAAC;IAv6BgE5E,EAAE,CAAA6E,UAAA,SAAAH,MAAA,CAAA0H,OAu6BnE,CAAC;EAAA;AAAA;AAzoC9B,MAAMC,KAAK,GAAGA,CAAC;EAAEC;AAAG,CAAC,KAAK;AAC1B;AACA;AACA;AACA,qBAAqBA,EAAE,CAAC,sBAAsB,CAAC;AAC/C,kBAAkBA,EAAE,CAAC,eAAe,CAAC;AACrC,kBAAkBA,EAAE,CAAC,mBAAmB,CAAC;AACzC,wBAAwBA,EAAE,CAAC,qBAAqB,CAAC;AACjD,aAAaA,EAAE,CAAC,cAAc,CAAC;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeA,EAAE,CAAC,wBAAwB,CAAC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeA,EAAE,CAAC,uBAAuB,CAAC;AAC1C;AACA;AACA;AACA,mBAAmBA,EAAE,CAAC,0BAA0B,CAAC;AACjD,iBAAiBA,EAAE,CAAC,wBAAwB,CAAC;AAC7C;AACA;AACA;AACA;AACA,eAAeA,EAAE,CAAC,uBAAuB,CAAC;AAC1C;AACA;AACA,WAAWA,EAAE,CAAC,mBAAmB,CAAC;AAClC;AACA;AACA;AACA;AACA;AACA,WAAWA,EAAE,CAAC,mBAAmB,CAAC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA,MAAMC,YAAY,GAAG;EACjBC,IAAI,EAAEA,CAAC;IAAEC;EAAS,CAAC,MAAM;IACrBpJ,QAAQ,EAAE,OAAO;IACjBC,MAAM,EAAE,MAAM;IACdC,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE,CAAC;IACPC,GAAG,EAAE,CAAC;IACNC,OAAO,EAAE,MAAM;IACfgJ,cAAc,EAAED,QAAQ,CAACpJ,QAAQ,KAAK,MAAM,IAAIoJ,QAAQ,CAACpJ,QAAQ,KAAK,SAAS,IAAIoJ,QAAQ,CAACpJ,QAAQ,KAAK,YAAY,GAC/G,YAAY,GACZoJ,QAAQ,CAACpJ,QAAQ,KAAK,OAAO,IAAIoJ,QAAQ,CAACpJ,QAAQ,KAAK,UAAU,IAAIoJ,QAAQ,CAACpJ,QAAQ,KAAK,aAAa,GACpG,UAAU,GACV,QAAQ;IAClBsJ,UAAU,EAAEF,QAAQ,CAACpJ,QAAQ,KAAK,KAAK,IAAIoJ,QAAQ,CAACpJ,QAAQ,KAAK,SAAS,IAAIoJ,QAAQ,CAACpJ,QAAQ,KAAK,UAAU,GACxG,YAAY,GACZoJ,QAAQ,CAACpJ,QAAQ,KAAK,QAAQ,IAAIoJ,QAAQ,CAACpJ,QAAQ,KAAK,YAAY,IAAIoJ,QAAQ,CAACpJ,QAAQ,KAAK,aAAa,GACvG,UAAU,GACV,QAAQ;IAClBuJ,aAAa,EAAEH,QAAQ,CAACN,KAAK,GAAG,MAAM,GAAG;EAC7C,CAAC,CAAC;EACFU,IAAI,EAAE;IACFnJ,OAAO,EAAE,MAAM;IACfoJ,aAAa,EAAE,QAAQ;IACvBF,aAAa,EAAE;EACnB;AACJ,CAAC;AACD,MAAMG,OAAO,GAAG;EACZP,IAAI,EAAEA,CAAC;IAAEC;EAAS,CAAC,KAAK;IACpB,MAAMO,SAAS,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,YAAY,EAAE,aAAa,CAAC;IACxG,MAAMC,GAAG,GAAGD,SAAS,CAACE,IAAI,CAAEC,IAAI,IAAKA,IAAI,KAAKV,QAAQ,CAACpJ,QAAQ,CAAC;IAChE,OAAO;MACH,eAAe,EAAE,IAAI;MACrB,qCAAqC,EAAEoJ,QAAQ,CAACN,KAAK;MACrD,CAAC,YAAYc,GAAG,EAAE,GAAGA;IACzB,CAAC;EACL,CAAC;EACDJ,IAAI,EAAEA,CAAC;IAAEJ;EAAS,CAAC,MAAM;IAAE,sBAAsB,EAAE,IAAI;IAAE,oBAAoB,EAAEA,QAAQ,CAAC7E,WAAW,IAAI6E,QAAQ,CAACpG;EAAU,CAAC,CAAC;EAC5HJ,MAAM,EAAE,iBAAiB;EACzBmH,KAAK,EAAE,gBAAgB;EACvBC,YAAY,EAAE,oBAAoB;EAClCC,aAAa,EAAE,yBAAyB;EACxCC,gBAAgB,EAAE,0BAA0B;EAC5CC,aAAa,EAAE,uBAAuB;EACtCC,OAAO,EAAE,kBAAkB;EAC3BC,MAAM,EAAE;AACZ,CAAC;AACD,MAAMC,WAAW,SAASrL,SAAS,CAAC;EAChCsL,IAAI,GAAG,QAAQ;EACfvB,KAAK,GAAGA,KAAK;EACbU,OAAO,GAAGA,OAAO;EACjBR,YAAY,GAAGA,YAAY;EAC3B,OAAOsB,IAAI;IAAA,IAAAC,wBAAA;IAAA,gBAAAC,oBAAAC,iBAAA;MAAA,QAAAF,wBAAA,KAAAA,wBAAA,GAA+E9N,EAAE,CAAAiO,qBAAA,CAAQN,WAAW,IAAAK,iBAAA,IAAXL,WAAW;IAAA;EAAA;EAC/G,OAAOO,KAAK,kBAD8ElO,EAAE,CAAAmO,kBAAA;IAAAC,KAAA,EACYT,WAAW;IAAAU,OAAA,EAAXV,WAAW,CAAAE;EAAA;AACvH;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KAH8FtO,EAAE,CAAAuO,iBAAA,CAGJZ,WAAW,EAAc,CAAC;IAC1Ga,IAAI,EAAEvO;EACV,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIwO,aAAa;AACjB,CAAC,UAAUA,aAAa,EAAE;EACtB;AACJ;AACA;EACIA,aAAa,CAAC,MAAM,CAAC,GAAG,eAAe;EACvC;AACJ;AACA;EACIA,aAAa,CAAC,MAAM,CAAC,GAAG,UAAU;EAClC;AACJ;AACA;EACIA,aAAa,CAAC,QAAQ,CAAC,GAAG,iBAAiB;EAC3C;AACJ;AACA;EACIA,aAAa,CAAC,OAAO,CAAC,GAAG,gBAAgB;EACzC;AACJ;AACA;EACIA,aAAa,CAAC,eAAe,CAAC,GAAG,yBAAyB;EAC1D;AACJ;AACA;EACIA,aAAa,CAAC,kBAAkB,CAAC,GAAG,0BAA0B;EAC9D;AACJ;AACA;EACIA,aAAa,CAAC,eAAe,CAAC,GAAG,uBAAuB;EACxD;AACJ;AACA;EACIA,aAAa,CAAC,SAAS,CAAC,GAAG,kBAAkB;EAC7C;AACJ;AACA;EACIA,aAAa,CAAC,QAAQ,CAAC,GAAG,iBAAiB;AAC/C,CAAC,EAAEA,aAAa,KAAKA,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC;AAEzC,MAAMC,aAAa,GAAGnP,SAAS,CAAC,CAACC,KAAK,CAAC;EAAEsE,SAAS,EAAE,eAAe;EAAE6K,OAAO,EAAE;AAAE,CAAC,CAAC,EAAElP,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC;AAC/G,MAAMmP,aAAa,GAAGrP,SAAS,CAAC,CAACE,OAAO,CAAC,gBAAgB,EAAED,KAAK,CAAC;EAAEsE,SAAS,EAAE,eAAe;EAAE6K,OAAO,EAAE;AAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/G;AACA;AACA;AACA;AACA,MAAME,MAAM,SAAS/M,aAAa,CAAC;EAC/B;AACJ;AACA;AACA;EACImE,MAAM;EACN;AACJ;AACA;AACA;EACI6I,SAAS,GAAG,IAAI;EAChB;AACJ;AACA;AACA;EACI1E,SAAS,GAAG,IAAI;EAChB;AACJ;AACA;AACA;AACA;EACI,IAAI2E,YAAYA,CAAA,EAAG;IACf,OAAO,CAAC;EACZ;EACA,IAAIA,YAAYA,CAACC,aAAa,EAAE;IAC5BC,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;EACvD;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,CAAC;EACZ;EACA,IAAIA,WAAWA,CAACC,YAAY,EAAE;IAC1BH,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;EACtD;EACA;AACJ;AACA;AACA;EACI1E,YAAY;EACZ;AACJ;AACA;AACA;EACID,iBAAiB;EACjB;AACJ;AACA;AACA;EACI4B,KAAK,GAAG,KAAK;EACb;AACJ;AACA;AACA;EACIkD,aAAa,GAAG,IAAI;EACpB;AACJ;AACA;AACA;EACIC,eAAe,GAAG,KAAK;EACvB;AACJ;AACA;AACA;EACIC,GAAG,GAAG,KAAK;EACX;AACJ;AACA;AACA;EACI5F,QAAQ,GAAG,IAAI;EACf;AACJ;AACA;AACA;AACA;EACI,IAAI6F,UAAUA,CAAA,EAAG;IACb,OAAO,KAAK;EAChB;EACA,IAAIA,UAAUA,CAACC,WAAW,EAAE;IACxBR,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;EACrD;EACA;AACJ;AACA;AACA;EACIQ,QAAQ;EACR;AACJ;AACA;AACA;EACIC,WAAW;EACX;AACJ;AACA;AACA;EACIrE,UAAU;EACV;AACJ;AACA;AACA;EACIU,cAAc;EACd;AACJ;AACA;AACA;EACID,SAAS;EACT;AACJ;AACA;AACA;EACI1B,UAAU,GAAG,IAAI;EACjB;AACJ;AACA;AACA;AACA;EACI,IAAIuF,UAAUA,CAAA,EAAG;IACb,OAAO,GAAG;EACd;EACA,IAAIA,UAAUA,CAACC,WAAW,EAAE;IACxBZ,OAAO,CAACC,GAAG,CAAC,mGAAmG,CAAC;EACpH;EACA;AACJ;AACA;AACA;EACIY,WAAW,GAAG,KAAK;EACnB;AACJ;AACA;AACA;EACIC,UAAU,GAAG,IAAI;EACjB;AACJ;AACA;AACA;EACIC,UAAU,GAAG,CAAC;EACd;AACJ;AACA;AACA;EACIC,IAAI,GAAG,CAAC;EACR;AACJ;AACA;AACA;EACIC,IAAI,GAAG,CAAC;EACR;AACJ;AACA;AACA;EACIC,WAAW,GAAG,IAAI;EAClB;AACJ;AACA;AACA;EACIvI,WAAW,GAAG,KAAK;EACnB;AACJ;AACA;AACA;EACIwI,cAAc,GAAG,IAAI;EACrB;AACJ;AACA;AACA;EACI3E,SAAS,GAAG,IAAI;EAChB;AACJ;AACA;AACA;EACIG,iBAAiB,GAAG,kCAAkC;EACtD;AACJ;AACA;AACA;EACI3D,SAAS;EACT;AACJ;AACA;AACA;EACIiB,cAAc;EACd;AACJ;AACA;AACA;EACIC,aAAa,GAAG,GAAG;EACnB;AACJ;AACA;AACA;EACI7C,YAAY;EACZ;AACJ;AACA;AACA;EACIC,YAAY;EACZ;AACJ;AACA;AACA;EACIoC,gBAAgB,GAAG;IACf0H,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE;EACb,CAAC;EACD;AACJ;AACA;AACA;EACIzI,mBAAmB,GAAG;IAClBuI,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE;EACb,CAAC;EACD;AACJ;AACA;AACA;EACI,IAAInE,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACoE,QAAQ;EACxB;EACA,IAAIpE,OAAOA,CAACpI,KAAK,EAAE;IACf,IAAI,CAACwM,QAAQ,GAAGxM,KAAK;IACrB,IAAI,IAAI,CAACwM,QAAQ,IAAI,CAAC,IAAI,CAACC,WAAW,EAAE;MACpC,IAAI,CAACA,WAAW,GAAG,IAAI;IAC3B;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIjR,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACkR,MAAM;EACtB;EACA,IAAIlR,KAAKA,CAACwE,KAAK,EAAE;IACb,IAAIA,KAAK,EAAE;MACP,IAAI,CAAC0M,MAAM,GAAG;QAAE,GAAG1M;MAAM,CAAC;MAC1B,IAAI,CAAC2M,aAAa,GAAG3M,KAAK;IAC9B;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIX,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACuN,SAAS;EACzB;EACA,IAAIvN,QAAQA,CAACW,KAAK,EAAE;IAChB,IAAI,CAAC4M,SAAS,GAAG5M,KAAK;IACtB,QAAQA,KAAK;MACT,KAAK,SAAS;MACd,KAAK,YAAY;MACjB,KAAK,MAAM;QACP,IAAI,CAAC2H,gBAAgB,GAAG,8BAA8B;QACtD;MACJ,KAAK,UAAU;MACf,KAAK,aAAa;MAClB,KAAK,OAAO;QACR,IAAI,CAACA,gBAAgB,GAAG,6BAA6B;QACrD;MACJ,KAAK,QAAQ;QACT,IAAI,CAACA,gBAAgB,GAAG,6BAA6B;QACrD;MACJ,KAAK,KAAK;QACN,IAAI,CAACA,gBAAgB,GAAG,8BAA8B;QACtD;MACJ;QACI,IAAI,CAACA,gBAAgB,GAAG,YAAY;QACpC;IACR;EACJ;EACA;AACJ;AACA;AACA;EACIE,IAAI,GAAG,QAAQ;EACf;AACJ;AACA;AACA;EACIgF,MAAM,GAAG,IAAI3Q,YAAY,CAAC,CAAC;EAC3B;AACJ;AACA;AACA;EACI4Q,MAAM,GAAG,IAAI5Q,YAAY,CAAC,CAAC;EAC3B;AACJ;AACA;AACA;AACA;EACI6Q,aAAa,GAAG,IAAI7Q,YAAY,CAAC,CAAC;EAClC;AACJ;AACA;AACA;AACA;EACI8Q,YAAY,GAAG,IAAI9Q,YAAY,CAAC,CAAC;EACjC;AACJ;AACA;AACA;AACA;EACI+Q,WAAW,GAAG,IAAI/Q,YAAY,CAAC,CAAC;EAChC;AACJ;AACA;AACA;AACA;EACIgR,SAAS,GAAG,IAAIhR,YAAY,CAAC,CAAC;EAC9B;AACJ;AACA;AACA;EACIiR,UAAU,GAAG,IAAIjR,YAAY,CAAC,CAAC;EAC/BkR,eAAe;EACfC,gBAAgB;EAChBC,eAAe;EACf;AACJ;AACA;AACA;EACI7H,cAAc;EACd;AACJ;AACA;AACA;EACIkB,eAAe;EACf;AACJ;AACA;AACA;EACIV,cAAc;EACd;AACJ;AACA;AACA;EACIzB,iBAAiB;EACjB;AACJ;AACA;AACA;EACI5B,oBAAoB;EACpB;AACJ;AACA;AACA;EACIG,oBAAoB;EACpB;AACJ;AACA;AACA;EACIhC,gBAAgB;EAChByE,eAAe;EACfkB,gBAAgB;EAChBV,eAAe;EACfzB,kBAAkB;EAClB5B,qBAAqB;EACrBG,qBAAqB;EACrBhC,iBAAiB;EACjB0L,QAAQ,GAAG,KAAK;EAChBC,WAAW;EACXc,SAAS;EACTC,OAAO;EACPC,QAAQ;EACR1L,cAAc,GAAG,IAAI,CAAC2L,iBAAiB,CAAC,CAAC;EACzCC,oBAAoB;EACpBC,uBAAuB;EACvBC,QAAQ;EACRC,sBAAsB;EACtBC,yBAAyB;EACzBC,sBAAsB;EACtBC,iBAAiB;EACjBC,SAAS;EACTC,SAAS;EACTC,+BAA+B;EAC/B/L,SAAS;EACTgM,wBAAwB;EACxBC,yBAAyB;EACzBC,0BAA0B;EAC1BC,gBAAgB;EAChBC,gBAAgB;EAChBC,EAAE,GAAG1R,IAAI,CAAC,QAAQ,CAAC;EACnB0P,MAAM,GAAG,CAAC,CAAC;EACXE,SAAS,GAAG,QAAQ;EACpBD,aAAa;EACbhF,gBAAgB,GAAG,YAAY;EAC/BgH,YAAY;EACZC,MAAM;EACNC,eAAe,GAAG1S,MAAM,CAACwN,WAAW,CAAC;EACrCjE,OAAO;EACPkB,QAAQ;EACRV,OAAO;EACPzB,UAAU;EACV5B,aAAa;EACbG,aAAa;EACbhC,SAAS;EACT,IAAI6C,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACiL,MAAM,CAACC,cAAc,CAACpR,eAAe,CAACqR,IAAI,CAAC,CAAC,eAAe,CAAC;EAC5E;EACAC,IAAI,GAAG9S,MAAM,CAACC,MAAM,CAAC;EACrB,IAAI6L,SAASA,CAAA,EAAG;IACZ,MAAMe,SAAS,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,YAAY,EAAE,aAAa,CAAC;IACxG,MAAMC,GAAG,GAAGD,SAAS,CAACE,IAAI,CAAEC,IAAI,IAAKA,IAAI,KAAK,IAAI,CAAC9J,QAAQ,CAAC;IAC5D,OAAO;MACH,eAAe,EAAE,IAAI;MACrB,qCAAqC,EAAE,IAAI,CAAC8I,KAAK,IAAI,IAAI,CAACmD,eAAe;MACzE,CAAC,YAAYrC,GAAG,EAAE,GAAGA;IACzB,CAAC;EACL;EACAiG,QAAQA,CAAA,EAAG;IACP,KAAK,CAACA,QAAQ,CAAC,CAAC;IAChB,IAAI,IAAI,CAACvD,WAAW,EAAE;MAClB,IAAI,CAACwD,WAAW,CAAC,CAAC;IACtB;EACJ;EACAC,SAAS;EACTC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACD,SAAS,EAAEE,OAAO,CAAEnG,IAAI,IAAK;MAC9B,QAAQA,IAAI,CAACoG,OAAO,CAAC,CAAC;QAClB,KAAK,QAAQ;UACT,IAAI,CAAC7J,OAAO,GAAGyD,IAAI,CAACqG,QAAQ;UAC5B;QACJ,KAAK,SAAS;UACV,IAAI,CAAC5I,QAAQ,GAAGuC,IAAI,CAACqG,QAAQ;UAC7B;QACJ,KAAK,QAAQ;UACT,IAAI,CAACtJ,OAAO,GAAGiD,IAAI,CAACqG,QAAQ;UAC5B;QACJ,KAAK,WAAW;UACZ,IAAI,CAAC/K,UAAU,GAAG0E,IAAI,CAACqG,QAAQ;UAC/B;QACJ,KAAK,cAAc;UACf,IAAI,CAAC3M,aAAa,GAAGsG,IAAI,CAACqG,QAAQ;UAClC;QACJ,KAAK,cAAc;UACf,IAAI,CAACxM,aAAa,GAAGmG,IAAI,CAACqG,QAAQ;UAClC;QACJ,KAAK,UAAU;UACX,IAAI,CAACxO,SAAS,GAAGmI,IAAI,CAACqG,QAAQ;UAC9B;QACJ;UACI,IAAI,CAAC5I,QAAQ,GAAGuC,IAAI,CAACqG,QAAQ;UAC7B;MACR;IACJ,CAAC,CAAC;EACN;EACA9B,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACzL,MAAM,KAAK,IAAI,GAAGjF,IAAI,CAAC,QAAQ,CAAC,GAAG,SAAS,GAAG,IAAI;EACnE;EACAyS,2BAA2BA,CAACC,cAAc,EAAE;IACxC,MAAMC,mBAAmB,GAAG,oBAAoB;IAChD,IAAIC,iBAAiB,GAAG,CAAC;IACzB,IAAIC,KAAK;IACT,OAAO,CAACA,KAAK,GAAGF,mBAAmB,CAACG,IAAI,CAACJ,cAAc,CAAC,MAAM,IAAI,EAAE;MAChE,MAAM1P,KAAK,GAAG+P,UAAU,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC;MAClC,MAAMG,IAAI,GAAGH,KAAK,CAAC,CAAC,CAAC;MACrB,IAAIG,IAAI,KAAK,IAAI,EAAE;QACfJ,iBAAiB,IAAI5P,KAAK;MAC9B,CAAC,MACI,IAAIgQ,IAAI,KAAK,GAAG,EAAE;QACnBJ,iBAAiB,IAAI5P,KAAK,GAAG,IAAI;MACrC;IACJ;IACA,IAAI4P,iBAAiB,KAAK,CAAC,EAAE;MACzB,OAAOK,SAAS;IACpB;IACA,OAAOL,iBAAiB;EAC5B;EACAM,MAAMA,CAACC,kBAAkB,EAAE;IACvB,IAAIA,kBAAkB,EAAE;MACpB,MAAMC,eAAe,GAAG,IAAI,CAACX,2BAA2B,CAAC,IAAI,CAAC7H,iBAAiB,CAAC;MAChF,IAAIyI,kBAAkB,GAAGrS,UAAU,CAACsS,oBAAoB,CAACH,kBAAkB,CAAC;MAC5E,IAAIE,kBAAkB,IAAIA,kBAAkB,CAACE,MAAM,GAAG,CAAC,EAAE;QACrD,IAAI,CAACtB,IAAI,CAACuB,iBAAiB,CAAC,MAAM;UAC9BC,UAAU,CAAC,MAAMJ,kBAAkB,CAAC,CAAC,CAAC,CAACK,KAAK,CAAC,CAAC,EAAEN,eAAe,IAAI,CAAC,CAAC;QACzE,CAAC,CAAC;QACF,OAAO,IAAI;MACf;IACJ;IACA,OAAO,KAAK;EAChB;EACAM,KAAKA,CAACP,kBAAkB,EAAE;IACtB,IAAIQ,OAAO,GAAG,IAAI,CAACT,MAAM,CAACC,kBAAkB,CAAC;IAC7C,IAAI,CAACQ,OAAO,EAAE;MACVA,OAAO,GAAG,IAAI,CAACT,MAAM,CAAC,IAAI,CAAC5C,eAAe,EAAEsD,aAAa,CAAC;MAC1D,IAAI,CAACD,OAAO,EAAE;QACVA,OAAO,GAAG,IAAI,CAACT,MAAM,CAAC,IAAI,CAAC9C,eAAe,EAAEwD,aAAa,CAAC;QAC1D,IAAI,CAACD,OAAO,EAAE;UACV,IAAI,CAACT,MAAM,CAAC,IAAI,CAAC7C,gBAAgB,EAAEuD,aAAa,CAAC;QACrD;MACJ;IACJ;EACJ;EACA7L,KAAKA,CAAC8L,KAAK,EAAE;IACT,IAAI,CAAC9D,aAAa,CAAC+D,IAAI,CAAC,KAAK,CAAC;IAC9BD,KAAK,CAACE,cAAc,CAAC,CAAC;EAC1B;EACAC,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACrL,QAAQ,IAAI,IAAI,CAAC2F,eAAe,EAAE;MACvC,IAAI,CAAC2C,iBAAiB,GAAG,IAAI,CAACgD,QAAQ,CAACC,MAAM,CAAC,IAAI,CAAC1D,OAAO,EAAE,WAAW,EAAGqD,KAAK,IAAK;QAChF,IAAI,IAAI,CAACrD,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC2D,UAAU,CAACN,KAAK,CAACO,MAAM,CAAC,EAAE;UACvD,IAAI,CAACrM,KAAK,CAAC8L,KAAK,CAAC;QACrB;MACJ,CAAC,CAAC;IACN;IACA,IAAI,IAAI,CAAC1I,KAAK,EAAE;MACZlL,eAAe,CAAC,CAAC;IACrB;EACJ;EACAoU,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAAC7D,OAAO,EAAE;MACd,IAAI,IAAI,CAAClC,eAAe,EAAE;QACtB,IAAI,CAACgG,uBAAuB,CAAC,CAAC;MAClC;MACA;MACA,MAAMC,cAAc,GAAGC,QAAQ,CAACC,gBAAgB,CAAC,8BAA8B,CAAC;MAChF,IAAI,IAAI,CAACtJ,KAAK,IAAIoJ,cAAc,IAAIA,cAAc,CAAChB,MAAM,IAAI,CAAC,EAAE;QAC5DrT,iBAAiB,CAAC,CAAC;MACvB;MACA,IAAI,CAAC,IAAI,CAACwU,EAAE,CAACC,SAAS,EAAE;QACpB,IAAI,CAACD,EAAE,CAACE,aAAa,CAAC,CAAC;MAC3B;IACJ;EACJ;EACAlO,QAAQA,CAAA,EAAG;IACP,IAAI,CAACrB,SAAS,GAAG,CAAC,IAAI,CAACA,SAAS;IAChC,IAAI,CAAC,IAAI,CAAC8F,KAAK,IAAI,CAAC,IAAI,CAAC2D,WAAW,EAAE;MAClC,IAAI,IAAI,CAACzJ,SAAS,EAAE;QAChBpF,eAAe,CAAC,CAAC;MACrB,CAAC,MACI;QACDC,iBAAiB,CAAC,CAAC;MACvB;IACJ;IACA,IAAI,CAACiQ,UAAU,CAAC2D,IAAI,CAAC;MAAEzO,SAAS,EAAE,IAAI,CAACA;IAAU,CAAC,CAAC;EACvD;EACAiP,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAACrD,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAAC,CAAC;MACxB,IAAI,CAACA,iBAAiB,GAAG,IAAI;IACjC;EACJ;EACA4D,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAAC9F,UAAU,EAAE;MACjB1N,WAAW,CAACyT,GAAG,CAAC,OAAO,EAAE,IAAI,CAACvE,SAAS,EAAE,IAAI,CAACvB,UAAU,GAAG,IAAI,CAAC8C,MAAM,CAACiD,MAAM,CAAC5J,KAAK,CAAC;MACpF,IAAI,CAACqF,OAAO,CAAChS,KAAK,CAACuW,MAAM,GAAGC,MAAM,CAACC,QAAQ,CAAC,IAAI,CAAC1E,SAAS,CAAC/R,KAAK,CAACuW,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;IACrF;EACJ;EACA5C,WAAWA,CAAA,EAAG;IACV,IAAIrT,iBAAiB,CAAC,IAAI,CAACoW,UAAU,CAAC,EAAE;MACpC,IAAI,CAAC,IAAI,CAACvD,YAAY,EAAE;QACpB,IAAI,CAACA,YAAY,GAAG,IAAI,CAACsC,QAAQ,CAACkB,aAAa,CAAC,OAAO,CAAC;QACxD,IAAI,CAACxD,YAAY,CAACnE,IAAI,GAAG,UAAU;QACnC,IAAI,CAACyG,QAAQ,CAACvT,WAAW,CAAC,IAAI,CAAC8T,QAAQ,CAACY,IAAI,EAAE,IAAI,CAACzD,YAAY,CAAC;QAChE,IAAI0D,SAAS,GAAG,EAAE;QAClB,KAAK,IAAIzG,UAAU,IAAI,IAAI,CAACD,WAAW,EAAE;UACrC0G,SAAS,IAAI;AACjC,wDAAwDzG,UAAU;AAClE,wCAAwC,IAAI,CAAC8C,EAAE;AAC/C,yCAAyC,IAAI,CAAC/C,WAAW,CAACC,UAAU,CAAC;AACrE;AACA;AACA,qBAAqB;QACL;QACA,IAAI,CAACqF,QAAQ,CAACqB,WAAW,CAAC,IAAI,CAAC3D,YAAY,EAAE,WAAW,EAAE0D,SAAS,CAAC;QACpElV,YAAY,CAAC,IAAI,CAACwR,YAAY,EAAE,OAAO,EAAE,IAAI,CAACG,MAAM,EAAEyD,GAAG,CAAC,CAAC,EAAEC,KAAK,CAAC;MACvE;IACJ;EACJ;EACAjN,QAAQA,CAACsL,KAAK,EAAE;IACZ,IAAIzT,QAAQ,CAACyT,KAAK,CAACO,MAAM,EAAE,wBAAwB,CAAC,IAAIhU,QAAQ,CAACyT,KAAK,CAACO,MAAM,EAAE,4BAA4B,CAAC,IAAIhU,QAAQ,CAACyT,KAAK,CAACO,MAAM,CAACqB,aAAa,EAAE,sBAAsB,CAAC,EAAE;MAC1K;IACJ;IACA,IAAI,IAAI,CAAC3H,SAAS,EAAE;MAChB,IAAI,CAAC2C,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACS,SAAS,GAAG2C,KAAK,CAAC6B,KAAK;MAC5B,IAAI,CAACvE,SAAS,GAAG0C,KAAK,CAAC8B,KAAK;MAC5B,IAAI,CAACpF,SAAS,CAAC/R,KAAK,CAACoX,MAAM,GAAG,GAAG;MACjCvV,QAAQ,CAAC,IAAI,CAACmU,QAAQ,CAACqB,IAAI,EAAE,qBAAqB,CAAC;IACvD;EACJ;EACAC,MAAMA,CAACjC,KAAK,EAAE;IACV,IAAI,IAAI,CAACpD,QAAQ,EAAE;MACf,MAAMsF,cAAc,GAAGzV,aAAa,CAAC,IAAI,CAACiQ,SAAS,CAAC;MACpD,MAAMyF,eAAe,GAAGzV,cAAc,CAAC,IAAI,CAACgQ,SAAS,CAAC;MACtD,MAAM0F,MAAM,GAAGpC,KAAK,CAAC6B,KAAK,GAAG,IAAI,CAACxE,SAAS;MAC3C,MAAMgF,MAAM,GAAGrC,KAAK,CAAC8B,KAAK,GAAG,IAAI,CAACxE,SAAS;MAC3C,MAAMgF,MAAM,GAAG,IAAI,CAAC5F,SAAS,CAAC6F,qBAAqB,CAAC,CAAC;MACrD,MAAMC,sBAAsB,GAAGC,gBAAgB,CAAC,IAAI,CAAC/F,SAAS,CAAC;MAC/D,MAAMgG,UAAU,GAAGxD,UAAU,CAACsD,sBAAsB,CAACG,UAAU,CAAC;MAChE,MAAMC,SAAS,GAAG1D,UAAU,CAACsD,sBAAsB,CAACK,SAAS,CAAC;MAC9D,MAAMC,OAAO,GAAGR,MAAM,CAAC3T,IAAI,GAAGyT,MAAM,GAAGM,UAAU;MACjD,MAAMK,MAAM,GAAGT,MAAM,CAAC1T,GAAG,GAAGyT,MAAM,GAAGO,SAAS;MAC9C,MAAMI,QAAQ,GAAGrW,WAAW,CAAC,CAAC;MAC9B,IAAI,CAAC+P,SAAS,CAAC/R,KAAK,CAAC6D,QAAQ,GAAG,OAAO;MACvC,IAAI,IAAI,CAAC+M,cAAc,EAAE;QACrB,IAAIuH,OAAO,IAAI,IAAI,CAAC1H,IAAI,IAAI0H,OAAO,GAAGZ,cAAc,GAAGc,QAAQ,CAACtU,KAAK,EAAE;UACnE,IAAI,CAACmN,MAAM,CAAClN,IAAI,GAAG,GAAGmU,OAAO,IAAI;UACjC,IAAI,CAACzF,SAAS,GAAG2C,KAAK,CAAC6B,KAAK;UAC5B,IAAI,CAACnF,SAAS,CAAC/R,KAAK,CAACgE,IAAI,GAAG,GAAGmU,OAAO,IAAI;QAC9C;QACA,IAAIC,MAAM,IAAI,IAAI,CAAC1H,IAAI,IAAI0H,MAAM,GAAGZ,eAAe,GAAGa,QAAQ,CAACvU,MAAM,EAAE;UACnE,IAAI,CAACoN,MAAM,CAACjN,GAAG,GAAG,GAAGmU,MAAM,IAAI;UAC/B,IAAI,CAACzF,SAAS,GAAG0C,KAAK,CAAC8B,KAAK;UAC5B,IAAI,CAACpF,SAAS,CAAC/R,KAAK,CAACiE,GAAG,GAAG,GAAGmU,MAAM,IAAI;QAC5C;MACJ,CAAC,MACI;QACD,IAAI,CAAC1F,SAAS,GAAG2C,KAAK,CAAC6B,KAAK;QAC5B,IAAI,CAACnF,SAAS,CAAC/R,KAAK,CAACgE,IAAI,GAAG,GAAGmU,OAAO,IAAI;QAC1C,IAAI,CAACxF,SAAS,GAAG0C,KAAK,CAAC8B,KAAK;QAC5B,IAAI,CAACpF,SAAS,CAAC/R,KAAK,CAACiE,GAAG,GAAG,GAAGmU,MAAM,IAAI;MAC5C;IACJ;EACJ;EACAE,OAAOA,CAACjD,KAAK,EAAE;IACX,IAAI,IAAI,CAACpD,QAAQ,EAAE;MACf,IAAI,CAACA,QAAQ,GAAG,KAAK;MACrBhQ,WAAW,CAAC,IAAI,CAAC+T,QAAQ,CAACqB,IAAI,EAAE,qBAAqB,CAAC;MACtD,IAAI,CAACnB,EAAE,CAACE,aAAa,CAAC,CAAC;MACvB,IAAI,CAAC1E,SAAS,CAAC4D,IAAI,CAACD,KAAK,CAAC;IAC9B;EACJ;EACAkD,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACxG,SAAS,CAAC/R,KAAK,CAAC6D,QAAQ,GAAG,EAAE;IAClC,IAAI,CAACkO,SAAS,CAAC/R,KAAK,CAACgE,IAAI,GAAG,EAAE;IAC9B,IAAI,CAAC+N,SAAS,CAAC/R,KAAK,CAACiE,GAAG,GAAG,EAAE;IAC7B,IAAI,CAAC8N,SAAS,CAAC/R,KAAK,CAACoX,MAAM,GAAG,EAAE;EACpC;EACA;EACAoB,MAAMA,CAAA,EAAG;IACL,IAAI,CAACD,aAAa,CAAC,CAAC;EACxB;EACArS,UAAUA,CAACmP,KAAK,EAAE;IACd,IAAI,IAAI,CAACzK,SAAS,EAAE;MAChB,IAAI,CAACyH,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACK,SAAS,GAAG2C,KAAK,CAAC6B,KAAK;MAC5B,IAAI,CAACvE,SAAS,GAAG0C,KAAK,CAAC8B,KAAK;MAC5BtV,QAAQ,CAAC,IAAI,CAACmU,QAAQ,CAACqB,IAAI,EAAE,qBAAqB,CAAC;MACnD,IAAI,CAAC7F,YAAY,CAAC8D,IAAI,CAACD,KAAK,CAAC;IACjC;EACJ;EACAoD,QAAQA,CAACpD,KAAK,EAAE;IACZ,IAAI,IAAI,CAAChD,QAAQ,EAAE;MACf,IAAIoF,MAAM,GAAGpC,KAAK,CAAC6B,KAAK,GAAG,IAAI,CAACxE,SAAS;MACzC,IAAIgF,MAAM,GAAGrC,KAAK,CAAC8B,KAAK,GAAG,IAAI,CAACxE,SAAS;MACzC,IAAI4E,cAAc,GAAGzV,aAAa,CAAC,IAAI,CAACiQ,SAAS,CAAC;MAClD,IAAIyF,eAAe,GAAGzV,cAAc,CAAC,IAAI,CAACgQ,SAAS,CAAC;MACpD,IAAI2G,aAAa,GAAG3W,cAAc,CAAC,IAAI,CAAC8P,gBAAgB,EAAEuD,aAAa,CAAC;MACxE,IAAIuD,QAAQ,GAAGpB,cAAc,GAAGE,MAAM;MACtC,IAAImB,SAAS,GAAGpB,eAAe,GAAGE,MAAM;MACxC,IAAImB,QAAQ,GAAG,IAAI,CAAC9G,SAAS,CAAC/R,KAAK,CAAC6Y,QAAQ;MAC5C,IAAIC,SAAS,GAAG,IAAI,CAAC/G,SAAS,CAAC/R,KAAK,CAAC8Y,SAAS;MAC9C,IAAInB,MAAM,GAAG,IAAI,CAAC5F,SAAS,CAAC6F,qBAAqB,CAAC,CAAC;MACnD,IAAIS,QAAQ,GAAGrW,WAAW,CAAC,CAAC;MAC5B,IAAI+W,cAAc,GAAG,CAACtC,QAAQ,CAAC,IAAI,CAAC1E,SAAS,CAAC/R,KAAK,CAACiE,GAAG,CAAC,IAAI,CAACwS,QAAQ,CAAC,IAAI,CAAC1E,SAAS,CAAC/R,KAAK,CAACgE,IAAI,CAAC;MAChG,IAAI+U,cAAc,EAAE;QAChBJ,QAAQ,IAAIlB,MAAM;QAClBmB,SAAS,IAAIlB,MAAM;MACvB;MACA,IAAI,CAAC,CAACmB,QAAQ,IAAIF,QAAQ,GAAGlC,QAAQ,CAACoC,QAAQ,CAAC,KAAKlB,MAAM,CAAC3T,IAAI,GAAG2U,QAAQ,GAAGN,QAAQ,CAACtU,KAAK,EAAE;QACzF,IAAI,CAACmN,MAAM,CAACnN,KAAK,GAAG4U,QAAQ,GAAG,IAAI;QACnC,IAAI,CAAC5G,SAAS,CAAC/R,KAAK,CAAC+D,KAAK,GAAG,IAAI,CAACmN,MAAM,CAACnN,KAAK;MAClD;MACA,IAAI,CAAC,CAAC+U,SAAS,IAAIF,SAAS,GAAGnC,QAAQ,CAACqC,SAAS,CAAC,KAAKnB,MAAM,CAAC1T,GAAG,GAAG2U,SAAS,GAAGP,QAAQ,CAACvU,MAAM,EAAE;QAC7F,IAAI,CAAC+N,gBAAgB,CAACuD,aAAa,CAACpV,KAAK,CAAC8D,MAAM,GAAG4U,aAAa,GAAGE,SAAS,GAAGpB,eAAe,GAAG,IAAI;QACrG,IAAI,IAAI,CAACtG,MAAM,CAACpN,MAAM,EAAE;UACpB,IAAI,CAACoN,MAAM,CAACpN,MAAM,GAAG8U,SAAS,GAAG,IAAI;UACrC,IAAI,CAAC7G,SAAS,CAAC/R,KAAK,CAAC8D,MAAM,GAAG,IAAI,CAACoN,MAAM,CAACpN,MAAM;QACpD;MACJ;MACA,IAAI,CAAC4O,SAAS,GAAG2C,KAAK,CAAC6B,KAAK;MAC5B,IAAI,CAACvE,SAAS,GAAG0C,KAAK,CAAC8B,KAAK;IAChC;EACJ;EACA6B,SAASA,CAAC3D,KAAK,EAAE;IACb,IAAI,IAAI,CAAChD,QAAQ,EAAE;MACf,IAAI,CAACA,QAAQ,GAAG,KAAK;MACrBpQ,WAAW,CAAC,IAAI,CAAC+T,QAAQ,CAACqB,IAAI,EAAE,qBAAqB,CAAC;MACtD,IAAI,CAAC5F,WAAW,CAAC6D,IAAI,CAACD,KAAK,CAAC;IAChC;EACJ;EACA4D,mBAAmBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAAC3J,SAAS,EAAE;MAChB,IAAI,CAAC4J,wBAAwB,CAAC,CAAC;MAC/B,IAAI,CAACC,2BAA2B,CAAC,CAAC;IACtC;IACA,IAAI,IAAI,CAACvO,SAAS,EAAE;MAChB,IAAI,CAACwO,2BAA2B,CAAC,CAAC;IACtC;IACA,IAAI,IAAI,CAACvJ,aAAa,IAAI,IAAI,CAAC1F,QAAQ,EAAE;MACrC,IAAI,CAACkP,0BAA0B,CAAC,CAAC;IACrC;EACJ;EACAC,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACC,0BAA0B,CAAC,CAAC;IACjC,IAAI,CAACC,6BAA6B,CAAC,CAAC;IACpC,IAAI,CAACC,6BAA6B,CAAC,CAAC;IACpC,IAAI,CAACC,4BAA4B,CAAC,CAAC;EACvC;EACAR,wBAAwBA,CAAA,EAAG;IACvB,IAAI,CAAC,IAAI,CAAC/G,oBAAoB,EAAE;MAC5B,IAAI,CAACsB,IAAI,CAACuB,iBAAiB,CAAC,MAAM;QAC9B,IAAI,CAAC7C,oBAAoB,GAAG,IAAI,CAACsD,QAAQ,CAACC,MAAM,CAAC,IAAI,CAACM,QAAQ,CAAC2D,WAAW,EAAE,WAAW,EAAE,IAAI,CAACrC,MAAM,CAACsC,IAAI,CAAC,IAAI,CAAC,CAAC;MACpH,CAAC,CAAC;IACN;EACJ;EACAL,0BAA0BA,CAAA,EAAG;IACzB,IAAI,IAAI,CAACpH,oBAAoB,EAAE;MAC3B,IAAI,CAACA,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAACA,oBAAoB,GAAG,IAAI;IACpC;EACJ;EACAgH,2BAA2BA,CAAA,EAAG;IAC1B,IAAI,CAAC,IAAI,CAAC/G,uBAAuB,EAAE;MAC/B,IAAI,CAACqB,IAAI,CAACuB,iBAAiB,CAAC,MAAM;QAC9B,IAAI,CAAC5C,uBAAuB,GAAG,IAAI,CAACqD,QAAQ,CAACC,MAAM,CAAC,IAAI,CAACM,QAAQ,CAAC2D,WAAW,EAAE,SAAS,EAAE,IAAI,CAACrB,OAAO,CAACsB,IAAI,CAAC,IAAI,CAAC,CAAC;MACtH,CAAC,CAAC;IACN;EACJ;EACAJ,6BAA6BA,CAAA,EAAG;IAC5B,IAAI,IAAI,CAACpH,uBAAuB,EAAE;MAC9B,IAAI,CAACA,uBAAuB,CAAC,CAAC;MAC9B,IAAI,CAACA,uBAAuB,GAAG,IAAI;IACvC;EACJ;EACAgH,2BAA2BA,CAAA,EAAG;IAC1B,IAAI,CAAC,IAAI,CAAC9G,sBAAsB,IAAI,CAAC,IAAI,CAACC,yBAAyB,EAAE;MACjE,IAAI,CAACkB,IAAI,CAACuB,iBAAiB,CAAC,MAAM;QAC9B,IAAI,CAAC1C,sBAAsB,GAAG,IAAI,CAACmD,QAAQ,CAACC,MAAM,CAAC,IAAI,CAACM,QAAQ,CAAC2D,WAAW,EAAE,WAAW,EAAE,IAAI,CAAClB,QAAQ,CAACmB,IAAI,CAAC,IAAI,CAAC,CAAC;QACpH,IAAI,CAACrH,yBAAyB,GAAG,IAAI,CAACkD,QAAQ,CAACC,MAAM,CAAC,IAAI,CAACM,QAAQ,CAAC2D,WAAW,EAAE,SAAS,EAAE,IAAI,CAACX,SAAS,CAACY,IAAI,CAAC,IAAI,CAAC,CAAC;MAC1H,CAAC,CAAC;IACN;EACJ;EACAH,6BAA6BA,CAAA,EAAG;IAC5B,IAAI,IAAI,CAACnH,sBAAsB,IAAI,IAAI,CAACC,yBAAyB,EAAE;MAC/D,IAAI,CAACD,sBAAsB,CAAC,CAAC;MAC7B,IAAI,CAACC,yBAAyB,CAAC,CAAC;MAChC,IAAI,CAACD,sBAAsB,GAAG,IAAI;MAClC,IAAI,CAACC,yBAAyB,GAAG,IAAI;IACzC;EACJ;EACA8G,0BAA0BA,CAAA,EAAG;IACzB,MAAMQ,cAAc,GAAG,IAAI,CAACC,EAAE,GAAG,IAAI,CAACA,EAAE,CAAC1E,aAAa,CAAC2E,aAAa,GAAG,UAAU;IACjF,IAAI,CAACvH,sBAAsB,GAAG,IAAI,CAACiD,QAAQ,CAACC,MAAM,CAACmE,cAAc,EAAE,SAAS,EAAGxE,KAAK,IAAK;MACrF,IAAIA,KAAK,CAAC2E,GAAG,IAAI,QAAQ,EAAE;QACvB,IAAI,CAACzQ,KAAK,CAAC8L,KAAK,CAAC;MACrB;IACJ,CAAC,CAAC;EACN;EACAqE,4BAA4BA,CAAA,EAAG;IAC3B,IAAI,IAAI,CAAClH,sBAAsB,EAAE;MAC7B,IAAI,CAACA,sBAAsB,CAAC,CAAC;MAC7B,IAAI,CAACA,sBAAsB,GAAG,IAAI;IACtC;EACJ;EACAyH,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAAC/J,QAAQ,EAAE;MACf,IAAI,IAAI,CAACA,QAAQ,KAAK,MAAM,EACxB,IAAI,CAACuF,QAAQ,CAACvT,WAAW,CAAC,IAAI,CAAC8T,QAAQ,CAACqB,IAAI,EAAE,IAAI,CAACrF,OAAO,CAAC,CAAC,KAE5D9P,WAAW,CAAC,IAAI,CAACgO,QAAQ,EAAE,IAAI,CAAC8B,OAAO,CAAC;IAChD;EACJ;EACAkI,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACnI,SAAS,IAAI,IAAI,CAAC7B,QAAQ,EAAE;MACjC,IAAI,CAACuF,QAAQ,CAACvT,WAAW,CAAC,IAAI,CAAC4X,EAAE,CAAC1E,aAAa,EAAE,IAAI,CAACpD,OAAO,CAAC;IAClE;EACJ;EACAxG,gBAAgBA,CAAC6J,KAAK,EAAE;IACpB,QAAQA,KAAK,CAAC8E,OAAO;MACjB,KAAK,SAAS;QACV,IAAI,CAACpI,SAAS,GAAGsD,KAAK,CAAC+E,OAAO;QAC9B,IAAI,CAACpI,OAAO,GAAG,IAAI,CAACD,SAAS,EAAEkF,aAAa;QAC5C,IAAI,CAACgD,eAAe,CAAC,CAAC;QACtB,IAAI,CAAC5D,SAAS,CAAC,CAAC;QAChB,IAAI,CAAC4C,mBAAmB,CAAC,CAAC;QAC1B,IAAI,CAAClH,SAAS,EAAEpQ,YAAY,CAAC,IAAI,CAACuR,EAAE,EAAE,EAAE,CAAC;QACzC,IAAI,IAAI,CAACvG,KAAK,EAAE;UACZ,IAAI,CAAC6I,cAAc,CAAC,CAAC;QACzB;QACA;QACA;QACA;QACA,IAAI,IAAI,CAAC7E,WAAW,EAAE;UAClB,IAAI,CAACuE,KAAK,CAAC,CAAC;QAChB;QACA;MACJ,KAAK,MAAM;QACP,IAAI,IAAI,CAAClD,OAAO,IAAI,IAAI,CAACrF,KAAK,EAAE;UAC5B9K,QAAQ,CAAC,IAAI,CAACmQ,OAAO,EAAE,sBAAsB,CAAC;QAClD;QACA;IACR;EACJ;EACAtG,cAAcA,CAAC2J,KAAK,EAAE;IAClB,QAAQA,KAAK,CAAC8E,OAAO;MACjB,KAAK,MAAM;QACP,IAAI,CAACE,kBAAkB,CAAC,CAAC;QACzB,IAAI,CAAC/I,MAAM,CAACgE,IAAI,CAAC,CAAC,CAAC,CAAC;QACpB,IAAI,CAACY,EAAE,CAACoE,YAAY,CAAC,CAAC;QACtB,IAAI,IAAI,CAACrJ,WAAW,KAAK,IAAI,CAACrE,OAAO,EAAE;UACnC,IAAI,CAACqE,WAAW,GAAG,IAAI,CAACrE,OAAO;QACnC;QACA;MACJ,KAAK,SAAS;QACV,IAAI,CAACyE,MAAM,CAACiE,IAAI,CAAC,CAAC,CAAC,CAAC;QACpB;IACR;EACJ;EACA+E,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACf,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAACrH,QAAQ,GAAG,KAAK;IACrB,IAAI,CAAChB,WAAW,GAAG,KAAK;IACxB,IAAI,IAAI,CAACpK,SAAS,EAAE;MAChB;MACA,IAAI,CAACmP,QAAQ,CAACqB,IAAI,CAACrX,KAAK,CAACua,cAAc,CAAC,oBAAoB,CAAC;MAC7D,IAAI,CAAC1T,SAAS,GAAG,KAAK;IAC1B;IACA,IAAI,IAAI,CAAC8F,KAAK,EAAE;MACZ,IAAI,CAACkJ,eAAe,CAAC,CAAC;IAC1B;IACA;IACA;IACA;IACA,IAAIjU,QAAQ,CAAC,IAAI,CAACoU,QAAQ,CAACqB,IAAI,EAAE,mBAAmB,CAAC,EAAE;MACnDpV,WAAW,CAAC,IAAI,CAAC+T,QAAQ,CAACqB,IAAI,EAAE,mBAAmB,CAAC;IACxD;IACA,IAAI,IAAI,CAACtF,SAAS,IAAI,IAAI,CAACxB,UAAU,EAAE;MACnC1N,WAAW,CAAC2X,KAAK,CAAC,IAAI,CAACzI,SAAS,CAAC;IACrC;IACA,IAAI,CAACA,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACd,MAAM,GAAG,IAAI,CAACC,aAAa,GAAG;MAAE,GAAG,IAAI,CAACA;IAAc,CAAC,GAAG,CAAC,CAAC;EACrE;EACAsJ,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACtH,YAAY,EAAE;MACnB,IAAI,CAACsC,QAAQ,CAACiF,WAAW,CAAC,IAAI,CAAC1E,QAAQ,CAACY,IAAI,EAAE,IAAI,CAACzD,YAAY,CAAC;MAChE,IAAI,CAACA,YAAY,GAAG,IAAI;IAC5B;EACJ;EACAwH,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC5I,SAAS,EAAE;MAChB,IAAI,CAACmI,aAAa,CAAC,CAAC;MACpB,IAAI,CAACG,kBAAkB,CAAC,CAAC;IAC7B;IACA,IAAI,CAACI,YAAY,CAAC,CAAC;IACnB,KAAK,CAACE,WAAW,CAAC,CAAC;EACvB;EACA,OAAOtM,IAAI;IAAA,IAAAuM,mBAAA;IAAA,gBAAAC,eAAArM,iBAAA;MAAA,QAAAoM,mBAAA,KAAAA,mBAAA,GAn5B+Epa,EAAE,CAAAiO,qBAAA,CAm5BQY,MAAM,IAAAb,iBAAA,IAANa,MAAM;IAAA;EAAA;EAC1G,OAAOyL,IAAI,kBAp5B+Eta,EAAE,CAAAua,iBAAA;IAAA/L,IAAA,EAo5BJK,MAAM;IAAA2L,SAAA;IAAAC,cAAA,WAAAC,sBAAAvW,EAAA,EAAAC,GAAA,EAAAuW,QAAA;MAAA,IAAAxW,EAAA;QAp5BJnE,EAAE,CAAA4a,cAAA,CAAAD,QAAA,EAAApY,GAAA;QAAFvC,EAAE,CAAA4a,cAAA,CAAAD,QAAA,EAAAnY,GAAA;QAAFxC,EAAE,CAAA4a,cAAA,CAAAD,QAAA,EAAAlY,GAAA;QAAFzC,EAAE,CAAA4a,cAAA,CAAAD,QAAA,EAAAjY,GAAA;QAAF1C,EAAE,CAAA4a,cAAA,CAAAD,QAAA,EAAAhY,GAAA;QAAF3C,EAAE,CAAA4a,cAAA,CAAAD,QAAA,EAAA/X,GAAA;QAAF5C,EAAE,CAAA4a,cAAA,CAAAD,QAAA,EAAA9X,GAAA;QAAF7C,EAAE,CAAA4a,cAAA,CAAAD,QAAA,EAo5BwvF9Y,aAAa;MAAA;MAAA,IAAAsC,EAAA;QAAA,IAAA0W,EAAA;QAp5BvwF7a,EAAE,CAAA8a,cAAA,CAAAD,EAAA,GAAF7a,EAAE,CAAA+a,WAAA,QAAA3W,GAAA,CAAAoF,eAAA,GAAAqR,EAAA,CAAAG,KAAA;QAAFhb,EAAE,CAAA8a,cAAA,CAAAD,EAAA,GAAF7a,EAAE,CAAA+a,WAAA,QAAA3W,GAAA,CAAAsG,gBAAA,GAAAmQ,EAAA,CAAAG,KAAA;QAAFhb,EAAE,CAAA8a,cAAA,CAAAD,EAAA,GAAF7a,EAAE,CAAA+a,WAAA,QAAA3W,GAAA,CAAA4F,eAAA,GAAA6Q,EAAA,CAAAG,KAAA;QAAFhb,EAAE,CAAA8a,cAAA,CAAAD,EAAA,GAAF7a,EAAE,CAAA+a,WAAA,QAAA3W,GAAA,CAAAmE,kBAAA,GAAAsS,EAAA,CAAAG,KAAA;QAAFhb,EAAE,CAAA8a,cAAA,CAAAD,EAAA,GAAF7a,EAAE,CAAA+a,WAAA,QAAA3W,GAAA,CAAAuC,qBAAA,GAAAkU,EAAA,CAAAG,KAAA;QAAFhb,EAAE,CAAA8a,cAAA,CAAAD,EAAA,GAAF7a,EAAE,CAAA+a,WAAA,QAAA3W,GAAA,CAAA0C,qBAAA,GAAA+T,EAAA,CAAAG,KAAA;QAAFhb,EAAE,CAAA8a,cAAA,CAAAD,EAAA,GAAF7a,EAAE,CAAA+a,WAAA,QAAA3W,GAAA,CAAAU,iBAAA,GAAA+V,EAAA,CAAAG,KAAA;QAAFhb,EAAE,CAAA8a,cAAA,CAAAD,EAAA,GAAF7a,EAAE,CAAA+a,WAAA,QAAA3W,GAAA,CAAAgP,SAAA,GAAAyH,EAAA;MAAA;IAAA;IAAAI,SAAA,WAAAC,aAAA/W,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFnE,EAAE,CAAAmb,WAAA,CAAArY,GAAA;QAAF9C,EAAE,CAAAmb,WAAA,CAAA3Y,GAAA;QAAFxC,EAAE,CAAAmb,WAAA,CAAA1Y,GAAA;MAAA;MAAA,IAAA0B,EAAA;QAAA,IAAA0W,EAAA;QAAF7a,EAAE,CAAA8a,cAAA,CAAAD,EAAA,GAAF7a,EAAE,CAAA+a,WAAA,QAAA3W,GAAA,CAAAgN,eAAA,GAAAyJ,EAAA,CAAAG,KAAA;QAAFhb,EAAE,CAAA8a,cAAA,CAAAD,EAAA,GAAF7a,EAAE,CAAA+a,WAAA,QAAA3W,GAAA,CAAAiN,gBAAA,GAAAwJ,EAAA,CAAAG,KAAA;QAAFhb,EAAE,CAAA8a,cAAA,CAAAD,EAAA,GAAF7a,EAAE,CAAA+a,WAAA,QAAA3W,GAAA,CAAAkN,eAAA,GAAAuJ,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAAI,MAAA;MAAAnV,MAAA;MAAA6I,SAAA,gCAo5BgHzO,gBAAgB;MAAA+J,SAAA,gCAAyC/J,gBAAgB;MAAA0O,YAAA;MAAAI,WAAA;MAAA3E,YAAA;MAAAD,iBAAA;MAAA4B,KAAA,wBAA6J9L,gBAAgB;MAAAgP,aAAA,wCAAqDhP,gBAAgB;MAAAiP,eAAA,4CAA2DjP,gBAAgB;MAAAkP,GAAA,oBAAuBlP,gBAAgB;MAAAsJ,QAAA,8BAAsCtJ,gBAAgB;MAAAmP,UAAA;MAAAE,QAAA;MAAAC,WAAA;MAAArE,UAAA;MAAAU,cAAA;MAAAD,SAAA;MAAA1B,UAAA,kCAA4MhK,gBAAgB;MAAAuP,UAAA;MAAAE,WAAA,oCAAyEzP,gBAAgB;MAAA0P,UAAA,kCAA4C1P,gBAAgB;MAAA2P,UAAA,kCAA4C1P,eAAe;MAAA2P,IAAA,sBAA0B3P,eAAe;MAAA4P,IAAA,sBAA0B5P,eAAe;MAAA6P,WAAA,oCAA+C9P,gBAAgB;MAAAuH,WAAA,oCAA+CvH,gBAAgB;MAAA+P,cAAA,0CAAwD/P,gBAAgB;MAAAoL,SAAA,gCAAyCpL,gBAAgB;MAAAuL,iBAAA;MAAA3D,SAAA;MAAAiB,cAAA;MAAAC,aAAA;MAAA7C,YAAA;MAAAC,YAAA;MAAAoC,gBAAA;MAAAb,mBAAA;MAAAsE,OAAA;MAAA5M,KAAA;MAAA6D,QAAA;MAAAwI,IAAA;MAAApC,cAAA;MAAAkB,eAAA;MAAAV,cAAA;MAAAzB,iBAAA;MAAA5B,oBAAA;MAAAG,oBAAA;MAAAhC,gBAAA;IAAA;IAAAsW,OAAA;MAAAxK,MAAA;MAAAC,MAAA;MAAAC,aAAA;MAAAC,YAAA;MAAAC,WAAA;MAAAC,SAAA;MAAAC,UAAA;IAAA;IAAAmK,QAAA,GAp5Bl1Ctb,EAAE,CAAAub,kBAAA,CAo5BopE,CAAC5N,WAAW,CAAC,GAp5BnqE3N,EAAE,CAAAwb,0BAAA;IAAAC,kBAAA,EAAAzY,GAAA;IAAA0Y,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAApI,QAAA,WAAAqI,gBAAA1X,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFnE,EAAE,CAAA8b,eAAA,CAAA/Y,GAAA;QAAF/C,EAAE,CAAAwE,UAAA,IAAAsH,qBAAA,iBAq6BxF,CAAC;MAAA;MAAA,IAAA3H,EAAA;QAr6BqFnE,EAAE,CAAA6E,UAAA,SAAAT,GAAA,CAAAqM,WAs5BnE,CAAC;MAAA;IAAA;IAAAsL,YAAA,GAkFkChc,YAAY,EAA+BF,EAAE,CAACmc,OAAO,EAAoFnc,EAAE,CAACoc,IAAI,EAA6Fpc,EAAE,CAACqc,gBAAgB,EAAoJrc,EAAE,CAACsc,OAAO,EAA2Epa,MAAM,EAA0YE,SAAS,EAAyFC,SAAS,EAAsDC,kBAAkB,EAA+DC,kBAAkB,EAA8DR,YAAY;IAAAwa,aAAA;IAAAC,IAAA;MAAA9c,SAAA,EAAiB,CAACG,OAAO,CAAC,WAAW,EAAE,CAACC,UAAU,CAAC,iBAAiB,EAAE,CAACC,YAAY,CAAC8O,aAAa,CAAC,CAAC,CAAC,EAAE/O,UAAU,CAAC,iBAAiB,EAAE,CAACC,YAAY,CAACgP,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;IAAC;IAAA0N,eAAA;EAAA;AAC56C;AACA;EAAA,QAAAhO,SAAA,oBAAAA,SAAA,KA1+B8FtO,EAAE,CAAAuO,iBAAA,CA0+BJM,MAAM,EAAc,CAAC;IACrGL,IAAI,EAAE1N,SAAS;IACfyb,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,UAAU;MACpBC,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE,CAAC3c,YAAY,EAAEgC,MAAM,EAAEE,SAAS,EAAEC,SAAS,EAAEC,kBAAkB,EAAEC,kBAAkB,EAAER,YAAY,CAAC;MAC3G4R,QAAQ,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;MACemJ,UAAU,EAAE,CAACjd,OAAO,CAAC,WAAW,EAAE,CAACC,UAAU,CAAC,iBAAiB,EAAE,CAACC,YAAY,CAAC8O,aAAa,CAAC,CAAC,CAAC,EAAE/O,UAAU,CAAC,iBAAiB,EAAE,CAACC,YAAY,CAACgP,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChK0N,eAAe,EAAEzb,uBAAuB,CAAC+b,MAAM;MAC/CR,aAAa,EAAExb,iBAAiB,CAACic,IAAI;MACrCC,SAAS,EAAE,CAACnP,WAAW;IAC3B,CAAC;EACT,CAAC,CAAC,QAAkB;IAAE1H,MAAM,EAAE,CAAC;MACvBuI,IAAI,EAAE/N;IACV,CAAC,CAAC;IAAEqO,SAAS,EAAE,CAAC;MACZN,IAAI,EAAE/N,KAAK;MACX8b,IAAI,EAAE,CAAC;QAAEzY,SAAS,EAAEzD;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE+J,SAAS,EAAE,CAAC;MACZoE,IAAI,EAAE/N,KAAK;MACX8b,IAAI,EAAE,CAAC;QAAEzY,SAAS,EAAEzD;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE0O,YAAY,EAAE,CAAC;MACfP,IAAI,EAAE/N;IACV,CAAC,CAAC;IAAE0O,WAAW,EAAE,CAAC;MACdX,IAAI,EAAE/N;IACV,CAAC,CAAC;IAAE+J,YAAY,EAAE,CAAC;MACfgE,IAAI,EAAE/N;IACV,CAAC,CAAC;IAAE8J,iBAAiB,EAAE,CAAC;MACpBiE,IAAI,EAAE/N;IACV,CAAC,CAAC;IAAE0L,KAAK,EAAE,CAAC;MACRqC,IAAI,EAAE/N,KAAK;MACX8b,IAAI,EAAE,CAAC;QAAEzY,SAAS,EAAEzD;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEgP,aAAa,EAAE,CAAC;MAChBb,IAAI,EAAE/N,KAAK;MACX8b,IAAI,EAAE,CAAC;QAAEzY,SAAS,EAAEzD;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEiP,eAAe,EAAE,CAAC;MAClBd,IAAI,EAAE/N,KAAK;MACX8b,IAAI,EAAE,CAAC;QAAEzY,SAAS,EAAEzD;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEkP,GAAG,EAAE,CAAC;MACNf,IAAI,EAAE/N,KAAK;MACX8b,IAAI,EAAE,CAAC;QAAEzY,SAAS,EAAEzD;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEsJ,QAAQ,EAAE,CAAC;MACX6E,IAAI,EAAE/N,KAAK;MACX8b,IAAI,EAAE,CAAC;QAAEzY,SAAS,EAAEzD;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEmP,UAAU,EAAE,CAAC;MACbhB,IAAI,EAAE/N;IACV,CAAC,CAAC;IAAEiP,QAAQ,EAAE,CAAC;MACXlB,IAAI,EAAE/N;IACV,CAAC,CAAC;IAAEkP,WAAW,EAAE,CAAC;MACdnB,IAAI,EAAE/N;IACV,CAAC,CAAC;IAAE6K,UAAU,EAAE,CAAC;MACbkD,IAAI,EAAE/N;IACV,CAAC,CAAC;IAAEuL,cAAc,EAAE,CAAC;MACjBwC,IAAI,EAAE/N;IACV,CAAC,CAAC;IAAEsL,SAAS,EAAE,CAAC;MACZyC,IAAI,EAAE/N;IACV,CAAC,CAAC;IAAE4J,UAAU,EAAE,CAAC;MACbmE,IAAI,EAAE/N,KAAK;MACX8b,IAAI,EAAE,CAAC;QAAEzY,SAAS,EAAEzD;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEuP,UAAU,EAAE,CAAC;MACbpB,IAAI,EAAE/N;IACV,CAAC,CAAC;IAAEqP,WAAW,EAAE,CAAC;MACdtB,IAAI,EAAE/N,KAAK;MACX8b,IAAI,EAAE,CAAC;QAAEzY,SAAS,EAAEzD;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE0P,UAAU,EAAE,CAAC;MACbvB,IAAI,EAAE/N,KAAK;MACX8b,IAAI,EAAE,CAAC;QAAEzY,SAAS,EAAEzD;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE2P,UAAU,EAAE,CAAC;MACbxB,IAAI,EAAE/N,KAAK;MACX8b,IAAI,EAAE,CAAC;QAAEzY,SAAS,EAAExD;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAE2P,IAAI,EAAE,CAAC;MACPzB,IAAI,EAAE/N,KAAK;MACX8b,IAAI,EAAE,CAAC;QAAEzY,SAAS,EAAExD;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAE4P,IAAI,EAAE,CAAC;MACP1B,IAAI,EAAE/N,KAAK;MACX8b,IAAI,EAAE,CAAC;QAAEzY,SAAS,EAAExD;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAE6P,WAAW,EAAE,CAAC;MACd3B,IAAI,EAAE/N,KAAK;MACX8b,IAAI,EAAE,CAAC;QAAEzY,SAAS,EAAEzD;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEuH,WAAW,EAAE,CAAC;MACd4G,IAAI,EAAE/N,KAAK;MACX8b,IAAI,EAAE,CAAC;QAAEzY,SAAS,EAAEzD;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE+P,cAAc,EAAE,CAAC;MACjB5B,IAAI,EAAE/N,KAAK;MACX8b,IAAI,EAAE,CAAC;QAAEzY,SAAS,EAAEzD;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEoL,SAAS,EAAE,CAAC;MACZ+C,IAAI,EAAE/N,KAAK;MACX8b,IAAI,EAAE,CAAC;QAAEzY,SAAS,EAAEzD;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEuL,iBAAiB,EAAE,CAAC;MACpB4C,IAAI,EAAE/N;IACV,CAAC,CAAC;IAAEwH,SAAS,EAAE,CAAC;MACZuG,IAAI,EAAE/N;IACV,CAAC,CAAC;IAAEyI,cAAc,EAAE,CAAC;MACjBsF,IAAI,EAAE/N;IACV,CAAC,CAAC;IAAE0I,aAAa,EAAE,CAAC;MAChBqF,IAAI,EAAE/N;IACV,CAAC,CAAC;IAAE6F,YAAY,EAAE,CAAC;MACfkI,IAAI,EAAE/N;IACV,CAAC,CAAC;IAAE8F,YAAY,EAAE,CAAC;MACfiI,IAAI,EAAE/N;IACV,CAAC,CAAC;IAAEkI,gBAAgB,EAAE,CAAC;MACnB6F,IAAI,EAAE/N;IACV,CAAC,CAAC;IAAEqH,mBAAmB,EAAE,CAAC;MACtB0G,IAAI,EAAE/N;IACV,CAAC,CAAC;IAAE2L,OAAO,EAAE,CAAC;MACVoC,IAAI,EAAE/N;IACV,CAAC,CAAC;IAAEjB,KAAK,EAAE,CAAC;MACRgP,IAAI,EAAE/N;IACV,CAAC,CAAC;IAAE4C,QAAQ,EAAE,CAAC;MACXmL,IAAI,EAAE/N;IACV,CAAC,CAAC;IAAEoL,IAAI,EAAE,CAAC;MACP2C,IAAI,EAAE/N;IACV,CAAC,CAAC;IAAEoQ,MAAM,EAAE,CAAC;MACTrC,IAAI,EAAE7N;IACV,CAAC,CAAC;IAAEmQ,MAAM,EAAE,CAAC;MACTtC,IAAI,EAAE7N;IACV,CAAC,CAAC;IAAEoQ,aAAa,EAAE,CAAC;MAChBvC,IAAI,EAAE7N;IACV,CAAC,CAAC;IAAEqQ,YAAY,EAAE,CAAC;MACfxC,IAAI,EAAE7N;IACV,CAAC,CAAC;IAAEsQ,WAAW,EAAE,CAAC;MACdzC,IAAI,EAAE7N;IACV,CAAC,CAAC;IAAEuQ,SAAS,EAAE,CAAC;MACZ1C,IAAI,EAAE7N;IACV,CAAC,CAAC;IAAEwQ,UAAU,EAAE,CAAC;MACb3C,IAAI,EAAE7N;IACV,CAAC,CAAC;IAAEyQ,eAAe,EAAE,CAAC;MAClB5C,IAAI,EAAE9N,SAAS;MACf6b,IAAI,EAAE,CAAC,UAAU;IACrB,CAAC,CAAC;IAAElL,gBAAgB,EAAE,CAAC;MACnB7C,IAAI,EAAE9N,SAAS;MACf6b,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAEjL,eAAe,EAAE,CAAC;MAClB9C,IAAI,EAAE9N,SAAS;MACf6b,IAAI,EAAE,CAAC,QAAQ;IACnB,CAAC,CAAC;IAAE9S,cAAc,EAAE,CAAC;MACjB+E,IAAI,EAAE/N,KAAK;MACX8b,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAE5R,eAAe,EAAE,CAAC;MAClB6D,IAAI,EAAE/N;IACV,CAAC,CAAC;IAAEwJ,cAAc,EAAE,CAAC;MACjBuE,IAAI,EAAE/N;IACV,CAAC,CAAC;IAAE+H,iBAAiB,EAAE,CAAC;MACpBgG,IAAI,EAAE/N;IACV,CAAC,CAAC;IAAEmG,oBAAoB,EAAE,CAAC;MACvB4H,IAAI,EAAE/N;IACV,CAAC,CAAC;IAAEsG,oBAAoB,EAAE,CAAC;MACvByH,IAAI,EAAE/N;IACV,CAAC,CAAC;IAAEsE,gBAAgB,EAAE,CAAC;MACnByJ,IAAI,EAAE/N;IACV,CAAC,CAAC;IAAE+I,eAAe,EAAE,CAAC;MAClBgF,IAAI,EAAEhO,YAAY;MAClB+b,IAAI,EAAE,CAAC,QAAQ,EAAE;QAAEQ,WAAW,EAAE;MAAM,CAAC;IAC3C,CAAC,CAAC;IAAErS,gBAAgB,EAAE,CAAC;MACnB8D,IAAI,EAAEhO,YAAY;MAClB+b,IAAI,EAAE,CAAC,SAAS,EAAE;QAAEQ,WAAW,EAAE;MAAM,CAAC;IAC5C,CAAC,CAAC;IAAE/S,eAAe,EAAE,CAAC;MAClBwE,IAAI,EAAEhO,YAAY;MAClB+b,IAAI,EAAE,CAAC,QAAQ,EAAE;QAAEQ,WAAW,EAAE;MAAM,CAAC;IAC3C,CAAC,CAAC;IAAExU,kBAAkB,EAAE,CAAC;MACrBiG,IAAI,EAAEhO,YAAY;MAClB+b,IAAI,EAAE,CAAC,WAAW,EAAE;QAAEQ,WAAW,EAAE;MAAM,CAAC;IAC9C,CAAC,CAAC;IAAEpW,qBAAqB,EAAE,CAAC;MACxB6H,IAAI,EAAEhO,YAAY;MAClB+b,IAAI,EAAE,CAAC,cAAc,EAAE;QAAEQ,WAAW,EAAE;MAAM,CAAC;IACjD,CAAC,CAAC;IAAEjW,qBAAqB,EAAE,CAAC;MACxB0H,IAAI,EAAEhO,YAAY;MAClB+b,IAAI,EAAE,CAAC,cAAc,EAAE;QAAEQ,WAAW,EAAE;MAAM,CAAC;IACjD,CAAC,CAAC;IAAEjY,iBAAiB,EAAE,CAAC;MACpB0J,IAAI,EAAEhO,YAAY;MAClB+b,IAAI,EAAE,CAAC,UAAU,EAAE;QAAEQ,WAAW,EAAE;MAAM,CAAC;IAC7C,CAAC,CAAC;IAAE3J,SAAS,EAAE,CAAC;MACZ5E,IAAI,EAAEjO,eAAe;MACrBgc,IAAI,EAAE,CAAC1a,aAAa;IACxB,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMmb,YAAY,CAAC;EACf,OAAOnP,IAAI,YAAAoP,qBAAAjP,iBAAA;IAAA,YAAAA,iBAAA,IAAyFgP,YAAY;EAAA;EAChH,OAAOE,IAAI,kBA9uC+Eld,EAAE,CAAAmd,gBAAA;IAAA3O,IAAA,EA8uCSwO,YAAY;IAAAN,OAAA,GAAY7N,MAAM,EAAEjN,YAAY;IAAAwb,OAAA,GAAavO,MAAM,EAAEjN,YAAY;EAAA;EAClL,OAAOyb,IAAI,kBA/uC+Erd,EAAE,CAAAsd,gBAAA;IAAAZ,OAAA,GA+uCiC7N,MAAM,EAAEjN,YAAY,EAAEA,YAAY;EAAA;AACnK;AACA;EAAA,QAAA0M,SAAA,oBAAAA,SAAA,KAjvC8FtO,EAAE,CAAAuO,iBAAA,CAivCJyO,YAAY,EAAc,CAAC;IAC3GxO,IAAI,EAAEzN,QAAQ;IACdwb,IAAI,EAAE,CAAC;MACCG,OAAO,EAAE,CAAC7N,MAAM,EAAEjN,YAAY,CAAC;MAC/Bwb,OAAO,EAAE,CAACvO,MAAM,EAAEjN,YAAY;IAClC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASiN,MAAM,EAAEJ,aAAa,EAAEuO,YAAY,EAAErP,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}