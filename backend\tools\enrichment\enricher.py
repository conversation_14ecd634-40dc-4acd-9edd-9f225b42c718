#!/usr/bin/env python3
"""
Data Enricher para Enriquecimento Contínuo

Implementa estratégias especializadas e algoritmos de enriquecimento
de dados para diferentes tipos de informações empresariais.
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

# Configurar logging
logger = logging.getLogger(__name__)


class EnrichmentStrategy(Enum):
    """Estratégias de enriquecimento disponíveis"""
    INCREMENTAL = "incremental"     # Atualização incremental
    FULL_REFRESH = "full_refresh"   # Atualização completa
    SMART_MERGE = "smart_merge"     # Mesclagem inteligente
    QUALITY_ENHANCEMENT = "quality_enhancement"  # Melhoria de qualidade


@dataclass
class EnrichmentRule:
    """Regra de enriquecimento"""
    data_type: str
    strategy: EnrichmentStrategy
    conditions: Dict[str, Any]
    priority: int
    enabled: bool = True


class DataEnricher:
    """
    Data Enricher Especializado

    Implementa lógicas avançadas de enriquecimento e merge
    de dados baseadas em regras e estratégias configuráveis.
    """

    def __init__(self):
        """Inicializa o data enricher"""
        self.enrichment_rules: List[EnrichmentRule] = []
        self.default_strategies = {
            "parcerias": EnrichmentStrategy.SMART_MERGE,
            "modelo_negocio": EnrichmentStrategy.INCREMENTAL,
            "pricing": EnrichmentStrategy.QUALITY_ENHANCEMENT
        }

    def add_enrichment_rule(self, rule: EnrichmentRule) -> None:
        """
        Adiciona regra de enriquecimento

        Args:
            rule: Regra a ser adicionada
        """
        self.enrichment_rules.append(rule)
        self.enrichment_rules.sort(key=lambda x: x.priority)
        logger.info(f"Regra de enriquecimento adicionada: {rule.data_type}")

    def enrich_data(self, data_type: str, new_data: Dict[str, Any],
                    existing_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Enriquece dados usando estratégias configuradas

        Args:
            data_type: Tipo de dados (parcerias, modelo_negocio, pricing)
            new_data: Novos dados coletados
            existing_data: Dados existentes (se houver)

        Returns:
            Dados enriquecidos
        """
        logger.info(f"Enriquecendo dados do tipo: {data_type}")

        # Determinar estratégia
        strategy = self._get_strategy_for_data_type(data_type)

        # Aplicar estratégia
        if strategy == EnrichmentStrategy.FULL_REFRESH:
            return self._full_refresh(new_data)
        elif strategy == EnrichmentStrategy.INCREMENTAL:
            return self._incremental_update(new_data, existing_data)
        elif strategy == EnrichmentStrategy.SMART_MERGE:
            return self._smart_merge(new_data, existing_data)
        elif strategy == EnrichmentStrategy.QUALITY_ENHANCEMENT:
            return self._quality_enhancement(new_data, existing_data)
        else:
            logger.warning(f"Estratégia não reconhecida: {strategy}")
            return new_data

    def _get_strategy_for_data_type(self, data_type: str) -> EnrichmentStrategy:
        """
        Determina estratégia para tipo de dados

        Args:
            data_type: Tipo de dados

        Returns:
            Estratégia de enriquecimento
        """
        # Verificar regras específicas
        for rule in self.enrichment_rules:
            if rule.enabled and rule.data_type == data_type:
                if self._evaluate_conditions(rule.conditions):
                    return rule.strategy

        # Usar estratégia padrão
        return self.default_strategies.get(data_type, EnrichmentStrategy.FULL_REFRESH)

    def _evaluate_conditions(self, conditions: Dict[str, Any]) -> bool:
        """
        Avalia condições de uma regra

        Args:
            conditions: Condições a avaliar

        Returns:
            True se condições são atendidas
        """
        # Implementação simplificada - pode ser expandida
        return True

    def _full_refresh(self, new_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Estratégia de atualização completa

        Args:
            new_data: Novos dados

        Returns:
            Dados processados
        """
        logger.debug("Aplicando estratégia FULL_REFRESH")

        # Simplesmente retorna os novos dados
        enriched_data = new_data.copy()
        enriched_data["enrichment_metadata"] = {
            "strategy_used": "full_refresh",
            "processed_at": datetime.utcnow().isoformat(),
            "enrichment_notes": "Dados completamente substituídos"
        }

        return enriched_data

    def _incremental_update(self, new_data: Dict[str, Any],
                            existing_data: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Estratégia de atualização incremental

        Args:
            new_data: Novos dados
            existing_data: Dados existentes

        Returns:
            Dados mesclados incrementalmente
        """
        logger.debug("Aplicando estratégia INCREMENTAL")

        if not existing_data:
            return self._full_refresh(new_data)

        # Mesclar dados preservando histórico
        enriched_data = existing_data.copy()

        # Atualizar campos específicos
        for key, value in new_data.items():
            if key not in ["processed_at", "data_quality_score"]:
                enriched_data[key] = value

        # Atualizar metadados
        enriched_data["processed_at"] = new_data.get(
            "processed_at", datetime.utcnow().isoformat())
        enriched_data["data_quality_score"] = new_data.get("data_quality_score",
                                                           existing_data.get("data_quality_score", 0))

        enriched_data["enrichment_metadata"] = {
            "strategy_used": "incremental",
            "processed_at": datetime.utcnow().isoformat(),
            "enrichment_notes": "Atualização incremental aplicada"
        }

        return enriched_data

    def _smart_merge(self, new_data: Dict[str, Any],
                     existing_data: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Estratégia de mesclagem inteligente

        Args:
            new_data: Novos dados
            existing_data: Dados existentes

        Returns:
            Dados mesclados inteligentemente
        """
        logger.debug("Aplicando estratégia SMART_MERGE")

        if not existing_data:
            return self._full_refresh(new_data)

        # Implementar lógica de merge inteligente
        enriched_data = existing_data.copy()

        # Combinar listas (ex: parcerias, produtos)
        for key, new_value in new_data.items():
            if isinstance(new_value, list) and key in existing_data:
                existing_list = existing_data.get(key, [])
                # Mesclar listas removendo duplicatas
                if isinstance(existing_list, list):
                    combined_list = existing_list + \
                        [item for item in new_value if item not in existing_list]
                    enriched_data[key] = combined_list
                else:
                    enriched_data[key] = new_value
            else:
                enriched_data[key] = new_value

        # Atualizar qualidade baseada em ambas as fontes
        existing_quality = existing_data.get("data_quality_score", 0)
        new_quality = new_data.get("data_quality_score", 0)

        # Usar a melhor qualidade disponível
        enriched_data["data_quality_score"] = max(
            existing_quality, new_quality)
        enriched_data["processed_at"] = datetime.utcnow().isoformat()

        enriched_data["enrichment_metadata"] = {
            "strategy_used": "smart_merge",
            "processed_at": datetime.utcnow().isoformat(),
            "enrichment_notes": "Mesclagem inteligente de dados aplicada",
            "sources_merged": 2
        }

        return enriched_data

    def _quality_enhancement(self, new_data: Dict[str, Any],
                             existing_data: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Estratégia de melhoria de qualidade

        Args:
            new_data: Novos dados
            existing_data: Dados existentes

        Returns:
            Dados com qualidade melhorada
        """
        logger.debug("Aplicando estratégia QUALITY_ENHANCEMENT")

        if not existing_data:
            return self._full_refresh(new_data)

        # Escolher dados de melhor qualidade
        existing_quality = existing_data.get("data_quality_score", 0)
        new_quality = new_data.get("data_quality_score", 0)

        if new_quality > existing_quality:
            # Novos dados são melhores
            enriched_data = new_data.copy()
            enhancement_note = f"Qualidade melhorada: {existing_quality} → {new_quality}"
        else:
            # Manter dados existentes
            enriched_data = existing_data.copy()
            enhancement_note = f"Dados existentes mantidos (qualidade superior: {existing_quality})"

        enriched_data["enrichment_metadata"] = {
            "strategy_used": "quality_enhancement",
            "processed_at": datetime.utcnow().isoformat(),
            "enrichment_notes": enhancement_note,
            "quality_comparison": {
                "existing_quality": existing_quality,
                "new_quality": new_quality,
                "selected_source": "new" if new_quality > existing_quality else "existing"
            }
        }

        return enriched_data

    def get_enrichment_stats(self) -> Dict[str, Any]:
        """
        Retorna estatísticas do enricher

        Returns:
            Estatísticas de enriquecimento
        """
        return {
            "total_rules": len(self.enrichment_rules),
            "active_rules": len([r for r in self.enrichment_rules if r.enabled]),
            "default_strategies": {k: v.value for k, v in self.default_strategies.items()},
            "available_strategies": [s.value for s in EnrichmentStrategy]
        }
