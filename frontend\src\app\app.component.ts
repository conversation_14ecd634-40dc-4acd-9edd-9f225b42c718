import { JsonPipe } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { Component, OnDestroy, OnInit, inject } from '@angular/core';
import { Router, RouterOutlet } from '@angular/router';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ToastModule } from 'primeng/toast';
import { Subscription } from 'rxjs';
import { WebSocketMessage, WebSocketService } from './shared/services/ws.service';

@Component({
	selector: 'root',
	standalone: true,
	imports: [RouterOutlet, JsonPipe, ToastModule, ConfirmDialogModule],
	providers: [ConfirmationService],
	template: `
		<p-toast></p-toast>
		<p-confirmDialog></p-confirmDialog>
		<router-outlet></router-outlet>
	`,
})
export class AppComponent implements OnInit, OnDestroy {
	private wsService = inject(WebSocketService);
	private messageService = inject(MessageService);
	private confirmationService = inject(ConfirmationService);
	private router = inject(Router);
	private http = inject(HttpClient);
	private subscriptions: Subscription[] = [];

	ngOnInit() {
		// ✅ Conectar WebSocket
		this.wsService.connect();

		// ✅ Escutar TODAS as mensagens WebSocket globalmente
		this.setupGlobalNotifications();
	}

	ngOnDestroy() {
		// ✅ Limpar subscriptions para evitar memory leaks
		this.subscriptions.forEach(sub => sub.unsubscribe());
		this.wsService.close();
	}

	// ✅ Configurar notificações globais
	private setupGlobalNotifications(): void {
		const subscription = this.wsService.allMessages$.subscribe((message: WebSocketMessage) => {
			this.handleGlobalNotification(message);
		});

		this.subscriptions.push(subscription);
	}

	// ✅ Processar notificações globalmente (aparecem em qualquer tela)
	private handleGlobalNotification(message: WebSocketMessage): void {
		switch (message.type) {
			case 'client_status_update':
				this.showClientStatusNotification(message);
				break;

			case 'collection_status_update':
				// ✅ NOVO: Mostrar notificação de coleta de dados
				this.showCollectionStatusNotification(message);
				break;

			case 'processing_started':
				this.showProcessingStartedNotification(message);
				break;

			case 'processing_complete':
				this.showProcessingCompleteNotification(message);
				break;

			case 'processing_error':
				this.showProcessingErrorNotification(message);
				break;

			case 'report_complete':
				this.showReportCompleteNotification(message);
				break;

			case 'ready_for_projects':
				this.showReadyForProjectsNotification(message);
				break;

			case 'projects_generation_started':
				this.showProjectsGenerationStartedNotification(message);
				break;

			case 'projects_generation_progress':
				this.showProjectsGenerationProgressNotification(message);
				break;

			case 'projects_generation_completed':
				this.showProjectsGenerationCompletedNotification(message);
				break;

			case 'projects_generation_error':
				this.showProjectsGenerationErrorNotification(message);
				break;

			case 'lighthouse_analysis_complete':
				// ❌ BLOQUEADO: Não exibir notificação do Lighthouse no fluxo automático
				break;

			default:
			// Notificação genérica para tipos não mapeados
			// if (message.message) {
			// 	this.messageService.add({
			// 		severity: 'info',
			// 		summary: 'Notificação',
			// 		detail: message.message,
			// 		life: 5000,
			// 	});
			// }
		}
	}

	// ✅ Notificação específica para atualização de status do cliente
	private showClientStatusNotification(message: WebSocketMessage): void {
		const clientName = this.extractClientName(message.message);

		this.messageService.add({
			severity: 'success',
			summary: '✅ Relatório Concluído',
			detail: message.message || `Relatório do cliente ${clientName} foi finalizado com sucesso!`,
			life: 10000,
			data: {
				clientId: message.clientId,
				action: message.action,
			},
		});
	}

	// ✅ Notificação para início de processamento
	private showProcessingStartedNotification(message: WebSocketMessage): void {
		const clientName = this.extractClientName(message.message);

		this.messageService.add({
			severity: 'info',
			summary: '🔄 Processamento Iniciado',
			detail: message.message || `Análise do cliente ${clientName} foi iniciada.`,
			life: 10000,
		});
	}

	// ✅ Notificação para processamento concluído
	private showProcessingCompleteNotification(message: WebSocketMessage): void {
		const clientName = this.extractClientName(message.message);

		this.messageService.add({
			severity: 'success',
			summary: '✅ Processamento Concluído',
			detail: message.message || `Análise do cliente ${clientName} foi concluída.`,
			life: 10000,
		});
	}

	// ✅ Notificação para erro de processamento
	private showProcessingErrorNotification(message: WebSocketMessage): void {
		const clientName = this.extractClientName(message.message);

		this.messageService.add({
			severity: 'error',
			summary: '❌ Erro no Processamento',
			detail: message.message || `Erro na análise do cliente ${clientName}.`,
			life: 10000,
		});
	}

	// ✅ Notificação para relatório completo
	private showReportCompleteNotification(message: WebSocketMessage): void {
		const clientName = this.extractClientName(message.message);

		this.messageService.add({
			severity: 'success',
			summary: '📄 Relatório Disponível',
			detail: message.message || `Relatório do cliente ${clientName} está disponível para download.`,
			life: 10000,
		});
	}

	// ✅ NOVO: Notificação para status de coleta de dados
	private showCollectionStatusNotification(message: WebSocketMessage): void {
		const clientName = this.extractClientName(message.message);
		const phase = message['phase'];

		switch (phase) {
			case 'data_collection_started':
				this.messageService.add({
					severity: 'info',
					summary: '🔄 Coletando Informações',
					detail:
						message.message || `Iniciando coleta de dados para ${clientName}. Tempo estimado: 10 minutos`,
					life: 10000,
				});
				break;

			default:
				// Fallback para outras fases
				// if (message.message) {
				// 	this.messageService.add({
				// 		severity: 'info',
				// 		summary: '🔄 Processando',
				// 		detail: message.message,
				// 		life: 10000,
				// 	});
				// }
				break;
		}
	}

	// ✅ Notificação para notificação de projetos prontos
	private showReadyForProjectsNotification(message: WebSocketMessage): void {
		const clientName = message['clientName'] || this.extractClientName(message.message);
		const clientId = message.clientId;
		const marketResearchCompleted = message['market_research_completed'];
		const sector = message['sector'];

		// Criar mensagem baseada no status da pesquisa de mercado
		let confirmMessage = '';
		let headerMessage = '🚀 Sugestão de projetos';

		if (marketResearchCompleted) {
			confirmMessage = `✅ Dossiê e pesquisa de mercado concluídos para ${clientName}! Gostaria de gerar sugestões de projetos agora?`;
			headerMessage = '🚀 Tudo pronto para projetos!';
		} else if (sector) {
			confirmMessage = `⚠️ Dossiê de ${clientName} concluído, mas houve problemas na pesquisa de mercado do setor "${sector}".\n\nAinda assim, gostaria de gerar sugestões de projetos?`;
			headerMessage = '🚀 Projetos (pesquisa limitada)';
		} else {
			confirmMessage = `📋 Dossiê de ${clientName} concluído!\n\nSetor não foi definido para pesquisa automática de mercado. Gostaria de gerar sugestões de projetos mesmo assim?`;
			headerMessage = '🚀 Projetos (sem pesquisa)';
		}

		this.confirmationService.confirm({
			message: confirmMessage,
			header: headerMessage,
			icon: 'pi pi-question-circle',
			accept: () => {
				// Chamar API para gerar projetos
				if (clientId) {
					this.generateProjectsForClient(clientId, clientName, marketResearchCompleted);
				}
			},
			reject: () => {
				// Mostrar notificação de que pode gerar depois
				this.messageService.add({
					severity: 'info',
					summary: 'ℹ️ Projetos não gerados',
					detail: `Você pode gerar projetos para ${clientName} mais tarde na página de clientes.`,
					life: 10000,
				});
			},
		});
	}

	// ✅ Notificação para início da geração de projetos
	private showProjectsGenerationStartedNotification(message: WebSocketMessage): void {
		const clientName = this.extractClientName(message.message);

		this.messageService.add({
			severity: 'info',
			summary: '🚀 Gerando Projetos',
			detail: message.message || `Iniciando geração de projetos para ${clientName}...`,
			life: 15000,
		});
	}

	// ✅ Notificação para progresso da geração de projetos
	private showProjectsGenerationProgressNotification(message: WebSocketMessage): void {
		const clientName = this.extractClientName(message.message);
		const phase = message['phase'];

		switch (phase) {
			case 'boardroom_analysis':
				this.messageService.add({
					severity: 'info',
					summary: '🤝 Consultoria em Andamento',
					detail: message.message || `Time da diretoria analisando ${clientName}...`,
					life: 15000,
				});
				break;

			default:
				this.messageService.add({
					severity: 'info',
					summary: '🔄 Processando Projetos',
					detail: message.message,
					life: 15000,
				});
				break;
		}
	}

	// ✅ Notificação para conclusão da geração de projetos
	private showProjectsGenerationCompletedNotification(message: WebSocketMessage): void {
		const clientName = this.extractClientName(message.message);
		const projectsCount = message['projects_count'] || 0;

		this.messageService.add({
			severity: 'success',
			summary: '✅ Projetos Gerados!',
			detail: message.message || `${projectsCount} projetos gerados para ${clientName}!`,
			life: 10000,
		});

		// Navegar para página de projetos após 3 segundos
		setTimeout(() => {
			this.router.navigate(['/projects']);
		}, 3000);
	}

	// ✅ Notificação para erro na geração de projetos
	private showProjectsGenerationErrorNotification(message: WebSocketMessage): void {
		const clientName = this.extractClientName(message.message);

		this.messageService.add({
			severity: 'error',
			summary: '❌ Erro na Geração de Projetos',
			detail: message.message || `Erro ao gerar projetos para ${clientName}.`,
			life: 15000,
		});
	}

	// ✅ Chamar API para gerar projetos
	private generateProjectsForClient(
		clientId: string,
		clientName: string,
		marketResearchCompleted: boolean = false,
	): void {
		// Chamar endpoint de geração de projetos (notificações via WebSocket)
		this.http.post(`http://localhost:8040/clients/${clientId}/generate-projects`, {}).subscribe({
			next: response => {
				// Não precisa exibir notificação aqui, será via WebSocket
			},
			error: error => {
				console.error('❌ Erro ao iniciar geração de projetos:', error);
				this.messageService.add({
					severity: 'error',
					summary: '❌ Erro na Inicialização',
					detail: `Erro ao iniciar geração de projetos para ${clientName}. Tente novamente.`,
					life: 8000,
				});
			},
		});
	}

	// ✅ Extrair nome do cliente da mensagem
	private extractClientName(message?: string): string {
		if (!message) return 'Cliente';

		// Tentar extrair nome entre aspas ou após "de "
		const matches = message.match(/(?:de |cliente )"?([^"]+)"?/i);
		return matches ? matches[1] : 'Cliente';
	}

	public sendPing() {
		this.wsService.sendMessage({ type: 'ping', timestamp: Date.now() });
	}
}
