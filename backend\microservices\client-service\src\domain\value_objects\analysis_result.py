"""Value Object para resultado de análise de cliente."""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Any, Dict, List, Optional
from enum import Enum


class AnalysisType(str, Enum):
    """Tipos de análise disponíveis."""
    EXPANDED_DOSSIER = "expanded_dossier"
    MARKET_RESEARCH = "market_research"
    TECHNICAL_DIAGNOSTIC = "technical_diagnostic"
    COMPETITOR_ANALYSIS = "competitor_analysis"


@dataclass(frozen=True)
class AnalysisResult:
    """Value Object imutável para resultado de análise.

    Representa o resultado estruturado de uma análise realizada para um cliente.
    """

    analysis_type: AnalysisType
    success: bool
    completed_at: datetime
    duration_seconds: float
    data: Dict[str, Any] = field(default_factory=dict)
    error_message: Optional[str] = None
    retry_count: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self) -> None:
        """Validações pós-inicialização."""
        if self.duration_seconds < 0:
            raise ValueError("Duração não pode ser negativa")

        if self.retry_count < 0:
            raise ValueError("Contador de retry não pode ser negativo")

        if not self.success and not self.error_message:
            raise ValueError("Análises com falha devem ter mensagem de erro")

        if self.success and self.error_message:
            raise ValueError(
                "Análises bem-sucedidas não devem ter mensagem de erro")

    @property
    def is_successful(self) -> bool:
        """Verifica se a análise foi bem-sucedida."""
        return self.success

    @property
    def has_data(self) -> bool:
        """Verifica se há dados no resultado."""
        return bool(self.data)

    @property
    def execution_time_minutes(self) -> float:
        """Retorna o tempo de execução em minutos."""
        return round(self.duration_seconds / 60, 2)

    def get_data_field(self, field: str, default: Any = None) -> Any:
        """Obtém um campo específico dos dados.

        Args:
            field: Nome do campo
            default: Valor padrão se não encontrado

        Returns:
            Valor do campo ou default
        """
        return self.data.get(field, default)

    def has_field(self, field: str) -> bool:
        """Verifica se um campo existe nos dados."""
        return field in self.data

    @classmethod
    def create_success(
        cls,
        analysis_type: AnalysisType,
        data: Dict[str, Any],
        duration_seconds: float,
        metadata: Optional[Dict[str, Any]] = None,
        retry_count: int = 0
    ) -> 'AnalysisResult':
        """Factory method para criar resultado de sucesso.

        Args:
            analysis_type: Tipo da análise
            data: Dados do resultado
            duration_seconds: Duração em segundos
            metadata: Metadados opcionais
            retry_count: Número de tentativas

        Returns:
            AnalysisResult de sucesso
        """
        return cls(
            analysis_type=analysis_type,
            success=True,
            completed_at=datetime.utcnow(),
            duration_seconds=duration_seconds,
            data=data,
            error_message=None,
            retry_count=retry_count,
            metadata=metadata or {}
        )

    @classmethod
    def create_failure(
        cls,
        analysis_type: AnalysisType,
        error_message: str,
        duration_seconds: float,
        metadata: Optional[Dict[str, Any]] = None,
        retry_count: int = 0
    ) -> 'AnalysisResult':
        """Factory method para criar resultado de falha.

        Args:
            analysis_type: Tipo da análise
            error_message: Mensagem de erro
            duration_seconds: Duração em segundos
            metadata: Metadados opcionais
            retry_count: Número de tentativas

        Returns:
            AnalysisResult de falha
        """
        return cls(
            analysis_type=analysis_type,
            success=False,
            completed_at=datetime.utcnow(),
            duration_seconds=duration_seconds,
            data={},
            error_message=error_message,
            retry_count=retry_count,
            metadata=metadata or {}
        )
