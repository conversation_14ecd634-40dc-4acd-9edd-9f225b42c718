"""
Testes para Product Owner Agent

Testes abrangentes cobrindo:
- Análise estratégica de produtos
- Identificação de oportunidades de mercado
- Geração de roadmaps
- Integração com ProductAnalysisTools
- Diferentes cenários de maturidade de produto
"""

import pytest
import logging
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, Any

from ..agents.product_owner_agent import ProductOwnerAgent
from ..tools.product_analysis_tools import ProductInsights, RoadmapPriority


class TestProductOwnerAgent:
    """Testes para o agente Product Owner"""

    @pytest.fixture
    def agent(self):
        """Fixture para criar instância do agente"""
        with patch('backend.tools.agents.agents.product_owner_agent.Agent'):
            with patch('backend.tools.agents.agents.product_owner_agent.ProductAnalysisTools') as mock_tools:
                agent = ProductOwnerAgent()
                agent.product_tools = mock_tools.return_value
                return agent

    @pytest.fixture
    def sample_product_data(self):
        """Dados de exemplo para análise de produto"""
        return {
            "features": [
                "User authentication", "Dashboard", "Reporting", "API integration",
                "Mobile app", "Analytics", "Data export", "Real-time notifications"
            ],
            "business_metrics": {
                "active_users": 5000,
                "retention_rate": 75,
                "nps_score": 45,
                "monthly_growth_rate": 12,
                "user_satisfaction": 78,
                "average_feature_usage": 65,
                "support_rating": 4.2
            },
            "user_feedback": [
                "Need better mobile experience",
                "Want advanced analytics",
                "Integration with third-party tools"
            ],
            "roadmap_requests": [
                "AI features", "Advanced security", "Better UX"
            ],
            "age_months": 18,
            "unique_features": ["Advanced AI integration", "Real-time collaboration"],
            "tech_stack": ["React", "Node.js", "AI", "cloud", "microservices"],
            "patents": 2,
            "user_requests": [
                "Advanced reporting", "Mobile optimization", "API improvements"
            ],
            "performance_metrics": {
                "speed_advantage": True,
                "reliability_score": 96
            },
            "pricing_tier": "premium",
            "target_audience": "Enterprise customers",
            "current_segments": ["Enterprise", "Mid-market"]
        }

    @pytest.fixture
    def sample_market_data(self):
        """Dados de exemplo de mercado"""
        return {
            "trends": [
                "AI automation", "Digital transformation", "Cloud migration",
                "Analytics-driven decisions", "Mobile-first approach"
            ],
            "growth_areas": ["AI integration", "Enterprise automation"],
            "target_segments": ["Enterprise", "SMB", "Startups"],
            "market_size": "Large",
            "growth_rate": 18
        }

    @pytest.fixture
    def sample_competitor_data(self):
        """Dados de exemplo de concorrentes"""
        return {
            "strong_competitors": ["CompetitorA", "CompetitorB"],
            "emerging_players": ["StartupX"],
            "market_leaders": ["MarketLeader1", "MarketLeader2"],
            "common_features": ["Analytics", "Reporting", "API integration", "Advanced AI"]
        }

    @pytest.fixture
    def sample_company_context(self):
        """Contexto de exemplo da empresa"""
        return {
            "name": "TechCorp",
            "sector": "SaaS",
            "business_model": "Subscription",
            "website": "https://techcorp.com"
        }

    @pytest.fixture
    def mock_product_insights(self):
        """Mock para ProductInsights"""
        return ProductInsights(
            product_maturity="Em crescimento",
            market_positioning="Premium/Enterprise - Foco em valor e qualidade",
            competitive_advantage="Funcionalidades únicas: Advanced AI integration, Real-time collaboration",
            feature_completeness="Básico - Funcionalidades fundamentais",
            feature_gaps=[
                "Funcionalidade solicitada: Advanced reporting",
                "Gap técnico comum: Automation features"
            ],
            market_opportunities=[
                "Trend de mercado: AI automation",
                "Área de crescimento: Enterprise automation"
            ],
            competitive_threats=[
                "Líder de mercado: MarketLeader1",
                "Concorrente forte: CompetitorA"
            ],
            product_market_fit_score=72.0,
            innovation_score=78.0,
            user_value_score=75.0
        )

    @pytest.fixture
    def sample_features_data(self):
        """Dados de exemplo para roadmap de features"""
        return [
            {
                "name": "AI Analytics Dashboard",
                "business_value": 9,
                "effort": 7,
                "user_impact": 8,
                "category": "Analytics"
            },
            {
                "name": "Mobile App Enhancement",
                "business_value": 7,
                "effort": 5,
                "user_impact": 9,
                "category": "Mobile"
            },
            {
                "name": "Advanced Security",
                "business_value": 8,
                "effort": 8,
                "user_impact": 7,
                "category": "Security"
            }
        ]

    def test_agent_initialization(self, agent):
        """Testa inicialização do agente"""
        assert agent is not None
        assert hasattr(agent, 'product_tools')
        assert hasattr(agent, 'agent')

    @patch('backend.tools.agents.agents.product_owner_agent.ProductAnalysisTools')
    def test_analyze_product_strategy_basic(self, mock_product_tools, agent, sample_product_data, mock_product_insights):
        """Testa análise estratégica básica de produto"""
        # Mock das ferramentas de produto
        agent.product_tools.analyze_product_data.return_value = mock_product_insights
        agent.product_tools.analyze_market_trends.return_value = {
            "market_potential": "Alto",
            "growth_rate": 18,
            "high_impact_trends": ["AI automation", "Digital transformation"]
        }

        # Mock do agente Agno
        mock_response = Mock()
        mock_response.content = "Análise estratégica detalhada com roadmap e recomendações"
        agent.agent.run.return_value = mock_response

        # Executar análise
        result = agent.analyze_product_strategy(sample_product_data)

        # Verificações
        assert result is not None
        assert result["agent_type"] == "ProductOwnerAgent"
        assert result["product_market_fit_score"] == 72.0
        assert result["innovation_score"] == 78.0
        assert result["user_value_score"] == 75.0
        assert result["product_maturity"] == "Em crescimento"
        assert "strategy_category" in result
        assert "analysis_summary" in result

        # Verificar cálculo do score geral
        expected_overall = round((72.0 + 78.0 + 75.0) / 3, 1)
        assert result["analysis_summary"]["overall_score"] == expected_overall

    @patch('backend.tools.agents.agents.product_owner_agent.ProductAnalysisTools')
    def test_analyze_with_market_and_competitor_data(self, mock_product_tools, agent,
                                                     sample_product_data, sample_market_data,
                                                     sample_competitor_data, sample_company_context,
                                                     mock_product_insights):
        """Testa análise com dados completos de mercado e concorrentes"""
        # Setup mocks
        agent.product_tools.analyze_product_data.return_value = mock_product_insights
        agent.product_tools.analyze_market_trends.return_value = {
            "market_potential": "Alto",
            "growth_rate": 18,
            "high_impact_trends": ["AI automation", "Digital transformation", "Cloud migration"],
            "medium_impact_trends": ["Analytics", "Security"]
        }

        mock_response = Mock()
        mock_response.content = "Análise estratégica completa com contexto de mercado"
        agent.agent.run.return_value = mock_response

        # Executar análise
        result = agent.analyze_product_strategy(
            sample_product_data,
            market_data=sample_market_data,
            competitor_data=sample_competitor_data,
            company_context=sample_company_context
        )

        # Verificações
        assert result["product_insights"]["product_maturity"] == "Em crescimento"
        assert "market_trends" in result
        assert result["market_trends"]["market_potential"] == "Alto"

        # Verificar se o prompt incluiu dados de mercado
        call_args = agent.agent.run.call_args[0][0]
        assert "TechCorp" in call_args
        assert "SaaS" in call_args
        assert "Subscription" in call_args
        assert "Alto" in call_args  # market potential
        assert "18%" in call_args  # growth rate

    def test_categorize_product_strategy(self, agent):
        """Testa categorização da estratégia de produto"""
        # Criar mock insights para diferentes cenários
        leader_insights = Mock()
        leader_insights.product_market_fit_score = 85.0
        leader_insights.innovation_score = 75.0
        assert agent._categorize_product_strategy(
            leader_insights) == "Líder de Mercado - Manter posição e inovar"

        strong_insights = Mock()
        strong_insights.product_market_fit_score = 75.0
        strong_insights.innovation_score = 65.0
        assert agent._categorize_product_strategy(
            strong_insights) == "Posição Forte - Expandir e otimizar"

        growth_insights = Mock()
        growth_insights.product_market_fit_score = 65.0
        growth_insights.innovation_score = 50.0
        assert agent._categorize_product_strategy(
            growth_insights) == "Crescimento - Focar em product-market fit"

        innovator_insights = Mock()
        innovator_insights.product_market_fit_score = 55.0
        innovator_insights.innovation_score = 75.0
        assert agent._categorize_product_strategy(
            innovator_insights) == "Inovador - Buscar encaixe no mercado"

        development_insights = Mock()
        development_insights.product_market_fit_score = 45.0
        development_insights.innovation_score = 50.0
        assert agent._categorize_product_strategy(
            development_insights) == "Desenvolvimento - Melhorar produto e posicionamento"

    @patch('backend.tools.agents.agents.product_owner_agent.ProductAnalysisTools')
    def test_create_feature_roadmap(self, mock_product_tools, agent, sample_features_data):
        """Testa criação de roadmap de features"""
        # Mock das prioridades
        mock_priorities = [
            RoadmapPriority(
                feature="Mobile App Enhancement",
                business_value=7,
                effort=5,
                user_impact=9,
                priority_score=12.6,
                category="Mobile"
            ),
            RoadmapPriority(
                feature="AI Analytics Dashboard",
                business_value=9,
                effort=7,
                user_impact=8,
                priority_score=10.3,
                category="Analytics"
            )
        ]

        agent.product_tools.prioritize_roadmap_features.return_value = mock_priorities

        mock_response = Mock()
        mock_response.content = "Roadmap estruturado por sprints com objetivos e métricas"
        agent.agent.run.return_value = mock_response

        # Executar criação do roadmap
        result = agent.create_feature_roadmap(sample_features_data)

        # Verificações
        assert result is not None
        assert "roadmap_analysis" in result
        assert "prioritized_features" in result
        assert result["total_features"] == 2
        assert result["high_priority_count"] == 2  # Ambas têm score >= 7
        assert result["roadmap_horizon"] == "18+ semanas"

    @patch('backend.tools.agents.agents.product_owner_agent.ProductAnalysisTools')
    def test_assess_market_opportunities(self, mock_product_tools, agent,
                                         sample_market_data, sample_product_data,
                                         sample_company_context):
        """Testa avaliação de oportunidades de mercado"""
        # Mock da análise de mercado
        mock_market_analysis = {
            "market_potential": "Alto",
            "growth_rate": 18,
            "high_impact_trends": ["AI automation", "Digital transformation"],
            "medium_impact_trends": ["Analytics", "Security"]
        }

        agent.product_tools.analyze_market_trends.return_value = mock_market_analysis

        mock_response = Mock()
        mock_response.content = "Análise detalhada de oportunidades de mercado"
        agent.agent.run.return_value = mock_response

        # Executar avaliação
        result = agent.assess_market_opportunities(
            sample_market_data,
            sample_product_data,
            sample_company_context
        )

        # Verificações
        assert result is not None
        assert "market_opportunity_analysis" in result
        assert "market_analysis" in result
        assert "opportunity_score" in result
        assert "current_positioning" in result

        # Verificar opportunity score calculation
        assert result["opportunity_score"] > 0
        assert result["current_positioning"]["feature_count"] == 8

    def test_calculate_opportunity_score(self, agent):
        """Testa cálculo do score de oportunidade"""
        # Cenário de alto potencial
        high_potential = {
            "growth_rate": 25,
            "market_potential": "Alto",
            "high_impact_trends": ["AI", "Cloud", "Mobile"]
        }
        score = agent._calculate_opportunity_score(high_potential)
        assert score >= 85  # 50 + 30 (growth) + 20 (potential) + 15 (trends)

        # Cenário médio
        medium_potential = {
            "growth_rate": 12,
            "market_potential": "Médio",
            "high_impact_trends": ["Analytics"]
        }
        score = agent._calculate_opportunity_score(medium_potential)
        assert 65 <= score <= 85

        # Cenário baixo
        low_potential = {
            "growth_rate": 3,
            "market_potential": "Baixo",
            "high_impact_trends": []
        }
        score = agent._calculate_opportunity_score(low_potential)
        assert score <= 65

    def test_get_strategic_recommendations_summary(self, agent):
        """Testa extração de resumo de recomendações estratégicas"""
        analysis_result = {
            "product_insights": {
                "product_market_fit_score": 65.0,
                "innovation_score": 55.0,
                "feature_gaps": [
                    "Gap 1", "Gap 2", "Gap 3", "Gap 4"
                ],
                "market_opportunities": [
                    "Oportunidade 1", "Oportunidade 2"
                ]
            }
        }

        recommendations = agent.get_strategic_recommendations_summary(
            analysis_result)

        # PMF + Innovation + Features + Market
        assert len(recommendations) == 4

        # Verificar tipos de recomendações
        categories = [rec["category"] for rec in recommendations]
        assert "Product-Market Fit" in categories
        assert "Innovation" in categories
        assert "Feature Development" in categories
        assert "Market Expansion" in categories

        # Verificar prioridades
        priorities = [rec["priority"] for rec in recommendations]
        assert "High" in priorities  # PMF < 70 e muitos gaps
        assert "Medium" in priorities

    @patch('backend.tools.agents.agents.product_owner_agent.ProductAnalysisTools')
    def test_error_handling(self, mock_product_tools, agent, sample_product_data):
        """Testa tratamento de erros"""
        # Simular erro nas ferramentas de produto
        agent.product_tools.analyze_product_data.side_effect = Exception(
            "Erro de análise")

        # Verificar se a exceção é propagada
        with pytest.raises(Exception, match="Erro de análise"):
            agent.analyze_product_strategy(sample_product_data)

    @patch('backend.tools.agents.agents.product_owner_agent.ProductAnalysisTools')
    def test_empty_product_data(self, mock_product_tools, agent):
        """Testa análise com dados de produto vazios"""
        empty_data = {}

        # Mock com dados limitados
        mock_insights = ProductInsights(
            product_maturity="Indeterminado",
            market_positioning="Posicionamento não analisado",
            competitive_advantage="Análise de vantagem competitiva indisponível",
            feature_completeness="Não avaliado",
            feature_gaps=["Análise de gaps não disponível"],
            market_opportunities=["Análise de mercado não disponível"],
            competitive_threats=["Análise competitiva não disponível"],
            product_market_fit_score=50.0,
            innovation_score=50.0,
            user_value_score=50.0
        )

        agent.product_tools.analyze_product_data.return_value = mock_insights

        mock_response = Mock()
        mock_response.content = "Análise limitada devido a dados insuficientes"
        agent.agent.run.return_value = mock_response

        result = agent.analyze_product_strategy(empty_data)

        assert result["product_market_fit_score"] == 50.0
        assert result["innovation_score"] == 50.0
        assert result["user_value_score"] == 50.0
        assert result["product_maturity"] == "Indeterminado"

    @patch('backend.tools.agents.agents.product_owner_agent.ProductAnalysisTools')
    def test_high_maturity_product_analysis(self, mock_product_tools, agent, sample_product_data):
        """Testa análise de produto de alta maturidade"""
        # Mock para produto maduro
        mature_insights = ProductInsights(
            product_maturity="Maduro",
            market_positioning="Premium/Enterprise - Foco em valor e qualidade",
            competitive_advantage="Funcionalidades únicas e vantagem de performance",
            feature_completeness="Completo - Rica funcionalidade",
            feature_gaps=[],
            market_opportunities=[
                "Expansão internacional", "Novos verticais"
            ],
            competitive_threats=[
                "Líder de mercado: CompetitorX"
            ],
            product_market_fit_score=88.0,
            innovation_score=82.0,
            user_value_score=90.0
        )

        agent.product_tools.analyze_product_data.return_value = mature_insights

        mock_response = Mock()
        mock_response.content = "Estratégia para produto maduro focada em expansão"
        agent.agent.run.return_value = mock_response

        result = agent.analyze_product_strategy(sample_product_data)

        assert result["product_market_fit_score"] == 88.0
        assert result["strategy_category"] == "Líder de Mercado - Manter posição e inovar"
        assert result["analysis_summary"]["total_gaps"] == 0
        assert result["analysis_summary"]["total_opportunities"] == 2

    def test_roadmap_error_handling(self, agent):
        """Testa tratamento de erro na criação de roadmap"""
        # Simular erro na priorização
        with patch.object(agent.product_tools, 'prioritize_roadmap_features', side_effect=Exception("Erro de priorização")):
            result = agent.create_feature_roadmap([])

            assert "error" in result
            assert "Erro de priorização" in result["error"]

    def test_market_opportunities_error_handling(self, agent):
        """Testa tratamento de erro na avaliação de oportunidades"""
        # Simular erro na análise de mercado
        with patch.object(agent.product_tools, 'analyze_market_trends', side_effect=Exception("Erro de mercado")):
            result = agent.assess_market_opportunities({}, {})

            assert "error" in result
            assert "Erro de mercado" in result["error"]

    def test_get_recommendations_summary_error_handling(self, agent):
        """Testa tratamento de erro na extração de recomendações"""
        # Dados vazios - o método deve processar o que conseguir
        empty_result = {}

        recommendations = agent.get_strategic_recommendations_summary(
            empty_result)

        # Com dados vazios, pode retornar lista vazia ou recomendações básicas
        assert isinstance(recommendations, list)

    @patch('backend.tools.agents.agents.product_owner_agent.ProductAnalysisTools')
    def test_roadmap_with_strategic_context(self, mock_product_tools, agent, sample_features_data):
        """Testa criação de roadmap com contexto estratégico"""
        mock_priorities = [
            RoadmapPriority(
                feature="Strategic Feature",
                business_value=10,
                effort=6,
                user_impact=9,
                priority_score=15.0,
                category="Strategic"
            )
        ]

        agent.product_tools.prioritize_roadmap_features.return_value = mock_priorities

        mock_response = Mock()
        mock_response.content = "Roadmap estratégico com foco em crescimento"
        agent.agent.run.return_value = mock_response

        strategic_context = {"strategic_focus": "Expansão de mercado"}

        result = agent.create_feature_roadmap(
            sample_features_data, strategic_context)

        # Verificar se o contexto estratégico foi incluído no prompt
        call_args = agent.agent.run.call_args[0][0]
        assert "Expansão de mercado" in call_args

        assert result["high_priority_count"] == 1
        assert result["prioritized_features"][0]["priority_score"] == 15.0
