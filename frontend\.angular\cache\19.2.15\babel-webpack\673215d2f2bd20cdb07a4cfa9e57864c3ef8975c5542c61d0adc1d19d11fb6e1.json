{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, inject, Input, Directive, input, booleanAttribute, computed, ViewEncapsulation, ChangeDetectionStrategy, Component, NgModule } from '@angular/core';\nimport { isEmpty, isNotEmpty, uuid, hasClass, removeClass, addClass } from '@primeuix/utils';\nimport { SharedModule } from 'primeng/api';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { BaseStyle } from 'primeng/base';\nconst theme = ({\n  dt\n}) => `\n.p-badge {\n    display: inline-flex;\n    border-radius: ${dt('badge.border.radius')};\n    justify-content: center;\n    padding: ${dt('badge.padding')};\n    background: ${dt('badge.primary.background')};\n    color: ${dt('badge.primary.color')};\n    font-size: ${dt('badge.font.size')};\n    font-weight: ${dt('badge.font.weight')};\n    min-width: ${dt('badge.min.width')};\n    height: ${dt('badge.height')};\n    line-height: ${dt('badge.height')};\n}\n\n.p-badge-dot {\n    width: ${dt('badge.dot.size')};\n    min-width: ${dt('badge.dot.size')};\n    height: ${dt('badge.dot.size')};\n    border-radius: 50%;\n    padding: 0;\n}\n\n.p-badge-circle {\n    padding: 0;\n    border-radius: 50%;\n}\n\n.p-badge-secondary {\n    background: ${dt('badge.secondary.background')};\n    color: ${dt('badge.secondary.color')};\n}\n\n.p-badge-success {\n    background: ${dt('badge.success.background')};\n    color: ${dt('badge.success.color')};\n}\n\n.p-badge-info {\n    background: ${dt('badge.info.background')};\n    color: ${dt('badge.info.color')};\n}\n\n.p-badge-warn {\n    background: ${dt('badge.warn.background')};\n    color: ${dt('badge.warn.color')};\n}\n\n.p-badge-danger {\n    background: ${dt('badge.danger.background')};\n    color: ${dt('badge.danger.color')};\n}\n\n.p-badge-contrast {\n    background: ${dt('badge.contrast.background')};\n    color: ${dt('badge.contrast.color')};\n}\n\n.p-badge-sm {\n    font-size: ${dt('badge.sm.font.size')};\n    min-width: ${dt('badge.sm.min.width')};\n    height: ${dt('badge.sm.height')};\n    line-height: ${dt('badge.sm.height')};\n}\n\n.p-badge-lg {\n    font-size: ${dt('badge.lg.font.size')};\n    min-width: ${dt('badge.lg.min.width')};\n    height: ${dt('badge.lg.height')};\n    line-height: ${dt('badge.lg.height')};\n}\n\n.p-badge-xl {\n    font-size: ${dt('badge.xl.font.size')};\n    min-width: ${dt('badge.xl.min.width')};\n    height: ${dt('badge.xl.height')};\n    line-height: ${dt('badge.xl.height')};\n}\n\n/* For PrimeNG (directive)*/\n\n.p-overlay-badge {\n    position: relative;\n}\n\n.p-overlay-badge > .p-badge {\n    position: absolute;\n    top: 0;\n    inset-inline-end: 0;\n    transform: translate(50%, -50%);\n    transform-origin: 100% 0;\n    margin: 0;\n}\n`;\nconst classes = {\n  root: ({\n    props,\n    instance\n  }) => ['p-badge p-component', {\n    'p-badge-circle': isNotEmpty(props.value) && String(props.value).length === 1,\n    'p-badge-dot': isEmpty(props.value) && !instance.$slots.default,\n    'p-badge-sm': props.size === 'small',\n    'p-badge-lg': props.size === 'large',\n    'p-badge-xl': props.size === 'xlarge',\n    'p-badge-info': props.severity === 'info',\n    'p-badge-success': props.severity === 'success',\n    'p-badge-warn': props.severity === 'warn',\n    'p-badge-danger': props.severity === 'danger',\n    'p-badge-secondary': props.severity === 'secondary',\n    'p-badge-contrast': props.severity === 'contrast'\n  }]\n};\nclass BadgeStyle extends BaseStyle {\n  name = 'badge';\n  theme = theme;\n  classes = classes;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵBadgeStyle_BaseFactory;\n    return function BadgeStyle_Factory(__ngFactoryType__) {\n      return (ɵBadgeStyle_BaseFactory || (ɵBadgeStyle_BaseFactory = i0.ɵɵgetInheritedFactory(BadgeStyle)))(__ngFactoryType__ || BadgeStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: BadgeStyle,\n    factory: BadgeStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BadgeStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n *\n * Badge represents people using icons, labels and images.\n *\n * [Live Demo](https://www.primeng.org/badge)\n *\n * @module badgestyle\n *\n */\nvar BadgeClasses;\n(function (BadgeClasses) {\n  /**\n   * Class name of the root element\n   */\n  BadgeClasses[\"root\"] = \"p-badge\";\n})(BadgeClasses || (BadgeClasses = {}));\n\n/**\n * Badge Directive is directive usage of badge component.\n * @group Components\n */\nclass BadgeDirective extends BaseComponent {\n  /**\n   * When specified, disables the component.\n   * @group Props\n   */\n  disabled;\n  /**\n   * Size of the badge, valid options are \"large\" and \"xlarge\".\n   * @group Props\n   */\n  badgeSize;\n  /**\n   * Size of the badge, valid options are \"large\" and \"xlarge\".\n   * @group Props\n   * @deprecated use badgeSize instead.\n   */\n  set size(value) {\n    this._size = value;\n    console.log('size property is deprecated and will removed in v18, use badgeSize instead.');\n  }\n  get size() {\n    return this._size;\n  }\n  _size;\n  /**\n   * Severity type of the badge.\n   * @group Props\n   */\n  severity;\n  /**\n   * Value to display inside the badge.\n   * @group Props\n   */\n  value;\n  /**\n   * Inline style of the element.\n   * @group Props\n   */\n  badgeStyle;\n  /**\n   * Class of the element.\n   * @group Props\n   */\n  badgeStyleClass;\n  id;\n  badgeEl;\n  _componentStyle = inject(BadgeStyle);\n  get activeElement() {\n    return this.el.nativeElement.nodeName.indexOf('-') != -1 ? this.el.nativeElement.firstChild : this.el.nativeElement;\n  }\n  get canUpdateBadge() {\n    return this.id && !this.disabled;\n  }\n  constructor() {\n    super();\n  }\n  ngOnChanges({\n    value,\n    size,\n    severity,\n    disabled,\n    badgeStyle,\n    badgeStyleClass\n  }) {\n    super.ngOnChanges({\n      value,\n      size,\n      severity,\n      disabled\n    });\n    if (disabled) {\n      this.toggleDisableState();\n    }\n    if (!this.canUpdateBadge) {\n      return;\n    }\n    if (severity) {\n      this.setSeverity(severity.previousValue);\n    }\n    if (size) {\n      this.setSizeClasses();\n    }\n    if (value) {\n      this.setValue();\n    }\n    if (badgeStyle || badgeStyleClass) {\n      this.applyStyles();\n    }\n  }\n  ngAfterViewInit() {\n    super.ngAfterViewInit();\n    this.id = uuid('pn_id_') + '_badge';\n    this.renderBadgeContent();\n  }\n  setValue(element) {\n    const badge = element ?? this.document.getElementById(this.id);\n    if (!badge) {\n      return;\n    }\n    if (this.value != null) {\n      if (hasClass(badge, 'p-badge-dot')) {\n        removeClass(badge, 'p-badge-dot');\n      }\n      if (this.value && String(this.value).length === 1) {\n        addClass(badge, 'p-badge-circle');\n      } else {\n        removeClass(badge, 'p-badge-circle');\n      }\n    } else {\n      if (!hasClass(badge, 'p-badge-dot')) {\n        addClass(badge, 'p-badge-dot');\n      }\n      removeClass(badge, 'p-badge-circle');\n    }\n    badge.innerHTML = '';\n    const badgeValue = this.value != null ? String(this.value) : '';\n    this.renderer.appendChild(badge, this.document.createTextNode(badgeValue));\n  }\n  setSizeClasses(element) {\n    const badge = element ?? this.document.getElementById(this.id);\n    if (!badge) {\n      return;\n    }\n    if (this.badgeSize) {\n      if (this.badgeSize === 'large') {\n        addClass(badge, 'p-badge-lg');\n        removeClass(badge, 'p-badge-xl');\n      }\n      if (this.badgeSize === 'xlarge') {\n        addClass(badge, 'p-badge-xl');\n        removeClass(badge, 'p-badge-lg');\n      }\n    } else if (this.size && !this.badgeSize) {\n      if (this.size === 'large') {\n        addClass(badge, 'p-badge-lg');\n        removeClass(badge, 'p-badge-xl');\n      }\n      if (this.size === 'xlarge') {\n        addClass(badge, 'p-badge-xl');\n        removeClass(badge, 'p-badge-lg');\n      }\n    } else {\n      removeClass(badge, 'p-badge-lg');\n      removeClass(badge, 'p-badge-xl');\n    }\n  }\n  renderBadgeContent() {\n    if (this.disabled) {\n      return null;\n    }\n    const el = this.activeElement;\n    const badge = this.document.createElement('span');\n    badge.id = this.id;\n    badge.className = 'p-badge p-component';\n    this.setSeverity(null, badge);\n    this.setSizeClasses(badge);\n    this.setValue(badge);\n    addClass(el, 'p-overlay-badge');\n    this.renderer.appendChild(el, badge);\n    this.badgeEl = badge;\n    this.applyStyles();\n  }\n  applyStyles() {\n    if (this.badgeEl && this.badgeStyle && typeof this.badgeStyle === 'object') {\n      for (const [key, value] of Object.entries(this.badgeStyle)) {\n        this.renderer.setStyle(this.badgeEl, key, value);\n      }\n    }\n    if (this.badgeEl && this.badgeStyleClass) {\n      this.badgeEl.classList.add(...this.badgeStyleClass.split(' '));\n    }\n  }\n  setSeverity(oldSeverity, element) {\n    const badge = element ?? this.document.getElementById(this.id);\n    if (!badge) {\n      return;\n    }\n    if (this.severity) {\n      addClass(badge, `p-badge-${this.severity}`);\n    }\n    if (oldSeverity) {\n      removeClass(badge, `p-badge-${oldSeverity}`);\n    }\n  }\n  toggleDisableState() {\n    if (!this.id) {\n      return;\n    }\n    if (this.disabled) {\n      const badge = this.activeElement?.querySelector(`#${this.id}`);\n      if (badge) {\n        this.renderer.removeChild(this.activeElement, badge);\n      }\n    } else {\n      this.renderBadgeContent();\n    }\n  }\n  static ɵfac = function BadgeDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || BadgeDirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: BadgeDirective,\n    selectors: [[\"\", \"pBadge\", \"\"]],\n    inputs: {\n      disabled: [0, \"badgeDisabled\", \"disabled\"],\n      badgeSize: \"badgeSize\",\n      size: \"size\",\n      severity: \"severity\",\n      value: \"value\",\n      badgeStyle: \"badgeStyle\",\n      badgeStyleClass: \"badgeStyleClass\"\n    },\n    features: [i0.ɵɵProvidersFeature([BadgeStyle]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BadgeDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[pBadge]',\n      providers: [BadgeStyle],\n      standalone: true\n    }]\n  }], () => [], {\n    disabled: [{\n      type: Input,\n      args: ['badgeDisabled']\n    }],\n    badgeSize: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }],\n    severity: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    badgeStyle: [{\n      type: Input\n    }],\n    badgeStyleClass: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Badge is a small status indicator for another element.\n * @group Components\n */\nclass Badge extends BaseComponent {\n  /**\n   * Class of the element.\n   * @group Props\n   */\n  styleClass = input();\n  /**\n   * Inline style of the element.\n   * @group Props\n   */\n  style = input();\n  /**\n   * Size of the badge, valid options are \"large\" and \"xlarge\".\n   * @group Props\n   */\n  badgeSize = input();\n  /**\n   * Size of the badge, valid options are \"large\" and \"xlarge\".\n   * @group Props\n   */\n  size = input();\n  /**\n   * Severity type of the badge.\n   * @group Props\n   */\n  severity = input();\n  /**\n   * Value to display inside the badge.\n   * @group Props\n   */\n  value = input();\n  /**\n   * When specified, disables the component.\n   * @group Props\n   */\n  badgeDisabled = input(false, {\n    transform: booleanAttribute\n  });\n  _componentStyle = inject(BadgeStyle);\n  /**\n   * Computes the container class for the badge element based on its properties.\n   * @returns An object representing the CSS classes to be applied to the badge container.\n   */\n  containerClass = computed(() => {\n    let classes = 'p-badge p-component';\n    if (isNotEmpty(this.value()) && String(this.value()).length === 1) {\n      classes += ' p-badge-circle';\n    }\n    if (this.badgeSize() === 'large') {\n      classes += ' p-badge-lg';\n    } else if (this.badgeSize() === 'xlarge') {\n      classes += ' p-badge-xl';\n    } else if (this.badgeSize() === 'small') {\n      classes += ' p-badge-sm';\n    }\n    if (isEmpty(this.value())) {\n      classes += ' p-badge-dot';\n    }\n    if (this.styleClass()) {\n      classes += ` ${this.styleClass()}`;\n    }\n    if (this.severity()) {\n      classes += ` p-badge-${this.severity()}`;\n    }\n    return classes;\n  });\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵBadge_BaseFactory;\n    return function Badge_Factory(__ngFactoryType__) {\n      return (ɵBadge_BaseFactory || (ɵBadge_BaseFactory = i0.ɵɵgetInheritedFactory(Badge)))(__ngFactoryType__ || Badge);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Badge,\n    selectors: [[\"p-badge\"]],\n    hostVars: 6,\n    hostBindings: function Badge_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵstyleMap(ctx.style());\n        i0.ɵɵclassMap(ctx.containerClass());\n        i0.ɵɵstyleProp(\"display\", ctx.badgeDisabled() ? \"none\" : null);\n      }\n    },\n    inputs: {\n      styleClass: [1, \"styleClass\"],\n      style: [1, \"style\"],\n      badgeSize: [1, \"badgeSize\"],\n      size: [1, \"size\"],\n      severity: [1, \"severity\"],\n      value: [1, \"value\"],\n      badgeDisabled: [1, \"badgeDisabled\"]\n    },\n    features: [i0.ɵɵProvidersFeature([BadgeStyle]), i0.ɵɵInheritDefinitionFeature],\n    decls: 1,\n    vars: 1,\n    template: function Badge_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtext(0);\n      }\n      if (rf & 2) {\n        i0.ɵɵtextInterpolate(ctx.value());\n      }\n    },\n    dependencies: [CommonModule, SharedModule],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Badge, [{\n    type: Component,\n    args: [{\n      selector: 'p-badge',\n      template: `{{ value() }}`,\n      standalone: true,\n      imports: [CommonModule, SharedModule],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [BadgeStyle],\n      host: {\n        '[class]': 'containerClass()',\n        '[style.display]': 'badgeDisabled() ? \"none\" : null',\n        '[style]': 'style()'\n      }\n    }]\n  }], null, null);\n})();\nclass BadgeModule {\n  static ɵfac = function BadgeModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || BadgeModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: BadgeModule,\n    imports: [Badge, BadgeDirective, SharedModule],\n    exports: [Badge, BadgeDirective, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [Badge, SharedModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BadgeModule, [{\n    type: NgModule,\n    args: [{\n      imports: [Badge, BadgeDirective, SharedModule],\n      exports: [Badge, BadgeDirective, SharedModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Badge, BadgeClasses, BadgeDirective, BadgeModule, BadgeStyle };", "map": {"version": 3, "names": ["CommonModule", "i0", "Injectable", "inject", "Input", "Directive", "input", "booleanAttribute", "computed", "ViewEncapsulation", "ChangeDetectionStrategy", "Component", "NgModule", "isEmpty", "isNotEmpty", "uuid", "hasClass", "removeClass", "addClass", "SharedModule", "BaseComponent", "BaseStyle", "theme", "dt", "classes", "root", "props", "instance", "value", "String", "length", "$slots", "default", "size", "severity", "BadgeStyle", "name", "ɵfac", "ɵBadgeStyle_BaseFactory", "BadgeStyle_Factory", "__ngFactoryType__", "ɵɵgetInheritedFactory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "BadgeClasses", "BadgeDirective", "disabled", "badgeSize", "_size", "console", "log", "badgeStyle", "badgeStyleClass", "id", "badgeEl", "_componentStyle", "activeElement", "el", "nativeElement", "nodeName", "indexOf", "<PERSON><PERSON><PERSON><PERSON>", "canUpdateBadge", "constructor", "ngOnChanges", "toggleDisableState", "setSeverity", "previousValue", "setSizeClasses", "setValue", "applyStyles", "ngAfterViewInit", "renderBadgeContent", "element", "badge", "document", "getElementById", "innerHTML", "badgeValue", "renderer", "append<PERSON><PERSON><PERSON>", "createTextNode", "createElement", "className", "key", "Object", "entries", "setStyle", "classList", "add", "split", "oldSeverity", "querySelector", "<PERSON><PERSON><PERSON><PERSON>", "BadgeDirective_Factory", "ɵdir", "ɵɵdefineDirective", "selectors", "inputs", "features", "ɵɵProvidersFeature", "ɵɵInheritDefinitionFeature", "ɵɵNgOnChangesFeature", "args", "selector", "providers", "standalone", "Badge", "styleClass", "style", "badgeDisabled", "transform", "containerClass", "ɵBadge_BaseFactory", "Badge_Factory", "ɵcmp", "ɵɵdefineComponent", "hostVars", "hostBindings", "Badge_HostBindings", "rf", "ctx", "ɵɵstyleMap", "ɵɵclassMap", "ɵɵstyleProp", "decls", "vars", "template", "Badge_Template", "ɵɵtext", "ɵɵtextInterpolate", "dependencies", "encapsulation", "changeDetection", "imports", "OnPush", "None", "host", "BadgeModule", "BadgeModule_Factory", "ɵmod", "ɵɵdefineNgModule", "exports", "ɵinj", "ɵɵdefineInjector"], "sources": ["C:/projetos/scope-ai/frontend/node_modules/primeng/fesm2022/primeng-badge.mjs"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, inject, Input, Directive, input, booleanAttribute, computed, ViewEncapsulation, ChangeDetectionStrategy, Component, NgModule } from '@angular/core';\nimport { isEmpty, isNotEmpty, uuid, hasClass, removeClass, addClass } from '@primeuix/utils';\nimport { SharedModule } from 'primeng/api';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { BaseStyle } from 'primeng/base';\n\nconst theme = ({ dt }) => `\n.p-badge {\n    display: inline-flex;\n    border-radius: ${dt('badge.border.radius')};\n    justify-content: center;\n    padding: ${dt('badge.padding')};\n    background: ${dt('badge.primary.background')};\n    color: ${dt('badge.primary.color')};\n    font-size: ${dt('badge.font.size')};\n    font-weight: ${dt('badge.font.weight')};\n    min-width: ${dt('badge.min.width')};\n    height: ${dt('badge.height')};\n    line-height: ${dt('badge.height')};\n}\n\n.p-badge-dot {\n    width: ${dt('badge.dot.size')};\n    min-width: ${dt('badge.dot.size')};\n    height: ${dt('badge.dot.size')};\n    border-radius: 50%;\n    padding: 0;\n}\n\n.p-badge-circle {\n    padding: 0;\n    border-radius: 50%;\n}\n\n.p-badge-secondary {\n    background: ${dt('badge.secondary.background')};\n    color: ${dt('badge.secondary.color')};\n}\n\n.p-badge-success {\n    background: ${dt('badge.success.background')};\n    color: ${dt('badge.success.color')};\n}\n\n.p-badge-info {\n    background: ${dt('badge.info.background')};\n    color: ${dt('badge.info.color')};\n}\n\n.p-badge-warn {\n    background: ${dt('badge.warn.background')};\n    color: ${dt('badge.warn.color')};\n}\n\n.p-badge-danger {\n    background: ${dt('badge.danger.background')};\n    color: ${dt('badge.danger.color')};\n}\n\n.p-badge-contrast {\n    background: ${dt('badge.contrast.background')};\n    color: ${dt('badge.contrast.color')};\n}\n\n.p-badge-sm {\n    font-size: ${dt('badge.sm.font.size')};\n    min-width: ${dt('badge.sm.min.width')};\n    height: ${dt('badge.sm.height')};\n    line-height: ${dt('badge.sm.height')};\n}\n\n.p-badge-lg {\n    font-size: ${dt('badge.lg.font.size')};\n    min-width: ${dt('badge.lg.min.width')};\n    height: ${dt('badge.lg.height')};\n    line-height: ${dt('badge.lg.height')};\n}\n\n.p-badge-xl {\n    font-size: ${dt('badge.xl.font.size')};\n    min-width: ${dt('badge.xl.min.width')};\n    height: ${dt('badge.xl.height')};\n    line-height: ${dt('badge.xl.height')};\n}\n\n/* For PrimeNG (directive)*/\n\n.p-overlay-badge {\n    position: relative;\n}\n\n.p-overlay-badge > .p-badge {\n    position: absolute;\n    top: 0;\n    inset-inline-end: 0;\n    transform: translate(50%, -50%);\n    transform-origin: 100% 0;\n    margin: 0;\n}\n`;\nconst classes = {\n    root: ({ props, instance }) => [\n        'p-badge p-component',\n        {\n            'p-badge-circle': isNotEmpty(props.value) && String(props.value).length === 1,\n            'p-badge-dot': isEmpty(props.value) && !instance.$slots.default,\n            'p-badge-sm': props.size === 'small',\n            'p-badge-lg': props.size === 'large',\n            'p-badge-xl': props.size === 'xlarge',\n            'p-badge-info': props.severity === 'info',\n            'p-badge-success': props.severity === 'success',\n            'p-badge-warn': props.severity === 'warn',\n            'p-badge-danger': props.severity === 'danger',\n            'p-badge-secondary': props.severity === 'secondary',\n            'p-badge-contrast': props.severity === 'contrast'\n        }\n    ]\n};\nclass BadgeStyle extends BaseStyle {\n    name = 'badge';\n    theme = theme;\n    classes = classes;\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: BadgeStyle, deps: null, target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: BadgeStyle });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: BadgeStyle, decorators: [{\n            type: Injectable\n        }] });\n/**\n *\n * Badge represents people using icons, labels and images.\n *\n * [Live Demo](https://www.primeng.org/badge)\n *\n * @module badgestyle\n *\n */\nvar BadgeClasses;\n(function (BadgeClasses) {\n    /**\n     * Class name of the root element\n     */\n    BadgeClasses[\"root\"] = \"p-badge\";\n})(BadgeClasses || (BadgeClasses = {}));\n\n/**\n * Badge Directive is directive usage of badge component.\n * @group Components\n */\nclass BadgeDirective extends BaseComponent {\n    /**\n     * When specified, disables the component.\n     * @group Props\n     */\n    disabled;\n    /**\n     * Size of the badge, valid options are \"large\" and \"xlarge\".\n     * @group Props\n     */\n    badgeSize;\n    /**\n     * Size of the badge, valid options are \"large\" and \"xlarge\".\n     * @group Props\n     * @deprecated use badgeSize instead.\n     */\n    set size(value) {\n        this._size = value;\n        console.log('size property is deprecated and will removed in v18, use badgeSize instead.');\n    }\n    get size() {\n        return this._size;\n    }\n    _size;\n    /**\n     * Severity type of the badge.\n     * @group Props\n     */\n    severity;\n    /**\n     * Value to display inside the badge.\n     * @group Props\n     */\n    value;\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    badgeStyle;\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    badgeStyleClass;\n    id;\n    badgeEl;\n    _componentStyle = inject(BadgeStyle);\n    get activeElement() {\n        return this.el.nativeElement.nodeName.indexOf('-') != -1 ? this.el.nativeElement.firstChild : this.el.nativeElement;\n    }\n    get canUpdateBadge() {\n        return this.id && !this.disabled;\n    }\n    constructor() {\n        super();\n    }\n    ngOnChanges({ value, size, severity, disabled, badgeStyle, badgeStyleClass }) {\n        super.ngOnChanges({ value, size, severity, disabled });\n        if (disabled) {\n            this.toggleDisableState();\n        }\n        if (!this.canUpdateBadge) {\n            return;\n        }\n        if (severity) {\n            this.setSeverity(severity.previousValue);\n        }\n        if (size) {\n            this.setSizeClasses();\n        }\n        if (value) {\n            this.setValue();\n        }\n        if (badgeStyle || badgeStyleClass) {\n            this.applyStyles();\n        }\n    }\n    ngAfterViewInit() {\n        super.ngAfterViewInit();\n        this.id = uuid('pn_id_') + '_badge';\n        this.renderBadgeContent();\n    }\n    setValue(element) {\n        const badge = element ?? this.document.getElementById(this.id);\n        if (!badge) {\n            return;\n        }\n        if (this.value != null) {\n            if (hasClass(badge, 'p-badge-dot')) {\n                removeClass(badge, 'p-badge-dot');\n            }\n            if (this.value && String(this.value).length === 1) {\n                addClass(badge, 'p-badge-circle');\n            }\n            else {\n                removeClass(badge, 'p-badge-circle');\n            }\n        }\n        else {\n            if (!hasClass(badge, 'p-badge-dot')) {\n                addClass(badge, 'p-badge-dot');\n            }\n            removeClass(badge, 'p-badge-circle');\n        }\n        badge.innerHTML = '';\n        const badgeValue = this.value != null ? String(this.value) : '';\n        this.renderer.appendChild(badge, this.document.createTextNode(badgeValue));\n    }\n    setSizeClasses(element) {\n        const badge = element ?? this.document.getElementById(this.id);\n        if (!badge) {\n            return;\n        }\n        if (this.badgeSize) {\n            if (this.badgeSize === 'large') {\n                addClass(badge, 'p-badge-lg');\n                removeClass(badge, 'p-badge-xl');\n            }\n            if (this.badgeSize === 'xlarge') {\n                addClass(badge, 'p-badge-xl');\n                removeClass(badge, 'p-badge-lg');\n            }\n        }\n        else if (this.size && !this.badgeSize) {\n            if (this.size === 'large') {\n                addClass(badge, 'p-badge-lg');\n                removeClass(badge, 'p-badge-xl');\n            }\n            if (this.size === 'xlarge') {\n                addClass(badge, 'p-badge-xl');\n                removeClass(badge, 'p-badge-lg');\n            }\n        }\n        else {\n            removeClass(badge, 'p-badge-lg');\n            removeClass(badge, 'p-badge-xl');\n        }\n    }\n    renderBadgeContent() {\n        if (this.disabled) {\n            return null;\n        }\n        const el = this.activeElement;\n        const badge = this.document.createElement('span');\n        badge.id = this.id;\n        badge.className = 'p-badge p-component';\n        this.setSeverity(null, badge);\n        this.setSizeClasses(badge);\n        this.setValue(badge);\n        addClass(el, 'p-overlay-badge');\n        this.renderer.appendChild(el, badge);\n        this.badgeEl = badge;\n        this.applyStyles();\n    }\n    applyStyles() {\n        if (this.badgeEl && this.badgeStyle && typeof this.badgeStyle === 'object') {\n            for (const [key, value] of Object.entries(this.badgeStyle)) {\n                this.renderer.setStyle(this.badgeEl, key, value);\n            }\n        }\n        if (this.badgeEl && this.badgeStyleClass) {\n            this.badgeEl.classList.add(...this.badgeStyleClass.split(' '));\n        }\n    }\n    setSeverity(oldSeverity, element) {\n        const badge = element ?? this.document.getElementById(this.id);\n        if (!badge) {\n            return;\n        }\n        if (this.severity) {\n            addClass(badge, `p-badge-${this.severity}`);\n        }\n        if (oldSeverity) {\n            removeClass(badge, `p-badge-${oldSeverity}`);\n        }\n    }\n    toggleDisableState() {\n        if (!this.id) {\n            return;\n        }\n        if (this.disabled) {\n            const badge = this.activeElement?.querySelector(`#${this.id}`);\n            if (badge) {\n                this.renderer.removeChild(this.activeElement, badge);\n            }\n        }\n        else {\n            this.renderBadgeContent();\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: BadgeDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.10\", type: BadgeDirective, isStandalone: true, selector: \"[pBadge]\", inputs: { disabled: [\"badgeDisabled\", \"disabled\"], badgeSize: \"badgeSize\", size: \"size\", severity: \"severity\", value: \"value\", badgeStyle: \"badgeStyle\", badgeStyleClass: \"badgeStyleClass\" }, providers: [BadgeStyle], usesInheritance: true, usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: BadgeDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[pBadge]',\n                    providers: [BadgeStyle],\n                    standalone: true\n                }]\n        }], ctorParameters: () => [], propDecorators: { disabled: [{\n                type: Input,\n                args: ['badgeDisabled']\n            }], badgeSize: [{\n                type: Input\n            }], size: [{\n                type: Input\n            }], severity: [{\n                type: Input\n            }], value: [{\n                type: Input\n            }], badgeStyle: [{\n                type: Input\n            }], badgeStyleClass: [{\n                type: Input\n            }] } });\n/**\n * Badge is a small status indicator for another element.\n * @group Components\n */\nclass Badge extends BaseComponent {\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    styleClass = input();\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    style = input();\n    /**\n     * Size of the badge, valid options are \"large\" and \"xlarge\".\n     * @group Props\n     */\n    badgeSize = input();\n    /**\n     * Size of the badge, valid options are \"large\" and \"xlarge\".\n     * @group Props\n     */\n    size = input();\n    /**\n     * Severity type of the badge.\n     * @group Props\n     */\n    severity = input();\n    /**\n     * Value to display inside the badge.\n     * @group Props\n     */\n    value = input();\n    /**\n     * When specified, disables the component.\n     * @group Props\n     */\n    badgeDisabled = input(false, { transform: booleanAttribute });\n    _componentStyle = inject(BadgeStyle);\n    /**\n     * Computes the container class for the badge element based on its properties.\n     * @returns An object representing the CSS classes to be applied to the badge container.\n     */\n    containerClass = computed(() => {\n        let classes = 'p-badge p-component';\n        if (isNotEmpty(this.value()) && String(this.value()).length === 1) {\n            classes += ' p-badge-circle';\n        }\n        if (this.badgeSize() === 'large') {\n            classes += ' p-badge-lg';\n        }\n        else if (this.badgeSize() === 'xlarge') {\n            classes += ' p-badge-xl';\n        }\n        else if (this.badgeSize() === 'small') {\n            classes += ' p-badge-sm';\n        }\n        if (isEmpty(this.value())) {\n            classes += ' p-badge-dot';\n        }\n        if (this.styleClass()) {\n            classes += ` ${this.styleClass()}`;\n        }\n        if (this.severity()) {\n            classes += ` p-badge-${this.severity()}`;\n        }\n        return classes;\n    });\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: Badge, deps: null, target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.1.0\", version: \"19.2.10\", type: Badge, isStandalone: true, selector: \"p-badge\", inputs: { styleClass: { classPropertyName: \"styleClass\", publicName: \"styleClass\", isSignal: true, isRequired: false, transformFunction: null }, style: { classPropertyName: \"style\", publicName: \"style\", isSignal: true, isRequired: false, transformFunction: null }, badgeSize: { classPropertyName: \"badgeSize\", publicName: \"badgeSize\", isSignal: true, isRequired: false, transformFunction: null }, size: { classPropertyName: \"size\", publicName: \"size\", isSignal: true, isRequired: false, transformFunction: null }, severity: { classPropertyName: \"severity\", publicName: \"severity\", isSignal: true, isRequired: false, transformFunction: null }, value: { classPropertyName: \"value\", publicName: \"value\", isSignal: true, isRequired: false, transformFunction: null }, badgeDisabled: { classPropertyName: \"badgeDisabled\", publicName: \"badgeDisabled\", isSignal: true, isRequired: false, transformFunction: null } }, host: { properties: { \"class\": \"containerClass()\", \"style.display\": \"badgeDisabled() ? \\\"none\\\" : null\", \"style\": \"style()\" } }, providers: [BadgeStyle], usesInheritance: true, ngImport: i0, template: `{{ value() }}`, isInline: true, dependencies: [{ kind: \"ngmodule\", type: CommonModule }, { kind: \"ngmodule\", type: SharedModule }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: Badge, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'p-badge',\n                    template: `{{ value() }}`,\n                    standalone: true,\n                    imports: [CommonModule, SharedModule],\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    providers: [BadgeStyle],\n                    host: {\n                        '[class]': 'containerClass()',\n                        '[style.display]': 'badgeDisabled() ? \"none\" : null',\n                        '[style]': 'style()'\n                    }\n                }]\n        }] });\nclass BadgeModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: BadgeModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.10\", ngImport: i0, type: BadgeModule, imports: [Badge, BadgeDirective, SharedModule], exports: [Badge, BadgeDirective, SharedModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: BadgeModule, imports: [Badge, SharedModule, SharedModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: BadgeModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [Badge, BadgeDirective, SharedModule],\n                    exports: [Badge, BadgeDirective, SharedModule]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Badge, BadgeClasses, BadgeDirective, BadgeModule, BadgeStyle };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,MAAM,EAAEC,KAAK,EAAEC,SAAS,EAAEC,KAAK,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AACxK,SAASC,OAAO,EAAEC,UAAU,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,QAAQ,QAAQ,iBAAiB;AAC5F,SAASC,YAAY,QAAQ,aAAa;AAC1C,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,SAAS,QAAQ,cAAc;AAExC,MAAMC,KAAK,GAAGA,CAAC;EAAEC;AAAG,CAAC,KAAK;AAC1B;AACA;AACA,qBAAqBA,EAAE,CAAC,qBAAqB,CAAC;AAC9C;AACA,eAAeA,EAAE,CAAC,eAAe,CAAC;AAClC,kBAAkBA,EAAE,CAAC,0BAA0B,CAAC;AAChD,aAAaA,EAAE,CAAC,qBAAqB,CAAC;AACtC,iBAAiBA,EAAE,CAAC,iBAAiB,CAAC;AACtC,mBAAmBA,EAAE,CAAC,mBAAmB,CAAC;AAC1C,iBAAiBA,EAAE,CAAC,iBAAiB,CAAC;AACtC,cAAcA,EAAE,CAAC,cAAc,CAAC;AAChC,mBAAmBA,EAAE,CAAC,cAAc,CAAC;AACrC;AACA;AACA;AACA,aAAaA,EAAE,CAAC,gBAAgB,CAAC;AACjC,iBAAiBA,EAAE,CAAC,gBAAgB,CAAC;AACrC,cAAcA,EAAE,CAAC,gBAAgB,CAAC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,4BAA4B,CAAC;AAClD,aAAaA,EAAE,CAAC,uBAAuB,CAAC;AACxC;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,0BAA0B,CAAC;AAChD,aAAaA,EAAE,CAAC,qBAAqB,CAAC;AACtC;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,uBAAuB,CAAC;AAC7C,aAAaA,EAAE,CAAC,kBAAkB,CAAC;AACnC;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,uBAAuB,CAAC;AAC7C,aAAaA,EAAE,CAAC,kBAAkB,CAAC;AACnC;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,yBAAyB,CAAC;AAC/C,aAAaA,EAAE,CAAC,oBAAoB,CAAC;AACrC;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,2BAA2B,CAAC;AACjD,aAAaA,EAAE,CAAC,sBAAsB,CAAC;AACvC;AACA;AACA;AACA,iBAAiBA,EAAE,CAAC,oBAAoB,CAAC;AACzC,iBAAiBA,EAAE,CAAC,oBAAoB,CAAC;AACzC,cAAcA,EAAE,CAAC,iBAAiB,CAAC;AACnC,mBAAmBA,EAAE,CAAC,iBAAiB,CAAC;AACxC;AACA;AACA;AACA,iBAAiBA,EAAE,CAAC,oBAAoB,CAAC;AACzC,iBAAiBA,EAAE,CAAC,oBAAoB,CAAC;AACzC,cAAcA,EAAE,CAAC,iBAAiB,CAAC;AACnC,mBAAmBA,EAAE,CAAC,iBAAiB,CAAC;AACxC;AACA;AACA;AACA,iBAAiBA,EAAE,CAAC,oBAAoB,CAAC;AACzC,iBAAiBA,EAAE,CAAC,oBAAoB,CAAC;AACzC,cAAcA,EAAE,CAAC,iBAAiB,CAAC;AACnC,mBAAmBA,EAAE,CAAC,iBAAiB,CAAC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,MAAMC,OAAO,GAAG;EACZC,IAAI,EAAEA,CAAC;IAAEC,KAAK;IAAEC;EAAS,CAAC,KAAK,CAC3B,qBAAqB,EACrB;IACI,gBAAgB,EAAEb,UAAU,CAACY,KAAK,CAACE,KAAK,CAAC,IAAIC,MAAM,CAACH,KAAK,CAACE,KAAK,CAAC,CAACE,MAAM,KAAK,CAAC;IAC7E,aAAa,EAAEjB,OAAO,CAACa,KAAK,CAACE,KAAK,CAAC,IAAI,CAACD,QAAQ,CAACI,MAAM,CAACC,OAAO;IAC/D,YAAY,EAAEN,KAAK,CAACO,IAAI,KAAK,OAAO;IACpC,YAAY,EAAEP,KAAK,CAACO,IAAI,KAAK,OAAO;IACpC,YAAY,EAAEP,KAAK,CAACO,IAAI,KAAK,QAAQ;IACrC,cAAc,EAAEP,KAAK,CAACQ,QAAQ,KAAK,MAAM;IACzC,iBAAiB,EAAER,KAAK,CAACQ,QAAQ,KAAK,SAAS;IAC/C,cAAc,EAAER,KAAK,CAACQ,QAAQ,KAAK,MAAM;IACzC,gBAAgB,EAAER,KAAK,CAACQ,QAAQ,KAAK,QAAQ;IAC7C,mBAAmB,EAAER,KAAK,CAACQ,QAAQ,KAAK,WAAW;IACnD,kBAAkB,EAAER,KAAK,CAACQ,QAAQ,KAAK;EAC3C,CAAC;AAET,CAAC;AACD,MAAMC,UAAU,SAASd,SAAS,CAAC;EAC/Be,IAAI,GAAG,OAAO;EACdd,KAAK,GAAGA,KAAK;EACbE,OAAO,GAAGA,OAAO;EACjB,OAAOa,IAAI;IAAA,IAAAC,uBAAA;IAAA,gBAAAC,mBAAAC,iBAAA;MAAA,QAAAF,uBAAA,KAAAA,uBAAA,GAA+ErC,EAAE,CAAAwC,qBAAA,CAAQN,UAAU,IAAAK,iBAAA,IAAVL,UAAU;IAAA;EAAA;EAC9G,OAAOO,KAAK,kBAD8EzC,EAAE,CAAA0C,kBAAA;IAAAC,KAAA,EACYT,UAAU;IAAAU,OAAA,EAAVV,UAAU,CAAAE;EAAA;AACtH;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KAH8F7C,EAAE,CAAA8C,iBAAA,CAGJZ,UAAU,EAAc,CAAC;IACzGa,IAAI,EAAE9C;EACV,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI+C,YAAY;AAChB,CAAC,UAAUA,YAAY,EAAE;EACrB;AACJ;AACA;EACIA,YAAY,CAAC,MAAM,CAAC,GAAG,SAAS;AACpC,CAAC,EAAEA,YAAY,KAAKA,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEvC;AACA;AACA;AACA;AACA,MAAMC,cAAc,SAAS9B,aAAa,CAAC;EACvC;AACJ;AACA;AACA;EACI+B,QAAQ;EACR;AACJ;AACA;AACA;EACIC,SAAS;EACT;AACJ;AACA;AACA;AACA;EACI,IAAInB,IAAIA,CAACL,KAAK,EAAE;IACZ,IAAI,CAACyB,KAAK,GAAGzB,KAAK;IAClB0B,OAAO,CAACC,GAAG,CAAC,6EAA6E,CAAC;EAC9F;EACA,IAAItB,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACoB,KAAK;EACrB;EACAA,KAAK;EACL;AACJ;AACA;AACA;EACInB,QAAQ;EACR;AACJ;AACA;AACA;EACIN,KAAK;EACL;AACJ;AACA;AACA;EACI4B,UAAU;EACV;AACJ;AACA;AACA;EACIC,eAAe;EACfC,EAAE;EACFC,OAAO;EACPC,eAAe,GAAGzD,MAAM,CAACgC,UAAU,CAAC;EACpC,IAAI0B,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACC,EAAE,CAACC,aAAa,CAACC,QAAQ,CAACC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAACH,EAAE,CAACC,aAAa,CAACG,UAAU,GAAG,IAAI,CAACJ,EAAE,CAACC,aAAa;EACvH;EACA,IAAII,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACT,EAAE,IAAI,CAAC,IAAI,CAACP,QAAQ;EACpC;EACAiB,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;EACX;EACAC,WAAWA,CAAC;IAAEzC,KAAK;IAAEK,IAAI;IAAEC,QAAQ;IAAEiB,QAAQ;IAAEK,UAAU;IAAEC;EAAgB,CAAC,EAAE;IAC1E,KAAK,CAACY,WAAW,CAAC;MAAEzC,KAAK;MAAEK,IAAI;MAAEC,QAAQ;MAAEiB;IAAS,CAAC,CAAC;IACtD,IAAIA,QAAQ,EAAE;MACV,IAAI,CAACmB,kBAAkB,CAAC,CAAC;IAC7B;IACA,IAAI,CAAC,IAAI,CAACH,cAAc,EAAE;MACtB;IACJ;IACA,IAAIjC,QAAQ,EAAE;MACV,IAAI,CAACqC,WAAW,CAACrC,QAAQ,CAACsC,aAAa,CAAC;IAC5C;IACA,IAAIvC,IAAI,EAAE;MACN,IAAI,CAACwC,cAAc,CAAC,CAAC;IACzB;IACA,IAAI7C,KAAK,EAAE;MACP,IAAI,CAAC8C,QAAQ,CAAC,CAAC;IACnB;IACA,IAAIlB,UAAU,IAAIC,eAAe,EAAE;MAC/B,IAAI,CAACkB,WAAW,CAAC,CAAC;IACtB;EACJ;EACAC,eAAeA,CAAA,EAAG;IACd,KAAK,CAACA,eAAe,CAAC,CAAC;IACvB,IAAI,CAAClB,EAAE,GAAG3C,IAAI,CAAC,QAAQ,CAAC,GAAG,QAAQ;IACnC,IAAI,CAAC8D,kBAAkB,CAAC,CAAC;EAC7B;EACAH,QAAQA,CAACI,OAAO,EAAE;IACd,MAAMC,KAAK,GAAGD,OAAO,IAAI,IAAI,CAACE,QAAQ,CAACC,cAAc,CAAC,IAAI,CAACvB,EAAE,CAAC;IAC9D,IAAI,CAACqB,KAAK,EAAE;MACR;IACJ;IACA,IAAI,IAAI,CAACnD,KAAK,IAAI,IAAI,EAAE;MACpB,IAAIZ,QAAQ,CAAC+D,KAAK,EAAE,aAAa,CAAC,EAAE;QAChC9D,WAAW,CAAC8D,KAAK,EAAE,aAAa,CAAC;MACrC;MACA,IAAI,IAAI,CAACnD,KAAK,IAAIC,MAAM,CAAC,IAAI,CAACD,KAAK,CAAC,CAACE,MAAM,KAAK,CAAC,EAAE;QAC/CZ,QAAQ,CAAC6D,KAAK,EAAE,gBAAgB,CAAC;MACrC,CAAC,MACI;QACD9D,WAAW,CAAC8D,KAAK,EAAE,gBAAgB,CAAC;MACxC;IACJ,CAAC,MACI;MACD,IAAI,CAAC/D,QAAQ,CAAC+D,KAAK,EAAE,aAAa,CAAC,EAAE;QACjC7D,QAAQ,CAAC6D,KAAK,EAAE,aAAa,CAAC;MAClC;MACA9D,WAAW,CAAC8D,KAAK,EAAE,gBAAgB,CAAC;IACxC;IACAA,KAAK,CAACG,SAAS,GAAG,EAAE;IACpB,MAAMC,UAAU,GAAG,IAAI,CAACvD,KAAK,IAAI,IAAI,GAAGC,MAAM,CAAC,IAAI,CAACD,KAAK,CAAC,GAAG,EAAE;IAC/D,IAAI,CAACwD,QAAQ,CAACC,WAAW,CAACN,KAAK,EAAE,IAAI,CAACC,QAAQ,CAACM,cAAc,CAACH,UAAU,CAAC,CAAC;EAC9E;EACAV,cAAcA,CAACK,OAAO,EAAE;IACpB,MAAMC,KAAK,GAAGD,OAAO,IAAI,IAAI,CAACE,QAAQ,CAACC,cAAc,CAAC,IAAI,CAACvB,EAAE,CAAC;IAC9D,IAAI,CAACqB,KAAK,EAAE;MACR;IACJ;IACA,IAAI,IAAI,CAAC3B,SAAS,EAAE;MAChB,IAAI,IAAI,CAACA,SAAS,KAAK,OAAO,EAAE;QAC5BlC,QAAQ,CAAC6D,KAAK,EAAE,YAAY,CAAC;QAC7B9D,WAAW,CAAC8D,KAAK,EAAE,YAAY,CAAC;MACpC;MACA,IAAI,IAAI,CAAC3B,SAAS,KAAK,QAAQ,EAAE;QAC7BlC,QAAQ,CAAC6D,KAAK,EAAE,YAAY,CAAC;QAC7B9D,WAAW,CAAC8D,KAAK,EAAE,YAAY,CAAC;MACpC;IACJ,CAAC,MACI,IAAI,IAAI,CAAC9C,IAAI,IAAI,CAAC,IAAI,CAACmB,SAAS,EAAE;MACnC,IAAI,IAAI,CAACnB,IAAI,KAAK,OAAO,EAAE;QACvBf,QAAQ,CAAC6D,KAAK,EAAE,YAAY,CAAC;QAC7B9D,WAAW,CAAC8D,KAAK,EAAE,YAAY,CAAC;MACpC;MACA,IAAI,IAAI,CAAC9C,IAAI,KAAK,QAAQ,EAAE;QACxBf,QAAQ,CAAC6D,KAAK,EAAE,YAAY,CAAC;QAC7B9D,WAAW,CAAC8D,KAAK,EAAE,YAAY,CAAC;MACpC;IACJ,CAAC,MACI;MACD9D,WAAW,CAAC8D,KAAK,EAAE,YAAY,CAAC;MAChC9D,WAAW,CAAC8D,KAAK,EAAE,YAAY,CAAC;IACpC;EACJ;EACAF,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAAC1B,QAAQ,EAAE;MACf,OAAO,IAAI;IACf;IACA,MAAMW,EAAE,GAAG,IAAI,CAACD,aAAa;IAC7B,MAAMkB,KAAK,GAAG,IAAI,CAACC,QAAQ,CAACO,aAAa,CAAC,MAAM,CAAC;IACjDR,KAAK,CAACrB,EAAE,GAAG,IAAI,CAACA,EAAE;IAClBqB,KAAK,CAACS,SAAS,GAAG,qBAAqB;IACvC,IAAI,CAACjB,WAAW,CAAC,IAAI,EAAEQ,KAAK,CAAC;IAC7B,IAAI,CAACN,cAAc,CAACM,KAAK,CAAC;IAC1B,IAAI,CAACL,QAAQ,CAACK,KAAK,CAAC;IACpB7D,QAAQ,CAAC4C,EAAE,EAAE,iBAAiB,CAAC;IAC/B,IAAI,CAACsB,QAAQ,CAACC,WAAW,CAACvB,EAAE,EAAEiB,KAAK,CAAC;IACpC,IAAI,CAACpB,OAAO,GAAGoB,KAAK;IACpB,IAAI,CAACJ,WAAW,CAAC,CAAC;EACtB;EACAA,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAChB,OAAO,IAAI,IAAI,CAACH,UAAU,IAAI,OAAO,IAAI,CAACA,UAAU,KAAK,QAAQ,EAAE;MACxE,KAAK,MAAM,CAACiC,GAAG,EAAE7D,KAAK,CAAC,IAAI8D,MAAM,CAACC,OAAO,CAAC,IAAI,CAACnC,UAAU,CAAC,EAAE;QACxD,IAAI,CAAC4B,QAAQ,CAACQ,QAAQ,CAAC,IAAI,CAACjC,OAAO,EAAE8B,GAAG,EAAE7D,KAAK,CAAC;MACpD;IACJ;IACA,IAAI,IAAI,CAAC+B,OAAO,IAAI,IAAI,CAACF,eAAe,EAAE;MACtC,IAAI,CAACE,OAAO,CAACkC,SAAS,CAACC,GAAG,CAAC,GAAG,IAAI,CAACrC,eAAe,CAACsC,KAAK,CAAC,GAAG,CAAC,CAAC;IAClE;EACJ;EACAxB,WAAWA,CAACyB,WAAW,EAAElB,OAAO,EAAE;IAC9B,MAAMC,KAAK,GAAGD,OAAO,IAAI,IAAI,CAACE,QAAQ,CAACC,cAAc,CAAC,IAAI,CAACvB,EAAE,CAAC;IAC9D,IAAI,CAACqB,KAAK,EAAE;MACR;IACJ;IACA,IAAI,IAAI,CAAC7C,QAAQ,EAAE;MACfhB,QAAQ,CAAC6D,KAAK,EAAE,WAAW,IAAI,CAAC7C,QAAQ,EAAE,CAAC;IAC/C;IACA,IAAI8D,WAAW,EAAE;MACb/E,WAAW,CAAC8D,KAAK,EAAE,WAAWiB,WAAW,EAAE,CAAC;IAChD;EACJ;EACA1B,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACZ,EAAE,EAAE;MACV;IACJ;IACA,IAAI,IAAI,CAACP,QAAQ,EAAE;MACf,MAAM4B,KAAK,GAAG,IAAI,CAAClB,aAAa,EAAEoC,aAAa,CAAC,IAAI,IAAI,CAACvC,EAAE,EAAE,CAAC;MAC9D,IAAIqB,KAAK,EAAE;QACP,IAAI,CAACK,QAAQ,CAACc,WAAW,CAAC,IAAI,CAACrC,aAAa,EAAEkB,KAAK,CAAC;MACxD;IACJ,CAAC,MACI;MACD,IAAI,CAACF,kBAAkB,CAAC,CAAC;IAC7B;EACJ;EACA,OAAOxC,IAAI,YAAA8D,uBAAA3D,iBAAA;IAAA,YAAAA,iBAAA,IAAyFU,cAAc;EAAA;EAClH,OAAOkD,IAAI,kBA1N+EnG,EAAE,CAAAoG,iBAAA;IAAArD,IAAA,EA0NJE,cAAc;IAAAoD,SAAA;IAAAC,MAAA;MAAApD,QAAA;MAAAC,SAAA;MAAAnB,IAAA;MAAAC,QAAA;MAAAN,KAAA;MAAA4B,UAAA;MAAAC,eAAA;IAAA;IAAA+C,QAAA,GA1NZvG,EAAE,CAAAwG,kBAAA,CA0NgQ,CAACtE,UAAU,CAAC,GA1N9QlC,EAAE,CAAAyG,0BAAA,EAAFzG,EAAE,CAAA0G,oBAAA;EAAA;AA2NhG;AACA;EAAA,QAAA7D,SAAA,oBAAAA,SAAA,KA5N8F7C,EAAE,CAAA8C,iBAAA,CA4NJG,cAAc,EAAc,CAAC;IAC7GF,IAAI,EAAE3C,SAAS;IACfuG,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,UAAU;MACpBC,SAAS,EAAE,CAAC3E,UAAU,CAAC;MACvB4E,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAE5D,QAAQ,EAAE,CAAC;MACnDH,IAAI,EAAE5C,KAAK;MACXwG,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAExD,SAAS,EAAE,CAAC;MACZJ,IAAI,EAAE5C;IACV,CAAC,CAAC;IAAE6B,IAAI,EAAE,CAAC;MACPe,IAAI,EAAE5C;IACV,CAAC,CAAC;IAAE8B,QAAQ,EAAE,CAAC;MACXc,IAAI,EAAE5C;IACV,CAAC,CAAC;IAAEwB,KAAK,EAAE,CAAC;MACRoB,IAAI,EAAE5C;IACV,CAAC,CAAC;IAAEoD,UAAU,EAAE,CAAC;MACbR,IAAI,EAAE5C;IACV,CAAC,CAAC;IAAEqD,eAAe,EAAE,CAAC;MAClBT,IAAI,EAAE5C;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA,MAAM4G,KAAK,SAAS5F,aAAa,CAAC;EAC9B;AACJ;AACA;AACA;EACI6F,UAAU,GAAG3G,KAAK,CAAC,CAAC;EACpB;AACJ;AACA;AACA;EACI4G,KAAK,GAAG5G,KAAK,CAAC,CAAC;EACf;AACJ;AACA;AACA;EACI8C,SAAS,GAAG9C,KAAK,CAAC,CAAC;EACnB;AACJ;AACA;AACA;EACI2B,IAAI,GAAG3B,KAAK,CAAC,CAAC;EACd;AACJ;AACA;AACA;EACI4B,QAAQ,GAAG5B,KAAK,CAAC,CAAC;EAClB;AACJ;AACA;AACA;EACIsB,KAAK,GAAGtB,KAAK,CAAC,CAAC;EACf;AACJ;AACA;AACA;EACI6G,aAAa,GAAG7G,KAAK,CAAC,KAAK,EAAE;IAAE8G,SAAS,EAAE7G;EAAiB,CAAC,CAAC;EAC7DqD,eAAe,GAAGzD,MAAM,CAACgC,UAAU,CAAC;EACpC;AACJ;AACA;AACA;EACIkF,cAAc,GAAG7G,QAAQ,CAAC,MAAM;IAC5B,IAAIgB,OAAO,GAAG,qBAAqB;IACnC,IAAIV,UAAU,CAAC,IAAI,CAACc,KAAK,CAAC,CAAC,CAAC,IAAIC,MAAM,CAAC,IAAI,CAACD,KAAK,CAAC,CAAC,CAAC,CAACE,MAAM,KAAK,CAAC,EAAE;MAC/DN,OAAO,IAAI,iBAAiB;IAChC;IACA,IAAI,IAAI,CAAC4B,SAAS,CAAC,CAAC,KAAK,OAAO,EAAE;MAC9B5B,OAAO,IAAI,aAAa;IAC5B,CAAC,MACI,IAAI,IAAI,CAAC4B,SAAS,CAAC,CAAC,KAAK,QAAQ,EAAE;MACpC5B,OAAO,IAAI,aAAa;IAC5B,CAAC,MACI,IAAI,IAAI,CAAC4B,SAAS,CAAC,CAAC,KAAK,OAAO,EAAE;MACnC5B,OAAO,IAAI,aAAa;IAC5B;IACA,IAAIX,OAAO,CAAC,IAAI,CAACe,KAAK,CAAC,CAAC,CAAC,EAAE;MACvBJ,OAAO,IAAI,cAAc;IAC7B;IACA,IAAI,IAAI,CAACyF,UAAU,CAAC,CAAC,EAAE;MACnBzF,OAAO,IAAI,IAAI,IAAI,CAACyF,UAAU,CAAC,CAAC,EAAE;IACtC;IACA,IAAI,IAAI,CAAC/E,QAAQ,CAAC,CAAC,EAAE;MACjBV,OAAO,IAAI,YAAY,IAAI,CAACU,QAAQ,CAAC,CAAC,EAAE;IAC5C;IACA,OAAOV,OAAO;EAClB,CAAC,CAAC;EACF,OAAOa,IAAI;IAAA,IAAAiF,kBAAA;IAAA,gBAAAC,cAAA/E,iBAAA;MAAA,QAAA8E,kBAAA,KAAAA,kBAAA,GAzT+ErH,EAAE,CAAAwC,qBAAA,CAyTQuE,KAAK,IAAAxE,iBAAA,IAALwE,KAAK;IAAA;EAAA;EACzG,OAAOQ,IAAI,kBA1T+EvH,EAAE,CAAAwH,iBAAA;IAAAzE,IAAA,EA0TJgE,KAAK;IAAAV,SAAA;IAAAoB,QAAA;IAAAC,YAAA,WAAAC,mBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QA1TH5H,EAAE,CAAA8H,UAAA,CA0TJD,GAAA,CAAAZ,KAAA,CAAM,CAAF,CAAC;QA1THjH,EAAE,CAAA+H,UAAA,CA0TJF,GAAA,CAAAT,cAAA,CAAe,CAAX,CAAC;QA1THpH,EAAE,CAAAgI,WAAA,YA0TJH,GAAA,CAAAX,aAAA,CAAc,CAAC,GAAG,MAAM,GAAG,IAAvB,CAAC;MAAA;IAAA;IAAAZ,MAAA;MAAAU,UAAA;MAAAC,KAAA;MAAA9D,SAAA;MAAAnB,IAAA;MAAAC,QAAA;MAAAN,KAAA;MAAAuF,aAAA;IAAA;IAAAX,QAAA,GA1THvG,EAAE,CAAAwG,kBAAA,CA0TqkC,CAACtE,UAAU,CAAC,GA1TnlClC,EAAE,CAAAyG,0BAAA;IAAAwB,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,eAAAR,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAF5H,EAAE,CAAAqI,MAAA,EA0T+oC,CAAC;MAAA;MAAA,IAAAT,EAAA;QA1TlpC5H,EAAE,CAAAsI,iBAAA,CAAAT,GAAA,CAAAlG,KAAA,EA0T+oC,CAAC;MAAA;IAAA;IAAA4G,YAAA,GAA4DxI,YAAY,EAA8BmB,YAAY;IAAAsH,aAAA;IAAAC,eAAA;EAAA;AACl2C;AACA;EAAA,QAAA5F,SAAA,oBAAAA,SAAA,KA5T8F7C,EAAE,CAAA8C,iBAAA,CA4TJiE,KAAK,EAAc,CAAC;IACpGhE,IAAI,EAAErC,SAAS;IACfiG,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,SAAS;MACnBuB,QAAQ,EAAE,eAAe;MACzBrB,UAAU,EAAE,IAAI;MAChB4B,OAAO,EAAE,CAAC3I,YAAY,EAAEmB,YAAY,CAAC;MACrCuH,eAAe,EAAEhI,uBAAuB,CAACkI,MAAM;MAC/CH,aAAa,EAAEhI,iBAAiB,CAACoI,IAAI;MACrC/B,SAAS,EAAE,CAAC3E,UAAU,CAAC;MACvB2G,IAAI,EAAE;QACF,SAAS,EAAE,kBAAkB;QAC7B,iBAAiB,EAAE,iCAAiC;QACpD,SAAS,EAAE;MACf;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;AACV,MAAMC,WAAW,CAAC;EACd,OAAO1G,IAAI,YAAA2G,oBAAAxG,iBAAA;IAAA,YAAAA,iBAAA,IAAyFuG,WAAW;EAAA;EAC/G,OAAOE,IAAI,kBA/U+EhJ,EAAE,CAAAiJ,gBAAA;IAAAlG,IAAA,EA+US+F,WAAW;IAAAJ,OAAA,GAAY3B,KAAK,EAAE9D,cAAc,EAAE/B,YAAY;IAAAgI,OAAA,GAAanC,KAAK,EAAE9D,cAAc,EAAE/B,YAAY;EAAA;EAC/M,OAAOiI,IAAI,kBAhV+EnJ,EAAE,CAAAoJ,gBAAA;IAAAV,OAAA,GAgVgC3B,KAAK,EAAE7F,YAAY,EAAEA,YAAY;EAAA;AACjK;AACA;EAAA,QAAA2B,SAAA,oBAAAA,SAAA,KAlV8F7C,EAAE,CAAA8C,iBAAA,CAkVJgG,WAAW,EAAc,CAAC;IAC1G/F,IAAI,EAAEpC,QAAQ;IACdgG,IAAI,EAAE,CAAC;MACC+B,OAAO,EAAE,CAAC3B,KAAK,EAAE9D,cAAc,EAAE/B,YAAY,CAAC;MAC9CgI,OAAO,EAAE,CAACnC,KAAK,EAAE9D,cAAc,EAAE/B,YAAY;IACjD,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAAS6F,KAAK,EAAE/D,YAAY,EAAEC,cAAc,EAAE6F,WAAW,EAAE5G,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}