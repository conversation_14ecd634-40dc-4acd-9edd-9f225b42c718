{"version": 3, "sources": ["../../../../../../node_modules/@mermaid-js/parser/dist/chunks/mermaid-parser.core/chunk-C4OEIS7N.mjs"], "sourcesContent": ["import { AbstractMermaidTokenBuilder, AbstractMermaidValueConverter, ArchitectureGeneratedModule, MermaidGeneratedSharedModule, __name } from \"./chunk-7PKI6E2E.mjs\";\n\n// src/language/architecture/module.ts\nimport { EmptyFileSystem, createDefaultCoreModule, createDefaultSharedCoreModule, inject } from \"langium\";\n\n// src/language/architecture/tokenBuilder.ts\nvar ArchitectureTokenBuilder = class extends AbstractMermaidTokenBuilder {\n  static {\n    __name(this, \"ArchitectureTokenBuilder\");\n  }\n  constructor() {\n    super([\"architecture\"]);\n  }\n};\n\n// src/language/architecture/valueConverter.ts\nvar ArchitectureValueConverter = class extends AbstractMermaidValueConverter {\n  static {\n    __name(this, \"ArchitectureValueConverter\");\n  }\n  runCustomConverter(rule, input, _cstNode) {\n    if (rule.name === \"ARCH_ICON\") {\n      return input.replace(/[()]/g, \"\").trim();\n    } else if (rule.name === \"ARCH_TEXT_ICON\") {\n      return input.replace(/[\"()]/g, \"\");\n    } else if (rule.name === \"ARCH_TITLE\") {\n      return input.replace(/[[\\]]/g, \"\").trim();\n    }\n    return void 0;\n  }\n};\n\n// src/language/architecture/module.ts\nvar ArchitectureModule = {\n  parser: {\n    TokenBuilder: /* @__PURE__ */__name(() => new ArchitectureTokenBuilder(), \"TokenBuilder\"),\n    ValueConverter: /* @__PURE__ */__name(() => new ArchitectureValueConverter(), \"ValueConverter\")\n  }\n};\nfunction createArchitectureServices(context = EmptyFileSystem) {\n  const shared = inject(createDefaultSharedCoreModule(context), MermaidGeneratedSharedModule);\n  const Architecture = inject(createDefaultCoreModule({\n    shared\n  }), ArchitectureGeneratedModule, ArchitectureModule);\n  shared.ServiceRegistry.register(Architecture);\n  return {\n    shared,\n    Architecture\n  };\n}\n__name(createArchitectureServices, \"createArchitectureServices\");\nexport { ArchitectureModule, createArchitectureServices };"], "mappings": ";;;;;;;;;;;;;;AAMA,IAAI,2BAA2B,cAAc,4BAA4B;AAAA,EACvE,OAAO;AACL,WAAO,MAAM,0BAA0B;AAAA,EACzC;AAAA,EACA,cAAc;AACZ,UAAM,CAAC,cAAc,CAAC;AAAA,EACxB;AACF;AAGA,IAAI,6BAA6B,cAAc,8BAA8B;AAAA,EAC3E,OAAO;AACL,WAAO,MAAM,4BAA4B;AAAA,EAC3C;AAAA,EACA,mBAAmB,MAAM,OAAO,UAAU;AACxC,QAAI,KAAK,SAAS,aAAa;AAC7B,aAAO,MAAM,QAAQ,SAAS,EAAE,EAAE,KAAK;AAAA,IACzC,WAAW,KAAK,SAAS,kBAAkB;AACzC,aAAO,MAAM,QAAQ,UAAU,EAAE;AAAA,IACnC,WAAW,KAAK,SAAS,cAAc;AACrC,aAAO,MAAM,QAAQ,UAAU,EAAE,EAAE,KAAK;AAAA,IAC1C;AACA,WAAO;AAAA,EACT;AACF;AAGA,IAAI,qBAAqB;AAAA,EACvB,QAAQ;AAAA,IACN,cAA6B,OAAO,MAAM,IAAI,yBAAyB,GAAG,cAAc;AAAA,IACxF,gBAA+B,OAAO,MAAM,IAAI,2BAA2B,GAAG,gBAAgB;AAAA,EAChG;AACF;AACA,SAAS,2BAA2B,UAAU,iBAAiB;AAC7D,QAAM,SAAS,OAAO,8BAA8B,OAAO,GAAG,4BAA4B;AAC1F,QAAM,eAAe,OAAO,wBAAwB;AAAA,IAClD;AAAA,EACF,CAAC,GAAG,6BAA6B,kBAAkB;AACnD,SAAO,gBAAgB,SAAS,YAAY;AAC5C,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,OAAO,4BAA4B,4BAA4B;", "names": []}