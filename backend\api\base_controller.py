"""
BaseController - Classe Base para Controladores

Este módulo define a classe base que todos os controladores da API
devem herdar, fornecendo funcionalidades comuns e padronizadas.
"""

import logging
import time
from typing import Dict, Any, Optional, List, Union
from fastapi import Request
from .response_formatter import ResponseFormatter, response_formatter
from .error_handler import (
    APIError, ValidationAPIError,
    UnauthorizedAPIError, ForbiddenAPIError
)
from .middlewares.auth_middleware import AuthMiddleware
from .middlewares.validation_middleware import ValidationMiddleware


logger = logging.getLogger(__name__)


class BaseController:
    """
    Controlador Base da API

    Responsável por:
    - Fornece métodos comuns a todos controladores
    - Padroniza tratamento de requisições e respostas
    - Integra autenticação e validação automaticamente
    - Controla paginação e filtros
    - Mede tempo de processamento
    """

    def __init__(self):
        """Inicializa o controlador base"""
        self.formatter = response_formatter
        self.auth_middleware = AuthMiddleware(None)  # Usado para validações
        self.validation_middleware = ValidationMiddleware(None)
        self.default_per_page = 20
        self.max_per_page = 100

    def _start_timer(self) -> float:
        """
        Inicia cronômetro para medir tempo de processamento

        Returns:
            Timestamp de início
        """
        return time.time()

    def _calculate_processing_time(self, start_time: float) -> float:
        """
        Calcula tempo de processamento em milissegundos

        Args:
            start_time: Timestamp de início

        Returns:
            Tempo em milissegundos
        """
        return round((time.time() - start_time) * 1000, 2)

    def _get_request_id(self, request: Request) -> Optional[str]:
        """
        Extrai ID da requisição do contexto

        Args:
            request: Requisição FastAPI

        Returns:
            ID da requisição se disponível
        """
        return getattr(request.state, 'request_id', None)

    def _get_current_user(self, request: Request) -> Dict[str, Any]:
        """
        Obtém informações do usuário atual

        Args:
            request: Requisição FastAPI

        Returns:
            Informações do usuário
        """
        return getattr(request.state, 'user', {"id": "anonymous", "role": "guest"})

    def _require_authentication(self, request: Request) -> Dict[str, Any]:
        """
        Força autenticação para a requisição

        Args:
            request: Requisição FastAPI

        Returns:
            Informações do usuário autenticado

        Raises:
            UnauthorizedAPIError: Se não autenticado
        """
        return self.auth_middleware.require_authentication(request)

    def _require_permission(self, request: Request, permission: str):
        """
        Verifica permissão específica

        Args:
            request: Requisição FastAPI
            permission: Permissão requerida

        Raises:
            ForbiddenAPIError: Se sem permissão
        """
        self.auth_middleware.require_permission(request, permission)

    def _require_role(self, request: Request, role: str):
        """
        Verifica role específica

        Args:
            request: Requisição FastAPI
            role: Role requerida

        Raises:
            ForbiddenAPIError: Se sem role
        """
        self.auth_middleware.require_role(request, role)

    def _get_pagination_params(self, request: Request) -> Dict[str, int]:
        """
        Extrai parâmetros de paginação da requisição

        Args:
            request: Requisição FastAPI

        Returns:
            Dicionário com page e per_page
        """
        try:
            page = int(request.query_params.get("page", 1))
            per_page = int(request.query_params.get(
                "per_page", self.default_per_page))

            # Validar limites
            page = max(1, page)
            per_page = min(max(1, per_page), self.max_per_page)

            return {"page": page, "per_page": per_page}

        except ValueError:
            return {"page": 1, "per_page": self.default_per_page}

    def _get_sort_params(self, request: Request, allowed_fields: Optional[List[str]] = None) -> Optional[Dict[str, str]]:
        """
        Extrai parâmetros de ordenação da requisição

        Args:
            request: Requisição FastAPI
            allowed_fields: Campos permitidos para ordenação

        Returns:
            Dicionário com field e direction ou None
        """
        sort_param = request.query_params.get("sort")
        if not sort_param:
            return None

        # Determinar direção (- para desc, + ou nada para asc)
        if sort_param.startswith("-"):
            field = sort_param[1:]
            direction = "desc"
        elif sort_param.startswith("+"):
            field = sort_param[1:]
            direction = "asc"
        else:
            field = sort_param
            direction = "asc"

        # Validar campo se lista fornecida
        if allowed_fields and field not in allowed_fields:
            return None

        return {"field": field, "direction": direction}

    def _get_filter_params(self, request: Request, allowed_filters: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Extrai parâmetros de filtro da requisição

        Args:
            request: Requisição FastAPI
            allowed_filters: Filtros permitidos

        Returns:
            Dicionário com filtros aplicáveis
        """
        filters = {}

        # Parâmetros padrão de filtro
        default_filter_params = ["search", "status", "type", "category"]

        # Usar filtros permitidos ou padrão
        filter_params = allowed_filters or default_filter_params

        for param in filter_params:
            value = request.query_params.get(param)
            if value:
                filters[param] = value

        return filters

    def _validate_id_parameter(self, id_value: str, field_name: str = "id") -> str:
        """
        Valida parâmetro de ID

        Args:
            id_value: Valor do ID
            field_name: Nome do campo

        Returns:
            ID validado

        Raises:
            ValidationAPIError: Se ID inválido
        """
        errors = self.validation_middleware.validate_id_parameter(
            id_value, field_name)
        if errors:
            raise ValidationAPIError(f"ID inválido", errors)
        return id_value

    def _validate_json_body(self, body: Dict[str, Any], required_fields: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Valida body JSON

        Args:
            body: Body da requisição
            required_fields: Campos obrigatórios

        Returns:
            Body validado

        Raises:
            ValidationAPIError: Se dados inválidos
        """
        errors = self.validation_middleware.validate_json_body(
            body, required_fields)
        if errors:
            raise ValidationAPIError("Dados de entrada inválidos", errors)
        return body

    def success_response(
        self,
        data: Optional[Union[Dict[str, Any], List[Dict[str, Any]]]] = None,
        message: str = "Operação realizada com sucesso",
        request_id: Optional[str] = None,
        processing_time_ms: Optional[float] = None
    ) -> Dict[str, Any]:
        """
        Gera resposta de sucesso padronizada

        Args:
            data: Dados da resposta
            message: Mensagem de sucesso
            request_id: ID da requisição
            processing_time_ms: Tempo de processamento

        Returns:
            Resposta formatada
        """
        return self.formatter.success(
            data=data,
            message=message,
            request_id=request_id,
            processing_time_ms=processing_time_ms
        )

    def paginated_response(
        self,
        data: List[Dict[str, Any]],
        page: int,
        per_page: int,
        total_items: int,
        message: str = "Dados recuperados com sucesso",
        request_id: Optional[str] = None,
        processing_time_ms: Optional[float] = None
    ) -> Dict[str, Any]:
        """
        Gera resposta paginada padronizada

        Args:
            data: Lista de dados
            page: Página atual
            per_page: Itens por página
            total_items: Total de itens
            message: Mensagem de sucesso
            request_id: ID da requisição
            processing_time_ms: Tempo de processamento

        Returns:
            Resposta paginada formatada
        """
        return self.formatter.paginated_success(
            data=data,
            page=page,
            per_page=per_page,
            total_items=total_items,
            message=message,
            request_id=request_id,
            processing_time_ms=processing_time_ms
        )

    def error_response(
        self,
        message: str = "Erro interno do servidor",
        errors: Optional[List[str]] = None,
        request_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Gera resposta de erro padronizada

        Args:
            message: Mensagem principal do erro
            errors: Lista de erros específicos
            request_id: ID da requisição

        Returns:
            Resposta de erro formatada
        """
        return self.formatter.error(
            message=message,
            errors=errors,
            request_id=request_id
        )

    def not_found_response(
        self,
        resource: str = "recurso",
        resource_id: Optional[str] = None,
        request_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Gera resposta de não encontrado

        Args:
            resource: Nome do recurso
            resource_id: ID do recurso
            request_id: ID da requisição

        Returns:
            Resposta de não encontrado
        """
        return self.formatter.not_found(
            resource=resource,
            resource_id=resource_id,
            request_id=request_id
        )

    def log_operation(
        self,
        operation: str,
        user_id: str,
        resource_type: str,
        resource_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        request_id: Optional[str] = None
    ):
        """
        Registra operação realizada

        Args:
            operation: Tipo de operação (create, read, update, delete)
            user_id: ID do usuário
            resource_type: Tipo do recurso
            resource_id: ID do recurso
            details: Detalhes adicionais
            request_id: ID da requisição
        """
        logger.info(
            f"Operation: {operation} {resource_type}",
            extra={
                "operation": operation,
                "user_id": user_id,
                "resource_type": resource_type,
                "resource_id": resource_id,
                "details": details or {},
                "request_id": request_id
            }
        )

    async def handle_controller_operation(
        self,
        request: Request,
        operation_func,
        *args,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Handler genérico para operações de controlador

        Args:
            request: Requisição FastAPI
            operation_func: Função a ser executada
            *args: Argumentos posicionais
            **kwargs: Argumentos nomeados

        Returns:
            Resposta formatada
        """
        start_time = self._start_timer()
        request_id = self._get_request_id(request)

        try:
            # Executar operação
            result = await operation_func(*args, **kwargs)

            # Calcular tempo de processamento
            processing_time = self._calculate_processing_time(start_time)

            # Retornar resultado com metadados
            if isinstance(result, dict) and "data" in result:
                return self.success_response(
                    data=result["data"],
                    message=result.get(
                        "message", "Operação realizada com sucesso"),
                    request_id=request_id,
                    processing_time_ms=processing_time
                )
            else:
                return self.success_response(
                    data=result,
                    request_id=request_id,
                    processing_time_ms=processing_time
                )

        except APIError:
            # Re-raise API errors para serem tratadas pelo ErrorHandler
            raise
        except Exception as e:
            logger.error(f"Controller operation error: {str(e)}", extra={
                         "request_id": request_id})
            # Converter para APIError genérico
            raise APIError(f"Erro na operação: {str(e)}")
