"""
Endpoints para geração de relatórios em Markdown e PDF
"""
import logging
from datetime import datetime, UTC
from fastapi import APIRouter, HTTPException, status
from fastapi.responses import Response, JSONResponse
from bson import ObjectId
from pymongo import MongoClient
from gridfs import GridFS
import os

# Importar geradores
from tools.reports.markdown_generator import get_markdown_report_generator
from tools.reports.markdown_to_pdf import MarkdownToPDFConverter
from tools.reports.html_report_generator import HTMLReportGenerator
from tools.reports.html_to_pdf_converter import HTMLToPDFConverter
from clients.db import clients_collection, db
from config.settings import env

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/reports", tags=["reports"])


@router.post("/clients/{client_id}/generate-markdown")
async def generate_markdown_report(client_id: str):
    """
    Gera relatório executivo em Markdown usando processamento paralelo.

    Returns:
        JSON com o conteúdo Markdown e metadados
    """
    try:
        # Verificar se cliente existe
        try:
            client_obj_id = ObjectId(client_id)
        except:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="ID de cliente inválido"
            )

        client = clients_collection.find_one({"_id": client_obj_id})
        if not client:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Cliente não encontrado"
            )

        client_name = client.get("name", "Cliente")
        logger.info(f"📝 Gerando relatório Markdown para {client_name}")

        # Verificar se tem dados suficientes
        reports = client.get("reports", [])
        has_dossier = any(r.get("reportType") ==
                          "dossie_expandido" for r in reports)

        if not has_dossier and not reports:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="Cliente não possui dados suficientes. Execute a análise completa primeiro."
            )

        # Gerar relatório em Markdown
        generator = get_markdown_report_generator()
        result = await generator.generate_markdown_report(client_id)

        # Salvar markdown no banco (opcional)
        markdown_id = None
        if result.get("markdown_content"):
            fs = GridFS(db)
            markdown_id = fs.put(
                result["markdown_content"].encode('utf-8'),
                filename=f"relatorio_markdown_{client_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md",
                content_type="text/markdown",
                client_id=str(client_obj_id),
                generated_at=datetime.now(UTC),
                report_type="markdown_executive_parallel"
            )

            # Atualizar cliente com ID do markdown
            clients_collection.update_one(
                {"_id": client_obj_id},
                {
                    "$set": {
                        "markdown_report_id": markdown_id,
                        "markdown_generated_at": datetime.now(UTC),
                        "updated_at": datetime.now(UTC)
                    }
                }
            )

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "success": True,
                "client_id": client_id,
                "client_name": client_name,
                "markdown_id": str(markdown_id) if markdown_id else None,
                "markdown_content": result["markdown_content"],
                "metadata": result["metadata"],
                "message": f"Relatório Markdown (paralelo) gerado com sucesso para {client_name}"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Erro ao gerar relatório Markdown: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro ao gerar relatório: {str(e)}"
        )


@router.get("/clients/{client_id}/markdown")
async def get_markdown_report(client_id: str):
    """
    Busca relatório existente em Markdown

    Args:
        client_id: ID do cliente

    Returns:
        JSON com conteúdo Markdown e metadados
    """
    try:
        # Verificar se cliente existe
        try:
            client_obj_id = ObjectId(client_id)
        except:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="ID de cliente inválido"
            )

        client = clients_collection.find_one({"_id": client_obj_id})
        if not client:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Cliente não encontrado"
            )

        client_name = client.get("name", "Cliente")

        # Buscar markdown existente
        markdown_id = client.get("markdown_report_id")
        if not markdown_id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Nenhum relatório Markdown encontrado. Gere o relatório primeiro."
            )

        fs = GridFS(db)
        try:
            markdown_file = fs.get(markdown_id)
            markdown_content = markdown_file.read().decode('utf-8')

            # Metadados do arquivo
            file_metadata = {
                "filename": markdown_file.filename,
                "upload_date": markdown_file.upload_date.isoformat() if markdown_file.upload_date else None,
                "content_type": markdown_file.content_type,
                "length": markdown_file.length
            }

        except Exception as e:
            logger.error(f"Erro ao buscar Markdown do GridFS: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Relatório Markdown não encontrado no banco de dados"
            )

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "success": True,
                "client_id": client_id,
                "client_name": client_name,
                "markdown_content": markdown_content,
                "metadata": {
                    "generated_at": client.get("markdown_generated_at").isoformat() if client.get("markdown_generated_at") else None,
                    "file_info": file_metadata,
                    "report_type": "markdown_executive"
                },
                "message": f"Relatório Markdown encontrado para {client_name}"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Erro ao buscar relatório Markdown: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro ao buscar relatório: {str(e)}"
        )


@router.post("/clients/{client_id}/markdown-to-pdf")
async def convert_markdown_to_pdf(client_id: str, markdown_content: str = None):
    """
    Converte relatório Markdown para PDF

    Args:
        client_id: ID do cliente
        markdown_content: Conteúdo Markdown (opcional, busca do banco se não fornecido)

    Returns:
        PDF file response
    """
    try:
        # Verificar se cliente existe
        try:
            client_obj_id = ObjectId(client_id)
        except:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="ID de cliente inválido"
            )

        client = clients_collection.find_one({"_id": client_obj_id})
        if not client:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Cliente não encontrado"
            )

        client_name = client.get("name", "Cliente")

        # Se não foi fornecido markdown_content, buscar do banco
        if not markdown_content:
            markdown_id = client.get("markdown_report_id")
            if not markdown_id:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Nenhum relatório Markdown encontrado. Gere o Markdown primeiro."
                )

            fs = GridFS(db)
            try:
                markdown_file = fs.get(markdown_id)
                markdown_content = markdown_file.read().decode('utf-8')
            except Exception as e:
                logger.error(f"Erro ao buscar Markdown do GridFS: {e}")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Relatório Markdown não encontrado no banco de dados"
                )

        logger.info(f"🔄 Convertendo Markdown para PDF - {client_name}")

        # Converter para PDF usando MarkdownToPDFConverter
        converter = MarkdownToPDFConverter()
        metadata = {
            "client_id": client_id,
            "client_name": client_name,
            "generated_at": datetime.now(UTC).isoformat()
        }

        pdf_bytes = converter.convert_to_pdf(markdown_content, metadata)

        # Salvar PDF no GridFS
        fs = GridFS(db)
        pdf_id = fs.put(
            pdf_bytes,
            filename=f"relatorio_executivo_md_{client_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
            content_type="application/pdf",
            client_id=str(client_obj_id),
            generated_at=datetime.now(UTC),
            report_type="pdf_from_markdown"
        )

        # Atualizar cliente
        clients_collection.update_one(
            {"_id": client_obj_id},
            {
                "$set": {
                    "markdown_pdf_id": pdf_id,
                    "markdown_pdf_generated_at": datetime.now(UTC),
                    "pdf_report_available": True,
                    "updated_at": datetime.now(UTC)
                }
            }
        )

        # Retornar PDF
        return Response(
            content=pdf_bytes,
            media_type="application/pdf",
            headers={
                "Content-Disposition": f"attachment; filename=relatorio_executivo_{client_name.replace(' ', '_')}.pdf"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Erro ao converter Markdown para PDF: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro ao converter para PDF: {str(e)}"
        )


@router.post("/clients/{client_id}/generate-complete-report")
async def generate_complete_report(client_id: str):
    """
    Gera relatório completo: HTML (via IA) -> PDF (via WeasyPrint)

    Returns:
        PDF file response
    """
    try:
        # Verificar se cliente existe
        try:
            client_obj_id = ObjectId(client_id)
        except:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="ID de cliente inválido"
            )

        client = clients_collection.find_one({"_id": client_obj_id})
        if not client:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Cliente não encontrado"
            )

        client_name = client.get("name", "Cliente")
        logger.info(f"🚀 Gerando relatório completo para {client_name}")

        # Verificar se tem dados suficientes
        reports = client.get("reports", [])
        has_dossier = any(r.get("reportType") ==
                          "dossie_expandido" for r in reports)

        if not has_dossier and not reports:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="Cliente não possui dados suficientes. Execute a análise completa primeiro."
            )

        # 1. Gerar HTML direto
        logger.info(f"📝 Etapa 1/2: Gerando relatório HTML estruturado")
        html_generator = HTMLReportGenerator()
        html_result = html_generator.generate_html_report(client_id)

        # 2. Converter HTML para PDF
        logger.info(f"📄 Etapa 2/2: Convertendo HTML para PDF")
        pdf_converter = HTMLToPDFConverter()
        metadata = html_result["metadata"]

        pdf_bytes = pdf_converter.convert_to_pdf(
            html_result["html_content"],
            metadata
        )

        # Salvar ambos no GridFS
        fs = GridFS(db)

        # Salvar HTML
        html_id = fs.put(
            html_result["html_content"].encode('utf-8'),
            filename=f"relatorio_html_{client_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html",
            content_type="text/html",
            client_id=str(client_obj_id),
            generated_at=datetime.now(UTC),
            report_type="html_executive"
        )

        # Salvar PDF
        pdf_id = fs.put(
            pdf_bytes,
            filename=f"relatorio_executivo_html_{client_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
            content_type="application/pdf",
            client_id=str(client_obj_id),
            generated_at=datetime.now(UTC),
            report_type="pdf_from_html",
            html_id=html_id
        )

        # Atualizar cliente
        clients_collection.update_one(
            {"_id": client_obj_id},
            {
                "$set": {
                    "html_report_id": html_id,
                    "html_pdf_id": pdf_id,
                    "html_report_generated_at": datetime.now(UTC),
                    "pdf_report_available": True,
                    "updated_at": datetime.now(UTC)
                }
            }
        )

        logger.info(
            f"✅ Relatório HTML completo gerado com sucesso para {client_name}")

        # Retornar PDF
        return Response(
            content=pdf_bytes,
            media_type="application/pdf",
            headers={
                "Content-Disposition": f"attachment; filename=relatorio_executivo_{client_name.replace(' ', '_')}.pdf"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Erro ao gerar relatório completo: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro ao gerar relatório: {str(e)}"
        )


@router.post("/clients/{client_id}/generate-html-report")
async def generate_html_report_endpoint(client_id: str):
    """Gera e salva um relatório HTML para um cliente."""
    try:
        client_obj_id = ObjectId(client_id)
    except Exception:
        raise HTTPException(status_code=400, detail="ID de cliente inválido")

    client = clients_collection.find_one({"_id": client_obj_id})
    if not client:
        raise HTTPException(status_code=404, detail="Cliente não encontrado")

    try:
        generator = HTMLReportGenerator(db)
        report_html = await generator.generate_report(client)

        # Salvar o relatório HTML no GridFS
        fs = GridFS(db, collection="html_reports")
        report_id = fs.put(
            report_html.encode('utf-8'),
            filename=f"relatorio_html_{client['name']}_{datetime.now(UTC).strftime('%Y%m%d')}.html",
            client_id=client_id,
            content_type="text/html"
        )

        # Atualizar o cliente com o ID do relatório HTML
        clients_collection.update_one(
            {"_id": client_obj_id},
            {"$set": {"html_report_id": report_id,
                      "updated_at": datetime.now(UTC)}}
        )

        return JSONResponse(
            content={"message": "Relatório HTML gerado e salvo com sucesso.",
                     "report_id": str(report_id)},
            status_code=200
        )
    except Exception as e:
        logger.error(f"Erro ao gerar relatório HTML: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/clients/{client_id}/html-to-pdf")
async def convert_html_to_pdf_endpoint(client_id: str):
    """Converte o relatório HTML mais recente de um cliente para PDF e salva."""
    try:
        client_obj_id = ObjectId(client_id)
    except Exception:
        raise HTTPException(status_code=400, detail="ID de cliente inválido")

    client = clients_collection.find_one({"_id": client_obj_id})
    if not client:
        raise HTTPException(status_code=404, detail="Cliente não encontrado")

    html_report_id = client.get("html_report_id")
    if not html_report_id:
        raise HTTPException(
            status_code=404, detail="Nenhum relatório HTML encontrado para este cliente.")

    try:
        fs = GridFS(db, collection="html_reports")
        html_file = fs.get(html_report_id)
        html_content = html_file.read().decode('utf-8')

        converter = HTMLToPDFConverter()
        pdf_bytes = await converter.convert(html_content)

        # Salvar o PDF no GridFS
        pdf_fs = GridFS(db, collection="pdfs")
        pdf_filename = f"relatorio_pdf_{client['name']}_{datetime.now(UTC).strftime('%Y%m%d')}.pdf"
        pdf_id = pdf_fs.put(
            pdf_bytes,
            filename=pdf_filename,
            content_type="application/pdf",
            client_id=client_id
        )

        # Atualizar o cliente com o ID do novo PDF
        clients_collection.update_one(
            {"_id": client_obj_id},
            {"$set": {"pdf_report_id": pdf_id,
                      "pdf_generated_at": datetime.now(UTC)}}
        )

        return JSONResponse(
            content={"message": "Relatório PDF gerado com sucesso a partir do HTML.",
                     "pdf_id": str(pdf_id)},
            status_code=200
        )

    except Exception as e:
        logger.error(f"Erro ao converter HTML para PDF: {e}")
        raise HTTPException(status_code=500, detail=str(e))
