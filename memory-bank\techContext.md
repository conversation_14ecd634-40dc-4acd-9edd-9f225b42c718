# Tech Context - ScopeAI

**Última Atualização**: 2025-01-29

## Stack Tecnológico

### Frontend
- **Framework**: Angular 19.0.0
- **Linguagem**: TypeScript 5.3
- **Estilização**: Tailwind CSS 3.4
- **Build**: Vite
- **State Management**: Signals (nativo Angular)
- **HTTP Client**: HttpClient (Angular)
- **WebSocket**: Native WebSocket API

### Backend
- **Framework**: FastAPI 0.104.1
- **Linguagem**: Python 3.12
- **Async**: asyncio nativo
- **Validation**: Pydantic v2
- **ORM**: Motor (async MongoDB)
- **Task Queue**: Background Tasks (FastAPI)
- **WebSocket**: FastAPI WebSocket

### Banco de Dados
- **Principal**: MongoDB Atlas 7.0
- **Storage**: GridFS (PDFs/arquivos)
- **Cache**: <PERSON><PERSON> (planejado)
- **Índices**: Otimizados para queries frequentes

### IA/ML
- **LLMs**: 
  - OpenAI GPT-4 (principal)
  - <PERSON><PERSON><PERSON> (coordinator)
  - <PERSON><PERSON><PERSON> 3.1:8b (agents)
- **Embeddings**: OpenAI text-embedding-3-small
- **Perplexity**: API para pesquisa web
- **Frameworks**: LangChain, Instructor

### Infraestrutura
- **Containers**: Docker & Docker Compose
- **OS**: Windows (dev), Linux (prod)
- **Shell**: Git Bash (Windows)
- **Proxy**: Nginx (prod)

## Configurações de Ambiente

### Desenvolvimento
```yaml
# docker-compose.yml
services:
  backend:
    build: ./backend
    ports:
      - "8040:8040"
    environment:
      - UVICORN_RELOAD=true
      - LOG_LEVEL=DEBUG
    volumes:
      - ./backend:/app

  frontend:
    build: ./frontend
    ports:
      - "4200:4200"
    volumes:
      - ./frontend:/app
```

### Variáveis de Ambiente
```bash
# Backend (.env)
MONGODB_URI=mongodb+srv://...
OPENAI_API_KEY=sk-...
PERPLEXITY_API_KEY=pplx-...
MISTRAL_API_KEY=...
REDIS_URL=redis://localhost:6379
JWT_SECRET=...
CORS_ORIGINS=["http://localhost:4200"]
```

### Produção
```bash
# Diferenças em produção
UVICORN_RELOAD=false
LOG_LEVEL=INFO
CORS_ORIGINS=["https://app.scopeai.com"]
SSL_CERT=/etc/ssl/certs/scopeai.crt
SSL_KEY=/etc/ssl/private/scopeai.key
```

## Dependências Principais

### Backend (pyproject.toml)
```toml
[tool.poetry.dependencies]
python = "^3.12"
fastapi = "^0.104.1"
uvicorn = "^0.24.0"
motor = "^3.3.2"
pydantic = "^2.5.0"
openai = "^1.6.0"
langchain = "^0.1.0"
instructor = "^0.4.0"
httpx = "^0.25.2"
python-multipart = "^0.0.6"
python-jose = "^3.3.0"
passlib = "^1.7.4"
pytest = "^7.4.3"
pytest-asyncio = "^0.21.1"
```

### Frontend (package.json)
```json
{
  "dependencies": {
    "@angular/animations": "^19.0.0",
    "@angular/common": "^19.0.0",
    "@angular/compiler": "^19.0.0",
    "@angular/core": "^19.0.0",
    "@angular/forms": "^19.0.0",
    "@angular/platform-browser": "^19.0.0",
    "@angular/router": "^19.0.0",
    "rxjs": "~7.8.0",
    "tslib": "^2.3.0",
    "zone.js": "~0.15.0",
    "tailwindcss": "^3.4.0",
    "marked": "^11.0.0",
    "mermaid": "^10.6.0"
  }
}
```

## Restrições Técnicas

### Performance
- **Timeout API**: 300s para operações longas
- **Max Upload**: 100MB
- **Concurrent Users**: 100 (atual)
- **Rate Limit**: 100 req/min por IP

### Segurança
- **CORS**: Configurado por ambiente
- **Auth**: JWT com refresh tokens
- **HTTPS**: Obrigatório em produção
- **Headers**: Security headers (HSTS, CSP)

### Compatibilidade
- **Browsers**: Chrome 90+, Firefox 88+, Safari 14+
- **Mobile**: Responsive design
- **API Version**: v1 (retrocompatível)

## Integrações Externas

### APIs
1. **OpenAI**
   - Modelos: gpt-4-turbo-preview, gpt-3.5-turbo
   - Embeddings: text-embedding-3-small
   - Rate limits aplicados

2. **Perplexity**
   - Modelo: sonar-medium-online
   - Pesquisa web em tempo real
   - Cache de 24h

3. **MongoDB Atlas**
   - Cluster: M10 (prod)
   - Região: us-east-1
   - Backup: Daily

### Webhooks
- **Client Events**: criação, atualização
- **Report Events**: início, conclusão, erro
- **Project Events**: geração, estimativa

## Ferramentas de Desenvolvimento

### Linting/Formatting
- **Python**: Black, Flake8, isort
- **TypeScript**: ESLint, Prettier
- **Pre-commit**: hooks configurados

### Testing
- **Backend**: pytest, pytest-asyncio
- **Frontend**: Jest, Cypress
- **Coverage**: 80%+ obrigatório

### CI/CD
- **Pipeline**: GitHub Actions
- **Stages**: Lint → Test → Build → Deploy
- **Environments**: dev, staging, prod

## Monitoramento

### Logs
- **Backend**: Structured JSON logs
- **Frontend**: Console + Sentry
- **Aggregation**: CloudWatch (AWS)

### Metrics
- **APM**: New Relic (planejado)
- **Uptime**: UptimeRobot
- **Performance**: Lighthouse CI

### Alertas
- **Errors**: Rate > 1%
- **Response Time**: p95 > 3s
- **Availability**: < 99.9%

## Limitações Conhecidas

1. **Windows Development**
   - WeasyPrint requer libs extras
   - Path handling diferente
   - Hot reload instável

2. **Async Limitations**
   - BackgroundTasks não aceita async
   - Alguns drivers sem suporte async

3. **Browser Limitations**
   - WebSocket reconexão manual
   - Large PDFs (>50MB) lento

## Próximas Atualizações

### Curto Prazo
- Redis cache implementation
- Prometheus metrics
- OpenTelemetry tracing

### Médio Prazo
- Kubernetes deployment
- GraphQL API
- Real-time collaboration

### Longo Prazo
- Multi-tenant architecture
- Plugin system
- Mobile app 