import { CommonModule, NgClass } from '@angular/common';
import { Component, OnInit, inject } from '@angular/core';

import { AvatarModule } from 'primeng/avatar';
import { AvatarGroupModule } from 'primeng/avatargroup';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { ChartModule } from 'primeng/chart';
import { DividerModule } from 'primeng/divider';
import { ProgressBarModule } from 'primeng/progressbar';
import { TableModule } from 'primeng/table';
import { TagModule } from 'primeng/tag';

import { IGeneratedProject } from '../projects/interfaces/generated-project.interface';
import { GeneratedProjectsService } from '../projects/services/generated-projects.service';

@Component({
	selector: 'app-dashboard-home',
	standalone: true,
	imports: [
		NgClass,
		CommonModule,
		CardModule,
		ButtonModule,
		ChartModule,
		TableModule,
		TagModule,
		ProgressBarModule,
		DividerModule,
		AvatarModule,
		AvatarGroupModule,
	],
	templateUrl: './dashboard-home.component.html',
	styles: [
		`
			:host ::ng-deep .p-card .p-card-content {
				padding-top: 1rem;
				padding-bottom: 1rem;
			}
		`,
	],
})
export class DashboardHomeComponent implements OnInit {
	private generatedProjectsService = inject(GeneratedProjectsService);

	// Dados dinâmicos
	public projetos: IGeneratedProject[] = [];
	public totalProjetos = 0;
	public clientesAtivos = 0;
	public projetosEmAndamento = 0;
	public projetosFinalizados = 0;
	public projetosAtrasados = 0;
	public projetosIniciando = 0;

	// Gráficos
	public projectStatusData: unknown;
	public doughnutOptions: unknown;
	public lineChartData: unknown;
	public lineChartOptions: unknown;

	// Projetos recentes (dados dinâmicos)
	public recentProjects: any[] = [];

	// Atividades Recentes
	public recentActivities = [
		{
			title: 'Novo projeto criado',
			description: 'Plataforma Educacional para EduLearn',
			time: 'Agora mesmo',
			icon: 'pi pi-plus',
			iconBg: 'bg-blue-500',
		},
		{
			title: 'Projeto concluído',
			description: 'App Bancário para FinanceBank',
			time: 'Há 2 horas',
			icon: 'pi pi-check',
			iconBg: 'bg-green-500',
		},
		{
			title: 'Cliente adicionado',
			description: 'InfoNews foi adicionado à sua lista',
			time: 'Ontem, 14:30',
			icon: 'pi pi-user-plus',
			iconBg: 'bg-purple-500',
		},
		{
			title: 'Alerta de prazo',
			description: 'O projeto Sistema de Gestão está atrasado',
			time: 'Ontem, 10:15',
			icon: 'pi pi-exclamation-triangle',
			iconBg: 'bg-orange-500',
		},
		{
			title: 'Estimativa atualizada',
			description: 'E-commerce Redesign: +2 sprints',
			time: 'Há 2 dias',
			icon: 'pi pi-calendar-plus',
			iconBg: 'bg-cyan-500',
		},
	];

	ngOnInit() {
		this.loadDashboardData();
	}

	/**
	 * 🎯 Carrega dados reais dos projetos para a dashboard
	 */
	private loadDashboardData(): void {
		// Buscar projetos do backend
		this.generatedProjectsService.projects$.subscribe({
			next: projetos => {
				this.projetos = projetos;
				this.calculateMetrics();
				this.updateCharts();
				this.updateRecentProjects();
			},
			error: error => {
				console.error('❌ Erro ao carregar projetos na dashboard:', error);
			},
		});

		// Forçar carregamento dos projetos
		this.generatedProjectsService.refreshProjects();
	}

	/**
	 * 🧮 Calcula métricas dinâmicas baseadas nos dados reais
	 */
	private calculateMetrics(): void {
		this.totalProjetos = this.projetos.length;

		// Contar clientes únicos
		const clientesUnicos = new Set(this.projetos.map(p => p.cliente_nome));
		this.clientesAtivos = clientesUnicos.size;

		// Contar projetos por status
		this.projetosEmAndamento = this.projetos.filter(
			p => p.status?.toLowerCase().includes('andamento') || p.status?.toLowerCase().includes('progress'),
		).length;

		this.projetosFinalizados = this.projetos.filter(
			p =>
				p.status?.toLowerCase().includes('concluído') ||
				p.status?.toLowerCase().includes('finalizado') ||
				p.status?.toLowerCase().includes('completed'),
		).length;

		this.projetosAtrasados = this.projetos.filter(
			p => p.status?.toLowerCase().includes('atrasado') || p.status?.toLowerCase().includes('delayed'),
		).length;

		this.projetosIniciando = this.projetos.filter(
			p => p.status?.toLowerCase().includes('iniciando') || p.status?.toLowerCase().includes('starting'),
		).length;
	}

	/**
	 * 📊 Atualiza gráficos com dados reais
	 */
	private updateCharts(): void {
		// Atualizar gráfico de status (doughnut)
		this.projectStatusData = {
			labels: ['Em andamento', 'Concluídos', 'Atrasados', 'Iniciando'],
			datasets: [
				{
					data: [
						this.projetosEmAndamento,
						this.projetosFinalizados,
						this.projetosAtrasados,
						this.projetosIniciando,
					],
					backgroundColor: ['#3B82F6', '#10B981', '#F97316', '#8B5CF6'],
					hoverBackgroundColor: ['#2563EB', '#059669', '#EA580C', '#7C3AED'],
				},
			],
		};

		this.initChartOptions();
	}

	/**
	 * 📋 Atualiza lista de projetos recentes com dados reais
	 */
	private updateRecentProjects(): void {
		// Pegar os 5 projetos mais recentes
		const projetosRecentes = this.projetos
			.sort(
				(a, b) =>
					new Date(b.updated_at || b.created_at).getTime() - new Date(a.updated_at || a.created_at).getTime(),
			)
			.slice(0, 5);

		this.recentProjects = projetosRecentes.map(projeto => ({
			name: projeto.nome_projeto,
			client: projeto.cliente_nome,
			status: this.mapStatus(projeto.status),
			progress: projeto.progresso || 0,
			team: this.generateTeamFromProject(projeto),
			// Dados específicos do projeto
			projeto: projeto.projeto, // Campo "projeto" com dados específicos
		}));
	}

	/**
	 * 🎨 Mapeia status para formato da dashboard
	 */
	private mapStatus(status: string): string {
		if (!status) return 'Indefinido';

		const statusLower = status.toLowerCase();
		if (statusLower.includes('andamento') || statusLower.includes('progress')) return 'Em andamento';
		if (statusLower.includes('concluído') || statusLower.includes('completed')) return 'Concluído';
		if (statusLower.includes('atrasado') || statusLower.includes('delayed')) return 'Atrasado';
		if (statusLower.includes('iniciando') || statusLower.includes('starting')) return 'Iniciando';

		return status;
	}

	/**
	 * 👥 Gera equipe fictícia baseada nos dados do projeto
	 */
	private generateTeamFromProject(projeto: IGeneratedProject): any[] {
		const cores = ['#3B82F6', '#10B981', '#F59E0B', '#6366F1', '#EC4899', '#8B5CF6', '#F97316', '#14B8A6'];

		// Se tem dados específicos da equipe no campo "projeto"
		if (projeto.projeto?.stacks) {
			const membros: any[] = [];
			projeto.projeto.stacks.forEach((stack, index) => {
				if (stack.desenvolvedores) {
					stack.desenvolvedores.forEach((dev, devIndex) => {
						membros.push({
							name: `Dev ${stack.stack} ${devIndex + 1}`,
							color: cores[(index + devIndex) % cores.length],
						});
					});
				}
			});
			return membros.slice(0, 4); // Máximo 4 membros
		}

		// Fallback: gerar equipe baseada no nome da equipe
		const nomeEquipe = projeto.equipe || 'Equipe Padrão';
		const numeroMembros = Math.min(Math.max(2, Math.floor(Math.random() * 4) + 1), 4);

		return Array.from({ length: numeroMembros }, (_, index) => ({
			name: `${nomeEquipe} ${index + 1}`,
			color: cores[index % cores.length],
		}));
	}

	/**
	 * ⚙️ Inicializa opções dos gráficos
	 */
	private initChartOptions(): void {
		this.doughnutOptions = {
			plugins: {
				legend: {
					position: 'bottom',
					labels: {
						usePointStyle: true,
						padding: 20,
					},
				},
			},
			cutout: '70%',
			responsive: true,
			maintainAspectRatio: false,
		};

		// Configuração do gráfico de linha com dados dinâmicos
		this.lineChartData = {
			labels: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'],
			datasets: [
				{
					label: 'Projetos Criados',
					data: this.calculateMonthlyData('created'),
					backgroundColor: 'rgba(59, 130, 246, 0.2)',
					borderColor: '#3B82F6',
					tension: 0.4,
					fill: true,
				},
				{
					label: 'Projetos Finalizados',
					data: this.calculateMonthlyData('completed'),
					backgroundColor: 'rgba(16, 185, 129, 0.2)',
					borderColor: '#10B981',
					tension: 0.4,
					fill: true,
				},
			],
		};

		this.lineChartOptions = {
			plugins: {
				legend: {
					position: 'bottom',
					labels: {
						usePointStyle: true,
						padding: 20,
					},
				},
				tooltip: {
					mode: 'index',
					intersect: false,
				},
			},
			scales: {
				x: {
					grid: {
						display: false,
					},
				},
				y: {
					beginAtZero: true,
					grid: {
						drawBorder: false,
					},
				},
			},
			responsive: true,
			maintainAspectRatio: false,
		};
	}

	/**
	 * 📅 Calcula dados mensais baseados nos projetos reais
	 */
	private calculateMonthlyData(type: 'created' | 'completed'): number[] {
		const monthlyData = new Array(12).fill(0);
		const currentYear = new Date().getFullYear();

		this.projetos.forEach(projeto => {
			let targetDate: string | undefined;

			if (type === 'created') {
				targetDate = projeto.created_at;
			} else if (type === 'completed') {
				// Considerar projetos finalizados baseado no status
				if (
					projeto.status?.toLowerCase().includes('concluído') ||
					projeto.status?.toLowerCase().includes('completed')
				) {
					targetDate = projeto.updated_at || projeto.created_at;
				}
			}

			if (targetDate) {
				const date = new Date(targetDate);
				if (date.getFullYear() === currentYear) {
					const month = date.getMonth();
					monthlyData[month]++;
				}
			}
		});

		return monthlyData;
	}

	public getSeverity(status: string): string {
		switch (status) {
			case 'Concluído':
				return 'success';
			case 'Em andamento':
				return 'info';
			case 'Atrasado':
				return 'danger';
			case 'Iniciando':
				return 'warning';
			default:
				return 'info';
		}
	}
}
