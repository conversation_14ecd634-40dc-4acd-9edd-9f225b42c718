<p-card
	styleClass="project-card h-full border-0 shadow-sm hover:shadow-lg transition-all duration-300 cursor-pointer"
	[ngClass]="{ 'card-clickable': clickable, 'card-new': project.status === 'Novo' }"
	(click)="onCardClick()"
	[pTooltip]="'Clique para ir para a próxima fase'"
	tooltipPosition="top"
	[tooltipDisabled]="!clickable"
>
	<ng-template pTemplate="header">
		<div class="relative">
			<!-- Header com gradiente vibrante baseado na pontuação -->
			<div class="h-[140px] relative flex items-end p-5 overflow-hidden" [style.background]="getGradientStyle()">
				<!-- Padrão decorativo opcional -->
				<div class="absolute top-0 right-0 w-32 h-32 bg-white opacity-10 rounded-full -mr-16 -mt-16"></div>
				<div class="absolute bottom-0 left-0 w-24 h-24 bg-white opacity-10 rounded-full -ml-12 -mb-12"></div>

				<!-- Badge de status no canto superior esquerdo -->
				<div class="absolute top-4 left-4">
					<p-tag
						[value]="project.status"
						[severity]="getStatusSeverity()"
						[rounded]="true"
						styleClass="text-xs font-medium"
					></p-tag>
				</div>

				<!-- Badge de pontuação no canto superior direito -->
				<div class="absolute top-4 right-4 flex items-center gap-2">
					<!-- 🎯 Badge de Estimativa Concluída -->
					@if (project.estimativa_status === 'concluida' && project.estimativa_detalhada) {
						<p-tag
							class="font-bold text-xs text-white bg-transparent border border-white/30 animate-pulse"
							[value]="'📊 Estimativa'"
							[rounded]="true"
							[pTooltip]="'Estimativa detalhada disponível! Clique para visualizar.'"
							tooltipPosition="bottom"
						></p-tag>
					}

					<p-tag
						class="font-bold text-sm text-white bg-transparent border border-white/30"
						[value]="'⭐ ' + (project.pontuacao || 0) + '/100'"
						[rounded]="true"
					></p-tag>
				</div>

				<!-- Título do projeto com sombra para melhor legibilidade -->
				<h3 class="text-white text-xl font-bold line-clamp-2 w-full drop-shadow-lg">
					{{ project.nome_projeto }}
				</h3>
			</div>

			<!-- Área do resumo com fundo suave -->
			<div class="bg-gradient-to-b from-gray-50 to-white p-4">
				<p class="text-gray-700 text-sm line-clamp-3">
					{{ getTruncatedSummary() }}
				</p>
			</div>
		</div>
	</ng-template>

	<!-- Conteúdo do Card -->
	<div class="flex flex-col h-full p-1">
		<!-- Cliente e Setor -->
		<div class="mb-3">
			<div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1">
				<div class="flex-1">
					<p class="text-xs text-gray-500 mb-1">Cliente</p>
					<p class="font-medium text-sm text-gray-900 truncate">{{ project.cliente_nome }}</p>
				</div>
				<div class="flex-1">
					<p class="text-xs text-gray-500 mb-1">Setor</p>
					<p class="text-sm text-gray-700 truncate">{{ project.cliente_sector }}</p>
				</div>
			</div>
		</div>

		<!-- Resumo -->
		<div class="mb-4 flex-grow">
			<p class="text-xs text-gray-500 mb-1">Resumo</p>
			<p class="text-gray-600 text-sm leading-relaxed">
				{{ getTruncatedSummary() }}
			</p>
			<!-- Justificativa como tooltip -->
			@if (project.justificativa) {
				<div
					class="mt-1 text-xs text-blue-600 cursor-help flex items-center"
					[pTooltip]="project.justificativa"
					tooltipPosition="top"
				>
					<i class="pi pi-info-circle mr-1"></i>
					<span>Ver justificativa</span>
				</div>
			}
		</div>

		<!-- Tags -->
		<div class="mb-4">
			<div class="flex flex-wrap gap-1.5">
				@for (tag of project.tags; track $index) {
					<p-chip [label]="tag" styleClass="bg-blue-50 text-blue-700 text-xs px-2 py-1"></p-chip>
				}
			</div>
		</div>

		<!-- Progresso -->
		<div class="mb-4">
			<div class="flex justify-between items-center mb-2">
				<span class="text-xs text-gray-500">Progresso</span>
				<span class="text-xs font-medium text-gray-700">{{ project.progresso }}%</span>
			</div>
			<p-progressBar
				[value]="project.progresso"
				[showValue]="false"
				[style]="{ height: '6px' }"
				styleClass="progress-bar-custom"
			></p-progressBar>
		</div>

		<!-- Status da Estimativa -->
		@if (project.estimativa_status === 'concluida' && project.estimativa_detalhada) {
			<div
				class="mb-3 p-2 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg cursor-pointer hover:from-green-100 hover:to-emerald-100 transition-all duration-200"
				(click)="onViewEstimate($event)"
			>
				<div class="flex items-center justify-between">
					<div class="flex items-center text-green-700">
						<i class="pi pi-check-circle mr-2"></i>
						<span class="text-xs font-medium">Estimativa Completa</span>
					</div>
					<p-chip
						label="Ver Detalhes"
						styleClass="bg-green-100 text-green-800 text-xs pointer-events-none"
						icon="pi pi-chart-line"
					></p-chip>
				</div>
			</div>
		} @else if (project.estimativa_status === 'processando') {
			<div class="mb-3 p-2 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg">
				<div class="flex items-center text-blue-700">
					<i class="pi pi-spin pi-spinner mr-2"></i>
					<span class="text-xs font-medium">Gerando Estimativa...</span>
				</div>
			</div>
		} @else if (project.estimativa_status === 'erro') {
			<div class="mb-3 p-2 bg-gradient-to-r from-red-50 to-pink-50 border border-red-200 rounded-lg">
				<div class="flex items-center text-red-700">
					<i class="pi pi-exclamation-circle mr-2"></i>
					<span class="text-xs font-medium">Erro na Estimativa</span>
				</div>
			</div>
		}

		<!-- Equipe e Data -->
		<div class="flex justify-between items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-100">
			<div class="flex items-center">
				<i class="pi pi-users mr-1.5"></i>
				<span>{{ project.equipe }}</span>
			</div>
			<div class="flex items-center">
				<i class="pi pi-calendar mr-1.5"></i>
				<span>{{ getFormattedDate() }}</span>
			</div>
		</div>
	</div>
</p-card>
