#!/usr/bin/env python3
"""
Sistema Principal de Enriquecimento Contínuo de Dados

Coordena todas as operações de atualização periódica e enriquecimento 
de dados de empresas monitoradas.
"""

import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

from clients.db import clients_collection
from clients.perplexity import (
    _gerar_dados_parcerias,
    _gerar_dados_modelo_negocio,
    _gerar_dados_pricing
)
from clients.parsers import (
    parse_dados_parcerias,
    parse_dados_modelo_negocio,
    parse_dados_pricing
)

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class EnrichmentType(Enum):
    """Tipos de enriquecimento disponíveis"""
    PARCERIAS = "parcerias"
    MODELO_NEGOCIO = "modelo_negocio"
    PRICING = "pricing"
    ALL = "all"


class PriorityLevel(Enum):
    """Níveis de prioridade para atualizações"""
    CRITICAL = 1    # Dados críticos desatualizados (>6 meses)
    HIGH = 2        # Dados importantes desatualizados (>3 meses)
    MEDIUM = 3      # Dados moderadamente desatualizados (>1 mês)
    LOW = 4         # Dados recentes (<1 mês)


@dataclass
class EnrichmentTask:
    """Tarefa de enriquecimento de dados"""
    company_id: str
    company_name: str
    enrichment_type: EnrichmentType
    priority: PriorityLevel
    last_updated: Optional[datetime]
    api_key: str
    site_url: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3


@dataclass
class EnrichmentResult:
    """Resultado de uma operação de enriquecimento"""
    company_id: str
    enrichment_type: EnrichmentType
    success: bool
    data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    processed_at: Optional[datetime] = None
    quality_score: Optional[float] = None

    def __post_init__(self):
        if self.processed_at is None:
            self.processed_at = datetime.utcnow()


class ContinuousEnrichmentSystem:
    """
    Sistema Principal de Enriquecimento Contínuo

    Coordena e executa operações de enriquecimento periódico de dados
    das empresas monitoradas.
    """

    def __init__(self, api_key: str, batch_size: int = 5,
                 max_concurrent: int = 3):
        """
        Inicializa o sistema de enriquecimento contínuo

        Args:
            api_key: Chave da API Perplexity
            batch_size: Número de empresas a processar por lote
            max_concurrent: Número máximo de operações concorrentes
        """
        self.api_key = api_key
        self.batch_size = batch_size
        self.max_concurrent = max_concurrent
        self.running = False

        # Contadores de estatísticas
        self.stats = {
            'total_processed': 0,
            'successful_updates': 0,
            'failed_updates': 0,
            'critical_priority_updates': 0,
            'last_run': None
        }

    async def start_continuous_enrichment(self,
                                          enrichment_types: Optional[List[EnrichmentType]] = None,
                                          max_companies: Optional[int] = None) -> Dict[str, Any]:
        """
        Inicia o processo de enriquecimento contínuo

        Args:
            enrichment_types: Tipos de enriquecimento a executar
            max_companies: Número máximo de empresas a processar

        Returns:
            Relatório de execução
        """
        if enrichment_types is None:
            enrichment_types = [EnrichmentType.ALL]

        logger.info("🚀 Iniciando enriquecimento contínuo de dados...")

        try:
            self.running = True
            start_time = datetime.utcnow()

            # 1. Identificar empresas que precisam de atualização
            companies_to_update = await self._identify_companies_for_update(
                enrichment_types, max_companies
            )

            if not companies_to_update:
                logger.info(
                    "✅ Nenhuma empresa necessita atualização no momento")
                return self._generate_summary_report(start_time, [])

            logger.info(
                f"📊 {len(companies_to_update)} empresas identificadas para atualização")

            # 2. Priorizar tarefas de enriquecimento
            enrichment_tasks = await self._prioritize_enrichment_tasks(companies_to_update)

            # 3. Executar enriquecimento em lotes
            results = await self._execute_enrichment_batches(enrichment_tasks)

            # 4. Processar e armazenar resultados
            await self._process_and_store_results(results)

            # 5. Gerar relatório de execução
            report = self._generate_summary_report(start_time, results)

            self.stats['last_run'] = datetime.utcnow()
            logger.info("✅ Enriquecimento contínuo concluído com sucesso")

            return report

        except Exception as e:
            logger.error(f"❌ Erro no enriquecimento contínuo: {e}")
            raise
        finally:
            self.running = False

    async def _identify_companies_for_update(self,
                                             enrichment_types: List[EnrichmentType],
                                             max_companies: Optional[int]) -> List[Dict]:
        """
        Identifica empresas que precisam de atualização de dados

        Args:
            enrichment_types: Tipos de enriquecimento solicitados
            max_companies: Limite máximo de empresas

        Returns:
            Lista de empresas candidatas a atualização
        """
        logger.info("🔍 Identificando empresas para atualização...")

        # Critérios de seleção baseados na idade dos dados
        cutoff_dates = {
            'critical': datetime.utcnow() - timedelta(days=180),  # 6 meses
            'high': datetime.utcnow() - timedelta(days=90),       # 3 meses
            'medium': datetime.utcnow() - timedelta(days=30),     # 1 mês
        }

        pipeline = []

        # Filtrar empresas com base nos tipos de enriquecimento
        match_conditions = {"$or": []}

        for enrichment_type in enrichment_types:
            if enrichment_type == EnrichmentType.ALL:
                # Buscar empresas com qualquer tipo de dado desatualizado
                match_conditions["$or"].extend([
                    {"reports.dados_parcerias.processed_at": {
                        "$lte": cutoff_dates['medium']}},
                    {"reports.dados_modelo_negocio.processed_at": {
                        "$lte": cutoff_dates['medium']}},
                    {"reports.dados_pricing.processed_at": {
                        "$lte": cutoff_dates['medium']}},
                    {"reports.dados_parcerias": {"$exists": False}},
                    {"reports.dados_modelo_negocio": {"$exists": False}},
                    {"reports.dados_pricing": {"$exists": False}}
                ])
            elif enrichment_type == EnrichmentType.PARCERIAS:
                match_conditions["$or"].extend([
                    {"reports.dados_parcerias.processed_at": {
                        "$lte": cutoff_dates['medium']}},
                    {"reports.dados_parcerias": {"$exists": False}}
                ])
            elif enrichment_type == EnrichmentType.MODELO_NEGOCIO:
                match_conditions["$or"].extend([
                    {"reports.dados_modelo_negocio.processed_at": {
                        "$lte": cutoff_dates['medium']}},
                    {"reports.dados_modelo_negocio": {"$exists": False}}
                ])
            elif enrichment_type == EnrichmentType.PRICING:
                match_conditions["$or"].extend([
                    {"reports.dados_pricing.processed_at": {
                        "$lte": cutoff_dates['medium']}},
                    {"reports.dados_pricing": {"$exists": False}}
                ])

        pipeline.append({"$match": match_conditions})

        # Projetar campos necessários
        pipeline.append({
            "$project": {
                "name": 1,
                "site": 1,
                "reports.dados_parcerias.processed_at": 1,
                "reports.dados_modelo_negocio.processed_at": 1,
                "reports.dados_pricing.processed_at": 1,
                "reports.dados_parcerias.data_quality_score": 1,
                "reports.dados_modelo_negocio.data_quality_score": 1,
                "reports.dados_pricing.data_quality_score": 1
            }
        })

        # Ordenar por prioridade (dados mais antigos primeiro)
        pipeline.append({"$sort": {"reports.dados_parcerias.processed_at": 1}})

        # Limitar número de empresas se especificado
        if max_companies:
            pipeline.append({"$limit": max_companies})

        try:
            companies = list(clients_collection.aggregate(pipeline))
            logger.info(
                f"📈 {len(companies)} empresas identificadas para atualização")
            return companies
        except Exception as e:
            logger.error(f"❌ Erro ao identificar empresas: {e}")
            return []

    async def _prioritize_enrichment_tasks(self, companies: List[Dict]) -> List[EnrichmentTask]:
        """
        Cria e prioriza tarefas de enriquecimento

        Args:
            companies: Lista de empresas a serem processadas

        Returns:
            Lista priorizada de tarefas de enriquecimento
        """
        logger.info("📋 Priorizando tarefas de enriquecimento...")

        tasks = []
        now = datetime.utcnow()

        for company in companies:
            company_id = str(company['_id'])
            company_name = company.get('name', 'Unknown')
            site_url = company.get('site')

            # Verificar cada tipo de dados
            reports = company.get('reports', [])

            # Encontrar dados de cada tipo
            parcerias_data = None
            modelo_negocio_data = None
            pricing_data = None

            for report in reports:
                if 'dados_parcerias' in report:
                    parcerias_data = report['dados_parcerias']
                if 'dados_modelo_negocio' in report:
                    modelo_negocio_data = report['dados_modelo_negocio']
                if 'dados_pricing' in report:
                    pricing_data = report['dados_pricing']

            # Criar tarefas baseadas na idade dos dados
            for enrichment_type, data in [
                (EnrichmentType.PARCERIAS, parcerias_data),
                (EnrichmentType.MODELO_NEGOCIO, modelo_negocio_data),
                (EnrichmentType.PRICING, pricing_data)
            ]:
                priority = self._calculate_priority(data, now)

                if priority != PriorityLevel.LOW:  # Só processa se precisar
                    last_updated = None
                    if data and 'processed_at' in data:
                        last_updated = data['processed_at']

                    task = EnrichmentTask(
                        company_id=company_id,
                        company_name=company_name,
                        enrichment_type=enrichment_type,
                        priority=priority,
                        last_updated=last_updated,
                        api_key=self.api_key,
                        site_url=site_url
                    )
                    tasks.append(task)

        # Ordenar por prioridade (crítico primeiro)
        tasks.sort(key=lambda x: (x.priority.value,
                   x.last_updated or datetime.min))

        logger.info(f"📊 {len(tasks)} tarefas de enriquecimento criadas")
        return tasks

    def _calculate_priority(self, data: Optional[Dict], now: datetime) -> PriorityLevel:
        """
        Calcula prioridade baseada na idade e qualidade dos dados

        Args:
            data: Dados existentes
            now: Timestamp atual

        Returns:
            Nível de prioridade calculado
        """
        if not data or 'processed_at' not in data:
            return PriorityLevel.CRITICAL  # Dados inexistentes

        last_update = data['processed_at']
        if isinstance(last_update, str):
            last_update = datetime.fromisoformat(
                last_update.replace('Z', '+00:00'))

        days_old = (now - last_update).days
        quality_score = data.get('data_quality_score', 0)

        # Considerar qualidade dos dados na priorização
        if days_old > 180 or quality_score < 50:
            return PriorityLevel.CRITICAL
        elif days_old > 90 or quality_score < 70:
            return PriorityLevel.HIGH
        elif days_old > 30 or quality_score < 85:
            return PriorityLevel.MEDIUM
        else:
            return PriorityLevel.LOW

    async def _execute_enrichment_batches(self, tasks: List[EnrichmentTask]) -> List[EnrichmentResult]:
        """
        Executa tarefas de enriquecimento em lotes

        Args:
            tasks: Lista de tarefas a executar

        Returns:
            Lista de resultados de enriquecimento
        """
        logger.info(
            f"⚡ Executando {len(tasks)} tarefas em lotes de {self.batch_size}")

        all_results = []

        # Processar em lotes
        for i in range(0, len(tasks), self.batch_size):
            batch = tasks[i:i + self.batch_size]
            logger.info(
                f"🔄 Processando lote {(i // self.batch_size) + 1} com {len(batch)} tarefas")

            # Executar lote com limite de concorrência
            semaphore = asyncio.Semaphore(self.max_concurrent)
            batch_results = await asyncio.gather(*[
                self._execute_single_enrichment_task(task, semaphore)
                for task in batch
            ], return_exceptions=True)

            # Processar resultados do lote
            for result in batch_results:
                if isinstance(result, Exception):
                    logger.error(f"❌ Erro no lote: {result}")
                    continue
                all_results.append(result)

            # Pausa entre lotes para não sobrecarregar APIs
            if i + self.batch_size < len(tasks):
                logger.info("⏸️ Pausa entre lotes...")
                await asyncio.sleep(2)

        logger.info(
            f"✅ {len(all_results)} tarefas de enriquecimento concluídas")
        return all_results

    async def _execute_single_enrichment_task(self,
                                              task: EnrichmentTask,
                                              semaphore: asyncio.Semaphore) -> EnrichmentResult:
        """
        Executa uma única tarefa de enriquecimento

        Args:
            task: Tarefa a executar
            semaphore: Semáforo para controle de concorrência

        Returns:
            Resultado da execução
        """
        async with semaphore:
            try:
                logger.info(
                    f"🔄 Enriquecendo {task.enrichment_type.value} para {task.company_name}")

                # Selecionar função apropriada
                if task.enrichment_type == EnrichmentType.PARCERIAS:
                    raw_data = _gerar_dados_parcerias(
                        task.api_key, task.company_name, task.site_url)
                    parsed_data = parse_dados_parcerias(raw_data)
                elif task.enrichment_type == EnrichmentType.MODELO_NEGOCIO:
                    raw_data = _gerar_dados_modelo_negocio(
                        task.api_key, task.company_name, task.site_url)
                    parsed_data = parse_dados_modelo_negocio(raw_data)
                elif task.enrichment_type == EnrichmentType.PRICING:
                    raw_data = _gerar_dados_pricing(
                        task.api_key, task.company_name, task.site_url)
                    parsed_data = parse_dados_pricing(raw_data)
                else:
                    raise ValueError(
                        f"Tipo de enriquecimento não suportado: {task.enrichment_type}")

                self.stats['successful_updates'] += 1

                if task.priority == PriorityLevel.CRITICAL:
                    self.stats['critical_priority_updates'] += 1

                return EnrichmentResult(
                    company_id=task.company_id,
                    enrichment_type=task.enrichment_type,
                    success=True,
                    data=parsed_data,
                    quality_score=parsed_data.get('data_quality_score', 0)
                )

            except Exception as e:
                logger.error(f"❌ Erro ao enriquecer {task.company_name}: {e}")
                self.stats['failed_updates'] += 1

                return EnrichmentResult(
                    company_id=task.company_id,
                    enrichment_type=task.enrichment_type,
                    success=False,
                    error_message=str(e)
                )
            finally:
                self.stats['total_processed'] += 1

    async def _process_and_store_results(self, results: List[EnrichmentResult]) -> None:
        """
        Processa e armazena resultados no MongoDB

        Args:
            results: Lista de resultados a armazenar
        """
        logger.info("💾 Armazenando resultados no MongoDB...")

        successful_updates = 0

        for result in results:
            if not result.success:
                continue

            try:
                # Preparar dados para atualização
                field_name = f"reports.$[elem].dados_{result.enrichment_type.value}"

                update_data = {
                    field_name: result.data
                }

                # Atualizar documento no MongoDB
                update_result = clients_collection.update_one(
                    {"_id": result.company_id},
                    {"$set": update_data},
                    array_filters=[{
                        "elem.report_type": {"$exists": True}
                    }],
                    upsert=False
                )

                if update_result.modified_count > 0:
                    successful_updates += 1
                    logger.debug(
                        f"✅ Dados de {result.enrichment_type.value} atualizados para empresa {result.company_id}")

            except Exception as e:
                logger.error(
                    f"❌ Erro ao armazenar resultado para {result.company_id}: {e}")

        logger.info(
            f"💾 {successful_updates} atualizações armazenadas com sucesso")

    def _generate_summary_report(self, start_time: datetime,
                                 results: List[EnrichmentResult]) -> Dict[str, Any]:
        """
        Gera relatório resumido da execução

        Args:
            start_time: Início da execução
            results: Resultados obtidos

        Returns:
            Relatório formatado
        """
        end_time = datetime.utcnow()
        duration = (end_time - start_time).total_seconds()

        successful = sum(1 for r in results if r.success)
        failed = len(results) - successful

        # Calcular qualidade média
        avg_quality = 0
        if successful > 0:
            quality_scores = [
                r.quality_score for r in results if r.success and r.quality_score]
            if quality_scores:
                avg_quality = sum(quality_scores) / len(quality_scores)

        # Agrupar por tipo de enriquecimento
        by_type = {}
        for result in results:
            enrichment_type = result.enrichment_type.value
            if enrichment_type not in by_type:
                by_type[enrichment_type] = {'success': 0, 'failed': 0}

            if result.success:
                by_type[enrichment_type]['success'] += 1
            else:
                by_type[enrichment_type]['failed'] += 1

        report = {
            'execution_summary': {
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration_seconds': round(duration, 2),
                'total_tasks': len(results),
                'successful_updates': successful,
                'failed_updates': failed,
                'success_rate': round((successful / len(results)) * 100, 2) if results else 0,
                'average_quality_score': round(avg_quality, 2)
            },
            'enrichment_breakdown': by_type,
            'system_stats': self.stats.copy()
        }

        logger.info(
            f"📊 Relatório gerado: {successful}/{len(results)} atualizações bem-sucedidas")
        return report

    def get_system_status(self) -> Dict[str, Any]:
        """
        Retorna status atual do sistema

        Returns:
            Status do sistema e estatísticas
        """
        return {
            'running': self.running,
            'configuration': {
                'batch_size': self.batch_size,
                'max_concurrent': self.max_concurrent
            },
            'statistics': self.stats.copy()
        }
