"""
ResponseFormatter - Formatação Padronizada de Respostas API

Este módulo é responsável por garantir que todas as respostas da API
sigam um padrão consistente, facilitando o consumo pelo frontend Angular.
"""

import logging
from typing import Dict, Any, Optional, List, Union
from datetime import datetime, timezone
from enum import Enum
from pydantic import BaseModel


logger = logging.getLogger(__name__)


class ResponseStatus(Enum):
    """Status padronizados de resposta"""
    SUCCESS = "success"
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"


class APIMetadata(BaseModel):
    """Metadados padronizados para respostas"""
    timestamp: str
    version: str = "1.0"
    request_id: Optional[str] = None
    processing_time_ms: Optional[float] = None


class APIPagination(BaseModel):
    """Informações de paginação"""
    page: int
    per_page: int
    total_items: int
    total_pages: int
    has_next: bool
    has_previous: bool


class APIResponse(BaseModel):
    """Estrutura padronizada de resposta da API"""
    status: ResponseStatus
    message: str
    data: Optional[Union[Dict[str, Any], List[Dict[str, Any]]]] = None
    metadata: APIMetadata
    pagination: Optional[APIPagination] = None
    errors: Optional[List[str]] = None
    warnings: Optional[List[str]] = None


class ResponseFormatter:
    """
    Formatador de Respostas da API

    Responsável por:
    - Padronizar formato de todas as respostas
    - Incluir metadados consistentes
    - Tratar paginação automaticamente
    - Formatar erros e warnings
    - Facilitar consumo pelo frontend
    """

    def __init__(self):
        """Inicializa o ResponseFormatter"""
        self.api_version = "1.0"

    def success(
        self,
        data: Optional[Union[Dict[str, Any], List[Dict[str, Any]]]] = None,
        message: str = "Operação realizada com sucesso",
        request_id: Optional[str] = None,
        processing_time_ms: Optional[float] = None,
        pagination: Optional[APIPagination] = None
    ) -> Dict[str, Any]:
        """
        Formata resposta de sucesso

        Args:
            data: Dados da resposta
            message: Mensagem de sucesso
            request_id: ID da requisição
            processing_time_ms: Tempo de processamento
            pagination: Informações de paginação

        Returns:
            Resposta formatada
        """
        try:
            response = APIResponse(
                status=ResponseStatus.SUCCESS,
                message=message,
                data=data,
                metadata=APIMetadata(
                    timestamp=datetime.now(timezone.utc).isoformat(),
                    version=self.api_version,
                    request_id=request_id,
                    processing_time_ms=processing_time_ms
                ),
                pagination=pagination
            )

            return response.model_dump(exclude_none=True)

        except Exception as e:
            logger.error(f"Erro ao formatar resposta de sucesso: {str(e)}")
            return self._fallback_response("Erro interno na formatação da resposta")

    def error(
        self,
        message: str = "Erro interno do servidor",
        errors: Optional[List[str]] = None,
        error_code: Optional[str] = None,
        request_id: Optional[str] = None,
        processing_time_ms: Optional[float] = None
    ) -> Dict[str, Any]:
        """
        Formata resposta de erro

        Args:
            message: Mensagem principal do erro
            errors: Lista de erros específicos
            error_code: Código do erro
            request_id: ID da requisição
            processing_time_ms: Tempo de processamento

        Returns:
            Resposta de erro formatada
        """
        try:
            response = APIResponse(
                status=ResponseStatus.ERROR,
                message=message,
                metadata=APIMetadata(
                    timestamp=datetime.now(timezone.utc).isoformat(),
                    version=self.api_version,
                    request_id=request_id,
                    processing_time_ms=processing_time_ms
                ),
                errors=errors or [message]
            )

            return response.model_dump(exclude_none=True)

        except Exception as e:
            logger.error(f"Erro ao formatar resposta de erro: {str(e)}")
            return self._fallback_response("Erro crítico na formatação da resposta")

    def warning(
        self,
        data: Optional[Union[Dict[str, Any], List[Dict[str, Any]]]] = None,
        message: str = "Operação concluída com avisos",
        warnings: Optional[List[str]] = None,
        request_id: Optional[str] = None,
        processing_time_ms: Optional[float] = None
    ) -> Dict[str, Any]:
        """
        Formata resposta com warnings

        Args:
            data: Dados da resposta
            message: Mensagem principal
            warnings: Lista de warnings
            request_id: ID da requisição
            processing_time_ms: Tempo de processamento

        Returns:
            Resposta com warnings formatada
        """
        try:
            response = APIResponse(
                status=ResponseStatus.WARNING,
                message=message,
                data=data,
                metadata=APIMetadata(
                    timestamp=datetime.now(timezone.utc).isoformat(),
                    version=self.api_version,
                    request_id=request_id,
                    processing_time_ms=processing_time_ms
                ),
                warnings=warnings or [message]
            )

            return response.model_dump(exclude_none=True)

        except Exception as e:
            logger.error(f"Erro ao formatar resposta de warning: {str(e)}")
            return self._fallback_response("Erro na formatação da resposta")

    def paginated_success(
        self,
        data: List[Dict[str, Any]],
        page: int,
        per_page: int,
        total_items: int,
        message: str = "Dados recuperados com sucesso",
        request_id: Optional[str] = None,
        processing_time_ms: Optional[float] = None
    ) -> Dict[str, Any]:
        """
        Formata resposta paginada de sucesso

        Args:
            data: Lista de dados
            page: Página atual
            per_page: Itens por página
            total_items: Total de itens
            message: Mensagem de sucesso
            request_id: ID da requisição
            processing_time_ms: Tempo de processamento

        Returns:
            Resposta paginada formatada
        """
        try:
            total_pages = (total_items + per_page - 1) // per_page
            has_next = page < total_pages
            has_previous = page > 1

            pagination = APIPagination(
                page=page,
                per_page=per_page,
                total_items=total_items,
                total_pages=total_pages,
                has_next=has_next,
                has_previous=has_previous
            )

            return self.success(
                data=data,
                message=message,
                request_id=request_id,
                processing_time_ms=processing_time_ms,
                pagination=pagination
            )

        except Exception as e:
            logger.error(f"Erro ao formatar resposta paginada: {str(e)}")
            return self.error("Erro na paginação dos dados")

    def validation_error(
        self,
        validation_errors: List[str],
        message: str = "Dados de entrada inválidos",
        request_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Formata erro de validação

        Args:
            validation_errors: Lista de erros de validação
            message: Mensagem principal
            request_id: ID da requisição

        Returns:
            Resposta de erro de validação
        """
        return self.error(
            message=message,
            errors=validation_errors,
            request_id=request_id
        )

    def not_found(
        self,
        resource: str = "recurso",
        resource_id: Optional[str] = None,
        request_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Formata erro de recurso não encontrado

        Args:
            resource: Nome do recurso
            resource_id: ID do recurso
            request_id: ID da requisição

        Returns:
            Resposta de não encontrado
        """
        message = f"{resource.title()} não encontrado"
        if resource_id:
            message += f" (ID: {resource_id})"

        return self.error(
            message=message,
            errors=[message],
            request_id=request_id
        )

    def unauthorized(
        self,
        message: str = "Acesso não autorizado",
        request_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Formata erro de não autorização

        Args:
            message: Mensagem de erro
            request_id: ID da requisição

        Returns:
            Resposta de não autorizado
        """
        return self.error(
            message=message,
            errors=[message],
            request_id=request_id
        )

    def forbidden(
        self,
        message: str = "Acesso proibido",
        request_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Formata erro de acesso proibido

        Args:
            message: Mensagem de erro
            request_id: ID da requisição

        Returns:
            Resposta de acesso proibido
        """
        return self.error(
            message=message,
            errors=[message],
            request_id=request_id
        )

    def _fallback_response(self, message: str) -> Dict[str, Any]:
        """
        Resposta de fallback em caso de erro crítico

        Args:
            message: Mensagem de erro

        Returns:
            Resposta básica de erro
        """
        return {
            "status": "error",
            "message": message,
            "metadata": {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "version": self.api_version
            },
            "errors": [message]
        }


# Instância global do formatter
response_formatter = ResponseFormatter()
