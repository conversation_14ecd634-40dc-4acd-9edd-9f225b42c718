import { Component, output, viewChild } from '@angular/core';
import { RouterLink } from '@angular/router';

import { MenuItem } from 'primeng/api';
import { AvatarModule } from 'primeng/avatar';
import { ButtonModule } from 'primeng/button';
import { Menu, MenuModule } from 'primeng/menu';
import { ToolbarModule } from 'primeng/toolbar';

@Component({
	selector: 'app-header',
	standalone: true,
	imports: [RouterLink, ButtonModule, ToolbarModule, AvatarModule, MenuModule],
	templateUrl: './header.component.html',
	styles: [
		`
			:host ::ng-deep .p-toolbar {
				background-color: transparent;
				border: none;
			}
		`,
	],
})
export class HeaderComponent {
	public menuToggle = output<void>();
	public readonly userMenu = viewChild.required<Menu>('userMenu');

	public userMenuItems: Array<MenuItem> = [
		{
			label: 'Perfil',
			icon: 'pi pi-user',
			routerLink: '/profile',
		},
		{
			label: 'Configurações',
			icon: 'pi pi-cog',
			routerLink: '/settings',
		},
		{
			separator: true,
		},
		{
			label: 'Sair',
			icon: 'pi pi-sign-out',
			command: () => {
				// Implementar lógica de logout aqui
				console.log('Logout');
			},
		},
	];

	public onMenuToggle(): void {
		this.menuToggle.emit();
	}

	public toggleUserMenu(event: Event): void {
		this.userMenu().toggle(event);
	}
}
