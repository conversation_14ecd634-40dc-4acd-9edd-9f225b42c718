import { CommonModule } from '@angular/common';
import { Component, Input, OnChanges, OnInit } from '@angular/core';
import { Dom<PERSON>anitizer, SafeHtml } from '@angular/platform-browser';

@Component({
	selector: 'app-markdown-viewer',
	standalone: true,
	imports: [CommonModule],
	template: `
		<div class="markdown-container">
			<div class="markdown-content" [innerHTML]="processedContent"></div>
		</div>
	`,
	styles: [
		`
			.markdown-container {
				width: 100%;
				background: #ffffff;
				border: 1px solid #e0e0e0;
				border-radius: 8px;
				padding: 1.5rem;
			}

			:host ::ng-deep .markdown-content {
				line-height: 1.6;
				color: #374151;
			}

			:host ::ng-deep .markdown-content h1 {
				color: #111827;
				font-size: 1.875rem;
				font-weight: 700;
				margin: 0 0 1rem 0;
				border-bottom: 2px solid #e5e7eb;
				padding-bottom: 0.5rem;
			}

			:host ::ng-deep .markdown-content h2 {
				color: #1f2937;
				font-size: 1.5rem;
				font-weight: 600;
				margin: 1.5rem 0 1rem 0;
			}

			:host ::ng-deep .markdown-content h3 {
				color: #374151;
				font-size: 1.25rem;
				font-weight: 600;
				margin: 1.25rem 0 0.75rem 0;
			}

			:host ::ng-deep .markdown-content h4 {
				color: #4b5563;
				font-size: 1.125rem;
				font-weight: 600;
				margin: 1rem 0 0.5rem 0;
			}

			:host ::ng-deep .markdown-content p {
				margin: 0.75rem 0;
				text-align: justify;
			}

			:host ::ng-deep .markdown-content ul,
			:host ::ng-deep .markdown-content ol {
				margin: 0.75rem 0;
				padding-left: 1.5rem;
			}

			:host ::ng-deep .markdown-content li {
				margin: 0.25rem 0;
			}

			:host ::ng-deep .markdown-content code {
				background-color: #f3f4f6;
				color: #1f2937;
				padding: 0.125rem 0.25rem;
				border-radius: 0.25rem;
				font-family: 'Courier New', monospace;
				font-size: 0.875rem;
			}

			:host ::ng-deep .markdown-content pre {
				background-color: #1f2937;
				color: #f9fafb;
				padding: 1rem;
				border-radius: 0.5rem;
				overflow-x: auto;
				margin: 1rem 0;
			}

			:host ::ng-deep .markdown-content pre code {
				background: none;
				color: inherit;
				padding: 0;
			}

			:host ::ng-deep .markdown-content blockquote {
				border-left: 4px solid #3b82f6;
				background-color: #eff6ff;
				padding: 1rem;
				margin: 1rem 0;
				font-style: italic;
			}

			:host ::ng-deep .markdown-content table {
				width: 100%;
				border-collapse: collapse;
				margin: 1rem 0;
			}

			:host ::ng-deep .markdown-content table th,
			:host ::ng-deep .markdown-content table td {
				border: 1px solid #d1d5db;
				padding: 0.5rem;
				text-align: left;
			}

			:host ::ng-deep .markdown-content table th {
				background-color: #f9fafb;
				font-weight: 600;
			}

			:host ::ng-deep .markdown-content strong {
				font-weight: 700;
				color: #111827;
			}

			:host ::ng-deep .markdown-content em {
				font-style: italic;
				color: #4b5563;
			}
		`,
	],
})
export class MarkdownViewerComponent implements OnInit, OnChanges {
	@Input() content: string = '';
	@Input() enableKatex: boolean = false;
	@Input() enableMermaid: boolean = false;

	markdownContent: string = '';
	processedContent: SafeHtml = '';

	constructor(private sanitizer: DomSanitizer) {}

	ngOnInit(): void {
		this.processMarkdown();
	}

	ngOnChanges(): void {
		this.processMarkdown();
	}

	private processMarkdown(): void {
		if (!this.content) {
			this.markdownContent = '';
			this.processedContent = '';
			return;
		}

		// Remover prefixo "markdown" se existir
		let processed = this.content.replace(/^markdown\s*\n?/, '').trim();

		// Conversão aprimorada de Markdown para HTML
		let html = processed
			// Headers (ordem importante: h4, h3, h2, h1)
			.replace(/^#### (.*)/gm, '<h4>$1</h4>')
			.replace(/^### (.*)/gm, '<h3>$1</h3>')
			.replace(/^## (.*)/gm, '<h2>$1</h2>')
			.replace(/^# (.*)/gm, '<h1>$1</h1>')

			// Code blocks (antes de inline code)
			.replace(/```([^`]+)```/gs, '<pre><code>$1</code></pre>')

			// Bold e Italic
			.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
			.replace(/\*(.*?)\*/g, '<em>$1</em>')

			// Inline code
			.replace(/`([^`]+)`/g, '<code>$1</code>')

			// Links
			.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')

			// Tables (básico)
			.replace(/\|(.+)\|/g, (_, content) => {
				const cells = content.split('|').map((cell: string) => cell.trim());
				return '<tr>' + cells.map((cell: string) => `<td>${cell}</td>`).join('') + '</tr>';
			})

			// Blockquotes
			.replace(/^> (.*)/gm, '<blockquote>$1</blockquote>')

			// Horizontal rules
			.replace(/^---$/gm, '<hr>')
			.replace(/^\*\*\*$/gm, '<hr>')

			// Numbered lists
			.replace(/^\d+\. (.*)/gm, '<li class="numbered">$1</li>')

			// Bullet lists
			.replace(/^- (.*)/gm, '<li>$1</li>')
			.replace(/^\* (.*)/gm, '<li>$1</li>')

			// Line breaks (depois de listas)
			.replace(/\n\n/g, '</p><p>')
			.replace(/\n/g, '<br>');

		// Wrap tables
		html = html.replace(/(<tr>.*<\/tr>)/gs, '<table class="markdown-table">$1</table>');

		// Wrap numbered lists
		html = html.replace(/(<li class="numbered">.*<\/li>)/gs, '<ol>$1</ol>');

		// Wrap bullet lists
		html = html.replace(/(<li>(?!.*class="numbered").*<\/li>)/gs, '<ul>$1</ul>');

		// Wrap in paragraphs (se não tem headers)
		if (!html.includes('<h1>') && !html.includes('<h2>') && !html.includes('<h3>') && !html.includes('<h4>')) {
			html = '<p>' + html + '</p>';
		}

		// Clean up empty paragraphs
		html = html.replace(/<p><\/p>/g, '');
		html = html.replace(/<p>\s*<\/p>/g, '');

		this.markdownContent = processed;
		this.processedContent = this.sanitizer.bypassSecurityTrustHtml(html);
	}
}
