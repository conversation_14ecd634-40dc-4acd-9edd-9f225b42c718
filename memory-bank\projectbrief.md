# Project Brief - ScopeAI

**Versão**: 1.0.0
**Última Atualização**: 2025-01-29
**Status**: Em Desenvolvimento Ativo

## 🎯 Visão Geral

ScopeAI é uma plataforma inteligente de análise e consultoria digital que utiliza múltiplos agentes de IA para fornecer insights profundos sobre empresas, mercados e projetos de software. O sistema analisa URLs de empresas e gera relatórios detalhados, diagnósticos técnicos, estimativas de projetos e recomendações estratégicas.

## 🚀 Objetivos Principais

1. **Análise Automatizada de Empresas**: Processar URLs e gerar dossiês expandidos com análise SWOT, stack técnico, modelo de negócio, parcerias e pricing
2. **Geração Inteligente de Projetos**: Sugerir projetos de software personalizados baseados na análise da empresa
3. **Estimativas Profissionais**: Utilizar Team Agno (10 agentes especializados) para gerar estimativas detalhadas de projetos
4. **Diagnósticos Técnicos**: Análise Lighthouse, screenshots Playwright, visual analysis e benchmarks
5. **Geração de Relatórios**: PDFs expandidos (15+ seções), relatórios HTML, podcasts e documentação técnica

## 📋 Requisitos Funcionais

### Core Features
- [x] Cadastro e gestão de clientes
- [x] Análise via Perplexity API (dossiê expandido)
- [x] Pesquisa de mercado automatizada
- [x] Geração automática de projetos sugeridos
- [x] Sistema de estimativas com Team Agno
- [x] Geração automática de PDFs expandidos
- [x] Sistema de notificações WebSocket
- [x] Knowledge Management System (embeddings OpenAI)
- [x] Cache distribuído com Redis
- [x] Sistema assíncrono com 266% speedup

### Módulos Especializados
- [x] Visual Analyzer (análise UX/UI)
- [x] Lighthouse Analyzer (performance/SEO)
- [x] Playwright Screenshots (evidências visuais)
- [x] Market Research Module
- [x] Report Generator (markdown/HTML/PDF)
- [x] Podcast Generator
- [x] Project Team (10 agentes especializados)

## 🎯 Escopo do MVP

1. **Frontend Angular 19**
   - Interface moderna com Tailwind CSS
   - Dashboard interativo
   - Visualização de projetos e estimativas
   - WebSocket para atualizações real-time

2. **Backend FastAPI**
   - Arquitetura assíncrona otimizada
   - MongoDB Atlas para persistência
   - GridFS para armazenamento de PDFs
   - Sistema de background tasks

3. **Integrações**
   - Perplexity API para análise
   - OpenAI para embeddings e análise
   - Cohere Command-R para geração de conteúdo
   - OpenAI para Team Agno

## 🚧 Limitações Conhecidas

1. Dependência de APIs externas (Perplexity, OpenAI)
2. Custo elevado por análise (~$2-5 por cliente)
3. Tempo de processamento completo (~5-10 minutos)
4. Arquitetura monolítica no backend (refatoração planejada)

## 📊 Métricas de Sucesso

- Tempo de análise completa < 10 minutos
- Precisão das estimativas > 85%
- Qualidade dos insights > 90%
- Uptime do sistema > 99%
- Satisfação do usuário > 4.5/5

## 🔄 Status Atual

- **Frontend**: Angular 19 migrado, control flow moderno
- **Backend**: Conversão assíncrona completa, 266% speedup
- **Features**: 80% implementadas e funcionais
- **Testes**: 42+ testes automatizados
- **Deploy**: Docker Compose local funcional

## 📅 Próximos Passos

1. Implementar Automated Validation Module (Playwright)
2. Completar refatoração para microserviços
3. Adicionar SearchSociety para reduzir custos
4. Implementar RAG com ChromaDB
5. Deploy em produção com Kubernetes 