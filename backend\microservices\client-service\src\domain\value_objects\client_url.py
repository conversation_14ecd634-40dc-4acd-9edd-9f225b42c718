"""Value Object para representar URL de cliente com validação."""

import re
from typing import Any
from urllib.parse import urlparse, urlunparse


class ClientUrl:
    """Value Object imutável para URL de cliente.

    Garante que a URL é válida e normalizada.
    """

    def __init__(self, url: str) -> None:
        """Inicializa ClientUrl com validação.

        Args:
            url: URL do cliente

        Raises:
            ValueError: Se a URL for inválida
        """
        if not url:
            raise ValueError("URL não pode ser vazia")

        # Remove espaços em branco
        url = url.strip()

        # Adiciona protocolo se não tiver
        if not url.startswith(('http://', 'https://')):
            url = f'https://{url}'

        # Valida formato da URL
        try:
            parsed = urlparse(url)
            if not parsed.netloc:
                raise ValueError(f"URL inválida: {url}")

            # Normaliza a URL
            # Remove trailing slash, converte para lowercase o domínio
            normalized = urlunparse((
                parsed.scheme.lower(),
                parsed.netloc.lower(),
                parsed.path.rstrip('/'),
                parsed.params,
                parsed.query,
                parsed.fragment
            ))

            self._value = normalized

        except Exception as e:
            raise ValueError(f"URL inválida: {url}. Erro: {str(e)}")

    @property
    def value(self) -> str:
        """Retorna o valor da URL normalizada."""
        return self._value

    @property
    def domain(self) -> str:
        """Retorna apenas o domínio da URL."""
        parsed = urlparse(self._value)
        return parsed.netloc

    @property
    def is_secure(self) -> bool:
        """Verifica se a URL usa HTTPS."""
        return self._value.startswith('https://')

    def __str__(self) -> str:
        """Representação string da URL."""
        return self._value

    def __repr__(self) -> str:
        """Representação para debug."""
        return f"ClientUrl('{self._value}')"

    def __eq__(self, other: Any) -> bool:
        """Compara duas URLs."""
        if not isinstance(other, ClientUrl):
            return False
        return self._value == other._value

    def __hash__(self) -> int:
        """Hash da URL para uso em sets/dicts."""
        return hash(self._value)
