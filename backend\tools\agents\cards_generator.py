"""
Gerador de Cards de Projetos

Módulo responsável por gerar automaticamente cards de projetos baseados na
análise consolidada dos agentes especializados do time Agno.

Princípios seguidos:
- Clean Core: Lógica de negócio separada da infraestrutura
- DRY: Reutilização de código e evitar duplicação
- Single Responsibility: Cada função tem uma responsabilidade específica
"""

import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from .schemas import (ProjectCard, ProjectCardsResponse,
                      ProjectCardStatus, PriorityLevel, EffortLevel)
from .storage import AgentStorage
from .orchestration.storage import OrchestrationStorage

logger = logging.getLogger(__name__)


class ProjectCardsGenerator:
    """
    Gerador de cards de projetos baseado em análises consolidadas

    Responsável por processar os resultados dos agentes e gerar
    sugestões estruturadas de projetos para os clientes.
    """

    def __init__(self, storage: AgentStorage):
        """
        Inicializa o gerador com acesso ao storage de dados

        Args:
            storage: Instância do storage para acessar dados das análises
        """
        self.storage = storage
        self._prompt_template = self._create_prompt_template()

    def _create_prompt_template(self) -> str:
        """
        Cria o template do prompt para geração de cards

        Returns:
            String com o template do prompt
        """
        return """
Com base na análise consolidada dos agentes especializados, gere {num_cards} sugestões de projetos para a empresa "{client_name}" do setor "{sector}".

CONTEXTO DA ANÁLISE:
- Score geral atual: {overall_score}/100
- Principais problemas identificados: {main_issues}
- Recomendações prioritárias: {priority_recommendations}
- Oportunidades de mercado: {market_opportunities}

INSTRUÇÕES PARA GERAÇÃO:
1. Cada projeto deve ter um título claro e específico (5-50 caracteres)
2. Resumo conciso do objetivo (20-90 caracteres)
3. Exatamente 3 tags relevantes que categorizam o projeto
4. Prioridade baseada no impacto e urgência identificados
5. Estimativa de esforço realista
6. Duração estimada considerando a complexidade

Os projetos devem ser baseados nas recomendações prioritárias e representar iniciativas concretas e acionáveis que resolvam os problemas identificados.

Formate sua resposta como JSON estruturado seguindo este esquema:
{{
  "projects": [
    {{
      "title": "Título do Projeto",
      "summary": "Resumo conciso do objetivo do projeto",
      "tags": ["Tag1", "Tag2", "Tag3"],
      "priority": "alta|média|baixa|crítica",
      "estimated_effort": "baixo|médio|alto|muito alto",
      "estimated_duration": "1-2 semanas|1-3 meses|etc",
      "recommendations_used": ["rec_id_1", "rec_id_2"]
    }}
  ]
}}
"""

    def generate_cards(
        self,
        client_id: str,
        analysis_id: str,
        num_cards: int = 3,
        model_config: Optional[Dict[str, Any]] = None
    ) -> ProjectCardsResponse:
        """
        Gera cards de projetos baseados na análise consolidada

        Args:
            client_id: ID do cliente
            analysis_id: ID da análise consolidada
            num_cards: Número de cards a gerar (padrão: 3)
            model_config: Configurações do modelo de IA (opcional)

        Returns:
            ProjectCardsResponse com os cards gerados

        Raises:
            ValueError: Se não encontrar dados da análise
            Exception: Se houver erro na geração
        """
        try:
            logger.info(
                f"Iniciando geração de {num_cards} cards para cliente {client_id}")

            # Recuperar dados da análise consolidada
            analysis_data = self._get_analysis_data(analysis_id)
            if not analysis_data:
                raise ValueError(f"Análise {analysis_id} não encontrada")

            # Extrair informações relevantes
            context = self._extract_analysis_context(analysis_data)

            # Gerar prompt
            prompt = self._build_prompt(context, num_cards)

            # Chamar IA para gerar cards
            generated_data = self._call_ai_for_generation(prompt, model_config)

            # Processar e validar dados gerados
            cards = self._process_generated_cards(
                generated_data, client_id, analysis_id, context
            )

            # Criar response
            response = ProjectCardsResponse(
                cards=cards,
                total_cards=len(cards),
                source_analysis_id=analysis_id,
                generation_metadata={
                    "num_requested": num_cards,
                    "num_generated": len(cards),
                    "model_used": model_config.get("model_name", "default") if model_config else "default",
                    "client_name": context.get("client_name", "Unknown"),
                    "sector": context.get("sector", "Unknown")
                }
            )

            # Salvar cards no storage
            self._save_cards_to_storage(cards, client_id)

            logger.info(
                f"Gerados {len(cards)} cards com sucesso para cliente {client_id}")

            return response

        except Exception as e:
            logger.error(
                f"Erro ao gerar cards para cliente {client_id}: {str(e)}")
            raise

    def _get_analysis_data(self, analysis_id: str) -> Optional[Dict[str, Any]]:
        """
        Recupera dados da análise consolidada

        Args:
            analysis_id: ID da análise

        Returns:
            Dados da análise ou None se não encontrada
        """
        try:
            # Buscar na collection de resultados de orquestração
            results = self.storage.get_orchestration_results(
                filters={"final_report_id": analysis_id}
            )

            if not results:
                # Buscar diretamente por ID
                analysis = self.storage.get_analysis_result(analysis_id)
                return analysis

            return results[0] if results else None

        except Exception as e:
            logger.error(f"Erro ao recuperar análise {analysis_id}: {str(e)}")
            return None

    def _extract_analysis_context(self, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extrai contexto relevante da análise para geração de cards

        Args:
            analysis_data: Dados completos da análise

        Returns:
            Dicionário com contexto extraído
        """
        context = {
            "client_name": "Cliente",
            "sector": "Geral",
            "overall_score": 50,
            "main_issues": [],
            "priority_recommendations": [],
            "market_opportunities": []
        }

        try:
            # Extrair informações da empresa
            if "company_info" in analysis_data:
                company_info = analysis_data["company_info"]
                context["client_name"] = company_info.get("name", "Cliente")
                context["sector"] = company_info.get("sector", "Geral")

            # Extrair score geral
            if "agent_results" in analysis_data:
                scores = []
                for agent_result in analysis_data["agent_results"].values():
                    if "result_data" in agent_result and agent_result["result_data"]:
                        data = agent_result["result_data"]
                        # Tentar extrair score de diferentes estruturas
                        if "overall_score" in data:
                            scores.append(data["overall_score"])
                        elif "current_overall_score" in data:
                            scores.append(data["current_overall_score"])

                if scores:
                    context["overall_score"] = sum(scores) / len(scores)

            # Extrair problemas e recomendações
            issues = set()
            recommendations = []

            if "agent_results" in analysis_data:
                for agent_result in analysis_data["agent_results"].values():
                    if "result_data" in agent_result and agent_result["result_data"]:
                        data = agent_result["result_data"]

                        # Extrair problemas
                        if "identified_problems" in data:
                            issues.update(data["identified_problems"])
                        if "critical_issues" in data:
                            issues.update(data["critical_issues"])

                        # Extrair recomendações
                        if "technical_recommendations" in data:
                            for rec in data["technical_recommendations"]:
                                if isinstance(rec, dict) and rec.get("priority") in ["alta", "crítica"]:
                                    recommendations.append({
                                        "title": rec.get("title", ""),
                                        "priority": rec.get("priority", ""),
                                        "category": rec.get("category", "")
                                    })

            context["main_issues"] = list(issues)[:5]  # Top 5 problemas
            # Top 8 recomendações
            context["priority_recommendations"] = recommendations[:8]

        except Exception as e:
            logger.warning(f"Erro ao extrair contexto da análise: {str(e)}")

        return context

    def _build_prompt(self, context: Dict[str, Any], num_cards: int) -> str:
        """
        Constrói o prompt para geração baseado no contexto

        Args:
            context: Contexto extraído da análise
            num_cards: Número de cards a gerar

        Returns:
            Prompt formatado para a IA
        """
        # Formatar listas para o prompt
        main_issues = "; ".join(
            context["main_issues"][:3]) if context["main_issues"] else "Não identificados"

        priority_recs = []
        for rec in context["priority_recommendations"][:5]:
            if isinstance(rec, dict):
                priority_recs.append(
                    f"- {rec.get('title', 'N/A')} ({rec.get('priority', 'N/A')})")
        priority_recommendations = "\n".join(
            priority_recs) if priority_recs else "Não identificadas"

        market_opportunities = "; ".join(
            context["market_opportunities"][:3]) if context["market_opportunities"] else "A definir baseado na análise"

        return self._prompt_template.format(
            num_cards=num_cards,
            client_name=context["client_name"],
            sector=context["sector"],
            overall_score=int(context["overall_score"]),
            main_issues=main_issues,
            priority_recommendations=priority_recommendations,
            market_opportunities=market_opportunities
        )

    def _call_ai_for_generation(
        self,
        prompt: str,
        model_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Chama a IA para gerar os cards baseado no prompt

        Args:
            prompt: Prompt formatado
            model_config: Configurações do modelo

        Returns:
            Dados gerados pela IA

        Raises:
            Exception: Se houver erro na chamada da IA
        """
        try:
            # Configurações padrão
            config = {
                "model_name": "command-r-plus",
                "temperature": 0.3,
                "max_tokens": 2000
            }

            if model_config:
                config.update(model_config)

            # Aqui seria a chamada real para a IA
            # Por enquanto, retornamos dados simulados para teste
            logger.info("Chamando IA para geração de cards...")

            # Simulação de resposta da IA
            simulated_response = {
                "projects": [
                    {
                        "title": "Sistema de Monitoramento de Performance",
                        "summary": "Implementar dashboard para monitorar métricas de performance em tempo real",
                        "tags": ["Performance", "Dashboard", "Monitoramento"],
                        "priority": "alta",
                        "estimated_effort": "médio",
                        "estimated_duration": "2-3 meses",
                        "recommendations_used": ["perf_001", "ui_002"]
                    },
                    {
                        "title": "Otimização de SEO Técnico",
                        "summary": "Melhorar estrutura técnica para aumentar visibilidade nos motores de busca",
                        "tags": ["SEO", "Técnico", "Otimização"],
                        "priority": "alta",
                        "estimated_effort": "baixo",
                        "estimated_duration": "3-4 semanas",
                        "recommendations_used": ["seo_001", "tech_003"]
                    },
                    {
                        "title": "Redesign da Interface do Usuário",
                        "summary": "Modernizar interface seguindo melhores práticas de UX/UI design",
                        "tags": ["UX", "UI", "Design"],
                        "priority": "média",
                        "estimated_effort": "alto",
                        "estimated_duration": "4-6 meses",
                        "recommendations_used": ["ux_001", "design_002"]
                    }
                ]
            }

            return simulated_response

        except Exception as e:
            logger.error(f"Erro na chamada da IA: {str(e)}")
            raise

    def _process_generated_cards(
        self,
        generated_data: Dict[str, Any],
        client_id: str,
        analysis_id: str,
        context: Dict[str, Any]
    ) -> List[ProjectCard]:
        """
        Processa e valida os dados gerados pela IA

        Args:
            generated_data: Dados gerados pela IA
            client_id: ID do cliente
            analysis_id: ID da análise
            context: Contexto da análise

        Returns:
            Lista de ProjectCard validados
        """
        cards = []

        try:
            projects = generated_data.get("projects", [])

            for project_data in projects:
                try:
                    # Mapear prioridade
                    priority_map = {
                        "crítica": PriorityLevel.CRITICAL,
                        "alta": PriorityLevel.HIGH,
                        "média": PriorityLevel.MEDIUM,
                        "baixa": PriorityLevel.LOW
                    }

                    # Mapear esforço
                    effort_map = {
                        "baixo": EffortLevel.LOW,
                        "médio": EffortLevel.MEDIUM,
                        "alto": EffortLevel.HIGH,
                        "muito alto": EffortLevel.VERY_HIGH
                    }

                    # Validar tags (garantir exatamente 3)
                    tags = project_data.get("tags", [])
                    if len(tags) != 3:
                        # Ajustar para ter exatamente 3 tags
                        if len(tags) < 3:
                            tags.extend(
                                ["Projeto", "Melhoria", "Implementação"][:3-len(tags)])
                        else:
                            tags = tags[:3]

                    # Criar card
                    card = ProjectCard(
                        title=project_data.get(
                            "title", "Projeto Sem Título")[:50],
                        client=context.get("client_name", "Cliente"),
                        sector=context.get("sector", "Geral"),
                        summary=project_data.get(
                            "summary", "Descrição não disponível")[:90],
                        tags=tags,
                        client_id=client_id,
                        source_analysis_id=analysis_id,
                        priority=priority_map.get(
                            project_data.get("priority", "média").lower(),
                            PriorityLevel.MEDIUM
                        ),
                        estimated_effort=effort_map.get(
                            project_data.get(
                                "estimated_effort", "médio").lower(),
                            EffortLevel.MEDIUM
                        ),
                        estimated_duration=project_data.get(
                            "estimated_duration"),
                        recommendations_used=project_data.get(
                            "recommendations_used", [])
                    )

                    cards.append(card)

                except Exception as e:
                    logger.warning(
                        f"Erro ao processar card individual: {str(e)}")
                    continue

        except Exception as e:
            logger.error(f"Erro ao processar dados gerados: {str(e)}")

        return cards

    def _save_cards_to_storage(self, cards: List[ProjectCard], client_id: str) -> None:
        """
        Salva os cards gerados no storage

        Args:
            cards: Lista de cards para salvar
            client_id: ID do cliente
        """
        try:
            for card in cards:
                # Converter para dict para salvar
                card_data = card.model_dump()

                # Salvar usando o storage
                self.storage.save_result(
                    agent_name="cards_generator",
                    client_id=client_id,
                    result_data=card_data,
                    metadata={
                        "type": "project_card",
                        "card_id": card.card_id,
                        "source_analysis": card.source_analysis_id
                    }
                )

            logger.info(
                f"Salvos {len(cards)} cards no storage para cliente {client_id}")

        except Exception as e:
            logger.error(f"Erro ao salvar cards no storage: {str(e)}")

    def get_cards_for_client(self, client_id: str) -> List[ProjectCard]:
        """
        Recupera todos os cards de um cliente

        Args:
            client_id: ID do cliente

        Returns:
            Lista de cards do cliente
        """
        try:
            results = self.storage.get_agent_results(
                agent_name="cards_generator",
                client_id=client_id
            )

            cards = []
            for result in results:
                try:
                    card_data = result.get("result_data", {})
                    card = ProjectCard(**card_data)
                    cards.append(card)
                except Exception as e:
                    logger.warning(
                        f"Erro ao converter resultado em card: {str(e)}")
                    continue

            return sorted(cards, key=lambda x: x.created_at, reverse=True)

        except Exception as e:
            logger.error(
                f"Erro ao recuperar cards do cliente {client_id}: {str(e)}")
            return []

    def update_card_status(
        self,
        card_id: str,
        new_status: ProjectCardStatus,
        client_id: str
    ) -> bool:
        """
        Atualiza o status de um card

        Args:
            card_id: ID do card
            new_status: Novo status
            client_id: ID do cliente

        Returns:
            True se atualizado com sucesso, False caso contrário
        """
        try:
            # Recuperar cards do cliente
            cards = self.get_cards_for_client(client_id)

            # Encontrar o card
            target_card = None
            for card in cards:
                if card.card_id == card_id:
                    target_card = card
                    break

            if not target_card:
                logger.warning(
                    f"Card {card_id} não encontrado para cliente {client_id}")
                return False

            # Atualizar status
            target_card.status = new_status
            target_card.updated_at = datetime.utcnow()

            # Salvar atualização
            self.storage.save_result(
                agent_name="cards_generator",
                client_id=client_id,
                result_data=target_card.model_dump(),
                metadata={
                    "type": "project_card",
                    "card_id": card_id,
                    "source_analysis": target_card.source_analysis_id,
                    "updated": True
                }
            )

            return True

        except Exception as e:
            logger.error(
                f"Erro ao atualizar status do card {card_id}: {str(e)}")
            return False
