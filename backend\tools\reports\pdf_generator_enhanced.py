"""
Gerador de PDF AI-ENHANCED para relatórios executivos robustos

Versão aprimorada que gera relatórios executivos muito mais detalhados
e profissionais usando GPT_4_1_MINI com 15+ seções especializadas.
"""

import os
import tempfile
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List
import logging
import json
import re

from openai import OpenAI
from weasyprint import HTML, CSS
from jinja2 import Environment, FileSystemLoader, Template
from pymongo import MongoClient
import gridfs

import sys
from pathlib import Path

# Adicionar o diretório raiz do projeto ao sys.path
current_dir = Path(__file__).parent
backend_dir = current_dir.parent.parent
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

try:
    from config.settings import Settings
except ImportError:
    # Fallback para desenvolvimento local
    import os
    class Settings:
        OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
        GPT_4_1_MINI = os.getenv('GPT_4_1_MINI', 'gpt-4o-mini')
        MONGODB_ATLAS_CONNECTION_URI = os.getenv('MONGODB_ATLAS_CONNECTION_URI', 'mongodb://localhost:27017/')
        MONGODB_DATABASE_NAME = os.getenv('MONGODB_DATABASE_NAME', 'scopeai')

logger = logging.getLogger(__name__)

class EnhancedPDFGenerator:
    """Gerador APRIMORADO de relatórios executivos em PDF com IA"""
    
    def __init__(self):
        self.generator = AIReportGeneratorEnhanced()
    
    def generate_report(self, expanded_dossier: Dict[str, Any], client_name: str) -> bytes:
        """Wrapper para compatibilidade"""
        return self.generator.generate_report(expanded_dossier, client_name)

class AIReportGeneratorEnhanced:
    """Gerador APRIMORADO de relatórios executivos em PDF com IA"""
    
    def __init__(self):
        self.settings = Settings()
        
        # Validar configurações críticas
        if not self.settings.OPENAI_API_KEY:
            raise ValueError("OPENAI_API_KEY não configurada no arquivo .env")
        
        # Configurar modelo GPT com fallback
        self.gpt_model = self.settings.GPT_4_1_MINI or "gpt-4o-mini"
        
        self.openai_client = OpenAI(api_key=self.settings.OPENAI_API_KEY)
        
        # MongoDB + GridFS para armazenar PDFs
        mongo_client = MongoClient(self.settings.MONGODB_ATLAS_CONNECTION_URI)
        self.db = mongo_client[self.settings.MONGODB_DATABASE_NAME]
        self.fs = gridfs.GridFS(self.db, collection="enhanced_pdf_reports")
        
        # Templates Jinja2
        template_dir = Path(__file__).parent / "templates"
        template_dir.mkdir(exist_ok=True)
        self.env = Environment(loader=FileSystemLoader(str(template_dir)))

    def generate_report(self, expanded_dossier: Dict[str, Any], client_name: str) -> bytes:
        """
        Gera relatório executivo APRIMORADO e DETALHADO com IA
        
        Args:
            expanded_dossier: Dados do dossiê expandido (11 dimensões)
            client_name: Nome do cliente
            
        Returns:
            Bytes do PDF gerado
        """
        try:
            logger.info(f"🚀 Iniciando geração de relatório APRIMORADO para {client_name}")
            
            # 1. Extrair e estruturar dados do dossiê expandido
            structured_data = self._extract_structured_data_from_dossier(expanded_dossier, client_name)
            
            # 2. Gerar conteúdo AI com 15+ seções especializadas
            ai_content = self._generate_comprehensive_ai_content(structured_data)
            
            # 3. Renderizar HTML com template aprimorado
            html_content = self._render_enhanced_html_template(structured_data, ai_content)
            
            # 4. Converter para PDF
            pdf_data = self._generate_pdf_from_html(html_content)
            
            logger.info(f"✅ PDF APRIMORADO gerado com sucesso para {client_name}")
            return pdf_data
            
        except Exception as e:
            logger.error(f"❌ Erro ao gerar PDF APRIMORADO para {client_name}: {str(e)}")
            raise e

    def _extract_structured_data_from_dossier(self, expanded_dossier: Dict[str, Any], client_name: str) -> Dict[str, Any]:
        """Extrai e estrutura dados especificamente do dossiê expandido"""
        
        # Dados básicos
        empresa = client_name
        
        # Extrair seções do dossiê expandido
        dossie_basico = expanded_dossier.get("dossie_basico", {})
        swot_expandida = expanded_dossier.get("swot_expandida", {})
        stack_tecnologica = expanded_dossier.get("stack_tecnologica", {})
        funding_investimentos = expanded_dossier.get("funding_investimentos", {})
        presenca_digital = expanded_dossier.get("presenca_digital", {})
        parcerias_estrategicas = expanded_dossier.get("parcerias_estrategicas", {})
        modelo_negocio = expanded_dossier.get("modelo_negocio", {})
        estrategia_pricing = expanded_dossier.get("estrategia_pricing", {})
        canais_reviews = expanded_dossier.get("canais_reviews", {})
        diagnostico_tecnico = expanded_dossier.get("diagnostico_tecnico", {})
        pesquisa_mercado = expanded_dossier.get("pesquisa_mercado", {})
        
        # Extrair informações básicas do dossiê
        setor = dossie_basico.get("setor", "Indefinido")
        site = dossie_basico.get("site", "")
        location = dossie_basico.get("localizacao", "N/A")
        
        return {
            # Dados básicos
            "empresa": empresa,
            "setor": setor,
            "site": site,
            "location": location,
            
            # Dossiê completo estruturado
            "dossie_basico": dossie_basico,
            "swot_expandida": swot_expandida,
            "stack_tecnologica": stack_tecnologica,
            "funding_investimentos": funding_investimentos,
            "presenca_digital": presenca_digital,
            "parcerias_estrategicas": parcerias_estrategicas,
            "modelo_negocio": modelo_negocio,
            "estrategia_pricing": estrategia_pricing,
            "canais_reviews": canais_reviews,
            "diagnostico_tecnico": diagnostico_tecnico,
            
            # Pesquisa de mercado
            "pesquisa_mercado": pesquisa_mercado,
            
            # Dados originais para fallback
            "raw_dossier": expanded_dossier
        }

    def _extract_structured_data(self, client_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extrai e estrutura todos os dados disponíveis do cliente"""
        
        # Dados básicos
        empresa = client_data.get("name", "Empresa")
        setor = client_data.get("sector", "Indefinido")
        site = client_data.get("site", "")
        location = client_data.get("location", "N/A")
        
        # Extrair dossiê expandido (11 dimensões)
        dossie_data = {}
        pesquisa_mercado = {}
        
        for report in client_data.get("reports", []):
            if report.get("reportType") == "dossie_expandido":
                dossie_data = report.get("data", {})
            elif report.get("reportType") == "pesquisa_mercado":
                pesquisa_mercado = report.get("data", {})
        
        # Extrair dados específicos do dossiê expandido
        dossie_basico = dossie_data.get("dossie_basico", {})
        swot_expandida = dossie_data.get("swot_expandida", {})
        stack_tecnologica = dossie_data.get("stack_tecnologica", {})
        funding_investimentos = dossie_data.get("funding_investimentos", {})
        presenca_digital = dossie_data.get("presenca_digital", {})
        parcerias_estrategicas = dossie_data.get("parcerias_estrategicas", {})
        modelo_negocio = dossie_data.get("modelo_negocio", {})
        estrategia_pricing = dossie_data.get("estrategia_pricing", {})
        canais_reviews = dossie_data.get("canais_reviews", {})
        diagnostico_tecnico = dossie_data.get("diagnostico_tecnico", {})
        
        return {
            # Dados básicos
            "empresa": empresa,
            "setor": setor,
            "site": site,
            "location": location,
            
            # Dossiê completo estruturado
            "dossie_basico": dossie_basico,
            "swot_expandida": swot_expandida,
            "stack_tecnologica": stack_tecnologica,
            "funding_investimentos": funding_investimentos,
            "presenca_digital": presenca_digital,
            "parcerias_estrategicas": parcerias_estrategicas,
            "modelo_negocio": modelo_negocio,
            "estrategia_pricing": estrategia_pricing,
            "canais_reviews": canais_reviews,
            "diagnostico_tecnico": diagnostico_tecnico,
            
            # Pesquisa de mercado
            "pesquisa_mercado": pesquisa_mercado,
            
            # Dados originais para fallback
            "raw_client_data": client_data
        }

    def _generate_comprehensive_ai_content(self, data: Dict[str, Any]) -> Dict[str, str]:
        """Gera conteúdo AI ABRANGENTE com 15+ seções especializadas"""
        
        logger.info(f"🤖 Gerando conteúdo AI abrangente para {data['empresa']}")
        
        # Preparar contexto completo
        full_context = self._prepare_full_context(data)
        
        # Gerar todas as seções especializadas
        sections = {
            # SEÇÃO 1: Resumo Executivo Detalhado (800 tokens)
            "executive_summary": self._generate_executive_summary_detailed(full_context, data),
            
            # SEÇÃO 2: Perfil Corporativo Completo (700 tokens)
            "corporate_profile": self._generate_corporate_profile(full_context, data),
            
            # SEÇÃO 3: Análise de Mercado e Concorrência (800 tokens)
            "market_competition_analysis": self._generate_market_competition_analysis(full_context, data),
            
            # SEÇÃO 4: Análise SWOT Profunda (900 tokens)
            "swot_deep_analysis": self._generate_swot_deep_analysis(full_context, data),
            
            # SEÇÃO 5: Análise Financeira e Investimentos (800 tokens)
            "financial_investment_analysis": self._generate_financial_investment_analysis(full_context, data),
            
            # SEÇÃO 6: Stack Tecnológico e Inovação (700 tokens)
            "tech_innovation_analysis": self._generate_tech_innovation_analysis(full_context, data),
            
            # SEÇÃO 7: Presença Digital e Marketing (600 tokens)
            "digital_marketing_analysis": self._generate_digital_marketing_analysis(full_context, data),
            
            # SEÇÃO 8: Parcerias e Ecossistema (600 tokens)
            "partnerships_ecosystem": self._generate_partnerships_ecosystem(full_context, data),
            
            # SEÇÃO 9: Análise de Riscos Detalhada (700 tokens)
            "risk_analysis": self._generate_risk_analysis(full_context, data),
            
            # SEÇÃO 10: Oportunidades de Crescimento (700 tokens)
            "growth_opportunities": self._generate_growth_opportunities(full_context, data),
            
            # SEÇÃO 11: Roadmap Estratégico (800 tokens)
            "strategic_roadmap": self._generate_strategic_roadmap(full_context, data),
            
            # SEÇÃO 12: Due Diligence Técnico (700 tokens)
            "technical_due_diligence": self._generate_technical_due_diligence(full_context, data),
            
            # SEÇÃO 13: Recomendações Executivas (600 tokens)
            "executive_recommendations": self._generate_executive_recommendations(full_context, data),
            
            # SEÇÃO 14: Métricas e KPIs (500 tokens)
            "metrics_kpis": self._generate_metrics_kpis(full_context, data),
            
            # SEÇÃO 15: Anexos e Dados Técnicos (400 tokens)
            "technical_appendix": self._generate_technical_appendix(full_context, data)
        }
        
        logger.info(f"✅ Geradas {len(sections)} seções especializadas com sucesso")
        return sections

    def _prepare_full_context(self, data: Dict[str, Any]) -> str:
        """Prepara contexto completo e estruturado para a IA"""
        
        context_parts = []
        
        # Dados básicos
        context_parts.append(f"""
EMPRESA: {data['empresa']}
SETOR: {data['setor']}
WEBSITE: {data['site']}
LOCALIZAÇÃO: {data['location']}
""")
        
        # Dossiê básico
        if data['dossie_basico']:
            context_parts.append(f"""
DOSSIÊ BÁSICO:
{json.dumps(data['dossie_basico'], indent=2, ensure_ascii=False)[:1500]}
""")
        
        # SWOT expandida
        if data['swot_expandida']:
            context_parts.append(f"""
ANÁLISE SWOT EXPANDIDA:
{json.dumps(data['swot_expandida'], indent=2, ensure_ascii=False)[:1000]}
""")
        
        # Stack tecnológica
        if data['stack_tecnologica']:
            context_parts.append(f"""
STACK TECNOLÓGICA:
{json.dumps(data['stack_tecnologica'], indent=2, ensure_ascii=False)[:1000]}
""")
        
        # Financeiro e investimentos
        if data['funding_investimentos']:
            context_parts.append(f"""
FUNDING & INVESTIMENTOS:
{json.dumps(data['funding_investimentos'], indent=2, ensure_ascii=False)[:1000]}
""")
        
        # Presença digital
        if data['presenca_digital']:
            context_parts.append(f"""
PRESENÇA DIGITAL:
{json.dumps(data['presenca_digital'], indent=2, ensure_ascii=False)[:800]}
""")
        
        # Modelo de negócio
        if data['modelo_negocio']:
            context_parts.append(f"""
MODELO DE NEGÓCIO:
{json.dumps(data['modelo_negocio'], indent=2, ensure_ascii=False)[:800]}
""")
        
        # Pesquisa de mercado
        if data['pesquisa_mercado']:
            context_parts.append(f"""
PESQUISA DE MERCADO:
{json.dumps(data['pesquisa_mercado'], indent=2, ensure_ascii=False)[:1500]}
""")
        
        return "\n".join(context_parts)[:8000]  # Limite total de contexto

    # ========================================
    # MÉTODOS DE GERAÇÃO DE SEÇÕES AI
    # ========================================

    def _generate_executive_summary_detailed(self, context: str, data: Dict) -> str:
        """Gera resumo executivo DETALHADO e ABRANGENTE"""
        
        prompt = f"""
        Com base nos dados completos da empresa {data['empresa']}, crie um RESUMO EXECUTIVO DETALHADO e PROFISSIONAL:

        CONTEXTO COMPLETO:
        {context}

        INSTRUÇÕES:
        - Mínimo 2000 palavras
        - Tom executivo profissional e persuasivo
        - Incluir métricas e números específicos quando disponíveis
        - Destacar principais diferenciais competitivos
        - Mencionar oportunidades de mercado e crescimento
        - Incluir análise de viabilidade e potencial
        - Formato de due diligence executiva

        ESTRUTURA OBRIGATÓRIA:
        1. **Visão Geral Estratégica**
        2. **Principais Forças e Diferenciais**
        3. **Análise de Mercado** (tamanho, crescimento, posição competitiva)
        4. **Performance Financeira** (se disponível: receita, crescimento, investimentos)
        5. **Oportunidades Identificadas**
        6. **Riscos e Mitigações** (principais riscos e como mitigar)
        7. **Conclusão Estratégica** (recomendação final sobre potencial)

        Seja específico, use dados concretos e mantenha tom profissional de consultoria.
        """

        try:
            response = self.openai_client.chat.completions.create(
                model=self.gpt_model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=1200,
                temperature=0.6
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"Erro ao gerar resumo executivo detalhado: {e}")
            return f"**Resumo Executivo para {data['empresa']}** - Análise detalhada em processamento."

    def _generate_corporate_profile(self, context: str, data: Dict) -> str:
        """Gera perfil corporativo completo e detalhado"""
        
        prompt = f"""
        Baseado nos dados disponíveis, crie um PERFIL CORPORATIVO COMPLETO para {data['empresa']}:

        CONTEXTO:
        {context}

        DIRETRIZES:
        - Mínimo 2000 palavras
        - Informações factuais e verificáveis
        - Incluir histórico, produtos/serviços, mercado-alvo
        - Mencionar estrutura organizacional se disponível
        - Destacar conquistas e marcos importantes
        - Incluir informações sobre liderança/fundadores

        ESTRUTURA:
        1. **História e Fundação** (origem, evolução, marcos)
        2. **Produtos e Serviços** (portfólio, principais ofertas)
        3. **Mercado e Clientes** (segmentos atendidos, base de clientes)
        4. **Estrutura Organizacional** (tamanho da equipe, departamentos)
        5. **Conquistas e Reconhecimentos** (prêmios, certificações, media)
        6. **Presença Geográfica** (localização, expansão, mercados)

        Use dados específicos e seja factual.
        """

        try:
            response = self.openai_client.chat.completions.create(
                model=self.gpt_model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=1000,
                temperature=0.5
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"Erro ao gerar perfil corporativo: {e}")
            return f"**Perfil Corporativo de {data['empresa']}** - Análise em processamento."

    def _generate_market_competition_analysis(self, context: str, data: Dict) -> str:
        """Gera análise detalhada de mercado e concorrência"""
        
        prompt = f"""
        Faça uma ANÁLISE DETALHADA DE MERCADO E CONCORRÊNCIA para {data['empresa']} no setor {data['setor']}:

        CONTEXTO:
        {context}

        INSTRUÇÕES:
        - Mínimo 2000 palavras
        - Análise profunda do mercado atual
        - Identificar principais competidores e posicionamento
        - Incluir tendências e projeções de crescimento
        - Análise de participação de mercado
        - Fatores críticos de sucesso no setor

        ESTRUTURA:
        1. **Panorama do Mercado** (tamanho, crescimento, características)
        2. **Análise Competitiva** (principais players, forças/fraquezas)
        3. **Posicionamento da Empresa** (diferenciação, vantagens)
        4. **Tendências e Drivers** (tecnologia, regulação, comportamento)
        5. **Barreiras de Entrada** (capital, tecnologia, regulação)
        6. **Oportunidades de Mercado** (nichos, expansão, inovação)
        7. **Ameaças Competitivas** (disrupção, novos entrantes)

        Seja analítico e use dados de mercado quando disponíveis.
        """

        try:
            response = self.openai_client.chat.completions.create(
                model=self.gpt_model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=1200,
                temperature=0.7
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"Erro ao gerar análise de mercado: {e}")
            return f"**Análise de Mercado - {data['setor']}** - Análise em processamento."

    def _generate_swot_deep_analysis(self, context: str, data: Dict) -> str:
        """Gera análise SWOT profunda e estratégica"""
        
        prompt = f"""
        Realize uma ANÁLISE SWOT PROFUNDA E ESTRATÉGICA para {data['empresa']}:

        CONTEXTO:
        {context}

        DIRETRIZES:
        - Mínimo 2000 palavras
        - Análise detalhada de cada quadrante SWOT
        - Interconexões entre forças/oportunidades
        - Estratégias específicas para cada área
        - Priorização de fatores por impacto

        ESTRUTURA:
        1. **FORÇAS (Strengths)** 
           - 5-7 forças principais com detalhamento
           - Como cada força cria vantagem competitiva
           
        2. **FRAQUEZAS (Weaknesses)**
           - Impacto de cada fraqueza no negócio
           - Planos de mitigação
           
        3. **OPORTUNIDADES (Opportunities)**
           - Viabilidade e potencial de cada uma
           - Recursos necessários para capturar
           
        4. **AMEAÇAS (Threats)**
           - Probabilidade e impacto
           - Estratégias de defesa
           
        5. **ESTRATÉGIAS CRUZADAS**
           - SO: Como usar forças para capturar oportunidades
           - WO: Como superar fraquezas via oportunidades
           - ST: Como usar forças para mitigar ameaças
           - WT: Como minimizar fraquezas e ameaças

        Seja específico e estratégico.
        """

        try:
            response = self.openai_client.chat.completions.create(
                model=self.gpt_model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=1300,
                temperature=0.6
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"Erro ao gerar análise SWOT profunda: {e}")
            return f"**Análise SWOT Estratégica** - Análise em processamento."

    def _generate_financial_investment_analysis(self, context: str, data: Dict) -> str:
        """Gera análise financeira e de investimentos detalhada"""
        
        prompt = f"""
        Faça uma ANÁLISE FINANCEIRA E DE INVESTIMENTOS para {data['empresa']}:

        CONTEXTO:
        {context}

        INSTRUÇÕES:
        - Mínimo 2000 palavras
        - Análise baseada em dados disponíveis
        - Incluir múltiplos de avaliação quando possível
        - Análise de estrutura de capital
        - Perspectivas de crescimento financeiro
        - Comparação com benchmarks do setor

        ESTRUTURA:
        1. **Performance Financeira Atual**
           - Receita, crescimento, margens (se disponível)
           - Análise de rentabilidade
           
        2. **Estrutura de Capital**
           - Funding recebido, investidores
           - Estrutura acionária
           
        3. **Análise de Investimentos**
           - Histórico de rodadas de financiamento
           - Uso de capital e ROI
           
        4. **Avaliação e Múltiplos**
           - Múltiplos de receita/EBITDA do setor
           - Comparação com competitors
           
        5. **Projeções e Potencial**
           - Crescimento esperado
           - Necessidades futuras de capital
           
        6. **Análise de Risco Financeiro**
           - Estrutura de custos
           - Sensibilidade a cenários
           
        7. **Recomendações de Investimento**
           - Perfil de risco-retorno
           - Estratégias de saída

        Use dados específicos quando disponíveis.
        """

        try:
            response = self.openai_client.chat.completions.create(
                model=self.gpt_model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=1200,
                temperature=0.5
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"Erro ao gerar análise financeira: {e}")
            return f"**Análise Financeira e Investimentos** - Análise em processamento."

    def _generate_tech_innovation_analysis(self, context: str, data: Dict) -> str:
        """Gera análise de stack tecnológico e inovação"""
        
        prompt = f"""
        Faça uma ANÁLISE DETALHADA DE TECNOLOGIA E INOVAÇÃO para {data['empresa']}:

        CONTEXTO:
        {context}

        DIRETRIZES:
        - Mínimo 2000 palavras
        - Análise do stack tecnológico atual
        - Avaliação de capacidades de inovação
        - Comparação com best practices do setor
        - Identificação de gaps e oportunidades

        ESTRUTURA:
        1. **Stack Tecnológico Atual**
           - Tecnologias utilizadas (frontend, backend, infra)
           - Arquitetura e escalabilidade
           
        2. **Capacidades de Desenvolvimento**
           - Maturidade técnica da equipe
           - Processos de desenvolvimento
           
        3. **Inovação e R&D**
           - Investimentos em pesquisa
           - Projetos inovadores
           
        4. **Infraestrutura e Operações**
           - Cloud, DevOps, segurança
           - Monitoramento e performance
           
        5. **Análise Competitiva Técnica**
           - Comparação com líderes do setor
           - Gaps tecnológicos
           
        6. **Roadmap Tecnológico**
           - Tendências relevantes
           - Recomendações de evolução

        Seja técnico mas acessível para executivos.
        """

        try:
            response = self.openai_client.chat.completions.create(
                model=self.gpt_model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=1000,
                temperature=0.6
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"Erro ao gerar análise tech: {e}")
            return f"**Análise Tecnológica e Inovação** - Análise em processamento."

    def _generate_digital_marketing_analysis(self, context: str, data: Dict) -> str:
        """Gera análise de presença digital e marketing"""
        
        prompt = f"""
        Analise a PRESENÇA DIGITAL E ESTRATÉGIA DE MARKETING de {data['empresa']}:

        CONTEXTO:
        {context}

        INSTRUÇÕES:
        - Mínimo 2000 palavras
        - Análise da presença digital atual
        - Performance em canais digitais
        - Estratégias de marketing e branding
        - Comparação com competitors

        ESTRUTURA:
        1. **Presença Digital Atual**
           - Website, SEO, redes sociais
           - Qualidade e performance
           
        2. **Estratégia de Conteúdo**
           - Blog, materiais educativos
           - Engajamento da audiência
           
        3. **Marketing Performance**
           - Métricas de tráfego e conversão
           - ROI de campanhas
           
        4. **Branding e Posicionamento**
           - Identidade visual e messaging
           - Percepção de marca
           
        5. **Análise Competitiva Digital**
           - Benchmarking com competitors
           - Gaps e oportunidades
           
        6. **Recomendações Digitais**
           - Áreas de melhoria
           - Investimentos prioritários

        Foque em dados e métricas quando disponíveis.
        """

        try:
            response = self.openai_client.chat.completions.create(
                model=self.gpt_model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=900,
                temperature=0.6
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"Erro ao gerar análise digital: {e}")
            return f"**Análise Digital e Marketing** - Análise em processamento."

    def _generate_partnerships_ecosystem(self, context: str, data: Dict) -> str:
        """Gera análise de parcerias e ecossistema"""
        
        prompt = f"""
        Analise o ECOSSISTEMA DE PARCERIAS de {data['empresa']}:

        CONTEXTO:
        {context}

        DIRETRIZES:
        - Mínimo 2000 palavras
        - Mapeamento do ecossistema atual
        - Valor gerado pelas parcerias
        - Oportunidades de expansão
        - Estratégias de relacionamento

        ESTRUTURA:
        1. **Parcerias Atuais**
           - Parceiros estratégicos principais
           - Tipos de parcerias (tecnológica, comercial, etc.)
           
        2. **Valor das Parcerias**
           - Benefícios mútuos
           - Impacto no negócio
           
        3. **Ecossistema de Fornecedores**
           - Dependências críticas
           - Qualidade dos relacionamentos
           
        4. **Canais de Distribuição**
           - Parceiros comerciais
           - Estratégias go-to-market
           
        5. **Oportunidades de Expansão**
           - Novos parceiros potenciais
           - Mercados não explorados
           
        6. **Gestão de Relacionamentos**
           - Processos de partnership
           - KPIs e governança

        Foque no valor estratégico das parcerias.
        """

        try:
            response = self.openai_client.chat.completions.create(
                model=self.gpt_model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=900,
                temperature=0.6
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"Erro ao gerar análise de parcerias: {e}")
            return f"**Ecossistema de Parcerias** - Análise em processamento."

    def _generate_risk_analysis(self, context: str, data: Dict) -> str:
        """Gera análise detalhada de riscos"""
        
        prompt = f"""
        Faça uma ANÁLISE DETALHADA DE RISCOS para {data['empresa']}:

        CONTEXTO:
        {context}

        INSTRUÇÕES:
        - Mínimo 2000 palavras
        - Identificação de riscos por categoria
        - Avaliação de probabilidade e impacto
        - Estratégias de mitigação
        - Monitoramento e controles

        ESTRUTURA:
        1. **Riscos de Mercado**
           - Mudanças regulatórias
           - Volatilidade econômica
           - Comportamento do consumidor
           
        2. **Riscos Operacionais**
           - Dependências de equipe
           - Processos críticos
           - Falhas sistêmicas
           
        3. **Riscos Tecnológicos**
           - Obsolescência tech
           - Segurança cibernética
           - Escalabilidade
           
        4. **Riscos Financeiros**
           - Fluxo de caixa
           - Concentração de clientes
           - Acesso a capital
           
        5. **Riscos Competitivos**
           - Novos entrantes
           - Disrupção tecnológica
           - Guerra de preços
           
        6. **Estratégias de Mitigação**
           - Controles preventivos
           - Planos de contingência
           - Diversificação

        Priorize por impacto e probabilidade.
        """

        try:
            response = self.openai_client.chat.completions.create(
                model=self.gpt_model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=1000,
                temperature=0.5
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"Erro ao gerar análise de riscos: {e}")
            return f"**Análise de Riscos** - Análise em processamento."

    def _generate_growth_opportunities(self, context: str, data: Dict) -> str:
        """Gera análise de oportunidades de crescimento"""
        
        prompt = f"""
        Identifique OPORTUNIDADES DE CRESCIMENTO para {data['empresa']}:

        CONTEXTO:
        {context}

        DIRETRIZES:
        - Mínimo 2000 palavras
        - Oportunidades concretas e acionáveis
        - Análise de viabilidade e retorno
        - Recursos necessários
        - Timeline de implementação

        ESTRUTURA:
        1. **Expansão de Mercado**
           - Novos segmentos de clientes
           - Expansão geográfica
           - Canais de distribuição
           
        2. **Desenvolvimento de Produtos**
           - Novos produtos/serviços
           - Melhorias no portfólio
           - Inovação tecnológica
           
        3. **Aquisições e Parcerias**
           - Targets de aquisição
           - Joint ventures
           - Integrações estratégicas
           
        4. **Otimização Operacional**
           - Eficiências de processo
           - Automação
           - Redução de custos
           
        5. **Transformação Digital**
           - Digitização de processos
           - Novos modelos de negócio
           - Plataformas digitais
           
        6. **Priorização e Roadmap**
           - Matriz impacto x esforço
           - Timeline de implementação
           - Quick wins vs projetos longos

        Seja específico sobre ROI e recursos.
        """

        try:
            response = self.openai_client.chat.completions.create(
                model=self.gpt_model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=1000,
                temperature=0.7
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"Erro ao gerar análise de oportunidades: {e}")
            return f"**Oportunidades de Crescimento** - Análise em processamento."

    def _generate_strategic_roadmap(self, context: str, data: Dict) -> str:
        """Gera roadmap estratégico detalhado"""
        
        prompt = f"""
        Desenvolva um ROADMAP ESTRATÉGICO para {data['empresa']}:

        CONTEXTO:
        {context}

        INSTRUÇÕES:
        - Mínimo 2000 palavras
        - Roadmap para 12-24 meses
        - Marcos e entregáveis específicos
        - Recursos e orçamento necessários
        - KPIs de acompanhamento

        ESTRUTURA:
        1. **Visão Estratégica**
           - Objetivos de curto e médio prazo
           - Visão de futuro (24 meses)
           
        2. **Roadmap Trimestral**
           - Q1: Iniciativas prioritárias
           - Q2: Expansão e consolidação
           - Q3: Otimização e crescimento
           - Q4: Preparação para próximo ciclo
           
        3. **Marcos Críticos**
           - Entregáveis principais
           - Deadlines importantes
           - Gates de aprovação
           
        4. **Recursos e Investimentos**
           - Equipe necessária
           - Orçamento por iniciativa
           - ROI esperado
           
        5. **Riscos e Contingências**
           - Cenários alternativos
           - Planos de mitigação
           
        6. **KPIs e Métricas**
           - Indicadores de sucesso
           - Frequência de acompanhamento

        Seja específico em prazos e recursos.
        """

        try:
            response = self.openai_client.chat.completions.create(
                model=self.gpt_model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=1200,
                temperature=0.5
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"Erro ao gerar roadmap estratégico: {e}")
            return f"**Roadmap Estratégico** - Análise em processamento."

    def _generate_technical_due_diligence(self, context: str, data: Dict) -> str:
        """Gera due diligence técnico detalhado"""
        
        prompt = f"""
        Realize um DUE DILIGENCE TÉCNICO para {data['empresa']}:

        CONTEXTO:
        {context}

        DIRETRIZES:
        - Mínimo 2000 palavras
        - Avaliação técnica profunda
        - Identificação de gaps críticos
        - Recomendações de melhoria
        - Assessment de maturidade

        ESTRUTURA:
        1. **Arquitetura e Infraestrutura**
           - Escalabilidade atual
           - Pontos de falha
           - Modernização necessária
           
        2. **Código e Desenvolvimento**
           - Qualidade do código
           - Documentação técnica
           - Processos de QA
           
        3. **Segurança e Compliance**
           - Vulnerabilidades identificadas
           - Conformidade regulatória
           - Políticas de segurança
           
        4. **Performance e Monitoramento**
           - Métricas de performance
           - Sistemas de alertas
           - Capacidade de observabilidade
           
        5. **Equipe e Processos**
           - Skills gaps
           - Metodologias ágeis
           - DevOps maturity
           
        6. **Recomendações Técnicas**
           - Prioridades de investimento
           - Timeline de melhorias
           - ROI técnico

        Foque em riscos e oportunidades técnicas.
        """

        try:
            response = self.openai_client.chat.completions.create(
                model=self.gpt_model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=1000,
                temperature=0.4
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"Erro ao gerar due diligence técnico: {e}")
            return f"**Due Diligence Técnico** - Análise em processamento."

    def _generate_executive_recommendations(self, context: str, data: Dict) -> str:
        """Gera recomendações executivas estratégicas"""
        
        prompt = f"""
        Formule RECOMENDAÇÕES EXECUTIVAS ESTRATÉGICAS para {data['empresa']}:

        CONTEXTO:
        {context}

        INSTRUÇÕES:
        - Mínimo 2000 palavras
        - Recomendações acionáveis e específicas
        - Priorização por impacto e urgência
        - Timeline de implementação
        - Recursos necessários

        ESTRUTURA:
        1. **Recomendações Prioritárias (Top 3)**
           - Ação específica
           - Justificativa
           - ROI esperado
           - Timeline
           
        2. **Melhorias Operacionais**
           - Eficiência de processos
           - Redução de custos
           - Qualidade de entrega
           
        3. **Investimentos Estratégicos**
           - Tecnologia
           - Mercado
           - Equipe
           
        4. **Gestão de Riscos**
           - Controles preventivos
           - Diversificação
           - Contingências
           
        5. **Cronograma de Implementação**
           - Quick wins (30-90 dias)
           - Médio prazo (6-12 meses)
           - Longo prazo (12+ meses)
           
        6. **Next Steps**
           - Próximas ações imediatas
           - Responsabilidades
           - Métricas de sucesso

        Seja direto e executivo nas recomendações.
        """

        try:
            response = self.openai_client.chat.completions.create(
                model=self.gpt_model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=900,
                temperature=0.4
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"Erro ao gerar recomendações executivas: {e}")
            return f"**Recomendações Executivas** - Análise em processamento."

    def _generate_metrics_kpis(self, context: str, data: Dict) -> str:
        """Gera dashboard de métricas e KPIs"""
        
        prompt = f"""
        Crie um DASHBOARD DE MÉTRICAS E KPIs para {data['empresa']}:

        CONTEXTO:
        {context}

        DIRETRIZES:
        - Mínimo 2000 palavras
        - KPIs específicos e mensuráveis
        - Benchmarks do setor quando possível
        - Frequência de acompanhamento
        - Targets realistas

        ESTRUTURA:
        1. **KPIs Financeiros**
           - Receita e crescimento
           - Margens e rentabilidade
           - Eficiência de capital
           
        2. **KPIs Operacionais**
           - Produtividade
           - Qualidade
           - Eficiência de processos
           
        3. **KPIs de Mercado**
           - Market share
           - Satisfação do cliente
           - Retenção e churn
           
        4. **KPIs de Tecnologia**
           - Performance sistemas
           - Disponibilidade
           - Time to market
           
        5. **KPIs de Equipe**
           - Produtividade
           - Satisfação
           - Retenção de talentos
           
        6. **Dashboard e Monitoramento**
           - Frequência de revisão
           - Responsáveis
           - Alertas automáticos

        Foque em métricas acionáveis.
        """

        try:
            response = self.openai_client.chat.completions.create(
                model=self.gpt_model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=750,
                temperature=0.4
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"Erro ao gerar métricas e KPIs: {e}")
            return f"**Métricas e KPIs** - Análise em processamento."

    def _generate_technical_appendix(self, context: str, data: Dict) -> str:
        """Gera anexo técnico com dados específicos"""
        
        prompt = f"""
        Compile um ANEXO TÉCNICO com dados específicos de {data['empresa']}:

        CONTEXTO:
        {context}

        INSTRUÇÕES:
        - Mínimo 2000 palavras
        - Dados técnicos e específicos
        - Tabelas e listas estruturadas
        - Informações de referência
        - Fontes e metodologia

        ESTRUTURA:
        1. **Dados Técnicos**
           - Especificações de sistema
           - Arquitetura atual
           - Integrações
           
        2. **Métricas de Performance**
           - Benchmarks atuais
           - Histórico de performance
           - Comparação com setor
           
        3. **Informações Financeiras**
           - Múltiplos de avaliação
           - Comparáveis de mercado
           - Projeções numéricas
           
        4. **Dados de Mercado**
           - Tamanho de mercado
           - Taxa de crescimento
           - Segmentação
           
        5. **Metodologia**
           - Fontes de dados
           - Premissas utilizadas
           - Limitações da análise
           
        6. **Glossário**
           - Termos técnicos
           - Definições importantes
           - Siglas utilizadas

        Mantenha formato de referência técnica.
        """

        try:
            response = self.openai_client.chat.completions.create(
                model=self.gpt_model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=600,
                temperature=0.3
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"Erro ao gerar anexo técnico: {e}")
            return f"**Anexo Técnico** - Dados em processamento."

    # ========================================
    # TEMPLATE HTML APRIMORADO
    # ========================================

    def _render_enhanced_html_template(self, data: Dict, ai_content: Dict) -> str:
        """Renderiza template HTML APRIMORADO e PROFISSIONAL"""
        
        current_date = datetime.now().strftime("%d/%m/%Y")
        
        # Template HTML muito mais robusto e detalhado
        html_template = f"""
        <!DOCTYPE html>
        <html lang="pt-BR">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Relatório Executivo APRIMORADO - {data['empresa']}</title>
            <style>
                @page {{
                    size: A4;
                    margin: 1.5cm;
                    @top-right {{
                        content: "{data['empresa']} | Pág. " counter(page);
                        font-size: 9px;
                        color: #666;
                    }}
                }}
                
                body {{
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    line-height: 1.7;
                    color: #2d3748;
                    margin: 0;
                    padding: 0;
                    font-size: 12px;
                }}
                
                .cover-page {{
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 50px 30px;
                    text-align: center;
                    border-radius: 12px;
                    margin-bottom: 40px;
                    page-break-after: always;
                }}
                
                .cover-page h1 {{
                    margin: 0;
                    font-size: 36px;
                    font-weight: 300;
                    margin-bottom: 20px;
                }}
                
                .cover-page .subtitle {{
                    font-size: 18px;
                    opacity: 0.9;
                    margin: 20px 0;
                }}
                
                .cover-page .meta {{
                    font-size: 14px;
                    margin-top: 40px;
                    opacity: 0.8;
                }}
                
                .executive-summary {{
                    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
                    padding: 30px;
                    border-radius: 12px;
                    border-left: 5px solid #3182ce;
                    margin-bottom: 40px;
                    page-break-inside: avoid;
                }}
                
                .section {{
                    margin-bottom: 40px;
                    page-break-inside: avoid;
                }}
                
                .section-title {{
                    font-size: 24px;
                    font-weight: 700;
                    color: #2b6cb0;
                    border-bottom: 3px solid #3182ce;
                    padding-bottom: 12px;
                    margin-bottom: 25px;
                    display: flex;
                    align-items: center;
                }}
                
                .section-icon {{
                    margin-right: 10px;
                    font-size: 28px;
                }}
                
                .section-content {{
                    font-size: 13px;
                    line-height: 1.8;
                    text-align: justify;
                }}
                
                .grid-2 {{
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 35px;
                    margin-bottom: 30px;
                }}
                
                .grid-3 {{
                    display: grid;
                    grid-template-columns: 1fr 1fr 1fr;
                    gap: 25px;
                    margin-bottom: 30px;
                }}
                
                .card {{
                    background: #f8fafc;
                    padding: 25px;
                    border-radius: 10px;
                    border-left: 4px solid #4299e1;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }}
                
                .card-title {{
                    font-size: 16px;
                    font-weight: 600;
                    color: #2b6cb0;
                    margin-bottom: 15px;
                }}
                
                .highlight-box {{
                    background: linear-gradient(135deg, #e6f3ff 0%, #cce7ff 100%);
                    border: 1px solid #3182ce;
                    border-radius: 10px;
                    padding: 25px;
                    margin: 25px 0;
                }}
                
                .table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin: 20px 0;
                    background: white;
                    border-radius: 8px;
                    overflow: hidden;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }}
                
                .table th {{
                    background: #3182ce;
                    color: white;
                    padding: 12px;
                    text-align: left;
                    font-weight: 600;
                }}
                
                .table td {{
                    padding: 12px;
                    border-bottom: 1px solid #e2e8f0;
                }}
                
                .table tr:nth-child(even) {{
                    background: #f7fafc;
                }}
                
                .metric-card {{
                    background: white;
                    border: 2px solid #e2e8f0;
                    border-radius: 10px;
                    padding: 20px;
                    text-align: center;
                    margin: 10px 0;
                }}
                
                .metric-value {{
                    font-size: 24px;
                    font-weight: 700;
                    color: #2b6cb0;
                }}
                
                .metric-label {{
                    font-size: 12px;
                    color: #718096;
                    text-transform: uppercase;
                    font-weight: 600;
                    margin-top: 5px;
                }}
                
                .footer {{
                    margin-top: 50px;
                    padding: 25px;
                    background: #2d3748;
                    color: white;
                    border-radius: 10px;
                    text-align: center;
                    font-size: 11px;
                }}
                
                .disclaimer {{
                    font-style: italic;
                    font-size: 10px;
                    color: #a0aec0;
                    margin-top: 15px;
                    padding: 15px;
                    background: #1a202c;
                    border-radius: 5px;
                }}
                
                ul {{
                    padding-left: 20px;
                }}
                
                li {{
                    margin-bottom: 10px;
                }}
                
                .toc {{
                    background: #f7fafc;
                    padding: 30px;
                    border-radius: 10px;
                    margin-bottom: 40px;
                    page-break-after: always;
                }}
                
                .toc-title {{
                    font-size: 24px;
                    font-weight: 700;
                    color: #2b6cb0;
                    margin-bottom: 20px;
                }}
                
                .toc-item {{
                    padding: 8px 0;
                    border-bottom: 1px dotted #cbd5e0;
                    display: flex;
                    justify-content: space-between;
                }}
                
                @media print {{
                    .grid-2, .grid-3 {{
                        grid-template-columns: 1fr;
                    }}
                }}
            </style>
        </head>
        <body>
            <!-- CAPA PROFISSIONAL -->
            <div class="cover-page">
                <h1>{data['empresa']}</h1>
                <div class="subtitle">RELATÓRIO EXECUTIVO APRIMORADO</div>
                <div class="subtitle">Análise Estratégica e Due Diligence Completa</div>
                <div class="meta">
                    <p><strong>Setor:</strong> {data['setor']}</p>
                    <p><strong>Website:</strong> {data['site'] or 'N/A'}</p>
                    <p><strong>Localização:</strong> {data['location']}</p>
                    <p><strong>Data do Relatório:</strong> {current_date}</p>
                    <p style="margin-top: 30px; font-size: 12px;">
                        Relatório gerado pelo ScopeAI<br>
                        Análise baseada em IA e dados de mercado atualizados
                    </p>
                </div>
            </div>
            
            <!-- ÍNDICE -->
            <div class="toc">
                <div class="toc-title">📋 Índice</div>
                <div class="toc-item"><span>1. Resumo Executivo Detalhado</span><span>3</span></div>
                <div class="toc-item"><span>2. Perfil Corporativo Completo</span><span>4</span></div>
                <div class="toc-item"><span>3. Análise de Mercado e Concorrência</span><span>5</span></div>
                <div class="toc-item"><span>4. Análise SWOT Profunda</span><span>6</span></div>
                <div class="toc-item"><span>5. Análise Financeira e Investimentos</span><span>7</span></div>
                <div class="toc-item"><span>6. Stack Tecnológico e Inovação</span><span>8</span></div>
                <div class="toc-item"><span>7. Presença Digital e Marketing</span><span>9</span></div>
                <div class="toc-item"><span>8. Parcerias e Ecossistema</span><span>10</span></div>
                <div class="toc-item"><span>9. Análise de Riscos Detalhada</span><span>11</span></div>
                <div class="toc-item"><span>10. Oportunidades de Crescimento</span><span>12</span></div>
                <div class="toc-item"><span>11. Roadmap Estratégico</span><span>13</span></div>
                <div class="toc-item"><span>12. Due Diligence Técnico</span><span>14</span></div>
                <div class="toc-item"><span>13. Recomendações Executivas</span><span>15</span></div>
                <div class="toc-item"><span>14. Métricas e KPIs</span><span>16</span></div>
                <div class="toc-item"><span>15. Anexos e Dados Técnicos</span><span>17</span></div>
            </div>
            
            <!-- RESUMO EXECUTIVO -->
            <div class="executive-summary">
                <h2 class="section-title">
                    <span class="section-icon">📊</span>
                    Resumo Executivo Detalhado
                </h2>
                <div class="section-content">
                    {ai_content.get('executive_summary', 'Análise em processamento...')}
                </div>
            </div>
            
            <!-- SEÇÕES PRINCIPAIS EM GRID -->
            <div class="grid-2">
                <!-- Perfil Corporativo -->
                <div class="section">
                    <h2 class="section-title">
                        <span class="section-icon">🏢</span>
                        Perfil Corporativo
                    </h2>
                    <div class="section-content">
                        {ai_content.get('corporate_profile', 'Análise em processamento...')}
                    </div>
                </div>
                
                <!-- Análise de Mercado -->
                <div class="section">
                    <h2 class="section-title">
                        <span class="section-icon">📈</span>
                        Mercado e Concorrência
                    </h2>
                    <div class="section-content">
                        {ai_content.get('market_competition_analysis', 'Análise em processamento...')}
                    </div>
                </div>
            </div>
            
            <!-- SWOT PROFUNDA (DESTAQUE) -->
            <div class="section">
                <h2 class="section-title">
                    <span class="section-icon">⚖️</span>
                    Análise SWOT Profunda
                </h2>
                <div class="highlight-box">
                    <div class="section-content">
                        {ai_content.get('swot_deep_analysis', 'Análise em processamento...')}
                    </div>
                </div>
            </div>
            
            <!-- ANÁLISES ESPECIALIZADAS -->
            <div class="grid-2">
                <!-- Financeiro -->
                <div class="section">
                    <h2 class="section-title">
                        <span class="section-icon">💰</span>
                        Análise Financeira
                    </h2>
                    <div class="section-content">
                        {ai_content.get('financial_investment_analysis', 'Análise em processamento...')}
                    </div>
                </div>
                
                <!-- Tecnologia -->
                <div class="section">
                    <h2 class="section-title">
                        <span class="section-icon">🔧</span>
                        Tecnologia e Inovação
                    </h2>
                    <div class="section-content">
                        {ai_content.get('tech_innovation_analysis', 'Análise em processamento...')}
                    </div>
                </div>
            </div>
            
            <div class="grid-2">
                <!-- Digital -->
                <div class="section">
                    <h2 class="section-title">
                        <span class="section-icon">🌐</span>
                        Presença Digital
                    </h2>
                    <div class="section-content">
                        {ai_content.get('digital_marketing_analysis', 'Análise em processamento...')}
                    </div>
                </div>
                
                <!-- Parcerias -->
                <div class="section">
                    <h2 class="section-title">
                        <span class="section-icon">🤝</span>
                        Ecossistema de Parcerias
                    </h2>
                    <div class="section-content">
                        {ai_content.get('partnerships_ecosystem', 'Análise em processamento...')}
                    </div>
                </div>
            </div>
            
            <!-- RISCOS E OPORTUNIDADES -->
            <div class="grid-2">
                <!-- Riscos -->
                <div class="section">
                    <h2 class="section-title">
                        <span class="section-icon">⚠️</span>
                        Análise de Riscos
                    </h2>
                    <div class="section-content">
                        {ai_content.get('risk_analysis', 'Análise em processamento...')}
                    </div>
                </div>
                
                <!-- Oportunidades -->
                <div class="section">
                    <h2 class="section-title">
                        <span class="section-icon">🚀</span>
                        Oportunidades de Crescimento
                    </h2>
                    <div class="section-content">
                        {ai_content.get('growth_opportunities', 'Análise em processamento...')}
                    </div>
                </div>
            </div>
            
            <!-- ROADMAP ESTRATÉGICO (DESTAQUE) -->
            <div class="section">
                <h2 class="section-title">
                    <span class="section-icon">🗺️</span>
                    Roadmap Estratégico
                </h2>
                <div class="highlight-box">
                    <div class="section-content">
                        {ai_content.get('strategic_roadmap', 'Análise em processamento...')}
                    </div>
                </div>
            </div>
            
            <!-- DUE DILIGENCE E RECOMENDAÇÕES -->
            <div class="grid-2">
                <!-- Due Diligence -->
                <div class="section">
                    <h2 class="section-title">
                        <span class="section-icon">🔍</span>
                        Due Diligence Técnico
                    </h2>
                    <div class="section-content">
                        {ai_content.get('technical_due_diligence', 'Análise em processamento...')}
                    </div>
                </div>
                
                <!-- Recomendações -->
                <div class="section">
                    <h2 class="section-title">
                        <span class="section-icon">📋</span>
                        Recomendações Executivas
                    </h2>
                    <div class="section-content">
                        {ai_content.get('executive_recommendations', 'Análise em processamento...')}
                    </div>
                </div>
            </div>
            
            <!-- MÉTRICAS E ANEXOS -->
            <div class="grid-2">
                <!-- KPIs -->
                <div class="section">
                    <h2 class="section-title">
                        <span class="section-icon">📊</span>
                        Métricas e KPIs
                    </h2>
                    <div class="section-content">
                        {ai_content.get('metrics_kpis', 'Análise em processamento...')}
                    </div>
                </div>
                
                <!-- Anexos -->
                <div class="section">
                    <h2 class="section-title">
                        <span class="section-icon">📎</span>
                        Anexos Técnicos
                    </h2>
                    <div class="section-content">
                        {ai_content.get('technical_appendix', 'Análise em processamento...')}
                    </div>
                </div>
            </div>
            
            <!-- FOOTER PROFISSIONAL -->
            <div class="footer">
                <p><strong>ScopeAI - Relatório Executivo Aprimorado</strong></p>
                <p>Gerado em {current_date} | {data['empresa']}</p>
                <div class="disclaimer">
                    <p><strong>Disclaimer:</strong> Este relatório foi gerado por inteligência artificial baseada em dados públicos disponíveis e análises de mercado. 
                    As informações apresentadas são para fins informativos e não constituem aconselhamento de investimento. 
                    Recomenda-se verificação independente de dados críticos para decisões de negócio.</p>
                    <p><strong>Confidencial:</strong> Este documento contém informações confidenciais e deve ser tratado com sigilo apropriado.</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        return html_template

    # ========================================
    # MÉTODOS DE GERAÇÃO E ARMAZENAMENTO PDF
    # ========================================

    def _generate_pdf_from_html(self, html_content: str) -> bytes:
        """Converte HTML para PDF usando WeasyPrint"""
        try:
            # Gerar PDF com WeasyPrint
            pdf_file = HTML(string=html_content, base_url=Path(__file__).parent)
            pdf_bytes = pdf_file.write_pdf()
            
            logger.info(f"📄 PDF APRIMORADO gerado com sucesso ({len(pdf_bytes)} bytes)")
            return pdf_bytes
            
        except Exception as e:
            logger.error(f"❌ Erro ao gerar PDF: {str(e)}")
            raise

    def _save_pdf_to_gridfs(self, pdf_data: bytes, client_name: str, client_id: str) -> str:
        """Salva PDF no GridFS do MongoDB"""
        try:
            filename = f"relatorio_executivo_aprimorado_{client_name.replace(' ', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            
            file_id = self.fs.put(
                pdf_data,
                filename=filename,
                content_type="application/pdf",
                metadata={{
                    "client_id": client_id,
                    "client_name": client_name,
                    "report_type": "enhanced_executive_report",
                    "generated_at": datetime.utcnow(),
                    "version": "2.0_enhanced"
                }}
            )
            
            logger.info(f"💾 PDF APRIMORADO salvo no GridFS - ID: {file_id}")
            return str(file_id)
            
        except Exception as e:
            logger.error(f"❌ Erro ao salvar PDF no GridFS: {str(e)}")
            raise

    def get_enhanced_pdf_from_gridfs(self, file_id: str) -> Optional[bytes]:
        """Recupera PDF aprimorado do GridFS"""
        try:
            file_data = self.fs.get(file_id)
            return file_data.read()
        except Exception as e:
            logger.error(f"❌ Erro ao recuperar PDF aprimorado do GridFS: {str(e)}")
            return None
