{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component } from '@angular/core';\nimport { BaseIcon } from 'primeng/icons/baseicon';\nclass ArrowDownRightIcon extends BaseIcon {\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵArrowDownRightIcon_BaseFactory;\n    return function ArrowDownRightIcon_Factory(__ngFactoryType__) {\n      return (ɵArrowDownRightIcon_BaseFactory || (ɵArrowDownRightIcon_BaseFactory = i0.ɵɵgetInheritedFactory(ArrowDownRightIcon)))(__ngFactoryType__ || ArrowDownRightIcon);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: ArrowDownRightIcon,\n    selectors: [[\"ArrowDownRightIcon\"]],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    decls: 2,\n    vars: 5,\n    consts: [[\"width\", \"14\", \"height\", \"14\", \"viewBox\", \"0 0 14 14\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"d\", \"M12 3.88141C12 3.70664 11.9306 3.53903 11.807 3.41545C11.6834 3.29187 11.5158 3.22244 11.341 3.22244C11.1662 3.22244 10.9986 3.29187 10.875 3.41545C10.7515 3.53903 10.682 3.70664 10.682 3.88141V9.75069L3.1082 2.17686C2.98328 2.06046 2.81806 1.99709 2.64734 2.0001C2.47662 2.00311 2.31373 2.07227 2.19299 2.19301C2.07226 2.31375 2.0031 2.47663 2.00009 2.64735C1.99708 2.81807 2.06045 2.9833 2.17685 3.10821L9.75068 10.682H3.8814C3.70663 10.682 3.53901 10.7515 3.41543 10.8751C3.29185 10.9986 3.22242 11.1663 3.22242 11.341C3.22242 11.5158 3.29185 11.6834 3.41543 11.807C3.53901 11.9306 3.70663 12 3.8814 12H11.3849C11.4725 11.9995 11.5592 11.9816 11.6397 11.9473C11.7439 11.8934 11.832 11.8131 11.8952 11.7144C11.9584 11.6157 11.9946 11.5021 12 11.385V3.88141Z\", \"fill\", \"currentColor\"]],\n    template: function ArrowDownRightIcon_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(0, \"svg\", 0);\n        i0.ɵɵelement(1, \"path\", 1);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.getClassNames());\n        i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel)(\"aria-hidden\", ctx.ariaHidden)(\"role\", ctx.role);\n      }\n    },\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ArrowDownRightIcon, [{\n    type: Component,\n    args: [{\n      selector: 'ArrowDownRightIcon',\n      standalone: true,\n      template: `\n        <svg width=\"14\" height=\"14\" viewBox=\"0 0 14 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" [attr.aria-label]=\"ariaLabel\" [attr.aria-hidden]=\"ariaHidden\" [attr.role]=\"role\" [class]=\"getClassNames()\">\n            <path\n                d=\"M12 3.88141C12 3.70664 11.9306 3.53903 11.807 3.41545C11.6834 3.29187 11.5158 3.22244 11.341 3.22244C11.1662 3.22244 10.9986 3.29187 10.875 3.41545C10.7515 3.53903 10.682 3.70664 10.682 3.88141V9.75069L3.1082 2.17686C2.98328 2.06046 2.81806 1.99709 2.64734 2.0001C2.47662 2.00311 2.31373 2.07227 2.19299 2.19301C2.07226 2.31375 2.0031 2.47663 2.00009 2.64735C1.99708 2.81807 2.06045 2.9833 2.17685 3.10821L9.75068 10.682H3.8814C3.70663 10.682 3.53901 10.7515 3.41543 10.8751C3.29185 10.9986 3.22242 11.1663 3.22242 11.341C3.22242 11.5158 3.29185 11.6834 3.41543 11.807C3.53901 11.9306 3.70663 12 3.8814 12H11.3849C11.4725 11.9995 11.5592 11.9816 11.6397 11.9473C11.7439 11.8934 11.832 11.8131 11.8952 11.7144C11.9584 11.6157 11.9946 11.5021 12 11.385V3.88141Z\"\n                fill=\"currentColor\"\n            />\n        </svg>\n    `\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ArrowDownRightIcon };", "map": {"version": 3, "names": ["i0", "Component", "BaseIcon", "ArrowDownRightIcon", "ɵfac", "ɵArrowDownRightIcon_BaseFactory", "ArrowDownRightIcon_Factory", "__ngFactoryType__", "ɵɵgetInheritedFactory", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "ArrowDownRightIcon_Template", "rf", "ctx", "ɵɵnamespaceSVG", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵclassMap", "getClassNames", "ɵɵattribute", "aria<PERSON><PERSON><PERSON>", "ariaHidden", "role", "encapsulation", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "standalone"], "sources": ["C:/projetos/scope-ai/frontend/node_modules/primeng/fesm2022/primeng-icons-arrowdownright.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component } from '@angular/core';\nimport { BaseIcon } from 'primeng/icons/baseicon';\n\nclass ArrowDownRightIcon extends BaseIcon {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: ArrowDownRightIcon, deps: null, target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.10\", type: ArrowDownRightIcon, isStandalone: true, selector: \"ArrowDownRightIcon\", usesInheritance: true, ngImport: i0, template: `\n        <svg width=\"14\" height=\"14\" viewBox=\"0 0 14 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" [attr.aria-label]=\"ariaLabel\" [attr.aria-hidden]=\"ariaHidden\" [attr.role]=\"role\" [class]=\"getClassNames()\">\n            <path\n                d=\"M12 3.88141C12 3.70664 11.9306 3.53903 11.807 3.41545C11.6834 3.29187 11.5158 3.22244 11.341 3.22244C11.1662 3.22244 10.9986 3.29187 10.875 3.41545C10.7515 3.53903 10.682 3.70664 10.682 3.88141V9.75069L3.1082 2.17686C2.98328 2.06046 2.81806 1.99709 2.64734 2.0001C2.47662 2.00311 2.31373 2.07227 2.19299 2.19301C2.07226 2.31375 2.0031 2.47663 2.00009 2.64735C1.99708 2.81807 2.06045 2.9833 2.17685 3.10821L9.75068 10.682H3.8814C3.70663 10.682 3.53901 10.7515 3.41543 10.8751C3.29185 10.9986 3.22242 11.1663 3.22242 11.341C3.22242 11.5158 3.29185 11.6834 3.41543 11.807C3.53901 11.9306 3.70663 12 3.8814 12H11.3849C11.4725 11.9995 11.5592 11.9816 11.6397 11.9473C11.7439 11.8934 11.832 11.8131 11.8952 11.7144C11.9584 11.6157 11.9946 11.5021 12 11.385V3.88141Z\"\n                fill=\"currentColor\"\n            />\n        </svg>\n    `, isInline: true });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: ArrowDownRightIcon, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'ArrowDownRightIcon',\n                    standalone: true,\n                    template: `\n        <svg width=\"14\" height=\"14\" viewBox=\"0 0 14 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" [attr.aria-label]=\"ariaLabel\" [attr.aria-hidden]=\"ariaHidden\" [attr.role]=\"role\" [class]=\"getClassNames()\">\n            <path\n                d=\"M12 3.88141C12 3.70664 11.9306 3.53903 11.807 3.41545C11.6834 3.29187 11.5158 3.22244 11.341 3.22244C11.1662 3.22244 10.9986 3.29187 10.875 3.41545C10.7515 3.53903 10.682 3.70664 10.682 3.88141V9.75069L3.1082 2.17686C2.98328 2.06046 2.81806 1.99709 2.64734 2.0001C2.47662 2.00311 2.31373 2.07227 2.19299 2.19301C2.07226 2.31375 2.0031 2.47663 2.00009 2.64735C1.99708 2.81807 2.06045 2.9833 2.17685 3.10821L9.75068 10.682H3.8814C3.70663 10.682 3.53901 10.7515 3.41543 10.8751C3.29185 10.9986 3.22242 11.1663 3.22242 11.341C3.22242 11.5158 3.29185 11.6834 3.41543 11.807C3.53901 11.9306 3.70663 12 3.8814 12H11.3849C11.4725 11.9995 11.5592 11.9816 11.6397 11.9473C11.7439 11.8934 11.832 11.8131 11.8952 11.7144C11.9584 11.6157 11.9946 11.5021 12 11.385V3.88141Z\"\n                fill=\"currentColor\"\n            />\n        </svg>\n    `\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ArrowDownRightIcon };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,QAAQ,eAAe;AACzC,SAASC,QAAQ,QAAQ,wBAAwB;AAEjD,MAAMC,kBAAkB,SAASD,QAAQ,CAAC;EACtC,OAAOE,IAAI;IAAA,IAAAC,+BAAA;IAAA,gBAAAC,2BAAAC,iBAAA;MAAA,QAAAF,+BAAA,KAAAA,+BAAA,GAA+EL,EAAE,CAAAQ,qBAAA,CAAQL,kBAAkB,IAAAI,iBAAA,IAAlBJ,kBAAkB;IAAA;EAAA;EACtH,OAAOM,IAAI,kBAD+ET,EAAE,CAAAU,iBAAA;IAAAC,IAAA,EACJR,kBAAkB;IAAAS,SAAA;IAAAC,QAAA,GADhBb,EAAE,CAAAc,0BAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFpB,EAAE,CAAAsB,cAAA;QAAFtB,EAAE,CAAAuB,cAAA,YAEiH,CAAC;QAFpHvB,EAAE,CAAAwB,SAAA,aAMnF,CAAC;QANgFxB,EAAE,CAAAyB,YAAA,CAOnF,CAAC;MAAA;MAAA,IAAAL,EAAA;QAPgFpB,EAAE,CAAA0B,UAAA,CAAAL,GAAA,CAAAM,aAAA,EAEgH,CAAC;QAFnH3B,EAAE,CAAA4B,WAAA,eAAAP,GAAA,CAAAQ,SAAA,iBAAAR,GAAA,CAAAS,UAAA,UAAAT,GAAA,CAAAU,IAAA;MAAA;IAAA;IAAAC,aAAA;EAAA;AAShG;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAV8FjC,EAAE,CAAAkC,iBAAA,CAUJ/B,kBAAkB,EAAc,CAAC;IACjHQ,IAAI,EAAEV,SAAS;IACfkC,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oBAAoB;MAC9BC,UAAU,EAAE,IAAI;MAChBnB,QAAQ,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;IACgB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASf,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}