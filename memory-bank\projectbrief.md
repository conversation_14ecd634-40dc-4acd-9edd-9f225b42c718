# Project Brief - ScopeAI

**Última Atualização**: 2025-01-29

## Visão Geral

O ScopeAI é uma plataforma inteligente de análise empresarial que utiliza IA para gerar insights profundos sobre empresas através de suas URLs. O sistema combina múltiplos agentes especializados para entregar análises completas, estimativas de projetos e recomendações estratégicas.

## Objetivos Principais

1. **Análise Automatizada de Empresas**: Coletar e analisar dados de empresas através de suas URLs
2. **Geração de Insights**: Produzir dossiês expandidos com análise SWOT, stack técnico, mercado
3. **Sugestão de Projetos**: Gerar projetos personalizados baseados nas necessidades identificadas
4. **Estimativas Profissionais**: Calcular custos, prazos e recursos com o Team Agno
5. **Entregáveis Ricos**: Gerar PDFs profissionais, podcasts e relatórios interativos

## Requisitos Funcionais

### 1. Cadastro e Análise de Clientes
- Cadastro de clientes com nome, URL e contexto
- Análise automática via Perplexity API
- Geração de dossiê expandido (15+ seções)
- Pesquisa de mercado e expectativas

### 2. Geração de Projetos
- Análise de necessidades baseada no dossiê
- Geração de 5-10 projetos sugeridos
- Categorização por prioridade e impacto
- Estimativas detalhadas por projeto

### 3. Team Agno - Estimativas
- 10 agentes especializados (Arquiteto, Designer, Engenheiro, etc)
- Análise convergente de custos e prazos
- Breakdown detalhado de tarefas
- Análise de riscos e dependências

### 4. Relatórios e Entregáveis
- PDFs expandidos com 15+ seções
- Podcasts gerados via IA
- Dashboard interativo
- Exportação de dados

## Requisitos Não-Funcionais

1. **Performance**: Análises completas em menos de 3 minutos
2. **Escalabilidade**: Suportar análises concorrentes
3. **Confiabilidade**: Sistema de fallbacks e cache
4. **Segurança**: Proteção de dados sensíveis
5. **UX**: Interface intuitiva e moderna

## Escopo Técnico

- **Frontend**: Angular 19 com Tailwind CSS
- **Backend**: FastAPI com Python 3.12
- **Banco de Dados**: MongoDB Atlas
- **IA**: OpenAI, Mistral, Llama3.1
- **Infraestrutura**: Docker, WebSockets

## Stakeholders

- **Usuários Finais**: Consultores, analistas de negócios
- **Clientes**: Empresas que buscam transformação digital
- **Time de Desenvolvimento**: Engenheiros, designers, QA

## Critérios de Sucesso

1. Taxa de acerto de 85%+ nas análises
2. Tempo médio de análise < 3 minutos
3. Satisfação do usuário > 4.5/5
4. Zero downtime crítico
5. ROI positivo em 6 meses 