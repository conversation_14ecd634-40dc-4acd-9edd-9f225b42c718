import logging
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from collections import defaultdict
import json

from .data_quality import DataQuality

logger = logging.getLogger(__name__)

@dataclass
class FallbackEvent:
    """Evento de uso de fallback"""
    timestamp: datetime
    source: str
    reason: str
    quality: DataQuality
    confidence_score: float
    service: str
    user_id: Optional[str] = None
    additional_data: Optional[Dict[str, Any]] = None

class FallbackMonitor:
    """
    Monitor para rastrear uso de fallbacks/dados simulados
    
    Responsável por:
    - Registrar eventos de fallback
    - Gerar alertas quando muitos fallbacks são usados
    - Fornecer estatísticas de qualidade de dados
    - Notificar administradores sobre problemas
    """
    
    def __init__(self):
        self.events: List[FallbackEvent] = []
        self.stats: Dict[str, Any] = defaultdict(int)
        self.alert_thresholds = {
            "max_fallbacks_per_hour": 10,
            "max_error_fallbacks_per_session": 3,
            "min_confidence_alert": 0.3
        }
    
    def log_fallback(
        self,
        source: str,
        reason: str,
        quality: DataQuality,
        confidence_score: float,
        service: str,
        user_id: Optional[str] = None,
        additional_data: Optional[Dict[str, Any]] = None
    ):
        """Registrar uso de fallback"""
        event = FallbackEvent(
            timestamp=datetime.now(timezone.utc),
            source=source,
            reason=reason,
            quality=quality,
            confidence_score=confidence_score,
            service=service,
            user_id=user_id,
            additional_data=additional_data
        )
        
        self.events.append(event)
        
        # Manter apenas últimas 1000 entradas
        if len(self.events) > 1000:
            self.events = self.events[-1000:]
        
        # Atualizar estatísticas
        self._update_stats(event)
        
        # Verificar se alertas devem ser disparados
        self._check_alerts(event)
        
        # Log baseado no tipo de fallback
        if quality == DataQuality.ERROR_FALLBACK:
            logger.error(f"🚨 ERROR FALLBACK: {source} - {reason}")
        elif quality == DataQuality.BASIC_FALLBACK:
            logger.warning(f"⚠️ BASIC FALLBACK: {source} - {reason}")
        elif quality == DataQuality.ENHANCED_FALLBACK:
            logger.info(f"ℹ️ ENHANCED FALLBACK: {source} - {reason}")
        else:
            logger.debug(f"📊 FALLBACK LOG: {source} - {reason}")
    
    def _update_stats(self, event: FallbackEvent):
        """Atualizar estatísticas internas"""
        self.stats["total_fallbacks"] += 1
        self.stats[f"fallbacks_{event.quality.value}"] += 1
        self.stats[f"service_{event.service}"] += 1
        self.stats[f"source_{event.source}"] += 1
        
        # Estatísticas de confiança
        if event.confidence_score < 0.2:
            self.stats["very_low_confidence"] += 1
        elif event.confidence_score < 0.5:
            self.stats["low_confidence"] += 1
        elif event.confidence_score < 0.8:
            self.stats["medium_confidence"] += 1
        else:
            self.stats["high_confidence"] += 1
    
    def _check_alerts(self, event: FallbackEvent):
        """Verificar se alertas devem ser disparados"""
        # Contar fallbacks na última hora
        hour_ago = datetime.now(timezone.utc).replace(minute=0, second=0, microsecond=0)
        recent_fallbacks = [
            e for e in self.events[-50:]  # Últimos 50 eventos
            if e.timestamp >= hour_ago
        ]
        
        # Alerta para muitos fallbacks
        if len(recent_fallbacks) >= self.alert_thresholds["max_fallbacks_per_hour"]:
            self._trigger_alert(
                "high_fallback_rate",
                f"🚨 Taxa alta de fallbacks: {len(recent_fallbacks)} na última hora",
                {"count": len(recent_fallbacks), "threshold": self.alert_thresholds["max_fallbacks_per_hour"]}
            )
        
        # Alerta para fallbacks de erro
        if event.quality == DataQuality.ERROR_FALLBACK:
            error_fallbacks = [
                e for e in recent_fallbacks
                if e.quality == DataQuality.ERROR_FALLBACK and e.user_id == event.user_id
            ]
            
            if len(error_fallbacks) >= self.alert_thresholds["max_error_fallbacks_per_session"]:
                self._trigger_alert(
                    "session_error_fallbacks",
                    f"🚨 Muitos fallbacks de erro para usuário {event.user_id}",
                    {"user_id": event.user_id, "error_count": len(error_fallbacks)}
                )
        
        # Alerta para baixa confiança
        if event.confidence_score <= self.alert_thresholds["min_confidence_alert"]:
            self._trigger_alert(
                "low_confidence_data",
                f"⚠️ Dados de baixa confiança: {event.source} (score: {event.confidence_score})",
                {"source": event.source, "confidence": event.confidence_score}
            )
    
    def _trigger_alert(self, alert_type: str, message: str, data: Dict[str, Any]):
        """Disparar alerta para administradores"""
        logger.critical(f"FALLBACK ALERT [{alert_type}]: {message}")
        
        # Aqui seria onde notificaria administradores
        # Por exemplo: webhook, email, Slack, etc.
        alert_data = {
            "type": alert_type,
            "message": message,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "data": data
        }
        
        # Log estruturado para análise posterior
        logger.critical(f"ALERT_DATA: {json.dumps(alert_data)}")
    
    def get_quality_report(self) -> Dict[str, Any]:
        """Gerar relatório de qualidade dos dados"""
        total_events = len(self.events)
        
        if total_events == 0:
            return {
                "summary": "Nenhum evento de fallback registrado",
                "total_events": 0
            }
        
        # Calcular estatísticas por qualidade
        quality_stats = {}
        for quality in DataQuality:
            count = self.stats.get(f"fallbacks_{quality.value}", 0)
            quality_stats[quality.value] = {
                "count": count,
                "percentage": (count / total_events) * 100 if total_events > 0 else 0
            }
        
        # Estatísticas por serviço
        service_stats = {}
        for key, value in self.stats.items():
            if key.startswith("service_"):
                service_name = key.replace("service_", "")
                service_stats[service_name] = value
        
        # Estatísticas por fonte
        source_stats = {}
        for key, value in self.stats.items():
            if key.startswith("source_"):
                source_name = key.replace("source_", "")
                source_stats[source_name] = value
        
        # Eventos recentes (últimas 24h)
        day_ago = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
        recent_events = [e for e in self.events if e.timestamp >= day_ago]
        
        # Score geral de qualidade (quanto maior, melhor)
        total_confidence = sum(e.confidence_score for e in self.events)
        avg_confidence = total_confidence / total_events if total_events > 0 else 1.0
        
        error_percentage = quality_stats.get("error_fallback", {}).get("percentage", 0)
        basic_percentage = quality_stats.get("basic_fallback", {}).get("percentage", 0)
        
        # Score: 100 - (% error * 2) - (% basic * 1) + (confiança média * 20)
        quality_score = max(0, min(100, 
            100 - (error_percentage * 2) - (basic_percentage * 1) + (avg_confidence * 20)
        ))
        
        return {
            "summary": f"Qualidade dos dados: {quality_score:.1f}/100",
            "quality_score": quality_score,
            "total_events": total_events,
            "recent_events_24h": len(recent_events),
            "average_confidence": avg_confidence,
            "quality_breakdown": quality_stats,
            "service_breakdown": service_stats,
            "source_breakdown": source_stats,
            "confidence_distribution": {
                "very_low": self.stats.get("very_low_confidence", 0),
                "low": self.stats.get("low_confidence", 0),
                "medium": self.stats.get("medium_confidence", 0),
                "high": self.stats.get("high_confidence", 0)
            },
            "recommendations": self._generate_recommendations(quality_score, quality_stats)
        }
    
    def _generate_recommendations(self, quality_score: float, quality_stats: Dict[str, Any]) -> List[str]:
        """Gerar recomendações baseadas na qualidade dos dados"""
        recommendations = []
        
        if quality_score < 30:
            recommendations.append("🚨 CRÍTICO: Muitos sistemas estão falhando. Verificar infraestrutura urgentemente.")
        elif quality_score < 50:
            recommendations.append("⚠️ ALERTA: Qualidade dos dados está baixa. Investigar APIs externas.")
        elif quality_score < 70:
            recommendations.append("ℹ️ Qualidade moderada. Monitorar e otimizar fallbacks.")
        else:
            recommendations.append("✅ Qualidade boa. Manter monitoramento.")
        
        # Recomendações específicas por tipo de fallback
        error_pct = quality_stats.get("error_fallback", {}).get("percentage", 0)
        if error_pct > 20:
            recommendations.append("Corrigir erros que estão causando fallbacks de erro.")
        
        basic_pct = quality_stats.get("basic_fallback", {}).get("percentage", 0)
        if basic_pct > 30:
            recommendations.append("Melhorar fallbacks básicos para fallbacks aprimorados.")
        
        return recommendations
    
    def get_recent_events(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Obter eventos recentes"""
        recent = self.events[-limit:] if len(self.events) > limit else self.events
        return [asdict(event) for event in recent]
    
    def clear_old_events(self, days_to_keep: int = 7):
        """Limpar eventos antigos"""
        cutoff = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
        cutoff = cutoff.replace(day=cutoff.day - days_to_keep)
        
        old_count = len(self.events)
        self.events = [e for e in self.events if e.timestamp >= cutoff]
        new_count = len(self.events)
        
        logger.info(f"Fallback monitor: Removidos {old_count - new_count} eventos antigos")
    
    def export_events_json(self, filepath: str):
        """Exportar eventos para análise"""
        data = {
            "exported_at": datetime.now(timezone.utc).isoformat(),
            "total_events": len(self.events),
            "events": [asdict(event) for event in self.events],
            "stats": dict(self.stats),
            "quality_report": self.get_quality_report()
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, default=str, ensure_ascii=False)
        
        logger.info(f"Eventos de fallback exportados para: {filepath}")

# Instância global do monitor
fallback_monitor = FallbackMonitor() 