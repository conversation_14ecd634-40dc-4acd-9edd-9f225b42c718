{"ast": null, "code": "/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { Injectable, NgModule } from '@angular/core';\nimport { Observable } from 'rxjs';\nimport { HttpHeaders, HttpResponse, HttpStatusCode, HttpErrorResponse, HttpEventType, HttpBackend, REQUESTS_CONTRIBUTE_TO_STABILITY, HttpClientModule } from '../module-z3bvLlVg.mjs';\nimport 'rxjs/operators';\nimport '../xhr-BfNfxNDv.mjs';\nimport '../dom_tokens-rA0ACyx7.mjs';\n\n/**\n * Controller to be injected into tests, that allows for mocking and flushing\n * of requests.\n *\n * @publicApi\n */\nclass HttpTestingController {}\n\n/**\n * A mock requests that was received and is ready to be answered.\n *\n * This interface allows access to the underlying `HttpRequest`, and allows\n * responding with `HttpEvent`s or `HttpErrorResponse`s.\n *\n * @publicApi\n */\nclass TestRequest {\n  request;\n  observer;\n  /**\n   * Whether the request was cancelled after it was sent.\n   */\n  get cancelled() {\n    return this._cancelled;\n  }\n  /**\n   * @internal set by `HttpClientTestingBackend`\n   */\n  _cancelled = false;\n  constructor(request, observer) {\n    this.request = request;\n    this.observer = observer;\n  }\n  /**\n   * Resolve the request by returning a body plus additional HTTP information (such as response\n   * headers) if provided.\n   * If the request specifies an expected body type, the body is converted into the requested type.\n   * Otherwise, the body is converted to `JSON` by default.\n   *\n   * Both successful and unsuccessful responses can be delivered via `flush()`.\n   */\n  flush(body, opts = {}) {\n    if (this.cancelled) {\n      throw new Error(`Cannot flush a cancelled request.`);\n    }\n    const url = this.request.urlWithParams;\n    const headers = opts.headers instanceof HttpHeaders ? opts.headers : new HttpHeaders(opts.headers);\n    body = _maybeConvertBody(this.request.responseType, body);\n    let statusText = opts.statusText;\n    let status = opts.status !== undefined ? opts.status : HttpStatusCode.Ok;\n    if (opts.status === undefined) {\n      if (body === null) {\n        status = HttpStatusCode.NoContent;\n        statusText ||= 'No Content';\n      } else {\n        statusText ||= 'OK';\n      }\n    }\n    if (statusText === undefined) {\n      throw new Error('statusText is required when setting a custom status.');\n    }\n    if (status >= 200 && status < 300) {\n      this.observer.next(new HttpResponse({\n        body,\n        headers,\n        status,\n        statusText,\n        url\n      }));\n      this.observer.complete();\n    } else {\n      this.observer.error(new HttpErrorResponse({\n        error: body,\n        headers,\n        status,\n        statusText,\n        url\n      }));\n    }\n  }\n  error(error, opts = {}) {\n    if (this.cancelled) {\n      throw new Error(`Cannot return an error for a cancelled request.`);\n    }\n    if (opts.status && opts.status >= 200 && opts.status < 300) {\n      throw new Error(`error() called with a successful status.`);\n    }\n    const headers = opts.headers instanceof HttpHeaders ? opts.headers : new HttpHeaders(opts.headers);\n    this.observer.error(new HttpErrorResponse({\n      error,\n      headers,\n      status: opts.status || 0,\n      statusText: opts.statusText || '',\n      url: this.request.urlWithParams\n    }));\n  }\n  /**\n   * Deliver an arbitrary `HttpEvent` (such as a progress event) on the response stream for this\n   * request.\n   */\n  event(event) {\n    if (this.cancelled) {\n      throw new Error(`Cannot send events to a cancelled request.`);\n    }\n    this.observer.next(event);\n  }\n}\n/**\n * Helper function to convert a response body to an ArrayBuffer.\n */\nfunction _toArrayBufferBody(body) {\n  if (typeof ArrayBuffer === 'undefined') {\n    throw new Error('ArrayBuffer responses are not supported on this platform.');\n  }\n  if (body instanceof ArrayBuffer) {\n    return body;\n  }\n  throw new Error('Automatic conversion to ArrayBuffer is not supported for response type.');\n}\n/**\n * Helper function to convert a response body to a Blob.\n */\nfunction _toBlob(body) {\n  if (typeof Blob === 'undefined') {\n    throw new Error('Blob responses are not supported on this platform.');\n  }\n  if (body instanceof Blob) {\n    return body;\n  }\n  if (ArrayBuffer && body instanceof ArrayBuffer) {\n    return new Blob([body]);\n  }\n  throw new Error('Automatic conversion to Blob is not supported for response type.');\n}\n/**\n * Helper function to convert a response body to JSON data.\n */\nfunction _toJsonBody(body, format = 'JSON') {\n  if (typeof ArrayBuffer !== 'undefined' && body instanceof ArrayBuffer) {\n    throw new Error(`Automatic conversion to ${format} is not supported for ArrayBuffers.`);\n  }\n  if (typeof Blob !== 'undefined' && body instanceof Blob) {\n    throw new Error(`Automatic conversion to ${format} is not supported for Blobs.`);\n  }\n  if (typeof body === 'string' || typeof body === 'number' || typeof body === 'object' || typeof body === 'boolean' || Array.isArray(body)) {\n    return body;\n  }\n  throw new Error(`Automatic conversion to ${format} is not supported for response type.`);\n}\n/**\n * Helper function to convert a response body to a string.\n */\nfunction _toTextBody(body) {\n  if (typeof body === 'string') {\n    return body;\n  }\n  if (typeof ArrayBuffer !== 'undefined' && body instanceof ArrayBuffer) {\n    throw new Error('Automatic conversion to text is not supported for ArrayBuffers.');\n  }\n  if (typeof Blob !== 'undefined' && body instanceof Blob) {\n    throw new Error('Automatic conversion to text is not supported for Blobs.');\n  }\n  return JSON.stringify(_toJsonBody(body, 'text'));\n}\n/**\n * Convert a response body to the requested type.\n */\nfunction _maybeConvertBody(responseType, body) {\n  if (body === null) {\n    return null;\n  }\n  switch (responseType) {\n    case 'arraybuffer':\n      return _toArrayBufferBody(body);\n    case 'blob':\n      return _toBlob(body);\n    case 'json':\n      return _toJsonBody(body);\n    case 'text':\n      return _toTextBody(body);\n    default:\n      throw new Error(`Unsupported responseType: ${responseType}`);\n  }\n}\n\n/**\n * A testing backend for `HttpClient` which both acts as an `HttpBackend`\n * and as the `HttpTestingController`.\n *\n * `HttpClientTestingBackend` works by keeping a list of all open requests.\n * As requests come in, they're added to the list. Users can assert that specific\n * requests were made and then flush them. In the end, a verify() method asserts\n * that no unexpected requests were made.\n *\n *\n */\nclass HttpClientTestingBackend {\n  /**\n   * List of pending requests which have not yet been expected.\n   */\n  open = [];\n  /**\n   * Used when checking if we need to throw the NOT_USING_FETCH_BACKEND_IN_SSR error\n   */\n  isTestingBackend = true;\n  /**\n   * Handle an incoming request by queueing it in the list of open requests.\n   */\n  handle(req) {\n    return new Observable(observer => {\n      const testReq = new TestRequest(req, observer);\n      this.open.push(testReq);\n      observer.next({\n        type: HttpEventType.Sent\n      });\n      return () => {\n        testReq._cancelled = true;\n      };\n    });\n  }\n  /**\n   * Helper function to search for requests in the list of open requests.\n   */\n  _match(match) {\n    if (typeof match === 'string') {\n      return this.open.filter(testReq => testReq.request.urlWithParams === match);\n    } else if (typeof match === 'function') {\n      return this.open.filter(testReq => match(testReq.request));\n    } else {\n      return this.open.filter(testReq => (!match.method || testReq.request.method === match.method.toUpperCase()) && (!match.url || testReq.request.urlWithParams === match.url));\n    }\n  }\n  /**\n   * Search for requests in the list of open requests, and return all that match\n   * without asserting anything about the number of matches.\n   */\n  match(match) {\n    const results = this._match(match);\n    results.forEach(result => {\n      const index = this.open.indexOf(result);\n      if (index !== -1) {\n        this.open.splice(index, 1);\n      }\n    });\n    return results;\n  }\n  /**\n   * Expect that a single outstanding request matches the given matcher, and return\n   * it.\n   *\n   * Requests returned through this API will no longer be in the list of open requests,\n   * and thus will not match twice.\n   */\n  expectOne(match, description) {\n    description ||= this.descriptionFromMatcher(match);\n    const matches = this.match(match);\n    if (matches.length > 1) {\n      throw new Error(`Expected one matching request for criteria \"${description}\", found ${matches.length} requests.`);\n    }\n    if (matches.length === 0) {\n      let message = `Expected one matching request for criteria \"${description}\", found none.`;\n      if (this.open.length > 0) {\n        // Show the methods and URLs of open requests in the error, for convenience.\n        const requests = this.open.map(describeRequest).join(', ');\n        message += ` Requests received are: ${requests}.`;\n      }\n      throw new Error(message);\n    }\n    return matches[0];\n  }\n  /**\n   * Expect that no outstanding requests match the given matcher, and throw an error\n   * if any do.\n   */\n  expectNone(match, description) {\n    description ||= this.descriptionFromMatcher(match);\n    const matches = this.match(match);\n    if (matches.length > 0) {\n      throw new Error(`Expected zero matching requests for criteria \"${description}\", found ${matches.length}.`);\n    }\n  }\n  /**\n   * Validate that there are no outstanding requests.\n   */\n  verify(opts = {}) {\n    let open = this.open;\n    // It's possible that some requests may be cancelled, and this is expected.\n    // The user can ask to ignore open requests which have been cancelled.\n    if (opts.ignoreCancelled) {\n      open = open.filter(testReq => !testReq.cancelled);\n    }\n    if (open.length > 0) {\n      // Show the methods and URLs of open requests in the error, for convenience.\n      const requests = open.map(describeRequest).join(', ');\n      throw new Error(`Expected no open requests, found ${open.length}: ${requests}`);\n    }\n  }\n  descriptionFromMatcher(matcher) {\n    if (typeof matcher === 'string') {\n      return `Match URL: ${matcher}`;\n    } else if (typeof matcher === 'object') {\n      const method = matcher.method || '(any)';\n      const url = matcher.url || '(any)';\n      return `Match method: ${method}, URL: ${url}`;\n    } else {\n      return `Match by function: ${matcher.name}`;\n    }\n  }\n  static ɵfac = function HttpClientTestingBackend_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HttpClientTestingBackend)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HttpClientTestingBackend,\n    factory: HttpClientTestingBackend.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpClientTestingBackend, [{\n    type: Injectable\n  }], null, null);\n})();\nfunction describeRequest(testRequest) {\n  const url = testRequest.request.urlWithParams;\n  const method = testRequest.request.method;\n  return `${method} ${url}`;\n}\nfunction provideHttpClientTesting() {\n  return [HttpClientTestingBackend, {\n    provide: HttpBackend,\n    useExisting: HttpClientTestingBackend\n  }, {\n    provide: HttpTestingController,\n    useExisting: HttpClientTestingBackend\n  }, {\n    provide: REQUESTS_CONTRIBUTE_TO_STABILITY,\n    useValue: false\n  }];\n}\n\n/**\n * Configures `HttpClientTestingBackend` as the `HttpBackend` used by `HttpClient`.\n *\n * Inject `HttpTestingController` to expect and flush requests in your tests.\n *\n * @publicApi\n *\n * @deprecated Add `provideHttpClientTesting()` to your providers instead.\n */\nclass HttpClientTestingModule {\n  static ɵfac = function HttpClientTestingModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HttpClientTestingModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: HttpClientTestingModule,\n    imports: [HttpClientModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [provideHttpClientTesting()],\n    imports: [HttpClientModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpClientTestingModule, [{\n    type: NgModule,\n    args: [{\n      imports: [HttpClientModule],\n      providers: [provideHttpClientTesting()]\n    }]\n  }], null, null);\n})();\nexport { HttpClientTestingModule, HttpTestingController, TestRequest, provideHttpClientTesting };", "map": {"version": 3, "names": ["i0", "Injectable", "NgModule", "Observable", "HttpHeaders", "HttpResponse", "HttpStatusCode", "HttpErrorResponse", "HttpEventType", "HttpBackend", "REQUESTS_CONTRIBUTE_TO_STABILITY", "HttpClientModule", "HttpTestingController", "TestRequest", "request", "observer", "cancelled", "_cancelled", "constructor", "flush", "body", "opts", "Error", "url", "urlWithParams", "headers", "_maybeConvertBody", "responseType", "statusText", "status", "undefined", "Ok", "NoContent", "next", "complete", "error", "event", "_toArrayBufferBody", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_toBlob", "Blob", "_toJsonBody", "format", "Array", "isArray", "_toTextBody", "JSON", "stringify", "HttpClientTestingBackend", "open", "isTestingBackend", "handle", "req", "testReq", "push", "type", "<PERSON><PERSON>", "_match", "match", "filter", "method", "toUpperCase", "results", "for<PERSON>ach", "result", "index", "indexOf", "splice", "expectOne", "description", "descriptionFromMatcher", "matches", "length", "message", "requests", "map", "describeRequest", "join", "expectNone", "verify", "ignoreCancelled", "matcher", "name", "ɵfac", "HttpClientTestingBackend_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "testRequest", "provideHttpClientTesting", "provide", "useExisting", "useValue", "HttpClientTestingModule", "HttpClientTestingModule_Factory", "ɵmod", "ɵɵdefineNgModule", "imports", "ɵinj", "ɵɵdefineInjector", "providers", "args"], "sources": ["C:/projetos/scope-ai/frontend/node_modules/@angular/common/fesm2022/http/testing.mjs"], "sourcesContent": ["/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { Injectable, NgModule } from '@angular/core';\nimport { Observable } from 'rxjs';\nimport { HttpHeaders, HttpResponse, HttpStatusCode, HttpErrorResponse, HttpEventType, HttpBackend, REQUESTS_CONTRIBUTE_TO_STABILITY, HttpClientModule } from '../module-z3bvLlVg.mjs';\nimport 'rxjs/operators';\nimport '../xhr-BfNfxNDv.mjs';\nimport '../dom_tokens-rA0ACyx7.mjs';\n\n/**\n * Controller to be injected into tests, that allows for mocking and flushing\n * of requests.\n *\n * @publicApi\n */\nclass HttpTestingController {\n}\n\n/**\n * A mock requests that was received and is ready to be answered.\n *\n * This interface allows access to the underlying `HttpRequest`, and allows\n * responding with `HttpEvent`s or `HttpErrorResponse`s.\n *\n * @publicApi\n */\nclass TestRequest {\n    request;\n    observer;\n    /**\n     * Whether the request was cancelled after it was sent.\n     */\n    get cancelled() {\n        return this._cancelled;\n    }\n    /**\n     * @internal set by `HttpClientTestingBackend`\n     */\n    _cancelled = false;\n    constructor(request, observer) {\n        this.request = request;\n        this.observer = observer;\n    }\n    /**\n     * Resolve the request by returning a body plus additional HTTP information (such as response\n     * headers) if provided.\n     * If the request specifies an expected body type, the body is converted into the requested type.\n     * Otherwise, the body is converted to `JSON` by default.\n     *\n     * Both successful and unsuccessful responses can be delivered via `flush()`.\n     */\n    flush(body, opts = {}) {\n        if (this.cancelled) {\n            throw new Error(`Cannot flush a cancelled request.`);\n        }\n        const url = this.request.urlWithParams;\n        const headers = opts.headers instanceof HttpHeaders ? opts.headers : new HttpHeaders(opts.headers);\n        body = _maybeConvertBody(this.request.responseType, body);\n        let statusText = opts.statusText;\n        let status = opts.status !== undefined ? opts.status : HttpStatusCode.Ok;\n        if (opts.status === undefined) {\n            if (body === null) {\n                status = HttpStatusCode.NoContent;\n                statusText ||= 'No Content';\n            }\n            else {\n                statusText ||= 'OK';\n            }\n        }\n        if (statusText === undefined) {\n            throw new Error('statusText is required when setting a custom status.');\n        }\n        if (status >= 200 && status < 300) {\n            this.observer.next(new HttpResponse({ body, headers, status, statusText, url }));\n            this.observer.complete();\n        }\n        else {\n            this.observer.error(new HttpErrorResponse({ error: body, headers, status, statusText, url }));\n        }\n    }\n    error(error, opts = {}) {\n        if (this.cancelled) {\n            throw new Error(`Cannot return an error for a cancelled request.`);\n        }\n        if (opts.status && opts.status >= 200 && opts.status < 300) {\n            throw new Error(`error() called with a successful status.`);\n        }\n        const headers = opts.headers instanceof HttpHeaders ? opts.headers : new HttpHeaders(opts.headers);\n        this.observer.error(new HttpErrorResponse({\n            error,\n            headers,\n            status: opts.status || 0,\n            statusText: opts.statusText || '',\n            url: this.request.urlWithParams,\n        }));\n    }\n    /**\n     * Deliver an arbitrary `HttpEvent` (such as a progress event) on the response stream for this\n     * request.\n     */\n    event(event) {\n        if (this.cancelled) {\n            throw new Error(`Cannot send events to a cancelled request.`);\n        }\n        this.observer.next(event);\n    }\n}\n/**\n * Helper function to convert a response body to an ArrayBuffer.\n */\nfunction _toArrayBufferBody(body) {\n    if (typeof ArrayBuffer === 'undefined') {\n        throw new Error('ArrayBuffer responses are not supported on this platform.');\n    }\n    if (body instanceof ArrayBuffer) {\n        return body;\n    }\n    throw new Error('Automatic conversion to ArrayBuffer is not supported for response type.');\n}\n/**\n * Helper function to convert a response body to a Blob.\n */\nfunction _toBlob(body) {\n    if (typeof Blob === 'undefined') {\n        throw new Error('Blob responses are not supported on this platform.');\n    }\n    if (body instanceof Blob) {\n        return body;\n    }\n    if (ArrayBuffer && body instanceof ArrayBuffer) {\n        return new Blob([body]);\n    }\n    throw new Error('Automatic conversion to Blob is not supported for response type.');\n}\n/**\n * Helper function to convert a response body to JSON data.\n */\nfunction _toJsonBody(body, format = 'JSON') {\n    if (typeof ArrayBuffer !== 'undefined' && body instanceof ArrayBuffer) {\n        throw new Error(`Automatic conversion to ${format} is not supported for ArrayBuffers.`);\n    }\n    if (typeof Blob !== 'undefined' && body instanceof Blob) {\n        throw new Error(`Automatic conversion to ${format} is not supported for Blobs.`);\n    }\n    if (typeof body === 'string' ||\n        typeof body === 'number' ||\n        typeof body === 'object' ||\n        typeof body === 'boolean' ||\n        Array.isArray(body)) {\n        return body;\n    }\n    throw new Error(`Automatic conversion to ${format} is not supported for response type.`);\n}\n/**\n * Helper function to convert a response body to a string.\n */\nfunction _toTextBody(body) {\n    if (typeof body === 'string') {\n        return body;\n    }\n    if (typeof ArrayBuffer !== 'undefined' && body instanceof ArrayBuffer) {\n        throw new Error('Automatic conversion to text is not supported for ArrayBuffers.');\n    }\n    if (typeof Blob !== 'undefined' && body instanceof Blob) {\n        throw new Error('Automatic conversion to text is not supported for Blobs.');\n    }\n    return JSON.stringify(_toJsonBody(body, 'text'));\n}\n/**\n * Convert a response body to the requested type.\n */\nfunction _maybeConvertBody(responseType, body) {\n    if (body === null) {\n        return null;\n    }\n    switch (responseType) {\n        case 'arraybuffer':\n            return _toArrayBufferBody(body);\n        case 'blob':\n            return _toBlob(body);\n        case 'json':\n            return _toJsonBody(body);\n        case 'text':\n            return _toTextBody(body);\n        default:\n            throw new Error(`Unsupported responseType: ${responseType}`);\n    }\n}\n\n/**\n * A testing backend for `HttpClient` which both acts as an `HttpBackend`\n * and as the `HttpTestingController`.\n *\n * `HttpClientTestingBackend` works by keeping a list of all open requests.\n * As requests come in, they're added to the list. Users can assert that specific\n * requests were made and then flush them. In the end, a verify() method asserts\n * that no unexpected requests were made.\n *\n *\n */\nclass HttpClientTestingBackend {\n    /**\n     * List of pending requests which have not yet been expected.\n     */\n    open = [];\n    /**\n     * Used when checking if we need to throw the NOT_USING_FETCH_BACKEND_IN_SSR error\n     */\n    isTestingBackend = true;\n    /**\n     * Handle an incoming request by queueing it in the list of open requests.\n     */\n    handle(req) {\n        return new Observable((observer) => {\n            const testReq = new TestRequest(req, observer);\n            this.open.push(testReq);\n            observer.next({ type: HttpEventType.Sent });\n            return () => {\n                testReq._cancelled = true;\n            };\n        });\n    }\n    /**\n     * Helper function to search for requests in the list of open requests.\n     */\n    _match(match) {\n        if (typeof match === 'string') {\n            return this.open.filter((testReq) => testReq.request.urlWithParams === match);\n        }\n        else if (typeof match === 'function') {\n            return this.open.filter((testReq) => match(testReq.request));\n        }\n        else {\n            return this.open.filter((testReq) => (!match.method || testReq.request.method === match.method.toUpperCase()) &&\n                (!match.url || testReq.request.urlWithParams === match.url));\n        }\n    }\n    /**\n     * Search for requests in the list of open requests, and return all that match\n     * without asserting anything about the number of matches.\n     */\n    match(match) {\n        const results = this._match(match);\n        results.forEach((result) => {\n            const index = this.open.indexOf(result);\n            if (index !== -1) {\n                this.open.splice(index, 1);\n            }\n        });\n        return results;\n    }\n    /**\n     * Expect that a single outstanding request matches the given matcher, and return\n     * it.\n     *\n     * Requests returned through this API will no longer be in the list of open requests,\n     * and thus will not match twice.\n     */\n    expectOne(match, description) {\n        description ||= this.descriptionFromMatcher(match);\n        const matches = this.match(match);\n        if (matches.length > 1) {\n            throw new Error(`Expected one matching request for criteria \"${description}\", found ${matches.length} requests.`);\n        }\n        if (matches.length === 0) {\n            let message = `Expected one matching request for criteria \"${description}\", found none.`;\n            if (this.open.length > 0) {\n                // Show the methods and URLs of open requests in the error, for convenience.\n                const requests = this.open.map(describeRequest).join(', ');\n                message += ` Requests received are: ${requests}.`;\n            }\n            throw new Error(message);\n        }\n        return matches[0];\n    }\n    /**\n     * Expect that no outstanding requests match the given matcher, and throw an error\n     * if any do.\n     */\n    expectNone(match, description) {\n        description ||= this.descriptionFromMatcher(match);\n        const matches = this.match(match);\n        if (matches.length > 0) {\n            throw new Error(`Expected zero matching requests for criteria \"${description}\", found ${matches.length}.`);\n        }\n    }\n    /**\n     * Validate that there are no outstanding requests.\n     */\n    verify(opts = {}) {\n        let open = this.open;\n        // It's possible that some requests may be cancelled, and this is expected.\n        // The user can ask to ignore open requests which have been cancelled.\n        if (opts.ignoreCancelled) {\n            open = open.filter((testReq) => !testReq.cancelled);\n        }\n        if (open.length > 0) {\n            // Show the methods and URLs of open requests in the error, for convenience.\n            const requests = open.map(describeRequest).join(', ');\n            throw new Error(`Expected no open requests, found ${open.length}: ${requests}`);\n        }\n    }\n    descriptionFromMatcher(matcher) {\n        if (typeof matcher === 'string') {\n            return `Match URL: ${matcher}`;\n        }\n        else if (typeof matcher === 'object') {\n            const method = matcher.method || '(any)';\n            const url = matcher.url || '(any)';\n            return `Match method: ${method}, URL: ${url}`;\n        }\n        else {\n            return `Match by function: ${matcher.name}`;\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpClientTestingBackend, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpClientTestingBackend });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpClientTestingBackend, decorators: [{\n            type: Injectable\n        }] });\nfunction describeRequest(testRequest) {\n    const url = testRequest.request.urlWithParams;\n    const method = testRequest.request.method;\n    return `${method} ${url}`;\n}\n\nfunction provideHttpClientTesting() {\n    return [\n        HttpClientTestingBackend,\n        { provide: HttpBackend, useExisting: HttpClientTestingBackend },\n        { provide: HttpTestingController, useExisting: HttpClientTestingBackend },\n        { provide: REQUESTS_CONTRIBUTE_TO_STABILITY, useValue: false },\n    ];\n}\n\n/**\n * Configures `HttpClientTestingBackend` as the `HttpBackend` used by `HttpClient`.\n *\n * Inject `HttpTestingController` to expect and flush requests in your tests.\n *\n * @publicApi\n *\n * @deprecated Add `provideHttpClientTesting()` to your providers instead.\n */\nclass HttpClientTestingModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpClientTestingModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpClientTestingModule, imports: [HttpClientModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpClientTestingModule, providers: [provideHttpClientTesting()], imports: [HttpClientModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpClientTestingModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [HttpClientModule],\n                    providers: [provideHttpClientTesting()],\n                }]\n        }] });\n\nexport { HttpClientTestingModule, HttpTestingController, TestRequest, provideHttpClientTesting };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,QAAQ,QAAQ,eAAe;AACpD,SAASC,UAAU,QAAQ,MAAM;AACjC,SAASC,WAAW,EAAEC,YAAY,EAAEC,cAAc,EAAEC,iBAAiB,EAAEC,aAAa,EAAEC,WAAW,EAAEC,gCAAgC,EAAEC,gBAAgB,QAAQ,wBAAwB;AACrL,OAAO,gBAAgB;AACvB,OAAO,qBAAqB;AAC5B,OAAO,4BAA4B;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,CAAC;;AAG5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,CAAC;EACdC,OAAO;EACPC,QAAQ;EACR;AACJ;AACA;EACI,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACC,UAAU;EAC1B;EACA;AACJ;AACA;EACIA,UAAU,GAAG,KAAK;EAClBC,WAAWA,CAACJ,OAAO,EAAEC,QAAQ,EAAE;IAC3B,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC5B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACII,KAAKA,CAACC,IAAI,EAAEC,IAAI,GAAG,CAAC,CAAC,EAAE;IACnB,IAAI,IAAI,CAACL,SAAS,EAAE;MAChB,MAAM,IAAIM,KAAK,CAAC,mCAAmC,CAAC;IACxD;IACA,MAAMC,GAAG,GAAG,IAAI,CAACT,OAAO,CAACU,aAAa;IACtC,MAAMC,OAAO,GAAGJ,IAAI,CAACI,OAAO,YAAYrB,WAAW,GAAGiB,IAAI,CAACI,OAAO,GAAG,IAAIrB,WAAW,CAACiB,IAAI,CAACI,OAAO,CAAC;IAClGL,IAAI,GAAGM,iBAAiB,CAAC,IAAI,CAACZ,OAAO,CAACa,YAAY,EAAEP,IAAI,CAAC;IACzD,IAAIQ,UAAU,GAAGP,IAAI,CAACO,UAAU;IAChC,IAAIC,MAAM,GAAGR,IAAI,CAACQ,MAAM,KAAKC,SAAS,GAAGT,IAAI,CAACQ,MAAM,GAAGvB,cAAc,CAACyB,EAAE;IACxE,IAAIV,IAAI,CAACQ,MAAM,KAAKC,SAAS,EAAE;MAC3B,IAAIV,IAAI,KAAK,IAAI,EAAE;QACfS,MAAM,GAAGvB,cAAc,CAAC0B,SAAS;QACjCJ,UAAU,KAAK,YAAY;MAC/B,CAAC,MACI;QACDA,UAAU,KAAK,IAAI;MACvB;IACJ;IACA,IAAIA,UAAU,KAAKE,SAAS,EAAE;MAC1B,MAAM,IAAIR,KAAK,CAAC,sDAAsD,CAAC;IAC3E;IACA,IAAIO,MAAM,IAAI,GAAG,IAAIA,MAAM,GAAG,GAAG,EAAE;MAC/B,IAAI,CAACd,QAAQ,CAACkB,IAAI,CAAC,IAAI5B,YAAY,CAAC;QAAEe,IAAI;QAAEK,OAAO;QAAEI,MAAM;QAAED,UAAU;QAAEL;MAAI,CAAC,CAAC,CAAC;MAChF,IAAI,CAACR,QAAQ,CAACmB,QAAQ,CAAC,CAAC;IAC5B,CAAC,MACI;MACD,IAAI,CAACnB,QAAQ,CAACoB,KAAK,CAAC,IAAI5B,iBAAiB,CAAC;QAAE4B,KAAK,EAAEf,IAAI;QAAEK,OAAO;QAAEI,MAAM;QAAED,UAAU;QAAEL;MAAI,CAAC,CAAC,CAAC;IACjG;EACJ;EACAY,KAAKA,CAACA,KAAK,EAAEd,IAAI,GAAG,CAAC,CAAC,EAAE;IACpB,IAAI,IAAI,CAACL,SAAS,EAAE;MAChB,MAAM,IAAIM,KAAK,CAAC,iDAAiD,CAAC;IACtE;IACA,IAAID,IAAI,CAACQ,MAAM,IAAIR,IAAI,CAACQ,MAAM,IAAI,GAAG,IAAIR,IAAI,CAACQ,MAAM,GAAG,GAAG,EAAE;MACxD,MAAM,IAAIP,KAAK,CAAC,0CAA0C,CAAC;IAC/D;IACA,MAAMG,OAAO,GAAGJ,IAAI,CAACI,OAAO,YAAYrB,WAAW,GAAGiB,IAAI,CAACI,OAAO,GAAG,IAAIrB,WAAW,CAACiB,IAAI,CAACI,OAAO,CAAC;IAClG,IAAI,CAACV,QAAQ,CAACoB,KAAK,CAAC,IAAI5B,iBAAiB,CAAC;MACtC4B,KAAK;MACLV,OAAO;MACPI,MAAM,EAAER,IAAI,CAACQ,MAAM,IAAI,CAAC;MACxBD,UAAU,EAAEP,IAAI,CAACO,UAAU,IAAI,EAAE;MACjCL,GAAG,EAAE,IAAI,CAACT,OAAO,CAACU;IACtB,CAAC,CAAC,CAAC;EACP;EACA;AACJ;AACA;AACA;EACIY,KAAKA,CAACA,KAAK,EAAE;IACT,IAAI,IAAI,CAACpB,SAAS,EAAE;MAChB,MAAM,IAAIM,KAAK,CAAC,4CAA4C,CAAC;IACjE;IACA,IAAI,CAACP,QAAQ,CAACkB,IAAI,CAACG,KAAK,CAAC;EAC7B;AACJ;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAACjB,IAAI,EAAE;EAC9B,IAAI,OAAOkB,WAAW,KAAK,WAAW,EAAE;IACpC,MAAM,IAAIhB,KAAK,CAAC,2DAA2D,CAAC;EAChF;EACA,IAAIF,IAAI,YAAYkB,WAAW,EAAE;IAC7B,OAAOlB,IAAI;EACf;EACA,MAAM,IAAIE,KAAK,CAAC,yEAAyE,CAAC;AAC9F;AACA;AACA;AACA;AACA,SAASiB,OAAOA,CAACnB,IAAI,EAAE;EACnB,IAAI,OAAOoB,IAAI,KAAK,WAAW,EAAE;IAC7B,MAAM,IAAIlB,KAAK,CAAC,oDAAoD,CAAC;EACzE;EACA,IAAIF,IAAI,YAAYoB,IAAI,EAAE;IACtB,OAAOpB,IAAI;EACf;EACA,IAAIkB,WAAW,IAAIlB,IAAI,YAAYkB,WAAW,EAAE;IAC5C,OAAO,IAAIE,IAAI,CAAC,CAACpB,IAAI,CAAC,CAAC;EAC3B;EACA,MAAM,IAAIE,KAAK,CAAC,kEAAkE,CAAC;AACvF;AACA;AACA;AACA;AACA,SAASmB,WAAWA,CAACrB,IAAI,EAAEsB,MAAM,GAAG,MAAM,EAAE;EACxC,IAAI,OAAOJ,WAAW,KAAK,WAAW,IAAIlB,IAAI,YAAYkB,WAAW,EAAE;IACnE,MAAM,IAAIhB,KAAK,CAAC,2BAA2BoB,MAAM,qCAAqC,CAAC;EAC3F;EACA,IAAI,OAAOF,IAAI,KAAK,WAAW,IAAIpB,IAAI,YAAYoB,IAAI,EAAE;IACrD,MAAM,IAAIlB,KAAK,CAAC,2BAA2BoB,MAAM,8BAA8B,CAAC;EACpF;EACA,IAAI,OAAOtB,IAAI,KAAK,QAAQ,IACxB,OAAOA,IAAI,KAAK,QAAQ,IACxB,OAAOA,IAAI,KAAK,QAAQ,IACxB,OAAOA,IAAI,KAAK,SAAS,IACzBuB,KAAK,CAACC,OAAO,CAACxB,IAAI,CAAC,EAAE;IACrB,OAAOA,IAAI;EACf;EACA,MAAM,IAAIE,KAAK,CAAC,2BAA2BoB,MAAM,sCAAsC,CAAC;AAC5F;AACA;AACA;AACA;AACA,SAASG,WAAWA,CAACzB,IAAI,EAAE;EACvB,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC1B,OAAOA,IAAI;EACf;EACA,IAAI,OAAOkB,WAAW,KAAK,WAAW,IAAIlB,IAAI,YAAYkB,WAAW,EAAE;IACnE,MAAM,IAAIhB,KAAK,CAAC,iEAAiE,CAAC;EACtF;EACA,IAAI,OAAOkB,IAAI,KAAK,WAAW,IAAIpB,IAAI,YAAYoB,IAAI,EAAE;IACrD,MAAM,IAAIlB,KAAK,CAAC,0DAA0D,CAAC;EAC/E;EACA,OAAOwB,IAAI,CAACC,SAAS,CAACN,WAAW,CAACrB,IAAI,EAAE,MAAM,CAAC,CAAC;AACpD;AACA;AACA;AACA;AACA,SAASM,iBAAiBA,CAACC,YAAY,EAAEP,IAAI,EAAE;EAC3C,IAAIA,IAAI,KAAK,IAAI,EAAE;IACf,OAAO,IAAI;EACf;EACA,QAAQO,YAAY;IAChB,KAAK,aAAa;MACd,OAAOU,kBAAkB,CAACjB,IAAI,CAAC;IACnC,KAAK,MAAM;MACP,OAAOmB,OAAO,CAACnB,IAAI,CAAC;IACxB,KAAK,MAAM;MACP,OAAOqB,WAAW,CAACrB,IAAI,CAAC;IAC5B,KAAK,MAAM;MACP,OAAOyB,WAAW,CAACzB,IAAI,CAAC;IAC5B;MACI,MAAM,IAAIE,KAAK,CAAC,6BAA6BK,YAAY,EAAE,CAAC;EACpE;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMqB,wBAAwB,CAAC;EAC3B;AACJ;AACA;EACIC,IAAI,GAAG,EAAE;EACT;AACJ;AACA;EACIC,gBAAgB,GAAG,IAAI;EACvB;AACJ;AACA;EACIC,MAAMA,CAACC,GAAG,EAAE;IACR,OAAO,IAAIjD,UAAU,CAAEY,QAAQ,IAAK;MAChC,MAAMsC,OAAO,GAAG,IAAIxC,WAAW,CAACuC,GAAG,EAAErC,QAAQ,CAAC;MAC9C,IAAI,CAACkC,IAAI,CAACK,IAAI,CAACD,OAAO,CAAC;MACvBtC,QAAQ,CAACkB,IAAI,CAAC;QAAEsB,IAAI,EAAE/C,aAAa,CAACgD;MAAK,CAAC,CAAC;MAC3C,OAAO,MAAM;QACTH,OAAO,CAACpC,UAAU,GAAG,IAAI;MAC7B,CAAC;IACL,CAAC,CAAC;EACN;EACA;AACJ;AACA;EACIwC,MAAMA,CAACC,KAAK,EAAE;IACV,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC3B,OAAO,IAAI,CAACT,IAAI,CAACU,MAAM,CAAEN,OAAO,IAAKA,OAAO,CAACvC,OAAO,CAACU,aAAa,KAAKkC,KAAK,CAAC;IACjF,CAAC,MACI,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE;MAClC,OAAO,IAAI,CAACT,IAAI,CAACU,MAAM,CAAEN,OAAO,IAAKK,KAAK,CAACL,OAAO,CAACvC,OAAO,CAAC,CAAC;IAChE,CAAC,MACI;MACD,OAAO,IAAI,CAACmC,IAAI,CAACU,MAAM,CAAEN,OAAO,IAAK,CAAC,CAACK,KAAK,CAACE,MAAM,IAAIP,OAAO,CAACvC,OAAO,CAAC8C,MAAM,KAAKF,KAAK,CAACE,MAAM,CAACC,WAAW,CAAC,CAAC,MACvG,CAACH,KAAK,CAACnC,GAAG,IAAI8B,OAAO,CAACvC,OAAO,CAACU,aAAa,KAAKkC,KAAK,CAACnC,GAAG,CAAC,CAAC;IACpE;EACJ;EACA;AACJ;AACA;AACA;EACImC,KAAKA,CAACA,KAAK,EAAE;IACT,MAAMI,OAAO,GAAG,IAAI,CAACL,MAAM,CAACC,KAAK,CAAC;IAClCI,OAAO,CAACC,OAAO,CAAEC,MAAM,IAAK;MACxB,MAAMC,KAAK,GAAG,IAAI,CAAChB,IAAI,CAACiB,OAAO,CAACF,MAAM,CAAC;MACvC,IAAIC,KAAK,KAAK,CAAC,CAAC,EAAE;QACd,IAAI,CAAChB,IAAI,CAACkB,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAC9B;IACJ,CAAC,CAAC;IACF,OAAOH,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIM,SAASA,CAACV,KAAK,EAAEW,WAAW,EAAE;IAC1BA,WAAW,KAAK,IAAI,CAACC,sBAAsB,CAACZ,KAAK,CAAC;IAClD,MAAMa,OAAO,GAAG,IAAI,CAACb,KAAK,CAACA,KAAK,CAAC;IACjC,IAAIa,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;MACpB,MAAM,IAAIlD,KAAK,CAAC,+CAA+C+C,WAAW,YAAYE,OAAO,CAACC,MAAM,YAAY,CAAC;IACrH;IACA,IAAID,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;MACtB,IAAIC,OAAO,GAAG,+CAA+CJ,WAAW,gBAAgB;MACxF,IAAI,IAAI,CAACpB,IAAI,CAACuB,MAAM,GAAG,CAAC,EAAE;QACtB;QACA,MAAME,QAAQ,GAAG,IAAI,CAACzB,IAAI,CAAC0B,GAAG,CAACC,eAAe,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;QAC1DJ,OAAO,IAAI,2BAA2BC,QAAQ,GAAG;MACrD;MACA,MAAM,IAAIpD,KAAK,CAACmD,OAAO,CAAC;IAC5B;IACA,OAAOF,OAAO,CAAC,CAAC,CAAC;EACrB;EACA;AACJ;AACA;AACA;EACIO,UAAUA,CAACpB,KAAK,EAAEW,WAAW,EAAE;IAC3BA,WAAW,KAAK,IAAI,CAACC,sBAAsB,CAACZ,KAAK,CAAC;IAClD,MAAMa,OAAO,GAAG,IAAI,CAACb,KAAK,CAACA,KAAK,CAAC;IACjC,IAAIa,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;MACpB,MAAM,IAAIlD,KAAK,CAAC,iDAAiD+C,WAAW,YAAYE,OAAO,CAACC,MAAM,GAAG,CAAC;IAC9G;EACJ;EACA;AACJ;AACA;EACIO,MAAMA,CAAC1D,IAAI,GAAG,CAAC,CAAC,EAAE;IACd,IAAI4B,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB;IACA;IACA,IAAI5B,IAAI,CAAC2D,eAAe,EAAE;MACtB/B,IAAI,GAAGA,IAAI,CAACU,MAAM,CAAEN,OAAO,IAAK,CAACA,OAAO,CAACrC,SAAS,CAAC;IACvD;IACA,IAAIiC,IAAI,CAACuB,MAAM,GAAG,CAAC,EAAE;MACjB;MACA,MAAME,QAAQ,GAAGzB,IAAI,CAAC0B,GAAG,CAACC,eAAe,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;MACrD,MAAM,IAAIvD,KAAK,CAAC,oCAAoC2B,IAAI,CAACuB,MAAM,KAAKE,QAAQ,EAAE,CAAC;IACnF;EACJ;EACAJ,sBAAsBA,CAACW,OAAO,EAAE;IAC5B,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MAC7B,OAAO,cAAcA,OAAO,EAAE;IAClC,CAAC,MACI,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MAClC,MAAMrB,MAAM,GAAGqB,OAAO,CAACrB,MAAM,IAAI,OAAO;MACxC,MAAMrC,GAAG,GAAG0D,OAAO,CAAC1D,GAAG,IAAI,OAAO;MAClC,OAAO,iBAAiBqC,MAAM,UAAUrC,GAAG,EAAE;IACjD,CAAC,MACI;MACD,OAAO,sBAAsB0D,OAAO,CAACC,IAAI,EAAE;IAC/C;EACJ;EACA,OAAOC,IAAI,YAAAC,iCAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAyFrC,wBAAwB;EAAA;EAC5H,OAAOsC,KAAK,kBAD8EtF,EAAE,CAAAuF,kBAAA;IAAAC,KAAA,EACYxC,wBAAwB;IAAAyC,OAAA,EAAxBzC,wBAAwB,CAAAmC;EAAA;AACpI;AACA;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KAH8F1F,EAAE,CAAA2F,iBAAA,CAGJ3C,wBAAwB,EAAc,CAAC;IACvHO,IAAI,EAAEtD;EACV,CAAC,CAAC;AAAA;AACV,SAAS2E,eAAeA,CAACgB,WAAW,EAAE;EAClC,MAAMrE,GAAG,GAAGqE,WAAW,CAAC9E,OAAO,CAACU,aAAa;EAC7C,MAAMoC,MAAM,GAAGgC,WAAW,CAAC9E,OAAO,CAAC8C,MAAM;EACzC,OAAO,GAAGA,MAAM,IAAIrC,GAAG,EAAE;AAC7B;AAEA,SAASsE,wBAAwBA,CAAA,EAAG;EAChC,OAAO,CACH7C,wBAAwB,EACxB;IAAE8C,OAAO,EAAErF,WAAW;IAAEsF,WAAW,EAAE/C;EAAyB,CAAC,EAC/D;IAAE8C,OAAO,EAAElF,qBAAqB;IAAEmF,WAAW,EAAE/C;EAAyB,CAAC,EACzE;IAAE8C,OAAO,EAAEpF,gCAAgC;IAAEsF,QAAQ,EAAE;EAAM,CAAC,CACjE;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,uBAAuB,CAAC;EAC1B,OAAOd,IAAI,YAAAe,gCAAAb,iBAAA;IAAA,YAAAA,iBAAA,IAAyFY,uBAAuB;EAAA;EAC3H,OAAOE,IAAI,kBAhC+EnG,EAAE,CAAAoG,gBAAA;IAAA7C,IAAA,EAgCS0C,uBAAuB;IAAAI,OAAA,GAAY1F,gBAAgB;EAAA;EACxJ,OAAO2F,IAAI,kBAjC+EtG,EAAE,CAAAuG,gBAAA;IAAAC,SAAA,EAiC6C,CAACX,wBAAwB,CAAC,CAAC,CAAC;IAAAQ,OAAA,GAAY1F,gBAAgB;EAAA;AACrM;AACA;EAAA,QAAA+E,SAAA,oBAAAA,SAAA,KAnC8F1F,EAAE,CAAA2F,iBAAA,CAmCJM,uBAAuB,EAAc,CAAC;IACtH1C,IAAI,EAAErD,QAAQ;IACduG,IAAI,EAAE,CAAC;MACCJ,OAAO,EAAE,CAAC1F,gBAAgB,CAAC;MAC3B6F,SAAS,EAAE,CAACX,wBAAwB,CAAC,CAAC;IAC1C,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASI,uBAAuB,EAAErF,qBAAqB,EAAEC,WAAW,EAAEgF,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}