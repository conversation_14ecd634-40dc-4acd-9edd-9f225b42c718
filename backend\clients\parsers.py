"""
Parsers para processar e validar dados coletados das APIs
"""

import asyncio
import json
import re
from datetime import datetime, timezone
from typing import Dict, Any, List

from pydantic import ValidationError
from .model import DadosFunding, DadosPresencaDigital
import logging

logger = logging.getLogger(__name__)


async def parse_dados_funding(dados_brutos: Dict[str, Any]) -> Dict[str, Any]:
    """
    Parser assíncrono para dados de funding coletados da API Perplexity.

    Args:
        dados_brutos: Dados brutos retornados da API

    Returns:
        Dict com dados processados e validados

    Raises:
        ValueError: Se os dados não puderem ser processados
    """
    try:
        # Se houver erro na resposta da API, retornar estrutura de erro
        if "erro" in dados_brutos:
            return {
                "error": True,
                "message": dados_brutos["erro"],
                "raw_data": dados_brutos,
                "processed_at": datetime.utcnow().isoformat()
            }

        # Processar e normalizar os dados - ASYNC
        dados_processados = await asyncio.to_thread(_processar_funding_data, dados_brutos)

        # Validar estrutura usando Pydantic - ASYNC
        try:
            dados_validados = await asyncio.to_thread(DadosFunding, **dados_processados)

            # Converter de volta para dict e adicionar metadados
            resultado = dados_validados.dict()
            resultado.update({
                "validation_status": "success",
                "processed_at": datetime.utcnow().isoformat(),
                "data_quality_score": await asyncio.to_thread(_calcular_score_qualidade_funding, dados_processados)
            })

            return resultado

        except ValidationError as e:
            logger.warning(f"Validação falhou para dados de funding: {e}")
            # Retornar dados processados mesmo com falha de validação
            return {
                "validation_status": "failed",
                "validation_errors": str(e),
                "processed_data": dados_processados,
                "processed_at": datetime.utcnow().isoformat(),
                "data_quality_score": await asyncio.to_thread(_calcular_score_qualidade_funding, dados_processados)
            }

    except Exception as e:
        logger.error(f"Erro ao processar dados de funding: {e}")
        return {
            "error": True,
            "message": f"Erro no processamento: {str(e)}",
            "raw_data": dados_brutos,
            "processed_at": datetime.utcnow().isoformat()
        }


def _processar_funding_data(dados: Dict[str, Any]) -> Dict[str, Any]:
    """Processa e normaliza dados específicos de funding"""

    resultado = {}

    # Processar resumo de funding
    if "resumo_funding" in dados:
        resultado["resumo_funding"] = _normalizar_resumo_funding(
            dados["resumo_funding"])
    else:
        resultado["resumo_funding"] = _resumo_funding_default()

    # Processar rodadas de investimento
    if "rodadas_investimento" in dados:
        resultado["rodadas_investimento"] = _normalizar_rodadas(
            dados["rodadas_investimento"])
    else:
        resultado["rodadas_investimento"] = []

    # Processar investidores principais
    if "investidores_principais" in dados:
        resultado["investidores_principais"] = _normalizar_investidores(
            dados["investidores_principais"])
    else:
        resultado["investidores_principais"] = _investidores_default()

    # Processar métricas de investimento
    if "metricas_investimento" in dados:
        resultado["metricas_investimento"] = _normalizar_metricas_funding(
            dados["metricas_investimento"])
    else:
        resultado["metricas_investimento"] = _metricas_funding_default()

    # Processar análise competitiva
    if "analise_competitiva_funding" in dados:
        resultado["analise_competitiva_funding"] = dados["analise_competitiva_funding"]
    else:
        resultado["analise_competitiva_funding"] = _analise_competitiva_default()

    # Processar perspectivas futuras
    if "perspectivas_futuras" in dados:
        resultado["perspectivas_futuras"] = dados["perspectivas_futuras"]
    else:
        resultado["perspectivas_futuras"] = _perspectivas_futuras_default()

    # Processar fontes de informação
    if "fontes_informacao" in dados:
        resultado["fontes_informacao"] = dados["fontes_informacao"]
    else:
        resultado["fontes_informacao"] = _fontes_informacao_default()

    return resultado


def _normalizar_resumo_funding(resumo: Dict[str, Any]) -> Dict[str, str]:
    """Normaliza dados do resumo de funding"""
    resultado = {}

    # Normalizar valores monetários
    if "total_captado" in resumo:
        resultado["total_captado"] = _normalizar_valor_monetario(
            resumo["total_captado"])
    else:
        resultado["total_captado"] = "Não disponível"

    if "numero_rodadas" in resumo:
        resultado["numero_rodadas"] = str(
            resumo["numero_rodadas"]) if resumo["numero_rodadas"] else "Não disponível"
    else:
        resultado["numero_rodadas"] = "Não disponível"

    if "ultimo_valuation" in resumo:
        resultado["ultimo_valuation"] = _normalizar_valor_monetario(
            resumo["ultimo_valuation"])
    else:
        resultado["ultimo_valuation"] = "Não disponível"

    if "status_funding" in resumo:
        resultado["status_funding"] = str(
            resumo["status_funding"]) if resumo["status_funding"] else "Não disponível"
    else:
        resultado["status_funding"] = "Não disponível"

    return resultado


def _normalizar_rodadas(rodadas: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Normaliza dados das rodadas de investimento"""
    if not isinstance(rodadas, list):
        return []

    resultado = []
    for rodada in rodadas:
        if isinstance(rodada, dict):
            rodada_normalizada = {}

            # Campos obrigatórios com fallback
            campos = [
                "rodada", "data", "valor", "valuation_pre", "valuation_pos",
                "lead_investor", "outros_investidores", "uso_capital"
            ]

            for campo in campos:
                if campo in rodada:
                    if campo == "valor" or "valuation" in campo:
                        rodada_normalizada[campo] = _normalizar_valor_monetario(
                            rodada[campo])
                    elif campo == "data":
                        rodada_normalizada[campo] = _normalizar_data(
                            rodada[campo])
                    elif campo == "outros_investidores":
                        rodada_normalizada[campo] = rodada[campo] if isinstance(
                            rodada[campo], list) else []
                    else:
                        rodada_normalizada[campo] = str(
                            rodada[campo]) if rodada[campo] else "Não disponível"
                else:
                    rodada_normalizada[campo] = "Não disponível" if campo != "outros_investidores" else [
                    ]

            resultado.append(rodada_normalizada)

    return resultado


def _normalizar_investidores(investidores: Dict[str, Any]) -> Dict[str, List[str]]:
    """Normaliza dados dos investidores principais"""
    categorias = [
        "venture_capital", "private_equity", "anjos_investidores",
        "investidores_corporativos", "fundos_governo"
    ]

    resultado = {}
    for categoria in categorias:
        if categoria in investidores and isinstance(investidores[categoria], list):
            resultado[categoria] = [str(inv)
                                    for inv in investidores[categoria] if inv]
        else:
            resultado[categoria] = []

    return resultado


def _normalizar_metricas_funding(metricas: Dict[str, Any]) -> Dict[str, str]:
    """Normaliza métricas de investimento"""
    campos = [
        "ticket_medio_rodada", "tempo_medio_entre_rodadas", "crescimento_valuation",
        "burn_rate_estimado", "runway_estimado"
    ]

    resultado = {}
    for campo in campos:
        if campo in metricas:
            if "ticket_medio" in campo or "burn_rate" in campo:
                resultado[campo] = _normalizar_valor_monetario(metricas[campo])
            else:
                resultado[campo] = str(
                    metricas[campo]) if metricas[campo] else "Não disponível"
        else:
            resultado[campo] = "Não disponível"

    return resultado


def _normalizar_valor_monetario(valor: Any) -> str:
    """Normaliza valores monetários para formato consistente"""
    if not valor or valor == "Não disponível":
        return "Não disponível"

    valor_str = str(valor).strip()

    # Se já está no formato correto, retornar
    if valor_str.startswith(('$', 'R$', '€', '£')) or valor_str == "Não disponível":
        return valor_str

    # Tentar extrair números e símbolos de moeda
    match = re.search(
        r'[\$€£R\$]*\s*([0-9,.]+(M|B|K|milhões|bilhões)?)', valor_str, re.IGNORECASE)
    if match:
        return f"${match.group(1)}"

    return valor_str


def _normalizar_data(data: Any) -> str:
    """Normaliza datas para formato MM/AAAA"""
    if not data:
        return "Não disponível"

    data_str = str(data).strip()

    # Se já está no formato MM/AAAA, retornar
    if re.match(r'\d{2}/\d{4}', data_str):
        return data_str

    # Tentar extrair mês e ano
    match = re.search(r'(\d{1,2})[/-](\d{4})', data_str)
    if match:
        mes = match.group(1).zfill(2)
        ano = match.group(2)
        return f"{mes}/{ano}"

    # Tentar extrair só o ano
    match = re.search(r'(\d{4})', data_str)
    if match:
        return f"--/{match.group(1)}"

    return data_str


def _calcular_score_qualidade_funding(dados: Dict[str, Any]) -> float:
    """Calcula score de qualidade dos dados de funding (0-100)"""
    score = 0.0
    peso_total = 0.0

    # Peso para cada seção
    pesos = {
        "resumo_funding": 0.25,
        "rodadas_investimento": 0.30,
        "investidores_principais": 0.20,
        "metricas_investimento": 0.15,
        "fontes_informacao": 0.10
    }

    for secao, peso in pesos.items():
        if secao in dados:
            peso_total += peso

            if secao == "resumo_funding":
                # Verificar completude do resumo
                campos_preenchidos = sum(
                    1 for v in dados[secao].values() if v != "Não disponível")
                score += (campos_preenchidos / len(dados[secao])) * peso * 100

            elif secao == "rodadas_investimento":
                # Verificar se há rodadas e se estão completas
                if isinstance(dados[secao], list) and len(dados[secao]) > 0:
                    score += peso * 100

            elif secao == "investidores_principais":
                # Verificar se há investidores listados
                total_investidores = sum(len(inv_list)
                                         for inv_list in dados[secao].values())
                if total_investidores > 0:
                    score += peso * 100

            elif secao == "fontes_informacao":
                # Verificar confiabilidade das fontes
                if "confiabilidade_dados" in dados[secao]:
                    conf = dados[secao]["confiabilidade_dados"].lower()
                    if conf == "alta":
                        score += peso * 100
                    elif conf == "média":
                        score += peso * 60
                    else:
                        score += peso * 30

    # Normalizar pelo peso total considerado
    return round(score / peso_total if peso_total > 0 else 0.0, 2)


# Funções para valores default
def _resumo_funding_default() -> Dict[str, str]:
    return {
        "total_captado": "Não disponível",
        "numero_rodadas": "Não disponível",
        "ultimo_valuation": "Não disponível",
        "status_funding": "Não disponível"
    }


def _investidores_default() -> Dict[str, List[str]]:
    return {
        "venture_capital": [],
        "private_equity": [],
        "anjos_investidores": [],
        "investidores_corporativos": [],
        "fundos_governo": []
    }


def _metricas_funding_default() -> Dict[str, str]:
    return {
        "ticket_medio_rodada": "Não disponível",
        "tempo_medio_entre_rodadas": "Não disponível",
        "crescimento_valuation": "Não disponível",
        "burn_rate_estimado": "Não disponível",
        "runway_estimado": "Não disponível"
    }


def _analise_competitiva_default() -> Dict[str, str]:
    return {
        "comparacao_setor": "Não disponível",
        "benchmark_concorrentes": "Não disponível",
        "posicionamento_mercado": "Não disponível"
    }


def _perspectivas_futuras_default() -> Dict[str, Any]:
    return {
        "proxima_rodada_estimada": "Não disponível",
        "necessidades_capital": "Não disponível",
        "potencial_ipo_aquisicao": "Não disponível",
        "riscos_funding": []
    }


def _fontes_informacao_default() -> Dict[str, Any]:
    return {
        "fontes_utilizadas": [],
        "confiabilidade_dados": "Baixa",
        "data_ultima_atualizacao": "Não disponível",
        "observacoes": "Dados limitados disponíveis"
    }


async def parse_dados_presenca_digital(dados_brutos: Dict[str, Any]) -> Dict[str, Any]:
    """
    Parser assíncrono para dados de presença digital coletados da API Perplexity.

    Args:
        dados_brutos: Dados brutos retornados da API

    Returns:
        Dict com dados processados e validados

    Raises:
        ValueError: Se os dados não puderem ser processados
    """
    try:
        # Se houver erro na resposta da API, retornar estrutura de erro
        if "erro" in dados_brutos:
            return {
                "error": True,
                "message": dados_brutos["erro"],
                "raw_data": dados_brutos,
                "processed_at": datetime.utcnow().isoformat()
            }

        # Processar e normalizar os dados - ASYNC
        dados_processados = await asyncio.to_thread(_processar_presenca_digital_data, dados_brutos)

        # Validar estrutura usando Pydantic - ASYNC
        try:
            dados_validados = await asyncio.to_thread(DadosPresencaDigital, **dados_processados)

            # Converter de volta para dict e adicionar metadados
            resultado = dados_validados.dict()
            resultado.update({
                "validation_status": "success",
                "processed_at": datetime.utcnow().isoformat(),
                "data_quality_score": await asyncio.to_thread(_calcular_score_qualidade_presenca_digital, dados_processados)
            })

            return resultado

        except ValidationError as e:
            logger.warning(
                f"Validação falhou para dados de presença digital: {e}")
            # Retornar dados processados mesmo com falha de validação
            return {
                "validation_status": "failed",
                "validation_errors": str(e),
                "processed_data": dados_processados,
                "processed_at": datetime.utcnow().isoformat(),
                "data_quality_score": await asyncio.to_thread(_calcular_score_qualidade_presenca_digital, dados_processados)
            }

    except Exception as e:
        logger.error(f"Erro ao processar dados de presença digital: {e}")
        return {
            "error": True,
            "message": f"Erro no processamento: {str(e)}",
            "raw_data": dados_brutos,
            "processed_at": datetime.utcnow().isoformat()
        }


def _processar_presenca_digital_data(dados: Dict[str, Any]) -> Dict[str, Any]:
    """Processa e normaliza dados específicos de presença digital"""

    resultado = {}

    # Processar resumo de presença digital
    if "resumo_presenca_digital" in dados:
        resultado["resumo_presenca_digital"] = _normalizar_resumo_presenca_digital(
            dados["resumo_presenca_digital"])
    else:
        resultado["resumo_presenca_digital"] = _resumo_presenca_digital_default()

    # Processar métricas SEO
    if "metricas_seo" in dados:
        resultado["metricas_seo"] = _normalizar_metricas_seo(
            dados["metricas_seo"])
    else:
        resultado["metricas_seo"] = _metricas_seo_default()

    # Processar principais keywords
    if "principais_keywords" in dados:
        resultado["principais_keywords"] = _normalizar_keywords(
            dados["principais_keywords"])
    else:
        resultado["principais_keywords"] = []

    # Processar redes sociais
    if "redes_sociais" in dados:
        resultado["redes_sociais"] = _normalizar_redes_sociais(
            dados["redes_sociais"])
    else:
        resultado["redes_sociais"] = _redes_sociais_default()

    # Processar análise do site
    if "analise_site" in dados:
        resultado["analise_site"] = dados["analise_site"]
    else:
        resultado["analise_site"] = _analise_site_default()

    # Processar conteúdo digital
    if "conteudo_digital" in dados:
        resultado["conteudo_digital"] = dados["conteudo_digital"]
    else:
        resultado["conteudo_digital"] = _conteudo_digital_default()

    # Processar reputação online
    if "reputacao_online" in dados:
        resultado["reputacao_online"] = dados["reputacao_online"]
    else:
        resultado["reputacao_online"] = _reputacao_online_default()

    # Processar competitividade digital
    if "competitividade_digital" in dados:
        resultado["competitividade_digital"] = dados["competitividade_digital"]
    else:
        resultado["competitividade_digital"] = _competitividade_digital_default()

    # Processar recomendações estratégicas
    if "recomendacoes_estrategicas" in dados:
        resultado["recomendacoes_estrategicas"] = dados["recomendacoes_estrategicas"]
    else:
        resultado["recomendacoes_estrategicas"] = _recomendacoes_estrategicas_default()

    # Processar fontes de análise
    if "fontes_analise" in dados:
        resultado["fontes_analise"] = dados["fontes_analise"]
    else:
        resultado["fontes_analise"] = _fontes_analise_default()

    return resultado


def _normalizar_resumo_presenca_digital(resumo: Dict[str, Any]) -> Dict[str, str]:
    """Normaliza dados do resumo de presença digital"""
    resultado = {}

    # Normalizar Domain Authority
    if "domain_authority" in resumo:
        resultado["domain_authority"] = _normalizar_score(
            resumo["domain_authority"])
    else:
        resultado["domain_authority"] = "Não disponível"

    if "ranking_seo_geral" in resumo:
        resultado["ranking_seo_geral"] = str(
            resumo["ranking_seo_geral"]) if resumo["ranking_seo_geral"] else "Não disponível"
    else:
        resultado["ranking_seo_geral"] = "Não disponível"

    if "trafego_mensal_estimado" in resumo:
        resultado["trafego_mensal_estimado"] = _normalizar_numero_visitantes(
            resumo["trafego_mensal_estimado"])
    else:
        resultado["trafego_mensal_estimado"] = "Não disponível"

    if "score_presenca_digital" in resumo:
        resultado["score_presenca_digital"] = _normalizar_score(
            resumo["score_presenca_digital"])
    else:
        resultado["score_presenca_digital"] = "Não disponível"

    return resultado


def _normalizar_metricas_seo(metricas: Dict[str, Any]) -> Dict[str, str]:
    """Normaliza métricas de SEO"""
    campos_score = ["domain_authority", "page_authority"]
    campos_numero = ["backlinks_totais", "dominios_referencia",
                     "keywords_organicas", "keywords_top_10"]
    campos_trafego = ["trafego_organico_mensal"]
    campos_valor = ["valor_trafego_organico"]

    resultado = {}

    for campo in campos_score + campos_numero + campos_trafego + campos_valor:
        if campo in metricas:
            if campo in campos_score:
                resultado[campo] = _normalizar_score(metricas[campo])
            elif campo in campos_numero:
                resultado[campo] = _normalizar_numero(metricas[campo])
            elif campo in campos_trafego:
                resultado[campo] = _normalizar_numero_visitantes(
                    metricas[campo])
            elif campo in campos_valor:
                resultado[campo] = _normalizar_valor_monetario(metricas[campo])
            else:
                resultado[campo] = str(
                    metricas[campo]) if metricas[campo] else "Não disponível"
        else:
            resultado[campo] = "Não disponível"

    return resultado


def _normalizar_keywords(keywords: List[Dict[str, Any]]) -> List[Dict[str, str]]:
    """Normaliza dados das keywords principais"""
    if not isinstance(keywords, list):
        return []

    resultado = []
    for keyword in keywords:
        if isinstance(keyword, dict):
            keyword_normalizada = {}

            campos = ["keyword", "posicao", "volume_busca", "dificuldade"]

            for campo in campos:
                if campo in keyword:
                    if campo == "posicao":
                        keyword_normalizada[campo] = _normalizar_posicao(
                            keyword[campo])
                    elif campo == "volume_busca":
                        keyword_normalizada[campo] = _normalizar_numero(
                            keyword[campo])
                    elif campo == "dificuldade":
                        keyword_normalizada[campo] = _normalizar_score(
                            keyword[campo])
                    else:
                        keyword_normalizada[campo] = str(
                            keyword[campo]) if keyword[campo] else "Não disponível"
                else:
                    keyword_normalizada[campo] = "Não disponível"

            resultado.append(keyword_normalizada)

    return resultado


def _normalizar_redes_sociais(redes: Dict[str, Any]) -> Dict[str, Dict[str, str]]:
    """Normaliza dados das redes sociais"""
    plataformas = ["linkedin", "instagram", "twitter", "facebook", "youtube"]

    resultado = {}
    for plataforma in plataformas:
        if plataforma in redes and isinstance(redes[plataforma], dict):
            resultado[plataforma] = _normalizar_rede_social_individual(
                redes[plataforma])
        else:
            resultado[plataforma] = _rede_social_default()

    return resultado


def _normalizar_rede_social_individual(rede: Dict[str, Any]) -> Dict[str, str]:
    """Normaliza dados de uma rede social específica"""
    resultado = {}

    campos = ["seguidores", "engajamento_medio",
              "frequencia_posts", "qualidade_conteudo"]
    # Para YouTube, pode ter "inscritos" e "visualizacoes_totais"
    if "inscritos" in rede:
        campos.extend(
            ["inscritos", "visualizacoes_totais", "frequencia_videos"])

    for campo in campos:
        if campo in rede:
            if campo in ["seguidores", "inscritos", "visualizacoes_totais"]:
                resultado[campo] = _normalizar_numero(rede[campo])
            elif "engajamento" in campo:
                resultado[campo] = _normalizar_percentual(rede[campo])
            else:
                resultado[campo] = str(
                    rede[campo]) if rede[campo] else "Não disponível"
        else:
            resultado[campo] = "Não disponível"

    return resultado


def _normalizar_score(score: Any) -> str:
    """Normaliza scores (0-100)"""
    if not score or score == "Não disponível":
        return "Não disponível"

    score_str = str(score).strip()

    # Extrair número do score
    match = re.search(r'(\d+)', score_str)
    if match:
        numero = int(match.group(1))
        # Garantir que está entre 0-100
        numero = max(0, min(100, numero))
        return str(numero)

    return score_str


def _normalizar_numero(numero: Any) -> str:
    """Normaliza números gerais (ex: backlinks, seguidores)"""
    if not numero or numero == "Não disponível":
        return "Não disponível"

    numero_str = str(numero).strip()

    # Remover pontuação e extrair números
    match = re.search(
        r'([0-9,.]+(K|M|B|mil|milhões|bilhões)?)', numero_str, re.IGNORECASE)
    if match:
        return match.group(1)

    return numero_str


def _normalizar_numero_visitantes(visitantes: Any) -> str:
    """Normaliza números de visitantes/tráfego"""
    if not visitantes or visitantes == "Não disponível":
        return "Não disponível"

    visitantes_str = str(visitantes).strip()

    # Extrair números com sufixos
    match = re.search(
        r'([0-9,.]+(K|M|B|mil|milhões|bilhões)?\s*(visitantes|visitas|pageviews)?)', visitantes_str, re.IGNORECASE)
    if match:
        return match.group(1)

    return visitantes_str


def _normalizar_percentual(percentual: Any) -> str:
    """Normaliza percentuais"""
    if not percentual or percentual == "Não disponível":
        return "Não disponível"

    percentual_str = str(percentual).strip()

    # Extrair percentual
    match = re.search(r'(\d+(?:\.\d+)?%?)', percentual_str)
    if match:
        valor = match.group(1)
        if not valor.endswith('%'):
            valor += '%'
        return valor

    return percentual_str


def _normalizar_posicao(posicao: Any) -> str:
    """Normaliza posições de ranking"""
    if not posicao or posicao == "Não disponível":
        return "Não disponível"

    posicao_str = str(posicao).strip()

    # Extrair número da posição
    match = re.search(r'(\d+)', posicao_str)
    if match:
        return match.group(1)

    return posicao_str


def _calcular_score_qualidade_presenca_digital(dados: Dict[str, Any]) -> float:
    """Calcula score de qualidade dos dados de presença digital (0-100)"""
    score = 0.0
    peso_total = 0.0

    # Peso para cada seção
    pesos = {
        "resumo_presenca_digital": 0.15,
        "metricas_seo": 0.25,
        "principais_keywords": 0.15,
        "redes_sociais": 0.20,
        "analise_site": 0.10,
        "fontes_analise": 0.15
    }

    for secao, peso in pesos.items():
        if secao in dados:
            peso_total += peso

            if secao == "resumo_presenca_digital":
                # Verificar completude do resumo
                campos_preenchidos = sum(
                    1 for v in dados[secao].values() if v != "Não disponível")
                score += (campos_preenchidos / len(dados[secao])) * peso * 100

            elif secao == "metricas_seo":
                # Verificar completude das métricas SEO
                campos_preenchidos = sum(
                    1 for v in dados[secao].values() if v != "Não disponível")
                score += (campos_preenchidos / len(dados[secao])) * peso * 100

            elif secao == "principais_keywords":
                # Verificar se há keywords listadas
                if isinstance(dados[secao], list) and len(dados[secao]) > 0:
                    score += peso * 100

            elif secao == "redes_sociais":
                # Verificar presença em redes sociais
                redes_ativas = sum(1 for rede in dados[secao].values()
                                   if any(v != "Não disponível" for v in rede.values()))
                total_redes = len(dados[secao])
                if total_redes > 0:
                    score += (redes_ativas / total_redes) * peso * 100

            elif secao == "fontes_analise":
                # Verificar confiabilidade das fontes
                if "confiabilidade_dados" in dados[secao]:
                    conf = dados[secao]["confiabilidade_dados"].lower()
                    if conf == "alta":
                        score += peso * 100
                    elif conf == "média":
                        score += peso * 60
                    else:
                        score += peso * 30

    # Normalizar pelo peso total considerado
    return round(score / peso_total if peso_total > 0 else 0.0, 2)


# Funções para valores default de presença digital
def _resumo_presenca_digital_default() -> Dict[str, str]:
    return {
        "domain_authority": "Não disponível",
        "ranking_seo_geral": "Não disponível",
        "trafego_mensal_estimado": "Não disponível",
        "score_presenca_digital": "Não disponível"
    }


def _metricas_seo_default() -> Dict[str, str]:
    return {
        "domain_authority": "Não disponível",
        "page_authority": "Não disponível",
        "backlinks_totais": "Não disponível",
        "dominios_referencia": "Não disponível",
        "keywords_organicas": "Não disponível",
        "keywords_top_10": "Não disponível",
        "trafego_organico_mensal": "Não disponível",
        "valor_trafego_organico": "Não disponível"
    }


def _redes_sociais_default() -> Dict[str, Dict[str, str]]:
    rede_default = _rede_social_default()
    return {
        "linkedin": rede_default.copy(),
        "instagram": rede_default.copy(),
        "twitter": rede_default.copy(),
        "facebook": rede_default.copy(),
        "youtube": rede_default.copy()
    }


def _rede_social_default() -> Dict[str, str]:
    return {
        "seguidores": "Não disponível",
        "engajamento_medio": "Não disponível",
        "frequencia_posts": "Não disponível",
        "qualidade_conteudo": "Não disponível"
    }


def _analise_site_default() -> Dict[str, str]:
    return {
        "velocidade_carregamento": "Não disponível",
        "mobile_friendly": "Não disponível",
        "certificado_ssl": "Não disponível",
        "estrutura_urls": "Não disponível",
        "meta_tags": "Não disponível",
        "schema_markup": "Não disponível",
        "analytics_implementado": "Não disponível"
    }


def _conteudo_digital_default() -> Dict[str, str]:
    return {
        "blog_ativo": "Não disponível",
        "frequencia_blog": "Não disponível",
        "qualidade_conteudo_blog": "Não disponível",
        "materiais_educativos": "Não disponível",
        "webinars_eventos": "Não disponível",
        "podcasts": "Não disponível"
    }


def _reputacao_online_default() -> Dict[str, Any]:
    return {
        "google_reviews": {
            "nota_media": "Não disponível",
            "numero_avaliacoes": "Não disponível",
            "tendencia_avaliacoes": "Não disponível"
        },
        "mencoes_imprensa": "Não disponível",
        "premios_reconhecimentos": "Não disponível",
        "sentiment_geral": "Não disponível"
    }


def _competitividade_digital_default() -> Dict[str, Any]:
    return {
        "ranking_vs_concorrentes": "Não disponível",
        "gaps_identificados": [],
        "oportunidades_melhoria": [],
        "benchmark_setor": "Não disponível"
    }


def _recomendacoes_estrategicas_default() -> Dict[str, Any]:
    return {
        "prioridade_alta": [],
        "prioridade_media": [],
        "prioridade_baixa": [],
        "investimento_estimado": "Não disponível",
        "roi_esperado": "Não disponível"
    }


def _fontes_analise_default() -> Dict[str, Any]:
    return {
        "ferramentas_utilizadas": [],
        "confiabilidade_dados": "Baixa",
        "data_analise": "Não disponível",
        "limitacoes": "Dados limitados disponíveis",
        "observacoes": "Análise baseada em dados públicos limitados"
    }


async def parse_dados_parcerias(dados_brutos: Dict[str, Any]) -> Dict[str, Any]:
    """
    Parser para dados de parcerias coletados da API Perplexity.

    Args:
        dados_brutos: Dados brutos retornados da API

    Returns:
        Dict com dados processados e validados

    Raises:
        ValueError: Se os dados não puderem ser processados
    """
    try:
        # Se houver erro na resposta da API, retornar estrutura de erro
        if "erro" in dados_brutos:
            return {
                "error": True,
                "message": dados_brutos["erro"],
                "raw_data": dados_brutos,
                "processed_at": datetime.utcnow().isoformat()
            }

        # Processar e normalizar os dados - ASYNC
        dados_processados = await asyncio.to_thread(_processar_parcerias_data, dados_brutos)

        # Adicionar metadados de processamento
        resultado = dados_processados.copy()
        resultado.update({
            "validation_status": "success",
            "processed_at": datetime.utcnow().isoformat(),
            "data_quality_score": await asyncio.to_thread(_calcular_score_qualidade_parcerias, dados_processados)
        })

        return resultado

    except Exception as e:
        logger.error(f"Erro ao processar dados de parcerias: {e}")
        return {
            "error": True,
            "message": f"Erro no processamento: {str(e)}",
            "raw_data": dados_brutos,
            "processed_at": datetime.utcnow().isoformat()
        }


def _processar_parcerias_data(dados: Dict[str, Any]) -> Dict[str, Any]:
    """Processa e normaliza dados específicos de parcerias"""
    resultado = {}

    # Processar resumo de parcerias
    if "resumo_parcerias" in dados:
        resultado["resumo_parcerias"] = _normalizar_resumo_parcerias(
            dados["resumo_parcerias"])
    else:
        resultado["resumo_parcerias"] = _resumo_parcerias_default()

    # Processar tipos de parceiros
    if "tipos_parceiros" in dados:
        resultado["tipos_parceiros"] = _normalizar_tipos_parceiros(
            dados["tipos_parceiros"])
    else:
        resultado["tipos_parceiros"] = _tipos_parceiros_default()

    # Processar estratégia de parcerias
    if "estrategia_parcerias" in dados:
        resultado["estrategia_parcerias"] = dados["estrategia_parcerias"]
    else:
        resultado["estrategia_parcerias"] = _estrategia_parcerias_default()

    # Processar integrações tecnológicas
    if "integracoes_tecnologicas" in dados:
        resultado["integracoes_tecnologicas"] = dados["integracoes_tecnologicas"]
    else:
        resultado["integracoes_tecnologicas"] = _integracoes_tecnologicas_default()

    # Processar casos de sucesso
    if "cases_sucesso_parcerias" in dados:
        resultado["cases_sucesso_parcerias"] = _normalizar_cases_sucesso(
            dados["cases_sucesso_parcerias"])
    else:
        resultado["cases_sucesso_parcerias"] = []

    # Processar análise competitiva
    if "analise_competitiva_parcerias" in dados:
        resultado["analise_competitiva_parcerias"] = dados["analise_competitiva_parcerias"]
    else:
        resultado["analise_competitiva_parcerias"] = _analise_competitiva_parcerias_default()

    # Processar métricas
    if "metricas_parcerias" in dados:
        resultado["metricas_parcerias"] = _normalizar_metricas_parcerias(
            dados["metricas_parcerias"])
    else:
        resultado["metricas_parcerias"] = _metricas_parcerias_default()

    # Processar fontes de informação
    if "fontes_informacao" in dados:
        resultado["fontes_informacao"] = dados["fontes_informacao"]
    else:
        resultado["fontes_informacao"] = _fontes_informacao_default()

    return resultado


def _normalizar_resumo_parcerias(resumo: Dict[str, Any]) -> Dict[str, str]:
    """Normaliza dados do resumo de parcerias"""
    resultado = {}

    campos = ["modelo_parcerias", "maturidade_programa",
              "foco_principal", "abrangencia_geografica"]

    for campo in campos:
        if campo in resumo:
            resultado[campo] = str(
                resumo[campo]) if resumo[campo] else "Não disponível"
        else:
            resultado[campo] = "Não disponível"

    return resultado


def _normalizar_tipos_parceiros(tipos: Dict[str, Any]) -> Dict[str, List[str]]:
    """Normaliza dados dos tipos de parceiros"""
    categorias = ["tecnologicos", "comerciais",
                  "estrategicos", "canais_distribuicao", "integradores"]

    resultado = {}
    for categoria in categorias:
        if categoria in tipos and isinstance(tipos[categoria], list):
            resultado[categoria] = [str(parceiro)
                                    for parceiro in tipos[categoria] if parceiro]
        else:
            resultado[categoria] = []

    return resultado


def _normalizar_cases_sucesso(cases: List[Dict[str, Any]]) -> List[Dict[str, str]]:
    """Normaliza dados dos cases de sucesso de parcerias"""
    if not isinstance(cases, list):
        return []

    resultado = []
    for case in cases:
        if isinstance(case, dict):
            case_normalizado = {}
            campos = ["parceiro", "tipo_parceria",
                      "resultado_obtido", "valor_gerado"]

            for campo in campos:
                if campo in case:
                    case_normalizado[campo] = str(
                        case[campo]) if case[campo] else "Não disponível"
                else:
                    case_normalizado[campo] = "Não disponível"

            resultado.append(case_normalizado)

    return resultado


def _normalizar_metricas_parcerias(metricas: Dict[str, Any]) -> Dict[str, str]:
    """Normaliza métricas de parcerias"""
    campos = ["receita_via_parceiros_estimada", "numero_parceiros_ativos",
              "crescimento_parcerias", "roi_programa_parceiros"]

    resultado = {}
    for campo in campos:
        if campo in metricas:
            if "receita" in campo or "roi" in campo:
                resultado[campo] = _normalizar_percentual(metricas[campo])
            elif "numero" in campo:
                resultado[campo] = _normalizar_numero(metricas[campo])
            else:
                resultado[campo] = str(
                    metricas[campo]) if metricas[campo] else "Não disponível"
        else:
            resultado[campo] = "Não disponível"

    return resultado


def _calcular_score_qualidade_parcerias(dados: Dict[str, Any]) -> float:
    """Calcula score de qualidade dos dados de parcerias (0-100)"""
    score = 0.0
    peso_total = 0.0

    pesos = {
        "resumo_parcerias": 0.20,
        "tipos_parceiros": 0.25,
        "estrategia_parcerias": 0.15,
        "integracoes_tecnologicas": 0.15,
        "cases_sucesso_parcerias": 0.15,
        "metricas_parcerias": 0.10
    }

    for secao, peso in pesos.items():
        if secao in dados:
            peso_total += peso

            if secao == "resumo_parcerias":
                campos_preenchidos = sum(
                    1 for v in dados[secao].values() if v != "Não disponível")
                score += (campos_preenchidos / len(dados[secao])) * peso * 100

            elif secao == "tipos_parceiros":
                total_parceiros = sum(len(parceiros)
                                      for parceiros in dados[secao].values())
                if total_parceiros > 0:
                    score += peso * 100

            elif secao in ["estrategia_parcerias", "integracoes_tecnologicas"]:
                if isinstance(dados[secao], dict):
                    campos_preenchidos = sum(1 for v in dados[secao].values()
                                             if v != "Não disponível" and v != [] and v)
                    if len(dados[secao]) > 0:
                        score += (campos_preenchidos /
                                  len(dados[secao])) * peso * 100

            elif secao == "cases_sucesso_parcerias":
                if isinstance(dados[secao], list) and len(dados[secao]) > 0:
                    score += peso * 100

            elif secao == "metricas_parcerias":
                campos_preenchidos = sum(
                    1 for v in dados[secao].values() if v != "Não disponível")
                score += (campos_preenchidos / len(dados[secao])) * peso * 100

    return round(score / peso_total if peso_total > 0 else 0.0, 2)


# Funções default para parcerias
def _resumo_parcerias_default() -> Dict[str, str]:
    return {
        "modelo_parcerias": "Não disponível",
        "maturidade_programa": "Não disponível",
        "foco_principal": "Não disponível",
        "abrangencia_geografica": "Não disponível"
    }


def _tipos_parceiros_default() -> Dict[str, List[str]]:
    return {
        "tecnologicos": [],
        "comerciais": [],
        "estrategicos": [],
        "canais_distribuicao": [],
        "integradores": []
    }


def _estrategia_parcerias_default() -> Dict[str, Any]:
    return {
        "criterios_selecao": "Não disponível",
        "processo_onboarding": "Não disponível",
        "suporte_parceiros": "Não disponível",
        "beneficios_oferecidos": []
    }


def _integracoes_tecnologicas_default() -> Dict[str, Any]:
    return {
        "tipos_integracoes": [],
        "apis_conectadas": [],
        "marketplaces": [],
        "facilidade_integracao": "Não disponível"
    }


def _analise_competitiva_parcerias_default() -> Dict[str, Any]:
    return {
        "benchmark_concorrentes": "Não disponível",
        "gaps_identificados": [],
        "oportunidades_parcerias": [],
        "ameacas_parcerias": []
    }


def _metricas_parcerias_default() -> Dict[str, str]:
    return {
        "receita_via_parceiros_estimada": "Não disponível",
        "numero_parceiros_ativos": "Não disponível",
        "crescimento_parcerias": "Não disponível",
        "roi_programa_parceiros": "Não disponível"
    }

# Funções default para modelo de negócio


def _get_default_modelo_negocio_secao(secao: str) -> Dict[str, Any]:
    defaults = {
        "classificacao_modelo": {
            "tipo_principal": "Não identificado",
            "subtipos_detalhados": {},
            "modelo_hibrido": {"e_hibrido": "Não", "combinacoes": []},
            "complexidade_modelo": {"nivel": "Não avaliado", "score_complexidade": "0"},
            "maturidade_digital": "Não identificado"
        },
        "proposta_valor": {
            "valor_principal": "Não identificado",
            "problemas_resolvidos": {"primarios": [], "secundarios": []},
            "beneficios_entregues": {"tangíveis": [], "intangíveis": []},
            "diferencial_competitivo": {"principal": "Não identificado"}
        },
        "benchmarking_setor": {
            "comparacao_modelos": "Não disponível",
            "melhores_praticas": [],
            "gaps_mercado": []
        }
    }
    return defaults.get(secao, {})


def _unit_economics_default() -> Dict[str, str]:
    return {
        "cac": "Não disponível",
        "ltv": "Não disponível",
        "ltv_cac_ratio": "Não disponível",
        "payback_period": "Não disponível",
        "gross_margin": "Não disponível"
    }


def _get_default_metricas_secao(secao: str) -> Dict[str, str]:
    defaults = {
        "crescimento": {
            "growth_rate": "Não disponível",
            "retention_rate": "Não disponível",
            "expansion_rate": "Não disponível"
        },
        "operacionais": {
            "capital_efficiency": "Não disponível",
            "working_capital": "Não disponível",
            "asset_turnover": "Não disponível"
        },
        "mercado": {
            "market_share": "Não disponível",
            "tam_sam_som": "Não disponível",
            "penetracao_mercado": "Não disponível"
        }
    }
    return defaults.get(secao, {})


def _analise_escalabilidade_default() -> Dict[str, str]:
    return {
        "nivel_escalabilidade": "Não avaliado",
        "score_escalabilidade": "0",
        "justificativa": "Não disponível"
    }


def _get_default_escalabilidade_secao(secao: str) -> Dict[str, Any]:
    defaults = {
        "fatores_limitantes": {
            "limitantes_internos": [],
            "limitantes_externos": [],
            "gargalos_principais": []
        },
        "alavancas_crescimento": {
            "alavancas_primarias": [],
            "alavancas_secundarias": [],
            "network_effects": "Não identificado"
        },
        "requisitos_escala": {
            "investimentos_necessarios": "Não disponível",
            "recursos_criticos": "Não disponível",
            "timeline_escala": "Não disponível"
        }
    }
    return defaults.get(secao, {})

# Funções default para pricing


def _get_default_pricing_secao(secao: str) -> Dict[str, Any]:
    defaults = {
        "resumo_pricing": {
            "estrategia_principal": "Não identificado",
            "modelo_pricing": "Não identificado",
            "transparencia_precos": "Não avaliado",
            "complexidade_estrutura": "Não avaliado"
        },
        "estrutura_precos": {
            "tipo_cobranca": "Não identificado",
            "moeda_principal": "Não identificado",
            "periodo_trial": "Não disponível"
        },
        "insights_estrategicos": {
            "pontos_fortes_pricing": [],
            "areas_melhoria": [],
            "oportunidades_revenue": [],
            "recomendacoes_estrategicas": []
        }
    }
    return defaults.get(secao, {})


def _metricas_pricing_avancadas_default() -> Dict[str, Dict[str, str]]:
    return {
        "financial_metrics": {
            "ticket_medio": "Não disponível",
            "ltv_estimado": "Não disponível",
            "churn_rate": "Não disponível",
            "arpu": "Não disponível"
        },
        "conversion_metrics": {
            "conversion_rate": "Não disponível",
            "upgrade_rate": "Não disponível",
            "downgrade_rate": "Não disponível"
        },
        "growth_metrics": {
            "expansion_revenue": "Não disponível",
            "net_revenue_retention": "Não disponível",
            "gross_revenue_retention": "Não disponível"
        },
        "pricing_efficiency": {
            "price_realization": "Não disponível",
            "discounting_rate": "Não disponível",
            "pricing_variance": "Não disponível"
        }
    }


def _get_default_pricing_metrics_secao(secao: str) -> Dict[str, str]:
    return _metricas_pricing_avancadas_default().get(secao, {})


async def parse_dados_modelo_negocio(dados_brutos: Dict[str, Any]) -> Dict[str, Any]:
    """
    Parser para dados de modelo de negócio coletados da API Perplexity.

    Args:
        dados_brutos: Dados brutos retornados da API

    Returns:
        Dict com dados processados e validados
    """
    try:
        # Se houver erro na resposta da API, retornar estrutura de erro
        if "erro" in dados_brutos:
            return {
                "error": True,
                "message": dados_brutos["erro"],
                "raw_data": dados_brutos,
                "processed_at": datetime.utcnow().isoformat()
            }

        # Processar e normalizar os dados - ASYNC
        dados_processados = await asyncio.to_thread(_processar_modelo_negocio_data, dados_brutos)

        # Adicionar metadados de processamento
        resultado = dados_processados.copy()
        resultado.update({
            "validation_status": "success",
            "processed_at": datetime.utcnow().isoformat(),
            "data_quality_score": await asyncio.to_thread(_calcular_score_qualidade_modelo_negocio, dados_processados)
        })

        return resultado

    except Exception as e:
        logger.error(f"Erro ao processar dados de modelo de negócio: {e}")
        return {
            "error": True,
            "message": f"Erro no processamento: {str(e)}",
            "raw_data": dados_brutos,
            "processed_at": datetime.utcnow().isoformat()
        }


def _processar_modelo_negocio_data(dados: Dict[str, Any]) -> Dict[str, Any]:
    """Processa e normaliza dados específicos de modelo de negócio"""
    resultado = {}

    # Seções principais a processar
    secoes = [
        "classificacao_modelo", "proposta_valor", "segmentos_clientes",
        "canais_distribuicao", "relacionamento_clientes", "recursos_principais",
        "metricas_modelo_negocio", "escalabilidade_modelo", "evolucao_modelo",
        "riscos_modelo", "benchmarking_setor"
    ]

    for secao in secoes:
        if secao in dados:
            if secao == "metricas_modelo_negocio":
                resultado[secao] = _normalizar_metricas_modelo_negocio(
                    dados[secao])
            elif secao == "escalabilidade_modelo":
                resultado[secao] = _normalizar_escalabilidade_modelo(
                    dados[secao])
            else:
                resultado[secao] = dados[secao]
        else:
            resultado[secao] = _get_default_modelo_negocio_secao(secao)

    return resultado


def _normalizar_metricas_modelo_negocio(metricas: Dict[str, Any]) -> Dict[str, Any]:
    """Normaliza métricas do modelo de negócio"""
    resultado = {}

    # Processar unit economics
    if "unit_economics" in metricas:
        unit_economics = metricas["unit_economics"]
        resultado["unit_economics"] = {}
        campos_monetarios = ["cac", "ltv", "gross_margin"]
        campos_tempo = ["payback_period"]
        campos_ratio = ["ltv_cac_ratio"]

        for campo in campos_monetarios + campos_tempo + campos_ratio:
            if campo in unit_economics:
                if campo in campos_monetarios:
                    resultado["unit_economics"][campo] = _normalizar_valor_monetario(
                        unit_economics[campo])
                else:
                    resultado["unit_economics"][campo] = str(
                        unit_economics[campo]) if unit_economics[campo] else "Não disponível"
            else:
                resultado["unit_economics"][campo] = "Não disponível"
    else:
        resultado["unit_economics"] = _unit_economics_default()

    # Processar outras seções de métricas
    secoes_metricas = ["crescimento", "operacionais", "mercado"]
    for secao in secoes_metricas:
        if secao in metricas:
            resultado[secao] = {}
            for campo, valor in metricas[secao].items():
                resultado[secao][campo] = str(
                    valor) if valor else "Não disponível"
        else:
            resultado[secao] = _get_default_metricas_secao(secao)

    return resultado


def _normalizar_escalabilidade_modelo(escalabilidade: Dict[str, Any]) -> Dict[str, Any]:
    """Normaliza dados de escalabilidade do modelo"""
    resultado = {}

    # Processar análise de escalabilidade
    if "analise_escalabilidade" in escalabilidade:
        analise = escalabilidade["analise_escalabilidade"]
        resultado["analise_escalabilidade"] = {}

        campos = ["nivel_escalabilidade",
                  "score_escalabilidade", "justificativa"]
        for campo in campos:
            if campo in analise:
                if campo == "score_escalabilidade":
                    resultado["analise_escalabilidade"][campo] = _normalizar_score(
                        analise[campo])
                else:
                    resultado["analise_escalabilidade"][campo] = str(
                        analise[campo]) if analise[campo] else "Não disponível"
            else:
                resultado["analise_escalabilidade"][campo] = "Não disponível"
    else:
        resultado["analise_escalabilidade"] = _analise_escalabilidade_default()

    # Processar outras seções
    secoes = ["fatores_limitantes",
              "alavancas_crescimento", "requisitos_escala"]
    for secao in secoes:
        if secao in escalabilidade:
            resultado[secao] = escalabilidade[secao]
        else:
            resultado[secao] = _get_default_escalabilidade_secao(secao)

    return resultado


def _calcular_score_qualidade_modelo_negocio(dados: Dict[str, Any]) -> float:
    """Calcula score de qualidade dos dados de modelo de negócio (0-100)"""
    score = 0.0
    peso_total = 0.0

    pesos = {
        "classificacao_modelo": 0.15,
        "proposta_valor": 0.15,
        "segmentos_clientes": 0.15,
        "canais_distribuicao": 0.10,
        "relacionamento_clientes": 0.10,
        "recursos_principais": 0.10,
        "metricas_modelo_negocio": 0.15,
        "escalabilidade_modelo": 0.10
    }

    for secao, peso in pesos.items():
        if secao in dados:
            peso_total += peso

            if isinstance(dados[secao], dict):
                # Calcular completude da seção
                total_campos = 0
                campos_preenchidos = 0

                def contar_campos(obj, path=""):
                    nonlocal total_campos, campos_preenchidos
                    if isinstance(obj, dict):
                        for k, v in obj.items():
                            if isinstance(v, dict):
                                contar_campos(v, f"{path}.{k}")
                            else:
                                total_campos += 1
                                if v and v != "Não disponível" and v != [] and v != "":
                                    campos_preenchidos += 1

                contar_campos(dados[secao])

                if total_campos > 0:
                    score += (campos_preenchidos / total_campos) * peso * 100

    return round(score / peso_total if peso_total > 0 else 0.0, 2)


async def parse_dados_pricing(dados_brutos: Dict[str, Any]) -> Dict[str, Any]:
    """
    Parser para dados de pricing coletados da API Perplexity.

    Args:
        dados_brutos: Dados brutos retornados da API

    Returns:
        Dict com dados processados e validados
    """
    try:
        # Se houver erro na resposta da API, retornar estrutura de erro
        if "erro" in dados_brutos:
            return {
                "error": True,
                "message": dados_brutos["erro"],
                "raw_data": dados_brutos,
                "processed_at": datetime.utcnow().isoformat()
            }

        # Processar e normalizar os dados - ASYNC
        dados_processados = await asyncio.to_thread(_processar_pricing_data, dados_brutos)

        # Adicionar metadados de processamento
        resultado = dados_processados.copy()
        resultado.update({
            "validation_status": "success",
            "processed_at": datetime.utcnow().isoformat(),
            "data_quality_score": await asyncio.to_thread(_calcular_score_qualidade_pricing, dados_processados)
        })

        return resultado

    except Exception as e:
        logger.error(f"Erro ao processar dados de pricing: {e}")
        return {
            "error": True,
            "message": f"Erro no processamento: {str(e)}",
            "raw_data": dados_brutos,
            "processed_at": datetime.utcnow().isoformat()
        }


def _processar_pricing_data(dados: Dict[str, Any]) -> Dict[str, Any]:
    """Processa e normaliza dados específicos de pricing"""
    resultado = {}

    # Seções principais a processar
    secoes_simples = [
        "resumo_pricing", "estrutura_precos", "politicas_desconto_expandidas",
        "add_ons_extras_detalhados", "analise_competitiva_expandida",
        "estrategias_monetizacao_avancadas", "elasticidade_preco_avancada",
        "psicologia_pricing", "tendencias_pricing_futuro", "insights_estrategicos",
        "fontes_informacao"
    ]

    for secao in secoes_simples:
        if secao in dados:
            resultado[secao] = dados[secao]
        else:
            resultado[secao] = _get_default_pricing_secao(secao)

    # Processar seções que precisam de normalização especial
    if "tiers_precos_detalhados" in dados:
        resultado["tiers_precos_detalhados"] = _normalizar_tiers_pricing(
            dados["tiers_precos_detalhados"])
    else:
        resultado["tiers_precos_detalhados"] = []

    if "metricas_pricing_avancadas" in dados:
        resultado["metricas_pricing_avancadas"] = _normalizar_metricas_pricing_avancadas(
            dados["metricas_pricing_avancadas"])
    else:
        resultado["metricas_pricing_avancadas"] = _metricas_pricing_avancadas_default()

    return resultado


def _normalizar_tiers_pricing(tiers: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Normaliza dados dos tiers de pricing"""
    if not isinstance(tiers, list):
        return []

    resultado = []
    for tier in tiers:
        if isinstance(tier, dict):
            tier_normalizado = {}

            # Campos de preço que precisam de normalização
            campos_preco = ["preco_mensal", "preco_anual", "valor_por_usuario"]
            # Campos de texto simples
            campos_texto = ["nome_tier", "posicionamento_tier", "target_audience",
                            "escalabilidade_tier", "roi_estimado"]
            # Campos de lista
            campos_lista = ["principais_features",
                            "features_exclusivas", "casos_uso_tipicos"]
            # Outros campos
            outros_campos = ["desconto_anual", "limites_uso"]

            for campo in campos_preco + campos_texto + campos_lista + outros_campos:
                if campo in tier:
                    if campo in campos_preco:
                        tier_normalizado[campo] = _normalizar_valor_monetario(
                            tier[campo])
                    elif campo in campos_lista:
                        tier_normalizado[campo] = tier[campo] if isinstance(
                            tier[campo], list) else []
                    else:
                        tier_normalizado[campo] = str(
                            tier[campo]) if tier[campo] else "Não disponível"
                else:
                    if campo in campos_lista:
                        tier_normalizado[campo] = []
                    else:
                        tier_normalizado[campo] = "Não disponível"

            resultado.append(tier_normalizado)

    return resultado


def _normalizar_metricas_pricing_avancadas(metricas: Dict[str, Any]) -> Dict[str, Any]:
    """Normaliza métricas avançadas de pricing"""
    resultado = {}

    secoes_metricas = ["financial_metrics", "conversion_metrics",
                       "growth_metrics", "pricing_efficiency"]

    for secao in secoes_metricas:
        if secao in metricas and isinstance(metricas[secao], dict):
            resultado[secao] = {}
            for campo, valor in metricas[secao].items():
                if campo in ["ticket_medio", "ltv_estimado", "arpu", "arpa", "aov"]:
                    resultado[secao][campo] = _normalizar_valor_monetario(
                        valor)
                elif "rate" in campo or "retention" in campo or "ratio" in campo:
                    resultado[secao][campo] = _normalizar_percentual(valor)
                else:
                    resultado[secao][campo] = str(
                        valor) if valor else "Não disponível"
        else:
            resultado[secao] = _get_default_pricing_metrics_secao(secao)

    return resultado


def _calcular_score_qualidade_pricing(dados: Dict[str, Any]) -> float:
    """Calcula score de qualidade dos dados de pricing (0-100)"""
    score = 0.0
    peso_total = 0.0

    pesos = {
        "resumo_pricing": 0.10,
        "estrutura_precos": 0.10,
        "tiers_precos_detalhados": 0.20,
        "metricas_pricing_avancadas": 0.15,
        "analise_competitiva_expandida": 0.15,
        "estrategias_monetizacao_avancadas": 0.10,
        "psicologia_pricing": 0.10,
        "insights_estrategicos": 0.10
    }

    for secao, peso in pesos.items():
        if secao in dados:
            peso_total += peso

            if secao == "tiers_precos_detalhados":
                if isinstance(dados[secao], list) and len(dados[secao]) > 0:
                    score += peso * 100
            elif isinstance(dados[secao], dict):
                total_campos = 0
                campos_preenchidos = 0

                def contar_campos_pricing(obj):
                    nonlocal total_campos, campos_preenchidos
                    if isinstance(obj, dict):
                        for k, v in obj.items():
                            if isinstance(v, dict):
                                contar_campos_pricing(v)
                            elif isinstance(v, list):
                                total_campos += 1
                                if v:  # Lista não vazia
                                    campos_preenchidos += 1
                            else:
                                total_campos += 1
                                if v and v != "Não disponível" and v != "":
                                    campos_preenchidos += 1

                contar_campos_pricing(dados[secao])

                if total_campos > 0:
                    score += (campos_preenchidos / total_campos) * peso * 100

    return round(score / peso_total if peso_total > 0 else 0.0, 2)


async def parse_dados_canais_reviews(dados_brutos: Dict[str, Any]) -> Dict[str, Any]:
    """
    Parser para dados de canais de distribuição, reviews e certificações coletados da API Perplexity.

    Args:
        dados_brutos: Dados brutos retornados da API

    Returns:
        Dict com dados processados e validados
    """
    try:
        # Se houver erro na resposta da API, retornar estrutura de erro
        if "erro" in dados_brutos:
            return {
                "error": True,
                "message": dados_brutos["erro"],
                "raw_data": dados_brutos,
                "processed_at": datetime.utcnow().isoformat()
            }

        # Processar e normalizar os dados - ASYNC
        dados_processados = await asyncio.to_thread(_processar_canais_reviews_data, dados_brutos)

        # Adicionar metadados de processamento
        resultado = dados_processados.copy()
        resultado.update({
            "validation_status": "success",
            "processed_at": datetime.utcnow().isoformat(),
            "data_quality_score": await asyncio.to_thread(_calcular_score_qualidade_canais_reviews, dados_processados)
        })

        return resultado

    except Exception as e:
        logger.error(f"Erro ao processar dados de canais/reviews: {e}")
        return {
            "error": True,
            "message": f"Erro no processamento: {str(e)}",
            "raw_data": dados_brutos,
            "processed_at": datetime.utcnow().isoformat()
        }


def _processar_canais_reviews_data(dados: Dict[str, Any]) -> Dict[str, Any]:
    """Processa e normaliza dados específicos de canais, reviews e certificações"""
    resultado = {}

    # Seções principais a processar
    secoes_simples = [
        "analise_reputacional", "competitividade_canais", "fontes_informacao"
    ]

    for secao in secoes_simples:
        if secao in dados:
            resultado[secao] = dados[secao]
        else:
            resultado[secao] = _get_default_canais_reviews_secao(secao)

    # Processar dados_canais_distribuicao
    if "dados_canais_distribuicao" in dados:
        resultado["dados_canais_distribuicao"] = _normalizar_dados_canais_distribuicao(
            dados["dados_canais_distribuicao"])
    else:
        resultado["dados_canais_distribuicao"] = _dados_canais_distribuicao_default()

    # Processar dados_reviews_feedback
    if "dados_reviews_feedback" in dados:
        resultado["dados_reviews_feedback"] = _normalizar_dados_reviews_feedback(
            dados["dados_reviews_feedback"])
    else:
        resultado["dados_reviews_feedback"] = _dados_reviews_feedback_default()

    # Processar dados_certificacoes
    if "dados_certificacoes" in dados:
        resultado["dados_certificacoes"] = _normalizar_dados_certificacoes(
            dados["dados_certificacoes"])
    else:
        resultado["dados_certificacoes"] = _dados_certificacoes_default()

    return resultado


def _normalizar_dados_canais_distribuicao(canais: Dict[str, Any]) -> Dict[str, Any]:
    """Normaliza dados de canais de distribuição"""
    resultado = {}

    # Processar resumo_canais
    if "resumo_canais" in canais:
        resultado["resumo_canais"] = canais["resumo_canais"]
    else:
        resultado["resumo_canais"] = _resumo_canais_default()

    # Processar canais_principais (lista)
    if "canais_principais" in canais and isinstance(canais["canais_principais"], list):
        resultado["canais_principais"] = []
        for canal in canais["canais_principais"]:
            if isinstance(canal, dict):
                canal_normalizado = {}
                campos = [
                    "nome_canal", "tipo_canal", "importancia_estrategica",
                    "percentual_vendas", "target_audience", "vantagens_canal", "limitacoes_canal"
                ]
                for campo in campos:
                    canal_normalizado[campo] = str(
                        canal.get(campo, "Não disponível")) if canal.get(campo) else "Não disponível"
                resultado["canais_principais"].append(canal_normalizado)
    else:
        resultado["canais_principais"] = []

    # Processar outras seções
    outras_secoes = ["estrategia_omnichannel",
                     "parceiros_distribuicao", "performance_canais", "go_to_market"]
    for secao in outras_secoes:
        if secao in canais:
            resultado[secao] = canais[secao]
        else:
            resultado[secao] = _get_default_canais_secao(secao)

    return resultado


def _normalizar_dados_reviews_feedback(reviews: Dict[str, Any]) -> Dict[str, Any]:
    """Normaliza dados de reviews e feedback"""
    resultado = {}

    # Processar resumo_reputacao
    if "resumo_reputacao" in reviews:
        resultado["resumo_reputacao"] = reviews["resumo_reputacao"]
    else:
        resultado["resumo_reputacao"] = _resumo_reputacao_default()

    # Processar plataformas_review
    if "plataformas_review" in reviews:
        resultado["plataformas_review"] = _normalizar_plataformas_review(
            reviews["plataformas_review"])
    else:
        resultado["plataformas_review"] = _plataformas_review_default()

    # Processar outras seções
    outras_secoes = ["sentiment_geral", "pontos_fortes_fracos",
                     "tendencias_satisfacao", "gestao_feedback"]
    for secao in outras_secoes:
        if secao in reviews:
            resultado[secao] = reviews[secao]
        else:
            resultado[secao] = _get_default_reviews_secao(secao)

    return resultado


def _normalizar_dados_certificacoes(certificacoes: Dict[str, Any]) -> Dict[str, Any]:
    """Normaliza dados de certificações"""
    resultado = {}

    # Processar resumo_certificacoes
    if "resumo_certificacoes" in certificacoes:
        resultado["resumo_certificacoes"] = certificacoes["resumo_certificacoes"]
    else:
        resultado["resumo_certificacoes"] = _resumo_certificacoes_default()

    # Processar outras seções
    outras_secoes = [
        "certificacoes_tecnicas", "premiacao_industria", "compliance_regulatorio",
        "reconhecimentos", "impacto_certificacoes"
    ]
    for secao in outras_secoes:
        if secao in certificacoes:
            resultado[secao] = certificacoes[secao]
        else:
            resultado[secao] = _get_default_certificacoes_secao(secao)

    return resultado


def _normalizar_plataformas_review(plataformas: Dict[str, Any]) -> Dict[str, Any]:
    """Normaliza dados de plataformas de review"""
    resultado = {}

    plataformas_conhecidas = [
        "google_reviews", "reclame_aqui", "trustpilot", "app_stores", "reviews_especializados"
    ]

    for plataforma in plataformas_conhecidas:
        if plataforma in plataformas and isinstance(plataformas[plataforma], dict):
            resultado[plataforma] = plataformas[plataforma]
        else:
            resultado[plataforma] = _get_default_plataforma_review(plataforma)

    return resultado


def _calcular_score_qualidade_canais_reviews(dados: Dict[str, Any]) -> float:
    """Calcula score de qualidade dos dados de canais/reviews (0-10)"""
    score = 0.0
    peso_total = 0.0

    pesos = {
        "dados_canais_distribuicao": 0.30,
        "dados_reviews_feedback": 0.35,
        "dados_certificacoes": 0.25,
        "analise_reputacional": 0.10
    }

    for secao, peso in pesos.items():
        if secao in dados:
            peso_total += peso

            if isinstance(dados[secao], dict):
                total_campos = 0
                campos_preenchidos = 0

                def contar_campos_canais_reviews(obj):
                    nonlocal total_campos, campos_preenchidos
                    if isinstance(obj, dict):
                        for k, v in obj.items():
                            if isinstance(v, dict):
                                contar_campos_canais_reviews(v)
                            elif isinstance(v, list):
                                total_campos += 1
                                if v:  # Lista não vazia
                                    campos_preenchidos += 1
                            else:
                                total_campos += 1
                                if v and v != "Não disponível" and v != "":
                                    campos_preenchidos += 1

                contar_campos_canais_reviews(dados[secao])

                if total_campos > 0:
                    score += (campos_preenchidos / total_campos) * peso * 10

    return round(score / peso_total if peso_total > 0 else 0.0, 2)


# Funções default para canais/reviews
def _dados_canais_distribuicao_default() -> Dict[str, Any]:
    """Retorna estrutura padrão para dados de canais de distribuição"""
    return {
        "resumo_canais": _resumo_canais_default(),
        "canais_principais": [],
        "estrategia_omnichannel": _get_default_canais_secao("estrategia_omnichannel"),
        "parceiros_distribuicao": _get_default_canais_secao("parceiros_distribuicao"),
        "performance_canais": _get_default_canais_secao("performance_canais"),
        "go_to_market": _get_default_canais_secao("go_to_market")
    }


def _dados_reviews_feedback_default() -> Dict[str, Any]:
    """Retorna estrutura padrão para dados de reviews e feedback"""
    return {
        "resumo_reputacao": _resumo_reputacao_default(),
        "plataformas_review": _plataformas_review_default(),
        "sentiment_geral": _get_default_reviews_secao("sentiment_geral"),
        "pontos_fortes_fracos": _get_default_reviews_secao("pontos_fortes_fracos"),
        "tendencias_satisfacao": _get_default_reviews_secao("tendencias_satisfacao"),
        "gestao_feedback": _get_default_reviews_secao("gestao_feedback")
    }


def _dados_certificacoes_default() -> Dict[str, Any]:
    """Retorna estrutura padrão para dados de certificações"""
    return {
        "resumo_certificacoes": _resumo_certificacoes_default(),
        "certificacoes_tecnicas": _get_default_certificacoes_secao("certificacoes_tecnicas"),
        "premiacao_industria": _get_default_certificacoes_secao("premiacao_industria"),
        "compliance_regulatorio": _get_default_certificacoes_secao("compliance_regulatorio"),
        "reconhecimentos": _get_default_certificacoes_secao("reconhecimentos"),
        "impacto_certificacoes": _get_default_certificacoes_secao("impacto_certificacoes")
    }


def _resumo_canais_default() -> Dict[str, str]:
    """Retorna resumo padrão de canais"""
    return {
        "estrategia_principal": "Não disponível",
        "modelo_distribuicao": "Não disponível",
        "cobertura_geografica": "Não disponível",
        "maturidade_canais": "Não disponível",
        "complexidade_rede": "Não disponível",
        "integracoes_sistemas": "Não disponível"
    }


def _resumo_reputacao_default() -> Dict[str, str]:
    """Retorna resumo padrão de reputação"""
    return {
        "score_reputacao_geral": "Não disponível",
        "numero_total_reviews": "Não disponível",
        "tendencia_satisfacao": "Não disponível",
        "confiabilidade_dados": "Não disponível",
        "fonte_principal_reviews": "Não disponível",
        "periodo_analise": "Não disponível"
    }


def _resumo_certificacoes_default() -> Dict[str, Any]:
    """Retorna resumo padrão de certificações"""
    return {
        "total_certificacoes": "Não disponível",
        "nivel_certificacao": "Não disponível",
        "areas_cobertas": [],
        "validade_media": "Não disponível",
        "investimento_estimado": "Não disponível",
        "impacto_mercado": "Não disponível"
    }


def _plataformas_review_default() -> Dict[str, Dict[str, str]]:
    """Retorna estrutura padrão para plataformas de review"""
    return {
        "google_reviews": _get_default_plataforma_review("google_reviews"),
        "reclame_aqui": _get_default_plataforma_review("reclame_aqui"),
        "trustpilot": _get_default_plataforma_review("trustpilot"),
        "app_stores": _get_default_plataforma_review("app_stores"),
        "reviews_especializados": _get_default_plataforma_review("reviews_especializados")
    }


def _get_default_canais_secao(secao: str) -> Dict[str, Any]:
    """Retorna dados padrão para seções de canais"""
    defaults = {
        "estrategia_omnichannel": {
            "integracao_canais": "Não disponível",
            "experiencia_unificada": "Não disponível",
            "dados_compartilhados": "Não disponível",
            "inventario_centralizado": "Não disponível",
            "politicas_unificadas": "Não disponível"
        },
        "parceiros_distribuicao": {
            "revendedores_autorizados": [],
            "distribuidores_regionais": [],
            "marketplaces_principais": [],
            "canais_especialistas": [],
            "programas_parceiros": "Não disponível"
        },
        "performance_canais": {
            "canal_mais_eficiente": "Não disponível",
            "crescimento_por_canal": "Não disponível",
            "custos_aquisicao": "Não disponível",
            "tempo_ciclo_vendas": "Não disponível",
            "satisfacao_parceiros": "Não disponível"
        },
        "go_to_market": {
            "estrategia_lancamento": "Não disponível",
            "segmentacao_canais": "Não disponível",
            "adaptacao_local": "Não disponível",
            "suporte_vendas": "Não disponível",
            "treinamento_canais": "Não disponível"
        }
    }
    return defaults.get(secao, {})


def _get_default_reviews_secao(secao: str) -> Dict[str, Any]:
    """Retorna dados padrão para seções de reviews"""
    defaults = {
        "sentiment_geral": {
            "sentiment_positivo": "Não disponível",
            "sentiment_neutro": "Não disponível",
            "sentiment_negativo": "Não disponível",
            "palavras_chave_positivas": [],
            "palavras_chave_negativas": []
        },
        "pontos_fortes_fracos": {
            "principais_elogios": [],
            "principais_criticas": [],
            "aspectos_mais_valorizados": [],
            "areas_melhoria": [],
            "comparacao_concorrentes": "Não disponível"
        },
        "tendencias_satisfacao": {
            "evolucao_ratings": "Não disponível",
            "sazonalidade_reviews": "Não disponível",
            "impacto_melhorias": "Não disponível",
            "nps_estimado": "Não disponível",
            "taxa_recomendacao": "Não disponível"
        },
        "gestao_feedback": {
            "resposta_reviews": "Não disponível",
            "tempo_resposta_medio": "Não disponível",
            "qualidade_respostas": "Não disponível",
            "resolucao_problemas": "Não disponível",
            "proatividade_melhorias": "Não disponível"
        }
    }
    return defaults.get(secao, {})


def _get_default_certificacoes_secao(secao: str) -> Dict[str, Any]:
    """Retorna dados padrão para seções de certificações"""
    defaults = {
        "certificacoes_tecnicas": {
            "iso_quality": [],
            "certificacoes_setor": [],
            "cloud_providers": [],
            "seguranca_dados": [],
            "metodologias_dev": []
        },
        "premiacao_industria": {
            "premios_nacionais": [],
            "premios_internacionais": [],
            "rankings_mercado": [],
            "reconhecimentos_midia": [],
            "cases_premiados": []
        },
        "compliance_regulatorio": {
            "licencas_operacao": [],
            "conformidade_legal": [],
            "auditorias_externas": "Não disponível",
            "compliance_internacional": [],
            "certificacoes_ambientais": []
        },
        "reconhecimentos": {
            "great_place_work": "Não disponível",
            "b_corp": "Não disponível",
            "startup_awards": [],
            "lideranca_mercado": [],
            "inovacao_premios": []
        },
        "impacto_certificacoes": {
            "credibilidade_mercado": "Não disponível",
            "diferenciacao_competitiva": "Não disponível",
            "acesso_novos_mercados": "Não disponível",
            "premium_pricing": "Não disponível",
            "parcerias_estrategicas": "Não disponível"
        }
    }
    return defaults.get(secao, {})


def _get_default_plataforma_review(plataforma: str) -> Dict[str, str]:
    """Retorna dados padrão para uma plataforma de review específica"""
    defaults = {
        "google_reviews": {
            "rating_medio": "Não disponível",
            "numero_avaliacoes": "Não disponível",
            "comentarios_recentes": "Não disponível"
        },
        "reclame_aqui": {
            "nota_reclame_aqui": "Não disponível",
            "tempo_resposta": "Não disponível",
            "indice_solucao": "Não disponível"
        },
        "trustpilot": {
            "rating_trustpilot": "Não disponível",
            "numero_reviews": "Não disponível",
            "classificacao_empresa": "Não disponível"
        },
        "app_stores": {
            "google_play_rating": "Não disponível",
            "app_store_rating": "Não disponível",
            "downloads_estimados": "Não disponível"
        },
        "reviews_especializados": {
            "sites_especializados": [],
            "ratings_especializados": "Não disponível",
            "premios_mercado": []
        }
    }
    return defaults.get(plataforma, {})


def _get_default_canais_reviews_secao(secao: str) -> Dict[str, Any]:
    """Retorna dados padrão para seções gerais de canais/reviews"""
    defaults = {
        "analise_reputacional": {
            "posicionamento_mercado": "Não disponível",
            "confianca_marca": "Não disponível",
            "awareness_mercado": "Não disponível",
            "recomendacao_espontanea": "Não disponível",
            "vulnerabilidades_reputacao": [],
            "oportunidades_melhoria": []
        },
        "competitividade_canais": {
            "benchmark_concorrentes": [],
            "gaps_distribuicao": [],
            "oportunidades_expansao": [],
            "ameacas_canais": []
        },
        "fontes_informacao": {
            "fontes_utilizadas": [],
            "confiabilidade_dados": "Não disponível",
            "data_ultima_atualizacao": "Não disponível",
            "observacoes": "Não disponível",
            "limitacoes_analise": "Não disponível"
        }
    }
    return defaults.get(secao, {})


async def parse_dados_pesquisa_mercado(dados_brutos: Dict[str, Any]) -> Dict[str, Any]:
    """
    Parser para dados de pesquisa de mercado e produtos/serviços

    Args:
        dados_brutos: Dados brutos retornados pela API Perplexity

    Returns:
        Dict com dados processados e normalizados, incluindo score de qualidade
    """
    try:
        # Tratar casos de erro da API
        if dados_brutos is None:
            return {
                "erro": "Erro ao processar dados - dados_brutos é None",
                "pesquisa_mercado": _pesquisa_mercado_default(),
                "analise_produtos_servicos": _analise_produtos_servicos_default(),
                "insights_competitivos": _insights_competitivos_default(),
                "fontes_informacao": _fontes_informacao_mercado_default(),
                "score_qualidade": 0
            }

        if isinstance(dados_brutos, dict) and "erro" in dados_brutos:
            return {
                "erro": dados_brutos.get("erro", "Erro desconhecido"),
                "raw": dados_brutos.get("raw", ""),
                "pesquisa_mercado": _pesquisa_mercado_default(),
                "analise_produtos_servicos": _analise_produtos_servicos_default(),
                "insights_competitivos": _insights_competitivos_default(),
                "fontes_informacao": _fontes_informacao_mercado_default(),
                "score_qualidade": 0
            }

        if not isinstance(dados_brutos, dict):
            return {
                "erro": f"Erro ao processar dados - tipo inválido: {type(dados_brutos)}",
                "pesquisa_mercado": _pesquisa_mercado_default(),
                "analise_produtos_servicos": _analise_produtos_servicos_default(),
                "insights_competitivos": _insights_competitivos_default(),
                "fontes_informacao": _fontes_informacao_mercado_default(),
                "score_qualidade": 0
            }

        # Processar e normalizar dados - ASYNC
        dados_processados = await asyncio.to_thread(_processar_pesquisa_mercado_data, dados_brutos)

        # Calcular score de qualidade - ASYNC
        score_qualidade = await asyncio.to_thread(_calcular_score_qualidade_pesquisa_mercado, dados_processados)
        dados_processados["score_qualidade"] = score_qualidade

        return dados_processados

    except Exception as e:
        return {
            "erro": f"Erro ao processar dados de pesquisa de mercado: {str(e)}",
            "pesquisa_mercado": _pesquisa_mercado_default(),
            "analise_produtos_servicos": _analise_produtos_servicos_default(),
            "insights_competitivos": _insights_competitivos_default(),
            "fontes_informacao": _fontes_informacao_mercado_default(),
            "score_qualidade": 0
        }


def _processar_pesquisa_mercado_data(dados: Dict[str, Any]) -> Dict[str, Any]:
    """Processa e normaliza dados de pesquisa de mercado"""

    # Extrair seções principais
    pesquisa_mercado = dados.get("pesquisa_mercado", {})
    analise_produtos_servicos = dados.get("analise_produtos_servicos", {})
    insights_competitivos = dados.get("insights_competitivos", {})
    fontes_informacao = dados.get("fontes_informacao", {})

    # Normalizar cada seção
    pesquisa_normalizada = _normalizar_pesquisa_mercado(pesquisa_mercado)
    analise_normalizada = _normalizar_analise_produtos_servicos(
        analise_produtos_servicos)
    insights_normalizados = _normalizar_insights_competitivos(
        insights_competitivos)
    fontes_normalizadas = _normalizar_fontes_informacao_mercado(
        fontes_informacao)

    return {
        "pesquisa_mercado": pesquisa_normalizada,
        "analise_produtos_servicos": analise_normalizada,
        "insights_competitivos": insights_normalizados,
        "fontes_informacao": fontes_normalizadas
    }


def _normalizar_pesquisa_mercado(pesquisa: Dict[str, Any]) -> Dict[str, Any]:
    """Normaliza dados de pesquisa de mercado"""

    # Processar tamanho de mercado
    tamanho_mercado = pesquisa.get("tamanho_mercado", {})
    tamanho_normalizado = {
        "tam_global": str(tamanho_mercado.get("tam_global", "Não disponível")),
        "sam_serviceable": str(tamanho_mercado.get("sam_serviceable", "Não disponível")),
        "som_obtainable": str(tamanho_mercado.get("som_obtainable", "Não disponível")),
        "crescimento_cagr": str(tamanho_mercado.get("crescimento_cagr", "Não disponível")),
        "geografia_mercados": tamanho_mercado.get("geografia_mercados", [])
    }

    # Processar tendências do setor
    tendencias_setor = pesquisa.get("tendencias_setor", [])
    tendencias_normalizadas = []
    for tendencia in tendencias_setor:
        if isinstance(tendencia, dict):
            tendencia_normalizada = {
                "tendencia": str(tendencia.get("tendencia", "Não disponível")),
                "impacto": str(tendencia.get("impacto", "Não disponível")),
                "horizonte_tempo": str(tendencia.get("horizonte_tempo", "Não disponível")),
                "evidencias": tendencia.get("evidencias", [])
            }
            tendencias_normalizadas.append(tendencia_normalizada)

    # Processar drivers e barreiras
    drivers_crescimento = pesquisa.get("drivers_crescimento", [])
    barreiras_entrada = pesquisa.get("barreiras_entrada", [])

    return {
        "tamanho_mercado": tamanho_normalizado,
        "tendencias_setor": tendencias_normalizadas,
        "drivers_crescimento": drivers_crescimento if isinstance(drivers_crescimento, list) else [],
        "barreiras_entrada": barreiras_entrada if isinstance(barreiras_entrada, list) else []
    }


def _normalizar_analise_produtos_servicos(analise: Dict[str, Any]) -> Dict[str, Any]:
    """Normaliza dados de análise de produtos/serviços"""

    # Processar portfolio de concorrentes
    portfolio_concorrentes = analise.get("portfolio_concorrentes", [])
    portfolio_normalizado = []
    for concorrente in portfolio_concorrentes:
        if isinstance(concorrente, dict):
            concorrente_normalizado = {
                "concorrente": str(concorrente.get("concorrente", "Não disponível")),
                "produtos_principais": concorrente.get("produtos_principais", []),
                "posicionamento": str(concorrente.get("posicionamento", "Não disponível")),
                "diferenciais": concorrente.get("diferenciais", [])
            }
            portfolio_normalizado.append(concorrente_normalizado)

    # Processar outras seções
    gaps_mercado = analise.get("gaps_mercado", [])
    inovacoes_recentes = analise.get("inovacoes_recentes", [])
    tendencias_produto = analise.get("tendencias_produto", [])
    oportunidades_produto = analise.get("oportunidades_produto", [])

    return {
        "portfolio_concorrentes": portfolio_normalizado,
        "gaps_mercado": gaps_mercado if isinstance(gaps_mercado, list) else [],
        "inovacoes_recentes": inovacoes_recentes if isinstance(inovacoes_recentes, list) else [],
        "tendencias_produto": tendencias_produto if isinstance(tendencias_produto, list) else [],
        "oportunidades_produto": oportunidades_produto if isinstance(oportunidades_produto, list) else []
    }


def _normalizar_insights_competitivos(insights: Dict[str, Any]) -> Dict[str, Any]:
    """Normaliza dados de insights competitivos"""

    # Processar dados de insights competitivos
    posicionamento_relativo = str(insights.get(
        "posicionamento_relativo", "Não disponível"))
    vantagens_competitivas = insights.get("vantagens_competitivas", [])
    vulnerabilidades = insights.get("vulnerabilidades", [])
    recomendacoes_estrategicas = insights.get("recomendacoes_estrategicas", [])

    return {
        "posicionamento_relativo": posicionamento_relativo,
        "vantagens_competitivas": vantagens_competitivas if isinstance(vantagens_competitivas, list) else [],
        "vulnerabilidades": vulnerabilidades if isinstance(vulnerabilidades, list) else [],
        "recomendacoes_estrategicas": recomendacoes_estrategicas if isinstance(recomendacoes_estrategicas, list) else []
    }


def _normalizar_fontes_informacao_mercado(fontes: Dict[str, Any]) -> Dict[str, Any]:
    """Normaliza dados de fontes de informação"""

    return {
        "fontes_utilizadas": fontes.get("fontes_utilizadas", []),
        "confiabilidade_dados": str(fontes.get("confiabilidade_dados", "Não disponível")),
        "data_ultima_atualizacao": str(fontes.get("data_ultima_atualizacao", "Não disponível")),
        "observacoes": str(fontes.get("observacoes", "Não disponível")),
        "limitacoes_analise": str(fontes.get("limitacoes_analise", "Não disponível"))
    }


def _calcular_score_qualidade_pesquisa_mercado(dados: Dict[str, Any]) -> float:
    """
    Calcula score de qualidade dos dados de pesquisa de mercado (0-10)

    Critérios de avaliação:
    - Completude dos dados de tamanho de mercado (25%)
    - Qualidade das tendências setoriais (25%)
    - Análise de concorrentes (20%)
    - Insights competitivos (20%)
    - Fontes de informação (10%)
    """
    try:
        score_total = 0.0

        # 1. Avaliar tamanho de mercado (25%)
        pesquisa_mercado = dados.get("pesquisa_mercado", {})
        tamanho_mercado = pesquisa_mercado.get("tamanho_mercado", {})

        score_tamanho = 0
        if tamanho_mercado.get("tam_global", "Não disponível") != "Não disponível":
            score_tamanho += 2
        if tamanho_mercado.get("sam_serviceable", "Não disponível") != "Não disponível":
            score_tamanho += 2
        if tamanho_mercado.get("som_obtainable", "Não disponível") != "Não disponível":
            score_tamanho += 2
        if tamanho_mercado.get("crescimento_cagr", "Não disponível") != "Não disponível":
            score_tamanho += 2
        if len(tamanho_mercado.get("geografia_mercados", [])) > 0:
            score_tamanho += 2

        score_total += (score_tamanho / 10) * 2.5

        # 2. Avaliar tendências setoriais (25%)
        tendencias = pesquisa_mercado.get("tendencias_setor", [])
        score_tendencias = 0

        if len(tendencias) > 0:
            score_tendencias += 3
            for tendencia in tendencias[:3]:  # Avaliar até 3 tendências
                if tendencia.get("tendencia", "Não disponível") != "Não disponível":
                    score_tendencias += 1
                if tendencia.get("impacto", "Não disponível") != "Não disponível":
                    score_tendencias += 1
                if len(tendencia.get("evidencias", [])) > 0:
                    score_tendencias += 1
            score_tendencias = min(score_tendencias, 10)

        score_total += (score_tendencias / 10) * 2.5

        # 3. Avaliar análise de concorrentes (20%)
        analise_produtos = dados.get("analise_produtos_servicos", {})
        concorrentes = analise_produtos.get("portfolio_concorrentes", [])

        score_concorrentes = 0
        if len(concorrentes) > 0:
            score_concorrentes += 3
            for concorrente in concorrentes[:3]:  # Avaliar até 3 concorrentes
                if concorrente.get("concorrente", "Não disponível") != "Não disponível":
                    score_concorrentes += 1
                if len(concorrente.get("produtos_principais", [])) > 0:
                    score_concorrentes += 1
                if len(concorrente.get("diferenciais", [])) > 0:
                    score_concorrentes += 1
            score_concorrentes = min(score_concorrentes, 10)

        if len(analise_produtos.get("gaps_mercado", [])) > 0:
            score_concorrentes += 1

        score_total += (score_concorrentes / 10) * 2.0

        # 4. Avaliar insights competitivos (20%)
        insights = dados.get("insights_competitivos", {})
        score_insights = 0

        if insights.get("posicionamento_relativo", "Não disponível") != "Não disponível":
            score_insights += 3
        if len(insights.get("vantagens_competitivas", [])) > 0:
            score_insights += 2
        if len(insights.get("vulnerabilidades", [])) > 0:
            score_insights += 2
        if len(insights.get("recomendacoes_estrategicas", [])) > 0:
            score_insights += 3

        score_total += (score_insights / 10) * 2.0

        # 5. Avaliar fontes de informação (10%)
        fontes = dados.get("fontes_informacao", {})
        score_fontes = 0

        if len(fontes.get("fontes_utilizadas", [])) > 0:
            score_fontes += 4
        if fontes.get("confiabilidade_dados", "Não disponível") != "Não disponível":
            score_fontes += 3
        if fontes.get("data_ultima_atualizacao", "Não disponível") != "Não disponível":
            score_fontes += 3

        score_total += (score_fontes / 10) * 1.0

        # Garantir que o score está entre 0 e 10
        return round(max(0.0, min(10.0, score_total)), 1)

    except Exception:
        return 0.0


def _pesquisa_mercado_default() -> Dict[str, Any]:
    """Retorna dados padrão para pesquisa de mercado"""
    return {
        "tamanho_mercado": {
            "tam_global": "Não disponível",
            "sam_serviceable": "Não disponível",
            "som_obtainable": "Não disponível",
            "crescimento_cagr": "Não disponível",
            "geografia_mercados": []
        },
        "tendencias_setor": [],
        "drivers_crescimento": [],
        "barreiras_entrada": []
    }


def _analise_produtos_servicos_default() -> Dict[str, Any]:
    """Retorna dados padrão para análise de produtos/serviços"""
    return {
        "portfolio_concorrentes": [],
        "gaps_mercado": [],
        "inovacoes_recentes": [],
        "tendencias_produto": [],
        "oportunidades_produto": []
    }


def _insights_competitivos_default() -> Dict[str, Any]:
    """Retorna dados padrão para insights competitivos"""
    return {
        "posicionamento_relativo": "Não disponível",
        "vantagens_competitivas": [],
        "vulnerabilidades": [],
        "recomendacoes_estrategicas": []
    }


def _fontes_informacao_mercado_default() -> Dict[str, Any]:
    """Retorna dados padrão para fontes de informação"""
    return {
        "fontes_utilizadas": [],
        "confiabilidade_dados": "Não disponível",
        "data_ultima_atualizacao": "Não disponível",
        "observacoes": "Não disponível",
        "limitacoes_analise": "Não disponível"
    }


async def parse_dados_diagnostico_tecnico(dados: Any) -> Dict[str, Any]:
    """
    Parser para dados de diagnóstico técnico (Lighthouse + Playwright + IA)

    Normaliza dados heterogêneos de análise técnica e calcula score de qualidade.

    Args:
        dados: Dados brutos de diagnóstico técnico

    Returns:
        Dict com dados normalizados e score de qualidade
    """
    try:
        if not dados:
            return _create_empty_diagnostico_tecnico_response()

        # Verificar se é erro
        if isinstance(dados, dict) and "erro" in dados:
            return {
                **_create_empty_diagnostico_tecnico_response(),
                "erro": dados.get("erro", "Erro desconhecido"),
                "raw_data": dados.get("raw", ""),
                "data_quality_score": 0.0
            }

        # Normalizar dados se for string (JSON)
        if isinstance(dados, str):
            try:
                dados = json.loads(dados)
            except json.JSONDecodeError:
                return {
                    **_create_empty_diagnostico_tecnico_response(),
                    "erro": "Dados inválidos - JSON malformado",
                    "data_quality_score": 0.0
                }

        if not isinstance(dados, dict):
            return {
                **_create_empty_diagnostico_tecnico_response(),
                "erro": "Formato de dados inválido",
                "data_quality_score": 0.0
            }

        # Extrair e normalizar dados principais - ASYNC
        diagnostico_lighthouse = await asyncio.to_thread(_normalize_lighthouse_data, dados.get("diagnostico_lighthouse", {}))
        analise_visual = await asyncio.to_thread(_normalize_visual_analysis, dados.get("analise_visual", {}))
        relatorio_consolidado = await asyncio.to_thread(_normalize_consolidated_report, dados.get("relatorio_consolidado", {}))

        # Calcular score de qualidade - ASYNC
        quality_score = await asyncio.to_thread(_calculate_diagnostico_quality_score, diagnostico_lighthouse, analise_visual, relatorio_consolidado)

        # Gerar metadata se não existir
        metadata = dados.get("metadata", {})
        if not metadata:
            metadata = {
                "parsed_at": datetime.now(timezone.utc).isoformat(),
                "parser_version": "1.0",
                "data_source": "lighthouse_playwright_ia"
            }

        return {
            "diagnostico_lighthouse": diagnostico_lighthouse,
            "analise_visual": analise_visual,
            "relatorio_consolidado": relatorio_consolidado,
            "data_quality_score": quality_score,
            "metadata": metadata
        }

    except Exception as e:
        return {
            **_create_empty_diagnostico_tecnico_response(),
            "erro": f"Erro no parse: {str(e)}",
            "data_quality_score": 0.0
        }


def _create_empty_diagnostico_tecnico_response() -> Dict[str, Any]:
    """Cria resposta vazia para diagnóstico técnico"""
    return {
        "diagnostico_lighthouse": {
            "performance": {"score": 0, "metricas_core": {}, "oportunidades": [], "diagnosticos": []},
            "acessibilidade": {"score": 0, "issues": [], "recomendacoes": []},
            "seo_tecnico": {"score": 0, "meta_tags": "não analisado", "estrutura_html": "não analisado", "mobile_friendly": "não analisado"},
            "best_practices": {"score": 0, "https": False, "vulnerabilidades": []}
        },
        "analise_visual": {
            "screenshots": {"desktop": "", "mobile": "", "metadata": {}},
            "analise_ui_ux": {"layout_quality": "não analisado", "user_experience": "não analisado", "visual_hierarchy": "não analisado", "responsive_design": "não analisado"},
            "recomendacoes_design": []
        },
        "relatorio_consolidado": {
            "score_geral": 0.0,
            "prioridades_melhorias": [],
            "impacto_business": "não avaliado",
            "timeline_implementacao": "não definido"
        },
        "data_quality_score": 0.0,
        "metadata": {}
    }


def _normalize_lighthouse_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """Normaliza dados Lighthouse"""
    try:
        normalized = {
            "performance": {
                "score": _safe_int(data.get("performance", {}).get("score", 0)),
                "metricas_core": data.get("performance", {}).get("metricas_core", {}),
                "oportunidades": data.get("performance", {}).get("oportunidades", []),
                "diagnosticos": data.get("performance", {}).get("diagnosticos", [])
            },
            "acessibilidade": {
                "score": _safe_int(data.get("acessibilidade", {}).get("score", 0)),
                "issues": data.get("acessibilidade", {}).get("issues", []),
                "recomendacoes": data.get("acessibilidade", {}).get("recomendacoes", [])
            },
            "seo_tecnico": {
                "score": _safe_int(data.get("seo_tecnico", {}).get("score", 0)),
                "meta_tags": data.get("seo_tecnico", {}).get("meta_tags", "não analisado"),
                "estrutura_html": data.get("seo_tecnico", {}).get("estrutura_html", "não analisado"),
                "mobile_friendly": data.get("seo_tecnico", {}).get("mobile_friendly", "não analisado")
            },
            "best_practices": {
                "score": _safe_int(data.get("best_practices", {}).get("score", 0)),
                "https": bool(data.get("best_practices", {}).get("https", False)),
                "vulnerabilidades": data.get("best_practices", {}).get("vulnerabilidades", [])
            }
        }

        # Validar métricas core
        metricas_core = normalized["performance"]["metricas_core"]
        core_metrics = ["fcp", "lcp", "cls", "fid", "ttfb"]
        for metric in core_metrics:
            if metric not in metricas_core:
                metricas_core[metric] = 0

        return normalized

    except Exception:
        return _create_empty_diagnostico_tecnico_response()["diagnostico_lighthouse"]


def _normalize_visual_analysis(data: Dict[str, Any]) -> Dict[str, Any]:
    """Normaliza dados de análise visual"""
    try:
        return {
            "screenshots": {
                "desktop": data.get("screenshots", {}).get("desktop", ""),
                "mobile": data.get("screenshots", {}).get("mobile", ""),
                "metadata": data.get("screenshots", {}).get("metadata", {})
            },
            "analise_ui_ux": {
                "layout_quality": data.get("analise_ui_ux", {}).get("layout_quality", "não analisado"),
                "user_experience": data.get("analise_ui_ux", {}).get("user_experience", "não analisado"),
                "visual_hierarchy": data.get("analise_ui_ux", {}).get("visual_hierarchy", "não analisado"),
                "responsive_design": data.get("analise_ui_ux", {}).get("responsive_design", "não analisado")
            },
            "recomendacoes_design": data.get("recomendacoes_design", [])
        }

    except Exception:
        return _create_empty_diagnostico_tecnico_response()["analise_visual"]


def _normalize_consolidated_report(data: Dict[str, Any]) -> Dict[str, Any]:
    """Normaliza relatório consolidado"""
    try:
        return {
            "score_geral": _safe_float(data.get("score_geral", 0.0)),
            "prioridades_melhorias": data.get("prioridades_melhorias", []),
            "impacto_business": data.get("impacto_business", "não avaliado"),
            "timeline_implementacao": data.get("timeline_implementacao", "não definido"),
            "resumo_executivo": data.get("resumo_executivo", ""),
            "metricas_chave": data.get("metricas_chave", {}),
            "recomendacoes_tecnicas": data.get("recomendacoes_tecnicas", [])
        }

    except Exception:
        return _create_empty_diagnostico_tecnico_response()["relatorio_consolidado"]


def _calculate_diagnostico_quality_score(lighthouse_data: Dict[str, Any], visual_analysis: Dict[str, Any], consolidated_report: Dict[str, Any]) -> float:
    """
    Calcula score de qualidade para dados de diagnóstico técnico

    Args:
        lighthouse_data: Dados Lighthouse normalizados
        visual_analysis: Dados de análise visual normalizados  
        consolidated_report: Relatório consolidado normalizado

    Returns:
        Score de qualidade (0.0-10.0)
    """
    try:
        score = 0.0

        # Avaliar completude dos dados Lighthouse (40% do score)
        lighthouse_score = 0.0
        lighthouse_categories = [
            "performance", "acessibilidade", "seo_tecnico", "best_practices"]

        for category in lighthouse_categories:
            if category in lighthouse_data:
                cat_data = lighthouse_data[category]
                if "score" in cat_data and isinstance(cat_data["score"], (int, float)):
                    lighthouse_score += 2.5  # 10 pontos / 4 categorias

                # Bonus para dados detalhados
                if category == "performance" and cat_data.get("metricas_core"):
                    core_metrics = ["fcp", "lcp", "cls", "fid", "ttfb"]
                    valid_metrics = sum(
                        1 for metric in core_metrics if cat_data["metricas_core"].get(metric, 0) > 0)
                    lighthouse_score += (valid_metrics /
                                         len(core_metrics)) * 1.0

        score += min(lighthouse_score, 4.0)

        # Avaliar qualidade da análise visual (30% do score)
        visual_score = 0.0
        visual_fields = ["layout_quality", "user_experience",
                         "visual_hierarchy", "responsive_design"]

        ui_ux_data = visual_analysis.get("analise_ui_ux", {})
        for field in visual_fields:
            value = ui_ux_data.get(field, "")
            if value and value != "não analisado":
                visual_score += 0.75  # 3 pontos / 4 campos

        # Bonus para screenshots válidos
        screenshots = visual_analysis.get("screenshots", {})
        if screenshots.get("desktop") and screenshots.get("mobile"):
            visual_score += 1.0

        # Bonus para recomendações
        if visual_analysis.get("recomendacoes_design"):
            visual_score += 0.25

        score += min(visual_score, 3.0)

        # Avaliar relatório consolidado (30% do score)
        report_score = 0.0

        # Score geral válido
        score_geral = consolidated_report.get("score_geral", 0)
        if isinstance(score_geral, (int, float)) and 0 <= score_geral <= 100:
            report_score += 1.0

        # Prioridades definidas
        if consolidated_report.get("prioridades_melhorias"):
            report_score += 1.0

        # Impacto e timeline definidos
        if consolidated_report.get("impacto_business") != "não avaliado":
            report_score += 0.5
        if consolidated_report.get("timeline_implementacao") != "não definido":
            report_score += 0.5

        score += min(report_score, 3.0)

        return round(min(score, 10.0), 1)

    except Exception:
        return 0.0


def _safe_int(value: Any, default: int = 0) -> int:
    """
    Converte valor para int de forma segura

    Args:
        value: Valor para conversão
        default: Valor padrão se conversão falhar

    Returns:
        Valor convertido para int ou padrão
    """
    try:
        if value is None:
            return default
        if isinstance(value, (int, float)):
            return int(value)
        if isinstance(value, str):
            # Remove caracteres não numéricos exceto ponto e vírgula
            cleaned = re.sub(r'[^\d.]', '', value.replace(',', '.'))
            if cleaned:
                return int(float(cleaned))
        return default
    except (ValueError, TypeError):
        return default


def _safe_float(value: Any, default: float = 0.0) -> float:
    """
    Converte valor para float de forma segura

    Args:
        value: Valor para conversão
        default: Valor padrão se conversão falhar

    Returns:
        Valor convertido para float ou padrão
    """
    try:
        if value is None:
            return default
        if isinstance(value, (int, float)):
            return float(value)
        if isinstance(value, str):
            # Remove caracteres não numéricos exceto ponto e vírgula
            cleaned = re.sub(r'[^\d.]', '', value.replace(',', '.'))
            if cleaned:
                return float(cleaned)
        return default
    except (ValueError, TypeError):
        return default
