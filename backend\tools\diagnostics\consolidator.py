"""
Módulo para consolidação de diagnósticos técnicos

Este módulo consolida resultados de:
- Análise Lighthouse (performance, acessibilidade, SEO)
- Screenshots e análise visual por IA
- Geração de relatório final estruturado
"""

from typing import Dict, Any, List, Optional
from datetime import datetime, timezone


class DiagnosticConsolidator:
    """
    Consolidador de diagnósticos técnicos

    Combina dados de diferentes fontes (Lighthouse, análise visual)
    em um relatório final estruturado e acionável.
    """

    def __init__(self):
        """Inicializa o consolidador"""
        self.weights = {
            "performance": 0.3,
            "acessibilidade": 0.25,
            "seo_tecnico": 0.25,
            "best_practices": 0.2
        }

    def build_final_report(self, lighthouse_data: Dict[str, Any], visual_analysis: Dict[str, Any], screenshots: Dict[str, Any]) -> Dict[str, Any]:
        """
        Constrói relatório final consolidado

        Args:
            lighthouse_data: Dados da análise Lighthouse
            visual_analysis: Resultado da análise visual por IA
            screenshots: Informações dos screenshots capturados

        Returns:
            Relatório final estruturado

        Raises:
            Exception: Se dados obrigatórios estiverem ausentes
        """
        if lighthouse_data is None or visual_analysis is None or screenshots is None:
            raise Exception("Dados obrigatórios ausentes para consolidação")

        try:
            # Calcular score geral
            score_geral = self._calculate_overall_score(lighthouse_data)

            # Identificar prioridades de melhoria
            prioridades = self._identify_improvement_priorities(
                lighthouse_data, visual_analysis)

            # Avaliar impacto nos negócios
            impacto_business = self._assess_business_impact(
                lighthouse_data, visual_analysis)

            # Gerar timeline de implementação
            timeline = self._generate_implementation_timeline(prioridades)

            # Consolidar recomendações de design
            recomendacoes_design = self._consolidate_design_recommendations(
                visual_analysis)

            return {
                "diagnostico_lighthouse": lighthouse_data,
                "analise_visual": {
                    "screenshots": screenshots,
                    "analise_ui_ux": visual_analysis,
                    "recomendacoes_design": recomendacoes_design
                },
                "relatorio_consolidado": {
                    "score_geral": score_geral,
                    "prioridades_melhorias": prioridades,
                    "impacto_business": impacto_business,
                    "timeline_implementacao": timeline,
                    "resumo_executivo": self._generate_executive_summary(score_geral, prioridades),
                    "metricas_chave": self._extract_key_metrics(lighthouse_data),
                    "recomendacoes_tecnicas": self._generate_technical_recommendations(lighthouse_data),
                    "data_analise": datetime.now(timezone.utc).isoformat()
                }
            }

        except Exception as e:
            # Retornar relatório básico em caso de erro
            return self._generate_fallback_report(lighthouse_data, visual_analysis, screenshots, str(e))

    def _calculate_overall_score(self, lighthouse_data: Dict[str, Any]) -> float:
        """
        Calcula score geral ponderado baseado nos scores Lighthouse

        Args:
            lighthouse_data: Dados Lighthouse

        Returns:
            Score geral (0-100)
        """
        try:
            scores = {}

            # Extrair scores das categorias
            if "performance" in lighthouse_data:
                scores["performance"] = lighthouse_data["performance"].get(
                    "score", 0)
            if "acessibilidade" in lighthouse_data:
                scores["acessibilidade"] = lighthouse_data["acessibilidade"].get(
                    "score", 0)
            if "seo_tecnico" in lighthouse_data:
                scores["seo_tecnico"] = lighthouse_data["seo_tecnico"].get(
                    "score", 0)
            if "best_practices" in lighthouse_data:
                scores["best_practices"] = lighthouse_data["best_practices"].get(
                    "score", 0)

            # Calcular média ponderada
            total_weight = 0
            weighted_sum = 0

            for category, score in scores.items():
                if category in self.weights:
                    weight = self.weights[category]
                    weighted_sum += score * weight
                    total_weight += weight

            if total_weight > 0:
                return round(weighted_sum / total_weight, 2)
            else:
                # Fallback para média simples
                return round(sum(scores.values()) / len(scores), 2) if scores else 0

        except Exception:
            return 0

    def _identify_improvement_priorities(self, lighthouse_data: Dict[str, Any], visual_analysis: Dict[str, Any]) -> List[str]:
        """
        Identifica prioridades de melhoria baseado nos scores e análises

        Args:
            lighthouse_data: Dados Lighthouse
            visual_analysis: Análise visual

        Returns:
            Lista de prioridades ordenadas
        """
        priorities = []

        try:
            # Analisar scores Lighthouse (priorizar scores baixos)
            if "performance" in lighthouse_data:
                score = lighthouse_data["performance"].get("score", 100)
                if score < 60:
                    priorities.append("Performance crítica")
                elif score < 80:
                    priorities.append("Otimização de performance")

            if "acessibilidade" in lighthouse_data:
                score = lighthouse_data["acessibilidade"].get("score", 100)
                if score < 70:
                    priorities.append("Melhorias de acessibilidade")

            if "seo_tecnico" in lighthouse_data:
                score = lighthouse_data["seo_tecnico"].get("score", 100)
                if score < 75:
                    priorities.append("Otimização SEO técnico")

            if "best_practices" in lighthouse_data:
                score = lighthouse_data["best_practices"].get("score", 100)
                if score < 80:
                    priorities.append("Implementação de best practices")

            # Analisar insights visuais
            if visual_analysis.get("layout_quality") in ["ruim", "regular"]:
                priorities.append("Redesign de layout")

            if visual_analysis.get("responsive_design") and "problema" in str(visual_analysis["responsive_design"]).lower():
                priorities.append("Melhorias de responsividade")

            # Limitar a 5 prioridades principais
            return priorities[:5] if priorities else ["Manutenção geral recomendada"]

        except Exception:
            return ["Análise manual necessária"]

    def _assess_business_impact(self, lighthouse_data: Dict[str, Any], visual_analysis: Dict[str, Any]) -> str:
        """
        Avalia impacto potencial nos negócios

        Args:
            lighthouse_data: Dados Lighthouse
            visual_analysis: Análise visual

        Returns:
            Descrição do impacto nos negócios
        """
        try:
            performance_score = lighthouse_data.get(
                "performance", {}).get("score", 100)
            accessibility_score = lighthouse_data.get(
                "acessibilidade", {}).get("score", 100)

            if performance_score < 50:
                return "Alto impacto: Performance baixa pode reduzir conversões em até 50% e prejudicar SEO"
            elif performance_score < 75:
                return "Médio impacto: Melhorias de performance podem aumentar conversões em 15-25%"
            elif accessibility_score < 60:
                return "Médio impacto: Problemas de acessibilidade limitam audiência e podem gerar questões legais"
            else:
                return "Baixo impacto: Site bem otimizado, melhorias incrementais recomendadas"

        except Exception:
            return "Impacto a ser avaliado após análise detalhada"

    def _generate_implementation_timeline(self, priorities: List[str]) -> str:
        """
        Gera timeline de implementação baseado nas prioridades

        Args:
            priorities: Lista de prioridades

        Returns:
            Timeline sugerido
        """
        try:
            if not priorities:
                return "Sem ações prioritárias identificadas"

            critical_items = [
                p for p in priorities if "crítica" in p.lower() or "alto" in p.lower()]
            medium_items = [p for p in priorities if p not in critical_items]

            if critical_items:
                return "1-2 semanas (crítico), 3-4 semanas (melhorias adicionais)"
            elif len(priorities) > 3:
                return "2-4 semanas (múltiplas melhorias)"
            else:
                return "1-3 semanas (melhorias pontuais)"

        except Exception:
            return "Timeline a ser definido"

    def _consolidate_design_recommendations(self, visual_analysis: Dict[str, Any]) -> List[str]:
        """
        Consolida recomendações de design da análise visual

        Args:
            visual_analysis: Dados da análise visual

        Returns:
            Lista de recomendações consolidadas
        """
        try:
            recommendations = []

            # Extrair recomendações existentes
            if "recomendacoes_design" in visual_analysis:
                recommendations.extend(visual_analysis["recomendacoes_design"])

            # Adicionar recomendações baseadas na análise
            if visual_analysis.get("layout_quality") == "ruim":
                recommendations.append(
                    "Revisar estrutura e organização visual")

            if visual_analysis.get("user_experience") and "difícil" in str(visual_analysis["user_experience"]).lower():
                recommendations.append(
                    "Simplificar navegação e fluxos de usuário")

            # Remover duplicatas e limitar
            unique_recommendations = list(dict.fromkeys(recommendations))
            return unique_recommendations[:8]  # Máximo 8 recomendações

        except Exception:
            return ["Análise visual detalhada recomendada"]

    def _generate_executive_summary(self, score_geral: float, priorities: List[str]) -> str:
        """
        Gera resumo executivo do diagnóstico

        Args:
            score_geral: Score geral do site
            priorities: Lista de prioridades

        Returns:
            Resumo executivo
        """
        try:
            if score_geral >= 85:
                status = "Excelente"
                description = "Site bem otimizado com poucos ajustes necessários"
            elif score_geral >= 70:
                status = "Bom"
                description = "Site em boa condição com oportunidades de melhoria"
            elif score_geral >= 50:
                status = "Regular"
                description = "Site necessita melhorias significativas"
            else:
                status = "Crítico"
                description = "Site requer intervenção urgente"

            priority_summary = f"Principais focos: {', '.join(priorities[:3])}" if priorities else "Sem prioridades críticas"

            return f"{status} (Score: {score_geral}). {description}. {priority_summary}."

        except Exception:
            return "Resumo a ser gerado após análise completa"

    def _extract_key_metrics(self, lighthouse_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extrai métricas-chave do Lighthouse

        Args:
            lighthouse_data: Dados Lighthouse

        Returns:
            Métricas-chave estruturadas
        """
        try:
            metrics = {}

            # Core Web Vitals
            if "performance" in lighthouse_data and "metricas_core" in lighthouse_data["performance"]:
                core_metrics = lighthouse_data["performance"]["metricas_core"]
                metrics["core_web_vitals"] = {
                    "lcp": f"{core_metrics.get('lcp', 0)}ms",
                    "fcp": f"{core_metrics.get('fcp', 0)}ms",
                    "cls": core_metrics.get('cls', 0),
                    "fid": f"{core_metrics.get('fid', 0)}ms"
                }

            # Scores por categoria
            metrics["scores"] = {
                "performance": lighthouse_data.get("performance", {}).get("score", 0),
                "acessibilidade": lighthouse_data.get("acessibilidade", {}).get("score", 0),
                "seo": lighthouse_data.get("seo_tecnico", {}).get("score", 0),
                "best_practices": lighthouse_data.get("best_practices", {}).get("score", 0)
            }

            return metrics

        except Exception:
            return {"erro": "Métricas não disponíveis"}

    def _generate_technical_recommendations(self, lighthouse_data: Dict[str, Any]) -> List[str]:
        """
        Gera recomendações técnicas específicas

        Args:
            lighthouse_data: Dados Lighthouse

        Returns:
            Lista de recomendações técnicas
        """
        try:
            recommendations = []

            # Recomendações de performance
            if "performance" in lighthouse_data:
                perf_data = lighthouse_data["performance"]
                if perf_data.get("score", 100) < 75:
                    if "oportunidades" in perf_data:
                        recommendations.extend(perf_data["oportunidades"][:3])
                    else:
                        recommendations.append("Otimizar imagens e recursos")
                        recommendations.append("Minimizar JavaScript e CSS")

            # Recomendações de acessibilidade
            if "acessibilidade" in lighthouse_data:
                acc_data = lighthouse_data["acessibilidade"]
                if acc_data.get("score", 100) < 80:
                    if "recomendacoes" in acc_data:
                        recommendations.extend(acc_data["recomendacoes"][:2])
                    else:
                        recommendations.append("Melhorar contraste de cores")
                        recommendations.append(
                            "Adicionar labels em formulários")

            # Recomendações de SEO
            if "seo_tecnico" in lighthouse_data:
                seo_data = lighthouse_data["seo_tecnico"]
                if seo_data.get("score", 100) < 80:
                    recommendations.append(
                        "Otimizar meta tags e estrutura HTML")
                    if seo_data.get("mobile_friendly") != "sim":
                        recommendations.append(
                            "Implementar design mobile-first")

            return recommendations[:6]  # Máximo 6 recomendações técnicas

        except Exception:
            return ["Análise técnica detalhada recomendada"]

    def _generate_fallback_report(self, lighthouse_data: Dict[str, Any], visual_analysis: Dict[str, Any], screenshots: Dict[str, Any], error: str) -> Dict[str, Any]:
        """
        Gera relatório básico em caso de erro na consolidação

        Args:
            lighthouse_data: Dados Lighthouse (podem estar incompletos)
            visual_analysis: Análise visual (pode estar incompleta)
            screenshots: Screenshots (podem estar incompletos)
            error: Descrição do erro

        Returns:
            Relatório básico de fallback
        """
        from shared.data_quality import DataQuality, wrap_with_quality
        
        fallback_report = {
            "diagnostico_lighthouse": lighthouse_data or {},
            "analise_visual": {
                "screenshots": screenshots or {},
                "analise_ui_ux": visual_analysis or {},
                "recomendacoes_design": ["Análise manual necessária - erro na consolidação automática"]
            },
            "relatorio_consolidado": {
                "score_geral": 0,
                "prioridades_melhorias": ["Análise manual necessária - erro na consolidação automática"],
                "impacto_business": "A ser avaliado",
                "timeline_implementacao": "A ser definido",
                "resumo_executivo": f"Erro na consolidação: {error} [DADOS DE FALLBACK]",
                "metricas_chave": {},
                "recomendacoes_tecnicas": ["Executar análise novamente"],
                "data_analise": datetime.now(timezone.utc).isoformat(),
                "erro_consolidacao": error
            }
        }
        
        return wrap_with_quality(
            data=fallback_report,
            quality=DataQuality.ERROR_FALLBACK,
            source="diagnostic_consolidator_error",
            confidence_score=0.0,
            fallback_reason=f"Erro na consolidação de diagnóstico: {error}"
        )
