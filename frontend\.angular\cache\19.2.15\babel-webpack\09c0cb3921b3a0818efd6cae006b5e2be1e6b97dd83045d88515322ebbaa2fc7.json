{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component } from '@angular/core';\nimport { uuid } from '@primeuix/utils';\nimport { BaseIcon } from 'primeng/icons/baseicon';\nclass BanIcon extends BaseIcon {\n  pathId;\n  ngOnInit() {\n    this.pathId = 'url(#' + uuid() + ')';\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵBanIcon_BaseFactory;\n    return function BanIcon_Factory(__ngFactoryType__) {\n      return (ɵBanIcon_BaseFactory || (ɵBanIcon_BaseFactory = i0.ɵɵgetInheritedFactory(BanIcon)))(__ngFactoryType__ || BanIcon);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: BanIcon,\n    selectors: [[\"BanIcon\"]],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    decls: 6,\n    vars: 7,\n    consts: [[\"width\", \"14\", \"height\", \"14\", \"viewBox\", \"0 0 14 14\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"d\", \"M7 0C5.61553 0 4.26215 0.410543 3.11101 1.17971C1.95987 1.94888 1.06266 3.04213 0.532846 4.32122C0.00303296 5.6003 -0.13559 7.00776 0.134506 8.36563C0.404603 9.7235 1.07129 10.9708 2.05026 11.9497C3.02922 12.9287 4.2765 13.5954 5.63437 13.8655C6.99224 14.1356 8.3997 13.997 9.67879 13.4672C10.9579 12.9373 12.0511 12.0401 12.8203 10.889C13.5895 9.73785 14 8.38447 14 7C14 5.14348 13.2625 3.36301 11.9497 2.05025C10.637 0.737498 8.85652 0 7 0ZM1.16667 7C1.16549 5.65478 1.63303 4.35118 2.48889 3.31333L10.6867 11.5111C9.83309 12.2112 8.79816 12.6544 7.70243 12.789C6.60669 12.9236 5.49527 12.744 4.49764 12.2713C3.50001 11.7986 2.65724 11.0521 2.06751 10.1188C1.47778 9.18558 1.16537 8.10397 1.16667 7ZM11.5111 10.6867L3.31334 2.48889C4.43144 1.57388 5.84966 1.10701 7.29265 1.1789C8.73565 1.2508 10.1004 1.85633 11.1221 2.87795C12.1437 3.89956 12.7492 5.26435 12.8211 6.70735C12.893 8.15034 12.4261 9.56856 11.5111 10.6867Z\", \"fill\", \"currentColor\"], [3, \"id\"], [\"width\", \"14\", \"height\", \"14\", \"fill\", \"white\"]],\n    template: function BanIcon_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(0, \"svg\", 0)(1, \"g\");\n        i0.ɵɵelement(2, \"path\", 1);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"defs\")(4, \"clipPath\", 2);\n        i0.ɵɵelement(5, \"rect\", 3);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.getClassNames());\n        i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel)(\"aria-hidden\", ctx.ariaHidden)(\"role\", ctx.role);\n        i0.ɵɵadvance();\n        i0.ɵɵattribute(\"clip-path\", ctx.pathId);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"id\", ctx.pathId);\n      }\n    },\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BanIcon, [{\n    type: Component,\n    args: [{\n      selector: 'BanIcon',\n      standalone: true,\n      template: `\n        <svg width=\"14\" height=\"14\" viewBox=\"0 0 14 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" [attr.aria-label]=\"ariaLabel\" [attr.aria-hidden]=\"ariaHidden\" [attr.role]=\"role\" [class]=\"getClassNames()\">\n            <g [attr.clip-path]=\"pathId\">\n                <path\n                    d=\"M7 0C5.61553 0 4.26215 0.410543 3.11101 1.17971C1.95987 1.94888 1.06266 3.04213 0.532846 4.32122C0.00303296 5.6003 -0.13559 7.00776 0.134506 8.36563C0.404603 9.7235 1.07129 10.9708 2.05026 11.9497C3.02922 12.9287 4.2765 13.5954 5.63437 13.8655C6.99224 14.1356 8.3997 13.997 9.67879 13.4672C10.9579 12.9373 12.0511 12.0401 12.8203 10.889C13.5895 9.73785 14 8.38447 14 7C14 5.14348 13.2625 3.36301 11.9497 2.05025C10.637 0.737498 8.85652 0 7 0ZM1.16667 7C1.16549 5.65478 1.63303 4.35118 2.48889 3.31333L10.6867 11.5111C9.83309 12.2112 8.79816 12.6544 7.70243 12.789C6.60669 12.9236 5.49527 12.744 4.49764 12.2713C3.50001 11.7986 2.65724 11.0521 2.06751 10.1188C1.47778 9.18558 1.16537 8.10397 1.16667 7ZM11.5111 10.6867L3.31334 2.48889C4.43144 1.57388 5.84966 1.10701 7.29265 1.1789C8.73565 1.2508 10.1004 1.85633 11.1221 2.87795C12.1437 3.89956 12.7492 5.26435 12.8211 6.70735C12.893 8.15034 12.4261 9.56856 11.5111 10.6867Z\"\n                    fill=\"currentColor\"\n                />\n            </g>\n            <defs>\n                <clipPath [id]=\"pathId\">\n                    <rect width=\"14\" height=\"14\" fill=\"white\" />\n                </clipPath>\n            </defs>\n        </svg>\n    `\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BanIcon };", "map": {"version": 3, "names": ["i0", "Component", "uuid", "BaseIcon", "BanIcon", "pathId", "ngOnInit", "ɵfac", "ɵBanIcon_BaseFactory", "BanIcon_Factory", "__ngFactoryType__", "ɵɵgetInheritedFactory", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "BanIcon_Template", "rf", "ctx", "ɵɵnamespaceSVG", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵclassMap", "getClassNames", "ɵɵattribute", "aria<PERSON><PERSON><PERSON>", "ariaHidden", "role", "ɵɵadvance", "ɵɵproperty", "encapsulation", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "standalone"], "sources": ["C:/projetos/scope-ai/frontend/node_modules/primeng/fesm2022/primeng-icons-ban.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component } from '@angular/core';\nimport { uuid } from '@primeuix/utils';\nimport { BaseIcon } from 'primeng/icons/baseicon';\n\nclass BanIcon extends BaseIcon {\n    pathId;\n    ngOnInit() {\n        this.pathId = 'url(#' + uuid() + ')';\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: BanIcon, deps: null, target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.10\", type: BanIcon, isStandalone: true, selector: \"BanIcon\", usesInheritance: true, ngImport: i0, template: `\n        <svg width=\"14\" height=\"14\" viewBox=\"0 0 14 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" [attr.aria-label]=\"ariaLabel\" [attr.aria-hidden]=\"ariaHidden\" [attr.role]=\"role\" [class]=\"getClassNames()\">\n            <g [attr.clip-path]=\"pathId\">\n                <path\n                    d=\"M7 0C5.61553 0 4.26215 0.410543 3.11101 1.17971C1.95987 1.94888 1.06266 3.04213 0.532846 4.32122C0.00303296 5.6003 -0.13559 7.00776 0.134506 8.36563C0.404603 9.7235 1.07129 10.9708 2.05026 11.9497C3.02922 12.9287 4.2765 13.5954 5.63437 13.8655C6.99224 14.1356 8.3997 13.997 9.67879 13.4672C10.9579 12.9373 12.0511 12.0401 12.8203 10.889C13.5895 9.73785 14 8.38447 14 7C14 5.14348 13.2625 3.36301 11.9497 2.05025C10.637 0.737498 8.85652 0 7 0ZM1.16667 7C1.16549 5.65478 1.63303 4.35118 2.48889 3.31333L10.6867 11.5111C9.83309 12.2112 8.79816 12.6544 7.70243 12.789C6.60669 12.9236 5.49527 12.744 4.49764 12.2713C3.50001 11.7986 2.65724 11.0521 2.06751 10.1188C1.47778 9.18558 1.16537 8.10397 1.16667 7ZM11.5111 10.6867L3.31334 2.48889C4.43144 1.57388 5.84966 1.10701 7.29265 1.1789C8.73565 1.2508 10.1004 1.85633 11.1221 2.87795C12.1437 3.89956 12.7492 5.26435 12.8211 6.70735C12.893 8.15034 12.4261 9.56856 11.5111 10.6867Z\"\n                    fill=\"currentColor\"\n                />\n            </g>\n            <defs>\n                <clipPath [id]=\"pathId\">\n                    <rect width=\"14\" height=\"14\" fill=\"white\" />\n                </clipPath>\n            </defs>\n        </svg>\n    `, isInline: true });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: BanIcon, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'BanIcon',\n                    standalone: true,\n                    template: `\n        <svg width=\"14\" height=\"14\" viewBox=\"0 0 14 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" [attr.aria-label]=\"ariaLabel\" [attr.aria-hidden]=\"ariaHidden\" [attr.role]=\"role\" [class]=\"getClassNames()\">\n            <g [attr.clip-path]=\"pathId\">\n                <path\n                    d=\"M7 0C5.61553 0 4.26215 0.410543 3.11101 1.17971C1.95987 1.94888 1.06266 3.04213 0.532846 4.32122C0.00303296 5.6003 -0.13559 7.00776 0.134506 8.36563C0.404603 9.7235 1.07129 10.9708 2.05026 11.9497C3.02922 12.9287 4.2765 13.5954 5.63437 13.8655C6.99224 14.1356 8.3997 13.997 9.67879 13.4672C10.9579 12.9373 12.0511 12.0401 12.8203 10.889C13.5895 9.73785 14 8.38447 14 7C14 5.14348 13.2625 3.36301 11.9497 2.05025C10.637 0.737498 8.85652 0 7 0ZM1.16667 7C1.16549 5.65478 1.63303 4.35118 2.48889 3.31333L10.6867 11.5111C9.83309 12.2112 8.79816 12.6544 7.70243 12.789C6.60669 12.9236 5.49527 12.744 4.49764 12.2713C3.50001 11.7986 2.65724 11.0521 2.06751 10.1188C1.47778 9.18558 1.16537 8.10397 1.16667 7ZM11.5111 10.6867L3.31334 2.48889C4.43144 1.57388 5.84966 1.10701 7.29265 1.1789C8.73565 1.2508 10.1004 1.85633 11.1221 2.87795C12.1437 3.89956 12.7492 5.26435 12.8211 6.70735C12.893 8.15034 12.4261 9.56856 11.5111 10.6867Z\"\n                    fill=\"currentColor\"\n                />\n            </g>\n            <defs>\n                <clipPath [id]=\"pathId\">\n                    <rect width=\"14\" height=\"14\" fill=\"white\" />\n                </clipPath>\n            </defs>\n        </svg>\n    `\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BanIcon };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,QAAQ,eAAe;AACzC,SAASC,IAAI,QAAQ,iBAAiB;AACtC,SAASC,QAAQ,QAAQ,wBAAwB;AAEjD,MAAMC,OAAO,SAASD,QAAQ,CAAC;EAC3BE,MAAM;EACNC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACD,MAAM,GAAG,OAAO,GAAGH,IAAI,CAAC,CAAC,GAAG,GAAG;EACxC;EACA,OAAOK,IAAI;IAAA,IAAAC,oBAAA;IAAA,gBAAAC,gBAAAC,iBAAA;MAAA,QAAAF,oBAAA,KAAAA,oBAAA,GAA+ER,EAAE,CAAAW,qBAAA,CAAQP,OAAO,IAAAM,iBAAA,IAAPN,OAAO;IAAA;EAAA;EAC3G,OAAOQ,IAAI,kBAD+EZ,EAAE,CAAAa,iBAAA;IAAAC,IAAA,EACJV,OAAO;IAAAW,SAAA;IAAAC,QAAA,GADLhB,EAAE,CAAAiB,0BAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,iBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFvB,EAAE,CAAAyB,cAAA;QAAFzB,EAAE,CAAA0B,cAAA,YAEiH,CAAC,OAC1K,CAAC;QAHqD1B,EAAE,CAAA2B,SAAA,aAO/E,CAAC;QAP4E3B,EAAE,CAAA4B,YAAA,CAQjF,CAAC;QAR8E5B,EAAE,CAAA0B,cAAA,UAS/E,CAAC,iBACqB,CAAC;QAVsD1B,EAAE,CAAA2B,SAAA,aAWjC,CAAC;QAX8B3B,EAAE,CAAA4B,YAAA,CAYtE,CAAC,CACT,CAAC,CACN,CAAC;MAAA;MAAA,IAAAL,EAAA;QAdgFvB,EAAE,CAAA6B,UAAA,CAAAL,GAAA,CAAAM,aAAA,EAEgH,CAAC;QAFnH9B,EAAE,CAAA+B,WAAA,eAAAP,GAAA,CAAAQ,SAAA,iBAAAR,GAAA,CAAAS,UAAA,UAAAT,GAAA,CAAAU,IAAA;QAAFlC,EAAE,CAAAmC,SAAA,CAGzD,CAAC;QAHsDnC,EAAE,CAAA+B,WAAA,cAAAP,GAAA,CAAAnB,MAAA;QAAFL,EAAE,CAAAmC,SAAA,EAU1D,CAAC;QAVuDnC,EAAE,CAAAoC,UAAA,OAAAZ,GAAA,CAAAnB,MAU1D,CAAC;MAAA;IAAA;IAAAgC,aAAA;EAAA;AAMvC;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAjB8FtC,EAAE,CAAAuC,iBAAA,CAiBJnC,OAAO,EAAc,CAAC;IACtGU,IAAI,EAAEb,SAAS;IACfuC,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,SAAS;MACnBC,UAAU,EAAE,IAAI;MAChBrB,QAAQ,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACgB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASjB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}