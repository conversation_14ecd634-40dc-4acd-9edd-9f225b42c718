# Mapeamento de Dependências - Client Domain

**Data**: 2025-01-29  
**Status**: ✅ CONCLUÍDO  
**Tempo**: 2h  
**Score**: 9/10

## 📊 Resumo Executivo

O domínio de clientes está altamente acoplado com 2.417 linhas no routes.py, múltiplas dependências externas e lógica espalhada por vários arquivos massivos. A migração exigirá cuidadosa extração e refatoração.

## 🗺️ Arquitetura Atual

### Arquivos Core
1. **routes.py** (2.417 linhas)
   - 16 endpoints principais
   - Lógica de negócio misturada
   - WebSocket integrado
   - Background tasks pesadas

2. **model.py** (297 linhas)
   - ClientCreate, ClientDB, ClientResponse
   - 15+ modelos de dados específicos
   - Validações Pydantic

3. **schemas.py** (2.554 linhas)
   - Schemas MongoDB complexos
   - Validações de estrutura
   - Índices e constraints

4. **db.py** / **async_db.py**
   - Conexões MongoDB síncronas e assíncronas
   - Collections: clients, projetos
   - GridFS para PDFs

## 📍 Endpoints Mapeados

### Clientes Core
- `POST /clients/quick` - Cadastro rápido (FASE 1)
- `POST /clients` - Cadastro completo (legacy)
- `GET /clients` - Listar todos
- `GET /clients/{id}/funding-data` - Dados de funding
- `GET /clients/{id}/enhanced-report` - Download PDF
- `GET /clients/{id}/report` - Download relatório

### Processamento
- `POST /clients/{id}/regenerate-funding-data` - Regenerar funding
- `POST /clients/{id}/generate-projects` - Gerar projetos
- `POST /clients/{id}/force-enhanced-report` - Forçar PDF (deprecated)
- `POST /clients/{id}/generate-enhanced-report` - Gerar PDF manual
- `POST /clients/{id}/generate-optimized-dossie` - Dossiê otimizado

### WebSocket
- `WS /clients/notifications` - Notificações real-time

### Outros
- `GET /performance/stats` - Estatísticas
- `POST /cache/invalidate/{empresa}` - Invalidar cache

## 🔗 Dependências Externas

### 1. **Perplexity Integration**
```python
from .perplexity import (
    gerar_dossie_perplexity,
    gerar_relatorio_basico,
    classificar_setor_simples
)
```
- API externa para análise de empresas
- Processamento pesado (~3-5 min)
- Cache implementado

### 2. **PDF Generation**
```python
from tools.reports.pdf_generator_enhanced import (
    EnhancedPDFGenerator,
    AIReportGeneratorEnhanced
)
```
- Geração de PDFs com 15+ seções
- Armazenamento em GridFS
- Background processing

### 3. **WebSocket Manager**
```python
from shared.websocket_manager import websocket_manager
```
- 25+ broadcasts diferentes
- Notificações de status
- Updates real-time

### 4. **Project Generation**
```python
from .project_generation_routes import router as project_generation_router
```
- Lógica separada mas acoplada
- Team Agno integration
- Estimativas complexas

### 5. **Tools & Diagnostics**
```python
from tools.diagnostics import (
    LighthouseAnalyzer,
    PlaywrightScreenshots,
    VisualAnalyzer
)
```
- Análises técnicas de sites
- Screenshots automatizados
- IA visual analysis

## 🗄️ Banco de Dados

### Collections Principais
1. **clients**
   - Dados básicos do cliente
   - Reports array (embedded)
   - Status e timestamps
   - Referências a projetos

2. **projetos**
   - Projetos sugeridos/aceitos
   - Vínculo com cliente_id
   - Estimativas e progresso
   - Team Agno results

### GridFS
- Armazenamento de PDFs
- Reports em HTML/PDF
- Podcasts gerados
- Screenshots

### Índices Críticos
```javascript
// Índices existentes identificados
{ "name": 1 }
{ "created_at": -1 }
{ "status": 1, "created_at": -1 }
{ "reports.reportType": 1 }
```

## 🌐 WebSocket Events

### Client Lifecycle
1. `client_created` - Novo cliente
2. `collection_status_update` - Status coleta
3. `ready_for_projects` - Pronto para projetos
4. `client_status_update` - Update geral

### Project Generation
1. `projects_generation_started` - Início
2. `project_created` - Projeto criado
3. `projects_generated` - Conclusão
4. `projects_generation_error` - Erro

### PDF Generation
1. `pdf_generation_started` - Início
2. `pdf_generation_completed` - Sucesso
3. `pdf_generation_error` - Falha

## 🔄 Background Tasks

### Principais Processamentos
1. **processar_relatorio_completo**
   - Gera dossiê via Perplexity
   - Trigger PDF automático
   - ~10 minutos total

2. **processar_geracao_projetos**
   - Analisa dossiê
   - Gera 5-10 projetos
   - Calcula scores

3. **processar_enhanced_report**
   - Gera PDF expandido
   - 15+ seções
   - GridFS storage

## 🚨 Acoplamentos Críticos

### 1. **Circular Dependencies**
- routes.py importa de api.report_routes
- api.report_routes importa de clients.*
- Necessita refatoração

### 2. **Shared State**
- WebSocket manager global
- Cache manager compartilhado
- MongoDB connections

### 3. **Business Logic Spread**
- Lógica em routes.py
- Parsers com 2.878 linhas
- Validações duplicadas

### 4. **External APIs**
- Perplexity API crítica
- OpenAI dependencies
- Mistral/Llama for agents

## 📋 Checklist de Migração

### Fase 1: Preparação
- [ ] Criar nova estrutura de microserviço
- [ ] Definir interfaces/contratos
- [ ] Mapear todos os use cases
- [ ] Identificar shared kernel

### Fase 2: Domain Layer
- [ ] Extrair entidades (Client, Report)
- [ ] Value Objects (ClientId, ClientStatus)
- [ ] Domain Events
- [ ] Business rules

### Fase 3: Application Layer
- [ ] Use Cases isolados
- [ ] DTOs definidos
- [ ] Command/Query handlers
- [ ] Service interfaces

### Fase 4: Infrastructure
- [ ] Repository pattern
- [ ] External service adapters
- [ ] Event publishing
- [ ] Database migrations

### Fase 5: API Layer
- [ ] REST controllers
- [ ] OpenAPI docs
- [ ] Versioning strategy
- [ ] Backward compatibility

## 🎯 Riscos Identificados

1. **Alta Complexidade**
   - Arquivos com 2k+ linhas
   - Lógica entrelaçada
   - Múltiplas responsabilidades

2. **Dependências Externas**
   - Perplexity API crítica
   - WebSocket real-time
   - Background processing

3. **Estado Compartilhado**
   - Cache global
   - WebSocket manager
   - MongoDB connections

4. **Compatibilidade**
   - Frontend expectations
   - API contracts
   - Event formats

## 📊 Métricas

- **Total de Linhas**: ~10.000 no domínio
- **Endpoints**: 16 principais
- **Dependências Externas**: 8+
- **WebSocket Events**: 15+
- **Background Tasks**: 5 principais
- **Complexidade Ciclomática**: Alta (>50)

## ✅ Conclusão

O mapeamento revelou um domínio altamente complexo e acoplado. A migração precisará ser feita com extremo cuidado, mantendo compatibilidade total durante a transição. A estratégia de Strangler Fig Pattern com database compartilhado temporário é a mais prudente.

**Próximo Passo**: MT-4 - Criar estrutura do microserviço com Clean Architecture. 