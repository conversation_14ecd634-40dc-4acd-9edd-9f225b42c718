"""
Validador de sintaxe Mermaid para garantir diagramas corretos
"""

import re
from typing import Dict, List, Tuple, Optional
from enum import Enum


class DiagramType(Enum):
    """Tipos de diagramas Mermaid suportados"""
    ERD = "erDiagram"
    FLOWCHART = "flowchart"
    SEQUENCE = "sequenceDiagram"


class MermaidValidator:
    """Validador de sintaxe Mermaid"""
    
    def __init__(self):
        self.errors = []
        self.warnings = []
    
    def validate_diagram(self, diagram_code: str, expected_type: Optional[DiagramType] = None) -> Dict:
        """
        Valida um diagrama Mermaid e retorna resultado da validação
        
        Args:
            diagram_code: Código do diagrama Mermaid
            expected_type: Tipo esperado do diagrama
            
        Returns:
            Dict com resultado da validação
        """
        self.errors = []
        self.warnings = []
        
        if not diagram_code or not diagram_code.strip():
            self.errors.append("Diagrama vazio")
            return self._build_result(False)
        
        # Limpar código
        clean_code = self._clean_diagram_code(diagram_code)
        
        # Detectar tipo do diagrama
        detected_type = self._detect_diagram_type(clean_code)
        
        if not detected_type:
            self.errors.append("Tipo de diagrama não detectado ou inválido")
            return self._build_result(False)
        
        # Validar tipo esperado
        if expected_type and detected_type != expected_type:
            self.errors.append(f"Tipo esperado: {expected_type.value}, detectado: {detected_type.value}")
        
        # Validações específicas por tipo
        if detected_type == DiagramType.ERD:
            self._validate_erd(clean_code)
        elif detected_type == DiagramType.FLOWCHART:
            self._validate_flowchart(clean_code)
        elif detected_type == DiagramType.SEQUENCE:
            self._validate_sequence(clean_code)
        
        # Validações gerais
        self._validate_general_syntax(clean_code)
        
        is_valid = len(self.errors) == 0
        return self._build_result(is_valid, detected_type, clean_code)
    
    def _clean_diagram_code(self, code: str) -> str:
        """Limpa o código do diagrama removendo prefixos e formatação desnecessária"""
        # Remover blocos de código markdown
        code = re.sub(r'```mermaid\s*\n?', '', code)
        code = re.sub(r'```\s*$', '', code)
        
        # Remover prefixo "mermaid" solto
        code = re.sub(r'^mermaid\s*\n?', '', code, flags=re.MULTILINE)
        
        # Limpar espaços extras
        code = code.strip()
        
        return code
    
    def _detect_diagram_type(self, code: str) -> Optional[DiagramType]:
        """Detecta o tipo do diagrama baseado no código"""
        first_line = code.split('\n')[0].strip().lower()
        
        if first_line.startswith('erdiagram'):
            return DiagramType.ERD
        elif first_line.startswith('flowchart'):
            return DiagramType.FLOWCHART
        elif first_line.startswith('sequencediagram'):
            return DiagramType.SEQUENCE
        
        return None
    
    def _validate_erd(self, code: str) -> None:
        """Valida diagrama ERD"""
        lines = [line.strip() for line in code.split('\n') if line.strip()]
        
        if not lines[0].lower().startswith('erdiagram'):
            self.errors.append("ERD deve começar com 'erDiagram'")
            return
        
        # Verificar relacionamentos válidos
        valid_relationships = ['||--||', '||--o{', '}o--||', '}o--o{', '||--o|', '|o--||', '||..||', '||..o{', '}o..||', '}o..o{']
        
        for i, line in enumerate(lines[1:], 2):
            if not line:
                continue
                
            # Verificar se é um relacionamento
            has_relationship = any(rel in line for rel in valid_relationships)
            
            if has_relationship:
                # Validar formato: Entidade1 ||--o{ Entidade2 : relacionamento
                pattern = r'^[A-Za-z_][A-Za-z0-9_]*\s+(\|\|--\|\||\|\|--o\{|\}o--\|\||\}o--o\{|\|\|--o\||\|o--\|\||\|\|\.\.\|\||\|\|\.\.o\{|\}o\.\.\|\||\}o\.\.o\{)\s+[A-Za-z_][A-Za-z0-9_]*\s*:\s*.+'
                if not re.match(pattern, line):
                    self.errors.append(f"Linha {i}: Relacionamento ERD inválido: {line}")
            else:
                # Verificar se não é uma definição de tabela SQL (erro comum)
                if '{' in line or '}' in line or 'PK' in line or 'FK' in line:
                    self.errors.append(f"Linha {i}: Sintaxe SQL detectada em ERD. Use relacionamentos Mermaid: {line}")
    
    def _validate_flowchart(self, code: str) -> None:
        """Valida diagrama Flowchart"""
        lines = [line.strip() for line in code.split('\n') if line.strip()]

        # Verificar se é C4Context (erro comum)
        if any('C4Context' in line or 'Person' in line or 'System' in line or 'Container' in line for line in lines):
            self.errors.append("Detectado C4Context em flowchart. Use apenas sintaxe flowchart TD")
            return

        if not lines[0].lower().startswith('flowchart'):
            self.errors.append("Flowchart deve começar com 'flowchart TD' ou 'flowchart LR'")
            return

        # Verificar direção válida
        first_line = lines[0].lower()
        valid_directions = ['td', 'lr', 'rl', 'bt']
        direction_found = any(direction in first_line for direction in valid_directions)

        if not direction_found:
            self.warnings.append("Direção do flowchart não especificada (TD, LR, RL, BT)")

        # Verificar se tem conexões
        has_connections = any('-->' in line or '---' in line for line in lines[1:])
        if not has_connections:
            self.errors.append("Flowchart deve ter pelo menos uma conexão (-->, ---)")
    
    def _validate_sequence(self, code: str) -> None:
        """Valida diagrama de Sequência"""
        lines = [line.strip() for line in code.split('\n') if line.strip()]
        
        if not lines[0].lower().startswith('sequencediagram'):
            self.errors.append("Diagrama de sequência deve começar com 'sequenceDiagram'")
            return
        
        # Verificar se tem mensagens
        has_messages = any('->' in line or '->>' in line for line in lines[1:])
        if not has_messages:
            self.errors.append("Diagrama de sequência deve ter pelo menos uma mensagem (->, ->>)")
        
        # Verificar participantes
        participants = set()
        for line in lines[1:]:
            if 'participant' in line.lower():
                continue
            
            # Extrair participantes das mensagens
            if '->' in line or '->>' in line:
                parts = re.split(r'->|->>', line)
                if len(parts) >= 2:
                    sender = parts[0].strip()
                    receiver = parts[1].split(':')[0].strip()
                    participants.add(sender)
                    participants.add(receiver)
        
        if len(participants) < 2:
            self.warnings.append("Diagrama de sequência deve ter pelo menos 2 participantes")
    

    
    def _validate_general_syntax(self, code: str) -> None:
        """Validações gerais de sintaxe"""
        # Verificar caracteres problemáticos
        if '\\n' in code:
            self.warnings.append("Contém caracteres de escape \\n")
        
        # Verificar se é muito curto
        if len(code) < 20:
            self.warnings.append("Diagrama muito curto (< 20 caracteres)")
        
        # Verificar linhas vazias excessivas
        empty_lines = code.count('\n\n\n')
        if empty_lines > 2:
            self.warnings.append("Muitas linhas vazias consecutivas")
    
    def _build_result(self, is_valid: bool, diagram_type: Optional[DiagramType] = None, clean_code: str = "") -> Dict:
        """Constrói resultado da validação"""
        return {
            "is_valid": is_valid,
            "diagram_type": diagram_type.value if diagram_type else None,
            "clean_code": clean_code,
            "errors": self.errors.copy(),
            "warnings": self.warnings.copy(),
            "error_count": len(self.errors),
            "warning_count": len(self.warnings)
        }
    
    def fix_common_issues(self, diagram_code: str, diagram_type: DiagramType) -> str:
        """
        Corrige problemas comuns em diagramas Mermaid
        
        Args:
            diagram_code: Código do diagrama
            diagram_type: Tipo do diagrama
            
        Returns:
            Código corrigido
        """
        # Limpar código
        fixed_code = self._clean_diagram_code(diagram_code)
        
        if diagram_type == DiagramType.ERD:
            fixed_code = self._fix_erd_issues(fixed_code)
        elif diagram_type == DiagramType.SEQUENCE:
            fixed_code = self._fix_sequence_issues(fixed_code)
        elif diagram_type == DiagramType.FLOWCHART:
            fixed_code = self._fix_flowchart_issues(fixed_code)
        
        return fixed_code
    
    def _fix_erd_issues(self, code: str) -> str:
        """Corrige problemas comuns em ERD"""
        lines = code.split('\n')
        
        # Garantir que começa com erDiagram
        if not lines[0].strip().lower().startswith('erdiagram'):
            lines[0] = 'erDiagram'
        
        # Remover definições de tabela SQL e converter para relacionamentos
        fixed_lines = [lines[0]]  # Manter cabeçalho
        
        for line in lines[1:]:
            line = line.strip()
            if not line:
                continue
                
            # Pular linhas com sintaxe SQL
            if '{' in line or '}' in line or 'PK' in line or 'FK' in line:
                continue
                
            # Manter relacionamentos válidos
            valid_relationships = ['||--||', '||--o{', '}o--||', '}o--o{', '||--o|', '|o--||']
            if any(rel in line for rel in valid_relationships):
                fixed_lines.append(f"    {line}")
        
        # Se não há relacionamentos, adicionar alguns básicos
        if len(fixed_lines) <= 1:
            fixed_lines.extend([
                "    Usuario ||--o{ Processo : cria",
                "    Processo ||--o{ Etapa : contem",
                "    Etapa ||--o{ Tarefa : possui",
                "    Usuario ||--o{ Tarefa : executa"
            ])
        
        return '\n'.join(fixed_lines)
    
    def _fix_sequence_issues(self, code: str) -> str:
        """Corrige problemas comuns em diagramas de sequência"""
        lines = code.split('\n')
        
        # Garantir que começa com sequenceDiagram
        if not lines[0].strip().lower().startswith('sequencediagram'):
            lines[0] = 'sequenceDiagram'
        
        return '\n'.join(lines)
    
    def _fix_flowchart_issues(self, code: str) -> str:
        """Corrige problemas comuns em flowcharts"""
        lines = code.split('\n')

        # Detectar e converter C4Context para flowchart
        if any('C4Context' in line or 'Person' in line or 'System' in line for line in lines):
            return self._convert_c4_to_flowchart()

        # Garantir que começa com flowchart
        if not lines[0].strip().lower().startswith('flowchart'):
            lines[0] = 'flowchart TD'

        return '\n'.join(lines)

    def _convert_c4_to_flowchart(self) -> str:
        """Converte diagrama C4 para flowchart válido"""
        return """flowchart TD
    Usuario[Usuario Final]

    subgraph Sistema["Sistema Web"]
        Frontend[Interface Web]
        Backend[API Backend]
        Database[Banco de Dados]
        Auth[Autenticacao]
    end

    Usuario --> Frontend
    Frontend --> Backend
    Backend --> Database
    Backend --> Auth"""


# Instância global do validador
mermaid_validator = MermaidValidator()


def validate_mermaid_diagram(diagram_code: str, expected_type: Optional[str] = None) -> Dict:
    """
    Função utilitária para validar diagramas Mermaid
    
    Args:
        diagram_code: Código do diagrama
        expected_type: Tipo esperado ('erDiagram', 'flowchart', 'sequenceDiagram')
        
    Returns:
        Resultado da validação
    """
    diagram_type = None
    if expected_type:
        type_mapping = {
            'erDiagram': DiagramType.ERD,
            'flowchart': DiagramType.FLOWCHART,
            'sequenceDiagram': DiagramType.SEQUENCE
        }
        diagram_type = type_mapping.get(expected_type)
    
    return mermaid_validator.validate_diagram(diagram_code, diagram_type)


def fix_mermaid_diagram(diagram_code: str, diagram_type: str) -> str:
    """
    Função utilitária para corrigir diagramas Mermaid
    
    Args:
        diagram_code: Código do diagrama
        diagram_type: Tipo do diagrama
        
    Returns:
        Código corrigido
    """
    type_mapping = {
        'erDiagram': DiagramType.ERD,
        'flowchart': DiagramType.FLOWCHART,
        'sequenceDiagram': DiagramType.SEQUENCE
    }
    
    mermaid_type = type_mapping.get(diagram_type, DiagramType.FLOWCHART)
    return mermaid_validator.fix_common_issues(diagram_code, mermaid_type)
