import { render, screen, fireEvent } from '@testing-library/angular';
import { ComponentFixture, fakeAsync, tick } from '@angular/core/testing';
import userEvent from '@testing-library/user-event';
import { ClientsHomeComponent } from './clients-home.component';
import { MessageService } from 'primeng/api';
import { FormsModule, ReactiveFormsModule, FormBuilder } from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { InputTextModule } from 'primeng/inputtext';

// Mock MessageService
class MockMessageService {
	add() {}
}

describe('ClientsHomeComponent (Novo Cliente Modal)', () => {
	let fixture: ComponentFixture<ClientsHomeComponent>;
	beforeEach(async () => {
		const { fixture: renderedFixture } = await render(ClientsHomeComponent, {
			imports: [
				FormsModule,
				ReactiveFormsModule,
				BrowserAnimationsModule,
				ButtonModule,
				DialogModule,
				InputTextModule,
			],
			providers: [
				{ provide: MessageService, useClass: MockMessageService },
				FormBuilder, // Adicionar FormBuilder aqui
			],
		});
		fixture = renderedFixture;
	});

	it('deve abrir o modal ao clicar em "Novo Cliente"', async () => {
		const btn = screen.getByRole('button', { name: /novo cliente/i });
		fireEvent.click(btn);
		expect(await screen.findByRole('dialog')).toBeTruthy();
		expect(screen.getByLabelText(/nome do cliente/i)).toBeTruthy();
	});

	it('deve exibir erro se tentar salvar sem preencher o nome', fakeAsync(async () => {
		const btn = screen.getByRole('button', { name: /novo cliente/i });
		fireEvent.click(btn);
		tick(); // Permitir que o modal abra e estabilize
		fixture.detectChanges();

		const salvarBtn = await screen.findByRole('button', { name: /salvar/i });
		fireEvent.click(salvarBtn);
		tick(); // Permitir que a lógica de saveNewClient (incluindo markAllAsTouched) execute
		fixture.detectChanges(); // Refletir mudanças de estado no DOM

		// Debugging logs
		const componentInstance = fixture.componentInstance;

		expect(await screen.findByText(/nome do cliente é obrigatório/i)).toBeTruthy();
	}));

	it('deve fechar o modal ao clicar em "Cancelar"', async () => {
		const btn = screen.getByRole('button', { name: /novo cliente/i });
		fireEvent.click(btn);
		const cancelarBtn = await screen.findByRole('button', { name: /cancelar/i });
		fireEvent.click(cancelarBtn);
		expect(screen.queryByRole('dialog')).toBeNull();
	});

	it('deve chamar método de salvar ao preencher nome e clicar em "Salvar"', fakeAsync(async () => {
		const user = userEvent.setup();
		const btn = screen.getByRole('button', { name: /novo cliente/i });
		await user.click(btn);
		tick();
		fixture.detectChanges();

		const nomeInput = await screen.findByLabelText(/nome do cliente/i);
		await user.type(nomeInput, 'Empresa Teste');
		tick(); // Permitir que o input propague e o estado do formulário atualize
		fixture.detectChanges(); // Refletir o novo valor e estado do formulário

		const salvarBtn = await screen.findByRole('button', { name: /salvar/i });
		await user.click(salvarBtn);
		tick(); // Permitir que a lógica de salvar e fechar o modal execute
		fixture.detectChanges();

		expect(screen.queryByRole('dialog')).toBeNull();
	}));
});
