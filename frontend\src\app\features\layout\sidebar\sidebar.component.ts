import { CommonModule } from '@angular/common';
import { Component, effect, input, OnInit, signal } from '@angular/core';
import { RouterLink, RouterLinkActive } from '@angular/router';

import { MenuItem } from 'primeng/api';
import { WebSocketService } from '../../../shared/services/ws.service';
import { ClientsService } from '../../clients/clients.service';

@Component({
	selector: 'app-sidebar',
	standalone: true,
	imports: [CommonModule, RouterLink, RouterLinkActive],
	templateUrl: './sidebar.component.html',
	host: {
		display: 'block',
		height: '100%',
	},
})
export class SidebarComponent implements OnInit {
	public readonly visible = input<boolean>(true);
	public hasClients = signal<boolean>(false);

	public menuItems: Array<MenuItem> = [
		{
			label: 'Dashboard',
			icon: 'pi pi-home',
			routerLink: '/dashboard',
		},
		{
			label: 'Clientes',
			icon: 'pi pi-users',
			routerLink: '/clients',
		},
		{
			label: 'Sugestão de projetos',
			icon: 'pi pi-folder',
			routerLink: '/projects',
			disabled: true,
		},
		{
			label: 'Configurações',
			icon: 'pi pi-cog',
			routerLink: '/settings',
		},
		{
			label: 'Sobre',
			icon: 'pi pi-info-circle',
			routerLink: '/about',
		},
	];

	constructor(
		private clientsService: ClientsService,
		private ws: WebSocketService,
	) {
		effect(() => {
			const temClientes = this.hasClients();
			const projetosIndex = this.menuItems.findIndex(item => item.label === 'Sugestão de projetos');
			if (projetosIndex !== -1) {
				this.menuItems[projetosIndex] = {
					...this.menuItems[projetosIndex],
					disabled: !temClientes,
				};
			}
		});

		effect(() => {
			const msgs = this.ws.messages();
			msgs.forEach((msg: any) => {
				if (msg && (msg.type === 'client_update' || msg.type === 'client_created')) {
					this.verificarClientes();
				}
			});
		});
	}

	ngOnInit(): void {
		this.verificarClientes();
	}

	private verificarClientes(): void {
		this.clientsService.getClients().subscribe({
			next: clients => {
				this.hasClients.set(clients.length > 0);
			},
			error: () => {
				console.error('Erro ao verificar clientes');
				this.hasClients.set(false);
			},
		});
	}
}
