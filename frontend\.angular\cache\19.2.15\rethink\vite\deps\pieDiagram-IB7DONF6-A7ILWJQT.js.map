{"version": 3, "sources": ["../../../../../../node_modules/mermaid/dist/chunks/mermaid.core/pieDiagram-IB7DONF6.mjs"], "sourcesContent": ["import { populateCommonDb } from \"./chunk-4BMEZGHF.mjs\";\nimport { cleanAndMerge, parseFontSize } from \"./chunk-O4NI6UNU.mjs\";\nimport { selectSvgElement } from \"./chunk-7B677QYD.mjs\";\nimport { __name, clear, configureSvgSize, defaultConfig_default, getAccDescription, getAccTitle, getConfig2 as getConfig, getDiagramTitle, log, setAccDescription, setAccTitle, setDiagramTitle } from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/pie/pieParser.ts\nimport { parse } from \"@mermaid-js/parser\";\n\n// src/diagrams/pie/pieDb.ts\nvar DEFAULT_PIE_CONFIG = defaultConfig_default.pie;\nvar DEFAULT_PIE_DB = {\n  sections: /* @__PURE__ */new Map(),\n  showData: false,\n  config: DEFAULT_PIE_CONFIG\n};\nvar sections = DEFAULT_PIE_DB.sections;\nvar showData = DEFAULT_PIE_DB.showData;\nvar config = structuredClone(DEFAULT_PIE_CONFIG);\nvar getConfig2 = /* @__PURE__ */__name(() => structuredClone(config), \"getConfig\");\nvar clear2 = /* @__PURE__ */__name(() => {\n  sections = /* @__PURE__ */new Map();\n  showData = DEFAULT_PIE_DB.showData;\n  clear();\n}, \"clear\");\nvar addSection = /* @__PURE__ */__name(({\n  label,\n  value\n}) => {\n  if (!sections.has(label)) {\n    sections.set(label, value);\n    log.debug(`added new section: ${label}, with value: ${value}`);\n  }\n}, \"addSection\");\nvar getSections = /* @__PURE__ */__name(() => sections, \"getSections\");\nvar setShowData = /* @__PURE__ */__name(toggle => {\n  showData = toggle;\n}, \"setShowData\");\nvar getShowData = /* @__PURE__ */__name(() => showData, \"getShowData\");\nvar db = {\n  getConfig: getConfig2,\n  clear: clear2,\n  setDiagramTitle,\n  getDiagramTitle,\n  setAccTitle,\n  getAccTitle,\n  setAccDescription,\n  getAccDescription,\n  addSection,\n  getSections,\n  setShowData,\n  getShowData\n};\n\n// src/diagrams/pie/pieParser.ts\nvar populateDb = /* @__PURE__ */__name((ast, db2) => {\n  populateCommonDb(ast, db2);\n  db2.setShowData(ast.showData);\n  ast.sections.map(db2.addSection);\n}, \"populateDb\");\nvar parser = {\n  parse: /* @__PURE__ */__name(async input => {\n    const ast = await parse(\"pie\", input);\n    log.debug(ast);\n    populateDb(ast, db);\n  }, \"parse\")\n};\n\n// src/diagrams/pie/pieStyles.ts\nvar getStyles = /* @__PURE__ */__name(options => `\n  .pieCircle{\n    stroke: ${options.pieStrokeColor};\n    stroke-width : ${options.pieStrokeWidth};\n    opacity : ${options.pieOpacity};\n  }\n  .pieOuterCircle{\n    stroke: ${options.pieOuterStrokeColor};\n    stroke-width: ${options.pieOuterStrokeWidth};\n    fill: none;\n  }\n  .pieTitleText {\n    text-anchor: middle;\n    font-size: ${options.pieTitleTextSize};\n    fill: ${options.pieTitleTextColor};\n    font-family: ${options.fontFamily};\n  }\n  .slice {\n    font-family: ${options.fontFamily};\n    fill: ${options.pieSectionTextColor};\n    font-size:${options.pieSectionTextSize};\n    // fill: white;\n  }\n  .legend text {\n    fill: ${options.pieLegendTextColor};\n    font-family: ${options.fontFamily};\n    font-size: ${options.pieLegendTextSize};\n  }\n`, \"getStyles\");\nvar pieStyles_default = getStyles;\n\n// src/diagrams/pie/pieRenderer.ts\nimport { arc, pie as d3pie, scaleOrdinal } from \"d3\";\nvar createPieArcs = /* @__PURE__ */__name(sections2 => {\n  const pieData = [...sections2.entries()].map(element => {\n    return {\n      label: element[0],\n      value: element[1]\n    };\n  }).sort((a, b) => {\n    return b.value - a.value;\n  });\n  const pie = d3pie().value(d3Section => d3Section.value);\n  return pie(pieData);\n}, \"createPieArcs\");\nvar draw = /* @__PURE__ */__name((text, id, _version, diagObj) => {\n  log.debug(\"rendering pie chart\\n\" + text);\n  const db2 = diagObj.db;\n  const globalConfig = getConfig();\n  const pieConfig = cleanAndMerge(db2.getConfig(), globalConfig.pie);\n  const MARGIN = 40;\n  const LEGEND_RECT_SIZE = 18;\n  const LEGEND_SPACING = 4;\n  const height = 450;\n  const pieWidth = height;\n  const svg = selectSvgElement(id);\n  const group = svg.append(\"g\");\n  group.attr(\"transform\", \"translate(\" + pieWidth / 2 + \",\" + height / 2 + \")\");\n  const {\n    themeVariables\n  } = globalConfig;\n  let [outerStrokeWidth] = parseFontSize(themeVariables.pieOuterStrokeWidth);\n  outerStrokeWidth ??= 2;\n  const textPosition = pieConfig.textPosition;\n  const radius = Math.min(pieWidth, height) / 2 - MARGIN;\n  const arcGenerator = arc().innerRadius(0).outerRadius(radius);\n  const labelArcGenerator = arc().innerRadius(radius * textPosition).outerRadius(radius * textPosition);\n  group.append(\"circle\").attr(\"cx\", 0).attr(\"cy\", 0).attr(\"r\", radius + outerStrokeWidth / 2).attr(\"class\", \"pieOuterCircle\");\n  const sections2 = db2.getSections();\n  const arcs = createPieArcs(sections2);\n  const myGeneratedColors = [themeVariables.pie1, themeVariables.pie2, themeVariables.pie3, themeVariables.pie4, themeVariables.pie5, themeVariables.pie6, themeVariables.pie7, themeVariables.pie8, themeVariables.pie9, themeVariables.pie10, themeVariables.pie11, themeVariables.pie12];\n  const color = scaleOrdinal(myGeneratedColors);\n  group.selectAll(\"mySlices\").data(arcs).enter().append(\"path\").attr(\"d\", arcGenerator).attr(\"fill\", datum => {\n    return color(datum.data.label);\n  }).attr(\"class\", \"pieCircle\");\n  let sum = 0;\n  sections2.forEach(section => {\n    sum += section;\n  });\n  group.selectAll(\"mySlices\").data(arcs).enter().append(\"text\").text(datum => {\n    return (datum.data.value / sum * 100).toFixed(0) + \"%\";\n  }).attr(\"transform\", datum => {\n    return \"translate(\" + labelArcGenerator.centroid(datum) + \")\";\n  }).style(\"text-anchor\", \"middle\").attr(\"class\", \"slice\");\n  group.append(\"text\").text(db2.getDiagramTitle()).attr(\"x\", 0).attr(\"y\", -(height - 50) / 2).attr(\"class\", \"pieTitleText\");\n  const legend = group.selectAll(\".legend\").data(color.domain()).enter().append(\"g\").attr(\"class\", \"legend\").attr(\"transform\", (_datum, index) => {\n    const height2 = LEGEND_RECT_SIZE + LEGEND_SPACING;\n    const offset = height2 * color.domain().length / 2;\n    const horizontal = 12 * LEGEND_RECT_SIZE;\n    const vertical = index * height2 - offset;\n    return \"translate(\" + horizontal + \",\" + vertical + \")\";\n  });\n  legend.append(\"rect\").attr(\"width\", LEGEND_RECT_SIZE).attr(\"height\", LEGEND_RECT_SIZE).style(\"fill\", color).style(\"stroke\", color);\n  legend.data(arcs).append(\"text\").attr(\"x\", LEGEND_RECT_SIZE + LEGEND_SPACING).attr(\"y\", LEGEND_RECT_SIZE - LEGEND_SPACING).text(datum => {\n    const {\n      label,\n      value\n    } = datum.data;\n    if (db2.getShowData()) {\n      return `${label} [${value}]`;\n    }\n    return label;\n  });\n  const longestTextWidth = Math.max(...legend.selectAll(\"text\").nodes().map(node => node?.getBoundingClientRect().width ?? 0));\n  const totalWidth = pieWidth + MARGIN + LEGEND_RECT_SIZE + LEGEND_SPACING + longestTextWidth;\n  svg.attr(\"viewBox\", `0 0 ${totalWidth} ${height}`);\n  configureSvgSize(svg, height, totalWidth, pieConfig.useMaxWidth);\n}, \"draw\");\nvar renderer = {\n  draw\n};\n\n// src/diagrams/pie/pieDiagram.ts\nvar diagram = {\n  parser,\n  db,\n  renderer,\n  styles: pieStyles_default\n};\nexport { diagram };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAI,qBAAqB,sBAAsB;AAC/C,IAAI,iBAAiB;AAAA,EACnB,UAAyB,oBAAI,IAAI;AAAA,EACjC,UAAU;AAAA,EACV,QAAQ;AACV;AACA,IAAI,WAAW,eAAe;AAC9B,IAAI,WAAW,eAAe;AAC9B,IAAI,SAAS,gBAAgB,kBAAkB;AAC/C,IAAIA,cAA4B,OAAO,MAAM,gBAAgB,MAAM,GAAG,WAAW;AACjF,IAAI,SAAwB,OAAO,MAAM;AACvC,aAA0B,oBAAI,IAAI;AAClC,aAAW,eAAe;AAC1B,QAAM;AACR,GAAG,OAAO;AACV,IAAI,aAA4B,OAAO,CAAC;AAAA,EACtC;AAAA,EACA;AACF,MAAM;AACJ,MAAI,CAAC,SAAS,IAAI,KAAK,GAAG;AACxB,aAAS,IAAI,OAAO,KAAK;AACzB,QAAI,MAAM,sBAAsB,KAAK,iBAAiB,KAAK,EAAE;AAAA,EAC/D;AACF,GAAG,YAAY;AACf,IAAI,cAA6B,OAAO,MAAM,UAAU,aAAa;AACrE,IAAI,cAA6B,OAAO,YAAU;AAChD,aAAW;AACb,GAAG,aAAa;AAChB,IAAI,cAA6B,OAAO,MAAM,UAAU,aAAa;AACrE,IAAI,KAAK;AAAA,EACP,WAAWA;AAAA,EACX,OAAO;AAAA,EACP;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAGA,IAAI,aAA4B,OAAO,CAAC,KAAK,QAAQ;AACnD,mBAAiB,KAAK,GAAG;AACzB,MAAI,YAAY,IAAI,QAAQ;AAC5B,MAAI,SAAS,IAAI,IAAI,UAAU;AACjC,GAAG,YAAY;AACf,IAAI,SAAS;AAAA,EACX,OAAsB,OAAO,CAAM,UAAS;AAC1C,UAAM,MAAM,MAAM,MAAM,OAAO,KAAK;AACpC,QAAI,MAAM,GAAG;AACb,eAAW,KAAK,EAAE;AAAA,EACpB,IAAG,OAAO;AACZ;AAGA,IAAI,YAA2B,OAAO,aAAW;AAAA;AAAA,cAEnC,QAAQ,cAAc;AAAA,qBACf,QAAQ,cAAc;AAAA,gBAC3B,QAAQ,UAAU;AAAA;AAAA;AAAA,cAGpB,QAAQ,mBAAmB;AAAA,oBACrB,QAAQ,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,iBAK9B,QAAQ,gBAAgB;AAAA,YAC7B,QAAQ,iBAAiB;AAAA,mBAClB,QAAQ,UAAU;AAAA;AAAA;AAAA,mBAGlB,QAAQ,UAAU;AAAA,YACzB,QAAQ,mBAAmB;AAAA,gBACvB,QAAQ,kBAAkB;AAAA;AAAA;AAAA;AAAA,YAI9B,QAAQ,kBAAkB;AAAA,mBACnB,QAAQ,UAAU;AAAA,iBACpB,QAAQ,iBAAiB;AAAA;AAAA,GAEvC,WAAW;AACd,IAAI,oBAAoB;AAIxB,IAAI,gBAA+B,OAAO,eAAa;AACrD,QAAM,UAAU,CAAC,GAAG,UAAU,QAAQ,CAAC,EAAE,IAAI,aAAW;AACtD,WAAO;AAAA,MACL,OAAO,QAAQ,CAAC;AAAA,MAChB,OAAO,QAAQ,CAAC;AAAA,IAClB;AAAA,EACF,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM;AAChB,WAAO,EAAE,QAAQ,EAAE;AAAA,EACrB,CAAC;AACD,QAAM,MAAM,YAAM,EAAE,MAAM,eAAa,UAAU,KAAK;AACtD,SAAO,IAAI,OAAO;AACpB,GAAG,eAAe;AAClB,IAAI,OAAsB,OAAO,CAAC,MAAM,IAAI,UAAU,YAAY;AAChE,MAAI,MAAM,0BAA0B,IAAI;AACxC,QAAM,MAAM,QAAQ;AACpB,QAAM,eAAe,WAAU;AAC/B,QAAM,YAAY,cAAc,IAAI,UAAU,GAAG,aAAa,GAAG;AACjE,QAAM,SAAS;AACf,QAAM,mBAAmB;AACzB,QAAM,iBAAiB;AACvB,QAAM,SAAS;AACf,QAAM,WAAW;AACjB,QAAM,MAAM,iBAAiB,EAAE;AAC/B,QAAM,QAAQ,IAAI,OAAO,GAAG;AAC5B,QAAM,KAAK,aAAa,eAAe,WAAW,IAAI,MAAM,SAAS,IAAI,GAAG;AAC5E,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,MAAI,CAAC,gBAAgB,IAAI,cAAc,eAAe,mBAAmB;AACzE,uBAAqB;AACrB,QAAM,eAAe,UAAU;AAC/B,QAAM,SAAS,KAAK,IAAI,UAAU,MAAM,IAAI,IAAI;AAChD,QAAM,eAAe,YAAI,EAAE,YAAY,CAAC,EAAE,YAAY,MAAM;AAC5D,QAAM,oBAAoB,YAAI,EAAE,YAAY,SAAS,YAAY,EAAE,YAAY,SAAS,YAAY;AACpG,QAAM,OAAO,QAAQ,EAAE,KAAK,MAAM,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,KAAK,KAAK,SAAS,mBAAmB,CAAC,EAAE,KAAK,SAAS,gBAAgB;AAC1H,QAAM,YAAY,IAAI,YAAY;AAClC,QAAM,OAAO,cAAc,SAAS;AACpC,QAAM,oBAAoB,CAAC,eAAe,MAAM,eAAe,MAAM,eAAe,MAAM,eAAe,MAAM,eAAe,MAAM,eAAe,MAAM,eAAe,MAAM,eAAe,MAAM,eAAe,MAAM,eAAe,OAAO,eAAe,OAAO,eAAe,KAAK;AACxR,QAAM,QAAQ,QAAa,iBAAiB;AAC5C,QAAM,UAAU,UAAU,EAAE,KAAK,IAAI,EAAE,MAAM,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,YAAY,EAAE,KAAK,QAAQ,WAAS;AAC1G,WAAO,MAAM,MAAM,KAAK,KAAK;AAAA,EAC/B,CAAC,EAAE,KAAK,SAAS,WAAW;AAC5B,MAAI,MAAM;AACV,YAAU,QAAQ,aAAW;AAC3B,WAAO;AAAA,EACT,CAAC;AACD,QAAM,UAAU,UAAU,EAAE,KAAK,IAAI,EAAE,MAAM,EAAE,OAAO,MAAM,EAAE,KAAK,WAAS;AAC1E,YAAQ,MAAM,KAAK,QAAQ,MAAM,KAAK,QAAQ,CAAC,IAAI;AAAA,EACrD,CAAC,EAAE,KAAK,aAAa,WAAS;AAC5B,WAAO,eAAe,kBAAkB,SAAS,KAAK,IAAI;AAAA,EAC5D,CAAC,EAAE,MAAM,eAAe,QAAQ,EAAE,KAAK,SAAS,OAAO;AACvD,QAAM,OAAO,MAAM,EAAE,KAAK,IAAI,gBAAgB,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,KAAK,SAAS,cAAc;AACxH,QAAM,SAAS,MAAM,UAAU,SAAS,EAAE,KAAK,MAAM,OAAO,CAAC,EAAE,MAAM,EAAE,OAAO,GAAG,EAAE,KAAK,SAAS,QAAQ,EAAE,KAAK,aAAa,CAAC,QAAQ,UAAU;AAC9I,UAAM,UAAU,mBAAmB;AACnC,UAAM,SAAS,UAAU,MAAM,OAAO,EAAE,SAAS;AACjD,UAAM,aAAa,KAAK;AACxB,UAAM,WAAW,QAAQ,UAAU;AACnC,WAAO,eAAe,aAAa,MAAM,WAAW;AAAA,EACtD,CAAC;AACD,SAAO,OAAO,MAAM,EAAE,KAAK,SAAS,gBAAgB,EAAE,KAAK,UAAU,gBAAgB,EAAE,MAAM,QAAQ,KAAK,EAAE,MAAM,UAAU,KAAK;AACjI,SAAO,KAAK,IAAI,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,mBAAmB,cAAc,EAAE,KAAK,KAAK,mBAAmB,cAAc,EAAE,KAAK,WAAS;AACvI,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,MAAM;AACV,QAAI,IAAI,YAAY,GAAG;AACrB,aAAO,GAAG,KAAK,KAAK,KAAK;AAAA,IAC3B;AACA,WAAO;AAAA,EACT,CAAC;AACD,QAAM,mBAAmB,KAAK,IAAI,GAAG,OAAO,UAAU,MAAM,EAAE,MAAM,EAAE,IAAI,UAAQ,MAAM,sBAAsB,EAAE,SAAS,CAAC,CAAC;AAC3H,QAAM,aAAa,WAAW,SAAS,mBAAmB,iBAAiB;AAC3E,MAAI,KAAK,WAAW,OAAO,UAAU,IAAI,MAAM,EAAE;AACjD,mBAAiB,KAAK,QAAQ,YAAY,UAAU,WAAW;AACjE,GAAG,MAAM;AACT,IAAI,WAAW;AAAA,EACb;AACF;AAGA,IAAI,UAAU;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAQ;AACV;", "names": ["getConfig2"]}