#!/usr/bin/env python3
"""
Agendador para Enriquecimento Contínuo

Implementa funcionalidades de agendamento para executar automaticamente
o enriquecimento contínuo de dados em intervalos regulares.
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from enum import Enum
import threading

from .continuous_enrichment import ContinuousEnrichmentSystem, EnrichmentType

# Configurar logging
logger = logging.getLogger(__name__)


class ScheduleFrequency(Enum):
    """Frequências de agendamento disponíveis"""
    HOURLY = "hourly"
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    CUSTOM = "custom"


@dataclass
class ScheduleConfig:
    """Configuração de agendamento"""
    frequency: ScheduleFrequency
    interval_hours: Optional[int] = None  # Para CUSTOM
    specific_hour: Optional[int] = None   # Para execução em hora específica
    enrichment_types: Optional[List[EnrichmentType]] = None
    max_companies_per_run: Optional[int] = None
    enabled: bool = True


@dataclass
class ScheduleHistory:
    """Histórico de execução agendada"""
    execution_id: str
    started_at: datetime
    completed_at: Optional[datetime]
    success: bool
    results_summary: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None


class EnrichmentScheduler:
    """
    Agendador para Enriquecimento Contínuo

    Gerencia a execução automática e periódica do sistema de 
    enriquecimento contínuo de dados.
    """

    def __init__(self, enrichment_system: ContinuousEnrichmentSystem):
        """
        Inicializa o agendador

        Args:
            enrichment_system: Sistema de enriquecimento a ser agendado
        """
        self.enrichment_system = enrichment_system
        self.schedule_config: Optional[ScheduleConfig] = None
        self.is_running = False
        self.scheduler_thread: Optional[threading.Thread] = None
        self.execution_history: List[ScheduleHistory] = []
        self.max_history_entries = 100

        # Callbacks para eventos
        self.on_execution_start: Optional[Callable] = None
        self.on_execution_complete: Optional[Callable] = None
        self.on_execution_error: Optional[Callable] = None

    def configure_schedule(self, config: ScheduleConfig) -> None:
        """
        Configura o agendamento

        Args:
            config: Configuração do agendamento
        """
        self.schedule_config = config
        logger.info(f"📅 Agendamento configurado: {config.frequency.value}")

        if config.frequency == ScheduleFrequency.CUSTOM and config.interval_hours:
            logger.info(
                f"⏰ Intervalo personalizado: {config.interval_hours} horas")

    def start_scheduler(self) -> bool:
        """
        Inicia o agendador

        Returns:
            True se iniciado com sucesso, False caso contrário
        """
        if not self.schedule_config:
            logger.error("❌ Configuração de agendamento não definida")
            return False

        if not self.schedule_config.enabled:
            logger.info("⏸️ Agendamento está desabilitado")
            return False

        if self.is_running:
            logger.warning("⚠️ Agendador já está em execução")
            return False

        self.is_running = True
        self.scheduler_thread = threading.Thread(
            target=self._run_scheduler_loop,
            daemon=True
        )
        self.scheduler_thread.start()

        logger.info("🚀 Agendador iniciado com sucesso")
        return True

    def stop_scheduler(self) -> None:
        """Para o agendador"""
        if not self.is_running:
            logger.info("ℹ️ Agendador já está parado")
            return

        self.is_running = False

        if self.scheduler_thread and self.scheduler_thread.is_alive():
            logger.info("⏹️ Parando agendador...")
            # O thread irá parar na próxima verificação

        logger.info("✅ Agendador parado")

    def _run_scheduler_loop(self) -> None:
        """
        Loop principal do agendador (executa em thread separada)
        """
        logger.info("🔄 Loop do agendador iniciado")

        while self.is_running:
            try:
                # Calcular próxima execução
                next_execution = self._calculate_next_execution()

                if next_execution is None:
                    logger.error(
                        "❌ Não foi possível calcular próxima execução")
                    break

                # Aguardar até a próxima execução
                now = datetime.utcnow()
                if next_execution > now:
                    wait_seconds = (next_execution - now).total_seconds()
                    logger.info(
                        f"⏰ Próxima execução em {wait_seconds/3600:.1f} horas")

                    # Aguardar com verificações periódicas para permitir parada
                    # Verificar a cada minuto
                    wait_interval = min(60, wait_seconds)
                    elapsed = 0

                    while elapsed < wait_seconds and self.is_running:
                        time.sleep(wait_interval)
                        elapsed += wait_interval

                # Executar enriquecimento se ainda estiver rodando
                if self.is_running:
                    asyncio.run(self._execute_scheduled_enrichment())

            except Exception as e:
                logger.error(f"❌ Erro no loop do agendador: {e}")
                # Continuar executando após erro
                time.sleep(300)  # Aguardar 5 minutos antes de tentar novamente

        logger.info("🏁 Loop do agendador finalizado")

    def _calculate_next_execution(self) -> Optional[datetime]:
        """
        Calcula quando deve ser a próxima execução

        Returns:
            Timestamp da próxima execução ou None se não puder calcular
        """
        if not self.schedule_config:
            return None

        now = datetime.utcnow()
        config = self.schedule_config

        if config.frequency == ScheduleFrequency.HOURLY:
            return now + timedelta(hours=1)

        elif config.frequency == ScheduleFrequency.DAILY:
            # Executar na próxima ocorrência da hora específica
            target_hour = config.specific_hour or 2  # 2 AM por padrão
            next_execution = now.replace(
                hour=target_hour, minute=0, second=0, microsecond=0)

            if next_execution <= now:
                next_execution += timedelta(days=1)

            return next_execution

        elif config.frequency == ScheduleFrequency.WEEKLY:
            # Executar toda segunda-feira na hora específica
            target_hour = config.specific_hour or 2
            days_ahead = 0 - now.weekday()  # Segunda = 0

            if days_ahead <= 0:  # Já passou desta semana
                days_ahead += 7

            next_execution = now + timedelta(days=days_ahead)
            next_execution = next_execution.replace(
                hour=target_hour, minute=0, second=0, microsecond=0
            )

            return next_execution

        elif config.frequency == ScheduleFrequency.MONTHLY:
            # Executar no primeiro dia do próximo mês
            target_hour = config.specific_hour or 2

            if now.month == 12:
                next_month = now.replace(year=now.year + 1, month=1, day=1)
            else:
                next_month = now.replace(month=now.month + 1, day=1)

            next_execution = next_month.replace(
                hour=target_hour, minute=0, second=0, microsecond=0
            )

            return next_execution

        elif config.frequency == ScheduleFrequency.CUSTOM and config.interval_hours:
            return now + timedelta(hours=config.interval_hours)

        else:
            logger.error(f"❌ Frequência não suportada: {config.frequency}")
            return None

    async def _execute_scheduled_enrichment(self) -> None:
        """
        Executa enriquecimento agendado
        """
        execution_id = f"scheduled_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
        start_time = datetime.utcnow()

        logger.info(f"🚀 Iniciando execução agendada: {execution_id}")

        # Criar entrada no histórico
        history_entry = ScheduleHistory(
            execution_id=execution_id,
            started_at=start_time,
            completed_at=None,
            success=False
        )

        try:
            # Callback de início
            if self.on_execution_start:
                self.on_execution_start(execution_id, start_time)

            # Executar enriquecimento
            results = await self.enrichment_system.start_continuous_enrichment(
                enrichment_types=self.schedule_config.enrichment_types if self.schedule_config else None,
                max_companies=self.schedule_config.max_companies_per_run if self.schedule_config else None
            )

            # Atualizar histórico com sucesso
            history_entry.completed_at = datetime.utcnow()
            history_entry.success = True
            history_entry.results_summary = results

            logger.info(f"✅ Execução agendada concluída: {execution_id}")

            # Callback de conclusão
            if self.on_execution_complete:
                self.on_execution_complete(execution_id, results)

        except Exception as e:
            # Atualizar histórico com erro
            history_entry.completed_at = datetime.utcnow()
            history_entry.success = False
            history_entry.error_message = str(e)

            logger.error(f"❌ Erro na execução agendada {execution_id}: {e}")

            # Callback de erro
            if self.on_execution_error:
                self.on_execution_error(execution_id, e)

        finally:
            # Adicionar ao histórico
            self.execution_history.append(history_entry)

            # Limitar tamanho do histórico
            if len(self.execution_history) > self.max_history_entries:
                self.execution_history = self.execution_history[-self.max_history_entries:]

    def get_schedule_status(self) -> Dict[str, Any]:
        """
        Retorna status atual do agendamento

        Returns:
            Status detalhado do agendador
        """
        status = {
            'is_running': self.is_running,
            'configuration': None,
            'next_execution': None,
            'last_execution': None,
            'execution_count': len(self.execution_history)
        }

        if self.schedule_config:
            status['configuration'] = {
                'frequency': self.schedule_config.frequency.value,
                'interval_hours': self.schedule_config.interval_hours,
                'specific_hour': self.schedule_config.specific_hour,
                'enrichment_types': [t.value for t in (self.schedule_config.enrichment_types or [])],
                'max_companies_per_run': self.schedule_config.max_companies_per_run,
                'enabled': self.schedule_config.enabled
            }

            if self.is_running:
                status['next_execution'] = self._calculate_next_execution()

        if self.execution_history:
            last_execution = self.execution_history[-1]
            status['last_execution'] = {
                'execution_id': last_execution.execution_id,
                'started_at': last_execution.started_at.isoformat(),
                'completed_at': last_execution.completed_at.isoformat() if last_execution.completed_at else None,
                'success': last_execution.success,
                'error_message': last_execution.error_message
            }

        return status

    def get_execution_history(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Retorna histórico de execuções

        Args:
            limit: Número máximo de entradas a retornar

        Returns:
            Lista de execuções históricas
        """
        history = self.execution_history[-limit:
                                         ] if limit else self.execution_history

        return [
            {
                'execution_id': entry.execution_id,
                'started_at': entry.started_at.isoformat(),
                'completed_at': entry.completed_at.isoformat() if entry.completed_at else None,
                'success': entry.success,
                'error_message': entry.error_message,
                'results_summary': entry.results_summary
            }
            for entry in history
        ]

    def trigger_immediate_execution(self) -> str:
        """
        Dispara uma execução imediata (fora do agendamento)

        Returns:
            ID da execução disparada
        """
        execution_id = f"manual_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"

        # Executar em thread separada para não bloquear
        def run_immediate():
            asyncio.run(self._execute_scheduled_enrichment())

        thread = threading.Thread(target=run_immediate, daemon=True)
        thread.start()

        logger.info(f"🎯 Execução imediata disparada: {execution_id}")
        return execution_id
