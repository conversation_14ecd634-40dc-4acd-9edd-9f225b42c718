<!-- <PERSON><PERSON> de Novo Cliente -->
<p-dialog
	header="Novo Cliente"
	[(visible)]="showNewClientDialog"
	[modal]="true"
	[closable]="false"
	[style]="{ 'width': '400px', 'max-width': '95vw' }"
	role="dialog"
>
	<form [formGroup]="newClientForm" (ngSubmit)="saveNewClient()" autocomplete="off">
		<div class="mb-3">
			<label for="clientName" class="block mb-1 font-medium">Cliente *</label>
			<input
				id="clientName"
				pInputText
				type="text"
				formControlName="name"
				[attr.aria-label]="'Cliente'"
				class="w-full"
				[ngClass]="{
					'p-invalid':
						newClientForm.controls['name'].invalid &&
						(newClientForm.controls['name'].dirty || newClientForm.controls['name'].touched),
				}"
			/>
			@if (
				newClientForm.controls['name'].invalid &&
				(newClientForm.controls['name'].dirty || newClientForm.controls['name'].touched)
			) {
				@if (newClientForm.controls['name'].errors?.['required']) {
					<div class="text-red-600 text-xs mt-1">Cliente é obrigatório.</div>
				}
			}
		</div>
		<div class="mb-3">
			<label for="city" class="block mb-1 font-medium">Cidade *</label>
			<input
				id="city"
				pInputText
				type="text"
				formControlName="city"
				class="w-full"
				[ngClass]="{
					'p-invalid':
						newClientForm.controls['city'].invalid &&
						(newClientForm.controls['city'].dirty || newClientForm.controls['city'].touched),
				}"
				[attr.aria-label]="'Cidade'"
			/>
			@if (
				newClientForm.controls['city'].invalid &&
				(newClientForm.controls['city'].dirty || newClientForm.controls['city'].touched)
			) {
				@if (newClientForm.controls['city'].errors?.['required']) {
					<div class="text-red-600 text-xs mt-1">Cidade é obrigatória.</div>
				}
			}
		</div>
		<div class="mb-3">
			<label for="state" class="block mb-1 font-medium">Estado *</label>
			<input
				id="state"
				pInputText
				type="text"
				formControlName="state"
				class="w-full"
				[ngClass]="{
					'p-invalid':
						newClientForm.controls['state'].invalid &&
						(newClientForm.controls['state'].dirty || newClientForm.controls['state'].touched),
				}"
				[attr.aria-label]="'Estado'"
			/>
			@if (
				newClientForm.controls['state'].invalid &&
				(newClientForm.controls['state'].dirty || newClientForm.controls['state'].touched)
			) {
				@if (newClientForm.controls['state'].errors?.['required']) {
					<div class="text-red-600 text-xs mt-1">Estado é obrigatório.</div>
				}
			}
		</div>
		<div class="mb-3">
			<label for="site" class="block mb-1">Site *</label>
			<input id="site" pInputText type="text" formControlName="site" class="w-full" [attr.aria-label]="'Site'" />
		</div>
		<div class="mb-3">
			<label for="cpfCnpj" class="block mb-1">CPF / CNPJ</label>
			<input
				id="cpfCnpj"
				pInputText
				type="text"
				formControlName="cpfCnpj"
				class="w-full"
				[attr.aria-label]="'CPF / CNPJ'"
			/>
		</div>
		<div class="mb-3">
			<label for="phone" class="block mb-1">Telefone</label>
			<input
				id="phone"
				pInputText
				type="text"
				formControlName="phone"
				class="w-full"
				[attr.aria-label]="'Telefone'"
			/>
		</div>
		<div class="mb-3">
			<label for="responsible" class="block mb-1">Responsável</label>
			<input
				id="responsible"
				pInputText
				type="text"
				formControlName="responsible"
				class="w-full"
				[attr.aria-label]="'Responsável'"
			/>
		</div>
		<div class="mb-3">
			<label for="responsibleRole" class="block mb-1">Cargo responsável</label>
			<input
				id="responsibleRole"
				pInputText
				type="text"
				formControlName="responsibleRole"
				class="w-full"
				[attr.aria-label]="'Cargo responsável'"
			/>
		</div>

		<div class="flex justify-end gap-2 mt-4">
			<button pButton type="button" label="Cancelar" class="p-button-text" (click)="cancelNewClient()"></button>
			<button pButton type="submit" label="Salvar" class="p-button-primary" [disabled]="newClientForm.invalid">
				Salvar
			</button>
		</div>
	</form>
</p-dialog>

<!-- Cabeçalho da Página de Clientes -->
<div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8">
	<div>
		<h1 class="text-3xl font-bold text-gray-900">Clientes</h1>
		<p class="text-gray-500">Gerencie e acompanhe todos os seus clientes e contatos</p>
	</div>
	<div class="flex flex-wrap gap-3 w-full md:w-auto">
		<button
			pButton
			label="Novo Cliente"
			icon="pi pi-plus"
			class="p-button-primary w-full md:w-auto"
			(click)="onNewClient()"
		></button>
		<button
			pButton
			label="Importar"
			icon="pi pi-file-import"
			class="p-button-outlined w-full md:w-auto"
			(click)="onImport()"
		></button>
	</div>
</div>

<!-- Filtros e Controles -->
<div class="bg-white p-5 rounded-xl shadow-sm mb-8">
	<div class="flex flex-col md:flex-row gap-4 items-center justify-between">
		<!-- Barra de pesquisa -->
		<div class="w-full md:w-1/3">
			<input
				type="text"
				pInputText
				class="w-full"
				placeholder="Buscar clientes..."
				[(ngModel)]="searchQuery"
				(input)="filterClients()"
			/>
		</div>

		<!-- Filtros -->
		<div class="flex flex-col sm:flex-row gap-3 w-full md:w-auto items-center">
			<p-select
				[options]="statusOptions"
				[(ngModel)]="selectedStatus"
				placeholder="Status"
				optionLabel="label"
				class="w-full md:w-44"
				[appendTo]="'body'"
				(onChange)="filterClients()"
			></p-select>

			<p-select
				[options]="sectorOptions"
				[(ngModel)]="selectedSector"
				placeholder="Setor"
				optionLabel="label"
				class="w-full md:w-44"
				[appendTo]="'body'"
				(onChange)="filterClients()"
			></p-select>

			<p-select
				[options]="sortOptions"
				[(ngModel)]="sortKey"
				placeholder="Ordenar por"
				optionLabel="label"
				class="w-full md:w-48"
				[appendTo]="'body'"
				(onChange)="onSortChange($event)"
			></p-select>

			<!-- Toggle de visualização -->
			<div class="flex border border-gray-200 rounded-lg overflow-hidden bg-gray-50 w-full md:w-48">
				<button
					pButton
					icon="pi pi-th-large"
					[ngClass]="{
						'bg-primary-500 text-white': layout === 'grid',
						'text-gray-600 hover:bg-gray-100': layout !== 'grid',
					}"
					(click)="layout = 'grid'"
					class="p-button-text"
					pTooltip="Visualização em grade"
					tooltipPosition="top"
				></button>
				<button
					pButton
					icon="pi pi-list"
					[ngClass]="{
						'bg-primary-500 text-white': layout === 'table',
						'text-gray-600 hover:bg-gray-100': layout !== 'table',
					}"
					(click)="layout = 'table'"
					class="p-button-text"
					pTooltip="Visualização em tabela"
					tooltipPosition="top"
				></button>
			</div>
		</div>
	</div>
</div>

<!-- Visualização dos Clientes -->
@if (isLoading) {
	<div class="flex justify-center items-center h-64">
		<p-progressSpinner></p-progressSpinner>
	</div>
} @else if (filteredClients.length === 0) {
	<div class="bg-white rounded-xl shadow-sm p-8 text-center">
		<i class="pi pi-inbox text-5xl text-gray-300 mb-4"></i>
		<h3 class="text-xl font-medium text-gray-700 mb-2">Nenhum cliente encontrado</h3>
		<p class="text-gray-500 mb-6">Tente ajustar seus filtros de busca ou cadastre um novo cliente</p>
		<button
			pButton
			label="Criar Cliente"
			icon="pi pi-plus"
			class="p-button-primary"
			(click)="onNewClient()"
		></button>
	</div>
} @else {
	<!-- Visualização em Grade -->
	@if (layout === 'grid') {
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
			@for (client of filteredClients.slice(first, first + rows); track client.id) {
				<div class="p-0">
					<p-card styleClass="h-full border-0 shadow-sm hover:shadow-md transition-shadow duration-200">
						<ng-template pTemplate="header">
							<div class="relative">
								<div class="absolute top-3 right-3 z-10">
									<button
										pButton
										icon="pi pi-ellipsis-v"
										class="p-button-rounded p-button-text p-button-sm"
										[pTooltip]="'Opções'"
										tooltipPosition="left"
										(click)="showMenuAtPosition($event, menu)"
									></button>
									<p-menu #menu [model]="itemsMenu" [popup]="true" appendTo="body"></p-menu>
								</div>
								<div class="h-32 bg-gray-100 relative flex items-end p-5">
									<div class="absolute top-4 left-4">
										<p-tag
											[value]="client.status"
											[severity]="getSeverity(client.status)"
											[rounded]="true"
											styleClass="status-tag-sm"
										></p-tag>
									</div>
									<h3 class="text-primary-700 text-xl font-semibold line-clamp-2 w-full">
										{{ client.company }}
									</h3>
								</div>
							</div>
						</ng-template>

						<div class="flex flex-col h-full p-1">
							<div class="mb-4">
								<p class="text-sm text-gray-500 mb-1">Contato Principal</p>
								<div class="text-sm text-gray-500">
									@if (getContactPrimary(client.contacts)?.name) {
										{{ getContactPrimary(client.contacts)?.name }}
										@if (getContactPrimary(client.contacts)?.position) {
											<span class="text-xs text-gray-400">
												({{ getContactPrimary(client.contacts)?.position }})
											</span>
										}
									}
								</div>
							</div>

							<div class="mb-4">
								<p class="text-sm text-gray-500 mb-1">Setor</p>
								<p class="font-medium">{{ resumirSetor(client.sector) }}</p>
							</div>

							<div class="flex flex-wrap gap-1.5 mb-5">
								@for (tag of client.tags; track $index) {
									<p-chip [label]="tag" styleClass="bg-gray-100 text-gray-700 text-xs"></p-chip>
								}
							</div>

							<div class="mt-auto pt-4 border-t border-gray-100">
								<div class="flex justify-between items-center">
									<span class="text-sm text-gray-600">Cliente desde</span>
									<span class="text-sm font-medium">{{ formatDate(client.since) }}</span>
								</div>
								<div class="flex justify-between items-center mt-2">
									<span class="text-sm text-gray-600">Projetos</span>
									<span class="text-sm font-medium">{{ client.projectsCount }}</span>
								</div>
								<div class="flex justify-between items-center mt-2">
									<span class="text-sm text-gray-600">Valor Total</span>
									<span class="text-sm font-medium text-primary-700">{{ client.totalValue }}</span>
								</div>

								<!-- 🚀 NOVO: Botão de Relatório AI -->
								<div class="mt-3 pt-3 border-t border-gray-100">
									<button
										pButton
										icon="pi pi-file-text"
										[label]="isPdfAvailable(client) ? 'Ver Relatório' : 'Gerar Relatório'"
										class="p-button-primary p-button-sm w-full"
										size="small"
										(click)="currentClient = client; generateCompleteReport(client.id, client.name)"
										[disabled]="isGeneratingReport(client.id)"
										[pTooltip]="
											isPdfAvailable(client)
												? 'Visualizar relatório completo' + getPdfAge(client)
												: 'Gerar relatório completo com IA'
										"
										tooltipPosition="top"
									>
										@if (isGeneratingReport(client.id)) {
											<i class="pi pi-spin pi-spinner mr-2"></i>
											Carregando...
										}
									</button>
								</div>
							</div>
						</div>
					</p-card>
				</div>
			}
		</div>
	} @else {
		<!-- Visualização em Tabela -->
		<div class="overflow-hidden bg-white rounded-lg shadow-sm">
			<table class="min-w-full divide-y divide-gray-200">
				<thead class="bg-gray-50">
					<tr>
						<th
							scope="col"
							class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
						>
							Empresa/Contato
						</th>
						<th
							scope="col"
							class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
						>
							Setor
						</th>
						<th
							scope="col"
							class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
						>
							Status
						</th>
						<th
							scope="col"
							class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
						>
							Projetos
						</th>
						<th
							scope="col"
							class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
						>
							Valor Total
						</th>
						<th
							scope="col"
							class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
						>
							Último Contato
						</th>
						<th
							scope="col"
							class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"
						>
							PDF AI
						</th>
						<th
							scope="col"
							class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"
						>
							Ações
						</th>
					</tr>
				</thead>
				<tbody class="bg-white divide-y divide-gray-200">
					@for (client of filteredClients.slice(first, first + rows); track client.id) {
						<tr class="hover:bg-gray-50">
							<td class="px-6 py-4">
								<div class="flex items-center">
									<div>
										<div class="text-sm font-medium text-gray-900">{{ client.company }}</div>

										<div class="text-sm text-gray-500">
											@if (getContactPrimary(client.contacts)?.name) {
												{{ getContactPrimary(client.contacts)?.name }}
												@if (getContactPrimary(client.contacts)?.position) {
													<span class="text-xs text-gray-400">
														({{ getContactPrimary(client.contacts)?.position }})
													</span>
												}
											}
										</div>
									</div>
								</div>
							</td>
							<td class="px-6 py-4">
								<div class="text-sm text-gray-900">
									{{ setFirstLetterToUppercase(resumirSetor(client.sector)) }}
								</div>
							</td>
							<td class="px-6 py-4">
								<p-tag
									[value]="client.status"
									[severity]="getSeverity(client.status)"
									[rounded]="true"
									styleClass="status-tag-sm"
								></p-tag>
							</td>
							<td class="px-6 py-4">
								<div class="text-sm text-gray-900">{{ client.projectsCount }}</div>
								<div class="text-xs text-gray-500">Cliente desde {{ formatDate(client.since) }}</div>
							</td>
							<td class="px-6 py-4">
								<div class="text-sm font-medium text-primary-700">
									{{ formatCurrency(client.totalValue) }}
								</div>
							</td>
							<td class="px-6 py-4">
								<div class="text-sm text-gray-900">{{ displayLastContact(client.lastContact) }}</div>
							</td>
							<td class="px-6 py-4">
								<button
									pButton
									icon="pi pi-file-text"
									[label]="isPdfAvailable(client) ? 'Ver' : 'Gerar'"
									class="p-button-primary p-button-sm"
									size="small"
									(click)="currentClient = client; generateCompleteReport(client.id, client.name)"
									[disabled]="isGeneratingReport(client.id)"
									[pTooltip]="
										isPdfAvailable(client)
											? 'Visualizar relatório' + getPdfAge(client)
											: 'Gerar relatório completo'
									"
									tooltipPosition="top"
								>
									@if (isGeneratingReport(client.id)) {
										<i class="pi pi-spin pi-spinner mr-2"></i>
										...
									}
								</button>
							</td>
							<td class="px-6 py-4 text-center text-sm">
								<div class="flex justify-center">
									<button
										pButton
										icon="pi pi-eye"
										class="p-button-text p-button-rounded p-button-sm"
										(click)="showDetails()"
										[pTooltip]="acoesDesabilitadas(client) ? 'Por favor, aguarde.' : 'Visualizar'"
										tooltipPosition="top"
										[disabled]="acoesDesabilitadas(client)"
									></button>
									<button
										pButton
										icon="pi pi-pencil"
										class="p-button-text p-button-rounded p-button-sm"
										(click)="editClient()"
										[pTooltip]="acoesDesabilitadas(client) ? 'Por favor, aguarde.' : 'Editar'"
										tooltipPosition="top"
										[disabled]="acoesDesabilitadas(client)"
									></button>
									<button
										pButton
										icon="pi pi-ellipsis-v"
										class="p-button-text p-button-rounded p-button-sm"
										(click)="showMenuAtPosition($event, tableMenu, client)"
										[pTooltip]="acoesDesabilitadas(client) ? 'Por favor, aguarde.' : 'Mais opções'"
										tooltipPosition="top"
										[disabled]="acoesDesabilitadas(client)"
									></button>
									<p-menu #tableMenu [model]="itemsMenu" [popup]="true" appendTo="body"></p-menu>
								</div>
							</td>
						</tr>
					}
				</tbody>
			</table>
		</div>
	}

	<!-- Paginação -->
	<div class="mt-2">
		<p-paginator
			[rows]="rows"
			[totalRecords]="filteredClients.length"
			styleClass="bg-white rounded-lg shadow-sm p-3"
			(onPageChange)="onPageChange($event)"
			[first]="first"
			[showCurrentPageReport]="true"
			[showJumpToPageDropdown]="false"
			currentPageReportTemplate="Mostrando {first} a {last} de {totalRecords} clientes"
			[alwaysShow]="true"
		></p-paginator>
	</div>
}

<!-- 🚀 NOVO: Report Viewer Modal -->
<app-report-viewer
	[visible]="reportViewerVisible"
	[reportData]="currentReportData"
	(onHide)="closeReportViewer()"
></app-report-viewer>
