{"version": 3, "sources": ["../../../../../../node_modules/mermaid/dist/chunks/mermaid.core/chunk-TYCBKAJE.mjs"], "sourcesContent": ["import { insertEdge, insertEdgeLabel, markers_default, positionEdgeLabel } from \"./chunk-IIMUDSI4.mjs\";\nimport { insertCluster, insertNode, labelHelper } from \"./chunk-HRU6DDCH.mjs\";\nimport { interpolateToCurve } from \"./chunk-O4NI6UNU.mjs\";\nimport { __name, common_default, getConfig, log } from \"./chunk-YTJNT7DU.mjs\";\n\n// src/internals.ts\nvar internalHelpers = {\n  common: common_default,\n  getConfig,\n  insertCluster,\n  insertEdge,\n  insertEdgeLabel,\n  insertMarkers: markers_default,\n  insertNode,\n  interpolateToCurve,\n  labelHelper,\n  log,\n  positionEdgeLabel\n};\n\n// src/rendering-util/render.ts\nvar layoutAlgorithms = {};\nvar registerLayoutLoaders = /* @__PURE__ */__name(loaders => {\n  for (const loader of loaders) {\n    layoutAlgorithms[loader.name] = loader;\n  }\n}, \"registerLayoutLoaders\");\nvar registerDefaultLayoutLoaders = /* @__PURE__ */__name(() => {\n  registerLayoutLoaders([{\n    name: \"dagre\",\n    loader: /* @__PURE__ */__name(async () => await import(\"./dagre-OKDRZEBW.mjs\"), \"loader\")\n  }]);\n}, \"registerDefaultLayoutLoaders\");\nregisterDefaultLayoutLoaders();\nvar render = /* @__PURE__ */__name(async (data4Layout, svg) => {\n  if (!(data4Layout.layoutAlgorithm in layoutAlgorithms)) {\n    throw new Error(`Unknown layout algorithm: ${data4Layout.layoutAlgorithm}`);\n  }\n  const layoutDefinition = layoutAlgorithms[data4Layout.layoutAlgorithm];\n  const layoutRenderer = await layoutDefinition.loader();\n  return layoutRenderer.render(data4Layout, svg, internalHelpers, {\n    algorithm: layoutDefinition.algorithm\n  });\n}, \"render\");\nvar getRegisteredLayoutAlgorithm = /* @__PURE__ */__name((algorithm = \"\", {\n  fallback = \"dagre\"\n} = {}) => {\n  if (algorithm in layoutAlgorithms) {\n    return algorithm;\n  }\n  if (fallback in layoutAlgorithms) {\n    log.warn(`Layout algorithm ${algorithm} is not registered. Using ${fallback} as fallback.`);\n    return fallback;\n  }\n  throw new Error(`Both layout algorithms ${algorithm} and ${fallback} are not registered.`);\n}, \"getRegisteredLayoutAlgorithm\");\nexport { registerLayoutLoaders, render, getRegisteredLayoutAlgorithm };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAMA,IAAI,kBAAkB;AAAA,EACpB,QAAQ;AAAA,EACR;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,eAAe;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAGA,IAAI,mBAAmB,CAAC;AACxB,IAAI,wBAAuC,OAAO,aAAW;AAC3D,aAAW,UAAU,SAAS;AAC5B,qBAAiB,OAAO,IAAI,IAAI;AAAA,EAClC;AACF,GAAG,uBAAuB;AAC1B,IAAI,+BAA8C,OAAO,MAAM;AAC7D,wBAAsB,CAAC;AAAA,IACrB,MAAM;AAAA,IACN,QAAuB,OAAO,MAAS;AAAG,mBAAM,OAAO,8BAAsB;AAAA,QAAG,QAAQ;AAAA,EAC1F,CAAC,CAAC;AACJ,GAAG,8BAA8B;AACjC,6BAA6B;AAC7B,IAAI,SAAwB,OAAO,CAAO,aAAa,QAAQ;AAC7D,MAAI,EAAE,YAAY,mBAAmB,mBAAmB;AACtD,UAAM,IAAI,MAAM,6BAA6B,YAAY,eAAe,EAAE;AAAA,EAC5E;AACA,QAAM,mBAAmB,iBAAiB,YAAY,eAAe;AACrE,QAAM,iBAAiB,MAAM,iBAAiB,OAAO;AACrD,SAAO,eAAe,OAAO,aAAa,KAAK,iBAAiB;AAAA,IAC9D,WAAW,iBAAiB;AAAA,EAC9B,CAAC;AACH,IAAG,QAAQ;AACX,IAAI,+BAA8C,OAAO,CAAC,YAAY,IAAI;AAAA,EACxE,WAAW;AACb,IAAI,CAAC,MAAM;AACT,MAAI,aAAa,kBAAkB;AACjC,WAAO;AAAA,EACT;AACA,MAAI,YAAY,kBAAkB;AAChC,QAAI,KAAK,oBAAoB,SAAS,6BAA6B,QAAQ,eAAe;AAC1F,WAAO;AAAA,EACT;AACA,QAAM,IAAI,MAAM,0BAA0B,SAAS,QAAQ,QAAQ,sBAAsB;AAC3F,GAAG,8BAA8B;", "names": []}