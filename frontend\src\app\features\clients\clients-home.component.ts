import { <PERSON>son<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@angular/common';
import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, inject, signal } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { Subscription } from 'rxjs';

import { MenuItem, MessageService } from 'primeng/api';
import { AvatarModule } from 'primeng/avatar';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { ChipModule } from 'primeng/chip';
import { DataViewModule } from 'primeng/dataview';
import { DialogModule } from 'primeng/dialog';
import { InputTextModule } from 'primeng/inputtext';
import { Menu, MenuModule } from 'primeng/menu';
import { PaginatorModule } from 'primeng/paginator';
import { ProgressBarModule } from 'primeng/progressbar';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { SelectModule } from 'primeng/select';
import { TableModule } from 'primeng/table';
import { TagModule } from 'primeng/tag';
import { TooltipModule } from 'primeng/tooltip';

import { ReportData, ReportViewerComponent } from '../../shared/components/report-viewer/report-viewer.component';
import { ProcessingNotificationService } from '../../shared/notification/notification.service';

import { WebSocketService } from '../../shared/services/ws.service';
import {
	IClient,
	IClientContact,
	IPageChangeEvent,
	IPageSizeOption,
	ISectorOption,
	ISortOption,
	IStatusOption,
} from './client.interface';
import { ClientsService } from './clients.service';

@Component({
	selector: 'app-clients-home',
	standalone: true,
	imports: [
		NgClass,
		NgFor,
		NgIf,
		FormsModule,
		ReactiveFormsModule,
		ButtonModule,
		CardModule,
		DataViewModule,
		SelectModule,
		InputTextModule,
		DialogModule,
		TagModule,
		TooltipModule,
		ChipModule,
		ProgressBarModule,
		ProgressSpinnerModule,
		AvatarModule,
		MenuModule,
		PaginatorModule,
		TableModule,
		JsonPipe,
		ReportViewerComponent,
	],
	templateUrl: './clients-home.component.html',
	styles: [
		`
			:host ::ng-deep .p-dataview .p-dataview-header {
				background-color: transparent;
				border: none;
				padding: 0;
			}

			:host ::ng-deep .p-card .p-card-content {
				padding-top: 1rem;
				padding-bottom: 1rem;
			}

			:host ::ng-deep .p-select-panel .p-select-items .p-select-item {
				padding: 0.75rem 1.25rem;
				white-space: nowrap;
			}

			:host ::ng-deep .p-select-panel .p-select-items {
				padding: 0.5rem 0;
			}

			:host ::ng-deep .p-dataview .p-dataview-content {
				background: transparent;
				border: none;
				padding: 0;
			}

			:host ::ng-deep .p-paginator {
				background: white;
				border-radius: 0.5rem;
				margin-top: 1rem;
				padding: 0.5rem;
			}

			:host ::ng-deep .p-select-empty-message {
				display: none;
			}

			:host ::ng-deep .p-card {
				border-radius: 0.5rem;
				overflow: hidden;
			}

			:host ::ng-deep .p-card .p-card-body {
				padding: 1.25rem;
			}

			:host ::ng-deep .p-card .p-card-header {
				padding: 0;
			}

			:host ::ng-deep .p-select {
				width: 100%;
			}

			:host ::ng-deep .p-menu.p-menu-overlay {
				z-index: 1000;
			}

			:host ::ng-deep .p-menu-overlay {
				box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
			}

			:host ::ng-deep .p-tag {
				font-size: 0.65rem;
				padding: 0.1rem 0.4rem;
				height: 1.4rem;
				line-height: 1;
				min-width: auto;
				white-space: nowrap;
			}

			:host ::ng-deep .status-tag-sm {
				font-size: 0.8rem !important;
				padding: 0.5rem 0.7rem !important;
				height: 1.1rem !important;
				line-height: 0.9 !important;
				margin: 0 !important;
				display: inline-flex !important;
				align-items: center !important;
				font-weight: normal !important;
				border-radius: 0.8rem !important;
			}

			:host ::ng-deep .p-chip {
				font-size: 0.7rem;
				padding: 0.1rem 0.4rem;
			}

			:host ::ng-deep .p-datatable .p-datatable-header {
				background: transparent;
				border: none;
				padding: 0 0 1rem 0;
			}

			:host ::ng-deep .p-datatable .p-datatable-thead > tr > th {
				background: #f8f9fa;
				color: #495057;
				font-weight: 600;
				padding: 0.75rem 1rem;
			}

			:host ::ng-deep .p-datatable .p-datatable-tbody > tr {
				background: white;
				transition: all 0.2s;
			}

			:host ::ng-deep .p-datatable .p-datatable-tbody > tr:hover {
				background: #f8f9fa;
			}

			@media (min-width: 768px) {
				:host ::ng-deep .p-select {
					min-width: 180px;
				}
			}
		`,
	],
})
export class ClientsHomeComponent implements OnInit, OnDestroy {
	private ws = inject(WebSocketService);
	private subscriptions: Array<Subscription> = [];

	public readonly clients = signal<Array<IClient>>([]);
	public filteredClients: Array<IClient> = [];
	public layout: 'grid' | 'table' = 'table';
	public currentClient?: IClient;
	public sortOptions: Array<ISortOption> = [
		{ label: 'Nome (A-Z)', value: 'name' },
		{ label: 'Nome (Z-A)', value: '!name' },
		{ label: 'Empresa (A-Z)', value: 'company' },
		{ label: 'Empresa (Z-A)', value: '!company' },
		{ label: 'Valor Total (maior)', value: '!totalValue' },
		{ label: 'Valor Total (menor)', value: 'totalValue' },
	];
	public sortKey = 'name';
	public sortOrder = 1;
	public sortField = 'name';
	public rows = 10;
	public isLoading = false;
	public searchQuery = '';
	public selectedStatus: IStatusOption = { label: 'Todos', value: null };
	public selectedSector: ISectorOption = { label: 'Todos', value: null };
	public itemsMenu: Array<MenuItem> = [];
	public pageSizeOptions: Array<IPageSizeOption> = [
		{ label: '5 por página', value: 5 },
		{ label: '10 por página', value: 10 },
		{ label: '20 por página', value: 20 },
		{ label: '50 por página', value: 50 },
	];
	public selectedPageSize: IPageSizeOption = { label: '10 por página', value: 10 };
	public first = 0;
	public isSaving = false;

	// 🚀 NOVO: Propriedades para ReportViewer
	public reportViewerVisible = signal(false);
	public currentReportData = signal<ReportData | null>(null);

	public statusOptions: Array<IStatusOption> = [
		{ label: 'Todos', value: null },
		{ label: 'Novo', value: 'Novo' },
		{ label: 'Ativo', value: 'Ativo' },
		{ label: 'Inativo', value: 'Inativo' },
		{ label: 'Processando', value: 'Processando' },
		{ label: 'Coletando informações', value: 'Coletando informações' },
		{ label: 'Prospecto', value: 'Prospecto' },
		{ label: 'Em pausa', value: 'Em pausa' },
		{ label: 'Erro', value: 'Erro' },
	];

	public sectorOptions: Array<ISectorOption> = [
		{ label: 'Todos', value: null },
		{ label: 'Tecnologia', value: 'Tecnologia' },
		{ label: 'Saúde', value: 'Saúde' },
		{ label: 'Educação', value: 'Educação' },
		{ label: 'Financeiro', value: 'Financeiro' },
		{ label: 'Varejo', value: 'Varejo' },
		{ label: 'Indústria', value: 'Indústria' },
		{ label: 'Outros', value: 'Outros' },
	];

	public showNewClientDialog = false;
	public newClientForm: FormGroup;

	// 🚀 NOVO: Mapa para controlar estado de geração
	private generatingReports = new Set<string>();

	constructor(
		private messageService: MessageService,
		private fb: FormBuilder,
		private clientsService: ClientsService,
		private processingNotificationService: ProcessingNotificationService,
	) {
		this.newClientForm = this.fb.group({
			name: ['', Validators.required],
			city: ['', Validators.required],
			state: ['', Validators.required],
			site: ['', Validators.required],
			cpfCnpj: [''],
			phone: [''],
			responsible: [''],
			responsibleRole: [''],
		});
	}

	public ngOnInit(): void {
		this.loadClients();
		this.setupMenuItems();

		// ✅ Escutar apenas atualizações de clientes (sem notificações globais)
		this.setupClientUpdates();
	}

	public ngOnDestroy(): void {
		// ✅ Limpar subscriptions
		this.subscriptions.forEach(sub => sub.unsubscribe());
	}

	// ✅ Configurar escuta específica para atualizações de clientes
	private setupClientUpdates(): void {
		// ✅ Escutar atualizações de status de cliente
		const clientUpdatesSubscription = this.ws.clientUpdates$.subscribe(update => {
			this.updateClientInList(update.clientId, update.status);
		});

		// ✅ NOVO: Escutar atualizações de status de coleta
		const collectionStatusSubscription = this.ws.collectionStatus$.subscribe(update => {
			this.updateClientInList(update.clientId, update.status);

			// ✅ NOTA: Notificações globais são tratadas pelo AppComponent
			// para evitar duplicação entre diferentes componentes
		});

		// ✅ NOVO: Escutar projetos gerados
		const projectsGeneratedSubscription = this.ws.projectsGenerated$.subscribe(update => {
			this.messageService.add({
				severity: 'success',
				summary: '✅ Projetos Gerados',
				detail: `${update.projects.length} projetos gerados para ${update.clientName}!`,
				life: 6000,
			});

			// Recarregar lista de clientes para atualizar contadores de projetos
			this.loadClients();
		});

		this.subscriptions.push(clientUpdatesSubscription, collectionStatusSubscription, projectsGeneratedSubscription);
	}

	// ✅ Atualizar cliente específico na lista
	private updateClientInList(clientId: string, newStatus: string): void {
		const clients = this.clients();
		const clientIndex = clients.findIndex(c => c.id === clientId);

		if (clientIndex >= 0) {
			// ✅ Criar novo array com cliente atualizado
			const updatedClients = [...clients];
			updatedClients[clientIndex] = {
				...updatedClients[clientIndex],
				status: newStatus,
			};

			this.clients.set(updatedClients);

			// ✅ Atualizar lista filtrada também
			this.filterClients();
		} else {
			console.log(`⚠️ Cliente ${clientId} não encontrado na lista`);
		}
	}

	public onNewClient(): void {
		this.newClientForm.reset();
		this.showNewClientDialog = true;
	}

	public saveNewClient(): void {
		if (this.newClientForm.valid) {
			this.isSaving = true;

			// Close dialog immediately to allow navigation
			const formData = { ...this.newClientForm.value };
			this.showNewClientDialog = false;
			this.newClientForm.reset();

			// Show optimistic message
			this.messageService.add({
				severity: 'info',
				summary: 'Cadastrando...',
				detail: 'Cliente está sendo cadastrado...',
				life: 2000,
			});

			// Execute in background without blocking
			this.clientsService.createClient(formData).subscribe({
				next: _created => {
					this.isSaving = false;
					this.messageService.add({
						severity: 'success',
						summary: 'Sucesso',
						detail: 'Cliente cadastrado com sucesso! 🎉',
					});
					this.loadClients();
				},
				error: _err => {
					this.isSaving = false;
					this.messageService.add({
						severity: 'error',
						summary: 'Erro ao cadastrar',
						detail: 'Não foi possível cadastrar o cliente. Verifique os dados ou tente novamente.',
					});
				},
			});
		} else {
			this.newClientForm.markAllAsTouched();
		}
	}

	public cancelNewClient(): void {
		this.showNewClientDialog = false;
		this.newClientForm.reset();
	}

	public onImport(): void {
		this.messageService.add({
			severity: 'info',
			summary: 'Importação',
			detail: 'Funcionalidade de importação de clientes será implementada em breve.',
		});
	}

	private loadClients(): void {
		// Don't show loading state to avoid blocking UI
		// Keep existing data visible while loading new data
		this.clientsService.getClients().subscribe({
			next: (clientes: Array<IClient>) => {
				const ajustados = clientes.map(c => {
					// Preservar setor válido já existente na lista local
					const clienteExistente = this.clients().find(existing => existing.id === c.id);
					const setorExistente = clienteExistente?.sector;

					// Usar setor do backend, mas preservar se já tínhamos um válido e o backend retornou vazio
					let setorFinal = c.sector && c.sector !== '' && c.sector !== null ? c.sector : 'Processando...';

					// Se já tínhamos um setor válido e o novo está "Processando...", preservar o anterior
					if (setorExistente && setorExistente !== 'Processando...' && setorFinal === 'Processando...') {
						setorFinal = setorExistente;
					}

					return {
						...c,
						sector: setorFinal,
					};
				});
				this.clients.set(ajustados);
				this.filteredClients = [...ajustados];
				this.isLoading = false;
			},
			error: () => {
				this.isLoading = false;
				// Only show error if we don't have any data
				if (this.clients().length === 0) {
					this.messageService.add({
						severity: 'error',
						summary: 'Erro ao carregar clientes',
						detail: 'Não foi possível carregar os clientes. Verifique a conexão com a internet ou tente novamente.',
					});
				}
			},
		});
	}

	private setupMenuItems(): void {
		// Verificar se o cliente atual tem relatório disponível
		const hasReport = this.currentClient && this.isPdfAvailable(this.currentClient);

		this.itemsMenu = [
			{
				label: 'Opções',
				items: [
					{
						label: 'Visualizar detalhes',
						icon: 'pi pi-eye',
						command: () => this.showDetails(),
					},
					{
						label: 'Editar cliente',
						icon: 'pi pi-pencil',
						command: () => this.editClient(),
					},
					{
						label: hasReport ? 'Visualizar Relatório' : 'Gerar Relatório',
						icon: 'pi pi-file-text',
						command: () =>
							this.currentClient &&
							this.generateCompleteReport(this.currentClient.id, this.currentClient.name),
					},
					// 🚀 NOVO: Opção para gerar nova versão - SEMPRE disponível
					{
						label: 'Gerar Nova Versão',
						icon: 'pi pi-refresh',
						command: () =>
							this.currentClient &&
							this.regenerateCompleteReport(this.currentClient.id, this.currentClient.name),
					},
					// 🚀 NOVO: Opção para recriar projetos
					{
						label: 'Recriar projetos',
						icon: 'pi pi-sync',
						command: () =>
							this.currentClient && this.recreateProjects(this.currentClient.id, this.currentClient.name),
					},
					{
						label: 'Arquivar',
						icon: 'pi pi-inbox',
						command: () => this.archiveClient(),
					},
				],
			},
		];
	}

	public showMenuAtPosition(event: MouseEvent, menu: Menu, client?: IClient): void {
		event.stopPropagation();
		// 🚀 NOVO: Guardar cliente atual para uso no menu
		this.currentClient = client;
		this.setupMenuItems(); // Recriar menu com base no cliente atual
		menu.toggle(event);
	}

	public showDetails(): void {
		this.messageService.add({
			severity: 'info',
			summary: 'Detalhes',
			detail: 'Detalhes do cliente serão exibidos em breve.',
		});
	}

	public editClient(): void {
		this.messageService.add({
			severity: 'info',
			summary: 'Editar Cliente',
			detail: 'Funcionalidade de edição de cliente será implementada em breve.',
		});
	}

	public generateReport(clientId?: string): void {
		if (!clientId) {
			this.messageService.add({
				severity: 'warn',
				summary: 'Cliente não selecionado',
				detail: 'Selecione um cliente para gerar o relatório.',
			});
			return;
		}

		// Verificar se já existe um processamento em andamento
		const processingState = this.ws.getProcessingState(clientId);
		if (processingState?.isProcessing) {
			this.messageService.add({
				severity: 'info',
				summary: 'Processamento em Andamento',
				detail: 'A análise deste cliente já está sendo processada.',
			});
			return;
		}

		// Verificar se o relatório já está disponível
		if (processingState?.canViewReport) {
			this.clientsService.downloadReport(clientId).subscribe({
				next: (blob: Blob) => {
					const url = window.URL.createObjectURL(blob);
					window.open(url, '_blank');
					window.URL.revokeObjectURL(url);
				},
				error: (error: unknown) => {
					console.error('Erro ao baixar relatório:', error);
					this.messageService.add({
						severity: 'error',
						summary: 'Erro ao Abrir Relatório',
						detail: 'Não foi possível abrir o relatório. Tente novamente.',
					});
				},
			});
			return;
		}

		// Iniciar novo processamento via HTTP endpoint
		this.clientsService.startFullProcessing(clientId).subscribe({
			next: (_response: unknown) => {
				this.messageService.add({
					severity: 'success',
					summary: 'Análise Iniciada',
					detail: 'A análise completa do cliente foi iniciada. Você será notificado via WebSocket quando estiver concluída.',
				});
			},
			error: (error: unknown) => {
				console.error('Erro ao iniciar processamento:', error);
				this.messageService.add({
					severity: 'error',
					summary: 'Erro ao Iniciar Análise',
					detail: 'Não foi possível iniciar a análise. Tente novamente.',
				});
			},
		});
	}

	public archiveClient(): void {
		this.messageService.add({
			severity: 'info',
			summary: 'Arquivar Cliente',
			detail: 'Funcionalidade de arquivamento de cliente será implementada em breve.',
		});
	}

	public onPageChange(event: IPageChangeEvent): void {
		this.first = event.first !== undefined ? event.first : 0;
		this.rows = event.rows !== undefined ? event.rows : this.rows;
	}

	public onPageSizeChange(event: { value: IPageSizeOption }): void {
		this.rows = event.value.value;
		this.first = 0;
	}

	public filterClients(): void {
		let filtered = [...this.clients()];

		// Filtro por texto
		if (this.searchQuery) {
			const searchLower = this.searchQuery.toLowerCase();
			filtered = filtered.filter(
				client =>
					client.name.toLowerCase().includes(searchLower) ||
					client.company.toLowerCase().includes(searchLower) ||
					client.email.toLowerCase().includes(searchLower) ||
					client.sector.toLowerCase().includes(searchLower) ||
					client.tags.some(tag => tag.toLowerCase().includes(searchLower)),
			);
		}

		// Filtro por status
		if (this.selectedStatus && this.selectedStatus.value !== null) {
			filtered = filtered.filter(client => client.status === this.selectedStatus.value);
		}

		// Filtro por setor
		if (this.selectedSector && this.selectedSector.value !== null) {
			filtered = filtered.filter(client => client.sector === this.selectedSector.value);
		}

		// Aplicar ordenação
		this.applySorting(filtered);

		this.filteredClients = filtered;
	}

	private applySorting(clients: Array<IClient>): void {
		if (!this.sortKey) return;

		const isReverse = this.sortKey.startsWith('!');
		const field = isReverse ? this.sortKey.substring(1) : this.sortKey;
		const direction = isReverse ? -1 : 1;

		clients.sort((a, b) => {
			let valueA: string | number | boolean | Array<string> | Array<IClientContact> | undefined;
			let valueB: string | number | boolean | Array<string> | Array<IClientContact> | undefined;

			// Determina os valores baseados no campo
			switch (field) {
				case 'name':
					valueA = a.name;
					valueB = b.name;
					break;
				case 'company':
					valueA = a.company;
					valueB = b.company;
					break;
				case 'sector':
					valueA = a.sector;
					valueB = b.sector;
					break;
				case 'projectsCount':
					valueA = a.projectsCount;
					valueB = b.projectsCount;
					break;
				case 'since':
					valueA = new Date(a.since).getTime();
					valueB = new Date(b.since).getTime();
					break;
				case 'lastContact':
					valueA = new Date(a.lastContact).getTime();
					valueB = new Date(b.lastContact).getTime();
					break;
				case 'status':
					valueA = a.status;
					valueB = b.status;
					break;
				case 'totalValue':
					// Extrair valor numérico removendo "R$ " e convertendo
					valueA = Number(a.totalValue.replace('R$ ', '').replace('.', '').replace(',', '.'));
					valueB = Number(b.totalValue.replace('R$ ', '').replace('.', '').replace(',', '.'));
					break;
				default:
					valueA = a[field as keyof IClient];
					valueB = b[field as keyof IClient];
			}

			// Tratamento de valores para garantir que a comparação funcione corretamente
			// Converte arrays para strings para poder compará-los ou trata valores undefined
			if (valueA === undefined) valueA = '';
			if (valueB === undefined) valueB = '';

			// Converter arrays para string para comparação
			if (Array.isArray(valueA)) {
				valueA = valueA.length.toString();
			}
			if (Array.isArray(valueB)) {
				valueB = valueB.length.toString();
			}

			if (valueA < valueB) return -1 * direction;
			if (valueA > valueB) return 1 * direction;
			return 0;
		});
	}

	public onSortChange(event: { value: string }): void {
		this.sortKey = event.value;
		this.sortOrder = event.value.startsWith('!') ? -1 : 1;
		this.sortField = event.value.replace('!', '');
		this.filterClients();
	}

	public getSeverity(status: string): string {
		switch (status) {
			case 'Novo':
				return 'success';
			case 'Ativo':
				return 'success';
			case 'Inativo':
				return 'danger';
			case 'Processando':
				return 'warning';
			case 'Coletando informações':
				return 'info';
			case 'Prospecto':
				return 'info';
			case 'Em pausa':
				return 'warning';
			case 'Erro':
				return 'danger';
			default:
				return 'info';
		}
	}

	public formatDate(dateString: string): string {
		const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'short', day: 'numeric' };
		return new Date(dateString).toLocaleDateString('pt-BR', options);
	}

	public formatCurrency(value: string): string {
		// O valor já vem formatado, mas poderíamos implementar uma formatação adicional se necessário
		return value;
	}

	public getContactPrimary(contacts: Array<IClientContact>): IClientContact | undefined {
		if (!contacts || contacts.length === 0) return undefined;
		return contacts.find(contact => contact.primary) || contacts[0];
	}

	public setFirstLetterToUppercase(sector: string): string {
		return sector.charAt(0).toUpperCase() + sector.slice(1);
	}

	public displayLastContact(value: string): string {
		if (!value || value === '-' || value.trim() === '') return '-';
		const date = new Date(value);
		return isNaN(date.getTime()) ? '-' : date.toLocaleDateString('pt-BR');
	}

	public resumirSetor(sector: string): string {
		if (!sector) return '-';
		return sector
			.split(' ')[0]
			.normalize('NFD')
			.replace(/[\u0300-\u036f]/g, '')
			.toLowerCase();
	}

	public acoesDesabilitadas(client: IClient): boolean {
		// ✅ Verificar se há processamento em andamento via WebSocket
		const processingState = this.ws.getProcessingState(client.id);
		if (processingState?.isProcessing) {
			return true;
		}

		// ✅ NOVO: Desabilitar ações durante coleta de informações
		if (client.status === 'Coletando informações' || client.status === 'Processando') {
			return true;
		}

		// ✅ Verificar se o setor ainda não foi processado
		return !client.sector || client.sector === 'Processando...';
	}

	// 🚀 NOVO: Verificar se PDF está disponível
	public isPdfAvailable(client: IClient): boolean {
		return this.clientsService.isPdfReportAvailable(client);
	}

	// 🚀 NOVO: Calcular idade do PDF para mostrar no tooltip
	public getPdfAge(client: IClient): string {
		if (!client.pdf_generated_at) return '';

		const generated = new Date(client.pdf_generated_at);
		const now = new Date();
		const diffTime = Math.abs(now.getTime() - generated.getTime());
		const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

		if (diffDays === 0) return ' (hoje)';
		if (diffDays === 1) return ' (1 dia)';
		if (diffDays <= 7) return ` (${diffDays} dias)`;
		return ' (antigo - considere gerar novo)';
	}

	// 🚀 NOVO: Verificar se está gerando relatório
	public isGeneratingReport(clientId: string): boolean {
		return this.generatingReports.has(clientId);
	}

	// 🚀 NOVO: Gerar relatório completo (carrega primeiro, depois abre modal)
	public generateCompleteReport(clientId: string, clientName: string): void {
		// Check if already generating to prevent duplicate requests
		if (this.generatingReports.has(clientId)) {
			this.messageService.add({
				severity: 'warn',
				summary: 'Relatório em Geração',
				detail: `O relatório para ${clientName} já está sendo gerado...`,
				life: 3000,
			});
			return;
		}

		// Adicionar ao set de relatórios em geração
		this.generatingReports.add(clientId);

		this.messageService.add({
			severity: 'info',
			summary: 'Gerando Relatório',
			detail: `Carregando relatório com IA para ${clientName}...`,
			life: 3000,
		});

		// Carregar/gerar relatório markdown SEM abrir modal ainda
		this.loadReportData(clientId, clientName);
	}

	// 🚀 NOVO: Carregar dados do relatório (SEM abrir modal)
	private loadReportData(clientId: string, clientName?: string): void {
		// Primeiro tentar buscar relatório existente
		this.clientsService.getMarkdownReport(clientId).subscribe({
			next: (response: any) => {
				// Remover do set quando carregar com sucesso
				this.generatingReports.delete(clientId);

				if (response.success && response.markdown_content) {
					// Definir dados do relatório
					this.currentReportData.set({
						client_id: response.client_id,
						client_name: response.client_name,
						markdown_content: response.markdown_content,
						metadata: response.metadata,
					});

					// AGORA sim abrir o modal
					this.showReportModal();
				} else {
					this.handleReportError('Relatório não encontrado ou vazio.');
				}
			},
			error: (error: any) => {
				console.error('Erro ao buscar relatório:', error);

				// Se não encontrou, tentar gerar novo
				if (error.status === 404) {
					this.generateNewReportData(clientId, clientName);
				} else {
					// Remover do set em caso de erro
					this.generatingReports.delete(clientId);
					this.handleReportError('Erro ao carregar relatório. Tente novamente.');
				}
			},
		});
	}

	// 🚀 NOVO: Gerar novo relatório (SEM abrir modal)
	private generateNewReportData(clientId: string, clientName?: string): void {
		this.clientsService.generateMarkdownReport(clientId).subscribe({
			next: (response: any) => {
				// Remover do set quando gerar com sucesso
				this.generatingReports.delete(clientId);

				if (response.success && response.markdown_content) {
					this.currentReportData.set({
						client_id: response.client_id,
						client_name: response.client_name,
						markdown_content: response.markdown_content,
						metadata: response.metadata,
					});

					this.messageService.add({
						severity: 'success',
						summary: 'Relatório Gerado',
						detail: `Novo relatório gerado com sucesso${clientName ? ` para ${clientName}` : ''}!`,
						life: 4000,
					});

					// Atualizar lista de clientes para refletir que agora tem relatório
					this.loadClients();

					// AGORA sim abrir o modal
					this.showReportModal();
				} else {
					this.handleReportError('Erro ao gerar relatório.');
				}
			},
			error: (error: any) => {
				console.error('Erro ao gerar relatório:', error);
				// Remover do set em caso de erro
				this.generatingReports.delete(clientId);
				this.handleReportError('Erro ao gerar relatório. Verifique se o cliente possui dados suficientes.');
			},
		});
	}

	// 🚀 NOVO: Abrir modal do relatório
	private showReportModal(): void {
		this.reportViewerVisible.set(true);
	}

	// 🚀 NOVO: Tratar erros do relatório
	private handleReportError(message: string): void {
		this.currentReportData.set(null);
		this.generatingReports.clear(); // Limpar todos os processamentos em caso de erro
		this.messageService.add({
			severity: 'error',
			summary: 'Erro no Relatório',
			detail: message,
			life: 5000,
		});
	}

	// 🚀 NOVO: Fechar visualizador de relatório
	public closeReportViewer(): void {
		this.reportViewerVisible.set(false);
		this.currentReportData.set(null);
	}

	// 🚀 NOVO: Regenerar relatório (FORÇAR nova geração)
	public regenerateCompleteReport(clientId: string, clientName: string): void {
		// Adicionar ao set de relatórios em geração
		this.generatingReports.add(clientId);

		this.messageService.add({
			severity: 'info',
			summary: 'Regenerando Relatório',
			detail: `Gerando nova versão do relatório para ${clientName}...`,
			life: 3000,
		});

		// FORÇAR regeneração usando diretamente o método de geração
		this.generateNewReportData(clientId, clientName);
	}

	// 🚀 NOVO: Recriar projetos para o cliente
	public recreateProjects(clientId: string, clientName: string): void {
		this.messageService.add({
			severity: 'info',
			summary: 'Recriando Projetos',
			detail: `Iniciando recriação de projetos para ${clientName}...`,
			life: 4000,
		});

		// Chamar o serviço para recriar projetos
		this.clientsService.recreateProjects(clientId).subscribe({
			next: (response: any) => {
				if (response.success) {
					this.messageService.add({
						severity: 'success',
						summary: 'Projetos Recriados',
						detail: `Novos projetos foram gerados com sucesso para ${clientName}!`,
						life: 5000,
					});
					// Atualizar lista de clientes para refletir mudanças
					this.loadClients();
				} else {
					this.messageService.add({
						severity: 'error',
						summary: 'Erro ao Recriar Projetos',
						detail: response.message || 'Não foi possível recriar os projetos.',
						life: 5000,
					});
				}
			},
			error: (error: any) => {
				console.error('Erro ao recriar projetos:', error);
				this.messageService.add({
					severity: 'error',
					summary: 'Erro ao Recriar Projetos',
					detail: 'Ocorreu um erro ao tentar recriar os projetos. Tente novamente.',
					life: 5000,
				});
			},
		});
	}
}
