from pymongo import MongoClient
from config.settings import env
import logging

logger = logging.getLogger(__name__)

MONGO_DETAILS = env.MONGODB_ATLAS_CONNECTION_URI

client = MongoClient(MONGO_DETAILS)
db = client[env.MONGODB_DATABASE_NAME]

# Coleções
clients_collection = db["clients"]
projetos_collection = db["projetos"]
# Adicione outras coleções conforme necessário

# Inicializar schemas e índices MongoDB


def initialize_database():
    """
    Inicializa configuração do banco de dados (schemas e índices)
    """
    try:
        from .schemas import initialize_mongodb_setup
        success = initialize_mongodb_setup()
        if success:
            logger.info("Database inicializado com sucesso")
        else:
            logger.warning("Database inicializado com alguns avisos")
        return success
    except ImportError:
        logger.warning(
            "Módulo schemas não encontrado, pulando inicialização de schemas")
        return False
    except Exception as e:
        logger.error(f"Erro ao inicializar database: {e}")
        return False


# Inicializar automaticamente quando o módulo for importado
try:
    initialize_database()
except Exception as e:
    logger.warning(f"Falha na inicialização automática do database: {e}")
