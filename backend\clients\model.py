from pydantic import BaseModel, Field, field_validator
from typing import List, Optional, Dict, Any, Literal
from datetime import datetime
from pydantic import validator


class ClientCreate(BaseModel):
    name: str
    city: str
    state: str
    cpfCnpj: Optional[str] = None
    phone: Optional[str] = None
    responsible: Optional[str] = None
    responsibleRole: Optional[str] = None
    site: Optional[str] = None
    contacts: List[Dict] = Field(default_factory=list)


class QuickClientCreate(BaseModel):
    """
    Modelo para cadastro rápido de cliente (FASE 1)

    Mesmos campos do ClientCreate mas otimizado para processamento rápido
    """
    name: str
    city: str
    state: str
    cpfCnpj: Optional[str] = None
    phone: Optional[str] = None
    responsible: Optional[str] = None
    responsibleRole: Optional[str] = None
    site: Optional[str] = None
    contacts: List[Dict] = Field(default_factory=list)

    class Config:
        json_schema_extra = {
            "example": {
                "name": "Empresa Exemplo",
                "city": "São Paulo",
                "state": "SP",
                "cpfCnpj": "12.345.678/0001-90",
                "phone": "(11) 99999-9999",
                "responsible": "João Silva",
                "responsibleRole": "CEO",
                "site": "https://exemplo.com.br"
            }
        }


class ReportMetadata(BaseModel):
    """Metadados para todos os tipos de reports"""
    dataCollectedAt: datetime = Field(default_factory=datetime.utcnow)
    processingTime: Optional[float] = None  # em segundos
    sources: List[str] = Field(default_factory=list)  # URLs ou APIs utilizadas
    aiModelsUsed: List[str] = Field(
        default_factory=list)  # modelos de IA utilizados


class Report(BaseModel):
    """Estrutura base para todos os reports"""
    reportType: Literal[
        "dossie_expandido",
        "mercado_futuro",
        "produtos_servicos",
        "diagnostico_lighthouse",
        "diagnostico_visual",
        "metricas_business"
    ]
    name: str  # Ex: "Análise de Mercado - Tecnologia 2024"
    data: Dict[str, Any]  # Dados específicos do tipo de report
    metadata: ReportMetadata
    status: Literal["processing", "completed",
                    "failed", "partial"] = "processing"
    version: str = "1.0"
    createdAt: datetime = Field(default_factory=datetime.utcnow)
    updatedAt: datetime = Field(default_factory=datetime.utcnow)


class DossieExpandido(BaseModel):
    """Estrutura específica para dossiê expandido"""
    informacoes_gerais: Dict[str, Any]
    historico: str
    produtos_servicos: List[str]
    concorrentes: Dict[str, Any]
    cases_sucesso: Dict[str, Any]
    saude_financeira: Dict[str, Any]
    analise_swot: Dict[str, Any]  # SWOT básico
    analise_swot_expandida: Dict[str, Any]  # SWOT detalhado
    stack_tecnico: Dict[str, Any]  # Análise técnica completa
    oportunidades_novos_produtos: List[str]
    melhorias_futuras: List[str]
    report_type: str = "dossie_expandido"
    version: str = "1.0"


class ClientDB(ClientCreate):
    projects: List[str] = []  # IDs dos projetos vinculados
    setor: Optional[str] = None
    sector: Optional[str] = None  # Campo adicional para compatibilidade
    tags: List[str] = []
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    reports: List[Dict[str, Any]] = []  # Lista de reports de diferentes tipos
    status: str = "Novo"
    projectsCount: int = 0
    totalValue: str = "R$ 0,00"
    lastContact: str = "-"


class AnaliseSwotExpandida(BaseModel):
    """Estrutura para análise SWOT expandida"""
    forcas_detalhadas: Dict[str, str]
    fraquezas_detalhadas: Dict[str, str]
    oportunidades_detalhadas: Dict[str, str]
    ameacas_detalhadas: Dict[str, str]
    estrategias_recomendadas: Dict[str, List[str]]
    prioridades_acao: Dict[str, List[str]]


class StackTecnico(BaseModel):
    """Estrutura para análise de stack técnico"""
    frontend: Dict[str, List[str]]
    backend: Dict[str, List[str]]
    banco_dados: Dict[str, List[str]]
    infraestrutura: Dict[str, List[str]]
    monitoramento: Dict[str, List[str]]
    seguranca: Dict[str, Any]
    analytics_marketing: Dict[str, List[str]]
    terceiros_integracoes: Dict[str, List[str]]
    analise_tecnica: Dict[str, Any]
    metodologia_deteccao: Dict[str, Any]


class ClientResponse(BaseModel):
    """Resposta padronizada para operações com clientes"""
    id: str
    name: str
    company: str
    email: str
    phone: str
    address: str
    sector: Optional[str]
    active: bool
    since: str
    projectsCount: int
    totalValue: str
    lastContact: str
    status: str
    tags: List[str]
    contacts: List[Dict[str, Any]]
    reports: List[Dict[str, Any]]
    created_at: str
    updated_at: str


class DadosFunding(BaseModel):
    """Estrutura para dados de histórico de funding"""
    resumo_funding: Dict[str, str]
    rodadas_investimento: List[Dict[str, Any]]
    investidores_principais: Dict[str, List[str]]
    metricas_investimento: Dict[str, str]
    analise_competitiva_funding: Dict[str, str]
    perspectivas_futuras: Dict[str, Any]
    fontes_informacao: Dict[str, Any]


class DadosPresencaDigital(BaseModel):
    """Estrutura para dados de presença digital"""
    resumo_presenca_digital: Dict[str, str]
    metricas_seo: Dict[str, str]
    principais_keywords: List[Dict[str, str]]
    redes_sociais: Dict[str, Dict[str, str]]
    analise_site: Dict[str, str]
    conteudo_digital: Dict[str, str]
    reputacao_online: Dict[str, Any]
    competitividade_digital: Dict[str, Any]
    recomendacoes_estrategicas: Dict[str, Any]
    fontes_analise: Dict[str, Any]


class DadosCanaisReviews(BaseModel):
    """Estrutura para dados de canais de distribuição, reviews e certificações"""
    dados_canais_distribuicao: Dict[str, Any]
    dados_reviews_feedback: Dict[str, Any]
    dados_certificacoes: Dict[str, Any]
    analise_reputacional: Dict[str, Any]
    competitividade_canais: Dict[str, Any]
    fontes_informacao: Dict[str, Any]


class DadosPesquisaMercado(BaseModel):
    """Estrutura para dados de pesquisa de mercado e produtos/serviços"""
    pesquisa_mercado: Dict[str, Any]
    analise_produtos_servicos: Dict[str, Any]
    insights_competitivos: Dict[str, Any]
    fontes_informacao: Dict[str, Any]


class DadosDiagnosticoTecnico(BaseModel):
    """
    Modelo para dados de diagnóstico técnico (Lighthouse + Playwright + IA)

    Estrutura dados de:
    - Análise Lighthouse (performance, acessibilidade, SEO, best practices)
    - Screenshots automatizados (desktop/mobile)
    - Análise visual por IA
    - Relatório consolidado com recomendações
    """
    diagnostico_lighthouse: Dict[str, Any] = Field(
        ...,
        description="Resultados da análise Lighthouse completa"
    )
    analise_visual: Dict[str, Any] = Field(
        ...,
        description="Análise visual dos screenshots por IA"
    )
    relatorio_consolidado: Dict[str, Any] = Field(
        ...,
        description="Relatório final consolidado com score geral e recomendações"
    )
    data_quality_score: float = Field(
        default=0.0,
        ge=0.0,
        le=10.0,
        description="Score de qualidade dos dados coletados (0-10)"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Metadados adicionais da análise (timestamp, versões, etc.)"
    )

    class Config:
        """Configuração do modelo"""
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }

    @field_validator('diagnostico_lighthouse')
    @classmethod
    def validate_lighthouse_data(cls, v):
        """Valida estrutura básica dos dados Lighthouse"""
        required_categories = ['performance',
                               'acessibilidade', 'seo_tecnico', 'best_practices']

        if not isinstance(v, dict):
            raise ValueError("diagnostico_lighthouse deve ser um dicionário")

        missing_categories = [
            cat for cat in required_categories if cat not in v]
        if missing_categories:
            raise ValueError(
                f"Categorias obrigatórias ausentes: {missing_categories}")

        # Validar scores básicos
        for category in required_categories:
            if 'score' not in v[category]:
                raise ValueError(f"Score ausente na categoria {category}")

        return v

    @field_validator('analise_visual')
    @classmethod
    def validate_visual_analysis(cls, v):
        """Valida estrutura da análise visual"""
        required_fields = ['screenshots', 'analise_ui_ux']

        if not isinstance(v, dict):
            raise ValueError("analise_visual deve ser um dicionário")

        missing_fields = [field for field in required_fields if field not in v]
        if missing_fields:
            raise ValueError(f"Campos obrigatórios ausentes: {missing_fields}")

        # Validar screenshots
        screenshots = v.get('screenshots', {})
        if not isinstance(screenshots, dict):
            raise ValueError("Screenshots devem ser um dicionário")

        return v

    @field_validator('relatorio_consolidado')
    @classmethod
    def validate_consolidated_report(cls, v):
        """Valida relatório consolidado"""
        if not isinstance(v, dict):
            raise ValueError("relatorio_consolidado deve ser um dicionário")

        if 'score_geral' not in v:
            raise ValueError(
                "score_geral é obrigatório no relatório consolidado")

        score = v.get('score_geral')
        if not isinstance(score, (int, float)) or not (0 <= score <= 100):
            raise ValueError("score_geral deve ser um número entre 0 e 100")

        return v
