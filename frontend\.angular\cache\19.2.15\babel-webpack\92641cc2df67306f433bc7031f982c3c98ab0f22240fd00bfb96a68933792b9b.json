{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component } from '@angular/core';\nimport { BaseIcon } from 'primeng/icons/baseicon';\nclass AngleDoubleRightIcon extends BaseIcon {\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵAngleDoubleRightIcon_BaseFactory;\n    return function AngleDoubleRightIcon_Factory(__ngFactoryType__) {\n      return (ɵAngleDoubleRightIcon_BaseFactory || (ɵAngleDoubleRightIcon_BaseFactory = i0.ɵɵgetInheritedFactory(AngleDoubleRightIcon)))(__ngFactoryType__ || AngleDoubleRightIcon);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: AngleDoubleRightIcon,\n    selectors: [[\"AngleDoubleRightIcon\"]],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    decls: 2,\n    vars: 5,\n    consts: [[\"width\", \"14\", \"height\", \"14\", \"viewBox\", \"0 0 14 14\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"fill-rule\", \"evenodd\", \"clip-rule\", \"evenodd\", \"d\", \"M7.68757 11.1451C7.7791 11.1831 7.8773 11.2024 7.9764 11.2019C8.07769 11.1985 8.17721 11.1745 8.26886 11.1312C8.36052 11.088 8.44238 11.0265 8.50943 10.9505L12.0294 7.49085C12.1707 7.34942 12.25 7.15771 12.25 6.95782C12.25 6.75794 12.1707 6.56622 12.0294 6.42479L8.50943 2.90479C8.37014 2.82159 8.20774 2.78551 8.04633 2.80192C7.88491 2.81833 7.73309 2.88635 7.6134 2.99588C7.4937 3.10541 7.41252 3.25061 7.38189 3.40994C7.35126 3.56927 7.37282 3.73423 7.44337 3.88033L10.4605 6.89748L7.44337 9.91463C7.30212 10.0561 7.22278 10.2478 7.22278 10.4477C7.22278 10.6475 7.30212 10.8393 7.44337 10.9807C7.51301 11.0512 7.59603 11.1071 7.68757 11.1451ZM1.94207 10.9505C2.07037 11.0968 2.25089 11.1871 2.44493 11.2019C2.63898 11.1871 2.81949 11.0968 2.94779 10.9505L6.46779 7.49085C6.60905 7.34942 6.68839 7.15771 6.68839 6.95782C6.68839 6.75793 6.60905 6.56622 6.46779 6.42479L2.94779 2.90479C2.80704 2.83757 2.6489 2.81563 2.49517 2.84201C2.34143 2.86839 2.19965 2.94178 2.08936 3.05207C1.97906 3.16237 1.90567 3.30415 1.8793 3.45788C1.85292 3.61162 1.87485 3.76975 1.94207 3.9105L4.95922 6.92765L1.94207 9.9448C1.81838 10.0831 1.75 10.2621 1.75 10.4477C1.75 10.6332 1.81838 10.8122 1.94207 10.9505Z\", \"fill\", \"currentColor\"]],\n    template: function AngleDoubleRightIcon_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(0, \"svg\", 0);\n        i0.ɵɵelement(1, \"path\", 1);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.getClassNames());\n        i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel)(\"aria-hidden\", ctx.ariaHidden)(\"role\", ctx.role);\n      }\n    },\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AngleDoubleRightIcon, [{\n    type: Component,\n    args: [{\n      selector: 'AngleDoubleRightIcon',\n      standalone: true,\n      template: `\n        <svg width=\"14\" height=\"14\" viewBox=\"0 0 14 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" [attr.aria-label]=\"ariaLabel\" [attr.aria-hidden]=\"ariaHidden\" [attr.role]=\"role\" [class]=\"getClassNames()\">\n            <path\n                fill-rule=\"evenodd\"\n                clip-rule=\"evenodd\"\n                d=\"M7.68757 11.1451C7.7791 11.1831 7.8773 11.2024 7.9764 11.2019C8.07769 11.1985 8.17721 11.1745 8.26886 11.1312C8.36052 11.088 8.44238 11.0265 8.50943 10.9505L12.0294 7.49085C12.1707 7.34942 12.25 7.15771 12.25 6.95782C12.25 6.75794 12.1707 6.56622 12.0294 6.42479L8.50943 2.90479C8.37014 2.82159 8.20774 2.78551 8.04633 2.80192C7.88491 2.81833 7.73309 2.88635 7.6134 2.99588C7.4937 3.10541 7.41252 3.25061 7.38189 3.40994C7.35126 3.56927 7.37282 3.73423 7.44337 3.88033L10.4605 6.89748L7.44337 9.91463C7.30212 10.0561 7.22278 10.2478 7.22278 10.4477C7.22278 10.6475 7.30212 10.8393 7.44337 10.9807C7.51301 11.0512 7.59603 11.1071 7.68757 11.1451ZM1.94207 10.9505C2.07037 11.0968 2.25089 11.1871 2.44493 11.2019C2.63898 11.1871 2.81949 11.0968 2.94779 10.9505L6.46779 7.49085C6.60905 7.34942 6.68839 7.15771 6.68839 6.95782C6.68839 6.75793 6.60905 6.56622 6.46779 6.42479L2.94779 2.90479C2.80704 2.83757 2.6489 2.81563 2.49517 2.84201C2.34143 2.86839 2.19965 2.94178 2.08936 3.05207C1.97906 3.16237 1.90567 3.30415 1.8793 3.45788C1.85292 3.61162 1.87485 3.76975 1.94207 3.9105L4.95922 6.92765L1.94207 9.9448C1.81838 10.0831 1.75 10.2621 1.75 10.4477C1.75 10.6332 1.81838 10.8122 1.94207 10.9505Z\"\n                fill=\"currentColor\"\n            />\n        </svg>\n    `\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AngleDoubleRightIcon };", "map": {"version": 3, "names": ["i0", "Component", "BaseIcon", "AngleDoubleRightIcon", "ɵfac", "ɵAngleDoubleRightIcon_BaseFactory", "AngleDoubleRightIcon_Factory", "__ngFactoryType__", "ɵɵgetInheritedFactory", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "AngleDoubleRightIcon_Template", "rf", "ctx", "ɵɵnamespaceSVG", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵclassMap", "getClassNames", "ɵɵattribute", "aria<PERSON><PERSON><PERSON>", "ariaHidden", "role", "encapsulation", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "standalone"], "sources": ["C:/projetos/scope-ai/frontend/node_modules/primeng/fesm2022/primeng-icons-angledoubleright.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component } from '@angular/core';\nimport { BaseIcon } from 'primeng/icons/baseicon';\n\nclass AngleDoubleRightIcon extends BaseIcon {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: Ang<PERSON>DoubleRightIcon, deps: null, target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.10\", type: AngleDoubleRightIcon, isStandalone: true, selector: \"AngleDoubleRightIcon\", usesInheritance: true, ngImport: i0, template: `\n        <svg width=\"14\" height=\"14\" viewBox=\"0 0 14 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" [attr.aria-label]=\"ariaLabel\" [attr.aria-hidden]=\"ariaHidden\" [attr.role]=\"role\" [class]=\"getClassNames()\">\n            <path\n                fill-rule=\"evenodd\"\n                clip-rule=\"evenodd\"\n                d=\"M7.68757 11.1451C7.7791 11.1831 7.8773 11.2024 7.9764 11.2019C8.07769 11.1985 8.17721 11.1745 8.26886 11.1312C8.36052 11.088 8.44238 11.0265 8.50943 10.9505L12.0294 7.49085C12.1707 7.34942 12.25 7.15771 12.25 6.95782C12.25 6.75794 12.1707 6.56622 12.0294 6.42479L8.50943 2.90479C8.37014 2.82159 8.20774 2.78551 8.04633 2.80192C7.88491 2.81833 7.73309 2.88635 7.6134 2.99588C7.4937 3.10541 7.41252 3.25061 7.38189 3.40994C7.35126 3.56927 7.37282 3.73423 7.44337 3.88033L10.4605 6.89748L7.44337 9.91463C7.30212 10.0561 7.22278 10.2478 7.22278 10.4477C7.22278 10.6475 7.30212 10.8393 7.44337 10.9807C7.51301 11.0512 7.59603 11.1071 7.68757 11.1451ZM1.94207 10.9505C2.07037 11.0968 2.25089 11.1871 2.44493 11.2019C2.63898 11.1871 2.81949 11.0968 2.94779 10.9505L6.46779 7.49085C6.60905 7.34942 6.68839 7.15771 6.68839 6.95782C6.68839 6.75793 6.60905 6.56622 6.46779 6.42479L2.94779 2.90479C2.80704 2.83757 2.6489 2.81563 2.49517 2.84201C2.34143 2.86839 2.19965 2.94178 2.08936 3.05207C1.97906 3.16237 1.90567 3.30415 1.8793 3.45788C1.85292 3.61162 1.87485 3.76975 1.94207 3.9105L4.95922 6.92765L1.94207 9.9448C1.81838 10.0831 1.75 10.2621 1.75 10.4477C1.75 10.6332 1.81838 10.8122 1.94207 10.9505Z\"\n                fill=\"currentColor\"\n            />\n        </svg>\n    `, isInline: true });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: AngleDoubleRightIcon, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'AngleDoubleRightIcon',\n                    standalone: true,\n                    template: `\n        <svg width=\"14\" height=\"14\" viewBox=\"0 0 14 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" [attr.aria-label]=\"ariaLabel\" [attr.aria-hidden]=\"ariaHidden\" [attr.role]=\"role\" [class]=\"getClassNames()\">\n            <path\n                fill-rule=\"evenodd\"\n                clip-rule=\"evenodd\"\n                d=\"M7.68757 11.1451C7.7791 11.1831 7.8773 11.2024 7.9764 11.2019C8.07769 11.1985 8.17721 11.1745 8.26886 11.1312C8.36052 11.088 8.44238 11.0265 8.50943 10.9505L12.0294 7.49085C12.1707 7.34942 12.25 7.15771 12.25 6.95782C12.25 6.75794 12.1707 6.56622 12.0294 6.42479L8.50943 2.90479C8.37014 2.82159 8.20774 2.78551 8.04633 2.80192C7.88491 2.81833 7.73309 2.88635 7.6134 2.99588C7.4937 3.10541 7.41252 3.25061 7.38189 3.40994C7.35126 3.56927 7.37282 3.73423 7.44337 3.88033L10.4605 6.89748L7.44337 9.91463C7.30212 10.0561 7.22278 10.2478 7.22278 10.4477C7.22278 10.6475 7.30212 10.8393 7.44337 10.9807C7.51301 11.0512 7.59603 11.1071 7.68757 11.1451ZM1.94207 10.9505C2.07037 11.0968 2.25089 11.1871 2.44493 11.2019C2.63898 11.1871 2.81949 11.0968 2.94779 10.9505L6.46779 7.49085C6.60905 7.34942 6.68839 7.15771 6.68839 6.95782C6.68839 6.75793 6.60905 6.56622 6.46779 6.42479L2.94779 2.90479C2.80704 2.83757 2.6489 2.81563 2.49517 2.84201C2.34143 2.86839 2.19965 2.94178 2.08936 3.05207C1.97906 3.16237 1.90567 3.30415 1.8793 3.45788C1.85292 3.61162 1.87485 3.76975 1.94207 3.9105L4.95922 6.92765L1.94207 9.9448C1.81838 10.0831 1.75 10.2621 1.75 10.4477C1.75 10.6332 1.81838 10.8122 1.94207 10.9505Z\"\n                fill=\"currentColor\"\n            />\n        </svg>\n    `\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AngleDoubleRightIcon };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,QAAQ,eAAe;AACzC,SAASC,QAAQ,QAAQ,wBAAwB;AAEjD,MAAMC,oBAAoB,SAASD,QAAQ,CAAC;EACxC,OAAOE,IAAI;IAAA,IAAAC,iCAAA;IAAA,gBAAAC,6BAAAC,iBAAA;MAAA,QAAAF,iCAAA,KAAAA,iCAAA,GAA+EL,EAAE,CAAAQ,qBAAA,CAAQL,oBAAoB,IAAAI,iBAAA,IAApBJ,oBAAoB;IAAA;EAAA;EACxH,OAAOM,IAAI,kBAD+ET,EAAE,CAAAU,iBAAA;IAAAC,IAAA,EACJR,oBAAoB;IAAAS,SAAA;IAAAC,QAAA,GADlBb,EAAE,CAAAc,0BAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFpB,EAAE,CAAAsB,cAAA;QAAFtB,EAAE,CAAAuB,cAAA,YAEiH,CAAC;QAFpHvB,EAAE,CAAAwB,SAAA,aAQnF,CAAC;QARgFxB,EAAE,CAAAyB,YAAA,CASnF,CAAC;MAAA;MAAA,IAAAL,EAAA;QATgFpB,EAAE,CAAA0B,UAAA,CAAAL,GAAA,CAAAM,aAAA,EAEgH,CAAC;QAFnH3B,EAAE,CAAA4B,WAAA,eAAAP,GAAA,CAAAQ,SAAA,iBAAAR,GAAA,CAAAS,UAAA,UAAAT,GAAA,CAAAU,IAAA;MAAA;IAAA;IAAAC,aAAA;EAAA;AAWhG;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAZ8FjC,EAAE,CAAAkC,iBAAA,CAYJ/B,oBAAoB,EAAc,CAAC;IACnHQ,IAAI,EAAEV,SAAS;IACfkC,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sBAAsB;MAChCC,UAAU,EAAE,IAAI;MAChBnB,QAAQ,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACgB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASf,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}