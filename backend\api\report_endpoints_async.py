"""
Endpoints para geração de relatórios em Markdown e PDF - Versão Async
Usando Motor para operações MongoDB assíncronas
"""
import logging
import asyncio
from datetime import datetime, UTC
from fastapi import APIRouter, HTTPException, status, BackgroundTasks
from fastapi.responses import Response, JSONResponse
from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorGridFSBucket
from concurrent.futures import ThreadPoolExecutor
import os
from typing import Optional, Dict, Any

# Importar geradores
from tools.reports.markdown_generator import get_markdown_report_generator
from tools.reports.markdown_to_pdf import MarkdownToPDFConverter
from tools.reports.html_report_generator import HTMLReportGenerator
from tools.reports.html_to_pdf_converter import HTMLToPDFConverter
from config.settings import env
from clients.async_db import motor_db, motor_clients_collection

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/reports", tags=["reports"])

# Thread pool for running sync operations
thread_pool = ThreadPoolExecutor(max_workers=4)


async def cleanup_thread_pool():
    """
    Cleanup function to be called on application shutdown
    """
    thread_pool.shutdown(wait=True)
    logger.info("Thread pool for report generation shut down")


async def get_client_by_id(client_id: str) -> Dict[str, Any]:
    """Helper function to get client by ID with validation"""
    try:
        client_obj_id = ObjectId(client_id)
    except:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="ID de cliente inválido"
        )
    
    client = await motor_clients_collection.find_one({"_id": client_obj_id})
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Cliente não encontrado"
        )
    
    return client


async def save_to_gridfs(content: bytes, filename: str, content_type: str, 
                        client_id: str, report_type: str) -> ObjectId:
    """Helper function to save content to GridFS asynchronously"""
    fs = AsyncIOMotorGridFSBucket(motor_db)
    
    file_id = await fs.upload_from_stream(
        filename,
        content,
        metadata={
            "content_type": content_type,
            "client_id": client_id,
            "generated_at": datetime.now(UTC),
            "report_type": report_type
        }
    )
    
    return file_id


async def read_from_gridfs(file_id: ObjectId) -> tuple[bytes, Dict[str, Any]]:
    """Helper function to read content from GridFS asynchronously"""
    fs = AsyncIOMotorGridFSBucket(motor_db)
    
    try:
        # Download file content
        grid_out = await fs.open_download_stream(file_id)
        content = await grid_out.read()
        
        # Get file metadata
        metadata = {
            "filename": grid_out.filename,
            "upload_date": grid_out.upload_date.isoformat() if grid_out.upload_date else None,
            "length": grid_out.length,
            "metadata": grid_out.metadata
        }
        
        return content, metadata
    except Exception as e:
        logger.error(f"Erro ao ler arquivo do GridFS: {e}")
        raise


@router.post("/clients/{client_id}/generate-markdown")
async def generate_markdown_report(client_id: str, background_tasks: BackgroundTasks):
    """
    Gera relatório executivo em Markdown usando processamento paralelo.
    
    Returns:
        JSON com o conteúdo Markdown e metadados
    """
    try:
        # Verificar se cliente existe
        client = await get_client_by_id(client_id)
        client_obj_id = client["_id"]
        client_name = client.get("name", "Cliente")
        
        logger.info(f"📝 Gerando relatório Markdown para {client_name}")
        
        # Verificar se tem dados suficientes
        reports = client.get("reports", [])
        has_dossier = any(r.get("reportType") == "dossie_expandido" for r in reports)
        
        if not has_dossier and not reports:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="Cliente não possui dados suficientes. Execute a análise completa primeiro."
            )
        
        # Gerar relatório em Markdown (já é async)
        generator = get_markdown_report_generator()
        result = await generator.generate_markdown_report(client_id)
        
        # Salvar markdown no banco (opcional)
        markdown_id = None
        if result.get("markdown_content"):
            markdown_id = await save_to_gridfs(
                content=result["markdown_content"].encode('utf-8'),
                filename=f"relatorio_markdown_{client_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md",
                content_type="text/markdown",
                client_id=str(client_obj_id),
                report_type="markdown_executive_parallel"
            )
            
            # Atualizar cliente com ID do markdown
            await motor_clients_collection.update_one(
                {"_id": client_obj_id},
                {
                    "$set": {
                        "markdown_report_id": markdown_id,
                        "markdown_generated_at": datetime.now(UTC),
                        "updated_at": datetime.now(UTC)
                    }
                }
            )
        
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "success": True,
                "client_id": client_id,
                "client_name": client_name,
                "markdown_id": str(markdown_id) if markdown_id else None,
                "markdown_content": result["markdown_content"],
                "metadata": result["metadata"],
                "message": f"Relatório Markdown (paralelo) gerado com sucesso para {client_name}"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Erro ao gerar relatório Markdown: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro ao gerar relatório: {str(e)}"
        )


@router.get("/clients/{client_id}/markdown")
async def get_markdown_report(client_id: str):
    """
    Busca relatório existente em Markdown
    
    Args:
        client_id: ID do cliente
    
    Returns:
        JSON com conteúdo Markdown e metadados
    """
    try:
        # Verificar se cliente existe
        client = await get_client_by_id(client_id)
        client_name = client.get("name", "Cliente")
        
        # Buscar markdown existente
        markdown_id = client.get("markdown_report_id")
        if not markdown_id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Nenhum relatório Markdown encontrado. Gere o relatório primeiro."
            )
        
        # Ler do GridFS
        content, file_metadata = await read_from_gridfs(markdown_id)
        markdown_content = content.decode('utf-8')
        
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "success": True,
                "client_id": client_id,
                "client_name": client_name,
                "markdown_content": markdown_content,
                "metadata": {
                    "generated_at": client.get("markdown_generated_at").isoformat() if client.get("markdown_generated_at") else None,
                    "file_info": file_metadata,
                    "report_type": "markdown_executive"
                },
                "message": f"Relatório Markdown encontrado para {client_name}"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Erro ao buscar relatório Markdown: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro ao buscar relatório: {str(e)}"
        )


@router.post("/clients/{client_id}/markdown-to-pdf")
async def convert_markdown_to_pdf(client_id: str, markdown_content: Optional[str] = None):
    """
    Converte relatório Markdown para PDF
    
    Args:
        client_id: ID do cliente
        markdown_content: Conteúdo Markdown (opcional, busca do banco se não fornecido)
    
    Returns:
        PDF file response
    """
    try:
        # Verificar se cliente existe
        client = await get_client_by_id(client_id)
        client_obj_id = client["_id"]
        client_name = client.get("name", "Cliente")
        
        # Se não foi fornecido markdown_content, buscar do banco
        if not markdown_content:
            markdown_id = client.get("markdown_report_id")
            if not markdown_id:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Nenhum relatório Markdown encontrado. Gere o Markdown primeiro."
                )
            
            # Ler do GridFS
            content, _ = await read_from_gridfs(markdown_id)
            markdown_content = content.decode('utf-8')
        
        logger.info(f"🔄 Convertendo Markdown para PDF - {client_name}")
        
        # Converter para PDF usando MarkdownToPDFConverter em thread pool
        converter = MarkdownToPDFConverter()
        metadata = {
            "client_id": client_id,
            "client_name": client_name,
            "generated_at": datetime.now(UTC).isoformat()
        }
        
        # Run sync converter in thread pool
        loop = asyncio.get_event_loop()
        pdf_bytes = await loop.run_in_executor(
            thread_pool,
            converter.convert_to_pdf,
            markdown_content,
            metadata
        )
        
        # Salvar PDF no GridFS
        pdf_id = await save_to_gridfs(
            content=pdf_bytes,
            filename=f"relatorio_executivo_md_{client_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
            content_type="application/pdf",
            client_id=str(client_obj_id),
            report_type="pdf_from_markdown"
        )
        
        # Atualizar cliente
        await motor_clients_collection.update_one(
            {"_id": client_obj_id},
            {
                "$set": {
                    "markdown_pdf_id": pdf_id,
                    "markdown_pdf_generated_at": datetime.now(UTC),
                    "pdf_report_available": True,
                    "updated_at": datetime.now(UTC)
                }
            }
        )
        
        # Retornar PDF
        return Response(
            content=pdf_bytes,
            media_type="application/pdf",
            headers={
                "Content-Disposition": f"attachment; filename=relatorio_executivo_{client_name.replace(' ', '_')}.pdf"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Erro ao converter Markdown para PDF: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro ao converter para PDF: {str(e)}"
        )


@router.post("/clients/{client_id}/generate-complete-report")
async def generate_complete_report(client_id: str, background_tasks: BackgroundTasks):
    """
    Gera relatório completo: HTML (via IA) -> PDF (via WeasyPrint)
    
    Returns:
        PDF file response
    """
    try:
        # Verificar se cliente existe
        client = await get_client_by_id(client_id)
        client_obj_id = client["_id"]
        client_name = client.get("name", "Cliente")
        
        logger.info(f"🚀 Gerando relatório completo para {client_name}")
        
        # Verificar se tem dados suficientes
        reports = client.get("reports", [])
        has_dossier = any(r.get("reportType") == "dossie_expandido" for r in reports)
        
        if not has_dossier and not reports:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="Cliente não possui dados suficientes. Execute a análise completa primeiro."
            )
        
        # 1. Gerar HTML direto em thread pool (sync method)
        logger.info(f"📝 Etapa 1/2: Gerando relatório HTML estruturado")
        html_generator = HTMLReportGenerator()
        
        # Run sync HTML generation in thread pool
        loop = asyncio.get_event_loop()
        html_result = await loop.run_in_executor(
            thread_pool,
            html_generator.generate_html_report,
            client_id
        )
        
        # 2. Converter HTML para PDF em thread pool
        logger.info(f"📄 Etapa 2/2: Convertendo HTML para PDF")
        pdf_converter = HTMLToPDFConverter()
        metadata = html_result["metadata"]
        
        # Run sync PDF conversion in thread pool
        pdf_bytes = await loop.run_in_executor(
            thread_pool,
            pdf_converter.convert_to_pdf,
            html_result["html_content"],
            metadata
        )
        
        # Salvar ambos no GridFS
        # Salvar HTML
        html_id = await save_to_gridfs(
            content=html_result["html_content"].encode('utf-8'),
            filename=f"relatorio_html_{client_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html",
            content_type="text/html",
            client_id=str(client_obj_id),
            report_type="html_executive"
        )
        
        # Salvar PDF
        pdf_id = await save_to_gridfs(
            content=pdf_bytes,
            filename=f"relatorio_executivo_html_{client_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
            content_type="application/pdf",
            client_id=str(client_obj_id),
            report_type="pdf_from_html"
        )
        
        # Atualizar cliente
        await motor_clients_collection.update_one(
            {"_id": client_obj_id},
            {
                "$set": {
                    "html_report_id": html_id,
                    "html_pdf_id": pdf_id,
                    "html_report_generated_at": datetime.now(UTC),
                    "pdf_report_available": True,
                    "updated_at": datetime.now(UTC)
                }
            }
        )
        
        logger.info(f"✅ Relatório HTML completo gerado com sucesso para {client_name}")
        
        # Retornar PDF
        return Response(
            content=pdf_bytes,
            media_type="application/pdf",
            headers={
                "Content-Disposition": f"attachment; filename=relatorio_executivo_{client_name.replace(' ', '_')}.pdf"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Erro ao gerar relatório completo: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro ao gerar relatório: {str(e)}"
        )


@router.post("/clients/{client_id}/generate-html-report")
async def generate_html_report_endpoint(client_id: str):
    """Gera e salva um relatório HTML para um cliente."""
    try:
        # Verificar se cliente existe
        client = await get_client_by_id(client_id)
        client_obj_id = client["_id"]
        
        generator = HTMLReportGenerator(motor_db)
        
        # Run sync method in thread pool
        loop = asyncio.get_event_loop()
        report_html = await loop.run_in_executor(
            thread_pool,
            generator.generate_report,
            client
        )
        
        # Salvar o relatório HTML no GridFS com collection específica
        fs = AsyncIOMotorGridFSBucket(motor_db, bucket_name="html_reports")
        
        report_id = await fs.upload_from_stream(
            f"relatorio_html_{client['name']}_{datetime.now(UTC).strftime('%Y%m%d')}.html",
            report_html.encode('utf-8'),
            metadata={
                "client_id": client_id,
                "content_type": "text/html",
                "generated_at": datetime.now(UTC)
            }
        )
        
        # Atualizar o cliente com o ID do relatório HTML
        await motor_clients_collection.update_one(
            {"_id": client_obj_id},
            {
                "$set": {
                    "html_report_id": report_id,
                    "updated_at": datetime.now(UTC)
                }
            }
        )
        
        return JSONResponse(
            content={
                "message": "Relatório HTML gerado e salvo com sucesso.",
                "report_id": str(report_id)
            },
            status_code=200
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao gerar relatório HTML: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/clients/{client_id}/html-to-pdf")
async def convert_html_to_pdf_endpoint(client_id: str):
    """Converte o relatório HTML mais recente de um cliente para PDF e salva."""
    try:
        # Verificar se cliente existe
        client = await get_client_by_id(client_id)
        client_obj_id = client["_id"]
        
        html_report_id = client.get("html_report_id")
        if not html_report_id:
            raise HTTPException(
                status_code=404,
                detail="Nenhum relatório HTML encontrado para este cliente."
            )
        
        # Ler HTML do GridFS
        fs_html = AsyncIOMotorGridFSBucket(motor_db, bucket_name="html_reports")
        grid_out = await fs_html.open_download_stream(html_report_id)
        html_content = await grid_out.read()
        html_content = html_content.decode('utf-8')
        
        converter = HTMLToPDFConverter()
        
        # Run sync converter in thread pool
        loop = asyncio.get_event_loop()
        pdf_bytes = await loop.run_in_executor(
            thread_pool,
            converter.convert,
            html_content
        )
        
        # Salvar o PDF no GridFS
        fs_pdf = AsyncIOMotorGridFSBucket(motor_db, bucket_name="pdfs")
        pdf_filename = f"relatorio_pdf_{client['name']}_{datetime.now(UTC).strftime('%Y%m%d')}.pdf"
        
        pdf_id = await fs_pdf.upload_from_stream(
            pdf_filename,
            pdf_bytes,
            metadata={
                "content_type": "application/pdf",
                "client_id": client_id,
                "generated_at": datetime.now(UTC)
            }
        )
        
        # Atualizar o cliente com o ID do novo PDF
        await motor_clients_collection.update_one(
            {"_id": client_obj_id},
            {
                "$set": {
                    "pdf_report_id": pdf_id,
                    "pdf_generated_at": datetime.now(UTC)
                }
            }
        )
        
        return JSONResponse(
            content={
                "message": "Relatório PDF gerado com sucesso a partir do HTML.",
                "pdf_id": str(pdf_id)
            },
            status_code=200
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao converter HTML para PDF: {e}")
        raise HTTPException(status_code=500, detail=str(e))