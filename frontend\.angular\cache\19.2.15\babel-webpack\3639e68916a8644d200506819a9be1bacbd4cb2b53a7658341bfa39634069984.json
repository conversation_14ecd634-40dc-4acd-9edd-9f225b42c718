{"ast": null, "code": "import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, inject, Directive, contentChild, computed, booleanAttribute, Input, EventEmitter, numberAttribute, ContentChildren, ContentChild, Output, ViewEncapsulation, ChangeDetectionStrategy, Component, NgModule } from '@angular/core';\nimport { addClass, isEmpty, findSingle } from '@primeuix/utils';\nimport { SharedModule, PrimeTemplate } from 'primeng/api';\nimport { AutoFocus } from 'primeng/autofocus';\nimport * as i2 from 'primeng/badge';\nimport { BadgeModule } from 'primeng/badge';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { SpinnerIcon } from 'primeng/icons';\nimport { Ripple } from 'primeng/ripple';\nimport { BaseStyle } from 'primeng/base';\nconst _c0 = [\"content\"];\nconst _c1 = [\"loadingicon\"];\nconst _c2 = [\"icon\"];\nconst _c3 = [\"*\"];\nconst _c4 = a0 => ({\n  class: a0\n});\nfunction Button_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Button_ng_container_3_ng_container_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.iconClass());\n    i0.ɵɵattribute(\"aria-hidden\", true)(\"data-pc-section\", \"loadingicon\");\n  }\n}\nfunction Button_ng_container_3_ng_container_1_SpinnerIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"SpinnerIcon\", 9);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"styleClass\", ctx_r0.spinnerIconClass())(\"spin\", true);\n    i0.ɵɵattribute(\"aria-hidden\", true)(\"data-pc-section\", \"loadingicon\");\n  }\n}\nfunction Button_ng_container_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Button_ng_container_3_ng_container_1_span_1_Template, 1, 3, \"span\", 6)(2, Button_ng_container_3_ng_container_1_SpinnerIcon_2_Template, 1, 4, \"SpinnerIcon\", 7);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.loadingIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.loadingIcon);\n  }\n}\nfunction Button_ng_container_3_2_ng_template_0_Template(rf, ctx) {}\nfunction Button_ng_container_3_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Button_ng_container_3_2_ng_template_0_Template, 0, 0, \"ng-template\", 10);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.loadingIconTemplate || ctx_r0._loadingIconTemplate);\n  }\n}\nfunction Button_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Button_ng_container_3_ng_container_1_Template, 3, 2, \"ng-container\", 2)(2, Button_ng_container_3_2_Template, 1, 1, null, 5);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.loadingIconTemplate && !ctx_r0._loadingIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.loadingIconTemplate || ctx_r0._loadingIconTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(3, _c4, ctx_r0.iconClass()));\n  }\n}\nfunction Button_ng_container_4_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r0.icon);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.iconClass());\n    i0.ɵɵattribute(\"data-pc-section\", \"icon\");\n  }\n}\nfunction Button_ng_container_4_2_ng_template_0_Template(rf, ctx) {}\nfunction Button_ng_container_4_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Button_ng_container_4_2_ng_template_0_Template, 0, 0, \"ng-template\", 10);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.icon && (ctx_r0.iconTemplate || ctx_r0._iconTemplate));\n  }\n}\nfunction Button_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Button_ng_container_4_span_1_Template, 1, 4, \"span\", 11)(2, Button_ng_container_4_2_Template, 1, 1, null, 5);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.icon && !ctx_r0.iconTemplate && !ctx_r0._iconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.iconTemplate || ctx_r0._iconTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(3, _c4, ctx_r0.iconClass()));\n  }\n}\nfunction Button_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"aria-hidden\", ctx_r0.icon && !ctx_r0.label)(\"data-pc-section\", \"label\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.label);\n  }\n}\nfunction Button_p_badge_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-badge\", 13);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", ctx_r0.badge)(\"severity\", ctx_r0.badgeSeverity);\n  }\n}\nconst theme = ({\n  dt\n}) => `\n.p-button {\n    display: inline-flex;\n    cursor: pointer;\n    user-select: none;\n    align-items: center;\n    justify-content: center;\n    overflow: hidden;\n    position: relative;\n    color: ${dt('button.primary.color')};\n    background: ${dt('button.primary.background')};\n    border: 1px solid ${dt('button.primary.border.color')};\n    padding-block: ${dt('button.padding.y')};\n    padding-inline: ${dt('button.padding.x')};\n    font-size: 1rem;\n    font-family: inherit;\n    font-feature-settings: inherit;\n    transition: background ${dt('button.transition.duration')}, color ${dt('button.transition.duration')}, border-color ${dt('button.transition.duration')},\n            outline-color ${dt('button.transition.duration')}, box-shadow ${dt('button.transition.duration')};\n    border-radius: ${dt('button.border.radius')};\n    outline-color: transparent;\n    gap: ${dt('button.gap')};\n}\n\n.p-button-icon,\n.p-button-icon:before,\n.p-button-icon:after {\n    line-height: inherit;\n}\n\n.p-button:disabled {\n    cursor: default;\n}\n\n.p-button-icon-right {\n    order: 1;\n}\n\n.p-button-icon-right:dir(rtl) {\n    order: -1;\n}\n\n.p-button:not(.p-button-vertical) .p-button-icon:not(.p-button-icon-right):dir(rtl) {\n    order: 1;\n}\n\n.p-button-icon-bottom {\n    order: 2;\n}\n\n.p-button-icon-only {\n    width: ${dt('button.icon.only.width')};\n    padding-inline-start: 0;\n    padding-inline-end: 0;\n    gap: 0;\n}\n\n.p-button-icon-only.p-button-rounded {\n    border-radius: 50%;\n    height: ${dt('button.icon.only.width')};\n}\n\n.p-button-icon-only .p-button-label {\n    visibility: hidden;\n    width: 0;\n}\n\n.p-button-sm {\n    font-size: ${dt('button.sm.font.size')};\n    padding-block: ${dt('button.sm.padding.y')};\n    padding-inline: ${dt('button.sm.padding.x')};\n}\n\n.p-button-sm .p-button-icon {\n    font-size: ${dt('button.sm.font.size')};\n}\n\n.p-button-sm.p-button-icon-only {\n    width: ${dt('button.sm.icon.only.width')};\n}\n\n.p-button-sm.p-button-icon-only.p-button-rounded {\n    height: ${dt('button.sm.icon.only.width')};\n}\n\n.p-button-lg {\n    font-size: ${dt('button.lg.font.size')};\n    padding-block: ${dt('button.lg.padding.y')};\n    padding-inline: ${dt('button.lg.padding.x')};\n}\n\n.p-button-lg .p-button-icon {\n    font-size: ${dt('button.lg.font.size')};\n}\n\n.p-button-lg.p-button-icon-only {\n    width: ${dt('button.lg.icon.only.width')};\n}\n\n.p-button-lg.p-button-icon-only.p-button-rounded {\n    height: ${dt('button.lg.icon.only.width')};\n}\n\n.p-button-vertical {\n    flex-direction: column;\n}\n\n.p-button-label {\n    font-weight: ${dt('button.label.font.weight')};\n}\n\n.p-button-fluid {\n    width: 100%;\n}\n\n.p-button-fluid.p-button-icon-only {\n    width: ${dt('button.icon.only.width')};\n}\n\n.p-button:not(:disabled):hover {\n    background: ${dt('button.primary.hover.background')};\n    border: 1px solid ${dt('button.primary.hover.border.color')};\n    color: ${dt('button.primary.hover.color')};\n}\n\n.p-button:not(:disabled):active {\n    background: ${dt('button.primary.active.background')};\n    border: 1px solid ${dt('button.primary.active.border.color')};\n    color: ${dt('button.primary.active.color')};\n}\n\n.p-button:focus-visible {\n    box-shadow: ${dt('button.primary.focus.ring.shadow')};\n    outline: ${dt('button.focus.ring.width')} ${dt('button.focus.ring.style')} ${dt('button.primary.focus.ring.color')};\n    outline-offset: ${dt('button.focus.ring.offset')};\n}\n\n.p-button .p-badge {\n    min-width: ${dt('button.badge.size')};\n    height: ${dt('button.badge.size')};\n    line-height: ${dt('button.badge.size')};\n}\n\n.p-button-raised {\n    box-shadow: ${dt('button.raised.shadow')};\n}\n\n.p-button-rounded {\n    border-radius: ${dt('button.rounded.border.radius')};\n}\n\n.p-button-secondary {\n    background: ${dt('button.secondary.background')};\n    border: 1px solid ${dt('button.secondary.border.color')};\n    color: ${dt('button.secondary.color')};\n}\n\n.p-button-secondary:not(:disabled):hover {\n    background: ${dt('button.secondary.hover.background')};\n    border: 1px solid ${dt('button.secondary.hover.border.color')};\n    color: ${dt('button.secondary.hover.color')};\n}\n\n.p-button-secondary:not(:disabled):active {\n    background: ${dt('button.secondary.active.background')};\n    border: 1px solid ${dt('button.secondary.active.border.color')};\n    color: ${dt('button.secondary.active.color')};\n}\n\n.p-button-secondary:focus-visible {\n    outline-color: ${dt('button.secondary.focus.ring.color')};\n    box-shadow: ${dt('button.secondary.focus.ring.shadow')};\n}\n\n.p-button-success {\n    background: ${dt('button.success.background')};\n    border: 1px solid ${dt('button.success.border.color')};\n    color: ${dt('button.success.color')};\n}\n\n.p-button-success:not(:disabled):hover {\n    background: ${dt('button.success.hover.background')};\n    border: 1px solid ${dt('button.success.hover.border.color')};\n    color: ${dt('button.success.hover.color')};\n}\n\n.p-button-success:not(:disabled):active {\n    background: ${dt('button.success.active.background')};\n    border: 1px solid ${dt('button.success.active.border.color')};\n    color: ${dt('button.success.active.color')};\n}\n\n.p-button-success:focus-visible {\n    outline-color: ${dt('button.success.focus.ring.color')};\n    box-shadow: ${dt('button.success.focus.ring.shadow')};\n}\n\n.p-button-info {\n    background: ${dt('button.info.background')};\n    border: 1px solid ${dt('button.info.border.color')};\n    color: ${dt('button.info.color')};\n}\n\n.p-button-info:not(:disabled):hover {\n    background: ${dt('button.info.hover.background')};\n    border: 1px solid ${dt('button.info.hover.border.color')};\n    color: ${dt('button.info.hover.color')};\n}\n\n.p-button-info:not(:disabled):active {\n    background: ${dt('button.info.active.background')};\n    border: 1px solid ${dt('button.info.active.border.color')};\n    color: ${dt('button.info.active.color')};\n}\n\n.p-button-info:focus-visible {\n    outline-color: ${dt('button.info.focus.ring.color')};\n    box-shadow: ${dt('button.info.focus.ring.shadow')};\n}\n\n.p-button-warn {\n    background: ${dt('button.warn.background')};\n    border: 1px solid ${dt('button.warn.border.color')};\n    color: ${dt('button.warn.color')};\n}\n\n.p-button-warn:not(:disabled):hover {\n    background: ${dt('button.warn.hover.background')};\n    border: 1px solid ${dt('button.warn.hover.border.color')};\n    color: ${dt('button.warn.hover.color')};\n}\n\n.p-button-warn:not(:disabled):active {\n    background: ${dt('button.warn.active.background')};\n    border: 1px solid ${dt('button.warn.active.border.color')};\n    color: ${dt('button.warn.active.color')};\n}\n\n.p-button-warn:focus-visible {\n    outline-color: ${dt('button.warn.focus.ring.color')};\n    box-shadow: ${dt('button.warn.focus.ring.shadow')};\n}\n\n.p-button-help {\n    background: ${dt('button.help.background')};\n    border: 1px solid ${dt('button.help.border.color')};\n    color: ${dt('button.help.color')};\n}\n\n.p-button-help:not(:disabled):hover {\n    background: ${dt('button.help.hover.background')};\n    border: 1px solid ${dt('button.help.hover.border.color')};\n    color: ${dt('button.help.hover.color')};\n}\n\n.p-button-help:not(:disabled):active {\n    background: ${dt('button.help.active.background')};\n    border: 1px solid ${dt('button.help.active.border.color')};\n    color: ${dt('button.help.active.color')};\n}\n\n.p-button-help:focus-visible {\n    outline-color: ${dt('button.help.focus.ring.color')};\n    box-shadow: ${dt('button.help.focus.ring.shadow')};\n}\n\n.p-button-danger {\n    background: ${dt('button.danger.background')};\n    border: 1px solid ${dt('button.danger.border.color')};\n    color: ${dt('button.danger.color')};\n}\n\n.p-button-danger:not(:disabled):hover {\n    background: ${dt('button.danger.hover.background')};\n    border: 1px solid ${dt('button.danger.hover.border.color')};\n    color: ${dt('button.danger.hover.color')};\n}\n\n.p-button-danger:not(:disabled):active {\n    background: ${dt('button.danger.active.background')};\n    border: 1px solid ${dt('button.danger.active.border.color')};\n    color: ${dt('button.danger.active.color')};\n}\n\n.p-button-danger:focus-visible {\n    outline-color: ${dt('button.danger.focus.ring.color')};\n    box-shadow: ${dt('button.danger.focus.ring.shadow')};\n}\n\n.p-button-contrast {\n    background: ${dt('button.contrast.background')};\n    border: 1px solid ${dt('button.contrast.border.color')};\n    color: ${dt('button.contrast.color')};\n}\n\n.p-button-contrast:not(:disabled):hover {\n    background: ${dt('button.contrast.hover.background')};\n    border: 1px solid ${dt('button.contrast.hover.border.color')};\n    color: ${dt('button.contrast.hover.color')};\n}\n\n.p-button-contrast:not(:disabled):active {\n    background: ${dt('button.contrast.active.background')};\n    border: 1px solid ${dt('button.contrast.active.border.color')};\n    color: ${dt('button.contrast.active.color')};\n}\n\n.p-button-contrast:focus-visible {\n    outline-color: ${dt('button.contrast.focus.ring.color')};\n    box-shadow: ${dt('button.contrast.focus.ring.shadow')};\n}\n\n.p-button-outlined {\n    background: transparent;\n    border-color: ${dt('button.outlined.primary.border.color')};\n    color: ${dt('button.outlined.primary.color')};\n}\n\n.p-button-outlined:not(:disabled):hover {\n    background: ${dt('button.outlined.primary.hover.background')};\n    border-color: ${dt('button.outlined.primary.border.color')};\n    color: ${dt('button.outlined.primary.color')};\n}\n\n.p-button-outlined:not(:disabled):active {\n    background: ${dt('button.outlined.primary.active.background')};\n    border-color: ${dt('button.outlined.primary.border.color')};\n    color: ${dt('button.outlined.primary.color')};\n}\n\n.p-button-outlined.p-button-secondary {\n    border-color: ${dt('button.outlined.secondary.border.color')};\n    color: ${dt('button.outlined.secondary.color')};\n}\n\n.p-button-outlined.p-button-secondary:not(:disabled):hover {\n    background: ${dt('button.outlined.secondary.hover.background')};\n    border-color: ${dt('button.outlined.secondary.border.color')};\n    color: ${dt('button.outlined.secondary.color')};\n}\n\n.p-button-outlined.p-button-secondary:not(:disabled):active {\n    background: ${dt('button.outlined.secondary.active.background')};\n    border-color: ${dt('button.outlined.secondary.border.color')};\n    color: ${dt('button.outlined.secondary.color')};\n}\n\n.p-button-outlined.p-button-success {\n    border-color: ${dt('button.outlined.success.border.color')};\n    color: ${dt('button.outlined.success.color')};\n}\n\n.p-button-outlined.p-button-success:not(:disabled):hover {\n    background: ${dt('button.outlined.success.hover.background')};\n    border-color: ${dt('button.outlined.success.border.color')};\n    color: ${dt('button.outlined.success.color')};\n}\n\n.p-button-outlined.p-button-success:not(:disabled):active {\n    background: ${dt('button.outlined.success.active.background')};\n    border-color: ${dt('button.outlined.success.border.color')};\n    color: ${dt('button.outlined.success.color')};\n}\n\n.p-button-outlined.p-button-info {\n    border-color: ${dt('button.outlined.info.border.color')};\n    color: ${dt('button.outlined.info.color')};\n}\n\n.p-button-outlined.p-button-info:not(:disabled):hover {\n    background: ${dt('button.outlined.info.hover.background')};\n    border-color: ${dt('button.outlined.info.border.color')};\n    color: ${dt('button.outlined.info.color')};\n}\n\n.p-button-outlined.p-button-info:not(:disabled):active {\n    background: ${dt('button.outlined.info.active.background')};\n    border-color: ${dt('button.outlined.info.border.color')};\n    color: ${dt('button.outlined.info.color')};\n}\n\n.p-button-outlined.p-button-warn {\n    border-color: ${dt('button.outlined.warn.border.color')};\n    color: ${dt('button.outlined.warn.color')};\n}\n\n.p-button-outlined.p-button-warn:not(:disabled):hover {\n    background: ${dt('button.outlined.warn.hover.background')};\n    border-color: ${dt('button.outlined.warn.border.color')};\n    color: ${dt('button.outlined.warn.color')};\n}\n\n.p-button-outlined.p-button-warn:not(:disabled):active {\n    background: ${dt('button.outlined.warn.active.background')};\n    border-color: ${dt('button.outlined.warn.border.color')};\n    color: ${dt('button.outlined.warn.color')};\n}\n\n.p-button-outlined.p-button-help {\n    border-color: ${dt('button.outlined.help.border.color')};\n    color: ${dt('button.outlined.help.color')};\n}\n\n.p-button-outlined.p-button-help:not(:disabled):hover {\n    background: ${dt('button.outlined.help.hover.background')};\n    border-color: ${dt('button.outlined.help.border.color')};\n    color: ${dt('button.outlined.help.color')};\n}\n\n.p-button-outlined.p-button-help:not(:disabled):active {\n    background: ${dt('button.outlined.help.active.background')};\n    border-color: ${dt('button.outlined.help.border.color')};\n    color: ${dt('button.outlined.help.color')};\n}\n\n.p-button-outlined.p-button-danger {\n    border-color: ${dt('button.outlined.danger.border.color')};\n    color: ${dt('button.outlined.danger.color')};\n}\n\n.p-button-outlined.p-button-danger:not(:disabled):hover {\n    background: ${dt('button.outlined.danger.hover.background')};\n    border-color: ${dt('button.outlined.danger.border.color')};\n    color: ${dt('button.outlined.danger.color')};\n}\n\n.p-button-outlined.p-button-danger:not(:disabled):active {\n    background: ${dt('button.outlined.danger.active.background')};\n    border-color: ${dt('button.outlined.danger.border.color')};\n    color: ${dt('button.outlined.danger.color')};\n}\n\n.p-button-outlined.p-button-contrast {\n    border-color: ${dt('button.outlined.contrast.border.color')};\n    color: ${dt('button.outlined.contrast.color')};\n}\n\n.p-button-outlined.p-button-contrast:not(:disabled):hover {\n    background: ${dt('button.outlined.contrast.hover.background')};\n    border-color: ${dt('button.outlined.contrast.border.color')};\n    color: ${dt('button.outlined.contrast.color')};\n}\n\n.p-button-outlined.p-button-contrast:not(:disabled):active {\n    background: ${dt('button.outlined.contrast.active.background')};\n    border-color: ${dt('button.outlined.contrast.border.color')};\n    color: ${dt('button.outlined.contrast.color')};\n}\n\n.p-button-outlined.p-button-plain {\n    border-color: ${dt('button.outlined.plain.border.color')};\n    color: ${dt('button.outlined.plain.color')};\n}\n\n.p-button-outlined.p-button-plain:not(:disabled):hover {\n    background: ${dt('button.outlined.plain.hover.background')};\n    border-color: ${dt('button.outlined.plain.border.color')};\n    color: ${dt('button.outlined.plain.color')};\n}\n\n.p-button-outlined.p-button-plain:not(:disabled):active {\n    background: ${dt('button.outlined.plain.active.background')};\n    border-color: ${dt('button.outlined.plain.border.color')};\n    color: ${dt('button.outlined.plain.color')};\n}\n\n.p-button-text {\n    background: transparent;\n    border-color: transparent;\n    color: ${dt('button.text.primary.color')};\n}\n\n.p-button-text:not(:disabled):hover {\n    background: ${dt('button.text.primary.hover.background')};\n    border-color: transparent;\n    color: ${dt('button.text.primary.color')};\n}\n\n.p-button-text:not(:disabled):active {\n    background: ${dt('button.text.primary.active.background')};\n    border-color: transparent;\n    color: ${dt('button.text.primary.color')};\n}\n\n.p-button-text.p-button-secondary {\n    background: transparent;\n    border-color: transparent;\n    color: ${dt('button.text.secondary.color')};\n}\n\n.p-button-text.p-button-secondary:not(:disabled):hover {\n    background: ${dt('button.text.secondary.hover.background')};\n    border-color: transparent;\n    color: ${dt('button.text.secondary.color')};\n}\n\n.p-button-text.p-button-secondary:not(:disabled):active {\n    background: ${dt('button.text.secondary.active.background')};\n    border-color: transparent;\n    color: ${dt('button.text.secondary.color')};\n}\n\n.p-button-text.p-button-success {\n    background: transparent;\n    border-color: transparent;\n    color: ${dt('button.text.success.color')};\n}\n\n.p-button-text.p-button-success:not(:disabled):hover {\n    background: ${dt('button.text.success.hover.background')};\n    border-color: transparent;\n    color: ${dt('button.text.success.color')};\n}\n\n.p-button-text.p-button-success:not(:disabled):active {\n    background: ${dt('button.text.success.active.background')};\n    border-color: transparent;\n    color: ${dt('button.text.success.color')};\n}\n\n.p-button-text.p-button-info {\n    background: transparent;\n    border-color: transparent;\n    color: ${dt('button.text.info.color')};\n}\n\n.p-button-text.p-button-info:not(:disabled):hover {\n    background: ${dt('button.text.info.hover.background')};\n    border-color: transparent;\n    color: ${dt('button.text.info.color')};\n}\n\n.p-button-text.p-button-info:not(:disabled):active {\n    background: ${dt('button.text.info.active.background')};\n    border-color: transparent;\n    color: ${dt('button.text.info.color')};\n}\n\n.p-button-text.p-button-warn {\n    background: transparent;\n    border-color: transparent;\n    color: ${dt('button.text.warn.color')};\n}\n\n.p-button-text.p-button-warn:not(:disabled):hover {\n    background: ${dt('button.text.warn.hover.background')};\n    border-color: transparent;\n    color: ${dt('button.text.warn.color')};\n}\n\n.p-button-text.p-button-warn:not(:disabled):active {\n    background: ${dt('button.text.warn.active.background')};\n    border-color: transparent;\n    color: ${dt('button.text.warn.color')};\n}\n\n.p-button-text.p-button-help {\n    background: transparent;\n    border-color: transparent;\n    color: ${dt('button.text.help.color')};\n}\n\n.p-button-text.p-button-help:not(:disabled):hover {\n    background: ${dt('button.text.help.hover.background')};\n    border-color: transparent;\n    color: ${dt('button.text.help.color')};\n}\n\n.p-button-text.p-button-help:not(:disabled):active {\n    background: ${dt('button.text.help.active.background')};\n    border-color: transparent;\n    color: ${dt('button.text.help.color')};\n}\n\n.p-button-text.p-button-danger {\n    background: transparent;\n    border-color: transparent;\n    color: ${dt('button.text.danger.color')};\n}\n\n.p-button-text.p-button-danger:not(:disabled):hover {\n    background: ${dt('button.text.danger.hover.background')};\n    border-color: transparent;\n    color: ${dt('button.text.danger.color')};\n}\n\n.p-button-text.p-button-danger:not(:disabled):active {\n    background: ${dt('button.text.danger.active.background')};\n    border-color: transparent;\n    color: ${dt('button.text.danger.color')};\n}\n\n.p-button-text.p-button-plain {\n    background: transparent;\n    border-color: transparent;\n    color: ${dt('button.text.plain.color')};\n}\n\n.p-button-text.p-button-plain:not(:disabled):hover {\n    background: ${dt('button.text.plain.hover.background')};\n    border-color: transparent;\n    color: ${dt('button.text.plain.color')};\n}\n\n.p-button-text.p-button-plain:not(:disabled):active {\n    background: ${dt('button.text.plain.active.background')};\n    border-color: transparent;\n    color: ${dt('button.text.plain.color')};\n}\n\n.p-button-text.p-button-contrast {\n    background: transparent;\n    border-color: transparent;\n    color: ${dt('button.text.contrast.color')};\n}\n\n.p-button-text.p-button-contrast:not(:disabled):hover {\n    background: ${dt('button.text.contrast.hover.background')};\n    border-color: transparent;\n    color: ${dt('button.text.contrast.color')};\n}\n\n.p-button-text.p-button-contrast:not(:disabled):active {\n    background: ${dt('button.text.contrast.active.background')};\n    border-color: transparent;\n    color: ${dt('button.text.contrast.color')};\n}\n\n.p-button-link {\n    background: transparent;\n    border-color: transparent;\n    color: ${dt('button.link.color')};\n}\n\n.p-button-link:not(:disabled):hover {\n    background: transparent;\n    border-color: transparent;\n    color: ${dt('button.link.hover.color')};\n}\n\n.p-button-link:not(:disabled):hover .p-button-label {\n    text-decoration: underline;\n}\n\n.p-button-link:not(:disabled):active {\n    background: transparent;\n    border-color: transparent;\n    color: ${dt('button.link.active.color')};\n}\n\n/* For PrimeNG */\n.p-button-icon-right {\n    order: 1;\n}\n\np-button[iconpos='right'] spinnericon {\n    order: 1;\n}\n`;\nconst classes = {\n  root: ({\n    instance,\n    props\n  }) => ['p-button p-component', {\n    'p-button-icon-only': instance.hasIcon && !props.label && !props.badge,\n    'p-button-vertical': (props.iconPos === 'top' || props.iconPos === 'bottom') && props.label,\n    'p-button-loading': props.loading,\n    'p-button-link': props.link,\n    [`p-button-${props.severity}`]: props.severity,\n    'p-button-raised': props.raised,\n    'p-button-rounded': props.rounded,\n    'p-button-text': props.text,\n    'p-button-outlined': props.outlined,\n    'p-button-sm': props.size === 'small',\n    'p-button-lg': props.size === 'large',\n    'p-button-plain': props.plain,\n    'p-button-fluid': props.fluid\n  }],\n  loadingIcon: 'p-button-loading-icon',\n  icon: ({\n    props\n  }) => ['p-button-icon', {\n    [`p-button-icon-${props.iconPos}`]: props.label\n  }],\n  label: 'p-button-label'\n};\nclass ButtonStyle extends BaseStyle {\n  name = 'button';\n  theme = theme;\n  classes = classes;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵButtonStyle_BaseFactory;\n    return function ButtonStyle_Factory(__ngFactoryType__) {\n      return (ɵButtonStyle_BaseFactory || (ɵButtonStyle_BaseFactory = i0.ɵɵgetInheritedFactory(ButtonStyle)))(__ngFactoryType__ || ButtonStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ButtonStyle,\n    factory: ButtonStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ButtonStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n *\n * Button is an extension to standard button element with icons and theming.\n *\n * [Live Demo](https://www.primeng.org/button/)\n *\n * @module buttonstyle\n *\n */\nvar ButtonClasses;\n(function (ButtonClasses) {\n  /**\n   * Class name of the root element\n   */\n  ButtonClasses[\"root\"] = \"p-button\";\n  /**\n   * Class name of the loading icon element\n   */\n  ButtonClasses[\"loadingIcon\"] = \"p-button-loading-icon\";\n  /**\n   * Class name of the icon element\n   */\n  ButtonClasses[\"icon\"] = \"p-button-icon\";\n  /**\n   * Class name of the label element\n   */\n  ButtonClasses[\"label\"] = \"p-button-label\";\n})(ButtonClasses || (ButtonClasses = {}));\nconst INTERNAL_BUTTON_CLASSES = {\n  button: 'p-button',\n  component: 'p-component',\n  iconOnly: 'p-button-icon-only',\n  disabled: 'p-disabled',\n  loading: 'p-button-loading',\n  labelOnly: 'p-button-loading-label-only'\n};\nclass ButtonLabel extends BaseComponent {\n  _componentStyle = inject(ButtonStyle);\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵButtonLabel_BaseFactory;\n    return function ButtonLabel_Factory(__ngFactoryType__) {\n      return (ɵButtonLabel_BaseFactory || (ɵButtonLabel_BaseFactory = i0.ɵɵgetInheritedFactory(ButtonLabel)))(__ngFactoryType__ || ButtonLabel);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: ButtonLabel,\n    selectors: [[\"\", \"pButtonLabel\", \"\"]],\n    hostVars: 2,\n    hostBindings: function ButtonLabel_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"p-button-label\", true);\n      }\n    },\n    features: [i0.ɵɵProvidersFeature([ButtonStyle]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ButtonLabel, [{\n    type: Directive,\n    args: [{\n      selector: '[pButtonLabel]',\n      providers: [ButtonStyle],\n      standalone: true,\n      host: {\n        '[class.p-button-label]': 'true'\n      }\n    }]\n  }], null, null);\n})();\nclass ButtonIcon extends BaseComponent {\n  _componentStyle = inject(ButtonStyle);\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵButtonIcon_BaseFactory;\n    return function ButtonIcon_Factory(__ngFactoryType__) {\n      return (ɵButtonIcon_BaseFactory || (ɵButtonIcon_BaseFactory = i0.ɵɵgetInheritedFactory(ButtonIcon)))(__ngFactoryType__ || ButtonIcon);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: ButtonIcon,\n    selectors: [[\"\", \"pButtonIcon\", \"\"]],\n    hostVars: 2,\n    hostBindings: function ButtonIcon_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"p-button-icon\", true);\n      }\n    },\n    features: [i0.ɵɵProvidersFeature([ButtonStyle]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ButtonIcon, [{\n    type: Directive,\n    args: [{\n      selector: '[pButtonIcon]',\n      providers: [ButtonStyle],\n      standalone: true,\n      host: {\n        '[class.p-button-icon]': 'true'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Button directive is an extension to button component.\n * @group Components\n */\nclass ButtonDirective extends BaseComponent {\n  /**\n   * Position of the icon.\n   * @deprecated utilize pButtonIcon and pButtonLabel directives.\n   * @group Props\n   */\n  iconPos = 'left';\n  /**\n   * Uses to pass attributes to the loading icon's DOM element.\n   * @deprecated utilize pButonIcon instead.\n   * @group Props\n   */\n  loadingIcon;\n  set label(val) {\n    this._label = val;\n    if (this.initialized) {\n      this.updateLabel();\n      this.updateIcon();\n      this.setStyleClass();\n    }\n  }\n  set icon(val) {\n    this._icon = val;\n    if (this.initialized) {\n      this.updateIcon();\n      this.setStyleClass();\n    }\n  }\n  /**\n   * Whether the button is in loading state.\n   * @group Props\n   */\n  get loading() {\n    return this._loading;\n  }\n  set loading(val) {\n    this._loading = val;\n    if (this.initialized) {\n      this.updateIcon();\n      this.setStyleClass();\n    }\n  }\n  _buttonProps;\n  iconSignal = contentChild(ButtonIcon);\n  labelSignal = contentChild(ButtonLabel);\n  isIconOnly = computed(() => !!(!this.labelSignal() && this.iconSignal()));\n  set buttonProps(val) {\n    this._buttonProps = val;\n    if (val && typeof val === 'object') {\n      //@ts-ignore\n      Object.entries(val).forEach(([k, v]) => this[`_${k}`] !== v && (this[`_${k}`] = v));\n    }\n  }\n  _severity;\n  /**\n   * Defines the style of the button.\n   * @group Props\n   */\n  get severity() {\n    return this._severity;\n  }\n  set severity(value) {\n    this._severity = value;\n    if (this.initialized) {\n      this.setStyleClass();\n    }\n  }\n  /**\n   * Add a shadow to indicate elevation.\n   * @group Props\n   */\n  raised = false;\n  /**\n   * Add a circular border radius to the button.\n   * @group Props\n   */\n  rounded = false;\n  /**\n   * Add a textual class to the button without a background initially.\n   * @group Props\n   */\n  text = false;\n  /**\n   * Add a border class without a background initially.\n   * @group Props\n   */\n  outlined = false;\n  /**\n   * Defines the size of the button.\n   * @group Props\n   */\n  size = null;\n  /**\n   * Add a plain textual class to the button without a background initially.\n   * @deprecated use variant property instead.\n   * @group Props\n   */\n  plain = false;\n  /**\n   * Spans 100% width of the container when enabled.\n   * @group Props\n   */\n  fluid;\n  _label;\n  _icon;\n  _loading = false;\n  initialized;\n  get htmlElement() {\n    return this.el.nativeElement;\n  }\n  _internalClasses = Object.values(INTERNAL_BUTTON_CLASSES);\n  isTextButton = computed(() => !!(!this.iconSignal() && this.labelSignal() && this.text));\n  /**\n   * Text of the button.\n   * @deprecated use pButtonLabel directive instead.\n   * @group Props\n   */\n  get label() {\n    return this._label;\n  }\n  /**\n   * Name of the icon.\n   * @deprecated use pButtonIcon directive instead\n   * @group Props\n   */\n  get icon() {\n    return this._icon;\n  }\n  /**\n   * Used to pass all properties of the ButtonProps to the Button component.\n   * @deprecated assign props directly to the button element.\n   * @group Props\n   */\n  get buttonProps() {\n    return this._buttonProps;\n  }\n  spinnerIcon = `<svg width=\"14\" height=\"14\" viewBox=\"0 0 14 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" class=\"p-icon-spin\">\n        <g clip-path=\"url(#clip0_417_21408)\">\n            <path\n                d=\"M6.99701 14C5.85441 13.999 4.72939 13.7186 3.72012 13.1832C2.71084 12.6478 1.84795 11.8737 1.20673 10.9284C0.565504 9.98305 0.165424 8.89526 0.041387 7.75989C-0.0826496 6.62453 0.073125 5.47607 0.495122 4.4147C0.917119 3.35333 1.59252 2.4113 2.46241 1.67077C3.33229 0.930247 4.37024 0.413729 5.4857 0.166275C6.60117 -0.0811796 7.76026 -0.0520535 8.86188 0.251112C9.9635 0.554278 10.9742 1.12227 11.8057 1.90555C11.915 2.01493 11.9764 2.16319 11.9764 2.31778C11.9764 2.47236 11.915 2.62062 11.8057 2.73C11.7521 2.78503 11.688 2.82877 11.6171 2.85864C11.5463 2.8885 11.4702 2.90389 11.3933 2.90389C11.3165 2.90389 11.2404 2.8885 11.1695 2.85864C11.0987 2.82877 11.0346 2.78503 10.9809 2.73C9.9998 1.81273 8.73246 1.26138 7.39226 1.16876C6.05206 1.07615 4.72086 1.44794 3.62279 2.22152C2.52471 2.99511 1.72683 4.12325 1.36345 5.41602C1.00008 6.70879 1.09342 8.08723 1.62775 9.31926C2.16209 10.5513 3.10478 11.5617 4.29713 12.1803C5.48947 12.7989 6.85865 12.988 8.17414 12.7157C9.48963 12.4435 10.6711 11.7264 11.5196 10.6854C12.3681 9.64432 12.8319 8.34282 12.8328 7C12.8328 6.84529 12.8943 6.69692 13.0038 6.58752C13.1132 6.47812 13.2616 6.41667 13.4164 6.41667C13.5712 6.41667 13.7196 6.47812 13.8291 6.58752C13.9385 6.69692 14 6.84529 14 7C14 8.85651 13.2622 10.637 11.9489 11.9497C10.6356 13.2625 8.85432 14 6.99701 14Z\"\n                fill=\"currentColor\"\n            />\n        </g>\n        <defs>\n            <clipPath id=\"clip0_417_21408\">\n                <rect width=\"14\" height=\"14\" fill=\"white\" />\n            </clipPath>\n        </defs>\n    </svg>`;\n  _componentStyle = inject(ButtonStyle);\n  ngAfterViewInit() {\n    super.ngAfterViewInit();\n    addClass(this.htmlElement, this.getStyleClass().join(' '));\n    this.createIcon();\n    this.createLabel();\n    this.initialized = true;\n  }\n  ngOnChanges(simpleChanges) {\n    super.ngOnChanges(simpleChanges);\n    const {\n      buttonProps\n    } = simpleChanges;\n    if (buttonProps) {\n      const props = buttonProps.currentValue;\n      for (const property in props) {\n        this[property] = props[property];\n      }\n    }\n  }\n  getStyleClass() {\n    const styleClass = [INTERNAL_BUTTON_CLASSES.button, INTERNAL_BUTTON_CLASSES.component];\n    if (this.icon && !this.label && isEmpty(this.htmlElement.textContent)) {\n      styleClass.push(INTERNAL_BUTTON_CLASSES.iconOnly);\n    }\n    if (this.loading) {\n      styleClass.push(INTERNAL_BUTTON_CLASSES.disabled, INTERNAL_BUTTON_CLASSES.loading);\n      if (!this.icon && this.label) {\n        styleClass.push(INTERNAL_BUTTON_CLASSES.labelOnly);\n      }\n      if (this.icon && !this.label && !isEmpty(this.htmlElement.textContent)) {\n        styleClass.push(INTERNAL_BUTTON_CLASSES.iconOnly);\n      }\n    }\n    if (this.text) {\n      styleClass.push('p-button-text');\n    }\n    if (this.severity) {\n      styleClass.push(`p-button-${this.severity}`);\n    }\n    if (this.plain) {\n      styleClass.push('p-button-plain');\n    }\n    if (this.raised) {\n      styleClass.push('p-button-raised');\n    }\n    if (this.size) {\n      styleClass.push(`p-button-${this.size}`);\n    }\n    if (this.outlined) {\n      styleClass.push('p-button-outlined');\n    }\n    if (this.rounded) {\n      styleClass.push('p-button-rounded');\n    }\n    if (this.size === 'small') {\n      styleClass.push('p-button-sm');\n    }\n    if (this.size === 'large') {\n      styleClass.push('p-button-lg');\n    }\n    if (this.hasFluid) {\n      styleClass.push('p-button-fluid');\n    }\n    return styleClass;\n  }\n  get hasFluid() {\n    const nativeElement = this.el.nativeElement;\n    const fluidComponent = nativeElement.closest('p-fluid');\n    return isEmpty(this.fluid) ? !!fluidComponent : this.fluid;\n  }\n  setStyleClass() {\n    const styleClass = this.getStyleClass();\n    this.removeExistingSeverityClass();\n    this.htmlElement.classList.remove(...this._internalClasses);\n    this.htmlElement.classList.add(...styleClass);\n  }\n  removeExistingSeverityClass() {\n    const severityArray = ['success', 'info', 'warn', 'danger', 'help', 'primary', 'secondary', 'contrast'];\n    const existingSeverityClass = this.htmlElement.classList.value.split(' ').find(cls => severityArray.some(severity => cls === `p-button-${severity}`));\n    if (existingSeverityClass) {\n      this.htmlElement.classList.remove(existingSeverityClass);\n    }\n  }\n  createLabel() {\n    const created = findSingle(this.htmlElement, '.p-button-label');\n    if (!created && this.label) {\n      let labelElement = this.document.createElement('span');\n      if (this.icon && !this.label) {\n        labelElement.setAttribute('aria-hidden', 'true');\n      }\n      labelElement.className = 'p-button-label';\n      labelElement.appendChild(this.document.createTextNode(this.label));\n      this.htmlElement.appendChild(labelElement);\n    }\n  }\n  createIcon() {\n    const created = findSingle(this.htmlElement, '.p-button-icon');\n    if (!created && (this.icon || this.loading)) {\n      let iconElement = this.document.createElement('span');\n      iconElement.className = 'p-button-icon';\n      iconElement.setAttribute('aria-hidden', 'true');\n      let iconPosClass = this.label ? 'p-button-icon-' + this.iconPos : null;\n      if (iconPosClass) {\n        addClass(iconElement, iconPosClass);\n      }\n      let iconClass = this.getIconClass();\n      if (iconClass) {\n        addClass(iconElement, iconClass);\n      }\n      if (!this.loadingIcon && this.loading) {\n        iconElement.innerHTML = this.spinnerIcon;\n      }\n      this.htmlElement.insertBefore(iconElement, this.htmlElement.firstChild);\n    }\n  }\n  updateLabel() {\n    let labelElement = findSingle(this.htmlElement, '.p-button-label');\n    if (!this.label) {\n      labelElement && this.htmlElement.removeChild(labelElement);\n      return;\n    }\n    labelElement ? labelElement.textContent = this.label : this.createLabel();\n  }\n  updateIcon() {\n    let iconElement = findSingle(this.htmlElement, '.p-button-icon');\n    let labelElement = findSingle(this.htmlElement, '.p-button-label');\n    if (this.loading && !this.loadingIcon && iconElement) {\n      iconElement.innerHTML = this.spinnerIcon;\n    } else if (iconElement?.innerHTML) {\n      iconElement.innerHTML = '';\n    }\n    if (iconElement) {\n      if (this.iconPos) {\n        iconElement.className = 'p-button-icon ' + (labelElement ? 'p-button-icon-' + this.iconPos : '') + ' ' + this.getIconClass();\n      } else {\n        iconElement.className = 'p-button-icon ' + this.getIconClass();\n      }\n    } else {\n      this.createIcon();\n    }\n  }\n  getIconClass() {\n    return this.loading ? 'p-button-loading-icon ' + (this.loadingIcon ? this.loadingIcon : 'p-icon') : this.icon || 'p-hidden';\n  }\n  ngOnDestroy() {\n    this.initialized = false;\n    super.ngOnDestroy();\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵButtonDirective_BaseFactory;\n    return function ButtonDirective_Factory(__ngFactoryType__) {\n      return (ɵButtonDirective_BaseFactory || (ɵButtonDirective_BaseFactory = i0.ɵɵgetInheritedFactory(ButtonDirective)))(__ngFactoryType__ || ButtonDirective);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: ButtonDirective,\n    selectors: [[\"\", \"pButton\", \"\"]],\n    contentQueries: function ButtonDirective_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuerySignal(dirIndex, ctx.iconSignal, ButtonIcon, 5);\n        i0.ɵɵcontentQuerySignal(dirIndex, ctx.labelSignal, ButtonLabel, 5);\n      }\n      if (rf & 2) {\n        i0.ɵɵqueryAdvance(2);\n      }\n    },\n    hostVars: 4,\n    hostBindings: function ButtonDirective_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"p-button-icon-only\", ctx.isIconOnly())(\"p-button-text\", ctx.isTextButton());\n      }\n    },\n    inputs: {\n      iconPos: \"iconPos\",\n      loadingIcon: \"loadingIcon\",\n      loading: \"loading\",\n      severity: \"severity\",\n      raised: [2, \"raised\", \"raised\", booleanAttribute],\n      rounded: [2, \"rounded\", \"rounded\", booleanAttribute],\n      text: [2, \"text\", \"text\", booleanAttribute],\n      outlined: [2, \"outlined\", \"outlined\", booleanAttribute],\n      size: \"size\",\n      plain: [2, \"plain\", \"plain\", booleanAttribute],\n      fluid: [2, \"fluid\", \"fluid\", booleanAttribute],\n      label: \"label\",\n      icon: \"icon\",\n      buttonProps: \"buttonProps\"\n    },\n    features: [i0.ɵɵProvidersFeature([ButtonStyle]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ButtonDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[pButton]',\n      standalone: true,\n      providers: [ButtonStyle],\n      host: {\n        '[class.p-button-icon-only]': 'isIconOnly()',\n        '[class.p-button-text]': 'isTextButton()'\n      }\n    }]\n  }], null, {\n    iconPos: [{\n      type: Input\n    }],\n    loadingIcon: [{\n      type: Input\n    }],\n    loading: [{\n      type: Input\n    }],\n    severity: [{\n      type: Input\n    }],\n    raised: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    rounded: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    text: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    outlined: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    size: [{\n      type: Input\n    }],\n    plain: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    fluid: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    label: [{\n      type: Input\n    }],\n    icon: [{\n      type: Input\n    }],\n    buttonProps: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Button is an extension to standard button element with icons and theming.\n * @group Components\n */\nclass Button extends BaseComponent {\n  /**\n   * Type of the button.\n   * @group Props\n   */\n  type = 'button';\n  /**\n   * Position of the icon.\n   * @group Props\n   */\n  iconPos = 'left';\n  /**\n   * Name of the icon.\n   * @group Props\n   */\n  icon;\n  /**\n   * Value of the badge.\n   * @group Props\n   */\n  badge;\n  /**\n   * Uses to pass attributes to the label's DOM element.\n   * @group Props\n   */\n  label;\n  /**\n   * When present, it specifies that the component should be disabled.\n   * @group Props\n   */\n  disabled;\n  /**\n   * Whether the button is in loading state.\n   * @group Props\n   */\n  loading = false;\n  /**\n   * Icon to display in loading state.\n   * @group Props\n   */\n  loadingIcon;\n  /**\n   * Add a shadow to indicate elevation.\n   * @group Props\n   */\n  raised = false;\n  /**\n   * Add a circular border radius to the button.\n   * @group Props\n   */\n  rounded = false;\n  /**\n   * Add a textual class to the button without a background initially.\n   * @group Props\n   */\n  text = false;\n  /**\n   * Add a plain textual class to the button without a background initially.\n   * @deprecated use variant property instead.\n   * @group Props\n   */\n  plain = false;\n  /**\n   * Defines the style of the button.\n   * @group Props\n   */\n  severity;\n  /**\n   * Add a border class without a background initially.\n   * @group Props\n   */\n  outlined = false;\n  /**\n   * Add a link style to the button.\n   * @group Props\n   */\n  link = false;\n  /**\n   * Add a tabindex to the button.\n   * @group Props\n   */\n  tabindex;\n  /**\n   * Defines the size of the button.\n   * @group Props\n   */\n  size;\n  /**\n   * Specifies the variant of the component.\n   * @group Props\n   */\n  variant;\n  /**\n   * Inline style of the element.\n   * @group Props\n   */\n  style;\n  /**\n   * Class of the element.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Style class of the badge.\n   * @group Props\n   * @deprecated use badgeSeverity instead.\n   */\n  badgeClass;\n  /**\n   * Severity type of the badge.\n   * @group Props\n   * @defaultValue secondary\n   */\n  badgeSeverity = 'secondary';\n  /**\n   * Used to define a string that autocomplete attribute the current element.\n   * @group Props\n   */\n  ariaLabel;\n  /**\n   * When present, it specifies that the component should automatically get focus on load.\n   * @group Props\n   */\n  autofocus;\n  /**\n   * Spans 100% width of the container when enabled.\n   * @group Props\n   */\n  fluid;\n  /**\n   * Callback to execute when button is clicked.\n   * This event is intended to be used with the <p-button> component. Using a regular <button> element, use (click).\n   * @param {MouseEvent} event - Mouse event.\n   * @group Emits\n   */\n  onClick = new EventEmitter();\n  /**\n   * Callback to execute when button is focused.\n   * This event is intended to be used with the <p-button> component. Using a regular <button> element, use (focus).\n   * @param {FocusEvent} event - Focus event.\n   * @group Emits\n   */\n  onFocus = new EventEmitter();\n  /**\n   * Callback to execute when button loses focus.\n   * This event is intended to be used with the <p-button> component. Using a regular <button> element, use (blur).\n   * @param {FocusEvent} event - Focus event.\n   * @group Emits\n   */\n  onBlur = new EventEmitter();\n  /**\n   * Template of the content.\n   * @group Templates\n   **/\n  contentTemplate;\n  /**\n   * Template of the loading.\n   * @group Templates\n   **/\n  loadingIconTemplate;\n  /**\n   * Template of the icon.\n   * @group Templates\n   **/\n  iconTemplate;\n  _buttonProps;\n  /**\n   * Used to pass all properties of the ButtonProps to the Button component.\n   * @group Props\n   */\n  get buttonProps() {\n    return this._buttonProps;\n  }\n  set buttonProps(val) {\n    this._buttonProps = val;\n    if (val && typeof val === 'object') {\n      //@ts-ignore\n      Object.entries(val).forEach(([k, v]) => this[`_${k}`] !== v && (this[`_${k}`] = v));\n    }\n  }\n  get hasFluid() {\n    const nativeElement = this.el.nativeElement;\n    const fluidComponent = nativeElement.closest('p-fluid');\n    return isEmpty(this.fluid) ? !!fluidComponent : this.fluid;\n  }\n  _componentStyle = inject(ButtonStyle);\n  templates;\n  _contentTemplate;\n  _iconTemplate;\n  _loadingIconTemplate;\n  ngAfterContentInit() {\n    this.templates?.forEach(item => {\n      switch (item.getType()) {\n        case 'content':\n          this._contentTemplate = item.template;\n          break;\n        case 'icon':\n          this._iconTemplate = item.template;\n          break;\n        case 'loadingicon':\n          this._loadingIconTemplate = item.template;\n          break;\n        default:\n          this._contentTemplate = item.template;\n          break;\n      }\n    });\n  }\n  ngOnChanges(simpleChanges) {\n    super.ngOnChanges(simpleChanges);\n    const {\n      buttonProps\n    } = simpleChanges;\n    if (buttonProps) {\n      const props = buttonProps.currentValue;\n      for (const property in props) {\n        this[property] = props[property];\n      }\n    }\n  }\n  spinnerIconClass() {\n    return Object.entries(this.iconClass()).filter(([, value]) => !!value).reduce((acc, [key]) => acc + ` ${key}`, 'p-button-loading-icon');\n  }\n  iconClass() {\n    return {\n      [`p-button-loading-icon pi-spin ${this.loadingIcon ?? ''}`]: this.loading,\n      'p-button-icon': true,\n      'p-button-icon-left': this.iconPos === 'left' && this.label,\n      'p-button-icon-right': this.iconPos === 'right' && this.label,\n      'p-button-icon-top': this.iconPos === 'top' && this.label,\n      'p-button-icon-bottom': this.iconPos === 'bottom' && this.label\n    };\n  }\n  get buttonClass() {\n    return {\n      'p-button p-component': true,\n      'p-button-icon-only': (this.icon || this.iconTemplate || this._iconTemplate || this.loadingIcon || this.loadingIconTemplate || this._loadingIconTemplate) && !this.label,\n      'p-button-vertical': (this.iconPos === 'top' || this.iconPos === 'bottom') && this.label,\n      'p-button-loading': this.loading,\n      'p-button-loading-label-only': this.loading && !this.icon && this.label && !this.loadingIcon && this.iconPos === 'left',\n      'p-button-link': this.link,\n      [`p-button-${this.severity}`]: this.severity,\n      'p-button-raised': this.raised,\n      'p-button-rounded': this.rounded,\n      'p-button-text': this.text || this.variant == 'text',\n      'p-button-outlined': this.outlined || this.variant == 'outlined',\n      'p-button-sm': this.size === 'small',\n      'p-button-lg': this.size === 'large',\n      'p-button-plain': this.plain,\n      'p-button-fluid': this.hasFluid,\n      [`${this.styleClass}`]: this.styleClass\n    };\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵButton_BaseFactory;\n    return function Button_Factory(__ngFactoryType__) {\n      return (ɵButton_BaseFactory || (ɵButton_BaseFactory = i0.ɵɵgetInheritedFactory(Button)))(__ngFactoryType__ || Button);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Button,\n    selectors: [[\"p-button\"]],\n    contentQueries: function Button_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, _c0, 5);\n        i0.ɵɵcontentQuery(dirIndex, _c1, 5);\n        i0.ɵɵcontentQuery(dirIndex, _c2, 5);\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.contentTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.loadingIconTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.iconTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    inputs: {\n      type: \"type\",\n      iconPos: \"iconPos\",\n      icon: \"icon\",\n      badge: \"badge\",\n      label: \"label\",\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n      loading: [2, \"loading\", \"loading\", booleanAttribute],\n      loadingIcon: \"loadingIcon\",\n      raised: [2, \"raised\", \"raised\", booleanAttribute],\n      rounded: [2, \"rounded\", \"rounded\", booleanAttribute],\n      text: [2, \"text\", \"text\", booleanAttribute],\n      plain: [2, \"plain\", \"plain\", booleanAttribute],\n      severity: \"severity\",\n      outlined: [2, \"outlined\", \"outlined\", booleanAttribute],\n      link: [2, \"link\", \"link\", booleanAttribute],\n      tabindex: [2, \"tabindex\", \"tabindex\", numberAttribute],\n      size: \"size\",\n      variant: \"variant\",\n      style: \"style\",\n      styleClass: \"styleClass\",\n      badgeClass: \"badgeClass\",\n      badgeSeverity: \"badgeSeverity\",\n      ariaLabel: \"ariaLabel\",\n      autofocus: [2, \"autofocus\", \"autofocus\", booleanAttribute],\n      fluid: [2, \"fluid\", \"fluid\", booleanAttribute],\n      buttonProps: \"buttonProps\"\n    },\n    outputs: {\n      onClick: \"onClick\",\n      onFocus: \"onFocus\",\n      onBlur: \"onBlur\"\n    },\n    features: [i0.ɵɵProvidersFeature([ButtonStyle]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c3,\n    decls: 7,\n    vars: 14,\n    consts: [[\"pRipple\", \"\", 3, \"click\", \"focus\", \"blur\", \"ngStyle\", \"disabled\", \"ngClass\", \"pAutoFocus\"], [4, \"ngTemplateOutlet\"], [4, \"ngIf\"], [\"class\", \"p-button-label\", 4, \"ngIf\"], [3, \"value\", \"severity\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [3, \"ngClass\", 4, \"ngIf\"], [3, \"styleClass\", \"spin\", 4, \"ngIf\"], [3, \"ngClass\"], [3, \"styleClass\", \"spin\"], [3, \"ngIf\"], [3, \"class\", \"ngClass\", 4, \"ngIf\"], [1, \"p-button-label\"], [3, \"value\", \"severity\"]],\n    template: function Button_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"button\", 0);\n        i0.ɵɵlistener(\"click\", function Button_Template_button_click_0_listener($event) {\n          return ctx.onClick.emit($event);\n        })(\"focus\", function Button_Template_button_focus_0_listener($event) {\n          return ctx.onFocus.emit($event);\n        })(\"blur\", function Button_Template_button_blur_0_listener($event) {\n          return ctx.onBlur.emit($event);\n        });\n        i0.ɵɵprojection(1);\n        i0.ɵɵtemplate(2, Button_ng_container_2_Template, 1, 0, \"ng-container\", 1)(3, Button_ng_container_3_Template, 3, 5, \"ng-container\", 2)(4, Button_ng_container_4_Template, 3, 5, \"ng-container\", 2)(5, Button_span_5_Template, 2, 3, \"span\", 3)(6, Button_p_badge_6_Template, 1, 2, \"p-badge\", 4);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngStyle\", ctx.style)(\"disabled\", ctx.disabled || ctx.loading)(\"ngClass\", ctx.buttonClass)(\"pAutoFocus\", ctx.autofocus);\n        i0.ɵɵattribute(\"type\", ctx.type)(\"aria-label\", ctx.ariaLabel)(\"data-pc-name\", \"button\")(\"data-pc-section\", \"root\")(\"tabindex\", ctx.tabindex);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.contentTemplate || ctx._contentTemplate);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.contentTemplate && !ctx._contentTemplate && ctx.label);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.contentTemplate && !ctx._contentTemplate && ctx.badge);\n      }\n    },\n    dependencies: [CommonModule, i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, Ripple, AutoFocus, SpinnerIcon, BadgeModule, i2.Badge, SharedModule],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Button, [{\n    type: Component,\n    args: [{\n      selector: 'p-button',\n      standalone: true,\n      imports: [CommonModule, Ripple, AutoFocus, SpinnerIcon, BadgeModule, SharedModule],\n      template: `\n        <button\n            [attr.type]=\"type\"\n            [attr.aria-label]=\"ariaLabel\"\n            [ngStyle]=\"style\"\n            [disabled]=\"disabled || loading\"\n            [ngClass]=\"buttonClass\"\n            (click)=\"onClick.emit($event)\"\n            (focus)=\"onFocus.emit($event)\"\n            (blur)=\"onBlur.emit($event)\"\n            pRipple\n            [attr.data-pc-name]=\"'button'\"\n            [attr.data-pc-section]=\"'root'\"\n            [attr.tabindex]=\"tabindex\"\n            [pAutoFocus]=\"autofocus\"\n        >\n            <ng-content></ng-content>\n            <ng-container *ngTemplateOutlet=\"contentTemplate || _contentTemplate\"></ng-container>\n            <ng-container *ngIf=\"loading\">\n                <ng-container *ngIf=\"!loadingIconTemplate && !_loadingIconTemplate\">\n                    <span *ngIf=\"loadingIcon\" [ngClass]=\"iconClass()\" [attr.aria-hidden]=\"true\" [attr.data-pc-section]=\"'loadingicon'\"></span>\n                    <SpinnerIcon *ngIf=\"!loadingIcon\" [styleClass]=\"spinnerIconClass()\" [spin]=\"true\" [attr.aria-hidden]=\"true\" [attr.data-pc-section]=\"'loadingicon'\" />\n                </ng-container>\n                <ng-template [ngIf]=\"loadingIconTemplate || _loadingIconTemplate\" *ngTemplateOutlet=\"loadingIconTemplate || _loadingIconTemplate; context: { class: iconClass() }\"></ng-template>\n            </ng-container>\n            <ng-container *ngIf=\"!loading\">\n                <span *ngIf=\"icon && !iconTemplate && !_iconTemplate\" [class]=\"icon\" [ngClass]=\"iconClass()\" [attr.data-pc-section]=\"'icon'\"></span>\n                <ng-template [ngIf]=\"!icon && (iconTemplate || _iconTemplate)\" *ngTemplateOutlet=\"iconTemplate || _iconTemplate; context: { class: iconClass() }\"></ng-template>\n            </ng-container>\n            <span class=\"p-button-label\" [attr.aria-hidden]=\"icon && !label\" *ngIf=\"!contentTemplate && !_contentTemplate && label\" [attr.data-pc-section]=\"'label'\">{{ label }}</span>\n            <p-badge *ngIf=\"!contentTemplate && !_contentTemplate && badge\" [value]=\"badge\" [severity]=\"badgeSeverity\"></p-badge>\n        </button>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [ButtonStyle]\n    }]\n  }], null, {\n    type: [{\n      type: Input\n    }],\n    iconPos: [{\n      type: Input\n    }],\n    icon: [{\n      type: Input\n    }],\n    badge: [{\n      type: Input\n    }],\n    label: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    loading: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    loadingIcon: [{\n      type: Input\n    }],\n    raised: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    rounded: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    text: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    plain: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    severity: [{\n      type: Input\n    }],\n    outlined: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    link: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    tabindex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    size: [{\n      type: Input\n    }],\n    variant: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    badgeClass: [{\n      type: Input\n    }],\n    badgeSeverity: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }],\n    autofocus: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    fluid: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    onClick: [{\n      type: Output\n    }],\n    onFocus: [{\n      type: Output\n    }],\n    onBlur: [{\n      type: Output\n    }],\n    contentTemplate: [{\n      type: ContentChild,\n      args: ['content']\n    }],\n    loadingIconTemplate: [{\n      type: ContentChild,\n      args: ['loadingicon']\n    }],\n    iconTemplate: [{\n      type: ContentChild,\n      args: ['icon']\n    }],\n    buttonProps: [{\n      type: Input\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }]\n  });\n})();\nclass ButtonModule {\n  static ɵfac = function ButtonModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ButtonModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ButtonModule,\n    imports: [CommonModule, ButtonDirective, Button, SharedModule, ButtonLabel, ButtonIcon],\n    exports: [ButtonDirective, Button, ButtonLabel, ButtonIcon, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, Button, SharedModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ButtonModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, ButtonDirective, Button, SharedModule, ButtonLabel, ButtonIcon],\n      exports: [ButtonDirective, Button, ButtonLabel, ButtonIcon, SharedModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Button, ButtonClasses, ButtonDirective, ButtonIcon, ButtonLabel, ButtonModule, ButtonStyle };", "map": {"version": 3, "names": ["i1", "CommonModule", "i0", "Injectable", "inject", "Directive", "contentChild", "computed", "booleanAttribute", "Input", "EventEmitter", "numberAttribute", "ContentChildren", "ContentChild", "Output", "ViewEncapsulation", "ChangeDetectionStrategy", "Component", "NgModule", "addClass", "isEmpty", "findSingle", "SharedModule", "PrimeTemplate", "AutoFocus", "i2", "BadgeModule", "BaseComponent", "SpinnerIcon", "<PERSON><PERSON><PERSON>", "BaseStyle", "_c0", "_c1", "_c2", "_c3", "_c4", "a0", "class", "Button_ng_container_2_Template", "rf", "ctx", "ɵɵelementContainer", "Button_ng_container_3_ng_container_1_span_1_Template", "ɵɵelement", "ctx_r0", "ɵɵnextContext", "ɵɵproperty", "iconClass", "ɵɵattribute", "Button_ng_container_3_ng_container_1_SpinnerIcon_2_Template", "spinnerIconClass", "Button_ng_container_3_ng_container_1_Template", "ɵɵelementContainerStart", "ɵɵtemplate", "ɵɵelementContainerEnd", "ɵɵadvance", "loadingIcon", "Button_ng_container_3_2_ng_template_0_Template", "Button_ng_container_3_2_Template", "loadingIconTemplate", "_loadingIconTemplate", "Button_ng_container_3_Template", "ɵɵpureFunction1", "Button_ng_container_4_span_1_Template", "ɵɵclassMap", "icon", "Button_ng_container_4_2_ng_template_0_Template", "Button_ng_container_4_2_Template", "iconTemplate", "_iconTemplate", "Button_ng_container_4_Template", "Button_span_5_Template", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "label", "ɵɵtextInterpolate", "But<PERSON>_p_badge_6_Template", "badge", "badgeSeverity", "theme", "dt", "classes", "root", "instance", "props", "hasIcon", "iconPos", "loading", "link", "severity", "raised", "rounded", "text", "outlined", "size", "plain", "fluid", "ButtonStyle", "name", "ɵfac", "ɵButtonStyle_BaseFactory", "ButtonStyle_Factory", "__ngFactoryType__", "ɵɵgetInheritedFactory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "ButtonClasses", "INTERNAL_BUTTON_CLASSES", "button", "component", "iconOnly", "disabled", "labelOnly", "ButtonLabel", "_componentStyle", "ɵButtonLabel_BaseFactory", "ButtonLabel_Factory", "ɵdir", "ɵɵdefineDirective", "selectors", "hostVars", "hostBindings", "ButtonLabel_HostBindings", "ɵɵclassProp", "features", "ɵɵProvidersFeature", "ɵɵInheritDefinitionFeature", "args", "selector", "providers", "standalone", "host", "ButtonIcon", "ɵButtonIcon_BaseFactory", "ButtonIcon_Factory", "ButtonIcon_HostBindings", "ButtonDirective", "val", "_label", "initialized", "updateLabel", "updateIcon", "setStyleClass", "_icon", "_loading", "_buttonProps", "iconSignal", "labelSignal", "isIconOnly", "buttonProps", "Object", "entries", "for<PERSON>ach", "k", "v", "_severity", "value", "htmlElement", "el", "nativeElement", "_internalClasses", "values", "isTextButton", "spinnerIcon", "ngAfterViewInit", "getStyleClass", "join", "createIcon", "createLabel", "ngOnChanges", "simpleChanges", "currentValue", "property", "styleClass", "textContent", "push", "hasFluid", "fluidComponent", "closest", "removeExistingSeverityClass", "classList", "remove", "add", "severityArray", "existingSeverityClass", "split", "find", "cls", "some", "created", "labelElement", "document", "createElement", "setAttribute", "className", "append<PERSON><PERSON><PERSON>", "createTextNode", "iconElement", "iconPosClass", "getIconClass", "innerHTML", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "ngOnDestroy", "ɵButtonDirective_BaseFactory", "ButtonDirective_Factory", "contentQueries", "ButtonDirective_ContentQueries", "dirIndex", "ɵɵcontentQuerySignal", "ɵɵqueryAdvance", "ButtonDirective_HostBindings", "inputs", "ɵɵNgOnChangesFeature", "transform", "<PERSON><PERSON>", "tabindex", "variant", "style", "badgeClass", "aria<PERSON><PERSON><PERSON>", "autofocus", "onClick", "onFocus", "onBlur", "contentTemplate", "templates", "_contentTemplate", "ngAfterContentInit", "item", "getType", "template", "filter", "reduce", "acc", "key", "buttonClass", "ɵButton_BaseFactory", "Button_Factory", "ɵcmp", "ɵɵdefineComponent", "Button_ContentQueries", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "outputs", "ngContentSelectors", "decls", "vars", "consts", "But<PERSON>_Template", "ɵɵprojectionDef", "ɵɵlistener", "Button_Template_button_click_0_listener", "$event", "emit", "Button_Template_button_focus_0_listener", "Button_Template_button_blur_0_listener", "ɵɵprojection", "dependencies", "Ng<PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "NgStyle", "Badge", "encapsulation", "changeDetection", "imports", "OnPush", "None", "ButtonModule", "ButtonModule_Factory", "ɵmod", "ɵɵdefineNgModule", "exports", "ɵinj", "ɵɵdefineInjector"], "sources": ["C:/projetos/scope-ai/frontend/node_modules/primeng/fesm2022/primeng-button.mjs"], "sourcesContent": ["import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, inject, Directive, contentChild, computed, booleanAttribute, Input, EventEmitter, numberAttribute, ContentChildren, ContentChild, Output, ViewEncapsulation, ChangeDetectionStrategy, Component, NgModule } from '@angular/core';\nimport { addClass, isEmpty, findSingle } from '@primeuix/utils';\nimport { SharedModule, PrimeTemplate } from 'primeng/api';\nimport { AutoFocus } from 'primeng/autofocus';\nimport * as i2 from 'primeng/badge';\nimport { BadgeModule } from 'primeng/badge';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { SpinnerIcon } from 'primeng/icons';\nimport { Ripple } from 'primeng/ripple';\nimport { BaseStyle } from 'primeng/base';\n\nconst theme = ({ dt }) => `\n.p-button {\n    display: inline-flex;\n    cursor: pointer;\n    user-select: none;\n    align-items: center;\n    justify-content: center;\n    overflow: hidden;\n    position: relative;\n    color: ${dt('button.primary.color')};\n    background: ${dt('button.primary.background')};\n    border: 1px solid ${dt('button.primary.border.color')};\n    padding-block: ${dt('button.padding.y')};\n    padding-inline: ${dt('button.padding.x')};\n    font-size: 1rem;\n    font-family: inherit;\n    font-feature-settings: inherit;\n    transition: background ${dt('button.transition.duration')}, color ${dt('button.transition.duration')}, border-color ${dt('button.transition.duration')},\n            outline-color ${dt('button.transition.duration')}, box-shadow ${dt('button.transition.duration')};\n    border-radius: ${dt('button.border.radius')};\n    outline-color: transparent;\n    gap: ${dt('button.gap')};\n}\n\n.p-button-icon,\n.p-button-icon:before,\n.p-button-icon:after {\n    line-height: inherit;\n}\n\n.p-button:disabled {\n    cursor: default;\n}\n\n.p-button-icon-right {\n    order: 1;\n}\n\n.p-button-icon-right:dir(rtl) {\n    order: -1;\n}\n\n.p-button:not(.p-button-vertical) .p-button-icon:not(.p-button-icon-right):dir(rtl) {\n    order: 1;\n}\n\n.p-button-icon-bottom {\n    order: 2;\n}\n\n.p-button-icon-only {\n    width: ${dt('button.icon.only.width')};\n    padding-inline-start: 0;\n    padding-inline-end: 0;\n    gap: 0;\n}\n\n.p-button-icon-only.p-button-rounded {\n    border-radius: 50%;\n    height: ${dt('button.icon.only.width')};\n}\n\n.p-button-icon-only .p-button-label {\n    visibility: hidden;\n    width: 0;\n}\n\n.p-button-sm {\n    font-size: ${dt('button.sm.font.size')};\n    padding-block: ${dt('button.sm.padding.y')};\n    padding-inline: ${dt('button.sm.padding.x')};\n}\n\n.p-button-sm .p-button-icon {\n    font-size: ${dt('button.sm.font.size')};\n}\n\n.p-button-sm.p-button-icon-only {\n    width: ${dt('button.sm.icon.only.width')};\n}\n\n.p-button-sm.p-button-icon-only.p-button-rounded {\n    height: ${dt('button.sm.icon.only.width')};\n}\n\n.p-button-lg {\n    font-size: ${dt('button.lg.font.size')};\n    padding-block: ${dt('button.lg.padding.y')};\n    padding-inline: ${dt('button.lg.padding.x')};\n}\n\n.p-button-lg .p-button-icon {\n    font-size: ${dt('button.lg.font.size')};\n}\n\n.p-button-lg.p-button-icon-only {\n    width: ${dt('button.lg.icon.only.width')};\n}\n\n.p-button-lg.p-button-icon-only.p-button-rounded {\n    height: ${dt('button.lg.icon.only.width')};\n}\n\n.p-button-vertical {\n    flex-direction: column;\n}\n\n.p-button-label {\n    font-weight: ${dt('button.label.font.weight')};\n}\n\n.p-button-fluid {\n    width: 100%;\n}\n\n.p-button-fluid.p-button-icon-only {\n    width: ${dt('button.icon.only.width')};\n}\n\n.p-button:not(:disabled):hover {\n    background: ${dt('button.primary.hover.background')};\n    border: 1px solid ${dt('button.primary.hover.border.color')};\n    color: ${dt('button.primary.hover.color')};\n}\n\n.p-button:not(:disabled):active {\n    background: ${dt('button.primary.active.background')};\n    border: 1px solid ${dt('button.primary.active.border.color')};\n    color: ${dt('button.primary.active.color')};\n}\n\n.p-button:focus-visible {\n    box-shadow: ${dt('button.primary.focus.ring.shadow')};\n    outline: ${dt('button.focus.ring.width')} ${dt('button.focus.ring.style')} ${dt('button.primary.focus.ring.color')};\n    outline-offset: ${dt('button.focus.ring.offset')};\n}\n\n.p-button .p-badge {\n    min-width: ${dt('button.badge.size')};\n    height: ${dt('button.badge.size')};\n    line-height: ${dt('button.badge.size')};\n}\n\n.p-button-raised {\n    box-shadow: ${dt('button.raised.shadow')};\n}\n\n.p-button-rounded {\n    border-radius: ${dt('button.rounded.border.radius')};\n}\n\n.p-button-secondary {\n    background: ${dt('button.secondary.background')};\n    border: 1px solid ${dt('button.secondary.border.color')};\n    color: ${dt('button.secondary.color')};\n}\n\n.p-button-secondary:not(:disabled):hover {\n    background: ${dt('button.secondary.hover.background')};\n    border: 1px solid ${dt('button.secondary.hover.border.color')};\n    color: ${dt('button.secondary.hover.color')};\n}\n\n.p-button-secondary:not(:disabled):active {\n    background: ${dt('button.secondary.active.background')};\n    border: 1px solid ${dt('button.secondary.active.border.color')};\n    color: ${dt('button.secondary.active.color')};\n}\n\n.p-button-secondary:focus-visible {\n    outline-color: ${dt('button.secondary.focus.ring.color')};\n    box-shadow: ${dt('button.secondary.focus.ring.shadow')};\n}\n\n.p-button-success {\n    background: ${dt('button.success.background')};\n    border: 1px solid ${dt('button.success.border.color')};\n    color: ${dt('button.success.color')};\n}\n\n.p-button-success:not(:disabled):hover {\n    background: ${dt('button.success.hover.background')};\n    border: 1px solid ${dt('button.success.hover.border.color')};\n    color: ${dt('button.success.hover.color')};\n}\n\n.p-button-success:not(:disabled):active {\n    background: ${dt('button.success.active.background')};\n    border: 1px solid ${dt('button.success.active.border.color')};\n    color: ${dt('button.success.active.color')};\n}\n\n.p-button-success:focus-visible {\n    outline-color: ${dt('button.success.focus.ring.color')};\n    box-shadow: ${dt('button.success.focus.ring.shadow')};\n}\n\n.p-button-info {\n    background: ${dt('button.info.background')};\n    border: 1px solid ${dt('button.info.border.color')};\n    color: ${dt('button.info.color')};\n}\n\n.p-button-info:not(:disabled):hover {\n    background: ${dt('button.info.hover.background')};\n    border: 1px solid ${dt('button.info.hover.border.color')};\n    color: ${dt('button.info.hover.color')};\n}\n\n.p-button-info:not(:disabled):active {\n    background: ${dt('button.info.active.background')};\n    border: 1px solid ${dt('button.info.active.border.color')};\n    color: ${dt('button.info.active.color')};\n}\n\n.p-button-info:focus-visible {\n    outline-color: ${dt('button.info.focus.ring.color')};\n    box-shadow: ${dt('button.info.focus.ring.shadow')};\n}\n\n.p-button-warn {\n    background: ${dt('button.warn.background')};\n    border: 1px solid ${dt('button.warn.border.color')};\n    color: ${dt('button.warn.color')};\n}\n\n.p-button-warn:not(:disabled):hover {\n    background: ${dt('button.warn.hover.background')};\n    border: 1px solid ${dt('button.warn.hover.border.color')};\n    color: ${dt('button.warn.hover.color')};\n}\n\n.p-button-warn:not(:disabled):active {\n    background: ${dt('button.warn.active.background')};\n    border: 1px solid ${dt('button.warn.active.border.color')};\n    color: ${dt('button.warn.active.color')};\n}\n\n.p-button-warn:focus-visible {\n    outline-color: ${dt('button.warn.focus.ring.color')};\n    box-shadow: ${dt('button.warn.focus.ring.shadow')};\n}\n\n.p-button-help {\n    background: ${dt('button.help.background')};\n    border: 1px solid ${dt('button.help.border.color')};\n    color: ${dt('button.help.color')};\n}\n\n.p-button-help:not(:disabled):hover {\n    background: ${dt('button.help.hover.background')};\n    border: 1px solid ${dt('button.help.hover.border.color')};\n    color: ${dt('button.help.hover.color')};\n}\n\n.p-button-help:not(:disabled):active {\n    background: ${dt('button.help.active.background')};\n    border: 1px solid ${dt('button.help.active.border.color')};\n    color: ${dt('button.help.active.color')};\n}\n\n.p-button-help:focus-visible {\n    outline-color: ${dt('button.help.focus.ring.color')};\n    box-shadow: ${dt('button.help.focus.ring.shadow')};\n}\n\n.p-button-danger {\n    background: ${dt('button.danger.background')};\n    border: 1px solid ${dt('button.danger.border.color')};\n    color: ${dt('button.danger.color')};\n}\n\n.p-button-danger:not(:disabled):hover {\n    background: ${dt('button.danger.hover.background')};\n    border: 1px solid ${dt('button.danger.hover.border.color')};\n    color: ${dt('button.danger.hover.color')};\n}\n\n.p-button-danger:not(:disabled):active {\n    background: ${dt('button.danger.active.background')};\n    border: 1px solid ${dt('button.danger.active.border.color')};\n    color: ${dt('button.danger.active.color')};\n}\n\n.p-button-danger:focus-visible {\n    outline-color: ${dt('button.danger.focus.ring.color')};\n    box-shadow: ${dt('button.danger.focus.ring.shadow')};\n}\n\n.p-button-contrast {\n    background: ${dt('button.contrast.background')};\n    border: 1px solid ${dt('button.contrast.border.color')};\n    color: ${dt('button.contrast.color')};\n}\n\n.p-button-contrast:not(:disabled):hover {\n    background: ${dt('button.contrast.hover.background')};\n    border: 1px solid ${dt('button.contrast.hover.border.color')};\n    color: ${dt('button.contrast.hover.color')};\n}\n\n.p-button-contrast:not(:disabled):active {\n    background: ${dt('button.contrast.active.background')};\n    border: 1px solid ${dt('button.contrast.active.border.color')};\n    color: ${dt('button.contrast.active.color')};\n}\n\n.p-button-contrast:focus-visible {\n    outline-color: ${dt('button.contrast.focus.ring.color')};\n    box-shadow: ${dt('button.contrast.focus.ring.shadow')};\n}\n\n.p-button-outlined {\n    background: transparent;\n    border-color: ${dt('button.outlined.primary.border.color')};\n    color: ${dt('button.outlined.primary.color')};\n}\n\n.p-button-outlined:not(:disabled):hover {\n    background: ${dt('button.outlined.primary.hover.background')};\n    border-color: ${dt('button.outlined.primary.border.color')};\n    color: ${dt('button.outlined.primary.color')};\n}\n\n.p-button-outlined:not(:disabled):active {\n    background: ${dt('button.outlined.primary.active.background')};\n    border-color: ${dt('button.outlined.primary.border.color')};\n    color: ${dt('button.outlined.primary.color')};\n}\n\n.p-button-outlined.p-button-secondary {\n    border-color: ${dt('button.outlined.secondary.border.color')};\n    color: ${dt('button.outlined.secondary.color')};\n}\n\n.p-button-outlined.p-button-secondary:not(:disabled):hover {\n    background: ${dt('button.outlined.secondary.hover.background')};\n    border-color: ${dt('button.outlined.secondary.border.color')};\n    color: ${dt('button.outlined.secondary.color')};\n}\n\n.p-button-outlined.p-button-secondary:not(:disabled):active {\n    background: ${dt('button.outlined.secondary.active.background')};\n    border-color: ${dt('button.outlined.secondary.border.color')};\n    color: ${dt('button.outlined.secondary.color')};\n}\n\n.p-button-outlined.p-button-success {\n    border-color: ${dt('button.outlined.success.border.color')};\n    color: ${dt('button.outlined.success.color')};\n}\n\n.p-button-outlined.p-button-success:not(:disabled):hover {\n    background: ${dt('button.outlined.success.hover.background')};\n    border-color: ${dt('button.outlined.success.border.color')};\n    color: ${dt('button.outlined.success.color')};\n}\n\n.p-button-outlined.p-button-success:not(:disabled):active {\n    background: ${dt('button.outlined.success.active.background')};\n    border-color: ${dt('button.outlined.success.border.color')};\n    color: ${dt('button.outlined.success.color')};\n}\n\n.p-button-outlined.p-button-info {\n    border-color: ${dt('button.outlined.info.border.color')};\n    color: ${dt('button.outlined.info.color')};\n}\n\n.p-button-outlined.p-button-info:not(:disabled):hover {\n    background: ${dt('button.outlined.info.hover.background')};\n    border-color: ${dt('button.outlined.info.border.color')};\n    color: ${dt('button.outlined.info.color')};\n}\n\n.p-button-outlined.p-button-info:not(:disabled):active {\n    background: ${dt('button.outlined.info.active.background')};\n    border-color: ${dt('button.outlined.info.border.color')};\n    color: ${dt('button.outlined.info.color')};\n}\n\n.p-button-outlined.p-button-warn {\n    border-color: ${dt('button.outlined.warn.border.color')};\n    color: ${dt('button.outlined.warn.color')};\n}\n\n.p-button-outlined.p-button-warn:not(:disabled):hover {\n    background: ${dt('button.outlined.warn.hover.background')};\n    border-color: ${dt('button.outlined.warn.border.color')};\n    color: ${dt('button.outlined.warn.color')};\n}\n\n.p-button-outlined.p-button-warn:not(:disabled):active {\n    background: ${dt('button.outlined.warn.active.background')};\n    border-color: ${dt('button.outlined.warn.border.color')};\n    color: ${dt('button.outlined.warn.color')};\n}\n\n.p-button-outlined.p-button-help {\n    border-color: ${dt('button.outlined.help.border.color')};\n    color: ${dt('button.outlined.help.color')};\n}\n\n.p-button-outlined.p-button-help:not(:disabled):hover {\n    background: ${dt('button.outlined.help.hover.background')};\n    border-color: ${dt('button.outlined.help.border.color')};\n    color: ${dt('button.outlined.help.color')};\n}\n\n.p-button-outlined.p-button-help:not(:disabled):active {\n    background: ${dt('button.outlined.help.active.background')};\n    border-color: ${dt('button.outlined.help.border.color')};\n    color: ${dt('button.outlined.help.color')};\n}\n\n.p-button-outlined.p-button-danger {\n    border-color: ${dt('button.outlined.danger.border.color')};\n    color: ${dt('button.outlined.danger.color')};\n}\n\n.p-button-outlined.p-button-danger:not(:disabled):hover {\n    background: ${dt('button.outlined.danger.hover.background')};\n    border-color: ${dt('button.outlined.danger.border.color')};\n    color: ${dt('button.outlined.danger.color')};\n}\n\n.p-button-outlined.p-button-danger:not(:disabled):active {\n    background: ${dt('button.outlined.danger.active.background')};\n    border-color: ${dt('button.outlined.danger.border.color')};\n    color: ${dt('button.outlined.danger.color')};\n}\n\n.p-button-outlined.p-button-contrast {\n    border-color: ${dt('button.outlined.contrast.border.color')};\n    color: ${dt('button.outlined.contrast.color')};\n}\n\n.p-button-outlined.p-button-contrast:not(:disabled):hover {\n    background: ${dt('button.outlined.contrast.hover.background')};\n    border-color: ${dt('button.outlined.contrast.border.color')};\n    color: ${dt('button.outlined.contrast.color')};\n}\n\n.p-button-outlined.p-button-contrast:not(:disabled):active {\n    background: ${dt('button.outlined.contrast.active.background')};\n    border-color: ${dt('button.outlined.contrast.border.color')};\n    color: ${dt('button.outlined.contrast.color')};\n}\n\n.p-button-outlined.p-button-plain {\n    border-color: ${dt('button.outlined.plain.border.color')};\n    color: ${dt('button.outlined.plain.color')};\n}\n\n.p-button-outlined.p-button-plain:not(:disabled):hover {\n    background: ${dt('button.outlined.plain.hover.background')};\n    border-color: ${dt('button.outlined.plain.border.color')};\n    color: ${dt('button.outlined.plain.color')};\n}\n\n.p-button-outlined.p-button-plain:not(:disabled):active {\n    background: ${dt('button.outlined.plain.active.background')};\n    border-color: ${dt('button.outlined.plain.border.color')};\n    color: ${dt('button.outlined.plain.color')};\n}\n\n.p-button-text {\n    background: transparent;\n    border-color: transparent;\n    color: ${dt('button.text.primary.color')};\n}\n\n.p-button-text:not(:disabled):hover {\n    background: ${dt('button.text.primary.hover.background')};\n    border-color: transparent;\n    color: ${dt('button.text.primary.color')};\n}\n\n.p-button-text:not(:disabled):active {\n    background: ${dt('button.text.primary.active.background')};\n    border-color: transparent;\n    color: ${dt('button.text.primary.color')};\n}\n\n.p-button-text.p-button-secondary {\n    background: transparent;\n    border-color: transparent;\n    color: ${dt('button.text.secondary.color')};\n}\n\n.p-button-text.p-button-secondary:not(:disabled):hover {\n    background: ${dt('button.text.secondary.hover.background')};\n    border-color: transparent;\n    color: ${dt('button.text.secondary.color')};\n}\n\n.p-button-text.p-button-secondary:not(:disabled):active {\n    background: ${dt('button.text.secondary.active.background')};\n    border-color: transparent;\n    color: ${dt('button.text.secondary.color')};\n}\n\n.p-button-text.p-button-success {\n    background: transparent;\n    border-color: transparent;\n    color: ${dt('button.text.success.color')};\n}\n\n.p-button-text.p-button-success:not(:disabled):hover {\n    background: ${dt('button.text.success.hover.background')};\n    border-color: transparent;\n    color: ${dt('button.text.success.color')};\n}\n\n.p-button-text.p-button-success:not(:disabled):active {\n    background: ${dt('button.text.success.active.background')};\n    border-color: transparent;\n    color: ${dt('button.text.success.color')};\n}\n\n.p-button-text.p-button-info {\n    background: transparent;\n    border-color: transparent;\n    color: ${dt('button.text.info.color')};\n}\n\n.p-button-text.p-button-info:not(:disabled):hover {\n    background: ${dt('button.text.info.hover.background')};\n    border-color: transparent;\n    color: ${dt('button.text.info.color')};\n}\n\n.p-button-text.p-button-info:not(:disabled):active {\n    background: ${dt('button.text.info.active.background')};\n    border-color: transparent;\n    color: ${dt('button.text.info.color')};\n}\n\n.p-button-text.p-button-warn {\n    background: transparent;\n    border-color: transparent;\n    color: ${dt('button.text.warn.color')};\n}\n\n.p-button-text.p-button-warn:not(:disabled):hover {\n    background: ${dt('button.text.warn.hover.background')};\n    border-color: transparent;\n    color: ${dt('button.text.warn.color')};\n}\n\n.p-button-text.p-button-warn:not(:disabled):active {\n    background: ${dt('button.text.warn.active.background')};\n    border-color: transparent;\n    color: ${dt('button.text.warn.color')};\n}\n\n.p-button-text.p-button-help {\n    background: transparent;\n    border-color: transparent;\n    color: ${dt('button.text.help.color')};\n}\n\n.p-button-text.p-button-help:not(:disabled):hover {\n    background: ${dt('button.text.help.hover.background')};\n    border-color: transparent;\n    color: ${dt('button.text.help.color')};\n}\n\n.p-button-text.p-button-help:not(:disabled):active {\n    background: ${dt('button.text.help.active.background')};\n    border-color: transparent;\n    color: ${dt('button.text.help.color')};\n}\n\n.p-button-text.p-button-danger {\n    background: transparent;\n    border-color: transparent;\n    color: ${dt('button.text.danger.color')};\n}\n\n.p-button-text.p-button-danger:not(:disabled):hover {\n    background: ${dt('button.text.danger.hover.background')};\n    border-color: transparent;\n    color: ${dt('button.text.danger.color')};\n}\n\n.p-button-text.p-button-danger:not(:disabled):active {\n    background: ${dt('button.text.danger.active.background')};\n    border-color: transparent;\n    color: ${dt('button.text.danger.color')};\n}\n\n.p-button-text.p-button-plain {\n    background: transparent;\n    border-color: transparent;\n    color: ${dt('button.text.plain.color')};\n}\n\n.p-button-text.p-button-plain:not(:disabled):hover {\n    background: ${dt('button.text.plain.hover.background')};\n    border-color: transparent;\n    color: ${dt('button.text.plain.color')};\n}\n\n.p-button-text.p-button-plain:not(:disabled):active {\n    background: ${dt('button.text.plain.active.background')};\n    border-color: transparent;\n    color: ${dt('button.text.plain.color')};\n}\n\n.p-button-text.p-button-contrast {\n    background: transparent;\n    border-color: transparent;\n    color: ${dt('button.text.contrast.color')};\n}\n\n.p-button-text.p-button-contrast:not(:disabled):hover {\n    background: ${dt('button.text.contrast.hover.background')};\n    border-color: transparent;\n    color: ${dt('button.text.contrast.color')};\n}\n\n.p-button-text.p-button-contrast:not(:disabled):active {\n    background: ${dt('button.text.contrast.active.background')};\n    border-color: transparent;\n    color: ${dt('button.text.contrast.color')};\n}\n\n.p-button-link {\n    background: transparent;\n    border-color: transparent;\n    color: ${dt('button.link.color')};\n}\n\n.p-button-link:not(:disabled):hover {\n    background: transparent;\n    border-color: transparent;\n    color: ${dt('button.link.hover.color')};\n}\n\n.p-button-link:not(:disabled):hover .p-button-label {\n    text-decoration: underline;\n}\n\n.p-button-link:not(:disabled):active {\n    background: transparent;\n    border-color: transparent;\n    color: ${dt('button.link.active.color')};\n}\n\n/* For PrimeNG */\n.p-button-icon-right {\n    order: 1;\n}\n\np-button[iconpos='right'] spinnericon {\n    order: 1;\n}\n`;\nconst classes = {\n    root: ({ instance, props }) => [\n        'p-button p-component',\n        {\n            'p-button-icon-only': instance.hasIcon && !props.label && !props.badge,\n            'p-button-vertical': (props.iconPos === 'top' || props.iconPos === 'bottom') && props.label,\n            'p-button-loading': props.loading,\n            'p-button-link': props.link,\n            [`p-button-${props.severity}`]: props.severity,\n            'p-button-raised': props.raised,\n            'p-button-rounded': props.rounded,\n            'p-button-text': props.text,\n            'p-button-outlined': props.outlined,\n            'p-button-sm': props.size === 'small',\n            'p-button-lg': props.size === 'large',\n            'p-button-plain': props.plain,\n            'p-button-fluid': props.fluid\n        }\n    ],\n    loadingIcon: 'p-button-loading-icon',\n    icon: ({ props }) => [\n        'p-button-icon',\n        {\n            [`p-button-icon-${props.iconPos}`]: props.label\n        }\n    ],\n    label: 'p-button-label'\n};\nclass ButtonStyle extends BaseStyle {\n    name = 'button';\n    theme = theme;\n    classes = classes;\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: ButtonStyle, deps: null, target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: ButtonStyle });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: ButtonStyle, decorators: [{\n            type: Injectable\n        }] });\n/**\n *\n * Button is an extension to standard button element with icons and theming.\n *\n * [Live Demo](https://www.primeng.org/button/)\n *\n * @module buttonstyle\n *\n */\nvar ButtonClasses;\n(function (ButtonClasses) {\n    /**\n     * Class name of the root element\n     */\n    ButtonClasses[\"root\"] = \"p-button\";\n    /**\n     * Class name of the loading icon element\n     */\n    ButtonClasses[\"loadingIcon\"] = \"p-button-loading-icon\";\n    /**\n     * Class name of the icon element\n     */\n    ButtonClasses[\"icon\"] = \"p-button-icon\";\n    /**\n     * Class name of the label element\n     */\n    ButtonClasses[\"label\"] = \"p-button-label\";\n})(ButtonClasses || (ButtonClasses = {}));\n\nconst INTERNAL_BUTTON_CLASSES = {\n    button: 'p-button',\n    component: 'p-component',\n    iconOnly: 'p-button-icon-only',\n    disabled: 'p-disabled',\n    loading: 'p-button-loading',\n    labelOnly: 'p-button-loading-label-only'\n};\nclass ButtonLabel extends BaseComponent {\n    _componentStyle = inject(ButtonStyle);\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: ButtonLabel, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.10\", type: ButtonLabel, isStandalone: true, selector: \"[pButtonLabel]\", host: { properties: { \"class.p-button-label\": \"true\" } }, providers: [ButtonStyle], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: ButtonLabel, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[pButtonLabel]',\n                    providers: [ButtonStyle],\n                    standalone: true,\n                    host: {\n                        '[class.p-button-label]': 'true'\n                    }\n                }]\n        }] });\nclass ButtonIcon extends BaseComponent {\n    _componentStyle = inject(ButtonStyle);\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: ButtonIcon, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.10\", type: ButtonIcon, isStandalone: true, selector: \"[pButtonIcon]\", host: { properties: { \"class.p-button-icon\": \"true\" } }, providers: [ButtonStyle], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: ButtonIcon, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[pButtonIcon]',\n                    providers: [ButtonStyle],\n                    standalone: true,\n                    host: {\n                        '[class.p-button-icon]': 'true'\n                    }\n                }]\n        }] });\n/**\n * Button directive is an extension to button component.\n * @group Components\n */\nclass ButtonDirective extends BaseComponent {\n    /**\n     * Position of the icon.\n     * @deprecated utilize pButtonIcon and pButtonLabel directives.\n     * @group Props\n     */\n    iconPos = 'left';\n    /**\n     * Uses to pass attributes to the loading icon's DOM element.\n     * @deprecated utilize pButonIcon instead.\n     * @group Props\n     */\n    loadingIcon;\n    set label(val) {\n        this._label = val;\n        if (this.initialized) {\n            this.updateLabel();\n            this.updateIcon();\n            this.setStyleClass();\n        }\n    }\n    set icon(val) {\n        this._icon = val;\n        if (this.initialized) {\n            this.updateIcon();\n            this.setStyleClass();\n        }\n    }\n    /**\n     * Whether the button is in loading state.\n     * @group Props\n     */\n    get loading() {\n        return this._loading;\n    }\n    set loading(val) {\n        this._loading = val;\n        if (this.initialized) {\n            this.updateIcon();\n            this.setStyleClass();\n        }\n    }\n    _buttonProps;\n    iconSignal = contentChild(ButtonIcon);\n    labelSignal = contentChild(ButtonLabel);\n    isIconOnly = computed(() => !!(!this.labelSignal() && this.iconSignal()));\n    set buttonProps(val) {\n        this._buttonProps = val;\n        if (val && typeof val === 'object') {\n            //@ts-ignore\n            Object.entries(val).forEach(([k, v]) => this[`_${k}`] !== v && (this[`_${k}`] = v));\n        }\n    }\n    _severity;\n    /**\n     * Defines the style of the button.\n     * @group Props\n     */\n    get severity() {\n        return this._severity;\n    }\n    set severity(value) {\n        this._severity = value;\n        if (this.initialized) {\n            this.setStyleClass();\n        }\n    }\n    /**\n     * Add a shadow to indicate elevation.\n     * @group Props\n     */\n    raised = false;\n    /**\n     * Add a circular border radius to the button.\n     * @group Props\n     */\n    rounded = false;\n    /**\n     * Add a textual class to the button without a background initially.\n     * @group Props\n     */\n    text = false;\n    /**\n     * Add a border class without a background initially.\n     * @group Props\n     */\n    outlined = false;\n    /**\n     * Defines the size of the button.\n     * @group Props\n     */\n    size = null;\n    /**\n     * Add a plain textual class to the button without a background initially.\n     * @deprecated use variant property instead.\n     * @group Props\n     */\n    plain = false;\n    /**\n     * Spans 100% width of the container when enabled.\n     * @group Props\n     */\n    fluid;\n    _label;\n    _icon;\n    _loading = false;\n    initialized;\n    get htmlElement() {\n        return this.el.nativeElement;\n    }\n    _internalClasses = Object.values(INTERNAL_BUTTON_CLASSES);\n    isTextButton = computed(() => !!(!this.iconSignal() && this.labelSignal() && this.text));\n    /**\n     * Text of the button.\n     * @deprecated use pButtonLabel directive instead.\n     * @group Props\n     */\n    get label() {\n        return this._label;\n    }\n    /**\n     * Name of the icon.\n     * @deprecated use pButtonIcon directive instead\n     * @group Props\n     */\n    get icon() {\n        return this._icon;\n    }\n    /**\n     * Used to pass all properties of the ButtonProps to the Button component.\n     * @deprecated assign props directly to the button element.\n     * @group Props\n     */\n    get buttonProps() {\n        return this._buttonProps;\n    }\n    spinnerIcon = `<svg width=\"14\" height=\"14\" viewBox=\"0 0 14 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" class=\"p-icon-spin\">\n        <g clip-path=\"url(#clip0_417_21408)\">\n            <path\n                d=\"M6.99701 14C5.85441 13.999 4.72939 13.7186 3.72012 13.1832C2.71084 12.6478 1.84795 11.8737 1.20673 10.9284C0.565504 9.98305 0.165424 8.89526 0.041387 7.75989C-0.0826496 6.62453 0.073125 5.47607 0.495122 4.4147C0.917119 3.35333 1.59252 2.4113 2.46241 1.67077C3.33229 0.930247 4.37024 0.413729 5.4857 0.166275C6.60117 -0.0811796 7.76026 -0.0520535 8.86188 0.251112C9.9635 0.554278 10.9742 1.12227 11.8057 1.90555C11.915 2.01493 11.9764 2.16319 11.9764 2.31778C11.9764 2.47236 11.915 2.62062 11.8057 2.73C11.7521 2.78503 11.688 2.82877 11.6171 2.85864C11.5463 2.8885 11.4702 2.90389 11.3933 2.90389C11.3165 2.90389 11.2404 2.8885 11.1695 2.85864C11.0987 2.82877 11.0346 2.78503 10.9809 2.73C9.9998 1.81273 8.73246 1.26138 7.39226 1.16876C6.05206 1.07615 4.72086 1.44794 3.62279 2.22152C2.52471 2.99511 1.72683 4.12325 1.36345 5.41602C1.00008 6.70879 1.09342 8.08723 1.62775 9.31926C2.16209 10.5513 3.10478 11.5617 4.29713 12.1803C5.48947 12.7989 6.85865 12.988 8.17414 12.7157C9.48963 12.4435 10.6711 11.7264 11.5196 10.6854C12.3681 9.64432 12.8319 8.34282 12.8328 7C12.8328 6.84529 12.8943 6.69692 13.0038 6.58752C13.1132 6.47812 13.2616 6.41667 13.4164 6.41667C13.5712 6.41667 13.7196 6.47812 13.8291 6.58752C13.9385 6.69692 14 6.84529 14 7C14 8.85651 13.2622 10.637 11.9489 11.9497C10.6356 13.2625 8.85432 14 6.99701 14Z\"\n                fill=\"currentColor\"\n            />\n        </g>\n        <defs>\n            <clipPath id=\"clip0_417_21408\">\n                <rect width=\"14\" height=\"14\" fill=\"white\" />\n            </clipPath>\n        </defs>\n    </svg>`;\n    _componentStyle = inject(ButtonStyle);\n    ngAfterViewInit() {\n        super.ngAfterViewInit();\n        addClass(this.htmlElement, this.getStyleClass().join(' '));\n        this.createIcon();\n        this.createLabel();\n        this.initialized = true;\n    }\n    ngOnChanges(simpleChanges) {\n        super.ngOnChanges(simpleChanges);\n        const { buttonProps } = simpleChanges;\n        if (buttonProps) {\n            const props = buttonProps.currentValue;\n            for (const property in props) {\n                this[property] = props[property];\n            }\n        }\n    }\n    getStyleClass() {\n        const styleClass = [INTERNAL_BUTTON_CLASSES.button, INTERNAL_BUTTON_CLASSES.component];\n        if (this.icon && !this.label && isEmpty(this.htmlElement.textContent)) {\n            styleClass.push(INTERNAL_BUTTON_CLASSES.iconOnly);\n        }\n        if (this.loading) {\n            styleClass.push(INTERNAL_BUTTON_CLASSES.disabled, INTERNAL_BUTTON_CLASSES.loading);\n            if (!this.icon && this.label) {\n                styleClass.push(INTERNAL_BUTTON_CLASSES.labelOnly);\n            }\n            if (this.icon && !this.label && !isEmpty(this.htmlElement.textContent)) {\n                styleClass.push(INTERNAL_BUTTON_CLASSES.iconOnly);\n            }\n        }\n        if (this.text) {\n            styleClass.push('p-button-text');\n        }\n        if (this.severity) {\n            styleClass.push(`p-button-${this.severity}`);\n        }\n        if (this.plain) {\n            styleClass.push('p-button-plain');\n        }\n        if (this.raised) {\n            styleClass.push('p-button-raised');\n        }\n        if (this.size) {\n            styleClass.push(`p-button-${this.size}`);\n        }\n        if (this.outlined) {\n            styleClass.push('p-button-outlined');\n        }\n        if (this.rounded) {\n            styleClass.push('p-button-rounded');\n        }\n        if (this.size === 'small') {\n            styleClass.push('p-button-sm');\n        }\n        if (this.size === 'large') {\n            styleClass.push('p-button-lg');\n        }\n        if (this.hasFluid) {\n            styleClass.push('p-button-fluid');\n        }\n        return styleClass;\n    }\n    get hasFluid() {\n        const nativeElement = this.el.nativeElement;\n        const fluidComponent = nativeElement.closest('p-fluid');\n        return isEmpty(this.fluid) ? !!fluidComponent : this.fluid;\n    }\n    setStyleClass() {\n        const styleClass = this.getStyleClass();\n        this.removeExistingSeverityClass();\n        this.htmlElement.classList.remove(...this._internalClasses);\n        this.htmlElement.classList.add(...styleClass);\n    }\n    removeExistingSeverityClass() {\n        const severityArray = ['success', 'info', 'warn', 'danger', 'help', 'primary', 'secondary', 'contrast'];\n        const existingSeverityClass = this.htmlElement.classList.value.split(' ').find((cls) => severityArray.some((severity) => cls === `p-button-${severity}`));\n        if (existingSeverityClass) {\n            this.htmlElement.classList.remove(existingSeverityClass);\n        }\n    }\n    createLabel() {\n        const created = findSingle(this.htmlElement, '.p-button-label');\n        if (!created && this.label) {\n            let labelElement = this.document.createElement('span');\n            if (this.icon && !this.label) {\n                labelElement.setAttribute('aria-hidden', 'true');\n            }\n            labelElement.className = 'p-button-label';\n            labelElement.appendChild(this.document.createTextNode(this.label));\n            this.htmlElement.appendChild(labelElement);\n        }\n    }\n    createIcon() {\n        const created = findSingle(this.htmlElement, '.p-button-icon');\n        if (!created && (this.icon || this.loading)) {\n            let iconElement = this.document.createElement('span');\n            iconElement.className = 'p-button-icon';\n            iconElement.setAttribute('aria-hidden', 'true');\n            let iconPosClass = this.label ? 'p-button-icon-' + this.iconPos : null;\n            if (iconPosClass) {\n                addClass(iconElement, iconPosClass);\n            }\n            let iconClass = this.getIconClass();\n            if (iconClass) {\n                addClass(iconElement, iconClass);\n            }\n            if (!this.loadingIcon && this.loading) {\n                iconElement.innerHTML = this.spinnerIcon;\n            }\n            this.htmlElement.insertBefore(iconElement, this.htmlElement.firstChild);\n        }\n    }\n    updateLabel() {\n        let labelElement = findSingle(this.htmlElement, '.p-button-label');\n        if (!this.label) {\n            labelElement && this.htmlElement.removeChild(labelElement);\n            return;\n        }\n        labelElement ? (labelElement.textContent = this.label) : this.createLabel();\n    }\n    updateIcon() {\n        let iconElement = findSingle(this.htmlElement, '.p-button-icon');\n        let labelElement = findSingle(this.htmlElement, '.p-button-label');\n        if (this.loading && !this.loadingIcon && iconElement) {\n            iconElement.innerHTML = this.spinnerIcon;\n        }\n        else if (iconElement?.innerHTML) {\n            iconElement.innerHTML = '';\n        }\n        if (iconElement) {\n            if (this.iconPos) {\n                iconElement.className = 'p-button-icon ' + (labelElement ? 'p-button-icon-' + this.iconPos : '') + ' ' + this.getIconClass();\n            }\n            else {\n                iconElement.className = 'p-button-icon ' + this.getIconClass();\n            }\n        }\n        else {\n            this.createIcon();\n        }\n    }\n    getIconClass() {\n        return this.loading ? 'p-button-loading-icon ' + (this.loadingIcon ? this.loadingIcon : 'p-icon') : this.icon || 'p-hidden';\n    }\n    ngOnDestroy() {\n        this.initialized = false;\n        super.ngOnDestroy();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: ButtonDirective, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"17.2.0\", version: \"19.2.10\", type: ButtonDirective, isStandalone: true, selector: \"[pButton]\", inputs: { iconPos: \"iconPos\", loadingIcon: \"loadingIcon\", loading: \"loading\", severity: \"severity\", raised: [\"raised\", \"raised\", booleanAttribute], rounded: [\"rounded\", \"rounded\", booleanAttribute], text: [\"text\", \"text\", booleanAttribute], outlined: [\"outlined\", \"outlined\", booleanAttribute], size: \"size\", plain: [\"plain\", \"plain\", booleanAttribute], fluid: [\"fluid\", \"fluid\", booleanAttribute], label: \"label\", icon: \"icon\", buttonProps: \"buttonProps\" }, host: { properties: { \"class.p-button-icon-only\": \"isIconOnly()\", \"class.p-button-text\": \"isTextButton()\" } }, providers: [ButtonStyle], queries: [{ propertyName: \"iconSignal\", first: true, predicate: ButtonIcon, descendants: true, isSignal: true }, { propertyName: \"labelSignal\", first: true, predicate: ButtonLabel, descendants: true, isSignal: true }], usesInheritance: true, usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: ButtonDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[pButton]',\n                    standalone: true,\n                    providers: [ButtonStyle],\n                    host: {\n                        '[class.p-button-icon-only]': 'isIconOnly()',\n                        '[class.p-button-text]': 'isTextButton()'\n                    }\n                }]\n        }], propDecorators: { iconPos: [{\n                type: Input\n            }], loadingIcon: [{\n                type: Input\n            }], loading: [{\n                type: Input\n            }], severity: [{\n                type: Input\n            }], raised: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], rounded: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], text: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], outlined: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], size: [{\n                type: Input\n            }], plain: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], fluid: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], label: [{\n                type: Input\n            }], icon: [{\n                type: Input\n            }], buttonProps: [{\n                type: Input\n            }] } });\n/**\n * Button is an extension to standard button element with icons and theming.\n * @group Components\n */\nclass Button extends BaseComponent {\n    /**\n     * Type of the button.\n     * @group Props\n     */\n    type = 'button';\n    /**\n     * Position of the icon.\n     * @group Props\n     */\n    iconPos = 'left';\n    /**\n     * Name of the icon.\n     * @group Props\n     */\n    icon;\n    /**\n     * Value of the badge.\n     * @group Props\n     */\n    badge;\n    /**\n     * Uses to pass attributes to the label's DOM element.\n     * @group Props\n     */\n    label;\n    /**\n     * When present, it specifies that the component should be disabled.\n     * @group Props\n     */\n    disabled;\n    /**\n     * Whether the button is in loading state.\n     * @group Props\n     */\n    loading = false;\n    /**\n     * Icon to display in loading state.\n     * @group Props\n     */\n    loadingIcon;\n    /**\n     * Add a shadow to indicate elevation.\n     * @group Props\n     */\n    raised = false;\n    /**\n     * Add a circular border radius to the button.\n     * @group Props\n     */\n    rounded = false;\n    /**\n     * Add a textual class to the button without a background initially.\n     * @group Props\n     */\n    text = false;\n    /**\n     * Add a plain textual class to the button without a background initially.\n     * @deprecated use variant property instead.\n     * @group Props\n     */\n    plain = false;\n    /**\n     * Defines the style of the button.\n     * @group Props\n     */\n    severity;\n    /**\n     * Add a border class without a background initially.\n     * @group Props\n     */\n    outlined = false;\n    /**\n     * Add a link style to the button.\n     * @group Props\n     */\n    link = false;\n    /**\n     * Add a tabindex to the button.\n     * @group Props\n     */\n    tabindex;\n    /**\n     * Defines the size of the button.\n     * @group Props\n     */\n    size;\n    /**\n     * Specifies the variant of the component.\n     * @group Props\n     */\n    variant;\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    style;\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Style class of the badge.\n     * @group Props\n     * @deprecated use badgeSeverity instead.\n     */\n    badgeClass;\n    /**\n     * Severity type of the badge.\n     * @group Props\n     * @defaultValue secondary\n     */\n    badgeSeverity = 'secondary';\n    /**\n     * Used to define a string that autocomplete attribute the current element.\n     * @group Props\n     */\n    ariaLabel;\n    /**\n     * When present, it specifies that the component should automatically get focus on load.\n     * @group Props\n     */\n    autofocus;\n    /**\n     * Spans 100% width of the container when enabled.\n     * @group Props\n     */\n    fluid;\n    /**\n     * Callback to execute when button is clicked.\n     * This event is intended to be used with the <p-button> component. Using a regular <button> element, use (click).\n     * @param {MouseEvent} event - Mouse event.\n     * @group Emits\n     */\n    onClick = new EventEmitter();\n    /**\n     * Callback to execute when button is focused.\n     * This event is intended to be used with the <p-button> component. Using a regular <button> element, use (focus).\n     * @param {FocusEvent} event - Focus event.\n     * @group Emits\n     */\n    onFocus = new EventEmitter();\n    /**\n     * Callback to execute when button loses focus.\n     * This event is intended to be used with the <p-button> component. Using a regular <button> element, use (blur).\n     * @param {FocusEvent} event - Focus event.\n     * @group Emits\n     */\n    onBlur = new EventEmitter();\n    /**\n     * Template of the content.\n     * @group Templates\n     **/\n    contentTemplate;\n    /**\n     * Template of the loading.\n     * @group Templates\n     **/\n    loadingIconTemplate;\n    /**\n     * Template of the icon.\n     * @group Templates\n     **/\n    iconTemplate;\n    _buttonProps;\n    /**\n     * Used to pass all properties of the ButtonProps to the Button component.\n     * @group Props\n     */\n    get buttonProps() {\n        return this._buttonProps;\n    }\n    set buttonProps(val) {\n        this._buttonProps = val;\n        if (val && typeof val === 'object') {\n            //@ts-ignore\n            Object.entries(val).forEach(([k, v]) => this[`_${k}`] !== v && (this[`_${k}`] = v));\n        }\n    }\n    get hasFluid() {\n        const nativeElement = this.el.nativeElement;\n        const fluidComponent = nativeElement.closest('p-fluid');\n        return isEmpty(this.fluid) ? !!fluidComponent : this.fluid;\n    }\n    _componentStyle = inject(ButtonStyle);\n    templates;\n    _contentTemplate;\n    _iconTemplate;\n    _loadingIconTemplate;\n    ngAfterContentInit() {\n        this.templates?.forEach((item) => {\n            switch (item.getType()) {\n                case 'content':\n                    this._contentTemplate = item.template;\n                    break;\n                case 'icon':\n                    this._iconTemplate = item.template;\n                    break;\n                case 'loadingicon':\n                    this._loadingIconTemplate = item.template;\n                    break;\n                default:\n                    this._contentTemplate = item.template;\n                    break;\n            }\n        });\n    }\n    ngOnChanges(simpleChanges) {\n        super.ngOnChanges(simpleChanges);\n        const { buttonProps } = simpleChanges;\n        if (buttonProps) {\n            const props = buttonProps.currentValue;\n            for (const property in props) {\n                this[property] = props[property];\n            }\n        }\n    }\n    spinnerIconClass() {\n        return Object.entries(this.iconClass())\n            .filter(([, value]) => !!value)\n            .reduce((acc, [key]) => acc + ` ${key}`, 'p-button-loading-icon');\n    }\n    iconClass() {\n        return {\n            [`p-button-loading-icon pi-spin ${this.loadingIcon ?? ''}`]: this.loading,\n            'p-button-icon': true,\n            'p-button-icon-left': this.iconPos === 'left' && this.label,\n            'p-button-icon-right': this.iconPos === 'right' && this.label,\n            'p-button-icon-top': this.iconPos === 'top' && this.label,\n            'p-button-icon-bottom': this.iconPos === 'bottom' && this.label\n        };\n    }\n    get buttonClass() {\n        return {\n            'p-button p-component': true,\n            'p-button-icon-only': (this.icon || this.iconTemplate || this._iconTemplate || this.loadingIcon || this.loadingIconTemplate || this._loadingIconTemplate) && !this.label,\n            'p-button-vertical': (this.iconPos === 'top' || this.iconPos === 'bottom') && this.label,\n            'p-button-loading': this.loading,\n            'p-button-loading-label-only': this.loading && !this.icon && this.label && !this.loadingIcon && this.iconPos === 'left',\n            'p-button-link': this.link,\n            [`p-button-${this.severity}`]: this.severity,\n            'p-button-raised': this.raised,\n            'p-button-rounded': this.rounded,\n            'p-button-text': this.text || this.variant == 'text',\n            'p-button-outlined': this.outlined || this.variant == 'outlined',\n            'p-button-sm': this.size === 'small',\n            'p-button-lg': this.size === 'large',\n            'p-button-plain': this.plain,\n            'p-button-fluid': this.hasFluid,\n            [`${this.styleClass}`]: this.styleClass\n        };\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: Button, deps: null, target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"19.2.10\", type: Button, isStandalone: true, selector: \"p-button\", inputs: { type: \"type\", iconPos: \"iconPos\", icon: \"icon\", badge: \"badge\", label: \"label\", disabled: [\"disabled\", \"disabled\", booleanAttribute], loading: [\"loading\", \"loading\", booleanAttribute], loadingIcon: \"loadingIcon\", raised: [\"raised\", \"raised\", booleanAttribute], rounded: [\"rounded\", \"rounded\", booleanAttribute], text: [\"text\", \"text\", booleanAttribute], plain: [\"plain\", \"plain\", booleanAttribute], severity: \"severity\", outlined: [\"outlined\", \"outlined\", booleanAttribute], link: [\"link\", \"link\", booleanAttribute], tabindex: [\"tabindex\", \"tabindex\", numberAttribute], size: \"size\", variant: \"variant\", style: \"style\", styleClass: \"styleClass\", badgeClass: \"badgeClass\", badgeSeverity: \"badgeSeverity\", ariaLabel: \"ariaLabel\", autofocus: [\"autofocus\", \"autofocus\", booleanAttribute], fluid: [\"fluid\", \"fluid\", booleanAttribute], buttonProps: \"buttonProps\" }, outputs: { onClick: \"onClick\", onFocus: \"onFocus\", onBlur: \"onBlur\" }, providers: [ButtonStyle], queries: [{ propertyName: \"contentTemplate\", first: true, predicate: [\"content\"], descendants: true }, { propertyName: \"loadingIconTemplate\", first: true, predicate: [\"loadingicon\"], descendants: true }, { propertyName: \"iconTemplate\", first: true, predicate: [\"icon\"], descendants: true }, { propertyName: \"templates\", predicate: PrimeTemplate }], usesInheritance: true, usesOnChanges: true, ngImport: i0, template: `\n        <button\n            [attr.type]=\"type\"\n            [attr.aria-label]=\"ariaLabel\"\n            [ngStyle]=\"style\"\n            [disabled]=\"disabled || loading\"\n            [ngClass]=\"buttonClass\"\n            (click)=\"onClick.emit($event)\"\n            (focus)=\"onFocus.emit($event)\"\n            (blur)=\"onBlur.emit($event)\"\n            pRipple\n            [attr.data-pc-name]=\"'button'\"\n            [attr.data-pc-section]=\"'root'\"\n            [attr.tabindex]=\"tabindex\"\n            [pAutoFocus]=\"autofocus\"\n        >\n            <ng-content></ng-content>\n            <ng-container *ngTemplateOutlet=\"contentTemplate || _contentTemplate\"></ng-container>\n            <ng-container *ngIf=\"loading\">\n                <ng-container *ngIf=\"!loadingIconTemplate && !_loadingIconTemplate\">\n                    <span *ngIf=\"loadingIcon\" [ngClass]=\"iconClass()\" [attr.aria-hidden]=\"true\" [attr.data-pc-section]=\"'loadingicon'\"></span>\n                    <SpinnerIcon *ngIf=\"!loadingIcon\" [styleClass]=\"spinnerIconClass()\" [spin]=\"true\" [attr.aria-hidden]=\"true\" [attr.data-pc-section]=\"'loadingicon'\" />\n                </ng-container>\n                <ng-template [ngIf]=\"loadingIconTemplate || _loadingIconTemplate\" *ngTemplateOutlet=\"loadingIconTemplate || _loadingIconTemplate; context: { class: iconClass() }\"></ng-template>\n            </ng-container>\n            <ng-container *ngIf=\"!loading\">\n                <span *ngIf=\"icon && !iconTemplate && !_iconTemplate\" [class]=\"icon\" [ngClass]=\"iconClass()\" [attr.data-pc-section]=\"'icon'\"></span>\n                <ng-template [ngIf]=\"!icon && (iconTemplate || _iconTemplate)\" *ngTemplateOutlet=\"iconTemplate || _iconTemplate; context: { class: iconClass() }\"></ng-template>\n            </ng-container>\n            <span class=\"p-button-label\" [attr.aria-hidden]=\"icon && !label\" *ngIf=\"!contentTemplate && !_contentTemplate && label\" [attr.data-pc-section]=\"'label'\">{{ label }}</span>\n            <p-badge *ngIf=\"!contentTemplate && !_contentTemplate && badge\" [value]=\"badge\" [severity]=\"badgeSeverity\"></p-badge>\n        </button>\n    `, isInline: true, dependencies: [{ kind: \"ngmodule\", type: CommonModule }, { kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i1.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: Ripple, selector: \"[pRipple]\" }, { kind: \"directive\", type: AutoFocus, selector: \"[pAutoFocus]\", inputs: [\"autofocus\", \"pAutoFocus\"] }, { kind: \"component\", type: SpinnerIcon, selector: \"SpinnerIcon\" }, { kind: \"ngmodule\", type: BadgeModule }, { kind: \"component\", type: i2.Badge, selector: \"p-badge\", inputs: [\"styleClass\", \"style\", \"badgeSize\", \"size\", \"severity\", \"value\", \"badgeDisabled\"] }, { kind: \"ngmodule\", type: SharedModule }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: Button, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'p-button',\n                    standalone: true,\n                    imports: [CommonModule, Ripple, AutoFocus, SpinnerIcon, BadgeModule, SharedModule],\n                    template: `\n        <button\n            [attr.type]=\"type\"\n            [attr.aria-label]=\"ariaLabel\"\n            [ngStyle]=\"style\"\n            [disabled]=\"disabled || loading\"\n            [ngClass]=\"buttonClass\"\n            (click)=\"onClick.emit($event)\"\n            (focus)=\"onFocus.emit($event)\"\n            (blur)=\"onBlur.emit($event)\"\n            pRipple\n            [attr.data-pc-name]=\"'button'\"\n            [attr.data-pc-section]=\"'root'\"\n            [attr.tabindex]=\"tabindex\"\n            [pAutoFocus]=\"autofocus\"\n        >\n            <ng-content></ng-content>\n            <ng-container *ngTemplateOutlet=\"contentTemplate || _contentTemplate\"></ng-container>\n            <ng-container *ngIf=\"loading\">\n                <ng-container *ngIf=\"!loadingIconTemplate && !_loadingIconTemplate\">\n                    <span *ngIf=\"loadingIcon\" [ngClass]=\"iconClass()\" [attr.aria-hidden]=\"true\" [attr.data-pc-section]=\"'loadingicon'\"></span>\n                    <SpinnerIcon *ngIf=\"!loadingIcon\" [styleClass]=\"spinnerIconClass()\" [spin]=\"true\" [attr.aria-hidden]=\"true\" [attr.data-pc-section]=\"'loadingicon'\" />\n                </ng-container>\n                <ng-template [ngIf]=\"loadingIconTemplate || _loadingIconTemplate\" *ngTemplateOutlet=\"loadingIconTemplate || _loadingIconTemplate; context: { class: iconClass() }\"></ng-template>\n            </ng-container>\n            <ng-container *ngIf=\"!loading\">\n                <span *ngIf=\"icon && !iconTemplate && !_iconTemplate\" [class]=\"icon\" [ngClass]=\"iconClass()\" [attr.data-pc-section]=\"'icon'\"></span>\n                <ng-template [ngIf]=\"!icon && (iconTemplate || _iconTemplate)\" *ngTemplateOutlet=\"iconTemplate || _iconTemplate; context: { class: iconClass() }\"></ng-template>\n            </ng-container>\n            <span class=\"p-button-label\" [attr.aria-hidden]=\"icon && !label\" *ngIf=\"!contentTemplate && !_contentTemplate && label\" [attr.data-pc-section]=\"'label'\">{{ label }}</span>\n            <p-badge *ngIf=\"!contentTemplate && !_contentTemplate && badge\" [value]=\"badge\" [severity]=\"badgeSeverity\"></p-badge>\n        </button>\n    `,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    providers: [ButtonStyle]\n                }]\n        }], propDecorators: { type: [{\n                type: Input\n            }], iconPos: [{\n                type: Input\n            }], icon: [{\n                type: Input\n            }], badge: [{\n                type: Input\n            }], label: [{\n                type: Input\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], loading: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], loadingIcon: [{\n                type: Input\n            }], raised: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], rounded: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], text: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], plain: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], severity: [{\n                type: Input\n            }], outlined: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], link: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], tabindex: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], size: [{\n                type: Input\n            }], variant: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], badgeClass: [{\n                type: Input\n            }], badgeSeverity: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input\n            }], autofocus: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], fluid: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], onClick: [{\n                type: Output\n            }], onFocus: [{\n                type: Output\n            }], onBlur: [{\n                type: Output\n            }], contentTemplate: [{\n                type: ContentChild,\n                args: ['content']\n            }], loadingIconTemplate: [{\n                type: ContentChild,\n                args: ['loadingicon']\n            }], iconTemplate: [{\n                type: ContentChild,\n                args: ['icon']\n            }], buttonProps: [{\n                type: Input\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }] } });\nclass ButtonModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: ButtonModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.10\", ngImport: i0, type: ButtonModule, imports: [CommonModule, ButtonDirective, Button, SharedModule, ButtonLabel, ButtonIcon], exports: [ButtonDirective, Button, ButtonLabel, ButtonIcon, SharedModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: ButtonModule, imports: [CommonModule, Button, SharedModule, SharedModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: ButtonModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, ButtonDirective, Button, SharedModule, ButtonLabel, ButtonIcon],\n                    exports: [ButtonDirective, Button, ButtonLabel, ButtonIcon, SharedModule]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Button, ButtonClasses, ButtonDirective, ButtonIcon, ButtonLabel, ButtonModule, ButtonStyle };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,MAAM,EAAEC,SAAS,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,gBAAgB,EAAEC,KAAK,EAAEC,YAAY,EAAEC,eAAe,EAAEC,eAAe,EAAEC,YAAY,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AACrP,SAASC,QAAQ,EAAEC,OAAO,EAAEC,UAAU,QAAQ,iBAAiB;AAC/D,SAASC,YAAY,EAAEC,aAAa,QAAQ,aAAa;AACzD,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,SAAS,QAAQ,cAAc;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAC,EAAA;EAAAC,KAAA,EAAAD;AAAA;AAAA,SAAAE,+BAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAqrBqDrC,EAAE,CAAAuC,kBAAA,EA8rBA,CAAC;EAAA;AAAA;AAAA,SAAAC,qDAAAH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9rBHrC,EAAE,CAAAyC,SAAA,aAisB6C,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,MAAA,GAjsBhD1C,EAAE,CAAA2C,aAAA;IAAF3C,EAAE,CAAA4C,UAAA,YAAAF,MAAA,CAAAG,SAAA,EAisB5B,CAAC;IAjsByB7C,EAAE,CAAA8C,WAAA;EAAA;AAAA;AAAA,SAAAC,4DAAAV,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFrC,EAAE,CAAAyC,SAAA,oBAksBwE,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,MAAA,GAlsB3E1C,EAAE,CAAA2C,aAAA;IAAF3C,EAAE,CAAA4C,UAAA,eAAAF,MAAA,CAAAM,gBAAA,EAksBV,CAAC,aAAa,CAAC;IAlsBPhD,EAAE,CAAA8C,WAAA;EAAA;AAAA;AAAA,SAAAG,8CAAAZ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFrC,EAAE,CAAAkD,uBAAA,EAgsBb,CAAC;IAhsBUlD,EAAE,CAAAmD,UAAA,IAAAX,oDAAA,iBAisBsC,CAAC,IAAAO,2DAAA,wBACiC,CAAC;IAlsB3E/C,EAAE,CAAAoD,qBAAA;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAK,MAAA,GAAF1C,EAAE,CAAA2C,aAAA;IAAF3C,EAAE,CAAAqD,SAAA,CAisBrD,CAAC;IAjsBkDrD,EAAE,CAAA4C,UAAA,SAAAF,MAAA,CAAAY,WAisBrD,CAAC;IAjsBkDtD,EAAE,CAAAqD,SAAA,CAksB7C,CAAC;IAlsB0CrD,EAAE,CAAA4C,UAAA,UAAAF,MAAA,CAAAY,WAksB7C,CAAC;EAAA;AAAA;AAAA,SAAAC,+CAAAlB,EAAA,EAAAC,GAAA;AAAA,SAAAkB,iCAAAnB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlsB0CrC,EAAE,CAAAmD,UAAA,IAAAI,8CAAA,yBAosBkF,CAAC;EAAA;EAAA,IAAAlB,EAAA;IAAA,MAAAK,MAAA,GApsBrF1C,EAAE,CAAA2C,aAAA;IAAF3C,EAAE,CAAA4C,UAAA,SAAAF,MAAA,CAAAe,mBAAA,IAAAf,MAAA,CAAAgB,oBAosBhB,CAAC;EAAA;AAAA;AAAA,SAAAC,+BAAAtB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IApsBarC,EAAE,CAAAkD,uBAAA,EA+rBvD,CAAC;IA/rBoDlD,EAAE,CAAAmD,UAAA,IAAAF,6CAAA,yBAgsBb,CAAC,IAAAO,gCAAA,eAI8F,CAAC;IApsBrFxD,EAAE,CAAAoD,qBAAA;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAK,MAAA,GAAF1C,EAAE,CAAA2C,aAAA;IAAF3C,EAAE,CAAAqD,SAAA,CAgsBf,CAAC;IAhsBYrD,EAAE,CAAA4C,UAAA,UAAAF,MAAA,CAAAe,mBAAA,KAAAf,MAAA,CAAAgB,oBAgsBf,CAAC;IAhsBY1D,EAAE,CAAAqD,SAAA,CAosBiD,CAAC;IApsBpDrD,EAAE,CAAA4C,UAAA,qBAAAF,MAAA,CAAAe,mBAAA,IAAAf,MAAA,CAAAgB,oBAosBiD,CAAC,4BApsBpD1D,EAAE,CAAA4D,eAAA,IAAA3B,GAAA,EAAAS,MAAA,CAAAG,SAAA,GAosBgF,CAAC;EAAA;AAAA;AAAA,SAAAgB,sCAAAxB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IApsBnFrC,EAAE,CAAAyC,SAAA,aAusBmD,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,MAAA,GAvsBtD1C,EAAE,CAAA2C,aAAA;IAAF3C,EAAE,CAAA8D,UAAA,CAAApB,MAAA,CAAAqB,IAusBb,CAAC;IAvsBU/D,EAAE,CAAA4C,UAAA,YAAAF,MAAA,CAAAG,SAAA,EAusBW,CAAC;IAvsBd7C,EAAE,CAAA8C,WAAA;EAAA;AAAA;AAAA,SAAAkB,+CAAA3B,EAAA,EAAAC,GAAA;AAAA,SAAA2B,iCAAA5B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFrC,EAAE,CAAAmD,UAAA,IAAAa,8CAAA,yBAwsBiE,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAAK,MAAA,GAxsBpE1C,EAAE,CAAA2C,aAAA;IAAF3C,EAAE,CAAA4C,UAAA,UAAAF,MAAA,CAAAqB,IAAA,KAAArB,MAAA,CAAAwB,YAAA,IAAAxB,MAAA,CAAAyB,aAAA,CAwsBnB,CAAC;EAAA;AAAA;AAAA,SAAAC,+BAAA/B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAxsBgBrC,EAAE,CAAAkD,uBAAA,EAssBtD,CAAC;IAtsBmDlD,EAAE,CAAAmD,UAAA,IAAAU,qCAAA,kBAusB4C,CAAC,IAAAI,gCAAA,eACoB,CAAC;IAxsBpEjE,EAAE,CAAAoD,qBAAA;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAK,MAAA,GAAF1C,EAAE,CAAA2C,aAAA;IAAF3C,EAAE,CAAAqD,SAAA,CAusB7B,CAAC;IAvsB0BrD,EAAE,CAAA4C,UAAA,SAAAF,MAAA,CAAAqB,IAAA,KAAArB,MAAA,CAAAwB,YAAA,KAAAxB,MAAA,CAAAyB,aAusB7B,CAAC;IAvsB0BnE,EAAE,CAAAqD,SAAA,CAwsBgC,CAAC;IAxsBnCrD,EAAE,CAAA4C,UAAA,qBAAAF,MAAA,CAAAwB,YAAA,IAAAxB,MAAA,CAAAyB,aAwsBgC,CAAC,4BAxsBnCnE,EAAE,CAAA4D,eAAA,IAAA3B,GAAA,EAAAS,MAAA,CAAAG,SAAA,GAwsB+D,CAAC;EAAA;AAAA;AAAA,SAAAwB,uBAAAhC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAxsBlErC,EAAE,CAAAsE,cAAA,cA0sBoE,CAAC;IA1sBvEtE,EAAE,CAAAuE,MAAA,EA0sB+E,CAAC;IA1sBlFvE,EAAE,CAAAwE,YAAA,CA0sBsF,CAAC;EAAA;EAAA,IAAAnC,EAAA;IAAA,MAAAK,MAAA,GA1sBzF1C,EAAE,CAAA2C,aAAA;IAAF3C,EAAE,CAAA8C,WAAA,gBAAAJ,MAAA,CAAAqB,IAAA,KAAArB,MAAA,CAAA+B,KAAA;IAAFzE,EAAE,CAAAqD,SAAA,CA0sB+E,CAAC;IA1sBlFrD,EAAE,CAAA0E,iBAAA,CAAAhC,MAAA,CAAA+B,KA0sB+E,CAAC;EAAA;AAAA;AAAA,SAAAE,0BAAAtC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA1sBlFrC,EAAE,CAAAyC,SAAA,iBA2sBgC,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,MAAA,GA3sBnC1C,EAAE,CAAA2C,aAAA;IAAF3C,EAAE,CAAA4C,UAAA,UAAAF,MAAA,CAAAkC,KA2sBN,CAAC,aAAAlC,MAAA,CAAAmC,aAA0B,CAAC;EAAA;AAAA;AA93CtH,MAAMC,KAAK,GAAGA,CAAC;EAAEC;AAAG,CAAC,KAAK;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAaA,EAAE,CAAC,sBAAsB,CAAC;AACvC,kBAAkBA,EAAE,CAAC,2BAA2B,CAAC;AACjD,wBAAwBA,EAAE,CAAC,6BAA6B,CAAC;AACzD,qBAAqBA,EAAE,CAAC,kBAAkB,CAAC;AAC3C,sBAAsBA,EAAE,CAAC,kBAAkB,CAAC;AAC5C;AACA;AACA;AACA,6BAA6BA,EAAE,CAAC,4BAA4B,CAAC,WAAWA,EAAE,CAAC,4BAA4B,CAAC,kBAAkBA,EAAE,CAAC,4BAA4B,CAAC;AAC1J,4BAA4BA,EAAE,CAAC,4BAA4B,CAAC,gBAAgBA,EAAE,CAAC,4BAA4B,CAAC;AAC5G,qBAAqBA,EAAE,CAAC,sBAAsB,CAAC;AAC/C;AACA,WAAWA,EAAE,CAAC,YAAY,CAAC;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAaA,EAAE,CAAC,wBAAwB,CAAC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAcA,EAAE,CAAC,wBAAwB,CAAC;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiBA,EAAE,CAAC,qBAAqB,CAAC;AAC1C,qBAAqBA,EAAE,CAAC,qBAAqB,CAAC;AAC9C,sBAAsBA,EAAE,CAAC,qBAAqB,CAAC;AAC/C;AACA;AACA;AACA,iBAAiBA,EAAE,CAAC,qBAAqB,CAAC;AAC1C;AACA;AACA;AACA,aAAaA,EAAE,CAAC,2BAA2B,CAAC;AAC5C;AACA;AACA;AACA,cAAcA,EAAE,CAAC,2BAA2B,CAAC;AAC7C;AACA;AACA;AACA,iBAAiBA,EAAE,CAAC,qBAAqB,CAAC;AAC1C,qBAAqBA,EAAE,CAAC,qBAAqB,CAAC;AAC9C,sBAAsBA,EAAE,CAAC,qBAAqB,CAAC;AAC/C;AACA;AACA;AACA,iBAAiBA,EAAE,CAAC,qBAAqB,CAAC;AAC1C;AACA;AACA;AACA,aAAaA,EAAE,CAAC,2BAA2B,CAAC;AAC5C;AACA;AACA;AACA,cAAcA,EAAE,CAAC,2BAA2B,CAAC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmBA,EAAE,CAAC,0BAA0B,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAaA,EAAE,CAAC,wBAAwB,CAAC;AACzC;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,iCAAiC,CAAC;AACvD,wBAAwBA,EAAE,CAAC,mCAAmC,CAAC;AAC/D,aAAaA,EAAE,CAAC,4BAA4B,CAAC;AAC7C;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,kCAAkC,CAAC;AACxD,wBAAwBA,EAAE,CAAC,oCAAoC,CAAC;AAChE,aAAaA,EAAE,CAAC,6BAA6B,CAAC;AAC9C;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,kCAAkC,CAAC;AACxD,eAAeA,EAAE,CAAC,yBAAyB,CAAC,IAAIA,EAAE,CAAC,yBAAyB,CAAC,IAAIA,EAAE,CAAC,iCAAiC,CAAC;AACtH,sBAAsBA,EAAE,CAAC,0BAA0B,CAAC;AACpD;AACA;AACA;AACA,iBAAiBA,EAAE,CAAC,mBAAmB,CAAC;AACxC,cAAcA,EAAE,CAAC,mBAAmB,CAAC;AACrC,mBAAmBA,EAAE,CAAC,mBAAmB,CAAC;AAC1C;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,sBAAsB,CAAC;AAC5C;AACA;AACA;AACA,qBAAqBA,EAAE,CAAC,8BAA8B,CAAC;AACvD;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,6BAA6B,CAAC;AACnD,wBAAwBA,EAAE,CAAC,+BAA+B,CAAC;AAC3D,aAAaA,EAAE,CAAC,wBAAwB,CAAC;AACzC;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,mCAAmC,CAAC;AACzD,wBAAwBA,EAAE,CAAC,qCAAqC,CAAC;AACjE,aAAaA,EAAE,CAAC,8BAA8B,CAAC;AAC/C;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,oCAAoC,CAAC;AAC1D,wBAAwBA,EAAE,CAAC,sCAAsC,CAAC;AAClE,aAAaA,EAAE,CAAC,+BAA+B,CAAC;AAChD;AACA;AACA;AACA,qBAAqBA,EAAE,CAAC,mCAAmC,CAAC;AAC5D,kBAAkBA,EAAE,CAAC,oCAAoC,CAAC;AAC1D;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,2BAA2B,CAAC;AACjD,wBAAwBA,EAAE,CAAC,6BAA6B,CAAC;AACzD,aAAaA,EAAE,CAAC,sBAAsB,CAAC;AACvC;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,iCAAiC,CAAC;AACvD,wBAAwBA,EAAE,CAAC,mCAAmC,CAAC;AAC/D,aAAaA,EAAE,CAAC,4BAA4B,CAAC;AAC7C;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,kCAAkC,CAAC;AACxD,wBAAwBA,EAAE,CAAC,oCAAoC,CAAC;AAChE,aAAaA,EAAE,CAAC,6BAA6B,CAAC;AAC9C;AACA;AACA;AACA,qBAAqBA,EAAE,CAAC,iCAAiC,CAAC;AAC1D,kBAAkBA,EAAE,CAAC,kCAAkC,CAAC;AACxD;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,wBAAwB,CAAC;AAC9C,wBAAwBA,EAAE,CAAC,0BAA0B,CAAC;AACtD,aAAaA,EAAE,CAAC,mBAAmB,CAAC;AACpC;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,8BAA8B,CAAC;AACpD,wBAAwBA,EAAE,CAAC,gCAAgC,CAAC;AAC5D,aAAaA,EAAE,CAAC,yBAAyB,CAAC;AAC1C;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,+BAA+B,CAAC;AACrD,wBAAwBA,EAAE,CAAC,iCAAiC,CAAC;AAC7D,aAAaA,EAAE,CAAC,0BAA0B,CAAC;AAC3C;AACA;AACA;AACA,qBAAqBA,EAAE,CAAC,8BAA8B,CAAC;AACvD,kBAAkBA,EAAE,CAAC,+BAA+B,CAAC;AACrD;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,wBAAwB,CAAC;AAC9C,wBAAwBA,EAAE,CAAC,0BAA0B,CAAC;AACtD,aAAaA,EAAE,CAAC,mBAAmB,CAAC;AACpC;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,8BAA8B,CAAC;AACpD,wBAAwBA,EAAE,CAAC,gCAAgC,CAAC;AAC5D,aAAaA,EAAE,CAAC,yBAAyB,CAAC;AAC1C;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,+BAA+B,CAAC;AACrD,wBAAwBA,EAAE,CAAC,iCAAiC,CAAC;AAC7D,aAAaA,EAAE,CAAC,0BAA0B,CAAC;AAC3C;AACA;AACA;AACA,qBAAqBA,EAAE,CAAC,8BAA8B,CAAC;AACvD,kBAAkBA,EAAE,CAAC,+BAA+B,CAAC;AACrD;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,wBAAwB,CAAC;AAC9C,wBAAwBA,EAAE,CAAC,0BAA0B,CAAC;AACtD,aAAaA,EAAE,CAAC,mBAAmB,CAAC;AACpC;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,8BAA8B,CAAC;AACpD,wBAAwBA,EAAE,CAAC,gCAAgC,CAAC;AAC5D,aAAaA,EAAE,CAAC,yBAAyB,CAAC;AAC1C;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,+BAA+B,CAAC;AACrD,wBAAwBA,EAAE,CAAC,iCAAiC,CAAC;AAC7D,aAAaA,EAAE,CAAC,0BAA0B,CAAC;AAC3C;AACA;AACA;AACA,qBAAqBA,EAAE,CAAC,8BAA8B,CAAC;AACvD,kBAAkBA,EAAE,CAAC,+BAA+B,CAAC;AACrD;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,0BAA0B,CAAC;AAChD,wBAAwBA,EAAE,CAAC,4BAA4B,CAAC;AACxD,aAAaA,EAAE,CAAC,qBAAqB,CAAC;AACtC;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,gCAAgC,CAAC;AACtD,wBAAwBA,EAAE,CAAC,kCAAkC,CAAC;AAC9D,aAAaA,EAAE,CAAC,2BAA2B,CAAC;AAC5C;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,iCAAiC,CAAC;AACvD,wBAAwBA,EAAE,CAAC,mCAAmC,CAAC;AAC/D,aAAaA,EAAE,CAAC,4BAA4B,CAAC;AAC7C;AACA;AACA;AACA,qBAAqBA,EAAE,CAAC,gCAAgC,CAAC;AACzD,kBAAkBA,EAAE,CAAC,iCAAiC,CAAC;AACvD;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,4BAA4B,CAAC;AAClD,wBAAwBA,EAAE,CAAC,8BAA8B,CAAC;AAC1D,aAAaA,EAAE,CAAC,uBAAuB,CAAC;AACxC;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,kCAAkC,CAAC;AACxD,wBAAwBA,EAAE,CAAC,oCAAoC,CAAC;AAChE,aAAaA,EAAE,CAAC,6BAA6B,CAAC;AAC9C;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,mCAAmC,CAAC;AACzD,wBAAwBA,EAAE,CAAC,qCAAqC,CAAC;AACjE,aAAaA,EAAE,CAAC,8BAA8B,CAAC;AAC/C;AACA;AACA;AACA,qBAAqBA,EAAE,CAAC,kCAAkC,CAAC;AAC3D,kBAAkBA,EAAE,CAAC,mCAAmC,CAAC;AACzD;AACA;AACA;AACA;AACA,oBAAoBA,EAAE,CAAC,sCAAsC,CAAC;AAC9D,aAAaA,EAAE,CAAC,+BAA+B,CAAC;AAChD;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,0CAA0C,CAAC;AAChE,oBAAoBA,EAAE,CAAC,sCAAsC,CAAC;AAC9D,aAAaA,EAAE,CAAC,+BAA+B,CAAC;AAChD;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,2CAA2C,CAAC;AACjE,oBAAoBA,EAAE,CAAC,sCAAsC,CAAC;AAC9D,aAAaA,EAAE,CAAC,+BAA+B,CAAC;AAChD;AACA;AACA;AACA,oBAAoBA,EAAE,CAAC,wCAAwC,CAAC;AAChE,aAAaA,EAAE,CAAC,iCAAiC,CAAC;AAClD;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,4CAA4C,CAAC;AAClE,oBAAoBA,EAAE,CAAC,wCAAwC,CAAC;AAChE,aAAaA,EAAE,CAAC,iCAAiC,CAAC;AAClD;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,6CAA6C,CAAC;AACnE,oBAAoBA,EAAE,CAAC,wCAAwC,CAAC;AAChE,aAAaA,EAAE,CAAC,iCAAiC,CAAC;AAClD;AACA;AACA;AACA,oBAAoBA,EAAE,CAAC,sCAAsC,CAAC;AAC9D,aAAaA,EAAE,CAAC,+BAA+B,CAAC;AAChD;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,0CAA0C,CAAC;AAChE,oBAAoBA,EAAE,CAAC,sCAAsC,CAAC;AAC9D,aAAaA,EAAE,CAAC,+BAA+B,CAAC;AAChD;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,2CAA2C,CAAC;AACjE,oBAAoBA,EAAE,CAAC,sCAAsC,CAAC;AAC9D,aAAaA,EAAE,CAAC,+BAA+B,CAAC;AAChD;AACA;AACA;AACA,oBAAoBA,EAAE,CAAC,mCAAmC,CAAC;AAC3D,aAAaA,EAAE,CAAC,4BAA4B,CAAC;AAC7C;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,uCAAuC,CAAC;AAC7D,oBAAoBA,EAAE,CAAC,mCAAmC,CAAC;AAC3D,aAAaA,EAAE,CAAC,4BAA4B,CAAC;AAC7C;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,wCAAwC,CAAC;AAC9D,oBAAoBA,EAAE,CAAC,mCAAmC,CAAC;AAC3D,aAAaA,EAAE,CAAC,4BAA4B,CAAC;AAC7C;AACA;AACA;AACA,oBAAoBA,EAAE,CAAC,mCAAmC,CAAC;AAC3D,aAAaA,EAAE,CAAC,4BAA4B,CAAC;AAC7C;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,uCAAuC,CAAC;AAC7D,oBAAoBA,EAAE,CAAC,mCAAmC,CAAC;AAC3D,aAAaA,EAAE,CAAC,4BAA4B,CAAC;AAC7C;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,wCAAwC,CAAC;AAC9D,oBAAoBA,EAAE,CAAC,mCAAmC,CAAC;AAC3D,aAAaA,EAAE,CAAC,4BAA4B,CAAC;AAC7C;AACA;AACA;AACA,oBAAoBA,EAAE,CAAC,mCAAmC,CAAC;AAC3D,aAAaA,EAAE,CAAC,4BAA4B,CAAC;AAC7C;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,uCAAuC,CAAC;AAC7D,oBAAoBA,EAAE,CAAC,mCAAmC,CAAC;AAC3D,aAAaA,EAAE,CAAC,4BAA4B,CAAC;AAC7C;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,wCAAwC,CAAC;AAC9D,oBAAoBA,EAAE,CAAC,mCAAmC,CAAC;AAC3D,aAAaA,EAAE,CAAC,4BAA4B,CAAC;AAC7C;AACA;AACA;AACA,oBAAoBA,EAAE,CAAC,qCAAqC,CAAC;AAC7D,aAAaA,EAAE,CAAC,8BAA8B,CAAC;AAC/C;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,yCAAyC,CAAC;AAC/D,oBAAoBA,EAAE,CAAC,qCAAqC,CAAC;AAC7D,aAAaA,EAAE,CAAC,8BAA8B,CAAC;AAC/C;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,0CAA0C,CAAC;AAChE,oBAAoBA,EAAE,CAAC,qCAAqC,CAAC;AAC7D,aAAaA,EAAE,CAAC,8BAA8B,CAAC;AAC/C;AACA;AACA;AACA,oBAAoBA,EAAE,CAAC,uCAAuC,CAAC;AAC/D,aAAaA,EAAE,CAAC,gCAAgC,CAAC;AACjD;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,2CAA2C,CAAC;AACjE,oBAAoBA,EAAE,CAAC,uCAAuC,CAAC;AAC/D,aAAaA,EAAE,CAAC,gCAAgC,CAAC;AACjD;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,4CAA4C,CAAC;AAClE,oBAAoBA,EAAE,CAAC,uCAAuC,CAAC;AAC/D,aAAaA,EAAE,CAAC,gCAAgC,CAAC;AACjD;AACA;AACA;AACA,oBAAoBA,EAAE,CAAC,oCAAoC,CAAC;AAC5D,aAAaA,EAAE,CAAC,6BAA6B,CAAC;AAC9C;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,wCAAwC,CAAC;AAC9D,oBAAoBA,EAAE,CAAC,oCAAoC,CAAC;AAC5D,aAAaA,EAAE,CAAC,6BAA6B,CAAC;AAC9C;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,yCAAyC,CAAC;AAC/D,oBAAoBA,EAAE,CAAC,oCAAoC,CAAC;AAC5D,aAAaA,EAAE,CAAC,6BAA6B,CAAC;AAC9C;AACA;AACA;AACA;AACA;AACA,aAAaA,EAAE,CAAC,2BAA2B,CAAC;AAC5C;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,sCAAsC,CAAC;AAC5D;AACA,aAAaA,EAAE,CAAC,2BAA2B,CAAC;AAC5C;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,uCAAuC,CAAC;AAC7D;AACA,aAAaA,EAAE,CAAC,2BAA2B,CAAC;AAC5C;AACA;AACA;AACA;AACA;AACA,aAAaA,EAAE,CAAC,6BAA6B,CAAC;AAC9C;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,wCAAwC,CAAC;AAC9D;AACA,aAAaA,EAAE,CAAC,6BAA6B,CAAC;AAC9C;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,yCAAyC,CAAC;AAC/D;AACA,aAAaA,EAAE,CAAC,6BAA6B,CAAC;AAC9C;AACA;AACA;AACA;AACA;AACA,aAAaA,EAAE,CAAC,2BAA2B,CAAC;AAC5C;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,sCAAsC,CAAC;AAC5D;AACA,aAAaA,EAAE,CAAC,2BAA2B,CAAC;AAC5C;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,uCAAuC,CAAC;AAC7D;AACA,aAAaA,EAAE,CAAC,2BAA2B,CAAC;AAC5C;AACA;AACA;AACA;AACA;AACA,aAAaA,EAAE,CAAC,wBAAwB,CAAC;AACzC;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,mCAAmC,CAAC;AACzD;AACA,aAAaA,EAAE,CAAC,wBAAwB,CAAC;AACzC;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,oCAAoC,CAAC;AAC1D;AACA,aAAaA,EAAE,CAAC,wBAAwB,CAAC;AACzC;AACA;AACA;AACA;AACA;AACA,aAAaA,EAAE,CAAC,wBAAwB,CAAC;AACzC;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,mCAAmC,CAAC;AACzD;AACA,aAAaA,EAAE,CAAC,wBAAwB,CAAC;AACzC;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,oCAAoC,CAAC;AAC1D;AACA,aAAaA,EAAE,CAAC,wBAAwB,CAAC;AACzC;AACA;AACA;AACA;AACA;AACA,aAAaA,EAAE,CAAC,wBAAwB,CAAC;AACzC;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,mCAAmC,CAAC;AACzD;AACA,aAAaA,EAAE,CAAC,wBAAwB,CAAC;AACzC;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,oCAAoC,CAAC;AAC1D;AACA,aAAaA,EAAE,CAAC,wBAAwB,CAAC;AACzC;AACA;AACA;AACA;AACA;AACA,aAAaA,EAAE,CAAC,0BAA0B,CAAC;AAC3C;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,qCAAqC,CAAC;AAC3D;AACA,aAAaA,EAAE,CAAC,0BAA0B,CAAC;AAC3C;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,sCAAsC,CAAC;AAC5D;AACA,aAAaA,EAAE,CAAC,0BAA0B,CAAC;AAC3C;AACA;AACA;AACA;AACA;AACA,aAAaA,EAAE,CAAC,yBAAyB,CAAC;AAC1C;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,oCAAoC,CAAC;AAC1D;AACA,aAAaA,EAAE,CAAC,yBAAyB,CAAC;AAC1C;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,qCAAqC,CAAC;AAC3D;AACA,aAAaA,EAAE,CAAC,yBAAyB,CAAC;AAC1C;AACA;AACA;AACA;AACA;AACA,aAAaA,EAAE,CAAC,4BAA4B,CAAC;AAC7C;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,uCAAuC,CAAC;AAC7D;AACA,aAAaA,EAAE,CAAC,4BAA4B,CAAC;AAC7C;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,wCAAwC,CAAC;AAC9D;AACA,aAAaA,EAAE,CAAC,4BAA4B,CAAC;AAC7C;AACA;AACA;AACA;AACA;AACA,aAAaA,EAAE,CAAC,mBAAmB,CAAC;AACpC;AACA;AACA;AACA;AACA;AACA,aAAaA,EAAE,CAAC,yBAAyB,CAAC;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAaA,EAAE,CAAC,0BAA0B,CAAC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,MAAMC,OAAO,GAAG;EACZC,IAAI,EAAEA,CAAC;IAAEC,QAAQ;IAAEC;EAAM,CAAC,KAAK,CAC3B,sBAAsB,EACtB;IACI,oBAAoB,EAAED,QAAQ,CAACE,OAAO,IAAI,CAACD,KAAK,CAACV,KAAK,IAAI,CAACU,KAAK,CAACP,KAAK;IACtE,mBAAmB,EAAE,CAACO,KAAK,CAACE,OAAO,KAAK,KAAK,IAAIF,KAAK,CAACE,OAAO,KAAK,QAAQ,KAAKF,KAAK,CAACV,KAAK;IAC3F,kBAAkB,EAAEU,KAAK,CAACG,OAAO;IACjC,eAAe,EAAEH,KAAK,CAACI,IAAI;IAC3B,CAAC,YAAYJ,KAAK,CAACK,QAAQ,EAAE,GAAGL,KAAK,CAACK,QAAQ;IAC9C,iBAAiB,EAAEL,KAAK,CAACM,MAAM;IAC/B,kBAAkB,EAAEN,KAAK,CAACO,OAAO;IACjC,eAAe,EAAEP,KAAK,CAACQ,IAAI;IAC3B,mBAAmB,EAAER,KAAK,CAACS,QAAQ;IACnC,aAAa,EAAET,KAAK,CAACU,IAAI,KAAK,OAAO;IACrC,aAAa,EAAEV,KAAK,CAACU,IAAI,KAAK,OAAO;IACrC,gBAAgB,EAAEV,KAAK,CAACW,KAAK;IAC7B,gBAAgB,EAAEX,KAAK,CAACY;EAC5B,CAAC,CACJ;EACDzC,WAAW,EAAE,uBAAuB;EACpCS,IAAI,EAAEA,CAAC;IAAEoB;EAAM,CAAC,KAAK,CACjB,eAAe,EACf;IACI,CAAC,iBAAiBA,KAAK,CAACE,OAAO,EAAE,GAAGF,KAAK,CAACV;EAC9C,CAAC,CACJ;EACDA,KAAK,EAAE;AACX,CAAC;AACD,MAAMuB,WAAW,SAASpE,SAAS,CAAC;EAChCqE,IAAI,GAAG,QAAQ;EACfnB,KAAK,GAAGA,KAAK;EACbE,OAAO,GAAGA,OAAO;EACjB,OAAOkB,IAAI;IAAA,IAAAC,wBAAA;IAAA,gBAAAC,oBAAAC,iBAAA;MAAA,QAAAF,wBAAA,KAAAA,wBAAA,GAA+EnG,EAAE,CAAAsG,qBAAA,CAAQN,WAAW,IAAAK,iBAAA,IAAXL,WAAW;IAAA;EAAA;EAC/G,OAAOO,KAAK,kBAD8EvG,EAAE,CAAAwG,kBAAA;IAAAC,KAAA,EACYT,WAAW;IAAAU,OAAA,EAAXV,WAAW,CAAAE;EAAA;AACvH;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KAH8F3G,EAAE,CAAA4G,iBAAA,CAGJZ,WAAW,EAAc,CAAC;IAC1Ga,IAAI,EAAE5G;EACV,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI6G,aAAa;AACjB,CAAC,UAAUA,aAAa,EAAE;EACtB;AACJ;AACA;EACIA,aAAa,CAAC,MAAM,CAAC,GAAG,UAAU;EAClC;AACJ;AACA;EACIA,aAAa,CAAC,aAAa,CAAC,GAAG,uBAAuB;EACtD;AACJ;AACA;EACIA,aAAa,CAAC,MAAM,CAAC,GAAG,eAAe;EACvC;AACJ;AACA;EACIA,aAAa,CAAC,OAAO,CAAC,GAAG,gBAAgB;AAC7C,CAAC,EAAEA,aAAa,KAAKA,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC;AAEzC,MAAMC,uBAAuB,GAAG;EAC5BC,MAAM,EAAE,UAAU;EAClBC,SAAS,EAAE,aAAa;EACxBC,QAAQ,EAAE,oBAAoB;EAC9BC,QAAQ,EAAE,YAAY;EACtB7B,OAAO,EAAE,kBAAkB;EAC3B8B,SAAS,EAAE;AACf,CAAC;AACD,MAAMC,WAAW,SAAS5F,aAAa,CAAC;EACpC6F,eAAe,GAAGpH,MAAM,CAAC8F,WAAW,CAAC;EACrC,OAAOE,IAAI;IAAA,IAAAqB,wBAAA;IAAA,gBAAAC,oBAAAnB,iBAAA;MAAA,QAAAkB,wBAAA,KAAAA,wBAAA,GA7C+EvH,EAAE,CAAAsG,qBAAA,CA6CQe,WAAW,IAAAhB,iBAAA,IAAXgB,WAAW;IAAA;EAAA;EAC/G,OAAOI,IAAI,kBA9C+EzH,EAAE,CAAA0H,iBAAA;IAAAb,IAAA,EA8CJQ,WAAW;IAAAM,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAAC,yBAAAzF,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QA9CTrC,EAAE,CAAA+H,WAAA,mBA8CJ,IAAU,CAAC;MAAA;IAAA;IAAAC,QAAA,GA9CThI,EAAE,CAAAiI,kBAAA,CA8C8H,CAACjC,WAAW,CAAC,GA9C7IhG,EAAE,CAAAkI,0BAAA;EAAA;AA+ChG;AACA;EAAA,QAAAvB,SAAA,oBAAAA,SAAA,KAhD8F3G,EAAE,CAAA4G,iBAAA,CAgDJS,WAAW,EAAc,CAAC;IAC1GR,IAAI,EAAE1G,SAAS;IACfgI,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,gBAAgB;MAC1BC,SAAS,EAAE,CAACrC,WAAW,CAAC;MACxBsC,UAAU,EAAE,IAAI;MAChBC,IAAI,EAAE;QACF,wBAAwB,EAAE;MAC9B;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;AACV,MAAMC,UAAU,SAAS/G,aAAa,CAAC;EACnC6F,eAAe,GAAGpH,MAAM,CAAC8F,WAAW,CAAC;EACrC,OAAOE,IAAI;IAAA,IAAAuC,uBAAA;IAAA,gBAAAC,mBAAArC,iBAAA;MAAA,QAAAoC,uBAAA,KAAAA,uBAAA,GA7D+EzI,EAAE,CAAAsG,qBAAA,CA6DQkC,UAAU,IAAAnC,iBAAA,IAAVmC,UAAU;IAAA;EAAA;EAC9G,OAAOf,IAAI,kBA9D+EzH,EAAE,CAAA0H,iBAAA;IAAAb,IAAA,EA8DJ2B,UAAU;IAAAb,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAAc,wBAAAtG,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QA9DRrC,EAAE,CAAA+H,WAAA,kBA8DJ,IAAS,CAAC;MAAA;IAAA;IAAAC,QAAA,GA9DRhI,EAAE,CAAAiI,kBAAA,CA8D2H,CAACjC,WAAW,CAAC,GA9D1IhG,EAAE,CAAAkI,0BAAA;EAAA;AA+DhG;AACA;EAAA,QAAAvB,SAAA,oBAAAA,SAAA,KAhE8F3G,EAAE,CAAA4G,iBAAA,CAgEJ4B,UAAU,EAAc,CAAC;IACzG3B,IAAI,EAAE1G,SAAS;IACfgI,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,eAAe;MACzBC,SAAS,EAAE,CAACrC,WAAW,CAAC;MACxBsC,UAAU,EAAE,IAAI;MAChBC,IAAI,EAAE;QACF,uBAAuB,EAAE;MAC7B;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA,MAAMK,eAAe,SAASnH,aAAa,CAAC;EACxC;AACJ;AACA;AACA;AACA;EACI4D,OAAO,GAAG,MAAM;EAChB;AACJ;AACA;AACA;AACA;EACI/B,WAAW;EACX,IAAImB,KAAKA,CAACoE,GAAG,EAAE;IACX,IAAI,CAACC,MAAM,GAAGD,GAAG;IACjB,IAAI,IAAI,CAACE,WAAW,EAAE;MAClB,IAAI,CAACC,WAAW,CAAC,CAAC;MAClB,IAAI,CAACC,UAAU,CAAC,CAAC;MACjB,IAAI,CAACC,aAAa,CAAC,CAAC;IACxB;EACJ;EACA,IAAInF,IAAIA,CAAC8E,GAAG,EAAE;IACV,IAAI,CAACM,KAAK,GAAGN,GAAG;IAChB,IAAI,IAAI,CAACE,WAAW,EAAE;MAClB,IAAI,CAACE,UAAU,CAAC,CAAC;MACjB,IAAI,CAACC,aAAa,CAAC,CAAC;IACxB;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAI5D,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC8D,QAAQ;EACxB;EACA,IAAI9D,OAAOA,CAACuD,GAAG,EAAE;IACb,IAAI,CAACO,QAAQ,GAAGP,GAAG;IACnB,IAAI,IAAI,CAACE,WAAW,EAAE;MAClB,IAAI,CAACE,UAAU,CAAC,CAAC;MACjB,IAAI,CAACC,aAAa,CAAC,CAAC;IACxB;EACJ;EACAG,YAAY;EACZC,UAAU,GAAGlJ,YAAY,CAACoI,UAAU,CAAC;EACrCe,WAAW,GAAGnJ,YAAY,CAACiH,WAAW,CAAC;EACvCmC,UAAU,GAAGnJ,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAACkJ,WAAW,CAAC,CAAC,IAAI,IAAI,CAACD,UAAU,CAAC,CAAC,CAAC,CAAC;EACzE,IAAIG,WAAWA,CAACZ,GAAG,EAAE;IACjB,IAAI,CAACQ,YAAY,GAAGR,GAAG;IACvB,IAAIA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MAChC;MACAa,MAAM,CAACC,OAAO,CAACd,GAAG,CAAC,CAACe,OAAO,CAAC,CAAC,CAACC,CAAC,EAAEC,CAAC,CAAC,KAAK,IAAI,CAAC,IAAID,CAAC,EAAE,CAAC,KAAKC,CAAC,KAAK,IAAI,CAAC,IAAID,CAAC,EAAE,CAAC,GAAGC,CAAC,CAAC,CAAC;IACvF;EACJ;EACAC,SAAS;EACT;AACJ;AACA;AACA;EACI,IAAIvE,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACuE,SAAS;EACzB;EACA,IAAIvE,QAAQA,CAACwE,KAAK,EAAE;IAChB,IAAI,CAACD,SAAS,GAAGC,KAAK;IACtB,IAAI,IAAI,CAACjB,WAAW,EAAE;MAClB,IAAI,CAACG,aAAa,CAAC,CAAC;IACxB;EACJ;EACA;AACJ;AACA;AACA;EACIzD,MAAM,GAAG,KAAK;EACd;AACJ;AACA;AACA;EACIC,OAAO,GAAG,KAAK;EACf;AACJ;AACA;AACA;EACIC,IAAI,GAAG,KAAK;EACZ;AACJ;AACA;AACA;EACIC,QAAQ,GAAG,KAAK;EAChB;AACJ;AACA;AACA;EACIC,IAAI,GAAG,IAAI;EACX;AACJ;AACA;AACA;AACA;EACIC,KAAK,GAAG,KAAK;EACb;AACJ;AACA;AACA;EACIC,KAAK;EACL+C,MAAM;EACNK,KAAK;EACLC,QAAQ,GAAG,KAAK;EAChBL,WAAW;EACX,IAAIkB,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,EAAE,CAACC,aAAa;EAChC;EACAC,gBAAgB,GAAGV,MAAM,CAACW,MAAM,CAACtD,uBAAuB,CAAC;EACzDuD,YAAY,GAAGjK,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAACiJ,UAAU,CAAC,CAAC,IAAI,IAAI,CAACC,WAAW,CAAC,CAAC,IAAI,IAAI,CAAC5D,IAAI,CAAC,CAAC;EACxF;AACJ;AACA;AACA;AACA;EACI,IAAIlB,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACqE,MAAM;EACtB;EACA;AACJ;AACA;AACA;AACA;EACI,IAAI/E,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACoF,KAAK;EACrB;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIM,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACJ,YAAY;EAC5B;EACAkB,WAAW,GAAG;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;EACPjD,eAAe,GAAGpH,MAAM,CAAC8F,WAAW,CAAC;EACrCwE,eAAeA,CAAA,EAAG;IACd,KAAK,CAACA,eAAe,CAAC,CAAC;IACvBvJ,QAAQ,CAAC,IAAI,CAACgJ,WAAW,EAAE,IAAI,CAACQ,aAAa,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC1D,IAAI,CAACC,UAAU,CAAC,CAAC;IACjB,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAI,CAAC7B,WAAW,GAAG,IAAI;EAC3B;EACA8B,WAAWA,CAACC,aAAa,EAAE;IACvB,KAAK,CAACD,WAAW,CAACC,aAAa,CAAC;IAChC,MAAM;MAAErB;IAAY,CAAC,GAAGqB,aAAa;IACrC,IAAIrB,WAAW,EAAE;MACb,MAAMtE,KAAK,GAAGsE,WAAW,CAACsB,YAAY;MACtC,KAAK,MAAMC,QAAQ,IAAI7F,KAAK,EAAE;QAC1B,IAAI,CAAC6F,QAAQ,CAAC,GAAG7F,KAAK,CAAC6F,QAAQ,CAAC;MACpC;IACJ;EACJ;EACAP,aAAaA,CAAA,EAAG;IACZ,MAAMQ,UAAU,GAAG,CAAClE,uBAAuB,CAACC,MAAM,EAAED,uBAAuB,CAACE,SAAS,CAAC;IACtF,IAAI,IAAI,CAAClD,IAAI,IAAI,CAAC,IAAI,CAACU,KAAK,IAAIvD,OAAO,CAAC,IAAI,CAAC+I,WAAW,CAACiB,WAAW,CAAC,EAAE;MACnED,UAAU,CAACE,IAAI,CAACpE,uBAAuB,CAACG,QAAQ,CAAC;IACrD;IACA,IAAI,IAAI,CAAC5B,OAAO,EAAE;MACd2F,UAAU,CAACE,IAAI,CAACpE,uBAAuB,CAACI,QAAQ,EAAEJ,uBAAuB,CAACzB,OAAO,CAAC;MAClF,IAAI,CAAC,IAAI,CAACvB,IAAI,IAAI,IAAI,CAACU,KAAK,EAAE;QAC1BwG,UAAU,CAACE,IAAI,CAACpE,uBAAuB,CAACK,SAAS,CAAC;MACtD;MACA,IAAI,IAAI,CAACrD,IAAI,IAAI,CAAC,IAAI,CAACU,KAAK,IAAI,CAACvD,OAAO,CAAC,IAAI,CAAC+I,WAAW,CAACiB,WAAW,CAAC,EAAE;QACpED,UAAU,CAACE,IAAI,CAACpE,uBAAuB,CAACG,QAAQ,CAAC;MACrD;IACJ;IACA,IAAI,IAAI,CAACvB,IAAI,EAAE;MACXsF,UAAU,CAACE,IAAI,CAAC,eAAe,CAAC;IACpC;IACA,IAAI,IAAI,CAAC3F,QAAQ,EAAE;MACfyF,UAAU,CAACE,IAAI,CAAC,YAAY,IAAI,CAAC3F,QAAQ,EAAE,CAAC;IAChD;IACA,IAAI,IAAI,CAACM,KAAK,EAAE;MACZmF,UAAU,CAACE,IAAI,CAAC,gBAAgB,CAAC;IACrC;IACA,IAAI,IAAI,CAAC1F,MAAM,EAAE;MACbwF,UAAU,CAACE,IAAI,CAAC,iBAAiB,CAAC;IACtC;IACA,IAAI,IAAI,CAACtF,IAAI,EAAE;MACXoF,UAAU,CAACE,IAAI,CAAC,YAAY,IAAI,CAACtF,IAAI,EAAE,CAAC;IAC5C;IACA,IAAI,IAAI,CAACD,QAAQ,EAAE;MACfqF,UAAU,CAACE,IAAI,CAAC,mBAAmB,CAAC;IACxC;IACA,IAAI,IAAI,CAACzF,OAAO,EAAE;MACduF,UAAU,CAACE,IAAI,CAAC,kBAAkB,CAAC;IACvC;IACA,IAAI,IAAI,CAACtF,IAAI,KAAK,OAAO,EAAE;MACvBoF,UAAU,CAACE,IAAI,CAAC,aAAa,CAAC;IAClC;IACA,IAAI,IAAI,CAACtF,IAAI,KAAK,OAAO,EAAE;MACvBoF,UAAU,CAACE,IAAI,CAAC,aAAa,CAAC;IAClC;IACA,IAAI,IAAI,CAACC,QAAQ,EAAE;MACfH,UAAU,CAACE,IAAI,CAAC,gBAAgB,CAAC;IACrC;IACA,OAAOF,UAAU;EACrB;EACA,IAAIG,QAAQA,CAAA,EAAG;IACX,MAAMjB,aAAa,GAAG,IAAI,CAACD,EAAE,CAACC,aAAa;IAC3C,MAAMkB,cAAc,GAAGlB,aAAa,CAACmB,OAAO,CAAC,SAAS,CAAC;IACvD,OAAOpK,OAAO,CAAC,IAAI,CAAC6E,KAAK,CAAC,GAAG,CAAC,CAACsF,cAAc,GAAG,IAAI,CAACtF,KAAK;EAC9D;EACAmD,aAAaA,CAAA,EAAG;IACZ,MAAM+B,UAAU,GAAG,IAAI,CAACR,aAAa,CAAC,CAAC;IACvC,IAAI,CAACc,2BAA2B,CAAC,CAAC;IAClC,IAAI,CAACtB,WAAW,CAACuB,SAAS,CAACC,MAAM,CAAC,GAAG,IAAI,CAACrB,gBAAgB,CAAC;IAC3D,IAAI,CAACH,WAAW,CAACuB,SAAS,CAACE,GAAG,CAAC,GAAGT,UAAU,CAAC;EACjD;EACAM,2BAA2BA,CAAA,EAAG;IAC1B,MAAMI,aAAa,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,CAAC;IACvG,MAAMC,qBAAqB,GAAG,IAAI,CAAC3B,WAAW,CAACuB,SAAS,CAACxB,KAAK,CAAC6B,KAAK,CAAC,GAAG,CAAC,CAACC,IAAI,CAAEC,GAAG,IAAKJ,aAAa,CAACK,IAAI,CAAExG,QAAQ,IAAKuG,GAAG,KAAK,YAAYvG,QAAQ,EAAE,CAAC,CAAC;IACzJ,IAAIoG,qBAAqB,EAAE;MACvB,IAAI,CAAC3B,WAAW,CAACuB,SAAS,CAACC,MAAM,CAACG,qBAAqB,CAAC;IAC5D;EACJ;EACAhB,WAAWA,CAAA,EAAG;IACV,MAAMqB,OAAO,GAAG9K,UAAU,CAAC,IAAI,CAAC8I,WAAW,EAAE,iBAAiB,CAAC;IAC/D,IAAI,CAACgC,OAAO,IAAI,IAAI,CAACxH,KAAK,EAAE;MACxB,IAAIyH,YAAY,GAAG,IAAI,CAACC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;MACtD,IAAI,IAAI,CAACrI,IAAI,IAAI,CAAC,IAAI,CAACU,KAAK,EAAE;QAC1ByH,YAAY,CAACG,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;MACpD;MACAH,YAAY,CAACI,SAAS,GAAG,gBAAgB;MACzCJ,YAAY,CAACK,WAAW,CAAC,IAAI,CAACJ,QAAQ,CAACK,cAAc,CAAC,IAAI,CAAC/H,KAAK,CAAC,CAAC;MAClE,IAAI,CAACwF,WAAW,CAACsC,WAAW,CAACL,YAAY,CAAC;IAC9C;EACJ;EACAvB,UAAUA,CAAA,EAAG;IACT,MAAMsB,OAAO,GAAG9K,UAAU,CAAC,IAAI,CAAC8I,WAAW,EAAE,gBAAgB,CAAC;IAC9D,IAAI,CAACgC,OAAO,KAAK,IAAI,CAAClI,IAAI,IAAI,IAAI,CAACuB,OAAO,CAAC,EAAE;MACzC,IAAImH,WAAW,GAAG,IAAI,CAACN,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;MACrDK,WAAW,CAACH,SAAS,GAAG,eAAe;MACvCG,WAAW,CAACJ,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;MAC/C,IAAIK,YAAY,GAAG,IAAI,CAACjI,KAAK,GAAG,gBAAgB,GAAG,IAAI,CAACY,OAAO,GAAG,IAAI;MACtE,IAAIqH,YAAY,EAAE;QACdzL,QAAQ,CAACwL,WAAW,EAAEC,YAAY,CAAC;MACvC;MACA,IAAI7J,SAAS,GAAG,IAAI,CAAC8J,YAAY,CAAC,CAAC;MACnC,IAAI9J,SAAS,EAAE;QACX5B,QAAQ,CAACwL,WAAW,EAAE5J,SAAS,CAAC;MACpC;MACA,IAAI,CAAC,IAAI,CAACS,WAAW,IAAI,IAAI,CAACgC,OAAO,EAAE;QACnCmH,WAAW,CAACG,SAAS,GAAG,IAAI,CAACrC,WAAW;MAC5C;MACA,IAAI,CAACN,WAAW,CAAC4C,YAAY,CAACJ,WAAW,EAAE,IAAI,CAACxC,WAAW,CAAC6C,UAAU,CAAC;IAC3E;EACJ;EACA9D,WAAWA,CAAA,EAAG;IACV,IAAIkD,YAAY,GAAG/K,UAAU,CAAC,IAAI,CAAC8I,WAAW,EAAE,iBAAiB,CAAC;IAClE,IAAI,CAAC,IAAI,CAACxF,KAAK,EAAE;MACbyH,YAAY,IAAI,IAAI,CAACjC,WAAW,CAAC8C,WAAW,CAACb,YAAY,CAAC;MAC1D;IACJ;IACAA,YAAY,GAAIA,YAAY,CAAChB,WAAW,GAAG,IAAI,CAACzG,KAAK,GAAI,IAAI,CAACmG,WAAW,CAAC,CAAC;EAC/E;EACA3B,UAAUA,CAAA,EAAG;IACT,IAAIwD,WAAW,GAAGtL,UAAU,CAAC,IAAI,CAAC8I,WAAW,EAAE,gBAAgB,CAAC;IAChE,IAAIiC,YAAY,GAAG/K,UAAU,CAAC,IAAI,CAAC8I,WAAW,EAAE,iBAAiB,CAAC;IAClE,IAAI,IAAI,CAAC3E,OAAO,IAAI,CAAC,IAAI,CAAChC,WAAW,IAAImJ,WAAW,EAAE;MAClDA,WAAW,CAACG,SAAS,GAAG,IAAI,CAACrC,WAAW;IAC5C,CAAC,MACI,IAAIkC,WAAW,EAAEG,SAAS,EAAE;MAC7BH,WAAW,CAACG,SAAS,GAAG,EAAE;IAC9B;IACA,IAAIH,WAAW,EAAE;MACb,IAAI,IAAI,CAACpH,OAAO,EAAE;QACdoH,WAAW,CAACH,SAAS,GAAG,gBAAgB,IAAIJ,YAAY,GAAG,gBAAgB,GAAG,IAAI,CAAC7G,OAAO,GAAG,EAAE,CAAC,GAAG,GAAG,GAAG,IAAI,CAACsH,YAAY,CAAC,CAAC;MAChI,CAAC,MACI;QACDF,WAAW,CAACH,SAAS,GAAG,gBAAgB,GAAG,IAAI,CAACK,YAAY,CAAC,CAAC;MAClE;IACJ,CAAC,MACI;MACD,IAAI,CAAChC,UAAU,CAAC,CAAC;IACrB;EACJ;EACAgC,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACrH,OAAO,GAAG,wBAAwB,IAAI,IAAI,CAAChC,WAAW,GAAG,IAAI,CAACA,WAAW,GAAG,QAAQ,CAAC,GAAG,IAAI,CAACS,IAAI,IAAI,UAAU;EAC/H;EACAiJ,WAAWA,CAAA,EAAG;IACV,IAAI,CAACjE,WAAW,GAAG,KAAK;IACxB,KAAK,CAACiE,WAAW,CAAC,CAAC;EACvB;EACA,OAAO9G,IAAI;IAAA,IAAA+G,4BAAA;IAAA,gBAAAC,wBAAA7G,iBAAA;MAAA,QAAA4G,4BAAA,KAAAA,4BAAA,GA1X+EjN,EAAE,CAAAsG,qBAAA,CA0XQsC,eAAe,IAAAvC,iBAAA,IAAfuC,eAAe;IAAA;EAAA;EACnH,OAAOnB,IAAI,kBA3X+EzH,EAAE,CAAA0H,iBAAA;IAAAb,IAAA,EA2XJ+B,eAAe;IAAAjB,SAAA;IAAAwF,cAAA,WAAAC,+BAAA/K,EAAA,EAAAC,GAAA,EAAA+K,QAAA;MAAA,IAAAhL,EAAA;QA3XbrC,EAAE,CAAAsN,oBAAA,CAAAD,QAAA,EAAA/K,GAAA,CAAAgH,UAAA,EA2X4sBd,UAAU;QA3XxtBxI,EAAE,CAAAsN,oBAAA,CAAAD,QAAA,EAAA/K,GAAA,CAAAiH,WAAA,EA2XozBlC,WAAW;MAAA;MAAA,IAAAhF,EAAA;QA3Xj0BrC,EAAE,CAAAuN,cAAA;MAAA;IAAA;IAAA3F,QAAA;IAAAC,YAAA,WAAA2F,6BAAAnL,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFrC,EAAE,CAAA+H,WAAA,uBA2XJzF,GAAA,CAAAkH,UAAA,CAAW,CAAG,CAAC,kBAAflH,GAAA,CAAAgI,YAAA,CAAa,CAAC,CAAC;MAAA;IAAA;IAAAmD,MAAA;MAAApI,OAAA;MAAA/B,WAAA;MAAAgC,OAAA;MAAAE,QAAA;MAAAC,MAAA,0BAA8KnF,gBAAgB;MAAAoF,OAAA,4BAAmCpF,gBAAgB;MAAAqF,IAAA,sBAA0BrF,gBAAgB;MAAAsF,QAAA,8BAAsCtF,gBAAgB;MAAAuF,IAAA;MAAAC,KAAA,wBAA2CxF,gBAAgB;MAAAyF,KAAA,wBAA6BzF,gBAAgB;MAAAmE,KAAA;MAAAV,IAAA;MAAA0F,WAAA;IAAA;IAAAzB,QAAA,GA3XtchI,EAAE,CAAAiI,kBAAA,CA2X6nB,CAACjC,WAAW,CAAC,GA3X5oBhG,EAAE,CAAAkI,0BAAA,EAAFlI,EAAE,CAAA0N,oBAAA;EAAA;AA4XhG;AACA;EAAA,QAAA/G,SAAA,oBAAAA,SAAA,KA7X8F3G,EAAE,CAAA4G,iBAAA,CA6XJgC,eAAe,EAAc,CAAC;IAC9G/B,IAAI,EAAE1G,SAAS;IACfgI,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,WAAW;MACrBE,UAAU,EAAE,IAAI;MAChBD,SAAS,EAAE,CAACrC,WAAW,CAAC;MACxBuC,IAAI,EAAE;QACF,4BAA4B,EAAE,cAAc;QAC5C,uBAAuB,EAAE;MAC7B;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAElD,OAAO,EAAE,CAAC;MACxBwB,IAAI,EAAEtG;IACV,CAAC,CAAC;IAAE+C,WAAW,EAAE,CAAC;MACduD,IAAI,EAAEtG;IACV,CAAC,CAAC;IAAE+E,OAAO,EAAE,CAAC;MACVuB,IAAI,EAAEtG;IACV,CAAC,CAAC;IAAEiF,QAAQ,EAAE,CAAC;MACXqB,IAAI,EAAEtG;IACV,CAAC,CAAC;IAAEkF,MAAM,EAAE,CAAC;MACToB,IAAI,EAAEtG,KAAK;MACX4H,IAAI,EAAE,CAAC;QAAEwF,SAAS,EAAErN;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEoF,OAAO,EAAE,CAAC;MACVmB,IAAI,EAAEtG,KAAK;MACX4H,IAAI,EAAE,CAAC;QAAEwF,SAAS,EAAErN;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEqF,IAAI,EAAE,CAAC;MACPkB,IAAI,EAAEtG,KAAK;MACX4H,IAAI,EAAE,CAAC;QAAEwF,SAAS,EAAErN;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEsF,QAAQ,EAAE,CAAC;MACXiB,IAAI,EAAEtG,KAAK;MACX4H,IAAI,EAAE,CAAC;QAAEwF,SAAS,EAAErN;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEuF,IAAI,EAAE,CAAC;MACPgB,IAAI,EAAEtG;IACV,CAAC,CAAC;IAAEuF,KAAK,EAAE,CAAC;MACRe,IAAI,EAAEtG,KAAK;MACX4H,IAAI,EAAE,CAAC;QAAEwF,SAAS,EAAErN;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEyF,KAAK,EAAE,CAAC;MACRc,IAAI,EAAEtG,KAAK;MACX4H,IAAI,EAAE,CAAC;QAAEwF,SAAS,EAAErN;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEmE,KAAK,EAAE,CAAC;MACRoC,IAAI,EAAEtG;IACV,CAAC,CAAC;IAAEwD,IAAI,EAAE,CAAC;MACP8C,IAAI,EAAEtG;IACV,CAAC,CAAC;IAAEkJ,WAAW,EAAE,CAAC;MACd5C,IAAI,EAAEtG;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA,MAAMqN,MAAM,SAASnM,aAAa,CAAC;EAC/B;AACJ;AACA;AACA;EACIoF,IAAI,GAAG,QAAQ;EACf;AACJ;AACA;AACA;EACIxB,OAAO,GAAG,MAAM;EAChB;AACJ;AACA;AACA;EACItB,IAAI;EACJ;AACJ;AACA;AACA;EACIa,KAAK;EACL;AACJ;AACA;AACA;EACIH,KAAK;EACL;AACJ;AACA;AACA;EACI0C,QAAQ;EACR;AACJ;AACA;AACA;EACI7B,OAAO,GAAG,KAAK;EACf;AACJ;AACA;AACA;EACIhC,WAAW;EACX;AACJ;AACA;AACA;EACImC,MAAM,GAAG,KAAK;EACd;AACJ;AACA;AACA;EACIC,OAAO,GAAG,KAAK;EACf;AACJ;AACA;AACA;EACIC,IAAI,GAAG,KAAK;EACZ;AACJ;AACA;AACA;AACA;EACIG,KAAK,GAAG,KAAK;EACb;AACJ;AACA;AACA;EACIN,QAAQ;EACR;AACJ;AACA;AACA;EACII,QAAQ,GAAG,KAAK;EAChB;AACJ;AACA;AACA;EACIL,IAAI,GAAG,KAAK;EACZ;AACJ;AACA;AACA;EACIsI,QAAQ;EACR;AACJ;AACA;AACA;EACIhI,IAAI;EACJ;AACJ;AACA;AACA;EACIiI,OAAO;EACP;AACJ;AACA;AACA;EACIC,KAAK;EACL;AACJ;AACA;AACA;EACI9C,UAAU;EACV;AACJ;AACA;AACA;AACA;EACI+C,UAAU;EACV;AACJ;AACA;AACA;AACA;EACInJ,aAAa,GAAG,WAAW;EAC3B;AACJ;AACA;AACA;EACIoJ,SAAS;EACT;AACJ;AACA;AACA;EACIC,SAAS;EACT;AACJ;AACA;AACA;EACInI,KAAK;EACL;AACJ;AACA;AACA;AACA;AACA;EACIoI,OAAO,GAAG,IAAI3N,YAAY,CAAC,CAAC;EAC5B;AACJ;AACA;AACA;AACA;AACA;EACI4N,OAAO,GAAG,IAAI5N,YAAY,CAAC,CAAC;EAC5B;AACJ;AACA;AACA;AACA;AACA;EACI6N,MAAM,GAAG,IAAI7N,YAAY,CAAC,CAAC;EAC3B;AACJ;AACA;AACA;EACI8N,eAAe;EACf;AACJ;AACA;AACA;EACI7K,mBAAmB;EACnB;AACJ;AACA;AACA;EACIS,YAAY;EACZmF,YAAY;EACZ;AACJ;AACA;AACA;EACI,IAAII,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACJ,YAAY;EAC5B;EACA,IAAII,WAAWA,CAACZ,GAAG,EAAE;IACjB,IAAI,CAACQ,YAAY,GAAGR,GAAG;IACvB,IAAIA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MAChC;MACAa,MAAM,CAACC,OAAO,CAACd,GAAG,CAAC,CAACe,OAAO,CAAC,CAAC,CAACC,CAAC,EAAEC,CAAC,CAAC,KAAK,IAAI,CAAC,IAAID,CAAC,EAAE,CAAC,KAAKC,CAAC,KAAK,IAAI,CAAC,IAAID,CAAC,EAAE,CAAC,GAAGC,CAAC,CAAC,CAAC;IACvF;EACJ;EACA,IAAIsB,QAAQA,CAAA,EAAG;IACX,MAAMjB,aAAa,GAAG,IAAI,CAACD,EAAE,CAACC,aAAa;IAC3C,MAAMkB,cAAc,GAAGlB,aAAa,CAACmB,OAAO,CAAC,SAAS,CAAC;IACvD,OAAOpK,OAAO,CAAC,IAAI,CAAC6E,KAAK,CAAC,GAAG,CAAC,CAACsF,cAAc,GAAG,IAAI,CAACtF,KAAK;EAC9D;EACAuB,eAAe,GAAGpH,MAAM,CAAC8F,WAAW,CAAC;EACrCuI,SAAS;EACTC,gBAAgB;EAChBrK,aAAa;EACbT,oBAAoB;EACpB+K,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACF,SAAS,EAAE3E,OAAO,CAAE8E,IAAI,IAAK;MAC9B,QAAQA,IAAI,CAACC,OAAO,CAAC,CAAC;QAClB,KAAK,SAAS;UACV,IAAI,CAACH,gBAAgB,GAAGE,IAAI,CAACE,QAAQ;UACrC;QACJ,KAAK,MAAM;UACP,IAAI,CAACzK,aAAa,GAAGuK,IAAI,CAACE,QAAQ;UAClC;QACJ,KAAK,aAAa;UACd,IAAI,CAAClL,oBAAoB,GAAGgL,IAAI,CAACE,QAAQ;UACzC;QACJ;UACI,IAAI,CAACJ,gBAAgB,GAAGE,IAAI,CAACE,QAAQ;UACrC;MACR;IACJ,CAAC,CAAC;EACN;EACA/D,WAAWA,CAACC,aAAa,EAAE;IACvB,KAAK,CAACD,WAAW,CAACC,aAAa,CAAC;IAChC,MAAM;MAAErB;IAAY,CAAC,GAAGqB,aAAa;IACrC,IAAIrB,WAAW,EAAE;MACb,MAAMtE,KAAK,GAAGsE,WAAW,CAACsB,YAAY;MACtC,KAAK,MAAMC,QAAQ,IAAI7F,KAAK,EAAE;QAC1B,IAAI,CAAC6F,QAAQ,CAAC,GAAG7F,KAAK,CAAC6F,QAAQ,CAAC;MACpC;IACJ;EACJ;EACAhI,gBAAgBA,CAAA,EAAG;IACf,OAAO0G,MAAM,CAACC,OAAO,CAAC,IAAI,CAAC9G,SAAS,CAAC,CAAC,CAAC,CAClCgM,MAAM,CAAC,CAAC,GAAG7E,KAAK,CAAC,KAAK,CAAC,CAACA,KAAK,CAAC,CAC9B8E,MAAM,CAAC,CAACC,GAAG,EAAE,CAACC,GAAG,CAAC,KAAKD,GAAG,GAAG,IAAIC,GAAG,EAAE,EAAE,uBAAuB,CAAC;EACzE;EACAnM,SAASA,CAAA,EAAG;IACR,OAAO;MACH,CAAC,iCAAiC,IAAI,CAACS,WAAW,IAAI,EAAE,EAAE,GAAG,IAAI,CAACgC,OAAO;MACzE,eAAe,EAAE,IAAI;MACrB,oBAAoB,EAAE,IAAI,CAACD,OAAO,KAAK,MAAM,IAAI,IAAI,CAACZ,KAAK;MAC3D,qBAAqB,EAAE,IAAI,CAACY,OAAO,KAAK,OAAO,IAAI,IAAI,CAACZ,KAAK;MAC7D,mBAAmB,EAAE,IAAI,CAACY,OAAO,KAAK,KAAK,IAAI,IAAI,CAACZ,KAAK;MACzD,sBAAsB,EAAE,IAAI,CAACY,OAAO,KAAK,QAAQ,IAAI,IAAI,CAACZ;IAC9D,CAAC;EACL;EACA,IAAIwK,WAAWA,CAAA,EAAG;IACd,OAAO;MACH,sBAAsB,EAAE,IAAI;MAC5B,oBAAoB,EAAE,CAAC,IAAI,CAAClL,IAAI,IAAI,IAAI,CAACG,YAAY,IAAI,IAAI,CAACC,aAAa,IAAI,IAAI,CAACb,WAAW,IAAI,IAAI,CAACG,mBAAmB,IAAI,IAAI,CAACC,oBAAoB,KAAK,CAAC,IAAI,CAACe,KAAK;MACxK,mBAAmB,EAAE,CAAC,IAAI,CAACY,OAAO,KAAK,KAAK,IAAI,IAAI,CAACA,OAAO,KAAK,QAAQ,KAAK,IAAI,CAACZ,KAAK;MACxF,kBAAkB,EAAE,IAAI,CAACa,OAAO;MAChC,6BAA6B,EAAE,IAAI,CAACA,OAAO,IAAI,CAAC,IAAI,CAACvB,IAAI,IAAI,IAAI,CAACU,KAAK,IAAI,CAAC,IAAI,CAACnB,WAAW,IAAI,IAAI,CAAC+B,OAAO,KAAK,MAAM;MACvH,eAAe,EAAE,IAAI,CAACE,IAAI;MAC1B,CAAC,YAAY,IAAI,CAACC,QAAQ,EAAE,GAAG,IAAI,CAACA,QAAQ;MAC5C,iBAAiB,EAAE,IAAI,CAACC,MAAM;MAC9B,kBAAkB,EAAE,IAAI,CAACC,OAAO;MAChC,eAAe,EAAE,IAAI,CAACC,IAAI,IAAI,IAAI,CAACmI,OAAO,IAAI,MAAM;MACpD,mBAAmB,EAAE,IAAI,CAAClI,QAAQ,IAAI,IAAI,CAACkI,OAAO,IAAI,UAAU;MAChE,aAAa,EAAE,IAAI,CAACjI,IAAI,KAAK,OAAO;MACpC,aAAa,EAAE,IAAI,CAACA,IAAI,KAAK,OAAO;MACpC,gBAAgB,EAAE,IAAI,CAACC,KAAK;MAC5B,gBAAgB,EAAE,IAAI,CAACsF,QAAQ;MAC/B,CAAC,GAAG,IAAI,CAACH,UAAU,EAAE,GAAG,IAAI,CAACA;IACjC,CAAC;EACL;EACA,OAAO/E,IAAI;IAAA,IAAAgJ,mBAAA;IAAA,gBAAAC,eAAA9I,iBAAA;MAAA,QAAA6I,mBAAA,KAAAA,mBAAA,GA5qB+ElP,EAAE,CAAAsG,qBAAA,CA4qBQsH,MAAM,IAAAvH,iBAAA,IAANuH,MAAM;IAAA;EAAA;EAC1G,OAAOwB,IAAI,kBA7qB+EpP,EAAE,CAAAqP,iBAAA;IAAAxI,IAAA,EA6qBJ+G,MAAM;IAAAjG,SAAA;IAAAwF,cAAA,WAAAmC,sBAAAjN,EAAA,EAAAC,GAAA,EAAA+K,QAAA;MAAA,IAAAhL,EAAA;QA7qBJrC,EAAE,CAAAuP,cAAA,CAAAlC,QAAA,EAAAxL,GAAA;QAAF7B,EAAE,CAAAuP,cAAA,CAAAlC,QAAA,EAAAvL,GAAA;QAAF9B,EAAE,CAAAuP,cAAA,CAAAlC,QAAA,EAAAtL,GAAA;QAAF/B,EAAE,CAAAuP,cAAA,CAAAlC,QAAA,EA6qBg0ChM,aAAa;MAAA;MAAA,IAAAgB,EAAA;QAAA,IAAAmN,EAAA;QA7qB/0CxP,EAAE,CAAAyP,cAAA,CAAAD,EAAA,GAAFxP,EAAE,CAAA0P,WAAA,QAAApN,GAAA,CAAAgM,eAAA,GAAAkB,EAAA,CAAAG,KAAA;QAAF3P,EAAE,CAAAyP,cAAA,CAAAD,EAAA,GAAFxP,EAAE,CAAA0P,WAAA,QAAApN,GAAA,CAAAmB,mBAAA,GAAA+L,EAAA,CAAAG,KAAA;QAAF3P,EAAE,CAAAyP,cAAA,CAAAD,EAAA,GAAFxP,EAAE,CAAA0P,WAAA,QAAApN,GAAA,CAAA4B,YAAA,GAAAsL,EAAA,CAAAG,KAAA;QAAF3P,EAAE,CAAAyP,cAAA,CAAAD,EAAA,GAAFxP,EAAE,CAAA0P,WAAA,QAAApN,GAAA,CAAAiM,SAAA,GAAAiB,EAAA;MAAA;IAAA;IAAA/B,MAAA;MAAA5G,IAAA;MAAAxB,OAAA;MAAAtB,IAAA;MAAAa,KAAA;MAAAH,KAAA;MAAA0C,QAAA,8BA6qB2K7G,gBAAgB;MAAAgF,OAAA,4BAAmChF,gBAAgB;MAAAgD,WAAA;MAAAmC,MAAA,0BAA4DnF,gBAAgB;MAAAoF,OAAA,4BAAmCpF,gBAAgB;MAAAqF,IAAA,sBAA0BrF,gBAAgB;MAAAwF,KAAA,wBAA6BxF,gBAAgB;MAAAkF,QAAA;MAAAI,QAAA,8BAA4DtF,gBAAgB;MAAAiF,IAAA,sBAA0BjF,gBAAgB;MAAAuN,QAAA,8BAAsCpN,eAAe;MAAAoF,IAAA;MAAAiI,OAAA;MAAAC,KAAA;MAAA9C,UAAA;MAAA+C,UAAA;MAAAnJ,aAAA;MAAAoJ,SAAA;MAAAC,SAAA,gCAAuM5N,gBAAgB;MAAAyF,KAAA,wBAA6BzF,gBAAgB;MAAAmJ,WAAA;IAAA;IAAAmG,OAAA;MAAAzB,OAAA;MAAAC,OAAA;MAAAC,MAAA;IAAA;IAAArG,QAAA,GA7qBr3BhI,EAAE,CAAAiI,kBAAA,CA6qBs+B,CAACjC,WAAW,CAAC,GA7qBr/BhG,EAAE,CAAAkI,0BAAA,EAAFlI,EAAE,CAAA0N,oBAAA;IAAAmC,kBAAA,EAAA7N,GAAA;IAAA8N,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAApB,QAAA,WAAAqB,gBAAA5N,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFrC,EAAE,CAAAkQ,eAAA;QAAFlQ,EAAE,CAAAsE,cAAA,eA4rBxF,CAAC;QA5rBqFtE,EAAE,CAAAmQ,UAAA,mBAAAC,wCAAAC,MAAA;UAAA,OAorB3E/N,GAAA,CAAA6L,OAAA,CAAAmC,IAAA,CAAAD,MAAmB,CAAC;QAAA,EAAC,mBAAAE,wCAAAF,MAAA;UAAA,OACrB/N,GAAA,CAAA8L,OAAA,CAAAkC,IAAA,CAAAD,MAAmB,CAAC;QAAA,EAAC,kBAAAG,uCAAAH,MAAA;UAAA,OACtB/N,GAAA,CAAA+L,MAAA,CAAAiC,IAAA,CAAAD,MAAkB,CAAC;QAAA,EAAC;QAtrBsDrQ,EAAE,CAAAyQ,YAAA,EA6rB5D,CAAC;QA7rByDzQ,EAAE,CAAAmD,UAAA,IAAAf,8BAAA,yBA8rBf,CAAC,IAAAuB,8BAAA,yBACzC,CAAC,IAAAS,8BAAA,yBAOA,CAAC,IAAAC,sBAAA,iBAIyH,CAAC,IAAAM,yBAAA,oBAC/C,CAAC;QA3sBzB3E,EAAE,CAAAwE,YAAA,CA4sBhF,CAAC;MAAA;MAAA,IAAAnC,EAAA;QA5sB6ErC,EAAE,CAAA4C,UAAA,YAAAN,GAAA,CAAAyL,KAirBpE,CAAC,aAAAzL,GAAA,CAAA6E,QAAA,IAAA7E,GAAA,CAAAgD,OACc,CAAC,YAAAhD,GAAA,CAAA2M,WACV,CAAC,eAAA3M,GAAA,CAAA4L,SAQA,CAAC;QA3rB0DlO,EAAE,CAAA8C,WAAA,SAAAR,GAAA,CAAAuE,IAAA,gBAAAvE,GAAA,CAAA2L,SAAA,mEAAA3L,GAAA,CAAAuL,QAAA;QAAF7N,EAAE,CAAAqD,SAAA,EA8rBjB,CAAC;QA9rBcrD,EAAE,CAAA4C,UAAA,qBAAAN,GAAA,CAAAgM,eAAA,IAAAhM,GAAA,CAAAkM,gBA8rBjB,CAAC;QA9rBcxO,EAAE,CAAAqD,SAAA,CA+rBzD,CAAC;QA/rBsDrD,EAAE,CAAA4C,UAAA,SAAAN,GAAA,CAAAgD,OA+rBzD,CAAC;QA/rBsDtF,EAAE,CAAAqD,SAAA,CAssBxD,CAAC;QAtsBqDrD,EAAE,CAAA4C,UAAA,UAAAN,GAAA,CAAAgD,OAssBxD,CAAC;QAtsBqDtF,EAAE,CAAAqD,SAAA,CA0sBiC,CAAC;QA1sBpCrD,EAAE,CAAA4C,UAAA,UAAAN,GAAA,CAAAgM,eAAA,KAAAhM,GAAA,CAAAkM,gBAAA,IAAAlM,GAAA,CAAAmC,KA0sBiC,CAAC;QA1sBpCzE,EAAE,CAAAqD,SAAA,CA2sBvB,CAAC;QA3sBoBrD,EAAE,CAAA4C,UAAA,UAAAN,GAAA,CAAAgM,eAAA,KAAAhM,GAAA,CAAAkM,gBAAA,IAAAlM,GAAA,CAAAsC,KA2sBvB,CAAC;MAAA;IAAA;IAAA8L,YAAA,GAEV3Q,YAAY,EAA+BD,EAAE,CAAC6Q,OAAO,EAAoF7Q,EAAE,CAAC8Q,IAAI,EAA6F9Q,EAAE,CAAC+Q,gBAAgB,EAAoJ/Q,EAAE,CAACgR,OAAO,EAA2EnP,MAAM,EAAsDL,SAAS,EAA8FI,WAAW,EAAuDF,WAAW,EAA+BD,EAAE,CAACwP,KAAK,EAA+I3P,YAAY;IAAA4P,aAAA;IAAAC,eAAA;EAAA;AAC39B;AACA;EAAA,QAAAtK,SAAA,oBAAAA,SAAA,KA/sB8F3G,EAAE,CAAA4G,iBAAA,CA+sBJgH,MAAM,EAAc,CAAC;IACrG/G,IAAI,EAAE9F,SAAS;IACfoH,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,UAAU;MACpBE,UAAU,EAAE,IAAI;MAChB4I,OAAO,EAAE,CAACnR,YAAY,EAAE4B,MAAM,EAAEL,SAAS,EAAEI,WAAW,EAAEF,WAAW,EAAEJ,YAAY,CAAC;MAClFwN,QAAQ,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;MACeqC,eAAe,EAAEnQ,uBAAuB,CAACqQ,MAAM;MAC/CH,aAAa,EAAEnQ,iBAAiB,CAACuQ,IAAI;MACrC/I,SAAS,EAAE,CAACrC,WAAW;IAC3B,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEa,IAAI,EAAE,CAAC;MACrBA,IAAI,EAAEtG;IACV,CAAC,CAAC;IAAE8E,OAAO,EAAE,CAAC;MACVwB,IAAI,EAAEtG;IACV,CAAC,CAAC;IAAEwD,IAAI,EAAE,CAAC;MACP8C,IAAI,EAAEtG;IACV,CAAC,CAAC;IAAEqE,KAAK,EAAE,CAAC;MACRiC,IAAI,EAAEtG;IACV,CAAC,CAAC;IAAEkE,KAAK,EAAE,CAAC;MACRoC,IAAI,EAAEtG;IACV,CAAC,CAAC;IAAE4G,QAAQ,EAAE,CAAC;MACXN,IAAI,EAAEtG,KAAK;MACX4H,IAAI,EAAE,CAAC;QAAEwF,SAAS,EAAErN;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEgF,OAAO,EAAE,CAAC;MACVuB,IAAI,EAAEtG,KAAK;MACX4H,IAAI,EAAE,CAAC;QAAEwF,SAAS,EAAErN;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEgD,WAAW,EAAE,CAAC;MACduD,IAAI,EAAEtG;IACV,CAAC,CAAC;IAAEkF,MAAM,EAAE,CAAC;MACToB,IAAI,EAAEtG,KAAK;MACX4H,IAAI,EAAE,CAAC;QAAEwF,SAAS,EAAErN;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEoF,OAAO,EAAE,CAAC;MACVmB,IAAI,EAAEtG,KAAK;MACX4H,IAAI,EAAE,CAAC;QAAEwF,SAAS,EAAErN;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEqF,IAAI,EAAE,CAAC;MACPkB,IAAI,EAAEtG,KAAK;MACX4H,IAAI,EAAE,CAAC;QAAEwF,SAAS,EAAErN;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEwF,KAAK,EAAE,CAAC;MACRe,IAAI,EAAEtG,KAAK;MACX4H,IAAI,EAAE,CAAC;QAAEwF,SAAS,EAAErN;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEkF,QAAQ,EAAE,CAAC;MACXqB,IAAI,EAAEtG;IACV,CAAC,CAAC;IAAEqF,QAAQ,EAAE,CAAC;MACXiB,IAAI,EAAEtG,KAAK;MACX4H,IAAI,EAAE,CAAC;QAAEwF,SAAS,EAAErN;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEiF,IAAI,EAAE,CAAC;MACPsB,IAAI,EAAEtG,KAAK;MACX4H,IAAI,EAAE,CAAC;QAAEwF,SAAS,EAAErN;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEuN,QAAQ,EAAE,CAAC;MACXhH,IAAI,EAAEtG,KAAK;MACX4H,IAAI,EAAE,CAAC;QAAEwF,SAAS,EAAElN;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAEoF,IAAI,EAAE,CAAC;MACPgB,IAAI,EAAEtG;IACV,CAAC,CAAC;IAAEuN,OAAO,EAAE,CAAC;MACVjH,IAAI,EAAEtG;IACV,CAAC,CAAC;IAAEwN,KAAK,EAAE,CAAC;MACRlH,IAAI,EAAEtG;IACV,CAAC,CAAC;IAAE0K,UAAU,EAAE,CAAC;MACbpE,IAAI,EAAEtG;IACV,CAAC,CAAC;IAAEyN,UAAU,EAAE,CAAC;MACbnH,IAAI,EAAEtG;IACV,CAAC,CAAC;IAAEsE,aAAa,EAAE,CAAC;MAChBgC,IAAI,EAAEtG;IACV,CAAC,CAAC;IAAE0N,SAAS,EAAE,CAAC;MACZpH,IAAI,EAAEtG;IACV,CAAC,CAAC;IAAE2N,SAAS,EAAE,CAAC;MACZrH,IAAI,EAAEtG,KAAK;MACX4H,IAAI,EAAE,CAAC;QAAEwF,SAAS,EAAErN;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEyF,KAAK,EAAE,CAAC;MACRc,IAAI,EAAEtG,KAAK;MACX4H,IAAI,EAAE,CAAC;QAAEwF,SAAS,EAAErN;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE6N,OAAO,EAAE,CAAC;MACVtH,IAAI,EAAEjG;IACV,CAAC,CAAC;IAAEwN,OAAO,EAAE,CAAC;MACVvH,IAAI,EAAEjG;IACV,CAAC,CAAC;IAAEyN,MAAM,EAAE,CAAC;MACTxH,IAAI,EAAEjG;IACV,CAAC,CAAC;IAAE0N,eAAe,EAAE,CAAC;MAClBzH,IAAI,EAAElG,YAAY;MAClBwH,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAE1E,mBAAmB,EAAE,CAAC;MACtBoD,IAAI,EAAElG,YAAY;MAClBwH,IAAI,EAAE,CAAC,aAAa;IACxB,CAAC,CAAC;IAAEjE,YAAY,EAAE,CAAC;MACf2C,IAAI,EAAElG,YAAY;MAClBwH,IAAI,EAAE,CAAC,MAAM;IACjB,CAAC,CAAC;IAAEsB,WAAW,EAAE,CAAC;MACd5C,IAAI,EAAEtG;IACV,CAAC,CAAC;IAAEgO,SAAS,EAAE,CAAC;MACZ1H,IAAI,EAAEnG,eAAe;MACrByH,IAAI,EAAE,CAAC9G,aAAa;IACxB,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMgQ,YAAY,CAAC;EACf,OAAOnL,IAAI,YAAAoL,qBAAAjL,iBAAA;IAAA,YAAAA,iBAAA,IAAyFgL,YAAY;EAAA;EAChH,OAAOE,IAAI,kBA90B+EvR,EAAE,CAAAwR,gBAAA;IAAA3K,IAAA,EA80BSwK,YAAY;IAAAH,OAAA,GAAYnR,YAAY,EAAE6I,eAAe,EAAEgF,MAAM,EAAExM,YAAY,EAAEiG,WAAW,EAAEmB,UAAU;IAAAiJ,OAAA,GAAa7I,eAAe,EAAEgF,MAAM,EAAEvG,WAAW,EAAEmB,UAAU,EAAEpH,YAAY;EAAA;EACpR,OAAOsQ,IAAI,kBA/0B+E1R,EAAE,CAAA2R,gBAAA;IAAAT,OAAA,GA+0BiCnR,YAAY,EAAE6N,MAAM,EAAExM,YAAY,EAAEA,YAAY;EAAA;AACjL;AACA;EAAA,QAAAuF,SAAA,oBAAAA,SAAA,KAj1B8F3G,EAAE,CAAA4G,iBAAA,CAi1BJyK,YAAY,EAAc,CAAC;IAC3GxK,IAAI,EAAE7F,QAAQ;IACdmH,IAAI,EAAE,CAAC;MACC+I,OAAO,EAAE,CAACnR,YAAY,EAAE6I,eAAe,EAAEgF,MAAM,EAAExM,YAAY,EAAEiG,WAAW,EAAEmB,UAAU,CAAC;MACvFiJ,OAAO,EAAE,CAAC7I,eAAe,EAAEgF,MAAM,EAAEvG,WAAW,EAAEmB,UAAU,EAAEpH,YAAY;IAC5E,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASwM,MAAM,EAAE9G,aAAa,EAAE8B,eAAe,EAAEJ,UAAU,EAAEnB,WAAW,EAAEgK,YAAY,EAAErL,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}