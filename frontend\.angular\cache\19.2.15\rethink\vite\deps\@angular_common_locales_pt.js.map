{"version": 3, "sources": ["../../../../../../node_modules/@angular/common/locales/pt.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val,\n    i = Math.floor(Math.abs(val)),\n    v = val.toString().replace(/^[^.]*\\.?/, '').length,\n    e = parseInt(val.toString().replace(/^[^e]*(e([-+]?\\d+))?/, '$2')) || 0;\n  if (i === Math.floor(i) && i >= 0 && i <= 1) return 1;\n  if (e === 0 && !(i === 0) && i % 1000000 === 0 && v === 0 || !(e >= 0 && e <= 5)) return 4;\n  return 5;\n}\nexport default [\"pt\", [[\"AM\", \"PM\"], u, u], u, [[\"D\", \"S\", \"T\", \"Q\", \"Q\", \"S\", \"S\"], [\"dom.\", \"seg.\", \"ter.\", \"qua.\", \"qui.\", \"sex.\", \"sáb.\"], [\"domingo\", \"segunda-feira\", \"terça-feira\", \"quarta-feira\", \"quinta-feira\", \"sexta-feira\", \"sábado\"], [\"dom.\", \"seg.\", \"ter.\", \"qua.\", \"qui.\", \"sex.\", \"sáb.\"]], u, [[\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"], [\"jan.\", \"fev.\", \"mar.\", \"abr.\", \"mai.\", \"jun.\", \"jul.\", \"ago.\", \"set.\", \"out.\", \"nov.\", \"dez.\"], [\"janeiro\", \"fevereiro\", \"março\", \"abril\", \"maio\", \"junho\", \"julho\", \"agosto\", \"setembro\", \"outubro\", \"novembro\", \"dezembro\"]], u, [[\"a.C.\", \"d.C.\"], u, [\"antes de Cristo\", \"depois de Cristo\"]], 0, [6, 0], [\"dd/MM/y\", \"d 'de' MMM 'de' y\", \"d 'de' MMMM 'de' y\", \"EEEE, d 'de' MMMM 'de' y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤ #,##0.00\", \"#E0\"], \"BRL\", \"R$\", \"Real brasileiro\", {\n  \"AUD\": [\"AU$\", \"$\"],\n  \"BYN\": [u, \"р.\"],\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"PHP\": [u, \"₱\"],\n  \"PTE\": [\"Esc.\"],\n  \"RON\": [u, \"L\"],\n  \"SYP\": [u, \"S£\"],\n  \"THB\": [\"฿\"],\n  \"TWD\": [\"NT$\"],\n  \"USD\": [\"US$\", \"$\"]\n}, \"ltr\", plural];\n"], "mappings": ";;;AAQA,IAAM,IAAI;AACV,SAAS,OAAO,KAAK;AACnB,QAAM,IAAI,KACR,IAAI,KAAK,MAAM,KAAK,IAAI,GAAG,CAAC,GAC5B,IAAI,IAAI,SAAS,EAAE,QAAQ,aAAa,EAAE,EAAE,QAC5C,IAAI,SAAS,IAAI,SAAS,EAAE,QAAQ,wBAAwB,IAAI,CAAC,KAAK;AACxE,MAAI,MAAM,KAAK,MAAM,CAAC,KAAK,KAAK,KAAK,KAAK,EAAG,QAAO;AACpD,MAAI,MAAM,KAAK,EAAE,MAAM,MAAM,IAAI,QAAY,KAAK,MAAM,KAAK,EAAE,KAAK,KAAK,KAAK,GAAI,QAAO;AACzF,SAAO;AACT;AACA,IAAO,aAAQ,CAAC,MAAM,CAAC,CAAC,MAAM,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,GAAG,CAAC,WAAW,iBAAiB,eAAe,gBAAgB,gBAAgB,eAAe,QAAQ,GAAG,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,GAAG,CAAC,WAAW,aAAa,SAAS,SAAS,QAAQ,SAAS,SAAS,UAAU,YAAY,WAAW,YAAY,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,QAAQ,MAAM,GAAG,GAAG,CAAC,mBAAmB,kBAAkB,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,qBAAqB,sBAAsB,0BAA0B,GAAG,CAAC,SAAS,YAAY,cAAc,eAAe,GAAG,CAAC,WAAW,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG,CAAC,aAAa,UAAU,cAAc,KAAK,GAAG,OAAO,MAAM,mBAAmB;AAAA,EAC98B,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC,GAAG,IAAI;AAAA,EACf,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,MAAM;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,IAAI;AAAA,EACf,OAAO,CAAC,GAAG;AAAA,EACX,OAAO,CAAC,KAAK;AAAA,EACb,OAAO,CAAC,OAAO,GAAG;AACpB,GAAG,OAAO,MAAM;", "names": []}