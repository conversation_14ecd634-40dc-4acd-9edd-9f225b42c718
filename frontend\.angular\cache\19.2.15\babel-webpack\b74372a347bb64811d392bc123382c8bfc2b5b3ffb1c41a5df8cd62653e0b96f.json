{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {})) if (__hasOwnProp.call(b, prop)) __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols) for (var prop of __getOwnPropSymbols(b)) {\n    if (__propIsEnum.call(b, prop)) __defNormalProp(a, prop, b[prop]);\n  }\n  return a;\n};\n\n// src/object/methods/isFunction.ts\nfunction isFunction(value) {\n  return !!(value && value.constructor && value.call && value.apply);\n}\n\n// src/mergeprops/index.ts\nfunction mergeProps(...props) {\n  return props == null ? void 0 : props.reduce((merged, ps = {}) => {\n    for (const key in ps) {\n      const value = ps[key];\n      if (key === \"style\") {\n        merged[\"style\"] = __spreadValues(__spreadValues({}, merged[\"style\"]), ps[\"style\"]);\n      } else if (key === \"class\") {\n        merged[\"class\"] = [merged[\"class\"], ps[\"class\"]].join(\" \").trim() || void 0;\n      } else if (key === \"className\") {\n        merged[\"className\"] = [merged[\"className\"], ps[\"className\"]].join(\" \").trim() || void 0;\n      } else if (isFunction(value)) {\n        const fn = merged[key];\n        merged[key] = fn ? (...args) => {\n          fn(...args);\n          value(...args);\n        } : value;\n      } else {\n        merged[key] = value;\n      }\n    }\n    return merged;\n  }, {});\n}\nexport { mergeProps };", "map": {"version": 3, "names": ["__defProp", "Object", "defineProperty", "__getOwnPropSymbols", "getOwnPropertySymbols", "__hasOwnProp", "prototype", "hasOwnProperty", "__propIsEnum", "propertyIsEnumerable", "__defNormalProp", "obj", "key", "value", "enumerable", "configurable", "writable", "__spreadValues", "a", "b", "prop", "call", "isFunction", "constructor", "apply", "mergeProps", "props", "reduce", "merged", "ps", "join", "trim", "fn", "args"], "sources": ["C:/projetos/scope-ai/frontend/node_modules/@primeuix/utils/mergeprops/index.mjs"], "sourcesContent": ["var __defProp = Object.defineProperty;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\n\n// src/object/methods/isFunction.ts\nfunction isFunction(value) {\n  return !!(value && value.constructor && value.call && value.apply);\n}\n\n// src/mergeprops/index.ts\nfunction mergeProps(...props) {\n  return props == null ? void 0 : props.reduce((merged, ps = {}) => {\n    for (const key in ps) {\n      const value = ps[key];\n      if (key === \"style\") {\n        merged[\"style\"] = __spreadValues(__spreadValues({}, merged[\"style\"]), ps[\"style\"]);\n      } else if (key === \"class\") {\n        merged[\"class\"] = [merged[\"class\"], ps[\"class\"]].join(\" \").trim() || void 0;\n      } else if (key === \"className\") {\n        merged[\"className\"] = [merged[\"className\"], ps[\"className\"]].join(\" \").trim() || void 0;\n      } else if (isFunction(value)) {\n        const fn = merged[key];\n        merged[key] = fn ? (...args) => {\n          fn(...args);\n          value(...args);\n        } : value;\n      } else {\n        merged[key] = value;\n      }\n    }\n    return merged;\n  }, {});\n}\nexport {\n  mergeProps\n};\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,MAAM,CAACC,cAAc;AACrC,IAAIC,mBAAmB,GAAGF,MAAM,CAACG,qBAAqB;AACtD,IAAIC,YAAY,GAAGJ,MAAM,CAACK,SAAS,CAACC,cAAc;AAClD,IAAIC,YAAY,GAAGP,MAAM,CAACK,SAAS,CAACG,oBAAoB;AACxD,IAAIC,eAAe,GAAGA,CAACC,GAAG,EAAEC,GAAG,EAAEC,KAAK,KAAKD,GAAG,IAAID,GAAG,GAAGX,SAAS,CAACW,GAAG,EAAEC,GAAG,EAAE;EAAEE,UAAU,EAAE,IAAI;EAAEC,YAAY,EAAE,IAAI;EAAEC,QAAQ,EAAE,IAAI;EAAEH;AAAM,CAAC,CAAC,GAAGF,GAAG,CAACC,GAAG,CAAC,GAAGC,KAAK;AAC/J,IAAII,cAAc,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAK;EAC7B,KAAK,IAAIC,IAAI,IAAID,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC,CAAC,EAC5B,IAAId,YAAY,CAACgB,IAAI,CAACF,CAAC,EAAEC,IAAI,CAAC,EAC5BV,eAAe,CAACQ,CAAC,EAAEE,IAAI,EAAED,CAAC,CAACC,IAAI,CAAC,CAAC;EACrC,IAAIjB,mBAAmB,EACrB,KAAK,IAAIiB,IAAI,IAAIjB,mBAAmB,CAACgB,CAAC,CAAC,EAAE;IACvC,IAAIX,YAAY,CAACa,IAAI,CAACF,CAAC,EAAEC,IAAI,CAAC,EAC5BV,eAAe,CAACQ,CAAC,EAAEE,IAAI,EAAED,CAAC,CAACC,IAAI,CAAC,CAAC;EACrC;EACF,OAAOF,CAAC;AACV,CAAC;;AAED;AACA,SAASI,UAAUA,CAACT,KAAK,EAAE;EACzB,OAAO,CAAC,EAAEA,KAAK,IAAIA,KAAK,CAACU,WAAW,IAAIV,KAAK,CAACQ,IAAI,IAAIR,KAAK,CAACW,KAAK,CAAC;AACpE;;AAEA;AACA,SAASC,UAAUA,CAAC,GAAGC,KAAK,EAAE;EAC5B,OAAOA,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACC,MAAM,CAAC,CAACC,MAAM,EAAEC,EAAE,GAAG,CAAC,CAAC,KAAK;IAChE,KAAK,MAAMjB,GAAG,IAAIiB,EAAE,EAAE;MACpB,MAAMhB,KAAK,GAAGgB,EAAE,CAACjB,GAAG,CAAC;MACrB,IAAIA,GAAG,KAAK,OAAO,EAAE;QACnBgB,MAAM,CAAC,OAAO,CAAC,GAAGX,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEW,MAAM,CAAC,OAAO,CAAC,CAAC,EAAEC,EAAE,CAAC,OAAO,CAAC,CAAC;MACpF,CAAC,MAAM,IAAIjB,GAAG,KAAK,OAAO,EAAE;QAC1BgB,MAAM,CAAC,OAAO,CAAC,GAAG,CAACA,MAAM,CAAC,OAAO,CAAC,EAAEC,EAAE,CAAC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC;MAC7E,CAAC,MAAM,IAAInB,GAAG,KAAK,WAAW,EAAE;QAC9BgB,MAAM,CAAC,WAAW,CAAC,GAAG,CAACA,MAAM,CAAC,WAAW,CAAC,EAAEC,EAAE,CAAC,WAAW,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC;MACzF,CAAC,MAAM,IAAIT,UAAU,CAACT,KAAK,CAAC,EAAE;QAC5B,MAAMmB,EAAE,GAAGJ,MAAM,CAAChB,GAAG,CAAC;QACtBgB,MAAM,CAAChB,GAAG,CAAC,GAAGoB,EAAE,GAAG,CAAC,GAAGC,IAAI,KAAK;UAC9BD,EAAE,CAAC,GAAGC,IAAI,CAAC;UACXpB,KAAK,CAAC,GAAGoB,IAAI,CAAC;QAChB,CAAC,GAAGpB,KAAK;MACX,CAAC,MAAM;QACLe,MAAM,CAAChB,GAAG,CAAC,GAAGC,KAAK;MACrB;IACF;IACA,OAAOe,MAAM;EACf,CAAC,EAAE,CAAC,CAAC,CAAC;AACR;AACA,SACEH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}