import {
  ClassDB,
  classDiagram_default,
  classRenderer_v3_unified_default,
  styles_default
} from "./chunk-YT7ZFUHG.js";
import "./chunk-F75XBXFM.js";
import "./chunk-NOVPKVBE.js";
import "./chunk-NPXATEBX.js";
import "./chunk-RIVS45E3.js";
import "./chunk-HKFEYEPF.js";
import "./chunk-TRYUJMYI.js";
import "./chunk-S5QY7FYS.js";
import "./chunk-GQ77KXG4.js";
import "./chunk-XJ7X4FDH.js";
import "./chunk-WXWYZFL7.js";
import {
  __name
} from "./chunk-ULV4NQHW.js";
import "./chunk-S3PAIZX7.js";
import "./chunk-SERTD5K6.js";

// node_modules/mermaid/dist/chunks/mermaid.core/classDiagram-GIVACNV2.mjs
var diagram = {
  parser: classDiagram_default,
  get db() {
    return new ClassDB();
  },
  renderer: classRenderer_v3_unified_default,
  styles: styles_default,
  init: __name((cnf) => {
    if (!cnf.class) {
      cnf.class = {};
    }
    cnf.class.arrowMarkerAbsolute = cnf.arrowMarkerAbsolute;
  }, "init")
};
export {
  diagram
};
//# sourceMappingURL=classDiagram-GIVACNV2-TFO7V3HV.js.map
