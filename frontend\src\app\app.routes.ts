import { Routes } from '@angular/router';
import { DashboardHomeComponent } from './features/dashboard/dashboard-home.component';
import { MainLayoutComponent } from './features/layout/main-layout/main-layout.component';

export const ROUTES: Routes = [
	{
		path: '',
		component: MainLayoutComponent,
		children: [
			{ path: '', component: DashboardHomeComponent },
			{
				path: 'dashboard',
				loadComponent: () =>
					import('./features/dashboard/dashboard-home.component').then(m => m.DashboardHomeComponent),
			},
			{
				path: 'projects',
				loadComponent: () =>
					import('./features/projects/projects-home.component').then(m => m.ProjectsHomeComponent),
			},
			{
				path: 'clients',
				loadComponent: () =>
					import('./features/clients/clients-home.component').then(m => m.ClientsHomeComponent),
			},
			{
				path: 'settings',
				loadComponent: () =>
					import('./features/settings/settings-home.component').then(m => m.SettingsHomeComponent),
			},
			{
				path: 'about',
				loadComponent: () => import('./features/about/about-home.component').then(m => m.AboutHomeComponent),
			},
		],
	},
	{ path: '**', redirectTo: '' },
];
