"""
Storage para Sistema de Orquestração de Agentes

Responsável por persistir estados de execução, resultados e logs
do processo de orquestração usando MongoDB.
"""

import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase, AsyncIOMotorCollection
from pymongo.errors import DuplicateKeyError, PyMongoError
import json

from .schemas import (
    OrchestrationResult,
    AgentExecutionResult,
    OrchestrationStatus,
    AgentExecutionStatus
)

logger = logging.getLogger(__name__)


class OrchestrationStorage:
    """
    Sistema de storage para orquestração de agentes

    Responsável por:
    - Persistir resultados de orquestração
    - Armazenar logs de execução
    - Gerenciar estados intermediários
    - Fornecer consultas e relatórios
    """

    def __init__(self, database: AsyncIOMotorDatabase):
        """
        Inicializa o storage com conexão MongoDB

        Args:
            database: Instância do banco MongoDB
        """
        self.db = database

        # Collections
        self.orchestrations: AsyncIOMotorCollection = database.orchestrations
        self.agent_results: AsyncIOMotorCollection = database.agent_results
        self.execution_logs: AsyncIOMotorCollection = database.execution_logs

        logger.info("OrchestrationStorage inicializado")

    async def create_indexes(self):
        """Cria índices necessários para performance"""
        try:
            # Índices para orchestrations
            await self.orchestrations.create_index("orchestration_id", unique=True)
            await self.orchestrations.create_index("client_id")
            await self.orchestrations.create_index("status")
            await self.orchestrations.create_index("start_time")

            # Índices para agent_results
            await self.agent_results.create_index("orchestration_id")
            await self.agent_results.create_index("agent_name")
            await self.agent_results.create_index("status")
            await self.agent_results.create_index("phase_id")

            # Índices para execution_logs
            await self.execution_logs.create_index("orchestration_id")
            await self.execution_logs.create_index("timestamp")
            await self.execution_logs.create_index("level")

            logger.info("Índices criados com sucesso")

        except Exception as e:
            logger.error(f"Erro ao criar índices: {e}")
            raise

    async def save_orchestration(self, result: OrchestrationResult) -> bool:
        """
        Salva ou atualiza resultado de orquestração

        Args:
            result: Resultado da orquestração

        Returns:
            True se salvou com sucesso
        """
        try:
            # Converter para dict serializável
            data = self._serialize_orchestration_result(result)

            # Upsert (insert ou update)
            await self.orchestrations.replace_one(
                {"orchestration_id": result.orchestration_id},
                data,
                upsert=True
            )

            logger.debug(f"Orquestração {result.orchestration_id} salva")
            return True

        except Exception as e:
            logger.error(
                f"Erro ao salvar orquestração {result.orchestration_id}: {e}")
            return False

    async def get_orchestration(self, orchestration_id: str) -> Optional[OrchestrationResult]:
        """
        Recupera resultado de orquestração por ID

        Args:
            orchestration_id: ID da orquestração

        Returns:
            Resultado da orquestração ou None se não encontrado
        """
        try:
            data = await self.orchestrations.find_one(
                {"orchestration_id": orchestration_id}
            )

            if not data:
                return None

            return self._deserialize_orchestration_result(data)

        except Exception as e:
            logger.error(
                f"Erro ao recuperar orquestração {orchestration_id}: {e}")
            return None

    async def save_agent_result(self, orchestration_id: str, result: AgentExecutionResult) -> bool:
        """
        Salva resultado de execução de agente

        Args:
            orchestration_id: ID da orquestração
            result: Resultado da execução do agente

        Returns:
            True se salvou com sucesso
        """
        try:
            data = self._serialize_agent_result(result)
            data["orchestration_id"] = orchestration_id
            data["saved_at"] = datetime.now(timezone.utc)

            # Upsert baseado em orchestration_id + agent_name
            await self.agent_results.replace_one(
                {
                    "orchestration_id": orchestration_id,
                    "agent_name": result.agent_name
                },
                data,
                upsert=True
            )

            logger.debug(f"Resultado do agente {result.agent_name} salvo")
            return True

        except Exception as e:
            logger.error(
                f"Erro ao salvar resultado do agente {result.agent_name}: {e}")
            return False

    async def get_agent_results(self, orchestration_id: str) -> List[AgentExecutionResult]:
        """
        Recupera todos os resultados de agentes de uma orquestração

        Args:
            orchestration_id: ID da orquestração

        Returns:
            Lista de resultados dos agentes
        """
        try:
            cursor = self.agent_results.find(
                {"orchestration_id": orchestration_id})
            results = []

            async for data in cursor:
                result = self._deserialize_agent_result(data)
                if result:
                    results.append(result)

            return results

        except Exception as e:
            logger.error(f"Erro ao recuperar resultados dos agentes: {e}")
            return []

    async def log_execution(
        self,
        orchestration_id: str,
        level: str,
        message: str,
        agent_name: Optional[str] = None,
        phase_id: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Registra log de execução

        Args:
            orchestration_id: ID da orquestração
            level: Nível do log (INFO, WARNING, ERROR, etc.)
            message: Mensagem do log
            agent_name: Nome do agente (opcional)
            phase_id: ID da fase (opcional)
            metadata: Metadados adicionais (opcional)

        Returns:
            True se salvou com sucesso
        """
        try:
            log_entry = {
                "orchestration_id": orchestration_id,
                "timestamp": datetime.now(timezone.utc),
                "level": level.upper(),
                "message": message,
                "agent_name": agent_name,
                "phase_id": phase_id,
                "metadata": metadata or {}
            }

            await self.execution_logs.insert_one(log_entry)
            return True

        except Exception as e:
            logger.error(f"Erro ao salvar log: {e}")
            return False

    async def get_execution_logs(
        self,
        orchestration_id: str,
        level: Optional[str] = None,
        agent_name: Optional[str] = None,
        limit: int = 1000
    ) -> List[Dict[str, Any]]:
        """
        Recupera logs de execução

        Args:
            orchestration_id: ID da orquestração
            level: Filtrar por nível (opcional)
            agent_name: Filtrar por agente (opcional)
            limit: Limite de registros

        Returns:
            Lista de logs
        """
        try:
            query = {"orchestration_id": orchestration_id}

            if level:
                query["level"] = level.upper()
            if agent_name:
                query["agent_name"] = agent_name

            cursor = self.execution_logs.find(
                query).sort("timestamp", -1).limit(limit)
            logs = []

            async for log in cursor:
                # Converter ObjectId para string
                log["_id"] = str(log["_id"])
                logs.append(log)

            return logs

        except Exception as e:
            logger.error(f"Erro ao recuperar logs: {e}")
            return []

    async def get_orchestrations_by_client(
        self,
        client_id: str,
        status: Optional[OrchestrationStatus] = None,
        limit: int = 50
    ) -> List[OrchestrationResult]:
        """
        Recupera orquestrações de um cliente

        Args:
            client_id: ID do cliente
            status: Filtrar por status (opcional)
            limit: Limite de registros

        Returns:
            Lista de orquestrações
        """
        try:
            query = {"client_id": client_id}

            if status:
                query["status"] = status.value

            cursor = self.orchestrations.find(
                query).sort("start_time", -1).limit(limit)
            results = []

            async for data in cursor:
                result = self._deserialize_orchestration_result(data)
                if result:
                    results.append(result)

            return results

        except Exception as e:
            logger.error(
                f"Erro ao recuperar orquestrações do cliente {client_id}: {e}")
            return []

    async def get_orchestration_statistics(self) -> Dict[str, Any]:
        """
        Recupera estatísticas gerais das orquestrações

        Returns:
            Dicionário com estatísticas
        """
        try:
            # Contagem por status
            status_pipeline = [
                {"$group": {"_id": "$status", "count": {"$sum": 1}}}
            ]
            status_counts = {}
            async for doc in self.orchestrations.aggregate(status_pipeline):
                status_counts[doc["_id"]] = doc["count"]

            # Tempo médio de execução
            avg_time_pipeline = [
                {"$match": {"total_execution_time_ms": {"$ne": None}}},
                {"$group": {"_id": None, "avg_time": {
                    "$avg": "$total_execution_time_ms"}}}
            ]
            avg_time = 0
            async for doc in self.orchestrations.aggregate(avg_time_pipeline):
                avg_time = doc["avg_time"]

            # Total de orquestrações
            total_orchestrations = await self.orchestrations.count_documents({})

            # Orquestrações nas últimas 24h
            yesterday = datetime.now(timezone.utc).replace(
                hour=0, minute=0, second=0, microsecond=0)
            recent_orchestrations = await self.orchestrations.count_documents({
                "start_time": {"$gte": yesterday}
            })

            return {
                "total_orchestrations": total_orchestrations,
                "recent_orchestrations": recent_orchestrations,
                "status_distribution": status_counts,
                "average_execution_time_ms": round(avg_time, 2) if avg_time else 0,
                "generated_at": datetime.now(timezone.utc)
            }

        except Exception as e:
            logger.error(f"Erro ao gerar estatísticas: {e}")
            return {}

    async def cleanup_old_data(self, days_to_keep: int = 30) -> Dict[str, int]:
        """
        Remove dados antigos para manter performance

        Args:
            days_to_keep: Dias de dados para manter

        Returns:
            Dicionário com contagem de registros removidos
        """
        try:
            cutoff_date = datetime.now(timezone.utc).replace(
                hour=0, minute=0, second=0, microsecond=0
            )
            cutoff_date = cutoff_date.replace(
                day=cutoff_date.day - days_to_keep)

            # Remover orquestrações antigas
            orch_result = await self.orchestrations.delete_many({
                "start_time": {"$lt": cutoff_date}
            })

            # Remover resultados de agentes órfãos
            agent_result = await self.agent_results.delete_many({
                "saved_at": {"$lt": cutoff_date}
            })

            # Remover logs antigos
            logs_result = await self.execution_logs.delete_many({
                "timestamp": {"$lt": cutoff_date}
            })

            cleanup_stats = {
                "orchestrations_removed": orch_result.deleted_count,
                "agent_results_removed": agent_result.deleted_count,
                "logs_removed": logs_result.deleted_count,
                "cutoff_date": cutoff_date
            }

            logger.info(f"Cleanup concluído: {cleanup_stats}")
            return cleanup_stats

        except Exception as e:
            logger.error(f"Erro no cleanup: {e}")
            return {}

    def _serialize_orchestration_result(self, result: OrchestrationResult) -> Dict[str, Any]:
        """Converte OrchestrationResult para dict serializável"""
        data = result.dict()

        # Converter datetime para ISO string
        for field in ["start_time", "end_time", "last_update"]:
            if data.get(field):
                data[field] = data[field].isoformat()

        # Converter agent_results para lista
        if data.get("agent_results"):
            agent_results_list = []
            for agent_name, agent_result in data["agent_results"].items():
                agent_data = self._serialize_agent_result(agent_result)
                agent_data["agent_name"] = agent_name
                agent_results_list.append(agent_data)
            data["agent_results"] = agent_results_list

        return data

    def _deserialize_orchestration_result(self, data: Dict[str, Any]) -> Optional[OrchestrationResult]:
        """Converte dict para OrchestrationResult"""
        try:
            # Converter ISO strings para datetime
            for field in ["start_time", "end_time", "last_update"]:
                if data.get(field):
                    data[field] = datetime.fromisoformat(
                        data[field].replace('Z', '+00:00'))

            # Converter agent_results de lista para dict
            if data.get("agent_results") and isinstance(data["agent_results"], list):
                agent_results_dict = {}
                for agent_data in data["agent_results"]:
                    agent_result = self._deserialize_agent_result(agent_data)
                    if agent_result:
                        agent_results_dict[agent_result.agent_name] = agent_result
                data["agent_results"] = agent_results_dict

            return OrchestrationResult(**data)

        except Exception as e:
            logger.error(f"Erro ao deserializar OrchestrationResult: {e}")
            return None

    def _serialize_agent_result(self, result: AgentExecutionResult) -> Dict[str, Any]:
        """Converte AgentExecutionResult para dict serializável"""
        data = result.dict()

        # Converter datetime para ISO string
        for field in ["start_time", "end_time"]:
            if data.get(field):
                data[field] = data[field].isoformat()

        return data

    def _deserialize_agent_result(self, data: Dict[str, Any]) -> Optional[AgentExecutionResult]:
        """Converte dict para AgentExecutionResult"""
        try:
            # Converter ISO strings para datetime
            for field in ["start_time", "end_time"]:
                if data.get(field):
                    data[field] = datetime.fromisoformat(
                        data[field].replace('Z', '+00:00'))

            # Remover campos extras do MongoDB
            data.pop("_id", None)
            data.pop("orchestration_id", None)
            data.pop("saved_at", None)

            return AgentExecutionResult(**data)

        except Exception as e:
            logger.error(f"Erro ao deserializar AgentExecutionResult: {e}")
            return None


class StorageManager:
    """
    Gerenciador de storage para orquestração

    Fornece interface simplificada para operações de storage
    """

    def __init__(self, mongo_url: str, database_name: str = "scope_ai"):
        """
        Inicializa o gerenciador de storage

        Args:
            mongo_url: URL de conexão MongoDB
            database_name: Nome do banco de dados
        """
        self.mongo_url = mongo_url
        self.database_name = database_name
        self.client: Optional[AsyncIOMotorClient] = None
        self.storage: Optional[OrchestrationStorage] = None

        logger.info(f"StorageManager configurado para {database_name}")

    async def connect(self) -> bool:
        """
        Conecta ao MongoDB e inicializa storage

        Returns:
            True se conectou com sucesso
        """
        try:
            self.client = AsyncIOMotorClient(self.mongo_url)
            database = self.client[self.database_name]

            # Testar conexão
            await database.command("ping")

            self.storage = OrchestrationStorage(database)
            await self.storage.create_indexes()

            logger.info("Conexão com MongoDB estabelecida")
            return True

        except Exception as e:
            logger.error(f"Erro ao conectar com MongoDB: {e}")
            return False

    async def disconnect(self):
        """Desconecta do MongoDB"""
        if self.client:
            self.client.close()
            logger.info("Conexão com MongoDB fechada")

    def get_storage(self) -> OrchestrationStorage:
        """
        Retorna instância do storage

        Returns:
            Instância do OrchestrationStorage

        Raises:
            RuntimeError: Se não estiver conectado
        """
        if not self.storage:
            raise RuntimeError(
                "Storage não inicializado. Chame connect() primeiro.")

        return self.storage


# Instância global do gerenciador (será inicializada pela aplicação)
storage_manager: Optional[StorageManager] = None


def get_storage() -> OrchestrationStorage:
    """
    Função utilitária para obter storage

    Returns:
        Instância do OrchestrationStorage

    Raises:
        RuntimeError: Se storage não estiver inicializado
    """
    if not storage_manager:
        raise RuntimeError("StorageManager não inicializado")

    return storage_manager.get_storage()


async def initialize_storage(mongo_url: str, database_name: str = "scope_ai") -> bool:
    """
    Inicializa storage global

    Args:
        mongo_url: URL de conexão MongoDB
        database_name: Nome do banco de dados

    Returns:
        True se inicializou com sucesso
    """
    global storage_manager

    storage_manager = StorageManager(mongo_url, database_name)
    return await storage_manager.connect()


async def cleanup_storage():
    """Limpa recursos do storage global"""
    global storage_manager

    if storage_manager:
        await storage_manager.disconnect()
        storage_manager = None
