{"version": 3, "sources": ["../../../../../../node_modules/@mermaid-js/parser/dist/chunks/mermaid-parser.core/chunk-V4Q32G6S.mjs"], "sourcesContent": ["import { AbstractMermaidTokenBuilder, CommonValueConverter, MermaidGeneratedSharedModule, PacketGeneratedModule, __name } from \"./chunk-7PKI6E2E.mjs\";\n\n// src/language/packet/module.ts\nimport { EmptyFileSystem, createDefaultCoreModule, createDefaultSharedCoreModule, inject } from \"langium\";\n\n// src/language/packet/tokenBuilder.ts\nvar PacketTokenBuilder = class extends AbstractMermaidTokenBuilder {\n  static {\n    __name(this, \"PacketTokenBuilder\");\n  }\n  constructor() {\n    super([\"packet-beta\"]);\n  }\n};\n\n// src/language/packet/module.ts\nvar PacketModule = {\n  parser: {\n    TokenBuilder: /* @__PURE__ */__name(() => new PacketTokenBuilder(), \"TokenBuilder\"),\n    ValueConverter: /* @__PURE__ */__name(() => new CommonValueConverter(), \"ValueConverter\")\n  }\n};\nfunction createPacketServices(context = EmptyFileSystem) {\n  const shared = inject(createDefaultSharedCoreModule(context), MermaidGeneratedSharedModule);\n  const Packet = inject(createDefaultCoreModule({\n    shared\n  }), PacketGeneratedModule, PacketModule);\n  shared.ServiceRegistry.register(Packet);\n  return {\n    shared,\n    Packet\n  };\n}\n__name(createPacketServices, \"createPacketServices\");\nexport { PacketModule, createPacketServices };"], "mappings": ";;;;;;;;;;;;;;AAMA,IAAI,qBAAqB,cAAc,4BAA4B;AAAA,EACjE,OAAO;AACL,WAAO,MAAM,oBAAoB;AAAA,EACnC;AAAA,EACA,cAAc;AACZ,UAAM,CAAC,aAAa,CAAC;AAAA,EACvB;AACF;AAGA,IAAI,eAAe;AAAA,EACjB,QAAQ;AAAA,IACN,cAA6B,OAAO,MAAM,IAAI,mBAAmB,GAAG,cAAc;AAAA,IAClF,gBAA+B,OAAO,MAAM,IAAI,qBAAqB,GAAG,gBAAgB;AAAA,EAC1F;AACF;AACA,SAAS,qBAAqB,UAAU,iBAAiB;AACvD,QAAM,SAAS,OAAO,8BAA8B,OAAO,GAAG,4BAA4B;AAC1F,QAAM,SAAS,OAAO,wBAAwB;AAAA,IAC5C;AAAA,EACF,CAAC,GAAG,uBAAuB,YAAY;AACvC,SAAO,gBAAgB,SAAS,MAAM;AACtC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,OAAO,sBAAsB,sBAAsB;", "names": []}