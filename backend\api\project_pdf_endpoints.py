"""
Project PDF Endpoints - Extraído de main.py
Responsabilidade única: geração de PDFs de projetos
"""

from io import BytesIO
from fastapi import APIRouter, Request, HTTPException
from fastapi.responses import StreamingResponse

router = APIRouter()


@router.post("/projects/{project_id}/generate-pdf")
async def generate_project_pdf(project_id: str, request: Request):
    """
    Gera relatório PDF profissional para um projeto específico
    """
    try:
        # Importar as ferramentas de PDF
        from tools.reports.html_to_pdf_converter import HTMLToPDFConverter
        from tools.reports.project_pdf_template import ProjectPDFTemplate

        # Receber dados do projeto via request body
        body = await request.json()
        project_data = body.get("project_data", {})
        estimate_data = body.get("estimate_data", {})

        if not project_data:
            raise HTTPException(
                status_code=400, detail="Dados do projeto são obrigatórios")

        # Configurar metadados do PDF
        metadata = {
            "client_name": project_data.get("nome", "Projeto"),
            "project_name": project_data.get("nome", "Projeto"),
            "generated_at": "2025-01-01T12:00:00Z",
            "version": "1.0",
            "author": "ScopeAI Platform"
        }

        # Gerar template HTML do projeto
        template_generator = ProjectPDFTemplate()
        html_content = template_generator.generate_project_html(
            project_data=project_data,
            estimate_data=estimate_data,
            metadata=metadata
        )

        # Converter para PDF
        pdf_converter = HTMLToPDFConverter()
        pdf_bytes = pdf_converter.convert_to_pdf(html_content, metadata)

        # Criar nome do arquivo
        project_name = project_data.get(
            "nome", "projeto").replace(" ", "_").lower()
        filename = f"relatorio_{project_name}_{project_id[:8]}.pdf"

        # Retornar PDF como stream
        return StreamingResponse(
            BytesIO(pdf_bytes),
            media_type="application/pdf",
            headers={
                "Content-Disposition": f"inline; filename={filename}",
                "Content-Type": "application/pdf"
            }
        )

    except ImportError as e:
        raise HTTPException(
            status_code=500,
            detail=f"Módulos de PDF não disponíveis: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao gerar PDF: {str(e)}"
        )
