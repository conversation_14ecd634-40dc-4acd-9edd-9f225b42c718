# Memory Bank - ScopeAI

Sistema de memória estruturada para gestão do conhecimento e continuidade do desenvolvimento do projeto ScopeAI.

## 📁 Estrutura

```
memory-bank/
├── projectbrief.md       # Requisitos e escopo do projeto
├── productContext.md     # Contexto do produto e casos de uso
├── activeContext.md      # Estado atual e decisões recentes
├── systemPatterns.md     # Arquitetura e padrões técnicos
├── techContext.md        # Stack tecnológico e configurações
├── progress.md           # O que funciona e o que falta
├── roadmap/              # Tarefas futuras e planejamento
│   ├── tasks.md          # Tarefas ativas e backlog
│   └── [YYYY_MM_DD]*.md  # Detalhamento de tarefas
├── archive/              # Tarefas concluídas
│   └── index.md          # Índice cronológico
└── logs/                 # Logs de sessões de trabalho
    └── [YYYY_MM_DD]*.log # Registros detalhados
```

## 🔄 Fluxo de Trabalho

1. **Nova Sessão**: Ler TODOS os arquivos do memory-bank
2. **Durante Trabalho**: Atualizar activeContext.md conforme necessário
3. **Finalizar Tarefa**: Mover para archive/ e atualizar index.md
4. **Logs**: Criar log resumido em logs/ ao final de cada sessão

## 📊 Status Atual

- **Branch**: feat-microservices
- **Tarefa Ativa**: [TASK-001] Migração Microserviços - Client Service
- **Progresso**: 10% (4h/40h)
- **Última Atualização**: 2025-01-29

## 🔗 Links Rápidos

### Contexto
- [Project Brief](./projectbrief.md) - Visão geral e requisitos
- [Product Context](./productContext.md) - O que é e para que serve
- [Active Context](./activeContext.md) - Estado atual do desenvolvimento
- [System Patterns](./systemPatterns.md) - Arquitetura e decisões técnicas
- [Tech Context](./techContext.md) - Stack e configurações
- [Progress](./progress.md) - O que está pronto vs pendente

### Trabalho
- [Tasks](./roadmap/tasks.md) - Tarefas ativas e backlog
- [Archive](./archive/index.md) - Histórico de tarefas concluídas

### Logs Recentes
- [2025-01-29 19:54 - Migração Microserviços Início](./logs/2025_01_29__19-54__migracao_microservicos_inicio.log)
- [2025-06-29 19:42 - Memory Bank Initialization](./logs/2025_06_29__19-42__memory_bank_initialization.log)

## 📝 Convenções

### Nomenclatura de Arquivos
- Tarefas: `YYYY_MM_DD__HH-MM__[nome-da-tarefa].md`
- Logs: `YYYY_MM_DD__HH-MM__[contexto].log`

### Status de Tarefas
- ⏳ PENDENTE
- 🔄 EM ANDAMENTO
- ✅ CONCLUÍDO
- ❌ CANCELADO
- 🚫 BLOQUEADO

### Prioridades
- P1: Crítica (bloqueadora)
- P2: Alta (importante)
- P3: Média (nice to have)

## 🎯 Objetivo

Manter continuidade e contexto completo entre sessões de desenvolvimento, garantindo que nenhum conhecimento seja perdido e que o progresso seja sempre incremental e rastreável.

## 🚀 Como Usar

### Para Iniciar uma Nova Sessão
1. Ler TODOS os arquivos do memory-bank (obrigatório)
2. Fazer repo-scan completo do projeto
3. Identificar tarefa ativa em `roadmap/tasks.md`
4. Aplicar técnicas de raciocínio apropriadas

### Para Atualizar o Memory Bank
Digite um dos comandos:
- `umb`
- `update memory-bank`
- `atualize o memory bank`

### Para Criar Nova Tarefa
1. Usar Goal-Oriented Dialogue para entender a intenção
2. Criar arquivo em `roadmap/` com formato: `YYYY_MM_DD__HH-MM__[nome].md`
3. Atualizar `roadmap/tasks.md`
4. Dividir em microtarefas atômicas

### Para Arquivar Tarefa Concluída
1. Mover arquivo de `roadmap/` para `archive/YYYY/MM/`
2. Atualizar `archive/index.md` com entrada completa
3. Marcar como ✅ em `roadmap/tasks.md`

## 🔧 Manutenção

### Política de Retenção
- Arquivos com mais de 6 meses são avaliados
- Critérios de preservação:
  - Score ≥ 28/30
  - Marcos históricos
  - Decisões arquiteturais
  - Lições valiosas

### Frequência de Atualização
- **activeContext.md**: A cada decisão importante
- **progress.md**: Semanalmente
- **tasks.md**: A cada mudança de status
- **Logs**: A cada sessão significativa

## 🎯 Princípios

1. **Fonte da Verdade**: Memory Bank é a referência definitiva
2. **Documentação Viva**: Atualizar constantemente
3. **Rastreabilidade**: Todo trabalho tem histórico
4. **Aprendizado Contínuo**: Preservar conhecimento valioso

---

**Versão**: 2.1.0  
**Criado**: 2025-01-29  
**Mantido por**: AI Assistant + Team 