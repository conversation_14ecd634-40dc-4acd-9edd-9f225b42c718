<!-- Exemplo de Redesign da Página About - Innovation Scope AI -->

<!-- Hero Section com Gradiente e Animação -->
<section class="relative min-h-screen flex items-center justify-center overflow-hidden">
	<!-- Background Gradient Animado -->
	<div class="absolute inset-0 bg-gradient-to-br from-purple-600 via-blue-600 to-indigo-700 animate-gradient"></div>

	<!-- Overlay Pattern -->
	<div class="absolute inset-0 bg-grid-pattern opacity-10"></div>

	<!-- Content -->
	<div class="relative z-10 text-center px-6 max-w-5xl mx-auto">
		<h1 class="text-5xl md:text-7xl font-bold text-white mb-6 animate-fade-up">
			Innovation Scope
			<span class="text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-pink-400">AI</span>
		</h1>
		<p class="text-xl md:text-2xl text-white/90 mb-8 animate-fade-up animation-delay-200">
			Transformando ideias em projetos extraordinários com o poder da Inteligência Artificial
		</p>
		<div class="flex flex-col sm:flex-row gap-4 justify-center animate-fade-up animation-delay-400">
			<button
				class="px-8 py-4 bg-white text-purple-600 rounded-full font-semibold hover:scale-105 transition-transform shadow-xl"
			>
				Começar Agora
			</button>
			<button
				class="px-8 py-4 bg-white/20 backdrop-blur text-white rounded-full font-semibold hover:bg-white/30 transition-all"
			>
				Ver Demo
			</button>
		</div>
	</div>

	<!-- Scroll Indicator -->
	<div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
		<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
			<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
		</svg>
	</div>
</section>

<!-- Features Section com Cards Animados -->
<section class="py-20 px-6 bg-gray-50">
	<div class="max-w-7xl mx-auto">
		<h2 class="text-4xl font-bold text-center mb-16 text-gray-800">Por que escolher o Innovation Scope AI?</h2>

		<div class="grid md:grid-cols-3 gap-8">
			<!-- Feature Card 1 -->
			<div
				class="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2"
			>
				<div
					class="w-16 h-16 bg-gradient-to-br from-purple-500 to-blue-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform"
				>
					<i class="pi pi-bolt text-2xl text-white"></i>
				</div>
				<h3 class="text-2xl font-semibold mb-4 text-gray-800">Análise Instantânea</h3>
				<p class="text-gray-600">
					Nossa IA analisa seu projeto em segundos, identificando oportunidades e gerando insights valiosos.
				</p>
			</div>

			<!-- Feature Card 2 -->
			<div
				class="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2"
			>
				<div
					class="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform"
				>
					<i class="pi pi-chart-line text-2xl text-white"></i>
				</div>
				<h3 class="text-2xl font-semibold mb-4 text-gray-800">Relatórios Detalhados</h3>
				<p class="text-gray-600">
					Receba relatórios completos com análises de viabilidade, roadmap e estimativas precisas.
				</p>
			</div>

			<!-- Feature Card 3 -->
			<div
				class="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2"
			>
				<div
					class="w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform"
				>
					<i class="pi pi-users text-2xl text-white"></i>
				</div>
				<h3 class="text-2xl font-semibold mb-4 text-gray-800">Colaboração em Equipe</h3>
				<p class="text-gray-600">
					Trabalhe em conjunto com sua equipe, compartilhando insights e tomando decisões informadas.
				</p>
			</div>
		</div>
	</div>
</section>

<!-- Timeline Section -->
<section class="py-20 px-6 bg-white">
	<div class="max-w-6xl mx-auto">
		<h2 class="text-4xl font-bold text-center mb-16 text-gray-800">Como Funciona</h2>

		<div class="relative">
			<!-- Timeline Line -->
			<div
				class="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-purple-500 to-blue-500"
			></div>

			<!-- Step 1 -->
			<div class="relative flex items-center mb-16">
				<div class="flex-1 text-right pr-8">
					<h3 class="text-2xl font-semibold mb-2">1. Descreva seu Projeto</h3>
					<p class="text-gray-600">Conte-nos sobre sua ideia, objetivos e visão</p>
				</div>
				<div
					class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold z-10"
				>
					1
				</div>
				<div class="flex-1 pl-8"></div>
			</div>

			<!-- Step 2 -->
			<div class="relative flex items-center mb-16">
				<div class="flex-1"></div>
				<div
					class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold z-10"
				>
					2
				</div>
				<div class="flex-1 text-left pl-8">
					<h3 class="text-2xl font-semibold mb-2">2. IA em Ação</h3>
					<p class="text-gray-600">Nossa IA analisa e processa todas as informações</p>
				</div>
			</div>

			<!-- Step 3 -->
			<div class="relative flex items-center">
				<div class="flex-1 text-right pr-8">
					<h3 class="text-2xl font-semibold mb-2">3. Receba seu Relatório</h3>
					<p class="text-gray-600">Obtenha análises completas e plano de ação detalhado</p>
				</div>
				<div
					class="w-12 h-12 bg-indigo-500 rounded-full flex items-center justify-center text-white font-bold z-10"
				>
					3
				</div>
				<div class="flex-1 pl-8"></div>
			</div>
		</div>
	</div>
</section>

<!-- CTA Section -->
<section class="py-20 px-6 bg-gradient-to-r from-purple-600 to-blue-600">
	<div class="max-w-4xl mx-auto text-center">
		<h2 class="text-4xl font-bold text-white mb-6">Pronto para Transformar sua Ideia em Realidade?</h2>
		<p class="text-xl text-white/90 mb-8">
			Junte-se a milhares de empreendedores que já estão usando o Innovation Scope AI
		</p>
		<button
			class="px-10 py-5 bg-white text-purple-600 rounded-full font-bold text-lg hover:scale-105 transition-transform shadow-xl"
		>
			Começar Gratuitamente
		</button>
	</div>
</section>

<!-- Styles -->
<style>
	@keyframes gradient {
		0%,
		100% {
			background-position: 0% 50%;
		}
		50% {
			background-position: 100% 50%;
		}
	}

	.animate-gradient {
		background-size: 200% 200%;
		animation: gradient 15s ease infinite;
	}

	.bg-grid-pattern {
		background-image:
			linear-gradient(to right, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
			linear-gradient(to bottom, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
		background-size: 50px 50px;
	}

	.animate-fade-up {
		animation: fadeUp 0.8s ease-out forwards;
		opacity: 0;
	}

	.animation-delay-200 {
		animation-delay: 200ms;
	}

	.animation-delay-400 {
		animation-delay: 400ms;
	}

	@keyframes fadeUp {
		from {
			opacity: 0;
			transform: translateY(30px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}
</style>
