{"name": "rethink", "version": "1.0.0", "scripts": {"ng": "ng", "prestart": "npm run format && npm run lint", "start": "ng serve", "build": "ng build --output-hashing=all", "watch": "ng build --watch --configuration development", "test": "ng test", "format": "prettier --write \"src/**/*.{ts,html,scss,json}\"", "format:check": "prettier --check \"src/**/*.{ts,html,scss,json}\"", "lint": "ng lint", "verificar-atualizacoes": "npm outdated", "atualizar": "npx npm-check-updates -u && npm install"}, "private": true, "dependencies": {"@angular/animations": "^19.1.6", "@angular/common": "^19.1.6", "@angular/compiler": "^19.1.6", "@angular/core": "^19.1.6", "@angular/forms": "^19.1.6", "@angular/platform-browser": "^19.1.6", "@angular/platform-browser-dynamic": "^19.1.6", "@angular/router": "^19.1.6", "@primeng/themes": "^19.0.6", "@types/cytoscape": "^3.21.9", "@types/mermaid": "^9.1.0", "chart.js": "^4.4.9", "cytoscape": "^3.32.0", "cytoscape-dagre": "^2.5.0", "cytoscape-elk": "^2.3.0", "fs-extra": "^11.3.0", "mermaid": "^11.6.0", "primeicons": "^7.0.0", "primeng": "^19.0.6", "rxjs": "~7.8.1", "tailwindcss-primeui": "^0.4.0", "tslib": "^2.8.1", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.1.7", "@angular-eslint/builder": "19.1.0", "@angular-eslint/eslint-plugin": "^19.1.0", "@angular-eslint/eslint-plugin-template": "^19.1.0", "@angular-eslint/schematics": "19.1.0", "@angular-eslint/template-parser": "^19.1.0", "@angular/cli": "^19.1.7", "@angular/compiler-cli": "^19.1.6", "@tailwindcss/postcss": "^4.0.6", "@testing-library/angular": "^17.3.7", "@testing-library/dom": "^10.4.0", "@testing-library/user-event": "^14.6.1", "@types/jasmine": "~5.1.6", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "@typescript-eslint/utils": "^8.0.0", "autoprefixer": "^10.4.20", "eslint": "^8.57.0", "jasmine-core": "~5.6.0", "karma": "~6.4.4", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.1", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "npm-check-updates": "^17.1.14", "postcss": "^8.4.35", "prettier": "^3.5.1", "tailwindcss": "^3.4.1", "typescript": "~5.7.3"}}