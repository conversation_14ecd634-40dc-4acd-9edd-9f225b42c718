{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, booleanAttribute, Input, ViewEncapsulation, ChangeDetectionStrategy, Component } from '@angular/core';\nimport { isEmpty } from '@primeuix/utils';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { BaseStyle } from 'primeng/base';\nconst _c0 = [\"*\"];\nconst css = `\n.p-icon {\n    display: inline-block;\n    vertical-align: baseline;\n}\n\n.p-icon-spin {\n    -webkit-animation: p-icon-spin 2s infinite linear;\n    animation: p-icon-spin 2s infinite linear;\n}\n\n@-webkit-keyframes p-icon-spin {\n    0% {\n        -webkit-transform: rotate(0deg);\n        transform: rotate(0deg);\n    }\n    100% {\n        -webkit-transform: rotate(359deg);\n        transform: rotate(359deg);\n    }\n}\n\n@keyframes p-icon-spin {\n    0% {\n        -webkit-transform: rotate(0deg);\n        transform: rotate(0deg);\n    }\n    100% {\n        -webkit-transform: rotate(359deg);\n        transform: rotate(359deg);\n    }\n}\n`;\nclass BaseIconStyle extends BaseStyle {\n  name = 'baseicon';\n  inlineStyles = css;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵBaseIconStyle_BaseFactory;\n    return function BaseIconStyle_Factory(__ngFactoryType__) {\n      return (ɵBaseIconStyle_BaseFactory || (ɵBaseIconStyle_BaseFactory = i0.ɵɵgetInheritedFactory(BaseIconStyle)))(__ngFactoryType__ || BaseIconStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: BaseIconStyle,\n    factory: BaseIconStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BaseIconStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n *\n * [Live Demo](https://www.primeng.org/)\n *\n * @module baseiconstyle\n *\n */\nvar BaseIconClasses;\n(function (BaseIconClasses) {\n  BaseIconClasses[\"root\"] = \"p-icon\";\n})(BaseIconClasses || (BaseIconClasses = {}));\nclass BaseIcon extends BaseComponent {\n  label;\n  spin = false;\n  styleClass;\n  role;\n  ariaLabel;\n  ariaHidden;\n  ngOnInit() {\n    super.ngOnInit();\n    this.getAttributes();\n  }\n  getAttributes() {\n    const isLabelEmpty = isEmpty(this.label);\n    this.role = !isLabelEmpty ? 'img' : undefined;\n    this.ariaLabel = !isLabelEmpty ? this.label : undefined;\n    this.ariaHidden = isLabelEmpty;\n  }\n  getClassNames() {\n    return `p-icon ${this.styleClass ? this.styleClass + ' ' : ''}${this.spin ? 'p-icon-spin' : ''}`;\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵBaseIcon_BaseFactory;\n    return function BaseIcon_Factory(__ngFactoryType__) {\n      return (ɵBaseIcon_BaseFactory || (ɵBaseIcon_BaseFactory = i0.ɵɵgetInheritedFactory(BaseIcon)))(__ngFactoryType__ || BaseIcon);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: BaseIcon,\n    selectors: [[\"ng-component\"]],\n    hostAttrs: [1, \"p-component\", \"p-iconwrapper\"],\n    inputs: {\n      label: \"label\",\n      spin: [2, \"spin\", \"spin\", booleanAttribute],\n      styleClass: \"styleClass\"\n    },\n    features: [i0.ɵɵProvidersFeature([BaseIconStyle]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function BaseIcon_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BaseIcon, [{\n    type: Component,\n    args: [{\n      template: ` <ng-content></ng-content> `,\n      standalone: true,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [BaseIconStyle],\n      host: {\n        class: 'p-component p-iconwrapper'\n      }\n    }]\n  }], null, {\n    label: [{\n      type: Input\n    }],\n    spin: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    styleClass: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BaseIcon, BaseIconClasses, BaseIconStyle };", "map": {"version": 3, "names": ["i0", "Injectable", "booleanAttribute", "Input", "ViewEncapsulation", "ChangeDetectionStrategy", "Component", "isEmpty", "BaseComponent", "BaseStyle", "_c0", "css", "BaseIconStyle", "name", "inlineStyles", "ɵfac", "ɵBaseIconStyle_BaseFactory", "BaseIconStyle_Factory", "__ngFactoryType__", "ɵɵgetInheritedFactory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "BaseIconClasses", "BaseIcon", "label", "spin", "styleClass", "role", "aria<PERSON><PERSON><PERSON>", "ariaHidden", "ngOnInit", "getAttributes", "isLabelEmpty", "undefined", "getClassNames", "ɵBaseIcon_BaseFactory", "BaseIcon_Factory", "ɵcmp", "ɵɵdefineComponent", "selectors", "hostAttrs", "inputs", "features", "ɵɵProvidersFeature", "ɵɵInheritDefinitionFeature", "ngContentSelectors", "decls", "vars", "template", "BaseIcon_Template", "rf", "ctx", "ɵɵprojectionDef", "ɵɵprojection", "encapsulation", "changeDetection", "args", "standalone", "OnPush", "None", "providers", "host", "class", "transform"], "sources": ["C:/projetos/scope-ai/frontend/node_modules/primeng/fesm2022/primeng-icons-baseicon.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, booleanAttribute, Input, ViewEncapsulation, ChangeDetectionStrategy, Component } from '@angular/core';\nimport { isEmpty } from '@primeuix/utils';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { BaseStyle } from 'primeng/base';\n\nconst css = `\n.p-icon {\n    display: inline-block;\n    vertical-align: baseline;\n}\n\n.p-icon-spin {\n    -webkit-animation: p-icon-spin 2s infinite linear;\n    animation: p-icon-spin 2s infinite linear;\n}\n\n@-webkit-keyframes p-icon-spin {\n    0% {\n        -webkit-transform: rotate(0deg);\n        transform: rotate(0deg);\n    }\n    100% {\n        -webkit-transform: rotate(359deg);\n        transform: rotate(359deg);\n    }\n}\n\n@keyframes p-icon-spin {\n    0% {\n        -webkit-transform: rotate(0deg);\n        transform: rotate(0deg);\n    }\n    100% {\n        -webkit-transform: rotate(359deg);\n        transform: rotate(359deg);\n    }\n}\n`;\nclass BaseIconStyle extends BaseStyle {\n    name = 'baseicon';\n    inlineStyles = css;\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: BaseIconStyle, deps: null, target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: BaseIconStyle });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: BaseIconStyle, decorators: [{\n            type: Injectable\n        }] });\n/**\n *\n * [Live Demo](https://www.primeng.org/)\n *\n * @module baseiconstyle\n *\n */\nvar BaseIconClasses;\n(function (BaseIconClasses) {\n    BaseIconClasses[\"root\"] = \"p-icon\";\n})(BaseIconClasses || (BaseIconClasses = {}));\n\nclass BaseIcon extends BaseComponent {\n    label;\n    spin = false;\n    styleClass;\n    role;\n    ariaLabel;\n    ariaHidden;\n    ngOnInit() {\n        super.ngOnInit();\n        this.getAttributes();\n    }\n    getAttributes() {\n        const isLabelEmpty = isEmpty(this.label);\n        this.role = !isLabelEmpty ? 'img' : undefined;\n        this.ariaLabel = !isLabelEmpty ? this.label : undefined;\n        this.ariaHidden = isLabelEmpty;\n    }\n    getClassNames() {\n        return `p-icon ${this.styleClass ? this.styleClass + ' ' : ''}${this.spin ? 'p-icon-spin' : ''}`;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: BaseIcon, deps: null, target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"19.2.10\", type: BaseIcon, isStandalone: true, selector: \"ng-component\", inputs: { label: \"label\", spin: [\"spin\", \"spin\", booleanAttribute], styleClass: \"styleClass\" }, host: { classAttribute: \"p-component p-iconwrapper\" }, providers: [BaseIconStyle], usesInheritance: true, ngImport: i0, template: ` <ng-content></ng-content> `, isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: BaseIcon, decorators: [{\n            type: Component,\n            args: [{\n                    template: ` <ng-content></ng-content> `,\n                    standalone: true,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    providers: [BaseIconStyle],\n                    host: {\n                        class: 'p-component p-iconwrapper'\n                    }\n                }]\n        }], propDecorators: { label: [{\n                type: Input\n            }], spin: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], styleClass: [{\n                type: Input\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BaseIcon, BaseIconClasses, BaseIconStyle };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,gBAAgB,EAAEC,KAAK,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,SAAS,QAAQ,eAAe;AAC1H,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,SAAS,QAAQ,cAAc;AAAC,MAAAC,GAAA;AAEzC,MAAMC,GAAG,GAAG;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,MAAMC,aAAa,SAASH,SAAS,CAAC;EAClCI,IAAI,GAAG,UAAU;EACjBC,YAAY,GAAGH,GAAG;EAClB,OAAOI,IAAI;IAAA,IAAAC,0BAAA;IAAA,gBAAAC,sBAAAC,iBAAA;MAAA,QAAAF,0BAAA,KAAAA,0BAAA,GAA+EhB,EAAE,CAAAmB,qBAAA,CAAQP,aAAa,IAAAM,iBAAA,IAAbN,aAAa;IAAA;EAAA;EACjH,OAAOQ,KAAK,kBAD8EpB,EAAE,CAAAqB,kBAAA;IAAAC,KAAA,EACYV,aAAa;IAAAW,OAAA,EAAbX,aAAa,CAAAG;EAAA;AACzH;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KAH8FxB,EAAE,CAAAyB,iBAAA,CAGJb,aAAa,EAAc,CAAC;IAC5Gc,IAAI,EAAEzB;EACV,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI0B,eAAe;AACnB,CAAC,UAAUA,eAAe,EAAE;EACxBA,eAAe,CAAC,MAAM,CAAC,GAAG,QAAQ;AACtC,CAAC,EAAEA,eAAe,KAAKA,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC;AAE7C,MAAMC,QAAQ,SAASpB,aAAa,CAAC;EACjCqB,KAAK;EACLC,IAAI,GAAG,KAAK;EACZC,UAAU;EACVC,IAAI;EACJC,SAAS;EACTC,UAAU;EACVC,QAAQA,CAAA,EAAG;IACP,KAAK,CAACA,QAAQ,CAAC,CAAC;IAChB,IAAI,CAACC,aAAa,CAAC,CAAC;EACxB;EACAA,aAAaA,CAAA,EAAG;IACZ,MAAMC,YAAY,GAAG9B,OAAO,CAAC,IAAI,CAACsB,KAAK,CAAC;IACxC,IAAI,CAACG,IAAI,GAAG,CAACK,YAAY,GAAG,KAAK,GAAGC,SAAS;IAC7C,IAAI,CAACL,SAAS,GAAG,CAACI,YAAY,GAAG,IAAI,CAACR,KAAK,GAAGS,SAAS;IACvD,IAAI,CAACJ,UAAU,GAAGG,YAAY;EAClC;EACAE,aAAaA,CAAA,EAAG;IACZ,OAAO,UAAU,IAAI,CAACR,UAAU,GAAG,IAAI,CAACA,UAAU,GAAG,GAAG,GAAG,EAAE,GAAG,IAAI,CAACD,IAAI,GAAG,aAAa,GAAG,EAAE,EAAE;EACpG;EACA,OAAOf,IAAI;IAAA,IAAAyB,qBAAA;IAAA,gBAAAC,iBAAAvB,iBAAA;MAAA,QAAAsB,qBAAA,KAAAA,qBAAA,GAtC+ExC,EAAE,CAAAmB,qBAAA,CAsCQS,QAAQ,IAAAV,iBAAA,IAARU,QAAQ;IAAA;EAAA;EAC5G,OAAOc,IAAI,kBAvC+E1C,EAAE,CAAA2C,iBAAA;IAAAjB,IAAA,EAuCJE,QAAQ;IAAAgB,SAAA;IAAAC,SAAA;IAAAC,MAAA;MAAAjB,KAAA;MAAAC,IAAA,sBAAiG5B,gBAAgB;MAAA6B,UAAA;IAAA;IAAAgB,QAAA,GAvCvH/C,EAAE,CAAAgD,kBAAA,CAuCsN,CAACpC,aAAa,CAAC,GAvCvOZ,EAAE,CAAAiD,0BAAA;IAAAC,kBAAA,EAAAxC,GAAA;IAAAyC,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,kBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFvD,EAAE,CAAAyD,eAAA;QAAFzD,EAAE,CAAA0D,YAAA,EAuCgT,CAAC;MAAA;IAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA;AACjZ;AACA;EAAA,QAAApC,SAAA,oBAAAA,SAAA,KAzC8FxB,EAAE,CAAAyB,iBAAA,CAyCJG,QAAQ,EAAc,CAAC;IACvGF,IAAI,EAAEpB,SAAS;IACfuD,IAAI,EAAE,CAAC;MACCR,QAAQ,EAAE,6BAA6B;MACvCS,UAAU,EAAE,IAAI;MAChBF,eAAe,EAAEvD,uBAAuB,CAAC0D,MAAM;MAC/CJ,aAAa,EAAEvD,iBAAiB,CAAC4D,IAAI;MACrCC,SAAS,EAAE,CAACrD,aAAa,CAAC;MAC1BsD,IAAI,EAAE;QACFC,KAAK,EAAE;MACX;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEtC,KAAK,EAAE,CAAC;MACtBH,IAAI,EAAEvB;IACV,CAAC,CAAC;IAAE2B,IAAI,EAAE,CAAC;MACPJ,IAAI,EAAEvB,KAAK;MACX0D,IAAI,EAAE,CAAC;QAAEO,SAAS,EAAElE;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE6B,UAAU,EAAE,CAAC;MACbL,IAAI,EAAEvB;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASyB,QAAQ,EAAED,eAAe,EAAEf,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}