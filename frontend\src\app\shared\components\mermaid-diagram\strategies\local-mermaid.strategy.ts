import { Injectable } from '@angular/core';
import mermaid from 'mermaid';
import { Observable } from 'rxjs';
import { MermaidRenderResult, MermaidRenderStrategy } from './mermaid-render.strategy';

@Injectable()
export class LocalMermaidStrategy implements MermaidRenderStrategy {
	readonly name = 'Local Mermaid';
	readonly supportsDownload = false;
	readonly requiresCodeCleaning = false;

	private initialized = false;

	constructor() {
		this.initializeMermaid();
	}

	render(code: string): Observable<MermaidRenderResult> {
		return new Observable(observer => {
			this.renderDiagram(code)
				.then(svg => {
					observer.next({
						content: svg,
						success: true,
					});
					observer.complete();
				})
				.catch(error => {
					observer.next({
						content: '',
						success: false,
						error: error.message || 'Erro ao renderizar diagrama',
					});
					observer.complete();
				});
		});
	}

	private initializeMermaid(): void {
		if (this.initialized) return;

		mermaid.initialize({
			startOnLoad: false,
			theme: 'default',
			securityLevel: 'loose',
			fontFamily: 'Arial, sans-serif',
			fontSize: 14,
			flowchart: {
				useMaxWidth: true,
				htmlLabels: true,
			},
		});

		this.initialized = true;
	}

	private async renderDiagram(code: string): Promise<string> {
		if (!code || code.trim() === '') {
			throw new Error('Código Mermaid vazio');
		}

		try {
			// Gerar ID único para o diagrama
			const diagramId = `mermaid-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

			// Renderizar o diagrama
			const { svg } = await mermaid.render(diagramId, code);
			return svg;
		} catch (error) {
			console.error('❌ Erro ao renderizar diagrama Mermaid local:', error);
			throw new Error('Erro na renderização local: ' + (error as Error).message);
		}
	}
}
