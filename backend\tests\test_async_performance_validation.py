"""
Testes de Performance da Conversão Assíncrona - ScopeAI MT-5
Valida os benefícios de performance alcançados com a conversão async.
"""

import pytest
import asyncio
import time
from unittest.mock import AsyncMock, MagicMock, patch
from concurrent.futures import ThreadPoolExecutor
import statistics


class TestAsyncPerformanceValidation:
    """Testes de performance para validar benefícios da conversão async"""

    @pytest.fixture
    def setup_performance_data(self):
        """Setup para dados de teste de performance"""
        return {
            "test_data_sets": [
                {"funding": {"amount": "R$ 1M"}, "company": f"Company {i}"}
                for i in range(10)
            ],
            "client_data": {
                "_id": "test_client_id",
                "name": "Performance Test Client",
                "site": "https://test.com"
            }
        }

    # ====================================================================
    # TESTES DE THROUGHPUT E CONCORRÊNCIA
    # ====================================================================

    @pytest.mark.asyncio
    async def test_async_vs_sync_parsing_performance(self, setup_performance_data):
        """Compara performance de parsing assíncrono vs síncrono simulado"""
        
        # Simular parser síncrono (bloqueante)
        def sync_parser_simulation(data):
            time.sleep(0.1)  # Simular processamento bloqueante
            return {"parsed": data, "sync": True}
        
        # Simular parser assíncrono (não-bloqueante)
        async def async_parser_simulation(data):
            await asyncio.sleep(0.1)  # Simular processamento não-bloqueante
            return {"parsed": data, "async": True}
        
        test_datasets = setup_performance_data["test_data_sets"][:5]
        
        # Teste síncrono (sequencial)
        start_time = time.time()
        sync_results = []
        for data in test_datasets:
            result = sync_parser_simulation(data)
            sync_results.append(result)
        sync_time = time.time() - start_time
        
        # Teste assíncrono (concorrente)
        start_time = time.time()
        async_tasks = [async_parser_simulation(data) for data in test_datasets]
        async_results = await asyncio.gather(*async_tasks)
        async_time = time.time() - start_time
        
        # Validações
        assert len(sync_results) == len(async_results) == 5
        assert async_time < sync_time, f"Async ({async_time:.3f}s) deve ser mais rápido que sync ({sync_time:.3f}s)"
        
        speedup = sync_time / async_time
        print(f"📊 Performance: Sync: {sync_time:.3f}s, Async: {async_time:.3f}s, Speedup: {speedup:.2f}x")
        
        # Async deve ser pelo menos 2x mais rápido para 5 operações concorrentes
        assert speedup >= 2.0, f"Speedup insuficiente: {speedup:.2f}x"

    @pytest.mark.asyncio
    async def test_database_operations_concurrency(self, setup_performance_data):
        """Testa concorrência de operações de banco assíncronas"""
        
        # Mock de operação de banco assíncrona
        async def mock_async_db_operation(query_id):
            await asyncio.sleep(0.05)  # Simular latência de rede
            return {"query_id": query_id, "result": f"data_{query_id}"}
        
        # Mock de operação de banco síncrona
        def mock_sync_db_operation(query_id):
            time.sleep(0.05)  # Simular latência bloqueante
            return {"query_id": query_id, "result": f"data_{query_id}"}
        
        query_count = 8
        
        # Teste síncrono
        start_time = time.time()
        sync_results = []
        for i in range(query_count):
            result = mock_sync_db_operation(i)
            sync_results.append(result)
        sync_time = time.time() - start_time
        
        # Teste assíncrono
        start_time = time.time()
        async_tasks = [mock_async_db_operation(i) for i in range(query_count)]
        async_results = await asyncio.gather(*async_tasks)
        async_time = time.time() - start_time
        
        assert len(async_results) == query_count
        speedup = sync_time / async_time
        
        print(f"📊 DB Operations: {query_count} queries - Sync: {sync_time:.3f}s, Async: {async_time:.3f}s, Speedup: {speedup:.2f}x")
        
        # Para operações I/O bound, speedup deve ser significativo
        assert speedup >= 3.0, f"DB speedup insuficiente: {speedup:.2f}x"

    @pytest.mark.asyncio
    async def test_task_manager_throughput(self, setup_performance_data):
        """Testa throughput do TaskManager vs execução sequencial"""
        try:
            from shared.task_manager import create_background_task, TaskPriority
            
            async def throughput_task(task_id):
                await asyncio.sleep(0.08)
                return {"throughput_test": task_id}
            
            task_count = 12
            
            # Teste com TaskManager (concorrente)
            start_time = time.time()
            task_ids = []
            for i in range(task_count):
                task_id = await create_background_task(
                    f"throughput_test_{i}",
                    throughput_task,
                    TaskPriority.MEDIUM,
                    f"client_{i}",
                    f"Client {i}",
                    1,
                    i
                )
                task_ids.append(task_id)
            
            # Aguardar conclusão
            await asyncio.sleep(0.5)
            taskmanager_time = time.time() - start_time
            
            # Teste sequencial para comparação
            start_time = time.time()
            sequential_results = []
            for i in range(task_count):
                result = await throughput_task(i)
                sequential_results.append(result)
            sequential_time = time.time() - start_time
            
            assert len(task_ids) == task_count
            speedup = sequential_time / taskmanager_time
            
            print(f"📊 TaskManager throughput: {task_count} tasks - Sequential: {sequential_time:.3f}s, TaskManager: {taskmanager_time:.3f}s, Speedup: {speedup:.2f}x")
            
            # TaskManager deve ser mais eficiente
            assert speedup >= 1.5, f"TaskManager speedup insuficiente: {speedup:.2f}x"
            
        except ImportError:
            pytest.skip("TaskManager não disponível")

    # ====================================================================
    # TESTES DE LATÊNCIA E RESPONSIVIDADE
    # ====================================================================

    @pytest.mark.asyncio
    async def test_async_response_time_improvement(self, setup_performance_data):
        """Testa melhoria no tempo de resposta com operações assíncronas"""
        
        async def async_endpoint_simulation():
            # Simular endpoint que faz múltiplas operações em paralelo
            tasks = [
                asyncio.sleep(0.1),  # Simular busca no banco
                asyncio.sleep(0.15), # Simular parsing
                asyncio.sleep(0.12), # Simular processamento
                asyncio.sleep(0.08)  # Simular validação
            ]
            
            await asyncio.gather(*tasks)
            return {"endpoint": "async", "operations": 4}
        
        def sync_endpoint_simulation():
            # Simular endpoint síncrono que faz operações sequenciais
            time.sleep(0.1)   # Busca no banco
            time.sleep(0.15)  # Parsing
            time.sleep(0.12)  # Processamento
            time.sleep(0.08)  # Validação
            return {"endpoint": "sync", "operations": 4}
        
        # Medir tempos de resposta
        start_time = time.time()
        sync_result = sync_endpoint_simulation()
        sync_response_time = time.time() - start_time
        
        start_time = time.time()
        async_result = await async_endpoint_simulation()
        async_response_time = time.time() - start_time
        
        improvement = (sync_response_time - async_response_time) / sync_response_time * 100
        
        print(f"📊 Response Time: Sync: {sync_response_time:.3f}s, Async: {async_response_time:.3f}s, Improvement: {improvement:.1f}%")
        
        assert async_response_time < sync_response_time
        assert improvement >= 60, f"Melhoria insuficiente: {improvement:.1f}%"

    @pytest.mark.asyncio
    async def test_concurrent_user_simulation(self, setup_performance_data):
        """Simula múltiplos usuários fazendo requests simultâneos"""
        
        async def user_request_simulation(user_id):
            # Simular request de usuário com múltiplas operações
            start_time = time.time()
            
            # Operações que um usuário típico faria
            operations = [
                asyncio.sleep(0.05),  # Autenticação
                asyncio.sleep(0.1),   # Busca de dados
                asyncio.sleep(0.08),  # Processamento
                asyncio.sleep(0.06)   # Resposta
            ]
            
            await asyncio.gather(*operations)
            response_time = time.time() - start_time
            
            return {
                "user_id": user_id,
                "response_time": response_time,
                "operations_completed": len(operations)
            }
        
        # Simular 10 usuários simultâneos
        user_count = 10
        start_time = time.time()
        
        user_tasks = [user_request_simulation(i) for i in range(user_count)]
        user_results = await asyncio.gather(*user_tasks)
        
        total_time = time.time() - start_time
        
        # Analisar resultados
        response_times = [result["response_time"] for result in user_results]
        avg_response_time = statistics.mean(response_times)
        max_response_time = max(response_times)
        
        assert len(user_results) == user_count
        # Com 10 usuários simultâneos, tempo total deve ser próximo ao tempo individual
        assert total_time < 0.4, f"Tempo total muito alto para {user_count} usuários: {total_time:.3f}s"
        assert avg_response_time < 0.35, f"Tempo médio de resposta muito alto: {avg_response_time:.3f}s"
        
        print(f"📊 Concurrent Users: {user_count} users, Total: {total_time:.3f}s, Avg Response: {avg_response_time:.3f}s, Max: {max_response_time:.3f}s")

    # ====================================================================
    # TESTES DE RESOURCE UTILIZATION
    # ====================================================================

    @pytest.mark.asyncio
    async def test_cpu_utilization_efficiency(self, setup_performance_data):
        """Testa eficiência de utilização de CPU com operações assíncronas"""
        
        async def cpu_intensive_task(task_id):
            # Simular operação CPU-intensiva que deveria usar thread pool
            await asyncio.to_thread(self._cpu_intensive_operation, task_id)
            return {"cpu_task": task_id}
        
        def _cpu_intensive_operation(self, task_id):
            # Simular processamento CPU-intensivo
            result = 0
            for i in range(10000):
                result += i * task_id
            return result
        
        # Testar com múltiplas operações CPU-intensivas
        task_count = 6
        start_time = time.time()
        
        cpu_tasks = [cpu_intensive_task(i) for i in range(task_count)]
        cpu_results = await asyncio.gather(*cpu_tasks)
        
        cpu_time = time.time() - start_time
        
        assert len(cpu_results) == task_count
        # Operações CPU-intensivas em thread pool devem ser eficientes
        assert cpu_time < 2.0, f"CPU tasks muito lentas: {cpu_time:.3f}s"
        
        print(f"📊 CPU Utilization: {task_count} CPU-intensive tasks em {cpu_time:.3f}s")

    @pytest.mark.asyncio
    async def test_memory_efficiency_async_operations(self, setup_performance_data):
        """Testa eficiência de memória com operações assíncronas"""
        
        import gc
        import tracemalloc
        
        tracemalloc.start()
        gc.collect()
        
        async def memory_efficient_task(data_size):
            # Simular operação que processa dados sem manter tudo em memória
            result = []
            for i in range(data_size):
                # Processar em chunks pequenos
                chunk = {"item": i, "processed": True}
                result.append(chunk)
                if i % 100 == 0:
                    await asyncio.sleep(0)  # Yield control
            
            return {"processed_items": len(result)}
        
        # Processar multiple datasets
        data_sizes = [500, 300, 400, 250, 350]
        
        memory_tasks = [memory_efficient_task(size) for size in data_sizes]
        memory_results = await asyncio.gather(*memory_tasks)
        
        current, peak = tracemalloc.get_traced_memory()
        tracemalloc.stop()
        
        total_items = sum(result["processed_items"] for result in memory_results)
        
        assert len(memory_results) == len(data_sizes)
        assert total_items == sum(data_sizes)
        
        # Verificar eficiência de memória (limite: 20MB)
        peak_mb = peak / 1024 / 1024
        assert peak_mb < 20, f"Uso de memória muito alto: {peak_mb:.2f} MB"
        
        print(f"📊 Memory Efficiency: {total_items} items processados, Peak memory: {peak_mb:.2f} MB")

    # ====================================================================
    # TESTES DE SCALABILITY
    # ====================================================================

    @pytest.mark.asyncio
    async def test_scaling_behavior(self, setup_performance_data):
        """Testa comportamento de escala com diferentes cargas de trabalho"""
        
        async def scalable_task(complexity_level):
            # Simular task com diferentes níveis de complexidade
            sleep_time = complexity_level * 0.02
            await asyncio.sleep(sleep_time)
            return {"complexity": complexity_level, "time": sleep_time}
        
        # Testar diferentes escalas
        scales = [5, 10, 20, 30]
        scale_results = {}
        
        for scale in scales:
            start_time = time.time()
            
            # Criar tasks para cada escala
            scale_tasks = [scalable_task(i % 5 + 1) for i in range(scale)]
            results = await asyncio.gather(*scale_tasks)
            
            elapsed = time.time() - start_time
            scale_results[scale] = {
                "time": elapsed,
                "throughput": scale / elapsed,
                "tasks": len(results)
            }
        
        # Analisar escalabilidade
        for scale, result in scale_results.items():
            print(f"📊 Scale {scale}: {result['time']:.3f}s, Throughput: {result['throughput']:.1f} tasks/s")
        
        # Verificar que throughput não degrada drasticamente
        throughputs = [result["throughput"] for result in scale_results.values()]
        min_throughput = min(throughputs)
        max_throughput = max(throughputs)
        
        # Degradação não deve ser maior que 50%
        degradation = (max_throughput - min_throughput) / max_throughput
        assert degradation < 0.5, f"Degradação de throughput muito alta: {degradation:.2%}"

    # ====================================================================
    # HELPER METHODS
    # ====================================================================

    def _cpu_intensive_operation(self, task_id):
        """Operação CPU-intensiva para testes"""
        result = 0
        for i in range(10000):
            result += i * task_id
        return result


# =====================================================================
# CONFIGURAÇÃO
# =====================================================================

def pytest_configure(config):
    """Configuração do pytest para testes de performance"""
    config.addinivalue_line("markers", "asyncio: mark test as async")
    config.addinivalue_line("markers", "performance: mark test as performance")


if __name__ == "__main__":
    import sys
    sys.exit(pytest.main([__file__, "-v", "--tb=short"])) 