"""
Database Migration Helper
Utilities to help migrate from synchronous pymongo to async motor operations
"""
import logging
from typing import Dict, Any, Optional, List, Callable
from functools import wraps
import asyncio
from bson import ObjectId

logger = logging.getLogger(__name__)


class DatabaseMigrationHelper:
    """
    Helper class to facilitate migration from sync to async MongoDB operations
    """
    
    @staticmethod
    def sync_to_async_wrapper(sync_func: Callable) -> Callable:
        """
        Wrapper to convert synchronous database operations to async
        This is a temporary solution during migration
        
        Usage:
            @sync_to_async_wrapper
            def find_client(client_id):
                return clients_collection.find_one({"_id": ObjectId(client_id)})
        """
        @wraps(sync_func)
        async def async_wrapper(*args, **kwargs):
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, sync_func, *args, **kwargs)
        return async_wrapper
    
    @staticmethod
    def create_async_endpoint(endpoint_func: Callable) -> Callable:
        """
        Decorator to ensure endpoints are properly async
        """
        @wraps(endpoint_func)
        async def wrapper(*args, **kwargs):
            if asyncio.iscoroutinefunction(endpoint_func):
                return await endpoint_func(*args, **kwargs)
            else:
                # Run sync function in executor to avoid blocking
                loop = asyncio.get_event_loop()
                return await loop.run_in_executor(None, endpoint_func, *args, **kwargs)
        return wrapper


# Migration guide documentation
MIGRATION_GUIDE = """
# Migration Guide: Sync to Async MongoDB Operations

## Step 1: Update imports
Replace:
    from clients.db import clients_collection
With:
    from clients.async_db import async_database

## Step 2: Update database operations

### Find operations:
Replace:
    client = clients_collection.find_one({"_id": ObjectId(client_id)})
With:
    client = await async_database.find_client_by_id(client_id)

### Update operations:
Replace:
    clients_collection.update_one(
        {"_id": ObjectId(client_id)},
        {"$set": update_data}
    )
With:
    await async_database.update_client(client_id, update_data)

### Insert operations:
Replace:
    result = clients_collection.insert_one(client_data)
    client_id = str(result.inserted_id)
With:
    client_id = await async_database.insert_client(client_data)

### Find multiple:
Replace:
    clients = list(clients_collection.find(filter_dict).limit(10))
With:
    clients = await async_database.find_clients(filter_dict, limit=10)

## Step 3: Update function signatures
Ensure all functions that perform database operations are async:

Replace:
    def get_client(client_id: str):
        client = clients_collection.find_one({"_id": ObjectId(client_id)})
        return client

With:
    async def get_client(client_id: str):
        client = await async_database.find_client_by_id(client_id)
        return client

## Step 4: Update endpoint handlers
Ensure all FastAPI endpoints that call async database operations are async:

Replace:
    @router.get("/clients/{client_id}")
    def get_client_endpoint(client_id: str):
        client = get_client(client_id)
        return client

With:
    @router.get("/clients/{client_id}")
    async def get_client_endpoint(client_id: str):
        client = await get_client(client_id)
        return client

## Step 5: Handle background tasks
For background tasks, ensure they are async and use async database operations:

Replace:
    def process_client(client_id: str):
        client = clients_collection.find_one({"_id": ObjectId(client_id)})
        # processing...
        clients_collection.update_one(...)

With:
    async def process_client(client_id: str):
        client = await async_database.find_client_by_id(client_id)
        # processing...
        await async_database.update_client(...)

## Common Pitfalls to Avoid:
1. Don't mix sync and async operations in the same function
2. Always await async operations
3. Ensure parent functions are async if they call async operations
4. Use async context managers when available
5. Be careful with loops - use asyncio.gather() for concurrent operations

## Performance Benefits:
- Non-blocking I/O operations
- Better concurrency handling
- Improved response times for API endpoints
- Ability to handle more concurrent requests
"""


def print_migration_guide():
    """Print the migration guide"""
    print(MIGRATION_GUIDE)


# Example usage functions
async def example_migration():
    """
    Example showing how to migrate from sync to async operations
    """
    from clients.async_db import async_database
    
    # Example 1: Simple find operation
    client = await async_database.find_client_by_id("client_id_here")
    
    # Example 2: Update operation
    success = await async_database.update_client(
        "client_id_here",
        {"status": "processing", "updated_at": "2024-01-01"}
    )
    
    # Example 3: Batch operations with asyncio.gather
    client_ids = ["id1", "id2", "id3"]
    clients = await asyncio.gather(*[
        async_database.find_client_by_id(client_id) 
        for client_id in client_ids
    ])
    
    return clients