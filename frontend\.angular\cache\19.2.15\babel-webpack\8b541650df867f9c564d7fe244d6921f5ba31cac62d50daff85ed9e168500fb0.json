{"ast": null, "code": "import { Subscription } from '../Subscription';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { arrRemove } from '../util/arrRemove';\nimport { asyncScheduler } from '../scheduler/async';\nimport { popScheduler } from '../util/args';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function bufferTime(bufferTimeSpan, ...otherArgs) {\n  var _a, _b;\n  const scheduler = (_a = popScheduler(otherArgs)) !== null && _a !== void 0 ? _a : asyncScheduler;\n  const bufferCreationInterval = (_b = otherArgs[0]) !== null && _b !== void 0 ? _b : null;\n  const maxBufferSize = otherArgs[1] || Infinity;\n  return operate((source, subscriber) => {\n    let bufferRecords = [];\n    let restartOnEmit = false;\n    const emit = record => {\n      const {\n        buffer,\n        subs\n      } = record;\n      subs.unsubscribe();\n      arrRemove(bufferRecords, record);\n      subscriber.next(buffer);\n      restartOnEmit && startBuffer();\n    };\n    const startBuffer = () => {\n      if (bufferRecords) {\n        const subs = new Subscription();\n        subscriber.add(subs);\n        const buffer = [];\n        const record = {\n          buffer,\n          subs\n        };\n        bufferRecords.push(record);\n        executeSchedule(subs, scheduler, () => emit(record), bufferTimeSpan);\n      }\n    };\n    if (bufferCreationInterval !== null && bufferCreationInterval >= 0) {\n      executeSchedule(subscriber, scheduler, startBuffer, bufferCreationInterval, true);\n    } else {\n      restartOnEmit = true;\n    }\n    startBuffer();\n    const bufferTimeSubscriber = createOperatorSubscriber(subscriber, value => {\n      const recordsCopy = bufferRecords.slice();\n      for (const record of recordsCopy) {\n        const {\n          buffer\n        } = record;\n        buffer.push(value);\n        maxBufferSize <= buffer.length && emit(record);\n      }\n    }, () => {\n      while (bufferRecords === null || bufferRecords === void 0 ? void 0 : bufferRecords.length) {\n        subscriber.next(bufferRecords.shift().buffer);\n      }\n      bufferTimeSubscriber === null || bufferTimeSubscriber === void 0 ? void 0 : bufferTimeSubscriber.unsubscribe();\n      subscriber.complete();\n      subscriber.unsubscribe();\n    }, undefined, () => bufferRecords = null);\n    source.subscribe(bufferTimeSubscriber);\n  });\n}", "map": {"version": 3, "names": ["Subscription", "operate", "createOperatorSubscriber", "arr<PERSON><PERSON><PERSON>", "asyncScheduler", "popScheduler", "executeSchedule", "bufferTime", "bufferTimeSpan", "otherArgs", "_a", "_b", "scheduler", "bufferCreationInterval", "maxBufferSize", "Infinity", "source", "subscriber", "bufferRecords", "restartOnEmit", "emit", "record", "buffer", "subs", "unsubscribe", "next", "startBuffer", "add", "push", "bufferTimeSubscriber", "value", "recordsCopy", "slice", "length", "shift", "complete", "undefined", "subscribe"], "sources": ["C:/projetos/scope-ai/frontend/node_modules/rxjs/dist/esm/internal/operators/bufferTime.js"], "sourcesContent": ["import { Subscription } from '../Subscription';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { arrRemove } from '../util/arrRemove';\nimport { asyncScheduler } from '../scheduler/async';\nimport { popScheduler } from '../util/args';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function bufferTime(bufferTimeSpan, ...otherArgs) {\n    var _a, _b;\n    const scheduler = (_a = popScheduler(otherArgs)) !== null && _a !== void 0 ? _a : asyncScheduler;\n    const bufferCreationInterval = (_b = otherArgs[0]) !== null && _b !== void 0 ? _b : null;\n    const maxBufferSize = otherArgs[1] || Infinity;\n    return operate((source, subscriber) => {\n        let bufferRecords = [];\n        let restartOnEmit = false;\n        const emit = (record) => {\n            const { buffer, subs } = record;\n            subs.unsubscribe();\n            arrRemove(bufferRecords, record);\n            subscriber.next(buffer);\n            restartOnEmit && startBuffer();\n        };\n        const startBuffer = () => {\n            if (bufferRecords) {\n                const subs = new Subscription();\n                subscriber.add(subs);\n                const buffer = [];\n                const record = {\n                    buffer,\n                    subs,\n                };\n                bufferRecords.push(record);\n                executeSchedule(subs, scheduler, () => emit(record), bufferTimeSpan);\n            }\n        };\n        if (bufferCreationInterval !== null && bufferCreationInterval >= 0) {\n            executeSchedule(subscriber, scheduler, startBuffer, bufferCreationInterval, true);\n        }\n        else {\n            restartOnEmit = true;\n        }\n        startBuffer();\n        const bufferTimeSubscriber = createOperatorSubscriber(subscriber, (value) => {\n            const recordsCopy = bufferRecords.slice();\n            for (const record of recordsCopy) {\n                const { buffer } = record;\n                buffer.push(value);\n                maxBufferSize <= buffer.length && emit(record);\n            }\n        }, () => {\n            while (bufferRecords === null || bufferRecords === void 0 ? void 0 : bufferRecords.length) {\n                subscriber.next(bufferRecords.shift().buffer);\n            }\n            bufferTimeSubscriber === null || bufferTimeSubscriber === void 0 ? void 0 : bufferTimeSubscriber.unsubscribe();\n            subscriber.complete();\n            subscriber.unsubscribe();\n        }, undefined, () => (bufferRecords = null));\n        source.subscribe(bufferTimeSubscriber);\n    });\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,SAASC,cAAc,QAAQ,oBAAoB;AACnD,SAASC,YAAY,QAAQ,cAAc;AAC3C,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAO,SAASC,UAAUA,CAACC,cAAc,EAAE,GAAGC,SAAS,EAAE;EACrD,IAAIC,EAAE,EAAEC,EAAE;EACV,MAAMC,SAAS,GAAG,CAACF,EAAE,GAAGL,YAAY,CAACI,SAAS,CAAC,MAAM,IAAI,IAAIC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGN,cAAc;EAChG,MAAMS,sBAAsB,GAAG,CAACF,EAAE,GAAGF,SAAS,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI;EACxF,MAAMG,aAAa,GAAGL,SAAS,CAAC,CAAC,CAAC,IAAIM,QAAQ;EAC9C,OAAOd,OAAO,CAAC,CAACe,MAAM,EAAEC,UAAU,KAAK;IACnC,IAAIC,aAAa,GAAG,EAAE;IACtB,IAAIC,aAAa,GAAG,KAAK;IACzB,MAAMC,IAAI,GAAIC,MAAM,IAAK;MACrB,MAAM;QAAEC,MAAM;QAAEC;MAAK,CAAC,GAAGF,MAAM;MAC/BE,IAAI,CAACC,WAAW,CAAC,CAAC;MAClBrB,SAAS,CAACe,aAAa,EAAEG,MAAM,CAAC;MAChCJ,UAAU,CAACQ,IAAI,CAACH,MAAM,CAAC;MACvBH,aAAa,IAAIO,WAAW,CAAC,CAAC;IAClC,CAAC;IACD,MAAMA,WAAW,GAAGA,CAAA,KAAM;MACtB,IAAIR,aAAa,EAAE;QACf,MAAMK,IAAI,GAAG,IAAIvB,YAAY,CAAC,CAAC;QAC/BiB,UAAU,CAACU,GAAG,CAACJ,IAAI,CAAC;QACpB,MAAMD,MAAM,GAAG,EAAE;QACjB,MAAMD,MAAM,GAAG;UACXC,MAAM;UACNC;QACJ,CAAC;QACDL,aAAa,CAACU,IAAI,CAACP,MAAM,CAAC;QAC1Bf,eAAe,CAACiB,IAAI,EAAEX,SAAS,EAAE,MAAMQ,IAAI,CAACC,MAAM,CAAC,EAAEb,cAAc,CAAC;MACxE;IACJ,CAAC;IACD,IAAIK,sBAAsB,KAAK,IAAI,IAAIA,sBAAsB,IAAI,CAAC,EAAE;MAChEP,eAAe,CAACW,UAAU,EAAEL,SAAS,EAAEc,WAAW,EAAEb,sBAAsB,EAAE,IAAI,CAAC;IACrF,CAAC,MACI;MACDM,aAAa,GAAG,IAAI;IACxB;IACAO,WAAW,CAAC,CAAC;IACb,MAAMG,oBAAoB,GAAG3B,wBAAwB,CAACe,UAAU,EAAGa,KAAK,IAAK;MACzE,MAAMC,WAAW,GAAGb,aAAa,CAACc,KAAK,CAAC,CAAC;MACzC,KAAK,MAAMX,MAAM,IAAIU,WAAW,EAAE;QAC9B,MAAM;UAAET;QAAO,CAAC,GAAGD,MAAM;QACzBC,MAAM,CAACM,IAAI,CAACE,KAAK,CAAC;QAClBhB,aAAa,IAAIQ,MAAM,CAACW,MAAM,IAAIb,IAAI,CAACC,MAAM,CAAC;MAClD;IACJ,CAAC,EAAE,MAAM;MACL,OAAOH,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACe,MAAM,EAAE;QACvFhB,UAAU,CAACQ,IAAI,CAACP,aAAa,CAACgB,KAAK,CAAC,CAAC,CAACZ,MAAM,CAAC;MACjD;MACAO,oBAAoB,KAAK,IAAI,IAAIA,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,oBAAoB,CAACL,WAAW,CAAC,CAAC;MAC9GP,UAAU,CAACkB,QAAQ,CAAC,CAAC;MACrBlB,UAAU,CAACO,WAAW,CAAC,CAAC;IAC5B,CAAC,EAAEY,SAAS,EAAE,MAAOlB,aAAa,GAAG,IAAK,CAAC;IAC3CF,MAAM,CAACqB,SAAS,CAACR,oBAAoB,CAAC;EAC1C,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}