"""
APIRouter - Roteador Central da API

Este módulo implementa o roteador principal que unifica todos os 
endpoints da API em uma estrutura consistente e organizada.
"""

import logging
from typing import Dict, Any, Optional
from fastapi import APIRouter as FastAPIRouter, Request, HTTPException
from fastapi.responses import JSONResponse
from .response_formatter import response_formatter
from .error_handler import error_handler, APIError


logger = logging.getLogger(__name__)


class APIRouter:
    """
    Roteador Central da API ScopeAI

    Responsável por:
    - Organizar endpoints em estrutura hierárquica
    - Aplicar middlewares globais
    - Configurar tratamento de erros
    - Gerar documentação automática
    - Integrar todos os módulos do sistema
    """

    def __init__(self):
        """Inicializa o roteador central"""
        # Roteador principal com prefixo /api/v1
        self.main_router = FastAPIRouter(
            prefix="/api/v1",
            tags=["API v1"],
            responses={
                404: {"description": "Recurso não encontrado"},
                422: {"description": "Dados de entrada inválidos"},
                500: {"description": "Erro interno do servidor"}
            }
        )

        # Roteadores específicos por módulo
        self.clients_router = FastAPIRouter(
            prefix="/clients", tags=["Clientes"])
        self.diagnostics_router = FastAPIRouter(
            prefix="/diagnostics", tags=["Diagnósticos"])
        self.enrichment_router = FastAPIRouter(
            prefix="/enrichment", tags=["Enrichment"])
        self.competitive_router = FastAPIRouter(
            prefix="/competitive", tags=["Análise Competitiva"])
        self.reports_router = FastAPIRouter(
            prefix="/reports", tags=["Relatórios"])
        self.market_router = FastAPIRouter(
            prefix="/market", tags=["Pesquisa de Mercado"])
        self.digital_router = FastAPIRouter(
            prefix="/digital", tags=["Presença Digital"])
        self.admin_router = FastAPIRouter(
            prefix="/admin", tags=["Administração"])

        # Configurar endpoints básicos
        self._setup_health_endpoints()
        self._setup_error_handlers()

        # Incluir roteadores de módulos
        self._include_module_routers()

    def _setup_health_endpoints(self):
        """Configura endpoints de saúde da API"""

        @self.main_router.get("/health", summary="Status da API")
        async def health_check():
            """Endpoint de verificação de saúde da API"""
            return response_formatter.success(
                data={
                    "status": "healthy",
                    "version": "1.0",
                    "modules": {
                        "clients": "operational",
                        "diagnostics": "operational",
                        "enrichment": "operational",
                        "competitive": "operational",
                        "reports": "operational",
                        "market": "operational",
                        "digital": "operational"
                    }
                },
                message="API funcionando corretamente"
            )

        @self.main_router.get("/version", summary="Versão da API")
        async def version_info():
            """Informações de versão da API"""
            return response_formatter.success(
                data={
                    "api_version": "1.0",
                    "build_date": "2024-12-30",
                    "environment": "development",
                    "features": [
                        "authentication",
                        "validation",
                        "error_handling",
                        "pagination",
                        "documentation"
                    ]
                },
                message="Informações de versão"
            )

    def _setup_error_handlers(self):
        """
        Configura handlers de erro personalizados

        Nota: Exception handlers devem ser configurados na aplicação principal,
        não no router. Este método está disponível para referência.
        """
        pass  # Implementação será feita no main.py

    def _include_module_routers(self):
        """Inclui roteadores de todos os módulos"""

        # Incluir roteadores no roteador principal
        self.main_router.include_router(self.clients_router)
        self.main_router.include_router(self.diagnostics_router)
        self.main_router.include_router(self.enrichment_router)
        self.main_router.include_router(self.competitive_router)
        self.main_router.include_router(self.reports_router)
        self.main_router.include_router(self.market_router)
        self.main_router.include_router(self.digital_router)
        self.main_router.include_router(self.admin_router)

    def setup_clients_endpoints(self):
        """Configura endpoints de clientes"""

        @self.clients_router.get("/", summary="Listar clientes")
        async def list_clients(
            request: Request,
            page: int = 1,
            per_page: int = 20,
            search: Optional[str] = None,
            status: Optional[str] = None
        ):
            """Lista clientes com paginação e filtros"""
            # Implementação será conectada ao módulo clients existente
            return response_formatter.success(
                data={
                    "message": "Endpoint de clientes será integrado com módulo existente",
                    "filters": {
                        "page": page,
                        "per_page": per_page,
                        "search": search,
                        "status": status
                    }
                },
                message="Clientes em desenvolvimento"
            )

        @self.clients_router.get("/{client_id}", summary="Obter cliente")
        async def get_client(request: Request, client_id: str):
            """Obtém dados de um cliente específico"""
            return response_formatter.success(
                data={
                    "client_id": client_id,
                    "message": "Integração com módulo clients em desenvolvimento"
                },
                message="Cliente encontrado"
            )

        @self.clients_router.post("/", summary="Criar cliente")
        async def create_client(request: Request, client_data: Dict[str, Any]):
            """Cria novo cliente"""
            return response_formatter.success(
                data={
                    "message": "Criação de cliente será integrada"
                },
                message="Cliente criado com sucesso"
            )

    def setup_diagnostics_endpoints(self):
        """Configura endpoints de diagnósticos"""

        @self.diagnostics_router.post("/run", summary="Executar diagnóstico")
        async def run_diagnostic(request: Request, diagnostic_config: Dict[str, Any]):
            """Executa diagnóstico técnico completo"""
            return response_formatter.success(
                data={
                    "diagnostic_id": "diag_123",
                    "status": "running",
                    "message": "Integração com módulo diagnostics em desenvolvimento"
                },
                message="Diagnóstico iniciado"
            )

        @self.diagnostics_router.get("/{diagnostic_id}", summary="Status do diagnóstico")
        async def get_diagnostic_status(request: Request, diagnostic_id: str):
            """Obtém status de um diagnóstico"""
            return response_formatter.success(
                data={
                    "diagnostic_id": diagnostic_id,
                    "status": "completed",
                    "message": "Integração em desenvolvimento"
                },
                message="Status do diagnóstico"
            )

    def setup_reports_endpoints(self):
        """Configura endpoints de reports"""

        @self.reports_router.post("/generate", summary="Gerar relatório")
        async def generate_report(request: Request, report_config: Dict[str, Any]):
            """Gera relatório estruturado"""
            return response_formatter.success(
                data={
                    "report_id": "report_123",
                    "status": "generating",
                    "message": "Integração com sistema de reports em desenvolvimento"
                },
                message="Relatório em geração"
            )

        @self.reports_router.get("/{report_id}", summary="Obter relatório")
        async def get_report(request: Request, report_id: str):
            """Obtém relatório gerado"""
            return response_formatter.success(
                data={
                    "report_id": report_id,
                    "status": "completed",
                    "message": "Integração em desenvolvimento"
                },
                message="Relatório encontrado"
            )

        @self.reports_router.get("/{report_id}/dashboard", summary="Dashboard do relatório")
        async def get_report_dashboard(request: Request, report_id: str):
            """Obtém configuração do dashboard para o relatório"""
            return response_formatter.success(
                data={
                    "dashboard_id": f"dash_{report_id}",
                    "layout": {"columns": 12, "responsive": True},
                    "widgets": [],
                    "message": "Dashboard config em desenvolvimento"
                },
                message="Dashboard configurado"
            )

    def setup_admin_endpoints(self):
        """Configura endpoints administrativos"""

        @self.admin_router.get("/stats", summary="Estatísticas do sistema")
        async def system_stats(request: Request):
            """Estatísticas gerais do sistema"""
            return response_formatter.success(
                data={
                    "total_clients": 0,
                    "total_reports": 0,
                    "total_diagnostics": 0,
                    "system_health": "good",
                    "uptime": "0 days",
                    "message": "Estatísticas em desenvolvimento"
                },
                message="Estatísticas do sistema"
            )

        @self.admin_router.get("/logs", summary="Logs do sistema")
        async def system_logs(request: Request, level: str = "info", limit: int = 100):
            """Logs recentes do sistema"""
            return response_formatter.success(
                data={
                    "logs": [],
                    "total": 0,
                    "level": level,
                    "limit": limit,
                    "message": "Sistema de logs em desenvolvimento"
                },
                message="Logs do sistema"
            )

        # Integrar endpoints de monitoramento de tasks
        try:
            from .task_monitoring_endpoints import router as task_monitoring_router
            self.admin_router.include_router(task_monitoring_router)
            logger.info("✅ Task monitoring endpoints integrados")
        except ImportError as e:
            logger.warning(f"⚠️ Task monitoring endpoints não disponíveis: {e}")

    def get_main_router(self) -> FastAPIRouter:
        """
        Retorna o roteador principal configurado

        Returns:
            FastAPIRouter configurado
        """
        # Configurar todos os endpoints
        self.setup_clients_endpoints()
        self.setup_diagnostics_endpoints()
        self.setup_reports_endpoints()
        self.setup_admin_endpoints()

        logger.info("API Router configurado com todos os endpoints")
        return self.main_router

    def get_openapi_tags(self) -> list:
        """
        Retorna tags para documentação OpenAPI

        Returns:
            Lista de tags organizadas
        """
        return [
            {
                "name": "API v1",
                "description": "Endpoints principais da API ScopeAI v1"
            },
            {
                "name": "Clientes",
                "description": "Gestão de clientes e empresas"
            },
            {
                "name": "Diagnósticos",
                "description": "Análises técnicas e diagnósticos automatizados"
            },
            {
                "name": "Enrichment",
                "description": "Enriquecimento inteligente de dados"
            },
            {
                "name": "Análise Competitiva",
                "description": "Análise de competitividade empresarial"
            },
            {
                "name": "Relatórios",
                "description": "Sistema de reports estruturados"
            },
            {
                "name": "Pesquisa de Mercado",
                "description": "Análise de mercado e oportunidades"
            },
            {
                "name": "Presença Digital",
                "description": "Análise de presença digital e canais"
            },
            {
                "name": "Administração",
                "description": "Endpoints administrativos e monitoramento"
            }
        ]


# Instância global do router
api_router = APIRouter()
