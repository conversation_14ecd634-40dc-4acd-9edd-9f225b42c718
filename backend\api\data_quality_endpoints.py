from fastapi import APIRouter, HTTPException, Query
from typing import Dict, Any, List, Optional
import logging

from ..shared.data_quality import quality_manager, DataQuality
from ..shared.fallback_monitor import fallback_monitor

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/data-quality", tags=["Data Quality"])

@router.get("/health")
async def get_data_quality_health() -> Dict[str, Any]:
    """
    Verificar saúde geral da qualidade dos dados
    
    Returns:
        Status de saúde dos dados com métricas principais
    """
    try:
        quality_stats = quality_manager.get_quality_stats()
        quality_report = fallback_monitor.get_quality_report()
        
        # Determinar status geral
        if quality_report.get("total_events", 0) == 0:
            status = "healthy"
            message = "Sistema funcionando normalmente - sem fallbacks registrados"
        else:
            quality_score = quality_report.get("quality_score", 100)
            if quality_score >= 80:
                status = "healthy"
                message = "Qualidade dos dados boa"
            elif quality_score >= 60:
                status = "warning"
                message = "Qualidade dos dados moderada - atenção necessária"
            elif quality_score >= 40:
                status = "degraded"
                message = "Qualidade dos dados baixa - investigação recomendada"
            else:
                status = "critical"
                message = "Qualidade dos dados crítica - ação imediata necessária"
        
        return {
            "status": status,
            "message": message,
            "timestamp": fallback_monitor.events[-1].timestamp.isoformat() if fallback_monitor.events else None,
            "summary": {
                "total_requests": quality_stats.get("total_requests", 0),
                "real_data_percentage": quality_stats.get("real_data_percentage", 100),
                "fallback_events_24h": quality_report.get("recent_events_24h", 0),
                "quality_score": quality_report.get("quality_score", 100),
                "average_confidence": quality_report.get("average_confidence", 1.0)
            },
            "alerts": quality_report.get("recommendations", [])
        }
        
    except Exception as e:
        logger.error(f"Erro ao obter saúde da qualidade dos dados: {e}")
        raise HTTPException(status_code=500, detail="Erro interno do servidor")

@router.get("/report")
async def get_detailed_quality_report() -> Dict[str, Any]:
    """
    Obter relatório detalhado de qualidade dos dados
    
    Returns:
        Relatório completo com estatísticas e breakdowns
    """
    try:
        quality_stats = quality_manager.get_quality_stats()
        fallback_report = fallback_monitor.get_quality_report()
        
        return {
            "overview": {
                "quality_score": fallback_report.get("quality_score", 100),
                "total_requests": quality_stats.get("total_requests", 0),
                "total_fallback_events": fallback_report.get("total_events", 0),
                "recent_events_24h": fallback_report.get("recent_events_24h", 0)
            },
            "data_sources": {
                "real_data_percentage": quality_stats.get("real_data_percentage", 100),
                "fallback_percentage": quality_stats.get("fallback_percentage", 0),
    
                "error_percentage": quality_stats.get("error_percentage", 0)
            },
            "quality_breakdown": fallback_report.get("quality_breakdown", {}),
            "service_breakdown": fallback_report.get("service_breakdown", {}),
            "source_breakdown": fallback_report.get("source_breakdown", {}),
            "confidence_distribution": fallback_report.get("confidence_distribution", {}),
            "recommendations": fallback_report.get("recommendations", []),
            "should_alert_admin": quality_manager.should_alert_admin()
        }
        
    except Exception as e:
        logger.error(f"Erro ao gerar relatório de qualidade: {e}")
        raise HTTPException(status_code=500, detail="Erro interno do servidor")

@router.get("/events")
async def get_fallback_events(
    limit: int = Query(50, ge=1, le=500, description="Número de eventos para retornar"),
    quality_filter: Optional[str] = Query(None, description="Filtrar por qualidade (ex: error_fallback)")
) -> Dict[str, Any]:
    """
    Obter eventos de fallback recentes
    
    Args:
        limit: Número máximo de eventos para retornar
        quality_filter: Filtro por tipo de qualidade
    
    Returns:
        Lista de eventos de fallback
    """
    try:
        events = fallback_monitor.get_recent_events(limit)
        
        # Aplicar filtro se especificado
        if quality_filter:
            try:
                quality_enum = DataQuality(quality_filter)
                events = [e for e in events if e.get("quality") == quality_enum.value]
            except ValueError:
                raise HTTPException(
                    status_code=400, 
                    detail=f"Qualidade inválida: {quality_filter}. Valores válidos: {[q.value for q in DataQuality]}"
                )
        
        return {
            "events": events,
            "total_returned": len(events),
            "filter_applied": quality_filter,
            "available_qualities": [q.value for q in DataQuality]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao obter eventos de fallback: {e}")
        raise HTTPException(status_code=500, detail="Erro interno do servidor")

@router.get("/services/{service_name}")
async def get_service_quality(service_name: str) -> Dict[str, Any]:
    """
    Obter qualidade dos dados para um serviço específico
    
    Args:
        service_name: Nome do serviço
    
    Returns:
        Estatísticas de qualidade do serviço
    """
    try:
        all_events = fallback_monitor.get_recent_events(1000)
        service_events = [e for e in all_events if e.get("service") == service_name]
        
        if not service_events:
            return {
                "service": service_name,
                "status": "healthy",
                "message": "Nenhum evento de fallback registrado para este serviço",
                "events_count": 0
            }
        
        # Calcular estatísticas do serviço
        total_events = len(service_events)
        error_events = [e for e in service_events if e.get("quality") == "error_fallback"]
        basic_events = [e for e in service_events if e.get("quality") == "basic_fallback"]
        enhanced_events = [e for e in service_events if e.get("quality") == "enhanced_fallback"]
        
        avg_confidence = sum(e.get("confidence_score", 0) for e in service_events) / total_events
        
        # Determinar status do serviço
        error_rate = len(error_events) / total_events * 100
        if error_rate > 20:
            status = "critical"
            message = f"Taxa alta de erros: {error_rate:.1f}%"
        elif error_rate > 10:
            status = "warning"
            message = f"Taxa moderada de erros: {error_rate:.1f}%"
        elif len(basic_events) / total_events * 100 > 50:
            status = "degraded"
            message = "Muitos fallbacks básicos"
        else:
            status = "healthy"
            message = "Serviço funcionando adequadamente"
        
        return {
            "service": service_name,
            "status": status,
            "message": message,
            "events_count": total_events,
            "statistics": {
                "error_fallbacks": len(error_events),
                "basic_fallbacks": len(basic_events),
                "enhanced_fallbacks": len(enhanced_events),
                "error_rate_percentage": error_rate,
                "average_confidence": avg_confidence
            },
            "recent_events": service_events[-10:]  # Últimos 10 eventos
        }
        
    except Exception as e:
        logger.error(f"Erro ao obter qualidade do serviço {service_name}: {e}")
        raise HTTPException(status_code=500, detail="Erro interno do servidor")

@router.post("/cleanup")
async def cleanup_old_events(days_to_keep: int = Query(7, ge=1, le=30)) -> Dict[str, Any]:
    """
    Limpar eventos antigos do monitor
    
    Args:
        days_to_keep: Número de dias de eventos para manter
    
    Returns:
        Resultado da limpeza
    """
    try:
        old_count = len(fallback_monitor.events)
        fallback_monitor.clear_old_events(days_to_keep)
        new_count = len(fallback_monitor.events)
        
        return {
            "events_before": old_count,
            "events_after": new_count,
            "events_removed": old_count - new_count,
            "days_kept": days_to_keep,
            "message": f"Limpeza concluída - {old_count - new_count} eventos removidos"
        }
        
    except Exception as e:
        logger.error(f"Erro ao limpar eventos: {e}")
        raise HTTPException(status_code=500, detail="Erro interno do servidor")

@router.get("/alerts")
async def get_active_alerts() -> Dict[str, Any]:
    """
    Obter alertas ativos baseados na qualidade dos dados
    
    Returns:
        Lista de alertas ativos
    """
    try:
        quality_report = fallback_monitor.get_quality_report()
        quality_score = quality_report.get("quality_score", 100)
        
        alerts = []
        
        # Verificar diferentes tipos de alertas
        if quality_score < 30:
            alerts.append({
                "type": "critical",
                "message": "Qualidade dos dados crítica - múltiplos sistemas falhando",
                "score": quality_score,
                "priority": "high"
            })
        elif quality_score < 50:
            alerts.append({
                "type": "warning",
                "message": "Qualidade dos dados baixa - investigação necessária",
                "score": quality_score,
                "priority": "medium"
            })
        
        # Verificar eventos de erro recentes
        recent_error_events = [
            e for e in fallback_monitor.events[-20:]
            if e.quality == DataQuality.ERROR_FALLBACK
        ]
        
        if len(recent_error_events) > 5:
            alerts.append({
                "type": "error_spike",
                "message": f"Pico de erros detectado: {len(recent_error_events)} erros recentes",
                "count": len(recent_error_events),
                "priority": "high"
            })
        
        # Verificar serviços com problemas
        service_stats = quality_report.get("service_breakdown", {})
        problematic_services = [
            service for service, count in service_stats.items()
            if count > 10  # Mais de 10 fallbacks
        ]
        
        if problematic_services:
            alerts.append({
                "type": "service_issues",
                "message": f"Serviços com problemas: {', '.join(problematic_services)}",
                "services": problematic_services,
                "priority": "medium"
            })
        
        return {
            "active_alerts": alerts,
            "alert_count": len(alerts),
            "quality_score": quality_score,
            "recommendations": quality_report.get("recommendations", []),
            "last_updated": fallback_monitor.events[-1].timestamp.isoformat() if fallback_monitor.events else None
        }
        
    except Exception as e:
        logger.error(f"Erro ao obter alertas: {e}")
        raise HTTPException(status_code=500, detail="Erro interno do servidor") 