import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnDestroy, Output } from '@angular/core';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';

export interface PdfModalData {
	visible: boolean;
	generating: boolean;
	url: string;
	title: string;
	projectId?: string;
}

@Component({
	selector: 'app-pdf-modal',
	standalone: true,
	imports: [CommonModule, DialogModule, ButtonModule],
	template: `
		<p-dialog
			[visible]="data.visible"
			[modal]="true"
			[closable]="true"
			[draggable]="false"
			[resizable]="false"
			styleClass="pdf-modal"
			[style]="{ width: '95vw', maxWidth: '1200px', height: '90vh' }"
			(onHide)="onClose()"
		>
			<ng-template pTemplate="header">
				<div class="flex items-center justify-between w-full">
					<div class="flex items-center gap-3">
						<i class="pi pi-file-pdf text-red-600 text-2xl"></i>
						<div>
							<h2 class="text-xl font-bold text-gray-900 m-0">Relatório PDF</h2>
							<p class="text-sm text-gray-600 m-0 mt-1">{{ data.title }}</p>
						</div>
					</div>
					<div class="flex items-center gap-2">
						@if (data.url && !data.generating) {
							<button
								pButton
								label="Download"
								icon="pi pi-download"
								class="p-button-outlined p-button-sm p-button-primary"
								(click)="downloadPdf()"
							></button>
						}
						<button
							pButton
							icon="pi pi-times"
							class="p-button-outlined p-button-sm p-button-secondary"
							(click)="onClose()"
						></button>
					</div>
				</div>
			</ng-template>

			<ng-template pTemplate="content">
				<div class="pdf-viewer-container">
					<!-- Loading State -->
					@if (data.generating) {
						<div class="flex flex-col items-center justify-center h-full min-h-96 bg-gray-50 rounded-lg">
							<div class="text-center">
								<i class="pi pi-spin pi-spinner text-4xl text-red-600 mb-4"></i>
								<h3 class="text-lg font-semibold text-gray-900 mb-2">Gerando Relatório PDF...</h3>
								<p class="text-sm text-gray-600 mb-4">
									Compilando dados e criando documento. Aguarde alguns instantes...
								</p>
								<div class="bg-white rounded-lg p-4 border border-red-200">
									<div class="flex items-center gap-2 text-sm text-red-700">
										<i class="pi pi-info-circle"></i>
										<span>Este processo pode levar até 2 minutos</span>
									</div>
								</div>
							</div>
						</div>
					}

					<!-- PDF Viewer -->
					@if (data.url && !data.generating) {
						<div class="pdf-viewer-wrapper">
							<iframe
								[src]="getSafeUrl(data.url)"
								class="w-full h-full border-0 rounded-lg"
								style="min-height: 70vh;"
								title="Relatório PDF"
							></iframe>
						</div>
					}

					<!-- Empty State -->
					@if (!data.url && !data.generating) {
						<div class="flex flex-col items-center justify-center h-full min-h-96 bg-gray-50 rounded-lg">
							<div class="text-center">
								<i class="pi pi-file-pdf text-6xl text-gray-400 mb-4"></i>
								<h3 class="text-lg font-semibold text-gray-900 mb-2">Nenhum PDF Disponível</h3>
								<p class="text-sm text-gray-600 mb-4">
									O relatório PDF ainda não foi gerado para este projeto.
								</p>
								@if (data.projectId) {
									<button
										pButton
										label="Gerar Relatório PDF"
										icon="pi pi-file-pdf"
										class="p-button-outlined p-button-primary"
										(click)="generatePdf()"
									></button>
								}
							</div>
						</div>
					}
				</div>
			</ng-template>

			<ng-template pTemplate="footer">
				<div class="flex justify-between items-center w-full">
					<div class="flex items-center gap-2 text-sm text-gray-600">
						@if (data.url && !data.generating) {
							<i class="pi pi-check-circle text-green-600"></i>
							<span>PDF carregado com sucesso</span>
						}
					</div>
					<div class="flex gap-2">
						@if (data.url && !data.generating) {
							<button
								pButton
								label="Download"
								icon="pi pi-download"
								class="p-button-outlined p-button-primary"
								(click)="downloadPdf()"
							></button>
						}
						<button
							pButton
							label="Fechar"
							icon="pi pi-times"
							class="p-button-outlined p-button-secondary"
							(click)="onClose()"
						></button>
					</div>
				</div>
			</ng-template>
		</p-dialog>
	`,
	styles: [
		`
			:host ::ng-deep .pdf-modal .p-dialog-content {
				padding: 0;
				height: calc(90vh - 120px);
			}

			:host ::ng-deep .pdf-modal .p-dialog-header {
				padding: 1.5rem;
				border-bottom: 1px solid #e5e7eb;
			}

			:host ::ng-deep .pdf-modal .p-dialog-footer {
				padding: 1rem 1.5rem;
				border-top: 1px solid #e5e7eb;
			}

			.pdf-viewer-container {
				height: 100%;
				padding: 1rem;
			}

			.pdf-viewer-wrapper {
				height: 100%;
				background: white;
				border-radius: 8px;
				overflow: hidden;
				box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
			}

			iframe {
				border: none;
				background: white;
			}

			/* Loading animation */
			.loading-dots {
				display: inline-block;
				position: relative;
				width: 40px;
				height: 10px;
			}

			.loading-dots div {
				position: absolute;
				top: 0;
				width: 8px;
				height: 8px;
				border-radius: 50%;
				background: #dc2626;
				animation: loading-dots 1.2s infinite ease-in-out;
			}

			.loading-dots div:nth-child(1) {
				left: 0;
				animation-delay: -0.24s;
			}
			.loading-dots div:nth-child(2) {
				left: 16px;
				animation-delay: -0.12s;
			}
			.loading-dots div:nth-child(3) {
				left: 32px;
				animation-delay: 0;
			}

			@keyframes loading-dots {
				0%,
				80%,
				100% {
					transform: scale(0);
				}
				40% {
					transform: scale(1);
				}
			}

			/* Responsive adjustments */
			@media (max-width: 768px) {
				:host ::ng-deep .pdf-modal {
					width: 100vw !important;
					height: 100vh !important;
					max-width: 100vw !important;
					max-height: 100vh !important;
					border-radius: 0;
				}

				:host ::ng-deep .pdf-modal .p-dialog-content {
					height: calc(100vh - 120px);
				}

				.pdf-viewer-container {
					padding: 0.5rem;
				}

				:host ::ng-deep .pdf-modal .p-dialog-header {
					padding: 1rem;
				}

				:host ::ng-deep .pdf-modal .p-dialog-footer {
					padding: 0.75rem 1rem;
				}
			}
		`,
	],
})
export class PdfModalComponent implements OnDestroy {
	@Input() data: PdfModalData = {
		visible: false,
		generating: false,
		url: '',
		title: '',
	};

	@Output() close = new EventEmitter<void>();
	@Output() generate = new EventEmitter<string>();
	@Output() download = new EventEmitter<void>();

	constructor(private sanitizer: DomSanitizer) {}

	ngOnDestroy(): void {
		// Limpar URL do blob para liberar memória
		if (this.data.url && this.data.url.startsWith('blob:')) {
			URL.revokeObjectURL(this.data.url);
		}
	}

	public onClose(): void {
		this.close.emit();
	}

	public generatePdf(): void {
		if (this.data.projectId) {
			this.generate.emit(this.data.projectId);
		}
	}

	public downloadPdf(): void {
		if (!this.data.url) return;

		const link = document.createElement('a');
		link.href = this.data.url;
		link.download = `${this.data.title}.pdf`;
		link.click();

		this.download.emit();
	}

	public getSafeUrl(url: string): SafeResourceUrl {
		if (!url) return '';

		// Para URLs blob, adicionar parâmetro para viewer do navegador
		if (url.startsWith('blob:')) {
			return this.sanitizer.bypassSecurityTrustResourceUrl(url);
		}

		// Para URLs externas, usar visualizador PDF do Google
		const viewerUrl = `https://docs.google.com/viewer?url=${encodeURIComponent(url)}&embedded=true`;
		return this.sanitizer.bypassSecurityTrustResourceUrl(viewerUrl);
	}

	/**
	 * Verifica se o PDF está carregado
	 */
	public isPdfLoaded(): boolean {
		return !!(this.data.url && !this.data.generating);
	}

	/**
	 * Retorna o tamanho estimado do arquivo
	 */
	public getFileSizeEstimate(): string {
		// Estimativa baseada no tipo de relatório
		return '~2-5 MB';
	}

	/**
	 * Retorna o status de carregamento
	 */
	public getLoadingStatus(): string {
		if (this.data.generating) {
			return 'Gerando relatório...';
		}
		if (this.data.url) {
			return 'PDF carregado';
		}
		return 'Nenhum PDF disponível';
	}
}
