"""
Sistema de Enriquecimento Contínuo de Dados

Este módulo implementa um sistema completo para atualização periódica e 
enriquecimento contínuo dos dados de parcerias, modelo de negócio e pricing 
das empresas monitoradas.

Componentes principais:
- Scheduler: Agendamento de verificações periódicas
- Detector: Detecção de mudanças em dados
- Enricher: Enriquecimento e atualização de dados
- Prioritizer: Priorização inteligente de atualizações
"""

from .continuous_enrichment import ContinuousEnrichmentSystem
from .scheduler import EnrichmentScheduler
from .detector import ChangeDetector
from .enricher import DataEnricher
from .prioritizer import UpdatePrioritizer

__all__ = [
    'ContinuousEnrichmentSystem',
    'EnrichmentScheduler',
    'ChangeDetector',
    'DataEnricher',
    'UpdatePrioritizer'
]
