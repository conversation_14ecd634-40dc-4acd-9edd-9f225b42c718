{"ast": null, "code": "function cov_r32n2fhxm() {\n  var path = \"C:\\\\projetos\\\\scope-ai\\\\frontend\\\\src\\\\app\\\\features\\\\projects\\\\components\\\\podcast-modal\\\\podcast-modal.component.ts\";\n  var hash = \"2f21efd3ceae7a6ad27657490057ba70e0370e28\";\n  var global = new Function(\"return this\")();\n  var gcv = \"__coverage__\";\n  var coverageData = {\n    path: \"C:\\\\projetos\\\\scope-ai\\\\frontend\\\\src\\\\app\\\\features\\\\projects\\\\components\\\\podcast-modal\\\\podcast-modal.component.ts\",\n    statementMap: {\n      \"0\": {\n        start: {\n          line: 19,\n          column: 28\n        },\n        end: {\n          line: 63,\n          column: 1\n        }\n      },\n      \"1\": {\n        start: {\n          line: 20,\n          column: 11\n        },\n        end: {\n          line: 25,\n          column: 5\n        }\n      },\n      \"2\": {\n        start: {\n          line: 26,\n          column: 12\n        },\n        end: {\n          line: 26,\n          column: 30\n        }\n      },\n      \"3\": {\n        start: {\n          line: 27,\n          column: 15\n        },\n        end: {\n          line: 27,\n          column: 33\n        }\n      },\n      \"4\": {\n        start: {\n          line: 29,\n          column: 17\n        },\n        end: {\n          line: 29,\n          column: 21\n        }\n      },\n      \"5\": {\n        start: {\n          line: 32,\n          column: 8\n        },\n        end: {\n          line: 32,\n          column: 31\n        }\n      },\n      \"6\": {\n        start: {\n          line: 38,\n          column: 8\n        },\n        end: {\n          line: 38,\n          column: 31\n        }\n      },\n      \"7\": {\n        start: {\n          line: 39,\n          column: 8\n        },\n        end: {\n          line: 39,\n          column: 26\n        }\n      },\n      \"8\": {\n        start: {\n          line: 46,\n          column: 8\n        },\n        end: {\n          line: 48,\n          column: 9\n        }\n      },\n      \"9\": {\n        start: {\n          line: 47,\n          column: 12\n        },\n        end: {\n          line: 47,\n          column: 52\n        }\n      },\n      \"10\": {\n        start: {\n          line: 55,\n          column: 8\n        },\n        end: {\n          line: 55,\n          column: 56\n        }\n      },\n      \"11\": {\n        start: {\n          line: 56,\n          column: 8\n        },\n        end: {\n          line: 56,\n          column: 82\n        }\n      },\n      \"12\": {\n        start: {\n          line: 58,\n          column: 28\n        },\n        end: {\n          line: 62,\n          column: 5\n        }\n      },\n      \"13\": {\n        start: {\n          line: 64,\n          column: 0\n        },\n        end: {\n          line: 201,\n          column: 26\n        }\n      }\n    },\n    fnMap: {\n      \"0\": {\n        name: \"(anonymous_0)\",\n        decl: {\n          start: {\n            line: 30,\n            column: 4\n          },\n          end: {\n            line: 30,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 30,\n            column: 18\n          },\n          end: {\n            line: 33,\n            column: 5\n          }\n        },\n        line: 30\n      },\n      \"1\": {\n        name: \"(anonymous_1)\",\n        decl: {\n          start: {\n            line: 37,\n            column: 4\n          },\n          end: {\n            line: 37,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 37,\n            column: 14\n          },\n          end: {\n            line: 40,\n            column: 5\n          }\n        },\n        line: 37\n      },\n      \"2\": {\n        name: \"(anonymous_2)\",\n        decl: {\n          start: {\n            line: 45,\n            column: 4\n          },\n          end: {\n            line: 45,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 45,\n            column: 22\n          },\n          end: {\n            line: 49,\n            column: 5\n          }\n        },\n        line: 45\n      },\n      \"3\": {\n        name: \"(anonymous_3)\",\n        decl: {\n          start: {\n            line: 54,\n            column: 4\n          },\n          end: {\n            line: 54,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 54,\n            column: 24\n          },\n          end: {\n            line: 57,\n            column: 5\n          }\n        },\n        line: 54\n      }\n    },\n    branchMap: {\n      \"0\": {\n        loc: {\n          start: {\n            line: 46,\n            column: 8\n          },\n          end: {\n            line: 48,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 46,\n            column: 8\n          },\n          end: {\n            line: 48,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 46\n      }\n    },\n    s: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0\n    },\n    f: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0\n    },\n    b: {\n      \"0\": [0, 0]\n    },\n    inputSourceMap: {\n      version: 3,\n      file: \"podcast-modal.component.js\",\n      sourceRoot: \"\",\n      sources: [\"C:\\\\projetos\\\\scope-ai\\\\frontend\\\\src\\\\app\\\\features\\\\projects\\\\components\\\\podcast-modal\\\\podcast-modal.component.ts\"],\n      names: [],\n      mappings: \";;AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAC/C,OAAO,EAAE,SAAS,EAAE,YAAY,EAAE,KAAK,EAAa,MAAM,EAAE,MAAM,eAAe,CAAC;AAClF,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAC9C,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAkB9C;;;;;;;;;;;GAWG;AAgLI,IAAM,qBAAqB,GAA3B,MAAM,qBAAqB;IAExB,IAAI,GAAqB;QACjC,OAAO,EAAE,KAAK;QACd,UAAU,EAAE,KAAK;QACjB,GAAG,EAAE,EAAE;QACP,KAAK,EAAE,EAAE;KACT;IAGS,KAAK,GAAG,IAAI,YAAY,EAAQ;IAGhC,QAAQ,GAAG,IAAI,YAAY,EAAU;IAE/C,4CAA4C;IACrC,UAAU,GAAkB,IAAI,CAAC;IAExC,WAAW;QACV,+CAA+C;QAC/C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACxB,CAAC;IAED;;OAEG;IACI,OAAO;QACb,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;IACnB,CAAC;IAED;;;OAGG;IACI,eAAe;QACrB,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACzB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACzC,CAAC;IACF,CAAC;IAED;;;OAGG;IACI,YAAY,CAAC,KAAY;QAC/B,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;QAChD,IAAI,CAAC,UAAU,GAAG,uDAAuD,CAAC;IAC3E,CAAC;;uBA9CA,KAAK;wBAQL,MAAM;2BAGN,MAAM;;;AAbK,qBAAqB;IA/KjC,SAAS,CAAC;QACV,QAAQ,EAAE,mBAAmB;QAC7B,UAAU,EAAE,IAAI;QAChB,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,CAAC;QACnD,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAiIT;;KAyCD,CAAC;GACW,qBAAqB,CAiDjC\",\n      sourcesContent: [\"import { CommonModule } from '@angular/common';\\r\\nimport { Component, EventEmitter, Input, OnDestroy, Output } from '@angular/core';\\r\\nimport { ButtonModule } from 'primeng/button';\\r\\nimport { DialogModule } from 'primeng/dialog';\\r\\n\\r\\n/**\\r\\n * Interface para os dados do modal de podcast\\r\\n */\\r\\nexport interface PodcastModalData {\\r\\n\\t/** Controla a visibilidade do modal */\\r\\n\\tvisible: boolean;\\r\\n\\t/** Indica se o podcast est\\xE1 sendo gerado */\\r\\n\\tgenerating: boolean;\\r\\n\\t/** URL do arquivo de \\xE1udio do podcast */\\r\\n\\turl: string;\\r\\n\\t/** T\\xEDtulo do projeto para exibi\\xE7\\xE3o */\\r\\n\\ttitle: string;\\r\\n\\t/** ID do projeto (opcional, usado para gerar podcast) */\\r\\n\\tprojectId?: string;\\r\\n}\\r\\n\\r\\n/**\\r\\n * Componente de modal para reprodu\\xE7\\xE3o de podcasts\\r\\n *\\r\\n * @example\\r\\n * ```html\\r\\n * <app-podcast-modal\\r\\n *   [data]=\\\"{ visible: true, url: 'audio.mp3', title: 'Meu Projeto' }\\\"\\r\\n *   (close)=\\\"fecharModal()\\\"\\r\\n *   (generate)=\\\"gerarPodcast($event)\\\"\\r\\n * ></app-podcast-modal>\\r\\n * ```\\r\\n */\\r\\n@Component({\\r\\n\\tselector: 'app-podcast-modal',\\r\\n\\tstandalone: true,\\r\\n\\timports: [CommonModule, DialogModule, ButtonModule],\\r\\n\\ttemplate: `\\r\\n\\t\\t<p-dialog\\r\\n\\t\\t\\t[visible]=\\\"data.visible\\\"\\r\\n\\t\\t\\t[modal]=\\\"true\\\"\\r\\n\\t\\t\\t[closable]=\\\"true\\\"\\r\\n\\t\\t\\t[draggable]=\\\"false\\\"\\r\\n\\t\\t\\t[resizable]=\\\"false\\\"\\r\\n\\t\\t\\tstyleClass=\\\"podcast-modal\\\"\\r\\n\\t\\t\\t[style]=\\\"{ width: '90vw', maxWidth: '600px' }\\\"\\r\\n\\t\\t\\t(onHide)=\\\"onClose()\\\"\\r\\n\\t\\t>\\r\\n\\t\\t\\t<ng-template pTemplate=\\\"header\\\">\\r\\n\\t\\t\\t\\t<div class=\\\"flex items-center gap-3\\\">\\r\\n\\t\\t\\t\\t\\t<i class=\\\"pi pi-microphone text-purple-600 text-2xl\\\"></i>\\r\\n\\t\\t\\t\\t\\t<div>\\r\\n\\t\\t\\t\\t\\t\\t<h2 class=\\\"text-xl font-bold text-gray-900 m-0\\\">Podcast do Projeto</h2>\\r\\n\\t\\t\\t\\t\\t\\t<p class=\\\"text-sm text-gray-600 m-0 mt-1\\\">{{ data.title }}</p>\\r\\n\\t\\t\\t\\t\\t</div>\\r\\n\\t\\t\\t\\t</div>\\r\\n\\t\\t\\t</ng-template>\\r\\n\\r\\n\\t\\t\\t<ng-template pTemplate=\\\"content\\\">\\r\\n\\t\\t\\t\\t<div class=\\\"podcast-player-container\\\">\\r\\n\\t\\t\\t\\t\\t<!-- Player Container -->\\r\\n\\t\\t\\t\\t\\t<div\\r\\n\\t\\t\\t\\t\\t\\tclass=\\\"bg-gradient-to-br from-purple-50 to-indigo-50 rounded-2xl p-8 text-center border border-purple-200\\\"\\r\\n\\t\\t\\t\\t\\t>\\r\\n\\t\\t\\t\\t\\t\\t<!-- Podcast Icon -->\\r\\n\\t\\t\\t\\t\\t\\t<div class=\\\"mb-6\\\">\\r\\n\\t\\t\\t\\t\\t\\t\\t<div\\r\\n\\t\\t\\t\\t\\t\\t\\t\\tclass=\\\"w-20 h-20 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto shadow-lg\\\"\\r\\n\\t\\t\\t\\t\\t\\t\\t>\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t<i class=\\\"pi pi-microphone text-white text-3xl\\\"></i>\\r\\n\\t\\t\\t\\t\\t\\t\\t</div>\\r\\n\\t\\t\\t\\t\\t\\t</div>\\r\\n\\r\\n\\t\\t\\t\\t\\t\\t<!-- Project Info -->\\r\\n\\t\\t\\t\\t\\t\\t<div class=\\\"mb-6\\\">\\r\\n\\t\\t\\t\\t\\t\\t\\t<h3 class=\\\"text-lg font-semibold text-gray-900 mb-2\\\">{{ data.title }}</h3>\\r\\n\\t\\t\\t\\t\\t\\t\\t<p class=\\\"text-sm text-gray-600\\\">An\\xE1lise detalhada do projeto em formato de podcast</p>\\r\\n\\t\\t\\t\\t\\t\\t</div>\\r\\n\\r\\n\\t\\t\\t\\t\\t\\t<!-- Loading State -->\\r\\n\\t\\t\\t\\t\\t\\t@if (data.generating) {\\r\\n\\t\\t\\t\\t\\t\\t\\t<div class=\\\"bg-white rounded-xl p-6 shadow-sm border border-gray-200\\\">\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t<div class=\\\"flex flex-col items-center gap-4\\\">\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t<i class=\\\"pi pi-spin pi-spinner text-3xl text-purple-600\\\"></i>\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t<div class=\\\"text-center\\\">\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t<p class=\\\"font-semibold text-gray-900\\\">Gerando Podcast...</p>\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t<p class=\\\"text-sm text-gray-600\\\">\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\tAguarde 1-2 minutos enquanto criamos o \\xE1udio\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t</p>\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t</div>\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t</div>\\r\\n\\t\\t\\t\\t\\t\\t\\t</div>\\r\\n\\t\\t\\t\\t\\t\\t}\\r\\n\\r\\n\\t\\t\\t\\t\\t\\t<!-- Audio Player -->\\r\\n\\t\\t\\t\\t\\t\\t@if (data.url && !data.generating) {\\r\\n\\t\\t\\t\\t\\t\\t\\t<div class=\\\"bg-white rounded-xl p-4 shadow-sm border border-gray-200\\\">\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t<audio\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tcontrols\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tautoplay\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tclass=\\\"w-full\\\"\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t[src]=\\\"data.url\\\"\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tpreload=\\\"auto\\\"\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t(error)=\\\"onAudioError($event)\\\"\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t>\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t<source [src]=\\\"data.url\\\" type=\\\"audio/mpeg\\\" />\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t<p class=\\\"text-red-600 text-sm\\\">Seu navegador n\\xE3o suporta o elemento de \\xE1udio.</p>\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t</audio>\\r\\n\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t<!-- Nota sobre autoplay no Safari -->\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t<div class=\\\"mt-2 text-xs text-gray-500 text-center\\\">\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t<p>\\u26A0\\uFE0F Alguns navegadores bloqueiam autoplay. Clique no play se necess\\xE1rio.</p>\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t</div>\\r\\n\\t\\t\\t\\t\\t\\t\\t</div>\\r\\n\\t\\t\\t\\t\\t\\t}\\r\\n\\r\\n\\t\\t\\t\\t\\t\\t<!-- Empty State -->\\r\\n\\t\\t\\t\\t\\t\\t@if (!data.url && !data.generating) {\\r\\n\\t\\t\\t\\t\\t\\t\\t<div class=\\\"bg-white rounded-xl p-6 shadow-sm border border-gray-200\\\">\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t<div class=\\\"text-center\\\">\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t<i class=\\\"pi pi-microphone text-4xl text-gray-400 mb-4\\\"></i>\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t<p class=\\\"text-gray-600 mb-4\\\">Nenhum podcast dispon\\xEDvel</p>\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t@if (data.projectId) {\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t<button\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\tpButton\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\tlabel=\\\"Gerar Podcast\\\"\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\ticon=\\\"pi pi-play\\\"\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\tclass=\\\"p-button-outlined p-button-primary\\\"\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t(click)=\\\"generatePodcast()\\\"\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t[attr.aria-label]=\\\"'Gerar podcast para ' + data.title\\\"\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t></button>\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t}\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t</div>\\r\\n\\t\\t\\t\\t\\t\\t\\t</div>\\r\\n\\t\\t\\t\\t\\t\\t}\\r\\n\\r\\n\\t\\t\\t\\t\\t\\t<!-- Error State -->\\r\\n\\t\\t\\t\\t\\t\\t@if (audioError) {\\r\\n\\t\\t\\t\\t\\t\\t\\t<div class=\\\"bg-red-50 rounded-xl p-4 mt-4 border border-red-200\\\">\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t<div class=\\\"flex items-center gap-2 text-red-700\\\">\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t<i class=\\\"pi pi-exclamation-triangle\\\"></i>\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t<p class=\\\"text-sm\\\">{{ audioError }}</p>\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t</div>\\r\\n\\t\\t\\t\\t\\t\\t\\t</div>\\r\\n\\t\\t\\t\\t\\t\\t}\\r\\n\\r\\n\\t\\t\\t\\t\\t\\t<!-- Player Info -->\\r\\n\\t\\t\\t\\t\\t\\t<div class=\\\"mt-4 text-xs text-gray-500\\\">\\r\\n\\t\\t\\t\\t\\t\\t\\t<p>\\uD83D\\uDCA1 Use os controles de \\xE1udio para pausar, ajustar volume ou pular trechos</p>\\r\\n\\t\\t\\t\\t\\t\\t</div>\\r\\n\\t\\t\\t\\t\\t</div>\\r\\n\\r\\n\\t\\t\\t\\t\\t<!-- Additional Actions -->\\r\\n\\t\\t\\t\\t\\t<div class=\\\"flex justify-center gap-3 mt-6\\\">\\r\\n\\t\\t\\t\\t\\t\\t<button\\r\\n\\t\\t\\t\\t\\t\\t\\tpButton\\r\\n\\t\\t\\t\\t\\t\\t\\tlabel=\\\"Fechar\\\"\\r\\n\\t\\t\\t\\t\\t\\t\\ticon=\\\"pi pi-times\\\"\\r\\n\\t\\t\\t\\t\\t\\t\\tclass=\\\"p-button-outlined p-button-secondary\\\"\\r\\n\\t\\t\\t\\t\\t\\t\\t(click)=\\\"onClose()\\\"\\r\\n\\t\\t\\t\\t\\t\\t\\t[attr.aria-label]=\\\"'Fechar modal do podcast'\\\"\\r\\n\\t\\t\\t\\t\\t\\t></button>\\r\\n\\t\\t\\t\\t\\t</div>\\r\\n\\t\\t\\t\\t</div>\\r\\n\\t\\t\\t</ng-template>\\r\\n\\t\\t</p-dialog>\\r\\n\\t`,\\r\\n\\tstyles: [\\r\\n\\t\\t`\\r\\n\\t\\t\\t:host ::ng-deep .podcast-modal .p-dialog-content {\\r\\n\\t\\t\\t\\tpadding: 0;\\r\\n\\t\\t\\t}\\r\\n\\r\\n\\t\\t\\t:host ::ng-deep .podcast-modal .p-dialog-header {\\r\\n\\t\\t\\t\\tpadding: 1.5rem 1.5rem 1rem 1.5rem;\\r\\n\\t\\t\\t\\tborder-bottom: 1px solid #e5e7eb;\\r\\n\\t\\t\\t}\\r\\n\\r\\n\\t\\t\\t.podcast-player-container {\\r\\n\\t\\t\\t\\tpadding: 1.5rem;\\r\\n\\t\\t\\t}\\r\\n\\r\\n\\t\\t\\taudio {\\r\\n\\t\\t\\t\\theight: 54px;\\r\\n\\t\\t\\t}\\r\\n\\r\\n\\t\\t\\taudio::-webkit-media-controls-panel {\\r\\n\\t\\t\\t\\tbackground-color: #f9fafb;\\r\\n\\t\\t\\t\\tborder-radius: 8px;\\r\\n\\t\\t\\t}\\r\\n\\r\\n\\t\\t\\t/* Responsive adjustments */\\r\\n\\t\\t\\t@media (max-width: 640px) {\\r\\n\\t\\t\\t\\t.podcast-player-container {\\r\\n\\t\\t\\t\\t\\tpadding: 1rem;\\r\\n\\t\\t\\t\\t}\\r\\n\\r\\n\\t\\t\\t\\t:host ::ng-deep .podcast-modal .p-dialog-header {\\r\\n\\t\\t\\t\\t\\tpadding: 1rem;\\r\\n\\t\\t\\t\\t}\\r\\n\\r\\n\\t\\t\\t\\t.bg-gradient-to-br {\\r\\n\\t\\t\\t\\t\\tpadding: 1.5rem;\\r\\n\\t\\t\\t\\t}\\r\\n\\t\\t\\t}\\r\\n\\t\\t`,\\r\\n\\t],\\r\\n})\\r\\nexport class PodcastModalComponent implements OnDestroy {\\r\\n\\t/** Dados do modal incluindo visibilidade, URL do \\xE1udio e t\\xEDtulo */\\r\\n\\t@Input() data: PodcastModalData = {\\r\\n\\t\\tvisible: false,\\r\\n\\t\\tgenerating: false,\\r\\n\\t\\turl: '',\\r\\n\\t\\ttitle: '',\\r\\n\\t};\\r\\n\\r\\n\\t/** Evento emitido quando o modal \\xE9 fechado */\\r\\n\\t@Output() close = new EventEmitter<void>();\\r\\n\\r\\n\\t/** Evento emitido quando o usu\\xE1rio solicita gera\\xE7\\xE3o de podcast */\\r\\n\\t@Output() generate = new EventEmitter<string>();\\r\\n\\r\\n\\t/** Mensagem de erro do \\xE1udio (se houver) */\\r\\n\\tpublic audioError: string | null = null;\\r\\n\\r\\n\\tngOnDestroy(): void {\\r\\n\\t\\t// Limpar estado de erro ao destruir componente\\r\\n\\t\\tthis.audioError = null;\\r\\n\\t}\\r\\n\\r\\n\\t/**\\r\\n\\t * Fecha o modal e emite evento de fechamento\\r\\n\\t */\\r\\n\\tpublic onClose(): void {\\r\\n\\t\\tthis.audioError = null;\\r\\n\\t\\tthis.close.emit();\\r\\n\\t}\\r\\n\\r\\n\\t/**\\r\\n\\t * Solicita gera\\xE7\\xE3o de podcast para o projeto\\r\\n\\t * Emite o ID do projeto se dispon\\xEDvel\\r\\n\\t */\\r\\n\\tpublic generatePodcast(): void {\\r\\n\\t\\tif (this.data.projectId) {\\r\\n\\t\\t\\tthis.generate.emit(this.data.projectId);\\r\\n\\t\\t}\\r\\n\\t}\\r\\n\\r\\n\\t/**\\r\\n\\t * Trata erros no carregamento do \\xE1udio\\r\\n\\t * @param event - Evento de erro do elemento audio\\r\\n\\t */\\r\\n\\tpublic onAudioError(event: Event): void {\\r\\n\\t\\tconsole.error('Erro ao carregar \\xE1udio:', event);\\r\\n\\t\\tthis.audioError = 'Erro ao carregar o \\xE1udio. Por favor, tente novamente.';\\r\\n\\t}\\r\\n}\\r\\n\"]\n    },\n    _coverageSchema: \"1a1c01bbd47fc00a2c39e90264f33305004495a9\",\n    hash: \"2f21efd3ceae7a6ad27657490057ba70e0370e28\"\n  };\n  var coverage = global[gcv] || (global[gcv] = {});\n  if (!coverage[path] || coverage[path].hash !== hash) {\n    coverage[path] = coverageData;\n  }\n  var actualCoverage = coverage[path];\n  {\n    // @ts-ignore\n    cov_r32n2fhxm = function () {\n      return actualCoverage;\n    };\n  }\n  return actualCoverage;\n}\ncov_r32n2fhxm();\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"C:/projetos/scope-ai/frontend/src/app/features/projects/components/podcast-modal/podcast-modal.component.ts.scss?ngResource!=!C:\\\\projetos\\\\scope-ai\\\\frontend\\\\node_modules\\\\@ngtools\\\\webpack\\\\src\\\\loaders\\\\inline-resource.js?data=CgkJCTpob3N0IDo6bmctZGVlcCAucG9kY2FzdC1tb2RhbCAucC1kaWFsb2ctY29udGVudCB7CgkJCQlwYWRkaW5nOiAwOwoJCQl9CgoJCQk6aG9zdCA6Om5nLWRlZXAgLnBvZGNhc3QtbW9kYWwgLnAtZGlhbG9nLWhlYWRlciB7CgkJCQlwYWRkaW5nOiAxLjVyZW0gMS41cmVtIDFyZW0gMS41cmVtOwoJCQkJYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlNWU3ZWI7CgkJCX0KCgkJCS5wb2RjYXN0LXBsYXllci1jb250YWluZXIgewoJCQkJcGFkZGluZzogMS41cmVtOwoJCQl9CgoJCQlhdWRpbyB7CgkJCQloZWlnaHQ6IDU0cHg7CgkJCX0KCgkJCWF1ZGlvOjotd2Via2l0LW1lZGlhLWNvbnRyb2xzLXBhbmVsIHsKCQkJCWJhY2tncm91bmQtY29sb3I6ICNmOWZhZmI7CgkJCQlib3JkZXItcmFkaXVzOiA4cHg7CgkJCX0KCgkJCS8qIFJlc3BvbnNpdmUgYWRqdXN0bWVudHMgKi8KCQkJQG1lZGlhIChtYXgtd2lkdGg6IDY0MHB4KSB7CgkJCQkucG9kY2FzdC1wbGF5ZXItY29udGFpbmVyIHsKCQkJCQlwYWRkaW5nOiAxcmVtOwoJCQkJfQoKCQkJCTpob3N0IDo6bmctZGVlcCAucG9kY2FzdC1tb2RhbCAucC1kaWFsb2ctaGVhZGVyIHsKCQkJCQlwYWRkaW5nOiAxcmVtOwoJCQkJfQoKCQkJCS5iZy1ncmFkaWVudC10by1iciB7CgkJCQkJcGFkZGluZzogMS41cmVtOwoJCQkJfQoJCQl9CgkJ!C:/projetos/scope-ai/frontend/src/app/features/projects/components/podcast-modal/podcast-modal.component.ts\";\nimport { CommonModule } from '@angular/common';\nimport { Component, EventEmitter, Input, Output } from '@angular/core';\nimport { ButtonModule } from 'primeng/button';\nimport { DialogModule } from 'primeng/dialog';\n/**\n * Componente de modal para reprodução de podcasts\n *\n * @example\n * ```html\n * <app-podcast-modal\n *   [data]=\"{ visible: true, url: 'audio.mp3', title: 'Meu Projeto' }\"\n *   (close)=\"fecharModal()\"\n *   (generate)=\"gerarPodcast($event)\"\n * ></app-podcast-modal>\n * ```\n */\ncov_r32n2fhxm().s[0]++;\nlet PodcastModalComponent = class PodcastModalComponent {\n  data = (cov_r32n2fhxm().s[1]++, {\n    visible: false,\n    generating: false,\n    url: '',\n    title: ''\n  });\n  close = (cov_r32n2fhxm().s[2]++, new EventEmitter());\n  generate = (cov_r32n2fhxm().s[3]++, new EventEmitter());\n  /** Mensagem de erro do áudio (se houver) */\n  audioError = (cov_r32n2fhxm().s[4]++, null);\n  ngOnDestroy() {\n    cov_r32n2fhxm().f[0]++;\n    cov_r32n2fhxm().s[5]++;\n    // Limpar estado de erro ao destruir componente\n    this.audioError = null;\n  }\n  /**\n   * Fecha o modal e emite evento de fechamento\n   */\n  onClose() {\n    cov_r32n2fhxm().f[1]++;\n    cov_r32n2fhxm().s[6]++;\n    this.audioError = null;\n    cov_r32n2fhxm().s[7]++;\n    this.close.emit();\n  }\n  /**\n   * Solicita geração de podcast para o projeto\n   * Emite o ID do projeto se disponível\n   */\n  generatePodcast() {\n    cov_r32n2fhxm().f[2]++;\n    cov_r32n2fhxm().s[8]++;\n    if (this.data.projectId) {\n      cov_r32n2fhxm().b[0][0]++;\n      cov_r32n2fhxm().s[9]++;\n      this.generate.emit(this.data.projectId);\n    } else {\n      cov_r32n2fhxm().b[0][1]++;\n    }\n  }\n  /**\n   * Trata erros no carregamento do áudio\n   * @param event - Evento de erro do elemento audio\n   */\n  onAudioError(event) {\n    cov_r32n2fhxm().f[3]++;\n    cov_r32n2fhxm().s[10]++;\n    console.error('Erro ao carregar áudio:', event);\n    cov_r32n2fhxm().s[11]++;\n    this.audioError = 'Erro ao carregar o áudio. Por favor, tente novamente.';\n  }\n  static propDecorators = (cov_r32n2fhxm().s[12]++, {\n    data: [{\n      type: Input\n    }],\n    close: [{\n      type: Output\n    }],\n    generate: [{\n      type: Output\n    }]\n  });\n};\ncov_r32n2fhxm().s[13]++;\nPodcastModalComponent = __decorate([Component({\n  selector: 'app-podcast-modal',\n  standalone: true,\n  imports: [CommonModule, DialogModule, ButtonModule],\n  template: `\n\t\t<p-dialog\n\t\t\t[visible]=\"data.visible\"\n\t\t\t[modal]=\"true\"\n\t\t\t[closable]=\"true\"\n\t\t\t[draggable]=\"false\"\n\t\t\t[resizable]=\"false\"\n\t\t\tstyleClass=\"podcast-modal\"\n\t\t\t[style]=\"{ width: '90vw', maxWidth: '600px' }\"\n\t\t\t(onHide)=\"onClose()\"\n\t\t>\n\t\t\t<ng-template pTemplate=\"header\">\n\t\t\t\t<div class=\"flex items-center gap-3\">\n\t\t\t\t\t<i class=\"pi pi-microphone text-purple-600 text-2xl\"></i>\n\t\t\t\t\t<div>\n\t\t\t\t\t\t<h2 class=\"text-xl font-bold text-gray-900 m-0\">Podcast do Projeto</h2>\n\t\t\t\t\t\t<p class=\"text-sm text-gray-600 m-0 mt-1\">{{ data.title }}</p>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</ng-template>\n\n\t\t\t<ng-template pTemplate=\"content\">\n\t\t\t\t<div class=\"podcast-player-container\">\n\t\t\t\t\t<!-- Player Container -->\n\t\t\t\t\t<div\n\t\t\t\t\t\tclass=\"bg-gradient-to-br from-purple-50 to-indigo-50 rounded-2xl p-8 text-center border border-purple-200\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<!-- Podcast Icon -->\n\t\t\t\t\t\t<div class=\"mb-6\">\n\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\tclass=\"w-20 h-20 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto shadow-lg\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<i class=\"pi pi-microphone text-white text-3xl\"></i>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t<!-- Project Info -->\n\t\t\t\t\t\t<div class=\"mb-6\">\n\t\t\t\t\t\t\t<h3 class=\"text-lg font-semibold text-gray-900 mb-2\">{{ data.title }}</h3>\n\t\t\t\t\t\t\t<p class=\"text-sm text-gray-600\">Análise detalhada do projeto em formato de podcast</p>\n\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t<!-- Loading State -->\n\t\t\t\t\t\t@if (data.generating) {\n\t\t\t\t\t\t\t<div class=\"bg-white rounded-xl p-6 shadow-sm border border-gray-200\">\n\t\t\t\t\t\t\t\t<div class=\"flex flex-col items-center gap-4\">\n\t\t\t\t\t\t\t\t\t<i class=\"pi pi-spin pi-spinner text-3xl text-purple-600\"></i>\n\t\t\t\t\t\t\t\t\t<div class=\"text-center\">\n\t\t\t\t\t\t\t\t\t\t<p class=\"font-semibold text-gray-900\">Gerando Podcast...</p>\n\t\t\t\t\t\t\t\t\t\t<p class=\"text-sm text-gray-600\">\n\t\t\t\t\t\t\t\t\t\t\tAguarde 1-2 minutos enquanto criamos o áudio\n\t\t\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t<!-- Audio Player -->\n\t\t\t\t\t\t@if (data.url && !data.generating) {\n\t\t\t\t\t\t\t<div class=\"bg-white rounded-xl p-4 shadow-sm border border-gray-200\">\n\t\t\t\t\t\t\t\t<audio\n\t\t\t\t\t\t\t\t\tcontrols\n\t\t\t\t\t\t\t\t\tautoplay\n\t\t\t\t\t\t\t\t\tclass=\"w-full\"\n\t\t\t\t\t\t\t\t\t[src]=\"data.url\"\n\t\t\t\t\t\t\t\t\tpreload=\"auto\"\n\t\t\t\t\t\t\t\t\t(error)=\"onAudioError($event)\"\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<source [src]=\"data.url\" type=\"audio/mpeg\" />\n\t\t\t\t\t\t\t\t\t<p class=\"text-red-600 text-sm\">Seu navegador não suporta o elemento de áudio.</p>\n\t\t\t\t\t\t\t\t</audio>\n\n\t\t\t\t\t\t\t\t<!-- Nota sobre autoplay no Safari -->\n\t\t\t\t\t\t\t\t<div class=\"mt-2 text-xs text-gray-500 text-center\">\n\t\t\t\t\t\t\t\t\t<p>⚠️ Alguns navegadores bloqueiam autoplay. Clique no play se necessário.</p>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t<!-- Empty State -->\n\t\t\t\t\t\t@if (!data.url && !data.generating) {\n\t\t\t\t\t\t\t<div class=\"bg-white rounded-xl p-6 shadow-sm border border-gray-200\">\n\t\t\t\t\t\t\t\t<div class=\"text-center\">\n\t\t\t\t\t\t\t\t\t<i class=\"pi pi-microphone text-4xl text-gray-400 mb-4\"></i>\n\t\t\t\t\t\t\t\t\t<p class=\"text-gray-600 mb-4\">Nenhum podcast disponível</p>\n\t\t\t\t\t\t\t\t\t@if (data.projectId) {\n\t\t\t\t\t\t\t\t\t\t<button\n\t\t\t\t\t\t\t\t\t\t\tpButton\n\t\t\t\t\t\t\t\t\t\t\tlabel=\"Gerar Podcast\"\n\t\t\t\t\t\t\t\t\t\t\ticon=\"pi pi-play\"\n\t\t\t\t\t\t\t\t\t\t\tclass=\"p-button-outlined p-button-primary\"\n\t\t\t\t\t\t\t\t\t\t\t(click)=\"generatePodcast()\"\n\t\t\t\t\t\t\t\t\t\t\t[attr.aria-label]=\"'Gerar podcast para ' + data.title\"\n\t\t\t\t\t\t\t\t\t\t></button>\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t<!-- Error State -->\n\t\t\t\t\t\t@if (audioError) {\n\t\t\t\t\t\t\t<div class=\"bg-red-50 rounded-xl p-4 mt-4 border border-red-200\">\n\t\t\t\t\t\t\t\t<div class=\"flex items-center gap-2 text-red-700\">\n\t\t\t\t\t\t\t\t\t<i class=\"pi pi-exclamation-triangle\"></i>\n\t\t\t\t\t\t\t\t\t<p class=\"text-sm\">{{ audioError }}</p>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t<!-- Player Info -->\n\t\t\t\t\t\t<div class=\"mt-4 text-xs text-gray-500\">\n\t\t\t\t\t\t\t<p>💡 Use os controles de áudio para pausar, ajustar volume ou pular trechos</p>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\n\t\t\t\t\t<!-- Additional Actions -->\n\t\t\t\t\t<div class=\"flex justify-center gap-3 mt-6\">\n\t\t\t\t\t\t<button\n\t\t\t\t\t\t\tpButton\n\t\t\t\t\t\t\tlabel=\"Fechar\"\n\t\t\t\t\t\t\ticon=\"pi pi-times\"\n\t\t\t\t\t\t\tclass=\"p-button-outlined p-button-secondary\"\n\t\t\t\t\t\t\t(click)=\"onClose()\"\n\t\t\t\t\t\t\t[attr.aria-label]=\"'Fechar modal do podcast'\"\n\t\t\t\t\t\t></button>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</ng-template>\n\t\t</p-dialog>\n\t`,\n  styles: [__NG_CLI_RESOURCE__0]\n})], PodcastModalComponent);\nexport { PodcastModalComponent };", "map": {"version": 3, "names": ["CommonModule", "Component", "EventEmitter", "Input", "Output", "ButtonModule", "DialogModule", "cov_r32n2fhxm", "s", "PodcastModalComponent", "data", "visible", "generating", "url", "title", "close", "generate", "audioError", "ngOnDestroy", "f", "onClose", "emit", "generatePodcast", "projectId", "b", "onAudioError", "event", "console", "error", "__decorate", "selector", "standalone", "imports", "template"], "sources": ["C:\\projetos\\scope-ai\\frontend\\src\\app\\features\\projects\\components\\podcast-modal\\podcast-modal.component.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\r\nimport { Component, EventEmitter, Input, OnDestroy, Output } from '@angular/core';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { DialogModule } from 'primeng/dialog';\r\n\r\n/**\r\n * Interface para os dados do modal de podcast\r\n */\r\nexport interface PodcastModalData {\r\n\t/** Controla a visibilidade do modal */\r\n\tvisible: boolean;\r\n\t/** Indica se o podcast está sendo gerado */\r\n\tgenerating: boolean;\r\n\t/** URL do arquivo de áudio do podcast */\r\n\turl: string;\r\n\t/** Título do projeto para exibição */\r\n\ttitle: string;\r\n\t/** ID do projeto (opcional, usado para gerar podcast) */\r\n\tprojectId?: string;\r\n}\r\n\r\n/**\r\n * Componente de modal para reprodução de podcasts\r\n *\r\n * @example\r\n * ```html\r\n * <app-podcast-modal\r\n *   [data]=\"{ visible: true, url: 'audio.mp3', title: 'Meu Projeto' }\"\r\n *   (close)=\"fecharModal()\"\r\n *   (generate)=\"gerarPodcast($event)\"\r\n * ></app-podcast-modal>\r\n * ```\r\n */\r\n@Component({\r\n\tselector: 'app-podcast-modal',\r\n\tstandalone: true,\r\n\timports: [CommonModule, DialogModule, ButtonModule],\r\n\ttemplate: `\r\n\t\t<p-dialog\r\n\t\t\t[visible]=\"data.visible\"\r\n\t\t\t[modal]=\"true\"\r\n\t\t\t[closable]=\"true\"\r\n\t\t\t[draggable]=\"false\"\r\n\t\t\t[resizable]=\"false\"\r\n\t\t\tstyleClass=\"podcast-modal\"\r\n\t\t\t[style]=\"{ width: '90vw', maxWidth: '600px' }\"\r\n\t\t\t(onHide)=\"onClose()\"\r\n\t\t>\r\n\t\t\t<ng-template pTemplate=\"header\">\r\n\t\t\t\t<div class=\"flex items-center gap-3\">\r\n\t\t\t\t\t<i class=\"pi pi-microphone text-purple-600 text-2xl\"></i>\r\n\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<h2 class=\"text-xl font-bold text-gray-900 m-0\">Podcast do Projeto</h2>\r\n\t\t\t\t\t\t<p class=\"text-sm text-gray-600 m-0 mt-1\">{{ data.title }}</p>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</ng-template>\r\n\r\n\t\t\t<ng-template pTemplate=\"content\">\r\n\t\t\t\t<div class=\"podcast-player-container\">\r\n\t\t\t\t\t<!-- Player Container -->\r\n\t\t\t\t\t<div\r\n\t\t\t\t\t\tclass=\"bg-gradient-to-br from-purple-50 to-indigo-50 rounded-2xl p-8 text-center border border-purple-200\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<!-- Podcast Icon -->\r\n\t\t\t\t\t\t<div class=\"mb-6\">\r\n\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\tclass=\"w-20 h-20 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto shadow-lg\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<i class=\"pi pi-microphone text-white text-3xl\"></i>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<!-- Project Info -->\r\n\t\t\t\t\t\t<div class=\"mb-6\">\r\n\t\t\t\t\t\t\t<h3 class=\"text-lg font-semibold text-gray-900 mb-2\">{{ data.title }}</h3>\r\n\t\t\t\t\t\t\t<p class=\"text-sm text-gray-600\">Análise detalhada do projeto em formato de podcast</p>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<!-- Loading State -->\r\n\t\t\t\t\t\t@if (data.generating) {\r\n\t\t\t\t\t\t\t<div class=\"bg-white rounded-xl p-6 shadow-sm border border-gray-200\">\r\n\t\t\t\t\t\t\t\t<div class=\"flex flex-col items-center gap-4\">\r\n\t\t\t\t\t\t\t\t\t<i class=\"pi pi-spin pi-spinner text-3xl text-purple-600\"></i>\r\n\t\t\t\t\t\t\t\t\t<div class=\"text-center\">\r\n\t\t\t\t\t\t\t\t\t\t<p class=\"font-semibold text-gray-900\">Gerando Podcast...</p>\r\n\t\t\t\t\t\t\t\t\t\t<p class=\"text-sm text-gray-600\">\r\n\t\t\t\t\t\t\t\t\t\t\tAguarde 1-2 minutos enquanto criamos o áudio\r\n\t\t\t\t\t\t\t\t\t\t</p>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t<!-- Audio Player -->\r\n\t\t\t\t\t\t@if (data.url && !data.generating) {\r\n\t\t\t\t\t\t\t<div class=\"bg-white rounded-xl p-4 shadow-sm border border-gray-200\">\r\n\t\t\t\t\t\t\t\t<audio\r\n\t\t\t\t\t\t\t\t\tcontrols\r\n\t\t\t\t\t\t\t\t\tautoplay\r\n\t\t\t\t\t\t\t\t\tclass=\"w-full\"\r\n\t\t\t\t\t\t\t\t\t[src]=\"data.url\"\r\n\t\t\t\t\t\t\t\t\tpreload=\"auto\"\r\n\t\t\t\t\t\t\t\t\t(error)=\"onAudioError($event)\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<source [src]=\"data.url\" type=\"audio/mpeg\" />\r\n\t\t\t\t\t\t\t\t\t<p class=\"text-red-600 text-sm\">Seu navegador não suporta o elemento de áudio.</p>\r\n\t\t\t\t\t\t\t\t</audio>\r\n\r\n\t\t\t\t\t\t\t\t<!-- Nota sobre autoplay no Safari -->\r\n\t\t\t\t\t\t\t\t<div class=\"mt-2 text-xs text-gray-500 text-center\">\r\n\t\t\t\t\t\t\t\t\t<p>⚠️ Alguns navegadores bloqueiam autoplay. Clique no play se necessário.</p>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t<!-- Empty State -->\r\n\t\t\t\t\t\t@if (!data.url && !data.generating) {\r\n\t\t\t\t\t\t\t<div class=\"bg-white rounded-xl p-6 shadow-sm border border-gray-200\">\r\n\t\t\t\t\t\t\t\t<div class=\"text-center\">\r\n\t\t\t\t\t\t\t\t\t<i class=\"pi pi-microphone text-4xl text-gray-400 mb-4\"></i>\r\n\t\t\t\t\t\t\t\t\t<p class=\"text-gray-600 mb-4\">Nenhum podcast disponível</p>\r\n\t\t\t\t\t\t\t\t\t@if (data.projectId) {\r\n\t\t\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\t\t\tpButton\r\n\t\t\t\t\t\t\t\t\t\t\tlabel=\"Gerar Podcast\"\r\n\t\t\t\t\t\t\t\t\t\t\ticon=\"pi pi-play\"\r\n\t\t\t\t\t\t\t\t\t\t\tclass=\"p-button-outlined p-button-primary\"\r\n\t\t\t\t\t\t\t\t\t\t\t(click)=\"generatePodcast()\"\r\n\t\t\t\t\t\t\t\t\t\t\t[attr.aria-label]=\"'Gerar podcast para ' + data.title\"\r\n\t\t\t\t\t\t\t\t\t\t></button>\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t<!-- Error State -->\r\n\t\t\t\t\t\t@if (audioError) {\r\n\t\t\t\t\t\t\t<div class=\"bg-red-50 rounded-xl p-4 mt-4 border border-red-200\">\r\n\t\t\t\t\t\t\t\t<div class=\"flex items-center gap-2 text-red-700\">\r\n\t\t\t\t\t\t\t\t\t<i class=\"pi pi-exclamation-triangle\"></i>\r\n\t\t\t\t\t\t\t\t\t<p class=\"text-sm\">{{ audioError }}</p>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t<!-- Player Info -->\r\n\t\t\t\t\t\t<div class=\"mt-4 text-xs text-gray-500\">\r\n\t\t\t\t\t\t\t<p>💡 Use os controles de áudio para pausar, ajustar volume ou pular trechos</p>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t<!-- Additional Actions -->\r\n\t\t\t\t\t<div class=\"flex justify-center gap-3 mt-6\">\r\n\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\tpButton\r\n\t\t\t\t\t\t\tlabel=\"Fechar\"\r\n\t\t\t\t\t\t\ticon=\"pi pi-times\"\r\n\t\t\t\t\t\t\tclass=\"p-button-outlined p-button-secondary\"\r\n\t\t\t\t\t\t\t(click)=\"onClose()\"\r\n\t\t\t\t\t\t\t[attr.aria-label]=\"'Fechar modal do podcast'\"\r\n\t\t\t\t\t\t></button>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</ng-template>\r\n\t\t</p-dialog>\r\n\t`,\r\n\tstyles: [\r\n\t\t`\r\n\t\t\t:host ::ng-deep .podcast-modal .p-dialog-content {\r\n\t\t\t\tpadding: 0;\r\n\t\t\t}\r\n\r\n\t\t\t:host ::ng-deep .podcast-modal .p-dialog-header {\r\n\t\t\t\tpadding: 1.5rem 1.5rem 1rem 1.5rem;\r\n\t\t\t\tborder-bottom: 1px solid #e5e7eb;\r\n\t\t\t}\r\n\r\n\t\t\t.podcast-player-container {\r\n\t\t\t\tpadding: 1.5rem;\r\n\t\t\t}\r\n\r\n\t\t\taudio {\r\n\t\t\t\theight: 54px;\r\n\t\t\t}\r\n\r\n\t\t\taudio::-webkit-media-controls-panel {\r\n\t\t\t\tbackground-color: #f9fafb;\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t}\r\n\r\n\t\t\t/* Responsive adjustments */\r\n\t\t\t@media (max-width: 640px) {\r\n\t\t\t\t.podcast-player-container {\r\n\t\t\t\t\tpadding: 1rem;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t:host ::ng-deep .podcast-modal .p-dialog-header {\r\n\t\t\t\t\tpadding: 1rem;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.bg-gradient-to-br {\r\n\t\t\t\t\tpadding: 1.5rem;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t`,\r\n\t],\r\n})\r\nexport class PodcastModalComponent implements OnDestroy {\r\n\t/** Dados do modal incluindo visibilidade, URL do áudio e título */\r\n\t@Input() data: PodcastModalData = {\r\n\t\tvisible: false,\r\n\t\tgenerating: false,\r\n\t\turl: '',\r\n\t\ttitle: '',\r\n\t};\r\n\r\n\t/** Evento emitido quando o modal é fechado */\r\n\t@Output() close = new EventEmitter<void>();\r\n\r\n\t/** Evento emitido quando o usuário solicita geração de podcast */\r\n\t@Output() generate = new EventEmitter<string>();\r\n\r\n\t/** Mensagem de erro do áudio (se houver) */\r\n\tpublic audioError: string | null = null;\r\n\r\n\tngOnDestroy(): void {\r\n\t\t// Limpar estado de erro ao destruir componente\r\n\t\tthis.audioError = null;\r\n\t}\r\n\r\n\t/**\r\n\t * Fecha o modal e emite evento de fechamento\r\n\t */\r\n\tpublic onClose(): void {\r\n\t\tthis.audioError = null;\r\n\t\tthis.close.emit();\r\n\t}\r\n\r\n\t/**\r\n\t * Solicita geração de podcast para o projeto\r\n\t * Emite o ID do projeto se disponível\r\n\t */\r\n\tpublic generatePodcast(): void {\r\n\t\tif (this.data.projectId) {\r\n\t\t\tthis.generate.emit(this.data.projectId);\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Trata erros no carregamento do áudio\r\n\t * @param event - Evento de erro do elemento audio\r\n\t */\r\n\tpublic onAudioError(event: Event): void {\r\n\t\tconsole.error('Erro ao carregar áudio:', event);\r\n\t\tthis.audioError = 'Erro ao carregar o áudio. Por favor, tente novamente.';\r\n\t}\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAaC,MAAM,QAAQ,eAAe;AACjF,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,YAAY,QAAQ,gBAAgB;AAkB7C;;;;;;;;;;;;AAAAC,aAAA,GAAAC,CAAA;AA2LO,IAAMC,qBAAqB,GAA3B,MAAMA,qBAAqB;EAExBC,IAAI,IAAAH,aAAA,GAAAC,CAAA,OAAqB;IACjCG,OAAO,EAAE,KAAK;IACdC,UAAU,EAAE,KAAK;IACjBC,GAAG,EAAE,EAAE;IACPC,KAAK,EAAE;GACP;EAGSC,KAAK,IAAAR,aAAA,GAAAC,CAAA,OAAG,IAAIN,YAAY,EAAQ;EAGhCc,QAAQ,IAAAT,aAAA,GAAAC,CAAA,OAAG,IAAIN,YAAY,EAAU;EAE/C;EACOe,UAAU,IAAAV,aAAA,GAAAC,CAAA,OAAkB,IAAI;EAEvCU,WAAWA,CAAA;IAAAX,aAAA,GAAAY,CAAA;IAAAZ,aAAA,GAAAC,CAAA;IACV;IACA,IAAI,CAACS,UAAU,GAAG,IAAI;EACvB;EAEA;;;EAGOG,OAAOA,CAAA;IAAAb,aAAA,GAAAY,CAAA;IAAAZ,aAAA,GAAAC,CAAA;IACb,IAAI,CAACS,UAAU,GAAG,IAAI;IAACV,aAAA,GAAAC,CAAA;IACvB,IAAI,CAACO,KAAK,CAACM,IAAI,EAAE;EAClB;EAEA;;;;EAIOC,eAAeA,CAAA;IAAAf,aAAA,GAAAY,CAAA;IAAAZ,aAAA,GAAAC,CAAA;IACrB,IAAI,IAAI,CAACE,IAAI,CAACa,SAAS,EAAE;MAAAhB,aAAA,GAAAiB,CAAA;MAAAjB,aAAA,GAAAC,CAAA;MACxB,IAAI,CAACQ,QAAQ,CAACK,IAAI,CAAC,IAAI,CAACX,IAAI,CAACa,SAAS,CAAC;IACxC,CAAC;MAAAhB,aAAA,GAAAiB,CAAA;IAAA;EACF;EAEA;;;;EAIOC,YAAYA,CAACC,KAAY;IAAAnB,aAAA,GAAAY,CAAA;IAAAZ,aAAA,GAAAC,CAAA;IAC/BmB,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEF,KAAK,CAAC;IAACnB,aAAA,GAAAC,CAAA;IAChD,IAAI,CAACS,UAAU,GAAG,uDAAuD;EAC1E;;;YA9CCd;IAAK;;YAQLC;IAAM;;YAGNA;IAAM;;;;AAbKK,qBAAqB,GAAAoB,UAAA,EA/KjC5B,SAAS,CAAC;EACV6B,QAAQ,EAAE,mBAAmB;EAC7BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAChC,YAAY,EAAEM,YAAY,EAAED,YAAY,CAAC;EACnD4B,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAiIT;;CAyCD,CAAC,C,EACWxB,qBAAqB,CAiDjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}