<div class="w-full border-b border-gray-200 shadow-sm bg-white">
	<div class="mx-auto">
		<p-toolbar>
			<ng-template pTemplate="start">
				<div class="flex items-center">
					<button pButton icon="pi pi-bars" class="p-button-text mr-4" (click)="onMenuToggle()"></button>
					<a routerLink="/" class="no-underline flex items-center">
						<img
							src="images/scope.jpg"
							alt="ScopeAI Logo"
							height="40"
							class="mr-2 h-10"
							onerror="this.src='https://cdn-icons-png.flaticon.com/512/8059/8059485.png'; this.onerror=null;"
						/>
						<!-- <span class="text-xl font-bold text-blue-800">Scope</span>
						<span class="text-xl font-medium text-blue-600">AI</span> -->
					</a>
				</div>
			</ng-template>

			<ng-template pTemplate="center">
				<div class="hidden md:block">
					<span class="text-lg font-medium text-gray-700">Sistema de Estimativas de Escopo com IA</span>
				</div>
			</ng-template>

			<ng-template pTemplate="end">
				<div class="flex items-center gap-4">
					<div class="cursor-pointer flex items-center gap-2" (click)="userMenu.toggle($event)">
						<p-avatar
							icon="pi pi-user"
							shape="circle"
							[style]="{ 'background-color': '#2563eb', 'color': '#ffffff' }"
						>
						</p-avatar>
						<span class="hidden md:block text-sm font-medium">Usuário</span>
					</div>

					<p-menu #userMenu [popup]="true" [model]="userMenuItems"></p-menu>
				</div>
			</ng-template>
		</p-toolbar>
	</div>
</div>
