"""
Lighthouse Analysis Tools para Agno

Tools customizadas para análise de dados Lighthouse:
- Processamento de métricas Core Web Vitals
- Análise de performance, acessibilidade, SEO, best practices
- Extração de oportunidades e insights
"""

import logging
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class LighthouseMetrics(BaseModel):
    """Schema para métricas Lighthouse processadas"""
    performance_score: float = Field(...,
                                     description="Score de performance (0-100)")
    accessibility_score: float = Field(...,
                                       description="Score de acessibilidade (0-100)")
    seo_score: float = Field(..., description="Score de SEO (0-100)")
    best_practices_score: float = Field(...,
                                        description="Score de best practices (0-100)")

    # Core Web Vitals
    lcp: Optional[float] = Field(
        None, description="Largest Contentful Paint (ms)")
    fcp: Optional[float] = Field(
        None, description="First Contentful Paint (ms)")
    cls: Optional[float] = Field(None, description="Cumulative Layout Shift")
    fid: Optional[float] = Field(None, description="First Input Delay (ms)")

    # Issues e oportunidades
    performance_issues: List[str] = Field(default_factory=list)
    accessibility_issues: List[str] = Field(default_factory=list)
    seo_issues: List[str] = Field(default_factory=list)
    opportunities: List[str] = Field(default_factory=list)


class LighthouseAnalysisTools:
    """
    Tools para análise de dados Lighthouse

    Processa dados brutos do Lighthouse e extrai insights
    técnicos para análise arquitetural.
    """

    def analyze_lighthouse_data(self, lighthouse_data: Dict[str, Any]) -> LighthouseMetrics:
        """
        Analisa dados brutos do Lighthouse e extrai métricas estruturadas

        Args:
            lighthouse_data: Dados brutos do Lighthouse

        Returns:
            Métricas processadas e estruturadas
        """
        try:
            logger.info("Iniciando análise de dados Lighthouse")

            # Extrair scores principais
            performance_score = self._extract_score(
                lighthouse_data, "performance")
            accessibility_score = self._extract_score(
                lighthouse_data, "acessibilidade")
            seo_score = self._extract_score(lighthouse_data, "seo_tecnico")
            best_practices_score = self._extract_score(
                lighthouse_data, "best_practices")

            # Extrair Core Web Vitals
            core_vitals = self._extract_core_web_vitals(lighthouse_data)

            # Extrair issues e oportunidades
            issues = self._extract_issues(lighthouse_data)
            opportunities = self._extract_opportunities(lighthouse_data)

            metrics = LighthouseMetrics(
                performance_score=performance_score,
                accessibility_score=accessibility_score,
                seo_score=seo_score,
                best_practices_score=best_practices_score,
                lcp=core_vitals.get("lcp"),
                fcp=core_vitals.get("fcp"),
                cls=core_vitals.get("cls"),
                fid=core_vitals.get("fid"),
                performance_issues=issues.get("performance", []),
                accessibility_issues=issues.get("accessibility", []),
                seo_issues=issues.get("seo", []),
                opportunities=opportunities
            )

            logger.info(
                f"Lighthouse análise concluída - Performance: {performance_score}")
            return metrics

        except Exception as e:
            logger.error(f"Erro na análise Lighthouse: {str(e)}")
            raise

    def _extract_score(self, data: Dict[str, Any], category: str) -> float:
        """Extrai score de uma categoria específica"""
        try:
            category_data = data.get(category, {})
            score = category_data.get("score", 0)
            return float(score) if score is not None else 0.0
        except (ValueError, TypeError):
            return 0.0

    def _extract_core_web_vitals(self, data: Dict[str, Any]) -> Dict[str, Optional[float]]:
        """Extrai métricas Core Web Vitals"""
        try:
            performance_data = data.get("performance", {})
            metricas_core = performance_data.get("metricas_core", {})

            return {
                "lcp": self._safe_float(metricas_core.get("lcp")),
                "fcp": self._safe_float(metricas_core.get("fcp")),
                "cls": self._safe_float(metricas_core.get("cls")),
                "fid": self._safe_float(metricas_core.get("fid"))
            }
        except Exception:
            return {"lcp": None, "fcp": None, "cls": None, "fid": None}

    def _extract_issues(self, data: Dict[str, Any]) -> Dict[str, List[str]]:
        """Extrai issues por categoria"""
        try:
            issues = {
                "performance": [],
                "accessibility": [],
                "seo": []
            }

            # Performance issues
            perf_data = data.get("performance", {})
            if perf_data.get("score", 100) < 90:
                issues["performance"].extend(perf_data.get("issues", []))

            # Accessibility issues
            acc_data = data.get("acessibilidade", {})
            issues["accessibility"].extend(acc_data.get("issues", []))

            # SEO issues
            seo_data = data.get("seo_tecnico", {})
            issues["seo"].extend(seo_data.get("issues", []))

            return issues
        except Exception:
            return {"performance": [], "accessibility": [], "seo": []}

    def _extract_opportunities(self, data: Dict[str, Any]) -> List[str]:
        """Extrai oportunidades de melhoria"""
        try:
            performance_data = data.get("performance", {})
            return performance_data.get("oportunidades", [])
        except Exception:
            return []

    def _safe_float(self, value: Any) -> Optional[float]:
        """Converte valor para float de forma segura"""
        try:
            return float(value) if value is not None else None
        except (ValueError, TypeError):
            return None

    def get_critical_issues(self, lighthouse_data: Dict[str, Any]) -> List[str]:
        """
        Identifica issues críticos que requerem atenção imediata

        Args:
            lighthouse_data: Dados do Lighthouse

        Returns:
            Lista de issues críticos
        """
        try:
            critical_issues = []

            # Performance crítica (< 50)
            perf_score = self._extract_score(lighthouse_data, "performance")
            if perf_score < 50:
                critical_issues.append(
                    f"Performance crítica: {perf_score}/100")

            # Acessibilidade crítica (< 60)
            acc_score = self._extract_score(lighthouse_data, "acessibilidade")
            if acc_score < 60:
                critical_issues.append(
                    f"Acessibilidade crítica: {acc_score}/100")

            # Core Web Vitals ruins
            core_vitals = self._extract_core_web_vitals(lighthouse_data)
            lcp_value = core_vitals.get("lcp")
            cls_value = core_vitals.get("cls")

            if lcp_value is not None and lcp_value > 4000:  # > 4s
                critical_issues.append("LCP muito alto (> 4s)")
            if cls_value is not None and cls_value > 0.25:  # > 0.25
                critical_issues.append("CLS muito alto (> 0.25)")

            return critical_issues

        except Exception as e:
            logger.error(f"Erro ao identificar issues críticos: {str(e)}")
            return []

    def calculate_priority_score(self, lighthouse_data: Dict[str, Any]) -> float:
        """
        Calcula score de prioridade baseado nos dados Lighthouse

        Args:
            lighthouse_data: Dados do Lighthouse

        Returns:
            Score de prioridade (0-100, onde 100 = máxima prioridade)
        """
        try:
            # Scores individuais (invertidos - menor score = maior prioridade)
            perf_score = self._extract_score(lighthouse_data, "performance")
            acc_score = self._extract_score(lighthouse_data, "acessibilidade")
            seo_score = self._extract_score(lighthouse_data, "seo_tecnico")
            bp_score = self._extract_score(lighthouse_data, "best_practices")

            # Calcular prioridade (inverso da média)
            avg_score = (perf_score + acc_score + seo_score + bp_score) / 4
            priority_score = 100 - avg_score

            # Boost para issues críticos
            critical_issues = self.get_critical_issues(lighthouse_data)
            priority_score += len(critical_issues) * 10

            return min(priority_score, 100)  # Cap em 100

        except Exception as e:
            logger.error(f"Erro ao calcular priority score: {str(e)}")
            return 50  # Score neutro em caso de erro
