{"ast": null, "code": "import { Subject } from '../Subject';\nimport { asyncScheduler } from '../scheduler/async';\nimport { Subscription } from '../Subscription';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { arrRemove } from '../util/arrRemove';\nimport { popScheduler } from '../util/args';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function windowTime(windowTimeSpan, ...otherArgs) {\n  var _a, _b;\n  const scheduler = (_a = popScheduler(otherArgs)) !== null && _a !== void 0 ? _a : asyncScheduler;\n  const windowCreationInterval = (_b = otherArgs[0]) !== null && _b !== void 0 ? _b : null;\n  const maxWindowSize = otherArgs[1] || Infinity;\n  return operate((source, subscriber) => {\n    let windowRecords = [];\n    let restartOnClose = false;\n    const closeWindow = record => {\n      const {\n        window,\n        subs\n      } = record;\n      window.complete();\n      subs.unsubscribe();\n      arrRemove(windowRecords, record);\n      restartOnClose && startWindow();\n    };\n    const startWindow = () => {\n      if (windowRecords) {\n        const subs = new Subscription();\n        subscriber.add(subs);\n        const window = new Subject();\n        const record = {\n          window,\n          subs,\n          seen: 0\n        };\n        windowRecords.push(record);\n        subscriber.next(window.asObservable());\n        executeSchedule(subs, scheduler, () => closeWindow(record), windowTimeSpan);\n      }\n    };\n    if (windowCreationInterval !== null && windowCreationInterval >= 0) {\n      executeSchedule(subscriber, scheduler, startWindow, windowCreationInterval, true);\n    } else {\n      restartOnClose = true;\n    }\n    startWindow();\n    const loop = cb => windowRecords.slice().forEach(cb);\n    const terminate = cb => {\n      loop(({\n        window\n      }) => cb(window));\n      cb(subscriber);\n      subscriber.unsubscribe();\n    };\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      loop(record => {\n        record.window.next(value);\n        maxWindowSize <= ++record.seen && closeWindow(record);\n      });\n    }, () => terminate(consumer => consumer.complete()), err => terminate(consumer => consumer.error(err))));\n    return () => {\n      windowRecords = null;\n    };\n  });\n}", "map": {"version": 3, "names": ["Subject", "asyncScheduler", "Subscription", "operate", "createOperatorSubscriber", "arr<PERSON><PERSON><PERSON>", "popScheduler", "executeSchedule", "windowTime", "windowTimeSpan", "otherArgs", "_a", "_b", "scheduler", "windowCreationInterval", "maxWindowSize", "Infinity", "source", "subscriber", "windowRecords", "restartOnClose", "closeWindow", "record", "window", "subs", "complete", "unsubscribe", "startWindow", "add", "seen", "push", "next", "asObservable", "loop", "cb", "slice", "for<PERSON>ach", "terminate", "subscribe", "value", "consumer", "err", "error"], "sources": ["C:/projetos/scope-ai/frontend/node_modules/rxjs/dist/esm/internal/operators/windowTime.js"], "sourcesContent": ["import { Subject } from '../Subject';\nimport { asyncScheduler } from '../scheduler/async';\nimport { Subscription } from '../Subscription';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { arrRemove } from '../util/arrRemove';\nimport { popScheduler } from '../util/args';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function windowTime(windowTimeSpan, ...otherArgs) {\n    var _a, _b;\n    const scheduler = (_a = popScheduler(otherArgs)) !== null && _a !== void 0 ? _a : asyncScheduler;\n    const windowCreationInterval = (_b = otherArgs[0]) !== null && _b !== void 0 ? _b : null;\n    const maxWindowSize = otherArgs[1] || Infinity;\n    return operate((source, subscriber) => {\n        let windowRecords = [];\n        let restartOnClose = false;\n        const closeWindow = (record) => {\n            const { window, subs } = record;\n            window.complete();\n            subs.unsubscribe();\n            arrRemove(windowRecords, record);\n            restartOnClose && startWindow();\n        };\n        const startWindow = () => {\n            if (windowRecords) {\n                const subs = new Subscription();\n                subscriber.add(subs);\n                const window = new Subject();\n                const record = {\n                    window,\n                    subs,\n                    seen: 0,\n                };\n                windowRecords.push(record);\n                subscriber.next(window.asObservable());\n                executeSchedule(subs, scheduler, () => closeWindow(record), windowTimeSpan);\n            }\n        };\n        if (windowCreationInterval !== null && windowCreationInterval >= 0) {\n            executeSchedule(subscriber, scheduler, startWindow, windowCreationInterval, true);\n        }\n        else {\n            restartOnClose = true;\n        }\n        startWindow();\n        const loop = (cb) => windowRecords.slice().forEach(cb);\n        const terminate = (cb) => {\n            loop(({ window }) => cb(window));\n            cb(subscriber);\n            subscriber.unsubscribe();\n        };\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            loop((record) => {\n                record.window.next(value);\n                maxWindowSize <= ++record.seen && closeWindow(record);\n            });\n        }, () => terminate((consumer) => consumer.complete()), (err) => terminate((consumer) => consumer.error(err))));\n        return () => {\n            windowRecords = null;\n        };\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASC,cAAc,QAAQ,oBAAoB;AACnD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,SAASC,YAAY,QAAQ,cAAc;AAC3C,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAO,SAASC,UAAUA,CAACC,cAAc,EAAE,GAAGC,SAAS,EAAE;EACrD,IAAIC,EAAE,EAAEC,EAAE;EACV,MAAMC,SAAS,GAAG,CAACF,EAAE,GAAGL,YAAY,CAACI,SAAS,CAAC,MAAM,IAAI,IAAIC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGV,cAAc;EAChG,MAAMa,sBAAsB,GAAG,CAACF,EAAE,GAAGF,SAAS,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI;EACxF,MAAMG,aAAa,GAAGL,SAAS,CAAC,CAAC,CAAC,IAAIM,QAAQ;EAC9C,OAAOb,OAAO,CAAC,CAACc,MAAM,EAAEC,UAAU,KAAK;IACnC,IAAIC,aAAa,GAAG,EAAE;IACtB,IAAIC,cAAc,GAAG,KAAK;IAC1B,MAAMC,WAAW,GAAIC,MAAM,IAAK;MAC5B,MAAM;QAAEC,MAAM;QAAEC;MAAK,CAAC,GAAGF,MAAM;MAC/BC,MAAM,CAACE,QAAQ,CAAC,CAAC;MACjBD,IAAI,CAACE,WAAW,CAAC,CAAC;MAClBrB,SAAS,CAACc,aAAa,EAAEG,MAAM,CAAC;MAChCF,cAAc,IAAIO,WAAW,CAAC,CAAC;IACnC,CAAC;IACD,MAAMA,WAAW,GAAGA,CAAA,KAAM;MACtB,IAAIR,aAAa,EAAE;QACf,MAAMK,IAAI,GAAG,IAAItB,YAAY,CAAC,CAAC;QAC/BgB,UAAU,CAACU,GAAG,CAACJ,IAAI,CAAC;QACpB,MAAMD,MAAM,GAAG,IAAIvB,OAAO,CAAC,CAAC;QAC5B,MAAMsB,MAAM,GAAG;UACXC,MAAM;UACNC,IAAI;UACJK,IAAI,EAAE;QACV,CAAC;QACDV,aAAa,CAACW,IAAI,CAACR,MAAM,CAAC;QAC1BJ,UAAU,CAACa,IAAI,CAACR,MAAM,CAACS,YAAY,CAAC,CAAC,CAAC;QACtCzB,eAAe,CAACiB,IAAI,EAAEX,SAAS,EAAE,MAAMQ,WAAW,CAACC,MAAM,CAAC,EAAEb,cAAc,CAAC;MAC/E;IACJ,CAAC;IACD,IAAIK,sBAAsB,KAAK,IAAI,IAAIA,sBAAsB,IAAI,CAAC,EAAE;MAChEP,eAAe,CAACW,UAAU,EAAEL,SAAS,EAAEc,WAAW,EAAEb,sBAAsB,EAAE,IAAI,CAAC;IACrF,CAAC,MACI;MACDM,cAAc,GAAG,IAAI;IACzB;IACAO,WAAW,CAAC,CAAC;IACb,MAAMM,IAAI,GAAIC,EAAE,IAAKf,aAAa,CAACgB,KAAK,CAAC,CAAC,CAACC,OAAO,CAACF,EAAE,CAAC;IACtD,MAAMG,SAAS,GAAIH,EAAE,IAAK;MACtBD,IAAI,CAAC,CAAC;QAAEV;MAAO,CAAC,KAAKW,EAAE,CAACX,MAAM,CAAC,CAAC;MAChCW,EAAE,CAAChB,UAAU,CAAC;MACdA,UAAU,CAACQ,WAAW,CAAC,CAAC;IAC5B,CAAC;IACDT,MAAM,CAACqB,SAAS,CAAClC,wBAAwB,CAACc,UAAU,EAAGqB,KAAK,IAAK;MAC7DN,IAAI,CAAEX,MAAM,IAAK;QACbA,MAAM,CAACC,MAAM,CAACQ,IAAI,CAACQ,KAAK,CAAC;QACzBxB,aAAa,IAAI,EAAEO,MAAM,CAACO,IAAI,IAAIR,WAAW,CAACC,MAAM,CAAC;MACzD,CAAC,CAAC;IACN,CAAC,EAAE,MAAMe,SAAS,CAAEG,QAAQ,IAAKA,QAAQ,CAACf,QAAQ,CAAC,CAAC,CAAC,EAAGgB,GAAG,IAAKJ,SAAS,CAAEG,QAAQ,IAAKA,QAAQ,CAACE,KAAK,CAACD,GAAG,CAAC,CAAC,CAAC,CAAC;IAC9G,OAAO,MAAM;MACTtB,aAAa,GAAG,IAAI;IACxB,CAAC;EACL,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}