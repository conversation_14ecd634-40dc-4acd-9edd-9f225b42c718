export interface IClient {
	id: string;
	name: string;
	company: string;
	email: string;
	phone: string;
	address: string;
	sector: string;
	active: boolean;
	since: string;
	projectsCount: number;
	totalValue: string;
	lastContact: string;
	status: string;
	tags: Array<string>;
	contacts: Array<IClientContact>;
	created_at: string;
	updated_at: string;
	// 🚀 NOVO: Campos para PDF AI
	pdf_report_id?: string;
	pdf_generated_at?: string;
	pdf_report_available?: boolean;
}

export interface IClientContact {
	name: string;
	position: string;
	email: string;
	phone: string;
	primary: boolean;
}

export interface ISortOption {
	label: string;
	value: string;
}

export interface IStatusOption {
	label: string;
	value: string | null;
}

export interface ISectorOption {
	label: string;
	value: string | null;
}

export interface IPageSizeOption {
	label: string;
	value: number;
}

/**
 * Interface para o evento de mudança de página do PrimeNG Paginator
 * Equivalente ao PaginatorState do PrimeNG
 */
export interface IPageChangeEvent {
	first?: number;
	rows?: number;
	page?: number;
	pageCount?: number;
}
