{"version": 3, "sources": ["../../../../../../node_modules/@mermaid-js/parser/dist/chunks/mermaid-parser.core/chunk-EXZZNE6F.mjs"], "sourcesContent": ["import { AbstractMermaidTokenBuilder, CommonValueConverter, InfoGeneratedModule, MermaidGeneratedSharedModule, __name } from \"./chunk-7PKI6E2E.mjs\";\n\n// src/language/info/module.ts\nimport { EmptyFileSystem, createDefaultCoreModule, createDefaultSharedCoreModule, inject } from \"langium\";\n\n// src/language/info/tokenBuilder.ts\nvar InfoTokenBuilder = class extends AbstractMermaidTokenBuilder {\n  static {\n    __name(this, \"InfoTokenBuilder\");\n  }\n  constructor() {\n    super([\"info\", \"showInfo\"]);\n  }\n};\n\n// src/language/info/module.ts\nvar InfoModule = {\n  parser: {\n    TokenBuilder: /* @__PURE__ */__name(() => new InfoTokenBuilder(), \"TokenBuilder\"),\n    ValueConverter: /* @__PURE__ */__name(() => new CommonValueConverter(), \"ValueConverter\")\n  }\n};\nfunction createInfoServices(context = EmptyFileSystem) {\n  const shared = inject(createDefaultSharedCoreModule(context), MermaidGeneratedSharedModule);\n  const Info = inject(createDefaultCoreModule({\n    shared\n  }), InfoGeneratedModule, InfoModule);\n  shared.ServiceRegistry.register(Info);\n  return {\n    shared,\n    Info\n  };\n}\n__name(createInfoServices, \"createInfoServices\");\nexport { InfoModule, createInfoServices };"], "mappings": ";;;;;;;;;;;;;;AAMA,IAAI,mBAAmB,cAAc,4BAA4B;AAAA,EAC/D,OAAO;AACL,WAAO,MAAM,kBAAkB;AAAA,EACjC;AAAA,EACA,cAAc;AACZ,UAAM,CAAC,QAAQ,UAAU,CAAC;AAAA,EAC5B;AACF;AAGA,IAAI,aAAa;AAAA,EACf,QAAQ;AAAA,IACN,cAA6B,OAAO,MAAM,IAAI,iBAAiB,GAAG,cAAc;AAAA,IAChF,gBAA+B,OAAO,MAAM,IAAI,qBAAqB,GAAG,gBAAgB;AAAA,EAC1F;AACF;AACA,SAAS,mBAAmB,UAAU,iBAAiB;AACrD,QAAM,SAAS,OAAO,8BAA8B,OAAO,GAAG,4BAA4B;AAC1F,QAAM,OAAO,OAAO,wBAAwB;AAAA,IAC1C;AAAA,EACF,CAAC,GAAG,qBAAqB,UAAU;AACnC,SAAO,gBAAgB,SAAS,IAAI;AACpC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,OAAO,oBAAoB,oBAAoB;", "names": []}