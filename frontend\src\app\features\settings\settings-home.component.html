<div class="grid">
	<div class="col-12">
		<div class="card">
			<h2>Configurações do Sistema</h2>
			<p class="mb-5">
				Ajuste as configurações do sistema e preferências de usuário de acordo com suas necessidades.
			</p>

			@if (isLoading) {
				<div class="grid">
					<div class="col-12 flex justify-content-center">
						<i class="pi pi-spin pi-spinner" style="font-size: 2rem"></i>
					</div>
				</div>
			} @else {
				<div class="grid">
					<div class="col-12">
						<div class="flex justify-content-between align-items-center mb-3">
							<div>
								<button
									pButton
									icon="pi pi-user"
									label="Perfil"
									class="p-button-outlined mr-2"
									(click)="openProfileDialog()"
								></button>
							</div>
							<div>
								<button
									pButton
									icon="pi pi-undo"
									label="Restaurar <PERSON>dr<PERSON>"
									class="p-button-outlined p-button-secondary mr-2"
									(click)="resetSettings()"
								></button>
								<button
									pButton
									icon="pi pi-save"
									label="Salvar Alterações"
									(click)="saveSettings()"
									[loading]="isSaving"
								></button>
							</div>
						</div>

						<p-tabs [value]="activeTabIndex">
							<p-tablist>
								@for (section of sections; track section.id) {
									<p-tab [value]="sections.indexOf(section)">
										<span class="pi" [ngClass]="section.icon"></span> {{ section.title }}
									</p-tab>
								}
							</p-tablist>
							<p-tabpanels>
								@for (section of sections; track section.id) {
									<p-tabpanel [value]="sections.indexOf(section)">
										<p class="mb-4">{{ section.description }}</p>

										@if (section.id === 'notifications') {
											<p-panel header="Preferências de Notificação">
												<p class="mb-3">
													Escolha como deseja receber notificações para cada tipo de evento.
												</p>

												<div class="grid">
													<div class="col-12 md:col-8">
														@for (
															notification of notificationSettings;
															track notification.id
														) {
															<div class="grid align-items-center mb-3">
																<div class="col-12 md:col-5">
																	<label>{{ notification.description }}</label>
																</div>
																<div class="col-12 md:col-7">
																	<div class="flex gap-3">
																		<div class="flex align-items-center">
																			<p-checkbox
																				[binary]="true"
																				[(ngModel)]="notification.email"
																				(onChange)="
																					updateNotification(
																						notification,
																						'email'
																					)
																				"
																			></p-checkbox>
																			<label class="ml-2">Email</label>
																		</div>
																		<div class="flex align-items-center">
																			<p-checkbox
																				[binary]="true"
																				[(ngModel)]="notification.push"
																				(onChange)="
																					updateNotification(
																						notification,
																						'push'
																					)
																				"
																			></p-checkbox>
																			<label class="ml-2">Push</label>
																		</div>
																		<div class="flex align-items-center">
																			<p-checkbox
																				[binary]="true"
																				[(ngModel)]="notification.inApp"
																				(onChange)="
																					updateNotification(
																						notification,
																						'inApp'
																					)
																				"
																			></p-checkbox>
																			<label class="ml-2">No App</label>
																		</div>
																	</div>
																</div>
															</div>
														}
													</div>
												</div>

												<div class="flex justify-content-end mt-3">
													<button
														pButton
														icon="pi pi-bell"
														label="Testar Notificações"
														class="p-button-outlined"
														(click)="testNotifications()"
													></button>
												</div>
											</p-panel>
										}

										@if (section.id !== 'notifications') {
											@for (setting of getSettingsForSection(section.id); track setting.id) {
												<div class="setting-item">
													<div class="setting-item-label">{{ setting.label }}</div>
													@if (setting.description) {
														<div class="setting-item-description">
															{{ setting.description }}
														</div>
													}

													@switch (setting.type) {
														@case ('text') {
															<div class="p-inputgroup">
																<input
																	pInputText
																	type="text"
																	[(ngModel)]="setting.value"
																	[placeholder]="setting.placeholder || ''"
																	[required]="setting.required"
																/>
																@if (setting.info) {
																	<span class="p-inputgroup-addon">
																		<i
																			class="pi pi-info-circle"
																			pTooltip="{{ setting.info }}"
																		></i>
																	</span>
																}
															</div>
														}

														@case ('email') {
															<div class="p-inputgroup">
																<input
																	pInputText
																	type="email"
																	[(ngModel)]="setting.value"
																	[placeholder]="setting.placeholder || ''"
																	[required]="setting.required"
																/>
																@if (setting.info) {
																	<span class="p-inputgroup-addon">
																		<i
																			class="pi pi-info-circle"
																			pTooltip="{{ setting.info }}"
																		></i>
																	</span>
																}
															</div>
														}

														@case ('number') {
															<div class="p-inputgroup">
																<input
																	pInputText
																	type="number"
																	[(ngModel)]="setting.value"
																	[placeholder]="setting.placeholder || ''"
																	[required]="setting.required"
																/>
																@if (setting.info) {
																	<span class="p-inputgroup-addon">
																		<i
																			class="pi pi-info-circle"
																			pTooltip="{{ setting.info }}"
																		></i>
																	</span>
																}
															</div>
														}

														@case ('select') {
															<p-select
																[options]="setting.options"
																[(ngModel)]="setting.value"
																[placeholder]="setting.placeholder || 'Selecione'"
																styleClass="w-full"
															>
															</p-select>
														}

														@case ('color') {
															<p-colorPicker
																[(ngModel)]="setting.value"
																[inline]="false"
																[format]="'hex'"
															></p-colorPicker>
														}

														@case ('boolean') {
															<div class="field-checkbox">
																<p-inputSwitch
																	[(ngModel)]="setting.value"
																></p-inputSwitch>
															</div>
														}
													}
												</div>
											}
										}
									</p-tabpanel>
								}
							</p-tabpanels>
						</p-tabs>
					</div>
				</div>
			}
		</div>
	</div>
</div>

<!-- Diálogo de Perfil -->
<p-dialog header="Editar Perfil" [(visible)]="showProfileDialog" [modal]="true" [style]="{ width: '500px' }">
	@if (userProfile) {
		<div class="field">
			<div class="flex justify-content-center mb-4">
				<div class="relative">
					<img
						[src]="userProfile.avatar"
						alt="Foto de perfil"
						class="border-circle"
						style="width: 100px; height: 100px; object-fit: cover"
					/>
					<button
						pButton
						icon="pi pi-camera"
						class="p-button-rounded p-button-sm p-button-secondary"
						style="position: absolute; bottom: 0; right: 0"
					></button>
				</div>
			</div>

			<div class="field mb-3">
				<label for="name" class="block mb-2">Nome</label>
				<input id="name" type="text" pInputText [(ngModel)]="userProfile.name" class="w-full" />
			</div>

			<div class="field mb-3">
				<label for="email" class="block mb-2">Email</label>
				<input id="email" type="email" pInputText [(ngModel)]="userProfile.email" class="w-full" />
			</div>

			<div class="field mb-3">
				<label for="department" class="block mb-2">Departamento</label>
				<input id="department" type="text" pInputText [(ngModel)]="userProfile.department" class="w-full" />
			</div>

			<div class="field mb-3">
				<label for="position" class="block mb-2">Cargo</label>
				<input id="position" type="text" pInputText [(ngModel)]="userProfile.position" class="w-full" />
			</div>

			<div class="field mb-3">
				<label for="phone" class="block mb-2">Telefone</label>
				<input id="phone" type="text" pInputText [(ngModel)]="userProfile.phone" class="w-full" />
			</div>

			<div class="field mb-4">
				<label class="block mb-2">Última Conexão</label>
				<span class="p-tag status-tag-sm p-tag-info">{{ formatDate(userProfile.lastLogin) }}</span>
			</div>

			<p-divider></p-divider>

			<div class="flex justify-content-end">
				<button
					pButton
					label="Cancelar"
					icon="pi pi-times"
					class="p-button-text mr-2"
					(click)="showProfileDialog = false"
				></button>
				<button
					pButton
					label="Salvar Perfil"
					icon="pi pi-check"
					(click)="saveUserProfile()"
					[loading]="isSaving"
				></button>
			</div>
		</div>
	}
</p-dialog>
