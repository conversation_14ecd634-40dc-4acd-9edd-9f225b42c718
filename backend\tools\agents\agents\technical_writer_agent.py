"""
Technical Writer Agent - Agente especializado em consolidação de insights

Agente do time Agno focado em redação técnica e consolidação:
- Consolidação de insights de múltiplos agentes especializados
- Priorização de recomendações por impacto e esforço
- Identificação de temas e padrões comuns entre análises
- Geração de relatórios finais estratégicos e coesos
"""

import logging
from typing import Dict, Any, Optional, List
from agno.agent import Agent
from ...shared.model_config import model_config
from agno.tools.reasoning import ReasoningTools

from ..tools import TechnicalWriterTools

logger = logging.getLogger(__name__)


class TechnicalWriterAgent:
    """
    Agente especializado em consolidação de insights usando Agno

    Analisa outputs de múltiplos agentes especializados e gera
    relatórios finais consolidados com recomendações priorizadas.
    """

    def __init__(self, model_name: Optional[str] = None):
        """
        Inicializa o Technical Writer Agent usando modelos do settings.py

        Args:
            model_name: Modelo específico (opcional, usa automático se None)
        """
        self.writer_tools = TechnicalWriterTools()

        # Obter configuração de modelo baseada no settings.py
        agno_config = model_config.create_agno_config("writing")
        
        if model_name:
            # Validar se modelo está disponível no settings
            available_models = [
                model_config.get_openai_model("writing"),
                model_config.get_cohere_model("writing"), 
                model_config.get_mistral_model("writing")
            ]
            if model_name in available_models:
                self.model_name = model_name
            else:
                logger.warning(f"Modelo {model_name} não disponível no settings.py, usando automático")
                self.model_name = agno_config["model_id"]
        else:
            self.model_name = agno_config["model_id"]

        # Criar modelo baseado no provider
        if agno_config["provider"] == "openai":
            from agno.models.openai import OpenAI
            model_instance = OpenAI(id=self.model_name, api_key=agno_config["api_key"])
        elif agno_config["provider"] == "cohere":
            from agno.models.cohere import Cohere
            model_instance = Cohere(id=self.model_name, api_key=agno_config["api_key"])
        elif agno_config["provider"] == "mistral":
            from agno.models.mistral import Mistral
            model_instance = Mistral(id=self.model_name, api_key=agno_config["api_key"])
        else:
            from agno.models.openai import OpenAI
            model_instance = OpenAI(id="gpt-4o-mini", api_key=agno_config["api_key"])
            logger.warning(f"Provider {agno_config['provider']} não suportado, usando OpenAI fallback")

        # Criar agente Agno especializado
        self.agent = Agent(
            name="Technical Writer & Insights Consolidator",
            role="Expert in consolidating multi-agent analyses, prioritizing recommendations, and creating strategic reports",
            model=model_instance,
            tools=[ReasoningTools()],
            instructions=[
                "You are a technical writer specializing in consolidating insights from multiple domain experts",
                "Synthesize complex multi-agent analyses into coherent, actionable strategic reports",
                "Prioritize recommendations based on impact, effort, and strategic alignment",
                "Identify patterns and themes across different expert perspectives",
                "Create executive-level reports that balance technical depth with business clarity",
                "Focus on consensus-building and conflict resolution between expert opinions",
                "Always provide structured, prioritized, and timeline-based action plans",
                "Use data-driven insights to support all recommendations and priorities"
            ],
            description="Specialist in consolidating multi-agent insights, strategic report writing, and cross-functional recommendation prioritization",
            markdown=True,
            show_tool_calls=True
        )

        logger.info(f"Technical Writer Agent inicializado com {agno_config['provider']}:{self.model_name}")

    def consolidate_insights(self,
                             agent_analyses: Dict[str, Dict[str, Any]],
                             company_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Consolida insights de múltiplos agentes especializados

        Args:
            agent_analyses: Dicionário com análises de cada agente especializado
            company_context: Contexto da empresa (opcional)

        Returns:
            Insights consolidados com análise de consenso
        """
        try:
            logger.info(
                f"Consolidando insights de {len(agent_analyses)} agentes especializados")

            # Processar dados com as ferramentas de consolidação
            consolidated_insights = self.writer_tools.consolidate_agent_insights(
                agent_analyses, company_context)

            # Preparar contexto para análise do agente
            company_name = company_context.get(
                "name", "a empresa") if company_context else "a empresa"
            num_agents = len(agent_analyses)
            num_insights = len(consolidated_insights)

            # Calcular estatísticas de consenso
            high_consensus_insights = [
                i for i in consolidated_insights if i.agent_consensus > 70]
            conflicting_insights = [
                i for i in consolidated_insights if len(i.conflicting_views) > 0]

            # Criar prompt estruturado para análise
            consolidation_prompt = f"""
Analise a consolidação de insights de {num_agents} agentes especializados para {company_name}:

**ESTATÍSTICAS DE CONSOLIDAÇÃO:**
- Total de insights únicos identificados: {num_insights}
- Insights com alto consenso (>70%): {len(high_consensus_insights)}
- Insights com perspectivas conflitantes: {len(conflicting_insights)}
- Agentes analisados: {', '.join(agent_analyses.keys())}

**TOP 10 INSIGHTS CONSOLIDADOS:**
{self._format_consolidated_insights(consolidated_insights[:10])}

**ANÁLISE DE CONSENSO:**
{self._format_consensus_analysis(consolidated_insights)}

**INSIGHTS CONFLITANTES (se houver):**
{self._format_conflicting_insights(conflicting_insights)}

**CONTEXTO DA EMPRESA:**
{self._format_company_context(company_context)}

Forneça uma análise estratégica da consolidação incluindo:

1. **Qualidade da Consolidação** - Avaliação da convergência entre especialistas
2. **Insights Mais Críticos** - Top 5 insights por relevância e consenso
3. **Resolução de Conflitos** - Como conciliar perspectivas divergentes
4. **Lacunas Identificadas** - Áreas que precisam de mais análise
5. **Confiabilidade das Recomendações** - Nível de certeza para próximos passos
6. **Síntese Estratégica** - Visão unificada baseada em todos os especialistas

Use dados quantitativos de consenso e priorize insights com maior evidência.
"""

            # Executar análise com o agente Agno
            consolidation_response = self.agent.run(consolidation_prompt)

            # Estruturar resultado
            result = {
                "agent_type": "TechnicalWriterAgent",
                "analysis_type": "insights_consolidation",
                "company_data": {
                    "name": company_name,
                    "context": company_context
                },
                "consolidation_stats": {
                    "total_insights": num_insights,
                    "high_consensus_insights": len(high_consensus_insights),
                    "conflicting_insights": len(conflicting_insights),
                    "agents_analyzed": list(agent_analyses.keys()),
                    "average_consensus": sum(i.agent_consensus for i in consolidated_insights) / max(num_insights, 1)
                },
                "consolidated_insights": [insight.dict() for insight in consolidated_insights],
                "agent_analysis": consolidation_response.content,
                "analysis_timestamp": "2024-01-01T00:00:00Z"  # Placeholder
            }

            logger.info(
                f"Consolidação de insights concluída - {num_insights} insights únicos")
            return result

        except Exception as e:
            logger.error(f"Erro na consolidação de insights: {str(e)}")
            raise

    def prioritize_recommendations(self,
                                   consolidated_insights: List,
                                   business_objectives: Optional[List[str]] = None,
                                   resource_constraints: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Prioriza recomendações baseadas em insights consolidados

        Args:
            consolidated_insights: Lista de insights consolidados (dicts ou objetos)
            business_objectives: Objetivos estratégicos da empresa (opcional)
            resource_constraints: Limitações de recursos (opcional)

        Returns:
            Recomendações priorizadas com matriz impacto vs esforço
        """
        try:
            logger.info(
                f"Priorizando recomendações baseadas em {len(consolidated_insights)} insights")

            # Converter para objetos se necessário
            if consolidated_insights and isinstance(consolidated_insights[0], dict):
                from ..tools.technical_writer_tools import ConsolidatedInsight
                insight_objects = [ConsolidatedInsight(
                    **insight) for insight in consolidated_insights]
            else:
                insight_objects = consolidated_insights

            # Processar dados com as ferramentas de priorização
            prioritized_recommendations = self.writer_tools.prioritize_recommendations(
                insight_objects, business_objectives)

            # Preparar dados para análise do agente
            num_recommendations = len(prioritized_recommendations)
            critical_recs = [
                r for r in prioritized_recommendations if r.urgency == "Crítico"]
            high_impact_recs = [
                r for r in prioritized_recommendations if r.impact_score > 80]

            # Criar prompt estruturado para análise
            prioritization_prompt = f"""
Analise a priorização de {num_recommendations} recomendações estratégicas:

**ESTATÍSTICAS DE PRIORIZAÇÃO:**
- Total de recomendações geradas: {num_recommendations}
- Recomendações críticas (ação imediata): {len(critical_recs)}
- Recomendações alto impacto (>80 pontos): {len(high_impact_recs)}

**TOP 10 RECOMENDAÇÕES PRIORIZADAS:**
{self._format_prioritized_recommendations(prioritized_recommendations[:10])}

**MATRIZ IMPACTO VS ESFORÇO:**
{self._format_impact_effort_matrix(prioritized_recommendations)}

**OBJETIVOS DE NEGÓCIO:**
{', '.join(business_objectives) if business_objectives else 'Não especificados'}

**RESTRIÇÕES DE RECURSOS:**
{self._format_resource_constraints(resource_constraints)}

**ANÁLISE DE DEPENDÊNCIAS:**
{self._format_dependencies_analysis(prioritized_recommendations)}

Forneça análise estratégica da priorização incluindo:

1. **Validação da Matriz** - Verificação da lógica impacto vs esforço
2. **Sequenciamento Ótimo** - Ordem recomendada considerando dependências
3. **Quick Wins Identificados** - Recomendações de alto impacto e baixo esforço
4. **Investimentos Estratégicos** - Iniciativas de longo prazo com alto ROI
5. **Análise de Risco** - Fatores que podem impactar implementação
6. **Roadmap Executivo** - Timeline de 3, 6 e 12 meses

Priorize clareza executiva e acionabilidade das recomendações.
"""

            # Executar análise com o agente
            prioritization_response = self.agent.run(prioritization_prompt)

            result = {
                "agent_type": "TechnicalWriterAgent",
                "analysis_type": "recommendations_prioritization",
                "prioritization_stats": {
                    "total_recommendations": num_recommendations,
                    "critical_recommendations": len(critical_recs),
                    "high_impact_recommendations": len(high_impact_recs),
                    "average_priority_score": sum(r.priority_score for r in prioritized_recommendations) / max(num_recommendations, 1)
                },
                "prioritized_recommendations": [rec.dict() for rec in prioritized_recommendations],
                "business_objectives": business_objectives or [],
                "resource_constraints": resource_constraints or {},
                "agent_analysis": prioritization_response.content
            }

            logger.info(
                f"Priorização concluída - {len(critical_recs)} recomendações críticas identificadas")
            return result

        except Exception as e:
            logger.error(f"Erro na priorização de recomendações: {str(e)}")
            raise

    def identify_common_themes(self,
                               agent_analyses: Dict[str, Dict[str, Any]],
                               min_frequency: float = 2) -> Dict[str, Any]:
        """
        Identifica temas comuns entre análises de diferentes agentes

        Args:
            agent_analyses: Análises dos agentes especializados
            min_frequency: Frequência mínima para considerar um tema comum

        Returns:
            Temas comuns identificados com análise de convergência
        """
        try:
            logger.info(
                f"Identificando temas comuns entre {len(agent_analyses)} agentes especializados")

            # Processar dados com as ferramentas de identificação de temas
            common_themes = self.writer_tools.identify_common_themes(
                agent_analyses, min_frequency)

            # Preparar dados para análise do agente
            num_themes = len(common_themes)
            critical_themes = [
                t for t in common_themes if t.strategic_importance == "Crítico"]
            cross_agent_themes = [
                t for t in common_themes if len(t.mentioned_by_agents) >= 3]

            themes_prompt = f"""
Analise a identificação de {num_themes} temas comuns entre especialistas:

**ESTATÍSTICAS DE CONVERGÊNCIA:**
- Total de temas comuns identificados: {num_themes}
- Temas de importância crítica: {len(critical_themes)}
- Temas mencionados por 3+ agentes: {len(cross_agent_themes)}
- Frequência mínima aplicada: {min_frequency}

**TEMAS IDENTIFICADOS POR RELEVÂNCIA:**
{self._format_common_themes(common_themes)}

**ANÁLISE DE CONVERGÊNCIA POR AGENTE:**
{self._format_agent_theme_convergence(agent_analyses, common_themes)}

**MAPA DE FREQUÊNCIA E CONSISTÊNCIA:**
{self._format_theme_frequency_map(common_themes)}

Forneça análise estratégica dos temas comuns incluindo:

1. **Validação da Convergência** - Qualidade do consenso entre especialistas
2. **Temas Estratégicos Críticos** - Focos que demandam atenção imediata
3. **Padrões Emergentes** - Tendências identificadas nas análises
4. **Lacunas de Cobertura** - Áreas não abordadas ou com baixo consenso
5. **Interconexões Temáticas** - Como os temas se relacionam entre si
6. **Priorização de Iniciativas** - Sequência recomendada para abordar temas

Use dados de frequência e consistência para fundamentar recomendações.
"""

            # Executar análise com o agente
            themes_response = self.agent.run(themes_prompt)

            result = {
                "agent_type": "TechnicalWriterAgent",
                "analysis_type": "common_themes_identification",
                "themes_stats": {
                    "total_themes": num_themes,
                    "critical_themes": len(critical_themes),
                    "cross_agent_themes": len(cross_agent_themes),
                    "min_frequency_applied": min_frequency,
                    "average_frequency": sum(t.frequency_score for t in common_themes) / max(num_themes, 1),
                    "average_consistency": sum(t.consistency_score for t in common_themes) / max(num_themes, 1)
                },
                "common_themes": [theme.dict() for theme in common_themes],
                "agent_analyses_summary": {
                    "agents_analyzed": list(agent_analyses.keys()),
                    "total_agents": len(agent_analyses)
                },
                "agent_analysis": themes_response.content
            }

            logger.info(
                f"Identificação de temas concluída - {num_themes} temas comuns encontrados")
            return result

        except Exception as e:
            logger.error(f"Erro na identificação de temas: {str(e)}")
            raise

    def generate_final_report(self,
                              consolidated_insights: List,
                              prioritized_recommendations: List,
                              common_themes: List,
                              company_name: str = "Empresa",
                              executive_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Gera relatório final consolidado de todos os agentes

        Args:
            consolidated_insights: Insights consolidados (dicts ou objetos)
            prioritized_recommendations: Recomendações priorizadas (dicts ou objetos)
            common_themes: Temas comuns (dicts ou objetos)
            company_name: Nome da empresa
            executive_context: Contexto executivo adicional (opcional)

        Returns:
            Relatório final estruturado e executivo
        """
        try:
            logger.info(
                f"Gerando relatório final consolidado para {company_name}")

            # Converter para objetos se necessário
            if consolidated_insights and isinstance(consolidated_insights[0], dict):
                from ..tools.technical_writer_tools import ConsolidatedInsight, PrioritizedRecommendation, CommonTheme
                insight_objects = [ConsolidatedInsight(
                    **insight) for insight in consolidated_insights]
                rec_objects = [PrioritizedRecommendation(
                    **rec) for rec in prioritized_recommendations]
                theme_objects = [CommonTheme(**theme)
                                 for theme in common_themes]
            else:
                insight_objects = consolidated_insights
                rec_objects = prioritized_recommendations
                theme_objects = common_themes

            # Processar dados com as ferramentas de relatório
            final_report = self.writer_tools.generate_final_report_structure(
                insight_objects, rec_objects, theme_objects, company_name)

            # Preparar contexto para análise final do agente
            num_insights = len(insight_objects)
            num_recommendations = len(rec_objects)
            num_themes = len(theme_objects)

            final_report_prompt = f"""
Analise e refine o relatório final consolidado para {company_name}:

**ESCOPO DO RELATÓRIO:**
- Insights analisados: {num_insights}
- Recomendações priorizadas: {num_recommendations}
- Temas estratégicos: {num_themes}
- Agentes especialistas: {', '.join(final_report.agents_analyzed)}

**RESUMO EXECUTIVO ATUAL:**
{final_report.executive_summary}

**PRINCIPAIS DESCOBERTAS:**
{chr(10).join(f"- {finding}" for finding in final_report.key_findings)}

**TOP 5 RECOMENDAÇÕES:**
{chr(10).join(f"{i+1}. {rec}" for i, rec in enumerate(final_report.top_recommendations))}

**PLANO DE AÇÃO ESTRUTURADO:**
- Ações Imediatas (0-30 dias): {len(final_report.immediate_actions)} identificadas
- Objetivos Curto Prazo (1-3 meses): {len(final_report.short_term_goals)} definidos
- Visão Longo Prazo (6+ meses): {len(final_report.long_term_vision)} estabelecidos

**CONTEXTO EXECUTIVO:**
{self._format_executive_context(executive_context)}

Forneça análise final e refinamentos incluindo:

1. **Validação Executiva** - Adequação do relatório para tomada de decisão
2. **Clareza Estratégica** - Alinhamento entre descobertas e recomendações
3. **Acionabilidade** - Viabilidade de implementação das recomendações
4. **ROI Projetado** - Expectativa de retorno sobre investimentos sugeridos
5. **Riscos e Mitigações** - Principais obstáculos e como contorná-los
6. **Próximos Passos Críticos** - Primeiras ações para maximizar impacto

Foque em clareza executiva, priorização estratégica e resultados mensuráveis.
"""

            # Executar análise final com o agente
            final_analysis_response = self.agent.run(final_report_prompt)

            result = {
                "agent_type": "TechnicalWriterAgent",
                "analysis_type": "final_report_generation",
                "report_metadata": {
                    "company_name": company_name,
                    "report_id": final_report.report_id,
                    "analysis_date": final_report.analysis_date,
                    "total_insights": final_report.total_insights,
                    "total_recommendations": final_report.total_recommendations,
                    "agents_analyzed": final_report.agents_analyzed,
                    "sections_generated": len(final_report.sections)
                },
                "final_report": final_report.dict(),
                "executive_context": executive_context or {},
                "agent_analysis": final_analysis_response.content,
                "report_summary": {
                    "immediate_actions": len(final_report.immediate_actions),
                    "short_term_goals": len(final_report.short_term_goals),
                    "long_term_vision": len(final_report.long_term_vision),
                    "critical_recommendations": len([r for r in rec_objects if r.urgency == "Crítico"]),
                    "high_consensus_insights": len([i for i in insight_objects if i.agent_consensus > 70])
                }
            }

            logger.info(
                f"Relatório final gerado - {len(final_report.sections)} seções, {final_report.total_recommendations} recomendações")
            return result

        except Exception as e:
            logger.error(f"Erro na geração do relatório final: {str(e)}")
            raise

    # Métodos de apoio para formatação

    def _format_consolidated_insights(self, insights: List) -> str:
        """Formata insights consolidados para o prompt"""
        formatted = []
        for insight in insights:
            agents = ', '.join(insight.source_agents)
            formatted.append(f"""
**{insight.title}** (Consenso: {insight.agent_consensus:.0f}%)
- Categoria: {insight.category}
- Fontes: {agents}
- Impacto: {insight.impact_score:.0f}/100
- {insight.description[:150]}...
""")
        return '\n'.join(formatted)

    def _format_consensus_analysis(self, insights: List) -> str:
        """Formata análise de consenso para o prompt"""
        if not insights:
            return "Nenhum insight disponível para análise"

        high_consensus = len([i for i in insights if i.agent_consensus > 70])
        medium_consensus = len(
            [i for i in insights if 40 <= i.agent_consensus <= 70])
        low_consensus = len([i for i in insights if i.agent_consensus < 40])

        return f"""
- Alto consenso (>70%): {high_consensus} insights
- Consenso moderado (40-70%): {medium_consensus} insights  
- Baixo consenso (<40%): {low_consensus} insights
- Consenso médio geral: {sum(i.agent_consensus for i in insights) / len(insights):.1f}%
"""

    def _format_conflicting_insights(self, conflicting_insights: List) -> str:
        """Formata insights com perspectivas conflitantes"""
        if not conflicting_insights:
            return "Nenhuma perspectiva conflitante identificada"

        formatted = []
        for insight in conflicting_insights:
            formatted.append(f"""
**{insight.title}**
- Conflitos: {', '.join(insight.conflicting_views)}
- Consenso atual: {insight.agent_consensus:.0f}%
""")
        return '\n'.join(formatted)

    def _format_company_context(self, context: Optional[Dict[str, Any]]) -> str:
        """Formata contexto da empresa para o prompt"""
        if not context:
            return "Contexto da empresa não fornecido"

        items = []
        for key, value in context.items():
            if key != "name":
                items.append(f"{key}: {value}")

        return '\n'.join(items) if items else "Dados limitados de contexto"

    def _format_prioritized_recommendations(self, recommendations: List) -> str:
        """Formata recomendações priorizadas para o prompt"""
        formatted = []
        for i, rec in enumerate(recommendations, 1):
            urgency_emoji = "🔴" if rec.urgency == "Crítico" else "🟡" if rec.urgency == "Importante" else "🟢"
            formatted.append(f"""
{i}. {urgency_emoji} **{rec.title}**
   - Prioridade: {rec.priority_score:.0f}/100
   - Impacto: {rec.impact_score:.0f}/100 | Esforço: {rec.effort_score:.0f}/100
   - Urgência: {rec.urgency} | Complexidade: {rec.complexity}
   - Timeline: {rec.estimated_timeline}
   - {rec.description[:120]}...
""")
        return '\n'.join(formatted)

    def _format_impact_effort_matrix(self, recommendations: List) -> str:
        """Formata matriz impacto vs esforço"""
        if not recommendations:
            return "Nenhuma recomendação disponível"

        # Categorizar recomendações na matriz
        quick_wins = [r for r in recommendations if r.impact_score >
                      70 and r.effort_score < 50]
        major_projects = [
            r for r in recommendations if r.impact_score > 70 and r.effort_score >= 50]
        fill_ins = [r for r in recommendations if r.impact_score <=
                    70 and r.effort_score < 50]
        questionable = [
            r for r in recommendations if r.impact_score <= 70 and r.effort_score >= 50]

        return f"""
**MATRIZ IMPACTO vs ESFORÇO:**
- Quick Wins (Alto Impacto, Baixo Esforço): {len(quick_wins)} recomendações
- Projetos Principais (Alto Impacto, Alto Esforço): {len(major_projects)} recomendações
- Melhorias Incrementais (Baixo Impacto, Baixo Esforço): {len(fill_ins)} recomendações
- Questionáveis (Baixo Impacto, Alto Esforço): {len(questionable)} recomendações

**Quick Wins Identificados:**
{chr(10).join(f"- {qw.title}" for qw in quick_wins[:3])}
"""

    def _format_resource_constraints(self, constraints: Optional[Dict[str, Any]]) -> str:
        """Formata restrições de recursos"""
        if not constraints:
            return "Nenhuma restrição de recursos especificada"

        formatted = []
        for constraint_type, details in constraints.items():
            formatted.append(f"- {constraint_type}: {details}")

        return '\n'.join(formatted)

    def _format_dependencies_analysis(self, recommendations: List) -> str:
        """Formata análise de dependências"""
        deps_count = sum(1 for r in recommendations if r.dependencies)
        blocking_count = sum(1 for r in recommendations if r.blocking_factors)

        return f"""
- Recomendações com dependências: {deps_count}
- Recomendações com fatores bloqueantes: {blocking_count}
- Sequenciamento necessário para implementação ótima
"""

    def _format_common_themes(self, themes: List) -> str:
        """Formata temas comuns para o prompt"""
        formatted = []
        for theme in themes:
            agents_list = ', '.join(theme.mentioned_by_agents)
            formatted.append(f"""
**{theme.theme_name}** - {theme.strategic_importance} Importância
- Frequência: {theme.frequency_score:.0f}% | Consistência: {theme.consistency_score:.0f}%
- Mencionado por: {agents_list}
- Área: {theme.business_area}
- {theme.description[:100]}...
""")
        return '\n'.join(formatted)

    def _format_agent_theme_convergence(self, agent_analyses: Dict[str, Dict[str, Any]], themes: List) -> str:
        """Formata convergência de temas por agente"""
        convergence = {}
        for theme in themes:
            for agent in theme.mentioned_by_agents:
                if agent not in convergence:
                    convergence[agent] = 0
                convergence[agent] += 1

        formatted = []
        for agent, count in convergence.items():
            formatted.append(f"- {agent}: {count} temas contribuídos")

        return '\n'.join(formatted)

    def _format_theme_frequency_map(self, themes: List) -> str:
        """Formata mapa de frequência de temas"""
        if not themes:
            return "Nenhum tema disponível"

        sorted_themes = sorted(
            themes, key=lambda x: x.frequency_score, reverse=True)

        formatted = []
        for theme in sorted_themes[:5]:
            formatted.append(
                f"- {theme.theme_name}: {theme.frequency_score:.0f}% freq, {theme.consistency_score:.0f}% consist")

        return '\n'.join(formatted)

    def _format_executive_context(self, context: Optional[Dict[str, Any]]) -> str:
        """Formata contexto executivo"""
        if not context:
            return "Contexto executivo não fornecido"

        formatted = []
        for key, value in context.items():
            formatted.append(f"{key}: {value}")

        return '\n'.join(formatted) if formatted else "Dados limitados de contexto executivo"
