{"version": 3, "sources": ["../../../../../../node_modules/mermaid/dist/chunks/mermaid.core/xychartDiagram-VJFVF3MP.mjs"], "sourcesContent": ["import { computeDimensionOfText } from \"./chunk-C3MQ5ANM.mjs\";\nimport { cleanAndMerge } from \"./chunk-O4NI6UNU.mjs\";\nimport { selectSvgElement } from \"./chunk-7B677QYD.mjs\";\nimport { __name, clear, configureSvgSize, defaultConfig_default, getAccDescription, getAccTitle, getConfig, getDiagramTitle, getThemeVariables, log, sanitizeText, setAccDescription, setAccTitle, setDiagramTitle } from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/xychart/parser/xychart.jison\nvar parser = function () {\n  var o = /* @__PURE__ */__name(function (k, v, o2, l) {\n      for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v);\n      return o2;\n    }, \"o\"),\n    $V0 = [1, 10, 12, 14, 16, 18, 19, 21, 23],\n    $V1 = [2, 6],\n    $V2 = [1, 3],\n    $V3 = [1, 5],\n    $V4 = [1, 6],\n    $V5 = [1, 7],\n    $V6 = [1, 5, 10, 12, 14, 16, 18, 19, 21, 23, 34, 35, 36],\n    $V7 = [1, 25],\n    $V8 = [1, 26],\n    $V9 = [1, 28],\n    $Va = [1, 29],\n    $Vb = [1, 30],\n    $Vc = [1, 31],\n    $Vd = [1, 32],\n    $Ve = [1, 33],\n    $Vf = [1, 34],\n    $Vg = [1, 35],\n    $Vh = [1, 36],\n    $Vi = [1, 37],\n    $Vj = [1, 43],\n    $Vk = [1, 42],\n    $Vl = [1, 47],\n    $Vm = [1, 50],\n    $Vn = [1, 10, 12, 14, 16, 18, 19, 21, 23, 34, 35, 36],\n    $Vo = [1, 10, 12, 14, 16, 18, 19, 21, 23, 24, 26, 27, 28, 34, 35, 36],\n    $Vp = [1, 10, 12, 14, 16, 18, 19, 21, 23, 24, 26, 27, 28, 34, 35, 36, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50],\n    $Vq = [1, 64];\n  var parser2 = {\n    trace: /* @__PURE__ */__name(function trace() {}, \"trace\"),\n    yy: {},\n    symbols_: {\n      \"error\": 2,\n      \"start\": 3,\n      \"eol\": 4,\n      \"XYCHART\": 5,\n      \"chartConfig\": 6,\n      \"document\": 7,\n      \"CHART_ORIENTATION\": 8,\n      \"statement\": 9,\n      \"title\": 10,\n      \"text\": 11,\n      \"X_AXIS\": 12,\n      \"parseXAxis\": 13,\n      \"Y_AXIS\": 14,\n      \"parseYAxis\": 15,\n      \"LINE\": 16,\n      \"plotData\": 17,\n      \"BAR\": 18,\n      \"acc_title\": 19,\n      \"acc_title_value\": 20,\n      \"acc_descr\": 21,\n      \"acc_descr_value\": 22,\n      \"acc_descr_multiline_value\": 23,\n      \"SQUARE_BRACES_START\": 24,\n      \"commaSeparatedNumbers\": 25,\n      \"SQUARE_BRACES_END\": 26,\n      \"NUMBER_WITH_DECIMAL\": 27,\n      \"COMMA\": 28,\n      \"xAxisData\": 29,\n      \"bandData\": 30,\n      \"ARROW_DELIMITER\": 31,\n      \"commaSeparatedTexts\": 32,\n      \"yAxisData\": 33,\n      \"NEWLINE\": 34,\n      \"SEMI\": 35,\n      \"EOF\": 36,\n      \"alphaNum\": 37,\n      \"STR\": 38,\n      \"MD_STR\": 39,\n      \"alphaNumToken\": 40,\n      \"AMP\": 41,\n      \"NUM\": 42,\n      \"ALPHA\": 43,\n      \"PLUS\": 44,\n      \"EQUALS\": 45,\n      \"MULT\": 46,\n      \"DOT\": 47,\n      \"BRKT\": 48,\n      \"MINUS\": 49,\n      \"UNDERSCORE\": 50,\n      \"$accept\": 0,\n      \"$end\": 1\n    },\n    terminals_: {\n      2: \"error\",\n      5: \"XYCHART\",\n      8: \"CHART_ORIENTATION\",\n      10: \"title\",\n      12: \"X_AXIS\",\n      14: \"Y_AXIS\",\n      16: \"LINE\",\n      18: \"BAR\",\n      19: \"acc_title\",\n      20: \"acc_title_value\",\n      21: \"acc_descr\",\n      22: \"acc_descr_value\",\n      23: \"acc_descr_multiline_value\",\n      24: \"SQUARE_BRACES_START\",\n      26: \"SQUARE_BRACES_END\",\n      27: \"NUMBER_WITH_DECIMAL\",\n      28: \"COMMA\",\n      31: \"ARROW_DELIMITER\",\n      34: \"NEWLINE\",\n      35: \"SEMI\",\n      36: \"EOF\",\n      38: \"STR\",\n      39: \"MD_STR\",\n      41: \"AMP\",\n      42: \"NUM\",\n      43: \"ALPHA\",\n      44: \"PLUS\",\n      45: \"EQUALS\",\n      46: \"MULT\",\n      47: \"DOT\",\n      48: \"BRKT\",\n      49: \"MINUS\",\n      50: \"UNDERSCORE\"\n    },\n    productions_: [0, [3, 2], [3, 3], [3, 2], [3, 1], [6, 1], [7, 0], [7, 2], [9, 2], [9, 2], [9, 2], [9, 2], [9, 2], [9, 3], [9, 2], [9, 3], [9, 2], [9, 2], [9, 1], [17, 3], [25, 3], [25, 1], [13, 1], [13, 2], [13, 1], [29, 1], [29, 3], [30, 3], [32, 3], [32, 1], [15, 1], [15, 2], [15, 1], [33, 3], [4, 1], [4, 1], [4, 1], [11, 1], [11, 1], [11, 1], [37, 1], [37, 2], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1]],\n    performAction: /* @__PURE__ */__name(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 5:\n          yy.setOrientation($$[$0]);\n          break;\n        case 9:\n          yy.setDiagramTitle($$[$0].text.trim());\n          break;\n        case 12:\n          yy.setLineData({\n            text: \"\",\n            type: \"text\"\n          }, $$[$0]);\n          break;\n        case 13:\n          yy.setLineData($$[$0 - 1], $$[$0]);\n          break;\n        case 14:\n          yy.setBarData({\n            text: \"\",\n            type: \"text\"\n          }, $$[$0]);\n          break;\n        case 15:\n          yy.setBarData($$[$0 - 1], $$[$0]);\n          break;\n        case 16:\n          this.$ = $$[$0].trim();\n          yy.setAccTitle(this.$);\n          break;\n        case 17:\n        case 18:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 19:\n          this.$ = $$[$0 - 1];\n          break;\n        case 20:\n          this.$ = [Number($$[$0 - 2]), ...$$[$0]];\n          break;\n        case 21:\n          this.$ = [Number($$[$0])];\n          break;\n        case 22:\n          yy.setXAxisTitle($$[$0]);\n          break;\n        case 23:\n          yy.setXAxisTitle($$[$0 - 1]);\n          break;\n        case 24:\n          yy.setXAxisTitle({\n            type: \"text\",\n            text: \"\"\n          });\n          break;\n        case 25:\n          yy.setXAxisBand($$[$0]);\n          break;\n        case 26:\n          yy.setXAxisRangeData(Number($$[$0 - 2]), Number($$[$0]));\n          break;\n        case 27:\n          this.$ = $$[$0 - 1];\n          break;\n        case 28:\n          this.$ = [$$[$0 - 2], ...$$[$0]];\n          break;\n        case 29:\n          this.$ = [$$[$0]];\n          break;\n        case 30:\n          yy.setYAxisTitle($$[$0]);\n          break;\n        case 31:\n          yy.setYAxisTitle($$[$0 - 1]);\n          break;\n        case 32:\n          yy.setYAxisTitle({\n            type: \"text\",\n            text: \"\"\n          });\n          break;\n        case 33:\n          yy.setYAxisRangeData(Number($$[$0 - 2]), Number($$[$0]));\n          break;\n        case 37:\n          this.$ = {\n            text: $$[$0],\n            type: \"text\"\n          };\n          break;\n        case 38:\n          this.$ = {\n            text: $$[$0],\n            type: \"text\"\n          };\n          break;\n        case 39:\n          this.$ = {\n            text: $$[$0],\n            type: \"markdown\"\n          };\n          break;\n        case 40:\n          this.$ = $$[$0];\n          break;\n        case 41:\n          this.$ = $$[$0 - 1] + \"\" + $$[$0];\n          break;\n      }\n    }, \"anonymous\"),\n    table: [o($V0, $V1, {\n      3: 1,\n      4: 2,\n      7: 4,\n      5: $V2,\n      34: $V3,\n      35: $V4,\n      36: $V5\n    }), {\n      1: [3]\n    }, o($V0, $V1, {\n      4: 2,\n      7: 4,\n      3: 8,\n      5: $V2,\n      34: $V3,\n      35: $V4,\n      36: $V5\n    }), o($V0, $V1, {\n      4: 2,\n      7: 4,\n      6: 9,\n      3: 10,\n      5: $V2,\n      8: [1, 11],\n      34: $V3,\n      35: $V4,\n      36: $V5\n    }), {\n      1: [2, 4],\n      9: 12,\n      10: [1, 13],\n      12: [1, 14],\n      14: [1, 15],\n      16: [1, 16],\n      18: [1, 17],\n      19: [1, 18],\n      21: [1, 19],\n      23: [1, 20]\n    }, o($V6, [2, 34]), o($V6, [2, 35]), o($V6, [2, 36]), {\n      1: [2, 1]\n    }, o($V0, $V1, {\n      4: 2,\n      7: 4,\n      3: 21,\n      5: $V2,\n      34: $V3,\n      35: $V4,\n      36: $V5\n    }), {\n      1: [2, 3]\n    }, o($V6, [2, 5]), o($V0, [2, 7], {\n      4: 22,\n      34: $V3,\n      35: $V4,\n      36: $V5\n    }), {\n      11: 23,\n      37: 24,\n      38: $V7,\n      39: $V8,\n      40: 27,\n      41: $V9,\n      42: $Va,\n      43: $Vb,\n      44: $Vc,\n      45: $Vd,\n      46: $Ve,\n      47: $Vf,\n      48: $Vg,\n      49: $Vh,\n      50: $Vi\n    }, {\n      11: 39,\n      13: 38,\n      24: $Vj,\n      27: $Vk,\n      29: 40,\n      30: 41,\n      37: 24,\n      38: $V7,\n      39: $V8,\n      40: 27,\n      41: $V9,\n      42: $Va,\n      43: $Vb,\n      44: $Vc,\n      45: $Vd,\n      46: $Ve,\n      47: $Vf,\n      48: $Vg,\n      49: $Vh,\n      50: $Vi\n    }, {\n      11: 45,\n      15: 44,\n      27: $Vl,\n      33: 46,\n      37: 24,\n      38: $V7,\n      39: $V8,\n      40: 27,\n      41: $V9,\n      42: $Va,\n      43: $Vb,\n      44: $Vc,\n      45: $Vd,\n      46: $Ve,\n      47: $Vf,\n      48: $Vg,\n      49: $Vh,\n      50: $Vi\n    }, {\n      11: 49,\n      17: 48,\n      24: $Vm,\n      37: 24,\n      38: $V7,\n      39: $V8,\n      40: 27,\n      41: $V9,\n      42: $Va,\n      43: $Vb,\n      44: $Vc,\n      45: $Vd,\n      46: $Ve,\n      47: $Vf,\n      48: $Vg,\n      49: $Vh,\n      50: $Vi\n    }, {\n      11: 52,\n      17: 51,\n      24: $Vm,\n      37: 24,\n      38: $V7,\n      39: $V8,\n      40: 27,\n      41: $V9,\n      42: $Va,\n      43: $Vb,\n      44: $Vc,\n      45: $Vd,\n      46: $Ve,\n      47: $Vf,\n      48: $Vg,\n      49: $Vh,\n      50: $Vi\n    }, {\n      20: [1, 53]\n    }, {\n      22: [1, 54]\n    }, o($Vn, [2, 18]), {\n      1: [2, 2]\n    }, o($Vn, [2, 8]), o($Vn, [2, 9]), o($Vo, [2, 37], {\n      40: 55,\n      41: $V9,\n      42: $Va,\n      43: $Vb,\n      44: $Vc,\n      45: $Vd,\n      46: $Ve,\n      47: $Vf,\n      48: $Vg,\n      49: $Vh,\n      50: $Vi\n    }), o($Vo, [2, 38]), o($Vo, [2, 39]), o($Vp, [2, 40]), o($Vp, [2, 42]), o($Vp, [2, 43]), o($Vp, [2, 44]), o($Vp, [2, 45]), o($Vp, [2, 46]), o($Vp, [2, 47]), o($Vp, [2, 48]), o($Vp, [2, 49]), o($Vp, [2, 50]), o($Vp, [2, 51]), o($Vn, [2, 10]), o($Vn, [2, 22], {\n      30: 41,\n      29: 56,\n      24: $Vj,\n      27: $Vk\n    }), o($Vn, [2, 24]), o($Vn, [2, 25]), {\n      31: [1, 57]\n    }, {\n      11: 59,\n      32: 58,\n      37: 24,\n      38: $V7,\n      39: $V8,\n      40: 27,\n      41: $V9,\n      42: $Va,\n      43: $Vb,\n      44: $Vc,\n      45: $Vd,\n      46: $Ve,\n      47: $Vf,\n      48: $Vg,\n      49: $Vh,\n      50: $Vi\n    }, o($Vn, [2, 11]), o($Vn, [2, 30], {\n      33: 60,\n      27: $Vl\n    }), o($Vn, [2, 32]), {\n      31: [1, 61]\n    }, o($Vn, [2, 12]), {\n      17: 62,\n      24: $Vm\n    }, {\n      25: 63,\n      27: $Vq\n    }, o($Vn, [2, 14]), {\n      17: 65,\n      24: $Vm\n    }, o($Vn, [2, 16]), o($Vn, [2, 17]), o($Vp, [2, 41]), o($Vn, [2, 23]), {\n      27: [1, 66]\n    }, {\n      26: [1, 67]\n    }, {\n      26: [2, 29],\n      28: [1, 68]\n    }, o($Vn, [2, 31]), {\n      27: [1, 69]\n    }, o($Vn, [2, 13]), {\n      26: [1, 70]\n    }, {\n      26: [2, 21],\n      28: [1, 71]\n    }, o($Vn, [2, 15]), o($Vn, [2, 26]), o($Vn, [2, 27]), {\n      11: 59,\n      32: 72,\n      37: 24,\n      38: $V7,\n      39: $V8,\n      40: 27,\n      41: $V9,\n      42: $Va,\n      43: $Vb,\n      44: $Vc,\n      45: $Vd,\n      46: $Ve,\n      47: $Vf,\n      48: $Vg,\n      49: $Vh,\n      50: $Vi\n    }, o($Vn, [2, 33]), o($Vn, [2, 19]), {\n      25: 73,\n      27: $Vq\n    }, {\n      26: [2, 28]\n    }, {\n      26: [2, 20]\n    }],\n    defaultActions: {\n      8: [2, 1],\n      10: [2, 3],\n      21: [2, 2],\n      72: [2, 28],\n      73: [2, 20]\n    },\n    parseError: /* @__PURE__ */__name(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */__name(function parse(input) {\n      var self = this,\n        stack = [0],\n        tstack = [],\n        vstack = [null],\n        lstack = [],\n        table = this.table,\n        yytext = \"\",\n        yylineno = 0,\n        yyleng = 0,\n        recovering = 0,\n        TERROR = 2,\n        EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = {\n        yy: {}\n      };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      __name(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      __name(lex, \"lex\");\n      var symbol,\n        preErrorSymbol,\n        state,\n        action,\n        a,\n        r,\n        yyval = {},\n        p,\n        len,\n        newState,\n        expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [lstack[lstack.length - (len || 1)].range[0], lstack[lstack.length - 1].range[1]];\n            }\n            r = this.performAction.apply(yyval, [yytext, yyleng, yylineno, sharedState.yy, action[1], vstack, lstack].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */function () {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */__name(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */__name(function (input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */__name(function () {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */__name(function (ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */__name(function () {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */__name(function () {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */__name(function (n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */__name(function () {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */__name(function () {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */__name(function () {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */__name(function (match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */__name(function () {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */__name(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */__name(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */__name(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */__name(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */__name(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */__name(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */__name(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: {\n        \"case-insensitive\": true\n      },\n      performAction: /* @__PURE__ */__name(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            break;\n          case 1:\n            break;\n          case 2:\n            this.popState();\n            return 34;\n            break;\n          case 3:\n            this.popState();\n            return 34;\n            break;\n          case 4:\n            return 34;\n            break;\n          case 5:\n            break;\n          case 6:\n            return 10;\n            break;\n          case 7:\n            this.pushState(\"acc_title\");\n            return 19;\n            break;\n          case 8:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 9:\n            this.pushState(\"acc_descr\");\n            return 21;\n            break;\n          case 10:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 11:\n            this.pushState(\"acc_descr_multiline\");\n            break;\n          case 12:\n            this.popState();\n            break;\n          case 13:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 14:\n            return 5;\n            break;\n          case 15:\n            return 8;\n            break;\n          case 16:\n            this.pushState(\"axis_data\");\n            return \"X_AXIS\";\n            break;\n          case 17:\n            this.pushState(\"axis_data\");\n            return \"Y_AXIS\";\n            break;\n          case 18:\n            this.pushState(\"axis_band_data\");\n            return 24;\n            break;\n          case 19:\n            return 31;\n            break;\n          case 20:\n            this.pushState(\"data\");\n            return 16;\n            break;\n          case 21:\n            this.pushState(\"data\");\n            return 18;\n            break;\n          case 22:\n            this.pushState(\"data_inner\");\n            return 24;\n            break;\n          case 23:\n            return 27;\n            break;\n          case 24:\n            this.popState();\n            return 26;\n            break;\n          case 25:\n            this.popState();\n            break;\n          case 26:\n            this.pushState(\"string\");\n            break;\n          case 27:\n            this.popState();\n            break;\n          case 28:\n            return \"STR\";\n            break;\n          case 29:\n            return 24;\n            break;\n          case 30:\n            return 26;\n            break;\n          case 31:\n            return 43;\n            break;\n          case 32:\n            return \"COLON\";\n            break;\n          case 33:\n            return 44;\n            break;\n          case 34:\n            return 28;\n            break;\n          case 35:\n            return 45;\n            break;\n          case 36:\n            return 46;\n            break;\n          case 37:\n            return 48;\n            break;\n          case 38:\n            return 50;\n            break;\n          case 39:\n            return 47;\n            break;\n          case 40:\n            return 41;\n            break;\n          case 41:\n            return 49;\n            break;\n          case 42:\n            return 42;\n            break;\n          case 43:\n            break;\n          case 44:\n            return 35;\n            break;\n          case 45:\n            return 36;\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:%%(?!\\{)[^\\n]*)/i, /^(?:[^\\}]%%[^\\n]*)/i, /^(?:(\\r?\\n))/i, /^(?:(\\r?\\n))/i, /^(?:[\\n\\r]+)/i, /^(?:%%[^\\n]*)/i, /^(?:title\\b)/i, /^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:\\{)/i, /^(?:[^\\}]*)/i, /^(?:xychart-beta\\b)/i, /^(?:(?:vertical|horizontal))/i, /^(?:x-axis\\b)/i, /^(?:y-axis\\b)/i, /^(?:\\[)/i, /^(?:-->)/i, /^(?:line\\b)/i, /^(?:bar\\b)/i, /^(?:\\[)/i, /^(?:[+-]?(?:\\d+(?:\\.\\d+)?|\\.\\d+))/i, /^(?:\\])/i, /^(?:(?:`\\)                                    \\{ this\\.pushState\\(md_string\\); \\}\\n<md_string>\\(\\?:\\(\\?!`\"\\)\\.\\)\\+                  \\{ return MD_STR; \\}\\n<md_string>\\(\\?:`))/i, /^(?:[\"])/i, /^(?:[\"])/i, /^(?:[^\"]*)/i, /^(?:\\[)/i, /^(?:\\])/i, /^(?:[A-Za-z]+)/i, /^(?::)/i, /^(?:\\+)/i, /^(?:,)/i, /^(?:=)/i, /^(?:\\*)/i, /^(?:#)/i, /^(?:[\\_])/i, /^(?:\\.)/i, /^(?:&)/i, /^(?:-)/i, /^(?:[0-9]+)/i, /^(?:\\s+)/i, /^(?:;)/i, /^(?:$)/i],\n      conditions: {\n        \"data_inner\": {\n          \"rules\": [0, 1, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 20, 21, 23, 24, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45],\n          \"inclusive\": true\n        },\n        \"data\": {\n          \"rules\": [0, 1, 3, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 20, 21, 22, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45],\n          \"inclusive\": true\n        },\n        \"axis_band_data\": {\n          \"rules\": [0, 1, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 20, 21, 24, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45],\n          \"inclusive\": true\n        },\n        \"axis_data\": {\n          \"rules\": [0, 1, 2, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 18, 19, 20, 21, 23, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45],\n          \"inclusive\": true\n        },\n        \"acc_descr_multiline\": {\n          \"rules\": [12, 13],\n          \"inclusive\": false\n        },\n        \"acc_descr\": {\n          \"rules\": [10],\n          \"inclusive\": false\n        },\n        \"acc_title\": {\n          \"rules\": [8],\n          \"inclusive\": false\n        },\n        \"title\": {\n          \"rules\": [],\n          \"inclusive\": false\n        },\n        \"md_string\": {\n          \"rules\": [],\n          \"inclusive\": false\n        },\n        \"string\": {\n          \"rules\": [27, 28],\n          \"inclusive\": false\n        },\n        \"INITIAL\": {\n          \"rules\": [0, 1, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 20, 21, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45],\n          \"inclusive\": true\n        }\n      }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  __name(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar xychart_default = parser;\n\n// src/diagrams/xychart/chartBuilder/interfaces.ts\nfunction isBarPlot(data) {\n  return data.type === \"bar\";\n}\n__name(isBarPlot, \"isBarPlot\");\nfunction isBandAxisData(data) {\n  return data.type === \"band\";\n}\n__name(isBandAxisData, \"isBandAxisData\");\nfunction isLinearAxisData(data) {\n  return data.type === \"linear\";\n}\n__name(isLinearAxisData, \"isLinearAxisData\");\n\n// src/diagrams/xychart/chartBuilder/textDimensionCalculator.ts\nvar TextDimensionCalculatorWithFont = class {\n  constructor(parentGroup) {\n    this.parentGroup = parentGroup;\n  }\n  static {\n    __name(this, \"TextDimensionCalculatorWithFont\");\n  }\n  getMaxDimension(texts, fontSize) {\n    if (!this.parentGroup) {\n      return {\n        width: texts.reduce((acc, cur) => Math.max(cur.length, acc), 0) * fontSize,\n        height: fontSize\n      };\n    }\n    const dimension = {\n      width: 0,\n      height: 0\n    };\n    const elem = this.parentGroup.append(\"g\").attr(\"visibility\", \"hidden\").attr(\"font-size\", fontSize);\n    for (const t of texts) {\n      const bbox = computeDimensionOfText(elem, 1, t);\n      const width = bbox ? bbox.width : t.length * fontSize;\n      const height = bbox ? bbox.height : fontSize;\n      dimension.width = Math.max(dimension.width, width);\n      dimension.height = Math.max(dimension.height, height);\n    }\n    elem.remove();\n    return dimension;\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/axis/bandAxis.ts\nimport { scaleBand } from \"d3\";\n\n// src/diagrams/xychart/chartBuilder/components/axis/baseAxis.ts\nvar BAR_WIDTH_TO_TICK_WIDTH_RATIO = 0.7;\nvar MAX_OUTER_PADDING_PERCENT_FOR_WRT_LABEL = 0.2;\nvar BaseAxis = class {\n  constructor(axisConfig, title, textDimensionCalculator, axisThemeConfig) {\n    this.axisConfig = axisConfig;\n    this.title = title;\n    this.textDimensionCalculator = textDimensionCalculator;\n    this.axisThemeConfig = axisThemeConfig;\n    this.boundingRect = {\n      x: 0,\n      y: 0,\n      width: 0,\n      height: 0\n    };\n    this.axisPosition = \"left\";\n    this.showTitle = false;\n    this.showLabel = false;\n    this.showTick = false;\n    this.showAxisLine = false;\n    this.outerPadding = 0;\n    this.titleTextHeight = 0;\n    this.labelTextHeight = 0;\n    this.range = [0, 10];\n    this.boundingRect = {\n      x: 0,\n      y: 0,\n      width: 0,\n      height: 0\n    };\n    this.axisPosition = \"left\";\n  }\n  static {\n    __name(this, \"BaseAxis\");\n  }\n  setRange(range) {\n    this.range = range;\n    if (this.axisPosition === \"left\" || this.axisPosition === \"right\") {\n      this.boundingRect.height = range[1] - range[0];\n    } else {\n      this.boundingRect.width = range[1] - range[0];\n    }\n    this.recalculateScale();\n  }\n  getRange() {\n    return [this.range[0] + this.outerPadding, this.range[1] - this.outerPadding];\n  }\n  setAxisPosition(axisPosition) {\n    this.axisPosition = axisPosition;\n    this.setRange(this.range);\n  }\n  getTickDistance() {\n    const range = this.getRange();\n    return Math.abs(range[0] - range[1]) / this.getTickValues().length;\n  }\n  getAxisOuterPadding() {\n    return this.outerPadding;\n  }\n  getLabelDimension() {\n    return this.textDimensionCalculator.getMaxDimension(this.getTickValues().map(tick => tick.toString()), this.axisConfig.labelFontSize);\n  }\n  recalculateOuterPaddingToDrawBar() {\n    if (BAR_WIDTH_TO_TICK_WIDTH_RATIO * this.getTickDistance() > this.outerPadding * 2) {\n      this.outerPadding = Math.floor(BAR_WIDTH_TO_TICK_WIDTH_RATIO * this.getTickDistance() / 2);\n    }\n    this.recalculateScale();\n  }\n  calculateSpaceIfDrawnHorizontally(availableSpace) {\n    let availableHeight = availableSpace.height;\n    if (this.axisConfig.showAxisLine && availableHeight > this.axisConfig.axisLineWidth) {\n      availableHeight -= this.axisConfig.axisLineWidth;\n      this.showAxisLine = true;\n    }\n    if (this.axisConfig.showLabel) {\n      const spaceRequired = this.getLabelDimension();\n      const maxPadding = MAX_OUTER_PADDING_PERCENT_FOR_WRT_LABEL * availableSpace.width;\n      this.outerPadding = Math.min(spaceRequired.width / 2, maxPadding);\n      const heightRequired = spaceRequired.height + this.axisConfig.labelPadding * 2;\n      this.labelTextHeight = spaceRequired.height;\n      if (heightRequired <= availableHeight) {\n        availableHeight -= heightRequired;\n        this.showLabel = true;\n      }\n    }\n    if (this.axisConfig.showTick && availableHeight >= this.axisConfig.tickLength) {\n      this.showTick = true;\n      availableHeight -= this.axisConfig.tickLength;\n    }\n    if (this.axisConfig.showTitle && this.title) {\n      const spaceRequired = this.textDimensionCalculator.getMaxDimension([this.title], this.axisConfig.titleFontSize);\n      const heightRequired = spaceRequired.height + this.axisConfig.titlePadding * 2;\n      this.titleTextHeight = spaceRequired.height;\n      if (heightRequired <= availableHeight) {\n        availableHeight -= heightRequired;\n        this.showTitle = true;\n      }\n    }\n    this.boundingRect.width = availableSpace.width;\n    this.boundingRect.height = availableSpace.height - availableHeight;\n  }\n  calculateSpaceIfDrawnVertical(availableSpace) {\n    let availableWidth = availableSpace.width;\n    if (this.axisConfig.showAxisLine && availableWidth > this.axisConfig.axisLineWidth) {\n      availableWidth -= this.axisConfig.axisLineWidth;\n      this.showAxisLine = true;\n    }\n    if (this.axisConfig.showLabel) {\n      const spaceRequired = this.getLabelDimension();\n      const maxPadding = MAX_OUTER_PADDING_PERCENT_FOR_WRT_LABEL * availableSpace.height;\n      this.outerPadding = Math.min(spaceRequired.height / 2, maxPadding);\n      const widthRequired = spaceRequired.width + this.axisConfig.labelPadding * 2;\n      if (widthRequired <= availableWidth) {\n        availableWidth -= widthRequired;\n        this.showLabel = true;\n      }\n    }\n    if (this.axisConfig.showTick && availableWidth >= this.axisConfig.tickLength) {\n      this.showTick = true;\n      availableWidth -= this.axisConfig.tickLength;\n    }\n    if (this.axisConfig.showTitle && this.title) {\n      const spaceRequired = this.textDimensionCalculator.getMaxDimension([this.title], this.axisConfig.titleFontSize);\n      const widthRequired = spaceRequired.height + this.axisConfig.titlePadding * 2;\n      this.titleTextHeight = spaceRequired.height;\n      if (widthRequired <= availableWidth) {\n        availableWidth -= widthRequired;\n        this.showTitle = true;\n      }\n    }\n    this.boundingRect.width = availableSpace.width - availableWidth;\n    this.boundingRect.height = availableSpace.height;\n  }\n  calculateSpace(availableSpace) {\n    if (this.axisPosition === \"left\" || this.axisPosition === \"right\") {\n      this.calculateSpaceIfDrawnVertical(availableSpace);\n    } else {\n      this.calculateSpaceIfDrawnHorizontally(availableSpace);\n    }\n    this.recalculateScale();\n    return {\n      width: this.boundingRect.width,\n      height: this.boundingRect.height\n    };\n  }\n  setBoundingBoxXY(point) {\n    this.boundingRect.x = point.x;\n    this.boundingRect.y = point.y;\n  }\n  getDrawableElementsForLeftAxis() {\n    const drawableElement = [];\n    if (this.showAxisLine) {\n      const x = this.boundingRect.x + this.boundingRect.width - this.axisConfig.axisLineWidth / 2;\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"left-axis\", \"axisl-line\"],\n        data: [{\n          path: `M ${x},${this.boundingRect.y} L ${x},${this.boundingRect.y + this.boundingRect.height} `,\n          strokeFill: this.axisThemeConfig.axisLineColor,\n          strokeWidth: this.axisConfig.axisLineWidth\n        }]\n      });\n    }\n    if (this.showLabel) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"left-axis\", \"label\"],\n        data: this.getTickValues().map(tick => ({\n          text: tick.toString(),\n          x: this.boundingRect.x + this.boundingRect.width - (this.showLabel ? this.axisConfig.labelPadding : 0) - (this.showTick ? this.axisConfig.tickLength : 0) - (this.showAxisLine ? this.axisConfig.axisLineWidth : 0),\n          y: this.getScaleValue(tick),\n          fill: this.axisThemeConfig.labelColor,\n          fontSize: this.axisConfig.labelFontSize,\n          rotation: 0,\n          verticalPos: \"middle\",\n          horizontalPos: \"right\"\n        }))\n      });\n    }\n    if (this.showTick) {\n      const x = this.boundingRect.x + this.boundingRect.width - (this.showAxisLine ? this.axisConfig.axisLineWidth : 0);\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"left-axis\", \"ticks\"],\n        data: this.getTickValues().map(tick => ({\n          path: `M ${x},${this.getScaleValue(tick)} L ${x - this.axisConfig.tickLength},${this.getScaleValue(tick)}`,\n          strokeFill: this.axisThemeConfig.tickColor,\n          strokeWidth: this.axisConfig.tickWidth\n        }))\n      });\n    }\n    if (this.showTitle) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"left-axis\", \"title\"],\n        data: [{\n          text: this.title,\n          x: this.boundingRect.x + this.axisConfig.titlePadding,\n          y: this.boundingRect.y + this.boundingRect.height / 2,\n          fill: this.axisThemeConfig.titleColor,\n          fontSize: this.axisConfig.titleFontSize,\n          rotation: 270,\n          verticalPos: \"top\",\n          horizontalPos: \"center\"\n        }]\n      });\n    }\n    return drawableElement;\n  }\n  getDrawableElementsForBottomAxis() {\n    const drawableElement = [];\n    if (this.showAxisLine) {\n      const y = this.boundingRect.y + this.axisConfig.axisLineWidth / 2;\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"bottom-axis\", \"axis-line\"],\n        data: [{\n          path: `M ${this.boundingRect.x},${y} L ${this.boundingRect.x + this.boundingRect.width},${y}`,\n          strokeFill: this.axisThemeConfig.axisLineColor,\n          strokeWidth: this.axisConfig.axisLineWidth\n        }]\n      });\n    }\n    if (this.showLabel) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"bottom-axis\", \"label\"],\n        data: this.getTickValues().map(tick => ({\n          text: tick.toString(),\n          x: this.getScaleValue(tick),\n          y: this.boundingRect.y + this.axisConfig.labelPadding + (this.showTick ? this.axisConfig.tickLength : 0) + (this.showAxisLine ? this.axisConfig.axisLineWidth : 0),\n          fill: this.axisThemeConfig.labelColor,\n          fontSize: this.axisConfig.labelFontSize,\n          rotation: 0,\n          verticalPos: \"top\",\n          horizontalPos: \"center\"\n        }))\n      });\n    }\n    if (this.showTick) {\n      const y = this.boundingRect.y + (this.showAxisLine ? this.axisConfig.axisLineWidth : 0);\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"bottom-axis\", \"ticks\"],\n        data: this.getTickValues().map(tick => ({\n          path: `M ${this.getScaleValue(tick)},${y} L ${this.getScaleValue(tick)},${y + this.axisConfig.tickLength}`,\n          strokeFill: this.axisThemeConfig.tickColor,\n          strokeWidth: this.axisConfig.tickWidth\n        }))\n      });\n    }\n    if (this.showTitle) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"bottom-axis\", \"title\"],\n        data: [{\n          text: this.title,\n          x: this.range[0] + (this.range[1] - this.range[0]) / 2,\n          y: this.boundingRect.y + this.boundingRect.height - this.axisConfig.titlePadding - this.titleTextHeight,\n          fill: this.axisThemeConfig.titleColor,\n          fontSize: this.axisConfig.titleFontSize,\n          rotation: 0,\n          verticalPos: \"top\",\n          horizontalPos: \"center\"\n        }]\n      });\n    }\n    return drawableElement;\n  }\n  getDrawableElementsForTopAxis() {\n    const drawableElement = [];\n    if (this.showAxisLine) {\n      const y = this.boundingRect.y + this.boundingRect.height - this.axisConfig.axisLineWidth / 2;\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"top-axis\", \"axis-line\"],\n        data: [{\n          path: `M ${this.boundingRect.x},${y} L ${this.boundingRect.x + this.boundingRect.width},${y}`,\n          strokeFill: this.axisThemeConfig.axisLineColor,\n          strokeWidth: this.axisConfig.axisLineWidth\n        }]\n      });\n    }\n    if (this.showLabel) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"top-axis\", \"label\"],\n        data: this.getTickValues().map(tick => ({\n          text: tick.toString(),\n          x: this.getScaleValue(tick),\n          y: this.boundingRect.y + (this.showTitle ? this.titleTextHeight + this.axisConfig.titlePadding * 2 : 0) + this.axisConfig.labelPadding,\n          fill: this.axisThemeConfig.labelColor,\n          fontSize: this.axisConfig.labelFontSize,\n          rotation: 0,\n          verticalPos: \"top\",\n          horizontalPos: \"center\"\n        }))\n      });\n    }\n    if (this.showTick) {\n      const y = this.boundingRect.y;\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"top-axis\", \"ticks\"],\n        data: this.getTickValues().map(tick => ({\n          path: `M ${this.getScaleValue(tick)},${y + this.boundingRect.height - (this.showAxisLine ? this.axisConfig.axisLineWidth : 0)} L ${this.getScaleValue(tick)},${y + this.boundingRect.height - this.axisConfig.tickLength - (this.showAxisLine ? this.axisConfig.axisLineWidth : 0)}`,\n          strokeFill: this.axisThemeConfig.tickColor,\n          strokeWidth: this.axisConfig.tickWidth\n        }))\n      });\n    }\n    if (this.showTitle) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"top-axis\", \"title\"],\n        data: [{\n          text: this.title,\n          x: this.boundingRect.x + this.boundingRect.width / 2,\n          y: this.boundingRect.y + this.axisConfig.titlePadding,\n          fill: this.axisThemeConfig.titleColor,\n          fontSize: this.axisConfig.titleFontSize,\n          rotation: 0,\n          verticalPos: \"top\",\n          horizontalPos: \"center\"\n        }]\n      });\n    }\n    return drawableElement;\n  }\n  getDrawableElements() {\n    if (this.axisPosition === \"left\") {\n      return this.getDrawableElementsForLeftAxis();\n    }\n    if (this.axisPosition === \"right\") {\n      throw Error(\"Drawing of right axis is not implemented\");\n    }\n    if (this.axisPosition === \"bottom\") {\n      return this.getDrawableElementsForBottomAxis();\n    }\n    if (this.axisPosition === \"top\") {\n      return this.getDrawableElementsForTopAxis();\n    }\n    return [];\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/axis/bandAxis.ts\nvar BandAxis = class extends BaseAxis {\n  static {\n    __name(this, \"BandAxis\");\n  }\n  constructor(axisConfig, axisThemeConfig, categories, title, textDimensionCalculator) {\n    super(axisConfig, title, textDimensionCalculator, axisThemeConfig);\n    this.categories = categories;\n    this.scale = scaleBand().domain(this.categories).range(this.getRange());\n  }\n  setRange(range) {\n    super.setRange(range);\n  }\n  recalculateScale() {\n    this.scale = scaleBand().domain(this.categories).range(this.getRange()).paddingInner(1).paddingOuter(0).align(0.5);\n    log.trace(\"BandAxis axis final categories, range: \", this.categories, this.getRange());\n  }\n  getTickValues() {\n    return this.categories;\n  }\n  getScaleValue(value) {\n    return this.scale(value) ?? this.getRange()[0];\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/axis/linearAxis.ts\nimport { scaleLinear } from \"d3\";\nvar LinearAxis = class extends BaseAxis {\n  static {\n    __name(this, \"LinearAxis\");\n  }\n  constructor(axisConfig, axisThemeConfig, domain, title, textDimensionCalculator) {\n    super(axisConfig, title, textDimensionCalculator, axisThemeConfig);\n    this.domain = domain;\n    this.scale = scaleLinear().domain(this.domain).range(this.getRange());\n  }\n  getTickValues() {\n    return this.scale.ticks();\n  }\n  recalculateScale() {\n    const domain = [...this.domain];\n    if (this.axisPosition === \"left\") {\n      domain.reverse();\n    }\n    this.scale = scaleLinear().domain(domain).range(this.getRange());\n  }\n  getScaleValue(value) {\n    return this.scale(value);\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/axis/index.ts\nfunction getAxis(data, axisConfig, axisThemeConfig, tmpSVGGroup2) {\n  const textDimensionCalculator = new TextDimensionCalculatorWithFont(tmpSVGGroup2);\n  if (isBandAxisData(data)) {\n    return new BandAxis(axisConfig, axisThemeConfig, data.categories, data.title, textDimensionCalculator);\n  }\n  return new LinearAxis(axisConfig, axisThemeConfig, [data.min, data.max], data.title, textDimensionCalculator);\n}\n__name(getAxis, \"getAxis\");\n\n// src/diagrams/xychart/chartBuilder/components/chartTitle.ts\nvar ChartTitle = class {\n  constructor(textDimensionCalculator, chartConfig, chartData, chartThemeConfig) {\n    this.textDimensionCalculator = textDimensionCalculator;\n    this.chartConfig = chartConfig;\n    this.chartData = chartData;\n    this.chartThemeConfig = chartThemeConfig;\n    this.boundingRect = {\n      x: 0,\n      y: 0,\n      width: 0,\n      height: 0\n    };\n    this.showChartTitle = false;\n  }\n  static {\n    __name(this, \"ChartTitle\");\n  }\n  setBoundingBoxXY(point) {\n    this.boundingRect.x = point.x;\n    this.boundingRect.y = point.y;\n  }\n  calculateSpace(availableSpace) {\n    const titleDimension = this.textDimensionCalculator.getMaxDimension([this.chartData.title], this.chartConfig.titleFontSize);\n    const widthRequired = Math.max(titleDimension.width, availableSpace.width);\n    const heightRequired = titleDimension.height + 2 * this.chartConfig.titlePadding;\n    if (titleDimension.width <= widthRequired && titleDimension.height <= heightRequired && this.chartConfig.showTitle && this.chartData.title) {\n      this.boundingRect.width = widthRequired;\n      this.boundingRect.height = heightRequired;\n      this.showChartTitle = true;\n    }\n    return {\n      width: this.boundingRect.width,\n      height: this.boundingRect.height\n    };\n  }\n  getDrawableElements() {\n    const drawableElem = [];\n    if (this.showChartTitle) {\n      drawableElem.push({\n        groupTexts: [\"chart-title\"],\n        type: \"text\",\n        data: [{\n          fontSize: this.chartConfig.titleFontSize,\n          text: this.chartData.title,\n          verticalPos: \"middle\",\n          horizontalPos: \"center\",\n          x: this.boundingRect.x + this.boundingRect.width / 2,\n          y: this.boundingRect.y + this.boundingRect.height / 2,\n          fill: this.chartThemeConfig.titleColor,\n          rotation: 0\n        }]\n      });\n    }\n    return drawableElem;\n  }\n};\nfunction getChartTitleComponent(chartConfig, chartData, chartThemeConfig, tmpSVGGroup2) {\n  const textDimensionCalculator = new TextDimensionCalculatorWithFont(tmpSVGGroup2);\n  return new ChartTitle(textDimensionCalculator, chartConfig, chartData, chartThemeConfig);\n}\n__name(getChartTitleComponent, \"getChartTitleComponent\");\n\n// src/diagrams/xychart/chartBuilder/components/plot/linePlot.ts\nimport { line } from \"d3\";\nvar LinePlot = class {\n  constructor(plotData, xAxis, yAxis, orientation, plotIndex2) {\n    this.plotData = plotData;\n    this.xAxis = xAxis;\n    this.yAxis = yAxis;\n    this.orientation = orientation;\n    this.plotIndex = plotIndex2;\n  }\n  static {\n    __name(this, \"LinePlot\");\n  }\n  getDrawableElement() {\n    const finalData = this.plotData.data.map(d => [this.xAxis.getScaleValue(d[0]), this.yAxis.getScaleValue(d[1])]);\n    let path;\n    if (this.orientation === \"horizontal\") {\n      path = line().y(d => d[0]).x(d => d[1])(finalData);\n    } else {\n      path = line().x(d => d[0]).y(d => d[1])(finalData);\n    }\n    if (!path) {\n      return [];\n    }\n    return [{\n      groupTexts: [\"plot\", `line-plot-${this.plotIndex}`],\n      type: \"path\",\n      data: [{\n        path,\n        strokeFill: this.plotData.strokeFill,\n        strokeWidth: this.plotData.strokeWidth\n      }]\n    }];\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/plot/barPlot.ts\nvar BarPlot = class {\n  constructor(barData, boundingRect, xAxis, yAxis, orientation, plotIndex2) {\n    this.barData = barData;\n    this.boundingRect = boundingRect;\n    this.xAxis = xAxis;\n    this.yAxis = yAxis;\n    this.orientation = orientation;\n    this.plotIndex = plotIndex2;\n  }\n  static {\n    __name(this, \"BarPlot\");\n  }\n  getDrawableElement() {\n    const finalData = this.barData.data.map(d => [this.xAxis.getScaleValue(d[0]), this.yAxis.getScaleValue(d[1])]);\n    const barPaddingPercent = 0.05;\n    const barWidth = Math.min(this.xAxis.getAxisOuterPadding() * 2, this.xAxis.getTickDistance()) * (1 - barPaddingPercent);\n    const barWidthHalf = barWidth / 2;\n    if (this.orientation === \"horizontal\") {\n      return [{\n        groupTexts: [\"plot\", `bar-plot-${this.plotIndex}`],\n        type: \"rect\",\n        data: finalData.map(data => ({\n          x: this.boundingRect.x,\n          y: data[0] - barWidthHalf,\n          height: barWidth,\n          width: data[1] - this.boundingRect.x,\n          fill: this.barData.fill,\n          strokeWidth: 0,\n          strokeFill: this.barData.fill\n        }))\n      }];\n    }\n    return [{\n      groupTexts: [\"plot\", `bar-plot-${this.plotIndex}`],\n      type: \"rect\",\n      data: finalData.map(data => ({\n        x: data[0] - barWidthHalf,\n        y: data[1],\n        width: barWidth,\n        height: this.boundingRect.y + this.boundingRect.height - data[1],\n        fill: this.barData.fill,\n        strokeWidth: 0,\n        strokeFill: this.barData.fill\n      }))\n    }];\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/plot/index.ts\nvar BasePlot = class {\n  constructor(chartConfig, chartData, chartThemeConfig) {\n    this.chartConfig = chartConfig;\n    this.chartData = chartData;\n    this.chartThemeConfig = chartThemeConfig;\n    this.boundingRect = {\n      x: 0,\n      y: 0,\n      width: 0,\n      height: 0\n    };\n  }\n  static {\n    __name(this, \"BasePlot\");\n  }\n  setAxes(xAxis, yAxis) {\n    this.xAxis = xAxis;\n    this.yAxis = yAxis;\n  }\n  setBoundingBoxXY(point) {\n    this.boundingRect.x = point.x;\n    this.boundingRect.y = point.y;\n  }\n  calculateSpace(availableSpace) {\n    this.boundingRect.width = availableSpace.width;\n    this.boundingRect.height = availableSpace.height;\n    return {\n      width: this.boundingRect.width,\n      height: this.boundingRect.height\n    };\n  }\n  getDrawableElements() {\n    if (!(this.xAxis && this.yAxis)) {\n      throw Error(\"Axes must be passed to render Plots\");\n    }\n    const drawableElem = [];\n    for (const [i, plot] of this.chartData.plots.entries()) {\n      switch (plot.type) {\n        case \"line\":\n          {\n            const linePlot = new LinePlot(plot, this.xAxis, this.yAxis, this.chartConfig.chartOrientation, i);\n            drawableElem.push(...linePlot.getDrawableElement());\n          }\n          break;\n        case \"bar\":\n          {\n            const barPlot = new BarPlot(plot, this.boundingRect, this.xAxis, this.yAxis, this.chartConfig.chartOrientation, i);\n            drawableElem.push(...barPlot.getDrawableElement());\n          }\n          break;\n      }\n    }\n    return drawableElem;\n  }\n};\nfunction getPlotComponent(chartConfig, chartData, chartThemeConfig) {\n  return new BasePlot(chartConfig, chartData, chartThemeConfig);\n}\n__name(getPlotComponent, \"getPlotComponent\");\n\n// src/diagrams/xychart/chartBuilder/orchestrator.ts\nvar Orchestrator = class {\n  constructor(chartConfig, chartData, chartThemeConfig, tmpSVGGroup2) {\n    this.chartConfig = chartConfig;\n    this.chartData = chartData;\n    this.componentStore = {\n      title: getChartTitleComponent(chartConfig, chartData, chartThemeConfig, tmpSVGGroup2),\n      plot: getPlotComponent(chartConfig, chartData, chartThemeConfig),\n      xAxis: getAxis(chartData.xAxis, chartConfig.xAxis, {\n        titleColor: chartThemeConfig.xAxisTitleColor,\n        labelColor: chartThemeConfig.xAxisLabelColor,\n        tickColor: chartThemeConfig.xAxisTickColor,\n        axisLineColor: chartThemeConfig.xAxisLineColor\n      }, tmpSVGGroup2),\n      yAxis: getAxis(chartData.yAxis, chartConfig.yAxis, {\n        titleColor: chartThemeConfig.yAxisTitleColor,\n        labelColor: chartThemeConfig.yAxisLabelColor,\n        tickColor: chartThemeConfig.yAxisTickColor,\n        axisLineColor: chartThemeConfig.yAxisLineColor\n      }, tmpSVGGroup2)\n    };\n  }\n  static {\n    __name(this, \"Orchestrator\");\n  }\n  calculateVerticalSpace() {\n    let availableWidth = this.chartConfig.width;\n    let availableHeight = this.chartConfig.height;\n    let plotX = 0;\n    let plotY = 0;\n    let chartWidth = Math.floor(availableWidth * this.chartConfig.plotReservedSpacePercent / 100);\n    let chartHeight = Math.floor(availableHeight * this.chartConfig.plotReservedSpacePercent / 100);\n    let spaceUsed = this.componentStore.plot.calculateSpace({\n      width: chartWidth,\n      height: chartHeight\n    });\n    availableWidth -= spaceUsed.width;\n    availableHeight -= spaceUsed.height;\n    spaceUsed = this.componentStore.title.calculateSpace({\n      width: this.chartConfig.width,\n      height: availableHeight\n    });\n    plotY = spaceUsed.height;\n    availableHeight -= spaceUsed.height;\n    this.componentStore.xAxis.setAxisPosition(\"bottom\");\n    spaceUsed = this.componentStore.xAxis.calculateSpace({\n      width: availableWidth,\n      height: availableHeight\n    });\n    availableHeight -= spaceUsed.height;\n    this.componentStore.yAxis.setAxisPosition(\"left\");\n    spaceUsed = this.componentStore.yAxis.calculateSpace({\n      width: availableWidth,\n      height: availableHeight\n    });\n    plotX = spaceUsed.width;\n    availableWidth -= spaceUsed.width;\n    if (availableWidth > 0) {\n      chartWidth += availableWidth;\n      availableWidth = 0;\n    }\n    if (availableHeight > 0) {\n      chartHeight += availableHeight;\n      availableHeight = 0;\n    }\n    this.componentStore.plot.calculateSpace({\n      width: chartWidth,\n      height: chartHeight\n    });\n    this.componentStore.plot.setBoundingBoxXY({\n      x: plotX,\n      y: plotY\n    });\n    this.componentStore.xAxis.setRange([plotX, plotX + chartWidth]);\n    this.componentStore.xAxis.setBoundingBoxXY({\n      x: plotX,\n      y: plotY + chartHeight\n    });\n    this.componentStore.yAxis.setRange([plotY, plotY + chartHeight]);\n    this.componentStore.yAxis.setBoundingBoxXY({\n      x: 0,\n      y: plotY\n    });\n    if (this.chartData.plots.some(p => isBarPlot(p))) {\n      this.componentStore.xAxis.recalculateOuterPaddingToDrawBar();\n    }\n  }\n  calculateHorizontalSpace() {\n    let availableWidth = this.chartConfig.width;\n    let availableHeight = this.chartConfig.height;\n    let titleYEnd = 0;\n    let plotX = 0;\n    let plotY = 0;\n    let chartWidth = Math.floor(availableWidth * this.chartConfig.plotReservedSpacePercent / 100);\n    let chartHeight = Math.floor(availableHeight * this.chartConfig.plotReservedSpacePercent / 100);\n    let spaceUsed = this.componentStore.plot.calculateSpace({\n      width: chartWidth,\n      height: chartHeight\n    });\n    availableWidth -= spaceUsed.width;\n    availableHeight -= spaceUsed.height;\n    spaceUsed = this.componentStore.title.calculateSpace({\n      width: this.chartConfig.width,\n      height: availableHeight\n    });\n    titleYEnd = spaceUsed.height;\n    availableHeight -= spaceUsed.height;\n    this.componentStore.xAxis.setAxisPosition(\"left\");\n    spaceUsed = this.componentStore.xAxis.calculateSpace({\n      width: availableWidth,\n      height: availableHeight\n    });\n    availableWidth -= spaceUsed.width;\n    plotX = spaceUsed.width;\n    this.componentStore.yAxis.setAxisPosition(\"top\");\n    spaceUsed = this.componentStore.yAxis.calculateSpace({\n      width: availableWidth,\n      height: availableHeight\n    });\n    availableHeight -= spaceUsed.height;\n    plotY = titleYEnd + spaceUsed.height;\n    if (availableWidth > 0) {\n      chartWidth += availableWidth;\n      availableWidth = 0;\n    }\n    if (availableHeight > 0) {\n      chartHeight += availableHeight;\n      availableHeight = 0;\n    }\n    this.componentStore.plot.calculateSpace({\n      width: chartWidth,\n      height: chartHeight\n    });\n    this.componentStore.plot.setBoundingBoxXY({\n      x: plotX,\n      y: plotY\n    });\n    this.componentStore.yAxis.setRange([plotX, plotX + chartWidth]);\n    this.componentStore.yAxis.setBoundingBoxXY({\n      x: plotX,\n      y: titleYEnd\n    });\n    this.componentStore.xAxis.setRange([plotY, plotY + chartHeight]);\n    this.componentStore.xAxis.setBoundingBoxXY({\n      x: 0,\n      y: plotY\n    });\n    if (this.chartData.plots.some(p => isBarPlot(p))) {\n      this.componentStore.xAxis.recalculateOuterPaddingToDrawBar();\n    }\n  }\n  calculateSpace() {\n    if (this.chartConfig.chartOrientation === \"horizontal\") {\n      this.calculateHorizontalSpace();\n    } else {\n      this.calculateVerticalSpace();\n    }\n  }\n  getDrawableElement() {\n    this.calculateSpace();\n    const drawableElem = [];\n    this.componentStore.plot.setAxes(this.componentStore.xAxis, this.componentStore.yAxis);\n    for (const component of Object.values(this.componentStore)) {\n      drawableElem.push(...component.getDrawableElements());\n    }\n    return drawableElem;\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/index.ts\nvar XYChartBuilder = class {\n  static {\n    __name(this, \"XYChartBuilder\");\n  }\n  static build(config, chartData, chartThemeConfig, tmpSVGGroup2) {\n    const orchestrator = new Orchestrator(config, chartData, chartThemeConfig, tmpSVGGroup2);\n    return orchestrator.getDrawableElement();\n  }\n};\n\n// src/diagrams/xychart/xychartDb.ts\nvar plotIndex = 0;\nvar tmpSVGGroup;\nvar xyChartConfig = getChartDefaultConfig();\nvar xyChartThemeConfig = getChartDefaultThemeConfig();\nvar xyChartData = getChartDefaultData();\nvar plotColorPalette = xyChartThemeConfig.plotColorPalette.split(\",\").map(color => color.trim());\nvar hasSetXAxis = false;\nvar hasSetYAxis = false;\nfunction getChartDefaultThemeConfig() {\n  const defaultThemeVariables = getThemeVariables();\n  const config = getConfig();\n  return cleanAndMerge(defaultThemeVariables.xyChart, config.themeVariables.xyChart);\n}\n__name(getChartDefaultThemeConfig, \"getChartDefaultThemeConfig\");\nfunction getChartDefaultConfig() {\n  const config = getConfig();\n  return cleanAndMerge(defaultConfig_default.xyChart, config.xyChart);\n}\n__name(getChartDefaultConfig, \"getChartDefaultConfig\");\nfunction getChartDefaultData() {\n  return {\n    yAxis: {\n      type: \"linear\",\n      title: \"\",\n      min: Infinity,\n      max: -Infinity\n    },\n    xAxis: {\n      type: \"band\",\n      title: \"\",\n      categories: []\n    },\n    title: \"\",\n    plots: []\n  };\n}\n__name(getChartDefaultData, \"getChartDefaultData\");\nfunction textSanitizer(text) {\n  const config = getConfig();\n  return sanitizeText(text.trim(), config);\n}\n__name(textSanitizer, \"textSanitizer\");\nfunction setTmpSVGG(SVGG) {\n  tmpSVGGroup = SVGG;\n}\n__name(setTmpSVGG, \"setTmpSVGG\");\nfunction setOrientation(orientation) {\n  if (orientation === \"horizontal\") {\n    xyChartConfig.chartOrientation = \"horizontal\";\n  } else {\n    xyChartConfig.chartOrientation = \"vertical\";\n  }\n}\n__name(setOrientation, \"setOrientation\");\nfunction setXAxisTitle(title) {\n  xyChartData.xAxis.title = textSanitizer(title.text);\n}\n__name(setXAxisTitle, \"setXAxisTitle\");\nfunction setXAxisRangeData(min, max) {\n  xyChartData.xAxis = {\n    type: \"linear\",\n    title: xyChartData.xAxis.title,\n    min,\n    max\n  };\n  hasSetXAxis = true;\n}\n__name(setXAxisRangeData, \"setXAxisRangeData\");\nfunction setXAxisBand(categories) {\n  xyChartData.xAxis = {\n    type: \"band\",\n    title: xyChartData.xAxis.title,\n    categories: categories.map(c => textSanitizer(c.text))\n  };\n  hasSetXAxis = true;\n}\n__name(setXAxisBand, \"setXAxisBand\");\nfunction setYAxisTitle(title) {\n  xyChartData.yAxis.title = textSanitizer(title.text);\n}\n__name(setYAxisTitle, \"setYAxisTitle\");\nfunction setYAxisRangeData(min, max) {\n  xyChartData.yAxis = {\n    type: \"linear\",\n    title: xyChartData.yAxis.title,\n    min,\n    max\n  };\n  hasSetYAxis = true;\n}\n__name(setYAxisRangeData, \"setYAxisRangeData\");\nfunction setYAxisRangeFromPlotData(data) {\n  const minValue = Math.min(...data);\n  const maxValue = Math.max(...data);\n  const prevMinValue = isLinearAxisData(xyChartData.yAxis) ? xyChartData.yAxis.min : Infinity;\n  const prevMaxValue = isLinearAxisData(xyChartData.yAxis) ? xyChartData.yAxis.max : -Infinity;\n  xyChartData.yAxis = {\n    type: \"linear\",\n    title: xyChartData.yAxis.title,\n    min: Math.min(prevMinValue, minValue),\n    max: Math.max(prevMaxValue, maxValue)\n  };\n}\n__name(setYAxisRangeFromPlotData, \"setYAxisRangeFromPlotData\");\nfunction transformDataWithoutCategory(data) {\n  let retData = [];\n  if (data.length === 0) {\n    return retData;\n  }\n  if (!hasSetXAxis) {\n    const prevMinValue = isLinearAxisData(xyChartData.xAxis) ? xyChartData.xAxis.min : Infinity;\n    const prevMaxValue = isLinearAxisData(xyChartData.xAxis) ? xyChartData.xAxis.max : -Infinity;\n    setXAxisRangeData(Math.min(prevMinValue, 1), Math.max(prevMaxValue, data.length));\n  }\n  if (!hasSetYAxis) {\n    setYAxisRangeFromPlotData(data);\n  }\n  if (isBandAxisData(xyChartData.xAxis)) {\n    retData = xyChartData.xAxis.categories.map((c, i) => [c, data[i]]);\n  }\n  if (isLinearAxisData(xyChartData.xAxis)) {\n    const min = xyChartData.xAxis.min;\n    const max = xyChartData.xAxis.max;\n    const step = (max - min) / (data.length - 1);\n    const categories = [];\n    for (let i = min; i <= max; i += step) {\n      categories.push(`${i}`);\n    }\n    retData = categories.map((c, i) => [c, data[i]]);\n  }\n  return retData;\n}\n__name(transformDataWithoutCategory, \"transformDataWithoutCategory\");\nfunction getPlotColorFromPalette(plotIndex2) {\n  return plotColorPalette[plotIndex2 === 0 ? 0 : plotIndex2 % plotColorPalette.length];\n}\n__name(getPlotColorFromPalette, \"getPlotColorFromPalette\");\nfunction setLineData(title, data) {\n  const plotData = transformDataWithoutCategory(data);\n  xyChartData.plots.push({\n    type: \"line\",\n    strokeFill: getPlotColorFromPalette(plotIndex),\n    strokeWidth: 2,\n    data: plotData\n  });\n  plotIndex++;\n}\n__name(setLineData, \"setLineData\");\nfunction setBarData(title, data) {\n  const plotData = transformDataWithoutCategory(data);\n  xyChartData.plots.push({\n    type: \"bar\",\n    fill: getPlotColorFromPalette(plotIndex),\n    data: plotData\n  });\n  plotIndex++;\n}\n__name(setBarData, \"setBarData\");\nfunction getDrawableElem() {\n  if (xyChartData.plots.length === 0) {\n    throw Error(\"No Plot to render, please provide a plot with some data\");\n  }\n  xyChartData.title = getDiagramTitle();\n  return XYChartBuilder.build(xyChartConfig, xyChartData, xyChartThemeConfig, tmpSVGGroup);\n}\n__name(getDrawableElem, \"getDrawableElem\");\nfunction getChartThemeConfig() {\n  return xyChartThemeConfig;\n}\n__name(getChartThemeConfig, \"getChartThemeConfig\");\nfunction getChartConfig() {\n  return xyChartConfig;\n}\n__name(getChartConfig, \"getChartConfig\");\nvar clear2 = /* @__PURE__ */__name(function () {\n  clear();\n  plotIndex = 0;\n  xyChartConfig = getChartDefaultConfig();\n  xyChartData = getChartDefaultData();\n  xyChartThemeConfig = getChartDefaultThemeConfig();\n  plotColorPalette = xyChartThemeConfig.plotColorPalette.split(\",\").map(color => color.trim());\n  hasSetXAxis = false;\n  hasSetYAxis = false;\n}, \"clear\");\nvar xychartDb_default = {\n  getDrawableElem,\n  clear: clear2,\n  setAccTitle,\n  getAccTitle,\n  setDiagramTitle,\n  getDiagramTitle,\n  getAccDescription,\n  setAccDescription,\n  setOrientation,\n  setXAxisTitle,\n  setXAxisRangeData,\n  setXAxisBand,\n  setYAxisTitle,\n  setYAxisRangeData,\n  setLineData,\n  setBarData,\n  setTmpSVGG,\n  getChartThemeConfig,\n  getChartConfig\n};\n\n// src/diagrams/xychart/xychartRenderer.ts\nvar draw = /* @__PURE__ */__name((txt, id, _version, diagObj) => {\n  const db = diagObj.db;\n  const themeConfig = db.getChartThemeConfig();\n  const chartConfig = db.getChartConfig();\n  function getDominantBaseLine(horizontalPos) {\n    return horizontalPos === \"top\" ? \"text-before-edge\" : \"middle\";\n  }\n  __name(getDominantBaseLine, \"getDominantBaseLine\");\n  function getTextAnchor(verticalPos) {\n    return verticalPos === \"left\" ? \"start\" : verticalPos === \"right\" ? \"end\" : \"middle\";\n  }\n  __name(getTextAnchor, \"getTextAnchor\");\n  function getTextTransformation(data) {\n    return `translate(${data.x}, ${data.y}) rotate(${data.rotation || 0})`;\n  }\n  __name(getTextTransformation, \"getTextTransformation\");\n  log.debug(\"Rendering xychart chart\\n\" + txt);\n  const svg = selectSvgElement(id);\n  const group = svg.append(\"g\").attr(\"class\", \"main\");\n  const background = group.append(\"rect\").attr(\"width\", chartConfig.width).attr(\"height\", chartConfig.height).attr(\"class\", \"background\");\n  configureSvgSize(svg, chartConfig.height, chartConfig.width, true);\n  svg.attr(\"viewBox\", `0 0 ${chartConfig.width} ${chartConfig.height}`);\n  background.attr(\"fill\", themeConfig.backgroundColor);\n  db.setTmpSVGG(svg.append(\"g\").attr(\"class\", \"mermaid-tmp-group\"));\n  const shapes = db.getDrawableElem();\n  const groups = {};\n  function getGroup(gList) {\n    let elem = group;\n    let prefix = \"\";\n    for (const [i] of gList.entries()) {\n      let parent = group;\n      if (i > 0 && groups[prefix]) {\n        parent = groups[prefix];\n      }\n      prefix += gList[i];\n      elem = groups[prefix];\n      if (!elem) {\n        elem = groups[prefix] = parent.append(\"g\").attr(\"class\", gList[i]);\n      }\n    }\n    return elem;\n  }\n  __name(getGroup, \"getGroup\");\n  for (const shape of shapes) {\n    if (shape.data.length === 0) {\n      continue;\n    }\n    const shapeGroup = getGroup(shape.groupTexts);\n    switch (shape.type) {\n      case \"rect\":\n        shapeGroup.selectAll(\"rect\").data(shape.data).enter().append(\"rect\").attr(\"x\", data => data.x).attr(\"y\", data => data.y).attr(\"width\", data => data.width).attr(\"height\", data => data.height).attr(\"fill\", data => data.fill).attr(\"stroke\", data => data.strokeFill).attr(\"stroke-width\", data => data.strokeWidth);\n        break;\n      case \"text\":\n        shapeGroup.selectAll(\"text\").data(shape.data).enter().append(\"text\").attr(\"x\", 0).attr(\"y\", 0).attr(\"fill\", data => data.fill).attr(\"font-size\", data => data.fontSize).attr(\"dominant-baseline\", data => getDominantBaseLine(data.verticalPos)).attr(\"text-anchor\", data => getTextAnchor(data.horizontalPos)).attr(\"transform\", data => getTextTransformation(data)).text(data => data.text);\n        break;\n      case \"path\":\n        shapeGroup.selectAll(\"path\").data(shape.data).enter().append(\"path\").attr(\"d\", data => data.path).attr(\"fill\", data => data.fill ? data.fill : \"none\").attr(\"stroke\", data => data.strokeFill).attr(\"stroke-width\", data => data.strokeWidth);\n        break;\n    }\n  }\n}, \"draw\");\nvar xychartRenderer_default = {\n  draw\n};\n\n// src/diagrams/xychart/xychartDiagram.ts\nvar diagram = {\n  parser: xychart_default,\n  db: xychartDb_default,\n  renderer: xychartRenderer_default\n};\nexport { diagram };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,IAAI,SAAS,WAAY;AACvB,MAAI,IAAmB,OAAO,SAAU,GAAG,GAAG,IAAI,GAAG;AACjD,SAAK,KAAK,MAAM,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK,GAAG,EAAE,CAAC,CAAC,IAAI,EAAE;AACpD,WAAO;AAAA,EACT,GAAG,GAAG,GACN,MAAM,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GACxC,MAAM,CAAC,GAAG,CAAC,GACX,MAAM,CAAC,GAAG,CAAC,GACX,MAAM,CAAC,GAAG,CAAC,GACX,MAAM,CAAC,GAAG,CAAC,GACX,MAAM,CAAC,GAAG,CAAC,GACX,MAAM,CAAC,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GACvD,MAAM,CAAC,GAAG,EAAE,GACZ,MAAM,CAAC,GAAG,EAAE,GACZ,MAAM,CAAC,GAAG,EAAE,GACZ,MAAM,CAAC,GAAG,EAAE,GACZ,MAAM,CAAC,GAAG,EAAE,GACZ,MAAM,CAAC,GAAG,EAAE,GACZ,MAAM,CAAC,GAAG,EAAE,GACZ,MAAM,CAAC,GAAG,EAAE,GACZ,MAAM,CAAC,GAAG,EAAE,GACZ,MAAM,CAAC,GAAG,EAAE,GACZ,MAAM,CAAC,GAAG,EAAE,GACZ,MAAM,CAAC,GAAG,EAAE,GACZ,MAAM,CAAC,GAAG,EAAE,GACZ,MAAM,CAAC,GAAG,EAAE,GACZ,MAAM,CAAC,GAAG,EAAE,GACZ,MAAM,CAAC,GAAG,EAAE,GACZ,MAAM,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GACpD,MAAM,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GACpE,MAAM,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAC5G,MAAM,CAAC,GAAG,EAAE;AACd,MAAI,UAAU;AAAA,IACZ,OAAsB,OAAO,SAAS,QAAQ;AAAA,IAAC,GAAG,OAAO;AAAA,IACzD,IAAI,CAAC;AAAA,IACL,UAAU;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,MACP,WAAW;AAAA,MACX,eAAe;AAAA,MACf,YAAY;AAAA,MACZ,qBAAqB;AAAA,MACrB,aAAa;AAAA,MACb,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,cAAc;AAAA,MACd,UAAU;AAAA,MACV,cAAc;AAAA,MACd,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,aAAa;AAAA,MACb,mBAAmB;AAAA,MACnB,aAAa;AAAA,MACb,mBAAmB;AAAA,MACnB,6BAA6B;AAAA,MAC7B,uBAAuB;AAAA,MACvB,yBAAyB;AAAA,MACzB,qBAAqB;AAAA,MACrB,uBAAuB;AAAA,MACvB,SAAS;AAAA,MACT,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,aAAa;AAAA,MACb,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,UAAU;AAAA,MACV,iBAAiB;AAAA,MACjB,OAAO;AAAA,MACP,OAAO;AAAA,MACP,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,QAAQ;AAAA,IACV;AAAA,IACA,YAAY;AAAA,MACV,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,IACN;AAAA,IACA,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAAA,IACtc,eAA8B,OAAO,SAAS,UAAU,QAAQ,QAAQ,UAAU,IAAI,SAAS,IAAI,IAAI;AACrG,UAAI,KAAK,GAAG,SAAS;AACrB,cAAQ,SAAS;AAAA,QACf,KAAK;AACH,aAAG,eAAe,GAAG,EAAE,CAAC;AACxB;AAAA,QACF,KAAK;AACH,aAAG,gBAAgB,GAAG,EAAE,EAAE,KAAK,KAAK,CAAC;AACrC;AAAA,QACF,KAAK;AACH,aAAG,YAAY;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,UACR,GAAG,GAAG,EAAE,CAAC;AACT;AAAA,QACF,KAAK;AACH,aAAG,YAAY,GAAG,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC;AACjC;AAAA,QACF,KAAK;AACH,aAAG,WAAW;AAAA,YACZ,MAAM;AAAA,YACN,MAAM;AAAA,UACR,GAAG,GAAG,EAAE,CAAC;AACT;AAAA,QACF,KAAK;AACH,aAAG,WAAW,GAAG,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC;AAChC;AAAA,QACF,KAAK;AACH,eAAK,IAAI,GAAG,EAAE,EAAE,KAAK;AACrB,aAAG,YAAY,KAAK,CAAC;AACrB;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AACH,eAAK,IAAI,GAAG,EAAE,EAAE,KAAK;AACrB,aAAG,kBAAkB,KAAK,CAAC;AAC3B;AAAA,QACF,KAAK;AACH,eAAK,IAAI,GAAG,KAAK,CAAC;AAClB;AAAA,QACF,KAAK;AACH,eAAK,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;AACvC;AAAA,QACF,KAAK;AACH,eAAK,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC,CAAC;AACxB;AAAA,QACF,KAAK;AACH,aAAG,cAAc,GAAG,EAAE,CAAC;AACvB;AAAA,QACF,KAAK;AACH,aAAG,cAAc,GAAG,KAAK,CAAC,CAAC;AAC3B;AAAA,QACF,KAAK;AACH,aAAG,cAAc;AAAA,YACf,MAAM;AAAA,YACN,MAAM;AAAA,UACR,CAAC;AACD;AAAA,QACF,KAAK;AACH,aAAG,aAAa,GAAG,EAAE,CAAC;AACtB;AAAA,QACF,KAAK;AACH,aAAG,kBAAkB,OAAO,GAAG,KAAK,CAAC,CAAC,GAAG,OAAO,GAAG,EAAE,CAAC,CAAC;AACvD;AAAA,QACF,KAAK;AACH,eAAK,IAAI,GAAG,KAAK,CAAC;AAClB;AAAA,QACF,KAAK;AACH,eAAK,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;AAC/B;AAAA,QACF,KAAK;AACH,eAAK,IAAI,CAAC,GAAG,EAAE,CAAC;AAChB;AAAA,QACF,KAAK;AACH,aAAG,cAAc,GAAG,EAAE,CAAC;AACvB;AAAA,QACF,KAAK;AACH,aAAG,cAAc,GAAG,KAAK,CAAC,CAAC;AAC3B;AAAA,QACF,KAAK;AACH,aAAG,cAAc;AAAA,YACf,MAAM;AAAA,YACN,MAAM;AAAA,UACR,CAAC;AACD;AAAA,QACF,KAAK;AACH,aAAG,kBAAkB,OAAO,GAAG,KAAK,CAAC,CAAC,GAAG,OAAO,GAAG,EAAE,CAAC,CAAC;AACvD;AAAA,QACF,KAAK;AACH,eAAK,IAAI;AAAA,YACP,MAAM,GAAG,EAAE;AAAA,YACX,MAAM;AAAA,UACR;AACA;AAAA,QACF,KAAK;AACH,eAAK,IAAI;AAAA,YACP,MAAM,GAAG,EAAE;AAAA,YACX,MAAM;AAAA,UACR;AACA;AAAA,QACF,KAAK;AACH,eAAK,IAAI;AAAA,YACP,MAAM,GAAG,EAAE;AAAA,YACX,MAAM;AAAA,UACR;AACA;AAAA,QACF,KAAK;AACH,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,eAAK,IAAI,GAAG,KAAK,CAAC,IAAI,KAAK,GAAG,EAAE;AAChC;AAAA,MACJ;AAAA,IACF,GAAG,WAAW;AAAA,IACd,OAAO,CAAC,EAAE,KAAK,KAAK;AAAA,MAClB,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,IACN,CAAC,GAAG;AAAA,MACF,GAAG,CAAC,CAAC;AAAA,IACP,GAAG,EAAE,KAAK,KAAK;AAAA,MACb,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,IACN,CAAC,GAAG,EAAE,KAAK,KAAK;AAAA,MACd,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG,CAAC,GAAG,EAAE;AAAA,MACT,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,IACN,CAAC,GAAG;AAAA,MACF,GAAG,CAAC,GAAG,CAAC;AAAA,MACR,GAAG;AAAA,MACH,IAAI,CAAC,GAAG,EAAE;AAAA,MACV,IAAI,CAAC,GAAG,EAAE;AAAA,MACV,IAAI,CAAC,GAAG,EAAE;AAAA,MACV,IAAI,CAAC,GAAG,EAAE;AAAA,MACV,IAAI,CAAC,GAAG,EAAE;AAAA,MACV,IAAI,CAAC,GAAG,EAAE;AAAA,MACV,IAAI,CAAC,GAAG,EAAE;AAAA,MACV,IAAI,CAAC,GAAG,EAAE;AAAA,IACZ,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG;AAAA,MACpD,GAAG,CAAC,GAAG,CAAC;AAAA,IACV,GAAG,EAAE,KAAK,KAAK;AAAA,MACb,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,IACN,CAAC,GAAG;AAAA,MACF,GAAG,CAAC,GAAG,CAAC;AAAA,IACV,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG;AAAA,MAChC,GAAG;AAAA,MACH,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,IACN,CAAC,GAAG;AAAA,MACF,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,IACN,GAAG;AAAA,MACD,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,IACN,GAAG;AAAA,MACD,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,IACN,GAAG;AAAA,MACD,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,IACN,GAAG;AAAA,MACD,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,IACN,GAAG;AAAA,MACD,IAAI,CAAC,GAAG,EAAE;AAAA,IACZ,GAAG;AAAA,MACD,IAAI,CAAC,GAAG,EAAE;AAAA,IACZ,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG;AAAA,MAClB,GAAG,CAAC,GAAG,CAAC;AAAA,IACV,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG;AAAA,MACjD,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,IACN,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG;AAAA,MAChQ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,IACN,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG;AAAA,MACpC,IAAI,CAAC,GAAG,EAAE;AAAA,IACZ,GAAG;AAAA,MACD,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,IACN,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG;AAAA,MAClC,IAAI;AAAA,MACJ,IAAI;AAAA,IACN,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG;AAAA,MACnB,IAAI,CAAC,GAAG,EAAE;AAAA,IACZ,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG;AAAA,MAClB,IAAI;AAAA,MACJ,IAAI;AAAA,IACN,GAAG;AAAA,MACD,IAAI;AAAA,MACJ,IAAI;AAAA,IACN,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG;AAAA,MAClB,IAAI;AAAA,MACJ,IAAI;AAAA,IACN,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG;AAAA,MACrE,IAAI,CAAC,GAAG,EAAE;AAAA,IACZ,GAAG;AAAA,MACD,IAAI,CAAC,GAAG,EAAE;AAAA,IACZ,GAAG;AAAA,MACD,IAAI,CAAC,GAAG,EAAE;AAAA,MACV,IAAI,CAAC,GAAG,EAAE;AAAA,IACZ,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG;AAAA,MAClB,IAAI,CAAC,GAAG,EAAE;AAAA,IACZ,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG;AAAA,MAClB,IAAI,CAAC,GAAG,EAAE;AAAA,IACZ,GAAG;AAAA,MACD,IAAI,CAAC,GAAG,EAAE;AAAA,MACV,IAAI,CAAC,GAAG,EAAE;AAAA,IACZ,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG;AAAA,MACpD,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,IACN,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG;AAAA,MACnC,IAAI;AAAA,MACJ,IAAI;AAAA,IACN,GAAG;AAAA,MACD,IAAI,CAAC,GAAG,EAAE;AAAA,IACZ,GAAG;AAAA,MACD,IAAI,CAAC,GAAG,EAAE;AAAA,IACZ,CAAC;AAAA,IACD,gBAAgB;AAAA,MACd,GAAG,CAAC,GAAG,CAAC;AAAA,MACR,IAAI,CAAC,GAAG,CAAC;AAAA,MACT,IAAI,CAAC,GAAG,CAAC;AAAA,MACT,IAAI,CAAC,GAAG,EAAE;AAAA,MACV,IAAI,CAAC,GAAG,EAAE;AAAA,IACZ;AAAA,IACA,YAA2B,OAAO,SAAS,WAAW,KAAK,MAAM;AAC/D,UAAI,KAAK,aAAa;AACpB,aAAK,MAAM,GAAG;AAAA,MAChB,OAAO;AACL,YAAI,QAAQ,IAAI,MAAM,GAAG;AACzB,cAAM,OAAO;AACb,cAAM;AAAA,MACR;AAAA,IACF,GAAG,YAAY;AAAA,IACf,OAAsB,OAAO,SAAS,MAAM,OAAO;AACjD,UAAI,OAAO,MACT,QAAQ,CAAC,CAAC,GACV,SAAS,CAAC,GACV,SAAS,CAAC,IAAI,GACd,SAAS,CAAC,GACV,QAAQ,KAAK,OACb,SAAS,IACT,WAAW,GACX,SAAS,GACT,aAAa,GACb,SAAS,GACT,MAAM;AACR,UAAI,OAAO,OAAO,MAAM,KAAK,WAAW,CAAC;AACzC,UAAI,SAAS,OAAO,OAAO,KAAK,KAAK;AACrC,UAAI,cAAc;AAAA,QAChB,IAAI,CAAC;AAAA,MACP;AACA,eAAS,KAAK,KAAK,IAAI;AACrB,YAAI,OAAO,UAAU,eAAe,KAAK,KAAK,IAAI,CAAC,GAAG;AACpD,sBAAY,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC;AAAA,QAC/B;AAAA,MACF;AACA,aAAO,SAAS,OAAO,YAAY,EAAE;AACrC,kBAAY,GAAG,QAAQ;AACvB,kBAAY,GAAG,SAAS;AACxB,UAAI,OAAO,OAAO,UAAU,aAAa;AACvC,eAAO,SAAS,CAAC;AAAA,MACnB;AACA,UAAI,QAAQ,OAAO;AACnB,aAAO,KAAK,KAAK;AACjB,UAAI,SAAS,OAAO,WAAW,OAAO,QAAQ;AAC9C,UAAI,OAAO,YAAY,GAAG,eAAe,YAAY;AACnD,aAAK,aAAa,YAAY,GAAG;AAAA,MACnC,OAAO;AACL,aAAK,aAAa,OAAO,eAAe,IAAI,EAAE;AAAA,MAChD;AACA,eAAS,SAAS,GAAG;AACnB,cAAM,SAAS,MAAM,SAAS,IAAI;AAClC,eAAO,SAAS,OAAO,SAAS;AAChC,eAAO,SAAS,OAAO,SAAS;AAAA,MAClC;AACA,aAAO,UAAU,UAAU;AAC3B,eAAS,MAAM;AACb,YAAI;AACJ,gBAAQ,OAAO,IAAI,KAAK,OAAO,IAAI,KAAK;AACxC,YAAI,OAAO,UAAU,UAAU;AAC7B,cAAI,iBAAiB,OAAO;AAC1B,qBAAS;AACT,oBAAQ,OAAO,IAAI;AAAA,UACrB;AACA,kBAAQ,KAAK,SAAS,KAAK,KAAK;AAAA,QAClC;AACA,eAAO;AAAA,MACT;AACA,aAAO,KAAK,KAAK;AACjB,UAAI,QACF,gBACA,OACA,QACA,GACA,GACA,QAAQ,CAAC,GACT,GACA,KACA,UACA;AACF,aAAO,MAAM;AACX,gBAAQ,MAAM,MAAM,SAAS,CAAC;AAC9B,YAAI,KAAK,eAAe,KAAK,GAAG;AAC9B,mBAAS,KAAK,eAAe,KAAK;AAAA,QACpC,OAAO;AACL,cAAI,WAAW,QAAQ,OAAO,UAAU,aAAa;AACnD,qBAAS,IAAI;AAAA,UACf;AACA,mBAAS,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,MAAM;AAAA,QAC9C;AACA,YAAI,OAAO,WAAW,eAAe,CAAC,OAAO,UAAU,CAAC,OAAO,CAAC,GAAG;AACjE,cAAI,SAAS;AACb,qBAAW,CAAC;AACZ,eAAK,KAAK,MAAM,KAAK,GAAG;AACtB,gBAAI,KAAK,WAAW,CAAC,KAAK,IAAI,QAAQ;AACpC,uBAAS,KAAK,MAAM,KAAK,WAAW,CAAC,IAAI,GAAG;AAAA,YAC9C;AAAA,UACF;AACA,cAAI,OAAO,cAAc;AACvB,qBAAS,0BAA0B,WAAW,KAAK,QAAQ,OAAO,aAAa,IAAI,iBAAiB,SAAS,KAAK,IAAI,IAAI,aAAa,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UAC9K,OAAO;AACL,qBAAS,0BAA0B,WAAW,KAAK,mBAAmB,UAAU,MAAM,iBAAiB,OAAO,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UACrJ;AACA,eAAK,WAAW,QAAQ;AAAA,YACtB,MAAM,OAAO;AAAA,YACb,OAAO,KAAK,WAAW,MAAM,KAAK;AAAA,YAClC,MAAM,OAAO;AAAA,YACb,KAAK;AAAA,YACL;AAAA,UACF,CAAC;AAAA,QACH;AACA,YAAI,OAAO,CAAC,aAAa,SAAS,OAAO,SAAS,GAAG;AACnD,gBAAM,IAAI,MAAM,sDAAsD,QAAQ,cAAc,MAAM;AAAA,QACpG;AACA,gBAAQ,OAAO,CAAC,GAAG;AAAA,UACjB,KAAK;AACH,kBAAM,KAAK,MAAM;AACjB,mBAAO,KAAK,OAAO,MAAM;AACzB,mBAAO,KAAK,OAAO,MAAM;AACzB,kBAAM,KAAK,OAAO,CAAC,CAAC;AACpB,qBAAS;AACT,gBAAI,CAAC,gBAAgB;AACnB,uBAAS,OAAO;AAChB,uBAAS,OAAO;AAChB,yBAAW,OAAO;AAClB,sBAAQ,OAAO;AACf,kBAAI,aAAa,GAAG;AAClB;AAAA,cACF;AAAA,YACF,OAAO;AACL,uBAAS;AACT,+BAAiB;AAAA,YACnB;AACA;AAAA,UACF,KAAK;AACH,kBAAM,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC;AACpC,kBAAM,IAAI,OAAO,OAAO,SAAS,GAAG;AACpC,kBAAM,KAAK;AAAA,cACT,YAAY,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cAC/C,WAAW,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,cACrC,cAAc,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cACjD,aAAa,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,YACzC;AACA,gBAAI,QAAQ;AACV,oBAAM,GAAG,QAAQ,CAAC,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE,MAAM,CAAC,GAAG,OAAO,OAAO,SAAS,CAAC,EAAE,MAAM,CAAC,CAAC;AAAA,YACnG;AACA,gBAAI,KAAK,cAAc,MAAM,OAAO,CAAC,QAAQ,QAAQ,UAAU,YAAY,IAAI,OAAO,CAAC,GAAG,QAAQ,MAAM,EAAE,OAAO,IAAI,CAAC;AACtH,gBAAI,OAAO,MAAM,aAAa;AAC5B,qBAAO;AAAA,YACT;AACA,gBAAI,KAAK;AACP,sBAAQ,MAAM,MAAM,GAAG,KAAK,MAAM,CAAC;AACnC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AACjC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AAAA,YACnC;AACA,kBAAM,KAAK,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1C,mBAAO,KAAK,MAAM,CAAC;AACnB,mBAAO,KAAK,MAAM,EAAE;AACpB,uBAAW,MAAM,MAAM,MAAM,SAAS,CAAC,CAAC,EAAE,MAAM,MAAM,SAAS,CAAC,CAAC;AACjE,kBAAM,KAAK,QAAQ;AACnB;AAAA,UACF,KAAK;AACH,mBAAO;AAAA,QACX;AAAA,MACF;AACA,aAAO;AAAA,IACT,GAAG,OAAO;AAAA,EACZ;AACA,MAAI,QAAuB,WAAY;AACrC,QAAI,SAAS;AAAA,MACX,KAAK;AAAA,MACL,YAA2B,OAAO,SAAS,WAAW,KAAK,MAAM;AAC/D,YAAI,KAAK,GAAG,QAAQ;AAClB,eAAK,GAAG,OAAO,WAAW,KAAK,IAAI;AAAA,QACrC,OAAO;AACL,gBAAM,IAAI,MAAM,GAAG;AAAA,QACrB;AAAA,MACF,GAAG,YAAY;AAAA;AAAA,MAEf,UAAyB,OAAO,SAAU,OAAO,IAAI;AACnD,aAAK,KAAK,MAAM,KAAK,MAAM,CAAC;AAC5B,aAAK,SAAS;AACd,aAAK,QAAQ,KAAK,aAAa,KAAK,OAAO;AAC3C,aAAK,WAAW,KAAK,SAAS;AAC9B,aAAK,SAAS,KAAK,UAAU,KAAK,QAAQ;AAC1C,aAAK,iBAAiB,CAAC,SAAS;AAChC,aAAK,SAAS;AAAA,UACZ,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,WAAW;AAAA,UACX,aAAa;AAAA,QACf;AACA,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,QAAQ,CAAC,GAAG,CAAC;AAAA,QAC3B;AACA,aAAK,SAAS;AACd,eAAO;AAAA,MACT,GAAG,UAAU;AAAA;AAAA,MAEb,OAAsB,OAAO,WAAY;AACvC,YAAI,KAAK,KAAK,OAAO,CAAC;AACtB,aAAK,UAAU;AACf,aAAK;AACL,aAAK;AACL,aAAK,SAAS;AACd,aAAK,WAAW;AAChB,YAAI,QAAQ,GAAG,MAAM,iBAAiB;AACtC,YAAI,OAAO;AACT,eAAK;AACL,eAAK,OAAO;AAAA,QACd,OAAO;AACL,eAAK,OAAO;AAAA,QACd;AACA,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,MAAM,CAAC;AAAA,QACrB;AACA,aAAK,SAAS,KAAK,OAAO,MAAM,CAAC;AACjC,eAAO;AAAA,MACT,GAAG,OAAO;AAAA;AAAA,MAEV,OAAsB,OAAO,SAAU,IAAI;AACzC,YAAI,MAAM,GAAG;AACb,YAAI,QAAQ,GAAG,MAAM,eAAe;AACpC,aAAK,SAAS,KAAK,KAAK;AACxB,aAAK,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,OAAO,SAAS,GAAG;AAC5D,aAAK,UAAU;AACf,YAAI,WAAW,KAAK,MAAM,MAAM,eAAe;AAC/C,aAAK,QAAQ,KAAK,MAAM,OAAO,GAAG,KAAK,MAAM,SAAS,CAAC;AACvD,aAAK,UAAU,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,CAAC;AAC7D,YAAI,MAAM,SAAS,GAAG;AACpB,eAAK,YAAY,MAAM,SAAS;AAAA,QAClC;AACA,YAAI,IAAI,KAAK,OAAO;AACpB,aAAK,SAAS;AAAA,UACZ,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,SAAS,MAAM,WAAW,SAAS,SAAS,KAAK,OAAO,eAAe,KAAK,SAAS,SAAS,SAAS,MAAM,MAAM,EAAE,SAAS,MAAM,CAAC,EAAE,SAAS,KAAK,OAAO,eAAe;AAAA,QAC1L;AACA,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,SAAS,GAAG;AAAA,QACrD;AACA,aAAK,SAAS,KAAK,OAAO;AAC1B,eAAO;AAAA,MACT,GAAG,OAAO;AAAA;AAAA,MAEV,MAAqB,OAAO,WAAY;AACtC,aAAK,QAAQ;AACb,eAAO;AAAA,MACT,GAAG,MAAM;AAAA;AAAA,MAET,QAAuB,OAAO,WAAY;AACxC,YAAI,KAAK,QAAQ,iBAAiB;AAChC,eAAK,aAAa;AAAA,QACpB,OAAO;AACL,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,qIAAqI,KAAK,aAAa,GAAG;AAAA,YAChO,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACb,CAAC;AAAA,QACH;AACA,eAAO;AAAA,MACT,GAAG,QAAQ;AAAA;AAAA,MAEX,MAAqB,OAAO,SAAU,GAAG;AACvC,aAAK,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC;AAAA,MAChC,GAAG,MAAM;AAAA;AAAA,MAET,WAA0B,OAAO,WAAY;AAC3C,YAAI,OAAO,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,KAAK,MAAM,MAAM;AACzE,gBAAQ,KAAK,SAAS,KAAK,QAAQ,MAAM,KAAK,OAAO,GAAG,EAAE,QAAQ,OAAO,EAAE;AAAA,MAC7E,GAAG,WAAW;AAAA;AAAA,MAEd,eAA8B,OAAO,WAAY;AAC/C,YAAI,OAAO,KAAK;AAChB,YAAI,KAAK,SAAS,IAAI;AACpB,kBAAQ,KAAK,OAAO,OAAO,GAAG,KAAK,KAAK,MAAM;AAAA,QAChD;AACA,gBAAQ,KAAK,OAAO,GAAG,EAAE,KAAK,KAAK,SAAS,KAAK,QAAQ,KAAK,QAAQ,OAAO,EAAE;AAAA,MACjF,GAAG,eAAe;AAAA;AAAA,MAElB,cAA6B,OAAO,WAAY;AAC9C,YAAI,MAAM,KAAK,UAAU;AACzB,YAAI,IAAI,IAAI,MAAM,IAAI,SAAS,CAAC,EAAE,KAAK,GAAG;AAC1C,eAAO,MAAM,KAAK,cAAc,IAAI,OAAO,IAAI;AAAA,MACjD,GAAG,cAAc;AAAA;AAAA,MAEjB,YAA2B,OAAO,SAAU,OAAO,cAAc;AAC/D,YAAI,OAAO,OAAO;AAClB,YAAI,KAAK,QAAQ,iBAAiB;AAChC,mBAAS;AAAA,YACP,UAAU,KAAK;AAAA,YACf,QAAQ;AAAA,cACN,YAAY,KAAK,OAAO;AAAA,cACxB,WAAW,KAAK;AAAA,cAChB,cAAc,KAAK,OAAO;AAAA,cAC1B,aAAa,KAAK,OAAO;AAAA,YAC3B;AAAA,YACA,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,SAAS,KAAK;AAAA,YACd,SAAS,KAAK;AAAA,YACd,QAAQ,KAAK;AAAA,YACb,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,QAAQ,KAAK;AAAA,YACb,IAAI,KAAK;AAAA,YACT,gBAAgB,KAAK,eAAe,MAAM,CAAC;AAAA,YAC3C,MAAM,KAAK;AAAA,UACb;AACA,cAAI,KAAK,QAAQ,QAAQ;AACvB,mBAAO,OAAO,QAAQ,KAAK,OAAO,MAAM,MAAM,CAAC;AAAA,UACjD;AAAA,QACF;AACA,gBAAQ,MAAM,CAAC,EAAE,MAAM,iBAAiB;AACxC,YAAI,OAAO;AACT,eAAK,YAAY,MAAM;AAAA,QACzB;AACA,aAAK,SAAS;AAAA,UACZ,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,QAAQ,MAAM,MAAM,SAAS,CAAC,EAAE,SAAS,MAAM,MAAM,SAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,SAAS,KAAK,OAAO,cAAc,MAAM,CAAC,EAAE;AAAA,QAC/I;AACA,aAAK,UAAU,MAAM,CAAC;AACtB,aAAK,SAAS,MAAM,CAAC;AACrB,aAAK,UAAU;AACf,aAAK,SAAS,KAAK,OAAO;AAC1B,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,QAAQ,CAAC,KAAK,QAAQ,KAAK,UAAU,KAAK,MAAM;AAAA,QAC9D;AACA,aAAK,QAAQ;AACb,aAAK,aAAa;AAClB,aAAK,SAAS,KAAK,OAAO,MAAM,MAAM,CAAC,EAAE,MAAM;AAC/C,aAAK,WAAW,MAAM,CAAC;AACvB,gBAAQ,KAAK,cAAc,KAAK,MAAM,KAAK,IAAI,MAAM,cAAc,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC;AACtH,YAAI,KAAK,QAAQ,KAAK,QAAQ;AAC5B,eAAK,OAAO;AAAA,QACd;AACA,YAAI,OAAO;AACT,iBAAO;AAAA,QACT,WAAW,KAAK,YAAY;AAC1B,mBAAS,KAAK,QAAQ;AACpB,iBAAK,CAAC,IAAI,OAAO,CAAC;AAAA,UACpB;AACA,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT,GAAG,YAAY;AAAA;AAAA,MAEf,MAAqB,OAAO,WAAY;AACtC,YAAI,KAAK,MAAM;AACb,iBAAO,KAAK;AAAA,QACd;AACA,YAAI,CAAC,KAAK,QAAQ;AAChB,eAAK,OAAO;AAAA,QACd;AACA,YAAI,OAAO,OAAO,WAAW;AAC7B,YAAI,CAAC,KAAK,OAAO;AACf,eAAK,SAAS;AACd,eAAK,QAAQ;AAAA,QACf;AACA,YAAI,QAAQ,KAAK,cAAc;AAC/B,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,sBAAY,KAAK,OAAO,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC,CAAC;AAClD,cAAI,cAAc,CAAC,SAAS,UAAU,CAAC,EAAE,SAAS,MAAM,CAAC,EAAE,SAAS;AAClE,oBAAQ;AACR,oBAAQ;AACR,gBAAI,KAAK,QAAQ,iBAAiB;AAChC,sBAAQ,KAAK,WAAW,WAAW,MAAM,CAAC,CAAC;AAC3C,kBAAI,UAAU,OAAO;AACnB,uBAAO;AAAA,cACT,WAAW,KAAK,YAAY;AAC1B,wBAAQ;AACR;AAAA,cACF,OAAO;AACL,uBAAO;AAAA,cACT;AAAA,YACF,WAAW,CAAC,KAAK,QAAQ,MAAM;AAC7B;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,YAAI,OAAO;AACT,kBAAQ,KAAK,WAAW,OAAO,MAAM,KAAK,CAAC;AAC3C,cAAI,UAAU,OAAO;AACnB,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT;AACA,YAAI,KAAK,WAAW,IAAI;AACtB,iBAAO,KAAK;AAAA,QACd,OAAO;AACL,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,2BAA2B,KAAK,aAAa,GAAG;AAAA,YACtH,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACb,CAAC;AAAA,QACH;AAAA,MACF,GAAG,MAAM;AAAA;AAAA,MAET,KAAoB,OAAO,SAAS,MAAM;AACxC,YAAI,IAAI,KAAK,KAAK;AAClB,YAAI,GAAG;AACL,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO,KAAK,IAAI;AAAA,QAClB;AAAA,MACF,GAAG,KAAK;AAAA;AAAA,MAER,OAAsB,OAAO,SAAS,MAAM,WAAW;AACrD,aAAK,eAAe,KAAK,SAAS;AAAA,MACpC,GAAG,OAAO;AAAA;AAAA,MAEV,UAAyB,OAAO,SAAS,WAAW;AAClD,YAAI,IAAI,KAAK,eAAe,SAAS;AACrC,YAAI,IAAI,GAAG;AACT,iBAAO,KAAK,eAAe,IAAI;AAAA,QACjC,OAAO;AACL,iBAAO,KAAK,eAAe,CAAC;AAAA,QAC9B;AAAA,MACF,GAAG,UAAU;AAAA;AAAA,MAEb,eAA8B,OAAO,SAAS,gBAAgB;AAC5D,YAAI,KAAK,eAAe,UAAU,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,GAAG;AACrF,iBAAO,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC,EAAE;AAAA,QAC9E,OAAO;AACL,iBAAO,KAAK,WAAW,SAAS,EAAE;AAAA,QACpC;AAAA,MACF,GAAG,eAAe;AAAA;AAAA,MAElB,UAAyB,OAAO,SAAS,SAAS,GAAG;AACnD,YAAI,KAAK,eAAe,SAAS,IAAI,KAAK,IAAI,KAAK,CAAC;AACpD,YAAI,KAAK,GAAG;AACV,iBAAO,KAAK,eAAe,CAAC;AAAA,QAC9B,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF,GAAG,UAAU;AAAA;AAAA,MAEb,WAA0B,OAAO,SAAS,UAAU,WAAW;AAC7D,aAAK,MAAM,SAAS;AAAA,MACtB,GAAG,WAAW;AAAA;AAAA,MAEd,gBAA+B,OAAO,SAAS,iBAAiB;AAC9D,eAAO,KAAK,eAAe;AAAA,MAC7B,GAAG,gBAAgB;AAAA,MACnB,SAAS;AAAA,QACP,oBAAoB;AAAA,MACtB;AAAA,MACA,eAA8B,OAAO,SAAS,UAAU,IAAI,KAAK,2BAA2B,UAAU;AACpG,YAAI,UAAU;AACd,gBAAQ,2BAA2B;AAAA,UACjC,KAAK;AACH;AAAA,UACF,KAAK;AACH;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,UAAU,WAAW;AAC1B,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,UAAU,WAAW;AAC1B,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,UAAU,qBAAqB;AACpC;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,UAAU,WAAW;AAC1B,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,UAAU,WAAW;AAC1B,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,UAAU,gBAAgB;AAC/B,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,UAAU,MAAM;AACrB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,UAAU,MAAM;AACrB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,UAAU,YAAY;AAC3B,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd;AAAA,UACF,KAAK;AACH,iBAAK,UAAU,QAAQ;AACvB;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,QACJ;AAAA,MACF,GAAG,WAAW;AAAA,MACd,OAAO,CAAC,wBAAwB,uBAAuB,iBAAiB,iBAAiB,iBAAiB,kBAAkB,iBAAiB,yBAAyB,yBAAyB,yBAAyB,yBAAyB,0BAA0B,YAAY,gBAAgB,wBAAwB,iCAAiC,kBAAkB,kBAAkB,YAAY,aAAa,gBAAgB,eAAe,YAAY,sCAAsC,YAAY,kLAAkL,aAAa,aAAa,eAAe,YAAY,YAAY,mBAAmB,WAAW,YAAY,WAAW,WAAW,YAAY,WAAW,cAAc,YAAY,WAAW,WAAW,gBAAgB,aAAa,WAAW,SAAS;AAAA,MACr6B,YAAY;AAAA,QACV,cAAc;AAAA,UACZ,SAAS,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,UAC7I,aAAa;AAAA,QACf;AAAA,QACA,QAAQ;AAAA,UACN,SAAS,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,UAC5I,aAAa;AAAA,QACf;AAAA,QACA,kBAAkB;AAAA,UAChB,SAAS,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,UACzI,aAAa;AAAA,QACf;AAAA,QACA,aAAa;AAAA,UACX,SAAS,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,UACpJ,aAAa;AAAA,QACf;AAAA,QACA,uBAAuB;AAAA,UACrB,SAAS,CAAC,IAAI,EAAE;AAAA,UAChB,aAAa;AAAA,QACf;AAAA,QACA,aAAa;AAAA,UACX,SAAS,CAAC,EAAE;AAAA,UACZ,aAAa;AAAA,QACf;AAAA,QACA,aAAa;AAAA,UACX,SAAS,CAAC,CAAC;AAAA,UACX,aAAa;AAAA,QACf;AAAA,QACA,SAAS;AAAA,UACP,SAAS,CAAC;AAAA,UACV,aAAa;AAAA,QACf;AAAA,QACA,aAAa;AAAA,UACX,SAAS,CAAC;AAAA,UACV,aAAa;AAAA,QACf;AAAA,QACA,UAAU;AAAA,UACR,SAAS,CAAC,IAAI,EAAE;AAAA,UAChB,aAAa;AAAA,QACf;AAAA,QACA,WAAW;AAAA,UACT,SAAS,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,UACrI,aAAa;AAAA,QACf;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT,EAAE;AACF,UAAQ,QAAQ;AAChB,WAAS,SAAS;AAChB,SAAK,KAAK,CAAC;AAAA,EACb;AACA,SAAO,QAAQ,QAAQ;AACvB,SAAO,YAAY;AACnB,UAAQ,SAAS;AACjB,SAAO,IAAI,OAAO;AACpB,EAAE;AACF,OAAO,SAAS;AAChB,IAAI,kBAAkB;AAGtB,SAAS,UAAU,MAAM;AACvB,SAAO,KAAK,SAAS;AACvB;AACA,OAAO,WAAW,WAAW;AAC7B,SAAS,eAAe,MAAM;AAC5B,SAAO,KAAK,SAAS;AACvB;AACA,OAAO,gBAAgB,gBAAgB;AACvC,SAAS,iBAAiB,MAAM;AAC9B,SAAO,KAAK,SAAS;AACvB;AACA,OAAO,kBAAkB,kBAAkB;AAG3C,IAAI,kCAAkC,MAAM;AAAA,EAC1C,YAAY,aAAa;AACvB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,OAAO;AACL,WAAO,MAAM,iCAAiC;AAAA,EAChD;AAAA,EACA,gBAAgB,OAAO,UAAU;AAC/B,QAAI,CAAC,KAAK,aAAa;AACrB,aAAO;AAAA,QACL,OAAO,MAAM,OAAO,CAAC,KAAK,QAAQ,KAAK,IAAI,IAAI,QAAQ,GAAG,GAAG,CAAC,IAAI;AAAA,QAClE,QAAQ;AAAA,MACV;AAAA,IACF;AACA,UAAM,YAAY;AAAA,MAChB,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AACA,UAAM,OAAO,KAAK,YAAY,OAAO,GAAG,EAAE,KAAK,cAAc,QAAQ,EAAE,KAAK,aAAa,QAAQ;AACjG,eAAW,KAAK,OAAO;AACrB,YAAM,OAAO,uBAAuB,MAAM,GAAG,CAAC;AAC9C,YAAM,QAAQ,OAAO,KAAK,QAAQ,EAAE,SAAS;AAC7C,YAAM,SAAS,OAAO,KAAK,SAAS;AACpC,gBAAU,QAAQ,KAAK,IAAI,UAAU,OAAO,KAAK;AACjD,gBAAU,SAAS,KAAK,IAAI,UAAU,QAAQ,MAAM;AAAA,IACtD;AACA,SAAK,OAAO;AACZ,WAAO;AAAA,EACT;AACF;AAMA,IAAI,gCAAgC;AACpC,IAAI,0CAA0C;AAC9C,IAAI,WAAW,MAAM;AAAA,EACnB,YAAY,YAAY,OAAO,yBAAyB,iBAAiB;AACvE,SAAK,aAAa;AAClB,SAAK,QAAQ;AACb,SAAK,0BAA0B;AAC/B,SAAK,kBAAkB;AACvB,SAAK,eAAe;AAAA,MAClB,GAAG;AAAA,MACH,GAAG;AAAA,MACH,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AACA,SAAK,eAAe;AACpB,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,eAAe;AACpB,SAAK,eAAe;AACpB,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AACvB,SAAK,QAAQ,CAAC,GAAG,EAAE;AACnB,SAAK,eAAe;AAAA,MAClB,GAAG;AAAA,MACH,GAAG;AAAA,MACH,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AACA,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,OAAO;AACL,WAAO,MAAM,UAAU;AAAA,EACzB;AAAA,EACA,SAAS,OAAO;AACd,SAAK,QAAQ;AACb,QAAI,KAAK,iBAAiB,UAAU,KAAK,iBAAiB,SAAS;AACjE,WAAK,aAAa,SAAS,MAAM,CAAC,IAAI,MAAM,CAAC;AAAA,IAC/C,OAAO;AACL,WAAK,aAAa,QAAQ,MAAM,CAAC,IAAI,MAAM,CAAC;AAAA,IAC9C;AACA,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,WAAW;AACT,WAAO,CAAC,KAAK,MAAM,CAAC,IAAI,KAAK,cAAc,KAAK,MAAM,CAAC,IAAI,KAAK,YAAY;AAAA,EAC9E;AAAA,EACA,gBAAgB,cAAc;AAC5B,SAAK,eAAe;AACpB,SAAK,SAAS,KAAK,KAAK;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAChB,UAAM,QAAQ,KAAK,SAAS;AAC5B,WAAO,KAAK,IAAI,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC,IAAI,KAAK,cAAc,EAAE;AAAA,EAC9D;AAAA,EACA,sBAAsB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,oBAAoB;AAClB,WAAO,KAAK,wBAAwB,gBAAgB,KAAK,cAAc,EAAE,IAAI,UAAQ,KAAK,SAAS,CAAC,GAAG,KAAK,WAAW,aAAa;AAAA,EACtI;AAAA,EACA,mCAAmC;AACjC,QAAI,gCAAgC,KAAK,gBAAgB,IAAI,KAAK,eAAe,GAAG;AAClF,WAAK,eAAe,KAAK,MAAM,gCAAgC,KAAK,gBAAgB,IAAI,CAAC;AAAA,IAC3F;AACA,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,kCAAkC,gBAAgB;AAChD,QAAI,kBAAkB,eAAe;AACrC,QAAI,KAAK,WAAW,gBAAgB,kBAAkB,KAAK,WAAW,eAAe;AACnF,yBAAmB,KAAK,WAAW;AACnC,WAAK,eAAe;AAAA,IACtB;AACA,QAAI,KAAK,WAAW,WAAW;AAC7B,YAAM,gBAAgB,KAAK,kBAAkB;AAC7C,YAAM,aAAa,0CAA0C,eAAe;AAC5E,WAAK,eAAe,KAAK,IAAI,cAAc,QAAQ,GAAG,UAAU;AAChE,YAAM,iBAAiB,cAAc,SAAS,KAAK,WAAW,eAAe;AAC7E,WAAK,kBAAkB,cAAc;AACrC,UAAI,kBAAkB,iBAAiB;AACrC,2BAAmB;AACnB,aAAK,YAAY;AAAA,MACnB;AAAA,IACF;AACA,QAAI,KAAK,WAAW,YAAY,mBAAmB,KAAK,WAAW,YAAY;AAC7E,WAAK,WAAW;AAChB,yBAAmB,KAAK,WAAW;AAAA,IACrC;AACA,QAAI,KAAK,WAAW,aAAa,KAAK,OAAO;AAC3C,YAAM,gBAAgB,KAAK,wBAAwB,gBAAgB,CAAC,KAAK,KAAK,GAAG,KAAK,WAAW,aAAa;AAC9G,YAAM,iBAAiB,cAAc,SAAS,KAAK,WAAW,eAAe;AAC7E,WAAK,kBAAkB,cAAc;AACrC,UAAI,kBAAkB,iBAAiB;AACrC,2BAAmB;AACnB,aAAK,YAAY;AAAA,MACnB;AAAA,IACF;AACA,SAAK,aAAa,QAAQ,eAAe;AACzC,SAAK,aAAa,SAAS,eAAe,SAAS;AAAA,EACrD;AAAA,EACA,8BAA8B,gBAAgB;AAC5C,QAAI,iBAAiB,eAAe;AACpC,QAAI,KAAK,WAAW,gBAAgB,iBAAiB,KAAK,WAAW,eAAe;AAClF,wBAAkB,KAAK,WAAW;AAClC,WAAK,eAAe;AAAA,IACtB;AACA,QAAI,KAAK,WAAW,WAAW;AAC7B,YAAM,gBAAgB,KAAK,kBAAkB;AAC7C,YAAM,aAAa,0CAA0C,eAAe;AAC5E,WAAK,eAAe,KAAK,IAAI,cAAc,SAAS,GAAG,UAAU;AACjE,YAAM,gBAAgB,cAAc,QAAQ,KAAK,WAAW,eAAe;AAC3E,UAAI,iBAAiB,gBAAgB;AACnC,0BAAkB;AAClB,aAAK,YAAY;AAAA,MACnB;AAAA,IACF;AACA,QAAI,KAAK,WAAW,YAAY,kBAAkB,KAAK,WAAW,YAAY;AAC5E,WAAK,WAAW;AAChB,wBAAkB,KAAK,WAAW;AAAA,IACpC;AACA,QAAI,KAAK,WAAW,aAAa,KAAK,OAAO;AAC3C,YAAM,gBAAgB,KAAK,wBAAwB,gBAAgB,CAAC,KAAK,KAAK,GAAG,KAAK,WAAW,aAAa;AAC9G,YAAM,gBAAgB,cAAc,SAAS,KAAK,WAAW,eAAe;AAC5E,WAAK,kBAAkB,cAAc;AACrC,UAAI,iBAAiB,gBAAgB;AACnC,0BAAkB;AAClB,aAAK,YAAY;AAAA,MACnB;AAAA,IACF;AACA,SAAK,aAAa,QAAQ,eAAe,QAAQ;AACjD,SAAK,aAAa,SAAS,eAAe;AAAA,EAC5C;AAAA,EACA,eAAe,gBAAgB;AAC7B,QAAI,KAAK,iBAAiB,UAAU,KAAK,iBAAiB,SAAS;AACjE,WAAK,8BAA8B,cAAc;AAAA,IACnD,OAAO;AACL,WAAK,kCAAkC,cAAc;AAAA,IACvD;AACA,SAAK,iBAAiB;AACtB,WAAO;AAAA,MACL,OAAO,KAAK,aAAa;AAAA,MACzB,QAAQ,KAAK,aAAa;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,iBAAiB,OAAO;AACtB,SAAK,aAAa,IAAI,MAAM;AAC5B,SAAK,aAAa,IAAI,MAAM;AAAA,EAC9B;AAAA,EACA,iCAAiC;AAC/B,UAAM,kBAAkB,CAAC;AACzB,QAAI,KAAK,cAAc;AACrB,YAAM,IAAI,KAAK,aAAa,IAAI,KAAK,aAAa,QAAQ,KAAK,WAAW,gBAAgB;AAC1F,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,YAAY,CAAC,aAAa,YAAY;AAAA,QACtC,MAAM,CAAC;AAAA,UACL,MAAM,KAAK,CAAC,IAAI,KAAK,aAAa,CAAC,MAAM,CAAC,IAAI,KAAK,aAAa,IAAI,KAAK,aAAa,MAAM;AAAA,UAC5F,YAAY,KAAK,gBAAgB;AAAA,UACjC,aAAa,KAAK,WAAW;AAAA,QAC/B,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,QAAI,KAAK,WAAW;AAClB,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,YAAY,CAAC,aAAa,OAAO;AAAA,QACjC,MAAM,KAAK,cAAc,EAAE,IAAI,WAAS;AAAA,UACtC,MAAM,KAAK,SAAS;AAAA,UACpB,GAAG,KAAK,aAAa,IAAI,KAAK,aAAa,SAAS,KAAK,YAAY,KAAK,WAAW,eAAe,MAAM,KAAK,WAAW,KAAK,WAAW,aAAa,MAAM,KAAK,eAAe,KAAK,WAAW,gBAAgB;AAAA,UACjN,GAAG,KAAK,cAAc,IAAI;AAAA,UAC1B,MAAM,KAAK,gBAAgB;AAAA,UAC3B,UAAU,KAAK,WAAW;AAAA,UAC1B,UAAU;AAAA,UACV,aAAa;AAAA,UACb,eAAe;AAAA,QACjB,EAAE;AAAA,MACJ,CAAC;AAAA,IACH;AACA,QAAI,KAAK,UAAU;AACjB,YAAM,IAAI,KAAK,aAAa,IAAI,KAAK,aAAa,SAAS,KAAK,eAAe,KAAK,WAAW,gBAAgB;AAC/G,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,YAAY,CAAC,aAAa,OAAO;AAAA,QACjC,MAAM,KAAK,cAAc,EAAE,IAAI,WAAS;AAAA,UACtC,MAAM,KAAK,CAAC,IAAI,KAAK,cAAc,IAAI,CAAC,MAAM,IAAI,KAAK,WAAW,UAAU,IAAI,KAAK,cAAc,IAAI,CAAC;AAAA,UACxG,YAAY,KAAK,gBAAgB;AAAA,UACjC,aAAa,KAAK,WAAW;AAAA,QAC/B,EAAE;AAAA,MACJ,CAAC;AAAA,IACH;AACA,QAAI,KAAK,WAAW;AAClB,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,YAAY,CAAC,aAAa,OAAO;AAAA,QACjC,MAAM,CAAC;AAAA,UACL,MAAM,KAAK;AAAA,UACX,GAAG,KAAK,aAAa,IAAI,KAAK,WAAW;AAAA,UACzC,GAAG,KAAK,aAAa,IAAI,KAAK,aAAa,SAAS;AAAA,UACpD,MAAM,KAAK,gBAAgB;AAAA,UAC3B,UAAU,KAAK,WAAW;AAAA,UAC1B,UAAU;AAAA,UACV,aAAa;AAAA,UACb,eAAe;AAAA,QACjB,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA,EACA,mCAAmC;AACjC,UAAM,kBAAkB,CAAC;AACzB,QAAI,KAAK,cAAc;AACrB,YAAM,IAAI,KAAK,aAAa,IAAI,KAAK,WAAW,gBAAgB;AAChE,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,YAAY,CAAC,eAAe,WAAW;AAAA,QACvC,MAAM,CAAC;AAAA,UACL,MAAM,KAAK,KAAK,aAAa,CAAC,IAAI,CAAC,MAAM,KAAK,aAAa,IAAI,KAAK,aAAa,KAAK,IAAI,CAAC;AAAA,UAC3F,YAAY,KAAK,gBAAgB;AAAA,UACjC,aAAa,KAAK,WAAW;AAAA,QAC/B,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,QAAI,KAAK,WAAW;AAClB,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,YAAY,CAAC,eAAe,OAAO;AAAA,QACnC,MAAM,KAAK,cAAc,EAAE,IAAI,WAAS;AAAA,UACtC,MAAM,KAAK,SAAS;AAAA,UACpB,GAAG,KAAK,cAAc,IAAI;AAAA,UAC1B,GAAG,KAAK,aAAa,IAAI,KAAK,WAAW,gBAAgB,KAAK,WAAW,KAAK,WAAW,aAAa,MAAM,KAAK,eAAe,KAAK,WAAW,gBAAgB;AAAA,UAChK,MAAM,KAAK,gBAAgB;AAAA,UAC3B,UAAU,KAAK,WAAW;AAAA,UAC1B,UAAU;AAAA,UACV,aAAa;AAAA,UACb,eAAe;AAAA,QACjB,EAAE;AAAA,MACJ,CAAC;AAAA,IACH;AACA,QAAI,KAAK,UAAU;AACjB,YAAM,IAAI,KAAK,aAAa,KAAK,KAAK,eAAe,KAAK,WAAW,gBAAgB;AACrF,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,YAAY,CAAC,eAAe,OAAO;AAAA,QACnC,MAAM,KAAK,cAAc,EAAE,IAAI,WAAS;AAAA,UACtC,MAAM,KAAK,KAAK,cAAc,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,cAAc,IAAI,CAAC,IAAI,IAAI,KAAK,WAAW,UAAU;AAAA,UACxG,YAAY,KAAK,gBAAgB;AAAA,UACjC,aAAa,KAAK,WAAW;AAAA,QAC/B,EAAE;AAAA,MACJ,CAAC;AAAA,IACH;AACA,QAAI,KAAK,WAAW;AAClB,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,YAAY,CAAC,eAAe,OAAO;AAAA,QACnC,MAAM,CAAC;AAAA,UACL,MAAM,KAAK;AAAA,UACX,GAAG,KAAK,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,KAAK;AAAA,UACrD,GAAG,KAAK,aAAa,IAAI,KAAK,aAAa,SAAS,KAAK,WAAW,eAAe,KAAK;AAAA,UACxF,MAAM,KAAK,gBAAgB;AAAA,UAC3B,UAAU,KAAK,WAAW;AAAA,UAC1B,UAAU;AAAA,UACV,aAAa;AAAA,UACb,eAAe;AAAA,QACjB,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA,EACA,gCAAgC;AAC9B,UAAM,kBAAkB,CAAC;AACzB,QAAI,KAAK,cAAc;AACrB,YAAM,IAAI,KAAK,aAAa,IAAI,KAAK,aAAa,SAAS,KAAK,WAAW,gBAAgB;AAC3F,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,YAAY,CAAC,YAAY,WAAW;AAAA,QACpC,MAAM,CAAC;AAAA,UACL,MAAM,KAAK,KAAK,aAAa,CAAC,IAAI,CAAC,MAAM,KAAK,aAAa,IAAI,KAAK,aAAa,KAAK,IAAI,CAAC;AAAA,UAC3F,YAAY,KAAK,gBAAgB;AAAA,UACjC,aAAa,KAAK,WAAW;AAAA,QAC/B,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,QAAI,KAAK,WAAW;AAClB,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,YAAY,CAAC,YAAY,OAAO;AAAA,QAChC,MAAM,KAAK,cAAc,EAAE,IAAI,WAAS;AAAA,UACtC,MAAM,KAAK,SAAS;AAAA,UACpB,GAAG,KAAK,cAAc,IAAI;AAAA,UAC1B,GAAG,KAAK,aAAa,KAAK,KAAK,YAAY,KAAK,kBAAkB,KAAK,WAAW,eAAe,IAAI,KAAK,KAAK,WAAW;AAAA,UAC1H,MAAM,KAAK,gBAAgB;AAAA,UAC3B,UAAU,KAAK,WAAW;AAAA,UAC1B,UAAU;AAAA,UACV,aAAa;AAAA,UACb,eAAe;AAAA,QACjB,EAAE;AAAA,MACJ,CAAC;AAAA,IACH;AACA,QAAI,KAAK,UAAU;AACjB,YAAM,IAAI,KAAK,aAAa;AAC5B,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,YAAY,CAAC,YAAY,OAAO;AAAA,QAChC,MAAM,KAAK,cAAc,EAAE,IAAI,WAAS;AAAA,UACtC,MAAM,KAAK,KAAK,cAAc,IAAI,CAAC,IAAI,IAAI,KAAK,aAAa,UAAU,KAAK,eAAe,KAAK,WAAW,gBAAgB,EAAE,MAAM,KAAK,cAAc,IAAI,CAAC,IAAI,IAAI,KAAK,aAAa,SAAS,KAAK,WAAW,cAAc,KAAK,eAAe,KAAK,WAAW,gBAAgB,EAAE;AAAA,UAClR,YAAY,KAAK,gBAAgB;AAAA,UACjC,aAAa,KAAK,WAAW;AAAA,QAC/B,EAAE;AAAA,MACJ,CAAC;AAAA,IACH;AACA,QAAI,KAAK,WAAW;AAClB,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,YAAY,CAAC,YAAY,OAAO;AAAA,QAChC,MAAM,CAAC;AAAA,UACL,MAAM,KAAK;AAAA,UACX,GAAG,KAAK,aAAa,IAAI,KAAK,aAAa,QAAQ;AAAA,UACnD,GAAG,KAAK,aAAa,IAAI,KAAK,WAAW;AAAA,UACzC,MAAM,KAAK,gBAAgB;AAAA,UAC3B,UAAU,KAAK,WAAW;AAAA,UAC1B,UAAU;AAAA,UACV,aAAa;AAAA,UACb,eAAe;AAAA,QACjB,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA,EACA,sBAAsB;AACpB,QAAI,KAAK,iBAAiB,QAAQ;AAChC,aAAO,KAAK,+BAA+B;AAAA,IAC7C;AACA,QAAI,KAAK,iBAAiB,SAAS;AACjC,YAAM,MAAM,0CAA0C;AAAA,IACxD;AACA,QAAI,KAAK,iBAAiB,UAAU;AAClC,aAAO,KAAK,iCAAiC;AAAA,IAC/C;AACA,QAAI,KAAK,iBAAiB,OAAO;AAC/B,aAAO,KAAK,8BAA8B;AAAA,IAC5C;AACA,WAAO,CAAC;AAAA,EACV;AACF;AAGA,IAAI,WAAW,cAAc,SAAS;AAAA,EACpC,OAAO;AACL,WAAO,MAAM,UAAU;AAAA,EACzB;AAAA,EACA,YAAY,YAAY,iBAAiB,YAAY,OAAO,yBAAyB;AACnF,UAAM,YAAY,OAAO,yBAAyB,eAAe;AACjE,SAAK,aAAa;AAClB,SAAK,QAAQ,KAAU,EAAE,OAAO,KAAK,UAAU,EAAE,MAAM,KAAK,SAAS,CAAC;AAAA,EACxE;AAAA,EACA,SAAS,OAAO;AACd,UAAM,SAAS,KAAK;AAAA,EACtB;AAAA,EACA,mBAAmB;AACjB,SAAK,QAAQ,KAAU,EAAE,OAAO,KAAK,UAAU,EAAE,MAAM,KAAK,SAAS,CAAC,EAAE,aAAa,CAAC,EAAE,aAAa,CAAC,EAAE,MAAM,GAAG;AACjH,QAAI,MAAM,2CAA2C,KAAK,YAAY,KAAK,SAAS,CAAC;AAAA,EACvF;AAAA,EACA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,cAAc,OAAO;AACnB,WAAO,KAAK,MAAM,KAAK,KAAK,KAAK,SAAS,EAAE,CAAC;AAAA,EAC/C;AACF;AAIA,IAAI,aAAa,cAAc,SAAS;AAAA,EACtC,OAAO;AACL,WAAO,MAAM,YAAY;AAAA,EAC3B;AAAA,EACA,YAAY,YAAY,iBAAiB,QAAQ,OAAO,yBAAyB;AAC/E,UAAM,YAAY,OAAO,yBAAyB,eAAe;AACjE,SAAK,SAAS;AACd,SAAK,QAAQ,OAAY,EAAE,OAAO,KAAK,MAAM,EAAE,MAAM,KAAK,SAAS,CAAC;AAAA,EACtE;AAAA,EACA,gBAAgB;AACd,WAAO,KAAK,MAAM,MAAM;AAAA,EAC1B;AAAA,EACA,mBAAmB;AACjB,UAAM,SAAS,CAAC,GAAG,KAAK,MAAM;AAC9B,QAAI,KAAK,iBAAiB,QAAQ;AAChC,aAAO,QAAQ;AAAA,IACjB;AACA,SAAK,QAAQ,OAAY,EAAE,OAAO,MAAM,EAAE,MAAM,KAAK,SAAS,CAAC;AAAA,EACjE;AAAA,EACA,cAAc,OAAO;AACnB,WAAO,KAAK,MAAM,KAAK;AAAA,EACzB;AACF;AAGA,SAAS,QAAQ,MAAM,YAAY,iBAAiB,cAAc;AAChE,QAAM,0BAA0B,IAAI,gCAAgC,YAAY;AAChF,MAAI,eAAe,IAAI,GAAG;AACxB,WAAO,IAAI,SAAS,YAAY,iBAAiB,KAAK,YAAY,KAAK,OAAO,uBAAuB;AAAA,EACvG;AACA,SAAO,IAAI,WAAW,YAAY,iBAAiB,CAAC,KAAK,KAAK,KAAK,GAAG,GAAG,KAAK,OAAO,uBAAuB;AAC9G;AACA,OAAO,SAAS,SAAS;AAGzB,IAAI,aAAa,MAAM;AAAA,EACrB,YAAY,yBAAyB,aAAa,WAAW,kBAAkB;AAC7E,SAAK,0BAA0B;AAC/B,SAAK,cAAc;AACnB,SAAK,YAAY;AACjB,SAAK,mBAAmB;AACxB,SAAK,eAAe;AAAA,MAClB,GAAG;AAAA,MACH,GAAG;AAAA,MACH,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AACA,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,OAAO;AACL,WAAO,MAAM,YAAY;AAAA,EAC3B;AAAA,EACA,iBAAiB,OAAO;AACtB,SAAK,aAAa,IAAI,MAAM;AAC5B,SAAK,aAAa,IAAI,MAAM;AAAA,EAC9B;AAAA,EACA,eAAe,gBAAgB;AAC7B,UAAM,iBAAiB,KAAK,wBAAwB,gBAAgB,CAAC,KAAK,UAAU,KAAK,GAAG,KAAK,YAAY,aAAa;AAC1H,UAAM,gBAAgB,KAAK,IAAI,eAAe,OAAO,eAAe,KAAK;AACzE,UAAM,iBAAiB,eAAe,SAAS,IAAI,KAAK,YAAY;AACpE,QAAI,eAAe,SAAS,iBAAiB,eAAe,UAAU,kBAAkB,KAAK,YAAY,aAAa,KAAK,UAAU,OAAO;AAC1I,WAAK,aAAa,QAAQ;AAC1B,WAAK,aAAa,SAAS;AAC3B,WAAK,iBAAiB;AAAA,IACxB;AACA,WAAO;AAAA,MACL,OAAO,KAAK,aAAa;AAAA,MACzB,QAAQ,KAAK,aAAa;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,sBAAsB;AACpB,UAAM,eAAe,CAAC;AACtB,QAAI,KAAK,gBAAgB;AACvB,mBAAa,KAAK;AAAA,QAChB,YAAY,CAAC,aAAa;AAAA,QAC1B,MAAM;AAAA,QACN,MAAM,CAAC;AAAA,UACL,UAAU,KAAK,YAAY;AAAA,UAC3B,MAAM,KAAK,UAAU;AAAA,UACrB,aAAa;AAAA,UACb,eAAe;AAAA,UACf,GAAG,KAAK,aAAa,IAAI,KAAK,aAAa,QAAQ;AAAA,UACnD,GAAG,KAAK,aAAa,IAAI,KAAK,aAAa,SAAS;AAAA,UACpD,MAAM,KAAK,iBAAiB;AAAA,UAC5B,UAAU;AAAA,QACZ,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AACF;AACA,SAAS,uBAAuB,aAAa,WAAW,kBAAkB,cAAc;AACtF,QAAM,0BAA0B,IAAI,gCAAgC,YAAY;AAChF,SAAO,IAAI,WAAW,yBAAyB,aAAa,WAAW,gBAAgB;AACzF;AACA,OAAO,wBAAwB,wBAAwB;AAIvD,IAAI,WAAW,MAAM;AAAA,EACnB,YAAY,UAAU,OAAO,OAAO,aAAa,YAAY;AAC3D,SAAK,WAAW;AAChB,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,cAAc;AACnB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,OAAO;AACL,WAAO,MAAM,UAAU;AAAA,EACzB;AAAA,EACA,qBAAqB;AACnB,UAAM,YAAY,KAAK,SAAS,KAAK,IAAI,OAAK,CAAC,KAAK,MAAM,cAAc,EAAE,CAAC,CAAC,GAAG,KAAK,MAAM,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC;AAC9G,QAAI;AACJ,QAAI,KAAK,gBAAgB,cAAc;AACrC,aAAO,aAAK,EAAE,EAAE,OAAK,EAAE,CAAC,CAAC,EAAE,EAAE,OAAK,EAAE,CAAC,CAAC,EAAE,SAAS;AAAA,IACnD,OAAO;AACL,aAAO,aAAK,EAAE,EAAE,OAAK,EAAE,CAAC,CAAC,EAAE,EAAE,OAAK,EAAE,CAAC,CAAC,EAAE,SAAS;AAAA,IACnD;AACA,QAAI,CAAC,MAAM;AACT,aAAO,CAAC;AAAA,IACV;AACA,WAAO,CAAC;AAAA,MACN,YAAY,CAAC,QAAQ,aAAa,KAAK,SAAS,EAAE;AAAA,MAClD,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL;AAAA,QACA,YAAY,KAAK,SAAS;AAAA,QAC1B,aAAa,KAAK,SAAS;AAAA,MAC7B,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AAGA,IAAI,UAAU,MAAM;AAAA,EAClB,YAAY,SAAS,cAAc,OAAO,OAAO,aAAa,YAAY;AACxE,SAAK,UAAU;AACf,SAAK,eAAe;AACpB,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,cAAc;AACnB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,OAAO;AACL,WAAO,MAAM,SAAS;AAAA,EACxB;AAAA,EACA,qBAAqB;AACnB,UAAM,YAAY,KAAK,QAAQ,KAAK,IAAI,OAAK,CAAC,KAAK,MAAM,cAAc,EAAE,CAAC,CAAC,GAAG,KAAK,MAAM,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC;AAC7G,UAAM,oBAAoB;AAC1B,UAAM,WAAW,KAAK,IAAI,KAAK,MAAM,oBAAoB,IAAI,GAAG,KAAK,MAAM,gBAAgB,CAAC,KAAK,IAAI;AACrG,UAAM,eAAe,WAAW;AAChC,QAAI,KAAK,gBAAgB,cAAc;AACrC,aAAO,CAAC;AAAA,QACN,YAAY,CAAC,QAAQ,YAAY,KAAK,SAAS,EAAE;AAAA,QACjD,MAAM;AAAA,QACN,MAAM,UAAU,IAAI,WAAS;AAAA,UAC3B,GAAG,KAAK,aAAa;AAAA,UACrB,GAAG,KAAK,CAAC,IAAI;AAAA,UACb,QAAQ;AAAA,UACR,OAAO,KAAK,CAAC,IAAI,KAAK,aAAa;AAAA,UACnC,MAAM,KAAK,QAAQ;AAAA,UACnB,aAAa;AAAA,UACb,YAAY,KAAK,QAAQ;AAAA,QAC3B,EAAE;AAAA,MACJ,CAAC;AAAA,IACH;AACA,WAAO,CAAC;AAAA,MACN,YAAY,CAAC,QAAQ,YAAY,KAAK,SAAS,EAAE;AAAA,MACjD,MAAM;AAAA,MACN,MAAM,UAAU,IAAI,WAAS;AAAA,QAC3B,GAAG,KAAK,CAAC,IAAI;AAAA,QACb,GAAG,KAAK,CAAC;AAAA,QACT,OAAO;AAAA,QACP,QAAQ,KAAK,aAAa,IAAI,KAAK,aAAa,SAAS,KAAK,CAAC;AAAA,QAC/D,MAAM,KAAK,QAAQ;AAAA,QACnB,aAAa;AAAA,QACb,YAAY,KAAK,QAAQ;AAAA,MAC3B,EAAE;AAAA,IACJ,CAAC;AAAA,EACH;AACF;AAGA,IAAI,WAAW,MAAM;AAAA,EACnB,YAAY,aAAa,WAAW,kBAAkB;AACpD,SAAK,cAAc;AACnB,SAAK,YAAY;AACjB,SAAK,mBAAmB;AACxB,SAAK,eAAe;AAAA,MAClB,GAAG;AAAA,MACH,GAAG;AAAA,MACH,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AAAA,EACF;AAAA,EACA,OAAO;AACL,WAAO,MAAM,UAAU;AAAA,EACzB;AAAA,EACA,QAAQ,OAAO,OAAO;AACpB,SAAK,QAAQ;AACb,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,iBAAiB,OAAO;AACtB,SAAK,aAAa,IAAI,MAAM;AAC5B,SAAK,aAAa,IAAI,MAAM;AAAA,EAC9B;AAAA,EACA,eAAe,gBAAgB;AAC7B,SAAK,aAAa,QAAQ,eAAe;AACzC,SAAK,aAAa,SAAS,eAAe;AAC1C,WAAO;AAAA,MACL,OAAO,KAAK,aAAa;AAAA,MACzB,QAAQ,KAAK,aAAa;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,sBAAsB;AACpB,QAAI,EAAE,KAAK,SAAS,KAAK,QAAQ;AAC/B,YAAM,MAAM,qCAAqC;AAAA,IACnD;AACA,UAAM,eAAe,CAAC;AACtB,eAAW,CAAC,GAAG,IAAI,KAAK,KAAK,UAAU,MAAM,QAAQ,GAAG;AACtD,cAAQ,KAAK,MAAM;AAAA,QACjB,KAAK;AACH;AACE,kBAAM,WAAW,IAAI,SAAS,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK,YAAY,kBAAkB,CAAC;AAChG,yBAAa,KAAK,GAAG,SAAS,mBAAmB,CAAC;AAAA,UACpD;AACA;AAAA,QACF,KAAK;AACH;AACE,kBAAM,UAAU,IAAI,QAAQ,MAAM,KAAK,cAAc,KAAK,OAAO,KAAK,OAAO,KAAK,YAAY,kBAAkB,CAAC;AACjH,yBAAa,KAAK,GAAG,QAAQ,mBAAmB,CAAC;AAAA,UACnD;AACA;AAAA,MACJ;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AACA,SAAS,iBAAiB,aAAa,WAAW,kBAAkB;AAClE,SAAO,IAAI,SAAS,aAAa,WAAW,gBAAgB;AAC9D;AACA,OAAO,kBAAkB,kBAAkB;AAG3C,IAAI,eAAe,MAAM;AAAA,EACvB,YAAY,aAAa,WAAW,kBAAkB,cAAc;AAClE,SAAK,cAAc;AACnB,SAAK,YAAY;AACjB,SAAK,iBAAiB;AAAA,MACpB,OAAO,uBAAuB,aAAa,WAAW,kBAAkB,YAAY;AAAA,MACpF,MAAM,iBAAiB,aAAa,WAAW,gBAAgB;AAAA,MAC/D,OAAO,QAAQ,UAAU,OAAO,YAAY,OAAO;AAAA,QACjD,YAAY,iBAAiB;AAAA,QAC7B,YAAY,iBAAiB;AAAA,QAC7B,WAAW,iBAAiB;AAAA,QAC5B,eAAe,iBAAiB;AAAA,MAClC,GAAG,YAAY;AAAA,MACf,OAAO,QAAQ,UAAU,OAAO,YAAY,OAAO;AAAA,QACjD,YAAY,iBAAiB;AAAA,QAC7B,YAAY,iBAAiB;AAAA,QAC7B,WAAW,iBAAiB;AAAA,QAC5B,eAAe,iBAAiB;AAAA,MAClC,GAAG,YAAY;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAO;AACL,WAAO,MAAM,cAAc;AAAA,EAC7B;AAAA,EACA,yBAAyB;AACvB,QAAI,iBAAiB,KAAK,YAAY;AACtC,QAAI,kBAAkB,KAAK,YAAY;AACvC,QAAI,QAAQ;AACZ,QAAI,QAAQ;AACZ,QAAI,aAAa,KAAK,MAAM,iBAAiB,KAAK,YAAY,2BAA2B,GAAG;AAC5F,QAAI,cAAc,KAAK,MAAM,kBAAkB,KAAK,YAAY,2BAA2B,GAAG;AAC9F,QAAI,YAAY,KAAK,eAAe,KAAK,eAAe;AAAA,MACtD,OAAO;AAAA,MACP,QAAQ;AAAA,IACV,CAAC;AACD,sBAAkB,UAAU;AAC5B,uBAAmB,UAAU;AAC7B,gBAAY,KAAK,eAAe,MAAM,eAAe;AAAA,MACnD,OAAO,KAAK,YAAY;AAAA,MACxB,QAAQ;AAAA,IACV,CAAC;AACD,YAAQ,UAAU;AAClB,uBAAmB,UAAU;AAC7B,SAAK,eAAe,MAAM,gBAAgB,QAAQ;AAClD,gBAAY,KAAK,eAAe,MAAM,eAAe;AAAA,MACnD,OAAO;AAAA,MACP,QAAQ;AAAA,IACV,CAAC;AACD,uBAAmB,UAAU;AAC7B,SAAK,eAAe,MAAM,gBAAgB,MAAM;AAChD,gBAAY,KAAK,eAAe,MAAM,eAAe;AAAA,MACnD,OAAO;AAAA,MACP,QAAQ;AAAA,IACV,CAAC;AACD,YAAQ,UAAU;AAClB,sBAAkB,UAAU;AAC5B,QAAI,iBAAiB,GAAG;AACtB,oBAAc;AACd,uBAAiB;AAAA,IACnB;AACA,QAAI,kBAAkB,GAAG;AACvB,qBAAe;AACf,wBAAkB;AAAA,IACpB;AACA,SAAK,eAAe,KAAK,eAAe;AAAA,MACtC,OAAO;AAAA,MACP,QAAQ;AAAA,IACV,CAAC;AACD,SAAK,eAAe,KAAK,iBAAiB;AAAA,MACxC,GAAG;AAAA,MACH,GAAG;AAAA,IACL,CAAC;AACD,SAAK,eAAe,MAAM,SAAS,CAAC,OAAO,QAAQ,UAAU,CAAC;AAC9D,SAAK,eAAe,MAAM,iBAAiB;AAAA,MACzC,GAAG;AAAA,MACH,GAAG,QAAQ;AAAA,IACb,CAAC;AACD,SAAK,eAAe,MAAM,SAAS,CAAC,OAAO,QAAQ,WAAW,CAAC;AAC/D,SAAK,eAAe,MAAM,iBAAiB;AAAA,MACzC,GAAG;AAAA,MACH,GAAG;AAAA,IACL,CAAC;AACD,QAAI,KAAK,UAAU,MAAM,KAAK,OAAK,UAAU,CAAC,CAAC,GAAG;AAChD,WAAK,eAAe,MAAM,iCAAiC;AAAA,IAC7D;AAAA,EACF;AAAA,EACA,2BAA2B;AACzB,QAAI,iBAAiB,KAAK,YAAY;AACtC,QAAI,kBAAkB,KAAK,YAAY;AACvC,QAAI,YAAY;AAChB,QAAI,QAAQ;AACZ,QAAI,QAAQ;AACZ,QAAI,aAAa,KAAK,MAAM,iBAAiB,KAAK,YAAY,2BAA2B,GAAG;AAC5F,QAAI,cAAc,KAAK,MAAM,kBAAkB,KAAK,YAAY,2BAA2B,GAAG;AAC9F,QAAI,YAAY,KAAK,eAAe,KAAK,eAAe;AAAA,MACtD,OAAO;AAAA,MACP,QAAQ;AAAA,IACV,CAAC;AACD,sBAAkB,UAAU;AAC5B,uBAAmB,UAAU;AAC7B,gBAAY,KAAK,eAAe,MAAM,eAAe;AAAA,MACnD,OAAO,KAAK,YAAY;AAAA,MACxB,QAAQ;AAAA,IACV,CAAC;AACD,gBAAY,UAAU;AACtB,uBAAmB,UAAU;AAC7B,SAAK,eAAe,MAAM,gBAAgB,MAAM;AAChD,gBAAY,KAAK,eAAe,MAAM,eAAe;AAAA,MACnD,OAAO;AAAA,MACP,QAAQ;AAAA,IACV,CAAC;AACD,sBAAkB,UAAU;AAC5B,YAAQ,UAAU;AAClB,SAAK,eAAe,MAAM,gBAAgB,KAAK;AAC/C,gBAAY,KAAK,eAAe,MAAM,eAAe;AAAA,MACnD,OAAO;AAAA,MACP,QAAQ;AAAA,IACV,CAAC;AACD,uBAAmB,UAAU;AAC7B,YAAQ,YAAY,UAAU;AAC9B,QAAI,iBAAiB,GAAG;AACtB,oBAAc;AACd,uBAAiB;AAAA,IACnB;AACA,QAAI,kBAAkB,GAAG;AACvB,qBAAe;AACf,wBAAkB;AAAA,IACpB;AACA,SAAK,eAAe,KAAK,eAAe;AAAA,MACtC,OAAO;AAAA,MACP,QAAQ;AAAA,IACV,CAAC;AACD,SAAK,eAAe,KAAK,iBAAiB;AAAA,MACxC,GAAG;AAAA,MACH,GAAG;AAAA,IACL,CAAC;AACD,SAAK,eAAe,MAAM,SAAS,CAAC,OAAO,QAAQ,UAAU,CAAC;AAC9D,SAAK,eAAe,MAAM,iBAAiB;AAAA,MACzC,GAAG;AAAA,MACH,GAAG;AAAA,IACL,CAAC;AACD,SAAK,eAAe,MAAM,SAAS,CAAC,OAAO,QAAQ,WAAW,CAAC;AAC/D,SAAK,eAAe,MAAM,iBAAiB;AAAA,MACzC,GAAG;AAAA,MACH,GAAG;AAAA,IACL,CAAC;AACD,QAAI,KAAK,UAAU,MAAM,KAAK,OAAK,UAAU,CAAC,CAAC,GAAG;AAChD,WAAK,eAAe,MAAM,iCAAiC;AAAA,IAC7D;AAAA,EACF;AAAA,EACA,iBAAiB;AACf,QAAI,KAAK,YAAY,qBAAqB,cAAc;AACtD,WAAK,yBAAyB;AAAA,IAChC,OAAO;AACL,WAAK,uBAAuB;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,SAAK,eAAe;AACpB,UAAM,eAAe,CAAC;AACtB,SAAK,eAAe,KAAK,QAAQ,KAAK,eAAe,OAAO,KAAK,eAAe,KAAK;AACrF,eAAW,aAAa,OAAO,OAAO,KAAK,cAAc,GAAG;AAC1D,mBAAa,KAAK,GAAG,UAAU,oBAAoB,CAAC;AAAA,IACtD;AACA,WAAO;AAAA,EACT;AACF;AAGA,IAAI,iBAAiB,MAAM;AAAA,EACzB,OAAO;AACL,WAAO,MAAM,gBAAgB;AAAA,EAC/B;AAAA,EACA,OAAO,MAAM,QAAQ,WAAW,kBAAkB,cAAc;AAC9D,UAAM,eAAe,IAAI,aAAa,QAAQ,WAAW,kBAAkB,YAAY;AACvF,WAAO,aAAa,mBAAmB;AAAA,EACzC;AACF;AAGA,IAAI,YAAY;AAChB,IAAI;AACJ,IAAI,gBAAgB,sBAAsB;AAC1C,IAAI,qBAAqB,2BAA2B;AACpD,IAAI,cAAc,oBAAoB;AACtC,IAAI,mBAAmB,mBAAmB,iBAAiB,MAAM,GAAG,EAAE,IAAI,WAAS,MAAM,KAAK,CAAC;AAC/F,IAAI,cAAc;AAClB,IAAI,cAAc;AAClB,SAAS,6BAA6B;AACpC,QAAM,wBAAwB,mBAAkB;AAChD,QAAM,SAAS,UAAU;AACzB,SAAO,cAAc,sBAAsB,SAAS,OAAO,eAAe,OAAO;AACnF;AACA,OAAO,4BAA4B,4BAA4B;AAC/D,SAAS,wBAAwB;AAC/B,QAAM,SAAS,UAAU;AACzB,SAAO,cAAc,sBAAsB,SAAS,OAAO,OAAO;AACpE;AACA,OAAO,uBAAuB,uBAAuB;AACrD,SAAS,sBAAsB;AAC7B,SAAO;AAAA,IACL,OAAO;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,MACP,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,MACP,YAAY,CAAC;AAAA,IACf;AAAA,IACA,OAAO;AAAA,IACP,OAAO,CAAC;AAAA,EACV;AACF;AACA,OAAO,qBAAqB,qBAAqB;AACjD,SAAS,cAAc,MAAM;AAC3B,QAAM,SAAS,UAAU;AACzB,SAAO,aAAa,KAAK,KAAK,GAAG,MAAM;AACzC;AACA,OAAO,eAAe,eAAe;AACrC,SAAS,WAAW,MAAM;AACxB,gBAAc;AAChB;AACA,OAAO,YAAY,YAAY;AAC/B,SAAS,eAAe,aAAa;AACnC,MAAI,gBAAgB,cAAc;AAChC,kBAAc,mBAAmB;AAAA,EACnC,OAAO;AACL,kBAAc,mBAAmB;AAAA,EACnC;AACF;AACA,OAAO,gBAAgB,gBAAgB;AACvC,SAAS,cAAc,OAAO;AAC5B,cAAY,MAAM,QAAQ,cAAc,MAAM,IAAI;AACpD;AACA,OAAO,eAAe,eAAe;AACrC,SAAS,kBAAkB,KAAK,KAAK;AACnC,cAAY,QAAQ;AAAA,IAClB,MAAM;AAAA,IACN,OAAO,YAAY,MAAM;AAAA,IACzB;AAAA,IACA;AAAA,EACF;AACA,gBAAc;AAChB;AACA,OAAO,mBAAmB,mBAAmB;AAC7C,SAAS,aAAa,YAAY;AAChC,cAAY,QAAQ;AAAA,IAClB,MAAM;AAAA,IACN,OAAO,YAAY,MAAM;AAAA,IACzB,YAAY,WAAW,IAAI,OAAK,cAAc,EAAE,IAAI,CAAC;AAAA,EACvD;AACA,gBAAc;AAChB;AACA,OAAO,cAAc,cAAc;AACnC,SAAS,cAAc,OAAO;AAC5B,cAAY,MAAM,QAAQ,cAAc,MAAM,IAAI;AACpD;AACA,OAAO,eAAe,eAAe;AACrC,SAAS,kBAAkB,KAAK,KAAK;AACnC,cAAY,QAAQ;AAAA,IAClB,MAAM;AAAA,IACN,OAAO,YAAY,MAAM;AAAA,IACzB;AAAA,IACA;AAAA,EACF;AACA,gBAAc;AAChB;AACA,OAAO,mBAAmB,mBAAmB;AAC7C,SAAS,0BAA0B,MAAM;AACvC,QAAM,WAAW,KAAK,IAAI,GAAG,IAAI;AACjC,QAAM,WAAW,KAAK,IAAI,GAAG,IAAI;AACjC,QAAM,eAAe,iBAAiB,YAAY,KAAK,IAAI,YAAY,MAAM,MAAM;AACnF,QAAM,eAAe,iBAAiB,YAAY,KAAK,IAAI,YAAY,MAAM,MAAM;AACnF,cAAY,QAAQ;AAAA,IAClB,MAAM;AAAA,IACN,OAAO,YAAY,MAAM;AAAA,IACzB,KAAK,KAAK,IAAI,cAAc,QAAQ;AAAA,IACpC,KAAK,KAAK,IAAI,cAAc,QAAQ;AAAA,EACtC;AACF;AACA,OAAO,2BAA2B,2BAA2B;AAC7D,SAAS,6BAA6B,MAAM;AAC1C,MAAI,UAAU,CAAC;AACf,MAAI,KAAK,WAAW,GAAG;AACrB,WAAO;AAAA,EACT;AACA,MAAI,CAAC,aAAa;AAChB,UAAM,eAAe,iBAAiB,YAAY,KAAK,IAAI,YAAY,MAAM,MAAM;AACnF,UAAM,eAAe,iBAAiB,YAAY,KAAK,IAAI,YAAY,MAAM,MAAM;AACnF,sBAAkB,KAAK,IAAI,cAAc,CAAC,GAAG,KAAK,IAAI,cAAc,KAAK,MAAM,CAAC;AAAA,EAClF;AACA,MAAI,CAAC,aAAa;AAChB,8BAA0B,IAAI;AAAA,EAChC;AACA,MAAI,eAAe,YAAY,KAAK,GAAG;AACrC,cAAU,YAAY,MAAM,WAAW,IAAI,CAAC,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;AAAA,EACnE;AACA,MAAI,iBAAiB,YAAY,KAAK,GAAG;AACvC,UAAM,MAAM,YAAY,MAAM;AAC9B,UAAM,MAAM,YAAY,MAAM;AAC9B,UAAM,QAAQ,MAAM,QAAQ,KAAK,SAAS;AAC1C,UAAM,aAAa,CAAC;AACpB,aAAS,IAAI,KAAK,KAAK,KAAK,KAAK,MAAM;AACrC,iBAAW,KAAK,GAAG,CAAC,EAAE;AAAA,IACxB;AACA,cAAU,WAAW,IAAI,CAAC,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;AAAA,EACjD;AACA,SAAO;AACT;AACA,OAAO,8BAA8B,8BAA8B;AACnE,SAAS,wBAAwB,YAAY;AAC3C,SAAO,iBAAiB,eAAe,IAAI,IAAI,aAAa,iBAAiB,MAAM;AACrF;AACA,OAAO,yBAAyB,yBAAyB;AACzD,SAAS,YAAY,OAAO,MAAM;AAChC,QAAM,WAAW,6BAA6B,IAAI;AAClD,cAAY,MAAM,KAAK;AAAA,IACrB,MAAM;AAAA,IACN,YAAY,wBAAwB,SAAS;AAAA,IAC7C,aAAa;AAAA,IACb,MAAM;AAAA,EACR,CAAC;AACD;AACF;AACA,OAAO,aAAa,aAAa;AACjC,SAAS,WAAW,OAAO,MAAM;AAC/B,QAAM,WAAW,6BAA6B,IAAI;AAClD,cAAY,MAAM,KAAK;AAAA,IACrB,MAAM;AAAA,IACN,MAAM,wBAAwB,SAAS;AAAA,IACvC,MAAM;AAAA,EACR,CAAC;AACD;AACF;AACA,OAAO,YAAY,YAAY;AAC/B,SAAS,kBAAkB;AACzB,MAAI,YAAY,MAAM,WAAW,GAAG;AAClC,UAAM,MAAM,yDAAyD;AAAA,EACvE;AACA,cAAY,QAAQ,gBAAgB;AACpC,SAAO,eAAe,MAAM,eAAe,aAAa,oBAAoB,WAAW;AACzF;AACA,OAAO,iBAAiB,iBAAiB;AACzC,SAAS,sBAAsB;AAC7B,SAAO;AACT;AACA,OAAO,qBAAqB,qBAAqB;AACjD,SAAS,iBAAiB;AACxB,SAAO;AACT;AACA,OAAO,gBAAgB,gBAAgB;AACvC,IAAI,SAAwB,OAAO,WAAY;AAC7C,QAAM;AACN,cAAY;AACZ,kBAAgB,sBAAsB;AACtC,gBAAc,oBAAoB;AAClC,uBAAqB,2BAA2B;AAChD,qBAAmB,mBAAmB,iBAAiB,MAAM,GAAG,EAAE,IAAI,WAAS,MAAM,KAAK,CAAC;AAC3F,gBAAc;AACd,gBAAc;AAChB,GAAG,OAAO;AACV,IAAI,oBAAoB;AAAA,EACtB;AAAA,EACA,OAAO;AAAA,EACP;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAGA,IAAI,OAAsB,OAAO,CAAC,KAAK,IAAI,UAAU,YAAY;AAC/D,QAAM,KAAK,QAAQ;AACnB,QAAM,cAAc,GAAG,oBAAoB;AAC3C,QAAM,cAAc,GAAG,eAAe;AACtC,WAAS,oBAAoB,eAAe;AAC1C,WAAO,kBAAkB,QAAQ,qBAAqB;AAAA,EACxD;AACA,SAAO,qBAAqB,qBAAqB;AACjD,WAAS,cAAc,aAAa;AAClC,WAAO,gBAAgB,SAAS,UAAU,gBAAgB,UAAU,QAAQ;AAAA,EAC9E;AACA,SAAO,eAAe,eAAe;AACrC,WAAS,sBAAsB,MAAM;AACnC,WAAO,aAAa,KAAK,CAAC,KAAK,KAAK,CAAC,YAAY,KAAK,YAAY,CAAC;AAAA,EACrE;AACA,SAAO,uBAAuB,uBAAuB;AACrD,MAAI,MAAM,8BAA8B,GAAG;AAC3C,QAAM,MAAM,iBAAiB,EAAE;AAC/B,QAAM,QAAQ,IAAI,OAAO,GAAG,EAAE,KAAK,SAAS,MAAM;AAClD,QAAM,aAAa,MAAM,OAAO,MAAM,EAAE,KAAK,SAAS,YAAY,KAAK,EAAE,KAAK,UAAU,YAAY,MAAM,EAAE,KAAK,SAAS,YAAY;AACtI,mBAAiB,KAAK,YAAY,QAAQ,YAAY,OAAO,IAAI;AACjE,MAAI,KAAK,WAAW,OAAO,YAAY,KAAK,IAAI,YAAY,MAAM,EAAE;AACpE,aAAW,KAAK,QAAQ,YAAY,eAAe;AACnD,KAAG,WAAW,IAAI,OAAO,GAAG,EAAE,KAAK,SAAS,mBAAmB,CAAC;AAChE,QAAM,SAAS,GAAG,gBAAgB;AAClC,QAAM,SAAS,CAAC;AAChB,WAAS,SAAS,OAAO;AACvB,QAAI,OAAO;AACX,QAAI,SAAS;AACb,eAAW,CAAC,CAAC,KAAK,MAAM,QAAQ,GAAG;AACjC,UAAI,SAAS;AACb,UAAI,IAAI,KAAK,OAAO,MAAM,GAAG;AAC3B,iBAAS,OAAO,MAAM;AAAA,MACxB;AACA,gBAAU,MAAM,CAAC;AACjB,aAAO,OAAO,MAAM;AACpB,UAAI,CAAC,MAAM;AACT,eAAO,OAAO,MAAM,IAAI,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,MAAM,CAAC,CAAC;AAAA,MACnE;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO,UAAU,UAAU;AAC3B,aAAW,SAAS,QAAQ;AAC1B,QAAI,MAAM,KAAK,WAAW,GAAG;AAC3B;AAAA,IACF;AACA,UAAM,aAAa,SAAS,MAAM,UAAU;AAC5C,YAAQ,MAAM,MAAM;AAAA,MAClB,KAAK;AACH,mBAAW,UAAU,MAAM,EAAE,KAAK,MAAM,IAAI,EAAE,MAAM,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,UAAQ,KAAK,CAAC,EAAE,KAAK,KAAK,UAAQ,KAAK,CAAC,EAAE,KAAK,SAAS,UAAQ,KAAK,KAAK,EAAE,KAAK,UAAU,UAAQ,KAAK,MAAM,EAAE,KAAK,QAAQ,UAAQ,KAAK,IAAI,EAAE,KAAK,UAAU,UAAQ,KAAK,UAAU,EAAE,KAAK,gBAAgB,UAAQ,KAAK,WAAW;AACpT;AAAA,MACF,KAAK;AACH,mBAAW,UAAU,MAAM,EAAE,KAAK,MAAM,IAAI,EAAE,MAAM,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,QAAQ,UAAQ,KAAK,IAAI,EAAE,KAAK,aAAa,UAAQ,KAAK,QAAQ,EAAE,KAAK,qBAAqB,UAAQ,oBAAoB,KAAK,WAAW,CAAC,EAAE,KAAK,eAAe,UAAQ,cAAc,KAAK,aAAa,CAAC,EAAE,KAAK,aAAa,UAAQ,sBAAsB,IAAI,CAAC,EAAE,KAAK,UAAQ,KAAK,IAAI;AAC7X;AAAA,MACF,KAAK;AACH,mBAAW,UAAU,MAAM,EAAE,KAAK,MAAM,IAAI,EAAE,MAAM,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,UAAQ,KAAK,IAAI,EAAE,KAAK,QAAQ,UAAQ,KAAK,OAAO,KAAK,OAAO,MAAM,EAAE,KAAK,UAAU,UAAQ,KAAK,UAAU,EAAE,KAAK,gBAAgB,UAAQ,KAAK,WAAW;AAC5O;AAAA,IACJ;AAAA,EACF;AACF,GAAG,MAAM;AACT,IAAI,0BAA0B;AAAA,EAC5B;AACF;AAGA,IAAI,UAAU;AAAA,EACZ,QAAQ;AAAA,EACR,IAAI;AAAA,EACJ,UAAU;AACZ;", "names": []}