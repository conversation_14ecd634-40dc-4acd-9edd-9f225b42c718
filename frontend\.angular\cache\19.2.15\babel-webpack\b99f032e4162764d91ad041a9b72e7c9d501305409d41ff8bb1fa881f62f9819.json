{"ast": null, "code": "/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { Version, ViewEncapsulation, Injector, Compiler, Injectable, createPlatformFactory, COMPILER_OPTIONS, CompilerFactory } from '@angular/core';\nimport { CompilerConfig, ResourceLoader } from '@angular/compiler';\nimport { platformBrowser } from '@angular/platform-browser';\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser-dynamic package.\n */\n/**\n * @publicApi\n */\nconst VERSION = new Version('19.2.14');\nconst COMPILER_PROVIDERS = [{\n  provide: Compiler,\n  useFactory: () => new Compiler()\n}];\n/**\n * @publicApi\n *\n * @deprecated\n * Ivy JIT mode doesn't require accessing this symbol.\n */\nclass JitCompilerFactory {\n  _defaultOptions;\n  /** @internal */\n  constructor(defaultOptions) {\n    const compilerOptions = {\n      defaultEncapsulation: ViewEncapsulation.Emulated\n    };\n    this._defaultOptions = [compilerOptions, ...defaultOptions];\n  }\n  createCompiler(options = []) {\n    const opts = _mergeOptions(this._defaultOptions.concat(options));\n    const injector = Injector.create({\n      providers: [COMPILER_PROVIDERS, {\n        provide: CompilerConfig,\n        useFactory: () => {\n          return new CompilerConfig({\n            defaultEncapsulation: opts.defaultEncapsulation,\n            preserveWhitespaces: opts.preserveWhitespaces\n          });\n        },\n        deps: []\n      }, opts.providers]\n    });\n    return injector.get(Compiler);\n  }\n}\nfunction _mergeOptions(optionsArr) {\n  return {\n    defaultEncapsulation: _lastDefined(optionsArr.map(options => options.defaultEncapsulation)),\n    providers: _mergeArrays(optionsArr.map(options => options.providers)),\n    preserveWhitespaces: _lastDefined(optionsArr.map(options => options.preserveWhitespaces))\n  };\n}\nfunction _lastDefined(args) {\n  for (let i = args.length - 1; i >= 0; i--) {\n    if (args[i] !== undefined) {\n      return args[i];\n    }\n  }\n  return undefined;\n}\nfunction _mergeArrays(parts) {\n  const result = [];\n  parts.forEach(part => part && result.push(...part));\n  return result;\n}\nclass ResourceLoaderImpl extends ResourceLoader {\n  get(url) {\n    let resolve;\n    let reject;\n    const promise = new Promise((res, rej) => {\n      resolve = res;\n      reject = rej;\n    });\n    const xhr = new XMLHttpRequest();\n    xhr.open('GET', url, true);\n    xhr.responseType = 'text';\n    xhr.onload = function () {\n      const response = xhr.response;\n      let status = xhr.status;\n      // fix status code when it is 0 (0 status is undocumented).\n      // Occurs when accessing file resources or on Android 4.1 stock browser\n      // while retrieving files from application cache.\n      if (status === 0) {\n        status = response ? 200 : 0;\n      }\n      if (200 <= status && status <= 300) {\n        resolve(response);\n      } else {\n        reject(`Failed to load ${url}`);\n      }\n    };\n    xhr.onerror = function () {\n      reject(`Failed to load ${url}`);\n    };\n    xhr.send();\n    return promise;\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵResourceLoaderImpl_BaseFactory;\n    return function ResourceLoaderImpl_Factory(__ngFactoryType__) {\n      return (ɵResourceLoaderImpl_BaseFactory || (ɵResourceLoaderImpl_BaseFactory = i0.ɵɵgetInheritedFactory(ResourceLoaderImpl)))(__ngFactoryType__ || ResourceLoaderImpl);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ResourceLoaderImpl,\n    factory: ResourceLoaderImpl.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ResourceLoaderImpl, [{\n    type: Injectable\n  }], null, null);\n})();\nconst INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS = [{\n  provide: COMPILER_OPTIONS,\n  useValue: {\n    providers: [{\n      provide: ResourceLoader,\n      useClass: ResourceLoaderImpl,\n      deps: []\n    }]\n  },\n  multi: true\n}, {\n  provide: CompilerFactory,\n  useClass: JitCompilerFactory,\n  deps: [COMPILER_OPTIONS]\n}];\n/**\n * @publicApi\n */\nconst platformBrowserDynamic = createPlatformFactory(platformBrowser, 'browserDynamic', INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS);\nexport { JitCompilerFactory, VERSION, platformBrowserDynamic, INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS as ɵINTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS };", "map": {"version": 3, "names": ["i0", "Version", "ViewEncapsulation", "Injector", "Compiler", "Injectable", "createPlatformFactory", "COMPILER_OPTIONS", "CompilerFactory", "CompilerConfig", "Resource<PERSON><PERSON>der", "platformBrowser", "VERSION", "COMPILER_PROVIDERS", "provide", "useFactory", "JitCompilerFactory", "_defaultOptions", "constructor", "defaultOptions", "compilerOptions", "defaultEncapsulation", "Emulated", "createCompiler", "options", "opts", "_mergeOptions", "concat", "injector", "create", "providers", "preserveWhitespaces", "deps", "get", "optionsArr", "_lastDefined", "map", "_mergeArrays", "args", "i", "length", "undefined", "parts", "result", "for<PERSON>ach", "part", "push", "ResourceLoaderImpl", "url", "resolve", "reject", "promise", "Promise", "res", "rej", "xhr", "XMLHttpRequest", "open", "responseType", "onload", "response", "status", "onerror", "send", "ɵfac", "ɵResourceLoaderImpl_BaseFactory", "ResourceLoaderImpl_Factory", "__ngFactoryType__", "ɵɵgetInheritedFactory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS", "useValue", "useClass", "multi", "platformBrowserDynamic", "ɵINTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS"], "sources": ["C:/projetos/scope-ai/frontend/node_modules/@angular/platform-browser-dynamic/fesm2022/platform-browser-dynamic.mjs"], "sourcesContent": ["/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { Version, ViewEncapsulation, Injector, Compiler, Injectable, createPlatformFactory, COMPILER_OPTIONS, CompilerFactory } from '@angular/core';\nimport { CompilerConfig, ResourceLoader } from '@angular/compiler';\nimport { platformBrowser } from '@angular/platform-browser';\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser-dynamic package.\n */\n/**\n * @publicApi\n */\nconst VERSION = new Version('19.2.14');\n\nconst COMPILER_PROVIDERS = [\n    { provide: Compiler, useFactory: () => new Compiler() },\n];\n/**\n * @publicApi\n *\n * @deprecated\n * Ivy JIT mode doesn't require accessing this symbol.\n */\nclass JitCompilerFactory {\n    _defaultOptions;\n    /** @internal */\n    constructor(defaultOptions) {\n        const compilerOptions = {\n            defaultEncapsulation: ViewEncapsulation.Emulated,\n        };\n        this._defaultOptions = [compilerOptions, ...defaultOptions];\n    }\n    createCompiler(options = []) {\n        const opts = _mergeOptions(this._defaultOptions.concat(options));\n        const injector = Injector.create({\n            providers: [\n                COMPILER_PROVIDERS,\n                {\n                    provide: CompilerConfig,\n                    useFactory: () => {\n                        return new CompilerConfig({\n                            defaultEncapsulation: opts.defaultEncapsulation,\n                            preserveWhitespaces: opts.preserveWhitespaces,\n                        });\n                    },\n                    deps: [],\n                },\n                opts.providers,\n            ],\n        });\n        return injector.get(Compiler);\n    }\n}\nfunction _mergeOptions(optionsArr) {\n    return {\n        defaultEncapsulation: _lastDefined(optionsArr.map((options) => options.defaultEncapsulation)),\n        providers: _mergeArrays(optionsArr.map((options) => options.providers)),\n        preserveWhitespaces: _lastDefined(optionsArr.map((options) => options.preserveWhitespaces)),\n    };\n}\nfunction _lastDefined(args) {\n    for (let i = args.length - 1; i >= 0; i--) {\n        if (args[i] !== undefined) {\n            return args[i];\n        }\n    }\n    return undefined;\n}\nfunction _mergeArrays(parts) {\n    const result = [];\n    parts.forEach((part) => part && result.push(...part));\n    return result;\n}\n\nclass ResourceLoaderImpl extends ResourceLoader {\n    get(url) {\n        let resolve;\n        let reject;\n        const promise = new Promise((res, rej) => {\n            resolve = res;\n            reject = rej;\n        });\n        const xhr = new XMLHttpRequest();\n        xhr.open('GET', url, true);\n        xhr.responseType = 'text';\n        xhr.onload = function () {\n            const response = xhr.response;\n            let status = xhr.status;\n            // fix status code when it is 0 (0 status is undocumented).\n            // Occurs when accessing file resources or on Android 4.1 stock browser\n            // while retrieving files from application cache.\n            if (status === 0) {\n                status = response ? 200 : 0;\n            }\n            if (200 <= status && status <= 300) {\n                resolve(response);\n            }\n            else {\n                reject(`Failed to load ${url}`);\n            }\n        };\n        xhr.onerror = function () {\n            reject(`Failed to load ${url}`);\n        };\n        xhr.send();\n        return promise;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: ResourceLoaderImpl, deps: null, target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: ResourceLoaderImpl });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: ResourceLoaderImpl, decorators: [{\n            type: Injectable\n        }] });\n\nconst INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS = [\n    {\n        provide: COMPILER_OPTIONS,\n        useValue: { providers: [{ provide: ResourceLoader, useClass: ResourceLoaderImpl, deps: [] }] },\n        multi: true,\n    },\n    { provide: CompilerFactory, useClass: JitCompilerFactory, deps: [COMPILER_OPTIONS] },\n];\n/**\n * @publicApi\n */\nconst platformBrowserDynamic = createPlatformFactory(platformBrowser, 'browserDynamic', INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS);\n\nexport { JitCompilerFactory, VERSION, platformBrowserDynamic, INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS as ɵINTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,OAAO,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,qBAAqB,EAAEC,gBAAgB,EAAEC,eAAe,QAAQ,eAAe;AACpJ,SAASC,cAAc,EAAEC,cAAc,QAAQ,mBAAmB;AAClE,SAASC,eAAe,QAAQ,2BAA2B;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,OAAO,GAAG,IAAIX,OAAO,CAAC,SAAS,CAAC;AAEtC,MAAMY,kBAAkB,GAAG,CACvB;EAAEC,OAAO,EAAEV,QAAQ;EAAEW,UAAU,EAAEA,CAAA,KAAM,IAAIX,QAAQ,CAAC;AAAE,CAAC,CAC1D;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMY,kBAAkB,CAAC;EACrBC,eAAe;EACf;EACAC,WAAWA,CAACC,cAAc,EAAE;IACxB,MAAMC,eAAe,GAAG;MACpBC,oBAAoB,EAAEnB,iBAAiB,CAACoB;IAC5C,CAAC;IACD,IAAI,CAACL,eAAe,GAAG,CAACG,eAAe,EAAE,GAAGD,cAAc,CAAC;EAC/D;EACAI,cAAcA,CAACC,OAAO,GAAG,EAAE,EAAE;IACzB,MAAMC,IAAI,GAAGC,aAAa,CAAC,IAAI,CAACT,eAAe,CAACU,MAAM,CAACH,OAAO,CAAC,CAAC;IAChE,MAAMI,QAAQ,GAAGzB,QAAQ,CAAC0B,MAAM,CAAC;MAC7BC,SAAS,EAAE,CACPjB,kBAAkB,EAClB;QACIC,OAAO,EAAEL,cAAc;QACvBM,UAAU,EAAEA,CAAA,KAAM;UACd,OAAO,IAAIN,cAAc,CAAC;YACtBY,oBAAoB,EAAEI,IAAI,CAACJ,oBAAoB;YAC/CU,mBAAmB,EAAEN,IAAI,CAACM;UAC9B,CAAC,CAAC;QACN,CAAC;QACDC,IAAI,EAAE;MACV,CAAC,EACDP,IAAI,CAACK,SAAS;IAEtB,CAAC,CAAC;IACF,OAAOF,QAAQ,CAACK,GAAG,CAAC7B,QAAQ,CAAC;EACjC;AACJ;AACA,SAASsB,aAAaA,CAACQ,UAAU,EAAE;EAC/B,OAAO;IACHb,oBAAoB,EAAEc,YAAY,CAACD,UAAU,CAACE,GAAG,CAAEZ,OAAO,IAAKA,OAAO,CAACH,oBAAoB,CAAC,CAAC;IAC7FS,SAAS,EAAEO,YAAY,CAACH,UAAU,CAACE,GAAG,CAAEZ,OAAO,IAAKA,OAAO,CAACM,SAAS,CAAC,CAAC;IACvEC,mBAAmB,EAAEI,YAAY,CAACD,UAAU,CAACE,GAAG,CAAEZ,OAAO,IAAKA,OAAO,CAACO,mBAAmB,CAAC;EAC9F,CAAC;AACL;AACA,SAASI,YAAYA,CAACG,IAAI,EAAE;EACxB,KAAK,IAAIC,CAAC,GAAGD,IAAI,CAACE,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACvC,IAAID,IAAI,CAACC,CAAC,CAAC,KAAKE,SAAS,EAAE;MACvB,OAAOH,IAAI,CAACC,CAAC,CAAC;IAClB;EACJ;EACA,OAAOE,SAAS;AACpB;AACA,SAASJ,YAAYA,CAACK,KAAK,EAAE;EACzB,MAAMC,MAAM,GAAG,EAAE;EACjBD,KAAK,CAACE,OAAO,CAAEC,IAAI,IAAKA,IAAI,IAAIF,MAAM,CAACG,IAAI,CAAC,GAAGD,IAAI,CAAC,CAAC;EACrD,OAAOF,MAAM;AACjB;AAEA,MAAMI,kBAAkB,SAASrC,cAAc,CAAC;EAC5CuB,GAAGA,CAACe,GAAG,EAAE;IACL,IAAIC,OAAO;IACX,IAAIC,MAAM;IACV,MAAMC,OAAO,GAAG,IAAIC,OAAO,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;MACtCL,OAAO,GAAGI,GAAG;MACbH,MAAM,GAAGI,GAAG;IAChB,CAAC,CAAC;IACF,MAAMC,GAAG,GAAG,IAAIC,cAAc,CAAC,CAAC;IAChCD,GAAG,CAACE,IAAI,CAAC,KAAK,EAAET,GAAG,EAAE,IAAI,CAAC;IAC1BO,GAAG,CAACG,YAAY,GAAG,MAAM;IACzBH,GAAG,CAACI,MAAM,GAAG,YAAY;MACrB,MAAMC,QAAQ,GAAGL,GAAG,CAACK,QAAQ;MAC7B,IAAIC,MAAM,GAAGN,GAAG,CAACM,MAAM;MACvB;MACA;MACA;MACA,IAAIA,MAAM,KAAK,CAAC,EAAE;QACdA,MAAM,GAAGD,QAAQ,GAAG,GAAG,GAAG,CAAC;MAC/B;MACA,IAAI,GAAG,IAAIC,MAAM,IAAIA,MAAM,IAAI,GAAG,EAAE;QAChCZ,OAAO,CAACW,QAAQ,CAAC;MACrB,CAAC,MACI;QACDV,MAAM,CAAC,kBAAkBF,GAAG,EAAE,CAAC;MACnC;IACJ,CAAC;IACDO,GAAG,CAACO,OAAO,GAAG,YAAY;MACtBZ,MAAM,CAAC,kBAAkBF,GAAG,EAAE,CAAC;IACnC,CAAC;IACDO,GAAG,CAACQ,IAAI,CAAC,CAAC;IACV,OAAOZ,OAAO;EAClB;EACA,OAAOa,IAAI;IAAA,IAAAC,+BAAA;IAAA,gBAAAC,2BAAAC,iBAAA;MAAA,QAAAF,+BAAA,KAAAA,+BAAA,GAA+EjE,EAAE,CAAAoE,qBAAA,CAAQrB,kBAAkB,IAAAoB,iBAAA,IAAlBpB,kBAAkB;IAAA;EAAA;EACtH,OAAOsB,KAAK,kBAD8ErE,EAAE,CAAAsE,kBAAA;IAAAC,KAAA,EACYxB,kBAAkB;IAAAyB,OAAA,EAAlBzB,kBAAkB,CAAAiB;EAAA;AAC9H;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KAH8FzE,EAAE,CAAA0E,iBAAA,CAGJ3B,kBAAkB,EAAc,CAAC;IACjH4B,IAAI,EAAEtE;EACV,CAAC,CAAC;AAAA;AAEV,MAAMuE,2CAA2C,GAAG,CAChD;EACI9D,OAAO,EAAEP,gBAAgB;EACzBsE,QAAQ,EAAE;IAAE/C,SAAS,EAAE,CAAC;MAAEhB,OAAO,EAAEJ,cAAc;MAAEoE,QAAQ,EAAE/B,kBAAkB;MAAEf,IAAI,EAAE;IAAG,CAAC;EAAE,CAAC;EAC9F+C,KAAK,EAAE;AACX,CAAC,EACD;EAAEjE,OAAO,EAAEN,eAAe;EAAEsE,QAAQ,EAAE9D,kBAAkB;EAAEgB,IAAI,EAAE,CAACzB,gBAAgB;AAAE,CAAC,CACvF;AACD;AACA;AACA;AACA,MAAMyE,sBAAsB,GAAG1E,qBAAqB,CAACK,eAAe,EAAE,gBAAgB,EAAEiE,2CAA2C,CAAC;AAEpI,SAAS5D,kBAAkB,EAAEJ,OAAO,EAAEoE,sBAAsB,EAAEJ,2CAA2C,IAAIK,4CAA4C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}