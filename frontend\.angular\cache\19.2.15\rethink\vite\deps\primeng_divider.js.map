{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-divider.mjs"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, inject, HostBinding, Input, ViewEncapsulation, ChangeDetectionStrategy, Component, NgModule } from '@angular/core';\nimport { SharedModule } from 'primeng/api';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { BaseStyle } from 'primeng/base';\nconst _c0 = [\"*\"];\nconst theme = ({\n  dt\n}) => `\n.p-divider-horizontal {\n    display: flex;\n    width: 100%;\n    position: relative;\n    align-items: center;\n    margin: ${dt('divider.horizontal.margin')};\n    padding: ${dt('divider.horizontal.padding')};\n}\n\n.p-divider-horizontal:before {\n    position: absolute;\n    display: block;\n    inset-block-start: 50%;\n    inset-inline-start: 0;\n    width: 100%;\n    content: \"\";\n    border-block-start: 1px solid ${dt('divider.border.color')};\n}\n\n.p-divider-horizontal .p-divider-content {\n    padding: ${dt('divider.horizontal.content.padding')};\n}\n\n.p-divider-vertical {\n    min-height: 100%;\n    display: flex;\n    position: relative;\n    justify-content: center;\n    margin: ${dt('divider.vertical.margin')};\n    padding: ${dt('divider.vertical.padding')};\n}\n\n.p-divider-vertical:before {\n    position: absolute;\n    display: block;\n    inset-block-start: 0;\n    inset-inline-start: 50%;\n    height: 100%;\n    content: \"\";\n    border-inline-start: 1px solid ${dt('divider.border.color')};\n}\n\n.p-divider.p-divider-vertical .p-divider-content {\n    padding: ${dt('divider.vertical.content.padding')};\n}\n\n.p-divider-content {\n    z-index: 1;\n    background: ${dt('divider.content.background')};\n    color: ${dt('divider.content.color')};\n}\n\n.p-divider-solid.p-divider-horizontal:before {\n    border-block-start-style: solid;\n}\n\n.p-divider-solid.p-divider-vertical:before {\n    border-inline-start-style: solid;\n}\n\n.p-divider-dashed.p-divider-horizontal:before {\n    border-block-start-style: dashed;\n}\n\n.p-divider-dashed.p-divider-vertical:before {\n    border-inline-start-style: dashed;\n}\n\n.p-divider-dotted.p-divider-horizontal:before {\n    border-block-start-style: dotted;\n}\n\n.p-divider-dotted.p-divider-vertical:before {\n    border-inline-start-style: dotted;\n}\n\n.p-divider-left:dir(rtl),\n.p-divider-right:dir(rtl) {\n    flex-direction: row-reverse;\n}\n`;\n/* Position */\nconst inlineStyles = {\n  root: ({\n    props\n  }) => ({\n    justifyContent: props.layout === 'horizontal' ? props.align === 'center' || props.align === null ? 'center' : props.align === 'left' ? 'flex-start' : props.align === 'right' ? 'flex-end' : null : null,\n    alignItems: props.layout === 'vertical' ? props.align === 'center' || props.align === null ? 'center' : props.align === 'top' ? 'flex-start' : props.align === 'bottom' ? 'flex-end' : null : null\n  })\n};\nconst classes = {\n  root: ({\n    props\n  }) => ['p-divider p-component', 'p-divider-' + props.layout, 'p-divider-' + props.type, {\n    'p-divider-left': props.layout === 'horizontal' && (!props.align || props.align === 'left')\n  }, {\n    'p-divider-center': props.layout === 'horizontal' && props.align === 'center'\n  }, {\n    'p-divider-right': props.layout === 'horizontal' && props.align === 'right'\n  }, {\n    'p-divider-top': props.layout === 'vertical' && props.align === 'top'\n  }, {\n    'p-divider-center': props.layout === 'vertical' && (!props.align || props.align === 'center')\n  }, {\n    'p-divider-bottom': props.layout === 'vertical' && props.align === 'bottom'\n  }],\n  content: 'p-divider-content'\n};\nclass DividerStyle extends BaseStyle {\n  name = 'divider';\n  theme = theme;\n  classes = classes;\n  inlineStyles = inlineStyles;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵDividerStyle_BaseFactory;\n    return function DividerStyle_Factory(__ngFactoryType__) {\n      return (ɵDividerStyle_BaseFactory || (ɵDividerStyle_BaseFactory = i0.ɵɵgetInheritedFactory(DividerStyle)))(__ngFactoryType__ || DividerStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DividerStyle,\n    factory: DividerStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DividerStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n *\n * Divider is used to separate contents.\n *\n * [Live Demo](https://primeng.org/divider)\n *\n * @module dividerstyle\n *\n */\nvar DividerClasses;\n(function (DividerClasses) {\n  /**\n   * Class name of the root element\n   */\n  DividerClasses[\"root\"] = \"p-divider\";\n  /**\n   * Class name of the content element\n   */\n  DividerClasses[\"content\"] = \"p-divider-content\";\n})(DividerClasses || (DividerClasses = {}));\n\n/**\n * Divider is used to separate contents.\n * @group Components\n */\nclass Divider extends BaseComponent {\n  /**\n   * Inline style of the component.\n   * @group Props\n   */\n  style;\n  /**\n   * Style class of the component.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Specifies the orientation.\n   * @group Props\n   */\n  layout = 'horizontal';\n  /**\n   * Border style type.\n   * @group Props\n   */\n  type = 'solid';\n  /**\n   * Alignment of the content.\n   * @group Props\n   */\n  align;\n  _componentStyle = inject(DividerStyle);\n  get hostClass() {\n    return this.styleClass;\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵDivider_BaseFactory;\n    return function Divider_Factory(__ngFactoryType__) {\n      return (ɵDivider_BaseFactory || (ɵDivider_BaseFactory = i0.ɵɵgetInheritedFactory(Divider)))(__ngFactoryType__ || Divider);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Divider,\n    selectors: [[\"p-divider\"]],\n    hostVars: 33,\n    hostBindings: function Divider_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-orientation\", ctx.layout)(\"data-pc-name\", \"divider\")(\"role\", \"separator\");\n        i0.ɵɵclassMap(ctx.hostClass);\n        i0.ɵɵstyleProp(\"justify-content\", ctx.layout === \"horizontal\" ? ctx.align === \"center\" || ctx.align === undefined ? \"center\" : ctx.align === \"left\" ? \"flex-start\" : ctx.align === \"right\" ? \"flex-end\" : null : null)(\"align-items\", ctx.layout === \"vertical\" ? ctx.align === \"center\" || ctx.align === undefined ? \"center\" : ctx.align === \"top\" ? \"flex-start\" : ctx.align === \"bottom\" ? \"flex-end\" : null : null);\n        i0.ɵɵclassProp(\"p-divider\", true)(\"p-component\", true)(\"p-divider-horizontal\", ctx.layout === \"horizontal\")(\"p-divider-vertical\", ctx.layout === \"vertical\")(\"p-divider-solid\", ctx.type === \"solid\")(\"p-divider-dashed\", ctx.type === \"dashed\")(\"p-divider-dotted\", ctx.type === \"dotted\")(\"p-divider-left\", ctx.layout === \"horizontal\" && (!ctx.align || ctx.align === \"left\"))(\"p-divider-center\", ctx.layout === \"horizontal\" && ctx.align === \"center\" || ctx.layout === \"vertical\" && (!ctx.align || ctx.align === \"center\"))(\"p-divider-right\", ctx.layout === \"horizontal\" && ctx.align === \"right\")(\"p-divider-top\", ctx.layout === \"vertical\" && ctx.align === \"top\")(\"p-divider-bottom\", ctx.layout === \"vertical\" && ctx.align === \"bottom\");\n      }\n    },\n    inputs: {\n      style: \"style\",\n      styleClass: \"styleClass\",\n      layout: \"layout\",\n      type: \"type\",\n      align: \"align\"\n    },\n    features: [i0.ɵɵProvidersFeature([DividerStyle]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c0,\n    decls: 2,\n    vars: 0,\n    consts: [[1, \"p-divider-content\"]],\n    template: function Divider_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵelementEnd();\n      }\n    },\n    dependencies: [CommonModule, SharedModule],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Divider, [{\n    type: Component,\n    args: [{\n      selector: 'p-divider',\n      standalone: true,\n      imports: [CommonModule, SharedModule],\n      template: `\n        <div class=\"p-divider-content\">\n            <ng-content></ng-content>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        '[class.p-divider]': 'true',\n        '[class.p-component]': 'true',\n        '[class.p-divider-horizontal]': 'layout === \"horizontal\"',\n        '[class.p-divider-vertical]': 'layout === \"vertical\"',\n        '[class.p-divider-solid]': 'type === \"solid\"',\n        '[class.p-divider-dashed]': 'type === \"dashed\"',\n        '[class.p-divider-dotted]': 'type === \"dotted\"',\n        '[class.p-divider-left]': 'layout === \"horizontal\" && (!align || align === \"left\")',\n        '[class.p-divider-center]': '(layout === \"horizontal\" && align === \"center\") || (layout === \"vertical\" && (!align || align === \"center\"))',\n        '[class.p-divider-right]': 'layout === \"horizontal\" && align === \"right\"',\n        '[class.p-divider-top]': 'layout === \"vertical\" && align === \"top\"',\n        '[class.p-divider-bottom]': 'layout === \"vertical\" && align === \"bottom\"',\n        '[attr.aria-orientation]': 'layout',\n        '[attr.data-pc-name]': \"'divider'\",\n        '[attr.role]': '\"separator\"',\n        '[style.justifyContent]': 'layout === \"horizontal\" ? (align === \"center\" || align === undefined ? \"center\" : (align === \"left\" ? \"flex-start\" : (align === \"right\" ? \"flex-end\" : null))) : null',\n        '[style.alignItems]': 'layout === \"vertical\" ? (align === \"center\" || align === undefined ? \"center\" : (align === \"top\" ? \"flex-start\" : (align === \"bottom\" ? \"flex-end\" : null))) : null'\n      },\n      providers: [DividerStyle]\n    }]\n  }], null, {\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    layout: [{\n      type: Input\n    }],\n    type: [{\n      type: Input\n    }],\n    align: [{\n      type: Input\n    }],\n    hostClass: [{\n      type: HostBinding,\n      args: ['class']\n    }]\n  });\n})();\nclass DividerModule {\n  static ɵfac = function DividerModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DividerModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: DividerModule,\n    imports: [Divider],\n    exports: [Divider]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [Divider]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DividerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [Divider],\n      exports: [Divider]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Divider, DividerClasses, DividerModule, DividerStyle };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,QAAQ,CAAC;AAAA,EACb;AACF,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAMQ,GAAG,2BAA2B,CAAC;AAAA,eAC9B,GAAG,4BAA4B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oCAUX,GAAG,sBAAsB,CAAC;AAAA;AAAA;AAAA;AAAA,eAI/C,GAAG,oCAAoC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAQzC,GAAG,yBAAyB,CAAC;AAAA,eAC5B,GAAG,0BAA0B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qCAUR,GAAG,sBAAsB,CAAC;AAAA;AAAA;AAAA;AAAA,eAIhD,GAAG,kCAAkC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,kBAKnC,GAAG,4BAA4B,CAAC;AAAA,aACrC,GAAG,uBAAuB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiCxC,IAAM,eAAe;AAAA,EACnB,MAAM,CAAC;AAAA,IACL;AAAA,EACF,OAAO;AAAA,IACL,gBAAgB,MAAM,WAAW,eAAe,MAAM,UAAU,YAAY,MAAM,UAAU,OAAO,WAAW,MAAM,UAAU,SAAS,eAAe,MAAM,UAAU,UAAU,aAAa,OAAO;AAAA,IACpM,YAAY,MAAM,WAAW,aAAa,MAAM,UAAU,YAAY,MAAM,UAAU,OAAO,WAAW,MAAM,UAAU,QAAQ,eAAe,MAAM,UAAU,WAAW,aAAa,OAAO;AAAA,EAChM;AACF;AACA,IAAM,UAAU;AAAA,EACd,MAAM,CAAC;AAAA,IACL;AAAA,EACF,MAAM,CAAC,yBAAyB,eAAe,MAAM,QAAQ,eAAe,MAAM,MAAM;AAAA,IACtF,kBAAkB,MAAM,WAAW,iBAAiB,CAAC,MAAM,SAAS,MAAM,UAAU;AAAA,EACtF,GAAG;AAAA,IACD,oBAAoB,MAAM,WAAW,gBAAgB,MAAM,UAAU;AAAA,EACvE,GAAG;AAAA,IACD,mBAAmB,MAAM,WAAW,gBAAgB,MAAM,UAAU;AAAA,EACtE,GAAG;AAAA,IACD,iBAAiB,MAAM,WAAW,cAAc,MAAM,UAAU;AAAA,EAClE,GAAG;AAAA,IACD,oBAAoB,MAAM,WAAW,eAAe,CAAC,MAAM,SAAS,MAAM,UAAU;AAAA,EACtF,GAAG;AAAA,IACD,oBAAoB,MAAM,WAAW,cAAc,MAAM,UAAU;AAAA,EACrE,CAAC;AAAA,EACD,SAAS;AACX;AACA,IAAM,eAAN,MAAM,sBAAqB,UAAU;AAAA,EACnC,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,eAAe;AAAA,EACf,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,qBAAqB,mBAAmB;AACtD,cAAQ,8BAA8B,4BAA+B,sBAAsB,aAAY,IAAI,qBAAqB,aAAY;AAAA,IAC9I;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,cAAa;AAAA,EACxB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAI;AAAA,CACH,SAAUA,iBAAgB;AAIzB,EAAAA,gBAAe,MAAM,IAAI;AAIzB,EAAAA,gBAAe,SAAS,IAAI;AAC9B,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAM1C,IAAM,UAAN,MAAM,iBAAgB,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,EAKT,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAKP;AAAA,EACA,kBAAkB,OAAO,YAAY;AAAA,EACrC,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,gBAAgB,mBAAmB;AACjD,cAAQ,yBAAyB,uBAA0B,sBAAsB,QAAO,IAAI,qBAAqB,QAAO;AAAA,IAC1H;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,WAAW,CAAC;AAAA,IACzB,UAAU;AAAA,IACV,cAAc,SAAS,qBAAqB,IAAI,KAAK;AACnD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,oBAAoB,IAAI,MAAM,EAAE,gBAAgB,SAAS,EAAE,QAAQ,WAAW;AAC7F,QAAG,WAAW,IAAI,SAAS;AAC3B,QAAG,YAAY,mBAAmB,IAAI,WAAW,eAAe,IAAI,UAAU,YAAY,IAAI,UAAU,SAAY,WAAW,IAAI,UAAU,SAAS,eAAe,IAAI,UAAU,UAAU,aAAa,OAAO,IAAI,EAAE,eAAe,IAAI,WAAW,aAAa,IAAI,UAAU,YAAY,IAAI,UAAU,SAAY,WAAW,IAAI,UAAU,QAAQ,eAAe,IAAI,UAAU,WAAW,aAAa,OAAO,IAAI;AACvZ,QAAG,YAAY,aAAa,IAAI,EAAE,eAAe,IAAI,EAAE,wBAAwB,IAAI,WAAW,YAAY,EAAE,sBAAsB,IAAI,WAAW,UAAU,EAAE,mBAAmB,IAAI,SAAS,OAAO,EAAE,oBAAoB,IAAI,SAAS,QAAQ,EAAE,oBAAoB,IAAI,SAAS,QAAQ,EAAE,kBAAkB,IAAI,WAAW,iBAAiB,CAAC,IAAI,SAAS,IAAI,UAAU,OAAO,EAAE,oBAAoB,IAAI,WAAW,gBAAgB,IAAI,UAAU,YAAY,IAAI,WAAW,eAAe,CAAC,IAAI,SAAS,IAAI,UAAU,SAAS,EAAE,mBAAmB,IAAI,WAAW,gBAAgB,IAAI,UAAU,OAAO,EAAE,iBAAiB,IAAI,WAAW,cAAc,IAAI,UAAU,KAAK,EAAE,oBAAoB,IAAI,WAAW,cAAc,IAAI,UAAU,QAAQ;AAAA,MAC1tB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC,YAAY,CAAC,GAAM,0BAA0B;AAAA,IAC/E,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,mBAAmB,CAAC;AAAA,IACjC,UAAU,SAAS,iBAAiB,IAAI,KAAK;AAC3C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,aAAa,CAAC;AACjB,QAAG,aAAa;AAAA,MAClB;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAc,YAAY;AAAA,IACzC,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,SAAS,CAAC;AAAA,IAChF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc,YAAY;AAAA,MACpC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,MAKV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,qBAAqB;AAAA,QACrB,uBAAuB;AAAA,QACvB,gCAAgC;AAAA,QAChC,8BAA8B;AAAA,QAC9B,2BAA2B;AAAA,QAC3B,4BAA4B;AAAA,QAC5B,4BAA4B;AAAA,QAC5B,0BAA0B;AAAA,QAC1B,4BAA4B;AAAA,QAC5B,2BAA2B;AAAA,QAC3B,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,QAC5B,2BAA2B;AAAA,QAC3B,uBAAuB;AAAA,QACvB,eAAe;AAAA,QACf,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,MACxB;AAAA,MACA,WAAW,CAAC,YAAY;AAAA,IAC1B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,OAAO;AAAA,IACjB,SAAS,CAAC,OAAO;AAAA,EACnB,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,OAAO;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,OAAO;AAAA,MACjB,SAAS,CAAC,OAAO;AAAA,IACnB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["DividerClasses"]}