{"version": 3, "sources": ["../../../../../../node_modules/d3-sankey/node_modules/d3-array/src/ascending.js", "../../../../../../node_modules/d3-sankey/node_modules/d3-array/src/bisector.js", "../../../../../../node_modules/d3-sankey/node_modules/d3-array/src/number.js", "../../../../../../node_modules/d3-sankey/node_modules/d3-array/src/bisect.js", "../../../../../../node_modules/d3-sankey/node_modules/d3-array/src/array.js", "../../../../../../node_modules/d3-sankey/node_modules/d3-array/src/ticks.js", "../../../../../../node_modules/d3-sankey/node_modules/d3-array/src/max.js", "../../../../../../node_modules/d3-sankey/node_modules/d3-array/src/min.js", "../../../../../../node_modules/d3-sankey/node_modules/d3-array/src/shuffle.js", "../../../../../../node_modules/d3-sankey/node_modules/d3-array/src/sum.js", "../../../../../../node_modules/d3-sankey/src/align.js", "../../../../../../node_modules/d3-sankey/src/constant.js", "../../../../../../node_modules/d3-sankey/src/sankey.js", "../../../../../../node_modules/d3-sankey/node_modules/d3-path/src/path.js", "../../../../../../node_modules/d3-sankey/node_modules/d3-shape/src/constant.js", "../../../../../../node_modules/d3-sankey/node_modules/d3-shape/src/math.js", "../../../../../../node_modules/d3-sankey/node_modules/d3-shape/src/curve/linear.js", "../../../../../../node_modules/d3-sankey/node_modules/d3-shape/src/point.js", "../../../../../../node_modules/d3-sankey/node_modules/d3-shape/src/curve/radial.js", "../../../../../../node_modules/d3-sankey/node_modules/d3-shape/src/array.js", "../../../../../../node_modules/d3-sankey/node_modules/d3-shape/src/link/index.js", "../../../../../../node_modules/d3-sankey/node_modules/d3-shape/src/symbol/diamond.js", "../../../../../../node_modules/d3-sankey/node_modules/d3-shape/src/symbol/star.js", "../../../../../../node_modules/d3-sankey/node_modules/d3-shape/src/symbol/triangle.js", "../../../../../../node_modules/d3-sankey/node_modules/d3-shape/src/symbol/wye.js", "../../../../../../node_modules/d3-sankey/node_modules/d3-shape/src/noop.js", "../../../../../../node_modules/d3-sankey/node_modules/d3-shape/src/curve/basis.js", "../../../../../../node_modules/d3-sankey/node_modules/d3-shape/src/curve/basisClosed.js", "../../../../../../node_modules/d3-sankey/node_modules/d3-shape/src/curve/basisOpen.js", "../../../../../../node_modules/d3-sankey/node_modules/d3-shape/src/curve/bundle.js", "../../../../../../node_modules/d3-sankey/node_modules/d3-shape/src/curve/cardinal.js", "../../../../../../node_modules/d3-sankey/node_modules/d3-shape/src/curve/cardinalClosed.js", "../../../../../../node_modules/d3-sankey/node_modules/d3-shape/src/curve/cardinalOpen.js", "../../../../../../node_modules/d3-sankey/node_modules/d3-shape/src/curve/catmullRom.js", "../../../../../../node_modules/d3-sankey/node_modules/d3-shape/src/curve/catmullRomClosed.js", "../../../../../../node_modules/d3-sankey/node_modules/d3-shape/src/curve/catmullRomOpen.js", "../../../../../../node_modules/d3-sankey/node_modules/d3-shape/src/curve/linearClosed.js", "../../../../../../node_modules/d3-sankey/node_modules/d3-shape/src/curve/monotone.js", "../../../../../../node_modules/d3-sankey/node_modules/d3-shape/src/curve/natural.js", "../../../../../../node_modules/d3-sankey/node_modules/d3-shape/src/curve/step.js", "../../../../../../node_modules/d3-sankey/src/sankeyLinkHorizontal.js", "../../../../../../node_modules/mermaid/dist/chunks/mermaid.core/sankeyDiagram-QLVOVGJD.mjs"], "sourcesContent": ["export default function (a, b) {\n  return a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;\n}", "import ascending from \"./ascending.js\";\nexport default function (f) {\n  let delta = f;\n  let compare = f;\n  if (f.length === 1) {\n    delta = (d, x) => f(d) - x;\n    compare = ascendingComparator(f);\n  }\n  function left(a, x, lo, hi) {\n    if (lo == null) lo = 0;\n    if (hi == null) hi = a.length;\n    while (lo < hi) {\n      const mid = lo + hi >>> 1;\n      if (compare(a[mid], x) < 0) lo = mid + 1;else hi = mid;\n    }\n    return lo;\n  }\n  function right(a, x, lo, hi) {\n    if (lo == null) lo = 0;\n    if (hi == null) hi = a.length;\n    while (lo < hi) {\n      const mid = lo + hi >>> 1;\n      if (compare(a[mid], x) > 0) hi = mid;else lo = mid + 1;\n    }\n    return lo;\n  }\n  function center(a, x, lo, hi) {\n    if (lo == null) lo = 0;\n    if (hi == null) hi = a.length;\n    const i = left(a, x, lo, hi - 1);\n    return i > lo && delta(a[i - 1], x) > -delta(a[i], x) ? i - 1 : i;\n  }\n  return {\n    left,\n    center,\n    right\n  };\n}\nfunction ascendingComparator(f) {\n  return (d, x) => ascending(f(d), x);\n}", "export default function (x) {\n  return x === null ? NaN : +x;\n}\nexport function* numbers(values, valueof) {\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        yield value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        yield value;\n      }\n    }\n  }\n}", "import ascending from \"./ascending.js\";\nimport bisector from \"./bisector.js\";\nimport number from \"./number.js\";\nconst ascendingBisect = bisector(ascending);\nexport const bisectRight = ascendingBisect.right;\nexport const bisectLeft = ascendingBisect.left;\nexport const bisectCenter = bisector(number).center;\nexport default bisectRight;", "var array = Array.prototype;\nexport var slice = array.slice;\nexport var map = array.map;", "var e10 = Math.sqrt(50),\n  e5 = Math.sqrt(10),\n  e2 = Math.sqrt(2);\nexport default function (start, stop, count) {\n  var reverse,\n    i = -1,\n    n,\n    ticks,\n    step;\n  stop = +stop, start = +start, count = +count;\n  if (start === stop && count > 0) return [start];\n  if (reverse = stop < start) n = start, start = stop, stop = n;\n  if ((step = tickIncrement(start, stop, count)) === 0 || !isFinite(step)) return [];\n  if (step > 0) {\n    let r0 = Math.round(start / step),\n      r1 = Math.round(stop / step);\n    if (r0 * step < start) ++r0;\n    if (r1 * step > stop) --r1;\n    ticks = new Array(n = r1 - r0 + 1);\n    while (++i < n) ticks[i] = (r0 + i) * step;\n  } else {\n    step = -step;\n    let r0 = Math.round(start * step),\n      r1 = Math.round(stop * step);\n    if (r0 / step < start) ++r0;\n    if (r1 / step > stop) --r1;\n    ticks = new Array(n = r1 - r0 + 1);\n    while (++i < n) ticks[i] = (r0 + i) / step;\n  }\n  if (reverse) ticks.reverse();\n  return ticks;\n}\nexport function tickIncrement(start, stop, count) {\n  var step = (stop - start) / Math.max(0, count),\n    power = Math.floor(Math.log(step) / Math.LN10),\n    error = step / Math.pow(10, power);\n  return power >= 0 ? (error >= e10 ? 10 : error >= e5 ? 5 : error >= e2 ? 2 : 1) * Math.pow(10, power) : -Math.pow(10, -power) / (error >= e10 ? 10 : error >= e5 ? 5 : error >= e2 ? 2 : 1);\n}\nexport function tickStep(start, stop, count) {\n  var step0 = Math.abs(stop - start) / Math.max(0, count),\n    step1 = Math.pow(10, Math.floor(Math.log(step0) / Math.LN10)),\n    error = step0 / step1;\n  if (error >= e10) step1 *= 10;else if (error >= e5) step1 *= 5;else if (error >= e2) step1 *= 2;\n  return stop < start ? -step1 : step1;\n}", "export default function max(values, valueof) {\n  let max;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null && (max < value || max === undefined && value >= value)) {\n        max = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (max < value || max === undefined && value >= value)) {\n        max = value;\n      }\n    }\n  }\n  return max;\n}", "export default function min(values, valueof) {\n  let min;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null && (min > value || min === undefined && value >= value)) {\n        min = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (min > value || min === undefined && value >= value)) {\n        min = value;\n      }\n    }\n  }\n  return min;\n}", "export default shuffler(Math.random);\nexport function shuffler(random) {\n  return function shuffle(array, i0 = 0, i1 = array.length) {\n    let m = i1 - (i0 = +i0);\n    while (m) {\n      const i = random() * m-- | 0,\n        t = array[m + i0];\n      array[m + i0] = array[i + i0];\n      array[i + i0] = t;\n    }\n    return array;\n  };\n}", "export default function sum(values, valueof) {\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value = +value) {\n        sum += value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if (value = +valueof(value, ++index, values)) {\n        sum += value;\n      }\n    }\n  }\n  return sum;\n}", "import { min } from \"d3-array\";\nfunction targetDepth(d) {\n  return d.target.depth;\n}\nexport function left(node) {\n  return node.depth;\n}\nexport function right(node, n) {\n  return n - 1 - node.height;\n}\nexport function justify(node, n) {\n  return node.sourceLinks.length ? node.depth : n - 1;\n}\nexport function center(node) {\n  return node.targetLinks.length ? node.depth : node.sourceLinks.length ? min(node.sourceLinks, targetDepth) - 1 : 0;\n}", "export default function constant(x) {\n  return function () {\n    return x;\n  };\n}", "import { max, min, sum } from \"d3-array\";\nimport { justify } from \"./align.js\";\nimport constant from \"./constant.js\";\nfunction ascendingSourceBreadth(a, b) {\n  return ascendingBreadth(a.source, b.source) || a.index - b.index;\n}\nfunction ascendingTargetBreadth(a, b) {\n  return ascendingBreadth(a.target, b.target) || a.index - b.index;\n}\nfunction ascendingBreadth(a, b) {\n  return a.y0 - b.y0;\n}\nfunction value(d) {\n  return d.value;\n}\nfunction defaultId(d) {\n  return d.index;\n}\nfunction defaultNodes(graph) {\n  return graph.nodes;\n}\nfunction defaultLinks(graph) {\n  return graph.links;\n}\nfunction find(nodeById, id) {\n  const node = nodeById.get(id);\n  if (!node) throw new Error(\"missing: \" + id);\n  return node;\n}\nfunction computeLinkBreadths({\n  nodes\n}) {\n  for (const node of nodes) {\n    let y0 = node.y0;\n    let y1 = y0;\n    for (const link of node.sourceLinks) {\n      link.y0 = y0 + link.width / 2;\n      y0 += link.width;\n    }\n    for (const link of node.targetLinks) {\n      link.y1 = y1 + link.width / 2;\n      y1 += link.width;\n    }\n  }\n}\nexport default function Sankey() {\n  let x0 = 0,\n    y0 = 0,\n    x1 = 1,\n    y1 = 1; // extent\n  let dx = 24; // nodeWidth\n  let dy = 8,\n    py; // nodePadding\n  let id = defaultId;\n  let align = justify;\n  let sort;\n  let linkSort;\n  let nodes = defaultNodes;\n  let links = defaultLinks;\n  let iterations = 6;\n  function sankey() {\n    const graph = {\n      nodes: nodes.apply(null, arguments),\n      links: links.apply(null, arguments)\n    };\n    computeNodeLinks(graph);\n    computeNodeValues(graph);\n    computeNodeDepths(graph);\n    computeNodeHeights(graph);\n    computeNodeBreadths(graph);\n    computeLinkBreadths(graph);\n    return graph;\n  }\n  sankey.update = function (graph) {\n    computeLinkBreadths(graph);\n    return graph;\n  };\n  sankey.nodeId = function (_) {\n    return arguments.length ? (id = typeof _ === \"function\" ? _ : constant(_), sankey) : id;\n  };\n  sankey.nodeAlign = function (_) {\n    return arguments.length ? (align = typeof _ === \"function\" ? _ : constant(_), sankey) : align;\n  };\n  sankey.nodeSort = function (_) {\n    return arguments.length ? (sort = _, sankey) : sort;\n  };\n  sankey.nodeWidth = function (_) {\n    return arguments.length ? (dx = +_, sankey) : dx;\n  };\n  sankey.nodePadding = function (_) {\n    return arguments.length ? (dy = py = +_, sankey) : dy;\n  };\n  sankey.nodes = function (_) {\n    return arguments.length ? (nodes = typeof _ === \"function\" ? _ : constant(_), sankey) : nodes;\n  };\n  sankey.links = function (_) {\n    return arguments.length ? (links = typeof _ === \"function\" ? _ : constant(_), sankey) : links;\n  };\n  sankey.linkSort = function (_) {\n    return arguments.length ? (linkSort = _, sankey) : linkSort;\n  };\n  sankey.size = function (_) {\n    return arguments.length ? (x0 = y0 = 0, x1 = +_[0], y1 = +_[1], sankey) : [x1 - x0, y1 - y0];\n  };\n  sankey.extent = function (_) {\n    return arguments.length ? (x0 = +_[0][0], x1 = +_[1][0], y0 = +_[0][1], y1 = +_[1][1], sankey) : [[x0, y0], [x1, y1]];\n  };\n  sankey.iterations = function (_) {\n    return arguments.length ? (iterations = +_, sankey) : iterations;\n  };\n  function computeNodeLinks({\n    nodes,\n    links\n  }) {\n    for (const [i, node] of nodes.entries()) {\n      node.index = i;\n      node.sourceLinks = [];\n      node.targetLinks = [];\n    }\n    const nodeById = new Map(nodes.map((d, i) => [id(d, i, nodes), d]));\n    for (const [i, link] of links.entries()) {\n      link.index = i;\n      let {\n        source,\n        target\n      } = link;\n      if (typeof source !== \"object\") source = link.source = find(nodeById, source);\n      if (typeof target !== \"object\") target = link.target = find(nodeById, target);\n      source.sourceLinks.push(link);\n      target.targetLinks.push(link);\n    }\n    if (linkSort != null) {\n      for (const {\n        sourceLinks,\n        targetLinks\n      } of nodes) {\n        sourceLinks.sort(linkSort);\n        targetLinks.sort(linkSort);\n      }\n    }\n  }\n  function computeNodeValues({\n    nodes\n  }) {\n    for (const node of nodes) {\n      node.value = node.fixedValue === undefined ? Math.max(sum(node.sourceLinks, value), sum(node.targetLinks, value)) : node.fixedValue;\n    }\n  }\n  function computeNodeDepths({\n    nodes\n  }) {\n    const n = nodes.length;\n    let current = new Set(nodes);\n    let next = new Set();\n    let x = 0;\n    while (current.size) {\n      for (const node of current) {\n        node.depth = x;\n        for (const {\n          target\n        } of node.sourceLinks) {\n          next.add(target);\n        }\n      }\n      if (++x > n) throw new Error(\"circular link\");\n      current = next;\n      next = new Set();\n    }\n  }\n  function computeNodeHeights({\n    nodes\n  }) {\n    const n = nodes.length;\n    let current = new Set(nodes);\n    let next = new Set();\n    let x = 0;\n    while (current.size) {\n      for (const node of current) {\n        node.height = x;\n        for (const {\n          source\n        } of node.targetLinks) {\n          next.add(source);\n        }\n      }\n      if (++x > n) throw new Error(\"circular link\");\n      current = next;\n      next = new Set();\n    }\n  }\n  function computeNodeLayers({\n    nodes\n  }) {\n    const x = max(nodes, d => d.depth) + 1;\n    const kx = (x1 - x0 - dx) / (x - 1);\n    const columns = new Array(x);\n    for (const node of nodes) {\n      const i = Math.max(0, Math.min(x - 1, Math.floor(align.call(null, node, x))));\n      node.layer = i;\n      node.x0 = x0 + i * kx;\n      node.x1 = node.x0 + dx;\n      if (columns[i]) columns[i].push(node);else columns[i] = [node];\n    }\n    if (sort) for (const column of columns) {\n      column.sort(sort);\n    }\n    return columns;\n  }\n  function initializeNodeBreadths(columns) {\n    const ky = min(columns, c => (y1 - y0 - (c.length - 1) * py) / sum(c, value));\n    for (const nodes of columns) {\n      let y = y0;\n      for (const node of nodes) {\n        node.y0 = y;\n        node.y1 = y + node.value * ky;\n        y = node.y1 + py;\n        for (const link of node.sourceLinks) {\n          link.width = link.value * ky;\n        }\n      }\n      y = (y1 - y + py) / (nodes.length + 1);\n      for (let i = 0; i < nodes.length; ++i) {\n        const node = nodes[i];\n        node.y0 += y * (i + 1);\n        node.y1 += y * (i + 1);\n      }\n      reorderLinks(nodes);\n    }\n  }\n  function computeNodeBreadths(graph) {\n    const columns = computeNodeLayers(graph);\n    py = Math.min(dy, (y1 - y0) / (max(columns, c => c.length) - 1));\n    initializeNodeBreadths(columns);\n    for (let i = 0; i < iterations; ++i) {\n      const alpha = Math.pow(0.99, i);\n      const beta = Math.max(1 - alpha, (i + 1) / iterations);\n      relaxRightToLeft(columns, alpha, beta);\n      relaxLeftToRight(columns, alpha, beta);\n    }\n  }\n\n  // Reposition each node based on its incoming (target) links.\n  function relaxLeftToRight(columns, alpha, beta) {\n    for (let i = 1, n = columns.length; i < n; ++i) {\n      const column = columns[i];\n      for (const target of column) {\n        let y = 0;\n        let w = 0;\n        for (const {\n          source,\n          value\n        } of target.targetLinks) {\n          let v = value * (target.layer - source.layer);\n          y += targetTop(source, target) * v;\n          w += v;\n        }\n        if (!(w > 0)) continue;\n        let dy = (y / w - target.y0) * alpha;\n        target.y0 += dy;\n        target.y1 += dy;\n        reorderNodeLinks(target);\n      }\n      if (sort === undefined) column.sort(ascendingBreadth);\n      resolveCollisions(column, beta);\n    }\n  }\n\n  // Reposition each node based on its outgoing (source) links.\n  function relaxRightToLeft(columns, alpha, beta) {\n    for (let n = columns.length, i = n - 2; i >= 0; --i) {\n      const column = columns[i];\n      for (const source of column) {\n        let y = 0;\n        let w = 0;\n        for (const {\n          target,\n          value\n        } of source.sourceLinks) {\n          let v = value * (target.layer - source.layer);\n          y += sourceTop(source, target) * v;\n          w += v;\n        }\n        if (!(w > 0)) continue;\n        let dy = (y / w - source.y0) * alpha;\n        source.y0 += dy;\n        source.y1 += dy;\n        reorderNodeLinks(source);\n      }\n      if (sort === undefined) column.sort(ascendingBreadth);\n      resolveCollisions(column, beta);\n    }\n  }\n  function resolveCollisions(nodes, alpha) {\n    const i = nodes.length >> 1;\n    const subject = nodes[i];\n    resolveCollisionsBottomToTop(nodes, subject.y0 - py, i - 1, alpha);\n    resolveCollisionsTopToBottom(nodes, subject.y1 + py, i + 1, alpha);\n    resolveCollisionsBottomToTop(nodes, y1, nodes.length - 1, alpha);\n    resolveCollisionsTopToBottom(nodes, y0, 0, alpha);\n  }\n\n  // Push any overlapping nodes down.\n  function resolveCollisionsTopToBottom(nodes, y, i, alpha) {\n    for (; i < nodes.length; ++i) {\n      const node = nodes[i];\n      const dy = (y - node.y0) * alpha;\n      if (dy > 1e-6) node.y0 += dy, node.y1 += dy;\n      y = node.y1 + py;\n    }\n  }\n\n  // Push any overlapping nodes up.\n  function resolveCollisionsBottomToTop(nodes, y, i, alpha) {\n    for (; i >= 0; --i) {\n      const node = nodes[i];\n      const dy = (node.y1 - y) * alpha;\n      if (dy > 1e-6) node.y0 -= dy, node.y1 -= dy;\n      y = node.y0 - py;\n    }\n  }\n  function reorderNodeLinks({\n    sourceLinks,\n    targetLinks\n  }) {\n    if (linkSort === undefined) {\n      for (const {\n        source: {\n          sourceLinks\n        }\n      } of targetLinks) {\n        sourceLinks.sort(ascendingTargetBreadth);\n      }\n      for (const {\n        target: {\n          targetLinks\n        }\n      } of sourceLinks) {\n        targetLinks.sort(ascendingSourceBreadth);\n      }\n    }\n  }\n  function reorderLinks(nodes) {\n    if (linkSort === undefined) {\n      for (const {\n        sourceLinks,\n        targetLinks\n      } of nodes) {\n        sourceLinks.sort(ascendingTargetBreadth);\n        targetLinks.sort(ascendingSourceBreadth);\n      }\n    }\n  }\n\n  // Returns the target.y0 that would produce an ideal link from source to target.\n  function targetTop(source, target) {\n    let y = source.y0 - (source.sourceLinks.length - 1) * py / 2;\n    for (const {\n      target: node,\n      width\n    } of source.sourceLinks) {\n      if (node === target) break;\n      y += width + py;\n    }\n    for (const {\n      source: node,\n      width\n    } of target.targetLinks) {\n      if (node === source) break;\n      y -= width;\n    }\n    return y;\n  }\n\n  // Returns the source.y0 that would produce an ideal link from source to target.\n  function sourceTop(source, target) {\n    let y = target.y0 - (target.targetLinks.length - 1) * py / 2;\n    for (const {\n      source: node,\n      width\n    } of target.targetLinks) {\n      if (node === source) break;\n      y += width + py;\n    }\n    for (const {\n      target: node,\n      width\n    } of source.sourceLinks) {\n      if (node === target) break;\n      y -= width;\n    }\n    return y;\n  }\n  return sankey;\n}", "var pi = Math.PI,\n  tau = 2 * pi,\n  epsilon = 1e-6,\n  tauEpsilon = tau - epsilon;\nfunction Path() {\n  this._x0 = this._y0 =\n  // start of current subpath\n  this._x1 = this._y1 = null; // end of current subpath\n  this._ = \"\";\n}\nfunction path() {\n  return new Path();\n}\nPath.prototype = path.prototype = {\n  constructor: Path,\n  moveTo: function (x, y) {\n    this._ += \"M\" + (this._x0 = this._x1 = +x) + \",\" + (this._y0 = this._y1 = +y);\n  },\n  closePath: function () {\n    if (this._x1 !== null) {\n      this._x1 = this._x0, this._y1 = this._y0;\n      this._ += \"Z\";\n    }\n  },\n  lineTo: function (x, y) {\n    this._ += \"L\" + (this._x1 = +x) + \",\" + (this._y1 = +y);\n  },\n  quadraticCurveTo: function (x1, y1, x, y) {\n    this._ += \"Q\" + +x1 + \",\" + +y1 + \",\" + (this._x1 = +x) + \",\" + (this._y1 = +y);\n  },\n  bezierCurveTo: function (x1, y1, x2, y2, x, y) {\n    this._ += \"C\" + +x1 + \",\" + +y1 + \",\" + +x2 + \",\" + +y2 + \",\" + (this._x1 = +x) + \",\" + (this._y1 = +y);\n  },\n  arcTo: function (x1, y1, x2, y2, r) {\n    x1 = +x1, y1 = +y1, x2 = +x2, y2 = +y2, r = +r;\n    var x0 = this._x1,\n      y0 = this._y1,\n      x21 = x2 - x1,\n      y21 = y2 - y1,\n      x01 = x0 - x1,\n      y01 = y0 - y1,\n      l01_2 = x01 * x01 + y01 * y01;\n\n    // Is the radius negative? Error.\n    if (r < 0) throw new Error(\"negative radius: \" + r);\n\n    // Is this path empty? Move to (x1,y1).\n    if (this._x1 === null) {\n      this._ += \"M\" + (this._x1 = x1) + \",\" + (this._y1 = y1);\n    }\n\n    // Or, is (x1,y1) coincident with (x0,y0)? Do nothing.\n    else if (!(l01_2 > epsilon)) ;\n\n    // Or, are (x0,y0), (x1,y1) and (x2,y2) collinear?\n    // Equivalently, is (x1,y1) coincident with (x2,y2)?\n    // Or, is the radius zero? Line to (x1,y1).\n    else if (!(Math.abs(y01 * x21 - y21 * x01) > epsilon) || !r) {\n      this._ += \"L\" + (this._x1 = x1) + \",\" + (this._y1 = y1);\n    }\n\n    // Otherwise, draw an arc!\n    else {\n      var x20 = x2 - x0,\n        y20 = y2 - y0,\n        l21_2 = x21 * x21 + y21 * y21,\n        l20_2 = x20 * x20 + y20 * y20,\n        l21 = Math.sqrt(l21_2),\n        l01 = Math.sqrt(l01_2),\n        l = r * Math.tan((pi - Math.acos((l21_2 + l01_2 - l20_2) / (2 * l21 * l01))) / 2),\n        t01 = l / l01,\n        t21 = l / l21;\n\n      // If the start tangent is not coincident with (x0,y0), line to.\n      if (Math.abs(t01 - 1) > epsilon) {\n        this._ += \"L\" + (x1 + t01 * x01) + \",\" + (y1 + t01 * y01);\n      }\n      this._ += \"A\" + r + \",\" + r + \",0,0,\" + +(y01 * x20 > x01 * y20) + \",\" + (this._x1 = x1 + t21 * x21) + \",\" + (this._y1 = y1 + t21 * y21);\n    }\n  },\n  arc: function (x, y, r, a0, a1, ccw) {\n    x = +x, y = +y, r = +r, ccw = !!ccw;\n    var dx = r * Math.cos(a0),\n      dy = r * Math.sin(a0),\n      x0 = x + dx,\n      y0 = y + dy,\n      cw = 1 ^ ccw,\n      da = ccw ? a0 - a1 : a1 - a0;\n\n    // Is the radius negative? Error.\n    if (r < 0) throw new Error(\"negative radius: \" + r);\n\n    // Is this path empty? Move to (x0,y0).\n    if (this._x1 === null) {\n      this._ += \"M\" + x0 + \",\" + y0;\n    }\n\n    // Or, is (x0,y0) not coincident with the previous point? Line to (x0,y0).\n    else if (Math.abs(this._x1 - x0) > epsilon || Math.abs(this._y1 - y0) > epsilon) {\n      this._ += \"L\" + x0 + \",\" + y0;\n    }\n\n    // Is this arc empty? We’re done.\n    if (!r) return;\n\n    // Does the angle go the wrong way? Flip the direction.\n    if (da < 0) da = da % tau + tau;\n\n    // Is this a complete circle? Draw two arcs to complete the circle.\n    if (da > tauEpsilon) {\n      this._ += \"A\" + r + \",\" + r + \",0,1,\" + cw + \",\" + (x - dx) + \",\" + (y - dy) + \"A\" + r + \",\" + r + \",0,1,\" + cw + \",\" + (this._x1 = x0) + \",\" + (this._y1 = y0);\n    }\n\n    // Is this arc non-empty? Draw an arc!\n    else if (da > epsilon) {\n      this._ += \"A\" + r + \",\" + r + \",0,\" + +(da >= pi) + \",\" + cw + \",\" + (this._x1 = x + r * Math.cos(a1)) + \",\" + (this._y1 = y + r * Math.sin(a1));\n    }\n  },\n  rect: function (x, y, w, h) {\n    this._ += \"M\" + (this._x0 = this._x1 = +x) + \",\" + (this._y0 = this._y1 = +y) + \"h\" + +w + \"v\" + +h + \"h\" + -w + \"Z\";\n  },\n  toString: function () {\n    return this._;\n  }\n};\nexport default path;", "export default function (x) {\n  return function constant() {\n    return x;\n  };\n}", "export var abs = Math.abs;\nexport var atan2 = Math.atan2;\nexport var cos = Math.cos;\nexport var max = Math.max;\nexport var min = Math.min;\nexport var sin = Math.sin;\nexport var sqrt = Math.sqrt;\nexport var epsilon = 1e-12;\nexport var pi = Math.PI;\nexport var halfPi = pi / 2;\nexport var tau = 2 * pi;\nexport function acos(x) {\n  return x > 1 ? 0 : x < -1 ? pi : Math.acos(x);\n}\nexport function asin(x) {\n  return x >= 1 ? halfPi : x <= -1 ? -halfPi : Math.asin(x);\n}", "function Linear(context) {\n  this._context = context;\n}\nLinear.prototype = {\n  areaStart: function () {\n    this._line = 0;\n  },\n  areaEnd: function () {\n    this._line = NaN;\n  },\n  lineStart: function () {\n    this._point = 0;\n  },\n  lineEnd: function () {\n    if (this._line || this._line !== 0 && this._point === 1) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function (x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0:\n        this._point = 1;\n        this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y);\n        break;\n      case 1:\n        this._point = 2;\n      // proceed\n      default:\n        this._context.lineTo(x, y);\n        break;\n    }\n  }\n};\nexport default function (context) {\n  return new Linear(context);\n}", "export function x(p) {\n  return p[0];\n}\nexport function y(p) {\n  return p[1];\n}", "import curveLinear from \"./linear.js\";\nexport var curveRadialLinear = curveRadial(curveLinear);\nfunction Radial(curve) {\n  this._curve = curve;\n}\nRadial.prototype = {\n  areaStart: function () {\n    this._curve.areaStart();\n  },\n  areaEnd: function () {\n    this._curve.areaEnd();\n  },\n  lineStart: function () {\n    this._curve.lineStart();\n  },\n  lineEnd: function () {\n    this._curve.lineEnd();\n  },\n  point: function (a, r) {\n    this._curve.point(r * Math.sin(a), r * -Math.cos(a));\n  }\n};\nexport default function curveRadial(curve) {\n  function radial(context) {\n    return new Radial(curve(context));\n  }\n  radial._curve = curve;\n  return radial;\n}", "export var slice = Array.prototype.slice;", "import { path } from \"d3-path\";\nimport { slice } from \"../array.js\";\nimport constant from \"../constant.js\";\nimport { x as pointX, y as pointY } from \"../point.js\";\nimport pointRadial from \"../pointRadial.js\";\nfunction linkSource(d) {\n  return d.source;\n}\nfunction linkTarget(d) {\n  return d.target;\n}\nfunction link(curve) {\n  var source = linkSource,\n    target = linkTarget,\n    x = pointX,\n    y = pointY,\n    context = null;\n  function link() {\n    var buffer,\n      argv = slice.call(arguments),\n      s = source.apply(this, argv),\n      t = target.apply(this, argv);\n    if (!context) context = buffer = path();\n    curve(context, +x.apply(this, (argv[0] = s, argv)), +y.apply(this, argv), +x.apply(this, (argv[0] = t, argv)), +y.apply(this, argv));\n    if (buffer) return context = null, buffer + \"\" || null;\n  }\n  link.source = function (_) {\n    return arguments.length ? (source = _, link) : source;\n  };\n  link.target = function (_) {\n    return arguments.length ? (target = _, link) : target;\n  };\n  link.x = function (_) {\n    return arguments.length ? (x = typeof _ === \"function\" ? _ : constant(+_), link) : x;\n  };\n  link.y = function (_) {\n    return arguments.length ? (y = typeof _ === \"function\" ? _ : constant(+_), link) : y;\n  };\n  link.context = function (_) {\n    return arguments.length ? (context = _ == null ? null : _, link) : context;\n  };\n  return link;\n}\nfunction curveHorizontal(context, x0, y0, x1, y1) {\n  context.moveTo(x0, y0);\n  context.bezierCurveTo(x0 = (x0 + x1) / 2, y0, x0, y1, x1, y1);\n}\nfunction curveVertical(context, x0, y0, x1, y1) {\n  context.moveTo(x0, y0);\n  context.bezierCurveTo(x0, y0 = (y0 + y1) / 2, x1, y0, x1, y1);\n}\nfunction curveRadial(context, x0, y0, x1, y1) {\n  var p0 = pointRadial(x0, y0),\n    p1 = pointRadial(x0, y0 = (y0 + y1) / 2),\n    p2 = pointRadial(x1, y0),\n    p3 = pointRadial(x1, y1);\n  context.moveTo(p0[0], p0[1]);\n  context.bezierCurveTo(p1[0], p1[1], p2[0], p2[1], p3[0], p3[1]);\n}\nexport function linkHorizontal() {\n  return link(curveHorizontal);\n}\nexport function linkVertical() {\n  return link(curveVertical);\n}\nexport function linkRadial() {\n  var l = link(curveRadial);\n  l.angle = l.x, delete l.x;\n  l.radius = l.y, delete l.y;\n  return l;\n}", "var tan30 = Math.sqrt(1 / 3),\n  tan30_2 = tan30 * 2;\nexport default {\n  draw: function (context, size) {\n    var y = Math.sqrt(size / tan30_2),\n      x = y * tan30;\n    context.moveTo(0, -y);\n    context.lineTo(x, 0);\n    context.lineTo(0, y);\n    context.lineTo(-x, 0);\n    context.closePath();\n  }\n};", "import { pi, tau } from \"../math.js\";\nvar ka = 0.89081309152928522810,\n  kr = Math.sin(pi / 10) / Math.sin(7 * pi / 10),\n  kx = Math.sin(tau / 10) * kr,\n  ky = -Math.cos(tau / 10) * kr;\nexport default {\n  draw: function (context, size) {\n    var r = Math.sqrt(size * ka),\n      x = kx * r,\n      y = ky * r;\n    context.moveTo(0, -r);\n    context.lineTo(x, y);\n    for (var i = 1; i < 5; ++i) {\n      var a = tau * i / 5,\n        c = Math.cos(a),\n        s = Math.sin(a);\n      context.lineTo(s * r, -c * r);\n      context.lineTo(c * x - s * y, s * x + c * y);\n    }\n    context.closePath();\n  }\n};", "var sqrt3 = Math.sqrt(3);\nexport default {\n  draw: function (context, size) {\n    var y = -Math.sqrt(size / (sqrt3 * 3));\n    context.moveTo(0, y * 2);\n    context.lineTo(-sqrt3 * y, -y);\n    context.lineTo(sqrt3 * y, -y);\n    context.closePath();\n  }\n};", "var c = -0.5,\n  s = Math.sqrt(3) / 2,\n  k = 1 / Math.sqrt(12),\n  a = (k / 2 + 1) * 3;\nexport default {\n  draw: function (context, size) {\n    var r = Math.sqrt(size / a),\n      x0 = r / 2,\n      y0 = r * k,\n      x1 = x0,\n      y1 = r * k + r,\n      x2 = -x1,\n      y2 = y1;\n    context.moveTo(x0, y0);\n    context.lineTo(x1, y1);\n    context.lineTo(x2, y2);\n    context.lineTo(c * x0 - s * y0, s * x0 + c * y0);\n    context.lineTo(c * x1 - s * y1, s * x1 + c * y1);\n    context.lineTo(c * x2 - s * y2, s * x2 + c * y2);\n    context.lineTo(c * x0 + s * y0, c * y0 - s * x0);\n    context.lineTo(c * x1 + s * y1, c * y1 - s * x1);\n    context.lineTo(c * x2 + s * y2, c * y2 - s * x2);\n    context.closePath();\n  }\n};", "export default function () {}", "export function point(that, x, y) {\n  that._context.bezierCurveTo((2 * that._x0 + that._x1) / 3, (2 * that._y0 + that._y1) / 3, (that._x0 + 2 * that._x1) / 3, (that._y0 + 2 * that._y1) / 3, (that._x0 + 4 * that._x1 + x) / 6, (that._y0 + 4 * that._y1 + y) / 6);\n}\nexport function Basis(context) {\n  this._context = context;\n}\nBasis.prototype = {\n  areaStart: function () {\n    this._line = 0;\n  },\n  areaEnd: function () {\n    this._line = NaN;\n  },\n  lineStart: function () {\n    this._x0 = this._x1 = this._y0 = this._y1 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function () {\n    switch (this._point) {\n      case 3:\n        point(this, this._x1, this._y1);\n      // proceed\n      case 2:\n        this._context.lineTo(this._x1, this._y1);\n        break;\n    }\n    if (this._line || this._line !== 0 && this._point === 1) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function (x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0:\n        this._point = 1;\n        this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y);\n        break;\n      case 1:\n        this._point = 2;\n        break;\n      case 2:\n        this._point = 3;\n        this._context.lineTo((5 * this._x0 + this._x1) / 6, (5 * this._y0 + this._y1) / 6);\n      // proceed\n      default:\n        point(this, x, y);\n        break;\n    }\n    this._x0 = this._x1, this._x1 = x;\n    this._y0 = this._y1, this._y1 = y;\n  }\n};\nexport default function (context) {\n  return new Basis(context);\n}", "import noop from \"../noop.js\";\nimport { point } from \"./basis.js\";\nfunction BasisClosed(context) {\n  this._context = context;\n}\nBasisClosed.prototype = {\n  areaStart: noop,\n  areaEnd: noop,\n  lineStart: function () {\n    this._x0 = this._x1 = this._x2 = this._x3 = this._x4 = this._y0 = this._y1 = this._y2 = this._y3 = this._y4 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function () {\n    switch (this._point) {\n      case 1:\n        {\n          this._context.moveTo(this._x2, this._y2);\n          this._context.closePath();\n          break;\n        }\n      case 2:\n        {\n          this._context.moveTo((this._x2 + 2 * this._x3) / 3, (this._y2 + 2 * this._y3) / 3);\n          this._context.lineTo((this._x3 + 2 * this._x2) / 3, (this._y3 + 2 * this._y2) / 3);\n          this._context.closePath();\n          break;\n        }\n      case 3:\n        {\n          this.point(this._x2, this._y2);\n          this.point(this._x3, this._y3);\n          this.point(this._x4, this._y4);\n          break;\n        }\n    }\n  },\n  point: function (x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0:\n        this._point = 1;\n        this._x2 = x, this._y2 = y;\n        break;\n      case 1:\n        this._point = 2;\n        this._x3 = x, this._y3 = y;\n        break;\n      case 2:\n        this._point = 3;\n        this._x4 = x, this._y4 = y;\n        this._context.moveTo((this._x0 + 4 * this._x1 + x) / 6, (this._y0 + 4 * this._y1 + y) / 6);\n        break;\n      default:\n        point(this, x, y);\n        break;\n    }\n    this._x0 = this._x1, this._x1 = x;\n    this._y0 = this._y1, this._y1 = y;\n  }\n};\nexport default function (context) {\n  return new BasisClosed(context);\n}", "import { point } from \"./basis.js\";\nfunction BasisOpen(context) {\n  this._context = context;\n}\nBasisOpen.prototype = {\n  areaStart: function () {\n    this._line = 0;\n  },\n  areaEnd: function () {\n    this._line = NaN;\n  },\n  lineStart: function () {\n    this._x0 = this._x1 = this._y0 = this._y1 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function () {\n    if (this._line || this._line !== 0 && this._point === 3) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function (x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0:\n        this._point = 1;\n        break;\n      case 1:\n        this._point = 2;\n        break;\n      case 2:\n        this._point = 3;\n        var x0 = (this._x0 + 4 * this._x1 + x) / 6,\n          y0 = (this._y0 + 4 * this._y1 + y) / 6;\n        this._line ? this._context.lineTo(x0, y0) : this._context.moveTo(x0, y0);\n        break;\n      case 3:\n        this._point = 4;\n      // proceed\n      default:\n        point(this, x, y);\n        break;\n    }\n    this._x0 = this._x1, this._x1 = x;\n    this._y0 = this._y1, this._y1 = y;\n  }\n};\nexport default function (context) {\n  return new BasisOpen(context);\n}", "import { Basis } from \"./basis.js\";\nfunction Bundle(context, beta) {\n  this._basis = new Basis(context);\n  this._beta = beta;\n}\nBundle.prototype = {\n  lineStart: function () {\n    this._x = [];\n    this._y = [];\n    this._basis.lineStart();\n  },\n  lineEnd: function () {\n    var x = this._x,\n      y = this._y,\n      j = x.length - 1;\n    if (j > 0) {\n      var x0 = x[0],\n        y0 = y[0],\n        dx = x[j] - x0,\n        dy = y[j] - y0,\n        i = -1,\n        t;\n      while (++i <= j) {\n        t = i / j;\n        this._basis.point(this._beta * x[i] + (1 - this._beta) * (x0 + t * dx), this._beta * y[i] + (1 - this._beta) * (y0 + t * dy));\n      }\n    }\n    this._x = this._y = null;\n    this._basis.lineEnd();\n  },\n  point: function (x, y) {\n    this._x.push(+x);\n    this._y.push(+y);\n  }\n};\nexport default (function custom(beta) {\n  function bundle(context) {\n    return beta === 1 ? new Basis(context) : new Bundle(context, beta);\n  }\n  bundle.beta = function (beta) {\n    return custom(+beta);\n  };\n  return bundle;\n})(0.85);", "export function point(that, x, y) {\n  that._context.bezierCurveTo(that._x1 + that._k * (that._x2 - that._x0), that._y1 + that._k * (that._y2 - that._y0), that._x2 + that._k * (that._x1 - x), that._y2 + that._k * (that._y1 - y), that._x2, that._y2);\n}\nexport function Cardinal(context, tension) {\n  this._context = context;\n  this._k = (1 - tension) / 6;\n}\nCardinal.prototype = {\n  areaStart: function () {\n    this._line = 0;\n  },\n  areaEnd: function () {\n    this._line = NaN;\n  },\n  lineStart: function () {\n    this._x0 = this._x1 = this._x2 = this._y0 = this._y1 = this._y2 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function () {\n    switch (this._point) {\n      case 2:\n        this._context.lineTo(this._x2, this._y2);\n        break;\n      case 3:\n        point(this, this._x1, this._y1);\n        break;\n    }\n    if (this._line || this._line !== 0 && this._point === 1) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function (x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0:\n        this._point = 1;\n        this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y);\n        break;\n      case 1:\n        this._point = 2;\n        this._x1 = x, this._y1 = y;\n        break;\n      case 2:\n        this._point = 3;\n      // proceed\n      default:\n        point(this, x, y);\n        break;\n    }\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\nexport default (function custom(tension) {\n  function cardinal(context) {\n    return new Cardinal(context, tension);\n  }\n  cardinal.tension = function (tension) {\n    return custom(+tension);\n  };\n  return cardinal;\n})(0);", "import noop from \"../noop.js\";\nimport { point } from \"./cardinal.js\";\nexport function CardinalClosed(context, tension) {\n  this._context = context;\n  this._k = (1 - tension) / 6;\n}\nCardinalClosed.prototype = {\n  areaStart: noop,\n  areaEnd: noop,\n  lineStart: function () {\n    this._x0 = this._x1 = this._x2 = this._x3 = this._x4 = this._x5 = this._y0 = this._y1 = this._y2 = this._y3 = this._y4 = this._y5 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function () {\n    switch (this._point) {\n      case 1:\n        {\n          this._context.moveTo(this._x3, this._y3);\n          this._context.closePath();\n          break;\n        }\n      case 2:\n        {\n          this._context.lineTo(this._x3, this._y3);\n          this._context.closePath();\n          break;\n        }\n      case 3:\n        {\n          this.point(this._x3, this._y3);\n          this.point(this._x4, this._y4);\n          this.point(this._x5, this._y5);\n          break;\n        }\n    }\n  },\n  point: function (x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0:\n        this._point = 1;\n        this._x3 = x, this._y3 = y;\n        break;\n      case 1:\n        this._point = 2;\n        this._context.moveTo(this._x4 = x, this._y4 = y);\n        break;\n      case 2:\n        this._point = 3;\n        this._x5 = x, this._y5 = y;\n        break;\n      default:\n        point(this, x, y);\n        break;\n    }\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\nexport default (function custom(tension) {\n  function cardinal(context) {\n    return new CardinalClosed(context, tension);\n  }\n  cardinal.tension = function (tension) {\n    return custom(+tension);\n  };\n  return cardinal;\n})(0);", "import { point } from \"./cardinal.js\";\nexport function CardinalOpen(context, tension) {\n  this._context = context;\n  this._k = (1 - tension) / 6;\n}\nCardinalOpen.prototype = {\n  areaStart: function () {\n    this._line = 0;\n  },\n  areaEnd: function () {\n    this._line = NaN;\n  },\n  lineStart: function () {\n    this._x0 = this._x1 = this._x2 = this._y0 = this._y1 = this._y2 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function () {\n    if (this._line || this._line !== 0 && this._point === 3) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function (x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0:\n        this._point = 1;\n        break;\n      case 1:\n        this._point = 2;\n        break;\n      case 2:\n        this._point = 3;\n        this._line ? this._context.lineTo(this._x2, this._y2) : this._context.moveTo(this._x2, this._y2);\n        break;\n      case 3:\n        this._point = 4;\n      // proceed\n      default:\n        point(this, x, y);\n        break;\n    }\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\nexport default (function custom(tension) {\n  function cardinal(context) {\n    return new CardinalOpen(context, tension);\n  }\n  cardinal.tension = function (tension) {\n    return custom(+tension);\n  };\n  return cardinal;\n})(0);", "import { epsilon } from \"../math.js\";\nimport { <PERSON> } from \"./cardinal.js\";\nexport function point(that, x, y) {\n  var x1 = that._x1,\n    y1 = that._y1,\n    x2 = that._x2,\n    y2 = that._y2;\n  if (that._l01_a > epsilon) {\n    var a = 2 * that._l01_2a + 3 * that._l01_a * that._l12_a + that._l12_2a,\n      n = 3 * that._l01_a * (that._l01_a + that._l12_a);\n    x1 = (x1 * a - that._x0 * that._l12_2a + that._x2 * that._l01_2a) / n;\n    y1 = (y1 * a - that._y0 * that._l12_2a + that._y2 * that._l01_2a) / n;\n  }\n  if (that._l23_a > epsilon) {\n    var b = 2 * that._l23_2a + 3 * that._l23_a * that._l12_a + that._l12_2a,\n      m = 3 * that._l23_a * (that._l23_a + that._l12_a);\n    x2 = (x2 * b + that._x1 * that._l23_2a - x * that._l12_2a) / m;\n    y2 = (y2 * b + that._y1 * that._l23_2a - y * that._l12_2a) / m;\n  }\n  that._context.bezierCurveTo(x1, y1, x2, y2, that._x2, that._y2);\n}\nfunction CatmullRom(context, alpha) {\n  this._context = context;\n  this._alpha = alpha;\n}\nCatmullRom.prototype = {\n  areaStart: function () {\n    this._line = 0;\n  },\n  areaEnd: function () {\n    this._line = NaN;\n  },\n  lineStart: function () {\n    this._x0 = this._x1 = this._x2 = this._y0 = this._y1 = this._y2 = NaN;\n    this._l01_a = this._l12_a = this._l23_a = this._l01_2a = this._l12_2a = this._l23_2a = this._point = 0;\n  },\n  lineEnd: function () {\n    switch (this._point) {\n      case 2:\n        this._context.lineTo(this._x2, this._y2);\n        break;\n      case 3:\n        this.point(this._x2, this._y2);\n        break;\n    }\n    if (this._line || this._line !== 0 && this._point === 1) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function (x, y) {\n    x = +x, y = +y;\n    if (this._point) {\n      var x23 = this._x2 - x,\n        y23 = this._y2 - y;\n      this._l23_a = Math.sqrt(this._l23_2a = Math.pow(x23 * x23 + y23 * y23, this._alpha));\n    }\n    switch (this._point) {\n      case 0:\n        this._point = 1;\n        this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y);\n        break;\n      case 1:\n        this._point = 2;\n        break;\n      case 2:\n        this._point = 3;\n      // proceed\n      default:\n        point(this, x, y);\n        break;\n    }\n    this._l01_a = this._l12_a, this._l12_a = this._l23_a;\n    this._l01_2a = this._l12_2a, this._l12_2a = this._l23_2a;\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\nexport default (function custom(alpha) {\n  function catmullRom(context) {\n    return alpha ? new CatmullRom(context, alpha) : new Cardinal(context, 0);\n  }\n  catmullRom.alpha = function (alpha) {\n    return custom(+alpha);\n  };\n  return catmullRom;\n})(0.5);", "import { CardinalClosed } from \"./cardinalClosed.js\";\nimport noop from \"../noop.js\";\nimport { point } from \"./catmullRom.js\";\nfunction CatmullRomClosed(context, alpha) {\n  this._context = context;\n  this._alpha = alpha;\n}\nCatmullRomClosed.prototype = {\n  areaStart: noop,\n  areaEnd: noop,\n  lineStart: function () {\n    this._x0 = this._x1 = this._x2 = this._x3 = this._x4 = this._x5 = this._y0 = this._y1 = this._y2 = this._y3 = this._y4 = this._y5 = NaN;\n    this._l01_a = this._l12_a = this._l23_a = this._l01_2a = this._l12_2a = this._l23_2a = this._point = 0;\n  },\n  lineEnd: function () {\n    switch (this._point) {\n      case 1:\n        {\n          this._context.moveTo(this._x3, this._y3);\n          this._context.closePath();\n          break;\n        }\n      case 2:\n        {\n          this._context.lineTo(this._x3, this._y3);\n          this._context.closePath();\n          break;\n        }\n      case 3:\n        {\n          this.point(this._x3, this._y3);\n          this.point(this._x4, this._y4);\n          this.point(this._x5, this._y5);\n          break;\n        }\n    }\n  },\n  point: function (x, y) {\n    x = +x, y = +y;\n    if (this._point) {\n      var x23 = this._x2 - x,\n        y23 = this._y2 - y;\n      this._l23_a = Math.sqrt(this._l23_2a = Math.pow(x23 * x23 + y23 * y23, this._alpha));\n    }\n    switch (this._point) {\n      case 0:\n        this._point = 1;\n        this._x3 = x, this._y3 = y;\n        break;\n      case 1:\n        this._point = 2;\n        this._context.moveTo(this._x4 = x, this._y4 = y);\n        break;\n      case 2:\n        this._point = 3;\n        this._x5 = x, this._y5 = y;\n        break;\n      default:\n        point(this, x, y);\n        break;\n    }\n    this._l01_a = this._l12_a, this._l12_a = this._l23_a;\n    this._l01_2a = this._l12_2a, this._l12_2a = this._l23_2a;\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\nexport default (function custom(alpha) {\n  function catmullRom(context) {\n    return alpha ? new CatmullRomClosed(context, alpha) : new CardinalClosed(context, 0);\n  }\n  catmullRom.alpha = function (alpha) {\n    return custom(+alpha);\n  };\n  return catmullRom;\n})(0.5);", "import { <PERSON><PERSON><PERSON> } from \"./cardinalOpen.js\";\nimport { point } from \"./catmullRom.js\";\nfunction CatmullRomOpen(context, alpha) {\n  this._context = context;\n  this._alpha = alpha;\n}\nCatmullRomOpen.prototype = {\n  areaStart: function () {\n    this._line = 0;\n  },\n  areaEnd: function () {\n    this._line = NaN;\n  },\n  lineStart: function () {\n    this._x0 = this._x1 = this._x2 = this._y0 = this._y1 = this._y2 = NaN;\n    this._l01_a = this._l12_a = this._l23_a = this._l01_2a = this._l12_2a = this._l23_2a = this._point = 0;\n  },\n  lineEnd: function () {\n    if (this._line || this._line !== 0 && this._point === 3) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function (x, y) {\n    x = +x, y = +y;\n    if (this._point) {\n      var x23 = this._x2 - x,\n        y23 = this._y2 - y;\n      this._l23_a = Math.sqrt(this._l23_2a = Math.pow(x23 * x23 + y23 * y23, this._alpha));\n    }\n    switch (this._point) {\n      case 0:\n        this._point = 1;\n        break;\n      case 1:\n        this._point = 2;\n        break;\n      case 2:\n        this._point = 3;\n        this._line ? this._context.lineTo(this._x2, this._y2) : this._context.moveTo(this._x2, this._y2);\n        break;\n      case 3:\n        this._point = 4;\n      // proceed\n      default:\n        point(this, x, y);\n        break;\n    }\n    this._l01_a = this._l12_a, this._l12_a = this._l23_a;\n    this._l01_2a = this._l12_2a, this._l12_2a = this._l23_2a;\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\nexport default (function custom(alpha) {\n  function catmullRom(context) {\n    return alpha ? new CatmullRomOpen(context, alpha) : new CardinalOpen(context, 0);\n  }\n  catmullRom.alpha = function (alpha) {\n    return custom(+alpha);\n  };\n  return catmullRom;\n})(0.5);", "import noop from \"../noop.js\";\nfunction LinearClosed(context) {\n  this._context = context;\n}\nLinearClosed.prototype = {\n  areaStart: noop,\n  areaEnd: noop,\n  lineStart: function () {\n    this._point = 0;\n  },\n  lineEnd: function () {\n    if (this._point) this._context.closePath();\n  },\n  point: function (x, y) {\n    x = +x, y = +y;\n    if (this._point) this._context.lineTo(x, y);else this._point = 1, this._context.moveTo(x, y);\n  }\n};\nexport default function (context) {\n  return new LinearClosed(context);\n}", "function sign(x) {\n  return x < 0 ? -1 : 1;\n}\n\n// Calculate the slopes of the tangents (Hermite-type interpolation) based on\n// the following paper: <PERSON>effen, M. 1990. A Simple Method for Monotonic\n// Interpolation in One Dimension. Astronomy and Astrophysics, Vol. 239, NO.\n// NOV(II), P. 443, 1990.\nfunction slope3(that, x2, y2) {\n  var h0 = that._x1 - that._x0,\n    h1 = x2 - that._x1,\n    s0 = (that._y1 - that._y0) / (h0 || h1 < 0 && -0),\n    s1 = (y2 - that._y1) / (h1 || h0 < 0 && -0),\n    p = (s0 * h1 + s1 * h0) / (h0 + h1);\n  return (sign(s0) + sign(s1)) * Math.min(Math.abs(s0), Math.abs(s1), 0.5 * Math.abs(p)) || 0;\n}\n\n// Calculate a one-sided slope.\nfunction slope2(that, t) {\n  var h = that._x1 - that._x0;\n  return h ? (3 * (that._y1 - that._y0) / h - t) / 2 : t;\n}\n\n// According to https://en.wikipedia.org/wiki/Cubic_Hermite_spline#Representations\n// \"you can express cubic Hermite interpolation in terms of cubic Bézier curves\n// with respect to the four values p0, p0 + m0 / 3, p1 - m1 / 3, p1\".\nfunction point(that, t0, t1) {\n  var x0 = that._x0,\n    y0 = that._y0,\n    x1 = that._x1,\n    y1 = that._y1,\n    dx = (x1 - x0) / 3;\n  that._context.bezierCurveTo(x0 + dx, y0 + dx * t0, x1 - dx, y1 - dx * t1, x1, y1);\n}\nfunction MonotoneX(context) {\n  this._context = context;\n}\nMonotoneX.prototype = {\n  areaStart: function () {\n    this._line = 0;\n  },\n  areaEnd: function () {\n    this._line = NaN;\n  },\n  lineStart: function () {\n    this._x0 = this._x1 = this._y0 = this._y1 = this._t0 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function () {\n    switch (this._point) {\n      case 2:\n        this._context.lineTo(this._x1, this._y1);\n        break;\n      case 3:\n        point(this, this._t0, slope2(this, this._t0));\n        break;\n    }\n    if (this._line || this._line !== 0 && this._point === 1) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function (x, y) {\n    var t1 = NaN;\n    x = +x, y = +y;\n    if (x === this._x1 && y === this._y1) return; // Ignore coincident points.\n    switch (this._point) {\n      case 0:\n        this._point = 1;\n        this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y);\n        break;\n      case 1:\n        this._point = 2;\n        break;\n      case 2:\n        this._point = 3;\n        point(this, slope2(this, t1 = slope3(this, x, y)), t1);\n        break;\n      default:\n        point(this, this._t0, t1 = slope3(this, x, y));\n        break;\n    }\n    this._x0 = this._x1, this._x1 = x;\n    this._y0 = this._y1, this._y1 = y;\n    this._t0 = t1;\n  }\n};\nfunction MonotoneY(context) {\n  this._context = new ReflectContext(context);\n}\n(MonotoneY.prototype = Object.create(MonotoneX.prototype)).point = function (x, y) {\n  MonotoneX.prototype.point.call(this, y, x);\n};\nfunction ReflectContext(context) {\n  this._context = context;\n}\nReflectContext.prototype = {\n  moveTo: function (x, y) {\n    this._context.moveTo(y, x);\n  },\n  closePath: function () {\n    this._context.closePath();\n  },\n  lineTo: function (x, y) {\n    this._context.lineTo(y, x);\n  },\n  bezierCurveTo: function (x1, y1, x2, y2, x, y) {\n    this._context.bezierCurveTo(y1, x1, y2, x2, y, x);\n  }\n};\nexport function monotoneX(context) {\n  return new MonotoneX(context);\n}\nexport function monotoneY(context) {\n  return new MonotoneY(context);\n}", "function Natural(context) {\n  this._context = context;\n}\nNatural.prototype = {\n  areaStart: function () {\n    this._line = 0;\n  },\n  areaEnd: function () {\n    this._line = NaN;\n  },\n  lineStart: function () {\n    this._x = [];\n    this._y = [];\n  },\n  lineEnd: function () {\n    var x = this._x,\n      y = this._y,\n      n = x.length;\n    if (n) {\n      this._line ? this._context.lineTo(x[0], y[0]) : this._context.moveTo(x[0], y[0]);\n      if (n === 2) {\n        this._context.lineTo(x[1], y[1]);\n      } else {\n        var px = controlPoints(x),\n          py = controlPoints(y);\n        for (var i0 = 0, i1 = 1; i1 < n; ++i0, ++i1) {\n          this._context.bezierCurveTo(px[0][i0], py[0][i0], px[1][i0], py[1][i0], x[i1], y[i1]);\n        }\n      }\n    }\n    if (this._line || this._line !== 0 && n === 1) this._context.closePath();\n    this._line = 1 - this._line;\n    this._x = this._y = null;\n  },\n  point: function (x, y) {\n    this._x.push(+x);\n    this._y.push(+y);\n  }\n};\n\n// See https://www.particleincell.com/2012/bezier-splines/ for derivation.\nfunction controlPoints(x) {\n  var i,\n    n = x.length - 1,\n    m,\n    a = new Array(n),\n    b = new Array(n),\n    r = new Array(n);\n  a[0] = 0, b[0] = 2, r[0] = x[0] + 2 * x[1];\n  for (i = 1; i < n - 1; ++i) a[i] = 1, b[i] = 4, r[i] = 4 * x[i] + 2 * x[i + 1];\n  a[n - 1] = 2, b[n - 1] = 7, r[n - 1] = 8 * x[n - 1] + x[n];\n  for (i = 1; i < n; ++i) m = a[i] / b[i - 1], b[i] -= m, r[i] -= m * r[i - 1];\n  a[n - 1] = r[n - 1] / b[n - 1];\n  for (i = n - 2; i >= 0; --i) a[i] = (r[i] - a[i + 1]) / b[i];\n  b[n - 1] = (x[n] + a[n - 1]) / 2;\n  for (i = 0; i < n - 1; ++i) b[i] = 2 * x[i + 1] - a[i + 1];\n  return [a, b];\n}\nexport default function (context) {\n  return new Natural(context);\n}", "function Step(context, t) {\n  this._context = context;\n  this._t = t;\n}\nStep.prototype = {\n  areaStart: function () {\n    this._line = 0;\n  },\n  areaEnd: function () {\n    this._line = NaN;\n  },\n  lineStart: function () {\n    this._x = this._y = NaN;\n    this._point = 0;\n  },\n  lineEnd: function () {\n    if (0 < this._t && this._t < 1 && this._point === 2) this._context.lineTo(this._x, this._y);\n    if (this._line || this._line !== 0 && this._point === 1) this._context.closePath();\n    if (this._line >= 0) this._t = 1 - this._t, this._line = 1 - this._line;\n  },\n  point: function (x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0:\n        this._point = 1;\n        this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y);\n        break;\n      case 1:\n        this._point = 2;\n      // proceed\n      default:\n        {\n          if (this._t <= 0) {\n            this._context.lineTo(this._x, y);\n            this._context.lineTo(x, y);\n          } else {\n            var x1 = this._x * (1 - this._t) + x * this._t;\n            this._context.lineTo(x1, this._y);\n            this._context.lineTo(x1, y);\n          }\n          break;\n        }\n    }\n    this._x = x, this._y = y;\n  }\n};\nexport default function (context) {\n  return new Step(context, 0.5);\n}\nexport function stepBefore(context) {\n  return new Step(context, 0);\n}\nexport function stepAfter(context) {\n  return new Step(context, 1);\n}", "import { linkHorizontal } from \"d3-shape\";\nfunction horizontalSource(d) {\n  return [d.source.x1, d.y0];\n}\nfunction horizontalTarget(d) {\n  return [d.target.x0, d.y1];\n}\nexport default function () {\n  return linkHorizontal().source(horizontalSource).target(horizontalTarget);\n}", "import { __name, clear, common_default, defaultConfig2 as defaultConfig, getAccDescription, getAccTitle, getConfig2 as getConfig, getDiagramTitle, setAccDescription, setAccTitle, setDiagramTitle, setupGraphViewbox } from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/sankey/parser/sankey.jison\nvar parser = function () {\n  var o = /* @__PURE__ */__name(function (k, v, o2, l) {\n      for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v);\n      return o2;\n    }, \"o\"),\n    $V0 = [1, 9],\n    $V1 = [1, 10],\n    $V2 = [1, 5, 10, 12];\n  var parser2 = {\n    trace: /* @__PURE__ */__name(function trace() {}, \"trace\"),\n    yy: {},\n    symbols_: {\n      \"error\": 2,\n      \"start\": 3,\n      \"SANKEY\": 4,\n      \"NEWLINE\": 5,\n      \"csv\": 6,\n      \"opt_eof\": 7,\n      \"record\": 8,\n      \"csv_tail\": 9,\n      \"EOF\": 10,\n      \"field[source]\": 11,\n      \"COMMA\": 12,\n      \"field[target]\": 13,\n      \"field[value]\": 14,\n      \"field\": 15,\n      \"escaped\": 16,\n      \"non_escaped\": 17,\n      \"DQUOTE\": 18,\n      \"ESCAPED_TEXT\": 19,\n      \"NON_ESCAPED_TEXT\": 20,\n      \"$accept\": 0,\n      \"$end\": 1\n    },\n    terminals_: {\n      2: \"error\",\n      4: \"SANKEY\",\n      5: \"NEWLINE\",\n      10: \"EOF\",\n      11: \"field[source]\",\n      12: \"COMMA\",\n      13: \"field[target]\",\n      14: \"field[value]\",\n      18: \"DQUOTE\",\n      19: \"ESCAPED_TEXT\",\n      20: \"NON_ESCAPED_TEXT\"\n    },\n    productions_: [0, [3, 4], [6, 2], [9, 2], [9, 0], [7, 1], [7, 0], [8, 5], [15, 1], [15, 1], [16, 3], [17, 1]],\n    performAction: /* @__PURE__ */__name(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 7:\n          const source = yy.findOrCreateNode($$[$0 - 4].trim().replaceAll('\"\"', '\"'));\n          const target = yy.findOrCreateNode($$[$0 - 2].trim().replaceAll('\"\"', '\"'));\n          const value = parseFloat($$[$0].trim());\n          yy.addLink(source, target, value);\n          break;\n        case 8:\n        case 9:\n        case 11:\n          this.$ = $$[$0];\n          break;\n        case 10:\n          this.$ = $$[$0 - 1];\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{\n      3: 1,\n      4: [1, 2]\n    }, {\n      1: [3]\n    }, {\n      5: [1, 3]\n    }, {\n      6: 4,\n      8: 5,\n      15: 6,\n      16: 7,\n      17: 8,\n      18: $V0,\n      20: $V1\n    }, {\n      1: [2, 6],\n      7: 11,\n      10: [1, 12]\n    }, o($V1, [2, 4], {\n      9: 13,\n      5: [1, 14]\n    }), {\n      12: [1, 15]\n    }, o($V2, [2, 8]), o($V2, [2, 9]), {\n      19: [1, 16]\n    }, o($V2, [2, 11]), {\n      1: [2, 1]\n    }, {\n      1: [2, 5]\n    }, o($V1, [2, 2]), {\n      6: 17,\n      8: 5,\n      15: 6,\n      16: 7,\n      17: 8,\n      18: $V0,\n      20: $V1\n    }, {\n      15: 18,\n      16: 7,\n      17: 8,\n      18: $V0,\n      20: $V1\n    }, {\n      18: [1, 19]\n    }, o($V1, [2, 3]), {\n      12: [1, 20]\n    }, o($V2, [2, 10]), {\n      15: 21,\n      16: 7,\n      17: 8,\n      18: $V0,\n      20: $V1\n    }, o([1, 5, 10], [2, 7])],\n    defaultActions: {\n      11: [2, 1],\n      12: [2, 5]\n    },\n    parseError: /* @__PURE__ */__name(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */__name(function parse(input) {\n      var self = this,\n        stack = [0],\n        tstack = [],\n        vstack = [null],\n        lstack = [],\n        table = this.table,\n        yytext = \"\",\n        yylineno = 0,\n        yyleng = 0,\n        recovering = 0,\n        TERROR = 2,\n        EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = {\n        yy: {}\n      };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      __name(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      __name(lex, \"lex\");\n      var symbol,\n        preErrorSymbol,\n        state,\n        action,\n        a,\n        r,\n        yyval = {},\n        p,\n        len,\n        newState,\n        expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [lstack[lstack.length - (len || 1)].range[0], lstack[lstack.length - 1].range[1]];\n            }\n            r = this.performAction.apply(yyval, [yytext, yyleng, yylineno, sharedState.yy, action[1], vstack, lstack].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */function () {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */__name(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */__name(function (input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */__name(function () {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */__name(function (ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */__name(function () {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */__name(function () {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */__name(function (n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */__name(function () {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */__name(function () {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */__name(function () {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */__name(function (match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */__name(function () {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */__name(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */__name(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */__name(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */__name(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */__name(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */__name(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */__name(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: {\n        \"case-insensitive\": true\n      },\n      performAction: /* @__PURE__ */__name(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            this.pushState(\"csv\");\n            return 4;\n            break;\n          case 1:\n            return 10;\n            break;\n          case 2:\n            return 5;\n            break;\n          case 3:\n            return 12;\n            break;\n          case 4:\n            this.pushState(\"escaped_text\");\n            return 18;\n            break;\n          case 5:\n            return 20;\n            break;\n          case 6:\n            this.popState(\"escaped_text\");\n            return 18;\n            break;\n          case 7:\n            return 19;\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:sankey-beta\\b)/i, /^(?:$)/i, /^(?:((\\u000D\\u000A)|(\\u000A)))/i, /^(?:(\\u002C))/i, /^(?:(\\u0022))/i, /^(?:([\\u0020-\\u0021\\u0023-\\u002B\\u002D-\\u007E])*)/i, /^(?:(\\u0022)(?!(\\u0022)))/i, /^(?:(([\\u0020-\\u0021\\u0023-\\u002B\\u002D-\\u007E])|(\\u002C)|(\\u000D)|(\\u000A)|(\\u0022)(\\u0022))*)/i],\n      conditions: {\n        \"csv\": {\n          \"rules\": [1, 2, 3, 4, 5, 6, 7],\n          \"inclusive\": false\n        },\n        \"escaped_text\": {\n          \"rules\": [6, 7],\n          \"inclusive\": false\n        },\n        \"INITIAL\": {\n          \"rules\": [0, 1, 2, 3, 4, 5, 6, 7],\n          \"inclusive\": true\n        }\n      }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  __name(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar sankey_default = parser;\n\n// src/diagrams/sankey/sankeyDB.ts\nvar links = [];\nvar nodes = [];\nvar nodesMap = /* @__PURE__ */new Map();\nvar clear2 = /* @__PURE__ */__name(() => {\n  links = [];\n  nodes = [];\n  nodesMap = /* @__PURE__ */new Map();\n  clear();\n}, \"clear\");\nvar SankeyLink = class {\n  constructor(source, target, value = 0) {\n    this.source = source;\n    this.target = target;\n    this.value = value;\n  }\n  static {\n    __name(this, \"SankeyLink\");\n  }\n};\nvar addLink = /* @__PURE__ */__name((source, target, value) => {\n  links.push(new SankeyLink(source, target, value));\n}, \"addLink\");\nvar SankeyNode = class {\n  constructor(ID) {\n    this.ID = ID;\n  }\n  static {\n    __name(this, \"SankeyNode\");\n  }\n};\nvar findOrCreateNode = /* @__PURE__ */__name(ID => {\n  ID = common_default.sanitizeText(ID, getConfig());\n  let node = nodesMap.get(ID);\n  if (node === void 0) {\n    node = new SankeyNode(ID);\n    nodesMap.set(ID, node);\n    nodes.push(node);\n  }\n  return node;\n}, \"findOrCreateNode\");\nvar getNodes = /* @__PURE__ */__name(() => nodes, \"getNodes\");\nvar getLinks = /* @__PURE__ */__name(() => links, \"getLinks\");\nvar getGraph = /* @__PURE__ */__name(() => ({\n  nodes: nodes.map(node => ({\n    id: node.ID\n  })),\n  links: links.map(link => ({\n    source: link.source.ID,\n    target: link.target.ID,\n    value: link.value\n  }))\n}), \"getGraph\");\nvar sankeyDB_default = {\n  nodesMap,\n  getConfig: /* @__PURE__ */__name(() => getConfig().sankey, \"getConfig\"),\n  getNodes,\n  getLinks,\n  getGraph,\n  addLink,\n  findOrCreateNode,\n  getAccTitle,\n  setAccTitle,\n  getAccDescription,\n  setAccDescription,\n  getDiagramTitle,\n  setDiagramTitle,\n  clear: clear2\n};\n\n// src/diagrams/sankey/sankeyRenderer.ts\nimport { select as d3select, scaleOrdinal as d3scaleOrdinal, schemeTableau10 as d3schemeTableau10 } from \"d3\";\nimport { sankey as d3Sankey, sankeyLinkHorizontal as d3SankeyLinkHorizontal, sankeyLeft as d3SankeyLeft, sankeyRight as d3SankeyRight, sankeyCenter as d3SankeyCenter, sankeyJustify as d3SankeyJustify } from \"d3-sankey\";\n\n// src/rendering-util/uid.ts\nvar Uid = class _Uid {\n  static {\n    __name(this, \"Uid\");\n  }\n  static {\n    this.count = 0;\n  }\n  static next(name) {\n    return new _Uid(name + ++_Uid.count);\n  }\n  constructor(id) {\n    this.id = id;\n    this.href = `#${id}`;\n  }\n  toString() {\n    return \"url(\" + this.href + \")\";\n  }\n};\n\n// src/diagrams/sankey/sankeyRenderer.ts\nvar alignmentsMap = {\n  left: d3SankeyLeft,\n  right: d3SankeyRight,\n  center: d3SankeyCenter,\n  justify: d3SankeyJustify\n};\nvar draw = /* @__PURE__ */__name(function (text, id, _version, diagObj) {\n  const {\n    securityLevel,\n    sankey: conf\n  } = getConfig();\n  const defaultSankeyConfig = defaultConfig.sankey;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = d3select(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? d3select(sandboxElement.nodes()[0].contentDocument.body) : d3select(\"body\");\n  const svg = securityLevel === \"sandbox\" ? root.select(`[id=\"${id}\"]`) : d3select(`[id=\"${id}\"]`);\n  const width = conf?.width ?? defaultSankeyConfig.width;\n  const height = conf?.height ?? defaultSankeyConfig.width;\n  const useMaxWidth = conf?.useMaxWidth ?? defaultSankeyConfig.useMaxWidth;\n  const nodeAlignment = conf?.nodeAlignment ?? defaultSankeyConfig.nodeAlignment;\n  const prefix = conf?.prefix ?? defaultSankeyConfig.prefix;\n  const suffix = conf?.suffix ?? defaultSankeyConfig.suffix;\n  const showValues = conf?.showValues ?? defaultSankeyConfig.showValues;\n  const graph = diagObj.db.getGraph();\n  const nodeAlign = alignmentsMap[nodeAlignment];\n  const nodeWidth = 10;\n  const sankey = d3Sankey().nodeId(d => d.id).nodeWidth(nodeWidth).nodePadding(10 + (showValues ? 15 : 0)).nodeAlign(nodeAlign).extent([[0, 0], [width, height]]);\n  sankey(graph);\n  const colorScheme = d3scaleOrdinal(d3schemeTableau10);\n  svg.append(\"g\").attr(\"class\", \"nodes\").selectAll(\".node\").data(graph.nodes).join(\"g\").attr(\"class\", \"node\").attr(\"id\", d => (d.uid = Uid.next(\"node-\")).id).attr(\"transform\", function (d) {\n    return \"translate(\" + d.x0 + \",\" + d.y0 + \")\";\n  }).attr(\"x\", d => d.x0).attr(\"y\", d => d.y0).append(\"rect\").attr(\"height\", d => {\n    return d.y1 - d.y0;\n  }).attr(\"width\", d => d.x1 - d.x0).attr(\"fill\", d => colorScheme(d.id));\n  const getText = /* @__PURE__ */__name(({\n    id: id2,\n    value\n  }) => {\n    if (!showValues) {\n      return id2;\n    }\n    return `${id2}\n${prefix}${Math.round(value * 100) / 100}${suffix}`;\n  }, \"getText\");\n  svg.append(\"g\").attr(\"class\", \"node-labels\").attr(\"font-size\", 14).selectAll(\"text\").data(graph.nodes).join(\"text\").attr(\"x\", d => d.x0 < width / 2 ? d.x1 + 6 : d.x0 - 6).attr(\"y\", d => (d.y1 + d.y0) / 2).attr(\"dy\", `${showValues ? \"0\" : \"0.35\"}em`).attr(\"text-anchor\", d => d.x0 < width / 2 ? \"start\" : \"end\").text(getText);\n  const link = svg.append(\"g\").attr(\"class\", \"links\").attr(\"fill\", \"none\").attr(\"stroke-opacity\", 0.5).selectAll(\".link\").data(graph.links).join(\"g\").attr(\"class\", \"link\").style(\"mix-blend-mode\", \"multiply\");\n  const linkColor = conf?.linkColor ?? \"gradient\";\n  if (linkColor === \"gradient\") {\n    const gradient = link.append(\"linearGradient\").attr(\"id\", d => (d.uid = Uid.next(\"linearGradient-\")).id).attr(\"gradientUnits\", \"userSpaceOnUse\").attr(\"x1\", d => d.source.x1).attr(\"x2\", d => d.target.x0);\n    gradient.append(\"stop\").attr(\"offset\", \"0%\").attr(\"stop-color\", d => colorScheme(d.source.id));\n    gradient.append(\"stop\").attr(\"offset\", \"100%\").attr(\"stop-color\", d => colorScheme(d.target.id));\n  }\n  let coloring;\n  switch (linkColor) {\n    case \"gradient\":\n      coloring = /* @__PURE__ */__name(d => d.uid, \"coloring\");\n      break;\n    case \"source\":\n      coloring = /* @__PURE__ */__name(d => colorScheme(d.source.id), \"coloring\");\n      break;\n    case \"target\":\n      coloring = /* @__PURE__ */__name(d => colorScheme(d.target.id), \"coloring\");\n      break;\n    default:\n      coloring = linkColor;\n  }\n  link.append(\"path\").attr(\"d\", d3SankeyLinkHorizontal()).attr(\"stroke\", coloring).attr(\"stroke-width\", d => Math.max(1, d.width));\n  setupGraphViewbox(void 0, svg, 0, useMaxWidth);\n}, \"draw\");\nvar sankeyRenderer_default = {\n  draw\n};\n\n// src/diagrams/sankey/sankeyUtils.ts\nvar prepareTextForParsing = /* @__PURE__ */__name(text => {\n  const textToParse = text.replaceAll(/^[^\\S\\n\\r]+|[^\\S\\n\\r]+$/g, \"\").replaceAll(/([\\n\\r])+/g, \"\\n\").trim();\n  return textToParse;\n}, \"prepareTextForParsing\");\n\n// src/diagrams/sankey/styles.js\nvar getStyles = /* @__PURE__ */__name(options => `.label {\n      font-family: ${options.fontFamily};\n    }`, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/sankey/sankeyDiagram.ts\nvar originalParse = sankey_default.parse.bind(sankey_default);\nsankey_default.parse = text => originalParse(prepareTextForParsing(text));\nvar diagram = {\n  styles: styles_default,\n  parser: sankey_default,\n  db: sankeyDB_default,\n  renderer: sankeyRenderer_default\n};\nexport { diagram };"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAe,SAAR,kBAAkBA,IAAG,GAAG;AAC7B,SAAOA,KAAI,IAAI,KAAKA,KAAI,IAAI,IAAIA,MAAK,IAAI,IAAI;AAC/C;;;ACDe,SAAR,iBAAkB,GAAG;AAC1B,MAAI,QAAQ;AACZ,MAAI,UAAU;AACd,MAAI,EAAE,WAAW,GAAG;AAClB,YAAQ,CAAC,GAAGC,OAAM,EAAE,CAAC,IAAIA;AACzB,cAAU,oBAAoB,CAAC;AAAA,EACjC;AACA,WAASC,MAAKC,IAAGF,IAAG,IAAI,IAAI;AAC1B,QAAI,MAAM,KAAM,MAAK;AACrB,QAAI,MAAM,KAAM,MAAKE,GAAE;AACvB,WAAO,KAAK,IAAI;AACd,YAAM,MAAM,KAAK,OAAO;AACxB,UAAI,QAAQA,GAAE,GAAG,GAAGF,EAAC,IAAI,EAAG,MAAK,MAAM;AAAA,UAAO,MAAK;AAAA,IACrD;AACA,WAAO;AAAA,EACT;AACA,WAASG,OAAMD,IAAGF,IAAG,IAAI,IAAI;AAC3B,QAAI,MAAM,KAAM,MAAK;AACrB,QAAI,MAAM,KAAM,MAAKE,GAAE;AACvB,WAAO,KAAK,IAAI;AACd,YAAM,MAAM,KAAK,OAAO;AACxB,UAAI,QAAQA,GAAE,GAAG,GAAGF,EAAC,IAAI,EAAG,MAAK;AAAA,UAAS,MAAK,MAAM;AAAA,IACvD;AACA,WAAO;AAAA,EACT;AACA,WAASI,QAAOF,IAAGF,IAAG,IAAI,IAAI;AAC5B,QAAI,MAAM,KAAM,MAAK;AACrB,QAAI,MAAM,KAAM,MAAKE,GAAE;AACvB,UAAM,IAAID,MAAKC,IAAGF,IAAG,IAAI,KAAK,CAAC;AAC/B,WAAO,IAAI,MAAM,MAAME,GAAE,IAAI,CAAC,GAAGF,EAAC,IAAI,CAAC,MAAME,GAAE,CAAC,GAAGF,EAAC,IAAI,IAAI,IAAI;AAAA,EAClE;AACA,SAAO;AAAA,IACL,MAAAC;AAAA,IACA,QAAAG;AAAA,IACA,OAAAD;AAAA,EACF;AACF;AACA,SAAS,oBAAoB,GAAG;AAC9B,SAAO,CAAC,GAAGH,OAAM,kBAAU,EAAE,CAAC,GAAGA,EAAC;AACpC;;;ACxCe,SAAR,eAAkBK,IAAG;AAC1B,SAAOA,OAAM,OAAO,MAAM,CAACA;AAC7B;;;ACCA,IAAM,kBAAkB,iBAAS,iBAAS;AACnC,IAAM,cAAc,gBAAgB;AACpC,IAAM,aAAa,gBAAgB;AACnC,IAAM,eAAe,iBAAS,cAAM,EAAE;;;ACN7C,IAAI,QAAQ,MAAM;AACX,IAAI,QAAQ,MAAM;AAClB,IAAI,MAAM,MAAM;;;ACFvB,IAAI,MAAM,KAAK,KAAK,EAAE;AAAtB,IACE,KAAK,KAAK,KAAK,EAAE;AADnB,IAEE,KAAK,KAAK,KAAK,CAAC;;;ACFH,SAAR,IAAqB,QAAQ,SAAS;AAC3C,MAAIC;AACJ,MAAI,YAAY,QAAW;AACzB,eAAWC,UAAS,QAAQ;AAC1B,UAAIA,UAAS,SAASD,OAAMC,UAASD,SAAQ,UAAaC,UAASA,SAAQ;AACzE,QAAAD,OAAMC;AAAA,MACR;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAIC,SAAQ;AACZ,aAASD,UAAS,QAAQ;AACxB,WAAKA,SAAQ,QAAQA,QAAO,EAAEC,QAAO,MAAM,MAAM,SAASF,OAAMC,UAASD,SAAQ,UAAaC,UAASA,SAAQ;AAC7G,QAAAD,OAAMC;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,SAAOD;AACT;;;ACjBe,SAAR,IAAqB,QAAQ,SAAS;AAC3C,MAAIG;AACJ,MAAI,YAAY,QAAW;AACzB,eAAWC,UAAS,QAAQ;AAC1B,UAAIA,UAAS,SAASD,OAAMC,UAASD,SAAQ,UAAaC,UAASA,SAAQ;AACzE,QAAAD,OAAMC;AAAA,MACR;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAIC,SAAQ;AACZ,aAASD,UAAS,QAAQ;AACxB,WAAKA,SAAQ,QAAQA,QAAO,EAAEC,QAAO,MAAM,MAAM,SAASF,OAAMC,UAASD,SAAQ,UAAaC,UAASA,SAAQ;AAC7G,QAAAD,OAAMC;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,SAAOD;AACT;;;ACjBA,IAAO,kBAAQ,SAAS,KAAK,MAAM;AAC5B,SAAS,SAAS,QAAQ;AAC/B,SAAO,SAAS,QAAQG,QAAO,KAAK,GAAG,KAAKA,OAAM,QAAQ;AACxD,QAAI,IAAI,MAAM,KAAK,CAAC;AACpB,WAAO,GAAG;AACR,YAAM,IAAI,OAAO,IAAI,MAAM,GACzB,IAAIA,OAAM,IAAI,EAAE;AAClB,MAAAA,OAAM,IAAI,EAAE,IAAIA,OAAM,IAAI,EAAE;AAC5B,MAAAA,OAAM,IAAI,EAAE,IAAI;AAAA,IAClB;AACA,WAAOA;AAAA,EACT;AACF;;;ACZe,SAAR,IAAqB,QAAQ,SAAS;AAC3C,MAAIC,OAAM;AACV,MAAI,YAAY,QAAW;AACzB,aAASC,UAAS,QAAQ;AACxB,UAAIA,SAAQ,CAACA,QAAO;AAClB,QAAAD,QAAOC;AAAA,MACT;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAIC,SAAQ;AACZ,aAASD,UAAS,QAAQ;AACxB,UAAIA,SAAQ,CAAC,QAAQA,QAAO,EAAEC,QAAO,MAAM,GAAG;AAC5C,QAAAF,QAAOC;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,SAAOD;AACT;;;AChBA,SAAS,YAAY,GAAG;AACtB,SAAO,EAAE,OAAO;AAClB;AACO,SAAS,KAAK,MAAM;AACzB,SAAO,KAAK;AACd;AACO,SAAS,MAAM,MAAM,GAAG;AAC7B,SAAO,IAAI,IAAI,KAAK;AACtB;AACO,SAAS,QAAQ,MAAM,GAAG;AAC/B,SAAO,KAAK,YAAY,SAAS,KAAK,QAAQ,IAAI;AACpD;AACO,SAAS,OAAO,MAAM;AAC3B,SAAO,KAAK,YAAY,SAAS,KAAK,QAAQ,KAAK,YAAY,SAAS,IAAI,KAAK,aAAa,WAAW,IAAI,IAAI;AACnH;;;ACfe,SAAR,SAA0BG,IAAG;AAClC,SAAO,WAAY;AACjB,WAAOA;AAAA,EACT;AACF;;;ACDA,SAAS,uBAAuBC,IAAG,GAAG;AACpC,SAAO,iBAAiBA,GAAE,QAAQ,EAAE,MAAM,KAAKA,GAAE,QAAQ,EAAE;AAC7D;AACA,SAAS,uBAAuBA,IAAG,GAAG;AACpC,SAAO,iBAAiBA,GAAE,QAAQ,EAAE,MAAM,KAAKA,GAAE,QAAQ,EAAE;AAC7D;AACA,SAAS,iBAAiBA,IAAG,GAAG;AAC9B,SAAOA,GAAE,KAAK,EAAE;AAClB;AACA,SAAS,MAAM,GAAG;AAChB,SAAO,EAAE;AACX;AACA,SAAS,UAAU,GAAG;AACpB,SAAO,EAAE;AACX;AACA,SAAS,aAAa,OAAO;AAC3B,SAAO,MAAM;AACf;AACA,SAAS,aAAa,OAAO;AAC3B,SAAO,MAAM;AACf;AACA,SAAS,KAAK,UAAU,IAAI;AAC1B,QAAM,OAAO,SAAS,IAAI,EAAE;AAC5B,MAAI,CAAC,KAAM,OAAM,IAAI,MAAM,cAAc,EAAE;AAC3C,SAAO;AACT;AACA,SAAS,oBAAoB;AAAA,EAC3B,OAAAC;AACF,GAAG;AACD,aAAW,QAAQA,QAAO;AACxB,QAAI,KAAK,KAAK;AACd,QAAI,KAAK;AACT,eAAWC,SAAQ,KAAK,aAAa;AACnC,MAAAA,MAAK,KAAK,KAAKA,MAAK,QAAQ;AAC5B,YAAMA,MAAK;AAAA,IACb;AACA,eAAWA,SAAQ,KAAK,aAAa;AACnC,MAAAA,MAAK,KAAK,KAAKA,MAAK,QAAQ;AAC5B,YAAMA,MAAK;AAAA,IACb;AAAA,EACF;AACF;AACe,SAAR,SAA0B;AAC/B,MAAI,KAAK,GACP,KAAK,GACL,KAAK,GACL,KAAK;AACP,MAAI,KAAK;AACT,MAAI,KAAK,GACP;AACF,MAAI,KAAK;AACT,MAAI,QAAQ;AACZ,MAAIC;AACJ,MAAI;AACJ,MAAIF,SAAQ;AACZ,MAAIG,SAAQ;AACZ,MAAI,aAAa;AACjB,WAAS,SAAS;AAChB,UAAM,QAAQ;AAAA,MACZ,OAAOH,OAAM,MAAM,MAAM,SAAS;AAAA,MAClC,OAAOG,OAAM,MAAM,MAAM,SAAS;AAAA,IACpC;AACA,qBAAiB,KAAK;AACtB,sBAAkB,KAAK;AACvB,sBAAkB,KAAK;AACvB,uBAAmB,KAAK;AACxB,wBAAoB,KAAK;AACzB,wBAAoB,KAAK;AACzB,WAAO;AAAA,EACT;AACA,SAAO,SAAS,SAAU,OAAO;AAC/B,wBAAoB,KAAK;AACzB,WAAO;AAAA,EACT;AACA,SAAO,SAAS,SAAU,GAAG;AAC3B,WAAO,UAAU,UAAU,KAAK,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,GAAG,UAAU;AAAA,EACvF;AACA,SAAO,YAAY,SAAU,GAAG;AAC9B,WAAO,UAAU,UAAU,QAAQ,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,GAAG,UAAU;AAAA,EAC1F;AACA,SAAO,WAAW,SAAU,GAAG;AAC7B,WAAO,UAAU,UAAUD,QAAO,GAAG,UAAUA;AAAA,EACjD;AACA,SAAO,YAAY,SAAU,GAAG;AAC9B,WAAO,UAAU,UAAU,KAAK,CAAC,GAAG,UAAU;AAAA,EAChD;AACA,SAAO,cAAc,SAAU,GAAG;AAChC,WAAO,UAAU,UAAU,KAAK,KAAK,CAAC,GAAG,UAAU;AAAA,EACrD;AACA,SAAO,QAAQ,SAAU,GAAG;AAC1B,WAAO,UAAU,UAAUF,SAAQ,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,GAAG,UAAUA;AAAA,EAC1F;AACA,SAAO,QAAQ,SAAU,GAAG;AAC1B,WAAO,UAAU,UAAUG,SAAQ,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,GAAG,UAAUA;AAAA,EAC1F;AACA,SAAO,WAAW,SAAU,GAAG;AAC7B,WAAO,UAAU,UAAU,WAAW,GAAG,UAAU;AAAA,EACrD;AACA,SAAO,OAAO,SAAU,GAAG;AACzB,WAAO,UAAU,UAAU,KAAK,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,KAAK,IAAI,KAAK,EAAE;AAAA,EAC7F;AACA,SAAO,SAAS,SAAU,GAAG;AAC3B,WAAO,UAAU,UAAU,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC;AAAA,EACtH;AACA,SAAO,aAAa,SAAU,GAAG;AAC/B,WAAO,UAAU,UAAU,aAAa,CAAC,GAAG,UAAU;AAAA,EACxD;AACA,WAAS,iBAAiB;AAAA,IACxB,OAAAH;AAAA,IACA,OAAAG;AAAA,EACF,GAAG;AACD,eAAW,CAAC,GAAG,IAAI,KAAKH,OAAM,QAAQ,GAAG;AACvC,WAAK,QAAQ;AACb,WAAK,cAAc,CAAC;AACpB,WAAK,cAAc,CAAC;AAAA,IACtB;AACA,UAAM,WAAW,IAAI,IAAIA,OAAM,IAAI,CAAC,GAAG,MAAM,CAAC,GAAG,GAAG,GAAGA,MAAK,GAAG,CAAC,CAAC,CAAC;AAClE,eAAW,CAAC,GAAGC,KAAI,KAAKE,OAAM,QAAQ,GAAG;AACvC,MAAAF,MAAK,QAAQ;AACb,UAAI;AAAA,QACF;AAAA,QACA;AAAA,MACF,IAAIA;AACJ,UAAI,OAAO,WAAW,SAAU,UAASA,MAAK,SAAS,KAAK,UAAU,MAAM;AAC5E,UAAI,OAAO,WAAW,SAAU,UAASA,MAAK,SAAS,KAAK,UAAU,MAAM;AAC5E,aAAO,YAAY,KAAKA,KAAI;AAC5B,aAAO,YAAY,KAAKA,KAAI;AAAA,IAC9B;AACA,QAAI,YAAY,MAAM;AACpB,iBAAW;AAAA,QACT;AAAA,QACA;AAAA,MACF,KAAKD,QAAO;AACV,oBAAY,KAAK,QAAQ;AACzB,oBAAY,KAAK,QAAQ;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AACA,WAAS,kBAAkB;AAAA,IACzB,OAAAA;AAAA,EACF,GAAG;AACD,eAAW,QAAQA,QAAO;AACxB,WAAK,QAAQ,KAAK,eAAe,SAAY,KAAK,IAAI,IAAI,KAAK,aAAa,KAAK,GAAG,IAAI,KAAK,aAAa,KAAK,CAAC,IAAI,KAAK;AAAA,IAC3H;AAAA,EACF;AACA,WAAS,kBAAkB;AAAA,IACzB,OAAAA;AAAA,EACF,GAAG;AACD,UAAM,IAAIA,OAAM;AAChB,QAAI,UAAU,IAAI,IAAIA,MAAK;AAC3B,QAAI,OAAO,oBAAI,IAAI;AACnB,QAAII,KAAI;AACR,WAAO,QAAQ,MAAM;AACnB,iBAAW,QAAQ,SAAS;AAC1B,aAAK,QAAQA;AACb,mBAAW;AAAA,UACT;AAAA,QACF,KAAK,KAAK,aAAa;AACrB,eAAK,IAAI,MAAM;AAAA,QACjB;AAAA,MACF;AACA,UAAI,EAAEA,KAAI,EAAG,OAAM,IAAI,MAAM,eAAe;AAC5C,gBAAU;AACV,aAAO,oBAAI,IAAI;AAAA,IACjB;AAAA,EACF;AACA,WAAS,mBAAmB;AAAA,IAC1B,OAAAJ;AAAA,EACF,GAAG;AACD,UAAM,IAAIA,OAAM;AAChB,QAAI,UAAU,IAAI,IAAIA,MAAK;AAC3B,QAAI,OAAO,oBAAI,IAAI;AACnB,QAAII,KAAI;AACR,WAAO,QAAQ,MAAM;AACnB,iBAAW,QAAQ,SAAS;AAC1B,aAAK,SAASA;AACd,mBAAW;AAAA,UACT;AAAA,QACF,KAAK,KAAK,aAAa;AACrB,eAAK,IAAI,MAAM;AAAA,QACjB;AAAA,MACF;AACA,UAAI,EAAEA,KAAI,EAAG,OAAM,IAAI,MAAM,eAAe;AAC5C,gBAAU;AACV,aAAO,oBAAI,IAAI;AAAA,IACjB;AAAA,EACF;AACA,WAAS,kBAAkB;AAAA,IACzB,OAAAJ;AAAA,EACF,GAAG;AACD,UAAMI,KAAI,IAAIJ,QAAO,OAAK,EAAE,KAAK,IAAI;AACrC,UAAMK,OAAM,KAAK,KAAK,OAAOD,KAAI;AACjC,UAAM,UAAU,IAAI,MAAMA,EAAC;AAC3B,eAAW,QAAQJ,QAAO;AACxB,YAAM,IAAI,KAAK,IAAI,GAAG,KAAK,IAAII,KAAI,GAAG,KAAK,MAAM,MAAM,KAAK,MAAM,MAAMA,EAAC,CAAC,CAAC,CAAC;AAC5E,WAAK,QAAQ;AACb,WAAK,KAAK,KAAK,IAAIC;AACnB,WAAK,KAAK,KAAK,KAAK;AACpB,UAAI,QAAQ,CAAC,EAAG,SAAQ,CAAC,EAAE,KAAK,IAAI;AAAA,UAAO,SAAQ,CAAC,IAAI,CAAC,IAAI;AAAA,IAC/D;AACA,QAAIH,MAAM,YAAW,UAAU,SAAS;AACtC,aAAO,KAAKA,KAAI;AAAA,IAClB;AACA,WAAO;AAAA,EACT;AACA,WAAS,uBAAuB,SAAS;AACvC,UAAMI,MAAK,IAAI,SAAS,QAAM,KAAK,MAAM,EAAE,SAAS,KAAK,MAAM,IAAI,GAAG,KAAK,CAAC;AAC5E,eAAWN,UAAS,SAAS;AAC3B,UAAIO,KAAI;AACR,iBAAW,QAAQP,QAAO;AACxB,aAAK,KAAKO;AACV,aAAK,KAAKA,KAAI,KAAK,QAAQD;AAC3B,QAAAC,KAAI,KAAK,KAAK;AACd,mBAAWN,SAAQ,KAAK,aAAa;AACnC,UAAAA,MAAK,QAAQA,MAAK,QAAQK;AAAA,QAC5B;AAAA,MACF;AACA,MAAAC,MAAK,KAAKA,KAAI,OAAOP,OAAM,SAAS;AACpC,eAAS,IAAI,GAAG,IAAIA,OAAM,QAAQ,EAAE,GAAG;AACrC,cAAM,OAAOA,OAAM,CAAC;AACpB,aAAK,MAAMO,MAAK,IAAI;AACpB,aAAK,MAAMA,MAAK,IAAI;AAAA,MACtB;AACA,mBAAaP,MAAK;AAAA,IACpB;AAAA,EACF;AACA,WAAS,oBAAoB,OAAO;AAClC,UAAM,UAAU,kBAAkB,KAAK;AACvC,SAAK,KAAK,IAAI,KAAK,KAAK,OAAO,IAAI,SAAS,OAAK,EAAE,MAAM,IAAI,EAAE;AAC/D,2BAAuB,OAAO;AAC9B,aAAS,IAAI,GAAG,IAAI,YAAY,EAAE,GAAG;AACnC,YAAM,QAAQ,KAAK,IAAI,MAAM,CAAC;AAC9B,YAAM,OAAO,KAAK,IAAI,IAAI,QAAQ,IAAI,KAAK,UAAU;AACrD,uBAAiB,SAAS,OAAO,IAAI;AACrC,uBAAiB,SAAS,OAAO,IAAI;AAAA,IACvC;AAAA,EACF;AAGA,WAAS,iBAAiB,SAAS,OAAO,MAAM;AAC9C,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC9C,YAAM,SAAS,QAAQ,CAAC;AACxB,iBAAW,UAAU,QAAQ;AAC3B,YAAIO,KAAI;AACR,YAAI,IAAI;AACR,mBAAW;AAAA,UACT;AAAA,UACA,OAAAC;AAAA,QACF,KAAK,OAAO,aAAa;AACvB,cAAI,IAAIA,UAAS,OAAO,QAAQ,OAAO;AACvC,UAAAD,MAAK,UAAU,QAAQ,MAAM,IAAI;AACjC,eAAK;AAAA,QACP;AACA,YAAI,EAAE,IAAI,GAAI;AACd,YAAIE,OAAMF,KAAI,IAAI,OAAO,MAAM;AAC/B,eAAO,MAAME;AACb,eAAO,MAAMA;AACb,yBAAiB,MAAM;AAAA,MACzB;AACA,UAAIP,UAAS,OAAW,QAAO,KAAK,gBAAgB;AACpD,wBAAkB,QAAQ,IAAI;AAAA,IAChC;AAAA,EACF;AAGA,WAAS,iBAAiB,SAAS,OAAO,MAAM;AAC9C,aAAS,IAAI,QAAQ,QAAQ,IAAI,IAAI,GAAG,KAAK,GAAG,EAAE,GAAG;AACnD,YAAM,SAAS,QAAQ,CAAC;AACxB,iBAAW,UAAU,QAAQ;AAC3B,YAAIK,KAAI;AACR,YAAI,IAAI;AACR,mBAAW;AAAA,UACT;AAAA,UACA,OAAAC;AAAA,QACF,KAAK,OAAO,aAAa;AACvB,cAAI,IAAIA,UAAS,OAAO,QAAQ,OAAO;AACvC,UAAAD,MAAK,UAAU,QAAQ,MAAM,IAAI;AACjC,eAAK;AAAA,QACP;AACA,YAAI,EAAE,IAAI,GAAI;AACd,YAAIE,OAAMF,KAAI,IAAI,OAAO,MAAM;AAC/B,eAAO,MAAME;AACb,eAAO,MAAMA;AACb,yBAAiB,MAAM;AAAA,MACzB;AACA,UAAIP,UAAS,OAAW,QAAO,KAAK,gBAAgB;AACpD,wBAAkB,QAAQ,IAAI;AAAA,IAChC;AAAA,EACF;AACA,WAAS,kBAAkBF,QAAO,OAAO;AACvC,UAAM,IAAIA,OAAM,UAAU;AAC1B,UAAM,UAAUA,OAAM,CAAC;AACvB,iCAA6BA,QAAO,QAAQ,KAAK,IAAI,IAAI,GAAG,KAAK;AACjE,iCAA6BA,QAAO,QAAQ,KAAK,IAAI,IAAI,GAAG,KAAK;AACjE,iCAA6BA,QAAO,IAAIA,OAAM,SAAS,GAAG,KAAK;AAC/D,iCAA6BA,QAAO,IAAI,GAAG,KAAK;AAAA,EAClD;AAGA,WAAS,6BAA6BA,QAAOO,IAAG,GAAG,OAAO;AACxD,WAAO,IAAIP,OAAM,QAAQ,EAAE,GAAG;AAC5B,YAAM,OAAOA,OAAM,CAAC;AACpB,YAAMS,OAAMF,KAAI,KAAK,MAAM;AAC3B,UAAIE,MAAK,KAAM,MAAK,MAAMA,KAAI,KAAK,MAAMA;AACzC,MAAAF,KAAI,KAAK,KAAK;AAAA,IAChB;AAAA,EACF;AAGA,WAAS,6BAA6BP,QAAOO,IAAG,GAAG,OAAO;AACxD,WAAO,KAAK,GAAG,EAAE,GAAG;AAClB,YAAM,OAAOP,OAAM,CAAC;AACpB,YAAMS,OAAM,KAAK,KAAKF,MAAK;AAC3B,UAAIE,MAAK,KAAM,MAAK,MAAMA,KAAI,KAAK,MAAMA;AACzC,MAAAF,KAAI,KAAK,KAAK;AAAA,IAChB;AAAA,EACF;AACA,WAAS,iBAAiB;AAAA,IACxB;AAAA,IACA;AAAA,EACF,GAAG;AACD,QAAI,aAAa,QAAW;AAC1B,iBAAW;AAAA,QACT,QAAQ;AAAA,UACN,aAAAG;AAAA,QACF;AAAA,MACF,KAAK,aAAa;AAChB,QAAAA,aAAY,KAAK,sBAAsB;AAAA,MACzC;AACA,iBAAW;AAAA,QACT,QAAQ;AAAA,UACN,aAAAC;AAAA,QACF;AAAA,MACF,KAAK,aAAa;AAChB,QAAAA,aAAY,KAAK,sBAAsB;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AACA,WAAS,aAAaX,QAAO;AAC3B,QAAI,aAAa,QAAW;AAC1B,iBAAW;AAAA,QACT;AAAA,QACA;AAAA,MACF,KAAKA,QAAO;AACV,oBAAY,KAAK,sBAAsB;AACvC,oBAAY,KAAK,sBAAsB;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AAGA,WAAS,UAAU,QAAQ,QAAQ;AACjC,QAAIO,KAAI,OAAO,MAAM,OAAO,YAAY,SAAS,KAAK,KAAK;AAC3D,eAAW;AAAA,MACT,QAAQ;AAAA,MACR;AAAA,IACF,KAAK,OAAO,aAAa;AACvB,UAAI,SAAS,OAAQ;AACrB,MAAAA,MAAK,QAAQ;AAAA,IACf;AACA,eAAW;AAAA,MACT,QAAQ;AAAA,MACR;AAAA,IACF,KAAK,OAAO,aAAa;AACvB,UAAI,SAAS,OAAQ;AACrB,MAAAA,MAAK;AAAA,IACP;AACA,WAAOA;AAAA,EACT;AAGA,WAAS,UAAU,QAAQ,QAAQ;AACjC,QAAIA,KAAI,OAAO,MAAM,OAAO,YAAY,SAAS,KAAK,KAAK;AAC3D,eAAW;AAAA,MACT,QAAQ;AAAA,MACR;AAAA,IACF,KAAK,OAAO,aAAa;AACvB,UAAI,SAAS,OAAQ;AACrB,MAAAA,MAAK,QAAQ;AAAA,IACf;AACA,eAAW;AAAA,MACT,QAAQ;AAAA,MACR;AAAA,IACF,KAAK,OAAO,aAAa;AACvB,UAAI,SAAS,OAAQ;AACrB,MAAAA,MAAK;AAAA,IACP;AACA,WAAOA;AAAA,EACT;AACA,SAAO;AACT;;;ACzYA,IAAI,KAAK,KAAK;AAAd,IACE,MAAM,IAAI;AADZ,IAEE,UAAU;AAFZ,IAGE,aAAa,MAAM;AACrB,SAAS,OAAO;AACd,OAAK,MAAM,KAAK;AAAA,EAEhB,KAAK,MAAM,KAAK,MAAM;AACtB,OAAK,IAAI;AACX;AACA,SAAS,OAAO;AACd,SAAO,IAAI,KAAK;AAClB;AACA,KAAK,YAAY,KAAK,YAAY;AAAA,EAChC,aAAa;AAAA,EACb,QAAQ,SAAUK,IAAGC,IAAG;AACtB,SAAK,KAAK,OAAO,KAAK,MAAM,KAAK,MAAM,CAACD,MAAK,OAAO,KAAK,MAAM,KAAK,MAAM,CAACC;AAAA,EAC7E;AAAA,EACA,WAAW,WAAY;AACrB,QAAI,KAAK,QAAQ,MAAM;AACrB,WAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK;AACrC,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAAA,EACA,QAAQ,SAAUD,IAAGC,IAAG;AACtB,SAAK,KAAK,OAAO,KAAK,MAAM,CAACD,MAAK,OAAO,KAAK,MAAM,CAACC;AAAA,EACvD;AAAA,EACA,kBAAkB,SAAU,IAAI,IAAID,IAAGC,IAAG;AACxC,SAAK,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,OAAO,KAAK,MAAM,CAACD,MAAK,OAAO,KAAK,MAAM,CAACC;AAAA,EAC/E;AAAA,EACA,eAAe,SAAU,IAAI,IAAI,IAAI,IAAID,IAAGC,IAAG;AAC7C,SAAK,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,OAAO,KAAK,MAAM,CAACD,MAAK,OAAO,KAAK,MAAM,CAACC;AAAA,EACvG;AAAA,EACA,OAAO,SAAU,IAAI,IAAI,IAAI,IAAI,GAAG;AAClC,SAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC;AAC7C,QAAI,KAAK,KAAK,KACZ,KAAK,KAAK,KACV,MAAM,KAAK,IACX,MAAM,KAAK,IACX,MAAM,KAAK,IACX,MAAM,KAAK,IACX,QAAQ,MAAM,MAAM,MAAM;AAG5B,QAAI,IAAI,EAAG,OAAM,IAAI,MAAM,sBAAsB,CAAC;AAGlD,QAAI,KAAK,QAAQ,MAAM;AACrB,WAAK,KAAK,OAAO,KAAK,MAAM,MAAM,OAAO,KAAK,MAAM;AAAA,IACtD,WAGS,EAAE,QAAQ,SAAU;AAAA,aAKpB,EAAE,KAAK,IAAI,MAAM,MAAM,MAAM,GAAG,IAAI,YAAY,CAAC,GAAG;AAC3D,WAAK,KAAK,OAAO,KAAK,MAAM,MAAM,OAAO,KAAK,MAAM;AAAA,IACtD,OAGK;AACH,UAAI,MAAM,KAAK,IACb,MAAM,KAAK,IACX,QAAQ,MAAM,MAAM,MAAM,KAC1B,QAAQ,MAAM,MAAM,MAAM,KAC1B,MAAM,KAAK,KAAK,KAAK,GACrB,MAAM,KAAK,KAAK,KAAK,GACrB,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,MAAM,QAAQ,QAAQ,UAAU,IAAI,MAAM,IAAI,KAAK,CAAC,GAChF,MAAM,IAAI,KACV,MAAM,IAAI;AAGZ,UAAI,KAAK,IAAI,MAAM,CAAC,IAAI,SAAS;AAC/B,aAAK,KAAK,OAAO,KAAK,MAAM,OAAO,OAAO,KAAK,MAAM;AAAA,MACvD;AACA,WAAK,KAAK,MAAM,IAAI,MAAM,IAAI,UAAU,EAAE,MAAM,MAAM,MAAM,OAAO,OAAO,KAAK,MAAM,KAAK,MAAM,OAAO,OAAO,KAAK,MAAM,KAAK,MAAM;AAAA,IACtI;AAAA,EACF;AAAA,EACA,KAAK,SAAUD,IAAGC,IAAG,GAAG,IAAI,IAAI,KAAK;AACnC,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA,IAAG,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC;AAChC,QAAI,KAAK,IAAI,KAAK,IAAI,EAAE,GACtB,KAAK,IAAI,KAAK,IAAI,EAAE,GACpB,KAAKD,KAAI,IACT,KAAKC,KAAI,IACT,KAAK,IAAI,KACT,KAAK,MAAM,KAAK,KAAK,KAAK;AAG5B,QAAI,IAAI,EAAG,OAAM,IAAI,MAAM,sBAAsB,CAAC;AAGlD,QAAI,KAAK,QAAQ,MAAM;AACrB,WAAK,KAAK,MAAM,KAAK,MAAM;AAAA,IAC7B,WAGS,KAAK,IAAI,KAAK,MAAM,EAAE,IAAI,WAAW,KAAK,IAAI,KAAK,MAAM,EAAE,IAAI,SAAS;AAC/E,WAAK,KAAK,MAAM,KAAK,MAAM;AAAA,IAC7B;AAGA,QAAI,CAAC,EAAG;AAGR,QAAI,KAAK,EAAG,MAAK,KAAK,MAAM;AAG5B,QAAI,KAAK,YAAY;AACnB,WAAK,KAAK,MAAM,IAAI,MAAM,IAAI,UAAU,KAAK,OAAOD,KAAI,MAAM,OAAOC,KAAI,MAAM,MAAM,IAAI,MAAM,IAAI,UAAU,KAAK,OAAO,KAAK,MAAM,MAAM,OAAO,KAAK,MAAM;AAAA,IAC9J,WAGS,KAAK,SAAS;AACrB,WAAK,KAAK,MAAM,IAAI,MAAM,IAAI,QAAQ,EAAE,MAAM,MAAM,MAAM,KAAK,OAAO,KAAK,MAAMD,KAAI,IAAI,KAAK,IAAI,EAAE,KAAK,OAAO,KAAK,MAAMC,KAAI,IAAI,KAAK,IAAI,EAAE;AAAA,IAChJ;AAAA,EACF;AAAA,EACA,MAAM,SAAUD,IAAGC,IAAG,GAAG,GAAG;AAC1B,SAAK,KAAK,OAAO,KAAK,MAAM,KAAK,MAAM,CAACD,MAAK,OAAO,KAAK,MAAM,KAAK,MAAM,CAACC,MAAK,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI;AAAA,EACnH;AAAA,EACA,UAAU,WAAY;AACpB,WAAO,KAAK;AAAA,EACd;AACF;AACA,IAAO,eAAQ;;;AC7HA,SAARC,kBAAkBC,IAAG;AAC1B,SAAO,SAASC,YAAW;AACzB,WAAOD;AAAA,EACT;AACF;;;ACGO,IAAIE,WAAU;AACd,IAAIC,MAAK,KAAK;AACd,IAAI,SAASA,MAAK;AAClB,IAAIC,OAAM,IAAID;;;ACVrB,SAAS,OAAO,SAAS;AACvB,OAAK,WAAW;AAClB;AACA,OAAO,YAAY;AAAA,EACjB,WAAW,WAAY;AACrB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,SAAS,WAAY;AACnB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW,WAAY;AACrB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAY;AACnB,QAAI,KAAK,SAAS,KAAK,UAAU,KAAK,KAAK,WAAW,EAAG,MAAK,SAAS,UAAU;AACjF,SAAK,QAAQ,IAAI,KAAK;AAAA,EACxB;AAAA,EACA,OAAO,SAAUE,IAAGC,IAAG;AACrB,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA;AACb,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AACH,aAAK,SAAS;AACd,aAAK,QAAQ,KAAK,SAAS,OAAOD,IAAGC,EAAC,IAAI,KAAK,SAAS,OAAOD,IAAGC,EAAC;AACnE;AAAA,MACF,KAAK;AACH,aAAK,SAAS;AAAA;AAAA,MAEhB;AACE,aAAK,SAAS,OAAOD,IAAGC,EAAC;AACzB;AAAA,IACJ;AAAA,EACF;AACF;AACe,SAAR,eAAkB,SAAS;AAChC,SAAO,IAAI,OAAO,OAAO;AAC3B;;;ACnCO,SAAS,EAAE,GAAG;AACnB,SAAO,EAAE,CAAC;AACZ;AACO,SAAS,EAAE,GAAG;AACnB,SAAO,EAAE,CAAC;AACZ;;;ACJO,IAAI,oBAAoB,YAAY,cAAW;AACtD,SAAS,OAAO,OAAO;AACrB,OAAK,SAAS;AAChB;AACA,OAAO,YAAY;AAAA,EACjB,WAAW,WAAY;AACrB,SAAK,OAAO,UAAU;AAAA,EACxB;AAAA,EACA,SAAS,WAAY;AACnB,SAAK,OAAO,QAAQ;AAAA,EACtB;AAAA,EACA,WAAW,WAAY;AACrB,SAAK,OAAO,UAAU;AAAA,EACxB;AAAA,EACA,SAAS,WAAY;AACnB,SAAK,OAAO,QAAQ;AAAA,EACtB;AAAA,EACA,OAAO,SAAUC,IAAG,GAAG;AACrB,SAAK,OAAO,MAAM,IAAI,KAAK,IAAIA,EAAC,GAAG,IAAI,CAAC,KAAK,IAAIA,EAAC,CAAC;AAAA,EACrD;AACF;AACe,SAAR,YAA6B,OAAO;AACzC,WAAS,OAAO,SAAS;AACvB,WAAO,IAAI,OAAO,MAAM,OAAO,CAAC;AAAA,EAClC;AACA,SAAO,SAAS;AAChB,SAAO;AACT;;;AC5BO,IAAIC,SAAQ,MAAM,UAAU;;;ACKnC,SAAS,WAAW,GAAG;AACrB,SAAO,EAAE;AACX;AACA,SAAS,WAAW,GAAG;AACrB,SAAO,EAAE;AACX;AACA,SAAS,KAAK,OAAO;AACnB,MAAI,SAAS,YACX,SAAS,YACTC,KAAI,GACJC,KAAI,GACJ,UAAU;AACZ,WAASC,QAAO;AACd,QAAI,QACF,OAAOC,OAAM,KAAK,SAAS,GAC3BC,KAAI,OAAO,MAAM,MAAM,IAAI,GAC3B,IAAI,OAAO,MAAM,MAAM,IAAI;AAC7B,QAAI,CAAC,QAAS,WAAU,SAAS,aAAK;AACtC,UAAM,SAAS,CAACJ,GAAE,MAAM,OAAO,KAAK,CAAC,IAAII,IAAG,KAAK,GAAG,CAACH,GAAE,MAAM,MAAM,IAAI,GAAG,CAACD,GAAE,MAAM,OAAO,KAAK,CAAC,IAAI,GAAG,KAAK,GAAG,CAACC,GAAE,MAAM,MAAM,IAAI,CAAC;AACnI,QAAI,OAAQ,QAAO,UAAU,MAAM,SAAS,MAAM;AAAA,EACpD;AACA,EAAAC,MAAK,SAAS,SAAU,GAAG;AACzB,WAAO,UAAU,UAAU,SAAS,GAAGA,SAAQ;AAAA,EACjD;AACA,EAAAA,MAAK,SAAS,SAAU,GAAG;AACzB,WAAO,UAAU,UAAU,SAAS,GAAGA,SAAQ;AAAA,EACjD;AACA,EAAAA,MAAK,IAAI,SAAU,GAAG;AACpB,WAAO,UAAU,UAAUF,KAAI,OAAO,MAAM,aAAa,IAAIK,kBAAS,CAAC,CAAC,GAAGH,SAAQF;AAAA,EACrF;AACA,EAAAE,MAAK,IAAI,SAAU,GAAG;AACpB,WAAO,UAAU,UAAUD,KAAI,OAAO,MAAM,aAAa,IAAII,kBAAS,CAAC,CAAC,GAAGH,SAAQD;AAAA,EACrF;AACA,EAAAC,MAAK,UAAU,SAAU,GAAG;AAC1B,WAAO,UAAU,UAAU,UAAU,KAAK,OAAO,OAAO,GAAGA,SAAQ;AAAA,EACrE;AACA,SAAOA;AACT;AACA,SAAS,gBAAgB,SAAS,IAAI,IAAI,IAAI,IAAI;AAChD,UAAQ,OAAO,IAAI,EAAE;AACrB,UAAQ,cAAc,MAAM,KAAK,MAAM,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAC9D;AAaO,SAAS,iBAAiB;AAC/B,SAAO,KAAK,eAAe;AAC7B;;;AC7DA,IAAI,QAAQ,KAAK,KAAK,IAAI,CAAC;AAA3B,IACE,UAAU,QAAQ;;;ACApB,IACE,KAAK,KAAK,IAAII,MAAK,EAAE,IAAI,KAAK,IAAI,IAAIA,MAAK,EAAE;AAD/C,IAEE,KAAK,KAAK,IAAIC,OAAM,EAAE,IAAI;AAF5B,IAGE,KAAK,CAAC,KAAK,IAAIA,OAAM,EAAE,IAAI;;;ACJ7B,IAAI,QAAQ,KAAK,KAAK,CAAC;;;ACAvB,IACE,IAAI,KAAK,KAAK,CAAC,IAAI;AADrB,IAEE,IAAI,IAAI,KAAK,KAAK,EAAE;AAFtB,IAGE,KAAK,IAAI,IAAI,KAAK;;;ACHL,SAAR,eAAoB;AAAC;;;ACArB,SAAS,MAAM,MAAMC,IAAGC,IAAG;AAChC,OAAK,SAAS,eAAe,IAAI,KAAK,MAAM,KAAK,OAAO,IAAI,IAAI,KAAK,MAAM,KAAK,OAAO,IAAI,KAAK,MAAM,IAAI,KAAK,OAAO,IAAI,KAAK,MAAM,IAAI,KAAK,OAAO,IAAI,KAAK,MAAM,IAAI,KAAK,MAAMD,MAAK,IAAI,KAAK,MAAM,IAAI,KAAK,MAAMC,MAAK,CAAC;AAC9N;AACO,SAAS,MAAM,SAAS;AAC7B,OAAK,WAAW;AAClB;AACA,MAAM,YAAY;AAAA,EAChB,WAAW,WAAY;AACrB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,SAAS,WAAY;AACnB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW,WAAY;AACrB,SAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AAC5C,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAY;AACnB,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AACH,cAAM,MAAM,KAAK,KAAK,KAAK,GAAG;AAAA;AAAA,MAEhC,KAAK;AACH,aAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AACvC;AAAA,IACJ;AACA,QAAI,KAAK,SAAS,KAAK,UAAU,KAAK,KAAK,WAAW,EAAG,MAAK,SAAS,UAAU;AACjF,SAAK,QAAQ,IAAI,KAAK;AAAA,EACxB;AAAA,EACA,OAAO,SAAUD,IAAGC,IAAG;AACrB,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA;AACb,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AACH,aAAK,SAAS;AACd,aAAK,QAAQ,KAAK,SAAS,OAAOD,IAAGC,EAAC,IAAI,KAAK,SAAS,OAAOD,IAAGC,EAAC;AACnE;AAAA,MACF,KAAK;AACH,aAAK,SAAS;AACd;AAAA,MACF,KAAK;AACH,aAAK,SAAS;AACd,aAAK,SAAS,QAAQ,IAAI,KAAK,MAAM,KAAK,OAAO,IAAI,IAAI,KAAK,MAAM,KAAK,OAAO,CAAC;AAAA;AAAA,MAEnF;AACE,cAAM,MAAMD,IAAGC,EAAC;AAChB;AAAA,IACJ;AACA,SAAK,MAAM,KAAK,KAAK,KAAK,MAAMD;AAChC,SAAK,MAAM,KAAK,KAAK,KAAK,MAAMC;AAAA,EAClC;AACF;;;AChDA,SAAS,YAAY,SAAS;AAC5B,OAAK,WAAW;AAClB;AACA,YAAY,YAAY;AAAA,EACtB,WAAW;AAAA,EACX,SAAS;AAAA,EACT,WAAW,WAAY;AACrB,SAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AAC9G,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAY;AACnB,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK,GACH;AACE,aAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AACvC,aAAK,SAAS,UAAU;AACxB;AAAA,MACF;AAAA,MACF,KAAK,GACH;AACE,aAAK,SAAS,QAAQ,KAAK,MAAM,IAAI,KAAK,OAAO,IAAI,KAAK,MAAM,IAAI,KAAK,OAAO,CAAC;AACjF,aAAK,SAAS,QAAQ,KAAK,MAAM,IAAI,KAAK,OAAO,IAAI,KAAK,MAAM,IAAI,KAAK,OAAO,CAAC;AACjF,aAAK,SAAS,UAAU;AACxB;AAAA,MACF;AAAA,MACF,KAAK,GACH;AACE,aAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAC7B,aAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAC7B,aAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAC7B;AAAA,MACF;AAAA,IACJ;AAAA,EACF;AAAA,EACA,OAAO,SAAUC,IAAGC,IAAG;AACrB,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA;AACb,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AACH,aAAK,SAAS;AACd,aAAK,MAAMD,IAAG,KAAK,MAAMC;AACzB;AAAA,MACF,KAAK;AACH,aAAK,SAAS;AACd,aAAK,MAAMD,IAAG,KAAK,MAAMC;AACzB;AAAA,MACF,KAAK;AACH,aAAK,SAAS;AACd,aAAK,MAAMD,IAAG,KAAK,MAAMC;AACzB,aAAK,SAAS,QAAQ,KAAK,MAAM,IAAI,KAAK,MAAMD,MAAK,IAAI,KAAK,MAAM,IAAI,KAAK,MAAMC,MAAK,CAAC;AACzF;AAAA,MACF;AACE,cAAM,MAAMD,IAAGC,EAAC;AAChB;AAAA,IACJ;AACA,SAAK,MAAM,KAAK,KAAK,KAAK,MAAMD;AAChC,SAAK,MAAM,KAAK,KAAK,KAAK,MAAMC;AAAA,EAClC;AACF;;;AC1DA,SAAS,UAAU,SAAS;AAC1B,OAAK,WAAW;AAClB;AACA,UAAU,YAAY;AAAA,EACpB,WAAW,WAAY;AACrB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,SAAS,WAAY;AACnB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW,WAAY;AACrB,SAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AAC5C,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAY;AACnB,QAAI,KAAK,SAAS,KAAK,UAAU,KAAK,KAAK,WAAW,EAAG,MAAK,SAAS,UAAU;AACjF,SAAK,QAAQ,IAAI,KAAK;AAAA,EACxB;AAAA,EACA,OAAO,SAAUC,IAAGC,IAAG;AACrB,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA;AACb,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AACH,aAAK,SAAS;AACd;AAAA,MACF,KAAK;AACH,aAAK,SAAS;AACd;AAAA,MACF,KAAK;AACH,aAAK,SAAS;AACd,YAAI,MAAM,KAAK,MAAM,IAAI,KAAK,MAAMD,MAAK,GACvC,MAAM,KAAK,MAAM,IAAI,KAAK,MAAMC,MAAK;AACvC,aAAK,QAAQ,KAAK,SAAS,OAAO,IAAI,EAAE,IAAI,KAAK,SAAS,OAAO,IAAI,EAAE;AACvE;AAAA,MACF,KAAK;AACH,aAAK,SAAS;AAAA;AAAA,MAEhB;AACE,cAAM,MAAMD,IAAGC,EAAC;AAChB;AAAA,IACJ;AACA,SAAK,MAAM,KAAK,KAAK,KAAK,MAAMD;AAChC,SAAK,MAAM,KAAK,KAAK,KAAK,MAAMC;AAAA,EAClC;AACF;;;AC3CA,SAAS,OAAO,SAAS,MAAM;AAC7B,OAAK,SAAS,IAAI,MAAM,OAAO;AAC/B,OAAK,QAAQ;AACf;AACA,OAAO,YAAY;AAAA,EACjB,WAAW,WAAY;AACrB,SAAK,KAAK,CAAC;AACX,SAAK,KAAK,CAAC;AACX,SAAK,OAAO,UAAU;AAAA,EACxB;AAAA,EACA,SAAS,WAAY;AACnB,QAAIC,KAAI,KAAK,IACXC,KAAI,KAAK,IACT,IAAID,GAAE,SAAS;AACjB,QAAI,IAAI,GAAG;AACT,UAAI,KAAKA,GAAE,CAAC,GACV,KAAKC,GAAE,CAAC,GACR,KAAKD,GAAE,CAAC,IAAI,IACZ,KAAKC,GAAE,CAAC,IAAI,IACZ,IAAI,IACJ;AACF,aAAO,EAAE,KAAK,GAAG;AACf,YAAI,IAAI;AACR,aAAK,OAAO,MAAM,KAAK,QAAQD,GAAE,CAAC,KAAK,IAAI,KAAK,UAAU,KAAK,IAAI,KAAK,KAAK,QAAQC,GAAE,CAAC,KAAK,IAAI,KAAK,UAAU,KAAK,IAAI,GAAG;AAAA,MAC9H;AAAA,IACF;AACA,SAAK,KAAK,KAAK,KAAK;AACpB,SAAK,OAAO,QAAQ;AAAA,EACtB;AAAA,EACA,OAAO,SAAUD,IAAGC,IAAG;AACrB,SAAK,GAAG,KAAK,CAACD,EAAC;AACf,SAAK,GAAG,KAAK,CAACC,EAAC;AAAA,EACjB;AACF;AACA,IAAO,iBAAS,SAAS,OAAO,MAAM;AACpC,WAAS,OAAO,SAAS;AACvB,WAAO,SAAS,IAAI,IAAI,MAAM,OAAO,IAAI,IAAI,OAAO,SAAS,IAAI;AAAA,EACnE;AACA,SAAO,OAAO,SAAUC,OAAM;AAC5B,WAAO,OAAO,CAACA,KAAI;AAAA,EACrB;AACA,SAAO;AACT,EAAG,IAAI;;;AC3CA,SAASC,OAAM,MAAMC,IAAGC,IAAG;AAChC,OAAK,SAAS,cAAc,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAMD,KAAI,KAAK,MAAM,KAAK,MAAM,KAAK,MAAMC,KAAI,KAAK,KAAK,KAAK,GAAG;AAClN;AACO,SAAS,SAAS,SAAS,SAAS;AACzC,OAAK,WAAW;AAChB,OAAK,MAAM,IAAI,WAAW;AAC5B;AACA,SAAS,YAAY;AAAA,EACnB,WAAW,WAAY;AACrB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,SAAS,WAAY;AACnB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW,WAAY;AACrB,SAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AAClE,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAY;AACnB,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AACH,aAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AACvC;AAAA,MACF,KAAK;AACH,QAAAF,OAAM,MAAM,KAAK,KAAK,KAAK,GAAG;AAC9B;AAAA,IACJ;AACA,QAAI,KAAK,SAAS,KAAK,UAAU,KAAK,KAAK,WAAW,EAAG,MAAK,SAAS,UAAU;AACjF,SAAK,QAAQ,IAAI,KAAK;AAAA,EACxB;AAAA,EACA,OAAO,SAAUC,IAAGC,IAAG;AACrB,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA;AACb,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AACH,aAAK,SAAS;AACd,aAAK,QAAQ,KAAK,SAAS,OAAOD,IAAGC,EAAC,IAAI,KAAK,SAAS,OAAOD,IAAGC,EAAC;AACnE;AAAA,MACF,KAAK;AACH,aAAK,SAAS;AACd,aAAK,MAAMD,IAAG,KAAK,MAAMC;AACzB;AAAA,MACF,KAAK;AACH,aAAK,SAAS;AAAA;AAAA,MAEhB;AACE,QAAAF,OAAM,MAAMC,IAAGC,EAAC;AAChB;AAAA,IACJ;AACA,SAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMD;AACrD,SAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMC;AAAA,EACvD;AACF;AACA,IAAO,mBAAS,SAASC,QAAO,SAAS;AACvC,WAAS,SAAS,SAAS;AACzB,WAAO,IAAI,SAAS,SAAS,OAAO;AAAA,EACtC;AACA,WAAS,UAAU,SAAUC,UAAS;AACpC,WAAOD,QAAO,CAACC,QAAO;AAAA,EACxB;AACA,SAAO;AACT,EAAG,CAAC;;;AC1DG,SAAS,eAAe,SAAS,SAAS;AAC/C,OAAK,WAAW;AAChB,OAAK,MAAM,IAAI,WAAW;AAC5B;AACA,eAAe,YAAY;AAAA,EACzB,WAAW;AAAA,EACX,SAAS;AAAA,EACT,WAAW,WAAY;AACrB,SAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AACpI,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAY;AACnB,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK,GACH;AACE,aAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AACvC,aAAK,SAAS,UAAU;AACxB;AAAA,MACF;AAAA,MACF,KAAK,GACH;AACE,aAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AACvC,aAAK,SAAS,UAAU;AACxB;AAAA,MACF;AAAA,MACF,KAAK,GACH;AACE,aAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAC7B,aAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAC7B,aAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAC7B;AAAA,MACF;AAAA,IACJ;AAAA,EACF;AAAA,EACA,OAAO,SAAUC,IAAGC,IAAG;AACrB,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA;AACb,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AACH,aAAK,SAAS;AACd,aAAK,MAAMD,IAAG,KAAK,MAAMC;AACzB;AAAA,MACF,KAAK;AACH,aAAK,SAAS;AACd,aAAK,SAAS,OAAO,KAAK,MAAMD,IAAG,KAAK,MAAMC,EAAC;AAC/C;AAAA,MACF,KAAK;AACH,aAAK,SAAS;AACd,aAAK,MAAMD,IAAG,KAAK,MAAMC;AACzB;AAAA,MACF;AACE,QAAAC,OAAM,MAAMF,IAAGC,EAAC;AAChB;AAAA,IACJ;AACA,SAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMD;AACrD,SAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMC;AAAA,EACvD;AACF;AACA,IAAO,yBAAS,SAASE,QAAO,SAAS;AACvC,WAAS,SAAS,SAAS;AACzB,WAAO,IAAI,eAAe,SAAS,OAAO;AAAA,EAC5C;AACA,WAAS,UAAU,SAAUC,UAAS;AACpC,WAAOD,QAAO,CAACC,QAAO;AAAA,EACxB;AACA,SAAO;AACT,EAAG,CAAC;;;AClEG,SAAS,aAAa,SAAS,SAAS;AAC7C,OAAK,WAAW;AAChB,OAAK,MAAM,IAAI,WAAW;AAC5B;AACA,aAAa,YAAY;AAAA,EACvB,WAAW,WAAY;AACrB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,SAAS,WAAY;AACnB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW,WAAY;AACrB,SAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AAClE,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAY;AACnB,QAAI,KAAK,SAAS,KAAK,UAAU,KAAK,KAAK,WAAW,EAAG,MAAK,SAAS,UAAU;AACjF,SAAK,QAAQ,IAAI,KAAK;AAAA,EACxB;AAAA,EACA,OAAO,SAAUC,IAAGC,IAAG;AACrB,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA;AACb,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AACH,aAAK,SAAS;AACd;AAAA,MACF,KAAK;AACH,aAAK,SAAS;AACd;AAAA,MACF,KAAK;AACH,aAAK,SAAS;AACd,aAAK,QAAQ,KAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG,IAAI,KAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AAC/F;AAAA,MACF,KAAK;AACH,aAAK,SAAS;AAAA;AAAA,MAEhB;AACE,QAAAC,OAAM,MAAMF,IAAGC,EAAC;AAChB;AAAA,IACJ;AACA,SAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMD;AACrD,SAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMC;AAAA,EACvD;AACF;AACA,IAAO,uBAAS,SAASE,QAAO,SAAS;AACvC,WAAS,SAAS,SAAS;AACzB,WAAO,IAAI,aAAa,SAAS,OAAO;AAAA,EAC1C;AACA,WAAS,UAAU,SAAUC,UAAS;AACpC,WAAOD,QAAO,CAACC,QAAO;AAAA,EACxB;AACA,SAAO;AACT,EAAG,CAAC;;;AClDG,SAASC,OAAM,MAAMC,IAAGC,IAAG;AAChC,MAAI,KAAK,KAAK,KACZ,KAAK,KAAK,KACVC,MAAK,KAAK,KACVC,MAAK,KAAK;AACZ,MAAI,KAAK,SAASC,UAAS;AACzB,QAAIC,KAAI,IAAI,KAAK,UAAU,IAAI,KAAK,SAAS,KAAK,SAAS,KAAK,SAC9D,IAAI,IAAI,KAAK,UAAU,KAAK,SAAS,KAAK;AAC5C,UAAM,KAAKA,KAAI,KAAK,MAAM,KAAK,UAAU,KAAK,MAAM,KAAK,WAAW;AACpE,UAAM,KAAKA,KAAI,KAAK,MAAM,KAAK,UAAU,KAAK,MAAM,KAAK,WAAW;AAAA,EACtE;AACA,MAAI,KAAK,SAASD,UAAS;AACzB,QAAI,IAAI,IAAI,KAAK,UAAU,IAAI,KAAK,SAAS,KAAK,SAAS,KAAK,SAC9D,IAAI,IAAI,KAAK,UAAU,KAAK,SAAS,KAAK;AAC5C,IAAAF,OAAMA,MAAK,IAAI,KAAK,MAAM,KAAK,UAAUF,KAAI,KAAK,WAAW;AAC7D,IAAAG,OAAMA,MAAK,IAAI,KAAK,MAAM,KAAK,UAAUF,KAAI,KAAK,WAAW;AAAA,EAC/D;AACA,OAAK,SAAS,cAAc,IAAI,IAAIC,KAAIC,KAAI,KAAK,KAAK,KAAK,GAAG;AAChE;AACA,SAAS,WAAW,SAAS,OAAO;AAClC,OAAK,WAAW;AAChB,OAAK,SAAS;AAChB;AACA,WAAW,YAAY;AAAA,EACrB,WAAW,WAAY;AACrB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,SAAS,WAAY;AACnB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW,WAAY;AACrB,SAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AAClE,SAAK,SAAS,KAAK,SAAS,KAAK,SAAS,KAAK,UAAU,KAAK,UAAU,KAAK,UAAU,KAAK,SAAS;AAAA,EACvG;AAAA,EACA,SAAS,WAAY;AACnB,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AACH,aAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AACvC;AAAA,MACF,KAAK;AACH,aAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAC7B;AAAA,IACJ;AACA,QAAI,KAAK,SAAS,KAAK,UAAU,KAAK,KAAK,WAAW,EAAG,MAAK,SAAS,UAAU;AACjF,SAAK,QAAQ,IAAI,KAAK;AAAA,EACxB;AAAA,EACA,OAAO,SAAUH,IAAGC,IAAG;AACrB,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA;AACb,QAAI,KAAK,QAAQ;AACf,UAAI,MAAM,KAAK,MAAMD,IACnB,MAAM,KAAK,MAAMC;AACnB,WAAK,SAAS,KAAK,KAAK,KAAK,UAAU,KAAK,IAAI,MAAM,MAAM,MAAM,KAAK,KAAK,MAAM,CAAC;AAAA,IACrF;AACA,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AACH,aAAK,SAAS;AACd,aAAK,QAAQ,KAAK,SAAS,OAAOD,IAAGC,EAAC,IAAI,KAAK,SAAS,OAAOD,IAAGC,EAAC;AACnE;AAAA,MACF,KAAK;AACH,aAAK,SAAS;AACd;AAAA,MACF,KAAK;AACH,aAAK,SAAS;AAAA;AAAA,MAEhB;AACE,QAAAF,OAAM,MAAMC,IAAGC,EAAC;AAChB;AAAA,IACJ;AACA,SAAK,SAAS,KAAK,QAAQ,KAAK,SAAS,KAAK;AAC9C,SAAK,UAAU,KAAK,SAAS,KAAK,UAAU,KAAK;AACjD,SAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMD;AACrD,SAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMC;AAAA,EACvD;AACF;AACA,IAAO,qBAAS,SAASK,QAAO,OAAO;AACrC,WAAS,WAAW,SAAS;AAC3B,WAAO,QAAQ,IAAI,WAAW,SAAS,KAAK,IAAI,IAAI,SAAS,SAAS,CAAC;AAAA,EACzE;AACA,aAAW,QAAQ,SAAUC,QAAO;AAClC,WAAOD,QAAO,CAACC,MAAK;AAAA,EACtB;AACA,SAAO;AACT,EAAG,GAAG;;;ACjFN,SAAS,iBAAiB,SAAS,OAAO;AACxC,OAAK,WAAW;AAChB,OAAK,SAAS;AAChB;AACA,iBAAiB,YAAY;AAAA,EAC3B,WAAW;AAAA,EACX,SAAS;AAAA,EACT,WAAW,WAAY;AACrB,SAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AACpI,SAAK,SAAS,KAAK,SAAS,KAAK,SAAS,KAAK,UAAU,KAAK,UAAU,KAAK,UAAU,KAAK,SAAS;AAAA,EACvG;AAAA,EACA,SAAS,WAAY;AACnB,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK,GACH;AACE,aAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AACvC,aAAK,SAAS,UAAU;AACxB;AAAA,MACF;AAAA,MACF,KAAK,GACH;AACE,aAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AACvC,aAAK,SAAS,UAAU;AACxB;AAAA,MACF;AAAA,MACF,KAAK,GACH;AACE,aAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAC7B,aAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAC7B,aAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAC7B;AAAA,MACF;AAAA,IACJ;AAAA,EACF;AAAA,EACA,OAAO,SAAUC,IAAGC,IAAG;AACrB,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA;AACb,QAAI,KAAK,QAAQ;AACf,UAAI,MAAM,KAAK,MAAMD,IACnB,MAAM,KAAK,MAAMC;AACnB,WAAK,SAAS,KAAK,KAAK,KAAK,UAAU,KAAK,IAAI,MAAM,MAAM,MAAM,KAAK,KAAK,MAAM,CAAC;AAAA,IACrF;AACA,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AACH,aAAK,SAAS;AACd,aAAK,MAAMD,IAAG,KAAK,MAAMC;AACzB;AAAA,MACF,KAAK;AACH,aAAK,SAAS;AACd,aAAK,SAAS,OAAO,KAAK,MAAMD,IAAG,KAAK,MAAMC,EAAC;AAC/C;AAAA,MACF,KAAK;AACH,aAAK,SAAS;AACd,aAAK,MAAMD,IAAG,KAAK,MAAMC;AACzB;AAAA,MACF;AACE,QAAAC,OAAM,MAAMF,IAAGC,EAAC;AAChB;AAAA,IACJ;AACA,SAAK,SAAS,KAAK,QAAQ,KAAK,SAAS,KAAK;AAC9C,SAAK,UAAU,KAAK,SAAS,KAAK,UAAU,KAAK;AACjD,SAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMD;AACrD,SAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMC;AAAA,EACvD;AACF;AACA,IAAO,2BAAS,SAASE,QAAO,OAAO;AACrC,WAAS,WAAW,SAAS;AAC3B,WAAO,QAAQ,IAAI,iBAAiB,SAAS,KAAK,IAAI,IAAI,eAAe,SAAS,CAAC;AAAA,EACrF;AACA,aAAW,QAAQ,SAAUC,QAAO;AAClC,WAAOD,QAAO,CAACC,MAAK;AAAA,EACtB;AACA,SAAO;AACT,EAAG,GAAG;;;ACzEN,SAAS,eAAe,SAAS,OAAO;AACtC,OAAK,WAAW;AAChB,OAAK,SAAS;AAChB;AACA,eAAe,YAAY;AAAA,EACzB,WAAW,WAAY;AACrB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,SAAS,WAAY;AACnB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW,WAAY;AACrB,SAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AAClE,SAAK,SAAS,KAAK,SAAS,KAAK,SAAS,KAAK,UAAU,KAAK,UAAU,KAAK,UAAU,KAAK,SAAS;AAAA,EACvG;AAAA,EACA,SAAS,WAAY;AACnB,QAAI,KAAK,SAAS,KAAK,UAAU,KAAK,KAAK,WAAW,EAAG,MAAK,SAAS,UAAU;AACjF,SAAK,QAAQ,IAAI,KAAK;AAAA,EACxB;AAAA,EACA,OAAO,SAAUC,IAAGC,IAAG;AACrB,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA;AACb,QAAI,KAAK,QAAQ;AACf,UAAI,MAAM,KAAK,MAAMD,IACnB,MAAM,KAAK,MAAMC;AACnB,WAAK,SAAS,KAAK,KAAK,KAAK,UAAU,KAAK,IAAI,MAAM,MAAM,MAAM,KAAK,KAAK,MAAM,CAAC;AAAA,IACrF;AACA,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AACH,aAAK,SAAS;AACd;AAAA,MACF,KAAK;AACH,aAAK,SAAS;AACd;AAAA,MACF,KAAK;AACH,aAAK,SAAS;AACd,aAAK,QAAQ,KAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG,IAAI,KAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AAC/F;AAAA,MACF,KAAK;AACH,aAAK,SAAS;AAAA;AAAA,MAEhB;AACE,QAAAC,OAAM,MAAMF,IAAGC,EAAC;AAChB;AAAA,IACJ;AACA,SAAK,SAAS,KAAK,QAAQ,KAAK,SAAS,KAAK;AAC9C,SAAK,UAAU,KAAK,SAAS,KAAK,UAAU,KAAK;AACjD,SAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMD;AACrD,SAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMC;AAAA,EACvD;AACF;AACA,IAAO,yBAAS,SAASE,QAAO,OAAO;AACrC,WAAS,WAAW,SAAS;AAC3B,WAAO,QAAQ,IAAI,eAAe,SAAS,KAAK,IAAI,IAAI,aAAa,SAAS,CAAC;AAAA,EACjF;AACA,aAAW,QAAQ,SAAUC,QAAO;AAClC,WAAOD,QAAO,CAACC,MAAK;AAAA,EACtB;AACA,SAAO;AACT,EAAG,GAAG;;;AC3DN,SAAS,aAAa,SAAS;AAC7B,OAAK,WAAW;AAClB;AACA,aAAa,YAAY;AAAA,EACvB,WAAW;AAAA,EACX,SAAS;AAAA,EACT,WAAW,WAAY;AACrB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAY;AACnB,QAAI,KAAK,OAAQ,MAAK,SAAS,UAAU;AAAA,EAC3C;AAAA,EACA,OAAO,SAAUC,IAAGC,IAAG;AACrB,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA;AACb,QAAI,KAAK,OAAQ,MAAK,SAAS,OAAOD,IAAGC,EAAC;AAAA,QAAO,MAAK,SAAS,GAAG,KAAK,SAAS,OAAOD,IAAGC,EAAC;AAAA,EAC7F;AACF;;;ACjBA,SAAS,KAAKC,IAAG;AACf,SAAOA,KAAI,IAAI,KAAK;AACtB;AAMA,SAAS,OAAO,MAAM,IAAI,IAAI;AAC5B,MAAI,KAAK,KAAK,MAAM,KAAK,KACvB,KAAK,KAAK,KAAK,KACf,MAAM,KAAK,MAAM,KAAK,QAAQ,MAAM,KAAK,KAAK,KAC9C,MAAM,KAAK,KAAK,QAAQ,MAAM,KAAK,KAAK,KACxC,KAAK,KAAK,KAAK,KAAK,OAAO,KAAK;AAClC,UAAQ,KAAK,EAAE,IAAI,KAAK,EAAE,KAAK,KAAK,IAAI,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,GAAG,MAAM,KAAK,IAAI,CAAC,CAAC,KAAK;AAC5F;AAGA,SAAS,OAAO,MAAM,GAAG;AACvB,MAAI,IAAI,KAAK,MAAM,KAAK;AACxB,SAAO,KAAK,KAAK,KAAK,MAAM,KAAK,OAAO,IAAI,KAAK,IAAI;AACvD;AAKA,SAASC,OAAM,MAAM,IAAI,IAAI;AAC3B,MAAI,KAAK,KAAK,KACZ,KAAK,KAAK,KACV,KAAK,KAAK,KACV,KAAK,KAAK,KACV,MAAM,KAAK,MAAM;AACnB,OAAK,SAAS,cAAc,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,EAAE;AAClF;AACA,SAAS,UAAU,SAAS;AAC1B,OAAK,WAAW;AAClB;AACA,UAAU,YAAY;AAAA,EACpB,WAAW,WAAY;AACrB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,SAAS,WAAY;AACnB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW,WAAY;AACrB,SAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AACvD,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAY;AACnB,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AACH,aAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AACvC;AAAA,MACF,KAAK;AACH,QAAAA,OAAM,MAAM,KAAK,KAAK,OAAO,MAAM,KAAK,GAAG,CAAC;AAC5C;AAAA,IACJ;AACA,QAAI,KAAK,SAAS,KAAK,UAAU,KAAK,KAAK,WAAW,EAAG,MAAK,SAAS,UAAU;AACjF,SAAK,QAAQ,IAAI,KAAK;AAAA,EACxB;AAAA,EACA,OAAO,SAAUD,IAAGE,IAAG;AACrB,QAAI,KAAK;AACT,IAAAF,KAAI,CAACA,IAAGE,KAAI,CAACA;AACb,QAAIF,OAAM,KAAK,OAAOE,OAAM,KAAK,IAAK;AACtC,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AACH,aAAK,SAAS;AACd,aAAK,QAAQ,KAAK,SAAS,OAAOF,IAAGE,EAAC,IAAI,KAAK,SAAS,OAAOF,IAAGE,EAAC;AACnE;AAAA,MACF,KAAK;AACH,aAAK,SAAS;AACd;AAAA,MACF,KAAK;AACH,aAAK,SAAS;AACd,QAAAD,OAAM,MAAM,OAAO,MAAM,KAAK,OAAO,MAAMD,IAAGE,EAAC,CAAC,GAAG,EAAE;AACrD;AAAA,MACF;AACE,QAAAD,OAAM,MAAM,KAAK,KAAK,KAAK,OAAO,MAAMD,IAAGE,EAAC,CAAC;AAC7C;AAAA,IACJ;AACA,SAAK,MAAM,KAAK,KAAK,KAAK,MAAMF;AAChC,SAAK,MAAM,KAAK,KAAK,KAAK,MAAME;AAChC,SAAK,MAAM;AAAA,EACb;AACF;AACA,SAAS,UAAU,SAAS;AAC1B,OAAK,WAAW,IAAI,eAAe,OAAO;AAC5C;AAAA,CACC,UAAU,YAAY,OAAO,OAAO,UAAU,SAAS,GAAG,QAAQ,SAAUF,IAAGE,IAAG;AACjF,YAAU,UAAU,MAAM,KAAK,MAAMA,IAAGF,EAAC;AAC3C;AACA,SAAS,eAAe,SAAS;AAC/B,OAAK,WAAW;AAClB;AACA,eAAe,YAAY;AAAA,EACzB,QAAQ,SAAUA,IAAGE,IAAG;AACtB,SAAK,SAAS,OAAOA,IAAGF,EAAC;AAAA,EAC3B;AAAA,EACA,WAAW,WAAY;AACrB,SAAK,SAAS,UAAU;AAAA,EAC1B;AAAA,EACA,QAAQ,SAAUA,IAAGE,IAAG;AACtB,SAAK,SAAS,OAAOA,IAAGF,EAAC;AAAA,EAC3B;AAAA,EACA,eAAe,SAAU,IAAI,IAAI,IAAI,IAAIA,IAAGE,IAAG;AAC7C,SAAK,SAAS,cAAc,IAAI,IAAI,IAAI,IAAIA,IAAGF,EAAC;AAAA,EAClD;AACF;;;AC3GA,SAAS,QAAQ,SAAS;AACxB,OAAK,WAAW;AAClB;AACA,QAAQ,YAAY;AAAA,EAClB,WAAW,WAAY;AACrB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,SAAS,WAAY;AACnB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW,WAAY;AACrB,SAAK,KAAK,CAAC;AACX,SAAK,KAAK,CAAC;AAAA,EACb;AAAA,EACA,SAAS,WAAY;AACnB,QAAIG,KAAI,KAAK,IACXC,KAAI,KAAK,IACT,IAAID,GAAE;AACR,QAAI,GAAG;AACL,WAAK,QAAQ,KAAK,SAAS,OAAOA,GAAE,CAAC,GAAGC,GAAE,CAAC,CAAC,IAAI,KAAK,SAAS,OAAOD,GAAE,CAAC,GAAGC,GAAE,CAAC,CAAC;AAC/E,UAAI,MAAM,GAAG;AACX,aAAK,SAAS,OAAOD,GAAE,CAAC,GAAGC,GAAE,CAAC,CAAC;AAAA,MACjC,OAAO;AACL,YAAI,KAAK,cAAcD,EAAC,GACtB,KAAK,cAAcC,EAAC;AACtB,iBAAS,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,EAAE,IAAI,EAAE,IAAI;AAC3C,eAAK,SAAS,cAAc,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,CAAC,EAAE,EAAE,GAAGD,GAAE,EAAE,GAAGC,GAAE,EAAE,CAAC;AAAA,QACtF;AAAA,MACF;AAAA,IACF;AACA,QAAI,KAAK,SAAS,KAAK,UAAU,KAAK,MAAM,EAAG,MAAK,SAAS,UAAU;AACvE,SAAK,QAAQ,IAAI,KAAK;AACtB,SAAK,KAAK,KAAK,KAAK;AAAA,EACtB;AAAA,EACA,OAAO,SAAUD,IAAGC,IAAG;AACrB,SAAK,GAAG,KAAK,CAACD,EAAC;AACf,SAAK,GAAG,KAAK,CAACC,EAAC;AAAA,EACjB;AACF;AAGA,SAAS,cAAcD,IAAG;AACxB,MAAI,GACF,IAAIA,GAAE,SAAS,GACf,GACAE,KAAI,IAAI,MAAM,CAAC,GACf,IAAI,IAAI,MAAM,CAAC,GACf,IAAI,IAAI,MAAM,CAAC;AACjB,EAAAA,GAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAIF,GAAE,CAAC,IAAI,IAAIA,GAAE,CAAC;AACzC,OAAK,IAAI,GAAG,IAAI,IAAI,GAAG,EAAE,EAAG,CAAAE,GAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,IAAIF,GAAE,CAAC,IAAI,IAAIA,GAAE,IAAI,CAAC;AAC7E,EAAAE,GAAE,IAAI,CAAC,IAAI,GAAG,EAAE,IAAI,CAAC,IAAI,GAAG,EAAE,IAAI,CAAC,IAAI,IAAIF,GAAE,IAAI,CAAC,IAAIA,GAAE,CAAC;AACzD,OAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,KAAIE,GAAE,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,IAAI,EAAE,IAAI,CAAC;AAC3E,EAAAA,GAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC;AAC7B,OAAK,IAAI,IAAI,GAAG,KAAK,GAAG,EAAE,EAAG,CAAAA,GAAE,CAAC,KAAK,EAAE,CAAC,IAAIA,GAAE,IAAI,CAAC,KAAK,EAAE,CAAC;AAC3D,IAAE,IAAI,CAAC,KAAKF,GAAE,CAAC,IAAIE,GAAE,IAAI,CAAC,KAAK;AAC/B,OAAK,IAAI,GAAG,IAAI,IAAI,GAAG,EAAE,EAAG,GAAE,CAAC,IAAI,IAAIF,GAAE,IAAI,CAAC,IAAIE,GAAE,IAAI,CAAC;AACzD,SAAO,CAACA,IAAG,CAAC;AACd;;;ACzDA,SAAS,KAAK,SAAS,GAAG;AACxB,OAAK,WAAW;AAChB,OAAK,KAAK;AACZ;AACA,KAAK,YAAY;AAAA,EACf,WAAW,WAAY;AACrB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,SAAS,WAAY;AACnB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW,WAAY;AACrB,SAAK,KAAK,KAAK,KAAK;AACpB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAY;AACnB,QAAI,IAAI,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,WAAW,EAAG,MAAK,SAAS,OAAO,KAAK,IAAI,KAAK,EAAE;AAC1F,QAAI,KAAK,SAAS,KAAK,UAAU,KAAK,KAAK,WAAW,EAAG,MAAK,SAAS,UAAU;AACjF,QAAI,KAAK,SAAS,EAAG,MAAK,KAAK,IAAI,KAAK,IAAI,KAAK,QAAQ,IAAI,KAAK;AAAA,EACpE;AAAA,EACA,OAAO,SAAUC,IAAGC,IAAG;AACrB,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA;AACb,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AACH,aAAK,SAAS;AACd,aAAK,QAAQ,KAAK,SAAS,OAAOD,IAAGC,EAAC,IAAI,KAAK,SAAS,OAAOD,IAAGC,EAAC;AACnE;AAAA,MACF,KAAK;AACH,aAAK,SAAS;AAAA;AAAA,MAEhB,SACE;AACE,YAAI,KAAK,MAAM,GAAG;AAChB,eAAK,SAAS,OAAO,KAAK,IAAIA,EAAC;AAC/B,eAAK,SAAS,OAAOD,IAAGC,EAAC;AAAA,QAC3B,OAAO;AACL,cAAI,KAAK,KAAK,MAAM,IAAI,KAAK,MAAMD,KAAI,KAAK;AAC5C,eAAK,SAAS,OAAO,IAAI,KAAK,EAAE;AAChC,eAAK,SAAS,OAAO,IAAIC,EAAC;AAAA,QAC5B;AACA;AAAA,MACF;AAAA,IACJ;AACA,SAAK,KAAKD,IAAG,KAAK,KAAKC;AAAA,EACzB;AACF;;;AC5CA,SAAS,iBAAiB,GAAG;AAC3B,SAAO,CAAC,EAAE,OAAO,IAAI,EAAE,EAAE;AAC3B;AACA,SAAS,iBAAiB,GAAG;AAC3B,SAAO,CAAC,EAAE,OAAO,IAAI,EAAE,EAAE;AAC3B;AACe,SAAR,+BAAoB;AACzB,SAAO,eAAe,EAAE,OAAO,gBAAgB,EAAE,OAAO,gBAAgB;AAC1E;;;ACNA,IAAI,SAAS,WAAY;AACvB,MAAI,IAAmB,OAAO,SAAUC,IAAG,GAAG,IAAI,GAAG;AACjD,SAAK,KAAK,MAAM,CAAC,GAAG,IAAIA,GAAE,QAAQ,KAAK,GAAGA,GAAE,CAAC,CAAC,IAAI,EAAE;AACpD,WAAO;AAAA,EACT,GAAG,GAAG,GACN,MAAM,CAAC,GAAG,CAAC,GACX,MAAM,CAAC,GAAG,EAAE,GACZ,MAAM,CAAC,GAAG,GAAG,IAAI,EAAE;AACrB,MAAI,UAAU;AAAA,IACZ,OAAsB,OAAO,SAAS,QAAQ;AAAA,IAAC,GAAG,OAAO;AAAA,IACzD,IAAI,CAAC;AAAA,IACL,UAAU;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,MACT,UAAU;AAAA,MACV,WAAW;AAAA,MACX,OAAO;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,iBAAiB;AAAA,MACjB,SAAS;AAAA,MACT,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,SAAS;AAAA,MACT,WAAW;AAAA,MACX,eAAe;AAAA,MACf,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,QAAQ;AAAA,IACV;AAAA,IACA,YAAY;AAAA,MACV,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,IACN;AAAA,IACA,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAAA,IAC5G,eAA8B,OAAO,SAAS,UAAU,QAAQ,QAAQ,UAAU,IAAI,SAAS,IAAI,IAAI;AACrG,UAAI,KAAK,GAAG,SAAS;AACrB,cAAQ,SAAS;AAAA,QACf,KAAK;AACH,gBAAM,SAAS,GAAG,iBAAiB,GAAG,KAAK,CAAC,EAAE,KAAK,EAAE,WAAW,MAAM,GAAG,CAAC;AAC1E,gBAAM,SAAS,GAAG,iBAAiB,GAAG,KAAK,CAAC,EAAE,KAAK,EAAE,WAAW,MAAM,GAAG,CAAC;AAC1E,gBAAMC,SAAQ,WAAW,GAAG,EAAE,EAAE,KAAK,CAAC;AACtC,aAAG,QAAQ,QAAQ,QAAQA,MAAK;AAChC;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,eAAK,IAAI,GAAG,KAAK,CAAC;AAClB;AAAA,MACJ;AAAA,IACF,GAAG,WAAW;AAAA,IACd,OAAO,CAAC;AAAA,MACN,GAAG;AAAA,MACH,GAAG,CAAC,GAAG,CAAC;AAAA,IACV,GAAG;AAAA,MACD,GAAG,CAAC,CAAC;AAAA,IACP,GAAG;AAAA,MACD,GAAG,CAAC,GAAG,CAAC;AAAA,IACV,GAAG;AAAA,MACD,GAAG;AAAA,MACH,GAAG;AAAA,MACH,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,IACN,GAAG;AAAA,MACD,GAAG,CAAC,GAAG,CAAC;AAAA,MACR,GAAG;AAAA,MACH,IAAI,CAAC,GAAG,EAAE;AAAA,IACZ,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG;AAAA,MAChB,GAAG;AAAA,MACH,GAAG,CAAC,GAAG,EAAE;AAAA,IACX,CAAC,GAAG;AAAA,MACF,IAAI,CAAC,GAAG,EAAE;AAAA,IACZ,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG;AAAA,MACjC,IAAI,CAAC,GAAG,EAAE;AAAA,IACZ,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG;AAAA,MAClB,GAAG,CAAC,GAAG,CAAC;AAAA,IACV,GAAG;AAAA,MACD,GAAG,CAAC,GAAG,CAAC;AAAA,IACV,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG;AAAA,MACjB,GAAG;AAAA,MACH,GAAG;AAAA,MACH,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,IACN,GAAG;AAAA,MACD,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,IACN,GAAG;AAAA,MACD,IAAI,CAAC,GAAG,EAAE;AAAA,IACZ,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG;AAAA,MACjB,IAAI,CAAC,GAAG,EAAE;AAAA,IACZ,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG;AAAA,MAClB,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,IACN,GAAG,EAAE,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAAA,IACxB,gBAAgB;AAAA,MACd,IAAI,CAAC,GAAG,CAAC;AAAA,MACT,IAAI,CAAC,GAAG,CAAC;AAAA,IACX;AAAA,IACA,YAA2B,OAAO,SAAS,WAAW,KAAK,MAAM;AAC/D,UAAI,KAAK,aAAa;AACpB,aAAK,MAAM,GAAG;AAAA,MAChB,OAAO;AACL,YAAI,QAAQ,IAAI,MAAM,GAAG;AACzB,cAAM,OAAO;AACb,cAAM;AAAA,MACR;AAAA,IACF,GAAG,YAAY;AAAA,IACf,OAAsB,OAAO,SAAS,MAAM,OAAO;AACjD,UAAI,OAAO,MACT,QAAQ,CAAC,CAAC,GACV,SAAS,CAAC,GACV,SAAS,CAAC,IAAI,GACd,SAAS,CAAC,GACV,QAAQ,KAAK,OACb,SAAS,IACT,WAAW,GACX,SAAS,GACT,aAAa,GACb,SAAS,GACT,MAAM;AACR,UAAI,OAAO,OAAO,MAAM,KAAK,WAAW,CAAC;AACzC,UAAI,SAAS,OAAO,OAAO,KAAK,KAAK;AACrC,UAAI,cAAc;AAAA,QAChB,IAAI,CAAC;AAAA,MACP;AACA,eAASD,MAAK,KAAK,IAAI;AACrB,YAAI,OAAO,UAAU,eAAe,KAAK,KAAK,IAAIA,EAAC,GAAG;AACpD,sBAAY,GAAGA,EAAC,IAAI,KAAK,GAAGA,EAAC;AAAA,QAC/B;AAAA,MACF;AACA,aAAO,SAAS,OAAO,YAAY,EAAE;AACrC,kBAAY,GAAG,QAAQ;AACvB,kBAAY,GAAG,SAAS;AACxB,UAAI,OAAO,OAAO,UAAU,aAAa;AACvC,eAAO,SAAS,CAAC;AAAA,MACnB;AACA,UAAI,QAAQ,OAAO;AACnB,aAAO,KAAK,KAAK;AACjB,UAAI,SAAS,OAAO,WAAW,OAAO,QAAQ;AAC9C,UAAI,OAAO,YAAY,GAAG,eAAe,YAAY;AACnD,aAAK,aAAa,YAAY,GAAG;AAAA,MACnC,OAAO;AACL,aAAK,aAAa,OAAO,eAAe,IAAI,EAAE;AAAA,MAChD;AACA,eAAS,SAAS,GAAG;AACnB,cAAM,SAAS,MAAM,SAAS,IAAI;AAClC,eAAO,SAAS,OAAO,SAAS;AAChC,eAAO,SAAS,OAAO,SAAS;AAAA,MAClC;AACA,aAAO,UAAU,UAAU;AAC3B,eAAS,MAAM;AACb,YAAI;AACJ,gBAAQ,OAAO,IAAI,KAAK,OAAO,IAAI,KAAK;AACxC,YAAI,OAAO,UAAU,UAAU;AAC7B,cAAI,iBAAiB,OAAO;AAC1B,qBAAS;AACT,oBAAQ,OAAO,IAAI;AAAA,UACrB;AACA,kBAAQ,KAAK,SAAS,KAAK,KAAK;AAAA,QAClC;AACA,eAAO;AAAA,MACT;AACA,aAAO,KAAK,KAAK;AACjB,UAAI,QACF,gBACA,OACA,QACAE,IACA,GACA,QAAQ,CAAC,GACT,GACA,KACA,UACA;AACF,aAAO,MAAM;AACX,gBAAQ,MAAM,MAAM,SAAS,CAAC;AAC9B,YAAI,KAAK,eAAe,KAAK,GAAG;AAC9B,mBAAS,KAAK,eAAe,KAAK;AAAA,QACpC,OAAO;AACL,cAAI,WAAW,QAAQ,OAAO,UAAU,aAAa;AACnD,qBAAS,IAAI;AAAA,UACf;AACA,mBAAS,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,MAAM;AAAA,QAC9C;AACA,YAAI,OAAO,WAAW,eAAe,CAAC,OAAO,UAAU,CAAC,OAAO,CAAC,GAAG;AACjE,cAAI,SAAS;AACb,qBAAW,CAAC;AACZ,eAAK,KAAK,MAAM,KAAK,GAAG;AACtB,gBAAI,KAAK,WAAW,CAAC,KAAK,IAAI,QAAQ;AACpC,uBAAS,KAAK,MAAM,KAAK,WAAW,CAAC,IAAI,GAAG;AAAA,YAC9C;AAAA,UACF;AACA,cAAI,OAAO,cAAc;AACvB,qBAAS,0BAA0B,WAAW,KAAK,QAAQ,OAAO,aAAa,IAAI,iBAAiB,SAAS,KAAK,IAAI,IAAI,aAAa,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UAC9K,OAAO;AACL,qBAAS,0BAA0B,WAAW,KAAK,mBAAmB,UAAU,MAAM,iBAAiB,OAAO,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UACrJ;AACA,eAAK,WAAW,QAAQ;AAAA,YACtB,MAAM,OAAO;AAAA,YACb,OAAO,KAAK,WAAW,MAAM,KAAK;AAAA,YAClC,MAAM,OAAO;AAAA,YACb,KAAK;AAAA,YACL;AAAA,UACF,CAAC;AAAA,QACH;AACA,YAAI,OAAO,CAAC,aAAa,SAAS,OAAO,SAAS,GAAG;AACnD,gBAAM,IAAI,MAAM,sDAAsD,QAAQ,cAAc,MAAM;AAAA,QACpG;AACA,gBAAQ,OAAO,CAAC,GAAG;AAAA,UACjB,KAAK;AACH,kBAAM,KAAK,MAAM;AACjB,mBAAO,KAAK,OAAO,MAAM;AACzB,mBAAO,KAAK,OAAO,MAAM;AACzB,kBAAM,KAAK,OAAO,CAAC,CAAC;AACpB,qBAAS;AACT,gBAAI,CAAC,gBAAgB;AACnB,uBAAS,OAAO;AAChB,uBAAS,OAAO;AAChB,yBAAW,OAAO;AAClB,sBAAQ,OAAO;AACf,kBAAI,aAAa,GAAG;AAClB;AAAA,cACF;AAAA,YACF,OAAO;AACL,uBAAS;AACT,+BAAiB;AAAA,YACnB;AACA;AAAA,UACF,KAAK;AACH,kBAAM,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC;AACpC,kBAAM,IAAI,OAAO,OAAO,SAAS,GAAG;AACpC,kBAAM,KAAK;AAAA,cACT,YAAY,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cAC/C,WAAW,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,cACrC,cAAc,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cACjD,aAAa,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,YACzC;AACA,gBAAI,QAAQ;AACV,oBAAM,GAAG,QAAQ,CAAC,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE,MAAM,CAAC,GAAG,OAAO,OAAO,SAAS,CAAC,EAAE,MAAM,CAAC,CAAC;AAAA,YACnG;AACA,gBAAI,KAAK,cAAc,MAAM,OAAO,CAAC,QAAQ,QAAQ,UAAU,YAAY,IAAI,OAAO,CAAC,GAAG,QAAQ,MAAM,EAAE,OAAO,IAAI,CAAC;AACtH,gBAAI,OAAO,MAAM,aAAa;AAC5B,qBAAO;AAAA,YACT;AACA,gBAAI,KAAK;AACP,sBAAQ,MAAM,MAAM,GAAG,KAAK,MAAM,CAAC;AACnC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AACjC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AAAA,YACnC;AACA,kBAAM,KAAK,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1C,mBAAO,KAAK,MAAM,CAAC;AACnB,mBAAO,KAAK,MAAM,EAAE;AACpB,uBAAW,MAAM,MAAM,MAAM,SAAS,CAAC,CAAC,EAAE,MAAM,MAAM,SAAS,CAAC,CAAC;AACjE,kBAAM,KAAK,QAAQ;AACnB;AAAA,UACF,KAAK;AACH,mBAAO;AAAA,QACX;AAAA,MACF;AACA,aAAO;AAAA,IACT,GAAG,OAAO;AAAA,EACZ;AACA,MAAI,QAAuB,WAAY;AACrC,QAAI,SAAS;AAAA,MACX,KAAK;AAAA,MACL,YAA2B,OAAO,SAAS,WAAW,KAAK,MAAM;AAC/D,YAAI,KAAK,GAAG,QAAQ;AAClB,eAAK,GAAG,OAAO,WAAW,KAAK,IAAI;AAAA,QACrC,OAAO;AACL,gBAAM,IAAI,MAAM,GAAG;AAAA,QACrB;AAAA,MACF,GAAG,YAAY;AAAA;AAAA,MAEf,UAAyB,OAAO,SAAU,OAAO,IAAI;AACnD,aAAK,KAAK,MAAM,KAAK,MAAM,CAAC;AAC5B,aAAK,SAAS;AACd,aAAK,QAAQ,KAAK,aAAa,KAAK,OAAO;AAC3C,aAAK,WAAW,KAAK,SAAS;AAC9B,aAAK,SAAS,KAAK,UAAU,KAAK,QAAQ;AAC1C,aAAK,iBAAiB,CAAC,SAAS;AAChC,aAAK,SAAS;AAAA,UACZ,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,WAAW;AAAA,UACX,aAAa;AAAA,QACf;AACA,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,QAAQ,CAAC,GAAG,CAAC;AAAA,QAC3B;AACA,aAAK,SAAS;AACd,eAAO;AAAA,MACT,GAAG,UAAU;AAAA;AAAA,MAEb,OAAsB,OAAO,WAAY;AACvC,YAAI,KAAK,KAAK,OAAO,CAAC;AACtB,aAAK,UAAU;AACf,aAAK;AACL,aAAK;AACL,aAAK,SAAS;AACd,aAAK,WAAW;AAChB,YAAI,QAAQ,GAAG,MAAM,iBAAiB;AACtC,YAAI,OAAO;AACT,eAAK;AACL,eAAK,OAAO;AAAA,QACd,OAAO;AACL,eAAK,OAAO;AAAA,QACd;AACA,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,MAAM,CAAC;AAAA,QACrB;AACA,aAAK,SAAS,KAAK,OAAO,MAAM,CAAC;AACjC,eAAO;AAAA,MACT,GAAG,OAAO;AAAA;AAAA,MAEV,OAAsB,OAAO,SAAU,IAAI;AACzC,YAAI,MAAM,GAAG;AACb,YAAI,QAAQ,GAAG,MAAM,eAAe;AACpC,aAAK,SAAS,KAAK,KAAK;AACxB,aAAK,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,OAAO,SAAS,GAAG;AAC5D,aAAK,UAAU;AACf,YAAI,WAAW,KAAK,MAAM,MAAM,eAAe;AAC/C,aAAK,QAAQ,KAAK,MAAM,OAAO,GAAG,KAAK,MAAM,SAAS,CAAC;AACvD,aAAK,UAAU,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,CAAC;AAC7D,YAAI,MAAM,SAAS,GAAG;AACpB,eAAK,YAAY,MAAM,SAAS;AAAA,QAClC;AACA,YAAI,IAAI,KAAK,OAAO;AACpB,aAAK,SAAS;AAAA,UACZ,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,SAAS,MAAM,WAAW,SAAS,SAAS,KAAK,OAAO,eAAe,KAAK,SAAS,SAAS,SAAS,MAAM,MAAM,EAAE,SAAS,MAAM,CAAC,EAAE,SAAS,KAAK,OAAO,eAAe;AAAA,QAC1L;AACA,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,SAAS,GAAG;AAAA,QACrD;AACA,aAAK,SAAS,KAAK,OAAO;AAC1B,eAAO;AAAA,MACT,GAAG,OAAO;AAAA;AAAA,MAEV,MAAqB,OAAO,WAAY;AACtC,aAAK,QAAQ;AACb,eAAO;AAAA,MACT,GAAG,MAAM;AAAA;AAAA,MAET,QAAuB,OAAO,WAAY;AACxC,YAAI,KAAK,QAAQ,iBAAiB;AAChC,eAAK,aAAa;AAAA,QACpB,OAAO;AACL,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,qIAAqI,KAAK,aAAa,GAAG;AAAA,YAChO,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACb,CAAC;AAAA,QACH;AACA,eAAO;AAAA,MACT,GAAG,QAAQ;AAAA;AAAA,MAEX,MAAqB,OAAO,SAAU,GAAG;AACvC,aAAK,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC;AAAA,MAChC,GAAG,MAAM;AAAA;AAAA,MAET,WAA0B,OAAO,WAAY;AAC3C,YAAI,OAAO,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,KAAK,MAAM,MAAM;AACzE,gBAAQ,KAAK,SAAS,KAAK,QAAQ,MAAM,KAAK,OAAO,GAAG,EAAE,QAAQ,OAAO,EAAE;AAAA,MAC7E,GAAG,WAAW;AAAA;AAAA,MAEd,eAA8B,OAAO,WAAY;AAC/C,YAAI,OAAO,KAAK;AAChB,YAAI,KAAK,SAAS,IAAI;AACpB,kBAAQ,KAAK,OAAO,OAAO,GAAG,KAAK,KAAK,MAAM;AAAA,QAChD;AACA,gBAAQ,KAAK,OAAO,GAAG,EAAE,KAAK,KAAK,SAAS,KAAK,QAAQ,KAAK,QAAQ,OAAO,EAAE;AAAA,MACjF,GAAG,eAAe;AAAA;AAAA,MAElB,cAA6B,OAAO,WAAY;AAC9C,YAAI,MAAM,KAAK,UAAU;AACzB,YAAI,IAAI,IAAI,MAAM,IAAI,SAAS,CAAC,EAAE,KAAK,GAAG;AAC1C,eAAO,MAAM,KAAK,cAAc,IAAI,OAAO,IAAI;AAAA,MACjD,GAAG,cAAc;AAAA;AAAA,MAEjB,YAA2B,OAAO,SAAU,OAAO,cAAc;AAC/D,YAAI,OAAO,OAAO;AAClB,YAAI,KAAK,QAAQ,iBAAiB;AAChC,mBAAS;AAAA,YACP,UAAU,KAAK;AAAA,YACf,QAAQ;AAAA,cACN,YAAY,KAAK,OAAO;AAAA,cACxB,WAAW,KAAK;AAAA,cAChB,cAAc,KAAK,OAAO;AAAA,cAC1B,aAAa,KAAK,OAAO;AAAA,YAC3B;AAAA,YACA,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,SAAS,KAAK;AAAA,YACd,SAAS,KAAK;AAAA,YACd,QAAQ,KAAK;AAAA,YACb,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,QAAQ,KAAK;AAAA,YACb,IAAI,KAAK;AAAA,YACT,gBAAgB,KAAK,eAAe,MAAM,CAAC;AAAA,YAC3C,MAAM,KAAK;AAAA,UACb;AACA,cAAI,KAAK,QAAQ,QAAQ;AACvB,mBAAO,OAAO,QAAQ,KAAK,OAAO,MAAM,MAAM,CAAC;AAAA,UACjD;AAAA,QACF;AACA,gBAAQ,MAAM,CAAC,EAAE,MAAM,iBAAiB;AACxC,YAAI,OAAO;AACT,eAAK,YAAY,MAAM;AAAA,QACzB;AACA,aAAK,SAAS;AAAA,UACZ,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,QAAQ,MAAM,MAAM,SAAS,CAAC,EAAE,SAAS,MAAM,MAAM,SAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,SAAS,KAAK,OAAO,cAAc,MAAM,CAAC,EAAE;AAAA,QAC/I;AACA,aAAK,UAAU,MAAM,CAAC;AACtB,aAAK,SAAS,MAAM,CAAC;AACrB,aAAK,UAAU;AACf,aAAK,SAAS,KAAK,OAAO;AAC1B,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,QAAQ,CAAC,KAAK,QAAQ,KAAK,UAAU,KAAK,MAAM;AAAA,QAC9D;AACA,aAAK,QAAQ;AACb,aAAK,aAAa;AAClB,aAAK,SAAS,KAAK,OAAO,MAAM,MAAM,CAAC,EAAE,MAAM;AAC/C,aAAK,WAAW,MAAM,CAAC;AACvB,gBAAQ,KAAK,cAAc,KAAK,MAAM,KAAK,IAAI,MAAM,cAAc,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC;AACtH,YAAI,KAAK,QAAQ,KAAK,QAAQ;AAC5B,eAAK,OAAO;AAAA,QACd;AACA,YAAI,OAAO;AACT,iBAAO;AAAA,QACT,WAAW,KAAK,YAAY;AAC1B,mBAASF,MAAK,QAAQ;AACpB,iBAAKA,EAAC,IAAI,OAAOA,EAAC;AAAA,UACpB;AACA,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT,GAAG,YAAY;AAAA;AAAA,MAEf,MAAqB,OAAO,WAAY;AACtC,YAAI,KAAK,MAAM;AACb,iBAAO,KAAK;AAAA,QACd;AACA,YAAI,CAAC,KAAK,QAAQ;AAChB,eAAK,OAAO;AAAA,QACd;AACA,YAAI,OAAO,OAAO,WAAWG;AAC7B,YAAI,CAAC,KAAK,OAAO;AACf,eAAK,SAAS;AACd,eAAK,QAAQ;AAAA,QACf;AACA,YAAI,QAAQ,KAAK,cAAc;AAC/B,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,sBAAY,KAAK,OAAO,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC,CAAC;AAClD,cAAI,cAAc,CAAC,SAAS,UAAU,CAAC,EAAE,SAAS,MAAM,CAAC,EAAE,SAAS;AAClE,oBAAQ;AACR,YAAAA,SAAQ;AACR,gBAAI,KAAK,QAAQ,iBAAiB;AAChC,sBAAQ,KAAK,WAAW,WAAW,MAAM,CAAC,CAAC;AAC3C,kBAAI,UAAU,OAAO;AACnB,uBAAO;AAAA,cACT,WAAW,KAAK,YAAY;AAC1B,wBAAQ;AACR;AAAA,cACF,OAAO;AACL,uBAAO;AAAA,cACT;AAAA,YACF,WAAW,CAAC,KAAK,QAAQ,MAAM;AAC7B;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,YAAI,OAAO;AACT,kBAAQ,KAAK,WAAW,OAAO,MAAMA,MAAK,CAAC;AAC3C,cAAI,UAAU,OAAO;AACnB,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT;AACA,YAAI,KAAK,WAAW,IAAI;AACtB,iBAAO,KAAK;AAAA,QACd,OAAO;AACL,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,2BAA2B,KAAK,aAAa,GAAG;AAAA,YACtH,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACb,CAAC;AAAA,QACH;AAAA,MACF,GAAG,MAAM;AAAA;AAAA,MAET,KAAoB,OAAO,SAAS,MAAM;AACxC,YAAI,IAAI,KAAK,KAAK;AAClB,YAAI,GAAG;AACL,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO,KAAK,IAAI;AAAA,QAClB;AAAA,MACF,GAAG,KAAK;AAAA;AAAA,MAER,OAAsB,OAAO,SAAS,MAAM,WAAW;AACrD,aAAK,eAAe,KAAK,SAAS;AAAA,MACpC,GAAG,OAAO;AAAA;AAAA,MAEV,UAAyB,OAAO,SAAS,WAAW;AAClD,YAAI,IAAI,KAAK,eAAe,SAAS;AACrC,YAAI,IAAI,GAAG;AACT,iBAAO,KAAK,eAAe,IAAI;AAAA,QACjC,OAAO;AACL,iBAAO,KAAK,eAAe,CAAC;AAAA,QAC9B;AAAA,MACF,GAAG,UAAU;AAAA;AAAA,MAEb,eAA8B,OAAO,SAAS,gBAAgB;AAC5D,YAAI,KAAK,eAAe,UAAU,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,GAAG;AACrF,iBAAO,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC,EAAE;AAAA,QAC9E,OAAO;AACL,iBAAO,KAAK,WAAW,SAAS,EAAE;AAAA,QACpC;AAAA,MACF,GAAG,eAAe;AAAA;AAAA,MAElB,UAAyB,OAAO,SAAS,SAAS,GAAG;AACnD,YAAI,KAAK,eAAe,SAAS,IAAI,KAAK,IAAI,KAAK,CAAC;AACpD,YAAI,KAAK,GAAG;AACV,iBAAO,KAAK,eAAe,CAAC;AAAA,QAC9B,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF,GAAG,UAAU;AAAA;AAAA,MAEb,WAA0B,OAAO,SAAS,UAAU,WAAW;AAC7D,aAAK,MAAM,SAAS;AAAA,MACtB,GAAG,WAAW;AAAA;AAAA,MAEd,gBAA+B,OAAO,SAAS,iBAAiB;AAC9D,eAAO,KAAK,eAAe;AAAA,MAC7B,GAAG,gBAAgB;AAAA,MACnB,SAAS;AAAA,QACP,oBAAoB;AAAA,MACtB;AAAA,MACA,eAA8B,OAAO,SAAS,UAAU,IAAI,KAAK,2BAA2B,UAAU;AACpG,YAAI,UAAU;AACd,gBAAQ,2BAA2B;AAAA,UACjC,KAAK;AACH,iBAAK,UAAU,KAAK;AACpB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,UAAU,cAAc;AAC7B,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS,cAAc;AAC5B,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,QACJ;AAAA,MACF,GAAG,WAAW;AAAA,MACd,OAAO,CAAC,uBAAuB,WAAW,mCAAmC,kBAAkB,kBAAkB,sDAAsD,8BAA8B,kGAAkG;AAAA,MACvS,YAAY;AAAA,QACV,OAAO;AAAA,UACL,SAAS,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,UAC7B,aAAa;AAAA,QACf;AAAA,QACA,gBAAgB;AAAA,UACd,SAAS,CAAC,GAAG,CAAC;AAAA,UACd,aAAa;AAAA,QACf;AAAA,QACA,WAAW;AAAA,UACT,SAAS,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,UAChC,aAAa;AAAA,QACf;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT,EAAE;AACF,UAAQ,QAAQ;AAChB,WAAS,SAAS;AAChB,SAAK,KAAK,CAAC;AAAA,EACb;AACA,SAAO,QAAQ,QAAQ;AACvB,SAAO,YAAY;AACnB,UAAQ,SAAS;AACjB,SAAO,IAAI,OAAO;AACpB,EAAE;AACF,OAAO,SAAS;AAChB,IAAI,iBAAiB;AAGrB,IAAI,QAAQ,CAAC;AACb,IAAI,QAAQ,CAAC;AACb,IAAI,WAA0B,oBAAI,IAAI;AACtC,IAAI,SAAwB,OAAO,MAAM;AACvC,UAAQ,CAAC;AACT,UAAQ,CAAC;AACT,aAA0B,oBAAI,IAAI;AAClC,QAAM;AACR,GAAG,OAAO;AACV,IAAI,aAAa,MAAM;AAAA,EACrB,YAAY,QAAQ,QAAQF,SAAQ,GAAG;AACrC,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,QAAQA;AAAA,EACf;AAAA,EACA,OAAO;AACL,WAAO,MAAM,YAAY;AAAA,EAC3B;AACF;AACA,IAAI,UAAyB,OAAO,CAAC,QAAQ,QAAQA,WAAU;AAC7D,QAAM,KAAK,IAAI,WAAW,QAAQ,QAAQA,MAAK,CAAC;AAClD,GAAG,SAAS;AACZ,IAAI,aAAa,MAAM;AAAA,EACrB,YAAY,IAAI;AACd,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,OAAO;AACL,WAAO,MAAM,YAAY;AAAA,EAC3B;AACF;AACA,IAAI,mBAAkC,OAAO,QAAM;AACjD,OAAK,eAAe,aAAa,IAAI,WAAU,CAAC;AAChD,MAAI,OAAO,SAAS,IAAI,EAAE;AAC1B,MAAI,SAAS,QAAQ;AACnB,WAAO,IAAI,WAAW,EAAE;AACxB,aAAS,IAAI,IAAI,IAAI;AACrB,UAAM,KAAK,IAAI;AAAA,EACjB;AACA,SAAO;AACT,GAAG,kBAAkB;AACrB,IAAI,WAA0B,OAAO,MAAM,OAAO,UAAU;AAC5D,IAAI,WAA0B,OAAO,MAAM,OAAO,UAAU;AAC5D,IAAI,WAA0B,OAAO,OAAO;AAAA,EAC1C,OAAO,MAAM,IAAI,WAAS;AAAA,IACxB,IAAI,KAAK;AAAA,EACX,EAAE;AAAA,EACF,OAAO,MAAM,IAAI,CAAAG,WAAS;AAAA,IACxB,QAAQA,MAAK,OAAO;AAAA,IACpB,QAAQA,MAAK,OAAO;AAAA,IACpB,OAAOA,MAAK;AAAA,EACd,EAAE;AACJ,IAAI,UAAU;AACd,IAAI,mBAAmB;AAAA,EACrB;AAAA,EACA,WAA0B,OAAO,MAAM,WAAU,EAAE,QAAQ,WAAW;AAAA,EACtE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,OAAO;AACT;AAOA,IAAI,MAAM,MAAM,KAAK;AAAA,EACnB,OAAO;AACL,WAAO,MAAM,KAAK;AAAA,EACpB;AAAA,EACA,OAAO;AACL,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,OAAO,KAAK,MAAM;AAChB,WAAO,IAAI,KAAK,OAAO,EAAE,KAAK,KAAK;AAAA,EACrC;AAAA,EACA,YAAY,IAAI;AACd,SAAK,KAAK;AACV,SAAK,OAAO,IAAI,EAAE;AAAA,EACpB;AAAA,EACA,WAAW;AACT,WAAO,SAAS,KAAK,OAAO;AAAA,EAC9B;AACF;AAGA,IAAI,gBAAgB;AAAA,EAClB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAI,OAAsB,OAAO,SAAU,MAAM,IAAI,UAAU,SAAS;AACtE,QAAM;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,EACV,IAAI,WAAU;AACd,QAAM,sBAAsB,eAAc;AAC1C,MAAI;AACJ,MAAI,kBAAkB,WAAW;AAC/B,qBAAiB,eAAS,OAAO,EAAE;AAAA,EACrC;AACA,QAAM,OAAO,kBAAkB,YAAY,eAAS,eAAe,MAAM,EAAE,CAAC,EAAE,gBAAgB,IAAI,IAAI,eAAS,MAAM;AACrH,QAAM,MAAM,kBAAkB,YAAY,KAAK,OAAO,QAAQ,EAAE,IAAI,IAAI,eAAS,QAAQ,EAAE,IAAI;AAC/F,QAAM,QAAQ,MAAM,SAAS,oBAAoB;AACjD,QAAM,SAAS,MAAM,UAAU,oBAAoB;AACnD,QAAM,cAAc,MAAM,eAAe,oBAAoB;AAC7D,QAAM,gBAAgB,MAAM,iBAAiB,oBAAoB;AACjE,QAAM,SAAS,MAAM,UAAU,oBAAoB;AACnD,QAAM,SAAS,MAAM,UAAU,oBAAoB;AACnD,QAAM,aAAa,MAAM,cAAc,oBAAoB;AAC3D,QAAM,QAAQ,QAAQ,GAAG,SAAS;AAClC,QAAM,YAAY,cAAc,aAAa;AAC7C,QAAM,YAAY;AAClB,QAAM,SAAS,OAAS,EAAE,OAAO,OAAK,EAAE,EAAE,EAAE,UAAU,SAAS,EAAE,YAAY,MAAM,aAAa,KAAK,EAAE,EAAE,UAAU,SAAS,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,MAAM,CAAC,CAAC;AAC9J,SAAO,KAAK;AACZ,QAAM,cAAc,QAAe,iBAAiB;AACpD,MAAI,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO,EAAE,UAAU,OAAO,EAAE,KAAK,MAAM,KAAK,EAAE,KAAK,GAAG,EAAE,KAAK,SAAS,MAAM,EAAE,KAAK,MAAM,QAAM,EAAE,MAAM,IAAI,KAAK,OAAO,GAAG,EAAE,EAAE,KAAK,aAAa,SAAU,GAAG;AACzL,WAAO,eAAe,EAAE,KAAK,MAAM,EAAE,KAAK;AAAA,EAC5C,CAAC,EAAE,KAAK,KAAK,OAAK,EAAE,EAAE,EAAE,KAAK,KAAK,OAAK,EAAE,EAAE,EAAE,OAAO,MAAM,EAAE,KAAK,UAAU,OAAK;AAC9E,WAAO,EAAE,KAAK,EAAE;AAAA,EAClB,CAAC,EAAE,KAAK,SAAS,OAAK,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,QAAQ,OAAK,YAAY,EAAE,EAAE,CAAC;AACtE,QAAM,UAAyB,OAAO,CAAC;AAAA,IACrC,IAAI;AAAA,IACJ,OAAAH;AAAA,EACF,MAAM;AACJ,QAAI,CAAC,YAAY;AACf,aAAO;AAAA,IACT;AACA,WAAO,GAAG,GAAG;AAAA,EACf,MAAM,GAAG,KAAK,MAAMA,SAAQ,GAAG,IAAI,GAAG,GAAG,MAAM;AAAA,EAC/C,GAAG,SAAS;AACZ,MAAI,OAAO,GAAG,EAAE,KAAK,SAAS,aAAa,EAAE,KAAK,aAAa,EAAE,EAAE,UAAU,MAAM,EAAE,KAAK,MAAM,KAAK,EAAE,KAAK,MAAM,EAAE,KAAK,KAAK,OAAK,EAAE,KAAK,QAAQ,IAAI,EAAE,KAAK,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,KAAK,QAAM,EAAE,KAAK,EAAE,MAAM,CAAC,EAAE,KAAK,MAAM,GAAG,aAAa,MAAM,MAAM,IAAI,EAAE,KAAK,eAAe,OAAK,EAAE,KAAK,QAAQ,IAAI,UAAU,KAAK,EAAE,KAAK,OAAO;AACnU,QAAMG,QAAO,IAAI,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO,EAAE,KAAK,QAAQ,MAAM,EAAE,KAAK,kBAAkB,GAAG,EAAE,UAAU,OAAO,EAAE,KAAK,MAAM,KAAK,EAAE,KAAK,GAAG,EAAE,KAAK,SAAS,MAAM,EAAE,MAAM,kBAAkB,UAAU;AAC5M,QAAM,YAAY,MAAM,aAAa;AACrC,MAAI,cAAc,YAAY;AAC5B,UAAM,WAAWA,MAAK,OAAO,gBAAgB,EAAE,KAAK,MAAM,QAAM,EAAE,MAAM,IAAI,KAAK,iBAAiB,GAAG,EAAE,EAAE,KAAK,iBAAiB,gBAAgB,EAAE,KAAK,MAAM,OAAK,EAAE,OAAO,EAAE,EAAE,KAAK,MAAM,OAAK,EAAE,OAAO,EAAE;AACzM,aAAS,OAAO,MAAM,EAAE,KAAK,UAAU,IAAI,EAAE,KAAK,cAAc,OAAK,YAAY,EAAE,OAAO,EAAE,CAAC;AAC7F,aAAS,OAAO,MAAM,EAAE,KAAK,UAAU,MAAM,EAAE,KAAK,cAAc,OAAK,YAAY,EAAE,OAAO,EAAE,CAAC;AAAA,EACjG;AACA,MAAI;AACJ,UAAQ,WAAW;AAAA,IACjB,KAAK;AACH,iBAA0B,OAAO,OAAK,EAAE,KAAK,UAAU;AACvD;AAAA,IACF,KAAK;AACH,iBAA0B,OAAO,OAAK,YAAY,EAAE,OAAO,EAAE,GAAG,UAAU;AAC1E;AAAA,IACF,KAAK;AACH,iBAA0B,OAAO,OAAK,YAAY,EAAE,OAAO,EAAE,GAAG,UAAU;AAC1E;AAAA,IACF;AACE,iBAAW;AAAA,EACf;AACA,EAAAA,MAAK,OAAO,MAAM,EAAE,KAAK,KAAK,6BAAuB,CAAC,EAAE,KAAK,UAAU,QAAQ,EAAE,KAAK,gBAAgB,OAAK,KAAK,IAAI,GAAG,EAAE,KAAK,CAAC;AAC/H,oBAAkB,QAAQ,KAAK,GAAG,WAAW;AAC/C,GAAG,MAAM;AACT,IAAI,yBAAyB;AAAA,EAC3B;AACF;AAGA,IAAI,wBAAuC,OAAO,UAAQ;AACxD,QAAM,cAAc,KAAK,WAAW,4BAA4B,EAAE,EAAE,WAAW,cAAc,IAAI,EAAE,KAAK;AACxG,SAAO;AACT,GAAG,uBAAuB;AAG1B,IAAI,YAA2B,OAAO,aAAW;AAAA,qBAC5B,QAAQ,UAAU;AAAA,QAC/B,WAAW;AACnB,IAAI,iBAAiB;AAGrB,IAAI,gBAAgB,eAAe,MAAM,KAAK,cAAc;AAC5D,eAAe,QAAQ,UAAQ,cAAc,sBAAsB,IAAI,CAAC;AACxE,IAAI,UAAU;AAAA,EACZ,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,IAAI;AAAA,EACJ,UAAU;AACZ;", "names": ["a", "x", "left", "a", "right", "center", "x", "max", "value", "index", "min", "value", "index", "array", "sum", "value", "index", "x", "a", "nodes", "link", "sort", "links", "x", "kx", "ky", "y", "value", "dy", "sourceLinks", "targetLinks", "x", "y", "constant_default", "x", "constant", "epsilon", "pi", "tau", "x", "y", "a", "slice", "x", "y", "link", "slice", "s", "constant_default", "pi", "tau", "x", "y", "x", "y", "x", "y", "x", "y", "beta", "point", "x", "y", "custom", "tension", "x", "y", "point", "custom", "tension", "x", "y", "point", "custom", "tension", "point", "x", "y", "x2", "y2", "epsilon", "a", "custom", "alpha", "x", "y", "point", "custom", "alpha", "x", "y", "point", "custom", "alpha", "x", "y", "x", "point", "y", "x", "y", "a", "x", "y", "k", "value", "a", "index", "link"]}