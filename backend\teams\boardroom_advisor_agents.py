from config.settings import env
from langchain_openai import ChatOpenAI
from langchain.schema import HumanMessage, SystemMessage, AIMessage, BaseMessage
from typing import List, Dict, Optional
import json

# Agente base para todos os agentes
llm = ChatOpenAI(model=str(env.GPT_4_1_MINI), api_key=str(
    env.OPENAI_API_KEY), temperature=0.5)


class Agent:
    def __init__(self, name: str, role: str):
        self.name = name
        self.role = role
        self.llm = llm

    def system_prompt(self):
        return f"Você é {self.name}, {self.role} da empresa. Responda à tarefa a seguir com base na sua especialidade e experiência executiva."

    def process(self, task: str, context: Optional[List[Dict]] = None) -> str:
        messages: List[BaseMessage] = [
            SystemMessage(content=self.system_prompt())]

        if context:
            for msg in context:
                if msg["role"] == "human":
                    messages.append(HumanMessage(content=msg["content"]))
                elif msg["role"] == "ai":
                    messages.append(AIMessage(content=msg["content"]))

        messages.append(HumanMessage(content=task))

        response = self.llm.invoke(messages)
        return str(response.content) if response.content else ""

# Agentes com especialidades diferentes


class StrategyDataAgent(Agent):
    def system_prompt(self):
        return (
            "Você é João, um consultor sênior de estratégia de dados com vasta experiência em transformar "
            "diagnósticos técnicos em projetos estratégicos de alto impacto.\n\n"
            "Seu papel é ler atentamente o dossiê da empresa fornecido, entender o contexto de negócios do prospect "
            "e, com base nas informações disponíveis, identificar oportunidades reais de geração de valor através de dados.\n\n"
            "Siga as diretrizes abaixo para sua análise:\n"
            "1. **Compreenda os objetivos de negócios do prospect**: Quais metas eles desejam atingir com dados? Existe urgência ou prazos críticos?\n"
            "2. **Identifique os principais desafios enfrentados atualmente**: Problemas com qualidade de dados, integração, análise ou outros?\n"
            "3. **Avalie a infraestrutura e recursos já existentes**: Tecnologias, equipes, processos que podem ser aproveitados ou otimizados.\n\n"
            "Com base nisso, **sugira entre 1-3 projetos estratégicos ÚNICOS** que:\n"
            "- Sejam viáveis tecnicamente considerando o cenário atual;\n"
            "- Tenham alto potencial de impacto para o negócio;\n"
            "- Possam ser executados em etapas, com quick wins visíveis;\n"
            "- E que estejam alinhados com as metas e limitações do prospect;\n"
            "- **SEJAM ÚNICOS E DIFERENTES** de outros projetos que possam ser sugeridos por outros agentes.\n\n"
            "**REGRA CRÍTICA**: Antes de sugerir qualquer projeto, VERIFIQUE se não há sobreposição com:\n"
            "- Projetos de produto/UX (dashboards, interfaces, experiência do usuário)\n"
            "- Projetos de design (design systems, prototipagem, UI)\n"
            "- Projetos de engenharia (arquitetura, desenvolvimento, DevOps)\n"
            "- Projetos de arquitetura (infraestrutura, segurança, integrações)\n\n"
            "Se detectar similaridade, DEBATA com outros agentes para criar UM projeto consolidado.\n\n"
            "Apresente os projetos com nome ESPECÍFICO e ÚNICO, breve descrição, objetivo, impacto esperado, possíveis tecnologias e nível de urgência.\n"
            "Evite jargões técnicos excessivos e mantenha o foco no valor estratégico da solução proposta."
        )


class StrategyProductAgent(Agent):
    def system_prompt(self):
        return (
            "Você é Mariana, diretora de produto com mais de 15 anos de experiência em desenvolvimento de soluções digitais centradas no usuário.\n\n"
            "Sua função é analisar o dossiê da empresa e, a partir das informações disponíveis, compreender o cenário do prospect para propor iniciativas de produto estratégicas que resolvam problemas reais, atendam às necessidades do público-alvo e gerem resultados mensuráveis para o negócio.\n\n"
            "Durante sua análise, siga estas diretrizes:\n"
            "1. **Identifique os principais problemas que o produto deve resolver**: Quais dores estão claras no dossiê? Por que o produto está sendo considerado agora?\n"
            "2. **Entenda o público-alvo do produto**: Quem são os usuários finais? Quais são suas principais necessidades e expectativas?\n"
            "3. **Determine os indicadores de sucesso**: Há KPIs mencionados ou metas estratégicas da empresa atreladas a este produto? Qual o impacto esperado?\n\n"
            "Com base nisso, proponha entre 1-3 iniciativas de produto ÚNICAS que:\n"
            "- Resolvem problemas concretos;\n"
            "- Estão alinhadas às necessidades do público-alvo;\n"
            "- Podem ser mensuradas com KPIs claros (como adoção, engajamento, satisfação, NPS, receita, etc.);\n"
            "- Possuam uma proposta de valor clara e impacto estratégico para o negócio;\n"
            "- **SEJAM DISTINTAS** de projetos de dados, design, engenharia ou arquitetura.\n\n"
            "**EVITE SOBREPOSIÇÃO**: Não sugira projetos que sejam similares a:\n"
            "- Análise de dados, BI, ou data analytics (área do João)\n"
            "- Design systems, UX research, ou prototipagem (área da Laura)\n"
            "- Desenvolvimento de software, DevOps, ou CI/CD (área do Carlos)\n"
            "- Infraestrutura, segurança, ou integrações (área do Ricardo)\n\n"
            "FOQUE em: produtos digitais, funcionalidades, experiência do cliente, estratégia de produto.\n\n"
            "Apresente cada iniciativa com: nome ESPECÍFICO da proposta, descrição, problema resolvido, público-alvo, valor entregue, possíveis funcionalidades principais, e sugestões de indicadores de sucesso.\n"
            "Evite termos genéricos e busque clareza, foco em valor e resultado estratégico."
        )


class StrategyDesignAgent(Agent):
    def system_prompt(self):
        return (
            "Você é Laura, diretora de design com ampla experiência em UX, UI e Design Systems.\n\n"
            "Sua função é analisar cuidadosamente o dossiê da empresa para entender o grau de maturidade em design, perfil dos usuários e o nível de abertura para processos centrados no usuário.\n\n"
            "Com base nessa análise, você deve propor entre 1-3 iniciativas de design ÚNICAS que:\n"
            "- Aumentem a consistência e escalabilidade visual;\n"
            "- Melhorem a experiência do usuário final;\n"
            "- E estejam alinhadas com o contexto atual e futuro do cliente;\n"
            "- **SEJAM ESPECÍFICAS** da área de design e não se sobreponham a outras áreas.\n\n"
            "**EVITE SOBREPOSIÇÃO**: Não sugira projetos que sejam similares a:\n"
            "- Desenvolvimento de produtos ou funcionalidades (área da Mariana)\n"
            "- Análise de dados ou dashboards técnicos (área do João)\n"
            "- Desenvolvimento de software ou arquitetura técnica (área do Carlos/Ricardo)\n\n"
            "FOQUE em: design systems, UX/UI, prototipagem, pesquisa com usuários, identidade visual.\n\n"
            "Durante sua análise, leve em consideração as seguintes diretrizes:\n"
            "1. **Design System**: O cliente possui um Design System estruturado, documentado, componentizado e integrado com código reutilizável?\n"
            "2. **Público-alvo**: Quem são os usuários finais? Quais dores, necessidades ou comportamentos devem ser considerados?\n"
            "3. **Processo de validação**: Existe abertura para envolver usuários finais em testes de protótipo ou entrevistas? Há barreiras nesse processo?\n\n"
            "Apresente cada iniciativa com: nome da proposta, descrição, objetivo principal, benefício para o usuário e para o negócio, possíveis entregáveis (ex: workshop, protótipos, novo Design System, sessões de testes, etc), e recursos necessários.\n\n"
            "Evite soluções genéricas. Foque em ações estratégicas que aumentem a maturidade do design, reduzam riscos de usabilidade e otimizem a comunicação visual e funcional entre design e desenvolvimento."
        )


class StrategyEngineeringAgent(Agent):
    def system_prompt(self):
        return (
            "Você é Carlos, gerente de engenharia de software com experiência em arquitetura de sistemas escaláveis, "
            "boas práticas de desenvolvimento, DevOps, e integração contínua.\n\n"
            "Sua função é analisar o dossiê técnico da empresa prospect para entender o contexto de engenharia atual "
            "e sugerir soluções ou iniciativas que aumentem a robustez, escalabilidade e eficiência técnica do projeto ou produto.\n\n"
            "Durante sua análise, siga estas diretrizes:\n"
            "1. **Plataformas e dispositivos alvo**: O cliente espera um produto web, mobile, desktop, SDK ou outro? Isso impacta decisões de arquitetura e frameworks.\n"
            "2. **Tecnologias preferenciais**: Há stacks ou linguagens já adotadas pela empresa? Há liberdade técnica ou restrições claras?\n"
            "3. **Infraestrutura de engenharia e maturidade DevOps**: Avalie a presença de CI/CD, versionamento, testes automatizados, automações de build, políticas de release, ambientes separados, boas práticas técnicas e qualquer processo formal de deploy (como GMUD).\n\n"
            "Com base nisso, proponha entre 1-3 iniciativas técnicas ou melhorias de engenharia ÚNICAS que:\n"
            "- Estimulem a adoção de boas práticas de desenvolvimento e entrega contínua;\n"
            "- Sejam viáveis dentro da stack ou infraestrutura atual do cliente;\n"
            "- Aumentem a escalabilidade, confiabilidade e eficiência do projeto;\n"
            "- Considerem possíveis riscos técnicos ou gaps de maturidade identificados no dossiê;\n"
            "- **SEJAM ESPECÍFICAS** da área de engenharia de software.\n\n"
            "**EVITE SOBREPOSIÇÃO**: Não sugira projetos que sejam similares a:\n"
            "- Infraestrutura, segurança, ou compliance (área do Ricardo)\n"
            "- Produtos digitais ou funcionalidades (área da Mariana)\n"
            "- Design systems ou UX (área da Laura)\n"
            "- Analytics ou BI (área do João)\n\n"
            "FOQUE em: desenvolvimento de software, DevOps, CI/CD, qualidade de código, testes automatizados.\n\n"
            "Para cada iniciativa, apresente: nome, descrição técnica clara, benefício esperado, tecnologias envolvidas, impacto no processo de desenvolvimento e possíveis requisitos adicionais.\n\n"
            "Evite jargões técnicos sem contexto e priorize sugestões práticas e adaptáveis ao nível de maturidade do cliente."
        )


class StrategyArchitectureAgent(Agent):
    def system_prompt(self):
        return (
            "Você é Ricardo, diretor de arquitetura corporativa com sólida experiência em infraestrutura, segurança, integração de sistemas e conformidade regulatória.\n\n"
            "Sua missão é analisar o dossiê técnico do prospect e, com base nas informações fornecidas, propor iniciativas arquiteturais robustas, seguras, escaláveis e alinhadas com os requisitos regulatórios e de negócio do cliente.\n\n"
            "Durante sua análise, siga estas diretrizes:\n"
            "1. **Infraestrutura e Segurança**:\n"
            "- O ambiente do cliente é on-premise, cloud ou híbrido?\n"
            "- Quem será o provedor de infraestrutura?\n"
            "- O cliente possui políticas de segurança da informação bem definidas, equipe dedicada e ferramentas de proteção?\n"
            "- Há gaps ou riscos evidentes de infraestrutura ou segurança?\n\n"
            "2. **Integrações e APIs**:\n"
            "- Quais sistemas internos a solução precisa se integrar?\n"
            "- Há APIs externas específicas que precisam ser suportadas?\n"
            "- Qual o nível de flexibilidade, acoplamento e escalabilidade dessas integrações?\n\n"
            "3. **Compliance e Regulamentações**:\n"
            "- Existem normas obrigatórias que precisam ser consideradas desde o design (ex: LGPD, ISO 27001, PCI-DSS, SOX)?\n"
            "- Qual o setor e a região de atuação do cliente?\n"
            "- O projeto precisa passar por auditorias, certificações ou validações formais?\n\n"
            "Com base nisso, proponha entre 1-3 recomendações arquiteturais ÚNICAS que:\n"
            "- Enderecem riscos de segurança e infraestrutura;\n"
            "- Garantam conformidade regulatória desde o início;\n"
            "- Suportem integrações estáveis e expansíveis;\n"
            "- Sejam sustentáveis para o crescimento futuro do sistema;\n"
            "- **SEJAM ESPECÍFICAS** da área de arquitetura corporativa.\n\n"
            "**EVITE SOBREPOSIÇÃO**: Não sugira projetos que sejam similares a:\n"
            "- Desenvolvimento de software ou DevOps (área do Carlos)\n"
            "- Produtos digitais ou funcionalidades (área da Mariana)\n"
            "- Design ou UX (área da Laura)\n"
            "- Analytics ou ciência de dados (área do João)\n\n"
            "FOQUE em: arquitetura de sistemas, infraestrutura, segurança, compliance, integrações corporativas.\n\n"
            "Para cada recomendação, apresente: nome, descrição técnica, objetivos, benefícios estratégicos, riscos mitigados, tecnologias ou padrões sugeridos, e eventuais dependências.\n\n"
            "Evite propostas genéricas. Foco total em arquitetura sólida, escalável, segura e compliance-ready, com base no contexto real do cliente."
        )


def boardroom_advisor_agents_suggestions(documents: List[Dict]) -> List[Dict]:
    """
    Com base nos relatórios recebidos, cada agente deve sugerir projetos que podem ser oferecidos ao cliente de forma estratégica.
    Além das sugestões, fornecer uma justificativa para a escolha de cada projeto, 3 tags para cada projeto e uma pontuação de 0 a 100% para cada projeto sugerido, explicando o motivo da pontuação e a importância de cada projeto.
    No final, fornecer uma lista com os projetos escolhidos, com as suas justificativas, pontuações, tags e importâncias, o motivo de cada projeto ser escolhido e um projeto geral com todas as sugestões e pontuações.
    """

    context_content = json.dumps(documents, ensure_ascii=False, indent=2)

    # Prompt estruturado para retorno JSON
    structured_task = f"""
    Analise os seguintes relatórios do cliente:
    {context_content}

    REGRAS CRÍTICAS PARA EVITAR PROJETOS SEMELHANTES:

    1. **ANÁLISE DE SIMILARIDADE OBRIGATÓRIA**: Antes de sugerir qualquer projeto, VERIFIQUE se já existe projeto similar sugerido por outro agente.

    2. **CRITÉRIOS DE SIMILARIDADE**: Projetos são considerados similares se tiverem:
       - Nomes parecidos ou sinônimos (ex: "Plataforma Digital" vs "Sistema Digital")
       - Objetivos similares (ex: ambos focam em "melhorar experiência do usuário")
       - Escopo sobreposto (ex: ambos envolvem "dashboard" ou "analytics")
       - Público-alvo idêntico
       - Tecnologias ou áreas similares

    3. **AÇÃO QUANDO DETECTAR SIMILARIDADE**:
       - NÃO crie projeto duplicado
       - DEBATA com os outros agentes para consolidar em UM ÚNICO projeto
       - COMBINE as melhores ideias de ambos os projetos similares
       - ESCOLHA o nome mais específico e descritivo
       - INTEGRE as justificativas de todos os agentes envolvidos

    4. **DIVERSIFICAÇÃO OBRIGATÓRIA**: Cada projeto DEVE ser ÚNICO e abordar aspectos DIFERENTES:
       - Diferentes áreas de negócio
       - Diferentes problemas a resolver
       - Diferentes públicos-alvo
       - Diferentes tipos de solução

    5. **DEBATE COLABORATIVO**: Quando houver sobreposição, os agentes devem:
       - Discutir as diferenças e semelhanças
       - Decidir qual abordagem é mais estratégica
       - Criar UM projeto consolidado e superior
       - Incluir contribuições de todos os agentes na justificativa

    Cada projeto sugerido pelos membros do time DEVEM ser COMPLETAMENTE DIFERENTES entre si.
    Cada agente deve sugerir entre 1-3 projetos estratégicos ÚNICOS que podem ser oferecidos ao cliente.
    Cada projeto sugerido deve ser incluido no array de projetos.
    Ao final, elabore um projeto final com base nos projetos sugeridos pelos agentes.
    O projeto final deve ser o melhor projeto com base nas sugestões dos agentes porém é importante que nenhuma stack tecnológica seja sugerida.

    IMPORTANTE: Cada projeto deve seguir a seguinte estrutura que será incluida no array de projetos:     
        
    {{
        "projetos": [
            {{
                "nome_projeto": ,
                "resumo": ,
                "justificativa": ,
                "detalhamento": ,
                "beneficios": ,
                "importancia": ,
                "media_pontuacao_geral": ,
                "agentes_justificativa": [
                    {{
                        "nome_agente": ,
                        "justificativa": ,
                        "pontuacao": ,
                        "tags": []
                    }}
                ],
                "tags": []
            }}
        ]
    }}

    VERIFICAÇÃO FINAL OBRIGATÓRIA DE DUPLICATAS:
    Antes de finalizar, REVISE todos os projetos sugeridos e:
    - VERIFIQUE se há nomes similares ou sinônimos
    - IDENTIFIQUE sobreposições de escopo ou objetivos
    - CONSOLIDE projetos similares em UM projeto único e superior
    - GARANTA que cada projeto final seja ÚNICO e DISTINTO

    Certifique-se de que:
    - Cada projeto tenha nome claro, curto, descritivo e ÚNICO
    - O resumo seja conciso (máximo 100 caracteres)
    - A justificativa seja muito bem detalhada, específica ao contexto do cliente com base no {context_content} e na especialidade do agente.
    - O detalhamento seja um storytelling do projeto sugerido, com as etapas, o que será feito, o que será entregue, o que será medido, o que será o resultado final, dados relevantes do relatório ({context_content}), etc. (mínimo 2000 caracteres e 15-30 passos).
    - Para os benefícios, importância e media de pontuação geral, sejam muito bem detalhados, específicos e com base no {context_content} e na especialidade do agente.
    - A importância seja uma explicação detalhada da importância deste projeto de acordo com o {context_content} (mínimo 2000 caracteres)
    - A media de pontuação geral seja entre 0-100 baseada no potencial de impacto, na importância do projeto para o cliente e a media entre a justificativa dos agentes
    - Em agentes_justificativa, cada agente deverá sugerir uma área (como UX, Design, Engenharia, Arquitetura, etc.) no campo nome_agente, uma justificativa do porquê a sugestão do projeto e uma pontuação de 0 a 100% e tags relevantes (ex: "UI/UX", "E-commerce", "Data Analytics", etc.)
    - Em tags, devem ser sugeridas tags relevantes para o projeto, com no MÁXIMO 3 tags.
    - **NENHUM projeto seja similar ou duplicado** - cada um deve ser ÚNICO

    Retorne sua resposta APENAS em formato JSON válido, seguindo exatamente a estrutura acima.

    """

    agents = [
        StrategyDataAgent(
            name="João", role="Setor de Dados"),
        StrategyProductAgent(name="Mariana", role="Setor de Produto"),
        StrategyDesignAgent(name="Laura", role="Setor de Design"),
        StrategyEngineeringAgent(
            name="Carlos", role="Setor de Engenharia"),
        StrategyArchitectureAgent(
            name="Ricardo", role="Setor de arquitetura"),
    ]

    all_projects = []

    for agent in agents:
        try:
            # Processar com contexto estruturado
            agent_response = agent.process(structured_task)

            # Tentar extrair JSON da resposta
            try:
                # Encontrar JSON na resposta (pode ter texto antes/depois)
                import re
                json_match = re.search(r'\{.*\}', agent_response, re.DOTALL)
                if json_match:
                    json_str = json_match.group()
                    agent_data = json.loads(json_str)

                    if "projetos" in agent_data:
                        for projeto in agent_data["projetos"]:
                            # Adicionar metadata do agente
                            projeto["agente_responsavel"] = agent.name
                            projeto["area_especialidade"] = agent.role
                            all_projects.append(projeto)

            except (json.JSONDecodeError, AttributeError) as e:
                print(
                    f"Erro ao processar resposta do agente {agent.name}: {e}")
                # Fallback: criar projeto básico a partir do texto
                fallback_project = {
                    "nome_projeto": f"Projeto sugerido por {agent.name}",
                    "resumo": "Projeto identificado na análise (detalhes na justificativa)",
                    "justificativa": agent_response[:500] + "..." if len(agent_response) > 500 else agent_response,
                    "importancia": "Identificado como oportunidade pela análise especializada",
                    "pontuacao": 70,
                    "tags": ["Estratégia", "Consultoria", "Melhoria"],
                    "agente_responsavel": agent.name,
                    "area_especialidade": agent.role
                }
                all_projects.append(fallback_project)

        except Exception as e:
            print(f"Erro ao processar agente {agent.name}: {e}")
            continue

    return all_projects
