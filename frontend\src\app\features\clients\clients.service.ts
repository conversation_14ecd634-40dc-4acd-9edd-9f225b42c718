import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { IClient } from './client.interface';

@Injectable({ providedIn: 'root' })
export class ClientsService {
	private apiUrl = 'http://localhost:8040/clients'; // Ajuste se usar proxy

	constructor(private http: HttpClient) {}

	public createClient(client: Partial<IClient>): Observable<IClient> {
		return this.http.post<IClient>(this.apiUrl, client);
	}

	public getClients(): Observable<Array<IClient>> {
		return this.http.get<Array<IClient>>(this.apiUrl);
	}

	public downloadReport(clientId: string): Observable<Blob> {
		return this.http.get(`${this.apiUrl}/${clientId}/report`, { responseType: 'blob' });
	}

	// 🚀 NOVO: Download do relatório executivo AI
	public downloadReportAI(clientId: string, regenerate: boolean = false): Observable<Blob> {
		const params: Record<string, string> = regenerate ? { regenerate: 'true' } : {};
		return this.http.get(`${this.apiUrl}/${clientId}/report`, {
			responseType: 'blob',
			headers: {
				Accept: 'application/pdf',
			},
			params,
		});
	}

	// 🚀 NOVO: Gerar relatório completo (Markdown + PDF)
	public generateCompleteReport(clientId: string): Observable<Blob> {
		return this.http.post(
			`${this.apiUrl}/${clientId}/generate-complete-report`,
			{},
			{
				responseType: 'blob',
				headers: {
					Accept: 'application/pdf',
				},
			},
		);
	}

	// 🚀 NOVO: Buscar relatório em Markdown
	public getMarkdownReport(clientId: string): Observable<any> {
		return this.http.get(`${this.apiUrl}/${clientId}/markdown`);
	}

	// 🚀 NOVO: Gerar relatório em Markdown
	public generateMarkdownReport(clientId: string): Observable<any> {
		return this.http.post(`${this.apiUrl}/${clientId}/generate-markdown`, {});
	}

	// 🚀 NOVO: Converter Markdown para PDF
	public convertMarkdownToPdf(clientId: string, markdownContent?: string): Observable<Blob> {
		const body = markdownContent ? { markdown_content: markdownContent } : {};

		return this.http.post(`${this.apiUrl}/${clientId}/markdown-to-pdf`, body, {
			responseType: 'blob',
			headers: {
				Accept: 'application/pdf',
			},
		});
	}

	// Verificar se PDF está disponível
	public isPdfReportAvailable(client: IClient): boolean {
		return !!(client.pdf_report_id || client.pdf_report_available);
	}

	public startFullProcessing(clientId: string): Observable<Record<string, unknown>> {
		return this.http.post<Record<string, unknown>>(`${this.apiUrl}/${clientId}/full-processing`, {});
	}

	// 🚀 NOVO: Recriar projetos para o cliente
	public recreateProjects(clientId: string): Observable<any> {
		return this.http.post(`http://localhost:8040/api/boardroom/recreate-projects/${clientId}`, {});
	}
}
