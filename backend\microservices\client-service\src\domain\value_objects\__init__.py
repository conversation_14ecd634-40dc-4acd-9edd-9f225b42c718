"""Value Objects do domínio de clientes.

Value Objects são objetos imutáveis que representam conceitos do domínio
sem identidade própria. São definidos apenas por seus atributos.
"""

from .client_context import ClientContext
from .client_status import ClientStatus
from .client_url import ClientUrl
from .analysis_result import AnalysisResult

__all__ = [
    "ClientContext",
    "ClientStatus",
    "ClientUrl",
    "AnalysisResult",
]
