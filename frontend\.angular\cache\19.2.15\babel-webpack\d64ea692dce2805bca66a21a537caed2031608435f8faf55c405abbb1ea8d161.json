{"ast": null, "code": "import { isPlatformBrowser } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, inject, NgZone, effect, Directive, NgModule } from '@angular/core';\nimport { removeClass, getHeight, getWidth, getOuterWidth, getOuterHeight, getOffset, addClass, remove } from '@primeuix/utils';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { BaseStyle } from 'primeng/base';\nconst theme = ({\n  dt\n}) => `\n/* For PrimeNG */\n.p-ripple {\n    overflow: hidden;\n    position: relative;\n}\n\n.p-ink {\n    display: block;\n    position: absolute;\n    background: ${dt('ripple.background')};\n    border-radius: 100%;\n    transform: scale(0);\n}\n\n.p-ink-active {\n    animation: ripple 0.4s linear;\n}\n\n.p-ripple-disabled .p-ink {\n    display: none !important;\n}\n\n@keyframes ripple {\n    100% {\n        opacity: 0;\n        transform: scale(2.5);\n    }\n}\n`;\nconst classes = {\n  root: 'p-ink'\n};\nclass RippleStyle extends BaseStyle {\n  name = 'ripple';\n  theme = theme;\n  classes = classes;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵRippleStyle_BaseFactory;\n    return function RippleStyle_Factory(__ngFactoryType__) {\n      return (ɵRippleStyle_BaseFactory || (ɵRippleStyle_BaseFactory = i0.ɵɵgetInheritedFactory(RippleStyle)))(__ngFactoryType__ || RippleStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: RippleStyle,\n    factory: RippleStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RippleStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n *\n * Ripple directive adds ripple effect to the host element.\n *\n * [Live Demo](https://www.primeng.org/ripple)\n *\n * @module ripplestyle\n *\n */\nvar RippleClasses;\n(function (RippleClasses) {\n  /**\n   * Class name of the root element\n   */\n  RippleClasses[\"root\"] = \"p-ink\";\n})(RippleClasses || (RippleClasses = {}));\n\n/**\n * Ripple directive adds ripple effect to the host element.\n * @group Components\n */\nclass Ripple extends BaseComponent {\n  zone = inject(NgZone);\n  _componentStyle = inject(RippleStyle);\n  animationListener;\n  mouseDownListener;\n  timeout;\n  constructor() {\n    super();\n    effect(() => {\n      if (isPlatformBrowser(this.platformId)) {\n        if (this.config.ripple()) {\n          this.zone.runOutsideAngular(() => {\n            this.create();\n            this.mouseDownListener = this.renderer.listen(this.el.nativeElement, 'mousedown', this.onMouseDown.bind(this));\n          });\n        } else {\n          this.remove();\n        }\n      }\n    });\n  }\n  ngAfterViewInit() {\n    super.ngAfterViewInit();\n  }\n  onMouseDown(event) {\n    let ink = this.getInk();\n    if (!ink || this.document.defaultView?.getComputedStyle(ink, null).display === 'none') {\n      return;\n    }\n    removeClass(ink, 'p-ink-active');\n    if (!getHeight(ink) && !getWidth(ink)) {\n      let d = Math.max(getOuterWidth(this.el.nativeElement), getOuterHeight(this.el.nativeElement));\n      ink.style.height = d + 'px';\n      ink.style.width = d + 'px';\n    }\n    let offset = getOffset(this.el.nativeElement);\n    let x = event.pageX - offset.left + this.document.body.scrollTop - getWidth(ink) / 2;\n    let y = event.pageY - offset.top + this.document.body.scrollLeft - getHeight(ink) / 2;\n    this.renderer.setStyle(ink, 'top', y + 'px');\n    this.renderer.setStyle(ink, 'left', x + 'px');\n    addClass(ink, 'p-ink-active');\n    this.timeout = setTimeout(() => {\n      let ink = this.getInk();\n      if (ink) {\n        removeClass(ink, 'p-ink-active');\n      }\n    }, 401);\n  }\n  getInk() {\n    const children = this.el.nativeElement.children;\n    for (let i = 0; i < children.length; i++) {\n      if (typeof children[i].className === 'string' && children[i].className.indexOf('p-ink') !== -1) {\n        return children[i];\n      }\n    }\n    return null;\n  }\n  resetInk() {\n    let ink = this.getInk();\n    if (ink) {\n      removeClass(ink, 'p-ink-active');\n    }\n  }\n  onAnimationEnd(event) {\n    if (this.timeout) {\n      clearTimeout(this.timeout);\n    }\n    removeClass(event.currentTarget, 'p-ink-active');\n  }\n  create() {\n    let ink = this.renderer.createElement('span');\n    this.renderer.addClass(ink, 'p-ink');\n    this.renderer.appendChild(this.el.nativeElement, ink);\n    this.renderer.setAttribute(ink, 'aria-hidden', 'true');\n    this.renderer.setAttribute(ink, 'role', 'presentation');\n    if (!this.animationListener) {\n      this.animationListener = this.renderer.listen(ink, 'animationend', this.onAnimationEnd.bind(this));\n    }\n  }\n  remove() {\n    let ink = this.getInk();\n    if (ink) {\n      this.mouseDownListener && this.mouseDownListener();\n      this.animationListener && this.animationListener();\n      this.mouseDownListener = null;\n      this.animationListener = null;\n      remove(ink);\n    }\n  }\n  ngOnDestroy() {\n    if (this.config && this.config.ripple()) {\n      this.remove();\n    }\n    super.ngOnDestroy();\n  }\n  static ɵfac = function Ripple_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Ripple)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: Ripple,\n    selectors: [[\"\", \"pRipple\", \"\"]],\n    hostAttrs: [1, \"p-ripple\"],\n    features: [i0.ɵɵProvidersFeature([RippleStyle]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Ripple, [{\n    type: Directive,\n    args: [{\n      selector: '[pRipple]',\n      host: {\n        class: 'p-ripple'\n      },\n      standalone: true,\n      providers: [RippleStyle]\n    }]\n  }], () => [], null);\n})();\nclass RippleModule {\n  static ɵfac = function RippleModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || RippleModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: RippleModule,\n    imports: [Ripple],\n    exports: [Ripple]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RippleModule, [{\n    type: NgModule,\n    args: [{\n      imports: [Ripple],\n      exports: [Ripple]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Ripple, RippleClasses, RippleModule, RippleStyle };", "map": {"version": 3, "names": ["isPlatformBrowser", "i0", "Injectable", "inject", "NgZone", "effect", "Directive", "NgModule", "removeClass", "getHeight", "getWidth", "getOuterWidth", "getOuterHeight", "getOffset", "addClass", "remove", "BaseComponent", "BaseStyle", "theme", "dt", "classes", "root", "RippleStyle", "name", "ɵfac", "ɵRippleStyle_BaseFactory", "RippleStyle_Factory", "__ngFactoryType__", "ɵɵgetInheritedFactory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "RippleClasses", "<PERSON><PERSON><PERSON>", "zone", "_componentStyle", "animationListener", "mouseDownListener", "timeout", "constructor", "platformId", "config", "ripple", "runOutsideAngular", "create", "renderer", "listen", "el", "nativeElement", "onMouseDown", "bind", "ngAfterViewInit", "event", "ink", "getInk", "document", "defaultView", "getComputedStyle", "display", "d", "Math", "max", "style", "height", "width", "offset", "x", "pageX", "left", "body", "scrollTop", "y", "pageY", "top", "scrollLeft", "setStyle", "setTimeout", "children", "i", "length", "className", "indexOf", "resetInk", "onAnimationEnd", "clearTimeout", "currentTarget", "createElement", "append<PERSON><PERSON><PERSON>", "setAttribute", "ngOnDestroy", "Ripple_Factory", "ɵdir", "ɵɵdefineDirective", "selectors", "hostAttrs", "features", "ɵɵProvidersFeature", "ɵɵInheritDefinitionFeature", "args", "selector", "host", "class", "standalone", "providers", "RippleModule", "RippleModule_Factory", "ɵmod", "ɵɵdefineNgModule", "imports", "exports", "ɵinj", "ɵɵdefineInjector"], "sources": ["C:/projetos/scope-ai/frontend/node_modules/primeng/fesm2022/primeng-ripple.mjs"], "sourcesContent": ["import { isPlatformBrowser } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, inject, NgZone, effect, Directive, NgModule } from '@angular/core';\nimport { removeClass, getHeight, getWidth, getOuterWidth, getOuterHeight, getOffset, addClass, remove } from '@primeuix/utils';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { BaseStyle } from 'primeng/base';\n\nconst theme = ({ dt }) => `\n/* For PrimeNG */\n.p-ripple {\n    overflow: hidden;\n    position: relative;\n}\n\n.p-ink {\n    display: block;\n    position: absolute;\n    background: ${dt('ripple.background')};\n    border-radius: 100%;\n    transform: scale(0);\n}\n\n.p-ink-active {\n    animation: ripple 0.4s linear;\n}\n\n.p-ripple-disabled .p-ink {\n    display: none !important;\n}\n\n@keyframes ripple {\n    100% {\n        opacity: 0;\n        transform: scale(2.5);\n    }\n}\n`;\nconst classes = {\n    root: 'p-ink'\n};\nclass RippleStyle extends BaseStyle {\n    name = 'ripple';\n    theme = theme;\n    classes = classes;\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: RippleStyle, deps: null, target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: RippleStyle });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: RippleStyle, decorators: [{\n            type: Injectable\n        }] });\n/**\n *\n * Ripple directive adds ripple effect to the host element.\n *\n * [Live Demo](https://www.primeng.org/ripple)\n *\n * @module ripplestyle\n *\n */\nvar RippleClasses;\n(function (RippleClasses) {\n    /**\n     * Class name of the root element\n     */\n    RippleClasses[\"root\"] = \"p-ink\";\n})(RippleClasses || (RippleClasses = {}));\n\n/**\n * Ripple directive adds ripple effect to the host element.\n * @group Components\n */\nclass Ripple extends BaseComponent {\n    zone = inject(NgZone);\n    _componentStyle = inject(RippleStyle);\n    animationListener;\n    mouseDownListener;\n    timeout;\n    constructor() {\n        super();\n        effect(() => {\n            if (isPlatformBrowser(this.platformId)) {\n                if (this.config.ripple()) {\n                    this.zone.runOutsideAngular(() => {\n                        this.create();\n                        this.mouseDownListener = this.renderer.listen(this.el.nativeElement, 'mousedown', this.onMouseDown.bind(this));\n                    });\n                }\n                else {\n                    this.remove();\n                }\n            }\n        });\n    }\n    ngAfterViewInit() {\n        super.ngAfterViewInit();\n    }\n    onMouseDown(event) {\n        let ink = this.getInk();\n        if (!ink || this.document.defaultView?.getComputedStyle(ink, null).display === 'none') {\n            return;\n        }\n        removeClass(ink, 'p-ink-active');\n        if (!getHeight(ink) && !getWidth(ink)) {\n            let d = Math.max(getOuterWidth(this.el.nativeElement), getOuterHeight(this.el.nativeElement));\n            ink.style.height = d + 'px';\n            ink.style.width = d + 'px';\n        }\n        let offset = getOffset(this.el.nativeElement);\n        let x = event.pageX - offset.left + this.document.body.scrollTop - getWidth(ink) / 2;\n        let y = event.pageY - offset.top + this.document.body.scrollLeft - getHeight(ink) / 2;\n        this.renderer.setStyle(ink, 'top', y + 'px');\n        this.renderer.setStyle(ink, 'left', x + 'px');\n        addClass(ink, 'p-ink-active');\n        this.timeout = setTimeout(() => {\n            let ink = this.getInk();\n            if (ink) {\n                removeClass(ink, 'p-ink-active');\n            }\n        }, 401);\n    }\n    getInk() {\n        const children = this.el.nativeElement.children;\n        for (let i = 0; i < children.length; i++) {\n            if (typeof children[i].className === 'string' && children[i].className.indexOf('p-ink') !== -1) {\n                return children[i];\n            }\n        }\n        return null;\n    }\n    resetInk() {\n        let ink = this.getInk();\n        if (ink) {\n            removeClass(ink, 'p-ink-active');\n        }\n    }\n    onAnimationEnd(event) {\n        if (this.timeout) {\n            clearTimeout(this.timeout);\n        }\n        removeClass(event.currentTarget, 'p-ink-active');\n    }\n    create() {\n        let ink = this.renderer.createElement('span');\n        this.renderer.addClass(ink, 'p-ink');\n        this.renderer.appendChild(this.el.nativeElement, ink);\n        this.renderer.setAttribute(ink, 'aria-hidden', 'true');\n        this.renderer.setAttribute(ink, 'role', 'presentation');\n        if (!this.animationListener) {\n            this.animationListener = this.renderer.listen(ink, 'animationend', this.onAnimationEnd.bind(this));\n        }\n    }\n    remove() {\n        let ink = this.getInk();\n        if (ink) {\n            this.mouseDownListener && this.mouseDownListener();\n            this.animationListener && this.animationListener();\n            this.mouseDownListener = null;\n            this.animationListener = null;\n            remove(ink);\n        }\n    }\n    ngOnDestroy() {\n        if (this.config && this.config.ripple()) {\n            this.remove();\n        }\n        super.ngOnDestroy();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: Ripple, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.10\", type: Ripple, isStandalone: true, selector: \"[pRipple]\", host: { classAttribute: \"p-ripple\" }, providers: [RippleStyle], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: Ripple, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[pRipple]',\n                    host: {\n                        class: 'p-ripple'\n                    },\n                    standalone: true,\n                    providers: [RippleStyle]\n                }]\n        }], ctorParameters: () => [] });\nclass RippleModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: RippleModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.10\", ngImport: i0, type: RippleModule, imports: [Ripple], exports: [Ripple] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: RippleModule });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.10\", ngImport: i0, type: RippleModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [Ripple],\n                    exports: [Ripple]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Ripple, RippleClasses, RippleModule, RippleStyle };\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,iBAAiB;AACnD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AACvF,SAASC,WAAW,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,cAAc,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,iBAAiB;AAC9H,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,SAAS,QAAQ,cAAc;AAExC,MAAMC,KAAK,GAAGA,CAAC;EAAEC;AAAG,CAAC,KAAK;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkBA,EAAE,CAAC,mBAAmB,CAAC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,MAAMC,OAAO,GAAG;EACZC,IAAI,EAAE;AACV,CAAC;AACD,MAAMC,WAAW,SAASL,SAAS,CAAC;EAChCM,IAAI,GAAG,QAAQ;EACfL,KAAK,GAAGA,KAAK;EACbE,OAAO,GAAGA,OAAO;EACjB,OAAOI,IAAI;IAAA,IAAAC,wBAAA;IAAA,gBAAAC,oBAAAC,iBAAA;MAAA,QAAAF,wBAAA,KAAAA,wBAAA,GAA+ExB,EAAE,CAAA2B,qBAAA,CAAQN,WAAW,IAAAK,iBAAA,IAAXL,WAAW;IAAA;EAAA;EAC/G,OAAOO,KAAK,kBAD8E5B,EAAE,CAAA6B,kBAAA;IAAAC,KAAA,EACYT,WAAW;IAAAU,OAAA,EAAXV,WAAW,CAAAE;EAAA;AACvH;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KAH8FhC,EAAE,CAAAiC,iBAAA,CAGJZ,WAAW,EAAc,CAAC;IAC1Ga,IAAI,EAAEjC;EACV,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIkC,aAAa;AACjB,CAAC,UAAUA,aAAa,EAAE;EACtB;AACJ;AACA;EACIA,aAAa,CAAC,MAAM,CAAC,GAAG,OAAO;AACnC,CAAC,EAAEA,aAAa,KAAKA,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEzC;AACA;AACA;AACA;AACA,MAAMC,MAAM,SAASrB,aAAa,CAAC;EAC/BsB,IAAI,GAAGnC,MAAM,CAACC,MAAM,CAAC;EACrBmC,eAAe,GAAGpC,MAAM,CAACmB,WAAW,CAAC;EACrCkB,iBAAiB;EACjBC,iBAAiB;EACjBC,OAAO;EACPC,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACPtC,MAAM,CAAC,MAAM;MACT,IAAIL,iBAAiB,CAAC,IAAI,CAAC4C,UAAU,CAAC,EAAE;QACpC,IAAI,IAAI,CAACC,MAAM,CAACC,MAAM,CAAC,CAAC,EAAE;UACtB,IAAI,CAACR,IAAI,CAACS,iBAAiB,CAAC,MAAM;YAC9B,IAAI,CAACC,MAAM,CAAC,CAAC;YACb,IAAI,CAACP,iBAAiB,GAAG,IAAI,CAACQ,QAAQ,CAACC,MAAM,CAAC,IAAI,CAACC,EAAE,CAACC,aAAa,EAAE,WAAW,EAAE,IAAI,CAACC,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;UAClH,CAAC,CAAC;QACN,CAAC,MACI;UACD,IAAI,CAACvC,MAAM,CAAC,CAAC;QACjB;MACJ;IACJ,CAAC,CAAC;EACN;EACAwC,eAAeA,CAAA,EAAG;IACd,KAAK,CAACA,eAAe,CAAC,CAAC;EAC3B;EACAF,WAAWA,CAACG,KAAK,EAAE;IACf,IAAIC,GAAG,GAAG,IAAI,CAACC,MAAM,CAAC,CAAC;IACvB,IAAI,CAACD,GAAG,IAAI,IAAI,CAACE,QAAQ,CAACC,WAAW,EAAEC,gBAAgB,CAACJ,GAAG,EAAE,IAAI,CAAC,CAACK,OAAO,KAAK,MAAM,EAAE;MACnF;IACJ;IACAtD,WAAW,CAACiD,GAAG,EAAE,cAAc,CAAC;IAChC,IAAI,CAAChD,SAAS,CAACgD,GAAG,CAAC,IAAI,CAAC/C,QAAQ,CAAC+C,GAAG,CAAC,EAAE;MACnC,IAAIM,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACtD,aAAa,CAAC,IAAI,CAACwC,EAAE,CAACC,aAAa,CAAC,EAAExC,cAAc,CAAC,IAAI,CAACuC,EAAE,CAACC,aAAa,CAAC,CAAC;MAC7FK,GAAG,CAACS,KAAK,CAACC,MAAM,GAAGJ,CAAC,GAAG,IAAI;MAC3BN,GAAG,CAACS,KAAK,CAACE,KAAK,GAAGL,CAAC,GAAG,IAAI;IAC9B;IACA,IAAIM,MAAM,GAAGxD,SAAS,CAAC,IAAI,CAACsC,EAAE,CAACC,aAAa,CAAC;IAC7C,IAAIkB,CAAC,GAAGd,KAAK,CAACe,KAAK,GAAGF,MAAM,CAACG,IAAI,GAAG,IAAI,CAACb,QAAQ,CAACc,IAAI,CAACC,SAAS,GAAGhE,QAAQ,CAAC+C,GAAG,CAAC,GAAG,CAAC;IACpF,IAAIkB,CAAC,GAAGnB,KAAK,CAACoB,KAAK,GAAGP,MAAM,CAACQ,GAAG,GAAG,IAAI,CAAClB,QAAQ,CAACc,IAAI,CAACK,UAAU,GAAGrE,SAAS,CAACgD,GAAG,CAAC,GAAG,CAAC;IACrF,IAAI,CAACR,QAAQ,CAAC8B,QAAQ,CAACtB,GAAG,EAAE,KAAK,EAAEkB,CAAC,GAAG,IAAI,CAAC;IAC5C,IAAI,CAAC1B,QAAQ,CAAC8B,QAAQ,CAACtB,GAAG,EAAE,MAAM,EAAEa,CAAC,GAAG,IAAI,CAAC;IAC7CxD,QAAQ,CAAC2C,GAAG,EAAE,cAAc,CAAC;IAC7B,IAAI,CAACf,OAAO,GAAGsC,UAAU,CAAC,MAAM;MAC5B,IAAIvB,GAAG,GAAG,IAAI,CAACC,MAAM,CAAC,CAAC;MACvB,IAAID,GAAG,EAAE;QACLjD,WAAW,CAACiD,GAAG,EAAE,cAAc,CAAC;MACpC;IACJ,CAAC,EAAE,GAAG,CAAC;EACX;EACAC,MAAMA,CAAA,EAAG;IACL,MAAMuB,QAAQ,GAAG,IAAI,CAAC9B,EAAE,CAACC,aAAa,CAAC6B,QAAQ;IAC/C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,QAAQ,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;MACtC,IAAI,OAAOD,QAAQ,CAACC,CAAC,CAAC,CAACE,SAAS,KAAK,QAAQ,IAAIH,QAAQ,CAACC,CAAC,CAAC,CAACE,SAAS,CAACC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;QAC5F,OAAOJ,QAAQ,CAACC,CAAC,CAAC;MACtB;IACJ;IACA,OAAO,IAAI;EACf;EACAI,QAAQA,CAAA,EAAG;IACP,IAAI7B,GAAG,GAAG,IAAI,CAACC,MAAM,CAAC,CAAC;IACvB,IAAID,GAAG,EAAE;MACLjD,WAAW,CAACiD,GAAG,EAAE,cAAc,CAAC;IACpC;EACJ;EACA8B,cAAcA,CAAC/B,KAAK,EAAE;IAClB,IAAI,IAAI,CAACd,OAAO,EAAE;MACd8C,YAAY,CAAC,IAAI,CAAC9C,OAAO,CAAC;IAC9B;IACAlC,WAAW,CAACgD,KAAK,CAACiC,aAAa,EAAE,cAAc,CAAC;EACpD;EACAzC,MAAMA,CAAA,EAAG;IACL,IAAIS,GAAG,GAAG,IAAI,CAACR,QAAQ,CAACyC,aAAa,CAAC,MAAM,CAAC;IAC7C,IAAI,CAACzC,QAAQ,CAACnC,QAAQ,CAAC2C,GAAG,EAAE,OAAO,CAAC;IACpC,IAAI,CAACR,QAAQ,CAAC0C,WAAW,CAAC,IAAI,CAACxC,EAAE,CAACC,aAAa,EAAEK,GAAG,CAAC;IACrD,IAAI,CAACR,QAAQ,CAAC2C,YAAY,CAACnC,GAAG,EAAE,aAAa,EAAE,MAAM,CAAC;IACtD,IAAI,CAACR,QAAQ,CAAC2C,YAAY,CAACnC,GAAG,EAAE,MAAM,EAAE,cAAc,CAAC;IACvD,IAAI,CAAC,IAAI,CAACjB,iBAAiB,EAAE;MACzB,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAACS,QAAQ,CAACC,MAAM,CAACO,GAAG,EAAE,cAAc,EAAE,IAAI,CAAC8B,cAAc,CAACjC,IAAI,CAAC,IAAI,CAAC,CAAC;IACtG;EACJ;EACAvC,MAAMA,CAAA,EAAG;IACL,IAAI0C,GAAG,GAAG,IAAI,CAACC,MAAM,CAAC,CAAC;IACvB,IAAID,GAAG,EAAE;MACL,IAAI,CAAChB,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,CAAC,CAAC;MAClD,IAAI,CAACD,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,CAAC,CAAC;MAClD,IAAI,CAACC,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAACD,iBAAiB,GAAG,IAAI;MAC7BzB,MAAM,CAAC0C,GAAG,CAAC;IACf;EACJ;EACAoC,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAChD,MAAM,IAAI,IAAI,CAACA,MAAM,CAACC,MAAM,CAAC,CAAC,EAAE;MACrC,IAAI,CAAC/B,MAAM,CAAC,CAAC;IACjB;IACA,KAAK,CAAC8E,WAAW,CAAC,CAAC;EACvB;EACA,OAAOrE,IAAI,YAAAsE,eAAAnE,iBAAA;IAAA,YAAAA,iBAAA,IAAyFU,MAAM;EAAA;EAC1G,OAAO0D,IAAI,kBA5H+E9F,EAAE,CAAA+F,iBAAA;IAAA7D,IAAA,EA4HJE,MAAM;IAAA4D,SAAA;IAAAC,SAAA;IAAAC,QAAA,GA5HJlG,EAAE,CAAAmG,kBAAA,CA4HgG,CAAC9E,WAAW,CAAC,GA5H/GrB,EAAE,CAAAoG,0BAAA;EAAA;AA6HhG;AACA;EAAA,QAAApE,SAAA,oBAAAA,SAAA,KA9H8FhC,EAAE,CAAAiC,iBAAA,CA8HJG,MAAM,EAAc,CAAC;IACrGF,IAAI,EAAE7B,SAAS;IACfgG,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,WAAW;MACrBC,IAAI,EAAE;QACFC,KAAK,EAAE;MACX,CAAC;MACDC,UAAU,EAAE,IAAI;MAChBC,SAAS,EAAE,CAACrF,WAAW;IAC3B,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC,MAAMsF,YAAY,CAAC;EACf,OAAOpF,IAAI,YAAAqF,qBAAAlF,iBAAA;IAAA,YAAAA,iBAAA,IAAyFiF,YAAY;EAAA;EAChH,OAAOE,IAAI,kBA3I+E7G,EAAE,CAAA8G,gBAAA;IAAA5E,IAAA,EA2ISyE,YAAY;IAAAI,OAAA,GAAY3E,MAAM;IAAA4E,OAAA,GAAa5E,MAAM;EAAA;EACtJ,OAAO6E,IAAI,kBA5I+EjH,EAAE,CAAAkH,gBAAA;AA6IhG;AACA;EAAA,QAAAlF,SAAA,oBAAAA,SAAA,KA9I8FhC,EAAE,CAAAiC,iBAAA,CA8IJ0E,YAAY,EAAc,CAAC;IAC3GzE,IAAI,EAAE5B,QAAQ;IACd+F,IAAI,EAAE,CAAC;MACCU,OAAO,EAAE,CAAC3E,MAAM,CAAC;MACjB4E,OAAO,EAAE,CAAC5E,MAAM;IACpB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASA,MAAM,EAAED,aAAa,EAAEwE,YAAY,EAAEtF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}