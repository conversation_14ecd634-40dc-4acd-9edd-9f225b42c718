"""
Endpoints de IA para melhoramento de descrições e chat sobre escopo de projetos

Utiliza OpenAI GPT-4o-mini para:
- Melhoramento de descrições de projetos
- Chat conversacional para refinamento de escopo
"""
from typing import Dict, List
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from openai import OpenAI, AsyncOpenAI
from config.settings import env

router = APIRouter(prefix="/ai", tags=["AI"])

# Cliente OpenAI
client = AsyncOpenAI(api_key=env.OPENAI_API_KEY)
AI_MODEL = env.GPT_4_1_MINI or "gpt-4o-mini"


class EnhanceDescriptionRequest(BaseModel):
    description: str


class EnhanceDescriptionResponse(BaseModel):
    enhanced_description: str


class ChatProjectScopeRequest(BaseModel):
    project_name: str
    description: str
    chat_history: List[Dict[str, str]]
    user_message: str


class ChatProjectScopeResponse(BaseModel):
    ai_response: str
    scope_understood: bool
    refined_scope: str | None = None
    confidence_score: int = 0


@router.post("/enhance-description", response_model=EnhanceDescriptionResponse)
async def enhance_description(request: EnhanceDescriptionRequest):
    """Melhora e enriquece a descrição do projeto usando IA"""
    try:
        # Prompt para melhorar a descrição
        prompt = f"""
        Você é um especialista em escrever descrições de projetos de software.
        
        Melhore e enriqueça a seguinte descrição de projeto, mantendo a essência mas:
        - Adicionando mais detalhes técnicos relevantes
        - Esclarecendo objetivos de negócio
        - Incluindo benefícios esperados
        - Estruturando melhor o texto
        - Mantendo um tom profissional
        
        Descrição original:
        {request.description}
        
        Retorne APENAS a descrição melhorada, sem explicações adicionais.
        """

        response = await client.chat.completions.create(
            model=AI_MODEL,
            messages=[
                {"role": "system", "content": "Você é um especialista em escrever descrições de projetos."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=1024,
            temperature=0.7
        )
        enhanced = (response.choices[0].message.content or "").strip()

        return EnhanceDescriptionResponse(enhanced_description=enhanced)

    except Exception as e:
        print(f"Erro ao melhorar descrição: {str(e)}")
        raise HTTPException(
            status_code=500, detail="Erro ao processar descrição com IA")


@router.post("/chat-project-scope", response_model=ChatProjectScopeResponse)
async def chat_project_scope(request: ChatProjectScopeRequest):
    """Conversa com o usuário para entender melhor o escopo do projeto"""
    try:
        # Construir histórico de conversa
        chat_context = f"""
        Você é um analista de projetos experiente conversando com um CLIENTE LEIGO EM TECNOLOGIA sobre um novo projeto.
        SEMPRE use linguagem simples e acessível, evitando jargões técnicos.
        
        Nome do projeto: {request.project_name}
        Descrição atual: {request.description}
        
        Histórico da conversa:
        """

        for msg in request.chat_history:
            role = "Usuário" if msg["sender"] == "user" else "Você"
            chat_context += f"\n{role}: {msg['text']}"

        chat_context += f"\nUsuário: {request.user_message}"

        # Primeiro, calcular o nível de confiança sobre o entendimento do projeto
        confidence_prompt = f"""
        {chat_context}
        
        Com base na conversa acima, avalie seu nível de compreensão sobre o projeto.
        Considere os seguintes aspectos e seus pesos:
        - Objetivo principal do projeto (30%)
        - Funcionalidades principais (30%)
        - Público-alvo (20%)
        - Aspectos técnicos/integrações (20%)
        
        Calcule uma pontuação de 0 a 100 representando o quanto você compreende o projeto.
        Responda APENAS com um número entre 0 e 100.
        """

        confidence_response = await client.chat.completions.create(
            model=AI_MODEL,
            messages=[
                {"role": "system", "content": "Você é um analisador de confiança. Responda apenas com um número de 0 a 100."},
                {"role": "user", "content": confidence_prompt}
            ],
            max_tokens=10,
            temperature=0.1
        )
        confidence_response_content = (
            confidence_response.choices[0].message.content or "0").strip()

        # Extrair pontuação de confiança
        try:
            confidence_text = (confidence_response_content or "").strip()
            confidence_score = int(
                ''.join(filter(str.isdigit, confidence_text)))
            # Garantir entre 0-100
            confidence_score = max(0, min(100, confidence_score))
        except:
            confidence_score = 0

        # Lógica baseada no nível de confiança
        if confidence_score >= 80:
            # Alta confiança - confirmar entendimento
            prompt = f"""
            {chat_context}
            
            Com base em nossa conversa, acredito que compreendi bem o escopo do seu projeto. 
            
            Faça um resumo claro e organizado do que você entendeu sobre:
            - O que o sistema vai fazer (objetivo principal)
            - Principais funcionalidades que os usuários poderão usar
            - Quem vai usar o sistema (público-alvo)
            - Como o sistema vai funcionar de forma geral
            
            Use linguagem simples e evite termos técnicos. O cliente é leigo em tecnologia.
            
            Depois pergunte: "Está tudo certo com este entendimento do seu projeto? Posso prosseguir com estas informações?"
            
            Seja claro e objetivo no resumo, usando linguagem acessível.
            """
        else:
            # Confiança baixa - fazer pergunta contextualizada
            prompt = f"""
            {chat_context}
            
            Analise a conversa e identifique qual aspecto ESPECÍFICO do projeto ainda precisa ser esclarecido.
            
            NÃO faça perguntas genéricas ou técnicas.
            
            Baseie-se no contexto já fornecido para fazer uma pergunta ESPECÍFICA e RELEVANTE em linguagem simples ou sugerir idéias.
            
            Evite perguntas genéricas ou técnicas.
            Evite termos como: API, integração, backend, frontend, database, framework, etc.
            """

        response = await client.chat.completions.create(
            model=AI_MODEL,
            messages=[
                {"role": "system", "content": "Você é um analista de projetos conversando com um cliente leigo em tecnologia. Use linguagem simples e acessível."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=1500,
            temperature=0.7
        )
        ai_message = (
            response.choices[0].message.content or "").strip()

        # Verificar se o usuário confirmou satisfação (apenas quando confiança >= 80%)
        scope_understood = False
        user_confirmed = any(phrase in request.user_message.lower() for phrase in [
            "sim", "yes", "está bom", "perfeito", "pode prosseguir",
            "está ótimo", "correto", "isso mesmo", "exato", "confirmo"
        ])

        if confidence_score >= 80 and user_confirmed:
            scope_understood = True

        # Se compreendeu e usuário confirmou, gerar escopo refinado
        refined_scope = None
        if scope_understood:
            refine_prompt = f"""
            Com base em toda a conversa abaixo, escreva uma descrição completa e bem estruturada do projeto:
            
            {chat_context}
            
            A descrição deve ser profissional e incluir:
            - Objetivo principal e valor de negócio
            - Funcionalidades principais detalhadas
            - Público-alvo e personas
            - Tecnologias e integrações necessárias
            - Benefícios e resultados esperados
            
            Escreva em 1-2 parágrafos bem estruturados, sendo claro e objetivo.
            """

            refine_response = await client.chat.completions.create(
                model=AI_MODEL,
                messages=[
                    {"role": "system", "content": "Você é um especialista em escrever descrições profissionais de projetos."},
                    {"role": "user", "content": refine_prompt}
                ],
                max_tokens=1024,
                temperature=0.6
            )
            refined_scope = (
                refine_response.choices[0].message.content or "").strip()

        return ChatProjectScopeResponse(
            ai_response=ai_message,
            scope_understood=scope_understood,
            refined_scope=refined_scope,
            confidence_score=confidence_score
        )

    except Exception as e:
        print(f"Erro no chat com IA: {str(e)}")
        raise HTTPException(
            status_code=500, detail="Erro ao processar conversa com IA")
