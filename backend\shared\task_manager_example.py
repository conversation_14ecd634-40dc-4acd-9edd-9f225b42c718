"""
Exemplos de Uso do Task Manager - ScopeAI
Este arquivo demonstra como usar o novo sistema de background tasks.
"""

import asyncio
import logging
from datetime import datetime, UTC

# Importar o TaskManager
from .task_manager import create_background_task, TaskPriority, task_manager, get_task_status

logger = logging.getLogger(__name__)


# ===================================================================
# EXEMPLO 1: Task Simples de Análise
# ===================================================================

async def exemplo_analise_simples(client_id: str, client_name: str):
    """Exemplo de task simples de análise"""
    print(f"🔍 Analisando cliente: {client_name}")
    
    # Simular trabalho
    await asyncio.sleep(3)
    
    # Retornar resultado
    return {
        "client_id": client_id,
        "analysis_completed": True,
        "insights": ["Insight 1", "Insight 2", "Insight 3"],
        "timestamp": datetime.now(UTC).isoformat()
    }


async def executar_exemplo_1():
    """Executa exemplo de task simples"""
    print("\n=== EXEMPLO 1: Task Simples ===")
    
    # Criar task
    task_id = await create_background_task(
        "simple_analysis",
        exemplo_analise_simples,
        TaskPriority.MEDIUM,
        "12345",
        "Empresa Exemplo",
        5,
        "12345",  # client_id
        "Empresa Exemplo"  # client_name
    )
    
    print(f"✅ Task criada: {task_id}")
    
    # Monitorar progresso
    while True:
        task_info = await get_task_status(task_id)
        if task_info:
            print(f"📊 Status: {task_info.status.value} | Progresso: {task_info.progress:.1f}%")
            
            if task_info.is_completed:
                if task_info.status.value == "completed":
                    print(f"✅ Task concluída em {task_info.elapsed_time:.1f}s")
                else:
                    print(f"❌ Task falhou: {task_info.error_message}")
                break
        
        await asyncio.sleep(1)


# ===================================================================
# EXEMPLO 2: Task com Prioridade Crítica
# ===================================================================

async def exemplo_operacao_critica(client_id: str, urgency_level: str):
    """Exemplo de operação crítica com alta prioridade"""
    print(f"🚨 OPERAÇÃO CRÍTICA - Urgência: {urgency_level}")
    
    # Simular processamento crítico
    for i in range(5):
        print(f"📊 Processando parte {i+1}/5...")
        await asyncio.sleep(0.5)
    
    return {
        "operation": "critical_processing",
        "status": "completed",
        "urgency_handled": urgency_level,
        "priority_queue_bypassed": True
    }


async def executar_exemplo_2():
    """Executa exemplo de task crítica"""
    print("\n=== EXEMPLO 2: Task Crítica ===")
    
    # Criar task crítica (será executada primeiro)
    task_id = await create_background_task(
        "critical_operation",
        exemplo_operacao_critica,
        TaskPriority.CRITICAL,  # Prioridade máxima
        "67890",
        "Cliente Crítico",
        3,
        "67890",  # client_id
        "HIGH"  # urgency_level
    )
    
    print(f"🚨 Task crítica criada: {task_id}")
    
    # Task crítica executa imediatamente devido à prioridade
    await asyncio.sleep(4)
    
    task_info = await get_task_status(task_id)
    if task_info and task_info.is_completed:
        print(f"✅ Task crítica concluída em {task_info.elapsed_time:.1f}s")


# ===================================================================
# EXEMPLO 3: Múltiplas Tasks Concorrentes
# ===================================================================

async def exemplo_processamento_lote(batch_id: str, items_count: int):
    """Simula processamento de lote"""
    print(f"📦 Processando lote {batch_id} com {items_count} itens")
    
    for i in range(items_count):
        print(f"📊 Item {i+1}/{items_count} processado")
        await asyncio.sleep(0.3)
    
    return {
        "batch_id": batch_id,
        "items_processed": items_count,
        "status": "completed"
    }


async def executar_exemplo_3():
    """Executa múltiplas tasks simultâneas"""
    print("\n=== EXEMPLO 3: Tasks Concorrentes ===")
    
    # Criar múltiplas tasks
    task_ids = []
    
    for i in range(5):
        task_id = await create_background_task(
            "batch_processing",
            exemplo_processamento_lote,
            TaskPriority.MEDIUM,
            f"client_{i}",
            f"Cliente {i}",
            2,
            f"batch_{i}",  # batch_id
            3  # items_count
        )
        task_ids.append(task_id)
        print(f"📋 Task {i+1} criada: {task_id}")
    
    print(f"🚀 {len(task_ids)} tasks criadas. Aguardando conclusão...")
    
    # Monitorar todas as tasks
    completed_count = 0
    while completed_count < len(task_ids):
        completed_count = 0
        
        for task_id in task_ids:
            task_info = await get_task_status(task_id)
            if task_info and task_info.is_completed:
                completed_count += 1
        
        print(f"📊 Progresso: {completed_count}/{len(task_ids)} tasks concluídas")
        await asyncio.sleep(1)
    
    print("✅ Todas as tasks foram concluídas!")


# ===================================================================
# EXEMPLO 4: Monitoramento de Sistema
# ===================================================================

async def exemplo_estatisticas_sistema():
    """Demonstra como obter estatísticas do sistema"""
    print("\n=== EXEMPLO 4: Estatísticas do Sistema ===")
    
    # Obter estatísticas
    stats = task_manager.get_stats()
    
    print("📊 ESTATÍSTICAS DO SISTEMA:")
    print(f"   📋 Total de tasks: {stats.get('total_tasks', 0)}")
    print(f"   ✅ Tasks concluídas: {stats.get('completed_tasks', 0)}")
    print(f"   ❌ Tasks com falha: {stats.get('failed_tasks', 0)}")
    print(f"   🚫 Tasks canceladas: {stats.get('cancelled_tasks', 0)}")
    print(f"   🏃 Tasks em execução: {stats.get('running_tasks', 0)}")
    print(f"   🎯 Máximo concorrente: {stats.get('max_concurrent', 10)}")
    
    resource_usage = stats.get('resource_usage', {})
    if resource_usage:
        print("💾 USO DE RECURSOS:")
        for resource_type, count in resource_usage.items():
            print(f"   - {resource_type}: {count} execuções")


# ===================================================================
# EXEMPLO 5: Tasks de Clientes Reais (Integração)
# ===================================================================

async def exemplo_relatorio_completo_real(client_id: str, client_name: str, site: str):
    """Exemplo integrando com sistema real de relatórios"""
    print(f"📄 Gerando relatório completo para {client_name}")
    print(f"🌐 Site: {site}")
    
    # Simular fases do relatório real
    fases = [
        "Coletando dados básicos",
        "Análise de presença digital",
        "Pesquisa de mercado",
        "Análise competitiva",
        "Geração do dossiê",
        "Criação do PDF"
    ]
    
    for i, fase in enumerate(fases):
        print(f"📊 Fase {i+1}/{len(fases)}: {fase}")
        await asyncio.sleep(1)  # Simular trabalho real
    
    return {
        "client_id": client_id,
        "report_type": "complete_analysis",
        "status": "completed",
        "pages": 15,
        "insights_count": 8,
        "generated_at": datetime.now(UTC).isoformat()
    }


async def executar_exemplo_5():
    """Executa exemplo de integração com sistema real"""
    print("\n=== EXEMPLO 5: Integração com Sistema Real ===")
    
    # Usar prioridade HIGH para relatórios importantes
    task_id = await create_background_task(
        "complete_report",  # Mesmo tipo usado no sistema real
        exemplo_relatorio_completo_real,
        TaskPriority.HIGH,
        "real_client_123",
        "Rethink Software",
        600,  # 10 minutos (como no sistema real)
        "real_client_123",  # client_id
        "Rethink Software",  # client_name
        "https://www.rethink.com.br"  # site
    )
    
    print(f"📄 Relatório completo iniciado: {task_id}")
    print("💡 Use GET /tasks/{task_id} para monitorar via API")
    
    # Simular monitoramento via API
    await asyncio.sleep(8)
    
    task_info = await get_task_status(task_id)
    if task_info:
        print(f"📊 Status final: {task_info.status.value}")
        print(f"⏱️ Tempo total: {task_info.elapsed_time:.1f}s")


# ===================================================================
# FUNÇÃO PRINCIPAL - EXECUTA TODOS OS EXEMPLOS
# ===================================================================

async def executar_todos_os_exemplos():
    """Executa todos os exemplos sequencialmente"""
    print("🚀 EXEMPLOS DO TASK MANAGER - ScopeAI")
    print("=" * 50)
    
    # Executar exemplos
    await executar_exemplo_1()
    await executar_exemplo_2()
    await executar_exemplo_3()
    await exemplo_estatisticas_sistema()
    await executar_exemplo_5()
    
    print("\n" + "=" * 50)
    print("✅ TODOS OS EXEMPLOS CONCLUÍDOS!")
    print("💡 Para usar em produção:")
    print("   - Importe: from shared.task_manager import create_background_task")
    print("   - Use: task_id = await create_background_task(...)")
    print("   - Monitore: GET /tasks/{task_id}")


if __name__ == "__main__":
    # Executar exemplos diretamente
    asyncio.run(executar_todos_os_exemplos()) 