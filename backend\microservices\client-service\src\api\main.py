"""
Client Service API

Entry point for the client microservice using FastAPI.
Reutiliza as configurações do backend principal.
"""

from contextlib import asynccontextmanager
from typing import AsyncGenerator

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from loguru import logger
import uvicorn

# Importar do path relativo dentro do backend
from ..config import settings


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator:
    """
    Manage application lifecycle.

    Setup resources on startup and cleanup on shutdown.
    """
    # Startup
    logger.info(
        f"Starting {settings.service_name} v{settings.service_version}")
    logger.info(f"Environment: {settings.environment}")
    logger.info(f"Debug mode: {settings.debug}")

    # TODO: Initialize database connections
    # TODO: Initialize cache connections
    # TODO: Initialize message broker connections

    yield

    # Shutdown
    logger.info("Shutting down Client Service")
    # TODO: Close database connections
    # TODO: Close cache connections
    # TODO: Close message broker connections


def create_application() -> FastAPI:
    """
    Create and configure FastAPI application.

    Returns:
        FastAPI: Configured application instance
    """
    app = FastAPI(
        title=settings.api_title,
        description=settings.api_description,
        version=settings.service_version,
        openapi_url=f"{settings.api_v2_prefix}/openapi.json",
        docs_url=f"{settings.api_v2_prefix}/docs",
        redoc_url=f"{settings.api_v2_prefix}/redoc",
        lifespan=lifespan,
    )

    # Security middleware
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["*"] if settings.debug else ["localhost", "127.0.0.1"],
    )

    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins_list,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Request ID middleware
    @app.middleware("http")
    async def add_request_id(request, call_next):
        """Add unique request ID to each request."""
        import uuid
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id

        response = await call_next(request)
        response.headers["X-Request-ID"] = request_id
        return response

    # Logging middleware
    @app.middleware("http")
    async def log_requests(request, call_next):
        """Log all requests with timing information."""
        import time
        start_time = time.time()

        response = await call_next(request)

        process_time = time.time() - start_time
        logger.info(
            f"{request.method} {request.url.path} - "
            f"Status: {response.status_code} - "
            f"Duration: {process_time:.3f}s"
        )

        return response

    # Health check endpoints
    @app.get("/health", tags=["health"])
    async def health_check():
        """Basic health check endpoint."""
        return {
            "status": "healthy",
            "service": settings.service_name,
            "version": settings.service_version,
            "environment": settings.environment,
        }

    @app.get("/health/ready", tags=["health"])
    async def readiness_check():
        """Readiness check endpoint."""
        # TODO: Check database connection
        # TODO: Check cache connection
        # TODO: Check external services

        return {
            "status": "ready",
            "checks": {
                "database": "healthy",
                "cache": "healthy",
                "external_services": "healthy",
            }
        }

    # Include API routes
    # TODO: Import and include routers
    # from .routes import clients_router
    # app.include_router(clients_router, prefix=settings.api_v2_prefix)

    return app


# Create application instance
app = create_application()


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_config={
            "version": 1,
            "disable_existing_loggers": False,
            "formatters": {
                "default": {
                    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                },
            },
            "handlers": {
                "default": {
                    "formatter": "default",
                    "class": "logging.StreamHandler",
                    "stream": "ext://sys.stdout",
                },
            },
            "root": {
                "level": settings.log_level,
                "handlers": ["default"],
            },
        },
    )
