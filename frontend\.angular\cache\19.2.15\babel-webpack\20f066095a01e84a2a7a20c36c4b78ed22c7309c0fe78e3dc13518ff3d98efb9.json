{"ast": null, "code": "import { getTestBed } from '@angular/core/testing';\nimport { BrowserDynamicTestingModule, platformBrowserDynamicTesting } from '@angular/platform-browser-dynamic/testing';\n\n// Initialize the Angular testing environment.\ngetTestBed().initTestEnvironment(BrowserDynamicTestingModule, platformBrowserDynamicTesting(), {\n  errorOnUnknownElements: true,\n  errorOnUnknownProperties: true\n});", "map": {"version": 3, "names": ["getTestBed", "BrowserDynamicTestingModule", "platformBrowserDynamicTesting", "initTestEnvironment", "errorOnUnknownElements", "errorOnUnknownProperties"], "sources": ["data:text/javascript;base64,CiAgaW1wb3J0IHsgZ2V0VGVzdEJlZCB9IGZyb20gJ0Bhbmd1bGFyL2NvcmUvdGVzdGluZyc7CiAgaW1wb3J0IHsKICAgIEJyb3dzZXJEeW5hbWljVGVzdGluZ01vZHVsZSwKICAgIHBsYXRmb3JtQnJvd3NlckR5bmFtaWNUZXN0aW5nLAogICB9IGZyb20gJ0Bhbmd1bGFyL3BsYXRmb3JtLWJyb3dzZXItZHluYW1pYy90ZXN0aW5nJzsKCiAgLy8gSW5pdGlhbGl6ZSB0aGUgQW5ndWxhciB0ZXN0aW5nIGVudmlyb25tZW50LgogIGdldFRlc3RCZWQoKS5pbml0VGVzdEVudmlyb25tZW50KEJyb3dzZXJEeW5hbWljVGVzdGluZ01vZHVsZSwgcGxhdGZvcm1Ccm93c2VyRHluYW1pY1Rlc3RpbmcoKSwgewogICAgZXJyb3JPblVua25vd25FbGVtZW50czogdHJ1ZSwKICAgIGVycm9yT25Vbmtub3duUHJvcGVydGllczogdHJ1ZQogIH0pOwo="], "sourcesContent": ["\n  import { getTestBed } from '@angular/core/testing';\n  import {\n    BrowserDynamicTestingModule,\n    platformBrowserDynamicTesting,\n   } from '@angular/platform-browser-dynamic/testing';\n\n  // Initialize the Angular testing environment.\n  getTestBed().initTestEnvironment(BrowserDynamicTestingModule, platformBrowserDynamicTesting(), {\n    errorOnUnknownElements: true,\n    errorOnUnknownProperties: true\n  });\n"], "mappings": "AACE,SAASA,UAAU,QAAQ,uBAAuB;AAClD,SACEC,2BAA2B,EAC3BC,6BAA6B,QACvB,2CAA2C;;AAEnD;AACAF,UAAU,CAAC,CAAC,CAACG,mBAAmB,CAACF,2BAA2B,EAAEC,6BAA6B,CAAC,CAAC,EAAE;EAC7FE,sBAAsB,EAAE,IAAI;EAC5BC,wBAAwB,EAAE;AAC5B,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}