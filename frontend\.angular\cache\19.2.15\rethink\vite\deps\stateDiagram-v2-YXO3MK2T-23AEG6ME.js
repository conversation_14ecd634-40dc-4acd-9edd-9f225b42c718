import {
  StateDB,
  stateDiagram_default,
  stateRenderer_v3_unified_default,
  styles_default
} from "./chunk-NWX2BD7W.js";
import "./chunk-F75XBXFM.js";
import "./chunk-NOVPKVBE.js";
import "./chunk-NPXATEBX.js";
import "./chunk-RIVS45E3.js";
import "./chunk-HKFEYEPF.js";
import "./chunk-TRYUJMYI.js";
import "./chunk-S5QY7FYS.js";
import "./chunk-GQ77KXG4.js";
import "./chunk-XJ7X4FDH.js";
import "./chunk-WXWYZFL7.js";
import {
  __name
} from "./chunk-ULV4NQHW.js";
import "./chunk-S3PAIZX7.js";
import "./chunk-SERTD5K6.js";

// node_modules/mermaid/dist/chunks/mermaid.core/stateDiagram-v2-YXO3MK2T.mjs
var diagram = {
  parser: stateDiagram_default,
  get db() {
    return new StateDB(2);
  },
  renderer: stateRenderer_v3_unified_default,
  styles: styles_default,
  init: __name((cnf) => {
    if (!cnf.state) {
      cnf.state = {};
    }
    cnf.state.arrowMarkerAbsolute = cnf.arrowMarkerAbsolute;
  }, "init")
};
export {
  diagram
};
//# sourceMappingURL=stateDiagram-v2-YXO3MK2T-23AEG6ME.js.map
