"""
Security Agent - Agente especializado em segurança web

Agente focado em análise e recomendações de segurança:
- Análise de best practices Lighthouse
- HTTPS e certificados
- Headers de segurança
- Vulnerabilidades comuns
"""

import logging
from typing import Dict, Any, Optional
from agno.agent import Agent
from ...shared.model_config import model_config
from agno.tools.reasoning import ReasoningTools

from ..tools import LighthouseAnalysisTools

logger = logging.getLogger(__name__)


class SecurityAgent:
    """Agente especializado em segurança web usando Agno"""

    def __init__(self, model_name: Optional[str] = None):
        """
        Inicializa o Security Agent usando modelos do settings.py

        Args:
            model_name: Modelo específico (opcional, usa automático se None)
        """
        self.lighthouse_tools = LighthouseAnalysisTools()
        
        # Obter configuração de modelo baseada no settings.py
        agno_config = model_config.create_agno_config("analysis")
        
        if model_name:
            available_models = [
                model_config.get_openai_model("analysis"),
                model_config.get_cohere_model("analysis"), 
                model_config.get_mistral_model("analysis")
            ]
            if model_name in available_models:
                self.model_name = model_name
            else:
                logger.warning(f"Modelo {model_name} não disponível no settings.py, usando automático")
                self.model_name = agno_config["model_id"]
        else:
            self.model_name = agno_config["model_id"]

        # Criar modelo baseado no provider
        if agno_config["provider"] == "openai":
            from agno.models.openai import OpenAI
            model_instance = OpenAI(id=self.model_name, api_key=agno_config["api_key"])
        elif agno_config["provider"] == "cohere":
            from agno.models.cohere import Cohere
            model_instance = Cohere(id=self.model_name, api_key=agno_config["api_key"])
        elif agno_config["provider"] == "mistral":
            from agno.models.mistral import Mistral
            model_instance = Mistral(id=self.model_name, api_key=agno_config["api_key"])
        else:
            from agno.models.openai import OpenAI
            model_instance = OpenAI(id="gpt-4o-mini", api_key=agno_config["api_key"])

        self.agent = Agent(
            name="Web Security Specialist",
            role="Expert in web security and best practices analysis",
            model=model_instance,
            tools=[ReasoningTools()],
            instructions=[
                "You are a web security expert specializing in frontend security best practices",
                "Analyze security data from Lighthouse and identify vulnerabilities",
                "Focus on HTTPS, security headers, and common web vulnerabilities",
                "Provide specific recommendations for hardening web applications",
                "Consider OWASP guidelines and modern security standards",
                "Prioritize security fixes by risk level and ease of implementation"
            ],
            description="Specialist in web security, HTTPS, security headers, and vulnerability assessment",
            markdown=True,
            show_tool_calls=True
        )

        logger.info(f"Security Agent inicializado com {agno_config['provider']}:{self.model_name}")

    def analyze_security(self, lighthouse_data: Dict[str, Any], company_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Analisa segurança web usando dados Lighthouse"""
        try:
            lighthouse_metrics = self.lighthouse_tools.analyze_lighthouse_data(
                lighthouse_data)
            bp_score = lighthouse_metrics.best_practices_score

            # Extrair dados específicos de segurança
            best_practices_data = lighthouse_data.get("best_practices", {})
            https_enabled = best_practices_data.get("https", False)
            security_issues = []

            # Identificar issues de segurança
            if not https_enabled:
                security_issues.append("HTTPS não implementado")
            if bp_score < 80:
                security_issues.append(
                    "Best practices de segurança insuficientes")

            analysis_prompt = f"""
Analise a segurança web baseado nos dados Lighthouse:

**SCORE BEST PRACTICES:** {bp_score}/100
**HTTPS ATIVO:** {'Sim' if https_enabled else 'Não'}

**ISSUES DE SEGURANÇA IDENTIFICADOS:**
{chr(10).join(f"- {issue}" for issue in security_issues)}

Forneça análise detalhada incluindo:
1. **Vulnerabilidades críticas** identificadas
2. **Headers de segurança** recomendados (CSP, HSTS, etc.)
3. **Configuração HTTPS** e certificados
4. **Proteções XSS e CSRF** necessárias
5. **Prioridades de implementação** por nível de risco

Foque em hardening básico e proteções essenciais para aplicações web.
"""

            response = self.agent.run(analysis_prompt)

            return {
                "agent_type": "SecurityAgent",
                "best_practices_score": bp_score,
                "https_enabled": https_enabled,
                "security_issues": security_issues,
                "agent_analysis": response.content,
                "security_level": "Secure" if bp_score >= 90 and https_enabled else "Moderate" if bp_score >= 70 else "At Risk"
            }

        except Exception as e:
            logger.error(f"Erro na análise de segurança: {str(e)}")
            raise
