import { Injectable, signal } from '@angular/core';
import { Observable, Subject, filter } from 'rxjs';

export interface ProcessingState {
	clientId: string;
	isProcessing: boolean;
	statusMessage: string;
	progressPercentage: number;
	canViewReport: boolean;
	phase?: string;
	timestamp: Date;
}

export interface ProcessingNotification {
	type: 'processing_started' | 'processing_progress' | 'processing_complete' | 'processing_error';
	clientId: string;
	data: ProcessingState;
}

// ✅ Nova interface para mensagens WebSocket
export interface WebSocketMessage {
	type: string;
	clientId?: string;
	status?: string;
	message?: string;
	timestamp?: string;
	action?: string;
	[key: string]: any;
}

@Injectable({ providedIn: 'root' })
export class WebSocketService {
	private ws!: WebSocket;
	private readonly url = 'ws://localhost:8040/ws'; // Ajuste para seu backend

	// ✅ Signal para todas as mensagens (compatibilidade)
	public readonly messages = signal<Array<unknown>>([]);

	// ✅ Subject específico para todas as mensagens WebSocket
	private messageSubject = new Subject<WebSocketMessage>();
	public readonly allMessages$ = this.messageSubject.asObservable();

	// ✅ Subject específico para atualizações de cliente
	private clientUpdateSubject = new Subject<{
		clientId: string;
		status: string;
		message: string;
		timestamp: string;
	}>();
	public readonly clientUpdates$ = this.clientUpdateSubject.asObservable();

	// ✅ NOVO: Subject específico para alertas de geração de projetos
	private projectGenerationAlertSubject = new Subject<{
		clientId: string;
		clientName: string;
		message: string;
		timestamp: string;
	}>();
	public readonly projectGenerationAlerts$ = this.projectGenerationAlertSubject.asObservable();

	// ✅ NOVO: Subject específico para status de coleta de dados
	private collectionStatusSubject = new Subject<{
		clientId: string;
		status: string;
		message: string;
		phase: string;
		timestamp: string;
	}>();
	public readonly collectionStatus$ = this.collectionStatusSubject.asObservable();

	// ✅ NOVO: Subject específico para projetos gerados
	private projectsGeneratedSubject = new Subject<{
		clientId: string;
		clientName: string;
		projects: any[];
		message: string;
		timestamp: string;
	}>();
	public readonly projectsGenerated$ = this.projectsGeneratedSubject.asObservable();

	// Processing notifications (manter compatibilidade)
	private processingSubject = new Subject<ProcessingNotification>();
	public readonly processingNotifications$ = this.processingSubject.asObservable();

	// Processing states (manter compatibilidade)
	public readonly processingStates = signal<Map<string, ProcessingState>>(new Map());

	public connect(): void {
		if (this.ws && this.ws.readyState === WebSocket.OPEN) {
			return; // Já conectado
		}

		try {
			this.ws = new WebSocket(this.url);

			this.ws.onopen = () => {
				console.log('✅ WebSocket conectado com sucesso');
			};

			this.ws.onmessage = event => {
				try {
					const message: WebSocketMessage = JSON.parse(event.data);

					// ✅ Emitir para todas as mensagens
					this.messageSubject.next(message);

					// ✅ Atualizar signal para compatibilidade
					const currentMessages = this.messages();
					this.messages.set([...currentMessages, message]);

					// ✅ Processar tipos específicos de mensagem
					this.processMessage(message);
				} catch (error) {
					console.error('❌ Erro ao processar mensagem WebSocket:', error);
				}
			};

			this.ws.onerror = error => {
				console.error('❌ Erro no WebSocket:', error);
			};

			this.ws.onclose = () => {
				// Tentar reconectar após 3 segundos
				setTimeout(() => {
					this.connect();
				}, 3000);
			};
		} catch (error) {
			console.error('❌ Erro ao conectar WebSocket:', error);
		}
	}

	// ✅ Processar mensagens específicas
	private processMessage(message: WebSocketMessage): void {
		switch (message.type) {
			case 'client_status_update':
				// Emitir evento específico para atualização de cliente
				if (message.clientId && message.status) {
					this.clientUpdateSubject.next({
						clientId: message.clientId,
						status: message.status,
						message: message.message || '',
						timestamp: message.timestamp || new Date().toISOString(),
					});
				}
				break;

			case 'collection_status_update':
				// ✅ NOVO: Processar atualizações de status de coleta
				if (message.clientId && message.status) {
					this.collectionStatusSubject.next({
						clientId: message.clientId,
						status: message.status,
						message: message.message || '',
						phase: message['phase'] || '',
						timestamp: message.timestamp || new Date().toISOString(),
					});
				}
				break;

			case 'ready_for_projects':
				// ✅ NOVO: Processar alertas para geração de projetos
				if (message.clientId && message['clientName']) {
					this.projectGenerationAlertSubject.next({
						clientId: message.clientId,
						clientName: message['clientName'],
						message: message.message || '',
						timestamp: message.timestamp || new Date().toISOString(),
					});
				}
				break;

			case 'projects_generation_started':
				// ✅ NOVO: Processar alertas para geração de projetos
				if (message.clientId && message['clientName']) {
					this.projectGenerationAlertSubject.next({
						clientId: message.clientId,
						clientName: message['clientName'],
						message: message.message || '',
						timestamp: message.timestamp || new Date().toISOString(),
					});
				}
				break;

			case 'projects_generated':
				// ✅ NOVO: Processar projetos gerados
				if (message.clientId && message['clientName'] && message['projects']) {
					this.projectsGeneratedSubject.next({
						clientId: message.clientId,
						clientName: message['clientName'],
						projects: message['projects'],
						message: message.message || '',
						timestamp: message.timestamp || new Date().toISOString(),
					});
				}
				break;

			case 'projects_generation_error':
				// ✅ NOVO: Log erros de geração de projetos
				console.error('❌ Erro na geração de projetos:', message);
				break;

			case 'processing_started':
			case 'processing_progress':
			case 'processing_complete':
			case 'processing_error':
				// Processar notificações de processamento (compatibilidade)
				if (this.isProcessingNotification(message)) {
					this.handleProcessingNotification(message);
				}
				break;

			default:
				break;
		}
	}

	// ✅ Observável específico para mensagens de um tipo
	public getMessagesByType(type: string): Observable<WebSocketMessage> {
		return this.allMessages$.pipe(filter(message => message.type === type));
	}

	// ✅ Observável específico para atualizações de um cliente
	public getClientUpdates(clientId: string): Observable<{
		clientId: string;
		status: string;
		message: string;
		timestamp: string;
	}> {
		return this.clientUpdates$.pipe(filter(update => update.clientId === clientId));
	}

	// Métodos de compatibilidade (manter)
	private isProcessingNotification(message: any): message is ProcessingNotification {
		return (
			message &&
			typeof message === 'object' &&
			message.type &&
			['processing_started', 'processing_progress', 'processing_complete', 'processing_error'].includes(
				message.type,
			) &&
			message.clientId &&
			message.data
		);
	}

	private handleProcessingNotification(notification: ProcessingNotification): void {
		const states = this.processingStates();
		states.set(notification.clientId, notification.data);
		this.processingStates.set(new Map(states));
		this.processingSubject.next(notification);
	}

	public getProcessingState(clientId: string): ProcessingState | undefined {
		return this.processingStates().get(clientId);
	}

	public subscribeToClientProcessing(clientId: string): Observable<ProcessingNotification> {
		return this.processingNotifications$.pipe(filter(notification => notification.clientId === clientId));
	}

	public sendMessage(msg: unknown): void {
		if (this.ws && this.ws.readyState === WebSocket.OPEN) {
			this.ws.send(JSON.stringify(msg));
		} else {
			console.warn('⚠️ WebSocket não está conectado');
		}
	}

	public requestProcessingStart(clientId: string): void {
		this.sendMessage({
			type: 'request_processing_start',
			clientId: clientId,
			timestamp: new Date().toISOString(),
		});
	}

	public close(): void {
		if (this.ws) {
			this.ws.close();
		}
	}
}
