"""
Configurações do sistema de estimativas de escopo com IA.
"""
import os
from dotenv import load_dotenv

# Carrega variáveis de ambiente do arquivo .env
load_dotenv()


class Settings:
    """Classe para gerenciar configurações do sistema."""

    # Server
    SERVER_NAME = os.getenv('SERVER_NAME')
    SERVER_HOST = os.getenv('SERVER_HOST', 'localhost')
    SERVER_PORT = os.getenv('SERVER_PORT', 8040)

    # OpenAI
    OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')

    # Used OpenAI Models
    GPT_4o = os.getenv('GPT_4o', 'gpt-4o')
    GPT_4_1_MINI = os.getenv('GPT_4_1_MINI', 'gpt-4.1-mini')
    GPT_4_1_NANO = os.getenv('GPT_4_1_NANO', 'gpt-4.1-nano')

    # Cohere
    COHERE_API_KEY = os.getenv('COHERE_API_KEY')

    # Used Cohere Models
    COMMAND_R = os.getenv('COMMAND_R', 'command-r')
    COMMAND_R_PLUS = os.getenv('COMMAND_R_PLUS', 'command-r-plus')

    # Perplexity
    PERPLEXITY_API_KEY = os.getenv('PERPLEXITY_API_KEY')

    # MongoDB
    MONGODB_ATLAS_CONNECTION_URI = os.getenv('MONGODB_ATLAS_CONNECTION_URI')
    MONGODB_DATABASE_NAME = os.getenv('MONGODB_DATABASE_NAME', 'scope_ai')


# Instância única para uso em todo o sistema
env = Settings()
