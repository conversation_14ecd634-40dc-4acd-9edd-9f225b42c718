"""
🚀 Gerador de Relatórios em Markdown usando Cohere Command-R
Processa seções em paralelo para relatórios detalhados usando múltiplos workers.
"""
import os
import logging
import asyncio
import uuid
import threading
from datetime import datetime
from typing import Dict, Any, Optional, List
from concurrent.futures import ThreadPoolExecutor
import re

# Importar Cohere
try:
    import cohere
    COHERE_AVAILABLE = True
except ImportError:
    COHERE_AVAILABLE = False
    logging.error("⚠️ Cohere não disponível. Instale com: pip install cohere")
    raise ImportError(
        "Cohere é necessário para gerar relatórios. Instale com: pip install cohere")

from bson import ObjectId
from config.settings import env
from clients.db import clients_collection

# ==============================================================================
# 📝 CONFIGURAÇÕES OTIMIZADAS PARA RELATÓRIOS DETALHADOS
# ==============================================================================
MARKDOWN_CONFIG = {
    # ✅ NOVO: Opção para usar Command-R+ (mais detalhado)
    # "model": env.COMMAND_R_PLUS if hasattr(env, 'COMMAND_R_PLUS') else env.COMMAND_R,
    "model": env.COMMAND_R if hasattr(env, 'COMMAND_R') else 'command-r-plus',

    # 🚀 OTIMIZADO: Mais workers para paralelização máxima
    # Era 1, agora 3
    "max_workers": int(os.getenv("MARKDOWN_MAX_WORKERS", "3")),

    # 🎯 OTIMIZADO: Temperature mais baixa para consistência
    # Era 0.3, agora 0.2
    "temperature": float(os.getenv("MARKDOWN_TEMPERATURE", "0.2")),

    # 🎯 CONFIGURAÇÕES AVANÇADAS
    "p": float(os.getenv("MARKDOWN_TOP_P", "0.85")),  # Era 0.75, agora 0.85
    "k": int(os.getenv("MARKDOWN_TOP_K", "0")),

    # ⚡ MÁXIMO DE TOKENS (4096 é o limite do Command-R/R+)
    "max_tokens": int(os.getenv("MARKDOWN_MAX_TOKENS", "4096")),

    # 📊 OTIMIZADO: Chunk menor = mais seções detalhadas
    # Mantém 1 para máximo detalhamento
    "chunk_size": int(os.getenv("MARKDOWN_CHUNK_SIZE", "1")),
}

logger = logging.getLogger(__name__)


class CohereWorkerManager:
    """
    Gerenciador de Workers Dinâmicos para Cohere.
    Cria workers por sessão para melhor performance e isolamento.
    """

    def __init__(self):
        if not env.COHERE_API_KEY:
            raise ValueError("COHERE_API_KEY não configurada no ambiente")

        self.active_sessions = {}
        self.workers = {}
        self.max_workers = MARKDOWN_CONFIG["max_workers"]
        self.lock = threading.Lock()
        logger.info(
            f"CohereWorkerManager inicializado com {self.max_workers} workers.")

    def create_session_worker(self, session_id: Optional[str] = None) -> str:
        if not session_id:
            session_id = str(uuid.uuid4())

        with self.lock:
            if session_id not in self.workers:
                if len(self.workers) >= self.max_workers:
                    self._cleanup_inactive_workers()

                if len(self.workers) >= self.max_workers:
                    oldest_session = min(self.active_sessions.keys(),
                                         key=lambda x: self.active_sessions[x]['created_at'])
                    logger.warning(
                        f"Limite de workers atingido, reutilizando sessão {oldest_session}")
                    return oldest_session

                worker = CohereClient(session_id=session_id)
                self.workers[session_id] = worker
                self.active_sessions[session_id] = {
                    'created_at': datetime.now(),
                    'last_used': datetime.now(),
                    'requests_count': 0
                }
                logger.info(f"Worker criado para sessão {session_id}")
        return session_id

    def get_worker(self, session_id: str) -> 'CohereClient':
        with self.lock:
            if session_id not in self.workers:
                self.create_session_worker(session_id)

            if session_id in self.active_sessions:
                self.active_sessions[session_id]['last_used'] = datetime.now()
                self.active_sessions[session_id]['requests_count'] += 1
            return self.workers[session_id]

    def _cleanup_inactive_workers(self):
        current_time = datetime.now()
        inactive_sessions = [
            sid for sid, info in self.active_sessions.items()
            # 30 min
            if (current_time - info['last_used']).total_seconds() > 1800
        ]
        for session_id in inactive_sessions:
            self.remove_session(session_id)

    def remove_session(self, session_id: str):
        with self.lock:
            if session_id in self.workers:
                del self.workers[session_id]
            if session_id in self.active_sessions:
                del self.active_sessions[session_id]
            logger.info(f"Sessão {session_id} removida")

    def get_stats(self) -> Dict[str, Any]:
        with self.lock:
            return {
                'active_workers': len(self.workers),
                'max_workers': self.max_workers,
                'sessions': {
                    sid: {
                        'requests_count': info['requests_count'],
                        'age_minutes': (datetime.now() - info['created_at']).total_seconds() / 60,
                        'idle_minutes': (datetime.now() - info['last_used']).total_seconds() / 60
                    }
                    for sid, info in self.active_sessions.items()
                }
            }


class CohereClient:
    """
    Cliente para comunicação com Cohere Command-R, otimizado para geração de relatórios.
    """

    def __init__(self, session_id: Optional[str] = None):
        self.session_id = session_id or str(uuid.uuid4())
        self.model = MARKDOWN_CONFIG["model"]
        self.client = cohere.Client(api_key=env.COHERE_API_KEY)
        logger.info(
            f"Cliente Cohere inicializado para sessão {self.session_id} com modelo {self.model}")

    def generate(self, prompt: str, max_tokens: Optional[int] = None,
                 temperature: Optional[float] = None, p: Optional[float] = None,
                 k: Optional[int] = None) -> str:
        """
        Gera texto usando Cohere Command-R

        Args:
            prompt: Prompt para geração
            max_tokens: Número máximo de tokens
            temperature: Controla aleatoriedade (0-1)
            p: Top-p sampling
            k: Top-k sampling

        Returns:
            Texto gerado
        """
        try:
            # Usar valores das configurações se não fornecidos
            temperature = temperature if temperature is not None else MARKDOWN_CONFIG[
                "temperature"]
            p = p if p is not None else MARKDOWN_CONFIG["p"]
            k = k if k is not None else MARKDOWN_CONFIG["k"]
            max_tokens = max_tokens if max_tokens is not None else MARKDOWN_CONFIG["max_tokens"]

            # Construir mensagem com system prompt
            preamble = """Você é um consultor sênior especializado em análise empresarial. Sua tarefa é gerar seções específicas de um relatório executivo em Markdown.

🚨 REGRAS CRÍTICAS OBRIGATÓRIAS:
• **SOMENTE DADOS FORNECIDOS**: Use EXCLUSIVAMENTE as informações fornecidas no prompt
• **ZERO INVENÇÃO**: NUNCA crie estatísticas, valores monetários, datas, percentuais ou fatos não fornecidos
• **TRANSPARÊNCIA TOTAL**: Se dados específicos não estão disponíveis, escreva claramente "informação não disponível nos dados fornecidos"
• **PROIBIDO ESPECULAR**: Não faça suposições sobre investimentos, valores, crescimento ou dados não confirmados
• **CITAR FONTES**: Referencie sempre qual seção dos dados fornecidos você está usando

⚠️ EXEMPLOS DO QUE NÃO FAZER:
- "A empresa recebeu US$ X milhões" (se valor não está nos dados)
- "O blog é atualizado regularmente" (se dados dizem "Blog ativo: Não")
- "Crescimento de X%" (se não há números específicos nos dados)
- "Escritórios em cidade Y" (se não mencionado nos dados)

✅ EXEMPLOS CORRETOS:
- "Segundo os dados de funding: Total captado: Não disponível"
- "Os dados de presença digital indicam: Blog ativo: Não"
- "Conforme histórico fornecido: [citar texto exato]"

FORMATO: Markdown com `##` usando APENAS informações confirmadas nos dados fornecidos."""

            # Gerar resposta usando Cohere
            response = self.client.chat(
                model=self.model,
                message=prompt,
                preamble=preamble,
                temperature=temperature,
                p=p,
                k=k,
                max_tokens=max_tokens
            )

            # Extrair texto da resposta
            content = response.text

            logger.debug(
                f"Gerado {len(content)} caracteres para sessão {self.session_id}")
            return content

        except Exception as e:
            logger.error(
                f"Erro na geração com Cohere (sessão {self.session_id}): {e}")
            raise Exception(f"Falha na geração com Cohere Command-R: {e}")


# Instância global do gerenciador
worker_manager = None


def get_worker_manager() -> CohereWorkerManager:
    global worker_manager
    if worker_manager is None:
        worker_manager = CohereWorkerManager()
    return worker_manager


def get_cohere_client(session_id: Optional[str] = None) -> CohereClient:
    manager = get_worker_manager()
    if session_id is None:
        session_id = create_session()
    return manager.get_worker(session_id)


def create_session(session_id: Optional[str] = None) -> str:
    manager = get_worker_manager()
    return manager.create_session_worker(session_id)


def cleanup_session(session_id: str):
    manager = get_worker_manager()
    manager.remove_session(session_id)


class MarkdownReportGenerator:
    """
    Gera relatórios executivos detalhados usando processamento paralelo
    com múltiplos workers Cohere Command-R.
    """

    def __init__(self):
        if not env.COHERE_API_KEY:
            raise ValueError("COHERE_API_KEY não configurada")

        self.max_workers = MARKDOWN_CONFIG["max_workers"]
        self.model = MARKDOWN_CONFIG["model"]
        self.temperature = MARKDOWN_CONFIG["temperature"]
        self.p = MARKDOWN_CONFIG["p"]
        self.k = MARKDOWN_CONFIG["k"]
        self.chunk_size = MARKDOWN_CONFIG["chunk_size"]

        self.report_sections = [
            "Sumário Executivo", "Perfil Corporativo", "Análise de Mercado",
            "Análise SWOT Detalhada", "Avaliação Tecnológica", "Presença Digital e Marketing",
            "Modelo de Negócio e Estratégia", "Análise Financeira e Investimentos",
            "Diagnóstico de Performance", "Análise de Segurança", "Experiência do Usuário (UX)",
            "Oportunidades de Crescimento", "Riscos e Mitigações", "Roadmap Estratégico",
            "Recomendações Prioritárias"
        ]

        logger.info(
            f"🚀 MarkdownReportGenerator inicializado com {self.max_workers} workers e modelo {self.model}")

    async def generate_markdown_report(self, client_id: str) -> Dict[str, Any]:
        """
        Gera relatório executivo detalhado usando processamento paralelo.
        """
        try:
            start_time = datetime.now()
            client_data = await self._get_client_data(client_id)
            client_name = client_data.get("name", "Cliente")
            logger.info(f"🚀 Iniciando relatório paralelo para {client_name}")

            comprehensive_data = self._extract_comprehensive_data(client_data)
            section_chunks = self._create_section_chunks()

            logger.info(
                f"📊 Processando {len(section_chunks)} chunks com {self.max_workers} workers")

            section_results = await self._process_sections_parallel(
                client_name, comprehensive_data, section_chunks
            )

            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()

            final_report = self._consolidate_report(
                client_name, section_results, processing_time)

            metadata = {
                "client_id": client_id,
                "client_name": client_name,
                "generated_at": end_time.isoformat(),
                "processing_time_seconds": processing_time,
                "model_used": self.model,
                "workers_used": self.max_workers,
                "sections_processed": len(self.report_sections),
                "total_tokens_estimated": len(final_report) * 0.75,
                "quality_indicators": {"parallel_processing": True, "detailed_analysis": True}
            }

            logger.info(
                f"✅ Relatório paralelo concluído em {processing_time:.1f}s para {client_name}")

            return {"markdown_content": final_report, "metadata": metadata}

        except Exception as e:
            logger.error(f"❌ Erro no relatório paralelo: {str(e)}")
            raise

    async def _get_client_data(self, client_id: str) -> Dict[str, Any]:
        loop = asyncio.get_event_loop()
        client = await loop.run_in_executor(None, lambda: clients_collection.find_one({"_id": ObjectId(client_id)}))
        if not client:
            raise ValueError(f"Cliente {client_id} não encontrado")
        return client

    def _extract_comprehensive_data(self, client: Dict[str, Any]) -> Dict[str, Any]:
        # 🚨 BUG FIX: MongoDB armazena "report_type" não "reportType"
        # Buscar tanto report_type quanto reportType para compatibilidade
        dossier = {}

        # Tentar primeiro report_type (formato atual no MongoDB)
        for report in client.get("reports", []):
            if report.get("report_type") == "dossie_expandido":
                # ⚠️ IMPORTANTE: Os dados estão no ROOT do report, não em "data"
                dossier = {k: v for k, v in report.items() if k not in [
                    'report_type', 'version', 'timestamp']}
                break

        # Fallback: tentar reportType (formato antigo)
        if not dossier:
            dossier = next((r.get("data", {}) for r in client.get(
                "reports", []) if r.get("reportType") == "dossie_expandido"), {})

        # Buscar diagnósticos técnicos
        diagnostics = next((r.get("data", {}) for r in client.get(
            "reports", []) if r.get("report_type") == "diagnostico_tecnico"), {})

        return {
            "basic_info": {
                "nome": client.get("name", ""),
                "setor": client.get("sector", ""),
                "cidade": client.get("city", ""),
                "estado": client.get("state", ""),
                "site": client.get("site", ""),
                "descricao": client.get("description", "")
            },
            "dossier_data": dossier,
            "market_research": dossier.get("dados_pesquisa_mercado", {}),
            "diagnostics": diagnostics
        }

    def _create_section_chunks(self) -> List[List[str]]:
        chunks = [self.report_sections[i:i + self.chunk_size]
                  for i in range(0, len(self.report_sections), self.chunk_size)]
        logger.info(
            f"📊 Criados {len(chunks)} chunks de {self.chunk_size} seções cada.")
        return chunks

    async def _process_sections_parallel(self, client_name: str, comprehensive_data: Dict[str, Any],
                                         section_chunks: List[List[str]]) -> List[str]:
        logger.info(
            f"⚡ Iniciando processamento paralelo de {len(section_chunks)} chunks")

        tasks = [
            self._process_chunk(i, client_name, comprehensive_data, chunk)
            for i, chunk in enumerate(section_chunks)
        ]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"❌ Erro no worker {i}: {result}")
                processed_results.append(
                    f"# Erro no processamento do chunk {i}\n\nErro: {result}")
            else:
                processed_results.append(result)
                logger.info(f"✅ Worker {i} concluído com sucesso")

        return processed_results

    async def _process_chunk(self, worker_id: int, client_name: str,
                             comprehensive_data: Dict[str, Any], sections: List[str]) -> str:
        logger.info(f"🔧 Worker {worker_id} processando seções: {sections}")
        session_id = create_session(f"parallel_worker_{worker_id}")
        client = get_cohere_client(session_id)
        prompt = self._create_chunk_prompt(
            client_name, comprehensive_data, sections)

        try:
            # Usar executor para não bloquear o event loop
            loop = asyncio.get_event_loop()
            content = await loop.run_in_executor(
                None,
                lambda: client.generate(
                    prompt=prompt,
                    temperature=self.temperature,
                    p=self.p,
                    k=self.k
                )
            )

            logger.info(
                f"✅ Worker {worker_id} gerou {len(content)} caracteres")
            return content

        except Exception as e:
            logger.error(f"❌ Erro no worker {worker_id}: {e}")
            raise
        finally:
            cleanup_session(session_id)

    def _create_chunk_prompt(self, client_name: str, comprehensive_data: Dict[str, Any],
                             sections: List[str]) -> str:
        data_summary = self._format_data_for_prompt(comprehensive_data)
        sections_list = "\n".join([f"## {section}" for section in sections])

        return f"""Analise RIGOROSAMENTE os dados da empresa {client_name} fornecidos abaixo e crie as seções especificadas.

DADOS EMPRESARIAIS COMPLETOS:
{data_summary}

🎯 SEÇÕES PARA CRIAR:
{sections_list}

🚨 REGRAS CRÍTICAS OBRIGATÓRIAS:
• **SOMENTE DADOS FORNECIDOS**: Use EXCLUSIVAMENTE as informações listadas acima nos "DADOS EMPRESARIAIS COMPLETOS"
• **ZERO INVENÇÃO**: NUNCA crie estatísticas, valores monetários, datas, percentuais ou fatos não fornecidos
• **TRANSPARÊNCIA TOTAL**: Se dados específicos não estão disponíveis, escreva claramente "informação não disponível nos dados fornecidos"
• **PROIBIDO ESPECULAR**: Não faça suposições sobre investimentos, valores, crescimento ou dados não confirmados
• **CITAR FONTES**: Referencie sempre qual seção dos dados fornecidos você está usando

⚠️ EXEMPLOS DO QUE NÃO FAZER:
- "A empresa recebeu US$ X milhões" (se valor não está nos dados)
- "O blog é atualizado regularmente" (se dados dizem "Blog ativo: Não")
- "Crescimento de X%" (se não há números específicos nos dados)
- "Escritórios em cidade Y" (se não mencionado nos dados)

✅ EXEMPLOS CORRETOS:
- "Segundo os dados de funding: Total captado: Não disponível"
- "Os dados de presença digital indicam: Blog ativo: Não"
- "Conforme histórico fornecido: [citar texto exato]"

📋 ESTRUTURA ESPERADA:
- Use dados reais específicos mencionados acima
- Cite sempre a origem da informação ("Conforme histórico...", "Segundo dados de funding...")
- Indique claramente quando não há dados disponíveis
- Baseie recomendações apenas em informações confirmadas

FORMATO: Markdown com `##` para títulos das seções usando APENAS informações confirmadas nos dados fornecidos."""

    def _format_data_for_prompt(self, comprehensive_data: Dict[str, Any]) -> str:
        """Formata dados de forma otimizada para o prompt"""
        basic_info = comprehensive_data.get("basic_info", {})
        dossier = comprehensive_data.get("dossier_data", {})
        market_research = comprehensive_data.get("market_research", {})
        diagnostics = comprehensive_data.get("diagnostics", {})

        formatted_data = f"""
**INFORMAÇÕES BÁSICAS:**
- Nome: {basic_info.get('nome', 'N/A')}
- Setor: {basic_info.get('setor', 'N/A')}
- Localização: {basic_info.get('cidade', 'N/A')}, {basic_info.get('estado', 'N/A')}
- Website: {basic_info.get('site', 'N/A')}

**DOSSIÊ EMPRESARIAL:**
{self._format_dossier_summary(dossier)}

**PESQUISA DE MERCADO:**
{self._format_market_summary(market_research)}

**DIAGNÓSTICOS TÉCNICOS:**
{self._format_diagnostics_summary(diagnostics)}
"""

        return formatted_data

    def _format_dossier_summary(self, dossier: Dict[str, Any]) -> str:
        """Formata resumo ULTRA-DETALHADO do dossiê explorando TODAS as seções disponíveis"""
        if not dossier:
            return "- Dados do dossiê não disponíveis"

        summary = []

        # 🔧 HISTÓRICO COMPLETO E DETALHADO
        if "historico" in dossier:
            historico = dossier["historico"]
            if isinstance(historico, str) and len(historico) > 10:
                historico_clean = clean_unicode(historico)
                summary.append(f"**HISTÓRICO COMPLETO:**\n{historico_clean}")

        # 🔧 INFORMAÇÕES GERAIS DETALHADAS
        if "informacoes_gerais" in dossier:
            info_gerais = dossier["informacoes_gerais"]
            if isinstance(info_gerais, dict):
                info_details = []
                if "descricao" in info_gerais:
                    descricao = clean_unicode(str(info_gerais["descricao"]))
                    info_details.append(f"Descrição: {descricao}")
                if "executivos" in info_gerais:
                    executivos = info_gerais["executivos"]
                    if isinstance(executivos, list) and executivos:
                        info_details.append(
                            f"Executivos: {len(executivos)} líderes identificados")

                if info_details:
                    summary.append(f"**INFORMAÇÕES GERAIS:**\n" +
                                   "\n".join([f"- {detail}" for detail in info_details]))

        # 🆕 CASES DE SUCESSO (NOVO!)
        if "cases_sucesso" in dossier:
            cases = dossier["cases_sucesso"]
            if isinstance(cases, dict):
                cases_details = []
                for case_key, case_content in cases.items():
                    if isinstance(case_content, str) and len(case_content) > 10:
                        case_clean = clean_unicode(case_content)
                        cases_details.append(
                            f"{case_key.upper()}: {case_clean}")

                if cases_details:
                    summary.append(f"**CASES DE SUCESSO REAIS:**\n" +
                                   "\n".join([f"- {detail}" for detail in cases_details]))

        # 🆕 ANÁLISE COMPETITIVA DETALHADA (NOVO!)
        if "concorrentes" in dossier:
            concorrentes = dossier["concorrentes"]
            if isinstance(concorrentes, dict):
                comp_details = []
                if "principais" in concorrentes:
                    principais = concorrentes["principais"]
                    if isinstance(principais, list) and principais:
                        comp_details.append(
                            f"Principais concorrentes: {', '.join(principais)}")
                if "diferenciais_concorrentes" in concorrentes:
                    diferenciais = clean_unicode(
                        str(concorrentes["diferenciais_concorrentes"]))
                    comp_details.append(
                        f"Diferenciais competitivos: {diferenciais}")

                if comp_details:
                    summary.append(f"**ANÁLISE COMPETITIVA:**\n" +
                                   "\n".join([f"- {detail}" for detail in comp_details]))

        # 🆕 MODELO DE NEGÓCIO DETALHADO (NOVO!)
        if "dados_modelo_negocio" in dossier:
            modelo = dossier["dados_modelo_negocio"]
            if isinstance(modelo, dict):
                modelo_details = []

                # Proposta de valor
                if "proposta_valor" in modelo:
                    proposta = modelo["proposta_valor"]
                    if isinstance(proposta, dict):
                        modelo_details.append(
                            f"Proposta de valor: {len(proposta)} dimensões mapeadas")

                # Segmentos de clientes
                if "segmentos_clientes" in modelo:
                    segmentos = modelo["segmentos_clientes"]
                    if isinstance(segmentos, dict):
                        modelo_details.append(
                            f"Segmentos de clientes: {len(segmentos)} segmentos identificados")

                # Canais de distribuição
                if "canais_distribuicao" in modelo:
                    canais = modelo["canais_distribuicao"]
                    if isinstance(canais, dict):
                        modelo_details.append(
                            f"Canais de distribuição: {len(canais)} canais mapeados")

                if modelo_details:
                    summary.append(f"**MODELO DE NEGÓCIO:**\n" +
                                   "\n".join([f"- {detail}" for detail in modelo_details]))

        # 🆕 PARCERIAS ESTRATÉGICAS (NOVO!)
        if "dados_parcerias" in dossier:
            parcerias = dossier["dados_parcerias"]
            if isinstance(parcerias, dict):
                parceria_details = []

                if "parcerias_comerciais" in parcerias:
                    comerciais = parcerias["parcerias_comerciais"]
                    if isinstance(comerciais, dict):
                        parceria_details.append(
                            f"Parcerias comerciais: {len(comerciais)} aspectos mapeados")

                if "parcerias_tecnologicas" in parcerias:
                    tecnologicas = parcerias["parcerias_tecnologicas"]
                    if isinstance(tecnologicas, dict):
                        parceria_details.append(
                            f"Parcerias tecnológicas: {len(tecnologicas)} aspectos mapeados")

                if "parcerias_estrategicas" in parcerias:
                    estrategicas = parcerias["parcerias_estrategicas"]
                    if isinstance(estrategicas, dict):
                        parceria_details.append(
                            f"Parcerias estratégicas: {len(estrategicas)} aspectos mapeados")

                if parceria_details:
                    summary.append(f"**PARCERIAS ESTRATÉGICAS:**\n" +
                                   "\n".join([f"- {detail}" for detail in parceria_details]))

        # 🆕 ESTRUTURA DE PREÇOS (NOVO!)
        if "dados_pricing" in dossier:
            pricing = dossier["dados_pricing"]
            if isinstance(pricing, dict):
                pricing_details = []

                if "resumo_pricing" in pricing:
                    resumo = pricing["resumo_pricing"]
                    if isinstance(resumo, dict):
                        pricing_details.append(
                            f"Estrutura de preços: {len(resumo)} aspectos analisados")

                if "tiers_precos_detalhados" in pricing:
                    tiers = pricing["tiers_precos_detalhados"]
                    if isinstance(tiers, list) and tiers:
                        pricing_details.append(
                            f"Tiers de preços: {len(tiers)} planos identificados")

                if "estrategias_monetizacao" in pricing:
                    monetizacao = pricing["estrategias_monetizacao"]
                    if isinstance(monetizacao, dict):
                        pricing_details.append(
                            f"Estratégias de monetização: {len(monetizacao)} estratégias mapeadas")

                if pricing_details:
                    summary.append(f"**ESTRUTURA DE PREÇOS:**\n" +
                                   "\n".join([f"- {detail}" for detail in pricing_details]))

        # 🔧 STACK TÉCNICO ULTRA-DETALHADO (MELHORADO!)
        if "stack_tecnico" in dossier:
            tech = dossier["stack_tecnico"]
            if isinstance(tech, dict):
                tech_details = []

                # Frontend
                if "frontend" in tech:
                    frontend = tech["frontend"]
                    if isinstance(frontend, dict):
                        tech_details.append(
                            f"Frontend: {len(frontend)} tecnologias/aspectos")

                # Backend
                if "backend" in tech:
                    backend = tech["backend"]
                    if isinstance(backend, dict):
                        tech_details.append(
                            f"Backend: {len(backend)} tecnologias/aspectos")

                # Banco de dados
                if "banco_dados" in tech:
                    db = tech["banco_dados"]
                    if isinstance(db, dict):
                        tech_details.append(
                            f"Banco de dados: {len(db)} soluções/aspectos")

                # Analytics
                if "analytics_marketing" in tech:
                    analytics = tech["analytics_marketing"]
                    if isinstance(analytics, dict):
                        tech_details.append(
                            f"Analytics: {len(analytics)} ferramentas/aspectos")

                # Fallback para campos antigos
                if "tech_stack" in tech and tech["tech_stack"]:
                    tech_clean = clean_unicode(str(tech['tech_stack']))
                    tech_details.append(f"Tecnologias gerais: {tech_clean}")
                if "frameworks" in tech and tech["frameworks"]:
                    frameworks_clean = clean_unicode(str(tech['frameworks']))
                    tech_details.append(f"Frameworks: {frameworks_clean}")

                if tech_details:
                    summary.append(f"**STACK TÉCNICO DETALHADO:**\n" +
                                   "\n".join([f"- {detail}" for detail in tech_details]))

        # 🆕 REPUTAÇÃO E REVIEWS (NOVO!)
        if "dados_canais_reviews" in dossier:
            reviews = dossier["dados_canais_reviews"]
            if isinstance(reviews, dict):
                review_details = []

                if "analise_reputacional" in reviews:
                    reputacao = reviews["analise_reputacional"]
                    if isinstance(reputacao, dict):
                        review_details.append(
                            f"Análise reputacional: {len(reputacao)} aspectos")

                if "dados_certificacoes" in reviews:
                    certificacoes = reviews["dados_certificacoes"]
                    if isinstance(certificacoes, dict):
                        review_details.append(
                            f"Certificações: {len(certificacoes)} aspectos mapeados")

                if "dados_reviews_feedback" in reviews:
                    feedback = reviews["dados_reviews_feedback"]
                    if isinstance(feedback, dict):
                        review_details.append(
                            f"Reviews e feedback: {len(feedback)} fontes analisadas")

                if review_details:
                    summary.append(f"**REPUTAÇÃO E REVIEWS:**\n" +
                                   "\n".join([f"- {detail}" for detail in review_details]))

        # 🔧 FUNDING - DADOS ESPECÍFICOS REAIS (MANTIDO)
        if "dados_funding" in dossier:
            funding = dossier["dados_funding"]
            if isinstance(funding, dict):
                resumo_funding = funding.get("resumo_funding", {})
                funding_details = []
                if resumo_funding.get("total_captado"):
                    total_clean = clean_unicode(
                        str(resumo_funding['total_captado']))
                    funding_details.append(f"Total captado: {total_clean}")
                if resumo_funding.get("numero_rodadas"):
                    rodadas_clean = clean_unicode(
                        str(resumo_funding['numero_rodadas']))
                    funding_details.append(
                        f"Número de rodadas: {rodadas_clean}")
                if resumo_funding.get("status_funding"):
                    status_clean = clean_unicode(
                        str(resumo_funding['status_funding']))
                    funding_details.append(f"Status: {status_clean}")

                rodadas = funding.get("rodadas_investimento", [])
                if rodadas:
                    rodadas_clean = clean_unicode(str(rodadas))
                    funding_details.append(
                        f"Rodadas de investimento: {rodadas_clean}")
                else:
                    funding_details.append(
                        "Rodadas de investimento: Nenhuma identificada")

                if funding_details:
                    summary.append(
                        f"**FUNDING REAL:**\n" + "\n".join([f"- {detail}" for detail in funding_details]))

        # 🔧 PRESENÇA DIGITAL - DADOS ESPECÍFICOS (MANTIDO)
        if "dados_presenca_digital" in dossier:
            digital = dossier["dados_presenca_digital"]
            if isinstance(digital, dict):
                digital_details = []

                # Conteúdo digital específico
                conteudo = digital.get("conteudo_digital", {})
                if conteudo:
                    if conteudo.get("blog_ativo"):
                        blog_clean = clean_unicode(str(conteudo['blog_ativo']))
                        digital_details.append(f"Blog ativo: {blog_clean}")
                    if conteudo.get("frequencia_blog"):
                        freq_clean = clean_unicode(
                            str(conteudo['frequencia_blog']))
                        digital_details.append(
                            f"Frequência blog: {freq_clean}")
                    if conteudo.get("materiais_educativos"):
                        mat_clean = clean_unicode(
                            str(conteudo['materiais_educativos']))
                        digital_details.append(
                            f"Materiais educativos: {mat_clean}")

                resumo_presenca = digital.get("resumo_presenca_digital", {})
                if resumo_presenca.get("avaliacao_geral"):
                    aval_clean = clean_unicode(
                        str(resumo_presenca['avaliacao_geral']))
                    digital_details.append(f"Avaliação geral: {aval_clean}")

                if digital_details:
                    summary.append(f"**PRESENÇA DIGITAL REAL:**\n" +
                                   "\n".join([f"- {detail}" for detail in digital_details]))

        # 🔧 SWOT EXPANDIDA DETALHADA (MANTIDO)
        if "analise_swot_expandida" in dossier:
            swot = dossier["analise_swot_expandida"]
            if isinstance(swot, dict):
                swot_details = []
                for key in ["forcas", "fraquezas", "oportunidades", "ameacas"]:
                    if key in swot and isinstance(swot[key], list) and swot[key]:
                        swot_clean = clean_unicode(str(swot[key]))
                        swot_details.append(f"{key.title()}: {swot_clean}")

                if swot_details:
                    summary.append(f"**ANÁLISE SWOT REAL:**\n" +
                                   "\n".join([f"- {detail}" for detail in swot_details]))

        # 🔧 PESQUISA DE MERCADO ESPECÍFICA (MANTIDO)
        if "dados_pesquisa_mercado" in dossier:
            mercado = dossier["dados_pesquisa_mercado"]
            if isinstance(mercado, dict) and mercado:
                mercado_details = []
                for key, value in mercado.items():
                    if value and str(value) != "N/A":
                        value_clean = clean_unicode(str(value))
                        key_clean = clean_unicode(str(key))
                        mercado_details.append(f"{key_clean}: {value_clean}")

                if mercado_details:
                    summary.append(f"**PESQUISA DE MERCADO:**\n" +
                                   "\n".join([f"- {detail}" for detail in mercado_details]))

        return "\n\n".join(summary) if summary else "- Dossiê expandido encontrado mas sem dados estruturados"

    def _format_market_summary(self, market_research: Dict[str, Any]) -> str:
        """Formata resumo da pesquisa de mercado"""
        if not market_research:
            return "- Dados de mercado não disponíveis"

        summary = []

        if market_research.get("tamanho_mercado"):
            summary.append(
                f"- Tamanho: {market_research.get('tamanho_mercado')}")

        if market_research.get("crescimento_anual"):
            summary.append(
                f"- Crescimento: {market_research.get('crescimento_anual')}")

        trends = market_research.get("tendencias", [])
        if trends:
            summary.append(f"- {len(trends)} tendências identificadas")

        return "\n".join(summary) if summary else "- Pesquisa de mercado em andamento"

    def _format_diagnostics_summary(self, diagnostics: Dict[str, Any]) -> str:
        """Formata resumo dos diagnósticos"""
        if not diagnostics:
            return "- Dados de diagnóstico não disponíveis"

        summary = []

        # Performance
        perf = diagnostics.get("performance", {})
        if perf:
            summary.append(f"- Performance: {perf.get('score', 'N/A')}/100")

        # Segurança
        sec = diagnostics.get("security", {})
        if sec:
            summary.append(f"- Segurança: {sec.get('score', 'N/A')}/100")

        # SEO
        seo = diagnostics.get("seo", {})
        if seo:
            summary.append(f"- SEO: {seo.get('score', 'N/A')}/100")

        return "\n".join(summary) if summary else "- Diagnósticos em processamento"

    def _consolidate_report(self, client_name: str, section_results: List[str], processing_time: float) -> str:
        """Consolida todas as seções em um relatório final"""
        report_header = f"""# Relatório Executivo Detalhado - {client_name}

**Data de Geração:** {datetime.now().strftime('%d/%m/%Y às %H:%M')}  
**Tempo de Processamento:** {processing_time:.1f} segundos  
**Modelo de IA:** Cohere {self.model}  
**Processamento:** Paralelo com {self.max_workers} workers

---

"""

        # Juntar todas as seções
        full_content = report_header + "\n\n".join(section_results)

        # Footer
        report_footer = f"""

---

## Notas Finais

Este relatório foi gerado automaticamente utilizando inteligência artificial avançada (Cohere Command-R) 
com processamento paralelo para análise detalhada de múltiplas dimensões do negócio.

**Observação:** Todas as análises e recomendações devem ser validadas com stakeholders 
antes da implementação.

---
*Gerado por Innovation Scope AI - Powered by Cohere Command-R*
"""

        return full_content + report_footer


def get_markdown_report_generator() -> MarkdownReportGenerator:
    """Factory function para obter instância do gerador de relatórios"""
    return MarkdownReportGenerator()


def clean_unicode(text):
    """Remove caracteres Unicode problemáticos que causam erros de encoding"""
    if not isinstance(text, str):
        return str(text)

    # Remove surrogates e caracteres problemáticos
    cleaned = re.sub(r'[\ud800-\udfff]', '', text)  # Remove surrogates
    # Remove caracteres fora do BMP
    cleaned = re.sub(r'[^\u0000-\uffff]', '', cleaned)
    # Remove outros problemáticos
    cleaned = cleaned.encode('utf-8', errors='ignore').decode('utf-8')

    return cleaned


# Testes básicos
if __name__ == "__main__":
    # Testar se Cohere está configurado
    if not env.COHERE_API_KEY:
        print("❌ COHERE_API_KEY não configurada!")
    else:
        print(f"✅ Cohere configurado com modelo: {MARKDOWN_CONFIG['model']}")
        print(f"✅ Workers paralelos: {MARKDOWN_CONFIG['max_workers']}")
        print(f"✅ Temperatura: {MARKDOWN_CONFIG['temperature']}")
