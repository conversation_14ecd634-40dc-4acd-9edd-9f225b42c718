{"version": 3, "sources": ["../../../../../../node_modules/@mermaid-js/parser/dist/chunks/mermaid-parser.core/chunk-2O5ZK7RR.mjs"], "sourcesContent": ["import { AbstractMermaidTokenBuilder, CommonValueConverter, MermaidGeneratedSharedModule, RadarGeneratedModule, __name } from \"./chunk-7PKI6E2E.mjs\";\n\n// src/language/radar/module.ts\nimport { EmptyFileSystem, createDefaultCoreModule, createDefaultSharedCoreModule, inject } from \"langium\";\n\n// src/language/radar/tokenBuilder.ts\nvar RadarTokenBuilder = class extends AbstractMermaidTokenBuilder {\n  static {\n    __name(this, \"RadarTokenBuilder\");\n  }\n  constructor() {\n    super([\"radar-beta\"]);\n  }\n};\n\n// src/language/radar/module.ts\nvar RadarModule = {\n  parser: {\n    TokenBuilder: /* @__PURE__ */__name(() => new RadarTokenBuilder(), \"TokenBuilder\"),\n    ValueConverter: /* @__PURE__ */__name(() => new CommonValueConverter(), \"ValueConverter\")\n  }\n};\nfunction createRadarServices(context = EmptyFileSystem) {\n  const shared = inject(createDefaultSharedCoreModule(context), MermaidGeneratedSharedModule);\n  const Radar = inject(createDefaultCoreModule({\n    shared\n  }), RadarGeneratedModule, RadarModule);\n  shared.ServiceRegistry.register(Radar);\n  return {\n    shared,\n    Radar\n  };\n}\n__name(createRadarServices, \"createRadarServices\");\nexport { RadarModule, createRadarServices };"], "mappings": ";;;;;;;;;;;;;;AAMA,IAAI,oBAAoB,cAAc,4BAA4B;AAAA,EAChE,OAAO;AACL,WAAO,MAAM,mBAAmB;AAAA,EAClC;AAAA,EACA,cAAc;AACZ,UAAM,CAAC,YAAY,CAAC;AAAA,EACtB;AACF;AAGA,IAAI,cAAc;AAAA,EAChB,QAAQ;AAAA,IACN,cAA6B,OAAO,MAAM,IAAI,kBAAkB,GAAG,cAAc;AAAA,IACjF,gBAA+B,OAAO,MAAM,IAAI,qBAAqB,GAAG,gBAAgB;AAAA,EAC1F;AACF;AACA,SAAS,oBAAoB,UAAU,iBAAiB;AACtD,QAAM,SAAS,OAAO,8BAA8B,OAAO,GAAG,4BAA4B;AAC1F,QAAM,QAAQ,OAAO,wBAAwB;AAAA,IAC3C;AAAA,EACF,CAAC,GAAG,sBAAsB,WAAW;AACrC,SAAO,gBAAgB,SAAS,KAAK;AACrC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,OAAO,qBAAqB,qBAAqB;", "names": []}